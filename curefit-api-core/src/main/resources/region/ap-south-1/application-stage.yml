server:
  jetty:
    threads:
      max: 100

logfile:
  path: ${LOGFILE_PATH:/logs/curefit-api-v2}

rollbar:
  accessToken: alsknas
  disabled: true

sentry:
  disabled: false
  dsn: ${SENTRY_DSN:https://<EMAIL>/5186323}
  stacktrace:
    app:
      packages: com.curefit
  samplerate: 1.0
  mdctags: rID,uID,tID,sla_owner,pageId,widgetId,aktoRequest,securityTest
  uncaught:
    handler:
      enabled: true
  send-default-pii: true
  traces-sample-rate: 0.0

rashi:
  sns:
    topicArn: arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events

payment-service:
  baseUrl: http://payment.stage.cure.fit.internal
  apiKey: N8fHVfInFmq5ij1cQLywlvsGYZaTPgkz

tata:
  segmentation:
    potentialTataUser: TATA_POTENTIAL_TATA_USER
    neupassUser: TATA_NEUPASS_USER
    neupassNotActivated: TATA_NEUPASS_NOT_ACTIVATED
    neupassConsentPending: TATA_NEUPASS_CONSENT_PENDING
    tataUser: TATA_TATA_USER

redis:
  default:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 1000
    minIdle: 10
    maxIdle: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    minEvictableIdleTimeMillis: 60000
    softMinEvictableIdleTimeMillis: 90000
    timeBetweenEvictionRunsMillis: 30000
  session-transient:
    host: ${INFRA_CONFIG_REDIS_STAGE_SESSION_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_STAGE_SESSION_CACHE_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 1000
    minIdle: 10
    maxIdle: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    minEvictableIdleTimeMillis: 60000
    softMinEvictableIdleTimeMillis: 90000
    timeBetweenEvictionRunsMillis: 30000
  cfapi:
    host: ${INFRA_CONFIG_REDIS_CF_API_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CF_API_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 1500
    maxSize: 20
    minIdle: 5
    maxIdle: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    minEvictableIdleTimeMillis: 60000
    softMinEvictableIdleTimeMillis: 90000
    timeBetweenEvictionRunsMillis: 30000
  hamlet:
    host: ${INFRA_CONFIG_REDIS_CF_API_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CF_API_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 1500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  riddler-cache:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 1500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  eatfit-cache:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 1500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  gear:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 1500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  cyclops:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  social-service:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    redis:
      host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
      port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
      clusterEnabled: false
      timeoutMs: 4500
      maxSize: 20
      username: ${INFRA_CONFIG_REDIS_USERNAME}
      password: ${INFRA_CONFIG_REDIS_PASSWORD}

  cult-redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


curefit:
  pageIds:
    cult-live: LIVE
    home: PLAN
  segmentation:
    segmentIds:
      cult-live: b896fa50-faf5-4358-af9e-2481dc28574a
    redis:
      userSetNamePrefix: user_
      host: ${INFRA_CONFIG_REDIS_CUREFIT_STAGE_PLATFORMS_SEGMENTATION_HOST}
      port: ${INFRA_CONFIG_REDIS_CUREFIT_STAGE_PLATFORMS_SEGMENTATION_PORT}
      clusterEnabled: true
      timeoutMs: 500
      maxSize: 100
      minIdle: 2
      maxIdle: 10
      username: ${INFRA_CONFIG_REDIS_USERNAME}
      password: ${INFRA_CONFIG_REDIS_PASSWORD}
      minEvictableIdleTimeMillis: 600000
      softMinEvictableIdleTimeMillis: 900000
      timeBetweenEvictionRunsMillis: 600000
  rashi:
    redis:
      host: ${INFRA_CONFIG_REDIS_CUREFIT_STAGE_PLATFORMS_SEGMENTATION_HOST}
      port: ${INFRA_CONFIG_REDIS_CUREFIT_STAGE_PLATFORMS_SEGMENTATION_PORT}
      clusterEnabled: true
      username: ${INFRA_CONFIG_REDIS_USERNAME}
      password: ${INFRA_CONFIG_REDIS_PASSWORD}

  services:
    segmentation-service:
      baseUrl: http://segmentation.stage.cure.fit.internal
      apiKey: 6ced07ed-b06d-448d-9f7d-0dfddddd6c3d
      client:
        sqs:
         enabled: true
  sqs:
    queue:
      name:
        usage-metrics-queue: stage-segmentation-usage-metrics

pt-service:
  url: http://personal-trainer-v2.stage.cure.fit.internal
  agent-host: cf-api

identity-service:
  url: http://identity.stage.cure.fit.internal

spring:
  sleuth:
    enabled: true
  profiles:
    region: ${REGION}
  application:
    name: curefit-api
  data:
    mongodb:
      uri: mongodb+srv://cf-api-rw:<EMAIL>/curefit-stage?appname=${APP_NAME}&retryWrites=true&w=majority&replicaSet=stage-shard-0&ssl=true&authSource=admin&compressors=zlib&localThresholdMS=0&readPreference=primaryPreferred&maxStalenessSeconds=120&minPoolSize=1&maxPoolSize=6&readPreferenceTags=nodeType:ELECTABLE
      database: curefit-stage
  datasource:
    url: ***********************************************************************
    username: ${DB_USER}
    password: ${DB_PWD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 2
    replica:
      datasource:
        url: ***********************************************************************
        username: ${DB_USER}
        password: ${DB_PWD}
    jpa:
      database-platform: org.hibernate.dialect.MySQL5Dialect
      show-sql: false
      properties:
        hibernate:
          format_sql: true
  main:
    allow-bean-definition-overriding: true

gmf:
  baseUrl: http://*************:5015
  redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 10000
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


gymfit:
  baseUrl: http://gymfit.stage.cure.fit.internal
  apiKey: eca60a46-d888-4b93-8415-ff9c6315b9ed

pms:
  baseUrl: http://pack-management-service.stage.cure.fit.internal

meal-recommendation:
  baseUrl: http://recommendation-service-api.stage.cure.fit.internal
  apiKey: J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR
  redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 10000
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


fitcash:
  baseUrl: http://fitcash.stage.cure.fit.internal/wallet/v1
  apiKey: cfapi
  clientId: cfapi
  merchantId: curefit
  redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 10000
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


cult:
  baseUrl: http://cultapi.stage.cure.fit.internal
  apiKey: "66f7jWoQ3INWMlyye81Cm5q7z6q9LJfvF3mDR6zL"
  redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 10000
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


alfred:
  baseUrl: http://alfred.stage.cure.fit.internal
  redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 10000
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


iris:
  requestQueue: stage-iris-campaign
  baseUrl: http://iris.stage.cure.fit.internal

campaign-manager:
  baseUrl: http://campaign-manager.stage.cure.fit.internal

eat-api:
  baseUrl: http://eatapi.stage.cure.fit.internal

curio:
  baseUrl: http://curio.stage.cure.fit.internal
  apiKey: "curefit-api-stage-access"

distance-service:
  baseUrl: http://distance-svc.stage.cure.fit.internal

martha:
  baseUrl: https://martha.stage.curefit.co

fms:
  baseUrl: http://fitness-management-service.stage.cure.fit.internal

offer-service-v3:
  baseUrl: http://offer-service-v3.stage.cure.fit.internal

converse:
  baseUrl: http://converse.stage.cure.fit.internal
  apiKey: converse-stage-key
  chatServiceId: ISa71e70a245b140058329d455ef865fe0
  ttl: 43200
  pnSid: CR2ea185b2ea00391642b9a6dae04c84c0

search-service:
  baseUrl: http://search.stage.cure.fit.internal

membership-service:
  url: http://membership-service.stage.cure.fit.internal
  agent-host: curefit-api

sports-api:
  url: http://sports-api.stage.cure.fit.internal
  agent-host: curefit-api

thirdPartyIntegrations:
  baseUrl: http://tpis.stage.cure.fit.internal/

smartdevice-backend:
  baseUrl: http://smartdevice-backend.stage.cure.fit.internal

external:
  media-gateway-service:
    baseUrl: http://media-gateway.stage.cure.fit.internal
    apiKey: 2180a7a2-bce7-4ba3-b56e-293222afb116
  ollivander:
    baseUrl: http://ollivander.stage.cure.fit.internal
  user-service-v1:
    baseUrl: http://user-service.stage.cure.fit.internal/v1
  user-service-v2:
    baseUrl: http://user-service-v2.stage.cure.fit.internal/v1
  albus:
    baseUrl: http://albus.stage.cure.fit.internal
    apiKey: edd27bb41e081fe13639f6151f273b0091e1b226
  albus-sugarfit:
    baseUrl: http://albus-sugarfit.stage.cure.fit.internal
  metric:
    service:
      baseUrl: http://metrics.stage.cure.fit.internal
  personalTrainer:
    baseUrl: http://personal-trainer.stage.cure.fit.internal
  diyfs:
    baseUrl: http://diy-fs.stage.cure.fit.internal
  reward-service:
    baseUrl: http://reward-service.stage.cure.fit.internal
    apiKey: 36fa3a193d737e5ad86441e422ed5a78
  bifrost:
    host: bifrost.stage.cure.fit.internal
    port: 80
    apiKey: Y3VyZWZpdC1hcGktYmlmcm9zdC1zdGFnZQ==
  cfapi-node:
    host: cfapi.stage.cure.fit.internal
    port: 80
  ehr:
    baseUrl: http://ehr.stage.cure.fit.internal
  polaris:
    baseUrl: http://polaris.stage.cure.fit.internal
  falcon-client:
    services.falcon.baseUrl: http://falcon.stage.cure.fit.internal
    port: 1433
    services.falcon.apiKey: curefit-api-stage-access
  gearvault:
    baseUrl: http://gearvault-puma.stage.cure.fit.internal
    apiKey: 226fa7acf15fa9422032cc0e14eef49321a138d66495d3f0
    apiKey2: f01f21818c1404781370cdcbaf8d66c6f882ef1124fd2c16
  transform:
    baseUrl: http://albus-transform.stage.cure.fit.internal
  shifu:
    baseUrl: http://shifu.stage.cure.fit.internal
    apiKey: cf-api-stage-access
  micro-learning:
    baseUrl: http://shifu.stage.cure.fit.internal
    apiKey: cf-api-stage-access
  meal-planner:
    baseUrl: http://shifu.stage.cure.fit.internal
    apiKey: cf-api-stage-access
  foodway:
    baseUrl: http://foodway.stage.cure.fit.internal
    apiKey: 2b162366-6444-4e42-b87c-131fec7a6dbc
  onyx-api:
    baseUrl: http://onyx-api.stage.cure.fit.internal
  maverick:
    baseUrl: http://maverick.stage.cure.fit.internal
  center-service:
    baseUrl: http://center-service.stage.cure.fit.internal
  fuse:
    baseUrl: http://fuse.stage.cure.fit.internal/fuse/v1
    v2:
      baseUrl: http://fuse.stage.cure.fit.internal/fuse/v2
  sugarfit-referral:
    baseUrl: http://sf-lms.stage.cure.fit.internal
  sugarfit-lms:
    baseUrl: http://sf-lms.stage.cure.fit.internal
  social-service:
    baseUrl: http://social-service.stage.cure.fit.internal
    apiKey: e490ea30-276d-4236-81e6-365b48a4a0a1

  quest:
    baseUrl: http://quest.stage.cure.fit.internal
    group-activity-summary-queue-name: group-activity-summary-queue-name
  ambrosia:
    baseUrl: http://ambrosia.stage.cure.fit.internal
  chs:
    baseUrl: http://chs.stage.cure.fit.internal
    restTemplate:
      socketTimeoutMs: 10000
  catalog:
    baseUrl: http://sugarfit-cms.stage.cure.fit.internal
  xandar:
    baseUrl: http://xandar.stage.cure.fit.internal
  sms:
    baseUrl: http://sugarfit-subscription-manager.stage.cure.fit.internal
  sugarfit-nest:
    baseUrl: http://nest.stage.cure.fit.internal
    restTemplate:
      socketTimeoutMs: 15000
  lsq:
    baseUrl: https://api-in21.leadsquared.com
    accessKey: u$r714428ffd70b2911851f8dfe2d906879
    secretKey: e76a836e15f1039f0c7fba036e03acaf32971a8e
  oms:
    baseUrl: http://oms.stage.cure.fit.internal
    apiKey: stage-oms-key
  housemd:
    baseUrl: http://housemd.stage.cure.fit.internal
  task-management:
    baseUrl: http://audit.stage.cure.fit.internal
  indus:
    baseUrl: http://indus.stage.cure.fit.internal
  lexicon:
    baseUrl: http://lexicon.stage.cure.fit.internal
    apiKey: db0e935f-45c5-4d57-a30a-5934232c1fdd
  cf-api-analytics:
    baseUrl: https://dataplatform-webhook.curefit.co/prod/fitness/cf_api_analytics
    apiKey: 343ed474f70fcc1450ea50ebb66eee19
    isEnable: false
  customer-query-resolver:
    baseUrl: http://customer-query-resolver.stage.cure.fit.internal/cult-rag-agent
  capi:
    accessToken: EAACofrKuJr8BOwTLsyeiquIMxh1fmKMm1OgvdZA1rbyKMlXHQCZAqZBeD2jaikHX2dl07g8SEgjnelkNBDZBanAXGX4aEYYEF0V3OGhmVtbCyU1Mxh6UM4iTkZCNyqPRp1PbGUkH2WLbNKdzZAJeDj7qVnn6ZBSiKGJY8gqECNjWLu8QEOhaOMPsaBq9ZAMozbMv9AZDZD
    pixelId: 541452556895215

bifrost:
  headers:
    orderSource: x-order-source
  CF_HOMEPAGE_CANVAS_ID: explore
  PHONEPE_HOMEPAGE_CANVAS_ID: explore
  SEARCH_SERVICE_ENABLED: true
  CDN_IMAGE_PREFIX: https://cdn-images.cure.fit/www-curefit-com/image/upload/
  CDN_VIDEO_PREFIX: https://cdn-videos.cure.fit/www-curefit-com/video/upload/
  DEFAULT_HOMEPAGE_CANVAS_ID: explore
  TRAINER_OFFER_ID: x1cyldzoz,OB5i41yJ9,cYBV8dlSQ,oEYOSbyAf,pVvb7llAv,JaB2qlb6M

catalog:
  redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 500
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  cache:
    maxSize: 10000
    ttl: 300

loggingService:
  baseUrl: http://logging-service.stage.cure.fit.internal
  redis:
    host: ${INFRA_CONFIG_REDIS_CF_API_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CF_API_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 10000
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


authorization:
  personalTrainer:
    type: CFAPI
  albus-sugarfit:
    type: CFAPI
  user:
    type: CFAPI
  transform:
    type: CFAPI

services:
  riddler:
    host: http://riddler.stage.cure.fit.internal
    port: 80
  rashi:
    baseUrl: http://rashi.stage.cure.fit.internal
  magneto:
    baseUrl: http://magneto.stage.cure.fit.internal
  mystique:
    baseUrl: http://mystique.stage.cure.fit.internal
  constello:
    baseUrl: http://constello.stage.cure.fit.internal
  report-issues:
    baseUrl: http://report-issues.stage.cure.fit.internal
    apiKey: app-stage-access
  enterprise:
    baseUrl: http://enterprise-api.stage.cure.fit.internal
    readTimeoutInSecs: 30
  cultsport:
    feedback:
      baseUrl: http://cultsport-feedback.stage.cure.fit.internal
  curio:
    baseUrl: http://curio.stage.cure.fit.internal
    apiKey: curefit-api-stage-access
  smartdevice-backend:
    baseUrl: http://smartdevice-backend.stage.cure.fit.internal
  dataLake:
    url: https://data-platform-webhook.curefit.co/stage/backend-datalake-kafka
    apiKey: f2383579-3ae0-4cb9-b802-1d0f9d35e593
    cultsport-kafka-topic: cultsport_appevents_backend
  aakash_vani:
    baseUrl: http://aakash-vani.stage.cure.fit.internal

contactSyncService:
  baseUrl: http://contact-sync.stage.cure.fit.internal
  tenant: CF_APP

cfs:
  base:
    url: http://cfs.stage.cure.fit.internal
  api:
    key: 631efad6-dddc-4b72-bf40-4f48d259e2c7

zuul:
  ignored-services: "*"
  ignoredPatterns:
    - /status
  routes:
    allPaths:
      path: /**
      url: forward:/
  strip-prefix: true

ufs:
  baseUrl: http://user-fitness-service.stage.cure.fit.internal
  apiKey: cf-api-stage-access
  timeout: 10000

assessment:
  baseUrl: http://user-fitness-service.stage.cure.fit.internal
  apiKey: cf-api-stage-access
  timeout: 10000

activity-logging:
  baseUrl: http://user-activity-service.stage.cure.fit.internal
  apiKey: cf-api-stage-access
  timeout: 10000

hercules:
  baseUrl: http://hercules.stage.cure.fit.internal
  apiKey: MFM4kAiMG0yfpW9x3q9xo6sPT4zX792W

watchmen:
  baseUrl: http://watchmen.stage.cure.fit.internal
  apiKey: curefit-api-v2-stage-access

commons:
  restTemplate:
    connectTimeoutMs: 6000
    connectionRequestTimeoutMs: 6000
    socketTimeoutMs: 9000
    pool:
      maxSingleRouteConnections: 300
      maxTotalConnections: 1200
      defaultKeepAliveTimeMs: 200000
      idleConnectionWaitTimeMs: 200000

slack:
  clientId: "44746560048.1848218887285"
  clientSecret: "56ea7c1d2f5a997f6d19c0b3962b759c"

whoami: apsouth1_stage

tread:
  baseUrl: https://stagingcert.tread.fit
  apiKey:
    key: Token
    value: 1Acc-b99a-793a0bnQzd58-9a9eLme9

userId:
  mdcToken: uID
headersInterceptor:
  enabled: false

configstore:
  enabled: true
  apiKey: cf-api-stage-access
  url: http://config-store.stage.cure.fit.internal/

cloud:
  aws:
    end-point: https://sqs.ap-south-1.amazonaws.com/035243212545/

queue:
  activity: "stage-community-user-activity"

constants:
  cult:
    truecallerExperimentId: 605
    truecallerExperimentBucketId: 2

shortloop:
  enabled: true
  environment: staging
  maskHeaders: cookie,x-auth


sos-config:
  number: "+91-9958143475"

steps-update-fitness-report:
  queueUrl: ${UPDATE_FITNESS_REPORT_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-update-fitness-report}


moengage:
  endpoint: https://api-03.moengage.com
  appID: ZHPJV4VIXMM4FIWSFT4UEO0F_DEBUG
  username: ZHPJV4VIXMM4FIWSFT4UEO0F_DEBUG
  password: pZkdW-OzNjSrBDV-UTxCVDVm
  inboundQueue:
    url: https://sqs.ap-south-1.amazonaws.com/035243212545/stage-cultsport-moengage-ingestion-queue

user-activity-service:
  url: http://user-activity-service.stage.cure.fit.internal

com:
  curefit:
    maestro:
      baseUrl: http://maestro.stage.cure.fit.internal
      apiKey: maestro-stage-access

ticketingSystem:
  baseUrl: http://ticketing-system.stage.cure.fit.internal
  user: <EMAIL>

heimdallMongoAudit:
  baseUrl: http://auditlog.stage.cure.fit.internal
  apiKey: oiasjlkmskLasfkQsdfsdfioasjdkashflakswbnaCchJaz