server:
  connection-timeout: 10s
  tomcat:
    max-threads: 10
    min-spare-threads: 10
  jetty:
    threads:
      max: 100

rollbar:
  accessToken: alsknas
  disabled: true

sentry:
  disabled: true
  dsn: ${SENTRY_DSN:https://<EMAIL>/5186323}
  stacktrace:
    app:
      packages: com.curefit
  samplerate: 1.0
  mdctags: rID,uID,tID,sla_owner,pageId,widgetId,aktoRequest,securityTest
  uncaught:
    handler:
      enabled: true
  send-default-pii: true
  traces-sample-rate: 0.0

rashi:
  sns:
    topicArn: arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events

redis:
  default:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    minIdle: 2
    maxIdle: 5
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    minEvictableIdleTimeMillis: 60000
    softMinEvictableIdleTimeMillis: 90000
    timeBetweenEvictionRunsMillis: 30000
  session-transient:
    host: localhost
    port: 6399
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    minIdle: 2
    maxIdle: 5
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    minEvictableIdleTimeMillis: 60000
    softMinEvictableIdleTimeMillis: 90000
    timeBetweenEvictionRunsMillis: 30000
  cfapi:
    host: localhost
    port: 6374
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    minIdle: 5
    maxIdle: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    minEvictableIdleTimeMillis: 60000
    softMinEvictableIdleTimeMillis: 90000
    timeBetweenEvictionRunsMillis: 30000
  hamlet:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  riddler-cache:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  eatfit-cache:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  gear:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  cyclops:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  social-service:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    redis:
      host: localhost
      port: 6379
      clusterEnabled: false
      timeoutMs: 4500
      maxSize: 20
      username: ${INFRA_CONFIG_REDIS_USERNAME}
      password: ${INFRA_CONFIG_REDIS_PASSWORD}

  cult-redis:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


#safety number
sos-config:
  number: "+91-9958143475"

pt-service:
  url: http://localhost:48000
  agent-host: cf-api

identity-service:
  url: http://localhost:39000

user-activity-service:
  url: http://localhost:5050

curefit:
  pageIds:
    cult-live: LIVE
    home: PLAN
  segmentation:
    segmentIds:
      cult-live: b896fa50-faf5-4358-af9e-2481dc28574a
    redis:
      userSetNamePrefix: user_
      host: localhost
      port: 6379
      clusterEnabled: false
      timeoutMs: 4500
      maxSize: 20
      minIdle: 2
      maxIdle: 10
      username: ${INFRA_CONFIG_REDIS_USERNAME}
      password: ${INFRA_CONFIG_REDIS_PASSWORD}
      minEvictableIdleTimeMillis: 600000
      softMinEvictableIdleTimeMillis: 900000
      timeBetweenEvictionRunsMillis: 600000
  rashi:
    redis:
      host: localhost
      port: 6366
      clusterEnabled: false
      username: ${INFRA_CONFIG_REDIS_USERNAME}
      password: ${INFRA_CONFIG_REDIS_PASSWORD}

  services:
    segmentation-service:
      baseUrl: http://localhost:8081
      apiKey: 6ced07ed-b06d-448d-9f7d-0dfddddd6c3d
      client:
        sqs:
          enabled: true
  sqs:
    queue:
      name:
        usage-metrics-queue: stage-segmentation-usage-metrics

spring:
  sleuth:
    enabled: true
  profiles:
    region: ${REGION}
  application:
    name: curefit-api
  data:
    mongodb:
      uri: mongodb://cf-api-rw:<EMAIL>,stage-shard-00-01-tfwfr.mongodb.net,stage-shard-00-02-tfwfr.mongodb.net/curefit-stage?appname=${APP_NAME}&retryWrites=true&w=majority&replicaSet=stage-shard-0&ssl=true&authSource=admin&compressors=zlib&localThresholdMS=0&readPreference=primaryPreferred&readPreferenceTags=nodeType:ELECTABLE
      database: curefit-stage
  datasource:
    url: ************************************
    username: ${DB_USER}
    password: ${DB_PWD}
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQL5Dialect
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  main:
    allow-bean-definition-overriding: true

fitcash:
  baseUrl: http://localhost:5115/wallet/v1
  apiKey: cfapi
  clientId: cfapi
  merchantId: curefit
  redis:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


gmf:
  baseUrl: http://localhost:5015
  redis:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 10000
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

gymfit:
  baseUrl: http://localhost:5020
  apiKey: eca60a46-d888-4b93-8415-ff9c6315b9ed

pms:
  baseUrl: http://localhost:5050

meal-recommendation:
  baseUrl: http://localhost:8013
  apiKey: J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR
  redis:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 10000
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


alfred:
  baseUrl: http://localhost:3011
  redis:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


cult:
  baseUrl: http://localhost:5005
  apiKey: "66f7jWoQ3INWMlyye81Cm5q7z6q9LJfvF3mDR6zL"
  redis:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


iris:
  requestQueue: stage-iris-campaign
  baseUrl: http://localhost:3010

campaign-manager:
  baseUrl: http://localhost:7890

eat-api:
  baseUrl: http://localhost:6009

curio:
  baseUrl: http://localhost:8200
  apiKey: "curefit-api-stage-access"

distance-service:
  baseUrl: http://localhost:5201

martha:
  baseUrl: https://martha.stage.curefit.co

fms:
  baseUrl: http://fitness-management-service.stage.cure.fit.internal

converse:
  baseUrl: http://localhost:5004
  apiKey: converse-stage-key
  chatServiceId: ISa71e70a245b140058329d455ef865fe0
  ttl: 43200
  pnSid: CR2ea185b2ea00391642b9a6dae04c84c0

thirdPartyIntegrations:
  baseUrl: http://tpis.stage.cure.fit.internal/

smartdevice-backend:
  baseUrl: http://localhost:8083

cfs:
  base:
    url: https://cfs.stage.curefit.co
  api:
    key: 631efad6-dddc-4b72-bf40-4f48d259e2c7

membership-service:
  url: http://localhost:8080
  agent-host: curefit-api

sports-api:
  url: http://localhost:4000
  agent-host: curefit-api

external:
  center-service:
    baseUrl: http://localhost:5090
  media-gateway-service:
    baseUrl: http://localhost:8091
    apiKey: 2180a7a2-bce7-4ba3-b56e-293222afb116
  user-service-v1:
    baseUrl: http://localhost:5001/v1
  user-service-v2:
    baseUrl: http://localhost:8693/v1
  albus:
    baseUrl: http://localhost:5069
    apiKey: edd27bb41e081fe13639f6151f273b0091e1b226
  personalTrainer:
    baseUrl: http://personal-trainer.stage.cure.fit.internal
  albus-sugarfit:
    baseUrl: https://albus-sugarfit.stage.curefit.co
  ollivander:
    baseUrl: http://localhost:36000
  metric:
    service:
      baseUrl: http://localhost:5002
  diyfs:
    baseUrl: http://localhost:5010
  falcon-client:
    services.falcon.baseUrl: http://localhost:1433
    port: 1433
    services.falcon.apiKey: curefit-api-stage-access
  reward-service:
    baseUrl: http://localhost:3772
    apiKey: 36fa3a193d737e5ad86441e422ed5a78
  bifrost:
    host: localhost
    port: 8001
    apiKey: Y3VyZWZpdC1hcGktYmlmcm9zdC1zdGFnZQ==
  cfapi-node:
    host: localhost
    port: 3000
  ehr:
    baseUrl: http://localhost:38000
  polaris:
    baseUrl: http://localhost:5007
  transform:
    baseUrl: http://localhost:20000
  shifu:
    baseUrl: http://localhost:20010
    apiKey: cf-api-stage-access
    tenant: TRANSFORM
  micro-learning:
    baseUrl: http://localhost:20010
    apiKey: cf-api-stage-access
  meal-planner:
    baseUrl: http://localhost:20010
    apiKey: cf-api-stage-access
  gearvault:
    baseUrl: http://localhost:3000
    apiKey: 226fa7acf15fa9422032cc0e14eef49321a138d66495d3f0
    apiKey2: f01f21818c1404781370cdcbaf8d66c6f882ef1124fd2c16
  foodway:
    baseUrl: http://localhost:9000
    apiKey: 2b162366-6444-4e42-b87c-131fec7a6dbc
  metrics-service:
    baseUrl: http://localhost:7556
  onyx-api:
    baseUrl: http://localhost:5065
  maverick:
    baseUrl: http://localhost:5002
  fuse:
    baseUrl: http://localhost:25000/fuse/v1
    v2:
      baseUrl: http://localhost:25000/fuse/v2
  sugarfit-referral:
    baseUrl: http://localhost:8080
  sugarfit-lms:
    baseUrl: http://localhost:8080
  social-service:
    baseUrl: http://localhost:8080
    apiKey: abdcefghijklmnopqurstuvwxyz

  quest:
    baseUrl: http://localhost:5009
    group-activity-summary-queue-name: group-activity-summary-queue-name
  ambrosia:
    baseUrl: https://ambrosia.stage.curefit.co
  chs:
    baseUrl: https://chs.stage.curefit.co
    restTemplate:
      socketTimeoutMs: 10000
  catalog:
    baseUrl: https://sugarfit-cms.stage.curefit.co
  xandar:
    baseUrl: http://localhost:8080
  sms:
    baseUrl: https://sugarfit-subscription-manager.stage.curefit.co
  sugarfit-nest:
    baseUrl: https://nest.stage.curefit.co
    restTemplate:
      socketTimeoutMs: 15000
  lsq:
    baseUrl: https://api-in21.leadsquared.com
    accessKey: u$r714428ffd70b2911851f8dfe2d906879
    secretKey: e76a836e15f1039f0c7fba036e03acaf32971a8e
  oms:
    baseUrl: http://oms.stage.cure.fit.internal
    apiKey: stage-oms-key
  housemd:
    baseUrl: http://localhost:8080
  indus:
    baseUrl: https://indus.stage.curefit.co
  lexicon:
    baseUrl: http://localhost:6001
    apiKey: db0e935f-45c5-4d57-a30a-5934232c1fdd
  task-management:
    baseUrl: http://audit.stage.cure.fit.internal
  cf-api-analytics:
    baseUrl: https://localhost:8080
    apiKey: apikey
    isEnable: false
  capi:
    accessToken: EAACofrKuJr8BOwTLsyeiquIMxh1fmKMm1OgvdZA1rbyKMlXHQCZAqZBeD2jaikHX2dl07g8SEgjnelkNBDZBanAXGX4aEYYEF0V3OGhmVtbCyU1Mxh6UM4iTkZCNyqPRp1PbGUkH2WLbNKdzZAJeDj7qVnn6ZBSiKGJY8gqECNjWLu8QEOhaOMPsaBq9ZAMozbMv9AZDZD
    pixelId: 541452556895215
  customer-query-resolver:
    baseUrl: http://customer-query-resolver.stage.cure.fit.internal/cult-rag-agent

catalog:
  redis:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 500
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  cache:
    maxSize: 5000
    ttl: 300

bifrost:
  headers:
    orderSource: x-order-source
  CF_HOMEPAGE_CANVAS_ID: explore
  PHONEPE_HOMEPAGE_CANVAS_ID: explore
  SEARCH_SERVICE_ENABLED: true
  CDN_IMAGE_PREFIX: https://cdn-images.cure.fit/www-curefit-com/image/upload/
  CDN_VIDEO_PREFIX: https://cdn-videos.cure.fit/www-curefit-com/video/upload/
  DEFAULT_HOMEPAGE_CANVAS_ID: explore
  TRAINER_OFFER_ID: x1cyldzoz,OB5i41yJ9,cYBV8dlSQ,oEYOSbyAf,pVvb7llAv,JaB2qlb6M

offer-service-v3:
  baseUrl: http://localhost:7887

search-service:
  baseUrl: http://localhost:8082

loggingService:
  baseUrl: http://localhost:5070
  redis:
    host: localhost
    port: 6379
    clusterEnabled: false
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


authorization:
  personalTrainer:
    type: CFAPI
  albus-sugarfit:
    type: CFAPI
  user:
    type: CFAPI
  transform:
    type: CFAPI

services:
  riddler:
    host: http://localhost
    port: 7777
  rashi:
    baseUrl: http://localhost:8087
  magneto:
    baseUrl: http://localhost:1434
  mystique:
    baseUrl: http://localhost:8080
  constello:
    baseUrl: http://localhost:5225
  report-issues:
    baseUrl: http://localhost:3016
    apiKey: app-stage-access
  enterprise:
    baseUrl: http://localhost:3016
    readTimeoutInSecs: 30
  cultsport:
    feedback:
      baseUrl: http://localhost:1729
  curio:
    baseUrl: http://localhost:6068
    apiKey: curefit-api-stage-access
  smartdevice-backend:
    baseUrl: http://localhost:8083
  dataLake:
    url: https://data-platform-webhook.curefit.co/stage/backend-datalake-kafka
    apiKey: f2383579-3ae0-4cb9-b802-1d0f9d35e593
    cultsport-kafka-topic: cultsport_appevents_backend
  aakash_vani:
    baseUrl: http://aakash-vani.stage.cure.fit.internal

contactSyncService:
  baseUrl: http://localhost:5678
  tenant: CF_APP

zuul:
  ignored-services: "*"
  ignoredPatterns:
    - /status
  routes:
    allPaths:
      path: /**
      url: forward:/
  strip-prefix: true
  SendResponseFilter:
    post:
      disable: true

ufs:
  baseUrl: http://localhost:5030
  apiKey: cf-api-stage-access
  timeout: 10000

assessment:
  baseUrl: http://localhost:5030
  apiKey: cf-api-stage-access
  timeout: 10000

activity-logging:
  baseUrl: http://localhost:5050
  apiKey: cf-api-stage-access
  timeout: 10000

commons:
  restTemplate:
    connectTimeoutMs: 6000
    connectionRequestTimeoutMs: 6000
    socketTimeoutMs: 9000
    pool:
      maxSingleRouteConnections: 300
      maxTotalConnections: 1200
      defaultKeepAliveTimeMs: 200000
      idleConnectionWaitTimeMs: 200000

hercules:
  apiKey: MFM4kAiMG0yfpW9x3q9xo6sPT4zX792W
  baseUrl: http://localhost:9009

watchmen:
  baseUrl: http://localhost:5000
  apiKey: curefit-api-v2-stage-access

payment-service:
  baseUrl: http://payment.stage.cure.fit.internal
  apiKey: N8fHVfInFmq5ij1cQLywlvsGYZaTPgkz

tata:
  segmentation:
    potentialTataUser: TATA_POTENTIAL_TATA_USER
    neupassUser: TATA_NEUPASS_USER
    neupassNotActivated: TATA_NEUPASS_NOT_ACTIVATED
    neupassConsentPending: TATA_NEUPASS_CONSENT_PENDING
    tataUser: TATA_TATA_USER

slack:
  clientId: "44746560048.1848218887285"
  clientSecret: "56ea7c1d2f5a997f6d19c0b3962b759c"

whoami: apsouth1_local

tread:
  baseUrl: https://stagingcert.tread.fit
  apiKey:
    key: Token
    value: 1Acc-b99a-793a0bnQzd58-9a9eLme9

configstore:
  enabled: true
  apiKey: cf-api-stage-access
  url: https://config-store.stage.curefit.co/

cloud:
  aws:
    end-point: https://sqs.ap-south-1.amazonaws.com/035243212545/

queue:
  activity: "stage-community-user-activity"

constants:
  cult:
    truecallerExperimentId: 605
    truecallerExperimentBucketId: 2

shortloop:
  environment: dev

steps-update-fitness-report:
  queueUrl: ${UPDATE_FITNESS_REPORT_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-update-fitness-report}

moengage:
  endpoint: https://api-03.moengage.com
  appID: ZHPJV4VIXMM4FIWSFT4UEO0F_DEBUG
  username: ZHPJV4VIXMM4FIWSFT4UEO0F_DEBUG
  password: pZkdW-OzNjSrBDV-UTxCVDVm
  inboundQueue:
    url: a

com:
  curefit:
      maestro:
        baseUrl: http://maestro.stage.cure.fit.internal
        apiKey: maestro-stage-access

ticketingSystem:
  baseUrl: http://ticketing-system.stage.cure.fit.internal
  user: <EMAIL>

heimdallMongoAudit:
  baseUrl: http://auditlog.stage.cure.fit.internal
  apiKey: oiasjlkmskLasfkQsdfsdfioasjdkashflakswbnaCchJaz