server:
  jetty:
    threads:
      max: 50

logfile:
  path: ${LOGFILE_PATH:/logs/curefit-api-v2}

rollbar:
  accessToken: alsknas
  disabled: true

sentry:
  disabled: false
  dsn: ${SENTRY_DSN:https://<EMAIL>/5186323}
  stacktrace:
    app:
      packages: com.curefit
  samplerate: 1.0
  mdctags: rID,uID,tID,sla_owner,pageId,widgetId,aktoRequest,securityTest
  uncaught:
    handler:
      enabled: true
  send-default-pii: true
  traces-sample-rate: 0.0

rashi:
  sns:
    topicArn: arn:aws:sns:${AWS_REGION}:${AWS_ACCOUNT_ID}:production-rashi-user-events

redis:
  default:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_PROD_DEFAULT_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_PROD_DEFAULT_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 500
    minIdle: 20
    maxIdle: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    minEvictableIdleTimeMillis: 60000
    softMinEvictableIdleTimeMillis: 90000
    timeBetweenEvictionRunsMillis: 30000
  session-transient:
    host: ${INFRA_CONFIG_REDIS_PRODUCTION_SESSION_TRANSIENT_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_PRODUCTION_SESSION_TRANSIENT_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 500
    minIdle: 20
    maxIdle: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    minEvictableIdleTimeMillis: 60000
    softMinEvictableIdleTimeMillis: 90000
    timeBetweenEvictionRunsMillis: 30000
  cfapi:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 200
    minIdle: 20
    maxIdle: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    minEvictableIdleTimeMillis: 60000
    softMinEvictableIdleTimeMillis: 90000
    timeBetweenEvictionRunsMillis: 30000
  hamlet:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 150
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  riddler-cache:
    host: ${INFRA_CONFIG_REDIS_RIDDLER_SERVICE_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_RIDDLER_SERVICE_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  eatfit-cache:
    host: ${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_HOST}
    port: ${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  gear:
    host: ${INFRA_CONFIG_REDIS_FITSTORE_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_FITSTORE_CACHE_PORT}
    clusterEnabled: false
    timeoutMs: 1000
    maxSize: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  cyclops:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  social-service:
    host: ${INFRA_CONFIG_REDIS_CULT_PROD_SOCIAL_SERVICE_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CULT_PROD_SOCIAL_SERVICE_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 4500
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    redis:
      host: ${INFRA_CONFIG_REDIS_CULT_PROD_SOCIAL_SERVICE_CACHE_HOST}
      port: ${INFRA_CONFIG_REDIS_CULT_PROD_SOCIAL_SERVICE_CACHE_PORT}
      clusterEnabled: true
      timeoutMs: 4500
      maxSize: 20
      username: ${INFRA_CONFIG_REDIS_USERNAME}
      password: ${INFRA_CONFIG_REDIS_PASSWORD}

  cult-redis:
    host: ${INFRA_CONFIG_REDIS_CULT_PROD_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CULT_PROD_CACHE_PORT}
    clusterEnabled: false
    timeoutMs: 5000
    maxSize: 20
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


#safety number
sos-config:
  number: "18001024100"

curefit:
  pageIds:
    cult-live: LIVE
    home: PLAN
  segmentation:
    segmentIds:
      cult-live: ab1f0208-66fb-4022-a679-349bec9e6f4b
    redis:
      userSetNamePrefix: user_
      host: ${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_HOST}
      port: ${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_PORT}
      clusterEnabled: true
      timeoutMs: 500
      maxSize: 200
      minIdle: 10
      maxIdle: 50
      username: ${INFRA_CONFIG_REDIS_USERNAME}
      password: ${INFRA_CONFIG_REDIS_PASSWORD}
      minEvictableIdleTimeMillis: 600000
      softMinEvictableIdleTimeMillis: 900000
      timeBetweenEvictionRunsMillis: 600000
  rashi:
    redis:
      host: ${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_HOST}
      port: ${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_PORT}
      clusterEnabled: true
      maxSize: 200
      username: ${INFRA_CONFIG_REDIS_USERNAME}
      password: ${INFRA_CONFIG_REDIS_PASSWORD}

  services:
    segmentation-service:
      baseUrl: http://segmentation.alpha.cure.fit.internal
      apiKey: a83384a9-b29b-4c69-b337-b6fff128e92d
      client:
        sqs:
          enabled: true
  sqs:
    queue:
      name:
        usage-metrics-queue: production-segmentation-usage-metrics

spring:
  sleuth:
    enabled: true
  profiles:
    region: ${REGION}
  application:
    name: curefit-api
  data:
    mongodb:
      uri: mongodb+srv://cf-api-rw:<EMAIL>/curefit-prod?appname=${APP_NAME}&retryWrites=true&w=majority&replicaSet=prod-shard-0&ssl=true&authSource=admin&connectTimeoutMS=10000&compressors=zlib&localThresholdMS=0&readPreference=primaryPreferred&maxStalenessSeconds=120&minPoolSize=0&maxPoolSize=2&readPreferenceTags=nodeType:ELECTABLE
      database: curefit-prod
  datasource:
    url: ${DB_URL}
    username: ${DB_USER}
    password: ${DB_PWD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 2
    replica:
      datasource:
        url: ${DB_URL}
        username: ${DB_USER}
        password: ${DB_PWD}
    jpa:
      database-platform: org.hibernate.dialect.MySQL5Dialect
      show-sql: false
      properties:
        hibernate:
          format_sql: true
  main:
    allow-bean-definition-overriding: true

fitcash:
  baseUrl: http://fitcash.production.cure.fit.internal/wallet/v1
  apiKey: f0505710ca1f47da804a77a60b91e640
  clientId: cfapi
  merchantId: curefit
  redis:
    host: ${INFRA_CONFIG_REDIS_OFFER_SERVICE_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_OFFER_SERVICE_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


curio:
  baseUrl: http://curio.production.cure.fit.internal
  apiKey: "bb7d8e02-48e0-4056-84d6-6d78862f8d7e"

distance-service:
  baseUrl: http://distance-svc.alpha.cure.fit.internal

martha:
  baseUrl: https://martha.alpha.curefit.co

fms:
  baseUrl: http://fitness-management-service.alpha.cure.fit.internal

gmf:
  baseUrl: http://internal-gmf-api-prod-lb-846164103.${AWS_REGION}.elb.amazonaws.com
  redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


gymfit:
  baseUrl: http://gymfit.alpha.cure.fit.internal
  apiKey: eca60a46-d888-4b93-8415-ff9c6315b9ed

pms:
  baseUrl: http://pack-management-service.alpha.cure.fit.internal

meal-recommendation:
  baseUrl: http://recommendation-service-api.production.cure.fit.internal
  apiKey: J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR
  redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}


cult:
  baseUrl: http://cultapi.alpha.cure.fit.internal
  apiKey: "BxyPmucncAopDeOieJe7j0ELCbX809DL6NuwSCYK"
  redis:
    host: ${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_HOST}
    port: ${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}
    workoutCacheTTL: 600
    unboundCacheTTL: 600

alfred:
  baseUrl: http://alfred.alpha.cure.fit.internal
  redis:
    host: curefit.y66lea.0001.aps1.cache.amazonaws.com
    port: 6379
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 50
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

iris:
  requestQueue: production-iris-campaign
  baseUrl: http://iris.alpha.cure.fit.internal

campaign-manager:
  baseUrl: http://campaign-manager.alpha.cure.fit.internal

eat-api:
  baseUrl: http://eatapi.alpha.cure.fit.internal

search-service:
  baseUrl: http://search.alpha.cure.fit.internal

payment-service:
  baseUrl: http://payment.alpha.cure.fit.internal
  apiKey: N8fHVfInFmq5ij1cQLywlvsGYZaTPgkz

thirdPartyIntegrations:
  baseUrl: http://tpis.alpha.cure.fit.internal

tata:
  segmentation:
    potentialTataUser: TATA_POTENTIAL_TATA_USERS
    neupassUser: TATA_NEUPASS_USERS
    neupassNotActivated: TATA_NEUPASS_NOT_ACTIVATED_CLONE_1
    neupassConsentPending: TATA_NEUPASS_CONSENT_PENDING
    tataUser: TATA_TATA_USER

offer-service-v3:
  baseUrl: http://offer-service-v3.alpha.cure.fit.internal
converse:
  baseUrl: http://converse.production.cure.fit.internal
  chatServiceId: ISa71e70a245b140058329d455ef865fe0
  apiKey: converse-stage-key
  ttl: 43200
  pnSid: CR2ea185b2ea00391642b9a6dae04c84c0

membership-service:
  url: http://membership-service.alpha.cure.fit.internal
  agent-host: curefit-api
user-activity-service:
  url: http://user-activity-service.alpha.cure.fit.internal
sports-api:
  url: http://sports-api.alpha.cure.fit.internal
  agent-host: curefit-api
pt-service:
  url: http://personal-trainer-v2.alpha.cure.fit.internal
  agent-host: curefit-api

identity-service:
  url: http://identity.production.cure.fit.internal

smartdevice-backend:
  baseUrl: http://smartdevice-backend.alpha.cure.fit.internal

external:
  media-gateway-service:
    baseUrl: http://media-gateway.production.cure.fit.internal
    apiKey: fc372ab6-b95c-4ad4-945c-00c3f12e9fb5
  ollivander:
    baseUrl: http://ollivander.production.cure.fit.internal
  user-service-v1:
    baseUrl: http://user-service.alpha.cure.fit.internal/v1
  user-service-v2:
    baseUrl: http://user-service-v2.alpha.cure.fit.internal/v1
  albus:
    baseUrl: http://albus.alpha.cure.fit.internal
    apiKey: 057402293966e780645113b71795984a4ac4c710
  albus-sugarfit:
    baseUrl: http://albus-sugarfit.alpha.cure.fit.internal
  metric:
    service:
      baseUrl: http://metrics.production.cure.fit.internal
  personalTrainer:
    baseUrl: http://personal-trainer.alpha.cure.fit.internal
  diyfs:
    baseUrl: http://diy-fs.alpha.cure.fit.internal
  falcon-client:
    services.falcon.baseUrl: http://falcon.alpha.cure.fit.internal
    port: 1433
    services.falcon.apiKey: 074f11fd-2be9-48fb-9c7f-67675c159627
  reward-service:
    baseUrl: http://reward-service.production.cure.fit.internal
    apiKey: 0aa1ea9a5a04b78d4581dd6d17742627
  bifrost:
    host: bifrost.alpha.cure.fit.internal
    port: 80
    apiKey: Y3VyZWZpdC1hcGktYmlmcm9zdC1hbHBoYQ==
  cfapi-node:
    host: cfapi.alpha.cure.fit.internal
    port: 80
  ehr:
    baseUrl: http://ehr.production.cure.fit.internal
  polaris:
    baseUrl: http://polaris.production.cure.fit.internal
  gearvault:
    baseUrl: http://gearvault-puma.alpha.cure.fit.internal
    apiKey: 226fa7acf15fa9422032cc0e14eef49321a138d66495d3f0
    apiKey2: 8135582273c5acc63ace21746d2e1e0f6329680ec34516df
  transform:
    baseUrl: http://albus-transform.alpha.cure.fit.internal
  shifu:
    baseUrl: http://shifu.alpha.cure.fit.internal
    apiKey: 35d60e7d-49c2-4188-bdb6-402314cd00f9
  micro-learning:
    baseUrl: http://shifu.alpha.cure.fit.internal
    apiKey: 35d60e7d-49c2-4188-bdb6-402314cd00f9
  meal-planner:
      baseUrl: http://shifu.alpha.cure.fit.internal
      apiKey: 35d60e7d-49c2-4188-bdb6-402314cd00f9
  maverick:
    baseUrl: http://maverick.production.cure.fit.internal
  onyx-api:
    baseUrl: http://onyx-api.production.cure.fit.internal
  foodway:
    baseUrl: http://foodway.alpha.cure.fit.internal
    apiKey: bd8c6c31-3a3e-4efb-bf38-29793f3eed52
  center-service:
    baseUrl: http://center-service.production.cure.fit.internal
  fuse:
    baseUrl: http://fuse.production.cure.fit.internal/fuse/v1
    v2:
      baseUrl: http://fuse.production.cure.fit.internal/fuse/v2
  sugarfit-referral:
    baseUrl: http://lms.alpha.cure.fit.internal
  sugarfit-lms:
    baseUrl: http://lms.alpha.cure.fit.internal
  social-service:
    baseUrl: http://social-service.alpha.cure.fit.internal
    apiKey: e490ea30-276d-4236-81e6-365b48a4a0a1
  ambrosia:
    baseUrl: http://ambrosia.alpha.cure.fit.internal

  chs:
    baseUrl: http://chs.alpha.cure.fit.internal
    restTemplate:
      socketTimeoutMs: 10000
  quest:
    baseUrl: http://quest.alpha.cure.fit.internal
    group-activity-summary-queue-name: group-activity-summary-queue-name
  catalog:
    baseUrl: http://sugarfit-cms.alpha.cure.fit.internal
  xandar:
    baseUrl: http://xandar.alpha.cure.fit.internal
  sms:
    baseUrl: http://sugarfit-subscription-manager.alpha.cure.fit.internal
  sugarfit-nest:
    baseUrl: http://nest.alpha.cure.fit.internal
    restTemplate:
      socketTimeoutMs: 15000
  lsq:
    baseUrl: https://api-in21.leadsquared.com
    accessKey: u$r714428ffd70b2911851f8dfe2d906879
    secretKey: e76a836e15f1039f0c7fba036e03acaf32971a8e
  oms:
    baseUrl: http://oms.alpha.cure.fit.internal
    apiKey: a6f01546-96ca-4d0e-86d6-51694fba6c73
  housemd:
    baseUrl: http://housemd.production.cure.fit.internal
  task-management:
    baseUrl: http://audit.alpha.cure.fit.internal
  indus:
    baseUrl: http://indus.production.cure.fit.internal
  lexicon:
    baseUrl: http://lexicon.alpha.cure.fit.internal
    apiKey: db0e935f-45c5-4d57-a30a-5934232c1fdd
  customer-query-resolver:
    baseUrl: http://customer-query-resolver.alpha.cure.fit.internal/cult-rag-agent
  cf-api-analytics:
    baseUrl: https://dataplatform-webhook.curefit.co/prod/fitness/cf_api_analytics
    apiKey: 343ed474f70fcc1450ea50ebb66eee19
    isEnable: true
  capi:
    accessToken: EAACofrKuJr8BOwTLsyeiquIMxh1fmKMm1OgvdZA1rbyKMlXHQCZAqZBeD2jaikHX2dl07g8SEgjnelkNBDZBanAXGX4aEYYEF0V3OGhmVtbCyU1Mxh6UM4iTkZCNyqPRp1PbGUkH2WLbNKdzZAJeDj7qVnn6ZBSiKGJY8gqECNjWLu8QEOhaOMPsaBq9ZAMozbMv9AZDZD
    pixelId: 541452556895215

bifrost:
  headers:
    orderSource: x-order-source
  CF_HOMEPAGE_CANVAS_ID: fs-explore
  PHONEPE_HOMEPAGE_CANVAS_ID: sunset
  SEARCH_SERVICE_ENABLED: true
  CDN_IMAGE_PREFIX: https://cdn-images.cure.fit/www-curefit-com/image/upload/
  CDN_VIDEO_PREFIX: https://cdn-videos.cure.fit/www-curefit-com/video/upload/
  DEFAULT_HOMEPAGE_CANVAS_ID: fs-explore
  TRAINER_OFFER_ID: x1cyldzoz,OB5i41yJ9,cYBV8dlSQ,oEYOSbyAf,pVvb7llAv,JaB2qlb6M

catalog:
  redis:
    host: ${INFRA_CONFIG_REDIS_CATALOG_SERVICE_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CATALOG_SERVICE_CACHE_PORT}
    clusterEnabled: false
    timeoutMs: 500
    maxSize: 500
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

  cache:
    maxSize: 15000
    ttl: 300

loggingService:
  baseUrl: http://logging-service.alpha.cure.fit.internal
  redis:
    host: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}
    port: ${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}
    clusterEnabled: true
    timeoutMs: 500
    maxSize: 150
    username: ${INFRA_CONFIG_REDIS_USERNAME}
    password: ${INFRA_CONFIG_REDIS_PASSWORD}

authorization:
  personalTrainer:
    type: CFAPI
  albus-sugarfit:
    type: CFAPI
  user:
    type: CFAPI
  transform:
    type: CFAPI

services:
  riddler:
    host: http://riddler.production.cure.fit.internal
    port: 80
  rashi:
    baseUrl: http://rashi.alpha.cure.fit.internal
  magneto:
    baseUrl: http://magneto.alpha.cure.fit.internal
  mystique:
    baseUrl: http://mystique.alpha.cure.fit.internal
  constello:
    baseUrl: http://constello.production.cure.fit.internal
  report-issues:
    baseUrl: http://report-issues.alpha.cure.fit.internal
    apiKey: gauzietheiTua1fu5os0Eehoh0eigheejahcoh1i
  enterprise:
    baseUrl: http://enterprise-api.alpha.cure.fit.internal
    readTimeoutInSecs: 30
  cultsport:
    feedback:
      baseUrl: http://cultsport-feedback.alpha.cure.fit.internal
  curio:
    baseUrl: http://curio.alpha.cure.fit.internal
    apiKey: bb7d8e02-48e0-4056-84d6-6d78862f8d7e
  smartdevice-backend:
    baseUrl: http://smartdevice-backend.alpha.cure.fit.internal
  dataLake:
    url: https://data-platform-webhook.curefit.co/production/backend-datalake-kafka
    apiKey: d1607bbb-8838-4d36-86d7-3f61bc736669
    cultsport-kafka-topic: cultsport_appevents_backend
  aakash_vani:
    baseUrl: http://aakash-vani.alpha.cure.fit.internal

contactSyncService:
  baseUrl: http://contact-sync.alpha.cure.fit.internal
  tenant: CF_APP

zuul:
  ignored-services: "*"
  ignoredPatterns:
    - /status
  routes:
    allPaths:
      path: /**
      url: forward:/
  strip-prefix: true

hercules:
  baseUrl: http://hercules.alpha.cure.fit.internal
  apiKey: PjMlHnuk6w6O1WIqvP

watchmen:
  baseUrl: http://watchmen.alpha.cure.fit.internal
  apiKey: 2db654f9-c3d8-43b8-be69-46e96ee781b6

ufs:
  baseUrl: http://user-fitness-service.alpha.cure.fit.internal
  apiKey: random-replace-once-provided
  timeout: 10000

assessment:
  baseUrl: http://user-fitness-service.alpha.cure.fit.internal
  apiKey: random-replace-once-provided
  timeout: 10000

activity-logging:
  baseUrl: http://user-activity-service.alpha.cure.fit.internal
  apiKey: cf-api-stage-access
  timeout: 10000

cfs:
  base:
    url: http://cfs.production.cure.fit.internal
  api:
    key: 631efad6-dddc-4b72-bf40-4f48d259e2c7

commons:
  restTemplate:
    connectTimeoutMs: 6000
    connectionRequestTimeoutMs: 6000
    socketTimeoutMs: 9000
    pool:
      maxSingleRouteConnections: 600
      maxTotalConnections: 2000
      defaultKeepAliveTimeMs: 200000
      idleConnectionWaitTimeMs: 200000

resttemplate:
  read:
    timeout: 12000 # milliseconds

slack:
  clientId: "***********.*************"
  clientSecret: "56ea7c1d2f5a997f6d19c0b3962b759c"

whoami: apsouth1_alpha

tread:
  baseUrl: https://appapi.tread.fit
  apiKey:
    key: Token
    value: 2sEv-bFv0-819aPlnQzMa1-9a9eG5Xa

userId:
  mdcToken: uID
headersInterceptor:
  enabled: false

configstore:
  enabled: true
  apiKey: ${CONFIG_STORE_KEY}
  url: http://config-store.production.cure.fit.internal/

cloud:
  aws:
    end-point: https://sqs.${AWS_REGION}.amazonaws.com/${AWS_ACCOUNT_ID}/

queue:
  activity: "stage-community-user-activity"

constants:
  cult:
    truecallerExperimentId: 1438
    truecallerExperimentBucketId: 2

shortloop:
  environment: alpha

steps-update-fitness-report:
  queueUrl: ${UPDATE_FITNESS_REPORT_QUEUE_URL:https://sqs.${AWS_REGION}.amazonaws.com/${AWS_ACCOUNT_ID}/prod-update-fitness-report}

moengage:
  endpoint: https://api-03.moengage.com
  appID: ZHPJV4VIXMM4FIWSFT4UEO0F
  username: ZHPJV4VIXMM4FIWSFT4UEO0F
  password: z0LUoTsbYndsXul1YzUXR376
  inboundQueue:
    url: https://sqs.${AWS_REGION}.amazonaws.com/${AWS_ACCOUNT_ID}/prod-cultsport-moengage-ingestion-queue

com:
  curefit:
    maestro:
      baseUrl: http://maestro.alpha.cure.fit.internal
      apiKey: ${MAESTRO_API_KEY}

ticketingSystem:
  baseUrl: http://ticketing-system.alpha.cure.fit.internal
  user: <EMAIL>

heimdallMongoAudit:
  baseUrl: http://auditlog.production.cure.fit.internal
  apiKey: oiasjlkmskLasfkQsdfsdfioasjdkashflakswbnaCchJaz
