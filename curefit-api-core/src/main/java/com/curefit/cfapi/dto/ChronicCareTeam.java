package com.curefit.cfapi.dto;

import com.curefit.albus.common.PatientPreferredAgentResponse;
import lombok.*;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
public class ChronicCareTeam {
    PatientPreferredAgentResponse doctor;
    PatientPreferredAgentResponse coach;
    PatientPreferredAgentResponse supportTeam;
}
