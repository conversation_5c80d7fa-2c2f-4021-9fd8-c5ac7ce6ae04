package com.curefit.cfapi.view.viewbuilders.transform;

import com.curefit.albus.common.BundleProduct;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.TransformUtil;
import com.curefit.cfapi.view.viewmodels.transform.BeforeAfterJourneyView;
import com.curefit.cfapi.view.viewmodels.transform.RecommendedPlanView;
import com.curefit.cfapi.view.viewmodels.transform.SmallThumbnailCardData;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.WidgetHeader;
import com.curefit.cfapi.widgets.transform.SmallThumbnailListWidget;
import com.curefit.cfapi.widgets.transform.TransformImageUploadWidget;
import com.curefit.shifu.pojo.UserPictureEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class RecommendedPlanViewBuilder {

    public RecommendedPlanView buildView(ServiceInterfaces interfaces, UserContext userContext, String subCategoryCode) throws Exception {
        RecommendedPlanView recommendedPlanView = new RecommendedPlanView();
        BundleProduct bundleProduct = interfaces.transformClient.getRecommendedPackForUser(userContext.getUserProfile().getUserId());
        recommendedPlanView.setPageTitle("Recommended for you");

        SimpleDateFormat sdf = new SimpleDateFormat("d MMM yy");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, Math.toIntExact(bundleProduct.getDuration())); // Adding 5 days
        String finalDate = sdf.format(calendar.getTime());
        int month = Math.toIntExact(bundleProduct.getDuration() / 30);

        boolean isDiseaseManagementProduct = bundleProduct.getInfoSection().hasNonNull("disease") &&
                ("THYROID".equals(bundleProduct.getInfoSection().get("disease").toString()) ||
                        "PCOD".equals(bundleProduct.getInfoSection().get("disease").toString()) ||
                        "LIFESTYLE".equals(bundleProduct.getInfoSection().get("disease").toString()));

        if (TransformUtil.plusSubCategoryCode.equals(bundleProduct.getSubCategory())) {
            recommendedPlanView.setTitleImageUrl("image/transform/transform_plus_inline_title.png");
            recommendedPlanView.setDescription("Great news! Based on Transform PLUS users like you, we predict you’ll be able to conservatively hit your weight loss goal by " + finalDate);
            recommendedPlanView.setImageUrl("image/transform/transform_plus_graph.png");
            recommendedPlanView.setSubtitle(month + " MONTHS OF " + (isDiseaseManagementProduct ? "DISEASE" : "WEIGHT") + " MANAGEMENT");
            recommendedPlanView.setPrimaryAction(new Action("curefit://tf_new_checkout?productId=" + bundleProduct.getProductCode(), "BUY CULT TRANSFORM PLUS", ActionType.NAVIGATION));
        } else {
            recommendedPlanView.setTitleImageUrl("image/transform/transform_inline_title.png");
            recommendedPlanView.setDescription("Great news! Based on Transform users like you, we predict you’ll be able to conservatively hit your weight loss goal by " + finalDate);
            recommendedPlanView.setImageUrl("image/transform/transform_graph.png");
            recommendedPlanView.setSubtitle(month + " MONTHS OF " + (isDiseaseManagementProduct ? "DISEASE" : "WEIGHT") + " MANAGEMENT");
            recommendedPlanView.setPrimaryAction(new Action("curefit://tf_new_checkout?productId=" + bundleProduct.getProductCode(), "BUY CULT TRANSFORM", ActionType.NAVIGATION));
        }
        recommendedPlanView.setSecondaryAction(new Action("curefit://fl_listpage?pageId=transform_packs", "EXPLORE PLANS", ActionType.NAVIGATION));
        return recommendedPlanView;
    }

}
