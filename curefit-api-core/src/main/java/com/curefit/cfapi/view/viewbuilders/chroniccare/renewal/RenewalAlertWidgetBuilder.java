package com.curefit.cfapi.view.viewbuilders.chroniccare.renewal;

import com.curefit.albus.common.PatientPreferredAgentResponse;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.chronic.UserOnboardingActionWithContext;
import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.UFPackSubscriptionAlertWidget;
import com.curefit.cfapi.widgets.chroniccare.renewal.PackRenewalAlertWidget;
import com.curefit.cfapi.widgets.chroniccare.renewal.PackRenewalCongratulationsWidget;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.shifu.pojo.UserActionEntry;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.pojo.UserMetricEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class RenewalAlertWidgetBuilder {
    private static final String HABIT_FORMED_REASON = "Successfully%20built%20a%20habit";
    private static final String SHIFU_TENANT = "SUGARFIT";
    private static final List<Integer> FONT_SIZE_GRADIENT = Arrays.asList(38, 34, 24);
    public static final long HBA1C_METRIC_ID = 21L;
    public static final long METABOLIC_METRIC_ID = 9L;
    public static final String RENEWED_DATE_FORMAT = "dd MMMM yyyy";
    public static final String LIVE_WORKOUTS_MINUTE_PROPERTY = "live_class_attended_duration";
    public static final double HBA1C_CHANGE_MIN_VALUE = 0.5;
    public static final double METABOLIC_SCORE_CHANGE_MIN_VALUE = 1.0;
    public static final int METRIC_SEARCH_ROWS_LIMIT = 1000;
    public static final int MIN_HABITS_FORMED = 5;
    public static final int MIN_WORKOUT_MINUTES = 300;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final CHSClient chsClient;
    final ExceptionReportingService exceptionReportingService;
    final UserOnboardingService userOnboardingService;
    final UserAttributesClient userAttributesClient;
    public static final long HBA1C_METRIC_CHS_ID = 21L;
    final static String RENEW_URL = "curefit://renewsubscription";

    public BaseWidgetNonVM build(UserContext userContext, UserOnboardingActionWithContext onboardingActions,
                                 PatientPreferredAgentResponse coachData, List<BaseWidgetNonVM> result) {
        String title;
        int type = 1;
        boolean showModal = false;
        boolean showOnlyModal = false;
        Date packExpiryDate = null;
        String modalTitle = "Keep Achieving !";
        Action action = Action.builder().url("curefit://renewsubscription").actionType(ActionType.NAVIGATION).title("RENEW SUBSCRIPTION").build();
        String offerText = ChronicCareAppUtil.isRenewalOfferRunning() && AppUtil.isSugarFitApp(userContext) ? " Renew now & get additional discounts only valid for 12th and 13th Aug." : "";
        if (onboardingActions == null) {
            title = "Your subscription has Expired." + offerText;
            type = 2;
            showModal = true;
        } else {
            Long remainingDays = onboardingActions.getPackRenewalContextActionWithContext().getContext().getRemainingDays();
            if (onboardingActions.getPackRenewalContextActionWithContext().getContext().getRenewed()) {
                Optional<ActivePackResponse> upcomingPack = userOnboardingService.getSugarFitUpcomingPack(userContext.getUserProfile().getUserId());
                String header = "Congratulations !";
                String app = AppUtil.isUltraFitApp(userContext) ? "ultrafit" : "sugarfit";
                String body = "Your subscription with " + app + " has been renewed. Keep up the phenomenal work!";
                String footer = String.format("Your pack will start on %s.", getDateRepresentation(userContext, upcomingPack.get().getStartDate()));
                return new PackRenewalCongratulationsWidget(header, body, footer);
            } else {
                title = String.format("Your subscription with sugar.fit is ending after %s.", chronicCareServiceHelper.getDaysRemainingText(remainingDays)) + offerText;
                packExpiryDate = onboardingActions.getPackRenewalContextActionWithContext().getContext().getPackEndDate();
                if (remainingDays < 3) {
                    type = 2;
                    showModal = true;

                }
                if (AppUtil.isUltraFitApp(userContext)) {
                    title = String.format("Your subscription is ending after %s.", chronicCareServiceHelper.getDaysRemainingTextUF(remainingDays));
                    if (remainingDays < 3) {
                        showOnlyModal = true;
                        result.add(new UFPackSubscriptionAlertWidget(title, new Action(RENEW_URL, "RENEW NOW", ActionType.NAVIGATION), true));
                    } else
                        return null;
                }
            }
        }
        String bottomTitle = "KEEP UP THE PHENOMENAL WORK!";
        String imageURL = coachData != null && coachData.getAgentResponse() != null ? coachData.getAgentResponse().getDisplayImage() : "";
        String coachComment = "“ Great progress with your health and habits. Wish you the best for your future journey with us. “";
        List<Map<String, String>> items = new ArrayList<>();
        Iterator<Integer> iterator = FONT_SIZE_GRADIENT.iterator();
        if (AppUtil.isUltraFitApp(userContext)) {
            Map<String, String> metabolicScore = getMetabolicScore(userContext);
            if (!metabolicScore.isEmpty()) {
                items.add(metabolicScore);
            }
        } else {
            Map<String, String> hbA1cReduction = getHbA1cReduction(userContext);
            Map<String, String> habitsFormed = getHabitsFormed(userContext);
            Map<String, String> minutesOfWorkout = getMinutesOfWorkout(userContext);

            if (!hbA1cReduction.isEmpty()) {
                hbA1cReduction.put("fontSize", String.valueOf(iterator.next()));
                items.add(hbA1cReduction);
            }
            if (!habitsFormed.isEmpty()) {
                habitsFormed.put("fontSize", String.valueOf(iterator.next()));
                items.add(habitsFormed);
            }
            if (!minutesOfWorkout.isEmpty()) {
                minutesOfWorkout.put("fontSize", String.valueOf(iterator.next()));
                items.add(minutesOfWorkout);
            }
        }

        String subtitle = null;
        if(items.size()>0){
            subtitle = "You have achieved amazing results with us!" ;
        }

        return new PackRenewalAlertWidget(title, subtitle, bottomTitle, imageURL, coachComment, items, action, type, showModal, modalTitle, packExpiryDate, showOnlyModal);
    }

    private Map<String, String> getMinutesOfWorkout(UserContext userContext) {
        try {
            Long workoutMinutes = 0L;
            UserAttributesResponse userAttributesResponse = userAttributesClient.getAttributes(
                    Long.valueOf(userContext.getUserProfile().getUserId()), LIVE_WORKOUTS_MINUTE_PROPERTY, AppTenant.CUREFIT, null);
            if (null != userAttributesResponse && null != userAttributesResponse.getAttributes() && userAttributesResponse.getAttributes().containsKey(LIVE_WORKOUTS_MINUTE_PROPERTY)) {
                Long workoutMillis = ((Double) userAttributesResponse.getAttributes().get(LIVE_WORKOUTS_MINUTE_PROPERTY)).longValue();
                workoutMinutes = workoutMillis / (60 * 1000);
            }
            if (workoutMinutes >= MIN_WORKOUT_MINUTES) {
                Long finalWorkoutMinutes = workoutMinutes;
                return new HashMap<>() {{
                    put("title", "minutes of workout");
                    put("value", String.valueOf(finalWorkoutMinutes));
                    put("iconUrl", "/image/chroniccare/CalorieCounted.png");
                }};
            }
        } catch (Exception e) {
            String msg = String.format("Error in calculating workout minutes, :: %s", e.getMessage());
            log.error(msg, e);
            exceptionReportingService.reportException(msg, e);
        }
        return Collections.emptyMap();
    }

    private Map<String, String> getMetabolicScore(UserContext userContext) {
        double difference = 0.0;
        try {
            String query = "userId.eq:"+userContext.getUserProfile().getUserId()+";metricId.eq:"+METABOLIC_METRIC_ID;
            List<UserMetricEntry> startingMetabolicScore = chsClient.searchUserMetrics(
                    query, 0, 1, "startTime", "ASC"
            );
            List<UserMetricEntry> endingMetabolicScore = chsClient.searchUserMetrics(
                    query, 0, 1, "startTime", "DESC"
            );
            log.info("for user :: {}, metabolic Score for user :: {}", userContext.getUserProfile().getUserId(), startingMetabolicScore.size());
            if (startingMetabolicScore.size() >= 1) {
                if (endingMetabolicScore.size() >= 1) {
                    Double start = Double.valueOf(startingMetabolicScore.get(0).getValue());
                    Double end = Double.valueOf(endingMetabolicScore.get(0).getValue());
                    difference = start!=0.0 ? (end - start)*100 / start  : 0.0  ;
                } else {
                    difference = 0.0;
                }
            }
        } catch (Exception e) {
            String errorMessage = String.format("Error is searching metric :: %s", e.getMessage());
            exceptionReportingService.reportException(errorMessage, e);
            log.error(errorMessage, e);
        }
        if (difference >= METABOLIC_SCORE_CHANGE_MIN_VALUE) {
            Map<String, String> response = new HashMap<>();
            response.put("title", "in Metabolic Score");
            response.put("value", String.format("%.1f", difference));
            response.put("iconUrl", "/image/chroniccare/HbA1cReductionIcon.png");
            log.info("for user :: {}, metabolic score response :: {}", userContext.getUserProfile().getUserId(), response);
            return response;
        } else {
            return Collections.emptyMap();
        }
    }

    private Map<String, String> getHbA1cReduction(UserContext userContext) {
        double difference = 0.0;
        try {
            List<UserMetricEntry> userMetricEntries = chsClient.fetchHbA1C(Long.valueOf(userContext.getUserProfile().getUserId()),null,new Date().getTime());
            log.info("for user :: {}, total hba1c readings for user :: {}", userContext.getUserProfile().getUserId(), userMetricEntries.size());
            if (userMetricEntries.size() > 1) {
                Collections.sort(userMetricEntries, Comparator.comparingLong(x -> x.getStartTime().getTime()));
                double start = Double.valueOf(userMetricEntries.get(0).getValue());
                log.info("for user :: {}, Start value :: {}", userContext.getUserProfile().getUserId(), start);
                double end = Double.valueOf(userMetricEntries.get(userMetricEntries.size() - 1).getValue());
                log.info("for user :: {}, End value :: {}", userContext.getUserProfile().getUserId(), end);
                difference = start - end;
            }
        } catch (Exception e) {
            String errorMessage = String.format("Error is searching metric :: %s", e.getMessage());
            exceptionReportingService.reportException(errorMessage, e);
            log.error(errorMessage, e);
        }
        if (difference >= HBA1C_CHANGE_MIN_VALUE) {
            Map<String, String> response = new HashMap<>();
            response.put("title", "HbA1c reduction");
            response.put("value", String.format("%.1f", difference).concat("%"));
            response.put("iconUrl", "/image/chroniccare/HbA1cReductionIcon.png");
            log.info("for user :: {}, hba1c response :: {}", userContext.getUserProfile().getUserId(), response);
            return response;
        } else {
            return Collections.emptyMap();
        }
    }

    private Map<String, String> getHabitsFormed(UserContext userContext) {
        Map<String, String> responseMap = new HashMap<>();
        int habitsFormedCount = 0;
        try {
            String userId = userContext.getUserProfile().getUserId();
            String query = String.format("userId.eq:%s;tenant.eq:%s;expiredReason.eq:%s", userId, SHIFU_TENANT, HABIT_FORMED_REASON);
            List<UserActionEntry> userActionEntries = chronicCareServiceHelper.getShifuClientFromContext(userContext).searchUserActions(query, 0, METRIC_SEARCH_ROWS_LIMIT, "userId", "ASC");
            log.trace("Total habits created for user :: {} are :: {}", userId, userActionEntries.size());
            habitsFormedCount = userActionEntries.size();
            if (habitsFormedCount >= MIN_HABITS_FORMED) {
                responseMap.put("title", "New habits formed");
                responseMap.put("value", String.valueOf(habitsFormedCount));
                responseMap.put("iconUrl", "/image/chroniccare/HabitFormedIcon.png");
            }
        } catch (Exception e) {
            String errorMessage = String.format("Error is searching user actions :: %s", e.getMessage());
            exceptionReportingService.reportException(errorMessage, e);
            log.error(errorMessage, e);
        }
        log.trace("Habits response :: {}", responseMap);
        return responseMap;
    }

    private String getDateRepresentation(UserContext userContext, long millis) {
        TimeZone timeZone = TimeZone.getTimeZone(userContext.getUserProfile().getTimezone());
        return TimeUtil.formatDateInTimezone(timeZone, new Date(millis), RENEWED_DATE_FORMAT);
    }
}
