package com.curefit.cfapi.view.viewmodels.listingpages;

import com.curefit.cfapi.pojo.app.action.Action;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ListingCardView {
    String title;
    String subTitle;
    ArrayList<Media> mediaList;
    Action action;
    public ListingCardView(String title, String subTitle, Action action,  List<String> imageUrls, List<String> videoUrls) {
        this.title = title;
        this.subTitle = subTitle;
        mediaList = new ArrayList<Media>();
        for (String videoUrl : videoUrls) {
            mediaList.add(new Media("VIDEO", videoUrl));
        }
        for (String imageUrl : imageUrls) {
            mediaList.add(new Media("IMAGE", imageUrl));
        }
        this.action = action;
    }
}