package com.curefit.cfapi.view.viewbuilders.transform;

import com.curefit.albus.common.BundleOrderViewPayload;
import com.curefit.albus.response.bootcamp.UserProgramMetricsExtended;
import com.curefit.albus.response.bundle.BundleOrderViewResponse;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.PageContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.TransformUtil;
import com.curefit.cfapi.view.viewmodels.transform.UserMetricChartV2;
import com.curefit.cfapi.view.viewmodels.transform.UserProgressPageView;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.transform.*;
import com.curefit.shifu.enums.LifestyleScoreType;
import com.curefit.shifu.pojo.UserMetricInfo;
import com.curefit.shifu.pojo.UserProgressResponse;
import com.curefit.shifu.pojo.lifestyle.LifestyleScoreEntry;
import com.curefit.userattribute.common.enums.AttributeKeys;
import com.curefit.userattribute.common.pojo.UserAttributeEntry;
import com.curefit.userservice.pojo.entry.UserEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class UserProgressPageViewBuilder {
    long oneWeekInMilliseconds = 1000L * 7 * 24 * 60 * 60;

    public UserProgressPageView buildView(ServiceInterfaces serviceInterfaces, UserContext userContext, String subCategoryCode) throws Exception {
        boolean showHabitTab = false;
        boolean showHabitCalendar = false;
        boolean showLifestyleTab = false;
        UserProgressPageView pageView = new UserProgressPageView();
        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        LifestyleScoreEntry lifestyleScore = (LifestyleScoreEntry) userContext.getRequestCache().getRequestFuture(RequestType.GET_LIFESTYLE_SCORE, userContext).get();
        if (AppUtil.isTransformHabitTabSupported(userContext)) {
            showHabitTab = true;
        }
        if (AppUtil.isHabitCalendarEnabled(userContext)) {
            showHabitCalendar = true;
        }
        if (AppUtil.isTransformLifestyleFeatureSupported(userContext) && lifestyleScore.getScore() != null) {
            showLifestyleTab = true;
        }
        String firstName = user.getFirstName();
        pageView.setTitle("Kudos " + firstName + "!");
        SimpleDateFormat fmt = new SimpleDateFormat("MMM yyyy");
        pageView.setSubtitle("Witness your transformation from " + fmt.format(System.currentTimeMillis() - 6 * oneWeekInMilliseconds) + " to " + fmt.format(System.currentTimeMillis()));
        List<UserMetricInfo> userMetrics = serviceInterfaces.shifuClient.getUserMetricsProgress(userContext.getUserProfile().getUserId(), null, null, true, 7);
        UserProgressResponse userProgressResponse = serviceInterfaces.shifuClient.getUserHabitProgress(Long.parseLong(userContext.getUserProfile().getUserId()));
        pageView.addWidget(createTabbedContainer(serviceInterfaces, userContext, userMetrics, userProgressResponse, lifestyleScore, firstName, showHabitCalendar, showHabitTab, showLifestyleTab, subCategoryCode));
        return pageView;
    }

    private TabbedContainerWidget createTabbedContainer(ServiceInterfaces serviceInterfaces, UserContext userContext, List<UserMetricInfo> userMetrics, UserProgressResponse userProgressResponse, LifestyleScoreEntry lifestyleScore, String firstName, boolean showHabitCalendar, boolean showHabitTab, boolean showLifestyleTab, String subCategoryCode) throws Exception {
        TabbedContainerWidget tabbedContainer = new TabbedContainerWidget();
        PageContext pageContext = new PageContext();
        WidgetContext widgetContext = new WidgetContext(pageContext);
        List<String> tabs = new ArrayList<String>();
        boolean isTransformBootcampUser = TransformUtil.bootcampSubCategoryCode.equals(subCategoryCode);
        boolean isTransformPlusUser = TransformUtil.plusSubCategoryCode.equals(subCategoryCode);
        if (isTransformBootcampUser || isTransformPlusUser) {
            tabs.add(isTransformBootcampUser ? "PROGRESS" : "OUTCOMES");
            if (showHabitTab) {
                tabs.add("HABITS");
            }
        } else {
            if (showHabitTab) {
                tabs.add("HABITS");
            }
            tabs.add("OUTCOMES");
            if (showLifestyleTab) {
                tabs.add("LIFESTYLE SCORE");
            }
        }
        String subCategoryCodeParam = "subCategoryCode=" + subCategoryCode;
        tabbedContainer.setTabs(tabs);
        tabbedContainer.setMeasurementUpdateTitle("Been a while since you last logged your measurements. Do it now?");
        tabbedContainer.setUpdateButtonTitle("UPDATE NOW");
        tabbedContainer.setAction(new Action("curefit://tf_user_metric?" + subCategoryCodeParam, ActionType.NAVIGATION));
        long time = 0;
        for (UserMetricInfo userMetric : userMetrics) {
            if (userMetric != null && userMetric.getLatestValue() != null) {
                time = Math.max(time, userMetric.getLatestValue().getRecordedOn().getTime());
            }
        }
        //To show measurement updation alert box every 1 weeks.
        tabbedContainer.setShowAlertBox(System.currentTimeMillis() - time > oneWeekInMilliseconds);
        List<List<BaseWidget>> widgetList = new ArrayList<>();
        List<BaseWidget> brandWidgets = serviceInterfaces.getWidgetBuilder().buildWidget(Constants.TRANSFORM_SPONSOR_WIDGET_ID, userContext, widgetContext);

        // Habits
        if (showHabitTab) {
            List<BaseWidget> habitWidgets = new ArrayList<>();
            HabitProgressWidget habitProgressWidget = new HabitProgressWidget();
            List<BaseWidget> progressWidget = habitProgressWidget.buildView(userContext, userProgressResponse, firstName, showHabitCalendar);
            if(brandWidgets != null && !brandWidgets.isEmpty()) {
                habitWidgets.add(brandWidgets.get(0));
            }
            progressWidget.forEach(widget -> {
                habitWidgets.add(widget);
            });
            widgetList.add(habitWidgets);
        }
        // Outcomes
        List<BaseWidget> outcomeWidgets = new ArrayList<>();
        if (isTransformBootcampUser && AppUtil.isBootcampNewBatchSelectionSupported(userContext)) {

            UserProgramMetricsExtended programMetrics = serviceInterfaces.transformClient.getExtendedUserMetrics(Long.parseLong(userContext.getUserProfile().getUserId()), TransformUtil.bootcampSubCategoryCode);
            outcomeWidgets.add(createWeightMetricsWidgetV2(programMetrics));
            outcomeWidgets.add(createAttendanceViewWidget(programMetrics));
            outcomeWidgets.add(createStepsCountWidget(programMetrics));
            outcomeWidgets.add(createMealPicsWidget(programMetrics));

        } else {

            outcomeWidgets.add(createWeightMetricsWidget(userMetrics, serviceInterfaces, userContext, subCategoryCode));
            outcomeWidgets.add(createWaistMetricsWidget(userMetrics));
            outcomeWidgets.add(createNeckMetricsWidget(userMetrics));
            outcomeWidgets.add(createHipMetricsWidget(userMetrics));
            outcomeWidgets.add(createBeforeAfterPicsWidget(serviceInterfaces, userContext));

        }
        // adding sponsor widget at bottom in outcomes tab
        if (brandWidgets != null && !brandWidgets.isEmpty()) {
            outcomeWidgets.add(brandWidgets.get(0));
        }
        outcomeWidgets = outcomeWidgets.stream().filter(baseWidget -> baseWidget != null).collect(Collectors.toList());
        if (isTransformBootcampUser || isTransformPlusUser) {
            widgetList.add(0, outcomeWidgets);
        } else {
            widgetList.add(outcomeWidgets);
        }

        if (showLifestyleTab && !isTransformBootcampUser && !isTransformPlusUser) {
            //Lifestyle widgets
            List<BaseWidget> lifestyleWidgets = new ArrayList<>();
            LifestyleScoreSummaryWidget lifestyleScoreSummaryWidget = new LifestyleScoreSummaryWidget();
            LifestyleAreaHabitLevelScoreWidget lifestyleAreaHabitLevelScoreWidget = new LifestyleAreaHabitLevelScoreWidget();
            lifestyleWidgets.add(lifestyleScoreSummaryWidget.buildView(userContext, lifestyleScore, LifestyleScoreType.TRANSFORM));
            lifestyleWidgets.add(lifestyleAreaHabitLevelScoreWidget.buildView(lifestyleScore.getAreas(), firstName, LifestyleScoreType.TRANSFORM));

            widgetList.add(lifestyleWidgets);
        }

        tabbedContainer.setWidgets(widgetList);
        return tabbedContainer;
    }

    private BaseWidget createBeforeAfterPicsWidget(ServiceInterfaces serviceInterfaces, UserContext userContext) throws Exception {
        TransformImageUploadWidget transformImageUploadWidget = new TransformImageUploadWidget();
        transformImageUploadWidget.setLayoutProps(getLayoutData("20", "70"));
        return transformImageUploadWidget.buildView(serviceInterfaces, userContext, null).get(0);
    }

    private BaseWidget createWeightMetricsWidget(List<UserMetricInfo> userMetrics, ServiceInterfaces interfaces, UserContext userContext, String subCategoryCode) throws Exception {
        Optional<UserMetricInfo> weightInfo = userMetrics.stream().filter(info -> info.getMetricId().equals(3L)).findFirst();
        Optional<UserMetricInfo> bmiInfo = userMetrics.stream().filter(info -> info.getMetricId().equals(1L)).findFirst();
        if (weightInfo.isEmpty() || bmiInfo.isEmpty()) return null;
        Double startWeight = null;
        BundleOrderViewPayload payload = new BundleOrderViewPayload();
        payload.setUserId(userContext.getUserProfile().getUserId());
        payload.setSubCategoryCode(subCategoryCode != null && !subCategoryCode.isEmpty() ? subCategoryCode : TransformUtil.transformSubCategoryCode);
        List<BundleOrderViewResponse> orderViewResponses = interfaces.transformClient.getBundleOrdersView(payload);
        if (orderViewResponses != null && !orderViewResponses.isEmpty() && orderViewResponses.get(0).getBundleOrder() != null) {
            Long membershipId = orderViewResponses.get(0).getBundleOrder().getMembershipId();
            UserAttributeEntry attribute = interfaces.shifuClient.findByUserIdKeyAndMembershipId(Long.parseLong(userContext.getUserProfile().getUserId()), String.valueOf(AttributeKeys.START_WEIGHT), membershipId);
            if (attribute != null && attribute.getValue() != null) {
                startWeight = Double.parseDouble(attribute.getValue());
            }
        }
        UserMetricChartWidget metricChartWidget = new UserMetricChartWidget();
        BaseWidget baseWidget = metricChartWidget.buildView(weightInfo.get(), bmiInfo.get(), startWeight).get(0);
        if (metricChartWidget.getUserMetricChart() != null && metricChartWidget.getUserMetricChart().getLeftInfo() != null && metricChartWidget.getUserMetricChart().getRightInfo() != null) {
            return baseWidget;
        }
        return null;
    }

    private BaseWidget createWaistMetricsWidget(List<UserMetricInfo> userMetrics) throws Exception {
        Optional<UserMetricInfo> waistInfo = userMetrics.stream().filter(info -> info.getMetricId().equals(61L)).findFirst();
        if (waistInfo.isEmpty()) return null;
        UserMetricChartWidget metricChartWidget = new UserMetricChartWidget();
        BaseWidget baseWidget = metricChartWidget.buildView(waistInfo.get(), null, null).get(0);
        if (metricChartWidget.getUserMetricChart() != null && metricChartWidget.getUserMetricChart().getLeftInfo() != null && metricChartWidget.getUserMetricChart().getRightInfo() != null) {
            return baseWidget;
        }
        return null;
    }

    private BaseWidget createNeckMetricsWidget(List<UserMetricInfo> userMetrics) throws Exception {
        Optional<UserMetricInfo> neckInfo = userMetrics.stream().filter(info -> info.getMetricId().equals(1665L)).findFirst();
        if (neckInfo.isEmpty()) return null;
        UserMetricChartWidget metricChartWidget = new UserMetricChartWidget();
        BaseWidget baseWidget = metricChartWidget.buildView(neckInfo.get(), null, null).get(0);
        if (metricChartWidget.getUserMetricChart() != null && metricChartWidget.getUserMetricChart().getLeftInfo() != null && metricChartWidget.getUserMetricChart().getRightInfo() != null) {
            return baseWidget;
        }
        return null;
    }

    private BaseWidget createHipMetricsWidget(List<UserMetricInfo> userMetrics) throws Exception {
        Optional<UserMetricInfo> chestInfo = userMetrics.stream().filter(info -> info.getMetricId().equals(57L)).findFirst();
        if (chestInfo.isEmpty()) return null;
        UserMetricChartWidget metricChartWidget = new UserMetricChartWidget();
        BaseWidget baseWidget = metricChartWidget.buildView(chestInfo.get(), null, null).get(0);
        if (metricChartWidget.getUserMetricChart() != null && metricChartWidget.getUserMetricChart().getLeftInfo() != null && metricChartWidget.getUserMetricChart().getRightInfo() != null) {
            return baseWidget;
        }
        return null;
    }

    private BaseWidget createWeightMetricsWidgetV2(UserProgramMetricsExtended programMetrics) throws Exception {
        UserMetricChartWidgetV2 metricChartWidget = new UserMetricChartWidgetV2();
        List<BaseWidget> baseWidget = metricChartWidget.buildView(programMetrics, UserMetricChartV2.Graph.BASE_CHART);
        if (baseWidget != null && baseWidget.size() > 0) {
            return baseWidget.get(0);
        }
        return null;
    }

    private BaseWidget createAttendanceViewWidget(UserProgramMetricsExtended programMetrics) throws Exception {
        UserMetricChartWidgetV2 attendanceChartWidget = new UserMetricChartWidgetV2();
        List<BaseWidget> baseWidget = attendanceChartWidget.buildView(programMetrics, UserMetricChartV2.Graph.CALENDAR);
        if (baseWidget != null && baseWidget.size() > 0) {
            return baseWidget.get(0);
        }
        return null;
    }

    private BaseWidget createStepsCountWidget(UserProgramMetricsExtended programMetrics) throws Exception {
        UserMetricChartWidgetV2 stepsCountChartWidget = new UserMetricChartWidgetV2();
        List<BaseWidget> baseWidget = stepsCountChartWidget.buildView(programMetrics, UserMetricChartV2.Graph.BAR_CHART);
        if (baseWidget != null && baseWidget.size() > 0) {
            return baseWidget.get(0);
        }
        return null;
    }

    private BaseWidget createMealPicsWidget(UserProgramMetricsExtended programMetrics) throws Exception {
        UserMetricChartWidgetV2 mealPlanChartWidget = new UserMetricChartWidgetV2();
        List<BaseWidget> baseWidget = mealPlanChartWidget.buildView(programMetrics, UserMetricChartV2.Graph.PIE_CHART);
        if (baseWidget != null && baseWidget.size() > 0) {
            return baseWidget.get(0);
        }
        return null;
    }

    public Object getLayoutData(String top, String bottom) {
        Spacing spacing = new Spacing(top, bottom);
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", spacing);
        return layoutProps;
    }
}