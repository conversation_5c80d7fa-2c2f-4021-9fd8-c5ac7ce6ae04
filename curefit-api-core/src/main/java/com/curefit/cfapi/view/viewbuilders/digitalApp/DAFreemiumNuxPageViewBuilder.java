package com.curefit.cfapi.view.viewbuilders.digitalApp;

import com.curefit.albus.common.BundleProduct;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.service.EnvironmentService;
import com.curefit.cf.commons.pojo.SearchOperator;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.util.DigitalAppUtil;
import com.curefit.cfapi.view.viewmodels.digitalApp.DAFreemiumNuxPageView;
import com.curefit.cfapi.view.viewmodels.digitalApp.DANuxProfileAnswerType;
import com.curefit.cfapi.view.viewmodels.digitalApp.DANuxStep;
import com.curefit.cfapi.view.viewmodels.digitalApp.DANuxStepProfile;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.RuntimeBaseException;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.enums.UserProfileCategory;
import com.sugarfit.chs.pojo.AppLanguagePreferenceResponse;
import com.sugarfit.chs.pojo.FormQuestionEntry;
import com.sugarfit.chs.pojo.SearchRequestV2;
import com.sugarfit.chs.pojo.UserMetricEntry;
import com.sugarfit.chs.pojo.userProfile.UserProfileEntry;
import com.sugarfit.chs.pojo.userProfile.UserProfileResponse;
import com.sugarfit.sms.client.SMSClient;
import com.sugarfit.sms.enums.NUXProfile;
import com.sugarfit.sms.enums.NUXStepName;
import com.sugarfit.sms.response.NUXStatusResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.Period;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.curefit.cfapi.util.AppUtil.getAppTenantFromUserContext;
import static com.curefit.cfapi.util.ChronicCareAppUtil.*;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class DAFreemiumNuxPageViewBuilder {

    final SMSClient smsClient;
    final UserAttributesClient userAttributesClient;
    final ServiceInterfaces serviceInterfaces;
    final CHSClient chsClient;
    private UserEntry userEntry;
    private UserProfileResponse userProfileResponse;
    final ObjectMapper objectMapper;
    final EnvironmentService environmentService;
    final UserOnboardingService userOnboardingService;
    final List<NUXProfile> infoProfiles = List.of(NUXProfile.DEMOGRAPHIC_INFO, NUXProfile.DIABETES_PROFILE_INFO, NUXProfile.LIFESTYLE_INFO,
            NUXProfile.REDUCTION_IN_MEDICATION_INFO, NUXProfile.BLOOD_SUGAR_TRACKING_INFO, NUXProfile.SLEEP_QUALITY_INFO);

    public DAFreemiumNuxPageView buildView(UserContext userContext, String formType, boolean salesApp) throws HttpException {
        DAFreemiumNuxPageView page = new DAFreemiumNuxPageView();
        List<DANuxStep> steps = new ArrayList<>();
        page.setActiveStepIndex(0);
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        NUXStatusResponse nuxStatus;
        if (salesApp) {
            nuxStatus = smsClient.getNuxStatusSalesApp(userId, false, timeZone);
        } else {
            nuxStatus = smsClient.getNUXStatus(userId, false, timeZone);
        }
        UserEntry userEntry;
        try {
            userEntry = this.serviceInterfaces.userServiceClient.getUser(String.valueOf(userId)).get();
        } catch (RuntimeBaseException | InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }

        String formToFetch = formType;
        if (Objects.isNull(formToFetch)) {
            boolean isInternationalUser = ChronicCareAppUtil.isInternationalSugarfitUser(userContext);
            if (isInternationalUser) {
                formToFetch = DIGITAL_APP_QUESTION_INTERNATIONAL;
            } else {
                formToFetch = DIGITAL_APP_QUESTION_IN;
            }
        }

        List<FormQuestionEntry> formQuestions = chsClient.fetchForm(formToFetch);
        CompletableFuture<ActivePackResponse> activePackResponseFuture = userOnboardingService.getSugarFitActivePackResponseFuture(userContext.getUserProfile().getUserId());

        BundleProduct bundleProduct = null;
        try {
            if (Objects.nonNull(activePackResponseFuture) && Objects.nonNull(activePackResponseFuture.get())) {
                bundleProduct = activePackResponseFuture.get().getBundleProduct();
            }
        } catch (Exception e) {
            //empty
        }
        page.setLandingPage(getDaFreemiumNuxLandingPage(bundleProduct));

        HashMap<String, FormQuestionEntry> questionFormMap = new HashMap<>();

        formQuestions.forEach(formQuestionEntry -> {
            questionFormMap.put(formQuestionEntry.getQuestionKey(), formQuestionEntry);
        });

        if (!nuxStatus.getNuxCompleted() && nuxStatus.getProfiles() != null && nuxStatus.getProfiles().getStepToProfileMap() != null) {
            List<NUXStatusResponse.NUXStatusProfileResponse.NUXStepToProfileMap> stepToProfileMaps = nuxStatus.getProfiles().getStepToProfileMap();
            stepToProfileMaps.sort(Comparator.comparing(NUXStatusResponse.NUXStatusProfileResponse.NUXStepToProfileMap::getOrdinal));
            AtomicInteger index = new AtomicInteger();
            stepToProfileMaps.forEach(step -> {
                int currIndex = index.getAndIncrement();
                if(step.getActive()) {
                    page.setActiveStepIndex(Math.max(page.getActiveStepIndex(), currIndex));
                }

                DANuxStep nuxStep = new DANuxStep();
                nuxStep.setStepName(step.getStepName());
                nuxStep.setIconBaseUrl(getStepIconUrl(step.getStepName()));
                if(step.getProfiles() != null) {
                    final List<DANuxStepProfile> daNuxStepProfiles = new ArrayList<>();
                    final List<NUXProfile> profilesInStep = step.getProfiles();
                    int lastUnansweredIndex = -1;

                    for (NUXProfile profile : profilesInStep) {
                        DANuxStepProfile nuxProfile = createProfile(profile, questionFormMap.get(profile.name()), userEntry, userContext);

                        if (nuxProfile.getAnswer() != null && !nuxProfile.getAnswer().isEmpty()) {
                            nuxProfile.setIsCompleted(true);
                        }

                        daNuxStepProfiles.add(nuxProfile);
                    }

                    // Identify last unanswered profile which is not in infoProfiles
                    for (int i = daNuxStepProfiles.size() - 1; i >= 0; i--) {
                        DANuxStepProfile profile = daNuxStepProfiles.get(i);
                        if ((profile.getAnswer() == null || profile.getAnswer().isEmpty()) && !infoProfiles.contains(profile.getProfileName())) {
                            lastUnansweredIndex = i;
                        }else if(!infoProfiles.contains(profile.getProfileName())){
                            break;
                        }
                    }

                    int safeIndex = lastUnansweredIndex == -1 ? daNuxStepProfiles.size() - 1 : lastUnansweredIndex;
                    safeIndex = Math.max(0, Math.min(safeIndex, daNuxStepProfiles.size() - 1));
                    nuxStep.setProfiles(daNuxStepProfiles);
                    nuxStep.setActiveProfileIndex(safeIndex);
                }

                steps.add(nuxStep);

            });
        }

        page.setSteps(steps);
        page.setIsCompleted(nuxStatus.getNuxCompleted());

        return page;
    }

    private String getStepIconUrl(NUXStepName stepName){
        if (Objects.isNull(stepName)) {
            return "";
        }
        switch (stepName) {
            case DEMOGRAPHICS -> {
                return "https://d30nc97kamtr39.cloudfront.net/misc/demog-info-v2-";
            }
            case LIFESTYLE -> {
                return "https://d30nc97kamtr39.cloudfront.net/misc/lifestyle-v2-";
            }
            case DIABETES_PROFILE -> {
                return "https://d30nc97kamtr39.cloudfront.net/misc/dia-profile-v2-";
            }
            case GOALS -> {
                return "https://d30nc97kamtr39.cloudfront.net/misc/goals-v2-";
            }
            case CHALLENGES -> {
                return "https://d30nc97kamtr39.cloudfront.net/misc/challenge-v1-";
            }
            default -> {
                return "https://d30nc97kamtr39.cloudfront.net/misc/goals-v2-";
            }
        }
    }

    private List<UserProfileCategory> getUserProfileCategories(List<FormQuestionEntry> formQuestions){
        List<UserProfileCategory> categories = new ArrayList<>();
        formQuestions.forEach(formQuestionEntry -> {
            Map<String, Object> questionMetaData = formQuestionEntry.getMetaData();
            if(questionMetaData.containsKey("dataStore") && questionMetaData.get("dataStore") != null && questionMetaData.get("dataStore").equals(CHS)) {
                if (((String) questionMetaData.get("dataStore")).equals(CHS)) {
                    if (questionMetaData.containsKey("chsMeta")) {
                        Map<String, Object> chsMeta = (Map<String, Object>) questionMetaData.get("chsMeta");
                        Map<String, Object> userPreferenceMeta = (Map<String, Object>) chsMeta.get("userPreferenceMeta");
                        if(userPreferenceMeta != null && userPreferenceMeta.containsKey("category")) {
                            try{
                                String  category=(String) userPreferenceMeta.get("category");
                                categories.add(UserProfileCategory.valueOf(category));
                            } catch (IllegalArgumentException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                }
            }
        });

        return categories;
    }

    private static DAFreemiumNuxPageView.@NotNull DAFreemiumNuxLandingPage getDaFreemiumNuxLandingPage(BundleProduct bundleProduct) {
        boolean isDigitalPaidUser = DigitalAppUtil.isDigitalAppPaidUser(bundleProduct);
        DAFreemiumNuxPageView.DAFreemiumNuxLandingPage landingPage = new DAFreemiumNuxPageView.DAFreemiumNuxLandingPage();
        landingPage.setTitle(isDigitalPaidUser
                ? List.of("Welcome to your Sugar Control Plan \uD83D\uDC9C")
                : List.of("Hi there! \uD83D\uDC4B\uD83C\uDFFB", "Let’s Personalize Your Diabetes Care!"));
        landingPage.setSubTitle(isDigitalPaidUser ? "Your personalized path to better sugar levels, energy, and everyday health starts right now." :"Answer a few quick questions so we can better understand your health status and guide you toward your goals");
        landingPage.setCtaText("Personalise my journey");
        landingPage.setVideoUrl(isDigitalPaidUser ? "https://d30nc97kamtr39.cloudfront.net/misc/landing_page_video.mp4" : "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/-14de-4298-940c-eb7f94a2fbfc-2025-02-04-14:56.mp4");
        return landingPage;
    }

    private DANuxProfileAnswerType getNuxProfileQuestionType(String answerType) {
        return switch (answerType) {
            case SINGLE_CHOICE -> DANuxProfileAnswerType.SINGLE_SELECT;
            case MULTI_CHOICE -> DANuxProfileAnswerType.MULTI_SELECT;
            case TEXT_INPUT -> DANuxProfileAnswerType.INPUT;
            case NUMBER_INPUT -> DANuxProfileAnswerType.SLIDER_INPUT;
            case DOB_INPUT -> DANuxProfileAnswerType.DOB_INPUT;
            case INFORMATION -> DANuxProfileAnswerType.INFO;
            case DROPDOWN -> DANuxProfileAnswerType.DROPDOWN;
            default -> null;
        };
    }


    public DANuxStepProfile createProfile(NUXProfile nuxProfile, FormQuestionEntry questionEntry, UserEntry userEntry, UserContext userContext){
        DANuxStepProfile nuxStepProfile = new DANuxStepProfile();
        nuxStepProfile.setProfileName(nuxProfile);
        if(questionEntry == null ) return nuxStepProfile;
        nuxStepProfile.setQuestion(questionEntry.getTitle());
        nuxStepProfile.setDescription(questionEntry.getDescription());
        nuxStepProfile.setQuestionKey(questionEntry.getQuestionKey());
        nuxStepProfile.setIsMandatory(questionEntry.getIsMendatory());
        nuxStepProfile.setAnswerType(getNuxProfileQuestionType(questionEntry.getAnswerType()));

        Map<String, Object> questionMetaData = questionEntry.getMetaData();
        if(questionMetaData != null) {
            if (questionMetaData.get(OPTIONS) != null) {
                Object optionsObj = questionMetaData.get(OPTIONS);
                if (optionsObj instanceof Map) {
                    List<DANuxStepProfile.Option> nuxOptions = getNuxOptions((Map<String, Map<String, Object>>) optionsObj);
                    nuxStepProfile.setOptions(nuxOptions);
                } else {
                    System.out.println("Invalid options format in metaData");
                }
            }

            if (questionMetaData.get("uiMeta") != null) {
                DANuxStepProfile.Meta nuxMeta = new DANuxStepProfile.Meta();
                Object uiMetaObj = questionMetaData.get("uiMeta");
                if (uiMetaObj instanceof Map) {
                    Map<String, Object> uiMetaMap = (Map<String, Object>) uiMetaObj;
                    nuxMeta.setTitleSize(getValue(uiMetaMap, "titleSize", Integer.class));
                    nuxMeta.setSubTitleSize(getValue(uiMetaMap, "optionTitleSize", Integer.class));
                    nuxMeta.setNoOfColumns(getValue(uiMetaMap, "noOfColumns", Integer.class));
                    nuxMeta.setStepSize(getValue(uiMetaMap, "stepSize", Integer.class));
                    nuxMeta.setInputMaxValue(getValue(uiMetaMap, "inputMaxValue", Integer.class));
                    nuxMeta.setInputMinValue(getValue(uiMetaMap, "inputMinValue", Integer.class));
                    nuxMeta.setInputInitialValue(getValue(uiMetaMap, "inputInitialValue", Integer.class));
                    nuxMeta.setRulerPrintStepSize(getValue(uiMetaMap, "rulerPrintStepSize", Integer.class));
                    nuxMeta.setDefaultUnit(getValue(uiMetaMap, "defaultUnit", String.class));
                    List<DANuxStepProfile.Unit> parsedUnits = parseUnits(uiMetaMap.get("units"));
                    if (!parsedUnits.isEmpty()) {
                        nuxMeta.setUnits(parsedUnits);
                    }
                }
                nuxStepProfile.setMeta(nuxMeta);
            }

            if (questionMetaData.get("infoUiMeta") != null) {
                Map<String, Object> infoUiMetaObj = (Map<String, Object>) questionMetaData.get("infoUiMeta");
                try {
                    ObjectMapper objectMapper = new ObjectMapper();

                    // Process title list with the improved fixInvalidJson() method
                    List<?> rawTitleList = (List<?>) infoUiMetaObj.get("title");
                    List<DANuxStepProfile.InfoItem> titleList = new ArrayList<>();
                    if (rawTitleList != null) {
                        for (Object item : rawTitleList) {
                            if (item instanceof Map) {
                                titleList.add(objectMapper.convertValue(item, DANuxStepProfile.InfoItem.class));
                            } else if (item instanceof String) {
                                String raw = (String) item;
                                // Convert the non-standard string into valid JSON
                                String validJson = fixInvalidJson(raw);
                                Map<String, Object> fixedItem = objectMapper.readValue(validJson, new TypeReference<Map<String, Object>>() {});
                                titleList.add(objectMapper.convertValue(fixedItem, DANuxStepProfile.InfoItem.class));
                            } else {
                                System.err.println("Unexpected type in title list: " + item.getClass().getName());
                            }
                        }
                    }

                    // Process subTitle list similarly
                    List<?> rawSubTitleList = (List<?>) infoUiMetaObj.get("subTitle");
                    List<DANuxStepProfile.InfoItem> subTitleList = new ArrayList<>();
                    if (rawSubTitleList != null) {
                        for (Object item : rawSubTitleList) {
                            if (item instanceof Map) {
                                subTitleList.add(objectMapper.convertValue(item, DANuxStepProfile.InfoItem.class));
                            } else if (item instanceof String) {
                                String raw = (String) item;
                                String validJson = fixInvalidJson(raw);
                                Map<String, Object> fixedItem = objectMapper.readValue(validJson, new TypeReference<Map<String, Object>>() {});
                                subTitleList.add(objectMapper.convertValue(fixedItem, DANuxStepProfile.InfoItem.class));
                            }
                        }
                    }

                    // Process footer list
                    List<?> rawFooterList = (List<?>) infoUiMetaObj.get("footer");
                    List<DANuxStepProfile.InfoItem> footerList = new ArrayList<>();
                    if (rawFooterList != null) {
                        for (Object item : rawFooterList) {
                            if (item instanceof Map) {
                                footerList.add(objectMapper.convertValue(item, DANuxStepProfile.InfoItem.class));
                            } else if (item instanceof String) {
                                String raw = (String) item;
                                String validJson = fixInvalidJson(raw);
                                Map<String, Object> fixedItem = objectMapper.readValue(validJson, new TypeReference<Map<String, Object>>() {});
                                footerList.add(objectMapper.convertValue(fixedItem, DANuxStepProfile.InfoItem.class));
                            }
                        }
                    }

                    // Convert nested objects normally
                    DANuxStepProfile.InfoBody body = objectMapper.convertValue(infoUiMetaObj.get("body"), DANuxStepProfile.InfoBody.class);
                    DANuxStepProfile.InfoItemColorConfig colorConfig = objectMapper.convertValue(infoUiMetaObj.get("colorConfig"), DANuxStepProfile.InfoItemColorConfig.class);
                    String ctaText = (String) infoUiMetaObj.get("ctaText");

                    // Build final InfoMeta object
                    DANuxStepProfile.InfoMeta infoMeta = new DANuxStepProfile.InfoMeta();
                    infoMeta.setTitle(titleList);
                    infoMeta.setSubTitle(subTitleList);
                    infoMeta.setFooter(footerList);
                    infoMeta.setBody(body);
                    infoMeta.setColorConfig(colorConfig);
                    infoMeta.setCtaText(ctaText);
                    nuxStepProfile.setInfoMeta(infoMeta);

                } catch (Exception e) {
                    throw new RuntimeException("Error mapping infoUiMeta", e);
                }
            }

            populateSavedValue(nuxStepProfile, questionEntry.getQuestionKey(), questionMetaData, userEntry, userContext);

        }

        return nuxStepProfile;
    }

    private void populateSavedValue(DANuxStepProfile profile, String questionKey, Map<String, Object> questionMetaData , UserEntry userEntry, UserContext userContext) {
        List<String> value = new ArrayList<>();
        if(questionMetaData.containsKey("dataStore") && questionMetaData.get("dataStore") != null) {
            if (questionMetaData.get("dataStore").equals(CHS)) {
                if (questionMetaData.containsKey("chsMeta")) {
                    try {
                        boolean isInternationalUser = ChronicCareAppUtil.isInternationalSugarfitUser(userContext);
                        Optional<UserProfileEntry> userProfileEntryOptional = chsClient.getUserAnswerForFormKeyAndQuestionKey(userEntry.getId(), isInternationalUser ? DIGITAL_APP_QUESTION_INTERNATIONAL : DIGITAL_APP_QUESTION_IN, questionKey);
                        if(userProfileEntryOptional.isPresent()) {
                            UserProfileEntry userProfileEntry = userProfileEntryOptional.get();
                            String valueStr = userProfileEntry.getValue();
                            if (valueStr != null) {
                                try {
                                    Map<String, Object> valueMap = objectMapper.readValue(valueStr, new TypeReference<Map<String, Object>>() {});
                                    Object answerRaw;

                                    if (profile.getAnswerType() == DANuxProfileAnswerType.MULTI_SELECT) {
                                        answerRaw = valueMap.get("options");
                                    } else {
                                        answerRaw = valueMap.get("option");
                                    }

                                    if (answerRaw instanceof List<?>) {
                                        List<?> rawList = (List<?>) answerRaw;
                                        value = rawList.stream().map(Object::toString).toList();
                                    } else if (answerRaw instanceof Number || answerRaw instanceof String) {
                                        value = List.of(answerRaw.toString());
                                    }
                                } catch (Exception e) {
                                    log.error("Error parsing saved answer JSON: {}", valueStr, e);
                                }
                            }
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        } else {
            switch (profile.getProfileName()) {
                case AGE -> {
                    if(userEntry.getBirthday() !=null) {
                        Period period = Period.between(convertToLocalDateViaInstant(userEntry.getBirthday()), LocalDate.now());
                        value = List.of(String.valueOf(period.getYears()));
                    }
                }

                case NAME -> {
                    if(userEntry.getFirstName() != null)
                        value = List.of(userEntry.getFirstName());
                }

                case GENDER -> {
                    if(userEntry.getGender() != null)
                        value = List.of(userEntry.getGender());
                }

                case HEIGHT -> {
                    Double latestHeight = getLatestMetricValue(userEntry.getId().toString(), (environmentService.isProduction() || environmentService.isAlpha() ? HEIGHT_METRIC_ID_PROD : HEIGHT_METRIC_ID_STAGE));
                    if(latestHeight != null)
                        value = List.of(latestHeight.toString());
                }

                case WEIGHT -> {
                    Double latestWeight = getLatestMetricValue(userEntry.getId().toString(), (environmentService.isProduction() || environmentService.isAlpha() ? WEIGHT_METRIC_ID_PROD : WEIGHT_METRIC_ID_STAGE));
                    if(latestWeight != null)
                        value = List.of(latestWeight.toString());
                }

                case LANGUAGE -> {
                    try {
                        AppTenant appTenant = getAppTenantFromUserContext(userContext);
                        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                        AppLanguagePreferenceResponse appLanguagePreference = chsClient.fetchAppLanguagePreference(userId,appTenant);

                        if(appLanguagePreference.getLanguagePreference() != null)
                            value = List.of(appLanguagePreference.getLanguagePreference());
                    } catch (Exception e) {
//                        throw new RuntimeException(e);
                    }
                }
            }
        }

        profile.setAnswer(value);

    }

    public Double getLatestMetricValue(String userId, Long metricId) {
        try {
            SearchRequestV2 searchRequestV2 = new SearchRequestV2();
            Map<SearchOperator, Map<String, Object>> query = new HashMap<>();
            Map<String, Object> param = new HashMap<>();
            param.put("metricId", metricId);
            param.put("userId", userId);
            query.put(SearchOperator.eq, param);

            searchRequestV2.setQuery(query);
            searchRequestV2.setLimit(1);
            searchRequestV2.setOffset(0);
            searchRequestV2.setSortOrder("DESC");
            searchRequestV2.setSortBy("startTime");
            List<UserMetricEntry> latestWeight = chsClient.searchUserMetricsV2(searchRequestV2);

            if (!CollectionUtils.isEmpty(latestWeight)) {
                return Double.valueOf(latestWeight.getFirst().getValue());
            }
        } catch (Exception e) {
            String errorMessage = String.format("Error is searching metric :: %s", e.getMessage());
//            exceptionReportingService.reportException(errorMessage, e);
            log.error(errorMessage, e);
        }
        return null;
    }

    private static String fixInvalidJson(String input) {
        // Remove outer braces if present
        String trimmed = input.trim();
        if (trimmed.startsWith("{") && trimmed.endsWith("}")) {
            trimmed = trimmed.substring(1, trimmed.length() - 1);
        }
        // Regex: capture key as (\w+), then '=' and then value as (.+?) until the next ", key=" or end-of-string.
        Pattern pattern = Pattern.compile("(\\w+)=(.+?)(?=,\\s*\\w+=|$)");
        Matcher matcher = pattern.matcher(trimmed);
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        boolean first = true;
        while (matcher.find()) {
            if (!first) {
                sb.append(",");
            }
            String key = matcher.group(1);
            String value = matcher.group(2).trim();
            // If the value is numeric or a boolean, leave it unquoted; otherwise, quote it.
            if (value.matches("-?\\d+(\\.\\d+)?") || value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false")) {
                sb.append("\"").append(key).append("\":").append(value);
            } else {
                // Escape any inner quotes if necessary
                value = value.replace("\"", "\\\"");
                sb.append("\"").append(key).append("\":").append("\"").append(value).append("\"");
            }
            first = false;
        }
        sb.append("}");
        return sb.toString();
    }

    private static List<DANuxStepProfile.InfoItem> parseInfoItems(Object rawList) {
        List<DANuxStepProfile.InfoItem> infoItems = new ArrayList<>();

        if (rawList instanceof List<?>) {
            List<?> list = (List<?>) rawList;  // Casting to generic List

            for (Object obj : list) {
                if (obj instanceof Map) {
                    Map<?, ?> itemMap = (Map<?, ?>) obj; // Avoid ClassCastException

                    DANuxStepProfile.InfoItem infoItem = new DANuxStepProfile.InfoItem();
                    infoItem.setIsHighlighted((Boolean) itemMap.get("isHighlighted"));
                    infoItem.setText((String) itemMap.get("text"));
                    infoItem.setFontSize((Integer) itemMap.get("fontSize"));

                    infoItems.add(infoItem);
                } else {
                    System.out.println("Skipping invalid item: " + obj);
                }
            }
        }
        return infoItems;
    }

    public static <T> T getValue(Map<String, Object> map, String key, Class<T> clazz) {
        Object value = map.get(key);
        return value == null ? null : clazz.cast(value);
    }

    public @NotNull List<DANuxStepProfile.Option> getNuxOptions(Map<String, Map<String, Object>> optionsObj) {
        List<DANuxStepProfile.Option> nuxOptions = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> entry : optionsObj.entrySet()) {
            DANuxStepProfile.Option nuxOption = new DANuxStepProfile.Option();
            Map<String, Object> option = entry.getValue();

            nuxOption.setScore((int) option.get("score"));
            nuxOption.setSequence((int) option.get("sequence"));
            nuxOption.setCode((String) option.get("code"));

            Object valueObj = option.get("value");
            if (valueObj instanceof Map) {
                Map<String, String> valueMap = (Map<String, String>) valueObj;
                nuxOption.setTitle(valueMap.get("title"));
                nuxOption.setSubTitle(valueMap.get("subTitle"));
                nuxOption.setIconUrl(valueMap.get("iconUrl"));
                nuxOption.setDisplayValue(valueMap.get("displayValue"));
            }
            nuxOptions.add(nuxOption);
        }

        // Sort options based on sequence value if present
        nuxOptions.sort(Comparator.comparingInt(DANuxStepProfile.Option::getSequence));
        return nuxOptions;
    }

    /**
     * Parses the "units" array from the given object.
     */
    private List<DANuxStepProfile.Unit> parseUnits(Object unitsObj) {
        if (Objects.isNull(unitsObj) || !(unitsObj instanceof List<?> unitsList)) {
            return Collections.emptyList();
        }

        List<DANuxStepProfile.Unit> parsedUnits = new ArrayList<>();

        for (Object unitItem : unitsList) {
            try {
                Map<?, ?> unitMap;
                if (unitItem instanceof String) {
                    String fixedJson = fixInvalidJson((String) unitItem);
                    unitMap = objectMapper.readValue(fixedJson, new TypeReference<Map<String, Object>>() {});
                } else if (unitItem instanceof Map<?, ?>) {
                    unitMap = (Map<?, ?>) unitItem;
                } else {
                    continue;
                }

                DANuxStepProfile.Unit unit = new DANuxStepProfile.Unit();
                unit.setUnit((String) unitMap.get("unit"));

                Object conversionFactorObj = unitMap.get("conversionFactor");
                if (conversionFactorObj instanceof Number) {
                    unit.setConversionFactor(((Number) conversionFactorObj).doubleValue());
                } else if (conversionFactorObj instanceof String) {
                    try {
                        unit.setConversionFactor(Double.parseDouble((String) conversionFactorObj));
                    } catch (NumberFormatException e) {
                        continue;
                    }
                }

                parsedUnits.add(unit);
            } catch (Exception e) {
                log.error("Error parsing unit item: {}", unitItem, e);
            }
        }

        return parsedUnits;
    }
}