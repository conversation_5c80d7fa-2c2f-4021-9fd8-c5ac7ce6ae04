package com.curefit.cfapi.view.viewbuilders.chroniccare.challenges;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.util.SfDateUtils;
import com.curefit.cfapi.view.viewmodels.chroniccare.challenges.SfChallengesListPageView;
import com.curefit.cfapi.widgets.chroniccare.challenges.SfChallengesListItemWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.sugarfit.challenges.client.ChallengesClient;
import com.sugarfit.challenges.enums.ChallengeType;
import com.sugarfit.challenges.enums.Status;
import com.sugarfit.challenges.pojo.ChallengesEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class SfChallengesListPageBuilder {

    final ExceptionReportingService exceptionReportingService;
    final ChallengesClient challengesClient;

    public SfChallengesListPageView buildView(UserContext userContext, SfChallengesListPageView.ChallengeTabs type, Integer pageNumber) {

        SfChallengesListPageView pageView = new SfChallengesListPageView();

        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            List<ChallengesEntry> challenges = new ArrayList<>();
            if (type.equals(SfChallengesListPageView.ChallengeTabs.ACTIVE_CHALLENGES)) {
                SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                        RequestType.PLATFORM_SEGMENTS,
                        userContext).get();
                List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(Set.of()));
                challenges = challengesClient.fetchActiveChallengesForUser(userId, pageNumber * 20, 20, "createdOn", "DESC", userSegments).getElements();
            } else if (type.equals(SfChallengesListPageView.ChallengeTabs.PARTICIPATED_CHALLENGES)) {
                challenges = challengesClient.fetchUserParticipatedChallengesForUser(userId, pageNumber * 20, 20, "createdOn", "DESC").getElements();
            }
            if (CollectionUtils.isNotEmpty(challenges)) {
                List<ChallengesEntry> tempChallenges;
                if (!ChronicCareAppUtil.isReferralChallengeSupportedApp(userContext)) {
                    tempChallenges = challenges.stream().filter(c -> !c.getChallengeType().equals(ChallengeType.REFERRAL_CHALLENGE)).toList();
                    challenges.clear();
                    challenges.addAll(tempChallenges);
                }
            }
            if (CollectionUtils.isNotEmpty(challenges)) {
                challenges.forEach(p -> {
                    if (p != null) {
                        try {
                            pageView.addWidget(buildChallengesWidget(userContext, p));
                        } catch (BaseException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        } catch (Exception e) {
            String message = String.format("Error in building page, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }

        return pageView;
    }


    private SfChallengesListItemWidget buildChallengesWidget(UserContext userContext, ChallengesEntry challengesEntry) throws BaseException {
        if (challengesEntry.getStatus() != null && challengesEntry.getStatus().equals(Status.PUBLISHED)) {
            // fetch the challenge again to get all the details about questions in the PollEntry object
            boolean hasUserJoined = challengesEntry.getUserJoinedDate() != null;
            boolean hasChallengeStarted = !SfDateUtils.isFutureDate(userContext, challengesEntry.getStartDate());
            String tabKey = "ABOUT";
            if (hasUserJoined && hasChallengeStarted) {
                tabKey = "LEADERBOARD";
            }
            String pageUrl = "curefit://sfchallengedetailspage?challengeId=" + challengesEntry.getId() + "&tabKey=" + tabKey;
            Action action = Action.builder().url(pageUrl).actionType(ActionType.NAVIGATION).build();
            SfChallengesListItemWidget widget = new SfChallengesListItemWidget();
            widget.setChallengeId(challengesEntry.getId());
            widget.setTitle(challengesEntry.getTitle());
            widget.setStartTime(challengesEntry.getStartDate());
            widget.setParticipantCount(challengesEntry.getParticipantCount());
            widget.setChallengeAudienceType(challengesEntry.getChallengeAudience());
            widget.setCardAction(action);
            return widget;
        }
        return null;
    }

}