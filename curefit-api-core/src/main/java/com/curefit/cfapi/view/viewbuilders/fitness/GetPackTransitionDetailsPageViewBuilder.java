package com.curefit.cfapi.view.viewbuilders.fitness;

import com.curefit.center.dtos.CenterEntry;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.widgets.ProductPriceView;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.Debugger;
import com.curefit.cfapi.util.ErrorUtil;
import com.curefit.cfapi.util.FitnessCenterUtil;
import com.curefit.cfapi.util.GymUtil;
import com.curefit.cfapi.util.MembershipUtil;
import com.curefit.cfapi.util.StringUtil;
import com.curefit.cfapi.view.viewmodels.fitness.PackTransitionPageView;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.fitness.SKUComparisonWidgetV2;
import com.curefit.cfapi.widgets.fitness.SkuPackV2;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.location.models.City;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.pms.enums.Namespace;
import com.curefit.pms.enums.PackTransitionType;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.pms.enums.Visibility;
import com.curefit.pms.pojo.Restriction;
import com.curefit.pms.pojo.customPacks.OfflineFitnessPack;
import com.curefit.pms.pojo.customPacks.augments.AugmentedOfflineFitnessPack;
import com.curefit.pms.requests.PackTransitionPricingRequest;
import com.curefit.pms.requests.augments.AugmentContext;
import com.curefit.pms.requests.augments.AugmentedPackSearchRequest;
import com.curefit.pms.requests.augments.PackAugmentRequest;
import com.curefit.pms.responses.PackTransitionPricingResponse;
import com.curefit.product.enums.ProductType;
import com.curefit.product.enums.Status;
import org.apache.commons.collections.CollectionUtils;
import com.curefit.cfapi.constants.Constants;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

import static com.curefit.cfapi.pojo.app.action.ActionType.NAVIGATION;
import static com.curefit.cfapi.util.FitnessCenterUtil.CULT_CENTER_ID_KEY;
import static com.curefit.cfapi.view.viewbuilders.fitness.TransferUpgradePageViewBuilder.getVerticalPaddingLayout;
import static com.curefit.cfapi.view.viewbuilders.memberships.MembershipDetailPageViewBuilder.BASIC_SUPPORT_PAGE;
import static com.curefit.membership.types.Status.PAUSED;
import static com.curefit.membership.types.Status.PURCHASED;

public class GetPackTransitionDetailsPageViewBuilder {

    Membership membership = null;
    FitnessCenterUtil.UserMembershipType userMembershipType = null;
    Debugger debugger;
    City cityTo = null;
    City cityFrom = null;
    PackTransitionType transitionType = null;
    CenterEntry centerTo = null;
    ProductSubType sourceProductSubType = ProductSubType.GENERAL;
    Boolean isCenterToCenterTransfer = false;

    public PackTransitionPageView getPackTransitionDetailsPageView(Map<String, String> queryParams, ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        fillup(queryParams, interfaces, userContext);

        switch (transitionType) {
            case UPGRADE -> { return getUpgradeDetails(interfaces, userContext); }
            case TRANSFER -> { return getTransferDetails(interfaces, userContext); }
        }

        return null;
    }

    private PackTransitionPageView getUpgradeDetails(ServiceInterfaces interfaces, UserContext userContext) throws Exception {
        debugger.msg("getUpgradeDetails");
        PackTransitionPageView pageView = new PackTransitionPageView();
        List<BaseWidget> baseWidgets = new ArrayList<>();
        SKUComparisonWidgetV2 skuComparisonWidget = new SKUComparisonWidgetV2();
        List<SkuPackV2> packs = new ArrayList<>();

        switch (userMembershipType) {
            case ELITE -> {
                packs.add(getElitePlusSkuPackUpgrade(interfaces, userContext));
            }
            case PRO -> {
                packs.add(getProPlusSkuPackUpgrade(interfaces, userContext));
                packs.add(getEliteSkuPackUpgrade(interfaces, userContext));
                packs.add(getElitePlusSkuPackUpgrade(interfaces, userContext));
            }
            case ELITE_SELECT, PRO_PLUS -> {
                packs.add(getEliteSkuPackUpgrade(interfaces, userContext));
                packs.add(getElitePlusSkuPackUpgrade(interfaces, userContext));
            }
            case PRO_SELECT -> {
                packs.add(getProSkuPackUpgrade(interfaces, userContext));
                packs.add(getProPlusSkuPackUpgrade(interfaces, userContext));
                packs.add(getEliteSkuPackUpgrade(interfaces, userContext));
                packs.add(getElitePlusSkuPackUpgrade(interfaces, userContext));
            }
            default -> throwInternalServerException("Membership can not be upgraded: " + userMembershipType, true);
        }

        packs.removeIf(Objects::isNull);
        debugger.msg("packs", packs);

        skuComparisonWidget.setPacks(packs);
        skuComparisonWidget.setLayoutProps(getVerticalPaddingLayout("30", "70"));
        skuComparisonWidget.setHasDivideBelow(false);
        skuComparisonWidget.setHasDividerTop(false);

        baseWidgets.add(skuComparisonWidget);
        pageView.setWidgets(baseWidgets);
        pageView.setPageTitle("Choose Your Upgrade");
        pageView.setHeaderAction(getHelpAction());

        if (packs.isEmpty()) {
            throwInternalServerException("No packs found for upgrade", true);
        } else if (packs.size() == 1) {
            pageView.setInitAction(packs.getFirst().getCardAction());
        }

        return pageView;
    }

    private SkuPackV2 getEliteSkuPackUpgrade(ServiceInterfaces interfaces, UserContext userContext) {

        Restriction restriction = Restriction.builder().cities(Collections.singletonList(cityFrom.getCityId())).build();

        PackTransitionPricingRequest pricingRequest = getCommonPricingRequest(userContext);
        pricingRequest.setTargetProductType(ProductType.FITNESS);
        pricingRequest.setTargetProductSubType(ProductSubType.GENERAL);
        pricingRequest.setTargetRestrictions(restriction);
        debugger.msg("pricingRequest", pricingRequest);

        try {
            PackTransitionPricingResponse pricingResponse = interfaces.pricingService.getPackTransitionDetails(pricingRequest);
            debugger.msg("pricingResponse", pricingResponse);

            SkuPackV2 skuPack = new SkuPackV2();
            skuPack.setTitle("cultpass ELITE");
            skuPack.setSubTitle("Unlimited access to all cult centres, ELITE & PRO gyms");
            skuPack.setProductPrice(ProductPriceView.builder().currency("₹")
                .listingPrice(StringUtil.formatPrice(pricingResponse.getPriceDistribution().getTotalAmount().toString())).build());
            skuPack.setShowChevron(true);
            skuPack.setPackImage(Constants.ELITE_PACK_BANNER);
            skuPack.setWithGradient(true);
            skuPack.setCardAction(Action.builder().actionType(ActionType.NAVIGATION)
                .url(buildUpgradeMembershipTransitionUrl(ProductSubType.GENERAL, ProductType.FITNESS)).build());
            debugger.msg("skuPack", skuPack);
            return skuPack;
        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
            debugger.err(e);
        }

        return null;

    }

    private SkuPackV2 getElitePlusSkuPackUpgrade(ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        Restriction restriction = Restriction.builder().cities(Collections.singletonList(cityFrom.getCityId())).build();
        try {
            List<OfflineFitnessPack> plusPacks = userContext.getRequestCache().getNonAugmentedPackList(
                ProductType.FITNESS, ProductSubType.PLUS, true, userContext, null
            );
            debugger.msg("plusPacks", plusPacks);

            if (!CollectionUtils.isEmpty(plusPacks)) {
                PackTransitionPricingRequest pricingRequest = getCommonPricingRequest(userContext);
                pricingRequest.setTargetProductType(ProductType.FITNESS);
                pricingRequest.setTargetProductSubType(ProductSubType.PLUS);
                pricingRequest.setTargetRestrictions(restriction);
                debugger.msg("pricingRequest", pricingRequest);

                PackTransitionPricingResponse pricingResponse = interfaces.pricingService.getPackTransitionDetails(pricingRequest);
                debugger.msg("pricingResponse", pricingResponse);

                SkuPackV2 skuPack = new SkuPackV2();
                skuPack.setTitle("cultpass ELITE PLUS");
                skuPack.setSubTitle("Unlimited access to group classes, all gyms, at-home workouts and more");
                skuPack.setProductPrice(ProductPriceView.builder().currency("₹")
                    .listingPrice(StringUtil.formatPrice(pricingResponse.getPriceDistribution().getTotalAmount().toString())).build());
                skuPack.setShowChevron(true);
                skuPack.setPackImage(Constants.ELITE_PLUS_PACK_BANNER);
                skuPack.setWithGradient(true);
                skuPack.setCardAction(Action.builder().actionType(ActionType.NAVIGATION)
                    .url(buildUpgradeMembershipTransitionUrl(ProductSubType.PLUS, ProductType.FITNESS)).build());
                debugger.msg("skuPack", skuPack);
                return skuPack;
            }
        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
            debugger.err(e);
        }
        return null;

    }

    private SkuPackV2 getProSkuPackUpgrade(ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        Restriction restriction = Restriction.builder().cities(Collections.singletonList(cityFrom.getCityId())).build();
        PackTransitionPricingRequest pricingRequest = getCommonPricingRequest(userContext);
        pricingRequest.setTargetProductType(ProductType.GYMFIT_FITNESS_PRODUCT);
        pricingRequest.setTargetProductSubType(ProductSubType.GENERAL);
        pricingRequest.setTargetRestrictions(restriction);
        debugger.msg("pricingRequest", pricingRequest);

        try {
            PackTransitionPricingResponse pricingResponse = interfaces.pricingService.getPackTransitionDetails(pricingRequest);
            debugger.msg("pricingResponse", pricingResponse);

            SkuPackV2 skuPack = new SkuPackV2();
            skuPack.setTitle("cultpass PRO");
            skuPack.setSubTitle("Unlimited access to all Pro centres");
            skuPack.setProductPrice(ProductPriceView.builder().currency("₹")
                    .listingPrice(StringUtil.formatPrice(pricingResponse.getPriceDistribution().getTotalAmount().toString())).build());
            skuPack.setShowChevron(true);
            skuPack.setPackImage(Constants.PRO_PACK_BANNER);
            skuPack.setWithGradient(true);
            skuPack.setCardAction(Action.builder().actionType(ActionType.NAVIGATION)
                    .url(buildUpgradeMembershipTransitionUrl(ProductSubType.GENERAL, ProductType.GYMFIT_FITNESS_PRODUCT)).build());
            debugger.msg("skuPack", skuPack);
            return skuPack;
        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
            debugger.err(e);
        }
        return null;

    }

    private SkuPackV2 getProPlusSkuPackUpgrade(ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        Restriction restriction = Restriction.builder().cities(Collections.singletonList(cityFrom.getCityId())).build();
        try {
            List<OfflineFitnessPack> plusPacks = userContext.getRequestCache().getNonAugmentedPackList(
                ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.PLUS, true, userContext, null
            );
            debugger.msg("plusPacks", plusPacks);

            if (!CollectionUtils.isEmpty(plusPacks)) {
                PackTransitionPricingRequest pricingRequest = getCommonPricingRequest(userContext);
                pricingRequest.setTargetProductType(ProductType.GYMFIT_FITNESS_PRODUCT);
                pricingRequest.setTargetProductSubType(ProductSubType.PLUS);
                pricingRequest.setTargetRestrictions(restriction);
                debugger.msg("pricingRequest", pricingRequest);

                PackTransitionPricingResponse pricingResponse = interfaces.pricingService.getPackTransitionDetails(pricingRequest);
                debugger.msg("pricingResponse", pricingResponse);

                SkuPackV2 skuPack = new SkuPackV2();
                skuPack.setTitle("cultpass PRO PLUS");
                skuPack.setSubTitle("Unlimited access to all Pro centres, at-home workouts and more");
                skuPack.setProductPrice(ProductPriceView.builder().currency("₹")
                        .listingPrice(StringUtil.formatPrice(pricingResponse.getPriceDistribution().getTotalAmount().toString())).build());
                skuPack.setShowChevron(true);
                skuPack.setPackImage(Constants.PRO_PLUS_PACK_BANNER);
                skuPack.setWithGradient(true);
                skuPack.setCardAction(Action.builder().actionType(ActionType.NAVIGATION)
                        .url(buildUpgradeMembershipTransitionUrl(ProductSubType.PLUS, ProductType.GYMFIT_FITNESS_PRODUCT)).build());
                debugger.msg("skuPack", skuPack);

                return skuPack;
            }
        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
            debugger.err(e);
        }
        return null;

    }

    private PackTransitionPricingRequest getCommonPricingRequest(UserContext userContext) {
        PackTransitionPricingRequest pricingRequest = new PackTransitionPricingRequest();
        pricingRequest.setUserId(userContext.getUserProfile().getUserId());
        pricingRequest.setMembershipServiceId(membership.getId());
        pricingRequest.setSourceProductSubType(sourceProductSubType);
        pricingRequest.setPackTransitionType(transitionType);
        return pricingRequest;
    }

    private PackTransitionPageView getTransferDetails(ServiceInterfaces interfaces, UserContext userContext) throws Exception {
        debugger.msg("getTransferDetails");
        PackTransitionPageView pageView = new PackTransitionPageView();
        List<BaseWidget> baseWidgets = new ArrayList<>();
        SKUComparisonWidgetV2 skuComparisonWidget = new SKUComparisonWidgetV2();
        List<SkuPackV2> packs = new ArrayList<>();

        switch (userMembershipType) {
            case ELITE_PLUS -> {
                SkuPackV2 elitePlusSkuPack = getElitePlusSkuPackTransfer(interfaces, userContext);
                if (Objects.isNull(elitePlusSkuPack)) {
                    packs.add(getEliteSkuPackTransfer(interfaces, userContext));
                } else {
                    packs.add(elitePlusSkuPack);
                }
            }
            case ELITE -> {
                packs.add(getEliteSkuPackTransfer(interfaces, userContext));
            }
            case ELITE_SELECT, PRO_SELECT -> {
                packs.add(getSelectSkuPackTransfer(interfaces, userContext));
            }
        }

        packs.removeIf(Objects::isNull);
        debugger.msg("packs", packs);

        skuComparisonWidget.setPacks(packs);
        skuComparisonWidget.setLayoutProps(getVerticalPaddingLayout("30", "70"));
        skuComparisonWidget.setHasDivideBelow(false);
        skuComparisonWidget.setHasDividerTop(false);

        baseWidgets.add(skuComparisonWidget);
        pageView.setWidgets(baseWidgets);
        pageView.setPageTitle("Choose Your Transfer");
        pageView.setHeaderAction(getHelpAction());

        if (packs.isEmpty()) {
            throwInternalServerException("No packs found for transfer", true);
        } else if (packs.size() == 1) {
            pageView.setInitAction(packs.getFirst().getCardAction());
        }

        return pageView;
    }

    private SkuPackV2 getSelectSkuPackTransfer(ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        Restriction restriction = Restriction.builder().centers(Collections.singletonList(centerTo.getId().intValue())).build();
        PackTransitionPricingRequest pricingRequest = getCommonPricingRequest(userContext);
        if (GymUtil.isProCenter(centerTo)) {
            pricingRequest.setTargetProductType(ProductType.GYMFIT_FITNESS_PRODUCT);
        } else {
            pricingRequest.setTargetProductType(ProductType.FITNESS);
        }
        pricingRequest.setTargetProductSubType(ProductSubType.GENERAL);
        pricingRequest.setTargetRestrictions(restriction);
        debugger.msg("pricingRequest", pricingRequest);

        try {
            PackTransitionPricingResponse pricingResponse = interfaces.pricingService.getPackTransitionDetails(pricingRequest);
            debugger.msg("pricingResponse", pricingResponse);

            SkuPackV2 skuPack = new SkuPackV2();
            skuPack.setTitle("cultpass Select");
            skuPack.setSubTitle("Unlimited access to " + centerTo.getName());
            skuPack.setProductPrice(ProductPriceView.builder().currency("₹")
                    .listingPrice(StringUtil.formatPrice(pricingResponse.getPriceDistribution().getTotalAmount().toString())).build());
            skuPack.setShowChevron(true);
            skuPack.setPackImage(Constants.ELITE_PACK_BANNER);
            skuPack.setWithGradient(true);
            skuPack.setCardAction(Action.builder().actionType(ActionType.NAVIGATION).url(buildTransferMembershipTransitionUrl(pricingRequest)).build());
            debugger.msg("skuPack", skuPack);
            return skuPack;
        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
            debugger.err(e);
        }

        return null;
    }

    private SkuPackV2 getEliteSkuPackTransfer(ServiceInterfaces interfaces, UserContext userContext) {

        Restriction restriction = Restriction.builder().cities(Collections.singletonList(cityTo.getCityId())).build();

        PackTransitionPricingRequest pricingRequest = getCommonPricingRequest(userContext);
        pricingRequest.setTargetProductType(ProductType.FITNESS);
        pricingRequest.setTargetProductSubType(ProductSubType.GENERAL);
        pricingRequest.setTargetRestrictions(restriction);
        debugger.msg("pricingRequest", pricingRequest);

        try {
            PackTransitionPricingResponse pricingResponse = interfaces.pricingService.getPackTransitionDetails(pricingRequest);
            debugger.msg("pricingResponse", pricingResponse);

            SkuPackV2 skuPack = new SkuPackV2();
            skuPack.setTitle("cultpass ELITE");
            skuPack.setSubTitle("Unlimited access to all cult centres, ELITE & PRO gyms");
            skuPack.setPackImage("image/transform/elite.png");
            skuPack.setProductPrice(ProductPriceView.builder().currency("₹")
                    .listingPrice(StringUtil.formatPrice(pricingResponse.getPriceDistribution().getTotalAmount().toString())).build());
            skuPack.setShowChevron(true);
            skuPack.setPackImage("image/skuTitlesV9/black.png");
            skuPack.setWithGradient(true);
            skuPack.setCardAction(Action.builder().actionType(ActionType.NAVIGATION).url(buildTransferMembershipTransitionUrl(pricingRequest)).build());
            debugger.msg("skuPack", skuPack);
            return skuPack;
        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
            debugger.err(e);
        }

        return null;

    }

    private SkuPackV2 getElitePlusSkuPackTransfer(ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        Restriction restriction = Restriction.builder().cities(Collections.singletonList(cityTo.getCityId())).build();
        try {
            List<AugmentedOfflineFitnessPack> plusPacks = interfaces.offlineFitnessPackService.searchCachedPacksWithAugments(
                AugmentedPackSearchRequest.builder()
                    .namespace(Namespace.OFFLINE_FITNESS)
                    .productTypes(Collections.singletonList(ProductType.FITNESS))
                    .productSubType(ProductSubType.PLUS)
                    .restrictions(restriction)
                    .status(Status.ACTIVE)
                    .saleEnabled(true)
                    .visibility(Visibility.APP)
                    .isPrivate(false)
                    .augments(PackAugmentRequest.builder()
                        .context(AugmentContext.builder().userId(userContext.getUserProfile().getUserId()).build())
                        .includeExtraCharges(false)
                        .includeExhaustiveBenefits(false)
                        .build())
                    .build()
            ).get();
            debugger.msg("plusPacks", plusPacks);

            if (!CollectionUtils.isEmpty(plusPacks)) {
                PackTransitionPricingRequest pricingRequest = getCommonPricingRequest(userContext);
                pricingRequest.setTargetProductType(ProductType.FITNESS);
                pricingRequest.setTargetProductSubType(ProductSubType.PLUS);
                pricingRequest.setTargetRestrictions(restriction);
                debugger.msg("pricingRequest", pricingRequest);

                PackTransitionPricingResponse pricingResponse = interfaces.pricingService.getPackTransitionDetails(pricingRequest);
                debugger.msg("pricingResponse", pricingResponse);

                SkuPackV2 skuPack = new SkuPackV2();
                skuPack.setTitle("cultpass ELITE PLUS");
                skuPack.setSubTitle("Unlimited access to all cult centres, ELITE & PRO gyms");
                skuPack.setPackImage("image/transform/elite.png");
                skuPack.setProductPrice(ProductPriceView.builder().currency("₹")
                        .listingPrice(StringUtil.formatPrice(pricingResponse.getPriceDistribution().getTotalAmount().toString())).build());
                skuPack.setShowChevron(true);
                skuPack.setPackImage("image/skuTitlesV9/black.png");
                skuPack.setWithGradient(true);
                skuPack.setCardAction(Action.builder().actionType(ActionType.NAVIGATION).url(buildTransferMembershipTransitionUrl(pricingRequest)).build());
                debugger.msg("skuPack", skuPack);
                return skuPack;
            }
        } catch (Exception e) {
            debugger.err(e);
        }
        return null;
    }


    private void fillup(Map<String, String> queryParams, ServiceInterfaces interfaces, UserContext userContext) throws BaseException, ExecutionException, InterruptedException {
        debugger = Debugger.getDebuggerFromUserContext(userContext);

        String membershipServiceId = queryParams.getOrDefault("membershipServiceId", null);
        String cityIdTo = queryParams.getOrDefault("cityIdTo", null);
        String centerServiceIdTo = queryParams.getOrDefault("centerServiceIdTo", null);
        transitionType = PackTransitionType.valueOf(queryParams.getOrDefault("transitionType", null));

        membership = interfaces.membershipService.getMembershipById(Long.parseLong(membershipServiceId), userContext.getUserProfile().getUserId()).get();
        userMembershipType = FitnessCenterUtil.getUserMembershipType(membership, true, userContext, true);

        debugger.msg(membership);
        debugger.msg("cityIdTo", cityIdTo);
        debugger.msg("centerServiceIdTo", centerServiceIdTo);
        debugger.msg("membershipServiceId", membershipServiceId);
        debugger.msg("transitionType", transitionType);
        debugger.msg("userMembershipType", userMembershipType);

        cityFrom = MembershipUtil.getCityIdFromMembership(membership, userContext, interfaces.cityCache, interfaces.catalogueServicePMS);
        if (Objects.nonNull(cityIdTo)) cityTo = interfaces.cityCache.getCityById(cityIdTo);

        if (Objects.nonNull(centerServiceIdTo)) {
            centerTo = interfaces.centerService.getCenterDetails(Long.valueOf(centerServiceIdTo), true, null, null).get();
        }
        debugger.msg("cityFrom", cityFrom);
        debugger.msg("cityTo", cityTo);
        debugger.msg("centerTo", centerTo);

        switch (transitionType) {
            case TRANSFER -> {
                if (Objects.isNull(centerTo)) throwInternalServerException("Center is not available for transfer", true);
                if (Objects.isNull(cityTo)) throwInternalServerException("City is not available for transfer", true);
                isCenterToCenterTransfer = Objects.equals(centerTo.getCity(), cityFrom.getCityId());

                switch (userMembershipType) {
                    case ELITE_PLUS, ELITE -> {
                        if (isCenterToCenterTransfer) {
                            String message = "Membership Can't be transferred to the Same City";
                            debugger.err(message);
                            throw new BaseException(message, LogType.INFO, ErrorUtil.CFAPI_NAMESPACE, AppStatus.PRECONDITION_FAILED, "ERR_SAME_CITY_TRANSFER", message);
                        }
                    }
                }
            }
            case UPGRADE -> {
                if (Objects.isNull(cityTo)) cityTo = cityFrom;
                if (!Objects.equals(cityFrom.getCityId(), cityTo.getCityId())) throwInternalServerException("Can't upgrade membership to another city", true);
            }
        }

        switch (userMembershipType) {
            case PRO_PLUS, ELITE_PLUS -> sourceProductSubType = ProductSubType.PLUS;
            case ELITE_LITE, PRO_SELECT_LITE, ELITE_SELECT_LITE -> sourceProductSubType = ProductSubType.LITE;
        }

        boolean isActiveState = membership.getStatus().equals(PURCHASED) || membership.getStatus().equals(PAUSED);
        debugger.msg("isActiveState", isActiveState);

        if (!isActiveState) {
            String message = "InActive Membership Can't be transferred";
            throw new BaseException(message, LogType.INFO, ErrorUtil.CFAPI_NAMESPACE, AppStatus.PRECONDITION_FAILED, "ERR_INACTIVE_MEMBERSHIP_TRANSFER_UPGRADE", message);
        }
    }

    private String buildUpgradeMembershipTransitionUrl(ProductSubType targetProductSubType, ProductType targetProductType) {
        return "curefit://transition_membership?membershipServiceId=" + membership.getId()
                + "&transitionType=" + transitionType.getValue()
                + "&sourceProductSubType=" + sourceProductSubType.getValue()
                + "&targetProductSubType=" + targetProductSubType.getValue()
                + "&targetProductType=" + targetProductType.toString()
                + "&cityIdTo=" + cityFrom.getCityId();
    }

    private String buildTransferMembershipTransitionUrl(PackTransitionPricingRequest pricingRequest) {
        String centerId = Objects.nonNull(centerTo.getMeta().get(CULT_CENTER_ID_KEY)) ? centerTo.getMeta().get(CULT_CENTER_ID_KEY).toString() : null;
        return "curefit://transition_membership?membershipServiceId=" + membership.getId()
                + "&transitionType=" + transitionType.getValue()
                + "&sourceProductSubType=" + sourceProductSubType.getValue()
                + "&targetProductSubType=" + pricingRequest.getTargetProductSubType().getValue()
                + "&targetProductType=" + pricingRequest.getTargetProductType().toString()
                + "&cityIdTo=" + cityTo.getCityId()
                + "&centerServiceIdTo=" + centerTo.getId()
                + "&centerIdTo=" + centerId
                + "&selectedCenterName=" + centerTo.getName();
    }

    private Action getHelpAction() {
        return Action.builder().title("HELP").actionType(NAVIGATION).url(BASIC_SUPPORT_PAGE).build();
    }

    private void throwInternalServerException(String message, Boolean throwSentry) throws BaseException {
        debugger.err(message);
        throw new BaseException(
            message, throwSentry ? LogType.ERROR : LogType.INFO, ErrorUtil.CFAPI_NAMESPACE,
            throwSentry ? AppStatus.INTERNAL_SERVER_ERROR : AppStatus.PRECONDITION_FAILED,
            ErrorUtil.ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR.toString(), message
        );
    }
}
