package com.curefit.cfapi.view.viewbuilders.chroniccare.renewal;
import com.curefit.albus.common.BundleSellableProduct;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.view.viewbuilders.chroniccare.RenewSubscriptionPageViewBuilder;
import com.curefit.cfapi.view.viewmodels.chroniccare.RenewSubscriptionPageView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.renewal.RenewalCarouselWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.CarefitIllegalArgumentException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.subuser.common.pojo.PatientDetail;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class RenewalCarouselWidgetBuilder extends BaseWidgetNonVM {
    final ChronicCarePatientService chronicCarePatientService;
    final UserOnboardingService userOnboardingService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final ExceptionReportingService exceptionReportingService;
    final AppConfigCache appConfigCache;
    final ServiceInterfaces serviceInterfaces;
    final RenewSubscriptionPageViewBuilder renewSubscriptionPageViewBuilder;

    public RenewalCarouselWidget buildView(UserContext userContext) throws BaseException {
        ActivePackResponse lastPack;
        Optional<ActivePackResponse> activePackOptional = userOnboardingService.getSugarFitActivePack(userContext.getUserProfile().getUserId());
        if (activePackOptional.isPresent()) {
            lastPack = activePackOptional.get();
        } else {
            Optional<ActivePackResponse> expiredPackOptional = userOnboardingService.getSugarFitExpiredPack(userContext.getUserProfile().getUserId());
            lastPack = expiredPackOptional.orElseThrow(() -> new ResourceNotFoundException(
                    String.format("No expired and active pack for user :: %s", userContext.getUserProfile().getUserId())));
        }
        if (lastPack.getBundleProduct().getIsTrialProduct()) {
            throw new CarefitIllegalArgumentException(
                    String.format("Renewal Not allowed for trial pack user :: %s", userContext.getUserProfile().getUserId()));
        }
        return getRenewalCarouselWidget(userContext, lastPack);
    }

    private List<BundleSellableProduct> getRelatedExtensionPacks(String userId, ActivePackResponse latestPack) {
        String target = latestPack.getBundleProduct().getProductSpecs().get("target").textValue();
        return chronicCareServiceHelper.getAllRenewalPacks(userId, target, latestPack.getBundleProduct().getTenant());
    }

    private RenewalCarouselWidget getRenewalCarouselWidget(UserContext userContext, ActivePackResponse latestPack) throws ResourceNotFoundException {
        String userId = userContext.getUserProfile().getUserId();
        PatientDetail patient = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        RenewalCarouselWidget widget = new RenewalCarouselWidget();
        widget.setTitle("Renew your");
        widget.setBoldTitle(" Subscription");

        try {
            List<BundleSellableProduct> relatedExtensionPacks = getRelatedExtensionPacks(userId, latestPack);
            if(ChronicCareAppUtil.isRenewalPacksV2Supported(userContext)){
                List<RenewSubscriptionPageView.PackData> packDataList = renewSubscriptionPageViewBuilder.buildRecommendedPacks(
                        relatedExtensionPacks, userContext, patient, false
                );
                widget.setRecommendedPacks(packDataList);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return widget;
    }
}



