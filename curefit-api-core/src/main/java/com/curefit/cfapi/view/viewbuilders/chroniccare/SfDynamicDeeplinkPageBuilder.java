package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.albus.common.PatientPreferredAgentResponse;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.chronic.UserOnboardingActionWithContext;
import com.curefit.cfapi.dto.ChronicCareTeam;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.SfHomePageUtil;
import com.curefit.cfapi.view.viewmodels.SupportPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfDynamicDeeplinkPage;
import com.curefit.cfapi.widgets.chroniccare.HelpAndSupportWidget;
import com.curefit.cfapi.widgets.chroniccare.RMAgentWidget;
import com.curefit.ollivander.common.pojo.response.agent.AgentResponse;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.sugarfit.sms.client.SMSClient;
import com.sugarfit.sms.response.RMResponse;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static com.curefit.cfapi.util.ChronicCareAppUtil.*;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class SfDynamicDeeplinkPageBuilder {
    final ExceptionReportingService exceptionReportingService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final UserOnboardingService userOnboardingService;
    final ServiceInterfaces serviceInterfaces;
    final ChronicCarePatientService chronicCarePatientService;

    public SfDynamicDeeplinkPage buildView(UserContext userContext, SfDynamicDeeplinkPage.DeeplinkType deeplinkType) throws Exception {
        SfDynamicDeeplinkPage result = new SfDynamicDeeplinkPage();
        if (Objects.isNull(deeplinkType)) {
            return result;
        }

        Action action = null;
        switch (deeplinkType) {
            case COACH_CONSULTATION ->
                    action = chronicCareServiceHelper.getCoachConsultBookingAction(userContext, "");
            case DOCTOR_CONSULTATION ->
            {
                Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());
                CompletableFuture<UserOnboardingActionWithContext> onboardingActionFuture = getUserOnboardingActionsFuture(
                        serviceInterfaces,
                        userContext,
                        activePackResponse,
                        userOnboardingService,
                        exceptionReportingService
                );
                UserOnboardingActionWithContext onboardingActions = onboardingActionFuture.get();

                if (Objects.nonNull(onboardingActions) && activePackResponse.isPresent()
                        && Objects.nonNull(onboardingActions.getCoachCardActionWithContext())
                        && Objects.nonNull(onboardingActions.getCoachCardActionWithContext().getContext())
                        && !CollectionUtils.isEmpty(onboardingActions.getCoachCardActionWithContext().getContext().getProductCodes())) {
                    String coachConsultationProduct = onboardingActions.getCoachCardActionWithContext().getContext().getProductCodes().stream().findFirst().get();
                    PatientDetail patient = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
                    ChronicCareTeam assignedCareTeam = chronicCareServiceHelper.getAssignedCareTeam(userContext, patient.getId(), activePackResponse.get().getBundleProduct());
                    PatientPreferredAgentResponse doctor = assignedCareTeam.getDoctor();
                    Long coachCenterId = doctor.getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
                    Date upcomingCoachConsultationDate = chronicCareServiceHelper.getUpcomingConsultationDateForDisablingMultipleBookings(userContext, userContext.getUserProfile().getUserId(), coachConsultationProduct);
                    if (upcomingCoachConsultationDate != null) {
                        // Active booking exists. Don't allow one more
                    } else {
                        action = Action.builder()
                                .isEnabled(onboardingActions.getCoachCardActionWithContext().getAction().isActionPermitted())
                                .title("Book Slot")
                                .actionType(ActionType.NAVIGATION)
                                .url(SfHomePageUtil.getAgentDatePickerUrl(coachConsultationProduct, coachCenterId, doctor.getAgentResponse().getId(), activePackResponse.get()))
                                .build();
                    }
                }
            }
            case MENTAL_HEALTH_CONSULTATION ->
                    action = chronicCareServiceHelper.getPyschologyConsultBookingAction(userContext);
            default -> {
            }
        }
        result.setAction(action);
        return result;
    }
}
