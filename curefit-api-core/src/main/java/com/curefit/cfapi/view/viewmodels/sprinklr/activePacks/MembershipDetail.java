package com.curefit.cfapi.view.viewmodels.sprinklr.activePacks;

import com.curefit.cfapi.view.viewmodels.sprinklr.center.Center;
import com.curefit.cfapi.widgets.membership.MembershipBenefitsItem;
import com.curefit.product.enums.ProductType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;


@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MembershipDetail {
    Long membershipServiceRefId;
    String name;
    String startDate;
    String endDate;
    String orderId;
    ProductType productType;
    String status;
    String cityId;
    String membershipType;
    MembershipBenefitsItem benefits;
    Center accessCenter;
    Long membershipId;
}