package com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp;

import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
public class RagusAiConfig {
    boolean disableDirectChat = false;
    boolean disableQuickLinks = false;

    public RagusAiConfig() {

    }
}
