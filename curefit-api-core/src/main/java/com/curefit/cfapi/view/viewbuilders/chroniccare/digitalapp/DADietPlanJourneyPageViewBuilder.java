package com.curefit.cfapi.view.viewbuilders.chroniccare.digitalapp;


import com.curefit.albus.response.ActivePackResponse;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp.DADietPlanJourneyPageView;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.curefit.cfapi.pojo.app.action.ActionType.NAVIGATION;
import static com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper.SUGARFIT_LONG_COACH_CONSULT;

@Getter
@Setter
@Slf4j
@Component
@RequiredArgsConstructor
public class DADietPlanJourneyPageViewBuilder {

    final ServiceInterfaces serviceInterfaces;
    final ChronicCareServiceHelper chronicCareServiceHelper;

    public DADietPlanJourneyPageView buildView(UserContext userContext) {
        DADietPlanJourneyPageView dietPlanJourneyPageView = new DADietPlanJourneyPageView();

        try {
            boolean isDietPlanRequested = serviceInterfaces.getChronicCareServiceHelper().isDietPlanRequestedByDigitalAppUser(serviceInterfaces, userContext);
            String dietPlanPdfUrl = serviceInterfaces.getChronicCareServiceHelper().getPlanPdfIfDietPlanAvailableForDigitalAppUser(serviceInterfaces, userContext);
            dietPlanJourneyPageView.setDietPlanUrl(dietPlanPdfUrl);
            dietPlanJourneyPageView.setIsDietPlanRequested(isDietPlanRequested);
            dietPlanJourneyPageView.setIsSugarFitInternationalUser(ChronicCareAppUtil.isInternationalSugarfitUser(userContext));
            if (!isDietPlanRequested && (dietPlanPdfUrl == null || dietPlanPdfUrl.isEmpty())) {
                if (ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
                    CompletableFuture<ActivePackResponse> activePackResponseFuture = serviceInterfaces.getSfUserOnboardingService()
                            .getSugarFitActivePackResponseFuture(String.valueOf(userContext.getUserProfile().getUserId()));
                    ActivePackResponse activePackResponse = activePackResponseFuture.get();
                    if (ChronicCareServiceHelper.isCoachConsultationSupportedDigiInternationalUser(activePackResponse)) {
                        dietPlanJourneyPageView.setGenerateDietPlanAction(Action.builder().actionType(NAVIGATION)
                                .url(chronicCareServiceHelper.getDigitalAppDatePickerUrl(
                                        SUGARFIT_LONG_COACH_CONSULT, null, activePackResponse)).title("Book Coach Consult").build());
                    }
                } else {
                    dietPlanJourneyPageView.setGenerateDietPlanAction(Action.builder().url("curefit://sfdietplanpreferencespage").title("Generate your diet plan")
                            .actionType(ActionType.NAVIGATION).iconUrl("http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/fork-2025-03-14-23:04.png").build());
                }
            }
            if (dietPlanPdfUrl != null && !dietPlanPdfUrl.isEmpty()) {
                Action action = new Action();
                Map<String, Object> actionMeta = new HashMap<>();
                actionMeta.put("fileUrl", dietPlanPdfUrl);
                actionMeta.put("fileTitle", "Diet Plan");
                actionMeta.put("documentType", "DIET_PLAN");
                action.setActionType(ActionType.OPEN_ONLINE_PDF);
                action.setTitle("View your diet plan");
                action.setMeta(actionMeta);
                dietPlanJourneyPageView.setViewDietPlanAction(action);
            }
        } catch (Exception e){
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return dietPlanJourneyPageView;
    }
}
