package com.curefit.cfapi.view.viewbuilders.chroniccare.polls;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.view.viewmodels.chroniccare.polls.SfPollsPageView;
import com.curefit.cfapi.widgets.chroniccare.poll.SfPollListItemWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.sugarfit.poll.client.PollSupportClient;
import com.sugarfit.poll.enums.PollStatus;
import com.sugarfit.poll.enums.PollType;
import com.sugarfit.poll.enums.Status;
import com.sugarfit.poll.pojo.PollEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class SfPollsPageBuilder {

    final ExceptionReportingService exceptionReportingService;
    final PollSupportClient pollSupportClient;
    final ServiceInterfaces serviceInterfaces;

    public SfPollsPageView buildView(UserContext userContext, String pollType, Integer pageNumber) {

        SfPollsPageView pageView = new SfPollsPageView();

        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            List<PollEntry> polls = new ArrayList<>();
            if (pollType.equals("activePolls")) {
                SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                        RequestType.PLATFORM_SEGMENTS,
                        userContext).get();
                Set<String> relevantSegments = serviceInterfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.SF_POLL_CLIENT);
                List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(relevantSegments));
                polls = pollSupportClient.fetchActivePollsV2(userId, pageNumber * 20, 20, "createdOn", "DESC", userSegments).getElements();
            } else if (pollType.equals("participatedPolls")) {
                polls = pollSupportClient.fetchUserParticipatedPolls(userId, pageNumber * 20, 20, "createdOn", "DESC").getElements();
            }
            if (CollectionUtils.isNotEmpty(polls)) {
                polls.forEach(p -> {
                    if (p != null && p.getPollType() != PollType.DDS) {
                        try {
                            pageView.addWidget(buildPollWidget(userContext, p));
                        } catch (BaseException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        } catch (Exception e) {
            String message = String.format("Error in building page, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }

        return pageView;
    }

    private SfPollListItemWidget buildPollWidget(UserContext userContext, PollEntry p) throws BaseException {
        if (p.getStatus() != null && p.getStatus().equals(Status.PUBLISHED)) {
            // fetch the poll again to get all the details about questions in the PollEntry object
            PollEntry detailedPollEntry = pollSupportClient.fetchPoll(Long.valueOf(userContext.getUserProfile().getUserId()), p.getId());

            boolean isCompleted = detailedPollEntry.getPollStatus() != null && detailedPollEntry.getPollStatus().equals(PollStatus.COMPLETED);
            boolean hasMultipleQuestions = detailedPollEntry.getQuestions() != null && detailedPollEntry.getQuestions().size() > 1;

            String pageUrl = "curefit://sfpolldetailspage?pollId=" + detailedPollEntry.getId();
            if (isCompleted && hasMultipleQuestions) {
                pageUrl = "curefit://sfpollresult?actionType=VIEW_QUESTIONS&pollId="+ detailedPollEntry.getId();
            }
            Action action = Action.builder().url(pageUrl).actionType(ActionType.NAVIGATION).build();
            SfPollListItemWidget pollWidget = new SfPollListItemWidget();
            pollWidget.setPollId(detailedPollEntry.getId());
            pollWidget.setTitle(detailedPollEntry.getName());
            pollWidget.setTime(detailedPollEntry.getStartTime());
            pollWidget.setVotesCount(detailedPollEntry.getVoteCount());
            pollWidget.setCardAction(action);
            pollWidget.setPollType(detailedPollEntry.getPollType());
            return pollWidget;
        }
        return null;
    }

}