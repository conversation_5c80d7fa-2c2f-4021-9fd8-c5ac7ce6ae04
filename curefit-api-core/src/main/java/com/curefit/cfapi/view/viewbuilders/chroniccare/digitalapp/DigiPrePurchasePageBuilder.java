package com.curefit.cfapi.view.viewbuilders.chroniccare.digitalapp;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.chroniccare.sfdigital.DigiRiskProfile;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp.DigiPrePurchasePageView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DigiRiskScoreGoalWidget;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DigiRiskScoreWidget;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DigiSpacerWidget;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.GenericBannerWidget;
import com.curefit.common.data.exception.BaseException;
import com.sugarfit.catalog.client.CatalogClient;
import com.sugarfit.chs.pojo.UserMetricEntry;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.curefit.cfapi.view.viewbuilders.chroniccare.RenewSubscriptionPageViewBuilder.getVedicNumberFormat;

@Getter
@Setter
@Slf4j
@Component
@RequiredArgsConstructor
public class DigiPrePurchasePageBuilder {

    final ServiceInterfaces serviceInterfaces;
    final CatalogClient catalogClient;
    final ChronicCareServiceHelper chronicCareServiceHelper;

    public DigiPrePurchasePageView buildView(UserContext userContext, boolean paymentDisabled) throws BaseException {
        DigiPrePurchasePageView page = new DigiPrePurchasePageView();

        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            Map<String, UserMetricEntry> riskScoreProfile = serviceInterfaces.getChsClient().getProfileRiskScoreForUserId(userId);
            page.addWidget(getDigiRiskScoreWidget(riskScoreProfile));
            page.addWidget(getPageDividerWidget());

            DigiRiskScoreGoalWidget riskScoreGoalWidget = getDigiRiskScoreGoalWidget(riskScoreProfile);
            if (Objects.nonNull((riskScoreGoalWidget))) {
                page.addWidget(riskScoreGoalWidget);
                page.addWidget(getPageDividerWidget());
            }
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/prepurchase_banner_1-2025-07-21-17:42.jpg"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/prepurchase_banner_2-2025-07-21-17:42.jpg"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/prepurchase_banner_3-2025-07-21-17:42.jpg"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/prepurchase_banner_4-2025-07-21-17:43.jpg"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/prepurchase_banner_5-2025-07-21-17:43.jpg"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/prepurchase_banner_6-2025-07-21-17:44.jpg"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/prepuchase_banner5-2025-03-06-18:26.png"));
            if (!paymentDisabled) {
                DigiPrePurchasePageView.PackPurchaseModalData packPurchaseModalData = chronicCareServiceHelper.getDigiPackPurchaseModalData(userContext);
                page.setPackPurchaseModalData(packPurchaseModalData);
                page.setAction(Action.builder().title("Start Managing Diabetes").actionType(ActionType.SHOW_DIGI_PACKS_PURCHASE_MODAL).build());
            }
        } catch (Exception e) {
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return page;
    }

    public DigiRiskScoreWidget getDigiRiskScoreWidget(Map<String, UserMetricEntry> riskScoreProfile) {
        DigiRiskScoreWidget riskScoreWidget = new DigiRiskScoreWidget();
        if (Objects.nonNull(riskScoreProfile) && riskScoreProfile.containsKey("diabetes_risk_score")) {
            UserMetricEntry diabetesRiskMetric = riskScoreProfile.get("diabetes_risk_score");
            Double diabetesRiskScore = Double.valueOf(diabetesRiskMetric.getValue());
            String diabetesRiskLevel = getDiabetesRiskScoreLevel(diabetesRiskScore);

            DigiRiskScoreWidget.RiskScore riskScore = new DigiRiskScoreWidget.RiskScore();
            riskScore.setBgImg(getDiabetesRiskScoreMeterImg(diabetesRiskLevel));
            riskScore.setScore(diabetesRiskScore);
            riskScore.setRiskLevel(diabetesRiskLevel);
            riskScore.setRiskLevelColor(getDiabetesRiskScoreColor(diabetesRiskLevel));
            riskScoreWidget.setRiskScore(riskScore);

            DigiRiskProfile diabetesRiskProfile = new DigiRiskProfile();
            diabetesRiskProfile.setScore(diabetesRiskScore);
            diabetesRiskProfile.setRiskLevel(diabetesRiskLevel);
            diabetesRiskProfile.setRiskLevelColor(getDiabetesRiskScoreColor(diabetesRiskLevel));
            diabetesRiskProfile.setScoreDescription("Regular coaching and expert support can lead to a reduction in HbA1c levels of up to 1.5% over 3 months");
            diabetesRiskProfile.setScoreImage(getDiabetesRiskScoreScaleImg(diabetesRiskLevel));
            riskScoreWidget.setDiabetesRiskProfile(diabetesRiskProfile);
        }
        if (Objects.nonNull(riskScoreProfile) && riskScoreProfile.containsKey("obesity_risk_score")) {
            UserMetricEntry obesityRiskMetric = riskScoreProfile.get("obesity_risk_score");
            Double obesityRiskScore = Double.valueOf(obesityRiskMetric.getValue());

            DigiRiskProfile obesityRiskProfile = new DigiRiskProfile();
            obesityRiskProfile.setScore(obesityRiskScore);
            String obesityLevel = getObesityRiskScoreLevel(obesityRiskScore);
            obesityRiskProfile.setRiskLevel(obesityLevel);
            obesityRiskProfile.setRiskLevelColor(getRiskScoreColor(obesityLevel));
            obesityRiskProfile.setScoreDescription("Studies have shown a strong correlation between weight, diet, activity levels and insulin resistance.");
            riskScoreWidget.setObesityRiskProfile(obesityRiskProfile);
        }
        if (Objects.nonNull(riskScoreProfile) && riskScoreProfile.containsKey("lifestyle_risk_score")) {
            UserMetricEntry lifestyleRiskMetric = riskScoreProfile.get("lifestyle_risk_score");
            Double lifestyleRiskScore = Double.valueOf(lifestyleRiskMetric.getValue());

            DigiRiskProfile lifeStyleRiskProfile = new DigiRiskProfile();
            lifeStyleRiskProfile.setScore(lifestyleRiskScore);
            String lifestyleRiskLevel = getLifestyleRiskScoreLevel(lifestyleRiskScore);
            lifeStyleRiskProfile.setRiskLevel(lifestyleRiskLevel);
            lifeStyleRiskProfile.setRiskLevelColor(getRiskScoreColor(lifestyleRiskLevel));
            lifeStyleRiskProfile.setScoreDescription("Studies show that poor lifestyle choices like lack of exercise and poor diet have impact on insulin resistance");
            riskScoreWidget.setLifestyleRiskProfile(lifeStyleRiskProfile);
        }
        return riskScoreWidget;
    }

    public DigiRiskScoreGoalWidget getDigiRiskScoreGoalWidget(Map<String, UserMetricEntry> riskScoreProfile) {
        if (Objects.nonNull(riskScoreProfile) && riskScoreProfile.containsKey("diabetes_risk_score")) {
            UserMetricEntry diabetesRiskMetric = riskScoreProfile.get("diabetes_risk_score");
            DigiRiskScoreGoalWidget riskScoreGoalWidget = new DigiRiskScoreGoalWidget();
            riskScoreGoalWidget.setCurrentScore(Double.valueOf(diabetesRiskMetric.getValue()));
            riskScoreGoalWidget.setTargetScore(Double.parseDouble(diabetesRiskMetric.getValue()) > 40 ? Double.parseDouble(diabetesRiskMetric.getValue()) - 20: 0);
            riskScoreGoalWidget.setCoachImage("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/user-image-2025-03-06-19:50.png");   // exists in class
            riskScoreGoalWidget.setPeopleReversed("57,833+");
            riskScoreGoalWidget.setTasks(getGoalTasks());
            return riskScoreGoalWidget;
        }
        return null;
    }

    private String getCtaActionTitle(DigiPrePurchasePageView.PackPurchaseModalData packPurchaseModalData){
        try {
            long listingPrice =  packPurchaseModalData.getRecommendedPacks()
                    .getFirst()
                    .getListingPrice()
                    .longValue();
            return String.format("Buy Now at ₹%s/-", getVedicNumberFormat(listingPrice));
        } catch (Exception e) {
            return "Buy Now at lowest prices!";
        }
    }

    public DigiSpacerWidget getPageDividerWidget() {
        DigiSpacerWidget spacerWidget = new DigiSpacerWidget();
        spacerWidget.setBackgroundColor("#EEEFEF");
        return spacerWidget;
    }

    public List<String> getGoalTasks() {
        List<String> tasks = new ArrayList<>();
        tasks.add("Improve my HbA1c and reverse insulin resistance");
        tasks.add("Minimize the need for medical interventions");
        tasks.add("Prevent or delay diabetes-related complications");
        tasks.add("Feel stronger, lighter, and more in control of my health");
        return tasks;
    }

    public String getDiabetesRiskScoreLevel(Double score){
        if (score > 75) {
            return "Very High";
        } else if (score > 50){
            return "High";
        } else if (score >25){
            return "Medium";
        }
        return "Low";
    };

    public String getObesityRiskScoreLevel(Double score){
        if (score > 66) {
            return "High";
        } else if (score > 33){
            return "Medium";
        }
        return "Low";
    };

    public String getLifestyleRiskScoreLevel(Double score){
        return getDiabetesRiskScoreLevel(score);
    };

    public String getDiabetesRiskScoreColor(String riskScoreLevel){
        return switch (riskScoreLevel) {
            case "Very High" -> "#E02E3D";
            case "High" -> "#E37F1B";
            case "Medium" -> "#FEC737";
            default -> "#08875D";
        };
    };

    public String getRiskScoreColor(String riskScoreLevel){
        return switch (riskScoreLevel) {
            case "Very High", "High" -> "#E02E3D";
            case "Medium" -> "#E37F1B";
            default -> "#08875D";
        };
    };

    public String getDiabetesRiskScoreScaleImg(String riskScoreLevel){
        return switch (riskScoreLevel) {
            case "Very High" -> "http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/risk_profile_scale_very_high-2025-03-13-14:46.png";
            case "High" -> "http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/risk_profile_scale_high-2025-03-13-14:46.png";
            case "Medium" -> "http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/risk_profile_scale_medium-2025-03-13-14:46.png";
            default -> "http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/risk_profile_scale_low-2025-03-13-14:46.png";
        };
    }

    public String getDiabetesRiskScoreMeterImg(String riskScoreLevel){
        return switch (riskScoreLevel) {
            case "Very High" -> "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/risk_profile_meter_high-2025-03-05-16:34.jpg";
            case "High" -> "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/risk_profile_meter_medium-2025-03-05-16:34.jpg";
            case "Medium" -> "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/risk_profile_meter_low-2025-03-05-16:34.jpg";
            default -> "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/risk_profile_meter_normal-2025-03-05-16:33.jpg";
        };
    }
}
