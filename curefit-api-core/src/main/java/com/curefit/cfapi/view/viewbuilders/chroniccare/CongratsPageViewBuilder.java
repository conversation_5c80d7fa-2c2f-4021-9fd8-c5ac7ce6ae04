package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.albus.common.GroupClassSellableProductEntry;
import com.curefit.albus.common.PatientPreferredAgentResponse;
import com.curefit.albus.request.ConsultationBookingInfoFetchRequestParams;
import com.curefit.albus.request.ConsultationSearchRequestParamModel;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.ConsultationBookingInfo;
import com.curefit.albus.response.ConsultationProduct;
import com.curefit.albus.response.chronic.UserOnboardingActionWithContext;
import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.dto.ChronicCareTeam;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.app.action.NavigationType;
import com.curefit.cfapi.pojo.chroniccare.experiencecenter.SfWellnessTherapyProduct;
import com.curefit.cfapi.pojo.vm.items.InstructionItem;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.util.DigitalAppUtil;
import com.curefit.cfapi.view.viewmodels.chroniccare.AssignedAgentData;
import com.curefit.cfapi.view.viewmodels.chroniccare.CongratsPageView;
import com.curefit.cfapi.widgets.chroniccare.BookingSuccessWidget;
import com.curefit.cfapi.widgets.chroniccare.CongratulationsWidget;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.common.data.model.entity.Gender;
import com.curefit.ollivander.common.constant.AgentType;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.pojo.CgmOnboardingStatusResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import static com.curefit.cfapi.util.AppUtil.isSugarFitApp;
import static com.curefit.cfapi.util.ChronicCareAppUtil.*;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class CongratsPageViewBuilder {
    public static final String DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT = "You best match doctor will be assigned based on your report";
    public static final String DOCTOR_ASSIGNMENT_PLACEHOLDER_URL = "/image/chroniccare/doctor_male_v2.png";
    final UserOnboardingService userOnboardingService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final ChronicCarePatientService chronicCarePatientService;
    final ExceptionReportingService exceptionReportingService;
    final ServiceInterfaces serviceInterfaces;
    final CHSClient chsClient;
    final SegmentationCacheClient segmentationCacheClient;

    public static final Float WHATS_NEXT_V2 = 2.02f;

    public CongratsPageView buildView(UserContext userContext, String type, String consultationType, String productId, String bookingId) throws Exception {
        switch (type) {
            case "CGM_INSTALLATION_BOOKED":
            case "CGM_INSTALLATION_RESCHEDULED":
                return this.getCGMInstallationBookedPageView(userContext, type);
            case "PURCHASE_COMPLETE_ANNUAL":
                return this.purchaseCompleteView(userContext);
            case "PURCHASE_COMPLETED_TRIAL":
                return this.purchaseTrialCompleteView();
            case "PREFERENCE_FORM":
                if (AppUtil.isUltraFitApp(userContext) || getShouldSkipDoctorSelection(userContext)) {
                    return this.getFinalCongratsPage(userContext);
                }
                return this.preferenceFormView();
            case "CONSULTATION_BOOKED":
            case "CONSULTATION_RESCHEDULED":
            case "GROUP_CLASS":
                return this.consultationBookedView(consultationType, userContext, type, productId, bookingId);
            case "DIAGNOSTICS_BOOKED":
            case "DIAGNOSTICS_RESCHEDULED":
                return this.diagnosticsBookedView(userContext, type);
            case "ONBOARDING_START":
                return this.getFinalCongratsPage(userContext);
            case "PURCHASE_COMPLETED_DIGITAL":
                return this.getDigitalPackSuccessPage(type);
            default: return this.getGenericSuccessPage(type);
        }
    }

    private boolean getShouldSkipDoctorSelection(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            CompletableFuture<Optional<ActivePackResponse>> activePackResponseFuture = userOnboardingService.getSugarFitActivePackFuture(serviceInterfaces, userContext.getUserProfile().getUserId());
            ActivePackResponse activePackResponse = activePackResponseFuture.get().get();
            boolean skipAddressSelection = shouldSkipAddressSelection(activePackResponse);
            if (skipAddressSelection) {
                return true;
            } else {
                UserOnboardingActionWithContext onboardingStatus = userOnboardingService.getSugarFitUserOnboardingStatus(userId, activePackResponse);
                boolean allowDoctorSelection = onboardingStatus.getDoctorSelectionActionWithContext().getAction().isActionPermitted();
                return !allowDoctorSelection;
            }
        } catch (Exception e) {
            log.error("Exception in fetching active devices for congrats page", e);
            exceptionReportingService.reportException("Exception in fetching active devices for congrats page", e);
            return false;
        }
    }

    private boolean shouldSkipAddressSelection(ActivePackResponse activePackResponse) {
        return activePackResponse.getBundleProduct().getProductSpecs().has("skipAddressSelection")
                && activePackResponse.getBundleProduct().getProductSpecs().get("skipAddressSelection").asBoolean(false);
    }

    public CongratsPageView nonPhleboConsultationBookedView(String consultationType, UserContext userContext, String type, String productId, String bookingId) throws Exception {
        CongratsPageView page = new CongratsPageView();
        String subTitle = null;

        if (!Objects.isNull(bookingId) && !bookingId.isEmpty() && ChronicCareAppUtil.isExperienceCenterProduct(productId)) {
            ConsultationSearchRequestParamModel requestParamModel = new ConsultationSearchRequestParamModel();
            requestParamModel.setIsDoctorRequired(true);
            requestParamModel.setIsPatientRequired(true);
            requestParamModel.setIsUnreadContextRequired(true);
            requestParamModel.setIsPrescriptionRequired(true);
            requestParamModel.setIsRootBookingRequired(true);
            requestParamModel.setIsAppointmentContextRequired(true);
            requestParamModel.setIsCancelledAppointmentRequired(true);

            ConsultationBookingInfoFetchRequestParams params = new ConsultationBookingInfoFetchRequestParams();
            params.setParentBookingRequired(true);
            params.setAllRelatedBookingsRequired(false);
            params.setConsultationInfoParams(requestParamModel);
            ConsultationBookingInfo bookingInfo = serviceInterfaces.getSfAlbusClient().getBookingInfo(Long.valueOf(bookingId), params, ConsultationBookingInfo.class, userContext.getUserProfile().getUserId());
            if (!Objects.isNull(bookingInfo) && !Objects.isNull(bookingInfo.getConsultationOrderResponse()) && !Objects.isNull(bookingInfo.getConsultationOrderResponse().getStartTime())) {
                long additionalWaitingTimeInMillis = 900000L;
                Long startTime = bookingInfo.getConsultationOrderResponse().getStartTime().getTime() - additionalWaitingTimeInMillis;
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMM • h:mmaa");
                dateFormat.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
                String formattedDate = dateFormat.format(startTime).replace("am", "AM").replace("pm", "PM");
                subTitle = "Booking for " + formattedDate;
            }
        }

        BookingSuccessWidget bookingSuccessWidget = new BookingSuccessWidget();
        if (ChronicCareAppUtil.isOneOnOneTherapy(productId) || type.equals("GROUP_CLASS")) {
            SfWellnessTherapyProduct wellnessTherapyProduct = null;
            if (type.equals("GROUP_CLASS")) {
                List<GroupClassSellableProductEntry> groupClassSellableProductEntries = serviceInterfaces.getSfAlbusClient()
                        .groupClassProductSearch(null, true, userContext.getUserProfile().getUserId(), List.of(productId));
                if (CollectionUtils.isNotEmpty(groupClassSellableProductEntries)) {
                    GroupClassSellableProductEntry groupClassSellableProductEntry = groupClassSellableProductEntries.get(0);
                    if (Objects.nonNull(groupClassSellableProductEntry)) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        wellnessTherapyProduct = objectMapper.treeToValue(groupClassSellableProductEntry.getContentAssets(), SfWellnessTherapyProduct.class);
                    }
                }
            } else {
                ConsultationProduct therapyConsultation = serviceInterfaces.getSfAlbusClient().searchConsultationProductByProductCode(productId);
                if (Objects.nonNull(therapyConsultation)) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    wellnessTherapyProduct = objectMapper.treeToValue(therapyConsultation.getInfoSection(), SfWellnessTherapyProduct.class);
                }
            }
            bookingSuccessWidget = getTherapyConsultationSuccessWidget(consultationType, userContext, productId, subTitle, wellnessTherapyProduct);
        } else if (isSugarFitApp(userContext) && isSfNuxV2SupportedAppVersion(userContext)) {
            CompletableFuture<Optional<ActivePackResponse>> activePackResponseFuture = userOnboardingService.getSugarFitActivePackFuture(serviceInterfaces, userContext.getUserProfile().getUserId());
            Optional<ActivePackResponse> activePackResponseOptional = activePackResponseFuture.get();
            ActivePackResponse activePackResponse = activePackResponseOptional.orElse(null);
            boolean isDigitalAppUser = Objects.nonNull(activePackResponse) && DigitalAppUtil.isDigitalAppUser(activePackResponse.getBundleProduct());
            bookingSuccessWidget = getConsultationSuccessWidgetV2(consultationType, userContext, productId, subTitle, isDigitalAppUser);
        } else {
            bookingSuccessWidget = getConsultationSuccessWidget(consultationType, userContext);
        }
        bookingSuccessWidget.setBookingType(type);

        Action bottomAction = null;
        if (type.equals("CONSULTATION_BOOKED") && consultationType.equals("DOCTOR") && ChronicCareAppUtil.isFirstDoctorConsultationBooked(userContext, serviceInterfaces, userOnboardingService, exceptionReportingService)) {
            bottomAction = Action.builder().title("UPLOAD REPORT").actionType(ActionType.NAVIGATION).url("curefit://sfreportspage").pageFrom("orderconfirmationv1").build();
        }

        String userId = userContext.getUserProfile().getUserId();
        Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePackForHome(userId);
        CgmOnboardingStatusResponse cgmOnboardingStatusResponse = activePackResponse.isPresent()
                ? chsClient.fetchOnboardingStatus(Long.valueOf(userId), activePackResponse.get().getBookingId(), AppTenant.SUGARFIT)
                : null;
//        hasAcceptedDisclaimer used to avoid support of cgm installation on welcome call in NUX
        boolean hasAcceptedDisclaimer = cgmOnboardingStatusResponse != null && cgmOnboardingStatusResponse.getDisclaimerAccepted();
        if(hasAcceptedDisclaimer && (type.equals("CONSULTATION_BOOKED") || type.equals("CONSULTATION_RESCHEDULED")) && ChronicCareServiceHelper.isFirstCoachConsultationBooked(activePackResponse.orElseGet(null) , exceptionReportingService)) {
            CompletableFuture<UserOnboardingActionWithContext> context = getUserOnboardingActionsFuture(
                    serviceInterfaces,
                    userContext,
                    activePackResponse,
                    userOnboardingService,
                    exceptionReportingService
            );
            UserOnboardingActionWithContext onboardingActions = context.get();
            boolean isCGMInstallationAllowed = chronicCareServiceHelper.isCGMInstallationBookingAllowed(onboardingActions);
            boolean isCGMInstallationPending = chronicCareServiceHelper.isCGMInstallationBookingPending(onboardingActions);
//            if(ChronicCareAppUtil.isCGMDplusIEnabled(userContext, segmentationCacheClient) && isCGMInstallationAllowed && isCGMInstallationPending) {
//                bottomAction = Action.builder().title("Book CGM Installation").actionType(ActionType.NAVIGATION).navigationType(NavigationType.NAVIGATE_REPLACE).url("curefit://sfchooserecommendedbookingslotpage").build();
//            }

            if (Objects.nonNull(bottomAction)) {
                if (BookingSuccessWidget.isBottomActionArraySupportedVersion(userContext)) {
                    bookingSuccessWidget.setBottomActions(List.of(bottomAction));
                } else {
                    bookingSuccessWidget.setBottomAction(bottomAction);
                }
            }
        }

        page.addWidget(bookingSuccessWidget);
        return page;
    }

    public CongratsPageView phleboConsultationBookedView(String consultationType, UserContext userContext, String type, String productId, String bookingId) {
        CongratsPageView page = new CongratsPageView();
        BookingSuccessWidget bookingSuccessWidget = getPhleboConsultationSuccessWidget(consultationType, userContext);
        bookingSuccessWidget.setBookingType(type);

        Action bottomAction = Action.builder().title("DONE").actionType(ActionType.NAVIGATION).url("curefit://apptabs").pageFrom("orderconfirmationv1").build();
        bookingSuccessWidget.setBottomAction(bottomAction);
        page.addWidget(bookingSuccessWidget);
        return page;
    }

    public CongratsPageView getGenericSuccessPage(String type) {
        CongratsPageView page = new CongratsPageView();
        BookingSuccessWidget successWidget = new BookingSuccessWidget();
        successWidget.setTitle("Order Successful!");
        successWidget.setSubTitle("Thanks for the purchase");
        successWidget.setInstructionItems(new ArrayList<>());
        successWidget.setBookingType(type);

        Action bottomAction = Action.builder().title("DONE").actionType(ActionType.NAVIGATION).url("curefit://apptabs").pageFrom("orderconfirmationv1").build();
        successWidget.setBottomAction(bottomAction);
        page.addWidget(successWidget);
        return page;
    }

    public CongratsPageView getDigitalPackSuccessPage(String type) {
        CongratsPageView page = new CongratsPageView();
        BookingSuccessWidget successWidget = new BookingSuccessWidget();
        successWidget.setTitle("Hurray!");
        successWidget.setSubTitle("You’ve successfully enrolled in the Sugarfit Digital Diabetes Management Program.");

        successWidget.setInstructionTitle("What’s next?");
        InstructionItem instruction1 = new InstructionItem("/image/chroniccare/info.png", "You will have access to the Sugarfit app and its features.");
        InstructionItem instruction2 = new InstructionItem("/image/chroniccare/info.png", "Everyday one lesson will get unlocked for you to read.");
        InstructionItem instruction3 = new InstructionItem("/image/chroniccare/info.png", "Live Q&A sessions with our experts will be held every day");
        InstructionItem instruction4 = new InstructionItem("/image/chroniccare/info.png", "Keep logging your meals and activities to get the best out of the program.");
        successWidget.setInstructionItems(List.of(instruction1, instruction2, instruction3, instruction4));
        successWidget.setBookingType(type);

        Action bottomAction = Action.builder().title("DONE").actionType(ActionType.NAVIGATION).url("curefit://apptabs").pageFrom("orderconfirmationv1").build();
        successWidget.setBottomAction(bottomAction);
        page.addWidget(successWidget);
        return page;
    }

    public CongratsPageView consultationBookedView(String consultationType, UserContext userContext, String type, String productId, String bookingId) throws Exception {
        boolean isPhleboConsultationProduct = Objects.nonNull(productId) && ChronicCareAppUtil.isPhleboConsultation(productId);
        if(isPhleboConsultationProduct) {
            return phleboConsultationBookedView(consultationType, userContext, type, productId, bookingId);
        } else {
            if (ChronicCareAppUtil.isOneOnOneTherapy(productId)) {
                consultationType = "ONE_ON_ONE_THERAPIES";
            }
            return nonPhleboConsultationBookedView(consultationType, userContext, type, productId, bookingId);
        }
    }

    public CongratsPageView diagnosticsBookedView(UserContext userContext, String type) {
        CongratsPageView page = new CongratsPageView();
        BookingSuccessWidget bookingSuccessWidget = isSugarFitApp(userContext) && isSfNuxV2SupportedAppVersion(userContext)
                ? getDiagnosticSuccessWidgetV2()
                : getDiagnosticSuccessWidget();
        bookingSuccessWidget.setBookingType(type);
//        Action bottomAction = Action.builder().title("PROCEED").actionType(ActionType.NAVIGATION).url("curefit://apptabs").pageFrom("orderconfirmationv1").build();
//        if (Objects.nonNull(bottomAction)) {
//            if (BookingSuccessWidget.isBottomActionArraySupportedVersion(userContext)) {
//                bookingSuccessWidget.setBottomActions(List.of(bottomAction));
//            } else {
//                bookingSuccessWidget.setBottomAction(bottomAction);
//            }
//        }

        page.addWidget(bookingSuccessWidget);
        return page;
    }

    public CongratsPageView purchaseCompleteView(UserContext userContext) throws ResourceNotFoundException {
        CongratsPageView page = new CongratsPageView();
        CongratulationsWidget congratsWidget = getPurchaseCompleteWidget(userContext);
        page.addWidget(congratsWidget);
        return page;
    }

    public CongratulationsWidget getPurchaseCompleteWidget(UserContext userContext) throws ResourceNotFoundException {
        CongratulationsWidget congratsWidget = new CongratulationsWidget();
        Optional<ActivePackResponse> sugarFitActivePack = userOnboardingService.getSugarFitActivePack(userContext.getUserProfile().getUserId());
        Action bottomAction = null;
        if (sugarFitActivePack.isPresent()) {
            // pack has started
            if (isSugarFitApp(userContext) && isSfNuxV2SupportedAppVersion(userContext)) {
                bottomAction = Action.builder().title("START YOUR JOURNEY").actionType(ActionType.NAVIGATION)
                        .url("curefit://nuxpreferences").navigationType(NavigationType.NAVIGATE_REPLACE)
                        .pageFrom("orderconfirmationv1").build();
            } else {
                bottomAction = Action.builder().title("START YOUR JOURNEY").actionType(ActionType.NAVIGATION)
                        .url("curefit://preferenceform?pageNumber=1")
                        .pageFrom("orderconfirmationv1").build();
            }
        } else {
            Optional<ActivePackResponse> sugarFitFuturePack = userOnboardingService.getSugarFitUpcomingPack(userContext.getUserProfile().getUserId());
            if (sugarFitFuturePack.isPresent()) {
                // pack is available in future
                bottomAction = Action.builder().title("START YOUR JOURNEY").actionType(ActionType.NAVIGATION)
                        .url("curefit://apptabs")
                        .pageFrom("orderconfirmationv1").build();
                congratsWidget.setStartDateEpoch(sugarFitFuturePack.get().getStartDate());
            }
        }
        // as no pack was available so url is null
        if (bottomAction == null) {
            throw new ResourceNotFoundException(String.format("No active/Future pack for user :: %s", userContext.getUserProfile().getUserId()));
        }

        congratsWidget.setCreativeType("BLUE");
        congratsWidget.setTitle("Congratulations!");
        congratsWidget.setSubTitle("You have taken the first step in your path back from diabetes. Let us now help you manage your condition and bring out the best in you!");
        congratsWidget.setBottomAction(bottomAction);
        return congratsWidget;
    }

    ;

    public CongratsPageView purchaseTrialCompleteView() {
        CongratsPageView page = new CongratsPageView();
        CongratulationsWidget congratsWidget = new CongratulationsWidget();
        congratsWidget.setCreativeType("BLUE");
        congratsWidget.setTitle("Congratulations!");
        congratsWidget.setSubTitle("Taking the first step is the toughest. Let us now help you manage your condition and bring out the best in you!");
        congratsWidget.setBottomAction(Action.builder().title("START").actionType(ActionType.NAVIGATION).url("curefit://chroniccarehomepage").build());
        page.addWidget(congratsWidget);
        return page;
    }

    ;


    public CongratsPageView preferenceFormView() {
        CongratsPageView page = new CongratsPageView();
        CongratulationsWidget congratsWidget = getPreferenceFormCongratsWidget();
        page.addWidget(congratsWidget);
        return page;
    }

    public CongratulationsWidget getPreferenceFormCongratsWidget() {
        CongratulationsWidget congratsWidget = new CongratulationsWidget();
        congratsWidget.setCreativeType("YELLOW");
        congratsWidget.setTitle("Thank you!");
        congratsWidget.setSubTitle("You’re all set to begin your path back from diabetes! To start your journey, please select the Doctor of your choice.");
        congratsWidget.setBottomAction(Action.builder().title("SELECT YOUR DOCTOR").actionType(ActionType.NAVIGATION).url("curefit://doctorlisting?productId=SUGAR001&doctorType=GP&isDoctorSearch=true").pageFrom("orderconfirmationv1").build());
        return congratsWidget;
    }

    public CongratsPageView getFinalCongratsPage(UserContext userContext) {
        CongratsPageView page = new CongratsPageView();
        CongratulationsWidget congratsWidget = new CongratulationsWidget();
        congratsWidget.setCreativeType("YELLOW");
        congratsWidget.setTitle("Thank you!");
        congratsWidget.setSubTitle(AppUtil.isUltraFitApp((userContext)) ? "Thank you for helping us get to know you better! Now let’s help you take charge of your metabolic health and fitness!" :
                "Thank you for helping us to know better and starting this journey towards a better and a fitter you!");
        String userId = userContext.getUserProfile().getUserId();
        try {
            Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePack(userId);
            if (activePackResponse.isPresent()) {
                congratsWidget.setAutoNavigate(true);
                congratsWidget.setAutoNavigateTime(3000L);
                AssignedAgentData doctorData = null, coachData = null;
                PatientDetail patient = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
                ChronicCareTeam assignedCareTeam = getAssignedCareTeam(userContext, patient.getId(), activePackResponse.get().getBundleProduct().getTenant());
                PatientPreferredAgentResponse coach = assignedCareTeam.getCoach();
                PatientPreferredAgentResponse doctor = assignedCareTeam.getDoctor();
                String coachName = coach.getAgentResponse().getName();
                // SF-2392 : delayed doctor assignment handling
                doctorData = new AssignedAgentData();
                doctorData.setAgentType("DOCTOR");
                if (doctor == null) {
                    doctorData.setAgentAssigned(false);
                    doctorData.setSubtitle(null);
                    doctorData.setAgentName(DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT);
                    doctorData.setAgentImageUrl(DOCTOR_ASSIGNMENT_PLACEHOLDER_URL);
                } else {
                    String doctorName = getDoctorName(doctor.getAgentResponse().getName());
                    doctorData.setAgentName(doctorName);
                    doctorData.setAgentImageUrl(this.getAgentImageFromResponse(doctor));
                }
                coachData = new AssignedAgentData();
                coachData.setAgentName(coachName);
                coachData.setAgentImageUrl(getAgentImageFromResponse(coach));
                coachData.setAgentType(coach.getDoctorType().equalsIgnoreCase("LIFESTYLE_COACH") ? "COACH" : "");
                congratsWidget.setCoachData(coachData);
                congratsWidget.setDoctorData(doctorData);
                congratsWidget.setTeamTitle(" Based on your language preference, here are the experts on your team to help you unlock your metabolic health and fitness");
                log.info("congratsWidget", congratsWidget);

            }
        } catch (Exception e) {
            log.info("assignedCareTeam: NO data");
        }

        String url = "curefit://apptabs";
        page.addWidget(congratsWidget);
        page.addAction(Action.builder().title("PROCEED TO HOME").actionType(ActionType.NAVIGATION).url(url).pageFrom("congratspage").build());
        return page;
    }

    public BookingSuccessWidget getPhleboConsultationSuccessWidget(String consultationType, UserContext userContext) {
        BookingSuccessWidget consultationSuccessWidget = new BookingSuccessWidget();
        consultationSuccessWidget.setTitle("CGM Installation booked!");
        consultationSuccessWidget.setSubTitle("You can reschedule the appointment from the homepage 30 mins prior to installation ");
        consultationSuccessWidget.setInstructionTitle("INSTRUCTIONS TO BE FOLLOWED");
        List<InstructionItem> instructionItems = new ArrayList<>();
        instructionItems.add(new InstructionItem("image/chroniccare/instructions/wifi.png", "Ensure you have a steady internet during the video call."));
        instructionItems.add(new InstructionItem("image/chroniccare/instructions/drink.png", "Fasting is not needed for CGM installation."));
        consultationSuccessWidget.setInstructionItems(instructionItems);
        return consultationSuccessWidget;
    }

    public CongratsPageView getCGMInstallationBookedPageView(UserContext userContext, String type) {
        CongratsPageView page = new CongratsPageView();
        BookingSuccessWidget bookingSuccessWidget = new BookingSuccessWidget();
        switch (type) {
            case "CGM_INSTALLATION_BOOKED":
                bookingSuccessWidget.setTitle("CGM Installation booked!");
                bookingSuccessWidget.setSubTitle("You can reschedule the appointment from the homepage two hours prior to installation ");
                break;
            case "CGM_INSTALLATION_RESCHEDULED":
                bookingSuccessWidget.setTitle("CGM Installation rescheduled!");
                bookingSuccessWidget.setSubTitle("You can reschedule the appointment from the homepage two hours prior to installation ");
                break;
            default:;
        }
        bookingSuccessWidget.setInstructionTitle("INSTRUCTIONS TO BE FOLLOWED");
        List<InstructionItem> instructionItems = new ArrayList<>();
        instructionItems.add(new InstructionItem("image/chroniccare/instructions/phone.png", "CGM Installation experts's phone number will be shared with you on the day of installation."));
        instructionItems.add(new InstructionItem("image/chroniccare/instructions/drink.png", "Fasting is not needed for CGM installation."));
        bookingSuccessWidget.setInstructionItems(instructionItems);
        bookingSuccessWidget.setBookingType(type);
        page.addWidget(bookingSuccessWidget);
        return page;
    }

    public BookingSuccessWidget getConsultationSuccessWidget(String consultationType, UserContext userContext) {
        BookingSuccessWidget consultationSuccessWidget = new BookingSuccessWidget();
        String coachConsultText = AppUtil.isUltraFitApp(userContext) ? "Metabolic Expert Consultation" : "Coach Consultation";
        consultationSuccessWidget.setTitle(consultationType.equals("COACH") ? coachConsultText : "Doctor Consultation");
        consultationSuccessWidget.setInstructionTitle("About your consultation");

        List<InstructionItem> instructionItems = new ArrayList<>();
        List<InstructionItem> checkList = new ArrayList<>();
        instructionItems.add(new InstructionItem("/image/chroniccare/outgoing-call.png", "A link to join the call will be shown on the app homepage 30 mins before the call."));
        if (consultationType.equals("COACH")) {
            instructionItems.add(new InstructionItem("/image/chroniccare/time-before.png", "You can reschedule this call until 4 hours before the consultation using the more menu located on the consultation card on your homepage."));
            instructionItems.add(new InstructionItem("/image/chroniccare/heart.png", "The purpose of the call is for your coach to get to know you and your current lifestyle."));
        } else {
            checkList.add(new InstructionItem("/image/chroniccare/time-before.png", "Upload any outside diagnostic report or external prescriptions for doctor's reference."));
            checkList.add(new InstructionItem("/image/chroniccare/time-before.png", "Be ready with a brief of your medical History (Past conditions, surgeries, if any)."));
            checkList.add(new InstructionItem("/image/chroniccare/time-before.png", "Write down all the points which you want to discuss with the doctor to ensure effective use of consultation time."));
            checkList.add(new InstructionItem("/image/chroniccare/time-before.png", "Join the consultation 2 mins in advance and check your video/audio settings."));

            instructionItems.add(new InstructionItem("/image/chroniccare/time-before.png", "You can reschedule this call until 4 hours before the consultation using the more menu located on the consultation card on your homepage."));
            instructionItems.add(new InstructionItem("/image/chroniccare/heart.png", "The purpose of the call is for your doctor review your latest labs/diagnostic test results and re-assess your medication."));
        }
        consultationSuccessWidget.setChecklistItems(checkList);
        consultationSuccessWidget.setInstructionItems(instructionItems);
        return consultationSuccessWidget;
    }

    public BookingSuccessWidget getDiagnosticSuccessWidget() {
        BookingSuccessWidget diagnosticSuccessWidget = new BookingSuccessWidget();

        diagnosticSuccessWidget.setTitle("Diagnostic Tests");
        diagnosticSuccessWidget.setInstructionTitle("About your tests");

        List<InstructionItem> instructionItems = new ArrayList<>();
        instructionItems.add(new InstructionItem("/image/chroniccare/location.png", "A phlebotomist will arrive at your location in the selected time slot. It usually takes 10-15 mins to collect the samples."));
        instructionItems.add(new InstructionItem("/image/chroniccare/info.png", "The instructions will be easily accessible in the upcoming diagnostics card on your homepage."));
        instructionItems.add(new InstructionItem("/image/chroniccare/time-before.png", "You can reschedule your tests until 1 hour before the visit using the more menu located on the consultation card on your homepage."));
        instructionItems.add(new InstructionItem("/image/chroniccare/report-note.png", "It takes about 24 hours for your report to be generated."));

        diagnosticSuccessWidget.setInstructionItems(instructionItems);
        return diagnosticSuccessWidget;
    }

    public BookingSuccessWidget getConsultationSuccessWidgetV2(String consultationType, UserContext userContext, String productId, String subTitle, boolean isDigitalAppUser) {
        boolean isExperienceCenterProduct = Objects.nonNull(productId) && ChronicCareAppUtil.isExperienceCenterProduct(productId);
        BookingSuccessWidget consultationSuccessWidget = new BookingSuccessWidget();
        String coachConsultText = AppUtil.isUltraFitApp(userContext) ? "Metabolic Expert Consultation" : "Consultation booked!";
        String doctorConsultTitle = isExperienceCenterProduct ? "Booking successful!" : "Doctor consultation booked!";
        consultationSuccessWidget.setSubTitle(subTitle);

        if (AppUtil.isSugarFitApp(userContext) && consultationType.equals("COACH")) {
            if (isDigitalAppUser) {
                consultationSuccessWidget.setSubTitle(null);
            } else {
                consultationSuccessWidget.setSubTitle("In case you miss the consult, you will not be able to book any sessions for 3 days");
            }
        }
        consultationSuccessWidget.setTitle(consultationType.equals("COACH") ? coachConsultText : doctorConsultTitle);
        consultationSuccessWidget.setInstructionTitle(isExperienceCenterProduct ? "INSTRUCTIONS TO BE FOLLOWED" : "ABOUT YOUR CONSULTATION");

        List<InstructionItem> instructionItems = new ArrayList<>();
        List<InstructionItem> checkList = new ArrayList<>();
        if (consultationType.equals("COACH")) {
            instructionItems.add(new InstructionItem("/image/chroniccare/instructions/video_cam_icon.png", "Joining link will be shown on the homepage 30 mins before the consultation"));
            instructionItems.add(new InstructionItem("/image/chroniccare/instructions/reschedule_icon.png", "You can reschedule this session until 4 hours before the consultation using the more menu located on the consultation card on your homepage"));
//            instructionItems.add(new InstructionItem("/image/chroniccare/instructions/booking_disabled_icon.png", "In-case of NO-SHOW you won't be able to book any session for 3 days"));
        } else {
            if (isExperienceCenterProduct) {
                instructionItems.add(new InstructionItem("/image/chroniccare/instructions/access_time_icon.png", "Reach the venue 15 mins prior to the appointment time"));
            } else {
                consultationSuccessWidget.setChecklistTitle("CHECKLIST FOR CONSULTATION");
                checkList.add(new InstructionItem("/image/chroniccare/instructions/upload_icon.png", "Upload any outside diagnostic report or external prescriptions for doctor's reference"));
                checkList.add(new InstructionItem("/image/chroniccare/instructions/medical_services_icon.png", "Be ready with a brief of your medical History (Past conditions, surgeries, if any)"));
                checkList.add(new InstructionItem("/image/chroniccare/instructions/list_icon.png", "Write down all the points which you want to discuss with the doctor to ensure effective use of consultation time"));
                checkList.add(new InstructionItem("/image/chroniccare/instructions/access_time_icon.png", "Join the consultation 2 mins in advance and check your video/audio settings"));

                instructionItems.add(new InstructionItem("/image/chroniccare/instructions/video_cam_icon.png", "Joining link will be shown on the homepage 30 mins before the call"));
                instructionItems.add(new InstructionItem("/image/chroniccare/instructions/reschedule_icon.png", "You can reschedule this call until 4 hours before the consultation using the more menu located on the consultation card on your homepage"));
                instructionItems.add(new InstructionItem("/image/chroniccare/instructions/preview_icon.png", "Your doctor will review your latest lab/diagnostic test results and re-evaluate your medication"));
            }
        }
        consultationSuccessWidget.setChecklistItems(checkList);
        consultationSuccessWidget.setInstructionItems(instructionItems);
        return consultationSuccessWidget;
    }

    public BookingSuccessWidget getTherapyConsultationSuccessWidget(String consultationType, UserContext userContext, String productId, String subTitle, SfWellnessTherapyProduct wellnessTherapyProduct) {
        BookingSuccessWidget consultationSuccessWidget = new BookingSuccessWidget();
        consultationSuccessWidget.setSubTitle(subTitle);
        consultationSuccessWidget.setTitle("Your session has been booked!");
        consultationSuccessWidget.setInstructionTitle("INSTRUCTIONS TO BE FOLLOWED");

        List<InstructionItem> instructionItems = new ArrayList<>();
        if (wellnessTherapyProduct != null && CollectionUtils.isNotEmpty(wellnessTherapyProduct.getInstructions())) {
            wellnessTherapyProduct.getInstructions().forEach(i -> {
                instructionItems.add(new InstructionItem("/image/chroniccare/instructions/instructions_icon.png", i.getSubTitle()));
            });
        }
        consultationSuccessWidget.setInstructionItems(instructionItems);
        return consultationSuccessWidget;
    }

    public BookingSuccessWidget getDiagnosticSuccessWidgetV2() {
        BookingSuccessWidget diagnosticSuccessWidget = new BookingSuccessWidget();

        diagnosticSuccessWidget.setTitle("Diagnostics booked!");
        diagnosticSuccessWidget.setInstructionTitle("INSTRUCTIONS TO BE FOLLOWED");

        List<InstructionItem> instructionItems = new ArrayList<>();
        instructionItems.add(new InstructionItem("/image/chroniccare/instructions/access_time_icon.png", "A phlebotomist will arrive at your location in the selected time slot. It usually takes 10-15 mins to collect the samples"));
        instructionItems.add(new InstructionItem("/image/chroniccare/instructions/instructions_icon.png", "The instructions will be easily accessible in the upcoming diagnostics card on your homepage"));
        instructionItems.add(new InstructionItem("/image/chroniccare/instructions/reschedule_icon.png", "You can reschedule your tests until 1 hour before the visit using the more menu located on the consultation card on your homepage"));
        instructionItems.add(new InstructionItem("/image/chroniccare/instructions/upload_icon.png", "It takes about 24-48 hours for your report to be generated. You will receive the reports on your registered email Id"));

        diagnosticSuccessWidget.setInstructionItems(instructionItems);
        return diagnosticSuccessWidget;
    }

    private ChronicCareTeam getAssignedCareTeam(UserContext userContext, Long patientId, String tenant) {
        ChronicCareTeam.ChronicCareTeamBuilder builder = ChronicCareTeam.builder();
        String userId = userContext.getUserProfile().getUserId();
        // TODO : change this with ultrafit and sugarfit based on userContext.
        List<PatientPreferredAgentResponse> patientPreferredAgentResponses = this.serviceInterfaces.getSfAlbusClient()
                .getPreferredAgents(userId, Optional.ofNullable(patientId), tenant);
        if (!CollectionUtils.isEmpty(patientPreferredAgentResponses)) {
            for (PatientPreferredAgentResponse patientPreferredAgentResponse : patientPreferredAgentResponses) {
                if (patientPreferredAgentResponse.getAgentResponse().getType().equals(AgentType.DOCTOR)) {
                    builder.doctor(patientPreferredAgentResponse);
                } else if (patientPreferredAgentResponse.getAgentResponse().getType().equals(AgentType.LIFESTYLE_COACH)) {
                    builder.coach(patientPreferredAgentResponse);
                }
            }
        }
        return builder.build();
    }

    private String getAgentImageFromResponse(PatientPreferredAgentResponse agentResponse) {
        String imageUrl = agentResponse.getAgentResponse().getDisplayImage();
        if (imageUrl != null && !imageUrl.equals("")) {
            return imageUrl;
        } else {
            if (agentResponse.getAgentResponse().getGender().equals(Gender.Female)) {
                if (agentResponse.getAgentResponse().getType().equals(AgentType.LIFESTYLE_COACH))
                    return "/image/chroniccare/coach_female_v2.png";
                return "/image/chroniccare/doctor_female_v2.png";
            } else if (agentResponse.getAgentResponse().getGender().equals(Gender.Male)) {
                if (agentResponse.getAgentResponse().getType().equals(AgentType.LIFESTYLE_COACH))
                    return "/image/chroniccare/coach_male_v2.png";
                return "/image/chroniccare/doctor_male_v2.png";
            } else {
                return "/image/chroniccare/coach_male_v2.png";
            }
        }
    }

}
