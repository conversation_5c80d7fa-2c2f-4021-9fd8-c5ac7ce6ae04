package com.curefit.cfapi.view.viewbuilders.fitness;

import com.curefit.center.dtos.CenterEntry;
import com.curefit.center.dtos.Media;
import com.curefit.center.enums.CenterMediaTag;
import com.curefit.center.enums.MediaType;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.atom.Alignment;
import com.curefit.cfapi.pojo.vm.atom.CFContainerData;
import com.curefit.cfapi.pojo.vm.atom.CFImageData;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.Debugger;
import com.curefit.cfapi.util.ErrorUtil;
import com.curefit.cfapi.util.FitnessCenterUtil;
import com.curefit.cfapi.util.MembershipUtil;
import com.curefit.cfapi.util.OrderUtil;
import com.curefit.cfapi.util.StringUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.view.viewbuilders.fitso.ProductListWidget;
import com.curefit.cfapi.view.viewmodels.common.ProductListWidgetItem;
import com.curefit.cfapi.view.viewmodels.fitness.DetailBreakdownData;
import com.curefit.cfapi.view.viewmodels.fitness.PackTransitionPageView;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.ActionListWidget;
import com.curefit.cfapi.widgets.common.DetailPointCardWidget;
import com.curefit.cfapi.widgets.common.DoubleSkuImageWidget;
import com.curefit.cfapi.widgets.common.PurchaseOptionSelectionCTA;
import com.curefit.cfapi.widgets.common.RichTextItem;
import com.curefit.cfapi.widgets.common.RichTextListWidget;
import com.curefit.cfapi.widgets.common.TaxAndFeeDetailsWidget;
import com.curefit.cfapi.widgets.fitness.StringListWidget;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.location.models.City;
import com.curefit.membership.pojo.entry.Benefit;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.types.BenefitType;
import com.curefit.oms.models.order.OrderCreate;
import com.curefit.oms.models.order.OrderProduct;
import com.curefit.oms.models.order.OrderProductOption;
import com.curefit.oms.models.order.ReviewOrderResponse;
import com.curefit.pms.enums.PackTransitionType;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.pms.pojo.Restriction;
import com.curefit.pms.pojo.customPacks.augments.AugmentedPackEntry;
import com.curefit.pms.requests.PackTransitionPricingRequest;
import com.curefit.pms.responses.PackTransitionPricingResponse;
import com.curefit.product.enums.ProductType;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.curefit.cfapi.pojo.app.action.ActionType.NAVIGATION;
import static com.curefit.cfapi.view.viewbuilders.memberships.MembershipDetailPageViewBuilder.BASIC_SUPPORT_PAGE;
import static com.curefit.membership.types.Status.PAUSED;
import static com.curefit.membership.types.Status.PURCHASED;

@Slf4j
public class PackTransitionPageViewBuilder {

    Membership membership;
    PackTransitionPricingResponse packTransitionResponse;
    FitnessCenterUtil.UserMembershipType userMembershipType;
    FitnessCenterUtil.UserMembershipType transitionUserMembershipType = FitnessCenterUtil.UserMembershipType.UNKNOWN;
    City cityFrom;
    City cityTo;
    Boolean isCityTransfer = false;
    Boolean isCenterTransfer = false;
    CenterEntry centerFrom;
    CenterEntry centerTo;
    String selectedCenterServiceId;
    PackTransitionPricingRequest transitionRequest;
    Boolean isFree = false;
    PackTransitionType transitionType;
    ProductSubType sourceProductSubType;
    ProductSubType targetProductSubType;
    ProductType targetProductType;
    Debugger debugger;

    private static final String DEFAULT_GYM_IMAGE = "/image/icons/cult/gyms_default.jpg";
    private static final List<String> goldenCityImages = List.of("Ahmedabad", "Amritsar", "Bangalore", "Chandigarh", "Chennai", "Gurgaon",
            "Hyderabad", "Indore", "Jaipur", "Jammu", "Kochi", "Kolkata", "Kota", "Ludhiana", "Mumbai", "Mysore", "Pune", "Visakhapatnam");

    public PackTransitionPageView packTransitionPageView(Map<String, String> queryParams, ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        debugger = Debugger.getDebuggerFromUserContext(userContext);

        fillUp(queryParams, userContext, interfaces);
        populateTransitionRequest(userContext);
        getTransitionTo();
        fetch(interfaces, userContext);

        List<BaseWidget> baseWidgets = new ArrayList<>();

        PackTransitionPageView pageView = new PackTransitionPageView();
        baseWidgets.add(getDoubleSkuComparisonWidget());
        if (transitionType.equals(PackTransitionType.TRANSFER)) {
            if (userMembershipType.equals(FitnessCenterUtil.UserMembershipType.ELITE_PLUS) &&
                    transitionUserMembershipType.equals(FitnessCenterUtil.UserMembershipType.ELITE)) {
                baseWidgets.add(getElitePlusToEliteTransferAlertWidget());
            }
        }
        baseWidgets.add(getPaymentSummaryWidget());
        baseWidgets.add(getSummaryAfterTransferWidget(userContext));
        baseWidgets.add(getBenefitsOfNewMembership());
        baseWidgets.add(getHowItWorksWidget());
        baseWidgets.add(getTermsAndConditionsWidget());

        baseWidgets.removeIf(Objects::isNull);
        pageView.setWidgets(baseWidgets);
        pageView.setFooterWidget(getTransferFooterWidgets());
        pageView.setHeaderAction(Action.builder().title("HELP").actionType(NAVIGATION).url(BASIC_SUPPORT_PAGE).build());

        switch (transitionType) {
            case TRANSFER -> {
                pageView.setPageTitle("Transfer Membership");
                pageView.setTitle("Transfer Membership");
            }
            case UPGRADE -> {
                pageView.setPageTitle("Upgrade Membership");
                pageView.setTitle("Upgrade Membership");
            }
        }

        return pageView;
    }

    private void fillUp(Map<String, String> queryParams, UserContext userContext, ServiceInterfaces interfaces) throws Exception {

        String membershipServiceId = queryParams.getOrDefault("membershipServiceId", null);
        String cityIdTo = queryParams.getOrDefault("cityIdTo", null);
        String centerServiceIdTo = queryParams.getOrDefault("centerServiceIdTo", null);
        String transitionTypeString = queryParams.getOrDefault("transitionType", null);
        String sourceProductSubTypeString = queryParams.getOrDefault("sourceProductSubType", null);
        String targetProductSubTypeString = queryParams.getOrDefault("targetProductSubType", null);
        String targetProductTypeString = queryParams.getOrDefault("targetProductType", null);
        transitionType = PackTransitionType.valueOf(queryParams.getOrDefault("transitionType", null));
        sourceProductSubType = ProductSubType.valueOf(queryParams.getOrDefault("sourceProductSubType", null));
        targetProductSubType = ProductSubType.valueOf(queryParams.getOrDefault("targetProductSubType", null));
        targetProductType = ProductType.valueOf(queryParams.getOrDefault("targetProductType", null));
        selectedCenterServiceId = centerServiceIdTo;

        if (Objects.isNull(membershipServiceId) || Objects.isNull(transitionTypeString) || Objects.isNull(sourceProductSubTypeString) || Objects.isNull(targetProductSubTypeString) || Objects.isNull(targetProductTypeString)) {
            String message = "Missing Parameters";
            debugger.err(message);
            throw new BaseException(message, LogType.INFO, ErrorUtil.CFAPI_NAMESPACE, AppStatus.PRECONDITION_FAILED, ErrorUtil.ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR.toString(), message);
        }

        membership = interfaces.membershipService.getMembershipById(Long.parseLong(membershipServiceId), userContext.getUserProfile().getUserId()).get();
        userMembershipType = FitnessCenterUtil.getUserMembershipType(membership, true, userContext, true);
        debugger.msg(membership);
        debugger.msg(userMembershipType);

        centerFrom = MembershipUtil.getSelectCenterFromMembership(membership, interfaces.centerService);
        if (Objects.nonNull(centerServiceIdTo)) {
            centerTo = interfaces.centerService.getCenterDetails(Long.valueOf(centerServiceIdTo), true, null, null).get();
        }

        cityFrom = MembershipUtil.getCityIdFromMembership(membership, userContext, interfaces.cityCache, interfaces.catalogueServicePMS);
        if (Objects.nonNull(cityIdTo)) {
            cityTo = interfaces.cityCache.getCityById(cityIdTo);
        }

        debugger.msg("cityFrom", cityFrom);
        debugger.msg("cityTo", cityTo);
        debugger.msg("centerTo", centerTo);
        debugger.msg("centerFrom", centerFrom);
        debugger.msg("transitionType", transitionType);
        debugger.msg("sourceProductSubType", sourceProductSubType);
        debugger.msg("targetProductSubType", targetProductSubType);
        debugger.msg("targetProductType", targetProductType);
        debugger.msg("selectedCenterServiceId", selectedCenterServiceId);

        if (transitionType.equals(PackTransitionType.TRANSFER)) {
            isCityTransfer = !Objects.equals(cityFrom.getCityId(), cityTo.getCityId());
            isCenterTransfer = !isCityTransfer && Objects.equals(centerTo.getCity(), cityFrom.getCityId());
            debugger.msg("isCityTransfer", isCityTransfer);
            debugger.msg("isCenterTransfer", isCenterTransfer);
            if (!isCityTransfer && !isCenterTransfer) {
                throw new Exception("Can not find the transition type");
            }
        }

        boolean isActiveState = membership.getStatus().equals(PURCHASED) || membership.getStatus().equals(PAUSED);
        debugger.msg("isActiveState", isActiveState);
        if (!isActiveState) {
            String message = "InActive Membership Can't be transferred";
            debugger.err(message);
            throw new BaseException(message, LogType.INFO, ErrorUtil.CFAPI_NAMESPACE, AppStatus.PRECONDITION_FAILED, "ERR_INACTIVE_MEMBERSHIP_TRANSFER_UPGRADE", message);
        }

    }

    private void populateTransitionRequest(UserContext userContext) {

        PackTransitionPricingRequest pricingRequest = new PackTransitionPricingRequest();
        Restriction restriction = null;

        switch (transitionType) {
            case TRANSFER -> {
                switch (userMembershipType) {
                    case ELITE_SELECT, PRO_SELECT -> restriction = Restriction.builder().centers(List.of(Math.toIntExact(centerTo.getId()))).build();
                    default -> restriction = Restriction.builder().cities(List.of(cityTo.getCityId())).build();
                }
            }
            case UPGRADE -> {
                restriction = Restriction.builder().cities(List.of(cityFrom.getCityId())).build();
            }
        }

        pricingRequest.setUserId(userContext.getUserProfile().getUserId());
        pricingRequest.setMembershipServiceId(membership.getId());
        pricingRequest.setSourceProductSubType(sourceProductSubType);
        pricingRequest.setTargetProductType(targetProductType);
        pricingRequest.setTargetProductSubType(targetProductSubType);
        pricingRequest.setPackTransitionType(transitionType);
        pricingRequest.setTargetRestrictions(restriction);
        transitionRequest = pricingRequest;
        debugger.msg(transitionRequest);

    }

    private void fetch(ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        AugmentedPackEntry transitionPack = interfaces.pricingService.getTransitionPack(transitionRequest);
        debugger.msg(transitionPack);

        String centerId = null;
        String centerServiceId = null;
        if (Objects.nonNull(centerTo)) {
            centerServiceId = String.valueOf(centerTo.getId());
            centerId = FitnessCenterUtil.getCenterId(centerTo);
        }
        debugger.msg("centerId", centerId);
        debugger.msg("centerServiceId", centerServiceId);

        OrderCreate orderCreate = OrderUtil.getPackTransitionOrder(transitionRequest, cityFrom.getCityId(), userContext, transitionPack, centerId, centerServiceId);
        debugger.msg(orderCreate);
        ReviewOrderResponse orderResponse = interfaces.omsOrderService.reviewOrder(orderCreate);
        debugger.msg(orderResponse);
        packTransitionResponse = orderResponse.getOrder().getProductSnapshots().getFirst().getOption().getTransitionResponse();
        debugger.msg(packTransitionResponse);
        isFree = packTransitionResponse.getPriceDistribution().getTotalAmount() == 0;
        debugger.msg("isFree", isFree);

    }

    private void getTransitionTo() {
        boolean flag = isCityTransfer || transitionType.equals(PackTransitionType.UPGRADE);
        switch (transitionRequest.getTargetProductType()) {
            case FITNESS -> {
                switch (transitionRequest.getTargetProductSubType()) {
                    case PLUS -> {
                        if (flag) {
                            transitionUserMembershipType = FitnessCenterUtil.UserMembershipType.ELITE_PLUS;
                        }
                    }
                    case GENERAL -> {
                        if (flag) {
                            transitionUserMembershipType = FitnessCenterUtil.UserMembershipType.ELITE;
                        }
                        else {
                            transitionUserMembershipType = FitnessCenterUtil.UserMembershipType.ELITE_SELECT;
                        }
                    }
                }
            }
            case GYMFIT_FITNESS_PRODUCT -> {
                switch (transitionRequest.getTargetProductSubType()) {
                    case PLUS -> {
                        if (flag) {
                            transitionUserMembershipType = FitnessCenterUtil.UserMembershipType.PRO_PLUS;
                        }
                    }
                    case GENERAL -> {
                        if (flag) {
                            transitionUserMembershipType = FitnessCenterUtil.UserMembershipType.PRO;
                        }
                        else {
                            transitionUserMembershipType = FitnessCenterUtil.UserMembershipType.PRO_SELECT;
                        }
                    }
                }
            }
        }
    }

    private BaseWidget getDoubleSkuComparisonWidget() {
        return switch (transitionType) {
            case TRANSFER -> getTransferDoubleSkuComparisonWidget();
            case UPGRADE -> getUpgradeDoubleSkuComparisonWidget();
        };
    }

    private BaseWidget getTransferDoubleSkuComparisonWidget() {
        DoubleSkuImageWidget doubleSkuImageWidget = new DoubleSkuImageWidget();
        if (isCityTransfer) {
            doubleSkuImageWidget.setPackOneTitle(cityFrom.getName());
            doubleSkuImageWidget.setPackOneImage(getCityDisplayImage(cityFrom.getCityId()));
            doubleSkuImageWidget.setPackTwoTitle(cityTo.getName());
            doubleSkuImageWidget.setPackTwoImage(getCityDisplayImage(cityTo.getCityId()));
            doubleSkuImageWidget.setWithBGDecoration(false);
        } else {
            doubleSkuImageWidget.setPackOneTitle(centerFrom.getName());
            doubleSkuImageWidget.setPackOneImage(getCenterDisplayImage(centerFrom));
            doubleSkuImageWidget.setPackTwoTitle(centerTo.getName());
            doubleSkuImageWidget.setPackTwoImage(getCenterDisplayImage(centerTo));
        }
        return doubleSkuImageWidget;
    }

    private BaseWidget getUpgradeDoubleSkuComparisonWidget() {

        DoubleSkuImageWidget doubleSkuImageWidget = new DoubleSkuImageWidget();
        doubleSkuImageWidget.setHasDivideBelow(true);

        switch (userMembershipType) {
            case ELITE -> {
                doubleSkuImageWidget.setPackOneImage(Constants.ELITE_PACK_BANNER);
                doubleSkuImageWidget.setPackOneTitle("ELITE");
            }
            case ELITE_SELECT -> {
                doubleSkuImageWidget.setPackOneImage(Constants.ELITE_SELECT_BANNER);
                doubleSkuImageWidget.setPackOneTitle("ELITE SELECT");
            }
            case PRO -> {
                doubleSkuImageWidget.setPackOneImage(Constants.PRO_PACK_BANNER);
                doubleSkuImageWidget.setPackOneTitle("PRO");
            }
            case PRO_SELECT -> {
                doubleSkuImageWidget.setPackOneImage(Constants.PRO_SELECT_BANNER);
                doubleSkuImageWidget.setPackOneTitle("PRO SELECT");
            }
            case PRO_PLUS -> {
                doubleSkuImageWidget.setPackOneImage(Constants.PRO_PLUS_PACK_BANNER);
                doubleSkuImageWidget.setPackOneTitle("PRO PLUS");
            }
        }

        switch (transitionUserMembershipType) {
            case ELITE -> {
                doubleSkuImageWidget.setPackTwoImage(Constants.ELITE_PACK_BANNER);
                doubleSkuImageWidget.setPackTwoTitle("ELITE");
            }
            case ELITE_PLUS -> {
                doubleSkuImageWidget.setPackTwoImage(Constants.ELITE_PLUS_PACK_BANNER);
                doubleSkuImageWidget.setPackTwoTitle("ELITE PLUS");
            }
            case PRO -> {
                doubleSkuImageWidget.setPackTwoImage(Constants.PRO_PACK_BANNER);
                doubleSkuImageWidget.setPackTwoTitle("PRO");
            }
            case PRO_PLUS -> {
                doubleSkuImageWidget.setPackTwoImage(Constants.PRO_PLUS_PACK_BANNER);
                doubleSkuImageWidget.setPackTwoTitle("PRO PLUS");
            }
        }

        return doubleSkuImageWidget;

    }

    private String getCityDisplayImage(String cityId) {
        if (goldenCityImages.contains(cityId)) {
            return "/image/cities/golden/" + cityId + ".png";
        } else {
            return "/image/cities/golden/Other.png";
        }
    }

    private String getCenterDisplayImage(CenterEntry center) {
        if (Objects.isNull(center.getCenterMedias())) {
            return DEFAULT_GYM_IMAGE;
        }
        return center.getCenterMedias().stream()
                .filter(item -> item.getTag() == CenterMediaTag.PRODUCT_BANNER || item.getTag() == CenterMediaTag.FEATURED || item.getTag() == CenterMediaTag.HERO)
                .flatMap(item -> item.getMediaDetails().stream())
                .filter(media -> media.getType() == MediaType.IMAGE && Objects.nonNull(media.getCdnUrl()))
                .map(Media::getCdnUrl)
                .findFirst()
                .orElse(DEFAULT_GYM_IMAGE);
    }

    private BaseWidget getPaymentSummaryWidget() {
        DetailPointCardWidget detailPointCardWidget = new DetailPointCardWidget();
        if (transitionType.equals(PackTransitionType.TRANSFER)) {
            detailPointCardWidget.setTitle("Transfer Payment");
        } else {
            detailPointCardWidget.setTitle("Upgrade Payment");
        }
        detailPointCardWidget.setPointCardList(Collections.singletonList(
            DetailPointCardWidget.PointCard.builder()
                .title("Total Payable").rightText("₹" + packTransitionResponse.getPriceDistribution().getTotalAmount())
                .infoButton(true).cardAction(getPriceBreakdown())
                .build()
        ));
        detailPointCardWidget.setLayoutProps(getVerticalPaddingLayout("40"));
        return detailPointCardWidget;
    }

    private Action getPriceBreakdown(){

        Integer remainingDays = packTransitionResponse.getDaysDistribution().getDaysLeft();
        Integer remainingPackPrice = packTransitionResponse.getPriceDistribution().getRemainingDaysAmount();
        Integer carriedOverAmount = packTransitionResponse.getPriceDistribution().getCurrentAmountPaid();
        Integer fee = packTransitionResponse.getPriceDistribution().getFees();
        Integer totalPayable = packTransitionResponse.getPriceDistribution().getTotalAmount();

        Action breakdownAction = new Action();
        breakdownAction.setActionType(ActionType.DETAILS_BREAKDOWN_INFO_MODAL);
        DetailBreakdownData data = new DetailBreakdownData();
        data.setTitle("Payment Breakdown");
        switch (transitionType) {
            case UPGRADE -> data.setDescription(transitionTo() + " may cost more than " + transitionFrom() + ", but you pay proportionally, ensuring you only pay for what you use.");
            case TRANSFER -> data.setDescription("The " + ((isCenterTransfer) ? "center" : "city") + " you are transferring to may have higher pack Price, but you'll pay accordingly");
        }

        List<DetailBreakdownData.BreakdownPoint> points = new ArrayList<>();
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(true).build());
        if (membership.getMetadata() == null || !membership.getMetadata().containsKey("parentMembershipId")) {
            points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(false).title("Total Amount Paid").value("₹" + StringUtil.formatPrice(String.valueOf(membership.getPrice().intValue()))).build());
        }
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(true).title("Remaining " + ((remainingDays == 1) ? " day" : " days")).value(remainingDays + ((remainingDays == 1) ? " day" : " days")).build());
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(false).title(transitionTo() + " price for " + remainingDays + ((remainingDays == 1) ? " day" : " days")).value("+₹" + StringUtil.formatPrice(remainingPackPrice.toString())).build());
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(false).title(transitionFrom()  + " price for " + remainingDays + ((remainingDays == 1) ? " day" : " days")).value("-₹" + StringUtil.formatPrice(carriedOverAmount.toString())).build());
        if (Objects.nonNull(fee)) {
            String feeValue = fee >= 0 ? ("+₹" + StringUtil.formatPrice(fee.toString())) : ("-₹" + StringUtil.formatPrice(String.valueOf(Math.abs(fee))));
            points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(true).title(transitionType.equals(PackTransitionType.TRANSFER) ? "Transfer fee" : "Upgrade fee").value(feeValue).build());
        }
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(false).title("Total Payable").value("₹" + StringUtil.formatPrice(totalPayable.toString())).build());
        data.setPoints(points);
        breakdownAction.setMeta(new HashMap<>() {{
            put("detailBreakdownData", data);
        }});

        return breakdownAction;

    }

    private BaseWidget getSummaryAfterTransferWidget(UserContext userContext) {

        String tz = userContext.getUserProfile().getTimezone();
        Instant instant = TimeUtil.getDateFromTime(membership.getEnd(), tz).atZone(ZoneId.of(tz)).toInstant();
        Integer daysToIncrease = packTransitionResponse.getDaysDistribution().getDaysToIncrease();
        Date finalDate = TimeUtil.addDays(Date.from(instant), tz, daysToIncrease);
        String transferMemEndDate = TimeUtil.getNewAppDateFormat(finalDate.getTime(), userContext);

        Optional<Benefit> creditOldBenefit = membership.getBenefits().stream().filter(item -> item.getName().equalsIgnoreCase("ACCESS_CREDITS")).findFirst();
        Optional<Benefit> creditNewBenefit = packTransitionResponse.getNewBenefitList().stream().filter(item -> item.getName().equalsIgnoreCase("ACCESS_CREDITS")).findFirst();
        Optional<Benefit> cultAwayNewBenefit = packTransitionResponse.getNewBenefitList().stream().filter(item -> item.getName().equalsIgnoreCase("CULT_AWAY")).findFirst();
        Optional<Benefit> playNewBenefit = packTransitionResponse.getNewBenefitList().stream().filter(item -> item.getName().equalsIgnoreCase("PLAY")).findFirst();
        Optional<Benefit> centerAwayBenefit = packTransitionResponse.getNewBenefitList().stream().filter(item -> item.getName().equalsIgnoreCase("CENTER_AWAY")).findFirst();

        DetailPointCardWidget detailPointCardWidget = new DetailPointCardWidget();
        detailPointCardWidget.setTitle("Summary after " + StringUtil.capitalizeFirstLetter(transitionType.getValue(), false));

        List<DetailPointCardWidget.PointCard> pointCardList = new ArrayList<>();
        Action remainingDaysDetailBreakdownModalAction = getRemainingDaysDetailBreakdownModal();
        pointCardList.add(DetailPointCardWidget.PointCard.builder().title("Ends on").rightText(transferMemEndDate)
                .cardAction(remainingDaysDetailBreakdownModalAction).infoButton(Objects.nonNull(remainingDaysDetailBreakdownModalAction)).build());
        pointCardList.add(DetailPointCardWidget.PointCard.builder().title("Pause days left").rightText(String.valueOf(packTransitionResponse.getDaysDistribution().getPauseDays())).build());

        if (creditNewBenefit.isPresent()) {
            Benefit newCredit = creditNewBenefit.get();
            int totalCredits = newCredit.getMaxTickets();
            pointCardList.add(DetailPointCardWidget.PointCard.builder().title("Total Credits").rightText(Integer.toString(totalCredits)).rightTextPrefixImage("/image/icons/credit_pill2.svg")
                    .infoButton(true).cardAction(getCreditDetailBreakdownModal(newCredit, creditOldBenefit.orElse(null))).build());
        }

        cultAwayNewBenefit.ifPresent(benefit -> pointCardList.add(getDetailPointCardWidget(benefit, "Other city sessions")));
        centerAwayBenefit.ifPresent(benefit -> pointCardList.add(getDetailPointCardWidget(benefit, "Other center sessions")));
        switch (transitionUserMembershipType) {
            case ELITE_PLUS, PRO_PLUS -> playNewBenefit.ifPresent(benefit -> pointCardList.add(getDetailPointCardWidget(benefit, "Swim sessions")));
            default -> playNewBenefit.ifPresent(benefit -> pointCardList.add(getDetailPointCardWidget(benefit, "Play sessions")));
        }

        detailPointCardWidget.setPointCardList(pointCardList);
        if (isCityTransfer && creditNewBenefit.isEmpty() && creditOldBenefit.isPresent()) {
            detailPointCardWidget.setDescription("You have fixed limited sessions per month at other centers in " + cityTo.getName() + ", in contrast to credit-based access in " + cityFrom.getName() + ".");
        }
        detailPointCardWidget.setHasDivideBelow(true);
        detailPointCardWidget.setLayoutProps(getVerticalPaddingLayout("40"));
        return detailPointCardWidget;
    }

    private DetailPointCardWidget.PointCard getDetailPointCardWidget(Benefit benefit, String title) {
        String awaySessionText = BenefitType.MONTHLY.equals(benefit.getType()) ? benefit.getMaxTickets() + " sessions/mo" : benefit.getMaxTickets().toString();
        return DetailPointCardWidget.PointCard.builder().title(title).rightText(awaySessionText).build();
    }

    private Action getCreditDetailBreakdownModal(Benefit newCredit, Benefit oldBenefit) {

        Integer remainingCredits = Objects.isNull(oldBenefit) ? 0 : oldBenefit.getMaxTickets() - oldBenefit.getTicketsUsed();
        int extraCredits = newCredit.getMaxTickets() - remainingCredits;
        Integer totalCredits = newCredit.getMaxTickets();

        Action breakdownAction = new Action();
        breakdownAction.setActionType(ActionType.DETAILS_BREAKDOWN_INFO_MODAL);
        DetailBreakdownData data = new DetailBreakdownData();
        data.setTitle("Transferable credits breakdown");
        if (transitionRequest.getPackTransitionType().equals(PackTransitionType.TRANSFER)) {
            data.setDescription("Credits are transferred proportionally, reflecting your current center membership type, to the transferred membership");
        } else {
            data.setDescription("Credits are transferred proportionally, reflecting your current center membership type, to the upgraded membership");
        }
        List<DetailBreakdownData.BreakdownPoint> points = new ArrayList<>();
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(true).build());
        if (Objects.nonNull(oldBenefit)) points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(false).title("Credits remaining").value(remainingCredits + " Credits").valuePrefixImage("/image/icons/credit_pill2.svg").build());
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(true).title("Extra Credits adjusted").value("+" + extraCredits + " Credits").valuePrefixImage("/image/icons/credit_pill2.svg").build());
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(false).title("Total Transferable Credits").value(totalCredits + " Credits").valuePrefixImage("/image/icons/credit_pill2.svg").build());
        data.setPoints(points);
        data.setTopWidgets(Collections.singletonList(getCreditInfoWidget()));
        breakdownAction.setMeta(new HashMap<>() {{
            put("detailBreakdownData", data);
        }});
        return breakdownAction;
    }

    private static BaseWidget getCreditInfoWidget() {
        ProductListWidget howItWorksWidget = new ProductListWidget();
        howItWorksWidget.setShowSquareIcons(true);
        howItWorksWidget.setCollapsable(false);
        howItWorksWidget.setHideSeparatorLines(true);
        howItWorksWidget.setHeader(Header.builder().title("How credits Works?").build());
        List<ProductListWidgetItem> list = new ArrayList<>();

        list.add(new ProductListWidgetItem("Credits are required to access all other centers, apart from your home center.", "/image/icons/GitPullRequest.svg", 5.0));
        list.add(new ProductListWidgetItem("Credit amount differs at each center based on the center demand.", "/image/icons/Wallet.svg", 5.0));
        list.add(new ProductListWidgetItem("You get credit-based access at other centers, in contrast to limited sessions per month access in your current city.", "/image/icons/ChartBarHorizontal.svg", 5.0));

        howItWorksWidget.setItems(list);
        howItWorksWidget.setHasDivideBelow(false);

        return howItWorksWidget;
    }

    private Action getRemainingDaysDetailBreakdownModal() {

        Integer diffInDays = packTransitionResponse.getDaysDistribution().getDaysToIncrease();
        if (Objects.isNull(diffInDays) || diffInDays == 0) return null;

        Integer remainingDays = packTransitionResponse.getDaysDistribution().getDaysLeft();
        Integer totalDays = remainingDays + diffInDays;

        Action breakdownAction = new Action();
        breakdownAction.setActionType(ActionType.DETAILS_BREAKDOWN_INFO_MODAL);
        DetailBreakdownData data = new DetailBreakdownData();
        data.setTitle("Transferable Membership days breakdown");
        data.setDescription("The city/center you are transferring to has higher membership price. So, we have adjusted your membership days accordingly.");
        List<DetailBreakdownData.BreakdownPoint> points = new ArrayList<>();
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(true).build());
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(false).title("Current membership remaining Days").value("+" + remainingDays).build());
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(true).title("Days adjusted for transfer").value(((diffInDays > 0) ? "+" : "") + diffInDays).build());
        points.add(DetailBreakdownData.BreakdownPoint.builder().isDividerBelow(false).title("Remaining days after transfer").value(String.valueOf(totalDays)).build());
        data.setPoints(points);
        breakdownAction.setMeta(new HashMap<>() {{
            put("detailBreakdownData", data);
        }});
        return breakdownAction;
    }

    private BaseWidget getBenefitsOfNewMembership() {

        ProductListWidget benefitListItemWidget = new ProductListWidget();
        benefitListItemWidget.setShowSquareIcons(true);
        benefitListItemWidget.setCollapsable(false);
        List<ProductListWidgetItem> gridItems = new ArrayList<>();
        benefitListItemWidget.setHeader(Header.builder().title("Benefits of " + transitionTo()).build());

        String preferredCenterName = Objects.nonNull(centerTo) ? centerTo.getName() : "your preferred center";
        Optional<Benefit> creditNewBenefit = packTransitionResponse.getNewBenefitList().stream().filter(item -> item.getName().equalsIgnoreCase("ACCESS_CREDITS")).findFirst();
        Optional<Benefit> cultAwayNewBenefit = packTransitionResponse.getNewBenefitList().stream().filter(item -> item.getName().equalsIgnoreCase("CULT_AWAY")).findFirst();

        ProductListWidgetItem unlimitedProGym = ProductListWidgetItem.builder().title("Unlimited access").subTitle("To all PRO gyms").icon("/image/icons/cult/workout_benefit.png").build();
        ProductListWidgetItem unlimitedCenter = ProductListWidgetItem.builder().title("Unlimited access").subTitle("To " + preferredCenterName).icon("/image/icons/cult/workout_benefit.png").build();
        ProductListWidgetItem unlimitedAllGym = ProductListWidgetItem.builder().title("Unlimited access").subTitle("All PRO & ELITE gyms").icon("/image/icons/cult/workout_benefit.png").build();
        ProductListWidgetItem smartWorkoutPlan = ProductListWidgetItem.builder().title("Smart workout plan").subTitle("Customised for your fitness goals").icon("/image/icons/cult/smartWorkoutPlan.png").build();
        ProductListWidgetItem atCenterGroupClasses = ProductListWidgetItem.builder().title("At-center group classes").subTitle("Yoga, Dance fitness, Strength & more").icon("/image/icons/cult/group_benefit.png").build();

        switch (transitionUserMembershipType) {
            case ELITE, ELITE_PLUS -> {
                gridItems.add(unlimitedAllGym);
                gridItems.add(atCenterGroupClasses);
            }
            case ELITE_SELECT -> {
                gridItems.add(unlimitedCenter);
                gridItems.add(atCenterGroupClasses);
            }
            case PRO, PRO_PLUS -> {
                gridItems.add(unlimitedProGym);
            }
            case PRO_SELECT -> {
                gridItems.add(unlimitedCenter);
            }
        }

        gridItems.add(smartWorkoutPlan);

        String creditsDescription;
        if (transitionUserMembershipType.toString().contains("SELECT")) creditsDescription = "to access other cult centers in your city";
        else creditsDescription = "to access ELITE cult centers in your city";
        creditNewBenefit.ifPresent(benefit -> gridItems.add(ProductListWidgetItem.builder().title(benefit.getMaxTickets() + " Credits").subTitle(creditsDescription).icon("/image/icons/cult/group_benefit.png").build()));

        cultAwayNewBenefit.ifPresent(benefit -> gridItems.add(ProductListWidgetItem.builder().title("Other city access").subTitle("Workout at any center in India").icon("/image/icons/cult/group_benefit.png").build()));

        benefitListItemWidget.setItems(gridItems);
        benefitListItemWidget.setLayoutProps(getVerticalPaddingLayout("20"));
        return benefitListItemWidget;
    }

    private BaseWidget getHowItWorksWidget() {

        ProductListWidget howItWorksWidget = new ProductListWidget();
        howItWorksWidget.setShowSquareIcons(true);
        howItWorksWidget.setCollapsable(false);
        howItWorksWidget.setHideSeparatorLines(true);
        howItWorksWidget.setHasDividerBelow(true);
        howItWorksWidget.setHeader(Header.builder().title("How It Works?").build());
        List<ProductListWidgetItem> list = new ArrayList<>();

        String transitionToString = StringUtil.capitalizeFirstLetter(transitionUserMembershipType.toString().replace("_", " "), true);
        String transitionTypeString = StringUtil.capitalizeFirstLetter(transitionType.getValue(), false);
        Optional<Benefit> creditOldBenefit = membership.getBenefits().stream().filter(item -> item.getName().equalsIgnoreCase("ACCESS_CREDITS")).findFirst();
        Optional<Benefit> creditNewBenefit = packTransitionResponse.getNewBenefitList().stream().filter(item -> item.getName().equalsIgnoreCase("ACCESS_CREDITS")).findFirst();


        ProductListWidgetItem transitionCostMore = new ProductListWidgetItem(transitionToString + " costs more. You need to pay the price accordingly.", "/image/icons/cult/wallet_banefit.png", 5.0);
        ProductListWidgetItem remainingDays = new ProductListWidgetItem("Remaining membership days will remain the same after the " + transitionTypeString + ".", "/image/icons/cult/watch.png", 5.0);
        ProductListWidgetItem remainingDaysAdjust = new ProductListWidgetItem("Your membership remaining days will be adjusted as per the price difference", "/image/icons/cult/wallet_banefit.png", 5.0);
        ProductListWidgetItem remainingPauseDays = new ProductListWidgetItem("Remaining pause days will be transferred as they are.", "/image/icons/cult/pause_benefit.png", 5.0);
        ProductListWidgetItem creditsInfo = new ProductListWidgetItem("Unlock any center with your credits", "/image/icons/cult/unlock_icon.png", 5.0);
        ProductListWidgetItem cultUnlimitedNoCredits = new ProductListWidgetItem("Enjoy unlimited access to all cult centers/gyms in your city with your new membership\n– no credits needed.", "/image/icons/cult/workout_benefit.png", 5.0);
        ProductListWidgetItem cultUnlimited = new ProductListWidgetItem("Enjoy unlimited access to all cult centers/gyms in your city with your new membership.", "/image/icons/cult/workout_benefit.png", 5.0);
        ProductListWidgetItem selectUnlimited = new ProductListWidgetItem("Enjoy unlimited access to your preferred center with your new membership.", "/image/icons/cult/workout_benefit.png", 5.0);
        ProductListWidgetItem unlimitedProGymsInCity = new ProductListWidgetItem("Enjoy unlimited access to all pro centers/gyms in your city with your new membership.", "/image/icons/cult/workout_benefit.png", 5.0);
        ProductListWidgetItem newPackOnTransfer = new ProductListWidgetItem("A new pack is created for the new city, transferring remaining adjusted days.", "/image/icons/cult/workout_benefit.png", 5.0);
        ProductListWidgetItem payTheBalance = new ProductListWidgetItem("Pay the balance if the new " + ((isCityTransfer) ? "city" : "center") + "'s price is higher. duration stays the same.", "/image/icons/cult/wallet_banefit.png", 5.0);
        ProductListWidgetItem enjoyUnlimitedBooking = new ProductListWidgetItem("Enjoy unlimited class booking in the " + ((!isCenterTransfer) ? "city." : "center"), "/image/icons/cult/watch.png", 5.0);
        ProductListWidgetItem benefitsTransferredBasis = new ProductListWidgetItem(transitionToString + " benefits will transfer based on the remaining membership duration.", "/image/icons/cult/star.png", 5.0);
        ProductListWidgetItem cultSportDiscount = new ProductListWidgetItem("Upto 50% off on cultsports fitness gear", "/image/membership/cutsport_discount.svg", 5.0);
        ProductListWidgetItem atHomeBenefit = new ProductListWidgetItem("Workout at home with cultpass home", "/image/icons/cult/live_icon.svg", 5.0);

        switch (transitionUserMembershipType) {
            case ELITE_PLUS -> {
                if (creditOldBenefit.isPresent()) {
                    list.add(cultUnlimitedNoCredits);
                } else {
                    list.add(cultUnlimited);
                }
                list.add(enjoyUnlimitedBooking);
                list.add(cultSportDiscount);
                list.add(atHomeBenefit);
            }
            case ELITE  -> {
                if (creditOldBenefit.isPresent()) {
                    list.add(cultUnlimitedNoCredits);
                } else {
                    list.add(cultUnlimited);
                }
                list.add(enjoyUnlimitedBooking);
            }
            case PRO_PLUS -> {
                list.add(unlimitedProGymsInCity);
            }
            case PRO -> {
                list.add(unlimitedProGymsInCity);
                list.add(atHomeBenefit);
            }
            case ELITE_SELECT, PRO_SELECT -> {
                list.add(selectUnlimited);
            }
        }

        list.add(benefitsTransferredBasis);
        if (transitionType.equals(PackTransitionType.TRANSFER)) list.add(newPackOnTransfer);
        if (creditNewBenefit.isPresent()) list.add(creditsInfo);
        if (!isFree) {
            list.add(transitionCostMore);
            if (transitionType.equals(PackTransitionType.TRANSFER)) list.add(payTheBalance);
        }
        if (Objects.nonNull(packTransitionResponse.getDaysDistribution().getPauseDays()) && packTransitionResponse.getDaysDistribution().getPauseDays() > 0) list.add(remainingPauseDays);
        if (Objects.nonNull(packTransitionResponse.getDaysDistribution().getDaysToIncrease()) && packTransitionResponse.getDaysDistribution().getDaysToIncrease() != 0) list.add(remainingDaysAdjust);
        else list.add(remainingDays);

        howItWorksWidget.setItems(list);
        howItWorksWidget.setLayoutProps(getVerticalPaddingLayout("20"));

        return howItWorksWidget;
    }

    private RichTextListWidget getTermsAndConditionsWidget() {
        String transition = transitionRequest.getPackTransitionType().equals(PackTransitionType.TRANSFER) ? "transferring" : "upgrading";
        RichTextListWidget tncWidget = new RichTextListWidget();
        Action tncAction = getTnCBottomSheetAction();
        List<List<RichTextItem>> listItems = new ArrayList<>();
        List<RichTextItem> item = new ArrayList<>();
        item.add(new RichTextItem("By " + transition +" my membership to a different city. I hereby accept the ", "P8", null, null, null, "#99FFFFFF"));
        item.add(new RichTextItem("terms and conditions ", "P6", null, null, tncAction, "#99FFFFFF"));
        item.add(new RichTextItem("of membership transfer.", "P8", null, null, null, "#99FFFFFF"));
        listItems.add(item);
        tncWidget.setListItems(listItems);
        tncWidget.setSideSpacing(20.0);
        tncWidget.setMaxLines(4);
        tncWidget.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "40"));
        return tncWidget;
    }

    private Action getTnCBottomSheetAction(){
        Action action = new Action();
        action.setActionType(ActionType.SHOW_CUSTOM_BOTTOM_SHEET);
        HashMap<String, Object> meta = new HashMap<>();

        List<BaseWidget> widgets = new ArrayList<>();

        String transition = transitionRequest.getPackTransitionType().equals(PackTransitionType.TRANSFER) ? "transfer" : "upgrade";

        // T&C Widget
        TaxAndFeeDetailsWidget tncWidget = new TaxAndFeeDetailsWidget();
        tncWidget.setTitle("Terms & conditions for " + (isCenterTransfer ? "center ": "city ") + transition);
        tncWidget.setDescription("I understand and agree that upon " + transition + " of my membership, my existing membership's benefits will stand permanently modified to those available in the transferee membership (at the time of " + transition + " & under the same category of membership)");
        tncWidget.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("20", "30"));
        widgets.add(tncWidget);

        //action list widget
        ActionListWidget actionWidget = new ActionListWidget();
        Action action1 = new Action();
        action1.setActionType(ActionType.POP_NAVIGATION);
        action1.setTitle("CLOSE");
        action1.setVariant("secondary");
        actionWidget.setActionList(Collections.singletonList(action1));
        actionWidget.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "40"));
        widgets.add(actionWidget);

        meta.put("showTopNotch", true);
        meta.put("widgets", widgets);
        action.setMeta(meta);
        return action;
    }

    private String transitionFrom() {
        return "Cultpass " + StringUtil.capitalizeFirstLetter(userMembershipType.toString().replace("_", " "), true);
    }

    private String transitionTo() {
        return "Cultpass " + StringUtil.capitalizeFirstLetter(transitionUserMembershipType.toString().replace("_", " "), true);
    }

    private PurchaseOptionSelectionCTA getTransferFooterWidgets() {
        PurchaseOptionSelectionCTA cta = new PurchaseOptionSelectionCTA();
        Map<String, Object> payload = new HashMap<>() {{
            put("purchaseType", "transition");
        }};

        Map<String, Object> meta = getMeta();

        cta.setPurchaseAction(getPurchaseCTA(meta, payload));
        if (!isCenterTransfer) {
            String citySelectionUrl = "curefit://fl_selectpackpreferredcenters?cityId=" + cityTo.getCityId();
            if (transitionUserMembershipType.toString().toLowerCase().contains("pro")) {
                citySelectionUrl += "&productId=" + membership.getProductId();
            }
            cta.setCenterSelectionAction(Action.builder().actionType(NAVIGATION)
                .url(citySelectionUrl)
                .title("Select preferred center").build());
        }
        return cta;
    }

    private BaseWidget getElitePlusToEliteTransferAlertWidget() {
        StringListWidget stringListWidget = new StringListWidget();
        stringListWidget.setMainAlignment(Alignment.SPACEBETWEEN);
        List<CFTextData> textDataList = new ArrayList<>();
        textDataList.add(CFTextData.builder().text("Sorry, We don't have Elite Plus in " + cityTo.getName()).maxLine("2").typeScale(UiUtils.TextTypeScales.P3).build());
        textDataList.add(CFTextData.builder().text("The difference will be adjusted proportionally, ensuring you only pay what you use").opacity(0.6).maxLine("2").typeScale(UiUtils.TextTypeScales.P8).build());
        stringListWidget.setStringList(textDataList);
        stringListWidget.setPaddingInBetween(List.of(15, 10, 15));
        stringListWidget.setHorizontalPadding(10.0);
        stringListWidget.setContainerData(CFContainerData.builder().opacity(0.1).color(UiUtils.UIColors.COLOR_WHITE).borderRadius(List.of(10.0, 10.0, 10.0, 10.0)).marginLTRB(List.of(20.0, 0.0, 20.0, 0.0)).build());
        stringListWidget.setPrefixImage(CFImageData.builder().url(Constants.INFO_ICON).width(20.0).height(20.0).containerData(CFContainerData.builder().marginLTRB(List.of(0.0, 20.0, 10.0, 0.0)).build()).build());
        stringListWidget.setGraphicsAlignment(Alignment.START);
        stringListWidget.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("25", "30"));
        return stringListWidget;
    }

    private Action getPurchaseCTA(Map<String, Object> meta, Map<String, Object> payload) {
        if (isFree) {
            return Action.builder().actionType(ActionType.CARE_CART_PAY_NOW).url("curefit://payment")
                    .title("Confirm " + transitionRequest.getPackTransitionType().getValue().toLowerCase()).meta(meta).payload(payload).build();
        } else {
            return Action.builder().actionType(ActionType.CARE_CART_PAY_NOW).url("curefit://payment")
                    .title(("Pay ₹" + packTransitionResponse.getPriceDistribution().getTotalAmount())).meta(meta).payload(payload).build();
        }
    }

    private Map<String, Object> getMeta() {
        OrderProduct orderProduct = new OrderProduct();
        OrderProductOption orderProductOption = new OrderProductOption();
        orderProductOption.setTransitionRequest(transitionRequest);
        orderProduct.setOption(orderProductOption);
        Map<String, Object> meta = new HashMap<>(){{
            put("useFitCash", true);
            put("dontCreateRazorpayOrder", true);
            put("orderProducts", List.of(orderProduct));
        }};
        if (isFree) {
            meta.put("payFree", true);
        }
        return meta;
    }

    private static Map<String, Object> getVerticalPaddingLayout(String bottomPadding) {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", Spacing.builder().top("0").bottom(bottomPadding).build());
        return layoutProps;
    }

}