package com.curefit.cfapi.view.viewbuilders.chroniccare.digitalapp;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp.DigiScorePageView;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DigiRiskScoreGoalWidget;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DigiSpacerWidget;
import com.curefit.common.data.exception.BaseException;
import com.sugarfit.catalog.client.CatalogClient;
import com.sugarfit.chs.pojo.UserMetricEntry;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Getter
@Setter
@Slf4j
@Component
@RequiredArgsConstructor
public class DigiScorePageBuilder {

    final ServiceInterfaces serviceInterfaces;
    final CatalogClient catalogClient;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final DigiPrePurchasePageBuilder digiPrePurchasePageBuilder;

    public DigiScorePageView buildView(UserContext userContext) throws BaseException {
        DigiScorePageView page = new DigiScorePageView();

        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            Map<String, UserMetricEntry> riskScoreProfile = serviceInterfaces.getChsClient().getProfileRiskScoreForUserId(userId);

            page.addWidget(digiPrePurchasePageBuilder.getDigiRiskScoreWidget(riskScoreProfile));
            page.addWidget(getPageDividerWidget());

            DigiRiskScoreGoalWidget riskScoreGoalWidget = digiPrePurchasePageBuilder.getDigiRiskScoreGoalWidget(riskScoreProfile);
            if (Objects.nonNull((riskScoreGoalWidget))) {
                page.addWidget(riskScoreGoalWidget);
                page.addWidget(getPageDividerWidget());
            }
        } catch (Exception e) {
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return page;
    }

    public DigiSpacerWidget getPageDividerWidget() {
        DigiSpacerWidget spacerWidget = new DigiSpacerWidget();
        spacerWidget.setBackgroundColor("#EEEFEF");
        return spacerWidget;
    }
}
