package com.curefit.cfapi.view.viewmodels.heimdall;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

@ToString
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HeimdallMongoAuditLog {
    String action;
    String auditLogId;
    String createdBy;
    String createdDate;
    String entityId;
    String entityName;
    String newValue;
    String previousValue;
    String reason;
    String source;
    String comment;
    String fieldName;
    String parentEntityId;
    String parentEntityName;
    String requestId;
    String userId;
    String meta;
}