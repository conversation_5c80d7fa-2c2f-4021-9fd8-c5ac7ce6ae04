package com.curefit.cfapi.view.viewbuilders.fitness;


import com.curefit.base.enums.Tenant;
import com.curefit.center.enums.SkuName;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.view.TextStyle;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.view.viewmodels.fitness.SportsDetailPageView;
import com.curefit.cfapi.view.viewmodels.gymfit.CentersPageView;
import com.curefit.cfapi.widgets.*;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.FaqWidgetV2;
import com.curefit.cfapi.pojo.vm.widget.Text.TextItem;
import com.curefit.cfapi.pojo.vm.widget.Text.TextItemParent;
import com.curefit.cfapi.widgets.common.banner.BannerCarouselWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.common.banner.MerchantryWidget;
import com.curefit.cfapi.widgets.fitness.TextPointerListWidget;
import com.curefit.cfapi.widgets.fitso.PlayCLPCentersWidget;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.offers.dtos.OfferMini;
import com.curefit.offers.dtos.play.PlayProductPricesResponse;
import com.curefit.offers.enums.OrderSource;
import com.curefit.offers.types.UserInfo;
import com.curefit.product.enums.ProductType;
import com.curefit.product.models.cult.FitnessPack;
import com.curefit.sportsapi.pojo.BookingEligibility;
import com.curefit.sportsapi.pojo.FTSCenterInfo;
import com.curefit.sportsapi.pojo.FTSCultBenefits;
import com.curefit.sportsapi.pojo.FTSFaqModel;
import com.curefit.sportsapi.pojo.FTSMedia;
import com.curefit.sportsapi.pojo.FTSSportsInfo;
import com.curefit.userservice.pojo.entry.UserEntry;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.curefit.cfapi.util.PlayUtil.*;


@Slf4j
public class FTSSportDetailPageViewBuilder {

    protected ServiceInterfaces interfaces;
    WidgetContext widgetContext;
    private Boolean isSportLevelPricingSupported;

    public FTSSportDetailPageViewBuilder(ServiceInterfaces interfaces, WidgetContext widgetContext) {
        this.interfaces = interfaces;
        this.widgetContext = widgetContext;
    }

    public SportsDetailPageView buildView(UserContext userContext, FTSSportsInfo sportsInfo) throws Exception {
        SportsDetailPageView sportsPage = new SportsDetailPageView();

        isSportLevelPricingSupported = PlayUtil.isSportLevelPricingSupported(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
        sportsPage.setTitle(sportsInfo.getName());

        List <Membership> playMemberships = PlayUtil.getCurrentPlayMemberships(userContext, interfaces);

        List<CompletableFuture<BaseWidget>> contentWidgets = new ArrayList<>();
        if (AppUtil.isWeb(userContext)){
            //white theme web page widgets
            contentWidgets.add(getWebHeaderInfoWidget(sportsInfo));
            sportsPage.addImage(sportsInfo.getSportBanner());
            sportsPage.setAction(getCTAAction(sportsInfo.getBookingStatus(), null, sportsInfo.getId(), false, playMemberships, userContext));
        } else {
            contentWidgets.add(getHeaderWidgetWithVideo(
                sportsInfo.getSportMedia() != null ? sportsInfo.getSportMedia() : new ArrayList<>(),
                    getHeaderBannerUrl(sportsInfo.getId().toString())
            ));
        }

        contentWidgets.add(getBannerUsingSportId(sportsInfo.getId().toString(), "1"));
        contentWidgets.add(getBannerUsingSportId(sportsInfo.getId().toString(), "2"));
        contentWidgets.add(getBannerUsingSportId(sportsInfo.getId().toString(), "3"));
        CompletableFuture<BaseWidget> nearByCenters = getNearByCentersWidget(userContext, sportsInfo.getId(), sportsInfo.getBookingStatus());
        if (nearByCenters != null)
            contentWidgets.add(nearByCenters);

        sportsPage.addAction(getCTAAction(sportsInfo.getBookingStatus(), null, sportsInfo.getId(),
                false, playMemberships, userContext));

//        if (isSportLevelPricingSupported) {
//            //Offer Widget
//            List<OfferMini> offers = getSportsLevelOffers(userContext, sportsInfo.getId().toString());
//            if (offers != null) {
//                CompletableFuture<BaseWidget> widget = PlayUtil.getCenterOfferWidget(offers, interfaces, userContext, null);
//                if (widget != null)
//                    contentWidgets.add(widget);
//            }
//
//            try {
//                PlayCLPCentersWidget centersWidget = new PlayCLPCentersWidget();
//                ArrayList<SkuName> skuNames = new ArrayList<>();
//                skuNames.add(SkuName.PLAY);
//                centersWidget.setSkuNames(skuNames);
//                HashMap<String, String> queryParams = new HashMap<>();
//                queryParams.put("workoutName", sportsInfo.getName());
//                queryParams.put("workoutIds", sportsInfo.getId().toString());
//                queryParams.put("showFilters", "false");
//                widgetContext.setQueryParams(queryParams);
//                contentWidgets.add(CompletableFuture.completedFuture(centersWidget.buildView(interfaces, userContext, widgetContext).get(0)));
//                sportsPage.addAction(null);
//            } catch (Exception e) {
//                log.info("Sports page error ", e);
//            }
//        } else {
//            if (sportsInfo.getCultBenefits() != null)
//                contentWidgets.add(getCultBenefitsWidget(sportsInfo.getCultBenefits()));
//            CompletableFuture<BaseWidget> nearByCenters = getNearByCentersWidget(userContext, sportsInfo.getId(), sportsInfo.getBookingStatus());
//            if (nearByCenters != null)
//                contentWidgets.add(nearByCenters);
//
//            sportsPage.addAction(getCTAAction(sportsInfo.getBookingStatus(), null, sportsInfo.getId(),
//                    false, playMemberships, userContext));
//        }

        if (sportsInfo.getFaq() != null)
            contentWidgets.add(getFaqWidget(sportsInfo.getFaq()));

        CompletableFuture<List<BaseWidget>> widgetsPromise = FutureUtil.allOf(contentWidgets);
        sportsPage.addWidgets(widgetsPromise.get().stream().filter(Objects::nonNull).toList());
        return sportsPage;
    }

    private String getHeaderBannerUrl(String sportId) {
        switch (sportId){
            case BADMINTON_WORKOUT_ID:
                return "image/icons/fitsoImages/sport/header_badminton.png";
            case SWIMMING:
                return "image/icons/fitsoImages/sport/header_swim.png";
            case TT_WORKOUT_ID:
                return "image/icons/fitsoImages/sport/header_tt.png";
            case TENNIS_WORKOUT_ID:
                return "image/icons/fitsoImages/sport/header_tennis.png";
            case SQUASH_WORKOUT_ID:
                return "image/icons/fitsoImages/sport/header_squash.png";
            default:
                return DEFAULT_CENTER_IMAGE;
        }
    }

    private CompletableFuture<BaseWidget> getBannerUsingSportId(String sportId, String bannerPosition){
        CompletableFuture<BaseWidget> widget = null;
        switch (sportId){
            case BADMINTON_WORKOUT_ID:
                if (bannerPosition.equals("1")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/badminton_banner_011.png", 235);
                } else if (bannerPosition.equals("2")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/badminton_banner_012.png", 700);
                } else if (bannerPosition.equals("3")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/badminton_banner_013.png", 578);
                }
                break;
            case SWIMMING:
                if (bannerPosition.equals("1")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/swimming_banner_011.png", 235);
                } else if (bannerPosition.equals("2")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/swimming_banner_012.png", 700);
                } else if (bannerPosition.equals("3")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/swimming_banner_013.png", 782);
                }
                break;
            case TT_WORKOUT_ID:
                if (bannerPosition.equals("1")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/tt_banner_011.png", 235);
                } else if (bannerPosition.equals("2")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/tt_banner_012.png", 700);
                } else if (bannerPosition.equals("3")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/tt_banner_013.png", 506);
                }
                break;
            case TENNIS_WORKOUT_ID:
                if (bannerPosition.equals("1")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/tennis_banner_011.png", 235);
                } else if (bannerPosition.equals("2")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/tennis_banner_012.png", 700);
                } else if (bannerPosition.equals("3")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/tennis_banner_013.png", 630);
                }
                break;
            case SQUASH_WORKOUT_ID:
                if (bannerPosition.equals("1")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/squash_banner_011.png", 235);
                } else if (bannerPosition.equals("2")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/squash_banner_012.png", 700);
                } else if (bannerPosition.equals("3")) {
                    widget = getBannerWidget("image/icons/fitsoImages/sport/squash_banner_013.png", 634);
                }
                break;
        }

        return widget;
    }

    private CompletableFuture<BaseWidget> getBannerWidget(String imageUrl, int imageHeight) {
        BannerCarouselWidget bannerCarouselWidget = new BannerCarouselWidget();
        bannerCarouselWidget.setMaxNumBanners(1);
        bannerCarouselWidget.setMinNumBanners(0);
        bannerCarouselWidget.setTemplateId("sportsPageBanners");
        bannerCarouselWidget.setWidgetType(WidgetType.BANNER_CAROUSEL_WIDGET);
        BannerItem bannerItem = new BannerItem();
        bannerItem.setImage(imageUrl);
        bannerCarouselWidget.setData(List.of(bannerItem));
        Map<String, Object> layoutProps = new HashMap<>();
        Spacing spacing = new Spacing();
        spacing.setBottom("0");
        bannerCarouselWidget.setSpacing(spacing);
        layoutProps.put("roundedCorners", false);
        layoutProps.put("bannerHeight", imageHeight);
        layoutProps.put("bannerWidth", 375);
        layoutProps.put("bannerOriginalHeight", imageHeight);
        layoutProps.put("bannerOriginalWidth", 375);
        layoutProps.put("verticalPadding", 0);
        layoutProps.put("showPagination", false);
        layoutProps.put("edgeToEdge", true);
        layoutProps.put("spacing", spacing);
        bannerCarouselWidget.setLayoutProps(layoutProps);
        return CompletableFuture.completedFuture(bannerCarouselWidget);
    }

    private List<OfferMini> getSportsLevelOffers(UserContext userContext, String sportId) throws Exception {

        List<FitnessPack> citySportsPack = interfaces.catalogueService.getPlayProductCenterActivityByCity(userContext.getUserProfile().getCity().getCityId(), sportId, true, userContext.getUserProfile().getUserId());
        if (citySportsPack == null || citySportsPack.isEmpty())
            return null;

        citySportsPack = citySportsPack.stream().filter(pack -> pack.getVisibility() != null && pack.getVisibility().contains("app")).toList();
        List<String> productIds = citySportsPack.stream().map(pack -> pack.getProductId()).collect(Collectors.toList());
        PlayProductPricesResponse playProductPricesResponse = (PlayProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                ProductType.PLAY, userContext, productIds, userContext.getUserProfile().getCity().getCityId(),null
        ).get();

        return SelectPacksUtils.getOfferDetails(
                citySportsPack.get(0).getPrice(),
                productIds.get(0),
                playProductPricesResponse.getPriceMap(),
                playProductPricesResponse.getOfferMap()
        );
    }

    private CompletableFuture<BaseWidget> getWebHeaderInfoWidget(FTSSportsInfo sportsInfo) {
        ImageOverlayCardContainerWidget containerWidget = new ImageOverlayCardContainerWidget();
        containerWidget.setProductType(ProductType.FITNESS.toString());
        containerWidget.setAssets(new ArrayList<>() {{
            add(Assets.builder().assetType(FTSMedia.MediaType.IMAGE.toString()).assetUrl(sportsInfo.getSportBanner()).build());
        }});
        containerWidget.addWidget(getHeaderWidget(sportsInfo));
        containerWidget.addWidget(getCultCalorieWidget(sportsInfo));
        containerWidget.addWidget(getWorkoutBenefitWidget(sportsInfo));

        return CompletableFuture.completedFuture(containerWidget);
    }

    private BaseWidget getHeaderWidget(FTSSportsInfo sportsInfo){
        WebHeaderWidget widget = new WebHeaderWidget();
        widget.setWidgetTitle(Header.builder().title(sportsInfo.getName()).build());
        widget.setHeaderStyle(TextStyle.builder().color(UiUtils.UIColors.COLOR_BLACK).fontSize(24).marginLeft(0).build());
        widget.setHasTopPadding(false);
        widget.setHasDividerBelow(false);
        widget.setHasTopPadding(false);
        widget.setShowDivider(false);
        return widget;
    }

    private BaseWidget getCultCalorieWidget(FTSSportsInfo sportsInfo){
        CultWorkoutCaloriesWidget widget = new CultWorkoutCaloriesWidget();
        widget.setCalorieTitle("CALORIES");
        widget.setCalorieIcon("/image/icons/cult/calorie.png");
        widget.setCalorieText(sportsInfo.getMaxCaloriesBurnt());
        return widget;
    }

    private BaseWidget getWorkoutBenefitWidget(FTSSportsInfo sportsInfo){
        CultWorkoutBenefitsWidget widget = new CultWorkoutBenefitsWidget();
        widget.setTitle("BENEFIT");
        widget.setBenefits(String.join(" | ", sportsInfo.getBenefits()));
        return widget;
    }

    private CompletableFuture<BaseWidget> getHeaderWidgetWithVideo(List<FTSMedia> mediaList, String image){
        FTSMedia videoMedia = null;
        for (FTSMedia media : mediaList) {
            if (media.getMediaType() == FTSMedia.MediaType.VIDEO){
                videoMedia = media;
            }
        }
        MerchantryWidget merchantryWidget = new MerchantryWidget();
        List<BannerItem> items = new ArrayList<>();
        BannerItem item = new BannerItem();
        item.setTitle("");
        item.setImage(image);
        if (videoMedia != null) {
            merchantryWidget.setTemplateId("video_banner (375:470)");
            item.setVideoUrl(videoMedia.getMediaUrl());
            item.setLoopVideo(true);
        }
        items.add(item);
        merchantryWidget.setData(items);
        merchantryWidget.setEnableBottomFade(false);
        merchantryWidget.setLayoutProps(getLayoutProps());
        return CompletableFuture.completedFuture(merchantryWidget);

    }

    @GetMapping
    private Map<String, Object> getLayoutProps() {
        Map<String, Object> layoutProps = new HashMap<>();

        Map<String, Object> layoutData = new HashMap<>();
        layoutProps.put("type", "STORIES");
        layoutData.put("showPagination", false);
        layoutData.put("edgeToEdge", true);
        layoutData.put("aspectRatio", "375:527");
        layoutProps.put("data", layoutData);
        layoutProps.put("spacing", Spacing.builder().top("0").bottom("0").build());

        return layoutProps;
    }

    private CompletableFuture<BaseWidget> getCultBenefitsWidget(List<FTSCultBenefits> cultBenefits){
        TextPointerListWidget textListWidget = new TextPointerListWidget();
        Header header = new Header();
        header.setTitle("Why play at cult");
        textListWidget.setHeader(header);
        textListWidget.addSpacing("0", "70");

        ArrayList<TextItemParent> cultBenefitList = new ArrayList<>();

        for (FTSCultBenefits benefit : cultBenefits) {

            ArrayList<TextItem> itemList = new ArrayList<>();
            TextItem item = new TextItem();
            item.setText(benefit.getDescription());
            itemList.add(item);

            TextItemParent itemParent = new TextItemParent(itemList, null, benefit.getIcon(), null, null, null, null);
            cultBenefitList.add(itemParent);
        }

        textListWidget.setList(cultBenefitList);

        return CompletableFuture.completedFuture(textListWidget);
    }

    private CompletableFuture<BaseWidget> getNearByCentersWidget(UserContext userContext, Long sportId, BookingEligibility status) {
        try {
            GymCentersNearbyWidget gymCentersNearbyWidget = new GymCentersNearbyWidget();
            gymCentersNearbyWidget.setTitleAction(new Action("", ActionType.NAVIGATION));
            gymCentersNearbyWidget.setSkuType(CentersPageView.SkuTypeValue.PLAY);
            WidgetContext widgetContext = new WidgetContext();
            List<BaseWidget> widgetList = gymCentersNearbyWidget.buildView(interfaces, userContext, widgetContext);
            GymCentersNearbyWidget widget = (GymCentersNearbyWidget) widgetList.get(0);
            if(!AppUtil.isWeb(userContext)) widget.setSeeAllAction(null);
            widget.addSpacing("0", "70");

            return CompletableFuture.completedFuture(widget);
        }catch (Exception e){
            return null;
        }
    }

    String getCardTopTitle(FTSCenterInfo centerEntry) {
        return centerEntry.getDistanceFromSearchOrigin() + " KM" + " • " + centerEntry.getLocality();
    }

    private CompletableFuture<BaseWidget> getFaqWidget(List<FTSFaqModel> faq){
        FaqWidgetV2 faqWidgetV2 = new FaqWidgetV2();
        faqWidgetV2.setFaqTitle("FAQ");
        faqWidgetV2.setLinks(faqWidgetV2.setPointerList(faq));
        faqWidgetV2.addSpacing("0", "70");
        return CompletableFuture.completedFuture(faqWidgetV2);
    }


    private Action getAction(Boolean isMembershipActive, Boolean isTrialActive, Long sportId){
        if (isMembershipActive){
            return new Action(BOOKING_DEEPLINK + "workoutId=" + sportId, "BOOK NOW", ActionType.NAVIGATION);
        } else if (isTrialActive){
            //Give trial option
            return new Action(BOOKING_DEEPLINK + "workoutId=" + sportId, "TRY FOR FREE", ActionType.NAVIGATION);
        } else {
            //Give buy membership option
            return new Action(MEMBERSHIP_LANDING_PAGE_DEEPLINK, "BUY MEMBERSHIP", ActionType.NAVIGATION);
        }
    }

    private Action getCenterAction(Boolean isMembershipActive, Boolean isTrialActive, Long centerId, Long sportId){
        if (isMembershipActive){
            return new Action(BOOKING_DEEPLINK + "centerId=" + centerId + "&sportId=" + sportId + "&workoutId=" + sportId, "BOOK NOW", ActionType.NAVIGATION);
        } else if (isTrialActive){
            //Give trial option
            return new Action(BOOKING_DEEPLINK + "centerId=" + centerId + "&sportId=" + sportId + "&workoutId=" + sportId, "TRY FOR FREE", ActionType.NAVIGATION);
        }else {
            //Give buy membership option
            return new Action(MEMBERSHIP_LANDING_PAGE_DEEPLINK + "&sportId" + sportId, "BUY MEMBERSHIP", ActionType.NAVIGATION);
        }
    }

    private Action getCTAAction(
        BookingEligibility eligibility,
        Long centerId,
        Long sportId,
        Boolean isUpcomingCenter,
        List <Membership> playMemberships,
        UserContext userContext
    ){
        StringBuilder bookingUrl = new StringBuilder();
        bookingUrl.append(BOOKING_DEEPLINK);
        bookingUrl.append("sportId=").append(sportId);
        bookingUrl.append("&workoutId=").append(sportId);
        if (centerId != null)
            bookingUrl.append("&centerId=").append(centerId);

        Action ctaAction = null;

        switch (eligibility) {
            case PAUSE, MEMBERSHIP, OTHER_BENEFITS, UPCOMING_MEMBERSHIP, UPCOMING_PAUSE, PLAY_AWAY_BENEFITS:
                ctaAction = new Action(bookingUrl.toString(), "BOOK NOW", ActionType.NAVIGATION);
                break;
            case TRIAL:
                ctaAction = new Action(bookingUrl.toString(), "TRY FOR FREE", ActionType.NAVIGATION);
                break;
            case EXPIRED, TRIAL_EXHAUSTED, OTHER_BENEFITS_EXHAUSTED, SPORT_CENTER_SELECT_MEMBERSHIP,
                    PLAY_AWAY_BENEFITS_EXHAUSTED, LIMITED_AVAILABLE, LIMITED_SESSION_UNAVAILABLE, LIMITED_EXPIRED_SESSION:
                ctaAction = new Action(bookingUrl.toString(), "BOOK NOW", ActionType.NAVIGATION);
                break;
            case SELECT_MEMBERSHIP, PLAY_SELECT_PAUSE, UPCOMING_PLAY_SELECT_PAUSE:
                Membership playMembership = playMemberships.get(0);
                List<NameValuePair> nameValuePairs = new ArrayList<>();

                // City check to ensure user is in same city as of center.
                updatePageParamsIfUserInSameCityAsCenter(playMembership, userContext, nameValuePairs);

                nameValuePairs.add(new BasicNameValuePair("pageFrom", "sport"));
                nameValuePairs.add(new BasicNameValuePair("sportId", String.valueOf(sportId)));
                nameValuePairs.add(new BasicNameValuePair("workoutId", String.valueOf(sportId)));
                ctaAction = PlayUtil.getPlayBookNowCTAAction(false, false, nameValuePairs);
        }

        if (isUpcomingCenter && centerId != null){
            ctaAction.setDisabled(true);
        }

        return ctaAction;
    }
}
