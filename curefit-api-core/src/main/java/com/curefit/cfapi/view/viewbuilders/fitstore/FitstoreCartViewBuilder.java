package com.curefit.cfapi.view.viewbuilders.fitstore;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.segmentation.client.pojo.SegmentSet;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.curefit.alfred.models.order.GearCartReviewResponse;
import com.curefit.alfred.models.order.Order;
import com.curefit.alfred.models.order.ProductAvailability;
import com.curefit.alfred.models.order.ProductSnapshot;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.model.internal.common.PriceComponent;
import com.curefit.cfapi.model.internal.fitstore.FitstoreCartListItem;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.base.AlertInfo;
import com.curefit.cfapi.pojo.view.ContainerStyle;
import com.curefit.cfapi.pojo.view.WidgetStyle;
import com.curefit.cfapi.service.ApiKeyService;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.FitstoreUtil;
import com.curefit.cfapi.util.FutureUtil;
import com.curefit.cfapi.util.OrderUtil;
import com.curefit.cfapi.util.TataNeuUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.view.viewbuilders.fitstore.buildermodels.CartProcessingResponse;
import com.curefit.cfapi.view.viewbuilders.fitstore.buildermodels.CartServiceabilityResponse;
import com.curefit.cfapi.view.viewbuilders.fitstore.buildermodels.GearTrainerOfferProcessingResponse;
import com.curefit.cfapi.view.viewmodels.fitstore.CartPageBaseView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.common.BillingWidget;
import com.curefit.cfapi.widgets.common.NeuPassEarnedCoinsWidget;
import com.curefit.cfapi.widgets.common.ToggleNeuPassWidget;
import com.curefit.cfapi.widgets.eat.AddressWidgetV2;
import com.curefit.cfapi.widgets.eat.DeliverySlotWidget;
import com.curefit.cfapi.widgets.eat.EatAddressWidget;
import com.curefit.cfapi.widgets.fitstore.FitstoreCartListWidget;
import com.curefit.gearvault.models.CartItem;
import com.curefit.gearvault.models.CatalogueProduct;
import com.curefit.gearvault.models.GearCartResponse;
import com.curefit.hamlet.models.pojo.BaseHamletContext;
import com.curefit.hamlet.models.request.HamletConfigRequest;
import com.curefit.hamlet.service.HamletService;
import com.curefit.hamlet.types.ConfigQuery;
import com.curefit.location.models.UserDeliveryAddress;
import com.curefit.offers.types.Offer;
import com.curefit.product.ProductPrice;
import com.curefit.thirdPartyIntegrations.pojo.PotentialTataPointsEarnRequest;
import com.curefit.thirdPartyIntegrations.pojo.PotentialTataPointsEarnResponse;
import com.curefit.userservice.pojo.entry.UserEntry;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class FitstoreCartViewBuilder extends FitstoreBaseCartViewBuilder {
    String TATA_NEU_LOGO;

    final ServiceInterfaces serviceInterfaces;
    TataNeuUtil tataNeuUtil;
    private HamletService hamletService;
    private EnvironmentService envService;
    private ExceptionReportingService exceptionReportingService;
    private ApiKeyService apiKeyService;


    @Autowired
    public FitstoreCartViewBuilder(
            ServiceInterfaces serviceInterfaces,
            final TataNeuUtil tataNeuUtil,
            HamletService hamletService,
            EnvironmentService envService,
            ExceptionReportingService exceptionReportingService,
            ApiKeyService apiKeyService
    ) {
        this.serviceInterfaces = serviceInterfaces;
        this.tataNeuUtil = tataNeuUtil;
        this.TATA_NEU_LOGO = serviceInterfaces.tata3pServiceHelper.getTataNeuLogo();
        this.hamletService = hamletService;
        this.envService = envService;
        this.exceptionReportingService = exceptionReportingService;
        this.apiKeyService = apiKeyService;
    }


    public CartPageBaseView buildCartPageView(
            UserContext userContext,
            UserEntry user,
            GearCartResponse cart,
            GearCartReviewResponse cartReviewResponse,
            List<CatalogueProduct> addOnProducts,
            UserDeliveryAddress address,
            Integer fitCashBalance,
            Boolean isFitCashEnabled,
            Boolean cartUpdateFailed,
            Map<String, Offer> offerMap,
            Boolean isMaxItemQtyReached

    ) throws Exception {
        CartPageBaseView response = new CartPageBaseView();
        List <CompletableFuture<BaseWidgetNonVM>> widgetsPromise = new ArrayList<>();
        List <CompletableFuture<BaseWidgetNonVM>> footerWidgetsPromise = new ArrayList<>();
        AtomicReference<Boolean> isFlutterStorePDPSupported = new AtomicReference(false);

        isFlutterStorePDPSupported.set(AppUtil.doesUserBelongToStoreFlutterPDPExperiment(serviceInterfaces, serviceInterfaces.environmentService, userContext));

        CartServiceabilityResponse serviceabilityResponse = this.getCartServiceability(address, cartReviewResponse.getAvailabilities());
        Boolean isNotServiceable = serviceabilityResponse.getIsNotServiceable();
        Boolean isSomeProductNotAvailable = serviceabilityResponse.getSomeItemsNotAvailable();

        CartProcessingResponse processingResponse = this.getCartProcessingResponse(cart.getItems());
        Integer totalItems = processingResponse.getTotalItems();
        Boolean isNonReturnableProductPresent = processingResponse.getIsNonReturnableProductPresent();
        List<CartItem> orderItems = processingResponse.getOrderItems();

        GearTrainerOfferProcessingResponse trainerOfferProcessingResponse = this.getTrainerOffersProcessingResponse(offerMap, cartReviewResponse.getOrder().getProductSnapshots());
        Boolean isTrainerOfferApplied = trainerOfferProcessingResponse.getIsTrainerOfferApplied();
        Boolean isTrainerOfferBreached = trainerOfferProcessingResponse.getIsTrainerOfferBreached();

        Boolean isMaxCartQtyReached =  totalItems > FitstoreUtil.MAX_CART_QUANTITY;

        widgetsPromise.add(this.getHighlightedBannerInfoWidget(
                isNonReturnableProductPresent,
                isSomeProductNotAvailable,
                isNotServiceable,
                address != null,
                isMaxCartQtyReached,
                isTrainerOfferApplied,
                false
        ));
        widgetsPromise.add(this.getCartListWidget(cart, cartReviewResponse, isFlutterStorePDPSupported.get()));
        widgetsPromise.add(this.getAddOnListWidget(addOnProducts, cart, isFlutterStorePDPSupported.get(), false));
        widgetsPromise.add(this.getBillingWidget(userContext, cartReviewResponse.getOrder(), fitCashBalance, isFitCashEnabled));
        Order order = cartReviewResponse.getOrder();
        PotentialTataPointsEarnRequest potentialTataPointsEarnRequest = new PotentialTataPointsEarnRequest(order.getProductSnapshots(), order.getTotalAmountPayable(), order.getSource());
        SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();

//        widgetsPromise.add(this.getToggleNeuPassWidget(potentialTataPointsEarnRequest, this.tataNeuUtil.getNEUPASS_CONSENT_PENDING(), userSegments));
        String experimentId = this.envService.isProduction() || this.envService.isAlpha() ? "1064" : "527";
        String source = AppUtil.callSource(userContext.getSessionInfo().getApiKey(), apiKeyService);
        if ("TATA_NEU_WEB_APP".equals(source) || (isTataTestUser(userContext, experimentId) && Arrays.asList("CUREFIT_APP", "CUREFIT_NEW_WEBSITE").contains(source))) {
            widgetsPromise.add(this.getNeuPassEarnedCoinsWidget(potentialTataPointsEarnRequest, this.tataNeuUtil.getNEUPASS_USER(), userSegments));
        }
        widgetsPromise.add(this.getOfferDetailWidgetFromOrder(cartReviewResponse.getOrder(), offerMap));
        footerWidgetsPromise.add((this.getAddressAndDeliveryInfoWidget(userContext, address, cartReviewResponse, isNotServiceable)));

        List<Action> actions = new ArrayList<>(Objects.requireNonNull(this.getPageAction(
                userContext,
                user,
                address,
                isNotServiceable,
                isSomeProductNotAvailable,
                cartReviewResponse.getOrder(),
                isMaxCartQtyReached,
                isTrainerOfferBreached,
                false
        )));

        response.addWidgets(FutureUtil.allOf(widgetsPromise).get().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        response.addFooterWidgets(FutureUtil.allOf(footerWidgetsPromise).get().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        actions.stream().filter(Objects::nonNull).forEach(action -> {
            action.setUseFitCash(isFitCashEnabled);
            response.addAction(action);
        });
        // Passing this info to update cart with correct cart products
        response.setOrderItems(orderItems);
        response.setTotalItems(totalItems);
        response.setTotalPayable(OrderUtil.toFixedValue(Double.sum(cartReviewResponse.getOrder().getTotalAmountPayable(), cartReviewResponse.getOrder().getTotalFitCashPayable()), 2));
        AlertInfo applicableAlertInfo = getApplicableAlertInfo(isTrainerOfferBreached, cartUpdateFailed, isMaxItemQtyReached);

        if (applicableAlertInfo != null) {
            response.setAlertInfo(applicableAlertInfo);
        }
        return response;
    }

    private Boolean isTataTestUser(UserContext userContext, String experimentId) {
        String userId= userContext.getUserProfile().getUserId();
        log.info("FitstoreCartViewBuilder isTataTestUser being called for userId :: {}", userId);
        if (userId.isEmpty() || userId.equals("0")) {
            log.info("FitstoreCartViewBuilder isTataTestUser UserId is empty or 0");
            return false;
        }
        try {
            BaseHamletContext baseHamletContext = BaseHamletContext.builder()
                    .userId(userContext.getUserProfile().getUserId())
                    .tenant(AppTenant.CUREFIT)
                    .build();
            log.info("FitstoreCartViewBuilder isTataTestUser hamletService.getConfig being called for baseHamletContext :: {}", baseHamletContext);
            Boolean isTataTestUser = hamletService.getConfig(new HamletConfigRequest<Boolean>(new ConfigQuery(experimentId, "isTataTestUser", false), baseHamletContext));
            log.info("FitstoreCartViewBuilder isTataTestUser :: {}", isTataTestUser);
            return isTataTestUser;
        } catch (Exception e) {
            this.exceptionReportingService.reportException("error in FitstoreCartViewBuilder isTataTestUser getConfig", e);
            return false;
        }
    }

    private CompletableFuture<BaseWidgetNonVM> getCartListWidget(
            GearCartResponse cart,
            GearCartReviewResponse cartReviewResponse,
            Boolean isFlutterStorePDPSupported
    ) {

        FitstoreCartListWidget widget = new FitstoreCartListWidget();
        Order order =  cartReviewResponse.getOrder();
        Integer cartSize = cart.getItems().size();

        // widget.setTitle(cartSize.toString() + (cartSize > 1 ? " Items" : " Item"));

        cart.getItems().stream().forEach(item -> {
            ProductSnapshot productSnapShot = null;
            ProductAvailability productAvailability = null;
            FitstoreCartListItem cartItem = new FitstoreCartListItem();
            List<String> annotations = new ArrayList<>();

            for (ProductAvailability availability : cartReviewResponse.getAvailabilities()) {
                if (availability.getProductId().equals(item.getProductId())) {
                    productAvailability = availability;
                }
            }

            for (ProductSnapshot snapshot : order.getProductSnapshots()) {
                if(snapshot.getProductId().equals(item.getProductId())) {
                    productSnapShot = snapshot;
                }
            }


            if (productAvailability != null && !productAvailability.getIsServiceable()) {
                annotations.add(FitstoreUtil.ADDRESS_NOT_SERVICEABLE);
                cartItem.setIsDisabledUI(true);
            } else if (productAvailability != null && !productAvailability.getInStock()) {
                annotations.add(FitstoreUtil.ITEM_UNAVAILABLE_INFO);
                cartItem.setIsDisabledUI(true);
            } else {
                if (!item.getVariant().isReturnable()) {
                    annotations.add(FitstoreUtil.ITEM_NON_RETURNABLE_INFO);
                }
                if (item.getVariant() != null && item.getVariant().getAnnotations() != null) {
                    annotations.addAll(Arrays.asList(item.getVariant().getAnnotations()));
                }
            }
            if (item.getVariant().getImages()!= null) {
                cartItem.setImage(item.getVariant().getImages().getProduct_url());
            }
            if (productSnapShot != null) {
                ProductPrice price = new ProductPrice();
                price.setCurrency("INR");
                price.setMrp(BigDecimal.valueOf(Double.parseDouble(String.valueOf(productSnapShot.getPrice().getMrp()))).setScale(0, RoundingMode.HALF_UP));
                price.setListingPrice(BigDecimal.valueOf(Double.parseDouble(String.valueOf(productSnapShot.getPrice().getListingPrice()))).setScale(0, RoundingMode.HALF_UP));
                cartItem.setPrice(price);
            }
            cartItem.setQuantity(item.getQuantity());
            cartItem.setId(item.getVariant().getProduct_id());
            cartItem.setProductId(item.getSku());
            cartItem.setTitle(item.getVariant().getName());
            cartItem.setBrand(item.getVariant().getBrand());
            cartItem.setSubtitle("Size: " + item.getSize());
            cartItem.setAnnotations(annotations);
            cartItem.setAction(isFlutterStorePDPSupported ? FitstoreUtil.getFitStoreFlutterProductPageAction(String.valueOf(item.getVariant().getProduct_id())) : FitstoreUtil.getFitStoreProductPageAction(String.valueOf(item.getVariant().getProduct_id())));
            widget.addCartItem(cartItem);

        });
        return CompletableFuture.completedFuture(widget);
    }

    private CompletableFuture<BaseWidgetNonVM> getBillingWidget(UserContext userContext, Order order, Integer fitCashBalance, Boolean isFitcashEnabled) {
        BillingWidget widget = new BillingWidget();
        List<PriceComponent> priceComponents = new ArrayList<>();
        String currency = OrderUtil.getCurrencySymbol(userContext.getUserProfile().getCity().getCountry().getCurrencyCode());
        if (order.getPriceDetails() != null) {
            PriceComponent comp = new PriceComponent();
            comp.setTitle("Item Total");
            comp.setValueWithCurrency(currency + OrderUtil.toFixedValue(order.getPriceDetails().getTotal_without_discount(), 2));
            priceComponents.add(comp);
            if (order.getPriceDetails().getDiscount() != null) {
                PriceComponent comp1 = new PriceComponent();
                comp1.setTitle("Total Discount (-)");
                comp1.setValueWithCurrency("-" + currency + OrderUtil.toFixedValue(order.getPriceDetails().getDiscount(), 2));
                comp1.setDiscountBreakUpInfo(this.buildOfferDiscountBreakup(order));
                priceComponents.add(comp1);
            }

            if (order.getTotalFitCashPayable() != null || fitCashBalance != null) {
                PriceComponent comp2 = new PriceComponent();
                String value = order.getTotalFitCashPayable() != null ? OrderUtil.toFixedValue(order.getTotalFitCashPayable(), 2) : OrderUtil.toFixedValue(0.0, 2);
                comp2.setTitle("Use Fitcash");
                comp2.setIsEnabled(isFitcashEnabled);
                comp2.setIsFitcashPrice(true);
                comp2.setFitcashBalance(fitCashBalance);
                comp2.setValue(value);
                comp2.setValueWithCurrency(currency + value);
                priceComponents.add(comp2);
            }

            PriceComponent comp3 = new PriceComponent();
            comp3.setTitle("Total Payable");
            comp3.setValueWithCurrency(currency + OrderUtil.toFixedValue(order.getTotalAmountPayable(), 2));
            priceComponents.add(comp3);

            ContainerStyle containerStyle = new ContainerStyle();
            containerStyle.setPaddingBottom(20);

            widget.setPriceDetails(priceComponents);
            widget.setFitcashBalance(fitCashBalance);
            widget.setIsFitcashEnabled(isFitcashEnabled);
            widget.setCurrency(userContext.getUserProfile().getCity().getCountry().getCurrencyCode());
            widget.setContainerStyle(containerStyle);
            widget.setHasDividerBelow(true);
            widget.setDividerType("LARGE");

            return CompletableFuture.completedFuture(widget);
        }
        return CompletableFuture.completedFuture(null);
    }

    private CompletableFuture<BaseWidgetNonVM> getAddressAndDeliveryInfoWidget(
            UserContext userContext,
            UserDeliveryAddress address,
            GearCartReviewResponse cartReviewResponse,
            Boolean isNotServiceable) {

        EatAddressWidget widget = new EatAddressWidget();
        AddressWidgetV2 addressWidget = new AddressWidgetV2();
        DeliverySlotWidget deliverySlotWidget = new DeliverySlotWidget();

        deliverySlotWidget.setSubTitle("DELIVERY");

        if (address != null) {
            List<Action> actions = new ArrayList<>();
            actions.add(FitstoreUtil.getFitStoreAddressSelectionAction(userContext, "Select Address"));
            addressWidget.setTitle(address.getAddressType());
            addressWidget.setWeekdayAddressId(address.getAddressId());
            addressWidget.setWeekdayAddress(address.getAddressLine1() != null ? (address.getAddressLine1() + ", " + (address.getAddressLine2() != null ? address.getAddressLine2() : "")) : "");
            addressWidget.setActions(actions);
            if (cartReviewResponse.getArrivalInfo() != null && cartReviewResponse.getArrivalInfo().getEta() != null) {
                try {
                    deliverySlotWidget.setTitle(TimeUtil.formatDateInTimezoneFromString(cartReviewResponse.getArrivalInfo().getEta(), "yyyy-MM-dd'T'HH:mm:ss.SSSXXX", TimeZone.getTimeZone(userContext.getUserProfile().getCity().getTimezone()), "d MMM, hh:mm aaa"));
                } catch (Exception e) {
                    log.error("Error in date formatting", e);
                    deliverySlotWidget.setTitle(cartReviewResponse.getArrivalInfo().getEta());
                }

            } else  if (isNotServiceable){
                deliverySlotWidget.setTitle("Not available for this location");
            }
            widget.addWidget(addressWidget);
            widget.addWidget((deliverySlotWidget));
        } else {
            return CompletableFuture.completedFuture(null);
        }

        return CompletableFuture.completedFuture(widget);
    }

    @NotNull
    private Action getWidgetAction(Boolean activateNeuPass) {
        Action widgetAction = new Action();
        widgetAction.setActionType(ActionType.REST_API);
        widgetAction.setTitle("DISMISS");
//        Map<String, Object> meta = new HashMap<>();
//        meta.put("method", "POST");
//        meta.put("url", "/order/toggleActivateNeuPass");
//        Map<String, Object> body = new HashMap<>();
//        body.put("orderId", null);
//        body.put("activateNeuPass", activateNeuPass);
//        meta.put("body", body);
//        meta.put("showLoader", true);
        widgetAction.setMeta(null);
        log.info("FitstoreCartViewBuilder widgetAction :: {}", widgetAction);
        return widgetAction;
    }

    private CompletableFuture<BaseWidgetNonVM> getToggleNeuPassWidget(PotentialTataPointsEarnRequest potentialTataPointsEarnRequest, String segmentName, Set<String> userSegments) {
        log.info("FitstoreCartViewBuilder Generating ToggleNeuPassWidget for potentialTataPointsEarnRequest :: {}", potentialTataPointsEarnRequest);
        if (userSegments.contains(segmentName)) {
            return CompletableFuture.supplyAsync(new Supplier<PotentialTataPointsEarnResponse>() {
                    @SneakyThrows
                    @Override
                    public PotentialTataPointsEarnResponse get() {
                        return FitstoreCartViewBuilder.this.serviceInterfaces.tata3pService.getPotentialNeuPointsToBeEarned(potentialTataPointsEarnRequest);
                    }
                }, serviceInterfaces.getTaskExecutor())
                .thenApply((potentialNeuPointsToBeEarned) -> {
                    log.info("FitstoreCartViewBuilder ToggleNeuPassWidget potentialNeuPointsToBeEarned :: {}", potentialNeuPointsToBeEarned.getPoints());
                    ToggleNeuPassWidget widget = new ToggleNeuPassWidget();
//                        Will enable it later
//                        Action knowMoreAction = new Action();
//                        knowMoreAction.setActionType(ActionType.NAVIGATION);
//                        knowMoreAction.setTitle("KNOW MORE");
//                        knowMoreAction.setUrl("curefit://webview?uri=https://www.google.com&theme=dark&title=AboutNeuPass");
//                        widget.setKnowMore(knowMoreAction);

                    widget.setTitle("Activate your ");
                    if (this.tataNeuUtil.getNEUPASS_CONSENT_PENDING().equals(segmentName)) {
                        widget.setTitle2(" NeuPass Benefits");
                    } else {
                        widget.setTitle2(" NeuPass");
                    }
                    widget.setIcon(TATA_NEU_LOGO);
                    widget.setSubTitle("Earn upto " + potentialNeuPointsToBeEarned.getPoints() + " NeuCoins\n1 NeuCoin = ₹1");
                    widget.setChecked(true);
                    widget.setAction(getWidgetAction(true));

                    WidgetStyle widgetStyle = new WidgetStyle();
                    widgetStyle.setPaddingHorizontal(0);
                    widgetStyle.setPadding(0);

                    widget.setStyle(widgetStyle);
                    log.info("FitstoreCartViewBuilder ToggleNeuPassWidget :: {}", widget);
                    return widget;
                });
        } else {
            return CompletableFuture.completedFuture(null);
        }
    }

    private CompletableFuture<BaseWidgetNonVM> getNeuPassEarnedCoinsWidget(PotentialTataPointsEarnRequest potentialTataPointsEarnRequest, String segmentName, SegmentSet<String> userSegments) {
        log.info("FitstoreCartViewBuilder Generating NeuPassEarnedCoinsWidget for potentialTataPointsEarnRequest :: {}, segmentName :: {}", potentialTataPointsEarnRequest, segmentName);
        if (userSegments.contains(segmentName)) {
            return CompletableFuture.supplyAsync(new Supplier<PotentialTataPointsEarnResponse>() {
                        @SneakyThrows
                        @Override
                        public PotentialTataPointsEarnResponse get() {
                            return FitstoreCartViewBuilder.this.serviceInterfaces.tata3pService.getPotentialNeuPointsToBeEarned(potentialTataPointsEarnRequest);
                        }
                    }, serviceInterfaces.getTaskExecutor())
                    .thenApply((potentialNeuPointsToBeEarned) -> {
                        log.info("FitstoreCartViewBuilder NeuPassEarnedCoinsWidget potentialNeuPointsToBeEarned :: {}", potentialNeuPointsToBeEarned.getPoints());
                        NeuPassEarnedCoinsWidget widget = new NeuPassEarnedCoinsWidget();
                        widget.setTitle("Earn upto " + potentialNeuPointsToBeEarned.getPoints() + " NeuCoins!");
                        widget.setSubTitle("1 neucoin= ₹1\nNeuCoins will be credited post return window.");
                        widget.setIcon(TATA_NEU_LOGO);
                        WidgetStyle widgetStyle = new WidgetStyle();
                        widgetStyle.setPaddingHorizontal(0);
                        widgetStyle.setPaddingVertical(0);
                        widgetStyle.setPadding(0);
                        widgetStyle.setMarginHorizontal(20);
                        widget.setStyle(widgetStyle);
                        Action infoAction = new Action();
                        infoAction.setActionType(ActionType.SHOW_ALERT_MODAL);
                        infoAction.setIconUrl("image/tata/Info.png");
                        HashMap<String, Object> meta = new HashMap<>();
                        meta.put("title", "Info");
                        meta.put("subTitle", "0.25% NeuCoins will be awarded on the final payable amount post discounts");
                        meta.put("type", "Info");
                        List<Action> actions = new ArrayList<>();
                        Action hideAction = new Action();
                        hideAction.setActionType(ActionType.HIDE_ALERT_MODAL);
                        hideAction.setTitle("OKAY");
                        actions.add(hideAction);
                        meta.put("actions", actions);
                        infoAction.setMeta(meta);
                        widget.setInfoAction(infoAction);
                        log.info("FitstoreCartViewBuilder NeuPassEarnedCoinsWidget :: {}", widget);
                        return widget;
                    });
        } else {
            return CompletableFuture.completedFuture(null);
        }
    }

}
