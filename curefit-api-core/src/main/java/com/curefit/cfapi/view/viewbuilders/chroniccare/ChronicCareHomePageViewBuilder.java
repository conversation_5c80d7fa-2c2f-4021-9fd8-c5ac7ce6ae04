package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.albus.booking.ActiveConsultationResponse;
import com.curefit.albus.common.AppointmentActionsWithContext;
import com.curefit.albus.common.BundleProduct;
import com.curefit.albus.common.PatientPreferredAgentResponse;
import com.curefit.albus.response.*;
import com.curefit.albus.response.actions.UserPreferencePojo;
import com.curefit.albus.response.chronic.UserOnboardingActionWithContext;
import com.curefit.albus.response.contexts.chronic.CGMDeviceInfo;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.enums.Tenant;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.dto.ChronicCareTeam;
import com.curefit.cfapi.model.internal.chroniccare.FitnessDeviceSyncMeta;
import com.curefit.cfapi.model.internal.chroniccare.SfSalesBannerUrl;
import com.curefit.cfapi.model.internal.chroniccare.sfbadges.SfBadgeEntry;
import com.curefit.cfapi.model.internal.chroniccare.sfbadges.SfUserBadgeSummary;
import com.curefit.cfapi.model.internal.meta.LiveCalenderEvenetCancelPayload;
import com.curefit.cfapi.model.internal.meta.TeleconsultationManageOptionsMeta;
import com.curefit.cfapi.model.internal.meta.VideoSubscribeUnSubscribeActionMeta;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionIcon;
import com.curefit.cfapi.pojo.app.action.ActionId;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.app.action.NavigationType;
import com.curefit.cfapi.pojo.chroniccare.CareKitTrackerTimeline;
import com.curefit.cfapi.pojo.chroniccare.SfReferralBannerItem;
import com.curefit.cfapi.pojo.chroniccare.WellnessAtGlanceItem;
import com.curefit.cfapi.pojo.chroniccare.WellnessDataPerDay;
import com.curefit.cfapi.pojo.chroniccare.nux.DiagnosticActionMeta;
import com.curefit.cfapi.pojo.chroniccare.nux.ToastMessageMeta;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.service.DeviceService;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.util.LiveUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.RenewalAlertWidgetBuilder;
import com.curefit.cfapi.view.viewmodels.chroniccare.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.ActiveCardStatus.StatusType;
import com.curefit.cfapi.view.viewmodels.chroniccare.ChronicCareHomePageView.BookingModalConfig;
import com.curefit.cfapi.view.viewmodels.chroniccare.ChronicCareHomePageView.RagusMigrationDisclaimer;
import com.curefit.cfapi.view.viewmodels.chroniccare.ChronicCareHomePageView.SFAgentDetail;
import com.curefit.cfapi.view.viewmodels.chroniccare.ChronicCareHomePageView.SFCareTeam;
import com.curefit.cfapi.view.viewmodels.chroniccare.onboarding.CardData;
import com.curefit.cfapi.view.viewmodels.chroniccare.onboarding.WhatNextCardData;
import com.curefit.cfapi.view.viewmodels.transform.HabitCard;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.*;
import com.curefit.cfapi.widgets.chroniccare.challenges.SfChallengesGroupWidget;
import com.curefit.cfapi.widgets.chroniccare.poll.SfPollsGroupWidget;
import com.curefit.cfapi.widgets.chroniccare.prepurchase.PrePurchaseDiabetesTypeWidget;
import com.curefit.cfapi.widgets.chroniccare.prepurchase.UfPrePurchaseWidget;
import com.curefit.cfapi.widgets.common.NowLiveWidgetView;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.digital.NowLiveSessionWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.common.data.model.entity.Gender;
import com.curefit.common.util.DateTimeUtil;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.diyfs.client.spi.DiyfsService;
import com.curefit.diyfs.pojo.DIYFilterRequestV2;
import com.curefit.diyfs.pojo.LiveClass;
import com.curefit.diyfs.pojo.enums.PreferredStreamType;
import com.curefit.diyfs.pojo.enums.SubscriptionStatus;
import com.curefit.ehr.client.EHRClient;
import com.curefit.ehr.enums.PatientActivityType;
import com.curefit.math.enums.DataType;
import com.curefit.metricservice.pojo.AggregateUserMetricValue;
import com.curefit.ollivander.common.constant.AgentType;
import com.curefit.ollivander.common.exception.OllivanderClientException;
import com.curefit.ollivander.common.pojo.response.agent.AgentResponse;
import com.curefit.ollivander.common.pojo.response.center.CenterBaseResponse;
import com.curefit.ollivander.common.pojo.response.center.CenterResponseV2;
import com.curefit.pojo.PatientActivityLoggingResponse;
import com.curefit.product.enums.ProductType;
import com.curefit.product.models.Product;
import com.curefit.product.models.diy.DIYProduct;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.pojo.UserAttributeEntry;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.sfalbus.response.DeviceNfcLocationInfo;
import com.curefit.shifu.enums.HabitType;
import com.curefit.shifu.pojo.UserActivityEntry;
import com.curefit.shifu.pojo.goal.summary.GoalWithData;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.ufs.services.UfsService;
import com.curefit.userservice.pojo.entry.DeviceDetailEntry;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sugarfit.ambrosia.client.AmbrosiaClient;
import com.sugarfit.challenges.client.ChallengesClient;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.enums.BadgeCategory;
import com.sugarfit.chs.enums.GranularityType;
import com.sugarfit.chs.enums.ReadingMode;
import com.sugarfit.chs.enums.UserBadgeProgressStatus;
import com.sugarfit.chs.pojo.ActiveDeviceEntry;
import com.sugarfit.chs.pojo.CgmOnboardingStatusResponse;
import com.sugarfit.chs.pojo.CgmUserRequestStatus;
import com.sugarfit.chs.pojo.ReadingModeResponse;
import com.sugarfit.chs.pojo.badge.BadgeEntry;
import com.sugarfit.chs.pojo.badge.UserBadgeProgressEntry;
import com.sugarfit.chs.pojo.badge.UserBadgeSummary;
import com.sugarfit.chs.pojo.cgminsights.BgInsightsSummary;
import com.sugarfit.chs.pojo.cgminsights.BgInsightsSummaryRequest;
import com.sugarfit.chs.pojo.cgmstat.CgmStat;
import com.sugarfit.chs.pojo.cgmstat.DailySugarStat;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalLogsResponse;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalScansForDayResponse;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalsRequest;
import com.sugarfit.chs.pojo.fitnessData.CommonFitnessDataResponse;
import com.sugarfit.chs.pojo.fitnessData.FitnessDataRequest;
import com.sugarfit.chs.pojo.fitnessData.HeartRateDataPoint;
import com.sugarfit.chs.pojo.fitnessData.HeartRateDataResponse;
import com.sugarfit.chs.pojo.fitnessData.LastSyncedDataResponse;
import com.sugarfit.experiment.client.ExperimentClient;
import com.sugarfit.experiment.enums.ExperimentFilterType;
import com.sugarfit.experiment.pojo.UserAssignedExperimentFilterRequest;
import com.sugarfit.experiment.pojo.UserAssignedExperimentResponse;
import com.sugarfit.fitness.client.SFFitnessClient;
import com.sugarfit.housemd.HousemdClient;
import com.sugarfit.housemd.enums.OrderAction;
import com.sugarfit.housemd.pojo.order.OrderFilterRequest;
import com.sugarfit.housemd.pojo.order.OrderFilterResponse;
import com.sugarfit.housemd.pojo.order.OrderResponse;
import com.sugarfit.housemd.pojo.order.OrderStatus;
import com.sugarfit.lms.entry.ReferralClient;
import com.sugarfit.lms.referral.entry.ReferralConfigEntry;
import com.sugarfit.logging.client.LoggingClient;
import com.sugarfit.nest.client.MasterClassClient;
import com.sugarfit.poll.client.PollSupportClient;
import com.sugarfit.poll.enums.PollType;
import com.sugarfit.poll.pojo.PollEntry;
import com.sugarfit.sms.client.SMSClient;
import com.sugarfit.sms.response.NUXStatusResponse;
import com.sugarfit.sms.response.RMResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.curefit.albus.response.CategoryCode.DIAGNOSTICS;
import static com.curefit.cfapi.util.AppUtil.getAppTenantFromUserContext;
import static com.curefit.cfapi.util.AppUtil.isSugarFitApp;
import static com.curefit.cfapi.util.AppUtil.isUltraFitApp;
import static com.curefit.cfapi.util.ChronicCareAppUtil.*;
import static com.curefit.cfapi.util.SfBadgeUtils.getBadgeCategoryDisplayName;
import static com.curefit.cfapi.util.SfBadgeUtils.getBadgeProgressTitle;
import static com.curefit.cfapi.util.SfBadgeUtils.getLogActionFromBadgeCategory;
import static com.curefit.cfapi.util.SfBadgeUtils.getProgressColor;
import static com.curefit.cfapi.util.SfHomePageUtil.getAgentDatePickerUrl;
import static com.curefit.cfapi.util.SfHomePageUtil.mapUserInterventionActivity;
import static com.curefit.cfapi.view.viewbuilders.chroniccare.VitalPageViewBuilder.HEART_RATE_DIFF_ALLOWED;
import static com.curefit.cfapi.view.viewmodels.chroniccare.ActiveCardStatus.StatusType.*;
import static java.util.concurrent.CompletableFuture.supplyAsync;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class ChronicCareHomePageViewBuilder {
    public static final String DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT = "Your best match doctor will be assigned based on your report.";
    public static final String DOCTOR_ASSIGNMENT_PLACEHOLDER_URL = "/image/chroniccare/doctor_male_v2.png";
    public static final String SF_CARE_TEAM_DOCTOR_ASSIGNMENT_PLACEHOLDER_URL = "/image/chroniccare/sf_care_team_doctor_placeholder.png";

    public static final Long METRIC_ID = 1679L;
    public static final long MILLIS_IN_A_DAY = 86400000L;
    private static final String TENANT = "SUGARFIT";
    private static final int GLUCOSE_LOWER_LIMIT = 80;
    private static final int GLUCOSE_HIGHER_LIMIT = 140;
    private static final DiabetesType GESTATIONAL_DIABETES_PACK = new DiabetesType("Gestational Diabetes",
            "SUGARFIT_GD_PACK");
    private static final DiabetesType TYPE_2_DIABETES_PACK = new DiabetesType("Type 2 Diabetes", "SUGARFIT_T2D_ANNUAL");
    private static final DiabetesType PRE_DIABETIC_PACK = new DiabetesType("Pre Diabetes", "SUGARFIT_PD_PACK");
    private static final DiabetesType TRIAL_PACK = new DiabetesType("Trial Pack", "SUGARFIT_TRIAL");
    public static final String CARE_TEAM_CTA_MESSAGE = "BOOK";
    public static final String SF_COACH_WA_NUMBER = "918792514509";
    public static final String UF_COACH_WA_NUMBER = "919885022351";
    public static final String SUGARFIT_WELCOME_KIT_IMAGE = "/image/chroniccare/care_kit.png";
    public static final String SUGARFIT_RENEWAL_WELCOME_KIT_IMAGE = "/image/chroniccare/renewal_kit_sf.png";
    public static final String ULTRAFIT_WELCOME_KIT_IMAGE = "/image/chroniccare/care_kit_uf.png";
    private static final String CGM_ACTIVATION_DUMMY_DEVICE_ID = "cgm-activation-id";
    private final int SIX_HOURS_IN_SECONDS = 60 * 60 * 6;
    private static final int INTERVENTION_WIDGET_DATE_WINDOW = 3;
    private static final int WELLNESS_WIDGET_DATE_WINDOW = 3;
    private static final Tenant DIYFS_TENANT = Tenant.SUGARFIT_APP;
    private static final Map<String, Object> INTERVENTION_CARD_RENDERING_INFO = Collections
            .unmodifiableMap(new HashMap<>() {
                {
                    put("userResponse", Arrays.asList(
                            new HashMap<>() {
                                {
                                    put("key", "NOT_DONE");
                                    put("display", "No");
                                }
                            },
                            new HashMap<>() {
                                {
                                    put("key", "ALMOST_DONE");
                                    put("display", "Getting There");
                                }
                            },
                            new HashMap<>() {
                                {
                                    put("key", "DONE");
                                    put("display", "Done");
                                }
                            }));
                }
            });
    private static final Double EMPTY_INTERVENTION_DATE_MIN_VERSION = 1.21d;
    private static final String PAYMENT_PENDING_USER_SEGMENT = "sugarfit_payment_pending_blocked_users";
    private static final String UF_REFERRAL_BANNER_USER_SEGMENT_NAME = "ultrafit_referral_banner_enabled_users";
    public static final String REFERRAL_BOTTOM_BANNER_USER_SEGMENT_NAME = "sugarfit_referral_bottom_banner_enabled_users";
    public static final String REFERRAL_TOP_BANNER_USER_SEGMENT_NAME = "sugarfit_referral_top_banner_enabled_users";
    private static final String BG_ANIMATION_DISABLED_SEGMENT = "background-animation-disabled-users";
    public static final String CULT_MEMBERS_SEGMENT = "cult_members";
    public static final String CULTLIVE_MEMBERS_SEGMENT = "cultlive_members";
    public static final String GYMFIT_MEMBERS_SEGMENT = "Gym.fit Members";
    public static final Double MIN_METABOLIC_SCORE_FOR_CGM_INFO = 70.0;

    final ChronicCarePatientService chronicCarePatientService;
    final EHRClient ehrClient;
    final UserOnboardingService userOnboardingService;
    final CHSClient chsClient;
    final AmbrosiaClient ambrosiaClient;
    final ExperimentClient experimentClient;
    final SFFitnessClient sfFitnessClient;
    final ReferralClient referralClient;
    final SMSClient smsClient;
    final LoggingClient sfLoggingClient;
    final PollSupportClient pollSupportClient;
    final HousemdClient housemdClient;
    final MasterClassClient masterClassClient;

    final ChallengesClient challengesClient;

    @Qualifier("cfApiRedisKeyValueStore")
    final KeyValueStore cfApiRedisKeyValueStore;

    final ServiceInterfaces serviceInterfaces;

    final DeviceService deviceService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final CongratsPageViewBuilder congratsPageViewBuilder;
    final SugarJournalReadingsPageViewBuilder sugarJournalReadingsPageViewBuilder;
    final SugarFitBlogWidgetBuilder sugarFitBlogWidgetBuilder;
    final ExceptionReportingService exceptionReportingService;
    final DiyfsService diyfsService;
    final UfsService ufsService;
    final UserAttributesClient userAttributesClient;
    final SegmentationCacheClient segmentationCacheClient;
    final RenewalAlertWidgetBuilder renewalAlertWidgetBuilder;
    final SubscriptionPageViewBuilder subscriptionPageViewBuilder;
    final AppConfigCache appConfigCache;

    private static final String CARE_TEAM_TITLE = "My Care Team";
    private static final String CARE_KIT_STATUS_DELIVERED = "Delivered";
    public static final Set<String> MISSED_CONSULTATION_STATES = Set.of("MISSED", "CUSTOMER_MISSED", "AGENT_MISSED",
            "DOCTOR_UNAVAILABLE");
    public static final Set<String> UPCOMING_CONSULTATION_STATES = Set.of("BOOKED", "SCHEDULED", "RESCHEDULED",
            "ONBOARDING_USERS");
    public static final Set<String> COMPLETED_CONSULTATION_STATES = Set.of("COMPLETED", "CONSULTATION_COMPLETED",
            "PRESCRIPTION_GENERATED");
    public static final Set<String> STARTED_CONSULTATION_STATES = Set.of("PRESCRIPTION_IN_DRAFT",
            "CONSULTATION_STARTED");
    public static final Set<String> CANCELLED_CONSULTATION_STATES = Set.of("CANCELLED");
    public static final ArrayList<String> WELLNESS_GOALS = new ArrayList<>(
            Arrays.asList("STEPS", "CALORIES-EATEN", "CALORIES-BURNED", "WEIGHT", "SLEEP", "FASTING-GLUCOSE"));
    public static final ArrayList<String> WELLNESS_GOALS_UF = new ArrayList<>(
            Arrays.asList("STEPS", "CALORIES-EATEN", "CALORIES-BURNED", "WEIGHT", "SLEEP"));
    public static final long HIGH_PRIORITY_WINDOW_SIZE = 2 * 24 * 3600L * 1000;
    public static final long DIAGNOSTIC_REPORT_WINDOW_SIZE = 2 * 24 * 3600L * 1000;
    public static final long CGM_WIDGET_WINDOW_SIZE = 7 * 24 * 3600L * 1000;
    public static final long MISSED_ITEMS_WINDOW_SIZE = 24 * 3600L * 1000;
    public static final int DIAGNOSTIC_SCHEDULED_EXPIRY_DELAY = 7;
    public static final String CGM_CARD_DEFAULT_ACTION_TITLE = "Will be delivered within 5-7 days";
    public static final String CGM_CARD_DEFAULT_CARD_MESSAGE = "Your CGM will be delivered within 5-7 days";
    private static final long WHATS_NEXT_RELEASE_DATE = 1646048310305L;
    private static int TIMEZONE_OFFSET = 330 * 60 * 1000; // 5 hour 30min

    public ChronicCareHomePageView buildView(UserContext userContext, ServiceInterfaces serviceInterfaces,
            String cgmDeviceId) throws Exception {
        ChronicCareHomePageView result = new ChronicCareHomePageView(userContext);
        UserEntry user = userContext.getUserEntryCompletableFuture().join();
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        try {
            result.setUserDetails(this.getUserDetails(user));
            SfAppUpdateConfig config = chronicCareServiceHelper.getSfAppUpdateConfig();
            if (chronicCareServiceHelper.isAppUnderMaintenance()) {
                result.addWidget(chronicCareServiceHelper.getMaintenanceBanner(false));
                return result;
            }
            if (shouldShowAppUpdatePopUp(userContext, config)) {
                result.setAppUpdateData(getAppUpdateData(userContext, config));
            }
            NUXStatusResponse response = smsClient.getNUXStatus(Long.valueOf(userContext.getUserProfile().getUserId()), false, timeZone);
            if (response != null && response.getFreemium() && isFreemiumSupportedApp(userContext)) {
                if (!response.getNuxCompleted()) {
                    result.addWidget(new AutoNavigationToNuxWidget());
                    return result;
                } else {
                    // to go sffreemiumhomepage
                    AutoNavigationToNuxWidget widget = new AutoNavigationToNuxWidget();
                    widget.setPageName("sffreemiumhomepage");
                    result.addWidget(widget);
                    return result;
                }
            }

            Optional<ActivePackResponse> activePackResponse = userOnboardingService
                    .getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());
            CompletableFuture<PatientDetail> patientDetailFuture = chronicCarePatientService
                    .getChronicCarePatientForUserIdFuture(userContext);
            PatientDetail patientDetail = patientDetailFuture.get(5, TimeUnit.SECONDS);
            if (isPaymentPendingForUser(userContext)) {
                addDisabledUserPageWidgets(result, user, userContext);
                return result;
            }
            if (activePackResponse.isEmpty()) {
                Optional<ActivePackResponse> expiredPackResponse = userOnboardingService
                        .getSugarFitExpiredPackForHome(userContext.getUserProfile().getUserId());
                Optional<ActivePackResponse> upcomingPackResponse = userOnboardingService
                        .getSugarFitUpcomingPackForHome(userContext.getUserProfile().getUserId());
                // expired pack should not be a trial pack.
                if (expiredPackResponse.isPresent()
                        && (!expiredPackResponse.get().getBundleProduct().getIsTrialProduct())) {
                    // case when pack has expired.
                    addPackExpiredPageWidgets(userContext, result, user, expiredPackResponse.get(), patientDetail,
                            cgmDeviceId);
                } else if (upcomingPackResponse.isPresent()) {
                    // case when pack is purchased, but it has not yet started
                    addPackUpcomingPageWidgets(userContext, result, user, upcomingPackResponse.get(), patientDetail);
                } else {
                    log.info(String.format("No active/expired/upcoming pack, for user :: %s",
                            userContext.getUserProfile().getUserId()));
                    result.addWidget(getPrePurchaseWidget(userContext));
                }
            } else {
                CompletableFuture<UserOnboardingActionWithContext> onboardingActionFuture = ChronicCareAppUtil.getUserOnboardingActionsFuture(
                        serviceInterfaces,
                        userContext,
                        activePackResponse,
                        userOnboardingService,
                        exceptionReportingService
                        );
                UserOnboardingActionWithContext onboardingActions = onboardingActionFuture.get(5, TimeUnit.SECONDS);
                if (null == onboardingActions) {
                    throw new ResourceNotFoundException(String.format("Empty onboarding actions for user :: %s",
                            userContext.getUserProfile().getUserId()));
                }

                if (onboardingActions.isTrialPack()) {
                    // in this case show trial pack homepage
                    addTrialPackHomePageWidgets(userContext, result);
                } else if (onboardingActions.getAssessmentFormActionWithContext().getAction().isActionPermitted()) {
                    // case when assessment form has be be displayed
                    result.addWidget(getPackPurchaseGreetingWidget(userContext));
                } else if (onboardingActions.getDoctorSelectionActionWithContext().getAction().isActionPermitted()) {
                    // case when doctor selection widget has be to displayed
                    result.addWidget(congratsPageViewBuilder.getPreferenceFormCongratsWidget());
                } else {
                    // case when onboarding process is ongoing
                    addOnboardingPageWidgets(userContext, cgmDeviceId, result, activePackResponse.get(),
                            onboardingActions, patientDetail, activePackResponse.get().getBundleProduct());
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Exception in homepage", e);
            throw e;
        }

        result.setNavAction(getNavAction("navBarAction", "curefit://hamburgermenu", true, ActionType.NAVIGATION));
        result.setTtl(300000L);
        result.setFitnessttl(1800000L); // milliseconds of 30 minutes
        result.setShowBackgroundAnimation(!isBackgroundAnimationDisabled(userContext.getUserProfile().getUserId()));
        return result;
    }

    @GetMapping
    private Map<String, Object> getBlockedUserBannerLayoutProps() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", true);
        layoutProps.put("bannerHeight", 320);
        layoutProps.put("bannerWidth", 315);
        layoutProps.put("bannerOriginalHeight", 320);
        layoutProps.put("bannerOriginalWidth", 315);
        layoutProps.put("verticalPadding", 30);
        return layoutProps;
    }

    @GetMapping
    private Map<String, Object> getSalesBannerLayoutProps() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", true);
        layoutProps.put("bannerHeight", 180);
        layoutProps.put("bannerWidth", 315);
        layoutProps.put("bannerOriginalHeight", 180);
        layoutProps.put("bannerOriginalWidth", 315);
        layoutProps.put("verticalPadding", 30);
        layoutProps.put("showPagination", true);
        layoutProps.put("autoScroll", false);
        return layoutProps;
    }

    @GetMapping
    private Map<String, Object> getUfReferralBannerLayoutProps() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", false);
        layoutProps.put("bannerHeight", 130);
        layoutProps.put("bannerWidth", 360);
        layoutProps.put("bannerOriginalHeight", 130);
        layoutProps.put("bannerOriginalWidth", 360);
        layoutProps.put("verticalPadding", 30);
        layoutProps.put("edgeToEdge", true);
        layoutProps.put("showPagination", false);
        return layoutProps;
    }

    @GetMapping
    private Map<String, Object> getFBVBannerLayoutProps() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", false);
        layoutProps.put("bannerHeight", 102);
        layoutProps.put("bannerWidth", 315);
        layoutProps.put("bannerOriginalHeight", 102);
        layoutProps.put("bannerOriginalWidth", 315);
        layoutProps.put("verticalPadding", 20);
        layoutProps.put("showPagination", false);
        return layoutProps;
    }

    @GetMapping
    private Map<String, Object> getDiscordBannerLayoutProps() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", true);
        layoutProps.put("bannerHeight", 140);
        layoutProps.put("bannerWidth", 315);
        layoutProps.put("bannerOriginalHeight", 140);
        layoutProps.put("bannerOriginalWidth", 315);
        layoutProps.put("verticalPadding", 30);
        return layoutProps;
    }

    public static ChronicCareHomePageView.AppUpdateData getAppUpdateData(UserContext userContext,
            SfAppUpdateConfig config) {
        ChronicCareHomePageView.AppUpdateData appUpdateData = new ChronicCareHomePageView.AppUpdateData();
        SfAppUpdateConfig.AppTenantConfig tenantConfig = AppUtil.isSugarFitApp(userContext) ? config.getSugarfitConfig() : config.getUltrafitConfig();
        appUpdateData.setShowPopUp(true);
        appUpdateData.setIsForceUpdate(tenantConfig.isForced());
        appUpdateData.setSkipIntervalInDays(tenantConfig.getSkipIntervalInDays());

        String releaseNotes = ChronicCareAppUtil.getAppUpdateReleaseNotes(userContext, config);
        if (StringUtils.hasText(releaseNotes)) {
            appUpdateData.setReleaseNotes(releaseNotes);
        } else {
            appUpdateData.setReleaseNotes("• Performance improvements" +
                    "\n• Bug fixes");
        }
        return appUpdateData;
    }

    private AnnualPackPurchaseWidget getUltraFitAppPopUpWidget(UserContext userContext) {
        AnnualPackPurchaseWidget widget = new AnnualPackPurchaseWidget();
        widget.setHeadingText("NEW APP AVAILABLE");
        widget.setTitle("ultra.fit app is out now!");
        widget.setSubTitle(
                "To make your experience better we have built a separate ultra.fit app. You will no longer be able to continue on this app for your current subscription. Please follow these steps to install the new app!\n• Goto playstore/appstore by tapping the button below\n• Install the ultra.fit app\n• Login with your credentials");
        widget.setNextAction(Action.builder().url("https://onelink.to/vhuj9t").title("Install Now")
                .actionType(ActionType.EXTERNAL_DEEP_LINK).build());
        return widget;
    }

    private ChronicCareHomePageView.AppAnnouncementData getAppAnnouncementData(UserContext userContext,
            CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
            ActivePackResponse activePackResponse) {
        return null;
    }

    private SfBannerCarouselWidget getDisabledUserBannerWidget() {
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();
        BannerItem bannerItem = new BannerItem();
        bannerItem.setImage("image/chroniccare/disabled_user_banner_1.jpg");

        Action callAction = new Action();
        callAction.setActionType(ActionType.PHONE_CALL_NAVIGATION);
        callAction.setTitle("CALL");
        PhleboCallingMeta meta = new PhleboCallingMeta();
        meta.setPhoneNumber("9770864884");
        callAction.setMeta(meta);
        bannerItem.setAction(callAction);

        bannerItemList.add(bannerItem);
        sfBannerCarouselWidget.setData(bannerItemList);
        sfBannerCarouselWidget.setLayoutProps(getBlockedUserBannerLayoutProps());

        return sfBannerCarouselWidget;
    }

    private boolean hasFreeDiagnosticsLeft(ActivePackResponse activePackResponse) {
        AtomicBoolean isLeft = new AtomicBoolean(false);
        if (activePackResponse != null && !CollectionUtils.isEmpty(activePackResponse.getUserMembershipInfos())) {
            activePackResponse.getUserMembershipInfos().forEach(userMembershipInfo -> {
                if (userMembershipInfo != null && userMembershipInfo.getCategoryCode() == DIAGNOSTICS
                        && userMembershipInfo.getTickets() > userMembershipInfo.getTicketsConsumed()) {
                    isLeft.set(true);
                }
            });
        }
        return isLeft.get();
    }

    private SfBannerCarouselWidget getUfReferralBannerWidget(UserContext userContext) {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();

        try {
            ReferralConfigEntry referralConfigEntry = referralClient.getReferralConfig(appTenant);

            if (isUfReferralBannerEnabledForUser(userContext)) {
                Action referralAction = new Action();
                referralAction.setUrl("curefit://sfreferralpage");
                referralAction.setActionType(ActionType.NAVIGATION);
                BannerItem referralBannerItem = new BannerItem();
                referralBannerItem.setImage(referralConfigEntry.getHomepageImageUrl());
                referralBannerItem.setAction(referralAction);
                bannerItemList.add(referralBannerItem);
            }

            sfBannerCarouselWidget.setData(bannerItemList);
            sfBannerCarouselWidget.setLayoutProps(getUfReferralBannerLayoutProps());
        } catch (Exception e) {
            log.error("Exception in getting referral config", e);
            exceptionReportingService.reportException("Exception in getting referral config", e);
            return null;
        }

        if (CollectionUtils.isEmpty(bannerItemList))
            return null;

        return sfBannerCarouselWidget;
    }

    private SfBannerCarouselWidget getFbvPreCGMBannerWidget() {
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();
        BannerItem referralBannerItem = new BannerItem();
        referralBannerItem.setImage("image/chroniccare/face_based_vitals_pre_cgm_banner.png");
        bannerItemList.add(referralBannerItem);
        sfBannerCarouselWidget.setData(bannerItemList);
        sfBannerCarouselWidget.setLayoutProps(getFBVBannerLayoutProps());

        return sfBannerCarouselWidget;
    }

    private SfBannerCarouselWidget getCGMDeliveryDelayBannerWidget(UserContext userContext) {
        if (ChronicCareAppUtil.isCGMDeliveryDelayUser(userContext)) {
            SfBannerCarouselWidget bannerCarouselWidget = new SfBannerCarouselWidget();
            BannerItem bannerItem = new BannerItem();
            bannerItem.setImage("image/chroniccare/marketing/banners/home/<USER>/cgm_delay.png");
            bannerCarouselWidget.setData(List.of(bannerItem));
            bannerCarouselWidget.setLayoutProps(getSalesBannerLayoutProps());
            return bannerCarouselWidget;
        }
        return null;
    }

    private SfBannerCarouselWidget getSalesBannerWidget(UserContext userContext,
                                                        UserOnboardingActionWithContext onboardingAction,
                                                        CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                        BundleProduct bundleProduct,
                                                        boolean userOrderedAtleastOnceFromEcommerce) {
//        try {
//            if ((AppUtil.isSugarFitApp(userContext)
//                    && isSalesBannerCarouselEnabled(userContext, segmentationCacheClient))
//                    || userOrderedAtleastOnceFromEcommerce) {
//
//                HashMap<String, SfSalesBannerUrl> salesBannerConfig = appConfigCache.getConfig("SF_SALES_BANNER_CONFIG",
//                        new TypeReference<>() {
//                        }, new HashMap<>());
//                if (salesBannerConfig != null) {
//                    SfSalesBannerUrl homeBanners = salesBannerConfig.get("home");
//
//                    if (homeBanners == null)
//                        return null;
//
//                    SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
//                    List<BannerItem> bannerItemList = new ArrayList<>();
//
//                    if (onboardingAction == null || (onboardingAction.getPackRenewalContextActionWithContext().getAction()
//                            .isActionPermitted()
//                            && !onboardingAction.getPackRenewalContextActionWithContext().getContext().getRenewed())) {
//                        if (isRenewalOfferRunning()) {
//                            String renewalBannerUrl = homeBanners.getMedium().get("renewal");
//                            Action renewalPageAction = Action.builder().url("curefit://renewsubscription")
//                                    .actionType(ActionType.NAVIGATION).title("RENEW SUBSCRIPTION").build();
//                            BannerItem renewalBannerItem = new BannerItem();
//                            renewalBannerItem.setImage(renewalBannerUrl);
//                            renewalBannerItem.setAction(renewalPageAction);
//                            bannerItemList.add(renewalBannerItem);
//                        }
//                    } else {
//                        if (ChronicCareAppUtil.isDiaconUser(userContext, segmentationCacheClient)) {
//                            String diaconBannerImageUrl = homeBanners.getMedium().get("diacon");
//                            if (diaconBannerImageUrl != null) {
//                                BannerItem diaconBanner = new BannerItem();
//                                diaconBanner.setImage(diaconBannerImageUrl);
//                                bannerItemList.add(diaconBanner);
//                            }
//                        } else {
//
//                            if (isSalesCGMBannerEnabled(userContext, segmentationCacheClient, cgmOnboardingStatusResponse,
//                                    bundleProduct)) {
//                                String cgmMediumBannerUrl = ChronicCareAppUtil.isBoosterCGMPackEnabledUser(userContext,
//                                        segmentationCacheClient)
//                                        ? "/image/chroniccare/marketing/banners/home/<USER>/sales_cgm_booster_pack_v1.png"
//                                        : ((SfSalesBannerUrl) homeBanners).getMedium().get("cgm");
//
//                                Action cgmRequestAction = new Action();
//                                cgmRequestAction.setActionType(ActionType.SF_OPS_REQUEST);
//                                cgmRequestAction.setTitle("RAISE A REQUEST");
//                                cgmRequestAction.setMeta(new SfOpsRequestMeta(REQUEST_NEW_CGM));
//                                Action showSalesBannerAction1 = Action.builder().title("BUY NEW CGM")
//                                        .url(ChronicCareServiceHelper.getCgmStorePageLink(userContext, segmentationCacheClient)).actionType(ActionType.NAVIGATION).build();
//                                BannerItem cgmBannerItem = new BannerItem();
//                                cgmBannerItem.setImage(cgmMediumBannerUrl);
//                                cgmBannerItem.setAction(showSalesBannerAction1);
//                                bannerItemList.add(cgmBannerItem);
//                            }
//
//                            if (isReferralBottomBannerEnabledForUser(userContext.getUserProfile().getUserId())) {
//                                String referralMediumBannerUrl = ((SfSalesBannerUrl) homeBanners).getMedium()
//                                        .get("referral");
//                                String referralMediumBannerUrl2k = ((SfSalesBannerUrl) homeBanners).getMedium()
//                                        .get("referral_2k");
//                                Action referralAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfreferralpage").build();
//                                BannerItem referralBannerItem = new BannerItem();
//                                referralBannerItem.setImage(
//                                        ChronicCareAppUtil.isSugarfitPlusUser(userContext, segmentationCacheClient)
//                                                ? referralMediumBannerUrl2k
//                                                : referralMediumBannerUrl);
//                                referralBannerItem.setAction(referralAction);
//                                bannerItemList.add(referralBannerItem);
//                            }
//
//                            String wellnessBannerUrl = homeBanners.getMedium()
//                                    .get("wellness");
//                            String wellnessDeeplink = homeBanners.getMedium()
//                                    .getOrDefault("wellnessDeeplink", "curefit://chroniccarelive?pageId=chroniccarelive");
//                            if (wellnessBannerUrl != null && !wellnessBannerUrl.isEmpty()) {
//                                Action wellnessPageAction = Action.builder().actionType(ActionType.NAVIGATION).url(wellnessDeeplink).build();
//                                BannerItem wellnessBannerItem = new BannerItem();
//                                wellnessBannerItem.setImage(wellnessBannerUrl);
//                                wellnessBannerItem.setAction(wellnessPageAction);
//                                bannerItemList.add(wellnessBannerItem);
//                            }
//
//                            if (isSalesDiagnosticBannerEnabled(userContext, segmentationCacheClient)) {
//                                String diagnosticsLargeBannerUrl = ((SfSalesBannerUrl) homeBanners).getLarge()
//                                        .get("diagnostics");
//                                String diagnosticsMediumBannerUrl = ((SfSalesBannerUrl) homeBanners).getMedium()
//                                        .get("diagnostics");
//
//                                Action diagnosticRequestAction = new Action();
//                                diagnosticRequestAction.setActionType(ActionType.SF_OPS_REQUEST);
//                                diagnosticRequestAction.setTitle("RAISE A REQUEST");
//                                diagnosticRequestAction.setMeta(new SfOpsRequestMeta(REQUEST_DIAGNOSTICS));
//                                SfSalesBannerMeta sfSalesBannerMeta2 = new SfSalesBannerMeta(diagnosticsLargeBannerUrl,
//                                        "Please raise a request and we will reach out to you within 24 hours for next steps.",
//                                        diagnosticRequestAction);
//                                Action showSalesBannerAction2 = new Action();
//                                showSalesBannerAction2.setActionType(ActionType.SHOW_SF_SALES_BANNER_MODAL);
//                                showSalesBannerAction2.setMeta(sfSalesBannerMeta2);
//
//                                BannerItem diagnosticBannerItem = new BannerItem();
//                                diagnosticBannerItem.setImage(diagnosticsMediumBannerUrl);
//                                diagnosticBannerItem.setAction(showSalesBannerAction2);
//                                bannerItemList.add(diagnosticBannerItem);
//                            }
//                        }
//                    }
//
//                    if (isECommerceSupported(userContext, segmentationCacheClient) && userOrderedAtleastOnceFromEcommerce) {
//                        SfBannerCarouselWidget eComBannerWidget = chronicCareServiceHelper.getEComBannerWidget(userContext, "ecommerceSales", false);
//                        if (CollectionUtils.isNotEmpty(eComBannerWidget.getData())) {
//                            bannerItemList.add(0, eComBannerWidget.getData().get(0));
//                        }
//                    }
//
//                    if (CollectionUtils.isEmpty(bannerItemList))
//                        return null;
//
//                    sfBannerCarouselWidget.setData(bannerItemList);
//                    sfBannerCarouselWidget.setLayoutProps(getSalesBannerLayoutProps());
//                    return sfBannerCarouselWidget;
//                }
//            }
//        } catch (Exception e) {
//            exceptionReportingService.reportException("Exception in getSalesBannerWidget", e);
//        }
        return null;
    }

    private void addPackExpiredPageWidgets(UserContext userContext, ChronicCareHomePageView result, UserEntry user,
            ActivePackResponse expiredPackResponse,
            PatientDetail patientDetail, String cgmDeviceId)
            throws InterruptedException, ExecutionException, ResourceNotFoundException, HttpException {
        Long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        String timezone = userContext.getUserProfile().getTimezone() != null
                ? userContext.getUserProfile().getTimezone()
                : "Asia/Kolkata";
        CgmStat cgmStat = new CgmStat();
        CompletableFuture<UserOnboardingActionWithContext> onboardingActionFuture = ChronicCareAppUtil.getUserOnboardingActionsFuture(
                serviceInterfaces,
                userContext,
                Optional.ofNullable(expiredPackResponse),
                userOnboardingService,
                exceptionReportingService
        );
        CompletableFuture<Boolean> userOrderedAtleastOnceFromEcommerceFuture = chronicCareServiceHelper.hasUserPlacedAnyEcommerceOrder(userContext);
        UserOnboardingActionWithContext onboardingActions = onboardingActionFuture.get();
        CgmOnboardingStatusResponse cgmOnboardingStatusResponse = chsClient.fetchOnboardingStatus(userId, null,
                getAppTenantFromUserContext(userContext));
        List<com.sugarfit.chs.pojo.CGMDeviceInfo> deviceInfos = cgmOnboardingStatusResponse.getCgmDeviceInfos();
        try {
            if (cgmDeviceId == null || cgmDeviceId.isEmpty() || cgmDeviceId.equals(CGM_ACTIVATION_DUMMY_DEVICE_ID)) {
                cgmDeviceId = null;
                if (CollectionUtils.isNotEmpty(deviceInfos)) {
                    deviceInfos = deviceInfos.stream().filter(cgmDeviceInfo -> {
                        if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) ||
                                "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) ||
                                null == cgmDeviceInfo.getStartedOn()) {
                            return false;
                        }
                        return cgmDeviceInfo.isAtleastOneReadingDone()
                                || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState());
                    }).collect(Collectors.toList());

                    if (deviceInfos.size() > 0) {
                        cgmDeviceId = deviceInfos.get(deviceInfos.size() - 1).getDeviceId();
                    }
                }
            }
            cgmStat = chsClient.fetchCgmStat(Long.valueOf(userContext.getUserProfile().getUserId()),
                    patientDetail.getId(), cgmDeviceId, true,
                    AppUtil.getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezone));
            cgmStat.setDailySugarStatMap(getSortedStatMap(cgmStat.getDailySugarStatMap(), userContext));
        } catch (HttpException e) {
            log.error("Exception in fetchCgmStat", e);
            exceptionReportingService.reportException("Exception in fetchCgmStat", e);
        }
        CompletableFuture<CGMGraphWidget> cgmGraphWidgetFuture = getCGMGraphWidgetFuture(serviceInterfaces, userContext,
                onboardingActions, cgmStat, cgmOnboardingStatusResponse, true);
        boolean isNonCGMProduct = expiredPackResponse == null || ChronicCareAppUtil
                .isNonCGMProduct(expiredPackResponse.getBundleProduct(), cgmOnboardingStatusResponse);
        // CompletableFuture<AssignedAgentWidget> assignedAgentsWidgetFuture =
        // getAssignedAgentsWidgetFuture(userContext, null, pack, patientDetail,
        // expiredPackResponse);
        // TODO: Remove after a week
        if (showUltraFitAppMigrationPopUp(userContext)) {
            result.addWidget(getUltraFitAppPopUpWidget(userContext));
        }
        if (isAppUpdateAvailable(userContext)) {
            result.addWidget(getLatestApp());
        }
        // AssignedAgentWidget assignedAgentsWidget = assignedAgentsWidgetFuture.get();
        BaseWidgetNonVM salesBannerWidget = getSalesBannerWidget(userContext, onboardingActions,
                cgmOnboardingStatusResponse,
                expiredPackResponse != null ? expiredPackResponse.getBundleProduct() : null, userOrderedAtleastOnceFromEcommerceFuture.get());
        if (salesBannerWidget != null) {
            result.addWidget(salesBannerWidget);
        }
        addRenewalAlertWidget(userContext, result, null, patientDetail);

        if (!userOrderedAtleastOnceFromEcommerceFuture.get()) {
            BaseWidgetNonVM ecomBannerWidget = chronicCareServiceHelper.getEComBannerWidget(userContext, null, false);
            if (ecomBannerWidget != null) {
                result.addWidget(ecomBannerWidget);
            }
        }

        addCGMGraphWidgets(userContext, result, onboardingActions, cgmGraphWidgetFuture, isNonCGMProduct, false,
                cgmOnboardingStatusResponse, cgmDeviceId, null, false, null, patientDetail);
        if (!AppUtil.isUltraFitApp(userContext)) {
            this.addLoggingWidget(result);
        }
        result.addWidget(sugarFitBlogWidgetBuilder.buildBlogsWidget(userContext, null));
    }

    private void addPackUpcomingPageWidgets(UserContext userContext, ChronicCareHomePageView result, UserEntry user,
            ActivePackResponse upcomingPackResponse,
            PatientDetail patientDetail) throws HttpException {
        // TODO: Remove after a week
        if (showUltraFitAppMigrationPopUp(userContext)) {
            result.addWidget(getUltraFitAppPopUpWidget(userContext));
        }
        if (isAppUpdateAvailable(userContext)) {
            result.addWidget(getLatestApp());
        }
        this.addUpcomingSubscriptionWidget(userContext, result, upcomingPackResponse);
        if (!AppUtil.isUltraFitApp(userContext)) {
            this.addLoggingWidget(result);
        }
        result.addWidget(sugarFitBlogWidgetBuilder.buildBlogsWidget(userContext, null));
    }

    private void addDisabledUserPageWidgets(ChronicCareHomePageView result, UserEntry user, UserContext userContext)
            throws InterruptedException, ExecutionException, ResourceNotFoundException {
        result.addWidget(getDisabledUserBannerWidget());
        result.setNavAction(getNavAction("navBarAction", "curefit://hamburgermenu", true, ActionType.NAVIGATION));
        result.setTtl(300000L);
        result.setFitnessttl(1800000L); // milliseconds of 30 minutes
        result.setShowBackgroundAnimation(!isBackgroundAnimationDisabled(userContext.getUserProfile().getUserId()));
        SfAppUpdateConfig config = chronicCareServiceHelper.getSfAppUpdateConfig();
        if (shouldShowAppUpdatePopUp(userContext, config)) {
            result.setAppUpdateData(getAppUpdateData(userContext, config));
        }
        result.addWidget(sugarFitBlogWidgetBuilder.buildBlogsWidget(userContext, null));
    }

    private void addUpcomingSubscriptionWidget(UserContext userContext, ChronicCareHomePageView result,
            ActivePackResponse upcomingPackResponse) throws HttpException {
        SubscriptionCardWidget subscriptionWidgetForUpcomingPack = subscriptionPageViewBuilder
                .createSubscriptionWidgetForUpcomingPack(userContext, upcomingPackResponse);
        long maxStartDateDelay = chronicCareServiceHelper.getMaxStartDateDelay(upcomingPackResponse.getBundleProduct());
        subscriptionWidgetForUpcomingPack.setMaxStartDateDelayWindowInDays(maxStartDateDelay);
        if (maxStartDateDelay > 0) {
            subscriptionWidgetForUpcomingPack.setAllowStartDateChange(true);
            subscriptionWidgetForUpcomingPack
                    .setMaxStartDate(upcomingPackResponse.getPurchaseDate() + (maxStartDateDelay * MILLIS_IN_A_DAY));
        } else {
            subscriptionWidgetForUpcomingPack.setAllowStartDateChange(false);
        }
        result.addWidget(subscriptionWidgetForUpcomingPack);
    }

    private void addRenewalAlertWidget(UserContext userContext, ChronicCareHomePageView result,
            UserOnboardingActionWithContext userOnboardingActionWithContext, PatientDetail patientDetail) {
        if (userContext.getSessionInfo().getAppVersion() >= 1.29f) {
            ChronicCareTeam assignedCareTeam = getAssignedCareTeam(userContext, patientDetail.getId());
            ArrayList<BaseWidgetNonVM> widgets = new ArrayList<>();
            result.addWidget(renewalAlertWidgetBuilder.build(userContext, userOnboardingActionWithContext,
                    assignedCareTeam.getCoach(), widgets));
            result.addWidgets(widgets);
        }
    }

    private void addOnboardingPageWidgets(UserContext userContext, String cgmDeviceId, ChronicCareHomePageView result,
            ActivePackResponse activePackResponse,
            UserOnboardingActionWithContext onboardingActions, PatientDetail patientDetail,
            BundleProduct pack)
            throws InterruptedException, ExecutionException, BaseException, TimeoutException {
        Long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        String timezone = userContext.getUserProfile().getTimezone() != null
                ? userContext.getUserProfile().getTimezone()
                : "Asia/Kolkata";
        boolean isUltraFit = isUltraFitApp(userContext);
        boolean isSugarFit = isSugarFitApp(userContext);
        boolean isExperienceCentreManagerApp = isExperienceCentreManager(userContext);

        CgmStat cgmStat = new CgmStat();
        CgmUserRequestStatus cgmUserRequestStatus = chsClient.fetchCgmRequestStatus(userId,
                AppUtil.getAppTenantFromUserContext(userContext));
        CgmOnboardingStatusResponse cgmOnboardingStatusResponse = chsClient.fetchOnboardingStatus(userId, null,
                getAppTenantFromUserContext(userContext));
        List<com.sugarfit.chs.pojo.CGMDeviceInfo> deviceInfos = cgmOnboardingStatusResponse.getCgmDeviceInfos();
        boolean shouldShowScanCardInsideCGMGraph = false;
        boolean isSensorJustInitialized = false;
        boolean isOnGoingDevice = false;
        try {
            if (cgmDeviceId == null || cgmDeviceId.isEmpty() || cgmDeviceId.equals(CGM_ACTIVATION_DUMMY_DEVICE_ID)) {
                cgmDeviceId = null;
                if (CollectionUtils.isNotEmpty(deviceInfos)) {
                    deviceInfos = deviceInfos.stream().filter(cgmDeviceInfo -> {
                        if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) ||
                                "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) ||
                                null == cgmDeviceInfo.getStartedOn()) {
                            return false;
                        }
                        return cgmDeviceInfo.isAtleastOneReadingDone()
                                || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState());
                    }).collect(Collectors.toList());

                    if (deviceInfos.size() > 0) {
                        isSensorJustInitialized = true;
                        cgmDeviceId = deviceInfos.get(deviceInfos.size() - 1).getDeviceId();
                        isOnGoingDevice = deviceInfos.get(deviceInfos.size() - 1).isOngoing();
                    }
                }
                shouldShowScanCardInsideCGMGraph = (isSensorJustInitialized
                        && onboardingActions.getAGMDataActionWithContext().getContext().isEnableFirstInAppReading())
                        || ChronicCareAppUtil.cgmActivationAllowed(userContext,
                                cgmOnboardingStatusResponse);
            } else {
                String finalCgmDeviceId = cgmDeviceId;
                com.sugarfit.chs.pojo.CGMDeviceInfo deviceInfo = deviceInfos.stream()
                        .filter(cgmDeviceInfo -> "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState())
                                && cgmDeviceInfo.getDeviceId().equals(finalCgmDeviceId))
                        .findFirst().orElse(null);
                if (deviceInfo != null) {
                    isSensorJustInitialized = true;
                }
                com.sugarfit.chs.pojo.CGMDeviceInfo currentDevice = deviceInfos.stream()
                        .filter(cgmDeviceInfo -> cgmDeviceInfo.getDeviceId().equals(finalCgmDeviceId)).findFirst()
                        .orElse(null);
                if (currentDevice != null) {
                    isOnGoingDevice = currentDevice.isOngoing();
                }
                shouldShowScanCardInsideCGMGraph = isSensorJustInitialized
                        && onboardingActions.getAGMDataActionWithContext().getContext().isEnableFirstInAppReading();
            }
            cgmStat = chsClient.fetchCgmStat(Long.valueOf(userContext.getUserProfile().getUserId()),
                    patientDetail.getId(), cgmDeviceId, true,
                    AppUtil.getAppTenantFromUserContext(userContext),
                    TimeZone.getTimeZone(timezone));
            if (shouldShowScanCardInsideCGMGraph
                    && ChronicCareAppUtil.cgmActivationAllowed(userContext,
                            cgmOnboardingStatusResponse)) {
                cgmStat.setCgmDeviceId(CGM_ACTIVATION_DUMMY_DEVICE_ID);
            }
            cgmStat.setDailySugarStatMap(getSortedStatMap(cgmStat.getDailySugarStatMap(), userContext));
        } catch (HttpException e) {
            log.error("Exception in fetchCgmStat", e);
            exceptionReportingService.reportException("Exception in fetchCgmStat", e);
        }

        CompletableFuture<AssignedAgentWidget> assignedAgentsWidgetFuture = getAssignedAgentsWidgetFuture(
                serviceInterfaces, userContext, onboardingActions, pack, patientDetail, activePackResponse);
        CompletableFuture<BaseWidgetNonVM> whatsNextWidgetFuture = getWhatsNextWidgetFuture(serviceInterfaces,
                userContext, onboardingActions, activePackResponse, patientDetail);
        CompletableFuture<BaseWidgetNonVM> miniConsultationCardsWidgetFuture = getMiniConsultationCardsWidgetFuture(
                serviceInterfaces, userContext, onboardingActions, activePackResponse, patientDetail);
        CompletableFuture<BaseWidgetNonVM> onboardCompletionWidgetFuture = getOnboardCompletionWidgetFuture(
                serviceInterfaces, userContext, onboardingActions);
        CompletableFuture<BaseWidgetNonVM> cgmTrackerWidgetFuture = getCgmTrackerWidgetFuture(serviceInterfaces,
                userContext, onboardingActions, activePackResponse, patientDetail, cgmOnboardingStatusResponse);
        CompletableFuture<CGMGraphWidget> cgmGraphWidgetFuture = getCGMGraphWidgetFuture(serviceInterfaces, userContext,
                onboardingActions, cgmStat, cgmOnboardingStatusResponse, false);
        CompletableFuture<BaseWidgetNonVM> actionCardWidgetFuture = getActiveCardsWidgetFuture(serviceInterfaces,
                userContext, onboardingActions, patientDetail, activePackResponse);
        CompletableFuture<BaseWidgetNonVM> interventionWidgetFuture = getInterventionsWidgetFutureV2(serviceInterfaces,
                userContext, onboardingActions);
        CompletableFuture<BaseWidgetNonVM> referralBannerWidgetFuture = getReferralBannerWidgetFuture(serviceInterfaces,
                userContext);
        CompletableFuture<BaseWidgetNonVM> activityCompareWidgetFuture = getActivityCompareWidgetFuture(
                serviceInterfaces, userContext);
        CompletableFuture<BaseWidgetNonVM> experimentHomeWidgetFuture = getExperimentHomeWidgetFuture(serviceInterfaces,
                userContext);
        CompletableFuture<SfHomeScanFaceWidget> faceScanWidgetFuture = getFaceScanWidgetFuture(serviceInterfaces,
                userContext);
        CompletableFuture<Boolean> userOrderedAtleastOnceFromEcommerceFuture = chronicCareServiceHelper.hasUserPlacedAnyEcommerceOrder(userContext);

        boolean isCGMGraphPresent = cgmGraphWidgetFuture.get() != null;

        // BaseWidgetNonVM deliveryDelayBannerWidget =
        // getCGMDeliveryDelayBannerWidget(userContext);
        // if (deliveryDelayBannerWidget != null) {
        // result.addWidget(deliveryDelayBannerWidget);
        // }

        if (!isExperienceCentreManagerApp) {
            BaseWidgetNonVM salesBannerWidget = getSalesBannerWidget(userContext, onboardingActions,
                    cgmOnboardingStatusResponse, pack, userOrderedAtleastOnceFromEcommerceFuture.get());
            if (salesBannerWidget != null) {
                result.addWidget(salesBannerWidget);
            }
        }

        if (!isExperienceCentreManagerApp && !userOrderedAtleastOnceFromEcommerceFuture.get()) {
            BaseWidgetNonVM ecomBannerWidget = chronicCareServiceHelper.getEComBannerWidget(userContext, null, false);
            if (ecomBannerWidget != null) {
                result.addWidget(ecomBannerWidget);
            }
        }

        if (!isExperienceCentreManagerApp && isSugarFit && isCoachUserSessionSupportedVersion(userContext)) {
            SfCoachCommunitySessionWidget coachUserSessionWidget = new SfCoachCommunitySessionWidget();
            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            coachUserSessionWidget.setBuildTime(calendar.getTime().getTime());
            coachUserSessionWidget.setPosition("top");
            result.addWidget(coachUserSessionWidget);
        }

        if (!isExperienceCentreManagerApp && isUltraFit) {
            SfAnimatedScoreWidget animatedScoreWidget = getAnimatedScoreWidget(userContext, cgmStat, onboardingActions);
            if (animatedScoreWidget != null)
                result.addWidget(animatedScoreWidget);

            BaseWidgetNonVM sfRequestCgmWidget = getRequestCGMWidget(userContext);
            if (sfRequestCgmWidget != null)
                result.addWidget(sfRequestCgmWidget);
        }

        if (isAppUpdateAvailable(userContext)) {
            result.addWidget(getLatestApp());
        }

        BaseWidgetNonVM activeCardWidget = actionCardWidgetFuture.get(5, TimeUnit.SECONDS);
        if (null != activeCardWidget) {
            result.addWidget(activeCardWidget);
        }

        if (onboardingActions.getPackRenewalContextActionWithContext().getAction().isActionPermitted()) {
            addRenewalAlertWidget(userContext, result, onboardingActions, patientDetail);
        }

        if (isUltraFit) {
            if (onboardingActions.getCoachCardActionWithContext().getAction().isFirstConsultDone() ||
                    onboardingActions.getAGMDataActionWithContext().getContext().isAtleastOneCGMCompleted() ||
                    onboardingActions.getAGMDataActionWithContext().getContext().isCGMActive()) {
                BaseWidgetNonVM miniConsultationCardsWidget = miniConsultationCardsWidgetFuture.get(5,
                        TimeUnit.SECONDS);
                if (miniConsultationCardsWidget != null) {
                    result.addWidget(miniConsultationCardsWidget);
                }
            } else {
                BaseWidgetNonVM whatsNextWidget = whatsNextWidgetFuture.get(5, TimeUnit.SECONDS);
                if (null != whatsNextWidget) {
                    result.addWidget(whatsNextWidget);
                    result.setShowCoachMarks(false);
                }
            }
        } else {
            BaseWidgetNonVM whatsNextWidget = whatsNextWidgetFuture.get(5, TimeUnit.SECONDS);
            if (null != whatsNextWidget) {
                result.addWidget(whatsNextWidget);
                result.setShowCoachMarks(showCoachMarks(activePackResponse));
            } else {
                BaseWidgetNonVM onboardCompletionWidget = onboardCompletionWidgetFuture.get(5, TimeUnit.SECONDS);
                if (onboardCompletionWidget != null) {
                    result.addWidget(onboardCompletionWidget);
                }
            }
        }

        BaseWidgetNonVM cgmTrackerWidget = cgmTrackerWidgetFuture.get(5, TimeUnit.SECONDS);
        if (!isExperienceCentreManagerApp && null != cgmTrackerWidget) {
            result.addWidget(cgmTrackerWidget);
        }

        if (isOpenCommunityEnabledForUser(userContext)) {
            result.setShowCommunityIcon(true);
        }

        if (!isExperienceCentreManagerApp && isSugarFit) {
            BaseWidgetNonVM sfRequestCgmWidget = getRequestCGMWidget(userContext);
            if (sfRequestCgmWidget != null)
                result.addWidget(sfRequestCgmWidget);
        }

        if (!isExperienceCentreManagerApp && !cgmOnboardingStatusResponse.isAtleastOneCGMStarted()) {
            addFbvWidgetToResult(result, userContext, cgmOnboardingStatusResponse, activePackResponse, deviceInfos,
                    faceScanWidgetFuture);
        }

        addRecommendedFitnessPlanWidgetToResult(result, userContext);

        if (!isExperienceCentreManagerApp && !isChallengesDisabledForUser(userContext)) {
            addChallengeCardsToResult(result, userContext);
        }
        if (!isExperienceCentreManagerApp && !isPollsDisabledForUser(userContext)) {
            addPollSurveyToResult(result, userContext);
            addPollCardsToResult(result, userContext);
        }

        boolean isNonCGMProduct = ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(),
                cgmOnboardingStatusResponse);
        addCGMGraphWidgets(userContext, result, onboardingActions, cgmGraphWidgetFuture,
                isNonCGMProduct, shouldShowScanCardInsideCGMGraph, cgmOnboardingStatusResponse, cgmDeviceId,
                cgmUserRequestStatus, isOnGoingDevice, activePackResponse, patientDetail);

        if (!isExperienceCentreManagerApp && isSugarFit && isCoachUserSessionSupportedVersion(userContext)) {
            SfCoachCommunitySessionWidget coachUserSessionWidget = new SfCoachCommunitySessionWidget();
            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            coachUserSessionWidget.setBuildTime(calendar.getTime().getTime());
            coachUserSessionWidget.setPosition("bottom");
            result.addWidget(coachUserSessionWidget);
        }

//        BaseWidgetNonVM referralBannerWidget = referralBannerWidgetFuture.get(5, TimeUnit.SECONDS);
//        if (isCGMGraphPresent) {
//            if (referralBannerWidget != null) {
//                result.addWidget(referralBannerWidget);
//            }
//        }

        if (!isExperienceCentreManagerApp && isUltraFit) {
            BaseWidgetNonVM ufReferralBannerWidget = getUfReferralBannerWidget(userContext);
            if (ufReferralBannerWidget != null) {
                result.addWidget(ufReferralBannerWidget);
            }
        }

        if (!isExperienceCentreManagerApp && cgmOnboardingStatusResponse.isAtleastOneCGMStarted()) {
            addFbvWidgetToResult(result, userContext, cgmOnboardingStatusResponse, activePackResponse, deviceInfos,
                    faceScanWidgetFuture);
        }

        if (!isUltraFit) {
            this.addLoggingWidget(result);
        }

        if (!isExperienceCentreManagerApp && isUltraFit) {
            BaseWidgetNonVM experimentHomeWidget = experimentHomeWidgetFuture.get(5, TimeUnit.SECONDS);
            if (experimentHomeWidget != null) {
                result.addWidget(experimentHomeWidget);
            }
        }

        BaseWidgetNonVM interventionWidget = interventionWidgetFuture.get(10, TimeUnit.SECONDS);
        if (null != interventionWidget) {
            result.addWidget(interventionWidget);
        }

        if (!isExperienceCentreManagerApp && activityCompareWidgetFuture.get() != null) {
            result.addWidget((activityCompareWidgetFuture.get()));
        }

        if (!isExperienceCentreManagerApp) {
            result.addWidget(this.getLazyLoadingWellnessAtGlance(userContext));
        }

//        if (!isCGMGraphPresent) {
//            if (referralBannerWidget != null) {
//                result.addWidget(referralBannerWidget);
//            }
//        }

        AssignedAgentWidget assignedAgentsWidget = assignedAgentsWidgetFuture.get(5, TimeUnit.SECONDS);
        if (null != assignedAgentsWidget) {
            result.addWidget(assignedAgentsWidget);
        }

        if (!isExperienceCentreManagerApp && isUltraFit && isDiscordBannerEnabled(userContext)) {
            BaseWidgetNonVM discordBannerWidget = getDiscordBannerWidget();
            result.addWidget(discordBannerWidget);
        }

        result.addWidget(sugarFitBlogWidgetBuilder.buildBlogsWidget(userContext, null));
        if (!isExperienceCentreManagerApp) {
            result.setBookingModalConfig(
                    getBookingModalConfig(userContext, onboardingActions, activePackResponse, patientDetail));
        }
        if (isSugarFitApp(userContext) || (isUltraFitApp(userContext) && isUfInAppChatSupportedVersion(userContext))) {
            result.setSfCareTeam(getSfCareTeam(userContext, onboardingActions, patientDetail, activePackResponse));
        }
        if (!isExperienceCentreManagerApp && shouldShowAppAnnouncement()) {
            result.setAppAnnouncementData(
                    getAppAnnouncementData(userContext, cgmOnboardingStatusResponse, activePackResponse));
        }
    }

    private void addFbvWidgetToResult(ChronicCareHomePageView result, UserContext userContext,
            CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
            ActivePackResponse activePackResponse, List<com.sugarfit.chs.pojo.CGMDeviceInfo> cgmDeviceInfos,
            CompletableFuture<SfHomeScanFaceWidget> faceScanWidgetFuture)
            throws ExecutionException, InterruptedException, TimeoutException {
//        if (isFaceBasedVitalsEligibleForUser(userContext, segmentationCacheClient, deviceService)) {
//            if (cgmOnboardingStatusResponse.isAtleastOneCGMCompleted()
//                    || ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(),
//                            cgmOnboardingStatusResponse)
//                    || ChronicCareAppUtil.isDelayedCGMDeliveryUser(userContext, segmentationCacheClient)) {
//                SfHomeScanFaceWidget faceScanWidget = faceScanWidgetFuture.get(5, TimeUnit.SECONDS);
//                if (faceScanWidget != null) {
//                    result.addWidget(faceScanWidget);
//                    result.setShowFaceScanFab(getShowFaceScanFab(userContext, cgmDeviceInfos, faceScanWidget));
//                }
//            } else if (!ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(),
//                    cgmOnboardingStatusResponse)) {
//                result.addWidget(getFbvPreCGMBannerWidget());
//            }
//        }
        return;
    }

    public void addPollCardsToResult(ChronicCareHomePageView result, UserContext userContext) throws BaseException, ExecutionException, InterruptedException {
        if (ChronicCareAppUtil.isPollGroupSupportedAppVersion(userContext)) {
            result.addWidget(new SfPollsGroupWidget());
        } else {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            if (ChronicCareAppUtil.isPollsEnabled(userContext)) {
                SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache()
                        .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
                Set<String> relevantSegments = serviceInterfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.SF_POLL_CLIENT);
                List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(relevantSegments));
                List<PollEntry> activePolls = pollSupportClient.fetchActivePollsV2(userId, 0, 20, "createdOn", "DESC", new ArrayList<>(userSegments)).getElements();
                List<PollEntry> participatedPolls = pollSupportClient.fetchUserParticipatedPolls(userId, 0, 20, "createdOn", "DESC").getElements();
                List<PollEntry> polls = new ArrayList<>();
                if (!CollectionUtils.isEmpty(activePolls)) {
                    PollEntry mentalHealthPollCard = activePolls.stream().filter(el -> el.getPollType() == PollType.DDS).toList().get(0);
                    List<PollEntry> activePollEntries = activePolls.stream().filter(el -> el.getPollType() != PollType.DDS).toList();
                    polls.add(mentalHealthPollCard);
                    polls.addAll(activePollEntries);
                }
                if (participatedPolls != null) {
                    polls.addAll(participatedPolls);
                }
                if (CollectionUtils.isNotEmpty(polls)) {
                    List<BaseWidgetNonVM> pollWidgets = polls.stream().map(p -> {
                        try {
                            if(p.getPollType() == PollType.DDS) {
                                return ChronicCareServiceHelper.buildMentalHealthPollWidget(userContext, p);
                            } else {
                                return ChronicCareServiceHelper.buildPollWidgets(pollSupportClient, serviceInterfaces.userServiceClient, userContext, p, null);
                            }
                        } catch (BaseException e) {
                            e.printStackTrace();
                            return null;
                        }
                    }).toList();
                    if (CollectionUtils.isNotEmpty(pollWidgets)) {
                        pollWidgets.forEach(result::addWidget);
                    }
                }
            }
        }
    }

    public void addChallengeCardsToResult(ChronicCareHomePageView result, UserContext userContext)
            throws BaseException {
        result.addWidget(new SfChallengesGroupWidget());
    }

    public void addPollSurveyToResult(ChronicCareHomePageView result, UserContext userContext)
            throws BaseException {
        result.addWidget(new SfPollSurveyGroupWidget());
    }
        
    public void addRecommendedFitnessPlanWidgetToResult(ChronicCareHomePageView result, UserContext userContext)
            throws BaseException {
        result.addWidget(new SfRecommendedFitnessPlanWidget());
    }

    private Boolean showCoachMarks(ActivePackResponse activePackResponse) {
        return activePackResponse.getStartDate() >= WHATS_NEXT_RELEASE_DATE;
    }

//    private boolean getShowFaceScanFab(UserContext userContext,
//            List<com.sugarfit.chs.pojo.CGMDeviceInfo> cgmDeviceInfos,
//            SfHomeScanFaceWidget faceScanWidget) {
//        try {
//            boolean scanAvailableForDay = !faceScanWidget.getScanFaceAction().getDisabled();
//            return isFaceBasedVitalsEligibleForUser(userContext, segmentationCacheClient, deviceService)
//                    && scanAvailableForDay
//                    && !getIsCgmOngoing(cgmDeviceInfos);
//        } catch (Exception e) {
//            log.error("Exception in getShowFaceScanFab", e);
//            exceptionReportingService.reportException("Exception in getShowFaceScanFab", e);
//            return false;
//        }
//    }

    private boolean getIsCgmOngoing(List<com.sugarfit.chs.pojo.CGMDeviceInfo> cgmDeviceInfos) {
        AtomicReference<Boolean> isCgmActive = new AtomicReference<>(false);

        if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
            cgmDeviceInfos.forEach(cgmDeviceInfo -> {
                if (cgmDeviceInfo.isOngoing()) {
                    isCgmActive.set(true);
                }
            });
        }

        return isCgmActive.get();
    }

    private UfExperimentHomeWidget getExperimentHomeWidget(UserContext userContext) throws HttpException {
        UfExperimentHomeWidget experimentHomeWidget = new UfExperimentHomeWidget();

        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezoneId = userContext.getUserProfile().getTimezone() != null
                ? userContext.getUserProfile().getTimezone()
                : "Asia/Kolkata";
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
        AtomicReference<Boolean> isCgmActive = new AtomicReference<>(false);
        Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
        UserAssignedExperimentFilterRequest experimentRequest = new UserAssignedExperimentFilterRequest();
        experimentRequest.setUserId(userId);
        experimentRequest.setDate(calendar.getTime());
        experimentRequest.setType(ExperimentFilterType.ONGOING);

        List<com.sugarfit.chs.pojo.CGMDeviceInfo> cgmDeviceInfos = chsClient.fetchAllCgmDeviceInfo(userId, appTenant);
        UserAssignedExperimentResponse experimentResponse = experimentClient
                .filterAssignedExperiments(experimentRequest, appTenant, TimeZone.getTimeZone(timezoneId));

        if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
            cgmDeviceInfos.forEach(cgmDeviceInfo -> {
                if (cgmDeviceInfo.isOngoing()) {
                    isCgmActive.set(true);
                }
            });
        }

        if (CollectionUtils.isEmpty(cgmDeviceInfos) ||
                (!isCgmActive.get() && CollectionUtils.isEmpty(experimentResponse.getOngoingExperiments()))) {
            return null;
        }

        Action titleAction = Action.builder()
                .url("curefit://experimenttabs?index=1")
                .actionType(ActionType.NAVIGATION).build();

        Action joinMoreAction = Action.builder()
                .url("curefit://experimenttabs?index=0")
                .actionType(ActionType.NAVIGATION).build();

        experimentHomeWidget.setTitleAction(titleAction);
        experimentHomeWidget.setJoinMoreAction(joinMoreAction);
        experimentHomeWidget.setCgmActive(isCgmActive.get());
        return experimentHomeWidget;
    }

    private BookingModalConfig getBookingModalConfig(UserContext userContext,
            UserOnboardingActionWithContext onboardingActions,
            ActivePackResponse activePackResponse,
            PatientDetail patientDetail) {
        try {
            ChronicCareTeam assignedCareTeam = getAssignedCareTeam(userContext, patientDetail.getId());
            BookingModalConfig bookingModalConfig = new BookingModalConfig();

            if (isUltraFitApp(userContext)) {
                return bookingModalConfig;
            }

            if (onboardingActions.getCoachCardActionWithContext().getAction().isActionPermitted()) {
                CardData coachCardData = getCoachCardDataV2(userContext, onboardingActions, assignedCareTeam,
                        activePackResponse);
                coachCardData.setCardMessage(
                        isUltraFitApp(userContext) ? " is your Metabolic Coach. Book an appointment and start your journey!"
                                : " is your Diabetes Coach. Book an appointment and start your journey!");
                bookingModalConfig.setCardData(coachCardData);
            } else if (!isUltraFitApp(userContext) && ChronicCareAppUtil.isCGMDplusIEnabled(userContext) && chronicCareServiceHelper.isCGMInstallationBookingAllowed(onboardingActions) && chronicCareServiceHelper.isCGMInstallationBookingPending(onboardingActions)) {
                CardData cgmCardData = getCGMInstallationCardDataV2(userContext, onboardingActions);
                cgmCardData.setCardMessage("Learn how your body responds to your eating habits, book your CGM delivery and installation");
                bookingModalConfig.setCardData(cgmCardData);
            } else if (onboardingActions.getDiagnosticCardActionWithContext().getAction().isActionPermitted() &&
                    onboardingActions.getDiagnosticCardActionWithContext().getAction().getServiceableOnline() &&
                    !isUltraFitApp(userContext)) {
                CardData diagnosticCardData = getDiagnosticCardDataV2(userContext, onboardingActions, patientDetail);
                diagnosticCardData
                        .setCardMessage(" a time to get your diagnostic tests done from the comfort of your home!");
                bookingModalConfig.setCardData(diagnosticCardData);
            } else if (!chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct()) &&
                    onboardingActions.getDoctorCardActionWithContext().getAction().isActionPermitted() &&
                    onboardingActions.getDoctorCardActionWithContext().getAction().isShowCard() &&
                    !isUltraFitApp(userContext)) {
                CardData doctorCardData = getDoctorCardDataV2(onboardingActions, assignedCareTeam, activePackResponse,
                        userContext);
                doctorCardData.setCardMessage(" is your doctor on sugar.fit. Book an appointment and start your journey!");
                bookingModalConfig.setCardData(doctorCardData);
            }
            return bookingModalConfig;
        } catch (Exception e) {
            String message = String.format("Error in creating booking modal config for user :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
            return null;
        }
    }

    private void addCGMGraphWidgets(UserContext userContext, ChronicCareHomePageView result,
                                    UserOnboardingActionWithContext onboardingActions,
                                    CompletableFuture<CGMGraphWidget> cgmGraphWidgetFuture,
                                    boolean isNonCGMProduct, boolean shouldShowScanCardInsideCGMGraph,
                                    CgmOnboardingStatusResponse cgmOnboardingStatusResponse, String cgmDeviceId,
                                    CgmUserRequestStatus cgmUserRequestStatus, boolean isOnGoingDevice,
                                    ActivePackResponse activePackResponse, PatientDetail patientDetail) throws ExecutionException, InterruptedException, ResourceNotFoundException {
        ChronicCareHomePageView.UserCGMConfiguration userCGMConfiguration = new ChronicCareHomePageView.UserCGMConfiguration(
                userContext);
        userCGMConfiguration.setVoiceCommandEnabled(true);
        CGMGraphWidget cgmGraphWidget = cgmGraphWidgetFuture.get();
        boolean allowCGMActivation = false;
        boolean allowConsultationSlotBooking = false;
        if (onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse() != null
                && onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions() != null) {
            allowCGMActivation = onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions().equals(SelfInstallationActions.ALLOW_SELF_ACTIVATION);
            allowConsultationSlotBooking = onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions().equals(SelfInstallationActions.ALLOW_BOOKING_FOR_CGM);
        }
        if(allowCGMActivation) {
            userCGMConfiguration.setSelfInstallationCardTitle("Ready to install your next CGM?");
            Action watchVideoAction = chronicCareServiceHelper.getWatchCGMInstallationVideoAction("", false, false);
            userCGMConfiguration.setSelfInstallationWatchVideoAction(watchVideoAction);
        } else if (allowConsultationSlotBooking) {
//            userCGMConfiguration.setSelfInstallationCardTitle("Book video call with CGM Expert");
//            userCGMConfiguration.setSelfInstallationCardSubtitle("Please do not install without the video call from experts");
//            Action watchVideoAction = chronicCareServiceHelper.getWatchCGMInstallationVideoAction("WATCH INSTALLATION VIDEO", false, false);
//            Action bookSlotAction = chronicCareServiceHelper.getBookCGMInstallationAction(userContext, "BOOK INSTALLATION SLOTS");
//            userCGMConfiguration.setSelfInstallationBookConsultationAction(bookSlotAction);
//            userCGMConfiguration.setSelfInstallationWatchVideoAction(watchVideoAction);
            try {
                userCGMConfiguration.setSelfInstallationCardTitle("Ready to install your next CGM?");
                String phleboConsultProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
                Action bookSlotAction = chronicCareServiceHelper.getBookCGMInstallationByPhleboAction(userContext, "BOOK INSTALLATION SLOT", phleboConsultProductId);
                userCGMConfiguration.setSelfInstallationWatchVideoAction(bookSlotAction);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
        }

        userCGMConfiguration.setScanModeText("It's time to scan your CGM sensor to get real time glucose readings");
        userCGMConfiguration.setScanButtonText("SCAN SENSOR");
        userCGMConfiguration.setActivationModeText("Once installed, scan your sensor to activate it.");
        userCGMConfiguration.setActivationButtonText("ACTIVATE SENSOR");
        userCGMConfiguration
                .setInitAppModeInNFCDeviceText("Your sensor has been activated. You can scan your sensor in");
        userCGMConfiguration.setInitAppModeInNonNFCDeviceText(
                "Once your sensor has been installed, you should use NFC enabled device to take reading.");
        userCGMConfiguration.setInitPhleboModeText("Phlebo will do the reading for you");
        userCGMConfiguration.setReadingTimeout(40000);
        userCGMConfiguration.setScanRetryCount(5);
        userCGMConfiguration.setNewSensorActivationAllowed(cgmOnboardingStatusResponse.isNewSensorActivationAllowed());
        userCGMConfiguration.setEnableActivation(cgmOnboardingStatusResponse.isEnableActivation());
        userCGMConfiguration
                .setDeviceModelEnableActivation(cgmOnboardingStatusResponse.getDeviceModelEnableActivation());
        userCGMConfiguration.setDeviceModelNewSensorActivationAllowed(
                cgmOnboardingStatusResponse.getDeviceModelNewSensorActivationAllowed());
        List<com.sugarfit.chs.pojo.CGMDeviceInfo> cgmDeviceInfos = cgmOnboardingStatusResponse.getCgmDeviceInfos();
        if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
            cgmDeviceInfos.forEach(cgmDeviceInfo -> {
                if (cgmDeviceInfo.getDeviceId().equals(cgmDeviceId)) {
                    userCGMConfiguration.setLastScannedHexIndex(cgmDeviceInfo.getLastScannedHexIndex());
                    userCGMConfiguration.setCgmDeviceModel(cgmDeviceInfo.getDeviceModel());
                    userCGMConfiguration.setCgmDeviceHardwareId(
                            ChronicCareAppUtil.getModifiedHardwareIdToSendToApp(userContext,
                                    cgmDeviceInfo.getHardwareId(), cgmDeviceInfo.getDeviceModel()));
                }
            });
        }

        if (null != cgmGraphWidget) {
            if (shouldShowScanCardInsideCGMGraph) {
                // Used by app version < 4.21
                cgmGraphWidget.setShowScanSensorCardWidget(true);
                // Used by app version >= 4.21
                ScanSensorCardWidget scanSensorCardWidget = new ScanSensorCardWidget();
                scanSensorCardWidget.setAllowCGMActivation(allowCGMActivation || allowConsultationSlotBooking);
                scanSensorCardWidget.setAllowConsultationSlotBooking(allowConsultationSlotBooking);
                scanSensorCardWidget.setUserCGMConfiguration(userCGMConfiguration);
                if (ChronicCareAppUtil.cgmActivationAllowed(userContext,
                        cgmOnboardingStatusResponse)
                        && AppUtil.isSugarFitApp(userContext)) {
                    scanSensorCardWidget.setOpsRequestAllowed(
                            cgmUserRequestStatus != null && cgmUserRequestStatus.isEnableInstallCgmCta());
                }
                cgmGraphWidget.setSensorCardWidget(scanSensorCardWidget);

                cgmGraphWidget.setNavToInsight(false);
            }
            result.addWidget(cgmGraphWidget);
            userCGMConfiguration.setCgmReadingMode(cgmGraphWidget.getCgmGraphData().getCurrentReadingMode());
        } else {
            ReadingModeResponse readingModeResponse = getLatestReadingModeForUser(userContext);
            userCGMConfiguration
                    .setCgmReadingMode(readingModeResponse == null ? null : readingModeResponse.getReadingMode());
        }
        boolean appScanningEnabled = cgmOnboardingStatusResponse.isEnableInAppScanning() &&
                isInAppScanningEnabled(userContext) && isOnGoingDevice;
        userCGMConfiguration.setShowScanButton(appScanningEnabled);
        if (appScanningEnabled || ChronicCareAppUtil.cgmActivationAllowed(userContext,
                cgmOnboardingStatusResponse)) {
            userCGMConfiguration.setDeviceNFCLocationInfo(getDeviceNFCLocationInfo(userContext));
        }

        if (appScanningEnabled
                && onboardingActions.getAGMDataActionWithContext().getAction().isShowAGMReport()
                && onboardingActions.getAGMDataActionWithContext().getContext().isShowScanReadingPrompt()) {
            String redisKey = "CGM_INAPP_READING_PROMPT_" + userContext.getUserProfile().getUserId();
            String redisValue = this.cfApiRedisKeyValueStore.get(redisKey);
            if (redisValue == null || redisValue.isEmpty()) {
                ChronicCareHomePageView.UserCGMConfiguration.ReadingAlert readingAlert = new ChronicCareHomePageView.UserCGMConfiguration.ReadingAlert();
                readingAlert.setShowScanReadingPrompt(true);
                userCGMConfiguration.setReadingAlert(readingAlert);
                this.cfApiRedisKeyValueStore.set(redisKey, "TRUE", SIX_HOURS_IN_SECONDS);
            }
        }
        ChronicCareHomePageView.UserCGMConfiguration.SuccessConfig successConfig = userCGMConfiguration
                .getCgmReaderModalConfig().getSuccessConfig();
        boolean isUltraFit = AppUtil.isUltraFitApp(userContext);
        successConfig.setCtaText("VIEW INSIGHTS");
        successConfig.setCtaAction(Action.builder().url("curefit://cgmtabbedpage").title("VIEW INSIGHTS")
                    .actionType(ActionType.NAVIGATION).build());
        ChronicCareHomePageView.UserCGMConfiguration.CgmReaderModalConfig cgmReaderModalConfig = userCGMConfiguration
                .getCgmReaderModalConfig();
        cgmReaderModalConfig.setSuccessConfig(successConfig);
        userCGMConfiguration.setCgmReaderModalConfig(cgmReaderModalConfig);
        result.setCgmConfiguration(userCGMConfiguration);

        boolean status = false;
        ChronicCareHomePageView.UserCGMConfiguration.DisclaimerAlert disclaimerAlert = new ChronicCareHomePageView.UserCGMConfiguration.DisclaimerAlert();
        if (cgmGraphWidget != null) {
            if (isNonCGMProduct || isUltraFit) {
                status = false;
            } else {
                status = !(Optional.ofNullable(cgmGraphWidget.getCgmGraphData().getDisclaimerAccepted()).orElse(true));
            }
        }

        if(activePackResponse != null && cgmGraphWidget != null) {
            boolean isFirstCoachConsultNotBooked = onboardingActions.getCoachCardActionWithContext().getAction().isActionPermitted();
            if(isFirstCoachConsultNotBooked) {
                onboardingActions.getCoachCardActionWithContext().getAction();
                cgmGraphWidget.setShowCGMInsightsLocked(!isUltraFit);
                cgmGraphWidget.setCgmInsightsTitle("Book coach consult to unlock");
                ChronicCareTeam assignedCareTeam = chronicCareServiceHelper.getAssignedCareTeam(userContext, patientDetail.getId(), activePackResponse.getBundleProduct());
                if (assignedCareTeam.getCoach() != null && assignedCareTeam.getCoach().getAgentResponse() != null && assignedCareTeam.getCoach().getAgentResponse().getId() != null) {
                    try {
                        String consultationProduct = onboardingActions.getCoachCardActionWithContext().getContext()
                                .getProductCodes().stream().findFirst().get();

                        Long centerId = assignedCareTeam.getCoach().getAgentResponse().getAgentCenterMapping().stream().findFirst().get()
                                .getCenterId();
                        cgmGraphWidget.setCoachId(String.valueOf(assignedCareTeam.getCoach().getAgentResponse().getId()));
                        Action action = Action.builder()
                                .isEnabled(true)
                                .title("Book Coach Consultation")
                                .actionType(ActionType.NAVIGATION)
                                .url(getAgentDatePickerUrl(consultationProduct, centerId, assignedCareTeam.getCoach().getAgentResponse().getId(), activePackResponse))
                                .build();
                        cgmGraphWidget.setCgmInsightsPrimaryAction(action);
                    } catch (Exception exec) {
                        String message = "CGM Insights Locked, Exception: ";
                        log.error(message+exec.getMessage());
                        exceptionReportingService.reportException(message, exec);
                    }
                }
            } else {
                cgmGraphWidget.setShowCGMInsightsLocked(false);
            }
        }

        disclaimerAlert.setShowDisclaimerAlertPrompt(status);
        userCGMConfiguration.setDisclaimerAlert(disclaimerAlert);
    }

    private ReadingModeResponse getLatestReadingModeForUser(UserContext userContext) {
        try {
            return chsClient.fetchCgmReadingMode(Long.valueOf(userContext.getUserProfile().getUserId()),
                    AppUtil.getAppTenantFromUserContext(userContext));
        } catch (Exception e) {
            String message = String.format("Error in fetching latest reading mode for user :: %s", e.getMessage());
            log.error(message, e);
            return null;
        }
    }

    private boolean isInAppScanningEnabled(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        if (isUserPartOfNFCDisabledSegment(userContext)) {
            return false;
        } else if (isUserPartOfNFCEnabledSegment(userContext)) {
            return true;
        } else {
            return true;
        }
    }

    private void addTrialPackHomePageWidgets(UserContext userContext, ChronicCareHomePageView result)
            throws HttpException {
        Optional<ActivePackResponse> upcomingPackResponse = userOnboardingService
                .getSugarFitUpcomingPack(userContext.getUserProfile().getUserId());
        // if upcoming pack is present and its not a trial pack
        boolean isFullPackPurchased = upcomingPackResponse.isPresent()
                && (!upcomingPackResponse.get().getBundleProduct().getIsTrialProduct());
        if (isFullPackPurchased) {
            this.addUpcomingSubscriptionWidget(userContext, result, upcomingPackResponse.get());
        }
        result.addWidget(this.getTrailPackHomePageCreative());
        if (!isFullPackPurchased) {
            result.addWidget(this.annualPackPurchaseWidget());
        }
        result.addWidget(sugarFitBlogWidgetBuilder.buildBlogsWidget(userContext, null));
    }

    private CompletableFuture<BaseWidgetNonVM> getWhatsNextWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext,
            UserOnboardingActionWithContext onboardingActions,
            ActivePackResponse activePackResponse,
            PatientDetail patientDetail) {
        return supplyAsync(() -> {
            try {
                if(ChronicCareAppUtil.isCGMDplusIEnabled(userContext)) {
                    return this.getWhatNextWidgetV3(userContext, onboardingActions, activePackResponse, patientDetail);
                }
                return this.getWhatNextWidgetV2(userContext, onboardingActions, activePackResponse, patientDetail);
            } catch (Exception e) {
                log.error("Exception in getWhatNextWidget", e);
                exceptionReportingService.reportException("Exception in getWhatNextWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<BaseWidgetNonVM> getMiniConsultationCardsWidgetFuture(ServiceInterfaces serviceInterfaces, UserContext userContext,
            UserOnboardingActionWithContext onboardingActions,
            ActivePackResponse activePackResponse,
            PatientDetail patientDetail) {
        return supplyAsync(() -> {
            try {
                return this.getMiniConsultationCardsWidget(userContext, onboardingActions, activePackResponse,
                        patientDetail);
            } catch (Exception e) {
                log.error("Exception in getMiniConsultationCardsWidget", e);
                exceptionReportingService.reportException("Exception in getMiniConsultationCardsWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<BaseWidgetNonVM> getCgmTrackerWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext,
            UserOnboardingActionWithContext onboardingActions,
            ActivePackResponse activePackResponse,
            PatientDetail patientDetail, CgmOnboardingStatusResponse cgmOnboardingStatusResponse) {
        return supplyAsync(() -> {
            try {
                boolean isNonCGMProduct = ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(),
                        cgmOnboardingStatusResponse);
                if (isNonCGMProduct) {
                    if (!Objects.equals(
                            onboardingActions.getAGMDataActionWithContext().getContext().getIsDelivered(), null)
                            && !onboardingActions.getAGMDataActionWithContext().getContext().getIsDelivered()) {
                        return this.getCgmTrackerWidget(userContext, onboardingActions, activePackResponse, true);
                    }
                } else if (onboardingActions.getCgmInstallationCardActionWithContext().getAction()
                        .isActionPermitted()) {
                    return this.getCgmTrackerWidget(userContext, onboardingActions, activePackResponse, false);
                }
            } catch (Exception e) {
                log.error("Exception in getWhatNextWidget", e);
                exceptionReportingService.reportException("Exception in getWhatNextWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<AssignedAgentWidget> getAssignedAgentsWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext,
            UserOnboardingActionWithContext onboardingActions,
            BundleProduct pack,
            PatientDetail patientDetail,
            ActivePackResponse activePackResponse) {
        return supplyAsync(() -> {
            try {
                AssignedAgentWidget assignedAgentWidget = this.getAssignedAgentsWidgets(userContext, onboardingActions,
                        patientDetail, activePackResponse);
                assignedAgentWidget.setRmAgentWidget(null);
                return assignedAgentWidget;
            } catch (Exception e) {
                log.error("Exception in getAssignedAgentsWidgets", e);
                exceptionReportingService.reportException("Exception in getAssignedAgentsWidgets", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());

    }

    private CompletableFuture<BaseWidgetNonVM> getActiveCardsWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext,
            UserOnboardingActionWithContext onboardingActions,
            PatientDetail patientDetail, ActivePackResponse activePackResponse) {
        return supplyAsync(() -> {
            try {
                return this.getActiveCardsWidget(userContext, onboardingActions, patientDetail, activePackResponse);
            } catch (Exception e) {
                log.error("Exception in getActiveCardsWidget", e);
                exceptionReportingService.reportException("Exception in getActiveCardsWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<CGMGraphWidget> getCGMGraphWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext,
            UserOnboardingActionWithContext onboardingActions,
            CgmStat cgmStat, CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
            boolean isPackExpired) {
        // add version to constructor based on segment, v2 for lighter graph

        return supplyAsync(() -> {
            try {
                return this.getCGMGraphWidget(userContext, onboardingActions, cgmStat, cgmOnboardingStatusResponse,
                        isPackExpired);
            } catch (Exception e) {
                log.error("Exception in getCGMGraphWidget", e);
                exceptionReportingService.reportException("Exception in getCGMGraphWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());

    }

    private CompletableFuture<BaseWidgetNonVM> getReferralCarouselWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext) {
        return supplyAsync(() -> {
            try {
                if (isReferralBottomBannerEnabledForUser(userContext)) {
                    return this.getReferralCarouselWidget();
                }
            } catch (Exception e) {
                log.error("Exception in getReferralCarouselWidget", e);
                exceptionReportingService.reportException("Exception in getReferralCarouselWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<BaseWidgetNonVM> getReferralBannerWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext) {
        return supplyAsync(() -> {
            try {
                if (isReferralBottomBannerEnabledForUser(userContext)) {
                    return this.getReferralBannerWidget();
                }
            } catch (Exception e) {
                log.error("Exception in getReferralBannerWidget", e);
                exceptionReportingService.reportException("Exception in getReferralBannerWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<BaseWidgetNonVM> getExperimentHomeWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return this.getExperimentHomeWidget(userContext);
            } catch (Exception e) {
                log.error("Exception in getExperimentHomeWidget", e);
                exceptionReportingService.reportException("Exception in getExperimentHomeWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<SfHomeScanFaceWidget> getFaceScanWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return this.getScanFaceWidget(userContext);
            } catch (Exception e) {
                log.error("Exception in getFaceScanWidgetFuture", e);
                exceptionReportingService.reportException("Exception in getFaceScanWidgetFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<BaseWidgetNonVM> getActivityCompareWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext) {
        return supplyAsync(() -> {
            try {
                String timezoneId = userContext.getUserProfile().getTimezone() != null
                        ? userContext.getUserProfile().getTimezone()
                        : "Asia/Kolkata";
                boolean showCompareWidget = sfLoggingClient.areAnyTwoActivitiesComparable(
                        Long.valueOf(userContext.getUserProfile().getUserId()),
                        AppUtil.getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
                if (showCompareWidget) {
                    SfActivityCompareCardWidget sfActivityCompareCardWidget = new SfActivityCompareCardWidget();
                    Action action = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://impactcomparison")
                            .build();
                    sfActivityCompareCardWidget.setAction(action);
                    return sfActivityCompareCardWidget;
                }
                return null;
            } catch (Exception e) {
                log.error("Exception in getActivityCompareWidgetFuture", e);
                exceptionReportingService.reportException("Exception in getActivityCompareWidgetFuture", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfAnimatedScoreWidget getAnimatedScoreWidget(UserContext userContext, CgmStat cgmStat,
            UserOnboardingActionWithContext onboardingActions) {
        try {
            SfAnimatedScoreWidget animatedScoreWidget = new SfAnimatedScoreWidget();
            if (cgmStat != null && cgmStat.getDailySugarStatMap() != null) {
                List<DailySugarStat> dailySugarStats = Lists.newArrayList(cgmStat.getDailySugarStatMap().values());
                if (dailySugarStats.size() > 0) {
                    cgmStat.setDailySugarStatMap(getSortedStatMap(cgmStat.getDailySugarStatMap(), userContext));
                    List<CGMDeviceInfo> cgmDeviceInfos = onboardingActions.getAGMDataActionWithContext().getContext()
                            .getCgmDeviceInfos();
                    AtomicBoolean hasActiveCGM = new AtomicBoolean(false);
                    if (cgmDeviceInfos != null && cgmDeviceInfos.size() > 0) {
                        cgmDeviceInfos.forEach(cgmDeviceInfo -> {
                            if (cgmDeviceInfo.isOngoing()) {
                                hasActiveCGM.set(true);
                            }
                        });
                    }
                    DailySugarStat recentStat = null;
                    DailySugarStat prevStat = null;
                    int statIndex = dailySugarStats.size() - 1;
                    while (statIndex >= 0) {
                        if (dailySugarStats.get(statIndex).getSugarScore() != null) {
                            recentStat = dailySugarStats.get(statIndex);
                            if (statIndex > 0) {
                                prevStat = dailySugarStats.get(statIndex - 1);
                            }
                            break;
                        }
                        statIndex--;
                    }

                    if (hasActiveCGM.get() && recentStat != null) {
                        animatedScoreWidget.setMetabolicScore(recentStat.getSugarScore());
                        animatedScoreWidget.setTimeOutsideTarget(recentStat.getTimeOverRangeGlucosePercentage());
                        animatedScoreWidget.setAvgGlucose(recentStat.getAvgSugarValue());
                        if (prevStat != null && prevStat.getSugarScore() != null) {
                            double scoreDiff = recentStat.getSugarScore() - prevStat.getSugarScore();
                            animatedScoreWidget.setScoreDiff(scoreDiff);
                        }
                    } else {
                        animatedScoreWidget.setMetabolicScore(cgmStat.getAverageMetabolicScore());
                        animatedScoreWidget.setTimeOutsideTarget(cgmStat.getTimeOverRangeGlucosePercentage());
                        animatedScoreWidget.setAvgGlucose(cgmStat.getAvgGlucose());
                        animatedScoreWidget.setMetabolicScoreSubTitle("average over the last CGM Period");
                        animatedScoreWidget.setTimeOutsideSubTitle("average over the last CGM Period");
                        animatedScoreWidget.setAvgGlucoseSubTitle("average over the last CGM Period");
                    }
                }
            }
            return animatedScoreWidget;
        } catch (Exception e) {
            log.error("Exception in addLoggingWidget", e);
            exceptionReportingService.reportException("Exception in addLoggingWidget", e);
        }
        return null;
    }

    private BaseWidgetNonVM getRequestCGMWidget(UserContext userContext) {
        try {
            Long userId = Long.parseLong(userContext.getUserProfile().getUserId());
            CgmUserRequestStatus cgmUserRequestStatus = chsClient.fetchCgmRequestStatus(userId,
                    AppUtil.getAppTenantFromUserContext(userContext));
            CgmOnboardingStatusResponse cgmOnboardingStatusResponse = chsClient.fetchOnboardingStatus(userId, null,
                    AppUtil.getAppTenantFromUserContext(userContext));
            List<com.sugarfit.chs.pojo.CGMDeviceInfo> cgmDeviceInfos = cgmOnboardingStatusResponse.getCgmDeviceInfos();
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd");
            sdf.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
            if (cgmDeviceInfos != null && cgmDeviceInfos.size() > 0) {
                if (AppUtil.isUltraFitApp(userContext)) {
                    if (cgmUserRequestStatus.isEnableRequestCgmCta()) {
                        return new UFPackSubscriptionAlertWidget("Your CGM is expired",
                                new Action("curefit://renewsubscription", "BUY NEW", ActionType.NAVIGATION), true);
                    } else if (cgmUserRequestStatus.isEnableInstallCgmCta()) {
                        SfRequestCgmWidget sfRequestCgmWidget = new SfRequestCgmWidget();
                        com.sugarfit.chs.pojo.CGMDeviceInfo cgmDeviceInfo = cgmDeviceInfos
                                .get(cgmDeviceInfos.size() - 1);
                        Date startDate = cgmDeviceInfo.getStartedOn();
                        Date endDate = cgmDeviceInfo.getEndDate();
                        String startDateString = startDate != null ? sdf.format(startDate) : "";
                        String endDateString = endDate != null ? sdf.format(endDate) : "";
                        sfRequestCgmWidget
                                .setDurationText("Last CGM period: " + startDateString + " - " + endDateString);
                        sfRequestCgmWidget
                                .setNavAction(Action.builder().url("curefit://cgmtabbedpage").actionType(ActionType.NAVIGATION).build());
                        sfRequestCgmWidget.setIsInstallation(true);
                        sfRequestCgmWidget.setCtaAction(Action.builder().title("REQUEST CGM INSTALLATION").build());
                        return sfRequestCgmWidget;
                    } else {
                        AtomicReference<Date> cgmStartDate = new AtomicReference<>();
                        if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
                            cgmDeviceInfos.forEach(cgmDeviceInfo -> {
                                if (cgmDeviceInfo.isOngoing()) {
                                    cgmStartDate.set(cgmDeviceInfo.getStartedOn());
                                }
                            });
                        }
                        if (cgmStartDate.get() != null) {
                            long daysBetween = ChronoUnit.DAYS.between(
                                    LocalDateTime.ofInstant(cgmStartDate.get().toInstant(),
                                            ZoneId.of(userContext.getUserProfile().getTimezone())),
                                    LocalDateTime.now());
                            if (daysBetween > 11 && daysBetween < 14) {
                                long remainingDays = 14 - (daysBetween + 1);
                                String title = "Your CGM is expiring in " + remainingDays
                                        + (remainingDays == 1 ? " day" : " days");
                                return new UFPackSubscriptionAlertWidget(title,
                                        new Action("curefit://renewsubscription", "BUY NEW", ActionType.NAVIGATION),
                                        true);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception in addLoggingWidget", e);
            exceptionReportingService.reportException("Exception in addLoggingWidget", e);
        }
        return null;
    }

    private void addLoggingWidget(ChronicCareHomePageView result) {
        try {
            result.addWidget(this.getActivityLoggingWidget());
        } catch (Exception e) {
            log.error("Exception in addLoggingWidget", e);
            exceptionReportingService.reportException("Exception in addLoggingWidget", e);
        }
    }

    private ChronicCareUserDetails getUserDetails(UserEntry user) {
        ChronicCareUserDetails userDetails = new ChronicCareUserDetails();
        userDetails.setDisplayImage(user.getProfilePictureUrl());
        if (user.getFirstName() != null) {
            userDetails.setNameText(String.format("Hi %s!", user.getFirstName()));
        }
        return userDetails;
    }

    private CompletableFuture<BaseWidgetNonVM> getOnboardCompletionWidgetFuture(ServiceInterfaces serviceInterfaces,
            UserContext userContext, UserOnboardingActionWithContext context) {
        return supplyAsync(() -> {
            try {
                if (!isOnboardingCompletionShown(userContext)
                        && context.getDoctorCardActionWithContext().getAction().isFirstConsultDone()) {
                    OnboardingCompletionWidget onboardingCompletionWidget = new OnboardingCompletionWidget();
                    onboardingCompletionWidget.setWidgetTitle("Kickstart your reversal journey");
                    onboardingCompletionWidget.setTitle("Congratulations!");
                    onboardingCompletionWidget.setSubTitle(
                            "Your onboarding is complete. Wish you the best for your diabetes reversal journey!");

                    UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
                    userAttributeEntry.setUserId(Long.parseLong(userContext.getUserProfile().getUserId()));
                    userAttributeEntry.setAppTenant(AppUtil.getAppTenantFromUserContext(userContext));
                    userAttributeEntry.setAttribute("sf-onboarding-completion-msg-shown");
                    userAttributeEntry.setAttrValue(true);
                    userAttributeEntry.setDataType(DataType.BOOLEAN);
                    this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry,
                            AppUtil.getAppTenantFromUserContext(userContext));

                    return onboardingCompletionWidget;
                }
                return null;
            } catch (Exception e) {
                log.error("Exception in setting attribute", e);
                exceptionReportingService.reportException("Exception in updateAttribute", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfReferralCarouselWidget getReferralCarouselWidget() {
        SfReferralCarouselWidget referralCarouselWidget = new SfReferralCarouselWidget();
        List<SfReferralBannerItem> bannerData = new ArrayList<>();

        bannerData.add(new SfReferralBannerItem("image/chroniccare/referral/referral_banner_paaji.png"));

        Action referralAction = new Action();
        referralAction.setUrl("curefit://sfreferralpage");
        referralAction.setActionType(ActionType.NAVIGATION);

        referralCarouselWidget.setBannerData(bannerData);
        referralCarouselWidget.setAction(referralAction);

        return referralCarouselWidget;
    }

    private SfReferralTopBannerWidget getReferralBannerWidget() {
        SfReferralTopBannerWidget referralTopBannerWidget = new SfReferralTopBannerWidget();

        Action referralAction = new Action();
        referralAction.setUrl("curefit://sfreferralpage");
        referralAction.setActionType(ActionType.NAVIGATION);

        referralTopBannerWidget.setTitle("Refer and Earn");
        referralTopBannerWidget.setSubTitle("Refer your Loved ones and get ");
        referralTopBannerWidget.setBoldText("cash reward for every referral");
        referralTopBannerWidget.setAction(referralAction);

        return referralTopBannerWidget;
    }

    private SfBannerCarouselWidget getDiscordBannerWidget() {
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();

        BannerItem bannerItem = new BannerItem();
        bannerItem.setImage("image/chroniccare/marketing/banners/home/<USER>/discord_banner.png");
        Action action = new Action("https://discord.gg/xsJ52TCmBT", "Navigate", ActionType.EXTERNAL_DEEP_LINK);
        bannerItem.setAction(action);

        bannerItemList.add(bannerItem);
        sfBannerCarouselWidget.setData(bannerItemList);
        sfBannerCarouselWidget.setLayoutProps(getDiscordBannerLayoutProps());
        return sfBannerCarouselWidget;
    }

    private ChronicCareActiveCardsWidget getActiveCardsWidget(UserContext userContext,
            UserOnboardingActionWithContext context,
            PatientDetail patientDetail, ActivePackResponse activePackResponse)
            throws ExecutionException, InterruptedException, ResourceNotFoundException, TimeoutException {
        Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
        Date currentTime = calendar.getTime();
        String userId = userContext.getUserProfile().getUserId();
        UserEntry user = userContext.getUserEntryCompletableFuture().get(5, TimeUnit.SECONDS);
        List<ActiveConsultationResponse> consultationBookings = serviceInterfaces.sfAlbusClient
                .getActiveConsultations(userId, null);
        log.debug(String.format("for user :: %s, active consultations :: %s", userId,
                consultationBookings.stream().map(x -> x.getAppointmentId().toString())
                        .collect(Collectors.joining(","))));
        List<BookingInfo<DiagnosticStateInfo>> diagnosticBookings = serviceInterfaces.sfAlbusClient
                .getActiveDiagnosticBookings(userId, patientDetail.getId(), null);
        List<OrderResponse> diagnosticBookingsV2 = fetchEkinCareOrders(userId);
        List<ActiveCard> liveClassActiveCards = getLiveActiveCards(userContext);
        List<ActiveCard> phleboBookings = chronicCareServiceHelper.getPhleboActiveCards(userContext);
        List<ActiveCard> ecommerceOrders = chronicCareServiceHelper.getECommerceOrderActiveCards(userContext);
        List<ActiveCard> audioCallTasks = chronicCareServiceHelper.getAudioCallUpcomingCards(userContext);
        List<ActiveCard> diagnosticActiveCards = new ArrayList<>();
        List<ActiveCard> consultationActiveCards = new ArrayList<>();

        consultationBookings.forEach((consultation) -> {
            try {
                if(Objects.nonNull(consultation.getProductCode()) && ChronicCareAppUtil.isPhleboConsultation(consultation.getProductCode())) {
//                    Video Consultation - CGM Self Installation
                    consultationActiveCards.add(this.getCGMSelfInstallationConsultationActiveCard(userContext, consultation, user));
                } else {
//                    Video Consultation - Doctor/Coach
                    consultationActiveCards.add(this.getConsultationActiveCardV2(userContext, consultation, user, activePackResponse));
                }
            } catch (Exception e) {
                log.error("Error while fetching Consultation Active card", e);
                exceptionReportingService.reportException("Error while fetching Consultation Active card", e);
            }
        });
        filterAndcreateDiagnosticActiveCards(userContext, diagnosticBookings, diagnosticBookingsV2,
                diagnosticActiveCards);
        List<ActiveCard> upcomingCards = new ArrayList<>();
        List<ActiveCard> completedCards = new ArrayList<>();
        Set<StatusType> upcomingActiveCardStatus = Set.of(SCHEDULED, UPCOMING, STARTED);
        Set<StatusType> completedActiveCardStatus = Set.of(COMPLETED, REPORT_GENERATED, SAMPLE_COLLECTED);
        Set<StatusType> missedActiveCardStatus = Set.of(MISSED);
        Date consultationCompletedCutOff = DateUtils.addDays(currentTime, -1);
        Date diagnosticCompletedCutOff = DateUtils.addDays(currentTime, -2);
        for (ActiveCard activeCard : consultationActiveCards) {
            log.debug("for user :: {}, checking card :: {}", userId, activeCard);
            if (activeCard.getEndDate().after(consultationCompletedCutOff)) {
                if (upcomingActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    upcomingCards.add(activeCard);
                } else if (completedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    if (activeCard.getStartDate().after(consultationCompletedCutOff)) {
                        completedCards.add(activeCard);
                    }
                } else if (missedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    if (activeCard.getEndDate().after(currentTime)) {
                        completedCards.add(activeCard);
                    }
                }
            }
        }
        for (ActiveCard activeCard : diagnosticActiveCards) {
            log.debug("for user :: {}, processing card :: {}", userId, activeCard);
            if (activeCard.getEndDate().after(diagnosticCompletedCutOff)) {
                if (upcomingActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    upcomingCards.add(activeCard);
                } else if (completedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    completedCards.add(activeCard);
                } else if (missedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    completedCards.add(activeCard);
                }
            }
        }
        for (ActiveCard activeCard : phleboBookings) {
            log.debug("for user :: {}, processing phlebo card :: {}", userId, activeCard);
            if (activeCard.getEndDate() != null && activeCard.getEndDate().after(currentTime)) {
                upcomingCards.add(activeCard);
            }
        }
        upcomingCards.addAll(liveClassActiveCards);
        if (CollectionUtils.isNotEmpty(ecommerceOrders)) {
            upcomingCards.addAll(ecommerceOrders);
        }
        if (CollectionUtils.isNotEmpty(audioCallTasks)) {
            upcomingCards.addAll(audioCallTasks);
        }
        Collections.sort(upcomingCards,
                Comparator.comparingLong(a -> Math.abs(a.getStartDate().getTime() - currentTime.getTime())));
        Collections.sort(completedCards, Comparator.comparingLong(a -> a.getStartDate().getTime()));
        Collections.reverse(completedCards);
        List<ActiveCard> combinedCards = new ArrayList<>();
        combinedCards.addAll(upcomingCards);
        combinedCards.addAll(completedCards);
        ChronicCareActiveCardsWidget widget = new ChronicCareActiveCardsWidget();
        widget.setActiveCards(combinedCards);
        if (CollectionUtils.isEmpty(combinedCards)) {
            return null;
        }
        return widget;
    }

    private List<OrderResponse> fetchEkinCareOrders(String userId) {
        try {
            OrderFilterRequest diagnosticsFilterRequest = new OrderFilterRequest();
            diagnosticsFilterRequest.setActiveOrders(true);
            diagnosticsFilterRequest.setUserIds(List.of(Long.valueOf(userId)));
            OrderFilterResponse orderFilterResponse = housemdClient.filterOrders(diagnosticsFilterRequest, userId);
            if (orderFilterResponse != null && CollectionUtils.isNotEmpty(orderFilterResponse.getOrderResponses())) {
                return orderFilterResponse.getOrderResponses();
            }
        } catch (Exception e) {
            log.error("Error while fetching EkinCare diagnostic order", e);
            exceptionReportingService.reportException("Error while fetching EkinCare diagnostic order", e);
        }
        return new ArrayList<>();
    }

    private void filterAndcreateDiagnosticActiveCards(UserContext userContext,
            List<BookingInfo<DiagnosticStateInfo>> diagnosticBookings,
            List<OrderResponse> diagnosticBookingsV2,
            List<ActiveCard> diagnosticActiveCards) {
        for (BookingInfo<DiagnosticStateInfo> diagnosticBooking : diagnosticBookings) {
            DiagnosticsTestOrderResponse diagnosticsTestOrderResponse = diagnosticBooking
                    .getDiagnosticsTestOrderResponse().get(0);
            if ((diagnosticsTestOrderResponse.getThirdPartySellerMetaData() != null)
                    || (diagnosticsTestOrderResponse.getAtHomeDiagnosticOrder() != null)) {
                try {
                    diagnosticActiveCards.add(this.getDiagnosticActiveCardV2(userContext, diagnosticBooking));
                } catch (Exception e) {
                    log.error("Error while fetching Diagnostic Active card", e);
                    exceptionReportingService.reportException("Error while fetching Diagnostic Active card", e);
                }
            }
        }

        for (OrderResponse diagnosticBooking : diagnosticBookingsV2) {
            try {
                diagnosticActiveCards.add(this.getDiagnosticActiveCardV3(userContext, diagnosticBooking));
            } catch (Exception e) {
                log.error("Error while fetching Diagnostic Active card", e);
                exceptionReportingService.reportException("Error while fetching Diagnostic Active card", e);
            }
        }
    }

    private Long getEndTime(BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        DiagnosticsTestOrderResponse diagnosticsTestOrderResponse = diagnosticBooking.getDiagnosticsTestOrderResponse()
                .get(0);
        if (diagnosticsTestOrderResponse.getAtHomeDiagnosticOrder() != null) {
            return diagnosticsTestOrderResponse.getAtHomeDiagnosticOrder().getEndTime();
        } else {
            return diagnosticsTestOrderResponse.getThirdPartySellerMetaData().getSlot().getSlotEndTime().getTime();
        }
    }

    private Long getStartTime(BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        DiagnosticsTestOrderResponse diagnosticsTestOrderResponse = diagnosticBooking.getDiagnosticsTestOrderResponse()
                .get(0);
        if (diagnosticsTestOrderResponse.getAtHomeDiagnosticOrder() != null) {
            return diagnosticsTestOrderResponse.getAtHomeDiagnosticOrder().getStartTime();
        } else {
            return diagnosticsTestOrderResponse.getThirdPartySellerMetaData().getSlot().getSlotStartTime().getTime();
        }
    }

    private ActiveCard getDiagnosticActiveCardV2(UserContext userContext,
            BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        ActiveCard card = new ActiveCard();
        try {
            Product product = serviceInterfaces.getCatalogueService()
                    .getProduct(diagnosticBooking.getBooking().getProductCode());
            card.setTitle(product.getTitle());
        } catch (BaseException e) {
            String message = String.format("Error fetching product :: %s, error :: %s",
                    diagnosticBooking.getBooking().getProductCode(), e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
            card.setTitle("Diagnostic Tests");
        }
        card.setType("DIAGNOSTICS");
        card.setSubType("DIAGNOSTICS");
        StatusType status = this.getDiagnosticStatus(diagnosticBooking);
        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(status);
        cardStatus.setColor(this.getDiagnosticsStatusColor(status));
        card.setStatus(cardStatus);
        card.setInfoTitle("INSTRUCTIONS");
        card.setInstruction("Do not eat or drink anything except water 10-12 hours before the test");
        boolean is3POrder = diagnosticBooking.getDiagnosticsTestOrderResponse().get(0)
                .getAtHomeDiagnosticOrder() == null;
        if ((!is3POrder) && status.equals(SCHEDULED)) {
            List<DropdownAction> actions = new ArrayList<>();
            DropdownAction cancelDropdownAction = new DropdownAction();
            cancelDropdownAction.setText("Cancel Booking");
            Action cancelAction = new Action();
            cancelAction.setActionType(ActionType.CANCEL_TC);
            CancelActionMeta cancelActionMeta = new CancelActionMeta();
            cancelActionMeta.setTcBookingId(diagnosticBooking.getBooking().getId());
            cancelAction.setMeta(cancelActionMeta);
            cancelDropdownAction.setValue(cancelAction);
            actions.add(cancelDropdownAction);
            Boolean phleboCallingAllowed = diagnosticBooking.getDiagnosticsTestOrderResponse().get(0)
                    .getAtHomeStepInfo().getAllowedActions().indexOf(AlbusActionType.PHLEBO_CALLING) != -1;
            if (phleboCallingAllowed) {
                String phleboNumber = diagnosticBooking.getDiagnosticsTestOrderResponse().get(0)
                        .getAtHomeDiagnosticOrder().getPhleboMobileNumber();
                Action phleboCallAction = new Action();
                phleboCallAction.setActionType(ActionType.PHONE_CALL_NAVIGATION);
                phleboCallAction.setTitle("CALL");
                PhleboCallingMeta meta = new PhleboCallingMeta();
                meta.setPhoneNumber(phleboNumber);
                phleboCallAction.setMeta(meta);
                card.setPrimaryAction(phleboCallAction);
            } else {
                Action toastAction = this.getDisabledActionForToastMessage("Available 1 Hour before your slot");
                toastAction.setIsEnabled(false);
                toastAction.setTitle("CALL");
                card.setPrimaryAction(toastAction);
            }
            if (diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getAtHomeStepInfo().getAllowedActions()
                    .indexOf(AlbusActionType.RESCHEDULE) != -1
                    && ChronicCareAppUtil.isRescheduleDiagnosticsSupported(userContext)) {
                actions.add(this.getRescheduleDropdownAction(diagnosticBooking));
            }
            card.setSecondaryActions(actions);
            card.setInstructionImageUrl("/image/chroniccare/dont_eat.png");
            card.setTertiaryAction(this.getShowInstructionsAction());
        } else if (status.equals(SCHEDULED)) {
            card.setInstructionImageUrl("/image/chroniccare/dont_eat.png");
            card.setTertiaryAction(this.getShowInstructionsAction());
        } else if (status.equals(REPORT_GENERATED)) {
            card.setInfoTitle("EMAIL REPORTS");
            card.setTertiaryAction(getEmailReportAction(diagnosticBooking));
            card.setInstruction("Your report has been sent to you over Whatsapp.");
        } else if (status.equals(SAMPLE_COLLECTED)) {
            card.setInstruction(
                    "Samples for your diagnostic tests have been collected. You will receive your reports in 24-48 hours.");
        }
        ActiveCardDetails details = new ActiveCardDetails();
        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("MMM dd, h:mm");
        dateFormatStartTime.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        long startDate = getStartTime(diagnosticBooking);
        String formattedDateStart = dateFormatStartTime.format(startDate);
        SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
        dateFormatEndTime.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        long endTime = getEndTime(diagnosticBooking);
        String formattedDateEnd = dateFormatEndTime.format(endTime);
        String subTitle;
        card.setStartDate(new Date(startDate));
        card.setEndDate(new Date(endTime));
        if (startDate == endTime) {
            subTitle = formattedDateStart.replace("am", "AM").replace("pm", "PM");
        } else {
            subTitle = formattedDateStart + " - " + formattedDateEnd.replace("am", "AM").replace("pm", "PM");
        }
        details.setTitle("Diagnostic Tests");
        details.setSubTitle(subTitle);
        card.setStartDate(new Date(startDate));
        details.setIconImageUrl("/image/chroniccare/WhatsNextDiagnostics.png");
        card.setBookingDetails(details);
        return card;
    }

    private ActiveCard getDiagnosticActiveCardV3(UserContext userContext, OrderResponse diagnosticBooking) {
        ActiveCard card = new ActiveCard();
        try {
            Product product = serviceInterfaces.getCatalogueService()
                    .getProduct(diagnosticBooking.getOrderEntry().getProductCodes());
            card.setTitle(product.getTitle());
        } catch (BaseException e) {
            String message = String.format("Error fetching product :: %s, error :: %s",
                    diagnosticBooking.getOrderEntry().getProductCodes(), e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
            card.setTitle("Diagnostic Tests");
        }
        card.setType("DIAGNOSTICS");
        card.setSubType("DIAGNOSTICS");
        StatusType status = this.getDiagnosticStatusV2(diagnosticBooking);
        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(status);
        cardStatus.setColor(this.getDiagnosticsStatusColor(status));
        card.setStatus(cardStatus);
        card.setInfoTitle("INSTRUCTIONS");
        card.setInstruction("Do not eat or drink anything except water 10-12 hours before the test");
        if (status.equals(SCHEDULED)) {
            List<DropdownAction> actions = new ArrayList<>();
            DropdownAction cancelDropdownAction = new DropdownAction();
            cancelDropdownAction.setText("Cancel Booking");
            Action cancelAction = new Action();
            cancelAction.setActionType(ActionType.CANCEL_TC);
            CancelActionMeta cancelActionMeta = new CancelActionMeta();
            cancelActionMeta.setTcBookingId(diagnosticBooking.getOrderEntry().getBookingId());
            cancelAction.setMeta(cancelActionMeta);
            cancelDropdownAction.setValue(cancelAction);
            actions.add(cancelDropdownAction);
            boolean phleboCallingAllowed = diagnosticBooking.getAllowedActions().contains(OrderAction.PHLEBO_CALLING);
            if (phleboCallingAllowed) {
                String phleboNumber = diagnosticBooking.getOrderEntry().getPhleboNumber();
                Action phleboCallAction = new Action();
                phleboCallAction.setActionType(ActionType.PHONE_CALL_NAVIGATION);
                phleboCallAction.setTitle("CALL");
                PhleboCallingMeta meta = new PhleboCallingMeta();
                meta.setPhoneNumber(phleboNumber);
                phleboCallAction.setMeta(meta);
                card.setPrimaryAction(phleboCallAction);
            } else {
                Action toastAction = this.getDisabledActionForToastMessage("Available 1 Hour before your slot");
                toastAction.setIsEnabled(false);
                toastAction.setTitle("CALL");
                card.setPrimaryAction(toastAction);
            }
            if (diagnosticBooking.getAllowedActions().contains(OrderAction.RESCHEDULE)
                    && ChronicCareAppUtil.isRescheduleDiagnosticsSupported(userContext)) {
                actions.add(this.getRescheduleDropdownActionV2(diagnosticBooking));
            }
            card.setSecondaryActions(actions);
            card.setInstructionImageUrl("/image/chroniccare/dont_eat.png");
            card.setTertiaryAction(this.getShowInstructionsAction());
        } else if (status.equals(REPORT_GENERATED)) {
            card.setInfoTitle("EMAIL REPORTS");
            card.setTertiaryAction(getEmailReportActionV2(diagnosticBooking));
            card.setInstruction("Your report has been sent to you over Whatsapp.");
        } else if (status.equals(SAMPLE_COLLECTED)) {
            card.setInstruction(
                    "Samples for your diagnostic tests have been collected. You will receive your reports in 24-48 hours.");
        } else if (status.equals(MISSED)) {
            List<DropdownAction> actions = new ArrayList<>();
            if (diagnosticBooking.getAllowedActions().contains(OrderAction.RESCHEDULE)
                    && ChronicCareAppUtil.isRescheduleDiagnosticsSupported(userContext)) {
                actions.add(this.getRescheduleDropdownActionV2(diagnosticBooking));
                card.setPrimaryAction(this.getDiagnosticRescheduleAction(diagnosticBooking));
            }
            card.setSecondaryActions(actions);
            card.setInstruction("Please reschedule your appointment");
        }
        ActiveCardDetails details = new ActiveCardDetails();
        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("MMM dd, h:mm");
        dateFormatStartTime.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        long startDate = diagnosticBooking.getOrderEntry().getStartTime().getTime();
        String formattedDateStart = dateFormatStartTime.format(startDate);
        SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
        dateFormatEndTime.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        long endTime = diagnosticBooking.getOrderEntry().getEndTime().getTime();
        String formattedDateEnd = dateFormatEndTime.format(endTime);
        String subTitle;
        card.setStartDate(new Date(startDate));
        card.setEndDate(new Date(endTime));
        if (startDate == endTime) {
            subTitle = formattedDateStart.replace("am", "AM").replace("pm", "PM");
        } else {
            subTitle = formattedDateStart + " - " + formattedDateEnd.replace("am", "AM").replace("pm", "PM");
        }
        details.setTitle("Diagnostic Tests");
        details.setSubTitle(subTitle);
        card.setStartDate(new Date(startDate));
        details.setIconImageUrl("/image/chroniccare/WhatsNextDiagnostics.png");
        card.setBookingDetails(details);
        return card;
    }

    private Action getEmailReportAction(BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        Action emailAction = new Action();
        emailAction.setActionType(ActionType.SF_EMAIL_LABREPORTS);
        Map<String, String> emailActionMeta = new HashMap<>();
        emailActionMeta.put("carefitOrderId",
                String.valueOf(diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getBookingId()));
        emailAction.setMeta(emailActionMeta);
        return emailAction;
    }

    private Action getEmailReportActionV2(OrderResponse diagnosticBooking) {
        Action emailAction = new Action();
        emailAction.setActionType(ActionType.SF_EMAIL_LABREPORTS);
        Map<String, String> emailActionMeta = new HashMap<>();
        emailActionMeta.put("carefitOrderId", String.valueOf(diagnosticBooking.getOrderEntry().getBookingId()));
        emailAction.setMeta(emailActionMeta);
        return emailAction;
    }

    private Action getShowInstructionsAction() {
        Action showInstructionAction = new Action();
        showInstructionAction.setActionType(ActionType.SHOW_DIAGNOSTIC_INSTRUCTIONS_MODAL);
        return showInstructionAction;
    }

    private DropdownAction getRescheduleDropdownAction(BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        String addressIdV1 = diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getAtHomeDiagnosticOrder()
                .getAddressMetadata().getAddressId();
        String parentBookingId = diagnosticBooking.getBooking().getId().toString();
        String patientId = diagnosticBooking.getBooking().getPatientId().toString();
        String productId = diagnosticBooking.getBooking().getProductCode();
        String actionUrl = String.format(
                "curefit://selectCareDateV1?patientId=%s&productId=%s&parentBookingId=%s&type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&addressIdV1=%s",
                patientId, productId, parentBookingId, addressIdV1);
        Action atHomeAction = new Action();
        atHomeAction.setTitle("Reschedule");
        atHomeAction.setActionType(ActionType.NAVIGATION);
        atHomeAction.setUrl(actionUrl);
        DropdownAction dropdownAction = new DropdownAction();
        dropdownAction.setValue(atHomeAction);
        dropdownAction.setText("Reschedule");
        return dropdownAction;
    }

    private DropdownAction getRescheduleDropdownActionV2(OrderResponse diagnosticBooking) {
        String addressIdV1 = diagnosticBooking.getOrderEntry().getAddress().getAddressId();
        String parentBookingId = diagnosticBooking.getOrderEntry().getBookingId().toString();
        String productId = diagnosticBooking.getOrderEntry().getProductCodes();
        String actionUrl = String.format(
                "curefit://selectCareDateV1?productId=%s&parentBookingId=%s&type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&addressIdV1=%s&addressId=%s&diagnosticsProvider=EKINCARE&productCodes=%s",
                productId, parentBookingId, addressIdV1, addressIdV1, productId);
        Action atHomeAction = new Action();
        atHomeAction.setTitle("Reschedule");
        atHomeAction.setActionType(ActionType.NAVIGATION);
        atHomeAction.setUrl(actionUrl);
        DropdownAction dropdownAction = new DropdownAction();
        dropdownAction.setValue(atHomeAction);
        dropdownAction.setText("Reschedule");
        return dropdownAction;
    }

    private Action getDiagnosticRescheduleAction(OrderResponse diagnosticBooking) {
        String addressIdV1 = diagnosticBooking.getOrderEntry().getAddress().getAddressId();
        String parentBookingId = diagnosticBooking.getOrderEntry().getBookingId().toString();
        String productId = diagnosticBooking.getOrderEntry().getProductCodes();
        String actionUrl = String.format(
                "curefit://selectCareDateV1?productId=%s&parentBookingId=%s&type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&addressIdV1=%s&addressId=%s&diagnosticsProvider=EKINCARE&productCodes=%s",
                productId, parentBookingId, addressIdV1, addressIdV1, productId);
        Action atHomeAction = new Action();
        atHomeAction.setTitle("Reschedule");
        atHomeAction.setSubTitle("Please reschedule your appointment");
        atHomeAction.setActionType(ActionType.NAVIGATION);
        atHomeAction.setUrl(actionUrl);
        return atHomeAction;
    }

    private StatusType getDiagnosticStatus(BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        DiagnosticsTestOrderResponse diagnosticsTestOrderResponse = diagnosticBooking.getDiagnosticsTestOrderResponse()
                .get(0);
        DiagnosticsTestOrderState bookingStatus = diagnosticsTestOrderResponse.getStatus();
        if (bookingStatus.name().equals("REPORT_GENERATED")) {
            return REPORT_GENERATED;
        } else if (bookingStatus.name().equals("CONFIRMED")) {
            if (diagnosticsTestOrderResponse.getAtHomeDiagnosticOrder() != null) {
                String statusName = diagnosticsTestOrderResponse.getAtHomeDiagnosticOrder().getSubOrderStatus().name();
                if (statusName.equals("SAMPLE_COLLECTED") || statusName.equals("SAMPLE_DISPATCHED")
                        || statusName.equals("SAMPLE_RECEIVED") || statusName.equals("COMPLETED")) {
                    return SAMPLE_COLLECTED;
                }
            } else if (diagnosticsTestOrderResponse.getThirdPartySellerMetaData().getSampleCollectionTime() != null) {
                return SAMPLE_COLLECTED;
            }
        }
        return SCHEDULED;
    }

    private StatusType getDiagnosticStatusV2(OrderResponse diagnosticBooking) {
        OrderStatus bookingStatus = diagnosticBooking.getOrderEntry().getStatus();
        if (bookingStatus.equals(OrderStatus.REPORT_GENERATED)) {
            return REPORT_GENERATED;
        } else if (bookingStatus.equals(OrderStatus.SAMPLE_COLLECTED)
                || bookingStatus.equals(OrderStatus.REPORT_IN_PROGRESS)
                || bookingStatus.equals(OrderStatus.PARTIAL_REPORT_GENERATED)) {
            return SAMPLE_COLLECTED;
        } else if (bookingStatus.equals(OrderStatus.NOSHOW)) {
            return MISSED;
        }
        return SCHEDULED;
    }

    private Action getNavAction(String title, String url, Boolean isEnabeled, ActionType type) {
        Action action = Action.builder()
                .isEnabled(isEnabeled)
                .title(title)
                .actionType(type)
                .url(url)
                .build();
        return action;
    }

    private ActiveCard getConsultationActiveCardV2(UserContext userContext, ActiveConsultationResponse consultation,
            UserEntry user, ActivePackResponse activePackResponse) {
        // HPES For now backend failure handled with general exception
        DoctorResponse doctor = serviceInterfaces.sfAlbusClient.searchDoctorById(consultation.getDoctorId()).get(0);
        log.info(String.format("For user :: %s, consultation id :: %s, doctor Id :: %s, got doctor :: %s",
                user.getId(), consultation.getAppointmentId(), consultation.getDoctorId(), doctor.getName()));
        ActiveCard card = new ActiveCard();
        boolean isDoctorConsult = consultation.getConsultationProduct().getHasPrescription();
        if (isDoctorConsult) {
            // doctor
            card.setTitle("Doctor");
            card.setSubType("DOCTOR");
        } else {
            // non doctor
            card.setTitle("Diabetes Expert");
            card.setSubType("COACH");
        }
        card.setType("CONSULTATION");
        StatusType status = this.getConsultationStatus(consultation);
        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(status);
        cardStatus.setColor(this.getConsultationStatusColor(status));
        card.setStatus(cardStatus);
        List<DropdownAction> actions = new ArrayList<>();
        if (status.equals(UPCOMING) || status.equals(STARTED)) {
            Action joinAction = this.getJoinCallAction(consultation, doctor, user, false);
            card.setPrimaryAction(joinAction);
            if (!joinAction.getIsEnabled() && ChronicCareAppUtil.isFirstDoctorConsultationBooked(activePackResponse, exceptionReportingService)) {
                card.setInfoTitle("UPLOAD PRESCRIPTION");
                card.setInstruction("Upload any outside diagnostic report or external prescriptions for doctor's reference");
                card.setTertiaryAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfreportspage").pageFrom("chroniccarehomepage").build());
                card.setTertiaryActionBackground("image/chroniccare/generic_banner.png");
                card.setDarkTertiaryBackground(true);

                if (card.getPrimaryAction().getIsEnabled()) {
                    card.setShowInfoWithCta(true);
                    card.getPrimaryAction().setSubTitle(null);
                }
            } else {
                card.setInfoTitle("MORE INFO");
                card.setInstruction("A link to join the call will be shown here 30 mins before the call.");
            }
        } else if (status.equals(MISSED)) {
            card.setPrimaryAction(this.getRescheduleCallAction(consultation,
                    consultation.getConsultationProduct().getHasPrescription(), false));
        } else if (status.equals(COMPLETED)) {
            if (isDoctorConsult) {
                card.setInfoTitle("EMAIL PRESCRIPTION");
                card.setTertiaryAction(getEmailPrescriptionAction(consultation));
            } else {
                card.setInfoTitle("MORE INFO");
            }
            card.setInstruction(consultation.getConsultationProduct().getHasPrescription()
                    ? "Your prescription has been sent to your registered email ID."
                    : "Your nutrition, fitness and intervention plan has been updated on the app.");
        }
        if (consultation.getAppointmentActionsWithContext().getCancelActionWithContext().getAction()
                .isActionPermitted()) {
            DropdownAction cancelDropdownAction = new DropdownAction();
            cancelDropdownAction.setText("Cancel Booking");
            Action cancelAction = new Action();

            if(ChronicCareAppUtil.isCGMDplusIEnabled(userContext) && ChronicCareServiceHelper.isFirstCoachConsultationBooked(activePackResponse, exceptionReportingService)) {
//                Welcome Call
                Action wcCancelAction = chronicCareServiceHelper.getConsultCancellationAction(consultation, "Cancel Booking");
                Action wcRescheduleAction = chronicCareServiceHelper.getConsultRescheduleAction(consultation, "Reschedule");

//                Dropdown consultation cancel action
                cancelAction = chronicCareServiceHelper.getWelcomeCallCancellationAction("Cancel Booking", wcRescheduleAction, wcCancelAction);
            } else {
                cancelAction.setActionType(ActionType.CANCEL_TC);
                CancelActionMeta cancelActionMeta = new CancelActionMeta();
                cancelActionMeta.setTcBookingId(consultation.getBookingId());
                cancelAction.setMeta(cancelActionMeta);
            }
            cancelDropdownAction.setValue(cancelAction);
            actions.add(cancelDropdownAction);
        }
        if (consultation.getAppointmentActionsWithContext().getRescheduleActionWithContext().getAction()
                .isActionPermitted() && ChronicCareAppUtil.isRescheduleConsultationSupported(userContext)) {
            actions.add(this.getConsultationRescheduleAction(consultation));
        }
        if (ChronicCareAppUtil.isCsTicketSupportedVersion(userContext)) {
            DropdownAction dropdownAction = new DropdownAction();
            dropdownAction.setText("Contact helpdesk");
            Action action = Action.builder().actionType(ActionType.NAVIGATION)
                    .url("curefit://sfsupportpage").title("Contact helpdesk").build();
            dropdownAction.setValue(action);
            actions.add(dropdownAction);
        }
        card.setSecondaryActions(actions);
        ActiveCardDetails details = new ActiveCardDetails();
        details.setTitle("Call with " + doctor.getName());
        Date startTime = new Date(consultation.getStartDate());
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, h:mm aa");
        dateFormat.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        String formattedDate = dateFormat.format(startTime).replace("am", "AM").replace("pm", "PM");
        details.setSubTitle(formattedDate);
        card.setStartDate(new Date(consultation.getStartDate()));
        card.setEndDate(new Date(consultation.getStartDate() + consultation.getConsultationProduct().getDuration()));
        details.setIconImageUrl(doctor.getDisplayImage());
        card.setBookingDetails(details);
        log.debug(String.format("For user :: %s, consultation :: %s, created card :: %s", user.getId(), consultation.getAppointmentId(), card));
        return card;
    }

    @Deprecated(forRemoval = true)
    private ActiveCard getCGMSelfInstallationConsultationActiveCard(UserContext userContext, ActiveConsultationResponse consultation,
                                                   UserEntry user) {
        // HPES For now backend failure handled with general exception
        DoctorResponse phlebo = serviceInterfaces.sfAlbusClient.searchDoctorById(consultation.getDoctorId()).get(0);
        log.info(String.format("For user :: %s, consultation id :: %s, doctor Id :: %s, got doctor :: %s",
                user.getId(), consultation.getAppointmentId(), consultation.getDoctorId(), phlebo.getName()));
        ActiveCard card = new ActiveCard();
        card.setTitle("CGM Video Installation");
        card.setType("CONSULTATION");
        card.setSubType("CGM_INSTALLATION");
        StatusType status = getConsultationStatus(consultation);
        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(status);
        cardStatus.setColor(getConsultationStatusColor(status));
        card.setStatus(cardStatus);
        List<DropdownAction> actions = new ArrayList<>();

//        card.setInfoTitle("INSTRUCTIONS");
        card.setInstructionImageUrl("/image/chroniccare/active_card_play_btn.png");
        card.setInstruction("Click here to watch Installation video for reference");
        Action watchVideoAction = chronicCareServiceHelper.getWatchCGMInstallationVideoAction("", false, true);
        card.setTertiaryAction(watchVideoAction);

        ActiveCardDetails details = new ActiveCardDetails();
        Date startTime = new Date(consultation.getStartDate());
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, h:mm aa");
        dateFormat.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        String formattedDate = dateFormat.format(startTime).replace("am", "AM").replace("pm", "PM");
        details.setTitle("CGM Video Installation");
        details.setSubTitle(formattedDate);
        details.setIconImageUrl("image/chroniccare/active_card_cgm_logo.png");

        if (status.equals(UPCOMING) || status.equals(STARTED)) {
//            CGM Installation Consultation -> Started or Upcoming
            Action joinAction = this.getJoinCallAction(consultation, phlebo, user, true);
            card.setPrimaryAction(joinAction);
        } else if (status.equals(MISSED)) {
//            CGM InstallationConsultation -> Missed
            card.setPrimaryAction(this.getRescheduleCallAction(consultation,
                    consultation.getConsultationProduct().getHasPrescription(), true));
        }

        if (consultation.getAppointmentActionsWithContext().getCancelActionWithContext().getAction()
                .isActionPermitted()) {
            DropdownAction cancelDropdownAction = new DropdownAction();
            cancelDropdownAction.setText("Cancel Booking");
            Action cancelAction = new Action();
            cancelAction.setActionType(ActionType.CANCEL_TC);
            CancelActionMeta cancelActionMeta = new CancelActionMeta();
            cancelActionMeta.setTcBookingId(consultation.getBookingId());
            cancelAction.setMeta(cancelActionMeta);
            cancelDropdownAction.setValue(cancelAction);
            actions.add(cancelDropdownAction);
        }
        if (consultation.getAppointmentActionsWithContext().getRescheduleActionWithContext().getAction()
                .isActionPermitted() && ChronicCareAppUtil.isRescheduleConsultationSupported(userContext)) {
            actions.add(this.getConsultationRescheduleAction(consultation));
        }
        if (ChronicCareAppUtil.isCsTicketSupportedVersion(userContext)) {
            DropdownAction dropdownAction = new DropdownAction();
            dropdownAction.setText("Contact helpdesk");
            dropdownAction.setValue(Action.builder().actionType(ActionType.NAVIGATION)
                    .url("curefit://sfsupportpage").title("Contact helpdesk").build());
            actions.add(dropdownAction);
        }
        card.setSecondaryActions(actions);
        card.setStartDate(new Date(consultation.getStartDate()));
        card.setEndDate(new Date(consultation.getStartDate() + consultation.getConsultationProduct().getDuration()));
        card.setBookingDetails(details);
         if (Objects.nonNull(card.getPrimaryAction()) && Objects.nonNull(card.getPrimaryAction().getIsEnabled()) && card.getPrimaryAction().getIsEnabled()) {
            card.setShowInfoWithCta(true);
        }
        log.debug(String.format("For user :: %s, consultation :: %s, created card :: %s", user.getId(),
                consultation.getAppointmentId(), card));
        return card;
    }

    private void setFaceBasedVitalScanData(ActiveCard card, UserContext userContext,
            ActiveConsultationResponse consultation, Action joinAction) {
        try {
            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            Date currentTime = calendar.getTime();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone userTimeZone = TimeZone.getTimeZone(userContext.getUserProfile().getTimezone());
            FaceBasedVitalLogsResponse fbvResponse = chsClient.getLastScanFaceBasedVitalLogs(userId, userTimeZone);

            // show scan if user has never scanned or not scanned in last two hour before
            // consultation
            if (currentTime.after(new Date(consultation.getStartDate() - 60 * 60 * 1000)) && (fbvResponse == null
                    || fbvResponse.getTimeStamp().before(new Date(consultation.getStartDate() - 2 * 60 * 60 * 1000)))) {
                card.setInfoTitle("GET YOUR VITALS");
                card.setPrimaryActionInstruction(
                        !joinAction.getIsEnabled() ? "Link to join will be shared 30 mins before the call." : null);
                card.setInstruction(joinAction.getIsEnabled()
                        ? "Scan your vitals before your consult"
                        : "Scan your vitals before your consult for an even more efficient experience");
                card.setTertiaryAction(new Action("curefit://sfscanfacepage", "SCAN", ActionType.NAVIGATION));
                card.setTertiaryActionBackground("image/chroniccare/face_scan_home_background.png");
                card.setDarkTertiaryBackground(true);

                if (card.getPrimaryAction().getIsEnabled()) {
                    card.setShowInfoWithCta(true);
                    card.getPrimaryAction().setSubTitle(null);
                }
            } else {
                card.setInfoTitle("MORE INFO");
                card.setInstruction("A link to join the call will be shown here 30 mins before the call.");
            }
        } catch (Exception e) {
            card.setInfoTitle("MORE INFO");
            card.setInstruction("A link to join the call will be shown here 30 mins before the call.");
        }
    }

    private Action getEmailPrescriptionAction(ActiveConsultationResponse consultation) {
        Action emailAction = new Action();
        emailAction.setActionType(ActionType.SF_EMAIL_PRESCRIPTION);
        Map<String, String> emailActionMeta = new HashMap<>();
        emailActionMeta.put("bookingId", consultation.getBookingId().toString());
        emailActionMeta.put("consultationId", consultation.getAppointmentId().toString());
        emailAction.setMeta(emailActionMeta);
        return emailAction;
    }

    private DropdownAction getConsultationRescheduleAction(ActiveConsultationResponse consultation) {
        TeleconsultationManageOptionsMeta meta = new TeleconsultationManageOptionsMeta();
        meta.setTcBookingId(consultation.getBookingId());
        meta.setProductId(consultation.getProductCode());
        meta.setParentBookingId(consultation.getBookingId());
        meta.setPatientId(consultation.getPatientId());
        meta.setDoctorId(consultation.getDoctorId());

        Action rescheduleAction = new Action();
        rescheduleAction.setIcon(ActionIcon.RESCHEDULE);
        rescheduleAction.setTitle("Reschedule");
        rescheduleAction.setActionId(ActionId.RESCHEDULE);
        rescheduleAction.setActionType(ActionType.RESCHEDULE_TC);
        rescheduleAction.setMeta(meta);

        DropdownAction dropdownAction = new DropdownAction();
        dropdownAction.setText("Reschedule");
        dropdownAction.setValue(rescheduleAction);
        return dropdownAction;
    }

    ;

    public static String getConsultationStatusColor(StatusType status) {
        if (status.equals(MISSED) || status.equals(CANCELLED)) {
            return "YELLOW";
        } else if (status.equals(UPCOMING) || status.equals(STARTED)) {
            return "GREEN";
        } else {
            return "BLUE";
        }
    }

    private String getDiagnosticsStatusColor(StatusType status) {
        if (status.equals(SCHEDULED)) {
            return "GREEN";
        } else {
            return "BLUE";
        }
    }

    private Action getJoinCallAction(ActiveConsultationResponse consultation, DoctorResponse doctor, UserEntry user, Boolean isPhleboConsultation) {
        AppointmentActionsWithContext appointmentActionsWithContext = consultation.getAppointmentActionsWithContext();
        Boolean isVideoCallEnabled = (appointmentActionsWithContext.getVideoActionWithContext() != null)
                && appointmentActionsWithContext.getVideoActionWithContext().getAction().isActionPermitted();

        if (isVideoCallEnabled) {
            Action action = new Action();
            action.setTitle("JOIN CALL");
            if(!isPhleboConsultation) {
                if (consultation.isHasPrescription()) {
                    action.setSubTitle("Please join the call 2 mins before the start time");
                } else {
                    action.setSubTitle("In-case of NO-SHOW you won't be able to book any session for 3 days");
                }
            }
            String channel = appointmentActionsWithContext.getVideoActionWithContext().getContext()
                    .getTwilioCommunicationMode().getModeName();
            String modeSid = appointmentActionsWithContext.getVideoActionWithContext().getContext().getTwilioCommunicationMode().getModeSid();
            String modeName = appointmentActionsWithContext.getVideoActionWithContext().getContext().getTwilioCommunicationMode().getModeName();
            String chatChannel = null;
            action.setUrl("curefit://videochat?pageFrom=today&appointmentId=" + consultation.getAppointmentId()
                    + "&bookingId=" + consultation.getBookingId() + "&identity=Patient-" + consultation.getPatientId()
                    + "&docName=" + doctor.getName() + "&docImage=" + doctor.getDisplayImage() + "&userImage="
                    + user.getProfilePictureUrl() + "&doctorId=" + consultation.getDoctorId() + "&channel=" + channel + "&modeSid=" + modeSid + "&modeName=" + modeName
                    + "&chatChannel=" + chatChannel + "&enableLog=" + String.valueOf(true) + "&tenant=" + TENANT);
            action.setActionType(ActionType.TC_JOIN_CALL);
            action.setIsEnabled(true);
            return action;
        } else {
            Action toastAction = this
                    .getDisabledActionForToastMessage("You can join call 30 minutes before your slot time.");
            toastAction.setTitle("JOIN CALL");
            toastAction.setIsEnabled(false);
            return toastAction;
        }
    }

    private Action getRescheduleCallAction(ActiveConsultationResponse consultation, Boolean isDoctor, Boolean isPhleboConsultation) {
        TeleconsultationManageOptionsMeta meta = new TeleconsultationManageOptionsMeta();
        meta.setTcBookingId(consultation.getBookingId());
        meta.setProductId(consultation.getProductCode());
        meta.setParentBookingId(consultation.getBookingId());
        meta.setPatientId(consultation.getPatientId());
        meta.setDoctorId(consultation.getDoctorId());
        Action rescheduleAction = new Action();
        rescheduleAction.setIcon(ActionIcon.RESCHEDULE);
        rescheduleAction.setTitle("RESCHEDULE");
        if(isPhleboConsultation) {
            rescheduleAction.setSubTitle("Please reschedule your appointment for CGM Installation.");
        } else {
            rescheduleAction.setSubTitle(isDoctor ? "Please reschedule your appointment with your doctor."
                    : "Please reschedule your appointment with your coach.");
        }
        rescheduleAction.setActionId(ActionId.RESCHEDULE);
        rescheduleAction.setActionType(ActionType.RESCHEDULE_TC);
        rescheduleAction.setMeta(meta);
        return rescheduleAction;
    }

    public static StatusType getConsultationStatus(ActiveConsultationResponse consultation) {
        if (MISSED_CONSULTATION_STATES.contains(consultation.getStatus())) {
            return MISSED;
        } else if (COMPLETED_CONSULTATION_STATES.contains(consultation.getStatus())) {
            return COMPLETED;
        } else if (UPCOMING_CONSULTATION_STATES.contains(consultation.getStatus())) {
            return UPCOMING;
        } else if (STARTED_CONSULTATION_STATES.contains(consultation.getStatus())) {
            return STARTED;
        } else if (CANCELLED_CONSULTATION_STATES.contains(consultation.getStatus())) {
            return CANCELLED;
        }
        return UPCOMING;
    }

    private List<ActiveCard> getLiveActiveCards(UserContext userContext) {
        try {
            String userId = userContext.getUserProfile().getUserId();
            Tenant tenant = Tenant.fromString(AppUtil.getTenantFromUserContext(userContext).toString());
            String countryId = userContext.getUserProfile().getCity().getCountryId();
            DIYFilterRequestV2 diyFilterRequestV2 = DIYFilterRequestV2.builder()
                    .preferredStreamType(PreferredStreamType.VIDEO_CALL).build();
            List<LiveClass> liveClasses = serviceInterfaces.getDiyfsService().getUpcomingClassesByFilters(5, 0, tenant,
                    diyFilterRequestV2, userId, countryId).get();

            List<LiveClass> bookedLiveClasses = liveClasses.parallelStream().filter(
                    liveClass -> liveClass.getSlots().get(0).getSubscriptionStatus() == SubscriptionStatus.SUBSCRIBED)
                    .toList();

            return bookedLiveClasses.parallelStream().map(liveClass -> getUpcomingSession(liveClass, userContext))
                    .toList();
        } catch (Exception e) {
            String msg = String.format("Error on fetching live classes for user :: %s",
                    userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
            return new ArrayList<>();
        }
    }

    private ActiveCard getUpcomingSession(LiveClass session, UserContext userContext) {
        ActiveCard liveClassCard = new ActiveCard();

        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(SCHEDULED);
        cardStatus.setColor("GREEN");

        String title = "JOIN CLASS";
        Action sessionAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(session.getLiveClassId(),
                session.getSlots().get(0).getClassId(), "now_live_widget", title, userContext,
                session.getContentCategory());
        Action joinAction = LiveUtil.getLiveInteractiveSessionRestApiAction(userContext, session.getLiveClassId());
        Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
        if (session.getScheduledTimeEpoch() <= calendar.getTime().getTime()) {
            joinAction.setIsEnabled(true);
            joinAction.setTitle(title);
            joinAction.setSubTitle("Keep track of your workout energy and postures by keeping your video on");
        } else {
            sessionAction.setIsEnabled(true);
        }

        ActiveCardDetails details = new ActiveCardDetails();
        details.setTitle(session.getTitle());

        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("MMM dd, h:mm");
        dateFormatStartTime.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        long startDate = session.getScheduledTimeEpoch();
        String formattedDateStart = dateFormatStartTime.format(startDate);
        SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
        dateFormatEndTime.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        long endTime = session.getScheduledTimeEpoch() + session.getDuration();
        String formattedDateEnd = dateFormatEndTime.format(endTime);
        String subTitle;
        if (startDate == endTime) {
            subTitle = formattedDateStart.replace("am", "AM").replace("pm", "PM");
        } else {
            subTitle = formattedDateStart + " - " + formattedDateEnd.replace("am", "AM").replace("pm", "PM");
        }
        details.setSubTitle(subTitle);

        liveClassCard.setPrimaryAction(joinAction);
        liveClassCard.setStatus(cardStatus);
        liveClassCard.setSubType("LIVE_CLASS");
        liveClassCard.setInfoTitle("MORE INFO");
        liveClassCard.setTertiaryAction(sessionAction);
        liveClassCard.setInstruction("Keep track of your workout energy and postures by keeping your video on");
        liveClassCard.setStartDate(new Date(session.getScheduledTimeEpoch()));
        liveClassCard.setSecondaryActions(getLiveClassDropDownActions(session, userContext));
        details.setIconImageUrl(LiveUtil.getImage(session.getBannerImages(), userContext.getSessionInfo(), 1L));
        liveClassCard.setBookingDetails(details);
        return liveClassCard;
    }

    private List<DropdownAction> getLiveClassDropDownActions(LiveClass session, UserContext userContext) {
        List<DropdownAction> actions = new ArrayList<>();
        DropdownAction cancelDropdownAction = new DropdownAction();
        cancelDropdownAction.setText("Cancel Booking");
        Action cancelAction = new Action();
        cancelAction.setActionType(ActionType.VIDEO_SUBSCRIBE);
        VideoSubscribeUnSubscribeActionMeta cancelActionMeta = new VideoSubscribeUnSubscribeActionMeta();
        cancelActionMeta.setSubscriptionType("VIDEO");
        cancelActionMeta.setStatus("UNSUBSCRIBED");
        cancelActionMeta.setCatalogueEntryId(session.getLiveClassId());
        cancelActionMeta.setSuccessMessage("Your class has been cancelled successfully");

        String startDate = TimeUtil.getTimeInFormatFromMillis(session.getScheduledTimeEpoch(),
                "yyyy-MM-dd'T'HH:mm:ss.SSSZ", userContext.getUserProfile().getTimezone());
        String endDate = TimeUtil.getTimeInFormatFromMillis(session.getScheduledTimeEpoch() + session.getDuration(),
                "yyyy-MM-dd'T'HH:mm:ss.SSSZ", userContext.getUserProfile().getTimezone());
        Action liveCalenderEventCancelAction = new Action();
        liveCalenderEventCancelAction.setPayload(
                new LiveCalenderEvenetCancelPayload(session.getLiveClassId(), session.getTitle(), startDate, endDate));
        liveCalenderEventCancelAction.setActionType(ActionType.DELETE_CALENDAR_EVENT);

        cancelAction.setMeta(cancelActionMeta);
        cancelDropdownAction.setValue(cancelAction);
        actions.add(cancelDropdownAction);

        return actions;
    }

    public CompletableFuture<UserOnboardingActionWithContext> getUserOnboardingActions(
            ServiceInterfaces serviceInterfaces, UserContext userContext,
            Optional<ActivePackResponse> activePackResponse) throws ResourceNotFoundException {
        return supplyAsync(() -> {
            try {
                String userId = userContext.getUserProfile().getUserId();
                return userOnboardingService.getSugarFitUserOnboardingStatus(userId, activePackResponse.orElse(null));
            } catch (ResourceNotFoundException | NullPointerException e) {
                log.info(String.format("No active subscription for user"));
                exceptionReportingService.reportException("Exception in getUserOboardingActions", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<RagusMigrationDisclaimer> ragusMigrationDisclaimer(ServiceInterfaces serviceInterfaces,
            UserContext userContext, Optional<ActivePackResponse> activePackResponse) {
        return supplyAsync(() -> {
            try {
                if (null != activePackResponse && activePackResponse.isPresent()) {
                    Long ragusMigrationDate = 1637519400000L; // 2021-11-22
                    if (activePackResponse.get().getStartDate() > ragusMigrationDate) {
                        return RagusMigrationDisclaimer.DONT_SHOW_DISCLAIMER;
                    }
                }
                String attributeName = "sf-ragus-migration-disclaimer";
                Long userId = Long.parseLong(userContext.getUserProfile().getUserId());
                UserAttributesResponse userAttributesResponse = userAttributesClient.getAttributes(userId,
                        attributeName, AppUtil.getAppTenantFromUserContext(userContext), null);
                if (null == userAttributesResponse || null == userAttributesResponse.getAttributes()
                        || !userAttributesResponse.getAttributes().containsKey(attributeName)) {
                    return RagusMigrationDisclaimer.SHOW_DISCLAIMER;
                }
                String value = (String) userAttributesResponse.getAttributes().get(attributeName);
                return (!"ACCEPTED".equals(value)) ? RagusMigrationDisclaimer.SHOW_DISCLAIMER
                        : RagusMigrationDisclaimer.DONT_SHOW_DISCLAIMER;
            } catch (NullPointerException | BaseException e) {
                log.info(String.format("Exception in fetching user attributes for userId %s",
                        userContext.getUserProfile().getUserId()));
                exceptionReportingService.reportException("Exception in fetching user attributes", e);
            }
            return RagusMigrationDisclaimer.SHOW_DISCLAIMER;
        }, serviceInterfaces.getTaskExecutor());
    }

    private BaseWidgetNonVM getPrePurchaseWidget(UserContext userContext) {
        if (AppUtil.isUltraFitApp(userContext)) {
            return new UfPrePurchaseWidget();
        } else {
            PrePurchaseDiabetesTypeWidget widget = new PrePurchaseDiabetesTypeWidget();
            widget.setTitle("What type of diabetes do you have?");
            widget.setTypes(Arrays.asList(TYPE_2_DIABETES_PACK, PRE_DIABETIC_PACK));
            return widget;
        }
    }

    private CongratulationsWidget getPackPurchaseGreetingWidget(UserContext userContext) {
        boolean isUltraFit = AppUtil.isUltraFitApp(userContext);
        CongratulationsWidget congratsWidget = new CongratulationsWidget();
        String subTitle = isUltraFit
                ? "Taking the first step is the toughest. Let us now help you manage your condition and bring out the best in you!"
                : "Taking the first step is the toughest. Let us now help you manage your condition and bring out the best in you!";
        congratsWidget.setCreativeType("BLUE");
        congratsWidget.setTitle("Congratulations!");
        congratsWidget.setSubTitle(subTitle);

        if (isSugarFitApp(userContext) && isSfNuxV2SupportedAppVersion(userContext)) {
            congratsWidget.setBottomAction(Action.builder().title("START YOUR JOURNEY").actionType(ActionType.NAVIGATION)
                    .navigationType(NavigationType.NAVIGATE_REPLACE)
                    .url("curefit://nuxpreferences").build());
        } else {
            congratsWidget.setBottomAction(Action.builder().title("START YOUR JOURNEY").actionType(ActionType.NAVIGATION)
                    .url("curefit://preferenceform?pageNumber=1").build());
        }
        return congratsWidget;
    }

    public String chgetAgentDatePickerUrl(String productCode, Long centerId, Long agentId,
            ActivePackResponse activePackResponse) {
        StringBuilder actionBuilder = new StringBuilder().append("curefit://selectCareDateV1?productId=")
                .append(productCode).append("&centerId=")
                .append(centerId).append("&isExternal=true").append("&forceCurrentCenter=true&doctorId=")
                .append(agentId).append("&patientId=")
                .append(activePackResponse.getPatientId());
        Optional<UserMembershipInfo> membershipInfo = activePackResponse.getUserMembershipInfos().stream()
                .filter(x -> productCode.equals(x.getProductCode())).findFirst();
        if (membershipInfo.isPresent()
                && membershipInfo.get().getTicketsConsumed() < membershipInfo.get().getTickets()) {
            actionBuilder.append("&parentBookingId=").append(activePackResponse.getBookingId());
        }
        return actionBuilder.toString();
    }

    private Action getDisabledActionForToastMessage(String toastMessage) {
        return Action.builder()
                .isEnabled(false)
                .title("Book Coach Consult")
                .actionType(ActionType.SHOW_TOAST_MESSAGE)
                .meta(ToastMessageMeta.builder().message(toastMessage).build())
                .build();
    }

    private WhatNextWidget getWhatNextWidget(UserContext userContext, UserOnboardingActionWithContext context,
            ActivePackResponse activePackResponse, PatientDetail patientDetail)
            throws ResourceNotFoundException, NullPointerException {
        CardData coachCardData = null, doctorCardData = null, diagnosticCardData = null, agmCardData = null,
                cgmInstallationCard = null;
        List<CardData> foldedCards = new ArrayList<>();
        CardData topCard = null;
        ChronicCareTeam assignedCareTeam = getAssignedCareTeam(userContext, patientDetail.getId());
        // if (context.getCoachCardActionWithContext().getAction().isActionPermitted())
        // {
        coachCardData = getCoachCardData(context, assignedCareTeam, patientDetail, activePackResponse);
        foldedCards.add(coachCardData);
        // }
        // if (context.getAGMDataActionWithContext().getAction().isActionPermitted()) {
        agmCardData = getAGMCardData(context, activePackResponse);
        if (agmCardData.isEnabled()) {
            foldedCards.add(agmCardData);
        }
        // }

        if (!chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct())) {
            // if (context.getDoctorCardActionWithContext().getAction().isActionPermitted())
            // {
            try {
                doctorCardData = getDoctorCardData(userContext, context, assignedCareTeam, activePackResponse);
            } catch (Exception e) {
                log.error("CCHomePage: Exception in getDoctorCardData for user :: {}, e :: {}",
                        userContext.getUserProfile().getUserId(), e.getMessage(), e);
            }
            foldedCards.add(doctorCardData);
        }
        // }

        // filtering out non-show cards
        while (!foldedCards.isEmpty()) {
            CardData removedCard = foldedCards.remove(0);
            if (removedCard.isEnabled()) {
                topCard = removedCard;
                break;
            }
        }
        if (topCard == null) {
            return null;
        }
        WhatNextCardData whatNextCardData = WhatNextCardData.builder()
                .doctorCardData(doctorCardData)
                .coachCardData(coachCardData)
                .diagnosticCardData(diagnosticCardData)
                .AGMCardData(agmCardData)
                .showCard(true)
                .topCard(topCard)
                .foldedCards(foldedCards)
                .widgetTitle("What's Next").expandedViewTitle("Next Steps")
                .build();
        WhatNextWidget whatNextWidget = new WhatNextWidget();
        whatNextWidget.setWhatNextCardData(whatNextCardData);
        return whatNextWidget;
    }

    private WhatNextWidgetV2 getWhatNextWidgetV2(UserContext userContext, UserOnboardingActionWithContext context,
            ActivePackResponse activePackResponse, PatientDetail patientDetail) throws NullPointerException {
        CardData coachCardData = null, doctorCardData = null, diagnosticCardData = null;
        List<CardData> foldedCards = new ArrayList<>();
        boolean isDoctorConsultBooked = !context.getDoctorCardActionWithContext().getAction().isShowCard();
        boolean isQuarterlyTest = context.getDiagnosticCardActionWithContext().getAction()
                .getQuarterlyDiagnosticAllowed();
        boolean isFirstDoctorConsultDone = context.getDoctorCardActionWithContext().getAction().isFirstConsultDone();
        boolean isRenewalProduct = chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct());

        ChronicCareTeam assignedCareTeam = getAssignedCareTeam(userContext, patientDetail.getId());

        coachCardData = getCoachCardDataV2(userContext, context, assignedCareTeam, activePackResponse);
        if (context.getCoachCardActionWithContext().getAction().isActionPermitted() || !isFirstDoctorConsultDone) {
            foldedCards.add(coachCardData);
        }

        diagnosticCardData = getDiagnosticCardDataV2(userContext, context, patientDetail);
        if (!isUltraFitApp(userContext) &&
                (context.getDiagnosticCardActionWithContext().getAction().isActionPermitted()
                        || !isFirstDoctorConsultDone)
                &&
                !CollectionUtils.isEmpty(context.getDiagnosticCardActionWithContext().getContext().getProductCodes())) {
            foldedCards.add(diagnosticCardData);
        }

        if (isDoctorConsultBooked && diagnosticCardData.isCompleted() && coachCardData.isCompleted()) {
            return null;
        }

        if (!isUltraFitApp(userContext) && (!isRenewalProduct && (!isFirstDoctorConsultDone ||
                context.getDoctorCardActionWithContext().getAction().isShowCard()))) {
            try {
                doctorCardData = getDoctorCardDataV2(context, assignedCareTeam, activePackResponse, userContext);
            } catch (Exception e) {
                log.error("CCHomePage: Exception in getDoctorCardData for user :: {}, e :: {}",
                        userContext.getUserProfile().getUserId(), e.getMessage(), e);
            }
            foldedCards.add(doctorCardData);
        }

        WhatNextCardData whatNextCardData = WhatNextCardData.builder()
                .foldedCards(foldedCards)
                .widgetTitle(isQuarterlyTest || isFirstDoctorConsultDone || isRenewalProduct
                        ? "Continue your journey"
                        : "Kickstart your journey")
                .build();
        WhatNextWidgetV2 whatNextWidget = new WhatNextWidgetV2();
        whatNextWidget.setWhatNextCardData(whatNextCardData);
        return whatNextWidget;
    }


    private WhatNextWidgetV3 getWhatNextWidgetV3(UserContext userContext, UserOnboardingActionWithContext context,
                                                 ActivePackResponse activePackResponse, PatientDetail patientDetail) throws NullPointerException {
        try {
            WhatNextWidgetV3 widget = new WhatNextWidgetV3();
            CardData coachCardData = null, doctorCardData = null, diagnosticCardData = null, cgmCardData = null;
            List<CardData> cardsData = new ArrayList<>();
            boolean isDoctorConsultBooked = !context.getDoctorCardActionWithContext().getAction().isShowCard();
            boolean isQuarterlyTest = context.getDiagnosticCardActionWithContext().getAction()
                    .getQuarterlyDiagnosticAllowed();
            boolean isFirstDoctorConsultDone = context.getDoctorCardActionWithContext().getAction().isFirstConsultDone();
            boolean isRenewalProduct = chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct());
            boolean isCGMInstallationBookingAllowed = chronicCareServiceHelper.isCGMInstallationBookingAllowed(context);
            ChronicCareTeam assignedCareTeam = getAssignedCareTeam(userContext, patientDetail.getId());

            coachCardData = getCoachCardDataV2(userContext, context, assignedCareTeam, activePackResponse);
            if (context.getCoachCardActionWithContext().getAction().isActionPermitted() || !isFirstDoctorConsultDone) {
                cardsData.add(coachCardData);
            }

            if(!isUltraFitApp(userContext) && isCGMInstallationBookingAllowed) {
                try {
                    cgmCardData = getCGMInstallationCardDataV2(userContext, context);
                } catch (Exception e) {
                    log.error("CCHomePage: Exception in getCGMInstallationCardDataV2 for user :: {}, e :: {}", userContext.getUserProfile().getUserId(), e.getMessage(), e);
                }
                if(cgmCardData != null) {
                    cardsData.add(cgmCardData);
                }
            }

            diagnosticCardData = getDiagnosticCardDataV2(userContext, context, patientDetail);
            if (!isUltraFitApp(userContext) &&
                    (context.getDiagnosticCardActionWithContext().getAction().isActionPermitted()
                            || !isFirstDoctorConsultDone)
                    &&
                    !CollectionUtils.isEmpty(context.getDiagnosticCardActionWithContext().getContext().getProductCodes())) {
                cardsData.add(diagnosticCardData);
            }

            if (isDoctorConsultBooked && diagnosticCardData.isCompleted() && coachCardData.isCompleted()) {
                return null;
            }

            if (!isUltraFitApp(userContext) && (!isRenewalProduct && (!isFirstDoctorConsultDone ||
                    context.getDoctorCardActionWithContext().getAction().isShowCard()))) {
                try {
                    doctorCardData = getDoctorCardDataV2(context, assignedCareTeam, activePackResponse, userContext);
                } catch (Exception e) {
                    log.error("CCHomePage: Exception in getDoctorCardData for user :: {}, e :: {}",
                            userContext.getUserProfile().getUserId(), e.getMessage(), e);
                }
                cardsData.add(doctorCardData);
            }

            widget.setWidgetTitle(isQuarterlyTest || isFirstDoctorConsultDone || isRenewalProduct
                    ? "Continue your journey"
                    : "Kickstart your reversal journey");

            widget.setCardRegularTitle("tasks pending");
            widget.setCardsData(cardsData);
            int tasksPending = cardsData.stream().filter(el -> !el.isCompleted()).collect(Collectors.toList()).size();
            int totalTasks = cardsData.size();
            widget.setCardBoldTitle(tasksPending+" out of "+totalTasks);
            if(tasksPending == 0) {
                return null;
            }
            return widget;
        } catch (Exception e) {
            log.error("Exception in getting Nux Journey Card"+e.getMessage());
        }
        return null;
    }

    private UfMiniConsultationCardsWidget getMiniConsultationCardsWidget(UserContext userContext,
            UserOnboardingActionWithContext context, ActivePackResponse activePackResponse, PatientDetail patientDetail)
            throws ResourceNotFoundException, NullPointerException {
        CardData coachCardData = null, doctorCardData = null;
        List<CardData> foldedCards = new ArrayList<>();
        boolean isDoctorConsultBooked = !context.getDoctorCardActionWithContext().getAction().isShowCard();
        boolean isFirstDoctorConsultDone = context.getDoctorCardActionWithContext().getAction().isFirstConsultDone();
        boolean isFirstCoachConsultDone = context.getCoachCardActionWithContext().getAction().isFirstConsultDone();
        boolean isRenewalProduct = chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct());

        ChronicCareTeam assignedCareTeam = getAssignedCareTeam(userContext, patientDetail.getId());

        if (isDoctorConsultBooked && !context.getCoachCardActionWithContext().getAction().isActionPermitted()) {
            return null;
        }

        if ((!isRenewalProduct && (!isFirstDoctorConsultDone ||
                context.getDoctorCardActionWithContext().getAction().isShowCard())) &&
                context.getDoctorCardActionWithContext().getAction().isActionPermitted()) {
            try {
                doctorCardData = getDoctorCardDataV2(context, assignedCareTeam, activePackResponse, userContext);
                doctorCardData.setCollapsedMessage("Your Doctor");
                doctorCardData.getAction().setTitle("CONSULT");
            } catch (Exception e) {
                log.error("CCHomePage: Exception in getDoctorCardData for user :: {}, e :: {}",
                        userContext.getUserProfile().getUserId(), e.getMessage(), e);
            }
            foldedCards.add(doctorCardData);
        }

        coachCardData = getCoachCardDataV2(userContext, context, assignedCareTeam, activePackResponse);
        if (context.getCoachCardActionWithContext().getAction().isActionPermitted() || !isFirstCoachConsultDone) {
            coachCardData.setCollapsedMessage("Metabolic Expert");
            coachCardData.getAction().setTitle("CONSULT");
            foldedCards.add(coachCardData);
        }

        UfMiniConsultationCardsWidget miniConsultationCardsWidget = new UfMiniConsultationCardsWidget();
        miniConsultationCardsWidget.setConsultationCards(foldedCards);
        return miniConsultationCardsWidget;
    }

    public Boolean isOnboardingCompletionShown(UserContext userContext) {
        try {
            UserAttributesResponse userAttributesResponse = userAttributesClient.getAttributes(
                    Long.parseLong(userContext.getUserProfile().getUserId()), "sf-onboarding-completion-msg-shown",
                    AppUtil.getAppTenantFromUserContext(userContext), null);
            if (null == userAttributesResponse || null == userAttributesResponse.getAttributes()
                    || !userAttributesResponse.getAttributes().containsKey("sf-onboarding-completion-msg-shown")
                    || userAttributesResponse.getAttributes().get("sf-onboarding-completion-msg-shown") == null) {
                return false;
            } else {
                return Boolean.valueOf(
                        userAttributesResponse.getAttributes().get("sf-onboarding-completion-msg-shown").toString());
            }
        } catch (Exception e) {
            log.error(String.format("Exception in fetching user attributes for userId %s",
                    userContext.getUserProfile().getUserId()));
            exceptionReportingService.reportException("Exception in fetching user attributes", e);
        }
        return true;
    }

    private ChronicCareCgmTrackerWidget getCgmTrackerWidget(UserContext userContext,
            UserOnboardingActionWithContext context, ActivePackResponse activePackResponse, boolean isNonCGMProduct)
            throws ResourceNotFoundException, NullPointerException {

        if (isDiaconUser(userContext) || isNonCGMProduct || ChronicCareAppUtil.isDelayedCGMDeliveryUser(userContext))
            return null;

        ChronicCareCgmTrackerWidget cgmTrackerWidget = new ChronicCareCgmTrackerWidget();
        if (AppUtil.isUltraFitApp(userContext)) {
            cgmTrackerWidget.setTitle("CGM kit is on the way");
        }
        List<CareKitTrackerTimeline> cgmTimelines = new ArrayList<>();
        CareKitTrackerTimeline timeline1 = new CareKitTrackerTimeline();
        String trackerTitle = "The care kit is dispatched to your address";
        timeline1.setTitle(trackerTitle);
        timeline1.setCompleted(true);
        boolean coachWelcomeCallCompleted = context.getCoachCardActionWithContext().getContext()
                .getAtLeastOneConsultationCompleted();
        boolean isRenewalProduct = chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct());
        CareKitTrackerTimeline timeline2 = new CareKitTrackerTimeline();
        if (!StringUtils.isEmpty(context.getAGMDataActionWithContext().getContext().getEDD())) {
            String edd;
            if (isUltraFitApp(userContext) || coachWelcomeCallCompleted) {
                edd = Optional.ofNullable(context.getAGMDataActionWithContext().getContext().getEDD()).orElse("");
                timeline2.setTitle("CGM kit will be delivered within 5-7 days");
            } else {
                timeline2.setTitle("The care kit will be delivered after coach consultation.");
            }
            timeline2.setCompleted(true);
        } else if (!context.getAGMDataActionWithContext().getAction().isActionPermitted()) {
            cgmTrackerWidget.setTitle("Care kit has been delivered");
            timeline2.setTitle("The care kit has been delivered");
            timeline2.setCompleted(true);
        } else {
            if (isUltraFitApp(userContext) || coachWelcomeCallCompleted) {
                timeline2.setTitle("CGM kit will be delivered within 5-7 days");
            } else {
                timeline2.setTitle("The care kit will be delivered after coach consultation.");
            }
            timeline2.setCompleted(false);
        }

        CareKitTrackerTimeline timeline3 = new CareKitTrackerTimeline();
        timeline3.setTitle("CGM installation will be done post coach consult. We will reach out to schedule it");
        timeline3.setCompleted(!context.getAGMDataActionWithContext().getAction().isActionPermitted());

        String welcomeKitImageURL;
        if (isUltraFitApp(userContext)) {
            timeline2.setCompleted(true);
            cgmTimelines.add(timeline2);
            welcomeKitImageURL = ULTRAFIT_WELCOME_KIT_IMAGE;
        } else {
            cgmTimelines.add(timeline1);
            cgmTimelines.add(timeline2);
            cgmTimelines.add(timeline3);
            if (isRenewalProduct) {
                welcomeKitImageURL = SUGARFIT_RENEWAL_WELCOME_KIT_IMAGE;
            } else {
                welcomeKitImageURL = SUGARFIT_WELCOME_KIT_IMAGE;
            }
        }
        cgmTrackerWidget.setCgmTimelines(cgmTimelines);
        cgmTrackerWidget.setIsNonCGMProduct(false);
        // Pack type hardcoded temporarily to fix 50 strips problem. Should be changed
        // in next release.
        cgmTrackerWidget.setPackType(activePackResponse.getBundleProduct().getClubCode());
        cgmTrackerWidget.setImageUrl(welcomeKitImageURL);
        cgmTrackerWidget.setShowDetailsCta(!isUltraFitApp(userContext));

        boolean isPDpack = activePackResponse.getBundleProduct().getClubCode().startsWith("SUGARFIT_PD");
        if (ChronicCareAppUtil.isSugarfitPlusUser(userContext) || isRenewalProduct) {
            cgmTrackerWidget.setKitItemList(new ArrayList<>() {
                {
                    add(new ChronicCareCgmTrackerWidget.KitItem("One Continuous Glucose Monitor", "CGM"));
                }
            });
        } else {
            cgmTrackerWidget.setKitItemList(new ArrayList<>() {
                {
                    add(new ChronicCareCgmTrackerWidget.KitItem("One Continuous Glucose Monitor", "CGM"));
                }
            });
        }
        return cgmTrackerWidget;
    }

    private CardData getAGMCardData(UserOnboardingActionWithContext context, ActivePackResponse activePackResponse) {
        CardData agmCardData;
        Action AGMCardAction;
        String collapsedMessage = "Have your CGM delivered to your doorstep";
        String actionTitle = CGM_CARD_DEFAULT_ACTION_TITLE;
        String cardMessage = CGM_CARD_DEFAULT_CARD_MESSAGE;
        if (!StringUtils.isEmpty(context.getAGMDataActionWithContext().getContext().getEDD()) &&
                !StringUtils.isEmpty(context.getAGMDataActionWithContext().getContext().getStatus())) {
            String edd = Optional.ofNullable(context.getAGMDataActionWithContext().getContext().getEDD()).orElse("");
            String status = Optional.ofNullable(context.getAGMDataActionWithContext().getContext().getStatus())
                    .orElse("");
            if (chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct())) {
                cardMessage = String.format("Your CGM delivery ETA: %s\nStatus %s", edd, status);
                collapsedMessage = "Have your CGM delivered to your doorstep";
            } else {
                cardMessage = String.format("Your CGM delivery ETA: %s\nStatus %s", edd, status);
            }
            actionTitle = String.format("ETA: %s, status: %s", edd, status);
        }

        AGMCardAction = getDisabledActionForToastMessage(actionTitle);
        AGMCardAction.setIsEnabled(true);
        AGMCardAction.setIsPrimaryButton(false);
        AGMCardAction.setTitle(actionTitle);
        agmCardData = CardData.builder()
                .title("Device Delivery")
                .enabled(context.getAGMDataActionWithContext().getAction().isActionPermitted())
                .collapsedMessage(collapsedMessage)
                .cardMessage(cardMessage)
                .action(AGMCardAction)
                .imageURL("/image/chroniccare/agm_card_image.png")
                .build();
        return agmCardData;
    }

    private CardData getDiagnosticCardDataV2(UserContext userContext, UserOnboardingActionWithContext context,
            PatientDetail patientDetail) {
        CardData diagnosticCardData;
        Header header = new Header();
        header.setTitle("Instructions");
        Action diagnosticCardAction;
        Boolean isEnabled = context.getDiagnosticCardActionWithContext().getAction().isActionPermitted();
        if (!context.getDiagnosticCardActionWithContext().getAction().getServiceableOnline()) {
            diagnosticCardData = CardData.builder().enabled(isEnabled)
                    .type(CardData.CardType.DIAGNOSTICS)
                    .boldedText("Schedule")
                    .cardMessage(context.getDiagnosticCardActionWithContext().getAction().getMessage())
                    .title(isEnabled ? "Book Diagnostic Test" : "Diagnostic tests booked")
                    .instruction("Important step to baseline your current health markers")
                    .imageURL("/image/chroniccare/WhatsNextDiagnosticsV2.png")
                    .enabled(isEnabled)
                    .completed(!isEnabled)
                    .build();
        } else {
            if (isEnabled) {
                UserPreferencePojo userAddressIdPreference = this.serviceInterfaces.sfAlbusClient.getUserPreference(
                        userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "addressId");
                String diagnosticProductCode = context.getDiagnosticCardActionWithContext().getContext()
                        .getProductCodes().stream().findFirst().get();
                String provider = context.getDiagnosticCardActionWithContext().getContext().getDiagnosticProvider();
                diagnosticCardAction = Action.builder()
                        .isEnabled(isEnabled)
                        .title("Book Diagnostic Test")
                        .actionType(ActionType.NAVIGATION)
                        .meta(DiagnosticActionMeta.builder().header(header).build())
                        .url(new StringBuilder()
                                .append("curefit://selectCareDateV1?patientId=").append(patientDetail.getId())
                                .append("&productId=")
                                .append(diagnosticProductCode).append("&type=").append("DIAGNOSTICS")
                                .append("&category=AT_HOME_SLOT&nextAction=checkout&productCodes=")
                                .append(diagnosticProductCode)
                                .append("&addressId=").append(userAddressIdPreference.getPreferenceTypeValues().get(0))
                                .append("&parentBookingId=").append(context.getBookingId())
                                .append("&diagnosticsProvider=").append(provider).toString())
                        .build();
            } else {
                diagnosticCardAction = getDisabledActionForToastMessage(
                        context.getDiagnosticCardActionWithContext().getAction().getMessage());
                diagnosticCardAction.setTitle("Book Diagnostic Test");
            }
            diagnosticCardData = CardData.builder().enabled(isEnabled)
                    .action(diagnosticCardAction)
                    .type(CardData.CardType.DIAGNOSTICS)
                    .boldedText("Schedule")
                    .cardMessage("Schedule a time to get your test done from the comfort of your home!")
                    .title(isEnabled ? "Book Diagnostic Test" : "Diagnostic tests booked")
                    .instruction("Important step to baseline your current health markers")
                    .imageURL("/image/chroniccare/WhatsNextDiagnosticsV2.png")
                    .enabled(isEnabled)
                    .completed(!isEnabled)
                    .build();
        }
        return diagnosticCardData;
    }

    private CardData getCGMInstallationCard(UserContext userContext, UserOnboardingActionWithContext context,
            PatientDetail patientDetail) {
        CardData cgmInstallationCard;
        Header header = new Header();
        header.setTitle("CGM Installation");
        Action cgmInstallationCardAction;
        cgmInstallationCardAction = Action.builder()
                .isEnabled(context.getCgmInstallationCardActionWithContext().getAction().isActionPermitted())
                .isPrimaryButton(false)
                .title("CGM Installation")
                .actionType(ActionType.SHOW_TOAST_MESSAGE)
                .meta(ToastMessageMeta.builder().message("Your CGM will be installed soon").build())
                .build();
        cgmInstallationCard = CardData.builder()
                .enabled(context.getCgmInstallationCardActionWithContext().getAction().isActionPermitted())
                .action(cgmInstallationCardAction)
                .cardMessage("Your CGM will be installed soon")
                .collapsedMessage("Your CGM will be installed soon")
                .title("CGM Installation")
                .imageURL("/image/chroniccare/cgm_installation.png")
                .enabled(context.getCgmInstallationCardActionWithContext().getAction().isActionPermitted())
                .build();
        return cgmInstallationCard;
    }

    private String getAgentImageFromResponse(AgentResponse agentResponse) {
        String imageUrl = agentResponse.getDisplayImage();
        if (imageUrl != null && !imageUrl.equals("")) {
            return imageUrl;
        } else if (agentResponse.getType().equals(AgentType.SUGARFIT_RELATIONSHIP_MANAGER)) {
            return "/image/chroniccare/coach_male_v2.png";
        } else {
            if (agentResponse.getGender().equals(Gender.Female)) {
                if (agentResponse.getType().equals(AgentType.LIFESTYLE_COACH))
                    return "/image/chroniccare/coach_female_v2.png";
                return "/image/chroniccare/doctor_female_v2.png";
            } else if (agentResponse.getGender().equals(Gender.Male)) {
                if (agentResponse.getType().equals(AgentType.LIFESTYLE_COACH))
                    return "/image/chroniccare/coach_male_v2.png";
                return "/image/chroniccare/doctor_male_v2.png";
            } else {
                return "/image/chroniccare/coach_male_v2.png";
            }
        }
    }

    private CardData getDoctorCardData(UserContext userContext, UserOnboardingActionWithContext context, ChronicCareTeam assignedCareTeam,
            ActivePackResponse activePackResponse) throws OllivanderClientException {
        CardData doctorCardData;
        PatientPreferredAgentResponse doctor = assignedCareTeam.getDoctor();
        Action doctorCardAction;
        String doctorName = null, profileImage = null;
        if (context.getDoctorCardActionWithContext().getAction().isActionPermitted()) {
            Long doctorCenterId = getDoctorCenterIdForPaidUser(doctor);
            String doctorConsultationProduct = chronicCareServiceHelper
                    .getDoctorConsultationProduct(activePackResponse.getBundleProduct());
            Action doctorBookingAction = Action.builder()
                    .isEnabled(context.getDoctorCardActionWithContext().getAction().isActionPermitted())
                    .title("SCHEDULE")
                    .actionType(ActionType.NAVIGATION)
                    .url(getAgentDatePickerUrl(doctorConsultationProduct, doctorCenterId,
                            doctor.getAgentResponse().getId(), activePackResponse))
                    .isPrimaryButton(context.getDoctorCardActionWithContext().getAction().isActionPermitted())
                    .build();
            doctorCardAction = chronicCareServiceHelper.getModifiedDoctorConsultActionBasedOnCgmCompletion(userContext, activePackResponse, doctorBookingAction);
            doctorName = doctor.getAgentResponse().getName();
            profileImage = getAgentImageFromResponse(doctor.getAgentResponse());
        } else {
            if (context.getDoctorCardActionWithContext().getAction().isDoctorAssigned()) {
                doctorCardAction = getDisabledActionForToastMessage(
                        context.getDoctorCardActionWithContext().getAction().getReasonForProhibition());
                doctorCardAction.setTitle("SCHEDULE");
            } else {
                doctorCardAction = getDisabledActionForToastMessage(DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT);
                doctorCardAction.setTitle("SCHEDULE");
                doctorName = DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT;
                profileImage = DOCTOR_ASSIGNMENT_PLACEHOLDER_URL;
            }
        }
        doctorCardData = CardData.builder()
                .enabled(context.getDoctorCardActionWithContext().getAction().isActionPermitted())
                .action(doctorCardAction)
                .cardMessage(doctorName)
                .collapsedMessage("Consult with your Doctor")
                .imageURL(profileImage)
                .title("Consult Doctor")
                .enabled(context.getDoctorCardActionWithContext().getAction().isActionPermitted())
                .build();
        return doctorCardData;
    }

    private CardData getDoctorCardDataV2(UserOnboardingActionWithContext context, ChronicCareTeam assignedCareTeam,
            ActivePackResponse activePackResponse, UserContext userContext) throws OllivanderClientException {
        CardData doctorCardData;
        PatientPreferredAgentResponse doctor = assignedCareTeam.getDoctor();
        Action doctorCardAction;
        String doctorName = null, profileImage = null, boldedText = null, instruction = null, cardMessage = null;
        boolean isEnabled = context.getDoctorCardActionWithContext().getAction().isActionPermitted();
        boolean showDoctorConsult = context.getDoctorCardActionWithContext().getAction().isShowCard();
        if (isEnabled) {
            Long doctorCenterId = getDoctorCenterIdForPaidUser(doctor);
            String doctorConsultationProduct = chronicCareServiceHelper
                    .getDoctorConsultationProduct(activePackResponse.getBundleProduct());
            Action doctorBookingAction = Action.builder()
                    .isEnabled(true)
                    .title("Book Doctor Consult")
                    .actionType(ActionType.NAVIGATION)
                    .url(getAgentDatePickerUrl(doctorConsultationProduct, doctorCenterId,
                            doctor.getAgentResponse().getId(), activePackResponse))
                    .isPrimaryButton(context.getDoctorCardActionWithContext().getAction().isActionPermitted())
                    .build();
            doctorCardAction = chronicCareServiceHelper.getModifiedDoctorConsultActionBasedOnCgmCompletion(userContext, activePackResponse, doctorBookingAction);
            doctorName = getDoctorName(doctor.getAgentResponse().getName());
            profileImage = getAgentImageFromResponse(doctor.getAgentResponse());
            boldedText = doctorName;
            cardMessage = doctorName;
            instruction = "Book an appointment and consult with your doctor!";
        } else {
            if (context.getDoctorCardActionWithContext().getAction().isDoctorAssigned()) {
                doctorCardAction = getDisabledActionForToastMessage(
                        context.getDoctorCardActionWithContext().getAction().getReasonForProhibition());
                doctorCardAction.setTitle("Book Doctor Consult");
                doctorName = getDoctorName(doctor.getAgentResponse().getName());
                profileImage = getAgentImageFromResponse(doctor.getAgentResponse());
                boldedText = doctorName;
                cardMessage = doctorName;
                instruction = context.getDoctorCardActionWithContext().getAction().getReasonForProhibition();
            } else {
                doctorCardAction = getDisabledActionForToastMessage(DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT);
                doctorCardAction.setTitle("Book Doctor Consult");
                doctorName = DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT;
                profileImage = DOCTOR_ASSIGNMENT_PLACEHOLDER_URL;
                boldedText = "Consult with your doctor";
                instruction = DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT;
                cardMessage = "Consult with your doctor";
            }
        }
        doctorCardData = CardData.builder().enabled(isEnabled)
                .action(doctorCardAction)
                .type(CardData.CardType.DOCTOR)
                .cardMessage(cardMessage)
                .imageURL(profileImage)
                .boldedText(boldedText)
                .title("Consult with your doctor")
                .instruction(instruction)
                .enabled(isEnabled)
                .completed(!showDoctorConsult)
                .build();
        if (!AppUtil.isUltraFitApp(userContext) && doctor != null) {
            doctorCardData.setCollapsedMessage(
                    String.format("%d+ yrs experience", doctor.getAgentResponse().getExperience()));
        }
        return doctorCardData;
    }

    private Long getDoctorCenterIdForPaidUser(PatientPreferredAgentResponse doctor) throws OllivanderClientException {
        if (doctor.getAgentResponse().getAgentCenterMapping().size() > 1) {
            List<CenterResponseV2> experienceCenters = this.serviceInterfaces.ollivanderCenterClient.getSugarfitExperienceCenters();
            Set<Long> experienceCenterIds;
            if (CollectionUtils.isNotEmpty(experienceCenters)) {
                experienceCenterIds = experienceCenters.stream().map(CenterBaseResponse::getId).collect(Collectors.toSet());
            } else {
                experienceCenterIds = new HashSet<>();
            }
            return doctor.getAgentResponse().getAgentCenterMapping().stream()
                    .filter(center -> !experienceCenterIds.contains(center.getCenterId()))
                    .findFirst().get().getCenterId();
        } else {
            return doctor.getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
        }
    }

    private CardData getCoachCardData(UserOnboardingActionWithContext context, ChronicCareTeam assignedCareTeam,
            PatientDetail patientDetail, ActivePackResponse activePackResponse) {
        CardData coachCardData;
        PatientPreferredAgentResponse coach = assignedCareTeam.getCoach();
        String coachConsultationProduct = context.getCoachCardActionWithContext().getContext().getProductCodes()
                .stream().findFirst().get();
        Long coachCenterId = coach.getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
        Action coachCardAction;
        if (context.getCoachCardActionWithContext().getAction().isActionPermitted()) {
            coachCardAction = Action.builder()
                    .isEnabled(context.getCoachCardActionWithContext().getAction().isActionPermitted())
                    .title("GET IN TOUCH")
                    .actionType(ActionType.NAVIGATION)
                    .url(getAgentDatePickerUrl(coachConsultationProduct, coachCenterId,
                            coach.getAgentResponse().getId(), activePackResponse))
                    .build();
        } else {
            coachCardAction = getDisabledActionForToastMessage("Locked");
        }
        coachCardData = CardData.builder()
                .enabled(context.getCoachCardActionWithContext().getAction().isActionPermitted())
                .action(coachCardAction)
                .title("Meet your Diabetes Expert")
                .cardMessage(String.format("%s will be assisting you", coach.getAgentResponse().getName()))
                .imageURL(getAgentImageFromResponse(coach.getAgentResponse()))
                .enabled(context.getCoachCardActionWithContext().getAction().isActionPermitted())
                .build();
        return coachCardData;
    }

    private CardData getCoachCardDataV2(UserContext userContext, UserOnboardingActionWithContext context,
            ChronicCareTeam assignedCareTeam, ActivePackResponse activePackResponse) {
        CardData coachCardData;
        PatientPreferredAgentResponse coach = assignedCareTeam.getCoach();
        Action coachCardAction;
        boolean isEnabled = context.getCoachCardActionWithContext().getAction().isActionPermitted();
        if (isEnabled) {
            Long coachCenterId = coach.getAgentResponse().getAgentCenterMapping().stream().findFirst().get()
                    .getCenterId();
            String coachConsultationProduct = context.getCoachCardActionWithContext().getContext().getProductCodes()
                    .stream().findFirst().get();
            coachCardAction = Action.builder()
                    .isEnabled(context.getCoachCardActionWithContext().getAction().isActionPermitted())
                    .title(isUltraFitApp(userContext) ? "Book Expert Consult" : "Book Coach Consult")
                    .actionType(ActionType.NAVIGATION)
                    .url(getAgentDatePickerUrl(coachConsultationProduct, coachCenterId,
                            coach.getAgentResponse().getId(), activePackResponse))
                    .build();
        } else {
            coachCardAction = getDisabledActionForToastMessage("Locked");
        }
        coachCardData = CardData.builder().enabled(isEnabled)
                .action(coachCardAction)
                .type(CardData.CardType.COACH)
                .title(isEnabled ? "Consult with your coach" : "Call scheduled with Coach")
                .boldedText(coach.getAgentResponse().getName())
                .cardMessage(coach.getAgentResponse().getName())
                .instruction(isUltraFitApp(userContext) ? "Schedule a consult and start your metabolic health journey"
                        : "This is the most important step to initiate the diabetes reversal journey")
                .imageURL(getAgentImageFromResponse(coach.getAgentResponse()))
                .enabled(isEnabled)
                .completed(!isEnabled)
                .build();
        if (!AppUtil.isUltraFitApp(userContext))
            coachCardData.setCollapsedMessage("");
        return coachCardData;
    }

    private CardData getCGMInstallationCardDataV2(UserContext userContext, UserOnboardingActionWithContext context) {
        boolean isCGMInstallationBookingPending = chronicCareServiceHelper.isCGMInstallationBookingPending(context);
        boolean isCGMInstallationCompleted = chronicCareServiceHelper.isCGMInstallationCompleted(context);
        boolean isWelcomeCallScheduled = !context.getCoachCardActionWithContext().getAction().isActionPermitted();
        boolean isEnabled = isWelcomeCallScheduled && isCGMInstallationBookingPending;
        Action cgmCardAction = chronicCareServiceHelper.getPhleboTaskRecommendedSlotsPageRedirectAction("", isEnabled, false, null);
        CardData cgmCardData = CardData.builder().enabled(isEnabled)
                .action(cgmCardAction)
                .type(CardData.CardType.CGM_INSTALLATION)
                .title(isCGMInstallationCompleted ? "CGM Installation Completed" : isCGMInstallationBookingPending ? "Book CGM Installation" : "CGM Installation Scheduled")
                .boldedText("")
                .cardMessage("")
                .instruction(isEnabled ? "Learn how your body responds to your eating habits, book your CGM delivery and installation" : "You can book CGM installation after booking your coach consultation first")
                .imageURL("image/chroniccare/whats_next_cgm.png")
                .enabled(isEnabled)
                .completed(isCGMInstallationCompleted || !isCGMInstallationBookingPending)
                .build();
        if (!AppUtil.isUltraFitApp(userContext))
            cgmCardData.setCollapsedMessage("");
        return cgmCardData;
    }

    public CGMGraphWidget getCGMGraphWidget(UserContext userContext,
            UserOnboardingActionWithContext onboardingActions,
            CgmStat cgmStat, CgmOnboardingStatusResponse cgmOnboardingStatusResponse, boolean isPackExpired)
            throws HttpException, ResourceNotFoundException, ParseException, IOException {
        // HPES For now backend failure handled with general exception
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        Calendar cal = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
        Date lastReadingDate = cal.getTime();
        List<com.sugarfit.chs.pojo.CGMDeviceInfo> cgmDeviceInfos = cgmOnboardingStatusResponse.getCgmDeviceInfos();
        AtomicBoolean hasOnGoingCGM = new AtomicBoolean(false);
        if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
            cgmDeviceInfos.forEach(cgmDeviceInfo -> {
                if (cgmDeviceInfo.isOngoing()) {
                    hasOnGoingCGM.set(true);
                }
            });
        }
        try {
            if (Objects.nonNull(cgmOnboardingStatusResponse.getLastReadingDate())) {
                lastReadingDate = cgmOnboardingStatusResponse.getLastReadingDate();
                cal.add(Calendar.DATE, -7);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        if (!isPackExpired && lastReadingDate.before(cal.getTime()) &&
                !(hasOnGoingCGM.get() || ChronicCareAppUtil.cgmActivationAllowed(userContext,
                        cgmOnboardingStatusResponse))) {
            return null;
        }
        List<CGMGraphWidget.CGMActionSelection> cgmSelectionList = getCGMActionSelectionList(userContext,
                true, cgmOnboardingStatusResponse);
        boolean showScanButtonOnCgmGraphWidget = !cgmOnboardingStatusResponse.isEnableFirstInAppReading();
        CGMGraphWidget.CGMGraphTopDisclaimer cgmGraphTopDisclaimer = getCgmTopInstruction(cgmOnboardingStatusResponse,
                userContext);
        CGMGraphWidget.CGMPurchaseInfo cgmPurchaseInfo = getCgmPurchaseInfo(cgmOnboardingStatusResponse, cgmStat,
                userContext);
        return getCGMGraphWidget(userContext, cgmStat, cgmSelectionList,
                showScanButtonOnCgmGraphWidget, cgmGraphTopDisclaimer, cgmPurchaseInfo);

    }

    private CGMGraphWidget.CGMGraphTopDisclaimer getCgmTopInstruction(
            CgmOnboardingStatusResponse cgmOnboardingStatusResponse, UserContext userContext) {
        List<com.sugarfit.chs.pojo.CGMDeviceInfo> deviceInfoList = cgmOnboardingStatusResponse.getCgmDeviceInfos();

        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            com.sugarfit.chs.pojo.CGMDeviceInfo latestCgmDevice = deviceInfoList.get(deviceInfoList.size() - 1);
            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            if (latestCgmDevice.getCurrentState().equals("AGM_CRASHED")) {
                long diffInMillis = calendar.getTime().getTime() - latestCgmDevice.getEndDate().getTime();
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                if (diffInHours <= 72) {
                    return CGMGraphWidget.getCgmCrashedDisclaimer(userContext);
                }
            } else if (latestCgmDevice.getCurrentState().equals("AGM_FINAL_READING_UPLOADED") ||
                    latestCgmDevice.getCurrentState().equals("AGM_ENDED")) {
                long diffInMillis = calendar.getTime().getTime() - latestCgmDevice.getEndDate().getTime();
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                if (diffInHours <= 72 && isUltraFitApp(userContext)) {
                    return CGMGraphWidget.getCgmEndedDisclaimer(userContext);
                }
            } else if (latestCgmDevice.getEndDate() == null && latestCgmDevice.getStartedOn() != null &&
                    (latestCgmDevice.getCurrentState().equals("AGM_FIRST_READING_UPLOADED")
                            || latestCgmDevice.getCurrentState().equals("AGM_READING_UPLOADED"))) {
                long diffInMillis = calendar.getTime().getTime() - latestCgmDevice.getStartedOn().getTime();
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                if (diffInHours <= 24) {
                    return CGMGraphWidget.get24HoursDisclaimer(userContext);
                }
            }
        }

        return null;
    }

    private CGMGraphWidget.CGMPurchaseInfo getCgmPurchaseInfo(CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
            CgmStat cgmStat, UserContext userContext) throws IOException {
        CGMGraphWidget.CGMPurchaseInfo cgmPurchaseInfo = new CGMGraphWidget.CGMPurchaseInfo();
        boolean showCgmPurchaseCard = false;
        String metabolicScoreText = "";
        List<com.sugarfit.chs.pojo.CGMDeviceInfo> deviceInfoList = cgmOnboardingStatusResponse.getCgmDeviceInfos();

        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            com.sugarfit.chs.pojo.CGMDeviceInfo latestCgmDevice = deviceInfoList.get(deviceInfoList.size() - 1);
            if (latestCgmDevice.getCurrentState().equals("AGM_FINAL_READING_UPLOADED") ||
                    latestCgmDevice.getCurrentState().equals("AGM_ENDED")) {
                showCgmPurchaseCard = true;

                HashMap<String, SfSalesBannerUrl> salesBannerConfig = appConfigCache.getConfig("SF_SALES_BANNER_CONFIG",
                        new TypeReference<>() {
                        }, new HashMap<>());
                if (salesBannerConfig != null) {
                    SfSalesBannerUrl homeBanners = salesBannerConfig.get("home");
                    if (homeBanners != null) {
                        String cgm = homeBanners.getSmall().get("cgm");
                        if (cgm != null && !cgm.isEmpty()) {
                            cgmPurchaseInfo.setCgmBannerImage(cgm);
                        }
                    }
                }
                if (cgmStat.getAverageMetabolicScore() >= MIN_METABOLIC_SCORE_FOR_CGM_INFO)
                    metabolicScoreText = "You have had great progress. Buy another CGM & get more insights.";
                else
                    metabolicScoreText = "Unlock more insights by buying another CGM.";
            }
        }

        cgmPurchaseInfo.setMetabolicScoreText(metabolicScoreText);
        cgmPurchaseInfo.setShowCgmPurchaseCard(showCgmPurchaseCard);
        cgmPurchaseInfo.setBtnAction(Action.builder().title("BUY NEW CGM").url(ChronicCareServiceHelper.getCgmStorePageLink(userContext))
                .actionType(ActionType.NAVIGATION).build());
        return cgmPurchaseInfo;
    }

    public Map<String, BgInsightsSummary> getCgmEvents(UserContext userContext,
            Map<String, DailySugarStat> dailySugarStatMap) throws ParseException, HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezone = userContext.getUserProfile().getTimezone() != null
                ? userContext.getUserProfile().getTimezone()
                : "Asia/Kolkata";
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);

        Map<String, BgInsightsSummary> insightsSummaryMap = new HashMap<>();
        for (Map.Entry<String, DailySugarStat> entry : dailySugarStatMap.entrySet()) {
            String stringDate = entry.getKey();
            Date date = new Date(new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH).parse(stringDate).getTime());
            Float appVersion = userContext.getSessionInfo().getAppVersion();
            BgInsightsSummaryRequest req = new BgInsightsSummaryRequest();
            req.setUserId(userId);
            req.setAppVersion(appVersion);
            req.setDate(date);
            BgInsightsSummary summary = this.chsClient.fetchCgmInsightsSummary(req, appTenant,
                    TimeZone.getTimeZone(timezone));
            insightsSummaryMap.put(entry.getKey(), summary);
        }
        return insightsSummaryMap;
    }

    public CGMGraphWidget getCGMGraphWidget(UserContext userContext, CgmStat cgmStat,
            List<CGMGraphWidget.CGMActionSelection> cgmSelectionList,
            boolean showScanButtonOnCgmGraphWidget, CGMGraphWidget.CGMGraphTopDisclaimer cgmGraphTopDisclaimer,
            CGMGraphWidget.CGMPurchaseInfo cgmPurchaseInfo) {
        String userId = userContext.getUserProfile().getUserId();
        String graphVersion = isLightWeightGraphEnabledForUser(userContext) ? "v2" : "v1";
        boolean isSkiaEngineEnabled = isSkiaEngineEnabled(userContext);
        if (null != cgmStat.getCurrentReadingMode() && cgmStat.getCurrentReadingMode() == ReadingMode.APP) {
            return new CGMGraphWidget(cgmStat, CGMGraphWidget.IN_APP_SCAN_TEXT, cgmSelectionList,
                    showScanButtonOnCgmGraphWidget, graphVersion, cgmGraphTopDisclaimer, cgmPurchaseInfo, isSkiaEngineEnabled);
        } else {
            return new CGMGraphWidget(cgmStat, CGMGraphWidget.PHLEBO_SCAN_TEXT, cgmSelectionList,
                    showScanButtonOnCgmGraphWidget, graphVersion, cgmGraphTopDisclaimer, cgmPurchaseInfo, isSkiaEngineEnabled);
        }
    }

    public List<CGMGraphWidget.CGMActionSelection> getCGMActionSelectionList(UserContext userContext,
            boolean isHomePage, CgmOnboardingStatusResponse cgmOnboardingStatusResponse) {
        List<com.sugarfit.chs.pojo.CGMDeviceInfo> cgmDeviceInfos = cgmOnboardingStatusResponse.getCgmDeviceInfos();
        List<CGMGraphWidget.CGMActionSelection> cgmSelectionList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
            cgmDeviceInfos = cgmDeviceInfos.stream().filter(cgmDeviceInfo -> {
                if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) ||
                        "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) ||
                        null == cgmDeviceInfo.getStartedOn()) {
                    return false;
                }
                return isHomePage
                        ? cgmDeviceInfo.isAtleastOneReadingDone()
                                || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState())
                        : cgmDeviceInfo.isAtleastOneReadingDone();
            }).collect(Collectors.toList());
            for (int idx = 0; idx < cgmDeviceInfos.size(); idx++) {
                com.sugarfit.chs.pojo.CGMDeviceInfo cgmDeviceInfo = cgmDeviceInfos.get(idx);
                CGMGraphWidget.CGMActionSelection cgmSelection = new CGMGraphWidget.CGMActionSelection();
                String title = "CGM #" + (idx + 1);
                String subTitle;
                if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) ||
                        "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) ||
                        null == cgmDeviceInfo.getStartedOn()) {
                    continue;
                }
                if (cgmDeviceInfo.isOngoing()) {
                    subTitle = "(Ongoing)";
                } else {
                    String startDate = TimeUtil.formatDateInTimezone(TimeUtil.IST_TIMEZONE,
                            cgmDeviceInfo.getStartedOn(), "MMM d");
                    String endDate = TimeUtil.formatDateInTimezone(TimeUtil.IST_TIMEZONE, cgmDeviceInfo.getEndDate(),
                            "MMM d");
                    subTitle = "(" + startDate + " - " + endDate + ")";
                }
                cgmSelection.setTitle(title);
                cgmSelection.setId(cgmDeviceInfo.getDeviceId());
                cgmSelection.setSubTitle(subTitle);
                cgmSelection.setActionType("CGM_DATA");
                cgmSelectionList.add(cgmSelection);
            }
        }
        if (ChronicCareAppUtil.cgmActivationAllowed(userContext,
                cgmOnboardingStatusResponse)) {
            CGMGraphWidget.CGMActionSelection cgmSelection = new CGMGraphWidget.CGMActionSelection();
            String title = "CGM #" + (cgmDeviceInfos.size() + 1);
            cgmSelection.setTitle(title);
            cgmSelection.setSubTitle("");
            cgmSelection.setId(CGM_ACTIVATION_DUMMY_DEVICE_ID);
            cgmSelection.setActionType("CGM_DATA");
            cgmSelectionList.add(cgmSelection);
        }
        // if (!AppUtil.isUltraFitApp(userContext)) {
        // CGMGraphWidget.CGMActionSelection cgmRequestWidget = new
        // CGMGraphWidget.CGMActionSelection();
        // cgmRequestWidget.setTitle("You can now buy extra CGMs to track your
        // progress");
        // cgmRequestWidget.setActionType("CGM_REQUEST");
        // cgmRequestWidget.setActionText("REQUEST NOW");
        // cgmSelectionList.add(cgmRequestWidget);
        // }
        return cgmSelectionList;
    }

    private SfHomeScanFaceWidget getScanFaceWidget(UserContext userContext) throws HttpException {
        SfHomeScanFaceWidget scanFaceWidget = new SfHomeScanFaceWidget();
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone userTimeZone = ChronicCareAppUtil.getUserTimezone(userContext);

        long cooldownPeriod = 4 * 60 * 60 * 1000L;
        String remainingCooldownTime = "";
        Date currentDate = Calendar.getInstance(userTimeZone).getTime();

        FaceBasedVitalsRequest request = new FaceBasedVitalsRequest();
        request.setUserId(userId);
        request.setStartTimeEpoch(atStartOfDay(currentDate, userTimeZone));
        request.setEndTimeEpoch(atEndOfDay(currentDate, userTimeZone));

        FaceBasedVitalScansForDayResponse scansForDay = chsClient.getScansForDay(userId, userTimeZone);
        List<FaceBasedVitalLogsResponse> fbvLogs = chsClient.getFaceBasedVitalLogs(request, userTimeZone);
        boolean isScanEnabled = scansForDay.getScansDoneToday() == 0;
        boolean isScanExhasted = scansForDay.getScansDoneToday() >= scansForDay.getScansAllowedForDay();

        if (CollectionUtils.isNotEmpty(fbvLogs)) {
            FaceBasedVitalLogsResponse lastLog = fbvLogs.get(0);
            long differenceInMillis = currentDate.getTime() - lastLog.getTimeStamp().getTime();
            long remainingCooldownMillis = cooldownPeriod - differenceInMillis;
            if (remainingCooldownMillis < 60) {
                isScanEnabled = scansForDay.getScansDoneToday() < scansForDay.getScansAllowedForDay();
            } else {
                remainingCooldownTime = getFormattedCooldownTimeForFBV(remainingCooldownMillis);
            }
        }

        Action scanFace = new Action("curefit://sfscanfacepage", ActionType.NAVIGATION);
        scanFace.setTitle(StringUtils.isEmpty(remainingCooldownTime) ? "SCAN NOW" : remainingCooldownTime);
        scanFace.setSubTitle(!isScanEnabled ? "SCAN AGAIN IN" : null);
        if (ChronicCareAppUtil.isUnlimitedFaceScanEnabledUser(userContext)) {
            scanFace.setDisabled(false);
        } else {
            scanFace.setDisabled(!isScanEnabled);
        }

        Action showHistory = new Action("curefit://fbvhistorypage?tab=logs", "HISTORY", ActionType.NAVIGATION);

        List<SfHomeScanFaceWidget.ScanItem> scanItemList = new ArrayList<>();
        Calendar wakeUpScanCalendar = Calendar.getInstance(userTimeZone);
        wakeUpScanCalendar.set(Calendar.HOUR_OF_DAY, 11);
        wakeUpScanCalendar.set(Calendar.MINUTE, 0);
        wakeUpScanCalendar.set(Calendar.SECOND, 0);
        wakeUpScanCalendar.set(Calendar.MILLISECOND, 0);
        Date wakeUpScanDeadline = wakeUpScanCalendar.getTime();

        SfHomeScanFaceWidget.ScanItem scanItem1 = new SfHomeScanFaceWidget.ScanItem();
        scanItem1.setScanItemText("On waking up (by 11 am)");
        if (CollectionUtils.isEmpty(fbvLogs)) {
            if (currentDate.before(wakeUpScanDeadline)) {
                scanItem1.setStatus(SfHomeScanFaceWidget.ScanItemStatus.PENDING);
            } else {
                scanItem1.setStatus(SfHomeScanFaceWidget.ScanItemStatus.MISSED);
            }
        } else {
            FaceBasedVitalLogsResponse lastLog = fbvLogs.get(0);
            if (!lastLog.getTimeStamp().before(wakeUpScanDeadline)) {
                scanItem1.setStatus(SfHomeScanFaceWidget.ScanItemStatus.MISSED);
                isScanExhasted = true;
            } else {
                scanItem1.setStatus(SfHomeScanFaceWidget.ScanItemStatus.DONE);
            }
        }

        SfHomeScanFaceWidget.ScanItem scanItem2 = new SfHomeScanFaceWidget.ScanItem();
        scanItem2.setScanItemText("After food ");
        scanItem2.setStatus(SfHomeScanFaceWidget.ScanItemStatus.PENDING);

        scanItemList.add(scanItem1);
        scanItemList.add(scanItem2);

        scanFaceWidget
                .setDescription(isScanEnabled ? "Get your essential health vitals with a simple face scan" : null);
        scanFaceWidget.setScanFaceAction(scanFace);
        scanFaceWidget.setShowHistoryAction(showHistory);
        scanFaceWidget.setScanItems(scanItemList);
        scanFaceWidget.setScanExhausted(isScanExhasted);

        return scanFaceWidget;
    }

    private ActivityLoggingWidget getActivityLoggingWidget() {
        return new ActivityLoggingWidget();
    }

    public Map<String, DailySugarStat> getSortedStatMap(Map<String, DailySugarStat> dailySugarStatMap,
            UserContext userContext) {
        if (MapUtils.isEmpty(dailySugarStatMap)) {
            // this is a hack to support logging till first reading
            String tz = userContext.getUserProfile().getTimezone();
            TimeZone timeZone = TimeZone.getTimeZone(tz);
            String todayDate = TimeUtil.formatDateInTimezone(timeZone, new Date(), TimeUtil.DEFAULT_TIME_FORMAT);
            DailySugarStat dailySugarStat = new DailySugarStat();
            dailySugarStat.setDate(todayDate);
            dailySugarStat.setPatientActivityImpactList(Lists.newArrayList());
            dailySugarStat.setUserMetricValues(Lists.newArrayList());
            dailySugarStatMap = Maps.newHashMap();
            dailySugarStatMap.put(todayDate, dailySugarStat);
            return dailySugarStatMap;
        }
        List<String> dayList = Lists.newArrayList(dailySugarStatMap.keySet());
        dayList.sort(Comparator.naturalOrder());
        dayList = processDayList(dayList, userContext);
        LinkedHashMap<String, DailySugarStat> dailySugarStatLinkedHashMap = Maps.newLinkedHashMap();
        for (String day : dayList) {
            if (null == dailySugarStatMap.get(day)
                    || CollectionUtils.isEmpty(dailySugarStatMap.get(day).getUserMetricValues())) {
                continue;
            }
            dailySugarStatLinkedHashMap.put(day, dailySugarStatMap.get(day));
        }
        return dailySugarStatLinkedHashMap;
    }

    private List<String> processDayList(List<String> dayList, UserContext userContext) {
        try {
            int days = 15;
            if (userContext.getSessionInfo().getOsName().equalsIgnoreCase("android")) {
                DeviceDetailEntry device = deviceService.getDeviceByDeviceId(userContext.getSessionInfo().getDeviceId(),
                        AppUtil.getAppTenantFromUserContext(userContext)).get();
                Float osVersion = getOSVersion(device);
                if (osVersion != null && osVersion < 8f && dayList.size() > 5) {
                    days = 5;
                    log.info("Skipping old cgm graph for userId {} {} Android",
                            userContext.getUserProfile().getUserId(), days);
                    return dayList.stream().skip(dayList.size() - days).collect(Collectors.toList());
                }
            }
            if (dayList.size() > 15) {
                log.info("Skipping old cgm graph for userId {} {}", userContext.getUserProfile().getUserId(), days);
                return dayList.stream().skip(dayList.size() - days).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.info("Skipping old cgm graph for userId {} Exception {}", userContext.getUserProfile().getUserId(),
                    e.getMessage());
        }
        return dayList;
    }

    private Float getOSVersion(DeviceDetailEntry device) {
        try {
            return Float.parseFloat(device.getOsVersion());
        } catch (NumberFormatException e) {
            log.info("Skipping old cgm graph for osVersion {} NumberFormatException", device.getOsVersion());
        }
        return null;
    }

    private CGMGraphData.DailySugarStat getDailySugarStat(String day,
            List<AggregateUserMetricValue.MetricValue> metricValues,
            List<PatientActivityLoggingResponse> activities) {

        TreeSet<AggregateUserMetricValue.MetricValue> metricValueSet = Sets
                .newTreeSet((v1, v2) -> TimeUtil.dateComparator.compare(v1.getMetricDate(), v2.getMetricDate()));
        if (!CollectionUtils.isEmpty(metricValues)) {
            metricValueSet.addAll(metricValues);
        }

        CGMGraphData.DailySugarStat sugarStat = new CGMGraphData.DailySugarStat();
        sugarStat.setDate(day);
        sugarStat.setUserMetricValues(metricValues);
        List<CGMGraphData.PatientActivityImpact> patientActivityImpactList = Lists.newArrayList();

        for (PatientActivityLoggingResponse activity : activities) {
            patientActivityImpactList.add(getPatientActivityImpact(activity, metricValueSet));
        }
        sugarStat.setPatientActivityImpactList(patientActivityImpactList);
        double overRangePercent = overRangePercentage(metricValues);
        sugarStat.setSugarScore(BigDecimal.valueOf(100 - overRangePercent).setScale(1, RoundingMode.DOWN));
        return sugarStat;
    }

    private CGMGraphData.PatientActivityImpact getPatientActivityImpact(PatientActivityLoggingResponse activity,
            TreeSet<AggregateUserMetricValue.MetricValue> metricValueSet) {
        CGMGraphData.PatientActivityImpact patientActivityImpact = new CGMGraphData.PatientActivityImpact();
        patientActivityImpact.setPatientActivityLoggingResponse(activity);
        Date activityTime = activity.getActivityTime();
        Date post2HrTime = DateTimeUtil.addMinutes(activityTime, 120L);
        NavigableSet<AggregateUserMetricValue.MetricValue> activityRangeMetrics = getMetricValuesInDateRange(
                metricValueSet, activityTime, post2HrTime);
        if (activityRangeMetrics.isEmpty()) {
            return patientActivityImpact;
        }

        double currentGlucose = activityRangeMetrics.first().getValue();

        patientActivityImpact.setCurrentGlucoseValue(BigDecimal.valueOf(currentGlucose).setScale(1, RoundingMode.DOWN));

        if (activity.getPatientActivityType() == PatientActivityType.NUTRITION) {
            double glucosePeak = activityRangeMetrics.stream()
                    .mapToDouble(AggregateUserMetricValue.MetricValue::getValue).max().getAsDouble();
            double riseInGlucose = glucosePeak - currentGlucose;
            double glucosePost2Hr = activityRangeMetrics.last().getValue();
            double overRangePercent = overRangePercentage(activityRangeMetrics);

            patientActivityImpact.setGlucoseChange(BigDecimal.valueOf(riseInGlucose).setScale(1, RoundingMode.DOWN));
            patientActivityImpact.setGlucosePeak(BigDecimal.valueOf(glucosePeak).setScale(1, RoundingMode.DOWN));
            patientActivityImpact.setGlucosePost2Hr(BigDecimal.valueOf(glucosePost2Hr).setScale(1, RoundingMode.DOWN));
            patientActivityImpact
                    .setTimeOverTarget(BigDecimal.valueOf(overRangePercent).setScale(1, RoundingMode.DOWN) + "%");
            patientActivityImpact.setActivityScore(
                    BigDecimal.valueOf(5.0 * (riseInGlucose / currentGlucose) + 5.0 * (overRangePercent)).setScale(1,
                            RoundingMode.DOWN));
        }
        return patientActivityImpact;
    }

    private double overRangePercentage(Collection<AggregateUserMetricValue.MetricValue> metricValues) {
        if (metricValues.isEmpty()) {
            return 0;
        }
        long metricsOverRange = metricValues.stream()
                .filter(v -> v.getValue() < GLUCOSE_LOWER_LIMIT || v.getValue() > GLUCOSE_HIGHER_LIMIT).count();
        return metricsOverRange * 100.0 / metricValues.size();
    }

    private NavigableSet<AggregateUserMetricValue.MetricValue> getMetricValuesInDateRange(
            TreeSet<AggregateUserMetricValue.MetricValue> metricValueSet, Date startDate, Date endDate) {
        AggregateUserMetricValue.MetricValue startDummy = new AggregateUserMetricValue.MetricValue();
        startDummy.setMetricDate(startDate);
        AggregateUserMetricValue.MetricValue endDummy = new AggregateUserMetricValue.MetricValue();
        endDummy.setMetricDate(endDate);
        return metricValueSet.subSet(startDummy, true, endDummy, true);
    }

    private SFCareTeam getSfCareTeam(UserContext userContext,
            UserOnboardingActionWithContext onboardingActionWithContext,
            PatientDetail patient,
            ActivePackResponse activePackResponse) {
        try {
            SFAgentDetail coachDetail = null, doctorDetail = null, rmDetail = null;
            AssignedAgentWidget assignedAgentWidget = getAssignedAgentsWidgets(userContext,
                    onboardingActionWithContext,
                    patient,
                    activePackResponse);

            if (assignedAgentWidget.getCoachData() != null) {
                AssignedAgentData coachData = assignedAgentWidget.getCoachData();
                coachDetail = new SFAgentDetail(
                        coachData.getAgentType(),
                        "PERSONAL COACH",
                        coachData.getAgentName(),
                        coachData.getAgentImageUrl(),
                        "",
                        coachData.getProfileAction(),
                        true);
            }

            if (isSugarFitApp(userContext) && assignedAgentWidget.getDoctorData() != null) {
                AssignedAgentData doctorData = assignedAgentWidget.getDoctorData();
                // SF-2392 : handle doctor un-assigned scenario.
                String instruction = "";
                String agentImageUrl = doctorData.getAgentImageUrl();
                if (!doctorData.getProfileAction().getIsEnabled()) {
                    if (doctorData.getAgentAssigned()) {
                        instruction = doctorData.getSubtitle();
                    } else {
                        agentImageUrl = SF_CARE_TEAM_DOCTOR_ASSIGNMENT_PLACEHOLDER_URL;
                    }
                }
                doctorDetail = new SFAgentDetail(
                        doctorData.getAgentType(),
                        "PERSONAL DOCTOR",
                        doctorData.getAgentName(),
                        agentImageUrl,
                        instruction,
                        doctorData.getProfileAction(),
                        doctorData.getAgentAssigned());
            }

            if (isSugarFitApp(userContext) && ChronicCareAppUtil.isCsTicketSupportedVersion(userContext)) {
                Action rmProfileAction;
                rmProfileAction = Action.builder().actionType(ActionType.NAVIGATION)
                        .url("curefit://sfsupportpage").title("Contact helpdesk").build();
                rmProfileAction.setIsEnabled(true);
                rmDetail = new SFAgentDetail(
                        "CUSTOMER_SUPPORT",
                        "RAISE YOUR QUERIES HERE",
                        "Help & Support",
                        "image/chroniccare/sf_cs_help_support.png",
                        "", rmProfileAction, true);
            } else if (isSugarFitApp(userContext) && assignedAgentWidget.getRmAgentWidget() != null) {
                RMAgentWidget rmData = assignedAgentWidget.getRmAgentWidget();
                Action rmProfileAction = new Action(
                        "whatsapp://send?text=&phone=" + rmData.getWhatsAppNumber(),
                        ActionType.EXTERNAL_DEEP_LINK);
                rmProfileAction.setIsEnabled(true);
                rmDetail = new SFAgentDetail(
                        rmData.getAgentType(),
                        rmData.getTitle(),
                        "For any support during the program",
                        rmData.getProfilePictureUrl(),
                        "", rmProfileAction, true);
            }

            return new SFCareTeam(coachDetail, doctorDetail, rmDetail);
        } catch (Exception e) {
            log.error("Exception in getting care team", e);
            exceptionReportingService.reportException("Exception in getting care team", e);
            return null;
        }
    }

    public SfCoachCommunitySessionWidget getUpcomingCoachCommunitySessionWidget(UserContext userContext, WidgetContext widgetContext) {
        Map<String, String> queryParams = widgetContext.getQueryParams();
        SfCoachCommunitySessionWidget widget = new SfCoachCommunitySessionWidget();
        int LIVE_WINDOW_IN_MINTUTES = 60;
        try {
            NowLiveWidgetView widgetView = new NowLiveWidgetView();
            widgetView.setProductType(ProductType.LIVE_VIDEO_CALL);
            widgetView.setTitle("Events and Talk");

            String position = queryParams.getOrDefault("position", "");
            queryParams.put("isCoachSession", "true");
            queryParams.put("pageId", "LiveWorkout");
            List<BaseWidget> widgets = widgetView.buildView(serviceInterfaces, userContext, widgetContext);
            HashMap<String, String> config = appConfigCache.getConfig("SF_COACH_COMMUNITY_SESSION_CONFIG", new TypeReference<>() {
            }, new HashMap<>());
            int live_window_in_minute = config.get("live_window_in_minute") == null ?
                    LIVE_WINDOW_IN_MINTUTES : Integer.parseInt(config.get("live_window_in_minute"));
            long live_window_in_millis = (long) live_window_in_minute * 60 * 1000;
            widget.setLiveWindowInMillis(live_window_in_millis);
            if (widgets != null && widgets.size() > 0) {
                NowLiveWidgetView nowLiveWidget = (NowLiveWidgetView) widgets.get(0);
                List<NowLiveSessionWidget> sessions = nowLiveWidget.getWidgets();
                sessions.sort(Comparator.comparingLong(NowLiveSessionWidget::getScheduleEpochTime));

                NowLiveSessionWidget upcomingSession = sessions.get(0);
                boolean isToday = DateUtils.isSameDay(new Date(), new Date(upcomingSession.getScheduleEpochTime()));
                widget.setEventDetails(upcomingSession);
                widget.setBuildTime(new Date().getTime());
                widget.setWidgetTitle(isToday ? "Live Today" : "Upcoming Session");

                boolean isLive = upcomingSession.getScheduleEpochTime() < new Date().getTime() + live_window_in_millis;

                if ((isLive && position.equals("top")) || (!isLive && position.equals("bottom"))) {
                    return widget;
                }
            }
        } catch (Exception e) {
            String message = String.format("Error in fetching webinar widget, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        return null;
    }

    private AssignedAgentWidget getAssignedAgentsWidgets(UserContext userContext,
            UserOnboardingActionWithContext onboardingActionWithContext,
            PatientDetail patient,
            ActivePackResponse activePackResponse) throws ResourceNotFoundException, NullPointerException,
            ExecutionException, InterruptedException, TimeoutException, OllivanderClientException {
        ChronicCareTeam assignedCareTeam = getAssignedCareTeam(userContext, patient.getId());
        AssignedAgentData doctorData = null, coachData = null;
        boolean isUltraFit = AppUtil.isUltraFitApp(userContext);
        if (assignedCareTeam.getCoach() != null) {
            String consultationProduct = onboardingActionWithContext.getCoachCardActionWithContext().getContext()
                    .getProductCodes().stream().findFirst().get();
            coachData = getAssignedAgentData(userContext, isUltraFit, assignedCareTeam.getCoach(), consultationProduct,
                    patient.getId(), onboardingActionWithContext, activePackResponse);
        }
        doctorData = getAssignedAgentData(userContext, isUltraFit, assignedCareTeam.getDoctor(),
                chronicCareServiceHelper.getDoctorConsultationProduct(activePackResponse.getBundleProduct()),
                patient.getId(), onboardingActionWithContext, activePackResponse);

        RMAgentWidget rmAgentWidget = getRMAgentWidget(userContext);

        return new AssignedAgentWidget(CARE_TEAM_TITLE, doctorData, coachData, rmAgentWidget);
    }

    private ChronicCareTeam getAssignedCareTeam(UserContext userContext, Long patientId) {
        ChronicCareTeam.ChronicCareTeamBuilder builder = ChronicCareTeam.builder();
        String userId = userContext.getUserProfile().getUserId();
        // HPES For now backend failure handled with general exception
        List<PatientPreferredAgentResponse> patientPreferredAgentResponses = this.serviceInterfaces.getSfAlbusClient()
                .getPreferredAgents(userId,
                        Optional.ofNullable(patientId),
                        AppUtil.getAppTenantFromUserContext(userContext).toString());
        if (!CollectionUtils.isEmpty(patientPreferredAgentResponses)) {
            for (PatientPreferredAgentResponse patientPreferredAgentResponse : patientPreferredAgentResponses) {
                if (patientPreferredAgentResponse.getAgentResponse().getType().equals(AgentType.DOCTOR)) {
                    builder.doctor(patientPreferredAgentResponse);
                } else if (patientPreferredAgentResponse.getAgentResponse().getType()
                        .equals(AgentType.LIFESTYLE_COACH)) {
                    builder.coach(patientPreferredAgentResponse);
                }
            }
        }
        return builder.build();
    }

    private AssignedAgentData getAssignedAgentData(UserContext userContext, boolean isUltraFit,
            PatientPreferredAgentResponse agentResponse,
            String consultationProduct,
            Long patientId,
            UserOnboardingActionWithContext onboardingActionWithContext,
            ActivePackResponse activePackResponse) throws ExecutionException, InterruptedException, TimeoutException, OllivanderClientException {
        boolean allowBooking = true;
        String reasonForProhibition = "";
        String agentType = agentResponse == null ? "" : agentResponse.getDoctorType();
        String agentName = agentResponse == null ? "" : agentResponse.getAgentResponse().getName();
        String imageUrl = null;
        String careTeamSubtitle = null;
        boolean agentAssigned = true;
        if (agentType.equalsIgnoreCase("LIFESTYLE_COACH")) {
            agentType = "COACH";
        } else {
            agentName = getDoctorName(agentName);
            agentType = "DOCTOR";
            if (chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct())) {
                allowBooking = true;
            } else if (onboardingActionWithContext != null
                    && !onboardingActionWithContext.getDoctorCardActionWithContext().getAction().isDoctorAssigned()) {
                allowBooking = false;
                reasonForProhibition = "Your best match doctor will be assigned based on your report";
                agentName = DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT;
                imageUrl = DOCTOR_ASSIGNMENT_PLACEHOLDER_URL;
                agentAssigned = false;
                careTeamSubtitle = "";
            } else if (onboardingActionWithContext != null
                    && !onboardingActionWithContext.getDoctorCardActionWithContext().getAction().isFirstConsultDone()) {
                allowBooking = onboardingActionWithContext.getDoctorCardActionWithContext().getAction()
                        .isActionPermitted();
                reasonForProhibition = onboardingActionWithContext.getDoctorCardActionWithContext().getAction()
                        .getReasonForProhibition();
            }
        }
        Action action, profileAction;
        if (allowBooking) {
            Long centerId;
            if (agentType.equalsIgnoreCase("LIFESTYLE_COACH")) {
                centerId = agentResponse.getAgentResponse().getAgentCenterMapping().stream().findFirst().get()
                        .getCenterId();
                action = Action.builder()
                        .isEnabled(true)
                        .title(CARE_TEAM_CTA_MESSAGE)
                        .actionType(ActionType.NAVIGATION)
                        .url(getAgentDatePickerUrl(consultationProduct, centerId, agentResponse.getAgentResponse().getId(),
                                activePackResponse))
                        .build();
            } else {
                centerId = getDoctorCenterIdForPaidUser(agentResponse);
                Action doctorBookingAction = Action.builder()
                        .isEnabled(true)
                        .title(CARE_TEAM_CTA_MESSAGE)
                        .actionType(ActionType.NAVIGATION)
                        .url(getAgentDatePickerUrl(consultationProduct, centerId, agentResponse.getAgentResponse().getId(),
                                activePackResponse))
                        .build();
                action = chronicCareServiceHelper.getModifiedDoctorConsultActionBasedOnCgmCompletion(userContext, activePackResponse, doctorBookingAction);
            }
            String profileUrl = String.format("curefit://chroniccaredoctorprofile?doctorId=%s&productId=%s",
                    agentResponse.getAgentResponse().getId().toString(), consultationProduct);
            profileAction = Action.builder().actionType(ActionType.NAVIGATION).url(profileUrl).build();
            profileAction.setIsEnabled(true);
            Date lastMetDate = agentResponse.getLastMetTime();
            Date bookingDate = agentResponse.getFirstScheduledAppointment();
            if (lastMetDate != null) {
                DateFormat df = new SimpleDateFormat("MMM dd");
                df.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
                String lastMetDateString = df.format(lastMetDate);
                careTeamSubtitle = "Last met: " + lastMetDateString;
            } else if (bookingDate != null) {
                DateFormat df = new SimpleDateFormat("MMM dd");
                df.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
                String bookingDateString = df.format(bookingDate);
                careTeamSubtitle = "Booked for: " + bookingDateString;
            } else {
                careTeamSubtitle = "Book your first call";
            }
            Date upcomingConsultationDate = chronicCareServiceHelper.getUpcomingConsultationDateForDisablingMultipleBookings(
                    userContext,
                    userContext.getUserProfile().getUserId(), consultationProduct);
            if (upcomingConsultationDate != null) {
                action = getDisabledCoachOrDoctorBookingAction(userContext, upcomingConsultationDate, agentType.equalsIgnoreCase("COACH") ? "coach" : "doctor");
                profileAction = action;
            }
            if (chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
                try {
                    if (!masterClassClient.fetchCoachConsultationStatus(Long.valueOf(userContext.getUserProfile().getUserId()))) {
                        action = ChronicCareAppUtil.getDisabledCoachOrDoctorBookingActionForSpecialPackDelayed();
                        profileAction = action;
                    }
                } catch (Exception e) {
                    exceptionReportingService.reportException(e);
                }
            }
        } else {
            action = getDisabledActionForToastMessage(reasonForProhibition);
            action.setTitle(CARE_TEAM_CTA_MESSAGE);
            profileAction = getDisabledActionForToastMessage(reasonForProhibition);
            careTeamSubtitle = careTeamSubtitle == null ? reasonForProhibition : careTeamSubtitle;
        }
        return AssignedAgentData.builder()
                .agentName(agentName)
                .agentImageUrl(agentAssigned ? getAgentImageFromResponse(agentResponse.getAgentResponse()) : imageUrl)
                .agentType(agentType)
                .subtitle(careTeamSubtitle)
                .experience(agentAssigned ? agentResponse.getAgentResponse().getExperience() : 1)
                // send null
                .action(action)
                // null check not in app. send disabled toast message.
                .profileAction(profileAction)
                // send number as null
                .whatsappNumber(isUltraFit ? UF_COACH_WA_NUMBER : SF_COACH_WA_NUMBER)
                .agentAssigned(agentAssigned)
                .build();
    }

    private RMAgentWidget getRMAgentWidget(UserContext userContext) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        RMAgentWidget rmAgentWidget = new RMAgentWidget();

        try {
            RMResponse rmResponse = smsClient.getRelationshipManagerProfile(userId);
            if (rmResponse.isRmAssigned()) {
                AgentResponse agentResponse = rmResponse.getAgentResponse();
                rmAgentWidget.setDisplayName(agentResponse.getName());
                rmAgentWidget.setProfilePictureUrl(getAgentImageFromResponse(agentResponse));
                rmAgentWidget.setWhatsAppNumber(agentResponse.getPhoneNumber());
                rmAgentWidget.setHomeWidget(true);
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Exception in getting relationship manager profile", e);
            exceptionReportingService.reportException("Exception in getting RM profile", e);
            return null;
        }

        return rmAgentWidget;
    }

    private ChronicCareCreativeWidget getTrailPackHomePageCreative() {
        ChronicCareCreativeWidget chronicCareCreativeWidget = new ChronicCareCreativeWidget();
        chronicCareCreativeWidget.setCreativeType("TRAIL_PACK_HOMEPAGE");
        chronicCareCreativeWidget.setAspectRatio(0.53);
        chronicCareCreativeWidget.setMarginBottom(30);
        chronicCareCreativeWidget.setMarginRight(25);
        chronicCareCreativeWidget.setMarginTop(30);
        chronicCareCreativeWidget.setMarginLeft(25);
        return chronicCareCreativeWidget;
    }

    private ChronicCareHomePageView.UserCGMConfiguration.DeviceNFCLocationInfo getDeviceNFCLocationInfo(
            UserContext userContext) {
        ChronicCareHomePageView.UserCGMConfiguration.DeviceNFCLocationInfo deviceNFCLocationInfo = new ChronicCareHomePageView.UserCGMConfiguration.DeviceNFCLocationInfo();
        try {
            if (null == userContext || null == userContext.getSessionInfo()
                    || StringUtils.isEmpty(userContext.getSessionInfo().getDeviceId())) {
                return deviceNFCLocationInfo;
            }
            DeviceDetailEntry device = deviceService.getDeviceByDeviceId(userContext.getSessionInfo().getDeviceId(),
                    AppUtil.getAppTenantFromUserContext(userContext)).get();
            if (null == device) {
                log.error("Device info is null for userId: {}", userContext.getUserProfile().getUserId());
                return deviceNFCLocationInfo;
            }
            List<DeviceNfcLocationInfo> infos = serviceInterfaces.getSfAlbusClient()
                    .getDeviceNFCLocationInfo(device.getBrand(), device.getDeviceModel());
            if (!CollectionUtils.isEmpty(infos)) {
                deviceNFCLocationInfo.setImage(infos.get(0).getS3Url());
            }

        } catch (Exception e) {
            log.error("Error while fetching deviceNFCInfo for userId {}: {}", userContext.getUserProfile().getUserId(),
                    e.getMessage());
        }
        return deviceNFCLocationInfo;
    }

    private AnnualPackPurchaseWidget annualPackPurchaseWidget() {
        AnnualPackPurchaseWidget widget = new AnnualPackPurchaseWidget();
        widget.setHeadingText("Get full pack");
        widget.setTitle("Get Upgraded");
        widget.setSubTitle("Make this the year you conquer diabetes with our holistic programme!");
        widget.setNextAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://diabetestypeselection")
                .title("UPGRADE TO FULL PACK").build());
        return widget;
    }

    private AnnualPackPurchaseWidget getLatestApp() {
        AnnualPackPurchaseWidget widget = new AnnualPackPurchaseWidget();
        widget.setHeadingText("UPDATE AVAILABLE");
        widget.setTitle("Version 2.02 is out now!");
        widget.setSubTitle(
                "Please update to the latest version of the app to get exciting new features!\n• You can now refer your friends and family and get cashback and other rewards\n• App stability and bug resolution");
        widget.setNextAction(Action.builder().url("https://onelink.to/t8ebvc").title("Update Now")
                .actionType(ActionType.EXTERNAL_DEEP_LINK).build());
        return widget;
    }

    private CompletableFuture<BaseWidgetNonVM> getInterventionsWidgetFutureV2(ServiceInterfaces serviceInterfaces,
            UserContext userContext, UserOnboardingActionWithContext onboardingActions) {
        return supplyAsync(() -> {
            try {
                if (userContext.getSessionInfo().getAppVersion() != null
                        && userContext.getSessionInfo().getClientVersion() >= 1.20f) {
                    return this.getShifuInterventionWidget(userContext, onboardingActions);
                }
            } catch (Exception e) {
                log.error("Exception in get Interventions :: {}", e.getMessage(), e);
                exceptionReportingService.reportException(
                        String.format("Exception in getInterventionCardWidget :: {}", e.getMessage()), e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private InterventionCardWidget getShifuInterventionWidget(UserContext userContext,
            UserOnboardingActionWithContext context) throws Exception {
        String userId = userContext.getUserProfile().getUserId();
        String userTimeZone = userContext.getUserProfile().getTimezone();
        InterventionCardWidget interventionCardWidget = new InterventionCardWidget();
        interventionCardWidget.setTitle("Activities to Do");
        String url = "curefit://goalsprogress?pageKey=HABITS";
        interventionCardWidget.setChevronAction(
                Action.builder().title("Goals Page").actionType(ActionType.NAVIGATION).url(url).build());
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
        List<InterventionDetails> interventionDetailsList = new ArrayList<>();
        String image = "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/chroniccare/WorkoutCard3x.png";

        Calendar currentTime = Calendar.getInstance(TimeZone.getTimeZone(userTimeZone));
        currentTime.set(Calendar.HOUR_OF_DAY, 0);
        currentTime.set(Calendar.MINUTE, 0);
        currentTime.set(Calendar.SECOND, 0);
        currentTime.set(Calendar.MILLISECOND, 0);
        Date startDate = currentTime.getTime();
        for (int i = INTERVENTION_WIDGET_DATE_WINDOW - 1; i >= 0; i--) {
            Date loopDate = DateUtils.addDays(startDate, -1 * i);
            String loopDateString = dateFormat.format(loopDate);
            List<UserActivityEntry> userActivitiesByUserIdAndDate;
            userActivitiesByUserIdAndDate = this.chronicCareServiceHelper.getUserActivitiesFromShifu(userContext,
                    userId, loopDate, userTimeZone);
            List<InterventionDetails.UserActivityWrapper> userActivityWrapperList = new ArrayList<>();
            for (UserActivityEntry userActivityEntry : userActivitiesByUserIdAndDate) {
                if (StringUtils.isEmpty(userActivityEntry.getAnchor())
                        && !ChronicCareAppUtil.isAppVersionInRangeInclusive(userContext,
                                EMPTY_INTERVENTION_DATE_MIN_VERSION, Double.MAX_VALUE)) {
                    userActivityEntry.setAnchor("__________________________________");
                }
                InterventionDetails.UserActivityWrapper userActivityWrapper = InterventionDetails.UserActivityWrapper
                        .builder()
                        .userActivityEntry(mapUserInterventionActivity(userActivityEntry))
                        .workoutInfo(getWorkoutInfo(userActivityEntry, i == 0))
                        .build();
                userActivityWrapperList.add(userActivityWrapper);
            }
            boolean appendData = !userActivityWrapperList.isEmpty() && ChronicCareAppUtil
                    .isAppVersionInRangeInclusive(userContext, EMPTY_INTERVENTION_DATE_MIN_VERSION, Double.MAX_VALUE);
            if (appendData) {
                interventionDetailsList.add(
                        InterventionDetails.builder()
                                .date(loopDateString)
                                .activityList(userActivityWrapperList)
                                .imageSourceFull(image)
                                .build());
            }
        }
        if (interventionDetailsList.isEmpty() || !ChronicCareAppUtil.isAppVersionInRangeInclusive(userContext,
                EMPTY_INTERVENTION_DATE_MIN_VERSION, Double.MAX_VALUE)) {
            return null;
        } else {
            interventionCardWidget.setData(interventionDetailsList);
            interventionCardWidget.setRenderingInfo(INTERVENTION_CARD_RENDERING_INFO);
            return interventionCardWidget;
        }
    }

    private HabitCard.WorkoutInfo getWorkoutInfo(UserActivityEntry userActivityEntry, boolean wodEnabled) {
        if (HabitType.LIVE_CLASS.equals(userActivityEntry.getType())) {
            Action action = new Action(
                    "curefit://liveclassdetail?liveClassId=" + userActivityEntry.getResource().getLiveClassProductId()
                            + "&bookingNumber=" + userActivityEntry.getResource().getLiveClassProductId()
                            + "&productType=LIVE_FITNESS&isDIY=true",
                    ActionType.NAVIGATION);
            List<DIYProduct> products = diyfsService.getDIYFitnessProductsByProductIds(
                    userActivityEntry.getUserId().toString(),
                    Collections.singletonList(userActivityEntry.getResource().getLiveClassProductId()), DIYFS_TENANT);
            if (products.size() == 1) {
                return new HabitCard.WorkoutInfo(
                        products.get(0).getSubTitle(),
                        products.get(0).getDuration() / 60000 + " min",
                        products.get(0).getImageDetails().getThumbnailImage(),
                        action, false);
            }
        }
        return null;

    }

    boolean isPaymentPendingForUser(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            return userSegments.contains(PAYMENT_PENDING_USER_SEGMENT);
        } catch (Exception e) {
            String message = String.format("Payment pending user segment error, userId :: %s, error :: %s", userId,
                    e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    boolean isReferralBottomBannerEnabledForUser(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            return userSegments.contains(REFERRAL_BOTTOM_BANNER_USER_SEGMENT_NAME);
        } catch (Exception e) {
            String message = String.format("Referral user segment error, userId :: %s, error :: %s", userId,
                    e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    boolean isUfReferralBannerEnabledForUser(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            return userSegments.contains(UF_REFERRAL_BANNER_USER_SEGMENT_NAME);
        } catch (Exception e) {
            String message = String.format("Referral user segment error, userId :: %s, error :: %s", userId,
                    e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    public HealthGlanceWidget getHealthGlanceWidget(UserContext userContext) {

        long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        String deviceId = userContext.getSessionInfo().getDeviceId();
        String activeDevice = null;
        try {
            ActiveDeviceEntry activeDeviceEntry = this.chsClient.fetchActiveDevice(userId);
            if (activeDeviceEntry != null && activeDeviceEntry.getDeviceId() != null)
                activeDevice = activeDeviceEntry.getDeviceId();
        } catch (Throwable e) {
            exceptionReportingService.reportException("Failed fetching active device id", e);
        }
        String reqDevice = activeDevice == null ? deviceId : activeDevice;
        try {
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            HealthPageViewBuilder healthPageViewBuilder = new HealthPageViewBuilder();
            LastSyncedDataResponse lastSyncedDataResponse = this.chsClient.fetchLastSyncedData(userId, reqDevice);
            HealthGlanceWidget widget = new HealthGlanceWidget();
            widget.setTitle("Health at a Glance");
            widget.setEnableSync(true);
            FitnessDataRequest req = new FitnessDataRequest();
            req.setEndTime(new Date(healthPageViewBuilder.fetchTimeToLast(timeZone)));
            req.setUserId(userId);
            req.setGranularity(GranularityType.WEEK);
            widget.setActiveDeviceId(activeDevice);
            req.setDeviceId(reqDevice);

            try {
                req.setStartTime(new Date(healthPageViewBuilder.fetchTimeFromStart(-6, timeZone))); // last 7 days
                CommonFitnessDataResponse steps = this.chsClient.getStepsData(req, appTenant, timeZone);
                widget.setSteps(steps);
            } catch (Exception e) {
                widget.setSteps(null);
                this.exceptionReportingService.reportException("Error in health page getStepsData ==>", e);
            }
            try {
                req.setStartTime(new Date(healthPageViewBuilder.fetchTimeFromStart(-4, timeZone)));
                HeartRateDataResponse heartRate = this.chsClient.getHeartRateData(req);
                widget.setHeartRate(heartRate);
                widget.setHeartRateUnit("bpm");
            } catch (Exception e) {
                widget.setHeartRate(null);
                this.exceptionReportingService.reportException("Error in health page getHeartRateData ==>", e);

            }
            try {
                req.setStartTime(new Date(healthPageViewBuilder.fetchTimeFromStart(-4, timeZone)));
                CommonFitnessDataResponse cal = this.chsClient.getCalorieData(req, appTenant, timeZone);
                widget.setCalorie(cal);
                widget.setCalorieUnit("kcal");
            } catch (Exception e) {
                widget.setCalorie(null);
                this.exceptionReportingService.reportException("Error in health page getCalorieData ==>", e);

            }
            if (lastSyncedDataResponse != null) {
                List<Date> dates = new ArrayList<>();
                dates.add(lastSyncedDataResponse.getCalorieData().getEndDate());
                dates.add(lastSyncedDataResponse.getSleepData().getEndDate());
                dates.add(lastSyncedDataResponse.getStepsData().getEndDate());
                dates.add(lastSyncedDataResponse.getHeartRateData().getEndDate());
                try {
                    Date maxDate = dates.stream().filter(item -> item != null).max(Date::compareTo).get();
                    widget.setLastSyncedTime(maxDate.getTime());

                } catch (Exception e) {
                    // exceptionReportingService.reportException("error in latest lastsynced time:",
                    // e);
                }
            }
            widget.setHomepageWidget(true);
            widget.setTitleAction(Action.builder().title("HEALTH").actionType(ActionType.NAVIGATION)
                    .url("curefit://goalsprogress?pageKey=HEALTH").build());
            return widget;
        } catch (Exception e) {
            exceptionReportingService.reportException("Failed fetching last synced data", e);
        }
        return null;
    }

    public SfWellnessAtGlanceWidget getLazyLoadingWellnessAtGlance(UserContext userContext) {
        return new SfWellnessAtGlanceWidget();
    }

    public SfWellnessAtGlanceWidget getWellnessAtGlanceWidget(UserContext userContext) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String userTimeZone = userContext.getUserProfile().getTimezone();
        SfWellnessAtGlanceWidget wellnessAtGlanceWidget = new SfWellnessAtGlanceWidget();
        List<WellnessDataPerDay> wellnessData = new ArrayList<>();

        if (ChronicCareAppUtil.isBadgesEnabled(userContext, chronicCareServiceHelper)) {
            addBadgesDataToWellnessAtGlanceWidget(userContext, wellnessAtGlanceWidget);
        }

        Calendar currentTime = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
        currentTime.set(Calendar.HOUR_OF_DAY, 23);
        currentTime.set(Calendar.MINUTE, 59);
        currentTime.set(Calendar.SECOND, 59);
        currentTime.set(Calendar.MILLISECOND, 999);
        Date startDate = currentTime.getTime();
        for (int i = WELLNESS_WIDGET_DATE_WINDOW - 1; i >= 0; i--) {
            List<WellnessAtGlanceItem> wellnessAtGlanceItems = new ArrayList<>();
            WellnessDataPerDay wellnessDataPerDay = new WellnessDataPerDay();
            Date loopDate = DateUtils.addDays(startDate, -1 * i);
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            dateFormat.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
            String loopDateString = dateFormat.format(loopDate);
            try {
                List<GoalWithData> goalWithLatestData = chronicCareServiceHelper.getShifuRecentGoals(userContext,
                        userId, loopDate, true, userTimeZone);
                for (String goalName : isUltraFitApp(userContext) ? WELLNESS_GOALS_UF : WELLNESS_GOALS) {
                    GoalWithData goal = goalWithLatestData.stream()
                            .filter(goalWithData -> goalWithData.getGoal().getMeta().getUniqueKey().equals(goalName))
                            .findAny()
                            .orElse(null);

                    if (goal != null) {
                        WellnessAtGlanceItem item = new WellnessAtGlanceItem();

                        if (goal.getMetricValuePojo() != null && goal.getMetricValuePojo().getValue() != null) {
                            item.setCurrentValue(getFormattedRecentValue(goal.getMetricValuePojo().getValue(),
                                    goal.getGoal().getMeta().getUniqueKey()));
                        }
                        if (goal.getTarget() != null) {
                            item.setGoalValue(getTargetValue(goal.getTarget().getLow(), goal.getTarget().getHigh(),
                                    goal.getGoal().getMeta().getUniqueKey()));
                        }
                        item.setType(goal.getGoal().getMeta().getUniqueKey());
                        item.setDisplayName(goal.getGoal().getName());
                        item.setUnit(getWellnessGoalUnitByType(goalName));
                        item.setBackgroundColor(getWellnessCardColor(goalName));
                        item.setGoalTextColor(isUltraFitApp(userContext) ? getUFWellnessCardTextColor(goalName)
                                : getWellnessCardTextColor(goalName));
                        item.setLogAction(getGoalsLogAction(goalName, userContext, chronicCareServiceHelper));
                        item.setCardClickAction(Action.builder().actionType(ActionType.NAVIGATION)
                                .url("curefit://goalsprogress?goalType=" + goal.getGoal().getMeta().getUniqueKey())
                                .build());

                        wellnessAtGlanceItems.add(item);
                    }
                }
                wellnessDataPerDay.setDate(loopDateString);
                wellnessDataPerDay.setWellnessAtGlanceCards(wellnessAtGlanceItems);
                wellnessData.add(wellnessDataPerDay);
                if (isUltraFitApp(userContext)) {
                    wellnessDataPerDay.setHomePageHeartRateData(getHeartRateGlanceData(userContext,
                            this.atStartOfDay(loopDate, ChronicCareAppUtil.getUserTimezone(userContext)),
                            this.atEndOfDay(loopDate, ChronicCareAppUtil.getUserTimezone(userContext))));
                }
            } catch (Exception e) {
                this.exceptionReportingService.reportException("Error in getting recent goals data ==>", e);
            }
        }

        wellnessAtGlanceWidget.setData(wellnessData);

        FitnessDeviceSyncMeta fitnessDeviceSyncMeta = chronicCareServiceHelper.getFitnessDeviceSyncMeta(userContext);
        wellnessAtGlanceWidget.setActiveDeviceId(fitnessDeviceSyncMeta.getActiveDeviceId());
        if (fitnessDeviceSyncMeta.getLastSyncedTime() != null) {
            wellnessAtGlanceWidget.setLastSyncedTime(fitnessDeviceSyncMeta.getLastSyncedTime());
            wellnessAtGlanceWidget.setLastSyncedDisplayTime(fitnessDeviceSyncMeta.getLastSyncedDisplayTime());
        }

        wellnessAtGlanceWidget.setTitleAction(Action.builder().title("PROGRESS").actionType(ActionType.NAVIGATION)
                .url("curefit://goalsprogress").build());

        boolean showConnectWithCult = isUltraFitApp(userContext) && getShowSyncWithCultFitness(userContext);
        wellnessAtGlanceWidget.setShowConnectWithCult(showConnectWithCult);

        return wellnessAtGlanceWidget;
    }

    private void addBadgesDataToWellnessAtGlanceWidget(UserContext userContext, SfWellnessAtGlanceWidget widget) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);

        try {
            UserBadgeSummary summaryResponse = chsClient.filterUserBadgesSummary(userId, timeZone, appTenant);

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(summaryResponse.getBadges())) {
                SfUserBadgeSummary badgeSummary = new SfUserBadgeSummary();
                AtomicBoolean firstLogPending = new AtomicBoolean(true);
                List<Action> logActions = new ArrayList<>();

                summaryResponse.getBadges().forEach(badge -> {
                    if (!(badge.getStatus().equals(UserBadgeProgressStatus.PENDING)
                            && badge.getBadgeEntry().getOrder() == 0)) {
                        firstLogPending.set(false);
                    }

                    Action logAction = getLogActionFromBadgeCategory(badge.getBadgeEntry().getCategory(), userContext,
                            serviceInterfaces);
                    logAction.setTitle(getBadgeCategoryDisplayName(badge.getBadgeEntry().getCategory()));
                    logAction.setContentType(String.valueOf(badge.getBadgeEntry().getCategory()));
                    logActions.add(logAction);
                });

                badgeSummary.setBadges(summaryResponse.getBadges().stream()
                        .map(badge -> this.getBadgeEntry(badge, userContext)).toList());
                badgeSummary.setFirstLogPending(firstLogPending.get());
                badgeSummary.setLogActions(logActions);
                widget.setBadgeSummary(badgeSummary);
            }
        } catch (Exception e) {
            log.error("Error fetching badge summary for user :: {}. error :: {}", userId, e.getMessage(), e);
            exceptionReportingService.reportException("Error fetching badge summary for user", e);
        }
    }

    private SfBadgeEntry getBadgeEntry(UserBadgeProgressEntry badge, UserContext userContext) {
        BadgeCategory category = badge.getBadgeEntry().getCategory();
        BadgeEntry badgeEntry = badge.getBadgeEntry();

        Map<String, Object> badgeMetaData = badgeEntry.getMetadata();

        String formattedBadgeDescription = badge.getStatus().equals(UserBadgeProgressStatus.ACHIEVED)
                ? String.format("You earned this badge by meeting your goal of %s", badgeMetaData.get("title"))
                : String.format("You will earn this badge by meeting your goal of %s", badgeMetaData.get("title"));

        badgeMetaData.put("shareTitle", "I have achieved this Sugar.fit badge by daily logging activity!");
        badgeMetaData.put("badgeDescription", formattedBadgeDescription);

        return SfBadgeEntry.builder()
                .id(badge.getId())
                .category(category)
                .categoryDisplayName(getBadgeCategoryDisplayName(category))
                .badgeTitle(String.valueOf(badge.getBadgeEntry().getMetadata().get("title")))
                .badgeProgressTitle(getBadgeProgressTitle(badge))
                .badgeProgressColor(getProgressColor(category))
                .status(badge.getStatus())
                .target(badgeEntry.getTarget())
                .targetType(badgeEntry.getTargetType())
                .currentStreak(badge.getCurrentStreak())
                .targetStreakInDays(badgeEntry.getTargetStreakInDays())
                .firstAchievedDate(badge.getFirstAchievedDate())
                .lastAchievedDate(badge.getLastAchievedDate())
                .badgeUrlFilled(String.valueOf(badge.getBadgeEntry().getMetadata().get("badgeImageFilled")))
                .badgeUrlUnfilled(String.valueOf(badge.getBadgeEntry().getMetadata().get("badgeImageUnfilled")))
                .badgeAnimationUrl(String.valueOf(badge.getBadgeEntry().getMetadata().get("badgeVideo")))
                .badgeMetaData(badgeMetaData)
                .progressMetaData(badge.getMetadata())
                .logAction(getLogActionFromBadgeCategory(category, userContext, serviceInterfaces)).build();
    }

    private boolean getShowSyncWithCultFitness(UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            Map<Boolean, Long> permission = sfFitnessClient.checkIfCultClassAccessPermissionSet(userId);

            if (permission.containsKey(false) && permission.get(false) != null && permission.get(false) != 0) {
                String cultUserId = String.valueOf(permission.get(false));
                return isSyncWithCultFitnessEnabled(cultUserId);
            } else {
                return false;
            }
        } catch (Throwable e) {
            exceptionReportingService.reportException("Failed fetching flag to show sync with cult fitness", e);
            return false;
        }
    }

    public WellnessDataPerDay.HomePageHeartRateData getHeartRateGlanceData(UserContext userContext, long startTime,
            long endTime) {
        String userId = userContext.getUserProfile().getUserId();
        String deviceId = userContext.getSessionInfo().getDeviceId();
        WellnessDataPerDay.HomePageHeartRateData heartRateData = new WellnessDataPerDay.HomePageHeartRateData();
        heartRateData.setColor("#F7BA41");

        Hashtable<Integer, String> xAxisHR = new Hashtable<>();
        xAxisHR.put(0, "12am");
        xAxisHR.put(60 * 6, "6am");
        xAxisHR.put(60 * 12, "12pm");
        xAxisHR.put(60 * 18, "6pm");
        xAxisHR.put(60 * 24 - 1, "12am");

        FitnessDataRequest req = new FitnessDataRequest();
        req.setStartTime(new Date(startTime));
        req.setEndTime(new Date(endTime));
        req.setUserId(Long.valueOf(userId));
        try {
            ActiveDeviceEntry activeDeviceEntry = this.chsClient.fetchActiveDevice(Long.valueOf(userId));
            if (activeDeviceEntry != null && activeDeviceEntry.getDeviceId() != null)
                deviceId = activeDeviceEntry.getDeviceId();
        } catch (Throwable e) {
            exceptionReportingService.reportException("Failed fetching active device id", e);
        }
        req.setDeviceId(deviceId);
        req.setGranularity(GranularityType.DAY);
        try {
            HeartRateDataResponse dataSet = this.chsClient.getHeartRateData(req);
            int[] yData = this.getFormattedHeartRate(dataSet, ChronicCareAppUtil.getUserTimezone(userContext));
            heartRateData.setHeartRatePoints(yData);
            heartRateData.setXaxisHR(xAxisHR);
            heartRateData.setHeartRate(dataSet);
        } catch (HttpException e) {
            log.info(e.getMessage());
            exceptionReportingService.reportException("Error in vital page heartrate ==>", e);
            heartRateData.setHeartRate(null);
        }
        return heartRateData;
    }

    public int[] getFormattedHeartRate(HeartRateDataResponse dataSet, TimeZone timeZone) {
        if (dataSet == null) {
            return new int[0];
        }
        List<HeartRateDataPoint> data = dataSet.getData();
        int len = data.size();
        int[] array = new int[60 * 24];

        if (len == 0)
            return new int[0];
        if (len == 1) {
            array[this.getIndexFromTime(data.get(0).getStartTime(), timeZone)] = (int) Math
                    .round(data.get(0).getValue());
            return array;
        }

        for (int i = 0; i < len - 1; i++) {
            if (data.get(i).getEndTime().getTime() != data.get(i).getStartTime().getTime()) {
                Date x1 = data.get(i).getStartTime();
                Date x2 = data.get(i).getEndTime();
                int startIndex = this.getIndexFromTime(x1, timeZone);
                int endIndex = this.getIndexFromTime(x2, timeZone);
                for (int k = startIndex; k <= endIndex; k++) {
                    array[k] = (int) Math.round(data.get(i).getValue());
                }
            }
            Date x1 = data.get(i).getStartTime();
            Date x2 = data.get(i + 1).getStartTime();

            int startIndex = this.getIndexFromTime(x1, timeZone);
            int endIndex = this.getIndexFromTime(x2, timeZone);
            array[startIndex] = (int) Math.round(data.get(i).getValue());
            array[endIndex] = (int) Math.round(data.get(i + 1).getValue());
            if (endIndex - startIndex < HEART_RATE_DIFF_ALLOWED) {
                double ydiff = data.get(i + 1).getValue() - data.get(i).getValue();
                int xdiff = endIndex - startIndex;
                double slope = ydiff / xdiff;
                for (int k = 1; k < endIndex - startIndex; k++) {
                    array[startIndex + k] = (int) (slope * k + Math.round(data.get(i).getValue()));
                }
            }
        }
        return array;
    }

    public String[] getTimeValue() {
        String[] val = new String[24 * 60];
        for (int i = 0; i < 24 * 60; i++) {
            val[i] = this.getTimeFromIndex(i);
        }
        return val;
    }

    public int getIndexFromTime(Date date, TimeZone timeZone) {
        Calendar cal = Calendar.getInstance(timeZone);
        cal.setTime(date);
        int hour = cal.get(Calendar.HOUR_OF_DAY) * 60;
        int mm = cal.get(Calendar.MINUTE);
        return hour + mm;
    }

    public String getTimeFromIndex(int index) {
        int hour = (int) Math.floor(index / 60);
        int min = index % 60;
        String mm = min <= 10 ? "0" + String.valueOf(min) : String.valueOf(min);
        if (hour > 12)
            return hour - 12 + ":" + mm + "pm";
        else if (hour == 12)
            return 12 + ":" + mm + "pm";
        else
            return hour + ":" + mm + "am";
    };

    public Long atEndOfDay(Date date, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance(timeZone);
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTimeInMillis();
    }

    public Long atStartOfDay(Date date, TimeZone timeZone) {
        Calendar calendar = Calendar.getInstance(timeZone);
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    public String getWellnessCardColor(String goalName) {
        return switch (goalName) {
            case "STEPS" -> "#FFE8E9";
            case "CALORIES-EATEN" -> "#DCF0F3";
            case "CALORIES-BURNED" -> "#FFE8E9";
            case "SLEEP" -> "#FFEBC9";
            case "WEIGHT" -> "#DDEDFB";
            case "FASTING-GLUCOSE" -> "#DADBDD";
            default -> "#FFE8E9";
        };
    }

    public String getWellnessCardTextColor(String goalName) {
        return switch (goalName) {
            case "STEPS" -> "#5D2011";
            case "CALORIES-EATEN" -> "#2C5257";
            case "CALORIES-BURNED" -> "#5D2011";
            case "SLEEP" -> "#764B04";
            case "WEIGHT" -> "#1E3C57";
            case "FASTING-GLUCOSE" -> "#5C5C78";
            default -> "#5D2011";
        };
    }

    public String getUFWellnessCardTextColor(String goalName) {
        return switch (goalName) {
            case "STEPS" -> "#FF808A";
            case "CALORIES-EATEN" -> "#57C8C6";
            case "CALORIES-BURNED" -> "#FF808A";
            case "SLEEP" -> "#F9B227";
            case "WEIGHT" -> "#5080AE";
            case "FASTING-GLUCOSE" -> "#5C5C78";
            default -> "#FF808A";
        };
    }

    boolean isBackgroundAnimationDisabled(String userId) {
        try {
            SegmentSet<String> userSegments = segmentationCacheClient.getUserSegments(userId);
            return userSegments.contains(BG_ANIMATION_DISABLED_SEGMENT);
        } catch (Exception e) {
            String message = String.format("Background animation disabled segment error, userId :: %s, error :: %s",
                    userId, e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    boolean isSyncWithCultFitnessEnabled(String userId) {
        try {
            SegmentSet<String> userSegments = segmentationCacheClient.getUserSegments(userId);
            return  userSegments.contains(CULT_MEMBERS_SEGMENT) ||
                    userSegments.contains(CULTLIVE_MEMBERS_SEGMENT) ||
                    userSegments.contains(GYMFIT_MEMBERS_SEGMENT);
        } catch (Exception e) {
            String message = String.format("Cult fitness sync enabled segment error, userId :: %s, error :: %s", userId,
                    e.getMessage());
            log.error(message, e);
            return false;
        }
    }
}