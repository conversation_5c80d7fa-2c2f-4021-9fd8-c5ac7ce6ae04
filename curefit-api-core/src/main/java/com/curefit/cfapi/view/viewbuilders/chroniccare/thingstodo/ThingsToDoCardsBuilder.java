package com.curefit.cfapi.view.viewbuilders.chroniccare.thingstodo;

import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.actions.UserPreferencePojo;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.dto.ChronicCareTeam;
import com.curefit.cfapi.dto.sugarfit.thingstodo.*;
import com.curefit.cfapi.model.internal.chroniccare.FitnessDeviceSyncMeta;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.chroniccare.nux.DiagnosticActionMeta;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.service.DeviceService;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.view.viewmodels.transform.HabitCard;
import com.curefit.diyfs.client.spi.DiyfsService;
import com.curefit.ollivander.common.pojo.response.agent.AgentResponse;
import com.curefit.product.models.diy.DIYProduct;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.sugarfit.challenges.client.ChallengesClient;
import com.sugarfit.challenges.pojo.ChallengesEntry;
import com.sugarfit.chs.pojo.CgmOnboardingStatusResponse;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalScansForDayResponse;
import com.sugarfit.sms.enums.UserTodoTaskType;
import com.sugarfit.sms.response.UserTodoResponse;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper.SUGARFIT_PSYCHOLOGIST_AGENT_ID;
import static com.curefit.cfapi.util.ChronicCareAppUtil.isUnlimitedFaceScanEnabledUser;
import static com.curefit.cfapi.util.ChronicCareAppUtil.resolveNullable;
import static com.curefit.cfapi.util.SfHomePageUtil.DIYFS_TENANT;
import static com.curefit.cfapi.util.SfHomePageUtil.getAgentDatePickerUrl;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ThingsToDoCardsBuilder {
    final ServiceInterfaces serviceInterfaces;
    final ChallengesClient challengesClient;
    final DiyfsService diyfsService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final ExceptionReportingService exceptionReportingService;
    final DeviceService deviceService;

    public ThingsToDoCard getLoggingItemCard(List<UserTodoResponse> loggingTodoList, UserContext userContext, boolean isLoggingCompleted) {
        ThingsToDoCard card = new ThingsToDoCard();
        List<LoggingItem> loggingItems = new ArrayList<>();

        try {
            loggingTodoList.forEach(loggingTask -> {
                switch (loggingTask.getUserTodoTaskType()) {
                    case FOOD_LOGGING -> {
                        loggingItems.add(getFoodLoggingItem(loggingTask));
                        card.setUserTodoTaskType(UserTodoTaskType.FOOD_LOGGING);
                    }
                    case SUGAR_LOGGING -> {
                        loggingItems.add(getSugarLoggingItem(userContext, loggingTask));
                        card.setUserTodoTaskType(UserTodoTaskType.SUGAR_LOGGING);
                    }
                    case WEIGHT_LOGGING -> {
                        loggingItems.add(getWeightLoggingItem(loggingTask, userContext));
                        card.setUserTodoTaskType(UserTodoTaskType.WEIGHT_LOGGING);
                    }
                }
            });

            card.setCardUiType(CardUiType.LOGGING);
            card.setLoggingItems(loggingItems);
            card.setTitle("Log your activities");
            card.setColor(getCardColor(UserTodoTaskType.FOOD_LOGGING));
            card.setCompleted(isLoggingCompleted);
            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    private LoggingItem getFoodLoggingItem(UserTodoResponse loggingTask) {
        Action foodLogAction = Action.builder().actionType(ActionType.SHOW_SF_MEAL_SLOT_PICKER_MODAL).build();
        Long loggedCount = loggingTask.getMetadata().getFoodLoggedCount();
        Long requiredCount = loggingTask.getMetadata().getFoodLoggingRequiredCount();
        return new LoggingItem(
                "Food",
                "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/homepage/thingstodo/food_log.png",
                loggedCount,
                requiredCount,
                loggingTask.getIsCompleted(),
                foodLogAction);
    }

    private LoggingItem getSugarLoggingItem(UserContext userContext, UserTodoResponse loggingTask) {
        Action sugarLogAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sugarlogging").build();
        if (ChronicCareAppUtil.isSfSmartGlucometerPageSupported(userContext)) {
            sugarLogAction = Action.builder().actionType(
                    ActionType.SHOW_SF_SUGAR_LOGGING_MODE_SELECTION_MODAL).url("curefit://sugarlogging"
            ).build();
        }
        Long loggedCount = loggingTask.getMetadata().getSugarLoggedCount();
        Long requiredCount = loggingTask.getMetadata().getSugarLoggingRequiredCount();
        return new LoggingItem(
                "Sugar",
                "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/homepage/thingstodo/sugar_log.png",
                loggedCount,
                requiredCount,
                loggingTask.getIsCompleted(),
                sugarLogAction);
    }

    private LoggingItem getWeightLoggingItem(UserTodoResponse loggingTask, UserContext userContext) {
        Action weightLogAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://goalsupdatestats").build();
        if (chronicCareServiceHelper.isSmartScaleDelivered(userContext)) {
            if (ChronicCareAppUtil.isUserExperiencePartTwoReleased(userContext)) {
                weightLogAction = Action.builder()
                        .actionType(ActionType.SHOW_SF_WEIGHT_LOGGING_MODE_SELECTION_MODAL)
                        .meta(new HashMap<>() {
                            {
                                put("manualLoggingPageUrl", "curefit://sfweighingscalevitallistpage");
                            }
                        })
                        .build();
            } else {
                weightLogAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfweighingscalereaderpage").build();
            }
        }
        Long loggedCount = loggingTask.getMetadata().getWeightLoggedCount();
        Long requiredCount = loggingTask.getMetadata().getWeightLoggingRequiredCount();
        return new LoggingItem(
                "Weight",
                "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/homepage/thingstodo/weight_log_v5.png",
                loggedCount,
                requiredCount,
                loggingTask.getIsCompleted(),
                weightLogAction);
    }

    public ThingsToDoCard getBookCoachConsultCard(UserTodoResponse task, ChronicCareTeam assignedCareTeam, ActivePackResponse activePackResponse) {
        try {
            ThingsToDoCard card = new ThingsToDoCard();

            Long agentId = assignedCareTeam.getCoach().getAgentResponse().getId();
            Long centerId = assignedCareTeam.getCoach().getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
            String productCode = task.getMetadata().getConsultProductCode();
            Action action = Action.builder()
                    .isEnabled(true)
                    .title("Book Consult")
                    .actionType(ActionType.NAVIGATION)
                    .url(getAgentDatePickerUrl(productCode, centerId, agentId, activePackResponse))
                    .build();

            Optional<String> image = resolveNullable(() -> {
                try {
                    return assignedCareTeam.getCoach().getAgentResponse().getDisplayImage();
                } catch (Exception e) {
                    return null;
                }
            });
            String imageUrl = image.orElse("/image/chroniccare/coach_female.png");

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle("Book Coach Consult");
            card.setSubTitle("Choose a slot for video consultation");
            card.setImageUrl(imageUrl);
            card.setAction(action);
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    public ThingsToDoCard getBookPsychologistConsultCard(UserTodoResponse task, ActivePackResponse activePackResponse) {
        try {
            ThingsToDoCard card = new ThingsToDoCard();
            String productCode = task.getMetadata().getConsultProductCode();
            Action action = Action.builder()
                    .isEnabled(true)
                    .title("Book Consult")
                    .actionType(ActionType.NAVIGATION)
                    .url(chronicCareServiceHelper.getPsychologistAgentDatePickerUrl(productCode, activePackResponse))
                    .build();

            Optional<String> image = resolveNullable(() -> {
                try {
                    try {
                        AgentResponse psychologistAgent = serviceInterfaces.getOllivanderAgentClient().getAgent(SUGARFIT_PSYCHOLOGIST_AGENT_ID);
                        return psychologistAgent.getDisplayImage();
                    } catch (Exception e) {
                        return null;
                    }
                } catch (Exception e) {
                    return null;
                }
            });
            String imageUrl = image.orElse("/image/chroniccare/coach_female.png");

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle("Book Psychologist Consult");
            card.setSubTitle("Choose a slot for video consultation");
            card.setImageUrl(imageUrl);
            card.setAction(action);
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }


    public ThingsToDoCard getBookDoctorConsultCard(UserTodoResponse task, UserContext userContext, ChronicCareTeam assignedCareTeam, ActivePackResponse activePackResponse) {
        try {
            if (assignedCareTeam.getDoctor() == null) {
                return null;
            }

            ThingsToDoCard card = new ThingsToDoCard();

            Long agentId = assignedCareTeam.getDoctor().getAgentResponse().getId();
            Long centerId = assignedCareTeam.getDoctor().getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
            String productCode = task.getMetadata().getConsultProductCode();
            Action doctorBookingAction = Action.builder()
                    .isEnabled(true)
                    .title("Book Consult")
                    .actionType(ActionType.NAVIGATION)
                    .url(getAgentDatePickerUrl(productCode, centerId, agentId, activePackResponse))
                    .build();
            Action action = chronicCareServiceHelper.getModifiedDoctorConsultActionBasedOnCgmCompletion(userContext, activePackResponse, doctorBookingAction);

            Optional<String> image = resolveNullable(() -> {
                try {
                    return assignedCareTeam.getDoctor().getAgentResponse().getDisplayImage();
                } catch (Exception e) {
                    return null;
                }
            });
            String imageUrl = image.orElse("/image/chroniccare/coach_female.png");

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle("Book Doctor Consult");
            card.setSubTitle("Choose a slot for video consultation");
            card.setImageUrl(imageUrl);
            card.setAction(action);
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    public ThingsToDoCard getBookDiagnosticsCard(UserContext userContext, UserTodoResponse task,
                                                 ActivePackResponse activePackResponse, PatientDetail patientDetail) {
        try {
            ThingsToDoCard card = new ThingsToDoCard();

            String userId = userContext.getUserProfile().getUserId();
            UserPreferencePojo userAddressIdPreference = serviceInterfaces.sfAlbusClient.getUserPreference(userId, UserPreferencePojo.PreferenceType.META, "addressId");
            String addressId = userAddressIdPreference.getPreferenceTypeValues().getFirst();
            String diagnosticProductCode = task.getMetadata().getDiagnosticProductCode();
            String provider = task.getMetadata().getDiagnosticPartner();
            Header header = new Header();
            header.setTitle("Instructions");
            Action action = Action.builder().isEnabled(true)
                    .title("Book Test").actionType(ActionType.NAVIGATION)
                    .meta(DiagnosticActionMeta.builder().header(header).build())
                    .url("curefit://selectCareDateV1?patientId=" + patientDetail.getId()
                            + "&productId=" + diagnosticProductCode + "&type=" + "DIAGNOSTICS"
                            + "&category=AT_HOME_SLOT&nextAction=checkout&productCodes=" + diagnosticProductCode
                            + "&addressId=" + addressId
                            + "&parentBookingId=" + activePackResponse.getBookingId() + "&diagnosticsProvider=" + provider).build();

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle("Book Blood Test");
            card.setSubTitle("Know your latest HbA1c");
            card.setImageUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/homepage/thingstodo/diagnostics_test.png");
            card.setAction(action);
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    public ThingsToDoCard getFaceScanCard(UserTodoResponse task, UserContext userContext,
                                          CgmOnboardingStatusResponse cgmOnboardingStatusResponse, ActivePackResponse activePackResponse,
                                          FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        try {
            if (ChronicCareAppUtil.isFaceBasedVitalsEnabledForUser(userContext, cgmOnboardingStatusResponse, activePackResponse, faceBasedVitalScansForDayResponse)) {
                ThingsToDoCard card = new ThingsToDoCard();

                Action action = new Action("curefit://sfscanfacepage", "Scan Now", ActionType.NAVIGATION);

                card.setUserTodoTaskType(task.getUserTodoTaskType());
                card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
                card.setColor(getCardColor(task.getUserTodoTaskType()));
                card.setTitle("Take a Healthy-Selfie");
                card.setSubTitle("Scan and get your vitals from selfie");
                card.setImageUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/homepage/thingstodo/face_based_vitals.png");
                card.setAction(action);
                card.setCompleted(task.getIsCompleted());
                if (task.getIsCompleted()) {
                    if(isUnlimitedFaceScanEnabledUser(userContext)) {
                        card.setPostCompletionAction(action);
                    } else {
                        card.setPostCompletionAction(new Action("curefit://fbvhistorypage?tab=logs", "View Data", ActionType.NAVIGATION));
                    }
                }
                return card;
            }
            return null;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    public ThingsToDoCard getDiagnosticReportCard(UserTodoResponse task) {
        try {
            ThingsToDoCard card = new ThingsToDoCard();

            Action action = new Action();
            String reportUrl = task.getMetadata().getReportUrl();
            Map<String, Object> actionMeta = new HashMap<>();
            actionMeta.put("fileUrl", reportUrl);
            actionMeta.put("pageTitle", "Lab Report");
            action.setTitle("View Report");
            action.setActionType(ActionType.SF_VIEW_ASSET);
            action.setMeta(actionMeta);

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle("Your blood test report is here");
            card.setSubTitle("Click to check your progress");
            card.setImageUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/homepage/thingstodo/diagnostics_test.png");
            card.setAction(action);
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    public ThingsToDoCard getFitnessSyncCard(UserContext userContext, UserTodoResponse task) {
        try {
            ThingsToDoCard card = new ThingsToDoCard();

            Action action = new Action();
            action.setActionType(ActionType.SYNC_FITNESS_DEVICE);
            action.setTitle("Sync Now");
            FitnessDeviceSyncMeta meta = serviceInterfaces.chronicCareServiceHelper.getFitnessDeviceSyncMeta(userContext);
            action.setMeta(meta);

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle("Sync Your Fitness Watch");
            card.setSubTitle("Data will help us create better plans for you");
            card.setImageUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/homepage/thingstodo/pair_fitness_device.png");
            card.setAction(action);
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    public ThingsToDoCard getOfflineAccessCard(UserTodoResponse task) {
        try {
            ThingsToDoCard card = new ThingsToDoCard();

            Action action = Action.builder().title("Book Now").url("curefit://sfwellnessatcenterclp").actionType(ActionType.NAVIGATION).build();

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle("Access Offline Center"); // TODO
            card.setSubTitle("subtitle"); // TODO
            card.setImageUrl(""); // TODO
            card.setAction(action);
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    public ThingsToDoCard getSaveCoachContactCard(UserContext userContext, UserTodoResponse task) {
        try {
            ThingsToDoCard card = new ThingsToDoCard();

            Action action;
            try {
                action = serviceInterfaces.chronicCareServiceHelper.getContactDetailsAction(userContext);
            } catch (Exception e) {
                return null;
            }

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle("Save Contact Info");
            card.setSubTitle("Save your coach's contact information to avoid missing your scheduled audio consultation");
            card.setImageUrl(""); // TODO
            card.setAction(action);
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    public ThingsToDoCard getCoachCelebrationCard(UserTodoResponse task, ChronicCareTeam chronicCareTeam) {
        try {
            ThingsToDoCard card = new ThingsToDoCard();

            String actionUrl = "curefit://sfcoachcelebrationpage";
            Action action = Action.builder().actionType(ActionType.NAVIGATION).url(actionUrl).title("Send Thank You Card").build();

            Optional<String> image = resolveNullable(() -> {
                try {
                    return chronicCareTeam.getCoach().getAgentResponse().getDisplayImage();
                } catch (Exception e) {
                    return null;
                }
            });
            String imageUrl = image.orElse("/image/chroniccare/coach_female.png");

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.SINGLE_ACTION_3D_IMAGE);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle("Thank your coach");
            card.setSubTitle("Your coach is always there for you! It’s your turn to thank your coach");
            card.setImageUrl(imageUrl);
            card.setAction(action);
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    public ThingsToDoCard getJoinChallengeCard(UserContext userContext, UserTodoResponse task) {
        try {
            if (ChronicCareAppUtil.isChallengesDisabledForUser(userContext)) {
                return null;
            }

            ThingsToDoCard card = new ThingsToDoCard();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());

            Long challengeId = task.getMetadata().getChallengeId();
            Action action = new Action();
            action.setTitle("Join Challenge");
            action.setUrl("curefit://sfchallengedetailspage?challengeId=" + challengeId + "&tabKey=ABOUT");
            action.setActionType(ActionType.NAVIGATION);

            ChallengesEntry challengesEntry = challengesClient.fetchChallenge(userId, challengeId);
            SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                    RequestType.PLATFORM_SEGMENTS,
                    userContext).get();
            Set<String> relevantSegments = serviceInterfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.SF_CHALLENGES_CLIENT);
            List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(relevantSegments));
            List<ChallengesEntry> activeChallenges = challengesClient.fetchActiveChallengesForUser(userId, 0, 5, "createdOn", "DESC", userSegments).getElements();
            if (!CollectionUtils.isEmpty(activeChallenges) && Objects.nonNull(challengesEntry)) {
                boolean isChallengeActive = activeChallenges.stream().anyMatch(c -> c.getId().equals(challengeId));
                if (isChallengeActive) {
                    long totalUserCount;
                    List<ChallengeMeta.UserProfile> joinedUsers = new ArrayList<>();
                    List<Long> joinedUserIds;
                    if (!CollectionUtils.isEmpty(challengesEntry.getJoinedUserIds()) && challengesEntry.getParticipantCount() != null) {
                        totalUserCount = challengesEntry.getParticipantCount();
                        joinedUserIds = challengesEntry.getJoinedUserIds();
                    } else {
                        totalUserCount = 10;
                        List<Long> prodUserIds = new ArrayList<Long>(List.of(192L, 3758L, 13014L, 14468L, 20472L, 23465L, 26058L, 26919L, 27897L, 30643L, 31351L, 34629L, 35465L, 37118L, 37841L, 38364L, 38986L, 41828L, 44035L));
                        List<Long> stageUserIds = new ArrayList<Long>(List.of(4080998L, 4087783L, 4087868L, 4087783L, 4080998L, 4087783L));
                        joinedUserIds = serviceInterfaces.environmentService.isStage() ? stageUserIds : prodUserIds;
                        Collections.shuffle(joinedUserIds);
                    }

                    joinedUserIds.forEach(joinedUserId -> {
                        //Only 2 pics needed for UI
                        if (joinedUsers.size() < 3) {
                            UserEntry userEntry = null;
                            try {
                                userEntry = serviceInterfaces.userServiceClient.getUser(String.valueOf(joinedUserId)).get();
                            } catch (Exception e) {
                                // ignore
                            }
                            if (userEntry != null) {
                                String userProfilePic = userEntry.getProfilePictureUrl();
                                if (userProfilePic != null && !userProfilePic.isEmpty()) {
                                    String userName = userEntry.getFirstName() + " " + userEntry.getLastName();
                                    joinedUsers.add(new ChallengeMeta.UserProfile(userName, userProfilePic));
                                }
                            }
                        }
                    });

                    ChallengeMeta challengeMeta = new ChallengeMeta(joinedUsers, totalUserCount);

                    Calendar userCalendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
                    Date challengeStartDate = challengesEntry.getStartDate();
                    String subTitlePrefix = userCalendar.getTime().getTime() < challengeStartDate.getTime() ? "Starts on " : "Started on ";
                    DateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy");
                    String startDateString = dateFormat.format(challengeStartDate);
                    String subtitle = subTitlePrefix + startDateString;

                    card.setUserTodoTaskType(task.getUserTodoTaskType());
                    card.setCardUiType(CardUiType.CHALLENGE_OR_POLL);
                    card.setColor(getCardColor(task.getUserTodoTaskType()));
                    card.setTitle(challengesEntry.getTitle());
                    card.setSubTitle(subtitle);
                    card.setAction(action);
                    card.setChallengeMeta(challengeMeta);
                    card.setCompleted(task.getIsCompleted());

                    return card;
                }
            }

        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public ThingsToDoCard getInterventionCard(UserContext userContext, UserTodoResponse task, Boolean isLiveClass) {
        try {
            ThingsToDoCard card = new ThingsToDoCard();

            Map<String, String> skipActionMeta = new HashMap<>();
            skipActionMeta.put("activityId", String.valueOf(task.getMetadata().getUserActivityId()));
            skipActionMeta.put("value", "NOT_DONE");
            Action skipAction = Action.builder().title("Not Done").meta(skipActionMeta).actionType(ActionType.SF_UPDATE_INTERVENTION).iconUrl("close").build();

            Map<String, String> doneActionMeta = new HashMap<>();
            doneActionMeta.put("activityId", String.valueOf(task.getMetadata().getUserActivityId()));
            doneActionMeta.put("value", "DONE");
            Action doneAction = Action.builder().title("Done").meta(doneActionMeta).actionType(ActionType.SF_UPDATE_INTERVENTION).iconUrl("check").build();

            if (isLiveClass) {
                addLiveClassInterventionInfo(userContext, card, task);
            }

            card.setUserTodoTaskType(task.getUserTodoTaskType());
            card.setCardUiType(CardUiType.INTERVENTION);
            card.setColor(getCardColor(task.getUserTodoTaskType()));
            card.setTitle(task.getDescription());
            card.setActions(List.of(skipAction, doneAction));
            card.setCompleted(task.getIsCompleted());

            return card;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    private void addLiveClassInterventionInfo(UserContext userContext, ThingsToDoCard card, UserTodoResponse task) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            String liveClassProductId = task.getMetadata().getLiveClassProductId();
            Action action = new Action("curefit://liveclassdetail?liveClassId=" + liveClassProductId + "&bookingNumber=" + liveClassProductId + "&productType=LIVE_FITNESS&isDIY=true", ActionType.NAVIGATION);
            List<DIYProduct> products = diyfsService.getDIYFitnessProductsByProductIds(userId, Collections.singletonList(liveClassProductId), DIYFS_TENANT);
            if (products.size() == 1) {
                HabitCard.WorkoutInfo workoutInfo = new HabitCard.WorkoutInfo(products.get(0).getSubTitle(), products.get(0).getDuration() / 60000 + " min", products.get(0).getImageDetails().getThumbnailImage(), action, false);
                InterventionMeta meta = new InterventionMeta(workoutInfo);
                card.setInterventionMeta(meta);
            }
        } catch (Exception e) {
            String errorMessage = String.format("Error in fetching live class for user :: %s", userId);
            log.error(errorMessage, e);
        }
    }

    private String getCardColor(UserTodoTaskType taskType) {
        return switch (taskType) {
            case FOOD_LOGGING -> "#A195FF"; // Light Purple
            case FBV_SCAN, BOOK_COACH_CONSULT, BOOK_DOCTOR_CONSULT, PSYCHOLOGIST_CONSULT, FITNESS_DEVICE_SYNC, BOOK_DIAGNOSTICS, VIEW_DIAGNOSTIC_REPORT -> "#95D2FF"; // Blue
            case INTERVENTIONS, LIVE_CLASS -> "#85E5E4"; // Green
            case JOIN_CHALLENGE, CELEBRATE_YOUR_COACH -> "#FFD894"; //Yellow
            default -> "#A195FF"; // Light Purple
        };
    }
}
