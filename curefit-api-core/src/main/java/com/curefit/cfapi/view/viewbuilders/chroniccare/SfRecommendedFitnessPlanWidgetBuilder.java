package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.base.enums.Tenant;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.widgets.chroniccare.SfRecommendedFitnessPlanWidget;
import com.curefit.diyfs.client.spi.DiyfsService;
import com.curefit.product.models.diy.ContentType;
import com.curefit.product.models.diy.DIYProduct;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sugarfit.fitness.client.PlanClient;
import com.sugarfit.fitness.enums.PlanType;
import com.sugarfit.fitness.pojo.PlanEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class SfRecommendedFitnessPlanWidgetBuilder {
    final PlanClient planClient;
    final DiyfsService diyfsService;
    final ObjectMapper objectMapper;
    final ExceptionReportingService exceptionReportingService;

    public SfRecommendedFitnessPlanWidget buildView(UserContext userContext) {
        SfRecommendedFitnessPlanWidget recommendedFitnessPlanWidget = new SfRecommendedFitnessPlanWidget();
        try {
            Header widgetHeader = new Header();
            String allVideosPageUrl = "curefit://sfrecommendedfitnessplantabpage";
            Action widgetSeeMoreAction = Action.builder().actionType(ActionType.NAVIGATION).url(allVideosPageUrl).title("VIEW ALL").build();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            Tenant tenant = Tenant.fromString(AppUtil.getTenantFromUserContext(userContext).toString());
            List<PlanEntry> planEntries = planClient.fetchAllActive(userId);

            if (!CollectionUtils.isEmpty(planEntries)) {
                List<DIYProduct> diyProducts = new ArrayList<>();
                planEntries.forEach(planEntry -> {
                    if (planEntry.getPlanType().equals(PlanType.FITNESS)) {
                        diyProducts.addAll(diyfsService.getDIYFitnessProductsByProductIds(String.valueOf(userId), List.of(planEntry.getContentId()), tenant));
                    } else if (planEntry.getPlanType().equals(PlanType.MINDFULNESS)) {
                        diyProducts.addAll(diyfsService.getDIYMeditationProductsByProductIds(String.valueOf(userId), List.of(planEntry.getContentId()), tenant));
                    }
                });

                AtomicReference<Integer> index = new AtomicReference<>(0);
                if (!CollectionUtils.isEmpty(diyProducts)) {
                    List<SfRecommendedFitnessPlanWidget.BannerItem> banners = planEntries.stream().map(o -> {
                        if (index.get() <= diyProducts.size() - 1) {
                            SfRecommendedFitnessPlanWidget.BannerItem bannerWidget = objectMapper.convertValue(o, SfRecommendedFitnessPlanWidget.BannerItem.class);
                            if (o.getPlanType() == PlanType.FITNESS) {
                                bannerWidget.setColorCode("#E5C065");
                            }
                            if (o.getPlanType() == PlanType.MINDFULNESS) {
                                bannerWidget.setColorCode("#114AA3");
                            }
                            DIYProduct product = diyProducts.get(index.getAndUpdate(v -> v + 1));
                            if (!CollectionUtils.isEmpty(product.getTags())) {
                                Optional<String> imageUrlTag = product.getTags().stream().filter(t -> t.contains("image/upload/image/chroniccare/30-day-fitness")).findFirst();
                                imageUrlTag.ifPresent(bannerWidget::setImageUrl);
                            }
                            if (Objects.isNull(bannerWidget.getImageUrl())
                                    && !Objects.isNull(product.getImageDetails()) && !Objects.isNull(product.getImageDetails().getThumbnailImage())) {
                                bannerWidget.setImageUrl(product.getImageDetails().getThumbnailImage());
                            }
                            if(product.getTitle() != null) {
                                bannerWidget.setTitle(product.getTitle());
                            }
                            if(product.getSubTitle() != null) {
                                bannerWidget.setSubTitle((product.getDuration() / 60000) + " MINS");
                            }
                            Action itemAction = buildWidgetAction(product);
                            if(itemAction != null) {
                                bannerWidget.setAction(itemAction);
                            }
                            return bannerWidget;
                        }
                        return null;
                    }).collect(Collectors.toList());

                    widgetHeader.setTitle("Your 30 Day Plan");
                    widgetHeader.setSeemore(widgetSeeMoreAction);
                    recommendedFitnessPlanWidget.setHeader(widgetHeader);
                    recommendedFitnessPlanWidget.setBanners(banners);
                    return recommendedFitnessPlanWidget;
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }

        return null;
    }

    public Action buildWidgetAction(DIYProduct product) {
        try {
            if (product != null) {
                SfRecommendedFitnessItemWidgetBuilder.Content content = new SfRecommendedFitnessItemWidgetBuilder.Content();
                SfRecommendedFitnessItemWidgetBuilder.QueryParams queryParams = new SfRecommendedFitnessItemWidgetBuilder.QueryParams();
                SfRecommendedFitnessItemWidgetBuilder.Meta meta = new SfRecommendedFitnessItemWidgetBuilder.Meta();
                if(product.getTitle() != null) {
                    meta.setTitle(product.getTitle());
                    queryParams.setTitle(product.getTitle());
                }
                if(product.getProductType() != null) {
                    meta.setProductType(String.valueOf(product.getProductType()));
                    queryParams.setActivityType(String.valueOf(product.getProductType()));
                }
                if(product.getContentId() != null && product.getContentType() != null) {
                    content.setType(String.valueOf(product.getContentType()));
                    content.setId(product.getContentId());
                    String videoOrAudio = product.getContentType().equals(ContentType.audio) ? "audio" : "video";
                    content.setURL(String.format("curefit-content/%s/%s", videoOrAudio, product.getContentId()));
                    if (product.getContentFormat() != null && !content.getURL().contains(product.getContentFormat())) {
                        content.setURL(content.getURL() + "." + product.getContentFormat());
                    }
                    content.setDownloadUrl(product.getDownloadURL());
                    content.setAbsoluteUrl(product.getDownloadURL());

                    queryParams.setContentCategory(String.valueOf(product.getContentType()));
                    queryParams.setContentId(product.getContentId());
                }
                if(product.getContentFormat() != null) {
                    content.setFormat(product.getContentFormat());
                }
                if(product.getImageDetails().getTodayImage() != null) {
                    queryParams.setImage("/" + product.getImageDetails().getTodayImage());
                }
                if(product.getProductId() != null) {
                    queryParams.setActivityId(product.getProductId());
                }
                queryParams.setAdsEnabled(false);
                queryParams.setConsumptionRequired(true);
                meta.setCheckDownloadStatus(true);
                meta.setContent(content);
                meta.setQueryParams(queryParams);
                return Action.builder().actionType(ActionType.PLAY_VIDEO).title("PLAY").meta(meta).build();
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }


    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @Slf4j
    @Component
    @RequiredArgsConstructor
    public static class Content {
        String id;
        String type;
        String format;
        @JsonProperty("URL")
        String URL;
        String absoluteUrl;
        String downloadUrl;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @Slf4j
    @Component
    @RequiredArgsConstructor
    public static class Meta {
        Content content;
        QueryParams queryParams;
        String title;
        String packId;
        String productType;
        Boolean checkDownloadStatus;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @Slf4j
    @Component
    @RequiredArgsConstructor
    public static class QueryParams {
        String activityId;
        String packId;
        String contentId;
        Boolean consumptionRequired;
        String activityType;
        String title;
        String image;
        String contentCategory;
        Boolean adsEnabled;
    }
}
