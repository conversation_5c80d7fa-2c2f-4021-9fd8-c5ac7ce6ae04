package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.albus.common.BundleSellableProduct;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.cfapi.dto.sugarfit.MegaSaleData;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.view.viewmodels.chroniccare.RenewSubscriptionPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfRenewalConfig;
import com.curefit.cfapi.widgets.chroniccare.PackPurchaseWidget;
import com.curefit.cfapi.widgets.chroniccare.PackPurchaseWidget.PackDetail;
import com.curefit.cfapi.widgets.chroniccare.SfBannerCarouselWidget;
import com.curefit.cfapi.widgets.chroniccare.SubscriptionCardWidget.IncludedItem;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.GenericBannerWidget;
import com.curefit.cfapi.widgets.chroniccare.ecommerce.SfEComBannerWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.CarefitIllegalArgumentException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sugarfit.chs.pojo.CgmOnboardingStatusResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.curefit.cfapi.util.AppUtil.getAppTenantFromUserContext;
import static com.curefit.cfapi.util.ChronicCareAppUtil.isRenewalPageBannersSupported;
import static com.curefit.cfapi.util.SfHomePageUtil.WIDGET_LONG_TIMEOUT;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class RenewSubscriptionPageViewBuilder {
    final ChronicCarePatientService chronicCarePatientService;
    final UserOnboardingService userOnboardingService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final ExceptionReportingService exceptionReportingService;
    final ObjectMapper objectMapper;
    final AppConfigCache appConfigCache;
    final ServiceInterfaces serviceInterfaces;
    final List<String> NOT_HIGHLIGHTED_GRADIENT_CSS = Arrays.asList(
            "#FFFFFF",
            "#F9CF87",
            "#FFFFFF",
            "#FCBA4A",
            "#FFFFFF",
            "#FFFFFF"
    );
    static final String RUPEE_SYMBOL = "₹";

    public RenewSubscriptionPageView buildView(UserContext userContext) throws BaseException {
        ActivePackResponse lastPack;
        Optional<ActivePackResponse> activePackOptional = userOnboardingService.getSugarFitActivePack(userContext.getUserProfile().getUserId());
        if (activePackOptional.isPresent()) {
            lastPack = activePackOptional.get();
        } else {
            Optional<ActivePackResponse> expiredPackOptional = userOnboardingService.getSugarFitExpiredPack(userContext.getUserProfile().getUserId());
            lastPack = expiredPackOptional.orElseThrow(() -> new ResourceNotFoundException(
                    String.format("No expired and active pack for user :: %s", userContext.getUserProfile().getUserId())));
        }
        if (lastPack.getBundleProduct().getIsTrialProduct()) {
            throw new CarefitIllegalArgumentException(
                    String.format("Renewal Not allowed for trial pack user :: %s", userContext.getUserProfile().getUserId()));
        }
        return getRenewSubscriptionPageView(userContext, lastPack);
    }

    private List<BundleSellableProduct> getRelatedExtensionPacks(String userId, ActivePackResponse latestPack) {
        String target = latestPack.getBundleProduct().getProductSpecs().get("target").textValue();
        return chronicCareServiceHelper.getAllRenewalPacks(userId, target, latestPack.getBundleProduct().getTenant());
    }

    private RenewSubscriptionPageView getRenewSubscriptionPageView(UserContext userContext, ActivePackResponse latestPack) throws ResourceNotFoundException {
        String userId = userContext.getUserProfile().getUserId();
        PatientDetail patient = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        RenewSubscriptionPageView result = new RenewSubscriptionPageView();
        result.setPageTitle(AppUtil.isSugarFitApp(userContext) ? "New Renewal Plans" : "Renew Subscription");

        try {
            List<BundleSellableProduct> relatedExtensionPacks = getRelatedExtensionPacks(userId, latestPack);
            if(ChronicCareAppUtil.isRenewalPacksV2Supported(userContext)){
                List<RenewSubscriptionPageView.PackData> packDataList = buildRecommendedPacks(
                        relatedExtensionPacks, userContext, patient, false
                );
                result.setRecommendedPacks(packDataList);
                CompletableFuture<MegaSaleData> megaSalesBannerDataFuture = chronicCareServiceHelper.getMegaSalesDataFuture(userContext, null, null, null, true);
                MegaSaleData megaSaleData = megaSalesBannerDataFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (ChronicCareAppUtil.isTwentyTwentyFiveSaleLive(userContext, megaSaleData)){
                    result.setHeaderTitle("Start 2025 Strong\nwith our New Plan");
                } else {
                    result.setHeaderTitle("Continue Your Path to Diabetes Reversal");
                }

                ActivePackResponse activePackResponse = null;
                Boolean sfPackPurchased = this.serviceInterfaces.getSfUserOnboardingService().getIfSugarFitPackActive(userContext.getUserProfile().getUserId());
                if (sfPackPurchased) {
                    CompletableFuture<Optional<ActivePackResponse>> activePackResponseFuture = this.serviceInterfaces.getSfUserOnboardingService().getSugarFitActivePackFuture(serviceInterfaces, userContext.getUserProfile().getUserId());
                    Optional<ActivePackResponse> activePackResponseOptional = activePackResponseFuture.get();
                    activePackResponse = activePackResponseOptional.orElse(null);
                } else {
                    Optional<ActivePackResponse> expiredPackResponse = this.serviceInterfaces.getSfUserOnboardingService().getSugarFitExpiredPack(userContext.getUserProfile().getUserId());
                    if (expiredPackResponse.isPresent() && (!expiredPackResponse.get().getBundleProduct().getIsTrialProduct())) {
                        activePackResponse = expiredPackResponse.get();
                    }
                }
                CgmOnboardingStatusResponse cgmOnboardingStatus = serviceInterfaces.getChsClient().fetchOnboardingStatus(Long.valueOf(userContext.getUserProfile().getUserId()), null, getAppTenantFromUserContext(userContext));
                result.setMegaSaleData(chronicCareServiceHelper.getMegaSaleDataForStripBanner(userContext, cgmOnboardingStatus, activePackResponse));

//                if(ChronicCareAppUtil.isSfCustomRenewalPackEnabledUser(userContext)){
//                    result.setCustomPackModalData(getCustomPackModalData());
//                }

                if (isRenewalPageBannersSupported(userContext)){
                    result.addBanner(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/best-pack-features-2025-06-22-16:48.png"));
                    result.addBanner(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/prepuchase_banner5-2025-03-06-18:26.png"));
                } else {
                    result.setOutcomeSectionHeading("Improved Clinical Outcomes");
                    result.setOutcomeSectionImageUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/hba1c-levels-2024-12-27-16:42.png");
                    result.setNewTechSectionHeading("New technologies");
                    result.setNewTechSectionImageUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/gpt-2024-12-27-16:41.png");
                }

                return result;
            }
            SfEComBannerWidget renewalBannerWidget = new SfEComBannerWidget();
            List<SfEComBannerWidget.Banner> renewalBannerWidgetImages = new ArrayList<>();
            SfEComBannerWidget.Banner superCoachesBgImage = new SfEComBannerWidget.Banner();
            superCoachesBgImage.setImageUrl("image/chroniccare/referral/renewal_header_v1.png");
            superCoachesBgImage.setMinHeight(100);
            renewalBannerWidgetImages.add(superCoachesBgImage);
            renewalBannerWidget.setImages(renewalBannerWidgetImages);
            result.addWidget(renewalBannerWidget);


            // boolean isMegaSaleLive = Objects.nonNull(megaSaleData) && megaSaleData.isSaleLive();
            // if (isRenewalReportSupported(userContext) && !isMegaSaleLive) {
            //     result.addWidget(getRequestCallbackWidget(userContext));
            // }

            for (BundleSellableProduct bundleSellableProduct : relatedExtensionPacks) {
                PackPurchaseWidget packCardWidget = this.getPackCardWidget(patient, bundleSellableProduct);

                long discountPercent = Math.round(Double.valueOf(100.0 * (bundleSellableProduct.getMrp() - bundleSellableProduct.getListingPrice()) / bundleSellableProduct.getMrp()));
                packCardWidget.getPackDetail().setDiscountPercentLabel(String.format("%d%% OFF", discountPercent));

//            Double mrpMonthly = Double.valueOf((bundleSellableProduct.getMrp())/(bundleSellableProduct.getInfoSection().get("packDurationMonths").asDouble()));
//            packCardWidget.getPackDetail().setMonthlyStrikeOffPrice(String.format("%s %s", RUPEE_SYMBOL, getVedicNumberFormat(mrpMonthly.longValue())));
                packCardWidget.getPackDetail().setMonthlyStrikeOffPrice(String.format("%s %s", RUPEE_SYMBOL, getVedicNumberFormat(bundleSellableProduct.getMrp().longValue())));

                Double listingPriceMonthly = Double.valueOf((bundleSellableProduct.getListingPrice()) / (bundleSellableProduct.getInfoSection().get("packDurationMonths").asDouble()));
                packCardWidget.getPackDetail().setMonthlyFinalPrice(String.format("₹ %s/month", getVedicNumberFormat(listingPriceMonthly.longValue())));
                result.addWidget(packCardWidget);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        // result.addAction(Action.builder().title("RENEW SUBSCRIPTION").actionType(ActionType.NAVIGATION).url("curefit://renewsubscription").build());
        return result;
    }

    private RenewSubscriptionPageView.ModalData getCustomPackModalData() {
        RenewSubscriptionPageView.ModalData modalData = new RenewSubscriptionPageView.ModalData();
        modalData.setOpenModalSubtitle("Want something more specific?");
        modalData.setOpenModalActionTitle("Make your own Plan");
        modalData.setTitleText("Make your own plan for next @{boldValue}");
        modalData.setTitleTextHighlightValues(List.of("6 months"));
        modalData.setSubSectionTitle("Items Already Included in the pack");
        modalData.setSubSectionPoints(List.of(
                new RenewSubscriptionPageView.BoldText("@{boldValue} Coach Consultation", List.of("Unlimited")),
                new RenewSubscriptionPageView.BoldText("Nutrition Plan and Meal Logging", List.of()),
                new RenewSubscriptionPageView.BoldText("Fitness and Mindfulness Plan", List.of())
        ));
        modalData.setProductSectionTitle("Select from below");
        setCustomRenewalPackProductsAndBasePrice(modalData);
        return modalData;
    }

    private void setCustomRenewalPackProductsAndBasePrice(RenewSubscriptionPageView.ModalData modalDataResp){
        try {
            SfRenewalConfig renewalConfig = appConfigCache.getConfig("SF_RENEWAL_CONFIG", new TypeReference<>() {
            }, new SfRenewalConfig());

            if(renewalConfig!=null && renewalConfig.getBenefits()!=null && !renewalConfig.getBenefits().isEmpty()){
                List<RenewSubscriptionPageView.CustomPackItem> productItems = new ArrayList<>();
                renewalConfig.getBenefits().forEach(benefit -> {
                    if (benefit.getBenefitType().equalsIgnoreCase("CGM")) {
                        productItems.add(
                                new RenewSubscriptionPageView.CustomPackItem(benefit.getBenefitType(), "CGM", "", benefit.getListingPrice(), benefit.getMrp()));
                    } else if (benefit.getBenefitType().equalsIgnoreCase("DOCTOR_CONSULTATION")) {
                        productItems.add(
                                new RenewSubscriptionPageView.CustomPackItem(benefit.getBenefitType(), "Doctor Consultation", "", benefit.getListingPrice(), benefit.getMrp()));
                    } else if (benefit.getBenefitType().equalsIgnoreCase("DIAGNOSTICS")) {
                        productItems.add(
                                new RenewSubscriptionPageView.CustomPackItem(benefit.getBenefitType(), "Diagnostics", "70+ Parameters", benefit.getListingPrice(), benefit.getMrp()));
                    }
                });

                modalDataResp.setProductItems(productItems);
                modalDataResp.setPackBasePrice(renewalConfig.getPackBasePrice());
            }
        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
    }

    private SfBannerCarouselWidget getRequestCallbackWidget(UserContext userContext) {
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();
        BannerItem bannerItem = new BannerItem();
        bannerItem.setImage("image/chroniccare/sf_renewal_request_callback_banner.png");

        Map<String, String> meta = new HashMap<>();
        meta.put("userId", userContext.getUserProfile().getUserId());
        meta.put("requestType", "SF_RENEWAL_REQUEST_CALLBACK");

        Map<String, Object> analyticsData = new HashMap<>();
        analyticsData.put("eventKey", "widget_click");
        analyticsData.put("eventData", meta);
        bannerItem.setAction(Action.builder().actionType(ActionType.SF_REQUEST_CALLBACK).meta(meta).analyticsData(analyticsData).build());
        bannerItemList.add(bannerItem);
        sfBannerCarouselWidget.setData(bannerItemList);
        sfBannerCarouselWidget.setLayoutProps(getBannerLayoutProps());

        return sfBannerCarouselWidget;
    }

    @GetMapping
    private static Map<String, Object> getBannerLayoutProps() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", false);
        layoutProps.put("bannerHeight", 138);
        layoutProps.put("bannerWidth", 375);
        layoutProps.put("bannerOriginalHeight", 138);
        layoutProps.put("bannerOriginalWidth", 375);
        layoutProps.put("verticalPadding", 20);
        layoutProps.put("edgeToEdge", true);
        layoutProps.put("showPagination", false);
        return layoutProps;
    }

    public PackPurchaseWidget getPackCardWidget(PatientDetail patientDetail, BundleSellableProduct bundleSellableProduct) {
        PackPurchaseWidget widget = new PackPurchaseWidget();
        String actionUrl = String.format("curefit://carecartcheckout?patientId=%s&productCode=%s&productId=%s&title=Checkout",
                patientDetail.getId(), bundleSellableProduct.getProductCode(), bundleSellableProduct.getProductCode());
        /*
        ULTRAFIT_RENEW_1M ULTRAFIT_RENEW_3M are LITE
        ULTRAFIT_RENEW_6M ULTRAFIT_RENEW_12M are PRO*/

        String btnTitle = (bundleSellableProduct.getProductCode().equals("ULTRAFIT_RENEW_6M") || bundleSellableProduct.getProductCode().equals("ULTRAFIT_RENEW_12M")) ? "GET STARTED WITH PRO" : (bundleSellableProduct.getProductCode().equals("ULTRAFIT_RENEW_1M") || bundleSellableProduct.getProductCode().equals("ULTRAFIT_RENEW_3M")) ? "GET STARTED WITH LITE" : "Checkout";
        widget.setBottomAction(Action.builder().url(actionUrl).actionType(ActionType.NAVIGATION).title(btnTitle).build());
        PackDetail packDetail = new PackDetail();
        packDetail.setValue(Integer.parseInt(bundleSellableProduct.getInfoSection().get("packDurationMonths").asText()));
        String valueType = "MONTHS";
        int emi = ((Double) (bundleSellableProduct.getListingPrice() / (bundleSellableProduct.getDuration() / 30))).intValue();
//        packDetail.setPriceText(String.format("₹ %d/Month", emi));
        packDetail.setPriceText(String.format("₹ %s", getVedicNumberFormat(bundleSellableProduct.getListingPrice().longValue())));
        if (bundleSellableProduct.getTenant().equals("ULTRAFIT")) {
            packDetail.setPriceText(String.format("₹ %s", getVedicNumberFormat((long) emi)));
            packDetail.setPopular(bundleSellableProduct.getProductCode().equals("ULTRAFIT_RENEW_3M") || bundleSellableProduct.getProductCode().equals("ULTRAFIT_RENEW_12M"));
            if (bundleSellableProduct.getProductCode().equals("ULTRAFIT_RENEW_1M")) {
                valueType = "Month";
                packDetail.setShowPerMonth(false);
            } else
                valueType = "Months";
        }
        packDetail.setValueType(valueType);
        packDetail.setAddedText("");
        packDetail.setPackType(bundleSellableProduct.getInfoSection().get("packType").asText());
        packDetail.setRenderingInfo(bundleSellableProduct.getInfoSection().get("renderingInfo"));
        packDetail.setPackCode(bundleSellableProduct.getProductCode());
        packDetail.setTotalPriceText("");

        List<IncludedItem> planDetails = chronicCareServiceHelper.getSubscriptionPageFeatures(bundleSellableProduct);
        packDetail.setPlanDetails(planDetails);
        packDetail.setGradientColor(chronicCareServiceHelper.getGradientColor(bundleSellableProduct));
        widget.setPackDetail(packDetail);
        return widget;
    }

    public static String getVedicNumberFormat(Long amount) {
        String amountString = String.valueOf(amount);
        int amountStringLength = amountString.length();
        if (amountStringLength <= 3) {
            return amountString;
        } else {
            List<String> segments = new LinkedList<>();
            int currentIndex = 3;
            int previosIndex = 0;
            int startIdx = amountStringLength - currentIndex;
            int endIdx = amountStringLength - previosIndex;
            segments.add(amountString.substring(startIdx, endIdx));
            do {
                previosIndex = currentIndex;
                currentIndex = Math.min(currentIndex + 2, amountStringLength);
                startIdx = amountStringLength - currentIndex;
                endIdx = amountStringLength - previosIndex;
                segments.add(0, amountString.substring(startIdx, endIdx));
            } while (currentIndex < amountStringLength);
            return String.join(",", segments);
        }
    }

    public List<RenewSubscriptionPageView.PackData> buildRecommendedPacks(List<BundleSellableProduct> relatedExtensionPacks,
                                                                                 UserContext userContext,
                                                                                 PatientDetail patient, boolean isSalesApp) {
        List<RenewSubscriptionPageView.PackData> packDataList = new ArrayList<>();

        for (BundleSellableProduct bundleSellableProduct : relatedExtensionPacks) {
            JsonNode packDetailsFromConfig = bundleSellableProduct.getInfoSection().get("renewalPageV2");

            if (packDetailsFromConfig != null) {
                RenewSubscriptionPageView.PackData pack = objectMapper.convertValue(packDetailsFromConfig, RenewSubscriptionPageView.PackData.class);
                pack.setPackCode(bundleSellableProduct.getProductCode());
                pack.setPackTitle(bundleSellableProduct.getInfoSection().get("packTitle").asText());
                pack.setMrp(bundleSellableProduct.getMrp());
                pack.setListingPrice(bundleSellableProduct.getListingPrice());

                int packDurationMonths = bundleSellableProduct.getInfoSection().get("packDurationMonths").asInt();
                pack.setEmiPriceText(String.format("Just at @{boldValue} for %d %s", packDurationMonths, packDurationMonths > 1 ? "months" : "month"));

                Double listingPriceMonthly = bundleSellableProduct.getListingPrice() /
                        bundleSellableProduct.getInfoSection().get("packDurationMonths").asDouble();
                pack.setEmiPriceBoldValues(List.of(String.format("₹%s/month", getVedicNumberFormat(listingPriceMonthly.longValue()))));

                int discount = (int) Math.round(((bundleSellableProduct.getMrp() - bundleSellableProduct.getListingPrice()) * 100.0) / bundleSellableProduct.getMrp());
                if (discount > 0) {
                    pack.setOfferDiscount(discount);
                }

                if (Objects.equals(pack.getPackCode(), "SUGARFIT_T2D_RENEW_EMI_12_NY")) {
                    pack.setOfferDiscount(0);
                    pack.setTagText("NO\nCOST\nEMI");
                    pack.setEmiPriceText(String.format("@{boldValue} at just ₹%s/month", getVedicNumberFormat(listingPriceMonthly.longValue())));
                    pack.setEmiPriceBoldValues(List.of("No Cost EMI"));
                }

                String appUri = isSalesApp ? "sfcommunity" : "curefit";

                String actionUrl = String.format("%s://carecartcheckout?patientId=%s&productCode=%s&productId=%s&title=Checkout", appUri,
                        patient.getId(), bundleSellableProduct.getProductCode(), bundleSellableProduct.getProductCode());

                Action action = Action.builder()
                        .url(actionUrl)
                        .actionType(ActionType.NAVIGATION)
                        .title("Buy Now")
                        .build();
                pack.setAction(action);

                if (pack.isShowSubCard()) {
                    pack.setSubCardData(chronicCareServiceHelper.getRenewalSubCardData(userContext));
                }

                packDataList.add(pack);
            }
        }

        return packDataList;
    }


}

