package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.albus.common.PatientPreferredAgentResponse;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.dto.ChronicCareTeam;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.util.DigitalAppUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.view.viewmodels.chroniccare.SugarJournalV2PageView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.*;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.GenericBannerWidget;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.enums.GlucometerDataGroupingFilterType;
import com.sugarfit.chs.enums.GlucoseTargetRangeType;
import com.sugarfit.chs.pojo.DataPoint;
import com.sugarfit.chs.pojo.glucometerReadings.*;
import com.sugarfit.sms.response.NUXStatusResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class SugarJournalV2PageViewBuilder {
    final CHSClient chsClient;
    final ExceptionReportingService exceptionReportingService;
    final UserOnboardingService userOnboardingService;
    final ChronicCarePatientService chronicCarePatientService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final ServiceInterfaces serviceInterfaces;


    public SugarJournalV2PageView buildView(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        SugarJournalV2PageView page = new SugarJournalV2PageView();
        GlucometerReadingMasterData masterData = chsClient.getGlucometerReadingsMasterData();
        long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        page.setMasterData(masterData);
//        page.addWidget(this.getEstimatedHbA1cWidget(userContext));
        boolean salesApp = Objects.nonNull(queryParams) && queryParams.containsKey("salesApp") && Boolean.parseBoolean(queryParams.getOrDefault("salesApp", "false"));
        if (salesApp) {
            page.addWidget(this.getSLSugarLogListWidget());
            page.addWidget(this.getSLTrendWidget());
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/sugarlog-info-1-2025-08-22-15:37.png"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/sugarlog-info-2-2025-08-22-15:37.png"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/sugarlog-info-3-2025-08-22-15:37.png"));
        } else {
            page.addWidget(this.getTrendWidget(userId));
            page.addWidget(this.getMonthAverageReadingWidget(userId));
            page.addWidget(this.getTrendDeviationWidget(userId));
            page.addWidget(this.sugarLogListWidget(userContext));
        }
        return page;
    }


    public EstimatedHbA1cWidget getEstimatedHbA1cWidget(UserContext userContext) throws HttpException {
        EstimatedHbA1cWidget widget = new EstimatedHbA1cWidget();
        String userId = userContext.getUserProfile().getUserId();
        GlucometerReadingGroupedDataFilterRequest request = new GlucometerReadingGroupedDataFilterRequest();
        request.setUserId(Long.valueOf(userId));
        request.setFilterType(GlucometerDataGroupingFilterType.EST_HBA1C_TREND);
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        request.setStartTime(this.getStartOfYear(timeZone));
        request.setEndTime(this.getEndOfCurrentMonth(timeZone));
        EstHbA1cTrendGroupedData data = chsClient.filterAndGroupEstHbA1cGlucometerReadings(request, timeZone);
        widget.setTitle("Estimated HbA1c Summary");
        widget.setData(data);
        widget.setNoDataCard(new EstimatedHbA1cWidget.NoDataCard());
        widget.setSugarReadingMap(this.getSugarReadingMapsForHbA1c(this.getStartOfYear(timeZone), data.getDataPoints(), userContext.getUserProfile().getTimezone()));
        return widget;
    }

    private GlucoseTrendDeviationWidget getTrendDeviationWidget(long userId) {
        GlucoseTrendDeviationWidget widget = new GlucoseTrendDeviationWidget();
        widget.setTitle("In-range and Outside range");
        widget.setNoDataCard(new GlucoseTrendDeviationWidget.NoDataCard());
        return widget;
    }

    private GlucoseTrendWidget getTrendWidget(long userId) throws HttpException {
        GlucoseTrendWidget widget = new GlucoseTrendWidget();
        widget.setTitle("Glucometer Readings");
        widget.setNoDataCard(new GlucoseTrendWidget.NoDataCard());

        return widget;
    }

    private GlucoseAverageReadingWidget getMonthAverageReadingWidget(long userId) throws HttpException {
        GlucoseAverageReadingWidget widget = new GlucoseAverageReadingWidget();
        widget.setTitle("Through the program");
        widget.setNoDataCard(new GlucoseAverageReadingWidget.NoDataCard());

        return widget;
    }

    private BaseWidgetNonVM sugarLogListWidget(UserContext userContext) throws ParseException {
        SugarLogListWidget widget = new SugarLogListWidget();
        String userId = userContext.getUserProfile().getUserId();
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        boolean isUltraFit = AppUtil.isUltraFitApp(userContext);
        boolean isCoachPresent = false;

        try {
            Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePack(userId);
            NUXStatusResponse nuxStatusResponse = serviceInterfaces.getSmsClient().getNUXStatus(Long.valueOf(userId), true, timeZone);
            if (activePackResponse.isPresent() && !DigitalAppUtil.isDigitalAppUser(nuxStatusResponse.getActivePack().getProductCode(),
                    nuxStatusResponse.getFreemium())) {
                PatientDetail patient = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
                ChronicCareTeam assignedCareTeam = chronicCareServiceHelper.getAssignedCareTeam(userContext, patient.getId(), activePackResponse.get().getBundleProduct());
                if (Objects.nonNull(assignedCareTeam) && Objects.nonNull(assignedCareTeam.getCoach())) {
                    PatientPreferredAgentResponse coach = assignedCareTeam.getCoach();
                    String coachName = coach.getAgentResponse().getName();
                    String SF_WHATSAPP_NUM = isUltraFit ? "919885022351" : "918792514509";
                    Action coachCta;
                    if (ChronicCareAppUtil.isInAppChatEnabledForUser(userContext, activePackResponse.get().getBundleProduct(), chronicCareServiceHelper)) {
                        coachCta = Action.builder().url("curefit://sfcommunitychattabs?tab=my_coach").title("Chat Now").actionType(ActionType.NAVIGATION).build();
                    } else {
                        coachCta = Action.builder().url("https://wa.me/" + SF_WHATSAPP_NUM).title("Chat Now").actionType(ActionType.EXTERNAL_DEEP_LINK).build();
                    }
                    widget.setCoachCtaAction(coachCta);
                    widget.setCoachName("COACH " + coachName.toUpperCase());
                    widget.setCoachCardTitle("Please get in touch with your coach");
                    isCoachPresent = true;
                    widget.setCoachImageUrl(chronicCareServiceHelper.getAgentImageFromResponse(coach));
                }
            }
        } catch (Exception e) {
            log.info("assignedCareTeam: NO data");
        }
        widget.setShowCoachCard(isCoachPresent);
        widget.setTitle("Sugar logs");
        widget.setNoDataCard(new SugarLogListWidget.NoDataCard());
        return widget;
    }


    public GlucoseAverageReadingWidget getMonthAverageReadingWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        GlucoseAverageReadingWidget widget = new GlucoseAverageReadingWidget();
        long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        TimeZone timezone = ChronicCareAppUtil.getUserTimezone(userContext);
        long startTime = TimeUtil.getStartOfDay(new Date(this.getStartOfYear(timezone)), userContext.getUserProfile().getTimezone()).getTime();
        long endTime = TimeUtil.getEndOfDay(new Date(), userContext.getUserProfile().getTimezone()).getTime();
        String slot = queryParams.getOrDefault("slot", null);
        if (slot == null) {
            slot = chsClient.getGlucometerReadingsDefaultSlot(userId, startTime, endTime);
        }
        GlucometerReadingGroupedDataFilterRequest request = new GlucometerReadingGroupedDataFilterRequest();
        request.setUserId(userId);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        if (slot != null && !slot.equalsIgnoreCase("all")) {
            request.setSlot(slot);
        }
        request.setFilterType(GlucometerDataGroupingFilterType.GLUCOMETER_READING_MONTHLY_AVERAGE_TREND);
        // goals-range 3 graph
        GlucoseAverageGroupedData data = chsClient.filterAndGroupGlucometerReadingsForAverage(request, timezone);
        widget.setTitle("Through the program");
        widget.setData(data);
        widget.setSlot(slot);
        widget.setStartTime(startTime);
        widget.setEndTime(endTime);
        widget.setSugarReadingMap(getSugarReadingMapsForMonthlyAverage(startTime, endTime, data.getDataPoints(), userContext.getUserProfile().getTimezone()));
        return widget;
    }


    public GlucoseTrendDeviationWidget getTrendDeviationWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        GlucoseTrendDeviationWidget widget = new GlucoseTrendDeviationWidget();
        long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        TimeZone timezone = ChronicCareAppUtil.getUserTimezone(userContext);
        long startTime = TimeUtil.getStartOfDay(new Date(this.getStartOfYear(timezone)), userContext.getUserProfile().getTimezone()).getTime();
        long endTime = TimeUtil.getEndOfDay(new Date(), userContext.getUserProfile().getTimezone()).getTime();
        String slot = queryParams.getOrDefault("slot", null);

        if (slot == null) {
            slot = chsClient.getGlucometerReadingsDefaultSlot(userId, startTime, endTime);
        }

        GlucometerReadingGroupedDataFilterRequest request = new GlucometerReadingGroupedDataFilterRequest();
        request.setUserId(userId);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        if (slot != null && slot.equalsIgnoreCase("all")) {
            request.setSlot(null);
        } else
            request.setSlot(slot);
        request.setFilterType(GlucometerDataGroupingFilterType.GLUCOMETER_READING_MONTHLY_TYPE_TREND);
        // hyper hypos 4 graph
        GlucoseTypeCountGroupedData data = chsClient.filterAndGroupGlucometerReadingsForTypeTrendDeviation(request, timezone);
        widget.setData(data);
        widget.setSlot(slot);
        widget.setStartTime(startTime);
        widget.setEndTime(endTime);
        widget.setSugarReadingMap(getSugarReadingMapsForTrendType(startTime, endTime, data.getDataPoints(), userContext.getUserProfile().getTimezone()));

        return widget;
    }


    public GlucoseTrendWidget getTrendWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        GlucoseTrendWidget widget = new GlucoseTrendWidget();
        long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        TimeZone timezone = ChronicCareAppUtil.getUserTimezone(userContext);
        long startTime = Long.parseLong(queryParams.getOrDefault("startTime", String.valueOf(TimeUtil.getStartOfDay(new Date(this.getStartOfCurrentMonth(timezone)), userContext.getUserProfile().getTimezone()).getTime())));
        long endTime = Long.parseLong(queryParams.getOrDefault("endTime", String.valueOf(TimeUtil.getEndOfDay(new Date(), userContext.getUserProfile().getTimezone()).getTime())));
        String slot = queryParams.getOrDefault("slot", null);

        if (slot == null) {
            slot = chsClient.getGlucometerReadingsDefaultSlot(userId, startTime, endTime);
        }
        GlucometerReadingGroupedDataFilterRequest request = new GlucometerReadingGroupedDataFilterRequest();
        request.setUserId(userId);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        String dateText = new Date(endTime).toString();
        if (slot != null && !slot.equalsIgnoreCase("all")) {
            request.setSlot(slot);
        }
        request.setFilterType(GlucometerDataGroupingFilterType.GLUCOMETER_READING_DAILY_TREND);
        //2nd graph
        GlucoseTrendGroupedData data = chsClient.filterAndGroupGlucometerReadingsForTrend(request, timezone);
        widget.setTitle("Glucose Reading");
        widget.setData(data);
        widget.setSlot(slot);
        widget.setStartTime(startTime);
        widget.setEndTime(endTime);
        widget.setSugarReadingMap(getSugarReadingMapForTrend(startTime, endTime, data.getDataPoints(), userContext.getUserProfile().getTimezone()));

        return widget;
    }


    public SugarLogListWidget getSugarLogWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        SugarLogListWidget widget = new SugarLogListWidget();
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
        long start = Long.parseLong(queryParams.getOrDefault("startTime", String.valueOf(this.getStartOfCurrentMonth(timeZone))));
        long end = Long.parseLong(queryParams.getOrDefault("endTime", String.valueOf(this.getEndOfCurrentMonth(timeZone))));
        String type = queryParams.getOrDefault("type", null);
        String slot = queryParams.getOrDefault("slot", null);
        GlucometerReadingFilterRequest request = new GlucometerReadingFilterRequest();
        request.setUserId(userId);
        request.setStartTime(start);
        request.setEndTime(end);
        String dateText = new Date(end).toString();
        if (type != null && !type.equals("") && !type.equalsIgnoreCase("all")) {
            List<GlucoseTargetRangeType> typeList = new ArrayList<>() {{
                add(GlucoseTargetRangeType.valueOf(type));
            }};
            widget.setSelectedType(type);
            request.setTargetRangeTypes(typeList);
        } else if (type != null && type.equalsIgnoreCase("all")) {
            widget.setSelectedType(type);
        }
        if (slot != null && !slot.equals("") && !slot.equalsIgnoreCase("all")) {
            widget.setSelectedSlot(slot);
            request.setSlot(slot);
        } else if (slot != null && slot.equalsIgnoreCase("all")) {
            widget.setSelectedSlot(slot);
        }
        List<GlucometerReadingEntry> data = this.chsClient.filterGlucometerReadings(request, appTenant, timeZone);
        widget.setStartTime(start);
        widget.setEndTime(end);
        widget.setData(data);
        // add id to url on app side
        widget.setEditAction(Action.builder().url("curefit://sugarlogging?editFlow=true&id=").actionType(ActionType.NAVIGATION).build());
        return widget;
    }


    public long getStartOfCurrentMonth(TimeZone timeZone) {
        Calendar cal = Calendar.getInstance(timeZone);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime().getTime();
    }

    public long getEndOfCurrentMonth(TimeZone timeZone) {
        Calendar cal = Calendar.getInstance(timeZone);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTime().getTime();
    }

    public long getStartOfYear(TimeZone timeZone) {
        Calendar cal = Calendar.getInstance(timeZone);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.YEAR, -1);
        cal.add(Calendar.MONTH, 1);
        return cal.getTime().getTime();
    }

    public Map<String, List<DataPoint>> getSugarReadingMapForTrend(long start, long end, List<GlucoseMetricDataPoint> dataPoints, String timezone) {
        LocalDate startTime = LocalDate.ofInstant(Instant.ofEpochMilli(start), ZoneId.of(timezone));
        LocalDate endTime = startTime.with(TemporalAdjusters.lastDayOfMonth());
        LinkedHashMap<String, List<DataPoint>> map = new LinkedHashMap<>();
        List<LocalDate> dateArray = new ArrayList<>(startTime.datesUntil(endTime).toList());
        dateArray.add(endTime);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        for (LocalDate date : dateArray) {
            map.put(formatter.format(date), new ArrayList<>());
        }

        for (GlucoseMetricDataPoint dataPoint : dataPoints) {
            String dateString = formatter.format(dataPoint.getDate().toInstant().atZone(ZoneId.of(timezone)).toLocalDate());
            map.put(dateString, dataPoint.getGroupedValues());
        }
        return map;
    }

    private Map<String, Double> getSugarReadingMapsForMonthlyAverage(long start, long end, List<GlucoseMetricDataPoint> dataPoints, String timezone) {
        LocalDate startTime = LocalDate.ofInstant(Instant.ofEpochMilli(start), ZoneId.of(timezone));
        LocalDate endTime = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        LinkedHashMap<String, Double> map = new LinkedHashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        while (startTime.isBefore(endTime) || startTime.isEqual(endTime)) {
            startTime = startTime.with(TemporalAdjusters.firstDayOfMonth());
            map.put(formatter.format(startTime), 0.0);
            startTime = startTime.plusMonths(1);
        }

        for (GlucoseMetricDataPoint dataPoint : dataPoints) {
            String dateString = formatter.format(dataPoint.getDate().toInstant().atZone(ZoneId.of(timezone)).toLocalDate());
            map.put(dateString, dataPoint.getValue());
        }
        return map;
    }

    private Map<String, List<Double>> getSugarReadingMapsForTrendType(long start, long end, List<GlucoseMetricDataPoint> dataPoints, String timezone) {
        LocalDate startTime = LocalDate.ofInstant(Instant.ofEpochMilli(start), ZoneId.of(timezone));
        LocalDate endTime = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        LinkedHashMap<String, List<Double>> map = new LinkedHashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        while (startTime.isBefore(endTime) || startTime.isEqual(endTime)) {
            startTime = startTime.with(TemporalAdjusters.firstDayOfMonth());
            map.put(formatter.format(startTime), new ArrayList<>() {{
                add(0.0);
                add(150.0);
                add(300.0);
            }});
            startTime = startTime.plusMonths(1);
        }

        for (GlucoseMetricDataPoint dataPoint : dataPoints) {
            String dateString = formatter.format(dataPoint.getDate().toInstant().atZone(ZoneId.of(timezone)).toLocalDate());
            Double hyper = 300 + dataPoint.getPercentHyper();
            Double inrange = 150 + dataPoint.getPercentInRange();
            Double hypo = dataPoint.getPercentHypo();
            map.put(dateString, new ArrayList<>() {{
                add(hypo);
                add(inrange);
                add(hyper);
            }});
        }
        return map;
    }

    private Map<String, Double> getSugarReadingMapsForHbA1c(long start, List<GlucoseMetricDataPoint> dataPoints, String timezone) {
        LocalDate startTime = LocalDate.ofInstant(Instant.ofEpochMilli(start), ZoneId.of(timezone));
        LocalDate endTime = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        LinkedHashMap<String, Double> map = new LinkedHashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        while (startTime.isBefore(endTime) || startTime.isEqual(endTime)) {
            startTime = startTime.with(TemporalAdjusters.firstDayOfMonth());
            map.put(formatter.format(startTime), 0.0);
            startTime = startTime.plusMonths(1);
        }

        for (GlucoseMetricDataPoint dataPoint : dataPoints) {
            String dateString = formatter.format(dataPoint.getDate().toInstant().atZone(ZoneId.of(timezone)).toLocalDate());
            Double value = (double) Math.round(dataPoint.getValue() * 100) / 100;
            map.put(dateString, value);
        }
        return map;
    }

    private SLGlucoseTrendWidget getSLTrendWidget() {
        SLGlucoseTrendWidget widget = new SLGlucoseTrendWidget();
        widget.setTitle("Sugar Readings Trend");
        widget.setNoDataCard(new SLGlucoseTrendWidget.NoDataCard());
        return widget;
    }

    public SLGlucoseTrendWidget getSLTrendWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        SLGlucoseTrendWidget widget = new SLGlucoseTrendWidget();
        long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        TimeZone timezone = ChronicCareAppUtil.getUserTimezone(userContext);
        long startTime = Long.parseLong(queryParams.getOrDefault("startTime", String.valueOf(TimeUtil.getStartOfDay(new Date(this.getStartOfCurrentMonth(timezone)), userContext.getUserProfile().getTimezone()).getTime())));
        long endTime = Long.parseLong(queryParams.getOrDefault("endTime", String.valueOf(TimeUtil.getEndOfDay(new Date(), userContext.getUserProfile().getTimezone()).getTime())));
        String slot = queryParams.getOrDefault("slot", null);

        if (slot == null) {
            slot = chsClient.getGlucometerReadingsDefaultSlot(userId, startTime, endTime);
        }
        GlucometerReadingGroupedDataFilterRequest request = new GlucometerReadingGroupedDataFilterRequest();
        request.setUserId(userId);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        String dateText = new Date(endTime).toString();
        if (slot != null && !slot.equalsIgnoreCase("all")) {
            request.setSlot(slot);
        }
        request.setFilterType(GlucometerDataGroupingFilterType.GLUCOMETER_READING_DAILY_TREND);
        //2nd graph
        GlucoseTrendGroupedData data = chsClient.filterAndGroupGlucometerReadingsForTrend(request, timezone);
        widget.setTitle("Glucometer Readings");
        widget.setData(data);
        widget.setSlot(slot);
        widget.setStartTime(startTime);
        widget.setEndTime(endTime);
        widget.setSugarReadingMap(getSugarReadingMapForTrend(startTime, endTime, data.getDataPoints(), userContext.getUserProfile().getTimezone()));

        return widget;
    }

    private BaseWidgetNonVM getSLSugarLogListWidget() {
        SLSugarLogListWidget widget = new SLSugarLogListWidget();
        widget.setTitle("Sugar logs");
        widget.setNoDataCard(new SLSugarLogListWidget.NoDataCard());
        return widget;
    }

    public SLSugarLogListWidget getSLSugarLogWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        SLSugarLogListWidget widget = new SLSugarLogListWidget();
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
        long start = Long.parseLong(queryParams.getOrDefault("startTime", String.valueOf(this.getStartOfCurrentMonth(timeZone))));
        long end = Long.parseLong(queryParams.getOrDefault("endTime", String.valueOf(this.getEndOfCurrentMonth(timeZone))));
        String type = queryParams.getOrDefault("type", null);
        String slot = queryParams.getOrDefault("slot", null);
        GlucometerReadingFilterRequest request = new GlucometerReadingFilterRequest();
        request.setUserId(userId);
        request.setStartTime(start);
        request.setEndTime(end);
        String dateText = new Date(end).toString();
        if (type != null && !type.equals("") && !type.equalsIgnoreCase("all")) {
            List<GlucoseTargetRangeType> typeList = new ArrayList<>() {{
                add(GlucoseTargetRangeType.valueOf(type));
            }};
            widget.setSelectedType(type);
            request.setTargetRangeTypes(typeList);
        } else if (type != null && type.equalsIgnoreCase("all")) {
            widget.setSelectedType(type);
        }
        if (slot != null && !slot.equals("") && !slot.equalsIgnoreCase("all")) {
            widget.setSelectedSlot(slot);
            request.setSlot(slot);
        } else if (slot != null && slot.equalsIgnoreCase("all")) {
            widget.setSelectedSlot(slot);
        }
        List<GlucometerReadingEntry> data = this.chsClient.filterGlucometerReadings(request, appTenant, timeZone);
        widget.setStartTime(start);
        widget.setEndTime(end);
        widget.setData(data);
        // add id to url on app side
        widget.setEditAction(Action.builder().url("sfcommunity://sugarlogging?editFlow=true&id=").actionType(ActionType.NAVIGATION).build());
        return widget;
    }


}
