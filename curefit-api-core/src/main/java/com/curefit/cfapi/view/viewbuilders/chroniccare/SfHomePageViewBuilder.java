package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.albus.booking.ActiveConsultationResponse;
import com.curefit.albus.common.BundleProduct;
import com.curefit.albus.common.PatientPreferredAgentResponse;
import com.curefit.albus.common.pojo.DiagnosticJourneyResponse;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.SelfInstallationActions;
import com.curefit.albus.response.UserMembershipInfo;
import com.curefit.albus.response.actions.UserPreferencePojo;
import com.curefit.albus.response.chronic.UserOnboardingActionWithContext;
import com.curefit.albus.response.group_class.ActiveGroupClassOrderResponse;
import com.curefit.albus.service.AlbusClient;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.enums.Tenant;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.dto.ChronicCareTeam;
import com.curefit.cfapi.dto.sugarfit.MegaSaleData;
import com.curefit.cfapi.dto.sugarfit.SfCgmDeviceStatus;
import com.curefit.cfapi.dto.sugarfit.SfUserCGMConfig;
import com.curefit.cfapi.model.internal.chroniccare.FitnessDeviceSyncMeta;
import com.curefit.cfapi.model.internal.chroniccare.SfAppAnnouncementConfig;
import com.curefit.cfapi.model.internal.chroniccare.SfHomepageBannerConfig;
import com.curefit.cfapi.model.internal.chroniccare.sfbadges.SfBadgeEntry;
import com.curefit.cfapi.model.internal.chroniccare.sfbadges.SfUserBadgeSummary;
import com.curefit.cfapi.model.internal.meta.chroniccare.SfOpsRequestMeta;
import com.curefit.cfapi.model.internal.meta.chroniccare.SfSalesBannerMeta;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionMeta;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.app.action.NavigationType;
import com.curefit.cfapi.pojo.chroniccare.CareKitTrackerTimeline;
import com.curefit.cfapi.pojo.chroniccare.WellnessAtGlanceItem;
import com.curefit.cfapi.pojo.chroniccare.WellnessDataPerDay;
import com.curefit.cfapi.pojo.chroniccare.experiencecenter.SfWellnessTherapyProduct;
import com.curefit.cfapi.pojo.chroniccare.nux.DiagnosticActionMeta;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.service.DeviceService;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.PackRenewalTimerStripWidgetBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.RenewalAlertWidgetBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.RenewalAlertWidgetBuilderV2;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.RenewalCarouselWidgetBuilder;
import com.curefit.cfapi.view.viewmodels.chroniccare.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfHomePageView.AppAnnouncementData;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfHomePageView.Banner;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfHomePageView.BookingModalConfig;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfHomePageView.HomeMenuItem;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfHomePageView.MenuItemType;
import com.curefit.cfapi.view.viewmodels.chroniccare.onboarding.CardData;
import com.curefit.cfapi.view.viewmodels.chroniccare.onboarding.WhatNextCardData;
import com.curefit.cfapi.view.viewmodels.transform.HabitCard;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.*;
import com.curefit.cfapi.widgets.chroniccare.cgm.SfBluconJourneyWidget;
import com.curefit.cfapi.widgets.chroniccare.experiencecenter.SfWellnessAtCenterTherapyListCardWidget;
import com.curefit.cfapi.widgets.chroniccare.experiencecenter.SfWellnessAtCenterTherapyListCardWidgetV2;
import com.curefit.cfapi.widgets.chroniccare.prepurchase.UfPrePurchaseWidget;
import com.curefit.cfapi.widgets.chroniccare.renewal.SfRenewalReportHomeWidget;
import com.curefit.cfapi.widgets.chroniccare.renewal.SfRenewalReportHomeWidget.SfReportMetric;
import com.curefit.cfapi.widgets.common.NowLiveWidgetView;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.digital.NowLiveSessionWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.diyfs.client.spi.DiyfsService;
import com.curefit.diyfs.pojo.DIYFilterRequestV2;
import com.curefit.diyfs.pojo.LiveClass;
import com.curefit.diyfs.pojo.enums.PreferredStreamType;
import com.curefit.diyfs.pojo.enums.SubscriptionStatus;
import com.curefit.iris.models.NotificationMeta;
import com.curefit.math.enums.DataType;
import com.curefit.ollivander.common.exception.OllivanderClientException;
import com.curefit.ollivander.common.pojo.response.agent.AgentCenterMappingResponse;
import com.curefit.ollivander.common.pojo.response.agent.AgentResponse;
import com.curefit.ollivander.common.pojo.response.center.CenterBaseResponse;
import com.curefit.ollivander.common.pojo.response.center.CenterResponseV2;
import com.curefit.product.enums.ProductType;
import com.curefit.product.models.diy.DIYProduct;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.pojo.UserAttributeEntry;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.shifu.enums.HabitType;
import com.curefit.shifu.pojo.UserActivityEntry;
import com.curefit.shifu.pojo.goal.summary.GoalWithData;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.userservice.pojo.entry.DeviceDetailEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sugarfit.challenges.client.ChallengesClient;
import com.sugarfit.challenges.enums.ChallengeType;
import com.sugarfit.challenges.pojo.ChallengesEntry;
import com.sugarfit.challenges.pojo.ChallengesUserResponseEntry;
import com.sugarfit.challenges.pojo.LeaderboardEntry;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.enums.*;
import com.sugarfit.chs.pojo.*;
import com.sugarfit.chs.pojo.badge.BadgeEntry;
import com.sugarfit.chs.pojo.badge.UserBadgeProgressEntry;
import com.sugarfit.chs.pojo.badge.UserBadgeSummary;
import com.sugarfit.chs.pojo.blucon.BluconEventEntry;
import com.sugarfit.chs.pojo.cgmstat.CgmStat;
import com.sugarfit.chs.pojo.cgmstat.DailySugarStat;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalLogsResponse;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalScansForDayResponse;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalsRequest;
import com.sugarfit.chs.pojo.fitnessData.FitnessDataRequest;
import com.sugarfit.chs.pojo.fitnessData.HeartRateDataResponse;
import com.sugarfit.experiment.client.ExperimentClient;
import com.sugarfit.experiment.enums.ExperimentFilterType;
import com.sugarfit.experiment.pojo.UserAssignedExperimentFilterRequest;
import com.sugarfit.experiment.pojo.UserAssignedExperimentResponse;
import com.sugarfit.fitness.client.SFFitnessClient;
import com.sugarfit.lms.entry.ReferralClient;
import com.sugarfit.lms.referral.entry.ReferralConfigEntry;
import com.sugarfit.logging.client.LoggingClient;
import com.sugarfit.nest.client.MasterClassClient;
import com.sugarfit.poll.client.PollSupportClient;
import com.sugarfit.poll.enums.PollType;
import com.sugarfit.poll.pojo.PollEntry;
import com.sugarfit.sms.client.SMSClient;
import com.sugarfit.sms.entry.UserSubscriptionEntry;
import com.sugarfit.sms.enums.RenewalMetrics;
import com.sugarfit.sms.enums.WelcomeCallStatus;
import com.sugarfit.sms.pojo.renewal_journey.RenewalJourneyMetric;
import com.sugarfit.sms.pojo.renewal_journey.RenewalJourneyResponse;
import com.sugarfit.sms.response.FlashBackResponse;
import com.sugarfit.sms.response.NUXStatusResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.curefit.cfapi.pojo.chroniccare.SfOpsRequestType.REQUEST_DIAGNOSTICS;
import static com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper.SUGARFIT_PSYCHOLOGIST_AGENT_ID;
import static com.curefit.cfapi.util.AppUtil.*;
import static com.curefit.cfapi.util.ChronicCareAppUtil.*;
import static com.curefit.cfapi.util.DigitalAppUtil.DIGITAL_AGENT_CENTER_ID;
import static com.curefit.cfapi.util.SfBadgeUtils.*;
import static com.curefit.cfapi.util.SfHomePageUtil.*;
import static com.curefit.cfapi.view.viewmodels.chroniccare.ActiveCardStatus.StatusType.*;
import static com.curefit.cfapi.view.viewmodels.chroniccare.SfHomePageView.*;
import static java.util.concurrent.CompletableFuture.supplyAsync;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
@Slf4j
@RequiredArgsConstructor
public class SfHomePageViewBuilder {
    final CHSClient chsClient;
    final SMSClient smsClient;
    final ChallengesClient challengesClient;
    final LoggingClient sfLoggingClient;
    final ExperimentClient experimentClient;
    final PollSupportClient pollSupportClient;
    final ReferralClient referralClient;
    final SFFitnessClient sfFitnessClient;
    final UserAttributesClient userAttributesClient;
    final SegmentationCacheClient segmentationCacheClient;
    final AppConfigCache appConfigCache;
    final ServiceInterfaces serviceInterfaces;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final MasterClassClient masterClassClient;
    final DiyfsService diyfsService;
    final DeviceService deviceService;
    final UserOnboardingService userOnboardingService;
    final ChronicCarePatientService chronicCarePatientService;
    final ExceptionReportingService exceptionReportingService;
    final SubscriptionPageViewBuilder subscriptionPageViewBuilder;
    final SugarFitBlogWidgetBuilder sugarFitBlogWidgetBuilder;
    final SfPollSurveyBuilder sfPollSurveyBuilder;
    final SfRecommendedFitnessPlanWidgetBuilder recommendedFitnessPlanWidgetBuilder;
    final RenewalAlertWidgetBuilder renewalAlertWidgetBuilder;
    final RenewalCarouselWidgetBuilder renewalCarouselWidgetBuilder;
    final RenewalAlertWidgetBuilderV2 renewalAlertWidgetBuilderV2;
    final PackRenewalTimerStripWidgetBuilder renewalTimerStripWidgetBuilder;
    @Qualifier("cfApiRedisKeyValueStore")
    final KeyValueStore cfApiRedisKeyValueStore;

    public SfHomePageView buildView(UserContext userContext, String cgmDeviceId, String sessionId, Integer cgmConfigVersion) throws Exception {
        SfHomePageView result = new SfHomePageView();
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            if (chronicCareServiceHelper.isAppUnderMaintenance()) {
                result.addWidget(chronicCareServiceHelper.getMaintenanceBanner(false));
                return result;
            }

            NUXStatusResponse nuxStatus = smsClient.getNUXStatus(userId, true, timeZone);

            SfAppUpdateConfig config = chronicCareServiceHelper.getSfAppUpdateConfig();
            if (shouldShowAppUpdatePopUp(userContext, config)) {
                result.setAppUpdateData(getAppUpdateData(userContext, config));
            }

            if (nuxStatus != null && nuxStatus.getFreemium() && isFreemiumSupportedApp(userContext)) {
                if (!nuxStatus.getNuxCompleted()) {
                    result.addWidget(new AutoNavigationToNuxWidget());
                } else {
                    navigateUserToFreemiumHomePage(result);
                }
                return result;
            }

            if (nuxStatus != null && nuxStatus.getActivePack() != null) {
                if (!nuxStatus.getFreemium() && !nuxStatus.getNuxCompleted()) {
                    result.addWidget(getPackPurchaseGreetingWidget(userContext));
                } else {
                    Long activePackBookingId = nuxStatus.getActivePack().getSubscriptionId();
                    addPremiumHomePageWidgets(result, userContext, cgmDeviceId, cgmConfigVersion, sessionId, activePackBookingId);
                }
            } else {
                if (nuxStatus != null && nuxStatus.getUpcomingPack() != null) {
                    addPackUpcomingPageWidgets(result, userContext, sessionId);
                } else if (nuxStatus != null && nuxStatus.getExpiredPack() != null) {
                    addPackExpiredPageWidgets(result, userContext, cgmDeviceId, cgmConfigVersion, sessionId);
                } else {
                    if (isUltraFitApp(userContext)) {
                        result.addWidget(new UfPrePurchaseWidget());
                    } else {
                        String errorMessage = "No active/upcoming/expired pack found for user";
                        log.error(errorMessage);
//                        exceptionReportingService.reportWarning(new Exception(errorMessage));
                        navigateUserToFreemiumHomePage(result);
                    }
                }
            }

            result.setUserDetails(getUserDetails(userContext));
            return result;
        } catch (Exception e) {
            String errorMessage = "Error in fetching homepage";
            exceptionReportingService.reportException(errorMessage, e);
            throw e;
        }
    }

    private void addPremiumHomePageWidgets(SfHomePageView result, UserContext userContext, String cgmDeviceId,
                                           Integer cgmConfigVersion, String sessionId, Long packBookingId) throws Exception {
        String userIdString = userContext.getUserProfile().getUserId();
        Long userId = Long.valueOf(userIdString);
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timezone = userContext.getUserProfile().getTimezone() != null ? TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()) : TimeZone.getTimeZone("Asia/Kolkata");
        boolean isUltraFit = isUltraFitApp(userContext);
        boolean isSugarFit = isSugarFitApp(userContext);
        boolean isExperienceCentre = isExperienceCentreManager(userContext);

        CompletableFuture<CgmOnboardingStatusResponse> cgmOnboardingStatusFuture = chronicCareServiceHelper.getCgmOnboardingStatusFuture(userId, appTenant);
        CompletableFuture<PatientDetail> patientDetailFuture = chronicCarePatientService.getChronicCarePatientForUserIdFuture(userContext);
        CompletableFuture<ActivePackResponse> activePackResponseFuture = userOnboardingService.getSugarFitActivePackResponseFuture(userIdString);
        CompletableFuture<ActivePackResponse> wellnessActivePackResponseFuture = userOnboardingService.getSugarFitWellnessActivePackResponseFuture(userIdString);
        CompletableFuture<UserOnboardingActionWithContext> onboardingActionsFuture = userOnboardingService.getUserOnboardingActionsFuture(userContext, packBookingId);
        CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture = chronicCareServiceHelper.getConsultationBookingsFuture(userContext);
        CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture = null;
        CompletableFuture<ChronicCareTeam> assignedCareTeamFuture = chronicCareServiceHelper.getAssignedCareTeamFuture(userContext, null, Objects.nonNull(activePackResponseFuture.get()) ? activePackResponseFuture.get().getBundleProduct() : null);
        CompletableFuture<RenewalJourneyResponse> renewalJourneyFuture = chronicCareServiceHelper.getRenewalJourneyFuture(userContext);
        CompletableFuture<CgmUserRequestStatus> cgmUserRequestStatusFuture = chronicCareServiceHelper.getCgmUserRequestStatusFuture(userId, appTenant); // TODO: move to onboarding status
        CompletableFuture<FaceBasedVitalScansForDayResponse> faceBasedVitalScansForDayResponseFuture = chronicCareServiceHelper.getFbvScanForDayDataFuture(userId, timezone);

        CgmOnboardingStatusResponse cgmOnboardingStatus = cgmOnboardingStatusFuture.get();
        SfCgmDeviceStatus cgmDeviceStatus = getCgmDeviceStatus(userContext, cgmOnboardingStatus, cgmDeviceId,chronicCareServiceHelper,serviceInterfaces.getSfAlbusClient());
        CompletableFuture<CgmStat> cgmStatFuture = chronicCareServiceHelper.getCgmStatFuture(userId, cgmDeviceStatus.getCgmDeviceId(), true, appTenant, timezone);

        CompletableFuture<BaseWidgetNonVM> animatedScoreWidgetFuture = null;
        CompletableFuture<BaseWidgetNonVM> requestCGMWidgetFuture = null;
        CompletableFuture<BaseWidgetNonVM> miniConsultationCardsWidgetFuture = null;
        CompletableFuture<SfHomeScanFaceWidget> fbvScanWidgetFuture = null;
        CompletableFuture<BaseWidgetNonVM> experimentsWidgetFuture = null;
        CompletableFuture<SfRecommendedFitnessPlanWidget> recommendedFitnessPlanWidgetFuture = null;
        CompletableFuture<List<BaseWidgetNonVM>> challengeWidgetsFuture = null;
        CompletableFuture<List<BaseWidgetNonVM>> pollWidgetsFuture = null;
        CompletableFuture<List<BaseWidgetNonVM>> pollSurveyWidgetsFuture = null;
        CompletableFuture<BaseWidgetNonVM> ufReferralBannerWidgetFuture = null;
        CompletableFuture<SfCoachCommunitySessionWidget> communityWebinarWidgetFuture = null;
        CompletableFuture<SfCgmInstallationWebinarWidget> cgmInstallationWebinarWidgetFuture = null;
        CompletableFuture<SfBannerCarouselWidget> eComBannerWidgetFuture = null;
        CompletableFuture<SfCoachCelebrationHomeWidget> coachCelebrationWidgetFuture = null;
        CompletableFuture<SfRenewalReportHomeWidget> renewalReportHomeWidgetFuture = null;
        CompletableFuture<SfFlashbackHomeWidget> flashbackHomeWidgetFuture = null;
        CompletableFuture<SfMasterLiveClassWidget> sfMasterLiveClassWidgetCompletableFuture = null;
        CompletableFuture<BaseWidgetNonVM> wellnessAtGlanceWidgetFuture = getWellnessAtGlanceWidgetFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> interventionsWidgetFuture = getInterventionsWidgetFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> blogsWidgetFuture = getBlogsWidgetFuture(userContext, sessionId);
        CompletableFuture<BaseWidgetNonVM> wellnessAtCenterWidgetFuture = null;

        if (isSugarFit) {
            flashbackHomeWidgetFuture = getFlashbackHomeWidgetFuture(userContext);
            cgmInstallationWebinarWidgetFuture = getCgmInstallationWebinarWidgetFuture(userContext);
            fbvScanWidgetFuture = getFbvScanWidgetFuture(userContext);
            pollWidgetsFuture = getPollCardWidgetsFuture(userContext);
            pollSurveyWidgetsFuture = getPollSurveyWidgetsFuture(userContext);
            challengeWidgetsFuture = getChallengeWidgetsFuture(userContext);
            renewalReportHomeWidgetFuture = getRenewalReportHomeWidgetFuture(userContext, renewalJourneyFuture);
            coachCelebrationWidgetFuture = getCoachCelebrationWidgetFuture(userContext, renewalJourneyFuture, assignedCareTeamFuture);
        }

        ActivePackResponse activePackResponse = activePackResponseFuture.get();
        ActivePackResponse wellnessActivePackResponse = wellnessActivePackResponseFuture.get();
        boolean isWellnessAtCenterActiveUser = ChronicCareAppUtil.isWellnessAtCenterActiveUser(userContext, wellnessActivePackResponse);
        if (isSugarFit) {
            if (Objects.nonNull(activePackResponse) && chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
                try {
                    if (masterClassClient.fetchCoachConsultationStatus(Long.valueOf(userContext.getUserProfile().getUserId()))) {
                        communityWebinarWidgetFuture = getCommunityWebinarWidgetFuture(userContext);
                    }
                } catch (Exception e) {
                    exceptionReportingService.reportException(e);
                }
            } else {
                communityWebinarWidgetFuture = getCommunityWebinarWidgetFuture(userContext);
            }
            if (isWellnessAtCenterActiveUser) {
                wellnessAtCenterWidgetFuture = getSfWellnessAtCenterWidgetFuture(userContext);
                groupClassBookingsFuture = chronicCareServiceHelper.getGroupClassBookingsFuture(userContext);
            } else {
                recommendedFitnessPlanWidgetFuture = getRecommendedFitnessPlanWidgetFuture(userContext);
            }

            if (Objects.nonNull(activePackResponse) &&  chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
                sfMasterLiveClassWidgetCompletableFuture = getMasterLiveClassWidgetFuture(userContext, activePackResponse.getBundleProduct().getProductCode(), null);
            }
        }
        CompletableFuture<BaseWidgetNonVM> activeCardsWidgetFuture = getActiveCardsWidgetFuture(userContext, patientDetailFuture.get(), activePackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, groupClassBookingsFuture, faceBasedVitalScansForDayResponseFuture.get());
        CompletableFuture<NPSModalData> npsModalDataCompletableFuture = getNPSDataFuture(userContext, activePackResponse);

        UserOnboardingActionWithContext onboardingActions = onboardingActionsFuture.get();
        if (Objects.isNull(onboardingActions)) {
            exceptionReportingService.reportWarning(new Exception("UserOnboardingActionWithContext is null"));
            return;
        }

        RenewalJourneyResponse renewalJourney = renewalJourneyFuture.get();
        if (isUltraFit) {
            miniConsultationCardsWidgetFuture = getMiniConsultationCardsWidgetFuture(userContext, onboardingActions, activePackResponse, assignedCareTeamFuture.get());
            animatedScoreWidgetFuture = getAnimatedScoreWidgetFuture(cgmStatFuture.get(), cgmOnboardingStatus,userContext);
            requestCGMWidgetFuture = getRequestCGMWidgetFuture(userContext, cgmOnboardingStatus, cgmUserRequestStatusFuture);
            experimentsWidgetFuture = getExperimentsWidgetFuture(userContext, cgmOnboardingStatus);
            ufReferralBannerWidgetFuture = getUfReferralBannerWidgetFuture(userContext);
        }
        CompletableFuture<BaseWidgetNonVM> onboardCompletionWidgetFuture = getOnboardCompletionWidgetFuture(userContext, onboardingActions);
        CompletableFuture<BaseWidgetNonVM> cgmTrackerWidgetFuture = getCgmTrackerWidgetFuture(userContext, onboardingActions, activePackResponse, cgmOnboardingStatus);
        CompletableFuture<CGMGraphWidgetV2> cgmGraphWidgetFuture = getCGMGraphWidgetFuture(userContext, cgmDeviceStatus, cgmStatFuture, cgmOnboardingStatus, false);
        CompletableFuture<BaseWidgetNonVM> activityCompareWidgetFuture = getActivityCompareWidgetFuture(userContext);
        CompletableFuture<AppAnnouncementData> appAnnouncementDataFuture = getAppAnnouncementDataFuture(userContext, cgmOnboardingStatus, activePackResponse, renewalJourney, wellnessActivePackResponse);
        CompletableFuture<CsTicketResolutionNotificationData> csTicketResolutionNotificationFuture = getCsTicketResolutionNotificationFuture(userContext);
        CompletableFuture<MegaSaleData> megaSalesBannerDataFuture = chronicCareServiceHelper.getMegaSalesDataFuture(userContext, cgmOnboardingStatus, activePackResponse.getBundleProduct(), wellnessActivePackResponse, false);
        CompletableFuture<SfBannerCarouselWidget> salesBannerWidgetFuture = getSalesBannerWidgetFuture(userContext, onboardingActions, cgmOnboardingStatus, renewalJourney, activePackResponse.getBundleProduct(), wellnessActivePackResponse);
        CompletableFuture<BaseWidgetNonVM> smartScaleIntegrationBannerWidgetFuture = chronicCareServiceHelper.getSmartScaleIntegrationBannerWidgetFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> smartScaleStatsWidgetCompletableFuture = chronicCareServiceHelper.getSmartScaleStatsWidgetCompletableFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> whatsNextWidgetFuture = getWhatsNextWidgetFuture(userContext, onboardingActions, activePackResponse, assignedCareTeamFuture.get(), patientDetailFuture.get(), wellnessActivePackResponse);
        CompletableFuture<AssignedAgentWidget> assignedAgentsWidgetFuture = getAssignedAgentsWidgetFuture(userContext, onboardingActions, activePackResponse, assignedCareTeamFuture.get(), consultationBookingsFuture, null);
        CompletableFuture<List<HomeMenuItem>> moreMenuItemsFuture = getMoreMenuItemsFuture(userContext, assignedAgentsWidgetFuture, wellnessAtCenterWidgetFuture);
        CompletableFuture<BookingModalConfig> bookingModalConfigFuture = getBookingModalConfigFuture(userContext, onboardingActions, activePackResponse, assignedCareTeamFuture.get(), patientDetailFuture.get());

        if (ChronicCareAppUtil.isSugarfitDocPlusUser(userContext)) {
            ChronicCareTeam assignedAgents = assignedCareTeamFuture.get();
            if (Objects.nonNull(assignedAgents) && Objects.nonNull(assignedAgents.getDoctor()) && Objects.nonNull(assignedAgents.getDoctor().getAgentResponse())) {
                String doctorName = assignedAgents.getDoctor().getAgentResponse().getName();
                DocPlusData docPlusData = new DocPlusData(
                        String.format("Dr. %s's Diabetes Reversal Program", doctorName),
                        String.format("Dr. %s has curated this program specifically for you based on your health condition.", doctorName));
                result.setDocPlusData(docPlusData);
            }
        }

        if (!isExperienceCentre && isSugarFit) {
            BaseWidgetNonVM renewalTimerStripWidget = getRenewalTimerStripWidget(userContext, onboardingActions, activePackResponse.getBundleProduct());
            if (renewalTimerStripWidget != null) {
                result.addWidget(renewalTimerStripWidget);
            }
        }

        if (!isExperienceCentre && !isInternationalSugarfitUser(userContext)) {
            try {
                MegaSaleData megaSaleData = megaSalesBannerDataFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (ChronicCareAppUtil.isMegaSaleSupportedAppVersion(userContext) && Objects.nonNull(megaSaleData)) {
                    result.setMegaSaleData(megaSaleData);
                } else {
                    BaseWidgetNonVM salesBannerWidget = salesBannerWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (salesBannerWidget != null) result.addWidget(salesBannerWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching salesBannerWidget", e);
            }
        }

        if (isSugarFit && !isExperienceCentre) {
            try {
                SfFlashbackHomeWidget flashbackHomeWidget = flashbackHomeWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (null != flashbackHomeWidget) {
                    result.addWidget(flashbackHomeWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching flashbackHomeWidget", e);
            }
        }

        SfCoachCommunitySessionWidget communityWebinarWidget = null;
        if (!isExperienceCentre && isSugarFit && communityWebinarWidgetFuture != null) {
            try {
                communityWebinarWidget = communityWebinarWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (communityWebinarWidget != null && communityWebinarWidget.isLive())
                    result.addWidget(communityWebinarWidget);
            } catch (Exception e) {
                // exceptionReportingService.reportException("Error in fetching communityWebinarWidget", e);
            }
        }

        if (!isExperienceCentre && isUltraFit) {
            try {
                BaseWidgetNonVM animatedScoreWidget = animatedScoreWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (animatedScoreWidget != null) result.addWidget(animatedScoreWidget);

                BaseWidgetNonVM sfRequestCgmWidget = requestCGMWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (sfRequestCgmWidget != null) result.addWidget(sfRequestCgmWidget);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching animatedScoreWidget", e);
            }
        }

        try {
            BaseWidgetNonVM activeCardWidget = activeCardsWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (null != activeCardWidget) {
                result.addWidget(activeCardWidget);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching activeCardWidget", e);
        }

        if (isSugarFit && !isExperienceCentre && sfMasterLiveClassWidgetCompletableFuture != null) {
            SfMasterLiveClassWidget sfMasterLiveClassWidget = sfMasterLiveClassWidgetCompletableFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
            if (sfMasterLiveClassWidget != null) {
                result.addWidget(sfMasterLiveClassWidget);
            }
        }

        if (onboardingActions.getPackRenewalContextActionWithContext().getAction().isActionPermitted()) {
            addRenewalAlertWidget(userContext, result, onboardingActions, assignedCareTeamFuture, activePackResponse.getBundleProduct(), cgmOnboardingStatus);
        } else if (chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
            BaseWidgetNonVM renewalWidget = renewalAlertWidgetBuilderV2.build(userContext, onboardingActions, activePackResponse.getBundleProduct().getProductCode());
            if (renewalWidget != null) {
                result.addWidget(renewalWidget);
            }
        }

        if (isUltraFit) {
            try {
                if (onboardingActions.getCoachCardActionWithContext().getAction().isFirstConsultDone() || onboardingActions.getAGMDataActionWithContext().getContext().isAtleastOneCGMCompleted() || onboardingActions.getAGMDataActionWithContext().getContext().isCGMActive()) {
                    BaseWidgetNonVM miniConsultationCardsWidget = miniConsultationCardsWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (miniConsultationCardsWidget != null) result.addWidget(miniConsultationCardsWidget);
                } else {
                    BaseWidgetNonVM whatsNextWidget = whatsNextWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (whatsNextWidget != null) result.addWidget(whatsNextWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching whatsNextWidget", e);
            }
        } else {
            try {
                BaseWidgetNonVM whatsNextWidget = whatsNextWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (whatsNextWidget != null) {
                    result.addWidget(whatsNextWidget);
                } else {
                    BaseWidgetNonVM onboardCompletionWidget = onboardCompletionWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (onboardCompletionWidget != null) result.addWidget(onboardCompletionWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching whatsNextWidget", e);
            }
        }

        try {
            BaseWidgetNonVM cgmTrackerWidget = cgmTrackerWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
            if (!isExperienceCentre && cgmTrackerWidget != null) {
                result.addWidget(cgmTrackerWidget);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching cgmTrackerWidget", e);
        }

        if (!isExperienceCentre && !cgmOnboardingStatus.isAtleastOneCGMStarted()) {
            addFbvWidgetToResult(result, userContext, cgmOnboardingStatus, activePackResponse, fbvScanWidgetFuture, faceBasedVitalScansForDayResponseFuture.get());
        }

        if (!isExperienceCentre && isSugarFit) {
            try {
                List<BaseWidgetNonVM> challengeWidgets = challengeWidgetsFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (CollectionUtils.isNotEmpty(challengeWidgets)) result.addWidgets(new ArrayList<>(challengeWidgets));
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching challengeWidgets", e);
            }
        }

        if (!isExperienceCentre && isSugarFit) {
            try {
                List<BaseWidgetNonVM> pollWidgets = pollWidgetsFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (CollectionUtils.isNotEmpty(pollWidgets)) result.addWidgets(new ArrayList<>(pollWidgets));
            } catch (Exception e) {
                log.error("Error in fetching pollWidgets" + e.getLocalizedMessage());
//                exceptionReportingService.reportException("Error in fetching pollWidgets", e);
            }

            try {
                List<BaseWidgetNonVM> pollSurveyWidgets = pollSurveyWidgetsFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (CollectionUtils.isNotEmpty(pollSurveyWidgets))
                    result.addWidgets(new ArrayList<>(pollSurveyWidgets));
            } catch (Exception e) {
                log.error("Error in fetching pollSurveyWidgets" + e.getLocalizedMessage());
//                exceptionReportingService.reportException("Error in fetching pollSurveyWidgets", e);
            }
        }

        if (!isExperienceCentre && isSugarFit) {
            try {
                SfCgmInstallationWebinarWidget cgmInstallationWebinarWidget = cgmInstallationWebinarWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (cgmInstallationWebinarWidget != null)
                    result.addWidget(cgmInstallationWebinarWidget);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching cgmInstallationWebinarWidget", e);
            }
        }

        if (!isExperienceCentre && isSugarFit) {
            try {
                if (ChronicCareAppUtil.smartScaleStatsWidgetSupported(userContext)) {
                    BaseWidgetNonVM smartScaleWidget = smartScaleStatsWidgetCompletableFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (smartScaleWidget != null) {
                        result.addWidget(smartScaleWidget);
                    }
                } else {
                    BaseWidgetNonVM scaleIntegrationBanner = smartScaleIntegrationBannerWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (scaleIntegrationBanner != null) {
                        result.addWidget(scaleIntegrationBanner);
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching smartScaleIntegrationBannerWidgetFuture", e);
            }
        }

        boolean shouldHideCgmGraph = false;
        if (!isExperienceCentre && isSugarFit) {
            try {
                SfCgmDeviceStatus sfCgmDeviceStatus = getCgmDeviceStatus(userContext, cgmOnboardingStatus, null,chronicCareServiceHelper,serviceInterfaces.getSfAlbusClient());
                if (!sfCgmDeviceStatus.getIsOnGoingDevice()
                        && sfCgmDeviceStatus.getShouldShowScanCardInsideCGMGraph()
                        && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                        && cgmOnboardingStatus.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon()
                        && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice())
                        && cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice().getStatus() == DeviceStatus.BCN_DELIVERED) {
                    SfBluconJourneyWidget bluconJourneyWidget = getBluconJourneyWidget(userContext,onboardingActions, cgmOnboardingStatus, patientDetailFuture.get().getId());
                    if (Objects.nonNull(bluconJourneyWidget))
                        result.addWidget(bluconJourneyWidget);
                    shouldHideCgmGraph = true;
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching SfBluconJourneyWidget", e);
            }
        }

        boolean isNonCGMProduct = ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(), cgmOnboardingStatus);
        if (!shouldHideCgmGraph) {
            addCGMGraphWidgetAndCgmConfigurations(userContext, result, onboardingActions, cgmGraphWidgetFuture, isNonCGMProduct, cgmDeviceStatus, cgmOnboardingStatus, patientDetailFuture.get().getId(), activePackResponse, assignedCareTeamFuture.get(), cgmUserRequestStatusFuture.get(), cgmConfigVersion);
        }

        if (isSugarFit && wellnessAtCenterWidgetFuture != null) {
            try {
                BaseWidgetNonVM wellnessAtCenterWidget = wellnessAtCenterWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (wellnessAtCenterWidget != null) result.addWidget(wellnessAtCenterWidget);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching recommendedFitnessPlanWidget", e);
            }
        }

        if (communityWebinarWidget != null && !communityWebinarWidget.isLive()) {
            result.addWidget(communityWebinarWidget);
        }

        if (!isExperienceCentre && isUltraFit) {
            try {
                BaseWidgetNonVM ufReferralBannerWidget = ufReferralBannerWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (ufReferralBannerWidget != null) {
                    result.addWidget(ufReferralBannerWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching ufReferralBannerWidget", e);
            }
        }

        if (isSugarFit && !isExperienceCentre && isRenewalReportSupported(userContext)) {
            try {
                SfCoachCelebrationHomeWidget coachCelebrationWidget = coachCelebrationWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (null != coachCelebrationWidget) {
                    result.addWidget(coachCelebrationWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching coachCelebrationWidget", e);
            }
        }

        if (isSugarFit && !isExperienceCentre && isRenewalReportSupported(userContext)
                && !chronicCareServiceHelper.isRenewalReportDisabledUser(userContext)) {
            try {
                SfRenewalReportHomeWidget renewalReportHomeWidget = renewalReportHomeWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (null != renewalReportHomeWidget) {
                    result.addWidget(renewalReportHomeWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching coachCelebrationWidget", e);
            }
        }

        if (!isExperienceCentre && cgmOnboardingStatus.isAtleastOneCGMStarted()) {
            addFbvWidgetToResult(result, userContext, cgmOnboardingStatus, activePackResponse, fbvScanWidgetFuture, faceBasedVitalScansForDayResponseFuture.get());
        }

        if (!isUltraFit) {
            result.addWidget(new ActivityLoggingWidget());
        }

        if (!isExperienceCentre && isUltraFit) {
            try {
                BaseWidgetNonVM experimentHomeWidget = experimentsWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (experimentHomeWidget != null) result.addWidget(experimentHomeWidget);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching experimentHomeWidget", e);
            }
        }

        try {
            BaseWidgetNonVM interventionsWidget = interventionsWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (interventionsWidget != null) result.addWidget(interventionsWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching interventionsWidget", e);
        }

        if (isSugarFit && recommendedFitnessPlanWidgetFuture != null) {
            try {
                SfRecommendedFitnessPlanWidget recommendedFitnessPlanWidget = recommendedFitnessPlanWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (recommendedFitnessPlanWidget != null) result.addWidget(recommendedFitnessPlanWidget);
            } catch (Exception e) {
                log.error("recommendedFitnessPlanWidgetFuture timeout" + e.getLocalizedMessage());
//                exceptionReportingService.reportException("Error in fetching recommendedFitnessPlanWidget", e);
            }
        }

        if (!isExperienceCentre && activityCompareWidgetFuture.get() != null) {
            try {
                result.addWidget((activityCompareWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS)));
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching activityCompareWidgetFuture", e);
            }
        }

        try {
            BaseWidgetNonVM wellnessAtGlanceWidget = wellnessAtGlanceWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (wellnessAtGlanceWidget != null) result.addWidget(wellnessAtGlanceWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching WellnessAtGlanceWidget", e);
        }

        try {
            if (assignedAgentsWidgetFuture != null) {
                AssignedAgentWidget assignedAgentsWidget = assignedAgentsWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (null != assignedAgentsWidget) {
                    result.addWidget(assignedAgentsWidget);
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching assignedAgentsWidget", e);
        }

        if (!isExperienceCentre && isUltraFit && isDiscordBannerEnabled(userContext)) {
            BaseWidgetNonVM discordBannerWidget = getDiscordBannerWidget();
            result.addWidget(discordBannerWidget);
        }

        try {
            BaseWidgetNonVM blogsWidget = blogsWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
            if (blogsWidget != null) result.addWidget(blogsWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching blogsWidget", e);
        }

        try {
            List<HomeMenuItem> moreMenuItems = moreMenuItemsFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
            if (moreMenuItems != null) result.setMenuItems(moreMenuItems);
        } catch (Exception e) {
            log.error("Error in fetching homepage moreMenuItems", e);
//            exceptionReportingService.reportException("Error in fetching homepage moreMenuItems", e);
        }

        if (!isExperienceCentre) {
            try {
                BookingModalConfig bookingModalConfig = bookingModalConfigFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (bookingModalConfig != null) result.setBookingModalConfig(bookingModalConfig);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching bookingModalConfig", e);
            }
        }

        if (!isExperienceCentre && shouldShowAppAnnouncement() && !isInternationalSugarfitUser(userContext)) {
            try {
                AppAnnouncementData appAnnouncementData = appAnnouncementDataFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (appAnnouncementData != null) {
                    result.setAppAnnouncementData(appAnnouncementData);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching appAnnouncementData", e);
            }
        }

        if (!isExperienceCentre && isSugarFit && npsModalDataCompletableFuture.get() != null) {
            NPSModalData npsModalData = npsModalDataCompletableFuture.get();
            result.setNpsModalData(npsModalData);
        }

        if (!isExperienceCentre && isSugarFit) {
            result.setReferralEnabled(ChronicCareAppUtil.isHomeHeaderReferralEnabled(userContext));
        }

        if (!isExperienceCentre) {
            try {
                CsTicketResolutionNotificationData csTicketResolutionNotificationData = csTicketResolutionNotificationFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (csTicketResolutionNotificationData != null) {
                    result.setCsTicketResolutionNotificationData(csTicketResolutionNotificationData);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching appAnnouncementData", e);
            }
        }
    }

    public void addPackUpcomingPageWidgets(SfHomePageView result, UserContext userContext, String sessionId) throws HttpException, ExecutionException, InterruptedException {
        String userId = userContext.getUserProfile().getUserId();
        Optional<ActivePackResponse> upcomingPackResponse = userOnboardingService.getSugarFitUpcomingPack(userId);

        if (upcomingPackResponse.isPresent()) {
            result.addWidget(getUpcomingSubscriptionWidget(userContext, upcomingPackResponse.get()));
            if (!isUltraFitApp(userContext)) {
                result.addWidget(new ActivityLoggingWidget());
            }
            result.addWidget(getBlogsWidgetFuture(userContext, sessionId).get());
        }
    }

    public void addPackExpiredPageWidgets(SfHomePageView result, UserContext userContext, String cgmDeviceId, Integer cgmConfigVersion, String sessionId) throws Exception {
        String userIdString = userContext.getUserProfile().getUserId();
        Long userId = Long.valueOf(userIdString);
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timezone = userContext.getUserProfile().getTimezone() != null ? TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()) : TimeZone.getTimeZone("Asia/Kolkata");
        CompletableFuture<PatientDetail> patientDetailFuture = chronicCarePatientService.getChronicCarePatientForUserIdFuture(userContext);
        CompletableFuture<ActivePackResponse> expiredPackResponseFuture = userOnboardingService.getSugarFitExpiredPackFuture(userIdString);
        CompletableFuture<ActivePackResponse> wellnessActivePackResponseFuture = userOnboardingService.getSugarFitWellnessActivePackResponseFuture(userIdString);
        CgmOnboardingStatusResponse cgmOnboardingStatus = chsClient.fetchOnboardingStatus(userId, null, getAppTenantFromUserContext(userContext));
        CompletableFuture<ChronicCareTeam> assignedCareTeamFuture = chronicCareServiceHelper.getAssignedCareTeamFuture(userContext, null, Objects.nonNull(expiredPackResponseFuture.get()) ? expiredPackResponseFuture.get().getBundleProduct() : null);
        CompletableFuture<RenewalJourneyResponse> renewalJourneyFuture = chronicCareServiceHelper.getRenewalJourneyFuture(userContext);
        ActivePackResponse expiredPackResponse = expiredPackResponseFuture.get();
        CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture = chronicCareServiceHelper.getConsultationBookingsFuture(userContext);
        CompletableFuture<UserOnboardingActionWithContext> onboardingActions = userOnboardingService.getUserOnboardingActionsFuture(userContext, expiredPackResponse != null ? expiredPackResponse.getBookingId() : null);

        SfCgmDeviceStatus cgmDeviceStatus = getExpiredPackCgmDeviceStatus(cgmOnboardingStatus, cgmDeviceId);
        CompletableFuture<CgmStat> cgmStatFuture = chronicCareServiceHelper.getCgmStatFuture(userId, cgmDeviceStatus.getCgmDeviceId(), true, appTenant, timezone);
        CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture = null;
        ActivePackResponse wellnessActivePackResponse = wellnessActivePackResponseFuture.get();
        CompletableFuture<MegaSaleData> megaSalesBannerDataFuture = chronicCareServiceHelper.getMegaSalesDataFuture(userContext, cgmOnboardingStatus, expiredPackResponse != null ? expiredPackResponse.getBundleProduct() : null, wellnessActivePackResponse, false);
        CompletableFuture<SfBannerCarouselWidget> salesBannerWidgetFuture = getSalesBannerWidgetFuture(userContext, onboardingActions.get(), cgmOnboardingStatus, renewalJourneyFuture.get(), expiredPackResponse != null ? expiredPackResponse.getBundleProduct() : null, wellnessActivePackResponse);
        CompletableFuture<CGMGraphWidgetV2> cgmGraphWidgetFuture = getCGMGraphWidgetFuture(userContext, cgmDeviceStatus, cgmStatFuture, cgmOnboardingStatus, true);
        CompletableFuture<BaseWidgetNonVM> blogsWidgetFuture = getBlogsWidgetFuture(userContext, sessionId);
        CompletableFuture<BaseWidgetNonVM> wellnessAtCenterWidgetFuture = null;

        boolean isWellnessAtCenterActiveUser = ChronicCareAppUtil.isWellnessAtCenterActiveUser(userContext, wellnessActivePackResponse);
        if (AppUtil.isSugarFitApp(userContext)) {
            if (isWellnessAtCenterActiveUser) {
                wellnessAtCenterWidgetFuture = getSfWellnessAtCenterWidgetFuture(userContext);
                groupClassBookingsFuture = chronicCareServiceHelper.getGroupClassBookingsFuture(userContext);
            }
        }
        CompletableFuture<BaseWidgetNonVM> activeCardsWidgetFuture = getActiveCardsWidgetFuture(userContext, patientDetailFuture.get(), expiredPackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, groupClassBookingsFuture, null);

        BaseWidgetNonVM renewalTimerStripWidget = getRenewalTimerStripWidget(userContext, null, expiredPackResponse == null ? null : expiredPackResponse.getBundleProduct());
        if (renewalTimerStripWidget != null) {
            result.addWidget(renewalTimerStripWidget);
        }

        try {
            MegaSaleData megaSaleData = megaSalesBannerDataFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (ChronicCareAppUtil.isMegaSaleSupportedAppVersion(userContext) && Objects.nonNull(megaSaleData)) {
                result.setMegaSaleData(megaSaleData);
            } else {
                BaseWidgetNonVM salesBannerWidget = salesBannerWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (salesBannerWidget != null) {
                    result.addWidget(salesBannerWidget);
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching ", e);
        }

        try {
            BaseWidgetNonVM activeCardWidget = activeCardsWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (null != activeCardWidget) {
                result.addWidget(activeCardWidget);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching activeCardWidget", e);
        }

        addRenewalAlertWidget(userContext, result, null, assignedCareTeamFuture, expiredPackResponse == null ? null : expiredPackResponse.getBundleProduct(), cgmOnboardingStatus);

        if (wellnessAtCenterWidgetFuture != null) {
            try {
                BaseWidgetNonVM wellnessAtCenterWidget = wellnessAtCenterWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (wellnessAtCenterWidget != null) result.addWidget(wellnessAtCenterWidget);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching recommendedFitnessPlanWidget", e);
            }
        }

        boolean isNonCGMProduct = expiredPackResponse == null || ChronicCareAppUtil.isNonCGMProduct(expiredPackResponse.getBundleProduct(), cgmOnboardingStatus);
        addCGMGraphWidgetAndCgmConfigurations(userContext, result, onboardingActions.get(), cgmGraphWidgetFuture, isNonCGMProduct, cgmDeviceStatus, cgmOnboardingStatus, patientDetailFuture.get().getId(), null, null, null, cgmConfigVersion);

        if (!AppUtil.isUltraFitApp(userContext)) {
            result.addWidget(new ActivityLoggingWidget());
        }

        result.addWidget(blogsWidgetFuture.get());
    }

    private void navigateUserToFreemiumHomePage(SfHomePageView result) {
        AutoNavigationToNuxWidget widget = new AutoNavigationToNuxWidget();
        widget.setPageName("sffreemiumhomepage");
        result.addWidget(widget);
    }

    private SubscriptionCardWidget getUpcomingSubscriptionWidget(UserContext userContext, ActivePackResponse upcomingPackResponse) throws HttpException {
        SubscriptionCardWidget upcomingSubscription = subscriptionPageViewBuilder.createSubscriptionWidgetForUpcomingPack(userContext, upcomingPackResponse);
        long maxStartDateDelay = chronicCareServiceHelper.getMaxStartDateDelay(upcomingPackResponse.getBundleProduct());
        upcomingSubscription.setMaxStartDateDelayWindowInDays(maxStartDateDelay);
        if (maxStartDateDelay > 0) {
            upcomingSubscription.setAllowStartDateChange(true);
            upcomingSubscription.setMaxStartDate(upcomingPackResponse.getPurchaseDate() + (maxStartDateDelay * MILLIS_IN_A_DAY));
        } else {
            upcomingSubscription.setAllowStartDateChange(false);
        }
        return upcomingSubscription;
    }

    private CongratulationsWidget getPackPurchaseGreetingWidget(UserContext userContext) {
        CongratulationsWidget congratsWidget = new CongratulationsWidget();
        congratsWidget.setCreativeType("BLUE");
        congratsWidget.setTitle("Congratulations!");
        congratsWidget.setSubTitle(isUltraFitApp(userContext) ? SF_WELCOME_MESSAGE : SF_WELCOME_MESSAGE);

        if (isSugarFitApp(userContext)) {
            congratsWidget.setBottomAction(Action.builder().title("START YOUR JOURNEY").actionType(ActionType.NAVIGATION).navigationType(NavigationType.NAVIGATE_REPLACE).url("curefit://nuxpreferences").build());
        } else {
            congratsWidget.setBottomAction(Action.builder().title("START YOUR JOURNEY").actionType(ActionType.NAVIGATION).url("curefit://preferenceform?pageNumber=1").build());
        }
        return congratsWidget;
    }

    private BaseWidgetNonVM getRenewalTimerStripWidget(UserContext userContext, UserOnboardingActionWithContext onboardingActions, BundleProduct bundleProduct) {
        try {
            if (chronicCareServiceHelper.isSpecialSugarControlPlanPack(bundleProduct)) {
                return renewalTimerStripWidgetBuilder.build(userContext, onboardingActions);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in adding renewal widgets", e);
        }
        return null;
    }

    public void addRenewalAlertWidget(UserContext userContext, SfHomePageView result, UserOnboardingActionWithContext onboardingActions, CompletableFuture<ChronicCareTeam> assignedCareTeamFuture, BundleProduct bundleProduct, CgmOnboardingStatusResponse cgmOnboardingStatusResponse) {
        try {
            if ((chronicCareServiceHelper.isQuickCommerceCGMPack(bundleProduct) || chronicCareServiceHelper.isShopifyCGMPack(bundleProduct)) && !cgmOnboardingStatusResponse.isAtleastOneCGMCompleted()) {
                return;
            }
            if (ChronicCareAppUtil.isStoreRCTComboEnabledUser(userContext)) {
                return;
            }
            ArrayList<BaseWidgetNonVM> widgets = new ArrayList<>();
            if (chronicCareServiceHelper.isSpecialSugarControlPlanPack(bundleProduct)) {
                BaseWidgetNonVM renewalWidget = renewalAlertWidgetBuilderV2.build(userContext, onboardingActions, bundleProduct.getProductCode());
                if (renewalWidget != null) {
                    result.addWidget(renewalWidget);
                }
            } else {
                ChronicCareTeam assignedCareTeam = assignedCareTeamFuture.get();
                if ((chronicCareServiceHelper.isQuickCommerceCGMPack(bundleProduct) || chronicCareServiceHelper.isShopifyCGMPack(bundleProduct))){
                    try {
                        SfBannerCarouselWidget cgmPurchaseBanner = chronicCareServiceHelper.getCgmPurchaseBannerWidget(bundleProduct);
                        if (Objects.nonNull(cgmPurchaseBanner)) {
                            result.addWidget(cgmPurchaseBanner);
                        }
                    } catch (Exception e) {
                        exceptionReportingService.reportException("Error in fetching CgmPurchaseBanner", e);
                    }
                    if (ChronicCareAppUtil.isRenewalCarouselWidgetSupported(userContext)){
                        result.addWidget(renewalCarouselWidgetBuilder.buildView(userContext));
                    }else{
                        result.addWidget(renewalAlertWidgetBuilder.build(userContext, onboardingActions, assignedCareTeam.getCoach(), widgets));
                    }
                } else {
                    result.addWidget(renewalAlertWidgetBuilder.build(userContext, onboardingActions, assignedCareTeam.getCoach(), widgets));
                    result.addWidgets(widgets);
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in adding renewal widgets", e);
        }
    }

    public CompletableFuture<SfFlashbackHomeWidget> getFlashbackHomeWidgetFuture(UserContext userContext) {

        return supplyAsync(() -> {
            try {
                if (ChronicCareAppUtil.shouldShowYearlyFlashbackCard()) {
                    Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                    FlashBackResponse flashBackResponse = smsClient.getLatestFlashBackCard(userId);

                    if (flashBackResponse != null && flashBackResponse.getFlashBackCardEntry() != null) {
                        SfFlashbackHomeWidget flashbackWidget = new SfFlashbackHomeWidget();

                        if (ChronicCareAppUtil.isTwentyFiveYearSupportedApp(userContext)) {
                            flashbackWidget.setBannerImage("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/year-recap-2-2024-12-31-23:12.png");
                            flashbackWidget.setYearLottie("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/year-review-2025-anim_v3-2024-12-24-19:19.json");
                        } else {
                            flashbackWidget.setBannerImage("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/year-recap-1-2024-12-31-23:11.png");
                        }
                        flashbackWidget.setAspectRatio((double) 750 /286);
                        flashbackWidget.setBannerAction(getFlashbackBannerAction());

                        return flashbackWidget;
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getFlashbackHomeWidgetFuture", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private static Action getFlashbackBannerAction() {
        Map<String, String> eventData = new HashMap<>();
        eventData.put("bannerType", "FLASHBACK_2024");
        eventData.put("widgetType", "SF_FLASHBACK_REPORT_HOME_WIDGET");
        Map<String, Object> analyticsData = new HashMap<>();
        analyticsData.put("eventKey", "widget_click");
        analyticsData.put("eventData", eventData);

        Action bannerAction = new Action();
        bannerAction.setUrl("curefit://sfflashbackreportpage");
        bannerAction.setActionType(ActionType.NAVIGATION);
        bannerAction.setAnalyticsData(analyticsData);
        return bannerAction;
    }

    private CompletableFuture<BaseWidgetNonVM> getActiveCardsWidgetFuture(UserContext userContext, PatientDetail patientDetail, CompletableFuture<ActivePackResponse> activePackResponseFuture,
                                                                          CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture, CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                                          CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture, FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        return supplyAsync(() -> {
            try {
                return getActiveCardsWidget(userContext, patientDetail, activePackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, groupClassBookingsFuture, faceBasedVitalScansForDayResponse);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getActiveCardsWidget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private ChronicCareActiveCardsWidget getActiveCardsWidget(UserContext userContext, PatientDetail patientDetail,
                                                              CompletableFuture<ActivePackResponse> activePackResponseFuture,
                                                              CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture,
                                                              CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                              CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture,
                                                              FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) throws ExecutionException, InterruptedException, TimeoutException {
        Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
        Date currentTime = calendar.getTime();

        CompletableFuture<List<ActiveCard>> consultationActiveCards = chronicCareServiceHelper.getConsultationActiveCardsFuture(userContext, activePackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, groupClassBookingsFuture, faceBasedVitalScansForDayResponse);
        CompletableFuture<List<ActiveCard>> diagnosticActiveCards = chronicCareServiceHelper.getDiagnosticActiveCardsFuture(userContext, patientDetail);
        CompletableFuture<List<ActiveCard>> liveClassActiveCards = chronicCareServiceHelper.getLiveActiveCardsFuture(userContext);
        CompletableFuture<List<ActiveCard>> phleboBookingCards = chronicCareServiceHelper.getPhleboActiveCardsFuture(userContext);
        CompletableFuture<ActiveCard> cgmDeliveryCard = chronicCareServiceHelper.getCgmDeliveryActiveCardFuture(userContext);
        CompletableFuture<List<ActiveCard>> ecommerceOrders = chronicCareServiceHelper.getECommerceOrderActiveCardsFuture(userContext);
        CompletableFuture<List<ActiveCard>> audioCallTasks = chronicCareServiceHelper.getAudioCallUpcomingCardsFuture(userContext);

        List<ActiveCard> upcomingCards = new ArrayList<>();
        List<ActiveCard> completedCards = new ArrayList<>();
        Set<ActiveCardStatus.StatusType> upcomingActiveCardStatus = Set.of(SCHEDULED, UPCOMING, STARTED);
        Set<ActiveCardStatus.StatusType> completedActiveCardStatus = Set.of(COMPLETED, REPORT_GENERATED, SAMPLE_COLLECTED);
        Set<ActiveCardStatus.StatusType> missedActiveCardStatus = Set.of(MISSED);
        Date consultationCompletedCutOff = DateUtils.addDays(currentTime, -1);
        Date diagnosticCompletedCutOff = DateUtils.addDays(currentTime, -2);
        for (ActiveCard activeCard : consultationActiveCards.get()) {
            if (activeCard.getEndDate().after(consultationCompletedCutOff)) {
                if (upcomingActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    upcomingCards.add(activeCard);
                } else if (completedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    if (activeCard.getStartDate().after(consultationCompletedCutOff)) {
                        completedCards.add(activeCard);
                    }
                } else if (missedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    if (activeCard.getEndDate().after(currentTime)) {
                        completedCards.add(activeCard);
                    }
                }
            }
        }
        for (ActiveCard activeCard : diagnosticActiveCards.get()) {
            if (activeCard.getEndDate().after(diagnosticCompletedCutOff)) {
                if (upcomingActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    upcomingCards.add(activeCard);
                } else if (completedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    completedCards.add(activeCard);
                } else if (missedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    completedCards.add(activeCard);
                }
            }
        }
        for (ActiveCard activeCard : phleboBookingCards.get()) {
            if (activeCard.getEndDate() != null && activeCard.getEndDate().after(currentTime)) {
                upcomingCards.add(activeCard);
            }
        }
        if (Objects.nonNull(cgmDeliveryCard.get())) {
            upcomingCards.add(cgmDeliveryCard.get());
        }
        upcomingCards.addAll(liveClassActiveCards.get());
        if (CollectionUtils.isNotEmpty(ecommerceOrders.get())) {
            upcomingCards.addAll(ecommerceOrders.get());
        }
        if (CollectionUtils.isNotEmpty(audioCallTasks.get())) {
            upcomingCards.addAll(audioCallTasks.get());
        }
        upcomingCards.sort(Comparator.comparingLong(a -> Math.abs(a.getStartDate().getTime() - currentTime.getTime())));
        completedCards.sort(Comparator.comparingLong(a -> a.getStartDate().getTime()));
        Collections.reverse(completedCards);
        List<ActiveCard> combinedCards = new ArrayList<>();
        combinedCards.addAll(upcomingCards);
        combinedCards.addAll(completedCards);
        ChronicCareActiveCardsWidget widget = new ChronicCareActiveCardsWidget();
        widget.setActiveCards(combinedCards);
        if (CollectionUtils.isEmpty(combinedCards)) {
            return null;
        }
        return widget;
    }

    private CompletableFuture<BaseWidgetNonVM> getWhatsNextWidgetFuture(UserContext userContext, UserOnboardingActionWithContext onboardingActions,
                                                                        ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam,
                                                                        PatientDetail patientDetail, ActivePackResponse wellnessActivePackResponse) {
        return supplyAsync(() -> {
            try {
                if (!chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
                    if (ChronicCareAppUtil.isCGMDplusIEnabled(userContext)) {
                        return getWhatNextWidgetV3(userContext, onboardingActions, activePackResponse, assignedCareTeam, patientDetail, wellnessActivePackResponse);
                    }
                    return getWhatNextWidgetV2(userContext, onboardingActions, activePackResponse, assignedCareTeam, patientDetail);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private WhatNextWidgetV2 getWhatNextWidgetV2(UserContext userContext, UserOnboardingActionWithContext context, ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam, PatientDetail patientDetail) throws NullPointerException {
        CardData coachCardData, doctorCardData = null, diagnosticCardData;
        List<CardData> foldedCards = new ArrayList<>();
        boolean isDoctorConsultBooked = !context.getDoctorCardActionWithContext().getAction().isShowCard();
        boolean isQuarterlyTest = context.getDiagnosticCardActionWithContext().getAction().getQuarterlyDiagnosticAllowed();
        boolean isFirstDoctorConsultDone = context.getDoctorCardActionWithContext().getAction().isFirstConsultDone();
        boolean isRenewalProduct = chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct());

        coachCardData = getCoachCardData(userContext, context, assignedCareTeam, activePackResponse);
        if (context.getCoachCardActionWithContext().getAction().isActionPermitted() || !isFirstDoctorConsultDone) {
            foldedCards.add(coachCardData);
        }

        diagnosticCardData = getDiagnosticCardData(userContext, context, patientDetail);
        boolean isDiagnosticsBookingAllowed = context.getDiagnosticCardActionWithContext().getAction().isActionPermitted();
        boolean isDiagnosticsProductAvailable = CollectionUtils.isNotEmpty(context.getDiagnosticCardActionWithContext().getContext().getProductCodes());
        if (!isUltraFitApp(userContext) && (isDiagnosticsBookingAllowed || !isFirstDoctorConsultDone) && isDiagnosticsProductAvailable) {
            foldedCards.add(diagnosticCardData);
        }

        if (isDoctorConsultBooked && diagnosticCardData.isCompleted() && coachCardData.isCompleted()) {
            return null;
        }

        boolean showDoctorConsultCard = context.getDoctorCardActionWithContext().getAction().isShowCard();
        if (!isUltraFitApp(userContext) && (!isRenewalProduct && (!isFirstDoctorConsultDone || showDoctorConsultCard))) {
            try {
                doctorCardData = getDoctorCardData(context, assignedCareTeam, activePackResponse, userContext);
            } catch (Exception e) {
                log.error("CCHomePage: Exception in getDoctorCardData for user :: {}, e :: {}", userContext.getUserProfile().getUserId(), e.getMessage(), e);
            }
            foldedCards.add(doctorCardData);
        }

        WhatNextCardData whatNextCardData = WhatNextCardData.builder().foldedCards(foldedCards).widgetTitle(isQuarterlyTest || isFirstDoctorConsultDone || isRenewalProduct ? "Continue your journey" : "Kickstart your journey").build();
        WhatNextWidgetV2 whatNextWidget = new WhatNextWidgetV2();
        whatNextWidget.setWhatNextCardData(whatNextCardData);
        return whatNextWidget;
    }

    private WhatNextWidgetV3 getWhatNextWidgetV3(UserContext userContext, UserOnboardingActionWithContext context,
                                                 ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam,
                                                 PatientDetail patientDetail, ActivePackResponse wellnessActivePackResponse) throws NullPointerException {
        try {
            WhatNextWidgetV3 widget = new WhatNextWidgetV3();
            CardData coachCardData = null, doctorCardData = null, diagnosticCardData = null, cgmCardData = null;
            List<CardData> cardsData = new ArrayList<>();
            boolean isDoctorConsultBooked = !context.getDoctorCardActionWithContext().getAction().isShowCard();
            boolean isQuarterlyTest = context.getDiagnosticCardActionWithContext().getAction()
                    .getQuarterlyDiagnosticAllowed();
            boolean isFirstDoctorConsultDone = context.getDoctorCardActionWithContext().getAction().isFirstConsultDone();
            boolean isRenewalProduct = chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct());
            boolean isCGMInstallationBookingAllowed = chronicCareServiceHelper.isCGMInstallationBookingAllowed(context);

            coachCardData = getCoachCardData(userContext, context, assignedCareTeam, activePackResponse);
            if (context.getCoachCardActionWithContext().getAction().isActionPermitted() || !isFirstDoctorConsultDone) {
                cardsData.add(coachCardData);
            }

            if(!isUltraFitApp(userContext) && isCGMInstallationBookingAllowed) {
                try {
                    cgmCardData = getCGMInstallationCardData(userContext, context);
                } catch (Exception e) {
                    log.error("CCHomePage: Exception in getCGMInstallationCardDataV2 for user :: {}, e :: {}", userContext.getUserProfile().getUserId(), e.getMessage(), e);
                }
                if(cgmCardData != null) {
                    cardsData.add(cgmCardData);
                }
            }

            diagnosticCardData = getDiagnosticCardData(userContext, context, patientDetail);
            if (!isUltraFitApp(userContext) &&
                    (context.getDiagnosticCardActionWithContext().getAction().isActionPermitted()
                            || !isFirstDoctorConsultDone)
                    &&
                    !CollectionUtils.isEmpty(context.getDiagnosticCardActionWithContext().getContext().getProductCodes())) {
                cardsData.add(diagnosticCardData);
            }

            if (isDoctorConsultBooked && diagnosticCardData.isCompleted() && coachCardData.isCompleted()) {
                return null;
            }

            if (!isUltraFitApp(userContext) && (!isRenewalProduct && (!isFirstDoctorConsultDone ||
                    context.getDoctorCardActionWithContext().getAction().isShowCard()))) {
                try {
                    doctorCardData = getDoctorCardData(context, assignedCareTeam, activePackResponse, userContext);
                } catch (Exception e) {
                    log.error("CCHomePage: Exception in getDoctorCardData for user :: {}, e :: {}",
                            userContext.getUserProfile().getUserId(), e.getMessage(), e);
                }
                cardsData.add(doctorCardData);
            }

            com.sugarfit.sms.entry.UserAttributeEntry userAttributeEntry = serviceInterfaces.getSmsClient().getUserAttributes(Long.valueOf(userContext.getUserProfile().getUserId()));
            if (userAttributeEntry != null && userAttributeEntry.getWelcomeCallStatus() != null
                    && userAttributeEntry.getWelcomeCallDate() != null
                    && Objects.equals(userAttributeEntry.getWelcomeCallStatus(), WelcomeCallStatus.COMPLETED)) {
                Calendar today = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
                if (SfDateUtils.getDifferenceDays(userAttributeEntry.getWelcomeCallDate(), today.getTime()) < 7) {
                    CardData wellnessAtCenterGroupClassCardData = getWellnessAtCenterGroupClassCardData(wellnessActivePackResponse, userContext);
                    if (wellnessAtCenterGroupClassCardData != null) {
                        cardsData.add(wellnessAtCenterGroupClassCardData);
                    }

                    CardData wellnessAtCenterTherapyClassCardData = getWellnessAtCenterTherapyCardData(wellnessActivePackResponse, userContext);
                    if (wellnessAtCenterTherapyClassCardData != null) {
                        cardsData.add(wellnessAtCenterTherapyClassCardData);
                    }
                }
            }

            widget.setWidgetTitle(isQuarterlyTest || isFirstDoctorConsultDone || isRenewalProduct
                    ? "Continue your journey"
                    : "Kickstart your reversal journey");

            widget.setCardsData(cardsData);
            int tasksPending = cardsData.stream().filter(el -> !el.isCompleted()).collect(Collectors.toList()).size();
            int totalTasks = cardsData.size();
            widget.setCardBoldTitle(tasksPending+" out of "+totalTasks);
            widget.setCardRegularTitle(totalTasks > 1 || totalTasks == 0 ? "tasks pending" : "task pending");
            if(tasksPending == 0) {
                return null;
            }
            return widget;
        } catch (Exception e) {
            log.error("Exception in getting Nux Journey Card"+e.getMessage());
        }
        return null;
    }

    private CardData getCoachCardData(UserContext userContext, UserOnboardingActionWithContext context, ChronicCareTeam assignedCareTeam, ActivePackResponse activePackResponse) {
        CardData coachCardData;
        PatientPreferredAgentResponse coach = assignedCareTeam.getCoach();
        Action coachCardAction;
        boolean isEnabled = context.getCoachCardActionWithContext().getAction().isActionPermitted();
        if (isEnabled) {
            Long coachCenterId = coach.getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
            Long coachUserId = coach.getAgentResponse().getId();
            String coachConsultationProduct = context.getCoachCardActionWithContext().getContext().getProductCodes().stream().findFirst().get();
            coachCardAction = Action.builder().isEnabled(true)
                    .title(isUltraFitApp(userContext) ? "Book Expert Consult" : "Book Coach Consult")
                    .actionType(ActionType.NAVIGATION)
                    .url(getAgentDatePickerUrl(coachConsultationProduct, coachCenterId, coachUserId, activePackResponse)).build();
        } else {
            coachCardAction = getDisabledActionForToastMessage("Locked", null);
        }
        coachCardData = CardData.builder().enabled(isEnabled).action(coachCardAction)
                .type(CardData.CardType.COACH).title(isEnabled ? "Consult with your coach" : "Call scheduled with Coach")
                .boldedText(coach.getAgentResponse().getName())
                .cardMessage(coach.getAgentResponse().getName()).instruction(isUltraFitApp(userContext)
                        ? "Schedule a consult and start your metabolic health journey"
                        : "This is the most important step to initiate the diabetes reversal journey")
                .imageURL(getAgentImageFromResponse(coach.getAgentResponse())).completed(!isEnabled).build();
        if (!AppUtil.isUltraFitApp(userContext)) coachCardData.setCollapsedMessage("");
        return coachCardData;
    }

    private CardData getDiagnosticCardData(UserContext userContext, UserOnboardingActionWithContext context, PatientDetail patientDetail) {
        CardData diagnosticCardData;
        Header header = new Header();
        header.setTitle("Instructions");
        Action diagnosticCardAction;
        boolean isEnabled = context.getDiagnosticCardActionWithContext().getAction().isActionPermitted();
        if (!context.getDiagnosticCardActionWithContext().getAction().getServiceableOnline()) {
            diagnosticCardData = CardData.builder()
                    .type(CardData.CardType.DIAGNOSTICS).boldedText("Schedule")
                    .cardMessage(context.getDiagnosticCardActionWithContext().getAction().getMessage())
                    .title(isEnabled ? "Book Diagnostic Test" : "Diagnostic tests booked")
                    .instruction("Important step to baseline your current health markers")
                    .imageURL("/image/chroniccare/WhatsNextDiagnosticsV2.png")
                    .enabled(isEnabled)
                    .completed(!isEnabled)
                    .build();
        } else {
            if (isEnabled) {
                UserPreferencePojo userAddressIdPreference = serviceInterfaces.sfAlbusClient.getUserPreference(userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "addressId");
                String diagnosticProductCode = context.getDiagnosticCardActionWithContext().getContext().getProductCodes().stream().findFirst().get();
                String provider = context.getDiagnosticCardActionWithContext().getContext().getDiagnosticProvider();
                diagnosticCardAction = Action.builder().isEnabled(true)
                        .title("Book Diagnostic Test").actionType(ActionType.NAVIGATION)
                        .meta(DiagnosticActionMeta.builder().header(header).build())
                        .url("curefit://selectCareDateV1?patientId=" + patientDetail.getId()
                                + "&productId=" + diagnosticProductCode + "&type=" + "DIAGNOSTICS"
                                + "&category=AT_HOME_SLOT&nextAction=checkout&productCodes=" + diagnosticProductCode
                                + "&addressId=" + userAddressIdPreference.getPreferenceTypeValues().get(0)
                                + "&parentBookingId=" + context.getBookingId() + "&diagnosticsProvider=" + provider).build();
            } else {
                String toastMessage = context.getDiagnosticCardActionWithContext().getAction().getMessage();
                diagnosticCardAction = getDisabledActionForToastMessage(toastMessage, "Book Diagnostic Test");
            }
            diagnosticCardData = CardData.builder().enabled(isEnabled)
                    .action(diagnosticCardAction).type(CardData.CardType.DIAGNOSTICS)
                    .boldedText("Schedule").cardMessage("Schedule a time to get your test done from the comfort of your home!")
                    .title(isEnabled ? "Book Diagnostic Test" : "Diagnostic tests booked")
                    .instruction("Important step to baseline your current health markers")
                    .imageURL("/image/chroniccare/WhatsNextDiagnosticsV2.png").completed(!isEnabled).build();
        }
        return diagnosticCardData;
    }

    private CardData getCGMInstallationCardData(UserContext userContext, UserOnboardingActionWithContext context) {

        boolean isCGMInstallationBookingPending = chronicCareServiceHelper.isCGMInstallationBookingPending(context);
        boolean isCGMInstallationCompleted = chronicCareServiceHelper.isCGMInstallationCompleted(context);
        boolean isWelcomeCallScheduled = !context.getCoachCardActionWithContext().getAction().isActionPermitted();
        boolean isEnabled = isWelcomeCallScheduled && isCGMInstallationBookingPending;
        Action cgmCardAction = chronicCareServiceHelper.getPhleboTaskRecommendedSlotsPageRedirectAction("", isEnabled, false, null);
        CardData cgmCardData = CardData.builder()
                .action(cgmCardAction)
                .type(CardData.CardType.CGM_INSTALLATION)
                .title(isCGMInstallationCompleted ? "CGM Installation Completed" : isCGMInstallationBookingPending ? "Book CGM Installation" : "CGM Installation Scheduled")
                .boldedText("")
                .cardMessage("")
                .instruction(isEnabled ? "Learn how your body responds to your eating habits, book your CGM delivery and installation" : "You can book CGM installation after booking your coach consultation first")
                .imageURL("image/chroniccare/whats_next_cgm.png")
                .enabled(isEnabled)
                .completed(isCGMInstallationCompleted || !isCGMInstallationBookingPending)
                .build();
        if (!AppUtil.isUltraFitApp(userContext))
            cgmCardData.setCollapsedMessage("");
        return cgmCardData;
    }

    private CardData getDoctorCardData(UserOnboardingActionWithContext context, ChronicCareTeam assignedCareTeam, ActivePackResponse activePackResponse, UserContext userContext) throws OllivanderClientException {
        CardData doctorCardData;
        PatientPreferredAgentResponse doctor = assignedCareTeam.getDoctor();
        Action doctorCardAction;
        String doctorName, profileImage, boldedText, instruction, cardMessage;
        boolean isEnabled = context.getDoctorCardActionWithContext().getAction().isActionPermitted();
        boolean showDoctorConsult = context.getDoctorCardActionWithContext().getAction().isShowCard();
        if (isEnabled) {
            Long doctorCenterId = getDoctorCenterIdForPaidUser(doctor);
            String doctorConsultationProduct = chronicCareServiceHelper.getDoctorConsultationProduct(activePackResponse.getBundleProduct());
            Action doctorBookingAction = Action.builder().isEnabled(true).title("Book Doctor Consult").actionType(ActionType.NAVIGATION).url(getAgentDatePickerUrl(doctorConsultationProduct, doctorCenterId, doctor.getAgentResponse().getId(), activePackResponse)).isPrimaryButton(true).build();
            doctorCardAction = chronicCareServiceHelper.getModifiedDoctorConsultActionBasedOnCgmCompletion(userContext, activePackResponse, doctorBookingAction);
            doctorName = getDoctorName(doctor.getAgentResponse().getName());
            profileImage = getAgentImageFromResponse(doctor.getAgentResponse());
            boldedText = doctorName;
            cardMessage = doctorName;
            instruction = "Book an appointment and consult with your doctor!";
        } else {
            if (context.getDoctorCardActionWithContext().getAction().isDoctorAssigned()) {
                String toastMessage = context.getDoctorCardActionWithContext().getAction().getReasonForProhibition();
                doctorCardAction = getDisabledActionForToastMessage(toastMessage, "Book Doctor Consult");
                doctorName = getDoctorName(doctor.getAgentResponse().getName());
                profileImage = getAgentImageFromResponse(doctor.getAgentResponse());
                boldedText = doctorName;
                cardMessage = doctorName;
                instruction = context.getDoctorCardActionWithContext().getAction().getReasonForProhibition();
            } else {
                doctorCardAction = getDisabledActionForToastMessage(DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT, "Book Doctor Consult");
                profileImage = DOCTOR_ASSIGNMENT_PLACEHOLDER_URL;
                boldedText = "Consult with your doctor";
                instruction = DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT;
                cardMessage = "Consult with your doctor";
            }
        }
        doctorCardData = CardData.builder()
                .action(doctorCardAction)
                .type(CardData.CardType.DOCTOR).cardMessage(cardMessage)
                .imageURL(profileImage).boldedText(boldedText)
                .title("Consult with your doctor").instruction(instruction)
                .enabled(isEnabled)
                .completed(!showDoctorConsult)
                .build();
        if (!AppUtil.isUltraFitApp(userContext) && doctor != null) {
            Long experience = Optional.ofNullable(doctor.getAgentResponse().getExperience()).orElse(1L);
            doctorCardData.setCollapsedMessage(String.format("%d+ yrs experience", experience));
        }
        return doctorCardData;
    }

    private CardData getWellnessAtCenterGroupClassCardData(ActivePackResponse wellnessActivePackResponse, UserContext userContext) {
        if (!ChronicCareAppUtil.isWellnessAtCenterActiveUser(userContext, wellnessActivePackResponse)) {
            return null;
        }

        boolean isEnabled = true;
        List<UserMembershipInfo> membershipInfos =  wellnessActivePackResponse.getUserMembershipInfos().stream().filter(i -> WELLNESS_CENTER_GROUP_THERAPY_PRODUCT.equals(i.getProductCode())).toList();
        if (CollectionUtils.isNotEmpty(membershipInfos)) {
            UserMembershipInfo membershipInfo = membershipInfos.get(0);
            if (Objects.nonNull(membershipInfo)) {
                isEnabled = membershipInfo.getTicketsConsumed() <= 0;
            }
        }

        CardData cardData = new CardData();
        cardData.setEnabled(isEnabled);
        cardData.setTitle("Book a Fitness session");
        cardData.setInstruction("Book a group wellness session");
        cardData.setImageURL("https://cdn-ext-sugarfit0.cure.fit/wellness/hero/group.png");
        cardData.setAction(Action.builder().isEnabled(true).title("Book session").actionType(ActionType.NAVIGATION).url("curefit://sfwellnessatcenterpdp?isGroupClass=true&productCode=" + WELLNESS_CENTER_GROUP_THERAPY_PRODUCT).isPrimaryButton(true).build());
        return cardData;
    }

    private CardData getWellnessAtCenterTherapyCardData(ActivePackResponse wellnessActivePackResponse, UserContext userContext) {
        if (!ChronicCareAppUtil.isWellnessAtCenterActiveUser(userContext, wellnessActivePackResponse)) {
            return null;
        }

        AtomicBoolean isEnabled = new AtomicBoolean(true);
        List<UserMembershipInfo> membershipInfos =  wellnessActivePackResponse.getUserMembershipInfos().stream().filter(i -> WELLNESS_CENTER_1_ON_1_THERAPY_PRODUCTS.contains(i.getProductCode())).toList();
        if (CollectionUtils.isNotEmpty(membershipInfos)) {
            membershipInfos.forEach(m -> {
                if (m.getTicketsConsumed() > 0) {
                    isEnabled.set(false);
                }
            });
        }

        CardData cardData = new CardData();
        cardData.setEnabled(isEnabled.get());
        cardData.setTitle("Book a Therapy session");
        cardData.setInstruction("Book a therapy session");
        cardData.setImageURL("https://cdn-ext-sugarfit0.cure.fit/wellness/hero/massage.png");
        cardData.setAction(Action.builder().isEnabled(true).title("Book session").actionType(ActionType.NAVIGATION).url("curefit://sfwellnessatcenterclp").isPrimaryButton(true).build());
        return cardData;
    }

    private CompletableFuture<BaseWidgetNonVM> getMiniConsultationCardsWidgetFuture(UserContext userContext, UserOnboardingActionWithContext onboardingActions, ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam) {
        return supplyAsync(() -> {
            try {
                return getMiniConsultationCardsWidget(userContext, onboardingActions, activePackResponse, assignedCareTeam);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private UfMiniConsultationCardsWidget getMiniConsultationCardsWidget(UserContext userContext, UserOnboardingActionWithContext context, ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam) throws NullPointerException {
        CardData coachCardData, doctorCardData = null;
        List<CardData> foldedCards = new ArrayList<>();
        boolean isDoctorConsultBooked = !context.getDoctorCardActionWithContext().getAction().isShowCard();
        boolean isFirstDoctorConsultDone = context.getDoctorCardActionWithContext().getAction().isFirstConsultDone();
        boolean isFirstCoachConsultDone = context.getCoachCardActionWithContext().getAction().isFirstConsultDone();
        boolean isCoachConsultBooked = !context.getCoachCardActionWithContext().getAction().isActionPermitted();
        boolean isRenewalProduct = chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct());

        if (isDoctorConsultBooked && isCoachConsultBooked) {
            return null;
        }

        if ((!isRenewalProduct && (!isFirstDoctorConsultDone || context.getDoctorCardActionWithContext().getAction().isShowCard())) && context.getDoctorCardActionWithContext().getAction().isActionPermitted()) {
            try {
                doctorCardData = getDoctorCardData(context, assignedCareTeam, activePackResponse, userContext);
                doctorCardData.setCollapsedMessage("Your Doctor");
                doctorCardData.getAction().setTitle("CONSULT");
            } catch (Exception e) {
                log.error("CCHomePage: Exception in getDoctorCardData for user :: {}, e :: {}", userContext.getUserProfile().getUserId(), e.getMessage(), e);
            }
            foldedCards.add(doctorCardData);
        }

        coachCardData = getCoachCardData(userContext, context, assignedCareTeam, activePackResponse);
        if (context.getCoachCardActionWithContext().getAction().isActionPermitted() || !isFirstCoachConsultDone) {
            coachCardData.setCollapsedMessage("Metabolic Expert");
            coachCardData.getAction().setTitle("CONSULT");
            foldedCards.add(coachCardData);
        }

        UfMiniConsultationCardsWidget miniConsultationCardsWidget = new UfMiniConsultationCardsWidget();
        miniConsultationCardsWidget.setConsultationCards(foldedCards);
        return miniConsultationCardsWidget;
    }

    private CompletableFuture<BaseWidgetNonVM> getOnboardCompletionWidgetFuture(UserContext userContext, UserOnboardingActionWithContext context) {
        return supplyAsync(() -> {
            try {
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
                String attributeName = "sf-onboarding-completion-msg-shown";
                boolean isOnboardingCompleted = context.getDoctorCardActionWithContext().getAction().isFirstConsultDone();

                if (!isOnboardingCompletionShown(userContext) && isOnboardingCompleted) {
                    OnboardingCompletionWidget onboardingCompletionWidget = new OnboardingCompletionWidget();
                    onboardingCompletionWidget.setWidgetTitle("Kickstart your reversal journey");
                    onboardingCompletionWidget.setTitle("Congratulations!");
                    onboardingCompletionWidget.setSubTitle("Your onboarding is complete. Wish you the best for your diabetes reversal journey!");

                    UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
                    userAttributeEntry.setUserId(userId);
                    userAttributeEntry.setAppTenant(appTenant);
                    userAttributeEntry.setAttribute(attributeName);
                    userAttributeEntry.setAttrValue(true);
                    userAttributeEntry.setDataType(DataType.BOOLEAN);
                    userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);

                    return onboardingCompletionWidget;
                }
            } catch (Exception e) {
                log.error("Exception in setting attribute", e);
                exceptionReportingService.reportException(e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private Boolean isOnboardingCompletionShown(UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            String attributeName = "sf-onboarding-completion-msg-shown";
            UserAttributesResponse userAttributesResponse = userAttributesClient.getAttributes(userId, attributeName, appTenant, null);
            if (null == userAttributesResponse || null == userAttributesResponse.getAttributes() || !userAttributesResponse.getAttributes().containsKey(attributeName) || userAttributesResponse.getAttributes().get(attributeName) == null) {
                return false;
            } else {
                return Boolean.valueOf(userAttributesResponse.getAttributes().get(attributeName).toString());
            }
        } catch (Exception e) {
            log.error(String.format("Exception in fetching user attributes for userId %s", userContext.getUserProfile().getUserId()));
            exceptionReportingService.reportException("Exception in fetching user attributes", e);
        }
        return true;
    }

    private CompletableFuture<BaseWidgetNonVM> getCgmTrackerWidgetFuture(UserContext userContext, UserOnboardingActionWithContext onboardingActions, ActivePackResponse activePackResponse, CgmOnboardingStatusResponse cgmOnboardingStatusResponse) {
        return supplyAsync(() -> {
            try {
                boolean isNonCGMProduct = ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(), cgmOnboardingStatusResponse);
                if (isNonCGMProduct) {
                    if (!Objects.equals(onboardingActions.getAGMDataActionWithContext().getContext().getIsDelivered(), null) && !onboardingActions.getAGMDataActionWithContext().getContext().getIsDelivered()) {
                        return getCgmTrackerWidget(userContext, onboardingActions, activePackResponse, true);
                    }
                } else if (onboardingActions.getCgmInstallationCardActionWithContext().getAction().isActionPermitted()) {
                    return getCgmTrackerWidget(userContext, onboardingActions, activePackResponse, false);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getCgmTrackerWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private ChronicCareCgmTrackerWidget getCgmTrackerWidget(UserContext userContext, UserOnboardingActionWithContext context, ActivePackResponse activePackResponse, boolean isNonCGMProduct) throws NullPointerException {
        if (isDiaconUser(userContext) || isNonCGMProduct || ChronicCareAppUtil.isDelayedCGMDeliveryUser(userContext))
            return null;

        ChronicCareCgmTrackerWidget cgmTrackerWidget = new ChronicCareCgmTrackerWidget();
        if (AppUtil.isUltraFitApp(userContext)) {
            cgmTrackerWidget.setTitle("CGM kit is on the way");
        }
        List<CareKitTrackerTimeline> cgmTimelines = new ArrayList<>();
        CareKitTrackerTimeline timeline1 = new CareKitTrackerTimeline();
        String trackerTitle = "The care kit is dispatched to your address";
        timeline1.setTitle(trackerTitle);
        timeline1.setCompleted(true);
        boolean coachWelcomeCallCompleted = context.getCoachCardActionWithContext().getContext().getAtLeastOneConsultationCompleted();
        boolean isRenewalProduct = chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct());
        CareKitTrackerTimeline timeline2 = new CareKitTrackerTimeline();
        if (!context.getAGMDataActionWithContext().getAction().isActionPermitted()) {
            cgmTrackerWidget.setTitle("Care kit has been delivered");
            timeline2.setTitle("The care kit has been delivered");
            timeline2.setCompleted(true);
        } else {
            if (isUltraFitApp(userContext) || coachWelcomeCallCompleted) {
                timeline2.setTitle("CGM kit will be delivered within 5-7 days");
            } else {
                timeline2.setTitle("The care kit will be delivered after coach consultation.");
            }
            timeline2.setCompleted(false);
        }

        CareKitTrackerTimeline timeline3 = new CareKitTrackerTimeline();
        timeline3.setTitle("CGM installation will be done post coach consult. We will reach out to schedule it");
        timeline3.setCompleted(!context.getAGMDataActionWithContext().getAction().isActionPermitted());

        String welcomeKitImageURL;
        if (isUltraFitApp(userContext)) {
            timeline2.setCompleted(true);
            cgmTimelines.add(timeline2);
            welcomeKitImageURL = ULTRAFIT_WELCOME_KIT_IMAGE;
        } else {
            cgmTimelines.add(timeline1);
            cgmTimelines.add(timeline2);
            cgmTimelines.add(timeline3);
            if (isRenewalProduct) {
                welcomeKitImageURL = SUGARFIT_RENEWAL_WELCOME_KIT_IMAGE;
            } else {
                welcomeKitImageURL = SUGARFIT_WELCOME_KIT_IMAGE;
            }
        }
        cgmTrackerWidget.setCgmTimelines(cgmTimelines);
        cgmTrackerWidget.setIsNonCGMProduct(false);
        cgmTrackerWidget.setPackType(activePackResponse.getBundleProduct().getClubCode());
        cgmTrackerWidget.setImageUrl(welcomeKitImageURL);
        cgmTrackerWidget.setShowDetailsCta(!isUltraFitApp(userContext));
        cgmTrackerWidget.setKitItemList(new ArrayList<>() {
            {
                add(new ChronicCareCgmTrackerWidget.KitItem("One Continuous Glucose Monitor", "CGM"));
            }
        });
        return cgmTrackerWidget;
    }

    private CompletableFuture<BaseWidgetNonVM> getAnimatedScoreWidgetFuture(CgmStat cgmStat, CgmOnboardingStatusResponse cgmOnboardingStatus, UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getAnimatedScoreWidget(cgmStat, cgmOnboardingStatus,userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfAnimatedScoreWidget getAnimatedScoreWidget(CgmStat cgmStat, CgmOnboardingStatusResponse cgmOnboardingStatus,UserContext userContext) {
        try {
            SfAnimatedScoreWidget animatedScoreWidget = new SfAnimatedScoreWidget();

            cgmStat.setDailySugarStatMap(getSortedStatMap(cgmStat.getDailySugarStatMap(),userContext));
            if (cgmStat.getDailySugarStatMap() != null) {
                List<DailySugarStat> dailySugarStats = Lists.newArrayList(cgmStat.getDailySugarStatMap().values());
                if (dailySugarStats.size() > 0) {
                    boolean isCgmActive = cgmOnboardingStatus.getCgmDeviceInfos().stream().anyMatch(CGMDeviceInfo::isOngoing);
                    DailySugarStat recentStat = null;
                    DailySugarStat prevStat = null;
                    int statIndex = dailySugarStats.size() - 1;
                    while (statIndex >= 0) {
                        if (dailySugarStats.get(statIndex).getSugarScore() != null) {
                            recentStat = dailySugarStats.get(statIndex);
                            if (statIndex > 0) {
                                prevStat = dailySugarStats.get(statIndex - 1);
                            }
                            break;
                        }
                        statIndex--;
                    }

                    if (isCgmActive && recentStat != null) {
                        animatedScoreWidget.setMetabolicScore(recentStat.getSugarScore());
                        animatedScoreWidget.setTimeOutsideTarget(recentStat.getTimeOverRangeGlucosePercentage());
                        animatedScoreWidget.setAvgGlucose(recentStat.getAvgSugarValue());
                        if (prevStat != null && prevStat.getSugarScore() != null) {
                            double scoreDiff = recentStat.getSugarScore() - prevStat.getSugarScore();
                            animatedScoreWidget.setScoreDiff(scoreDiff);
                        }
                    } else {
                        animatedScoreWidget.setMetabolicScore(cgmStat.getAverageMetabolicScore());
                        animatedScoreWidget.setTimeOutsideTarget(cgmStat.getTimeOverRangeGlucosePercentage());
                        animatedScoreWidget.setAvgGlucose(cgmStat.getAvgGlucose());
                        animatedScoreWidget.setMetabolicScoreSubTitle("average over the last CGM Period");
                        animatedScoreWidget.setTimeOutsideSubTitle("average over the last CGM Period");
                        animatedScoreWidget.setAvgGlucoseSubTitle("average over the last CGM Period");
                    }
                }
            }
            return animatedScoreWidget;
        } catch (Exception e) {
            log.error("Exception in animated score widget", e);
            exceptionReportingService.reportException("Exception in animated score widget", e);
        }
        return null;
    }

    private CompletableFuture<BaseWidgetNonVM> getRequestCGMWidgetFuture(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus, CompletableFuture<CgmUserRequestStatus> cgmUserRequestStatusFuture) {
        return supplyAsync(() -> {
            try {
                return getRequestCGMWidget(userContext, cgmOnboardingStatus, cgmUserRequestStatusFuture);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private BaseWidgetNonVM getRequestCGMWidget(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus, CompletableFuture<CgmUserRequestStatus> cgmUserRequestStatusFuture) {
        try {
            CgmUserRequestStatus cgmUserRequestStatus = cgmUserRequestStatusFuture.get();
            List<CGMDeviceInfo> cgmDeviceInfos = cgmOnboardingStatus.getCgmDeviceInfos();
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd");
            sdf.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
            if (cgmDeviceInfos != null && cgmDeviceInfos.size() > 0) {
                if (cgmUserRequestStatus.isEnableRequestCgmCta()) {
                    return new UFPackSubscriptionAlertWidget("Your CGM is expired", new Action("curefit://renewsubscription", "BUY NEW", ActionType.NAVIGATION), true);
                } else if (cgmUserRequestStatus.isEnableInstallCgmCta()) {
                    SfRequestCgmWidget sfRequestCgmWidget = new SfRequestCgmWidget();
                    CGMDeviceInfo cgmDeviceInfo = cgmDeviceInfos.get(cgmDeviceInfos.size() - 1);
                    Date startDate = cgmDeviceInfo.getStartedOn();
                    Date endDate = cgmDeviceInfo.getEndDate();
                    String startDateString = startDate != null ? sdf.format(startDate) : "";
                    String endDateString = endDate != null ? sdf.format(endDate) : "";
                    sfRequestCgmWidget.setDurationText("Last CGM period: " + startDateString + " - " + endDateString);
                    sfRequestCgmWidget.setNavAction(Action.builder().url("curefit://cgmtabbedpage").actionType(ActionType.NAVIGATION).build());
                    sfRequestCgmWidget.setIsInstallation(true);
                    sfRequestCgmWidget.setCtaAction(Action.builder().title("REQUEST CGM INSTALLATION").build());
                    return sfRequestCgmWidget;
                } else {
                    AtomicReference<Date> cgmStartDate = new AtomicReference<>();
                    if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
                        cgmDeviceInfos.forEach(cgmDeviceInfo -> {
                            if (cgmDeviceInfo.isOngoing()) {
                                cgmStartDate.set(cgmDeviceInfo.getStartedOn());
                            }
                        });
                    }
                    if (cgmStartDate.get() != null) {
                        long daysBetween = ChronoUnit.DAYS.between(LocalDateTime.ofInstant(cgmStartDate.get().toInstant(), ZoneId.of(userContext.getUserProfile().getTimezone())), LocalDateTime.now());
                        if (daysBetween > 11 && daysBetween < 14) {
                            long remainingDays = 14 - (daysBetween + 1);
                            String title = "Your CGM is expiring in " + remainingDays + (remainingDays == 1 ? " day" : " days");
                            Action renewAction = new Action("curefit://renewsubscription", "BUY NEW", ActionType.NAVIGATION);
                            return new UFPackSubscriptionAlertWidget(title, renewAction, true);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception in RequestCgmWidget", e);
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    private CompletableFuture<BaseWidgetNonVM> getUfReferralBannerWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> getUfReferralBannerWidget(userContext), serviceInterfaces.getTaskExecutor());
    }

    private SfBannerCarouselWidget getUfReferralBannerWidget(UserContext userContext) {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();

        try {
            ReferralConfigEntry referralConfigEntry = referralClient.getReferralConfig(appTenant);

            if (isUfReferralBannerEnabledForUser(userContext)) {
                Action referralAction = new Action();
                referralAction.setUrl("curefit://sfreferralpage");
                referralAction.setActionType(ActionType.NAVIGATION);
                BannerItem referralBannerItem = new BannerItem();
                referralBannerItem.setImage(referralConfigEntry.getHomepageImageUrl());
                referralBannerItem.setAction(referralAction);
                bannerItemList.add(referralBannerItem);
            }

            sfBannerCarouselWidget.setData(bannerItemList);
            sfBannerCarouselWidget.setLayoutProps(getUfReferralBannerLayoutProps());
        } catch (Exception e) {
            log.error("Exception in getting referral config", e);
            exceptionReportingService.reportException("Exception in getting referral config", e);
            return null;
        }

        if (CollectionUtils.isEmpty(bannerItemList)) return null;

        return sfBannerCarouselWidget;
    }

    private void addFbvWidgetToResult(SfHomePageView result, UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus,
                                      ActivePackResponse activePackResponse, CompletableFuture<SfHomeScanFaceWidget> faceScanWidgetFuture,
                                      FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        if (isFaceBasedVitalsEligibleForUser(userContext, cgmOnboardingStatus, activePackResponse)) {
            try {
                if (cgmOnboardingStatus.isAtleastOneCGMCompleted()
                        || ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(), cgmOnboardingStatus)
                        || ChronicCareAppUtil.isDelayedCGMDeliveryUser(userContext)
                        || ChronicCareAppUtil.isEnabledFbvFromDayOne(userContext)) {
                    SfHomeScanFaceWidget faceScanWidget = faceScanWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (faceScanWidget != null) {
                        result.addWidget(faceScanWidget);
                        result.setShowFaceScanFab(getShowFaceScanFab(userContext, cgmOnboardingStatus, faceScanWidget, activePackResponse, faceBasedVitalScansForDayResponse));
                    }
                } else if (!ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(), cgmOnboardingStatus)) {
                    result.addWidget(getFbvPreCGMBannerWidget());
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching faceScanWidget", e);
            }
        }
    }

    private CompletableFuture<SfHomeScanFaceWidget> getFbvScanWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getFbvScanWidget(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getFaceScanWidgetFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfHomeScanFaceWidget getFbvScanWidget(UserContext userContext) throws HttpException {
        SfHomeScanFaceWidget scanFaceWidget = new SfHomeScanFaceWidget();
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone userTimeZone = ChronicCareAppUtil.getUserTimezone(userContext);

        long coolDownPeriod = 4 * 60 * 60 * 1000L;
        String remainingCoolDownTime = "";
        Date currentDate = Calendar.getInstance(userTimeZone).getTime();

        FaceBasedVitalsRequest request = new FaceBasedVitalsRequest();
        request.setUserId(userId);
        request.setStartTimeEpoch(atStartOfDay(currentDate, userTimeZone));
        request.setEndTimeEpoch(atEndOfDay(currentDate, userTimeZone));

        FaceBasedVitalScansForDayResponse scansForDay = chsClient.getScansForDay(userId, userTimeZone);
        List<FaceBasedVitalLogsResponse> fbvLogs = chsClient.getFaceBasedVitalLogs(request, userTimeZone);
        boolean isScanEnabled = scansForDay.getScansDoneToday() == 0;
        boolean isScanExhasted = scansForDay.getScansDoneToday() >= scansForDay.getScansAllowedForDay();

        if (CollectionUtils.isNotEmpty(fbvLogs)) {
            FaceBasedVitalLogsResponse lastLog = fbvLogs.get(0);
            long differenceInMillis = currentDate.getTime() - lastLog.getTimeStamp().getTime();
            long remainingCooldownMillis = coolDownPeriod - differenceInMillis;
            if (remainingCooldownMillis < 60) {
                isScanEnabled = scansForDay.getScansDoneToday() < scansForDay.getScansAllowedForDay();
            } else {
                remainingCoolDownTime = getFormattedCooldownTimeForFBV(remainingCooldownMillis);
            }
        }

        Action scanFaceAction = new Action("curefit://sfscanfacepage", ActionType.NAVIGATION);
        scanFaceAction.setTitle(StringUtils.isNotEmpty(remainingCoolDownTime) ? remainingCoolDownTime : "SCAN NOW");
        scanFaceAction.setSubTitle(!isScanEnabled ? "SCAN AGAIN IN" : null);
        if (ChronicCareAppUtil.isUnlimitedFaceScanEnabledUser(userContext)) {
            scanFaceAction.setDisabled(false);
        } else {
            scanFaceAction.setDisabled(!isScanEnabled);
        }

        Action showHistory = new Action("curefit://fbvhistorypage?tab=logs", "HISTORY", ActionType.NAVIGATION);

        List<SfHomeScanFaceWidget.ScanItem> scanItemList = new ArrayList<>();
        Calendar wakeUpScanCalendar = Calendar.getInstance(userTimeZone);
        wakeUpScanCalendar.set(Calendar.HOUR_OF_DAY, 11);
        wakeUpScanCalendar.set(Calendar.MINUTE, 0);
        wakeUpScanCalendar.set(Calendar.SECOND, 0);
        wakeUpScanCalendar.set(Calendar.MILLISECOND, 0);
        Date wakeUpScanDeadline = wakeUpScanCalendar.getTime();

        SfHomeScanFaceWidget.ScanItem scanItem1 = new SfHomeScanFaceWidget.ScanItem();
        scanItem1.setScanItemText("On waking up (by 11 am)");
        if (CollectionUtils.isEmpty(fbvLogs)) {
            if (currentDate.before(wakeUpScanDeadline)) {
                scanItem1.setStatus(SfHomeScanFaceWidget.ScanItemStatus.PENDING);
            } else {
                scanItem1.setStatus(SfHomeScanFaceWidget.ScanItemStatus.MISSED);
            }
        } else {
            FaceBasedVitalLogsResponse lastLog = fbvLogs.get(0);
            if (!lastLog.getTimeStamp().before(wakeUpScanDeadline)) {
                scanItem1.setStatus(SfHomeScanFaceWidget.ScanItemStatus.MISSED);
                isScanExhasted = true;
            } else {
                scanItem1.setStatus(SfHomeScanFaceWidget.ScanItemStatus.DONE);
            }
        }

        SfHomeScanFaceWidget.ScanItem scanItem2 = new SfHomeScanFaceWidget.ScanItem();
        scanItem2.setScanItemText("After food ");
        scanItem2.setStatus(SfHomeScanFaceWidget.ScanItemStatus.PENDING);

        scanItemList.add(scanItem1);
        scanItemList.add(scanItem2);

        scanFaceWidget.setDescription(isScanEnabled ? "Get your essential health vitals with a simple face scan" : null);
        scanFaceWidget.setScanFaceAction(scanFaceAction);
        scanFaceWidget.setShowHistoryAction(showHistory);
        scanFaceWidget.setScanItems(scanItemList);
        scanFaceWidget.setScanExhausted(isScanExhasted);

        return scanFaceWidget;
    }

    private SfBannerCarouselWidget getFbvPreCGMBannerWidget() {
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();
        BannerItem referralBannerItem = new BannerItem();
        referralBannerItem.setImage("image/chroniccare/face_based_vitals_pre_cgm_banner.png");
        bannerItemList.add(referralBannerItem);
        sfBannerCarouselWidget.setData(bannerItemList);
        sfBannerCarouselWidget.setLayoutProps(getFBVBannerLayoutProps());

        return sfBannerCarouselWidget;
    }

    private boolean getShowFaceScanFab(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus,
                                       SfHomeScanFaceWidget faceScanWidget, ActivePackResponse activePackResponse,
                                       FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        try {
            boolean scanAvailableForDay = !faceScanWidget.getScanFaceAction().getDisabled();
            boolean isCgmActive = cgmOnboardingStatus.getCgmDeviceInfos().stream().anyMatch(CGMDeviceInfo::isOngoing);
            return isFaceBasedVitalsEnabledForUser(userContext, cgmOnboardingStatus, activePackResponse, faceBasedVitalScansForDayResponse) && scanAvailableForDay && !isCgmActive;
        } catch (Exception e) {
            log.error("Exception in getShowFaceScanFab", e);
            exceptionReportingService.reportException("Exception in getShowFaceScanFab", e);
            return false;
        }
    }

    public CompletableFuture<BaseWidgetNonVM> getExperimentsWidgetFuture(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus) {
        return supplyAsync(() -> {
            try {
                return getExperimentsWidget(userContext, cgmOnboardingStatus);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getExperimentHomeWidget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private UfExperimentHomeWidget getExperimentsWidget(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus) throws HttpException {
        UfExperimentHomeWidget experimentHomeWidget = new UfExperimentHomeWidget();

        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
        Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
        UserAssignedExperimentFilterRequest experimentRequest = new UserAssignedExperimentFilterRequest();
        experimentRequest.setUserId(userId);
        experimentRequest.setDate(calendar.getTime());
        experimentRequest.setType(ExperimentFilterType.ONGOING);

        UserAssignedExperimentResponse experimentResponse = experimentClient.filterAssignedExperiments(experimentRequest, appTenant, timeZone);
        boolean isCgmActive = cgmOnboardingStatus.getCgmDeviceInfos().stream().anyMatch(CGMDeviceInfo::isOngoing);

        if (CollectionUtils.isEmpty(cgmOnboardingStatus.getCgmDeviceInfos()) || (!isCgmActive && CollectionUtils.isEmpty(experimentResponse.getOngoingExperiments()))) {
            return null;
        }

        Action titleAction = Action.builder().url("curefit://experimenttabs?index=1").actionType(ActionType.NAVIGATION).build();
        Action joinMoreAction = Action.builder().url("curefit://experimenttabs?index=0").actionType(ActionType.NAVIGATION).build();

        experimentHomeWidget.setTitleAction(titleAction);
        experimentHomeWidget.setJoinMoreAction(joinMoreAction);
        experimentHomeWidget.setCgmActive(isCgmActive);
        return experimentHomeWidget;
    }

    private CompletableFuture<BaseWidgetNonVM> getSfWellnessAtCenterWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                if(ChronicCareAppUtil.isHomePageRevampEnabled(userContext)) {
                    return getSfWellnessAtCenterWidgetV2(userContext);
                }
                return getSfWellnessAtCenterWidget(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getExperimentHomeWidget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfWellnessAtCenterTherapyListCardWidget getSfWellnessAtCenterWidget(UserContext userContext) throws HttpException {
        try {
            SfWellnessAtCenterTherapyListCardWidget widget = new SfWellnessAtCenterTherapyListCardWidget();
            Header header = new Header();
            header.setTitle("Diabetes Reversal Clinic");
            header.setSeemore(Action.builder().url("curefit://sfwellnessatcenterclp").actionType(ActionType.NAVIGATION).build());
            widget.setHeader(header);
            List<SfWellnessTherapyProduct> therapies = ChronicCareServiceHelper.getAllWellnessAtCenterProducts(serviceInterfaces, userContext);
            widget.setTherapies(therapies.stream().peek(t -> t.setImages(t.getThumbnailImages())).toList());
            return widget;

        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    private SfWellnessAtCenterTherapyListCardWidgetV2 getSfWellnessAtCenterWidgetV2(UserContext userContext) throws HttpException {
        try {
            SfWellnessAtCenterTherapyListCardWidgetV2 widget = new SfWellnessAtCenterTherapyListCardWidgetV2();
            Header header = new Header();
            header.setTitle("Visit our\n");
            header.setBoldTitle("Diabetes Reversal Clinic");
            header.setSeemore(Action.builder().url("curefit://sfwellnessatcenterclp").actionType(ActionType.NAVIGATION).build());
            widget.setHeader(header);
            List<SfWellnessTherapyProduct> therapies = ChronicCareServiceHelper.getAllWellnessAtCenterProducts(serviceInterfaces, userContext);
            widget.setTherapies(therapies.stream().peek(t -> t.setImages(t.getThumbnailImages())).toList());
            return widget;

        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    private CompletableFuture<SfRecommendedFitnessPlanWidget> getRecommendedFitnessPlanWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            if (isRecommendedFitnessPlanEnabled(userContext)) {
                return recommendedFitnessPlanWidgetBuilder.buildView(userContext);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<SfMasterLiveClassWidget> getMasterLiveClassWidgetFuture(UserContext userContext, String productCode, List<LiveClass> liveClassesList) {
        return supplyAsync(() -> chronicCareServiceHelper.getMasterLiveClassWidget(userContext, productCode, liveClassesList), serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<List<BaseWidgetNonVM>> getChallengeWidgetsFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getChallengeWidgets(userContext);
            } catch (BaseException | ExecutionException | InterruptedException e) {
                exceptionReportingService.reportException(e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public List<BaseWidgetNonVM> getChallengeWidgets(UserContext userContext) throws BaseException, ExecutionException, InterruptedException {
        if (ChronicCareAppUtil.isChallengesEnabled(userContext)) {
            List<BaseWidgetNonVM> challengeCardWidgetArrayList = new ArrayList<>();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            Set<String> relevantSegments = serviceInterfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.SF_CHALLENGES_CLIENT);
            List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(relevantSegments));
            List<ChallengesEntry> activeChallenges = challengesClient.fetchActiveChallengesForUser(userId, 0, 5, "createdOn", "DESC", userSegments).getElements();
            List<ChallengesEntry> participatedChallenges = challengesClient.fetchUserParticipatedChallengesForUser(userId, 0, 5, "createdOn", "DESC").getElements();
            List<ChallengesEntry> challenges = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(activeChallenges)) challenges.addAll(activeChallenges);
            if (CollectionUtils.isNotEmpty(participatedChallenges)) {
                participatedChallenges.forEach(c -> {
                    Calendar yesterdayCal = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
                    yesterdayCal.add(Calendar.DATE, -1);
                    Calendar endDateCal = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
                    endDateCal.setTime(c.getEndDate());
                    if (endDateCal.get(Calendar.DAY_OF_YEAR) >= yesterdayCal.get(Calendar.DAY_OF_YEAR) && yesterdayCal.get(Calendar.YEAR) == endDateCal.get(Calendar.YEAR)) {
                        challenges.add(c);
                    }
                });
            }

            if (CollectionUtils.isNotEmpty(challenges)) {
                List<ChallengesEntry> tempChallenges;
                if (!ChronicCareAppUtil.isReferralChallengeSupportedApp(userContext)) {
                    tempChallenges = challenges.stream().filter(c -> !c.getChallengeType().equals(ChallengeType.REFERRAL_CHALLENGE)).toList();
                    challenges.clear();
                    challenges.addAll(tempChallenges);
                }
            }

            if (CollectionUtils.isNotEmpty(challenges)) {
                List<BaseWidgetNonVM> challengeCardWidgets = challenges.stream().map(challengesEntry -> {
                    try {
                        if (challengesEntry.getChallengeType() == ChallengeType.REFERRAL_CHALLENGE) {
                            return chronicCareServiceHelper.getReferralChallengeBannerWidget(userContext, challengesEntry, false);
                        } else {
                            List<ChallengesUserResponseEntry> userEntries = challengesClient.fetchUserResponsesEntries(userId, challengesEntry.getId());
                            List<LeaderboardEntry> leaderBoardEntries = challengesClient.fetchLeaderboard(userId, challengesEntry.getId(), 0, 4, "createdOn", "DESC").getElements();
                            return ChronicCareServiceHelper.buildChallengeCardWidget(userContext, challengesEntry, userEntries, CollectionUtils.isNotEmpty(leaderBoardEntries) ? leaderBoardEntries : null, serviceInterfaces, smsClient);
                        }
                    } catch (BaseException e) {
                        log.error("error in fetching challenges user response", e);
                        return null;
                    }
                }).toList();
                if (CollectionUtils.isNotEmpty(challengeCardWidgets)) {
                    challengeCardWidgetArrayList.addAll(challengeCardWidgets);
                }
            }
            return challengeCardWidgetArrayList;
        }
        return null;
    }

    public CompletableFuture<List<BaseWidgetNonVM>> getPollCardWidgetsFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getPollCardWidgets(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public List<BaseWidgetNonVM> getPollCardWidgets(UserContext userContext) throws Exception {
        if (ChronicCareAppUtil.isPollsEnabled(userContext)) {
            SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            Set<String> relevantSegments = serviceInterfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.SF_POLL_CLIENT);
            List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(relevantSegments));
            CompletableFuture<List<PollEntry>> activePollsFuture = getActivePollsFuture(userContext, userSegments);
            CompletableFuture<List<PollEntry>> participatedPollsFuture = getUserParticipatedPollsFuture(userContext);
            List<PollEntry> polls = new ArrayList<>();
            if (activePollsFuture.get() != null) polls.addAll(activePollsFuture.get());
            if (participatedPollsFuture.get() != null) polls.addAll(participatedPollsFuture.get());
            if (CollectionUtils.isNotEmpty(polls)) {
                List<CompletableFuture<BaseWidgetNonVM>> pollWidgetFutures = polls.stream().map(poll -> supplyAsync(() -> {
                    try {
                        if(poll.getPollType() == PollType.DDS) {
                            if(isMentalHealthEnabled(userContext)) {
                                return ChronicCareServiceHelper.buildMentalHealthPollWidget(userContext, poll);
                            } else {
                                return null;
                            }
                        } else {
                            return ChronicCareServiceHelper.buildPollWidgets(pollSupportClient, serviceInterfaces.userServiceClient, userContext, poll, null);
                        }
                    } catch (BaseException e) {
                        e.printStackTrace();
                        return null;
                    }
                }, serviceInterfaces.getTaskExecutor())).toList();
                List<BaseWidgetNonVM> pollWidgets = pollWidgetFutures.stream().map(pollWidgetFuture -> {
                    try {
                        return pollWidgetFuture.get();
                    } catch (Exception e) {
                        return null;
                    }
                }).filter(Objects::nonNull).toList();
                if (CollectionUtils.isNotEmpty(pollWidgets)) {
                    return pollWidgets;
                }
            }
        }
        return null;
    }

    CompletableFuture<List<PollEntry>> getActivePollsFuture(UserContext userContext, List<String> userSegments) {
        return supplyAsync(() -> {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            try {
                return pollSupportClient.fetchActivePollsV2(userId, 0, 10, "createdOn", "DESC", userSegments).getElements();
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching active polls", e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    CompletableFuture<List<PollEntry>> getUserParticipatedPollsFuture(UserContext userContext) {
        return supplyAsync(() -> {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            try {
                return pollSupportClient.fetchUserParticipatedPolls(userId, 0, 10, "createdOn", "DESC").getElements();
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching participated polls", e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    CompletableFuture<List<PollEntry>> getActiveSurveysFuture(UserContext userContext, List<String> userSegments) {
        return supplyAsync(() -> {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            try {
                return pollSupportClient.fetchActiveSurvey(userId, 0, 20, "createdOn", "DESC", userSegments).getElements();
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching participated polls", e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<List<BaseWidgetNonVM>> getPollSurveyWidgetsFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return sfPollSurveyBuilder.buildView(userContext);
            } catch (BaseException e) {
                exceptionReportingService.reportException(e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public static SfCgmDeviceStatus getCgmDeviceStatus(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus, String cgmDeviceId, ChronicCareServiceHelper chronicCareServiceHelper , AlbusClient albusClient) {
        SfCgmDeviceStatus deviceStatus = new SfCgmDeviceStatus(cgmDeviceId, false, false);
        boolean isSensorJustInitialized = false;
        boolean isTierTwoNonNfcWithSupportedApp = false;
        Boolean isOnGoingDevice = deviceStatus.getIsOnGoingDevice();
        boolean shouldShowScanCardInsideCGMGraph;
        List<CGMDeviceInfo> deviceInfos = cgmOnboardingStatus.getCgmDeviceInfos();
        if (cgmDeviceId == null || cgmDeviceId.isEmpty() || cgmDeviceId.equals(CGM_ACTIVATION_DUMMY_DEVICE_ID)) {
            cgmDeviceId = null;
            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                deviceInfos = deviceInfos.stream().filter(cgmDeviceInfo -> {
                    if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) || "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) || null == cgmDeviceInfo.getStartedOn()) {
                        return false;
                    }
                    return cgmDeviceInfo.isAtleastOneReadingDone() || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState());
                }).toList();

                if (deviceInfos.size() > 0) {
                    isSensorJustInitialized = true;
                    cgmDeviceId = deviceInfos.get(deviceInfos.size() - 1).getDeviceId();
                    isOnGoingDevice = deviceInfos.get(deviceInfos.size() - 1).isOngoing();
                }

                if(isSfPhotoReaderSupportedUser(userContext,albusClient, chronicCareServiceHelper))
                {
                    isTierTwoNonNfcWithSupportedApp = true;
                }
            }
            shouldShowScanCardInsideCGMGraph = (!isTierTwoNonNfcWithSupportedApp && isSensorJustInitialized && cgmOnboardingStatus.isEnableFirstInAppReading()) || cgmActivationAllowed(userContext, cgmOnboardingStatus);
        } else {
            String finalCgmDeviceId = cgmDeviceId;
            CGMDeviceInfo deviceInfo = deviceInfos.stream().filter(cgmDeviceInfo -> "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState()) && cgmDeviceInfo.getDeviceId().equals(finalCgmDeviceId)).findFirst().orElse(null);
            if (deviceInfo != null) {
                isSensorJustInitialized = true;
            }
            CGMDeviceInfo currentDevice = deviceInfos.stream().filter(cgmDeviceInfo -> cgmDeviceInfo.getDeviceId().equals(finalCgmDeviceId)).findFirst().orElse(null);
            if (currentDevice != null) {
                isOnGoingDevice = currentDevice.isOngoing();
            }
            if(isSfPhotoReaderSupportedUser(userContext,albusClient, chronicCareServiceHelper))
            {
                isTierTwoNonNfcWithSupportedApp = true;
            }
            shouldShowScanCardInsideCGMGraph = !isTierTwoNonNfcWithSupportedApp && isSensorJustInitialized && cgmOnboardingStatus.isEnableFirstInAppReading();
        }
        deviceStatus.setCgmDeviceId(cgmDeviceId);
        deviceStatus.setIsOnGoingDevice(isOnGoingDevice);
        deviceStatus.setShouldShowScanCardInsideCGMGraph(shouldShowScanCardInsideCGMGraph);
        return deviceStatus;
    }

    public SfCgmDeviceStatus getExpiredPackCgmDeviceStatus(CgmOnboardingStatusResponse cgmOnboardingStatus, String cgmDeviceId) {
        SfCgmDeviceStatus deviceStatus = new SfCgmDeviceStatus(cgmDeviceId, false, false);
        List<CGMDeviceInfo> deviceInfos = cgmOnboardingStatus.getCgmDeviceInfos();
        if (cgmDeviceId == null || cgmDeviceId.isEmpty() || cgmDeviceId.equals(CGM_ACTIVATION_DUMMY_DEVICE_ID)) {
            cgmDeviceId = null;
            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                deviceInfos = deviceInfos.stream().filter(cgmDeviceInfo -> {
                    if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) || "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) || null == cgmDeviceInfo.getStartedOn()) {
                        return false;
                    }
                    return cgmDeviceInfo.isAtleastOneReadingDone() || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState());
                }).toList();

                if (deviceInfos.size() > 0) {
                    cgmDeviceId = deviceInfos.get(deviceInfos.size() - 1).getDeviceId();
                }
            }
        }
        deviceStatus.setCgmDeviceId(cgmDeviceId);
        return deviceStatus;
    }

    private CompletableFuture<CGMGraphWidgetV2> getCGMGraphWidgetFuture(UserContext userContext, SfCgmDeviceStatus cgmDeviceStatus, CompletableFuture<CgmStat> cgmStatFuture, CgmOnboardingStatusResponse cgmOnboardingStatus, boolean isPackExpired) {
        return supplyAsync(() -> {
            try {
                return getCGMGraphWidget(userContext, cgmDeviceStatus, Objects.nonNull(cgmStatFuture) ? cgmStatFuture.get() : null, cgmOnboardingStatus, isPackExpired);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in getting cgm graph widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public SfBluconJourneyWidget getBluconJourneyWidget(UserContext userContext, UserOnboardingActionWithContext onboardingActions, CgmOnboardingStatusResponse cgmOnboardingStatus, Long patientId) throws ResourceNotFoundException {
        boolean allowConsultationSlotBooking = false;
        if (onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse() != null
                && onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions() != null) {
            allowConsultationSlotBooking = onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions().equals(SelfInstallationActions.ALLOW_BOOKING_FOR_BLUCON);
        }
        String os = userContext.getSessionInfo().getOsName();
        String loginDeeplink = "";
        if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink())) {
            loginDeeplink = cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink().get(os);
        }
        String passKey = "";
        if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice())) {
            passKey = cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice().getHardwareId();
        }

        SfBluconJourneyWidget bluconJourneyWidget = new SfBluconJourneyWidget();
        SfBluconJourneyWidget.Steps steps = bluconJourneyWidget.getSteps();

        SfBluconJourneyWidget.Booking booking = steps.getBooking();
        booking.setEnabled(allowConsultationSlotBooking);
        booking.setCompleted(!allowConsultationSlotBooking);
        if (booking.isCompleted()) {
            booking.setTitle("Your video consult is booked");
        }
        try {
            String phleboConsultProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
            Action bookSlotAction = chronicCareServiceHelper.getBookCGMInstallationByPhleboAction(patientId, "Book Consultation Slot", phleboConsultProductId);
            booking.setAction(bookSlotAction);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        SfBluconJourneyWidget.Download download = steps.getDownload();
        download.setEnabled(booking.isCompleted());
        download.setAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).title("Download App")
                .url(os.equalsIgnoreCase("android") ? "https://play.google.com/store/apps/details?id=com.ambrosia.linkblucon"
                        : "https://apps.apple.com/us/app/linkblucon/id1200239770").build());

        SfBluconJourneyWidget.Registration registration = steps.getRegistration();
        registration.setEnabled(booking.isCompleted());
        SfBluconJourneyWidget.RegdSteps regdSteps = registration.getSteps();
        SfBluconJourneyWidget.Passkey passkey = regdSteps.getPasskey();
        passkey.setPasskey(passKey);
        SfBluconJourneyWidget.Signin signin = regdSteps.getSignin();
        signin.setAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).title("Sign-in").url(loginDeeplink).build());
        regdSteps.setPasskey(passkey);
        regdSteps.setSignin(signin);
        registration.setSteps(regdSteps);

        steps.setBooking(booking);
        steps.setDownload(download);
        steps.setRegistration(registration);
        bluconJourneyWidget.setSteps(steps);
        return bluconJourneyWidget;
    }

    public CGMGraphWidgetV2 getCGMGraphWidget(UserContext userContext, SfCgmDeviceStatus cgmDeviceStatus, CgmStat cgmStat, CgmOnboardingStatusResponse cgmOnboardingStatus, boolean isPackExpired) throws IOException {
        boolean hasOnGoingCGM = cgmOnboardingStatus.getCgmDeviceInfos().stream().anyMatch(CGMDeviceInfo::isOngoing);

        cgmStat.setDailySugarStatMap(getSortedStatMap(cgmStat.getDailySugarStatMap(),userContext));
        if (!isPackExpired && cgmDeviceStatus.getShouldShowScanCardInsideCGMGraph() && cgmActivationAllowed(userContext, cgmOnboardingStatus)) {
            cgmStat.setCgmDeviceId(CGM_ACTIVATION_DUMMY_DEVICE_ID);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(getUserTimezone(userContext));
        Calendar cal = Calendar.getInstance(getUserTimezone(userContext));
        Date lastReadingDate = cal.getTime();
        try {
            if (Objects.nonNull(cgmOnboardingStatus.getLastReadingDate())) {
                lastReadingDate = cgmOnboardingStatus.getLastReadingDate();
                cal.add(Calendar.DATE, -HIDE_GRAPH_AFTER_DAYS_OF_COMPLETION);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        if (!isPackExpired && lastReadingDate.before(cal.getTime()) && !(hasOnGoingCGM || cgmActivationAllowed(userContext, cgmOnboardingStatus))) {
            return null;
        }
        List<CGMGraphWidgetV2.CGMActionSelection> cgmSelectionList = getCGMActionSelectionList(userContext, true, cgmOnboardingStatus);
        boolean showScanButtonOnCgmGraphWidget = !cgmOnboardingStatus.isEnableFirstInAppReading();
        CGMGraphWidgetV2.CGMGraphTopDisclaimer cgmGraphTopDisclaimer = getCgmTopInstruction(cgmOnboardingStatus, userContext);
        CGMGraphWidgetV2.CGMPurchaseInfo cgmPurchaseInfo = getCgmPurchaseInfo(cgmOnboardingStatus, cgmStat, userContext);
        return buildCGMGraphWidget(userContext, cgmStat, cgmSelectionList, showScanButtonOnCgmGraphWidget, cgmGraphTopDisclaimer, cgmPurchaseInfo);
    }

    public CGMGraphWidgetV2 buildCGMGraphWidget(UserContext userContext, CgmStat cgmStat,
                                                List<CGMGraphWidgetV2.CGMActionSelection> cgmSelectionList,
                                                boolean showScanButtonOnCgmGraphWidget,
                                                CGMGraphWidgetV2.CGMGraphTopDisclaimer cgmGraphTopDisclaimer,
                                                CGMGraphWidgetV2.CGMPurchaseInfo cgmPurchaseInfo) {
        String userId = userContext.getUserProfile().getUserId();
        String graphVersion = isLightWeightGraphEnabledForUser(userContext) ? "v2" : "v1";
        boolean isSkiaEngineEnabled = isSkiaEngineEnabled(userContext);
        if (null != cgmStat.getCurrentReadingMode() && cgmStat.getCurrentReadingMode() == ReadingMode.APP) {
            return new CGMGraphWidgetV2(userContext, cgmStat, CGMGraphWidget.IN_APP_SCAN_TEXT, cgmSelectionList, showScanButtonOnCgmGraphWidget, graphVersion, cgmGraphTopDisclaimer, cgmPurchaseInfo, isSkiaEngineEnabled);
        } else {
            return new CGMGraphWidgetV2(userContext, cgmStat, CGMGraphWidget.PHLEBO_SCAN_TEXT, cgmSelectionList, showScanButtonOnCgmGraphWidget, graphVersion, cgmGraphTopDisclaimer, cgmPurchaseInfo, isSkiaEngineEnabled);
        }
    }

    public List<CGMGraphWidgetV2.CGMActionSelection> getCGMActionSelectionList(UserContext userContext, boolean isHomePage, CgmOnboardingStatusResponse cgmOnboardingStatusResponse) {
        List<CGMDeviceInfo> cgmDeviceInfos = cgmOnboardingStatusResponse.getCgmDeviceInfos();
        List<CGMGraphWidgetV2.CGMActionSelection> cgmSelectionList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
            cgmDeviceInfos = cgmDeviceInfos.stream().filter(cgmDeviceInfo -> {
                if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) || "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) || null == cgmDeviceInfo.getStartedOn()) {
                    return false;
                }
                return isHomePage ? (cgmDeviceInfo.isAtleastOneReadingDone() || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState())) : cgmDeviceInfo.isAtleastOneReadingDone();
            }).collect(Collectors.toList());
            for (int idx = 0; idx < cgmDeviceInfos.size(); idx++) {
                CGMDeviceInfo cgmDeviceInfo = cgmDeviceInfos.get(idx);
                CGMGraphWidgetV2.CGMActionSelection cgmSelection = new CGMGraphWidgetV2.CGMActionSelection();
                String title = "CGM #" + (idx + 1);
                String subTitle;
                if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) || "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) || null == cgmDeviceInfo.getStartedOn()) {
                    continue;
                }
                if (cgmDeviceInfo.isOngoing()) {
                    subTitle = "(Ongoing)";
                } else {
                    String startDate = TimeUtil.formatDateInTimezone(TimeUtil.IST_TIMEZONE, cgmDeviceInfo.getStartedOn(), "MMM d");
                    String endDate = TimeUtil.formatDateInTimezone(TimeUtil.IST_TIMEZONE, cgmDeviceInfo.getEndDate(), "MMM d");
                    subTitle = "(" + startDate + " - " + endDate + ")";
                }
                cgmSelection.setTitle(title);
                cgmSelection.setId(cgmDeviceInfo.getDeviceId());
                cgmSelection.setSubTitle(subTitle);
                cgmSelection.setActionType("CGM_DATA");
                cgmSelectionList.add(cgmSelection);
            }
        }
        if (cgmActivationAllowed(userContext, cgmOnboardingStatusResponse)) {
            CGMGraphWidgetV2.CGMActionSelection cgmSelection = new CGMGraphWidgetV2.CGMActionSelection();
            String title = "CGM #" + (cgmDeviceInfos.size() + 1);
            cgmSelection.setTitle(title);
            cgmSelection.setSubTitle("");
            cgmSelection.setId(CGM_ACTIVATION_DUMMY_DEVICE_ID);
            cgmSelection.setActionType("CGM_DATA");
            cgmSelectionList.add(cgmSelection);
        }
        return cgmSelectionList;
    }

    private CGMGraphWidgetV2.CGMGraphTopDisclaimer getCgmTopInstruction(CgmOnboardingStatusResponse cgmOnboardingStatus, UserContext userContext) {
        boolean isOnBlucon = false;
        if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())) {
            isOnBlucon = cgmOnboardingStatus.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon();
        }
        List<CGMDeviceInfo> deviceInfoList = cgmOnboardingStatus.getCgmDeviceInfos();

        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            CGMDeviceInfo latestCgmDevice = deviceInfoList.get(deviceInfoList.size() - 1);
            Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
            if (latestCgmDevice.getCurrentState().equals("AGM_CRASHED")) {
                long diffInMillis = calendar.getTime().getTime() - latestCgmDevice.getEndDate().getTime();
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                if (diffInHours <= 72) {
                    return CGMGraphWidgetV2.getCgmCrashedDisclaimer(userContext);
                }
            } else if (latestCgmDevice.getCurrentState().equals("AGM_FINAL_READING_UPLOADED") || latestCgmDevice.getCurrentState().equals("AGM_ENDED")) {
                long diffInMillis = calendar.getTime().getTime() - latestCgmDevice.getEndDate().getTime();
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                if (diffInHours <= 72 && isUltraFitApp(userContext)) {
                    return CGMGraphWidgetV2.getCgmEndedDisclaimer(userContext);
                }
            } else if (latestCgmDevice.getEndDate() == null && latestCgmDevice.getStartedOn() != null && (latestCgmDevice.getCurrentState().equals("AGM_FIRST_READING_UPLOADED") || latestCgmDevice.getCurrentState().equals("AGM_READING_UPLOADED"))) {
                long diffInMillis = calendar.getTime().getTime() - latestCgmDevice.getStartedOn().getTime();
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                if (diffInHours <= 24) {
                    return CGMGraphWidgetV2.get24HoursDisclaimer(userContext);
                } else if (isOnBlucon) {
                    try {
                        BluconEventEntry bluconEventEntry = chsClient.fetchLatestBluconEvent(
                                Long.valueOf(userContext.getUserProfile().getUserId()), AppUtil.getAppTenantFromUserContext(userContext));
                        if (Objects.nonNull(bluconEventEntry)) {
                            String os = userContext.getSessionInfo().getOsName();
                            String loginDeeplink = "";
                            if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                                    && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink())) {
                                loginDeeplink = cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink().get(os);
                            }
                            switch (bluconEventEntry.getEventName()) {
                                case "BLUCON_CONNECTION" -> {
                                    if (bluconEventEntry.getResult().equalsIgnoreCase("Connected")) {
                                        return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                                    } else if (bluconEventEntry.getResult().equalsIgnoreCase("Disconnected")) {
                                        return CGMGraphWidgetV2.getBluconDisconnectedDisclaimer(userContext, loginDeeplink);
                                    }
                                }
                                case "BLUCON_BATTERY_STATUS" -> {
                                    if (bluconEventEntry.getResult().equalsIgnoreCase("low-battery")) {
                                        return CGMGraphWidgetV2.getBluconBatteryLowDisclaimer(userContext);
                                    }
                                }
                                case "BAD_READINGS" -> {
                                    return CGMGraphWidgetV2.getBluconDisplacedDisclaimer(userContext);
                                }
                                case "PATCH_ERROR" -> {
                                    if (bluconEventEntry.getResult().equalsIgnoreCase("Patch not found")
                                            || bluconEventEntry.getResult().equalsIgnoreCase("Patch reading error")) {
                                        return CGMGraphWidgetV2.getBluconDisplacedDisclaimer(userContext);
                                    }
                                }
                                case "LINKBLUCON_CRASH" -> {
                                    return CGMGraphWidgetV2.getBluconDisconnectedDisclaimer(userContext, loginDeeplink);
                                }
                                case "logout" -> {
                                    return CGMGraphWidgetV2.getBluconLoggedOutDisclaimer(userContext, loginDeeplink);
                                }
                                default -> {
                                    return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                                }
                            }
                        } else {
                            return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                        }
                    } catch (Exception e) {
                        exceptionReportingService.reportException(e);
                        return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                    }
                }
            }
        }

        return null;
    }

    private CGMGraphWidgetV2.CGMPurchaseInfo getCgmPurchaseInfo(CgmOnboardingStatusResponse cgmOnboardingStatusResponse, CgmStat cgmStat, UserContext userContext) throws IOException {
        CGMGraphWidgetV2.CGMPurchaseInfo cgmPurchaseInfo = new CGMGraphWidgetV2.CGMPurchaseInfo();
        boolean showCgmPurchaseCard = false;
        String metabolicScoreText = "";
        List<CGMDeviceInfo> deviceInfoList = cgmOnboardingStatusResponse.getCgmDeviceInfos();

        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            CGMDeviceInfo latestCgmDevice = deviceInfoList.get(deviceInfoList.size() - 1);
            if (latestCgmDevice.getCurrentState().equals("AGM_FINAL_READING_UPLOADED") || latestCgmDevice.getCurrentState().equals("AGM_ENDED")) {
                showCgmPurchaseCard = true;
                SfHomepageBannerConfig sfHomepageBannerConfig = null;
                try {
                    sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {}, new SfHomepageBannerConfig());
                } catch (Exception e) {
                    log.error("Error in fetching sales config", e);
                }
                if (sfHomepageBannerConfig != null && sfHomepageBannerConfig.getSaleBanners() != null && sfHomepageBannerConfig.getSaleBanners().getCgmSmall() != null) {
                    String bannerUrl = sfHomepageBannerConfig.getSaleBanners().getCgmSmall().getImageUrl();
                    if (bannerUrl != null && !bannerUrl.isEmpty()) {
                        cgmPurchaseInfo.setCgmBannerImage(bannerUrl);
                    }
                }
                if (cgmStat.getAverageMetabolicScore() >= MIN_METABOLIC_SCORE_FOR_CGM_INFO)
                    metabolicScoreText = "You have had great progress. Buy another CGM & get more insights.";
                else metabolicScoreText = "Unlock more insights by buying another CGM.";
            }
        }

        cgmPurchaseInfo.setMetabolicScoreText(metabolicScoreText);
        cgmPurchaseInfo.setShowCgmPurchaseCard(showCgmPurchaseCard);
        cgmPurchaseInfo.setBtnAction(Action.builder().title("BUY NEW CGM").url(ChronicCareServiceHelper.getCgmStorePageLink(userContext)).actionType(ActionType.NAVIGATION).build());
        return cgmPurchaseInfo;
    }

    private CGMGraphWidgetV2.BluconReturnModalData getBluconReturnModalData(UserContext userContext) {
        try {
            List<NotificationMeta> notificationMetaList = serviceInterfaces.inAppNotificationsService.getActiveUserAndAppId(userContext.getUserProfile().getUserId(),
                    "SUGARFIT");
            NotificationMeta notificationMeta = null;
            Optional<NotificationMeta> cgmEndedNotificationOpt = notificationMetaList.stream().filter(notification ->
                    !Objects.isNull(notification.getTaskId()) && notification.getTaskId().contains("CGM-END")).findFirst();
            Optional<NotificationMeta> cgmEndingNotificationOpt;
            if (cgmEndedNotificationOpt.isPresent()) {
                notificationMeta = cgmEndedNotificationOpt.get();
            } else {
                cgmEndingNotificationOpt = notificationMetaList.stream().filter(notification ->
                        !Objects.isNull(notification.getTaskId()) && notification.getTaskId().contains("BLUCON-END-REMINDER")).findFirst();
                if (cgmEndingNotificationOpt.isPresent()) {
                    notificationMeta = cgmEndingNotificationOpt.get();
                }
            }

            if (Objects.nonNull(notificationMeta)) {
                CGMGraphWidgetV2.BluconReturnModalData bluconReturnModalData = new CGMGraphWidgetV2.BluconReturnModalData();
                bluconReturnModalData.setShowModal(true);
                bluconReturnModalData.setTitle("IMPORTANT INFORMATION");
                bluconReturnModalData.setSubTitle(cgmEndedNotificationOpt.isPresent() ?
                        "Your CGM has ended.\nPlease make note of the following things" :
                        "Your CGM will end soon.\nPlease make note of the following things");

                Action viewedAction = new Action();
                viewedAction.setActionType(ActionType.REST_API);
                viewedAction.setTitle("I understand");
                ActionMeta closeActionMeta = new ActionMeta();
                closeActionMeta.setBody(new SfNoShowPenaltyBuilder.CloseActionState());
                closeActionMeta.setMethod("POST");
                closeActionMeta.setUrl("/user/inAppNotification/" + notificationMeta.getNotificationId());
                viewedAction.setMeta(closeActionMeta);
                bluconReturnModalData.setViewedAction(viewedAction);
                return bluconReturnModalData;
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    private void addCGMGraphWidgetAndCgmConfigurations(UserContext userContext, SfHomePageView result,
                                                       UserOnboardingActionWithContext onboardingActions,
                                                       CompletableFuture<CGMGraphWidgetV2> cgmGraphWidgetFuture,
                                                       Boolean isNonCGMProduct, SfCgmDeviceStatus cgmDeviceStatus,
                                                       CgmOnboardingStatusResponse cgmOnboardingStatus, Long patientId,
                                                       ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam,
                                                       CgmUserRequestStatus cgmUserRequestStatus, Integer cgmConfigVersion) {
        try {
            CGMGraphWidgetV2 cgmGraphWidget = cgmGraphWidgetFuture.get();
            SfUserCGMConfig userCGMConfiguration = new SfUserCGMConfig();
            boolean allowCGMActivation = false;
            boolean allowConsultationSlotBooking = false;
            if (onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse() != null
                    && onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions() != null) {
                allowCGMActivation = onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions().equals(SelfInstallationActions.ALLOW_SELF_ACTIVATION);
                allowConsultationSlotBooking = onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions().equals(SelfInstallationActions.ALLOW_BOOKING_FOR_CGM);
            }

            if (null != cgmGraphWidget) {
                boolean isOnBlucon = false;
                if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())) {
                    isOnBlucon = cgmOnboardingStatus.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon();
                    if (isOnBlucon) {
                        cgmGraphWidget.setBluconReturnModalData(getBluconReturnModalData(userContext));
                    }
                }

                if (cgmDeviceStatus.getShouldShowScanCardInsideCGMGraph()) {
                    ScanSensorCardWidget scanSensorCardWidget = buildSensorCardWidgetForCgmGraph(userContext, allowCGMActivation,
                            allowConsultationSlotBooking, cgmDeviceStatus, cgmOnboardingStatus, cgmUserRequestStatus, onboardingActions, patientId, activePackResponse);
                    cgmGraphWidget.setShowScanSensorCardWidget(true);
                    cgmGraphWidget.setSensorCardWidget(scanSensorCardWidget);
                    cgmGraphWidget.setNavToInsight(false);
                }
                result.addWidget(cgmGraphWidget);
                userCGMConfiguration.setOnBlucon(isOnBlucon);
                userCGMConfiguration.setCgmReadingMode(Objects.nonNull(cgmGraphWidget.getCgmGraphData()) ? cgmGraphWidget.getCgmGraphData().getCurrentReadingMode(): ReadingMode.APP);
            } else {
                ReadingModeResponse readingModeResponse = getLatestReadingModeForUser(userContext);
                userCGMConfiguration.setCgmReadingMode(readingModeResponse == null ? null : readingModeResponse.getReadingMode());
            }

            if (allowCGMActivation) {
                userCGMConfiguration.setSelfInstallationCardTitle("Ready to install your next CGM?");
               if(Objects.nonNull(cgmOnboardingStatus)) {
                   List<DeviceModel> availableCgmDevices = cgmOnboardingStatus.getPendingDevices();
                   Action watchVideoAction = chronicCareServiceHelper.getCgmWatchVideoAction(availableCgmDevices);
                   userCGMConfiguration.setSelfInstallationWatchVideoAction(watchVideoAction);
               }
            } else if (allowConsultationSlotBooking) {
                boolean isNfcUser = chronicCareServiceHelper.getUserNfcStatus(userContext);
                userCGMConfiguration.setSelfInstallationCardTitle("Ready to install your next CGM?");
                try {
                    String phleboConsultProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
                    userCGMConfiguration.setSelfInstallationWatchVideoAction(chronicCareServiceHelper.getBookCGMInstallationAction(userContext,phleboConsultProductId));
                } catch (Exception e) {
                    //Ignore
                }
            } else if (!cgmDeviceStatus.getIsOnGoingDevice() && chronicCareServiceHelper.isQuickCommerceCGMPack(Objects.nonNull(activePackResponse) ? activePackResponse.getBundleProduct() : null)) {
                userCGMConfiguration.setSelfInstallationCardTitle("Do you have a CGM and want to start using it?");
                Action cgmAddAction = Action.builder().actionType(ActionType.SF_ADD_A_CGM_TO_ACCOUNT).title("Yes, Let's do now").build();
                userCGMConfiguration.setSelfInstallationWatchVideoAction(cgmAddAction);
            }

            if(activePackResponse != null && cgmGraphWidget != null) {
                userCGMConfiguration.setSfCgmDeviceId(cgmGraphWidget.getCgmDeviceId());
                boolean isFirstCoachConsultNotBooked = onboardingActions.getCoachCardActionWithContext().getAction().isActionPermitted();
                if(isFirstCoachConsultNotBooked) {
                    onboardingActions.getCoachCardActionWithContext().getAction();
                    cgmGraphWidget.setShowCGMInsightsLocked(true);
                    cgmGraphWidget.setCgmInsightsTitle("Book coach consult to unlock");
                    if (assignedCareTeam.getCoach() != null && assignedCareTeam.getCoach().getAgentResponse() != null && assignedCareTeam.getCoach().getAgentResponse().getId() != null) {
                        try {
                            String consultationProduct = onboardingActions.getCoachCardActionWithContext().getContext()
                                    .getProductCodes().stream().findFirst().get();

                            Long centerId = assignedCareTeam.getCoach().getAgentResponse().getAgentCenterMapping().stream().findFirst().get()
                                    .getCenterId();
                            cgmGraphWidget.setCoachId(String.valueOf(assignedCareTeam.getCoach().getAgentResponse().getId()));
                            Action action = Action.builder()
                                    .isEnabled(true)
                                    .title("Book Coach Consultation")
                                    .actionType(ActionType.NAVIGATION)
                                    .url(getAgentDatePickerUrl(consultationProduct, centerId, assignedCareTeam.getCoach().getAgentResponse().getId(), activePackResponse))
                                    .build();
                            cgmGraphWidget.setCgmInsightsPrimaryAction(action);
                        } catch (Exception exec) {
                            String message = "CGM Insights Locked, Exception: ";
                            log.error(message+exec.getMessage());
                            exceptionReportingService.reportException(message, exec);
                        }
                    }
                } else {
                    cgmGraphWidget.setShowCGMInsightsLocked(false);
                }
            }

            userCGMConfiguration.setNewSensorActivationAllowed(cgmOnboardingStatus.isNewSensorActivationAllowed());
            userCGMConfiguration.setEnableActivation(cgmOnboardingStatus.isEnableActivation());
            userCGMConfiguration.setDeviceModelEnableActivation(cgmOnboardingStatus.getDeviceModelEnableActivation());
            userCGMConfiguration.setDeviceModelNewSensorActivationAllowed(cgmOnboardingStatus.getDeviceModelNewSensorActivationAllowed());
            List<CGMDeviceInfo> cgmDeviceInfos = cgmOnboardingStatus.getCgmDeviceInfos();
            if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
                cgmDeviceInfos.forEach(cgmDeviceInfo -> {
                    if (cgmDeviceInfo.getDeviceId().equals(cgmDeviceStatus.getCgmDeviceId())) {
                        userCGMConfiguration.setCgmDeviceModel(cgmDeviceInfo.getDeviceModel());
                        if (cgmDeviceInfo.isOngoing()) {
                            userCGMConfiguration.setLastScannedHexIndex(cgmDeviceInfo.getLastScannedHexIndex());
                            userCGMConfiguration.setCgmDeviceHardwareId(getModifiedHardwareIdToSendToApp(userContext, cgmDeviceInfo.getHardwareId(), cgmDeviceInfo.getDeviceModel()));
                        }
                    }
                });
            }

            boolean appScanningEnabled = cgmOnboardingStatus.isEnableInAppScanning() && isInAppScanningEnabled(userContext) && cgmDeviceStatus.getIsOnGoingDevice();
            DeviceDetailEntry device = null;
            NfcLocationConfigEntry nfcLocationConfigEntry = null;
            userCGMConfiguration.setShowScanButton(appScanningEnabled);
            if (appScanningEnabled || cgmActivationAllowed(userContext, cgmOnboardingStatus)) {
                try {
                    device = deviceService.getDeviceByDeviceId(userContext.getSessionInfo().getDeviceId(),
                            AppUtil.getAppTenantFromUserContext(userContext)).get();
                    if (Objects.nonNull(device)) {
                        nfcLocationConfigEntry = chsClient.fetchNfcLocation(device.getOsName(), device.getBrand(), device.getDeviceModel());
                    }
                } catch (Exception e) {
                    exceptionReportingService.reportException(e);
                }
                if (!Objects.isNull(nfcLocationConfigEntry)) {
                    userCGMConfiguration.setNfcLocationVideoUrl(nfcLocationConfigEntry.getLocationUri());
                }
                userCGMConfiguration.setDeviceNFCLocationInfo(getDeviceNFCLocationInfo(userContext, device));
            }

            if (appScanningEnabled && onboardingActions.getAGMDataActionWithContext().getAction().isShowAGMReport() && onboardingActions.getAGMDataActionWithContext().getContext().isShowScanReadingPrompt()) {
                String redisKey = "CGM_INAPP_READING_PROMPT_" + userContext.getUserProfile().getUserId();
                String redisValue = this.cfApiRedisKeyValueStore.get(redisKey);
                if (redisValue == null || redisValue.isEmpty()) {
                    SfUserCGMConfig.ReadingAlert readingAlert = new SfUserCGMConfig.ReadingAlert();
                    readingAlert.setShowScanReadingPrompt(true);
                    userCGMConfiguration.setReadingAlert(readingAlert);
                    this.cfApiRedisKeyValueStore.set(redisKey, "TRUE", SIX_HOURS_IN_SECONDS);
                }
            }

            boolean status = false;
            SfUserCGMConfig.DisclaimerAlert disclaimerAlert = new SfUserCGMConfig.DisclaimerAlert();
            if (cgmGraphWidget != null) {
                if (!isNonCGMProduct && !isUltraFitApp(userContext)) {
                    status = !(Optional.ofNullable(cgmGraphWidget.getCgmGraphData().getDisclaimerAccepted()).orElse(true));
                }
            }
            disclaimerAlert.setShowDisclaimerAlertPrompt(status);
            userCGMConfiguration.setDisclaimerAlert(disclaimerAlert);

            if (cgmConfigVersion == null || cgmConfigVersion < userCGMConfiguration.getVersion()) {
                userCGMConfiguration.setCgmReaderModalConfig(new SfUserCGMConfig.CgmReaderModalConfig(userContext));
                userCGMConfiguration.setGenericNFCLocationInfo(new SfUserCGMConfig.GenericNFCLocationInfo());
                userCGMConfiguration.setVoiceCommandEnabled(true);
                userCGMConfiguration.setScanModeText("It's time to scan your CGM sensor to get real time glucose readings");
                userCGMConfiguration.setScanButtonText("SCAN SENSOR");
                userCGMConfiguration.setActivationModeText("Once installed, scan your sensor to activate it.");
                userCGMConfiguration.setActivationButtonText("ACTIVATE SENSOR");
                userCGMConfiguration.setInitAppModeInNFCDeviceText("Your sensor has been activated. You can scan your sensor soon");
                userCGMConfiguration.setInitAppModeInNonNFCDeviceText("Once your sensor has been installed, you should use NFC enabled device to take reading.");
                userCGMConfiguration.setInitPhleboModeText("Phlebo will do the reading for you");
                userCGMConfiguration.setReadingTimeout(40000);
                userCGMConfiguration.setScanRetryCount(5);
            }

            result.setCgmConfiguration(userCGMConfiguration);
        } catch (Exception e) {
            log.error("Error in adding cgm graph widget", e);
            exceptionReportingService.reportException("Error in adding cgm graph widget", e);
        }
    }

    private ScanSensorCardWidget buildSensorCardWidgetForCgmGraph(UserContext userContext, boolean allowCGMActivation,
                                                                  boolean allowConsultationSlotBooking,
                                                                  SfCgmDeviceStatus cgmDeviceStatus,
                                                                  CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                                  CgmUserRequestStatus cgmUserRequestStatus,
                                                                  UserOnboardingActionWithContext onboardingActions,
                                                                  Long patientId, ActivePackResponse activePackResponse) {
        ScanSensorCardWidget scanSensorCardWidget = new ScanSensorCardWidget();
        scanSensorCardWidget.setAllowCGMActivation(allowCGMActivation || allowConsultationSlotBooking);
        scanSensorCardWidget.setAllowConsultationSlotBooking(allowConsultationSlotBooking);
        if (cgmActivationAllowed(userContext, cgmOnboardingStatus)) {
            scanSensorCardWidget.setOpsRequestAllowed(cgmUserRequestStatus != null && cgmUserRequestStatus.isEnableInstallCgmCta());
            scanSensorCardWidget.setCardDescription("Once installed, scan your sensor to activate it.");
            scanSensorCardWidget.setCgmActionButtonTitle("ACTIVATE SENSOR");
        } else {
            scanSensorCardWidget.setCardDescription("It's time to scan your CGM sensor to get real time glucose readings");
            scanSensorCardWidget.setCgmActionButtonTitle("SCAN SENSOR");
        }

        if (allowCGMActivation || allowConsultationSlotBooking) {
            List<Action> actions = new ArrayList<>();
            if (allowCGMActivation) {
                if(Objects.nonNull(cgmOnboardingStatus)){
                    Map<String, Object> meta = new HashMap<>();
                    meta.put("primary", true);
                    List<DeviceModel> availableCgmDevices = cgmOnboardingStatus.getPendingDevices();
                    Action watchVideoAction = chronicCareServiceHelper.getCgmWatchVideoAction(availableCgmDevices);
                    Map<String, Object> givenMeta = (Map<String, Object>) watchVideoAction.getMeta();
                    if (givenMeta == null) {
                        givenMeta = new HashMap<>();
                    }
                    givenMeta.putAll(meta);
                    watchVideoAction.setMeta(givenMeta);
                    actions.add(watchVideoAction);
                }
            }
            if (allowConsultationSlotBooking) {
                Map<String, Object> meta = new HashMap<>();
                meta.put("primary", true);
                try {
                    String phleboConsultProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
                    Action bookAction = chronicCareServiceHelper.getBookCGMInstallationAction(userContext,phleboConsultProductId);
                    bookAction.setMeta(meta);
                    actions.add(bookAction);
                } catch (Exception e) {
                   exceptionReportingService.reportException(e);
                }
            }

            Map<String, Object> cgmActionMeta = new HashMap<>();
            cgmActionMeta.put("isCgmAction", true);
            actions.add(Action.builder().title(scanSensorCardWidget.getCgmActionButtonTitle()).meta(cgmActionMeta).build());
            scanSensorCardWidget.setActions(actions);
        } else if (!cgmDeviceStatus.getIsOnGoingDevice() && chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct())) {
            scanSensorCardWidget.setCardDescription("Do you have a CGM and want to start using it?");
            Map<String, Object> meta = new HashMap<>();
            meta.put("primary", true);
            Action cgmAddAction = Action.builder().actionType(ActionType.SF_ADD_A_CGM_TO_ACCOUNT).title("Yes, Let's do now").meta(meta).build();
            scanSensorCardWidget.setActions(List.of(cgmAddAction));
        }
        return scanSensorCardWidget;
    }

    private ReadingModeResponse getLatestReadingModeForUser(UserContext userContext) {
        try {
            return chsClient.fetchCgmReadingMode(Long.valueOf(userContext.getUserProfile().getUserId()), AppUtil.getAppTenantFromUserContext(userContext));
        } catch (Exception e) {
            String message = String.format("Error in fetching latest reading mode for user :: %s", e.getMessage());
            log.error(message, e);
            return null;
        }
    }

    private SfUserCGMConfig.DeviceNFCLocationInfo getDeviceNFCLocationInfo(UserContext userContext,  DeviceDetailEntry device) {
        SfUserCGMConfig.DeviceNFCLocationInfo deviceNFCLocationInfo = new SfUserCGMConfig.DeviceNFCLocationInfo();
        String userId = userContext.getUserProfile().getUserId();
        String deviceId = userContext.getSessionInfo().getDeviceId();
        try {
            if (StringUtils.isEmpty(deviceId)) {
                return deviceNFCLocationInfo;
            }
            if (null == device) {
                log.error("Device info is null for userId: {}", userId);
                return deviceNFCLocationInfo;
            }
//            List<DeviceNfcLocationInfo> infos = serviceInterfaces.getSfAlbusClient().getDeviceNFCLocationInfo(device.getBrand(), device.getDeviceModel());
//            if (!CollectionUtils.isEmpty(infos)) {
//                deviceNFCLocationInfo.setImage(infos.get(0).getS3Url());
//            }
            deviceNFCLocationInfo.setImage("");
        } catch (Exception e) {
            log.error("Error while fetching deviceNFCInfo for userId {}: {}", userId, e.getMessage());
        }
        return deviceNFCLocationInfo;
    }

    private CompletableFuture<SfCoachCommunitySessionWidget> getCommunityWebinarWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> getUpcomingCoachCommunitySessionWidget(userContext), serviceInterfaces.getTaskExecutor());
    }

    public SfCoachCommunitySessionWidget getUpcomingCoachCommunitySessionWidget(UserContext userContext) {

        if (ChronicCareAppUtil.isSugarfitDocPlusUser(userContext)) {
            return null;
        }

        SfCoachCommunitySessionWidget widget = new SfCoachCommunitySessionWidget();
        int DEFAULT_LIVE_WINDOW_IN_MINUTES = 60;
        try {
            NowLiveWidgetView widgetView = new NowLiveWidgetView();
            widgetView.setProductType(ProductType.LIVE_VIDEO_CALL);
            widgetView.setTitle("Events and Talk");

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("isCoachSession", "true");
            queryParams.put("pageId", "LiveWorkout");
            WidgetContext widgetContext = new WidgetContext();
            widgetContext.setQueryParams(queryParams);
            List<BaseWidget> widgets = widgetView.buildView(serviceInterfaces, userContext, widgetContext);
            HashMap<String, String> config = appConfigCache.getConfig("SF_COACH_COMMUNITY_SESSION_CONFIG", new TypeReference<>() {
            }, new HashMap<>());
            int live_window_in_minute = config.get("live_window_in_minute") == null ? DEFAULT_LIVE_WINDOW_IN_MINUTES : Integer.parseInt(config.get("live_window_in_minute"));
            long live_window_in_millis = (long) live_window_in_minute * 60 * 1000;
            widget.setLiveWindowInMillis(live_window_in_millis);
            if (widgets != null && widgets.size() > 0) {
                NowLiveWidgetView nowLiveWidget = (NowLiveWidgetView) widgets.get(0);
                List<NowLiveSessionWidget> sessions = nowLiveWidget.getWidgets();
                sessions.sort(Comparator.comparingLong(NowLiveSessionWidget::getScheduleEpochTime));

                NowLiveSessionWidget upcomingSession = sessions.get(0);
                boolean isToday = DateUtils.isSameDay(new Date(), new Date(upcomingSession.getScheduleEpochTime()));
                boolean isLive = upcomingSession.getScheduleEpochTime() < new Date().getTime() + live_window_in_millis;
                widget.setEventDetails(upcomingSession);
                widget.setBuildTime(new Date().getTime());
                widget.setWidgetTitle(isToday ? "Live Today" : "Upcoming Session");
                widget.setLive(isLive);
                return widget;
            }
        } catch (Exception e) {
            String message = String.format("Error in fetching webinar widget, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        return null;
    }

    private CompletableFuture<SfCgmInstallationWebinarWidget> getCgmInstallationWebinarWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> getCgmInstallationWebinarWidget(userContext), serviceInterfaces.getTaskExecutor());
    }

    public SfCgmInstallationWebinarWidget getCgmInstallationWebinarWidget(UserContext userContext) {
        SfCgmInstallationWebinarWidget widget = new SfCgmInstallationWebinarWidget();
        try {
            String userId = userContext.getUserProfile().getUserId();
            Tenant tenant = Tenant.fromString(AppUtil.getTenantFromUserContext(userContext).toString());
            String countryId = userContext.getUserProfile().getCity().getCountryId();
            DIYFilterRequestV2 diyFilterRequestV2 = DIYFilterRequestV2.builder()
                    .preferredStreamType(PreferredStreamType.VIDEO_CALL).build();
            List<LiveClass> liveClasses = serviceInterfaces.getDiyfsService().getUpcomingClassesByFilters(50, 0, tenant, diyFilterRequestV2, userId, countryId).get();
            List<LiveClass> bookedCgmLiveClasses = new ArrayList<>(liveClasses.parallelStream().filter(liveClass -> {
                if (liveClass.getSlots().getFirst().getSubscriptionStatus() == SubscriptionStatus.SUBSCRIBED) {
                    return isCgmInstallationWebinar(liveClass);
                }
                return false;
            }).toList());

            HashMap<String, Map<String, String>> config = appConfigCache.getConfig("SF_UPCOMING_WEBINAR_CONFIG", new TypeReference<>() {}, new HashMap<>());
            int liveWindowInMinute = Integer.parseInt(resolveNullable(() -> config.get("cgm_installation").get("live_window_in_minute")).orElse("60"));
            int joinWindowFromEnd = Integer.parseInt(resolveNullable(() -> config.get("cgm_installation").get("join_window_from_end")).orElse("30"));
            long liveWindowInMillis = (long) liveWindowInMinute * 60 * 1000;
            long joinWindowInMillis = (long) joinWindowFromEnd * 60 * 1000;

            if (CollectionUtils.isNotEmpty(bookedCgmLiveClasses)) {
                bookedCgmLiveClasses.sort(Comparator.comparingLong(LiveClass::getScheduledTimeEpoch));
                LiveClass upcomingSession = bookedCgmLiveClasses.getFirst();

                long currentTime = Calendar.getInstance(getUserTimezone(userContext)).getTimeInMillis();
                boolean isLive = upcomingSession.getScheduledTimeEpoch() < currentTime + liveWindowInMillis;

                // Card visible till few minutes before end time
                if (currentTime > (upcomingSession.getScheduledTimeEpoch() + upcomingSession.getDuration() - joinWindowInMillis)) {
                    return null;
                }

                Action sessionAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(upcomingSession.getLiveClassId(), upcomingSession.getSlots().get(0).getClassId(), "now_live_widget", "View Details", userContext, upcomingSession.getContentCategory());
                Action joinAction = LiveUtil.getLiveInteractiveSessionRestApiAction(userContext, upcomingSession.getLiveClassId());
                joinAction.setIsEnabled(true);
                joinAction.setTitle("Join Session");

                widget.setAction(isLive ? joinAction : sessionAction);
                widget.setScheduleEpochTime(upcomingSession.getScheduledTimeEpoch());
                widget.setDurationEpoch(upcomingSession.getDuration());
                widget.setTitle(upcomingSession.getTitle());
                widget.setImage(LiveUtil.getImage(upcomingSession.getBannerImages(), userContext.getSessionInfo(), 1L));
                widget.setWidgetTitle(isLive ? "Live now" : "Upcoming CGM installation");
                widget.setLive(isLive);
                widget.setShowImage(true);
                widget.setBooked(true);
                return widget;
            }
        } catch (Exception e) {
            String message = String.format("Error in fetching cgm installation webinar widget, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        return null;
    }

    private CompletableFuture<BaseWidgetNonVM> getInterventionsWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return this.getInterventionsWidget(userContext);
            } catch (Exception e) {
                log.error("Exception in get interventions widget :: {}", e.getMessage(), e);
                exceptionReportingService.reportException(e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private InterventionCardWidget getInterventionsWidget(UserContext userContext) throws Exception {
        InterventionCardWidget interventionCardWidget = new InterventionCardWidget();

        String userId = userContext.getUserProfile().getUserId();
        String userTimeZone = userContext.getUserProfile().getTimezone();
        List<InterventionDetails> interventionDetailsList = new ArrayList<>();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateFormat.setTimeZone(getUserTimezone(userContext));
        Date startDate = TimeUtil.getStartOfDay(Calendar.getInstance().getTime(), userTimeZone);
        List<CompletableFuture<List<UserActivityEntry>>> userActivitiesByDateFutures = new ArrayList<>();
        for (int i = INTERVENTION_WIDGET_DATE_WINDOW - 1; i >= 0; i--) {
            Date loopDate = DateUtils.addDays(startDate, -1 * i);
            userActivitiesByDateFutures.add(chronicCareServiceHelper.getUserActivitiesFromShifuFuture(userContext, userId, loopDate, userTimeZone));
        }
        int j = 0;
        for (int i = INTERVENTION_WIDGET_DATE_WINDOW - 1; i >= 0; i--) {
            Date loopDate = DateUtils.addDays(startDate, -1 * i);
            String loopDateString = dateFormat.format(loopDate);
            try {
                List<InterventionDetails.UserActivityWrapper> userActivityWrapperList = new ArrayList<>();
                List<UserActivityEntry> activityEntryList = new ArrayList<>();
                try {
                    activityEntryList = userActivitiesByDateFutures.get(j).get();
                } catch (InterruptedException e) {
                    log.error(String.format("Error in fetching interventions for date :: %s", loopDateString), e);
                }
                for (UserActivityEntry userActivityEntry : activityEntryList) {
                    InterventionDetails.UserActivity userActivity = mapUserInterventionActivity(userActivityEntry);
                    InterventionDetails.UserActivityWrapper userActivityWrapper = InterventionDetails.UserActivityWrapper.builder().userActivityEntry(userActivity).workoutInfo(getWorkoutInfo(userActivityEntry)).build();
                    userActivityWrapperList.add(userActivityWrapper);
                }
                if (CollectionUtils.isNotEmpty(userActivityWrapperList)) {
                    interventionDetailsList.add(InterventionDetails.builder().date(loopDateString).activityList(userActivityWrapperList).build());
                }
            } catch (Exception e) {
                log.error(String.format("Error in fetching interventions for date :: %s", loopDateString), e);
                exceptionReportingService.reportException(String.format("Error in fetching interventions for date :: %s", loopDateString), e);
            }
            j++;
        }
        if (CollectionUtils.isNotEmpty(interventionDetailsList)) {
            interventionCardWidget.setTitle("Activities to Do");
            interventionCardWidget.setChevronAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://goalsprogress?pageKey=HABITS").title("Goals Page").build());
            interventionCardWidget.setData(interventionDetailsList);
            interventionCardWidget.setRenderingInfo(INTERVENTION_CARD_RENDERING_INFO);
            return interventionCardWidget;
        } else {
            return null;
        }
    }

    private HabitCard.WorkoutInfo getWorkoutInfo(UserActivityEntry userActivityEntry) {
        if (HabitType.LIVE_CLASS.equals(userActivityEntry.getType())) {
            Action action = new Action("curefit://liveclassdetail?liveClassId=" + userActivityEntry.getResource().getLiveClassProductId() + "&bookingNumber=" + userActivityEntry.getResource().getLiveClassProductId() + "&productType=LIVE_FITNESS&isDIY=true", ActionType.NAVIGATION);
            List<DIYProduct> products = diyfsService.getDIYFitnessProductsByProductIds(userActivityEntry.getUserId().toString(), Collections.singletonList(userActivityEntry.getResource().getLiveClassProductId()), DIYFS_TENANT);
            if (products.size() == 1) {
                return new HabitCard.WorkoutInfo(products.get(0).getSubTitle(), products.get(0).getDuration() / 60000 + " min", products.get(0).getImageDetails().getThumbnailImage(), action, false);
            }
        }
        return null;
    }

    private CompletableFuture<BaseWidgetNonVM> getActivityCompareWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
                TimeZone timeZone = getUserTimezone(userContext);
                boolean showCompareWidget = sfLoggingClient.areAnyTwoActivitiesComparable(userId, appTenant, timeZone);
                if (showCompareWidget) {
                    SfActivityCompareCardWidget sfActivityCompareCardWidget = new SfActivityCompareCardWidget();
                    Action action = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://impactcomparison").build();
                    sfActivityCompareCardWidget.setAction(action);
                    return sfActivityCompareCardWidget;
                }
            } catch (Exception e) {
                log.error("Exception in getActivityCompareWidgetFuture", e);
                exceptionReportingService.reportException("Exception in getActivityCompareWidgetFuture", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<BaseWidgetNonVM> getWellnessAtGlanceWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getWellnessAtGlanceWidget(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getWellnessAtGlanceWidgetFuture", e);
                log.error("Error in getting wellness at glance widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public SfWellnessAtGlanceWidget getWellnessAtGlanceWidget(UserContext userContext) throws ExecutionException, InterruptedException {
        SfWellnessAtGlanceWidget wellnessAtGlanceWidget = new SfWellnessAtGlanceWidget();

        CompletableFuture<FitnessDeviceSyncMeta> fitnessDeviceSyncMetaFuture = chronicCareServiceHelper.getFitnessDeviceSyncMetaFuture(userContext);
        CompletableFuture<List<WellnessDataPerDay>> wellnessGlanceDataFuture = getWellnessGlanceDataFuture(userContext);
        CompletableFuture<SfUserBadgeSummary> userBadgeSummaryFuture = null;

        if (ChronicCareAppUtil.isBadgesEnabled(userContext, chronicCareServiceHelper)) {
            userBadgeSummaryFuture = getBadgesSummaryFuture(userContext, fitnessDeviceSyncMetaFuture);
        }

        boolean showConnectWithCult = isUltraFitApp(userContext) && getShowSyncWithCultFitness(userContext);
        wellnessAtGlanceWidget.setShowConnectWithCult(showConnectWithCult);

        if (userBadgeSummaryFuture != null) {
            wellnessAtGlanceWidget.setBadgeSummary(userBadgeSummaryFuture.get());
        }

        FitnessDeviceSyncMeta deviceSyncMeta = fitnessDeviceSyncMetaFuture.get();
        wellnessAtGlanceWidget.setActiveDeviceId(deviceSyncMeta.getActiveDeviceId());
        if (deviceSyncMeta.getLastSyncedTime() != null) {
            wellnessAtGlanceWidget.setLastSyncedTime(deviceSyncMeta.getLastSyncedTime());
        }
        if (deviceSyncMeta.getLastSyncedDisplayTime() != null) {
            wellnessAtGlanceWidget.setLastSyncedDisplayTime(deviceSyncMeta.getLastSyncedDisplayTime());
        }
        wellnessAtGlanceWidget.setData(wellnessGlanceDataFuture.get());
        wellnessAtGlanceWidget.setTitleAction(Action.builder().title("PROGRESS").actionType(ActionType.NAVIGATION).url("curefit://goalsprogress").build());

        return wellnessAtGlanceWidget;
    }

    private CompletableFuture<List<WellnessDataPerDay>> getWellnessGlanceDataFuture(UserContext userContext) {
        return supplyAsync(() -> {
            List<WellnessDataPerDay> wellnessData = new ArrayList<>();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            String userTimeZone = userContext.getUserProfile().getTimezone();
            Date startDate = TimeUtil.getEndOfDay(Calendar.getInstance().getTime(), userTimeZone);

            List<WellnessAtGlanceItem> wellnessAtGlanceItems = new ArrayList<>();
            WellnessDataPerDay wellnessDataPerDay = new WellnessDataPerDay();
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            dateFormat.setTimeZone(getUserTimezone(userContext));
            String loopDateString = dateFormat.format(startDate);
            try {
                List<GoalWithData> goalWithLatestData = chronicCareServiceHelper.getShifuRecentGoals(userContext, userId, startDate, true, userTimeZone);
                for (String goalName : isUltraFitApp(userContext) ? WELLNESS_GOALS_UF : WELLNESS_GOALS) {
                    GoalWithData goal = goalWithLatestData.stream().filter(goalWithData -> goalWithData.getGoal().getMeta().getUniqueKey().equals(goalName)).findAny().orElse(null);

                    if (goal != null) {
                        WellnessAtGlanceItem item = new WellnessAtGlanceItem();

                        if (goal.getMetricValuePojo() != null && goal.getMetricValuePojo().getValue() != null) {
                            item.setCurrentValue(getFormattedRecentValue(goal.getMetricValuePojo().getValue(), goal.getGoal().getMeta().getUniqueKey()));
                        }
                        if (goal.getTarget() != null) {
                            item.setGoalValue(getTargetValue(goal.getTarget().getLow(), goal.getTarget().getHigh(), goal.getGoal().getMeta().getUniqueKey()));
                        }
                        item.setType(goal.getGoal().getMeta().getUniqueKey());
                        item.setDisplayName(goal.getGoal().getName());
                        item.setUnit(getWellnessGoalUnitByType(goalName));
                        item.setBackgroundColor(getWellnessCardColor(goalName));
                        item.setGoalTextColor(isUltraFitApp(userContext) ? getUFWellnessCardTextColor(goalName) : getWellnessCardTextColor(goalName));
                        item.setLogAction(getGoalsLogAction(goalName, userContext, chronicCareServiceHelper));

                        Action cardClickAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://goalsprogress?goalType=" + goal.getGoal().getMeta().getUniqueKey()).build();
                        if (goal.getGoal().getMeta().getUniqueKey().equals("WEIGHT")
                                && chronicCareServiceHelper.hasAtleastOneSmartScaleReading(userContext)) {
                            cardClickAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfweighingscalevitallistpage").build();
                        }
                        item.setCardClickAction(cardClickAction);

                        wellnessAtGlanceItems.add(item);
                    }
                }
                wellnessDataPerDay.setDate(loopDateString);
                wellnessDataPerDay.setWellnessAtGlanceCards(wellnessAtGlanceItems);
                wellnessData.add(wellnessDataPerDay);
                if (isUltraFitApp(userContext)) {
                    wellnessDataPerDay.setHomePageHeartRateData(getHeartRateGlanceData(userContext, atStartOfDay(startDate, getUserTimezone(userContext)), atEndOfDay(startDate, getUserTimezone(userContext))));
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in getting recent goals data ==>", e);
            }
            return wellnessData;
        }, serviceInterfaces.getTaskExecutor());
    }

    private boolean getShowSyncWithCultFitness(UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            Map<Boolean, Long> permission = sfFitnessClient.checkIfCultClassAccessPermissionSet(userId);

            if (permission.containsKey(false) && permission.get(false) != null && permission.get(false) != 0) {
                String cultUserId = String.valueOf(permission.get(false));
                return isSyncWithCultFitnessEnabled(cultUserId, segmentationCacheClient);
            } else {
                return false;
            }
        } catch (Throwable e) {
            exceptionReportingService.reportException("Failed fetching flag to show sync with cult fitness", e);
            return false;
        }
    }

    private CompletableFuture<SfUserBadgeSummary> getBadgesSummaryFuture(UserContext userContext, CompletableFuture<FitnessDeviceSyncMeta> fitnessDeviceSyncMetaFuture) {
        return supplyAsync(() -> {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);

            try {
                UserBadgeSummary summaryResponse = chsClient.filterUserBadgesSummary(userId, timeZone, appTenant);

                if (CollectionUtils.isNotEmpty(summaryResponse.getBadges())) {
                    SfUserBadgeSummary badgeSummary = new SfUserBadgeSummary();
                    AtomicBoolean firstLogPending = new AtomicBoolean(true);
                    List<Action> logActions = new ArrayList<>();

                    summaryResponse.getBadges().forEach(badge -> {
                        if (!(badge.getStatus().equals(UserBadgeProgressStatus.PENDING) && badge.getBadgeEntry().getOrder() == 0)) {
                            firstLogPending.set(false);
                        }

                        Action logAction = getLogActionFromBadgeCategory(badge.getBadgeEntry().getCategory(), fitnessDeviceSyncMetaFuture, userContext);
                        logAction.setTitle(getBadgeCategoryDisplayName(badge.getBadgeEntry().getCategory()));
                        logAction.setContentType(String.valueOf(badge.getBadgeEntry().getCategory()));
                        logActions.add(logAction);
                    });

                    badgeSummary.setBadges(summaryResponse.getBadges().stream().map(badge -> getBadgeEntry(badge, fitnessDeviceSyncMetaFuture, userContext)).toList());
                    badgeSummary.setFirstLogPending(firstLogPending.get());
                    badgeSummary.setLogActions(logActions);
                    return badgeSummary;
                }
            } catch (Exception e) {
                log.error("Error fetching badge summary for user :: {}. error :: {}", userId, e.getMessage(), e);
                exceptionReportingService.reportException("Error fetching badge summary for user", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfBadgeEntry getBadgeEntry(UserBadgeProgressEntry badge, CompletableFuture<FitnessDeviceSyncMeta> fitnessDeviceSyncMetaFuture, UserContext userContext) {
        BadgeCategory category = badge.getBadgeEntry().getCategory();
        BadgeEntry badgeEntry = badge.getBadgeEntry();

        Map<String, Object> badgeMetaData = badgeEntry.getMetadata();

        String formattedBadgeDescription = badge.getStatus().equals(UserBadgeProgressStatus.ACHIEVED) ? String.format("You earned this badge by meeting your goal of %s", badgeMetaData.get("title")) : String.format("You will earn this badge by meeting your goal of %s", badgeMetaData.get("title"));

        badgeMetaData.put("shareTitle", "I have achieved this Sugar.fit badge by daily logging activity!");
        badgeMetaData.put("badgeDescription", formattedBadgeDescription);

        return SfBadgeEntry.builder().id(badge.getId()).category(category)
                .categoryDisplayName(getBadgeCategoryDisplayName(category))
                .badgeTitle(String.valueOf(badge.getBadgeEntry().getMetadata().get("title")))
                .badgeProgressTitle(getBadgeProgressTitle(badge))
                .badgeProgressColor(getProgressColor(category))
                .status(badge.getStatus()).target(badgeEntry.getTarget())
                .targetType(badgeEntry.getTargetType())
                .currentStreak(badge.getCurrentStreak())
                .targetStreakInDays(badgeEntry.getTargetStreakInDays())
                .firstAchievedDate(badge.getFirstAchievedDate())
                .lastAchievedDate(badge.getLastAchievedDate())
                .badgeUrlFilled(String.valueOf(badge.getBadgeEntry().getMetadata().get("badgeImageFilled")))
                .badgeUrlUnfilled(String.valueOf(badge.getBadgeEntry().getMetadata().get("badgeImageUnfilled")))
                .badgeAnimationUrl(String.valueOf(badge.getBadgeEntry().getMetadata().get("badgeVideo")))
                .badgeMetaData(badgeMetaData).progressMetaData(badge.getMetadata())
                .logAction(getLogActionFromBadgeCategory(category, fitnessDeviceSyncMetaFuture, userContext))
                .build();
    }

    public WellnessDataPerDay.HomePageHeartRateData getHeartRateGlanceData(UserContext userContext, long startTime, long endTime) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String deviceId = userContext.getSessionInfo().getDeviceId();
        WellnessDataPerDay.HomePageHeartRateData heartRateData = new WellnessDataPerDay.HomePageHeartRateData();
        heartRateData.setColor("#F7BA41");

        Hashtable<Integer, String> xAxisHR = new Hashtable<>();
        xAxisHR.put(0, "12am");
        xAxisHR.put(60 * 6, "6am");
        xAxisHR.put(60 * 12, "12pm");
        xAxisHR.put(60 * 18, "6pm");
        xAxisHR.put(60 * 24 - 1, "12am");

        FitnessDataRequest req = new FitnessDataRequest();
        req.setStartTime(new Date(startTime));
        req.setEndTime(new Date(endTime));
        req.setUserId(userId);
        try {
            ActiveDeviceEntry activeDeviceEntry = this.chsClient.fetchActiveDevice(userId);
            if (activeDeviceEntry != null && activeDeviceEntry.getDeviceId() != null)
                deviceId = activeDeviceEntry.getDeviceId();
        } catch (Throwable e) {
            exceptionReportingService.reportException("Failed fetching active device id", e);
        }
        req.setDeviceId(deviceId);
        req.setGranularity(GranularityType.DAY);
        try {
            HeartRateDataResponse dataSet = this.chsClient.getHeartRateData(req);
            int[] yData = getFormattedHeartRate(dataSet, ChronicCareAppUtil.getUserTimezone(userContext));
            heartRateData.setHeartRatePoints(yData);
            heartRateData.setXaxisHR(xAxisHR);
            heartRateData.setHeartRate(dataSet);
        } catch (HttpException e) {
            log.info(e.getMessage());
            exceptionReportingService.reportException("Error in vital page heartrate ==>", e);
            heartRateData.setHeartRate(null);
        }
        return heartRateData;
    }

    public CompletableFuture<AssignedAgentWidget> getAssignedAgentsWidgetFuture(UserContext userContext, UserOnboardingActionWithContext onboardingActions,
                                                                                ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam,
                                                                                CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture,
                                                                                CompletableFuture<DiagnosticJourneyResponse> diagnosticJourneyResponseCompletableFuture) {        return supplyAsync(() -> {
            try {
                return getAssignedAgentsWidgets(userContext, onboardingActions, activePackResponse, assignedCareTeam, consultationBookingsFuture, diagnosticJourneyResponseCompletableFuture);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching assigned agent widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private AssignedAgentWidget getAssignedAgentsWidgets(UserContext userContext,
                                                         UserOnboardingActionWithContext onboardingActionWithContext,
                                                         ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam,
                                                         CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture,
                                                         CompletableFuture<DiagnosticJourneyResponse> diagnosticJourneyResponseCompletableFuture) throws OllivanderClientException, ExecutionException, InterruptedException, ResourceNotFoundException {
        AssignedAgentData doctorData = null, coachData = null, supportTeamData = null, diagnosticAgentData = null, psychologistData = null;
        if (assignedCareTeam == null) {
            return null;
        }
        if (chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(activePackResponse.getBundleProduct())) {
            return null;
        }
        if (assignedCareTeam.getCoach() != null) {
            String consultationProduct = "";
            if (Objects.nonNull(onboardingActionWithContext.getCoachCardActionWithContext().getContext().getProductCodes()) && onboardingActionWithContext.getCoachCardActionWithContext().getContext().getProductCodes().stream().findFirst().isPresent()) {
                consultationProduct = onboardingActionWithContext.getCoachCardActionWithContext().getContext().getProductCodes().stream().findFirst().get();
            }
            coachData = getAssignedAgentData(userContext, assignedCareTeam.getCoach(), consultationProduct, onboardingActionWithContext, activePackResponse, consultationBookingsFuture);
        }
        // Don't add null check for assignedCareTeam.getDoctor() because there is handling inside getAssignedAgentData for this
        String doctorConsultProduct = chronicCareServiceHelper.getDoctorConsultationProduct(activePackResponse.getBundleProduct());
        doctorData = getAssignedAgentData(userContext, assignedCareTeam.getDoctor(), doctorConsultProduct, onboardingActionWithContext, activePackResponse, consultationBookingsFuture);

        if(ChronicCareAppUtil.isHomePageRevampEnabled(userContext)) {
            Action supportAction = Action.builder().title("GET HELP").actionType(ActionType.NAVIGATION).url("curefit://sfsupportpage").build();
            supportTeamData = AssignedAgentData.builder().agentName("Support Team")
                    .agentImageUrl("image/chroniccare/customer_support_v1.png")
                    .agentType("SUPPORT").subtitle("")
                    .experience(null)
                    .action(supportAction).profileAction(supportAction).whatsappNumber(null).agentAssigned(true).build();
            if (Objects.nonNull(diagnosticJourneyResponseCompletableFuture) && Objects.nonNull(diagnosticJourneyResponseCompletableFuture.get()) && (!chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct()) && !chronicCareServiceHelper.isShopifyCGMPack(activePackResponse.getBundleProduct()))) {
                DiagnosticJourneyResponse diagnosticJourneyResponse = diagnosticJourneyResponseCompletableFuture.get();
                Action bookingAction = null;
                String subTitle = "";
                if (diagnosticJourneyResponse.isAllowBooking()) {
                    PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
                    UserPreferencePojo userAddressIdPreference = serviceInterfaces.sfAlbusClient.getUserPreference(userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "addressId");
                    String addressId = userAddressIdPreference.getPreferenceTypeValues().getFirst();
                    String diagnosticProductCode = diagnosticJourneyResponse.getProductCode();
                    String provider = diagnosticJourneyResponse.getPartner();
                    bookingAction = Action.builder().isEnabled(true)
                            .title("BOOK").actionType(ActionType.NAVIGATION)
                            .url("curefit://selectCareDateV1?patientId=" + patientDetail.getId()
                                    + "&productId=" + diagnosticProductCode + "&type=" + "DIAGNOSTICS"
                                    + "&category=AT_HOME_SLOT&nextAction=checkout&productCodes=" + diagnosticProductCode
                                    + "&addressId=" + addressId
                                    + "&parentBookingId=" + activePackResponse.getBookingId() + "&diagnosticsProvider=" + provider).build();
                } else {
                    if (Objects.nonNull(diagnosticJourneyResponse.getNextDiagnosticDate())) {
                        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("dd MMM yyyy");
                        String formattedDate = dateFormatStartTime.format(diagnosticJourneyResponse.getNextDiagnosticDate());
                        subTitle = "Next diagnostics will be enabled after " + formattedDate;
                    } else {
                        subTitle = "Next diagnostics will be enabled after reports are generated";
                    }
                }
                diagnosticAgentData = AssignedAgentData.builder().agentName("Diagnostic Tests")
                        .agentImageUrl("image/chroniccare/kickstart_diagnostics_v1.png")
                        .agentType("DIAGNOSTICS").subtitle(subTitle)
                        .experience(null)
                        .action(bookingAction).profileAction(null).whatsappNumber(null).agentAssigned(true).build();
            }

            try {
                Optional<String> image = resolveNullable(() -> {
                    try {
                        try {
                            AgentResponse psychologistAgent = serviceInterfaces.getOllivanderAgentClient().getAgent(SUGARFIT_PSYCHOLOGIST_AGENT_ID);
                            return psychologistAgent.getDisplayImage();
                        } catch (Exception e) {
                            return null;
                        }
                    } catch (Exception e) {
                        return null;
                    }
                });
                String imageUrl = image.orElse("/image/chroniccare/coach_female.png");
                Action psychologistBookingAction = chronicCareServiceHelper.getPyschologyConsultBookingAction(userContext);
                if(Objects.nonNull(psychologistBookingAction)) {
                    psychologistBookingAction.setTitle("BOOK");
                    psychologistData = AssignedAgentData.builder().agentName("Psychologist")
                            .agentImageUrl(imageUrl)
                            .agentType("PSYCHOLOGIST")
                            .experience(null)
                            .action(psychologistBookingAction).profileAction(null).whatsappNumber(null).agentAssigned(true).build();
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching psychologist booking action", e);
            }

            return new AssignedAgentWidget("Your ", "Care Team", doctorData, coachData, supportTeamData, diagnosticAgentData, psychologistData);
        }
        return new AssignedAgentWidget("My Care Team", doctorData, coachData, null);
    }

    private AssignedAgentData getAssignedAgentData(UserContext userContext, PatientPreferredAgentResponse agentResponse, String consultationProduct,
                                                   UserOnboardingActionWithContext onboardingActionWithContext, ActivePackResponse activePackResponse,
                                                   CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) throws OllivanderClientException {
        boolean allowBooking = true;
        String reasonForProhibition = "";
        String agentType = agentResponse == null ? "" : agentResponse.getDoctorType();
        String agentName = agentResponse == null ? "" : agentResponse.getAgentResponse().getName();
        String imageUrl = null;
        String careTeamSubtitle = null;
        boolean agentAssigned = true;
        if (agentType.equalsIgnoreCase("LIFESTYLE_COACH")) {
            agentType = "COACH";
        } else {
            agentName = getDoctorName(agentName);
            agentType = "DOCTOR";
            if (chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct())) {
                allowBooking = true;
            } else if (onboardingActionWithContext != null && !onboardingActionWithContext.getDoctorCardActionWithContext().getAction().isDoctorAssigned()) {
                allowBooking = false;
                reasonForProhibition = "Your best match doctor will be assigned based on your report";
                agentName = DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT;
                imageUrl = DOCTOR_ASSIGNMENT_PLACEHOLDER_URL;
                agentAssigned = false;
                careTeamSubtitle = "";
            } else if (onboardingActionWithContext != null && !onboardingActionWithContext.getDoctorCardActionWithContext().getAction().isFirstConsultDone()) {
                allowBooking = onboardingActionWithContext.getDoctorCardActionWithContext().getAction().isActionPermitted();
                reasonForProhibition = onboardingActionWithContext.getDoctorCardActionWithContext().getAction().getReasonForProhibition();
            }
        }
        Action action, profileAction;
        if (allowBooking && agentResponse != null) {
            Long agentId = agentResponse.getAgentResponse().getId();
            String agentActionTitle = SfHomePageUtil.getAgentActionTitle(consultationProduct, agentType, activePackResponse, userContext);
            Long centerId = getDoctorCenterIdForPaidUser(agentResponse);
            String profileUrl = String.format("curefit://chroniccaredoctorprofile?doctorId=%s&productId=%s", agentId.toString(), consultationProduct);
            profileAction = Action.builder().isEnabled(true).actionType(ActionType.NAVIGATION).url(profileUrl).build();
            if (agentType.equalsIgnoreCase("LIFESTYLE_COACH") || agentType.equalsIgnoreCase("COACH")) {
                action = Action.builder().isEnabled(true).title(agentActionTitle).actionType(ActionType.NAVIGATION).url(getAgentDatePickerUrl(consultationProduct, centerId, agentId, activePackResponse)).build();
            } else {
                Action doctorBookingAction = Action.builder().isEnabled(true).title(agentActionTitle).actionType(ActionType.NAVIGATION).url(getAgentDatePickerUrl(consultationProduct, centerId, agentId, activePackResponse)).build();
                action = chronicCareServiceHelper.getModifiedDoctorConsultActionBasedOnCgmCompletion(userContext, activePackResponse, doctorBookingAction);
                profileAction = chronicCareServiceHelper.getModifiedDoctorConsultActionBasedOnCgmCompletion(userContext, activePackResponse, profileAction);
            }
            Date lastMetDate = agentResponse.getLastMetTime();
            Date bookingDate = agentResponse.getFirstScheduledAppointment();
            if (lastMetDate != null) {
                DateFormat df = new SimpleDateFormat("MMM dd");
                df.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
                String lastMetDateString = df.format(lastMetDate);
                careTeamSubtitle = "Last met: " + lastMetDateString;
            } else if (bookingDate != null) {
                DateFormat df = new SimpleDateFormat("MMM dd");
                df.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
                String bookingDateString = df.format(bookingDate);
                careTeamSubtitle = "Booked for: " + bookingDateString;
            } else {
                careTeamSubtitle = "Book your first call";
            }
            Date upcomingConsultationDate = chronicCareServiceHelper.getUpcomingConsultationDateForDisablingMultipleBookings(userContext, consultationBookingsFuture, consultationProduct);
            if (upcomingConsultationDate != null) {
                action = getDisabledCoachOrDoctorBookingAction(userContext, upcomingConsultationDate, agentType.equalsIgnoreCase("COACH") ? "coach" : "doctor");
                profileAction = action;
            }
            if (chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
                try {
                    if (!masterClassClient.fetchCoachConsultationStatus(Long.valueOf(userContext.getUserProfile().getUserId()))) {
                        action = ChronicCareAppUtil.getDisabledCoachOrDoctorBookingActionForSpecialPackDelayed();
                        profileAction = action;
                    }
                } catch (Exception e) {
                    exceptionReportingService.reportException(e);
                }
            }
        } else {
            if (chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct()) || chronicCareServiceHelper.isShopifyCGMPack(activePackResponse.getBundleProduct())) {
                action = getDisabledActionForToastMessage(reasonForProhibition, "PAY & BOOK");
            } else {
                action = getDisabledActionForToastMessage(reasonForProhibition, "BOOK");
            }
            profileAction = getDisabledActionForToastMessage(reasonForProhibition, null);
            careTeamSubtitle = careTeamSubtitle == null ? reasonForProhibition : careTeamSubtitle;
        }
        Long experience = agentAssigned && agentResponse != null
                && agentResponse.getAgentResponse() != null
                && agentResponse.getAgentResponse().getExperience() != null ? agentResponse.getAgentResponse().getExperience() : 1L;
        return AssignedAgentData.builder().agentName(agentName)
                .agentImageUrl(agentAssigned && agentResponse != null ? getAgentImageFromResponse(Objects.requireNonNull(agentResponse).getAgentResponse()) : imageUrl)
                .agentType(agentType).subtitle(careTeamSubtitle)
                .experience(experience)
                .action(action).profileAction(profileAction).whatsappNumber(null).agentAssigned(agentAssigned).build();
    }

    private Long getDoctorCenterIdForPaidUser(PatientPreferredAgentResponse doctor) throws OllivanderClientException {
        if (doctor.getAgentResponse().getAgentCenterMapping().size() > 1) {
            List<CenterResponseV2> experienceCenters = this.serviceInterfaces.ollivanderCenterClient.getSugarfitExperienceCenters();
            Set<Long> experienceCenterIds;
            if (CollectionUtils.isNotEmpty(experienceCenters)) {
                experienceCenterIds = experienceCenters.stream().map(CenterBaseResponse::getId).collect(Collectors.toSet());
            } else {
                experienceCenterIds = new HashSet<>();
            }
            return doctor.getAgentResponse().getAgentCenterMapping().stream()
                    .filter(center -> !(experienceCenterIds.contains(center.getCenterId()) ||
                            DIGITAL_AGENT_CENTER_ID.equals(center.getCenterId())))
                    .map(AgentCenterMappingResponse::getCenterId)
                    .findFirst()
                    .orElse(null);
        } else {
            return doctor.getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
        }
    }

    public CompletableFuture<SfRenewalReportHomeWidget> getRenewalReportHomeWidgetFuture(UserContext userContext, CompletableFuture<RenewalJourneyResponse> renewalJourneyFuture) {
        return supplyAsync(() -> {
            try {
                if (isRenewalReportSupported(userContext)) {
                    return getRenewalReportHomeWidget(userContext, renewalJourneyFuture);
                }
                return null;
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching SfRenewalReportHomeWidget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfRenewalReportHomeWidget getRenewalReportHomeWidget(UserContext userContext, CompletableFuture<RenewalJourneyResponse> renewalJourneyFuture) throws ExecutionException, InterruptedException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        SfRenewalReportHomeWidget renewalReportWidget = new SfRenewalReportHomeWidget();

        RenewalJourneyResponse journeyResponse = renewalJourneyFuture.get();

        if (journeyResponse != null && journeyResponse.isReportCardEnabled()) {
            Optional<List<RenewalJourneyMetric>> renewalJourneyMetrics = resolveNullable(() -> journeyResponse.getRenewalReportCardEntry().getMetricData());

            if (renewalJourneyMetrics.isPresent() && CollectionUtils.isNotEmpty(renewalJourneyMetrics.get())) {
                List<SfReportMetric> metrics;
                List<RenewalJourneyMetric> metricsToShow = renewalJourneyMetrics.get().stream().limit(3).toList();
                metrics = metricsToShow.stream().map(metric -> {
                    try {
                        RenewalMetrics metricName = metric.getMetricName();
                        return new SfReportMetric(metricName,
                                getReportMetricDisplayName(metricName),
                                getFormattedReportMetricLatestValue(metric),
                                getFormattedReportMetricPreviousValue(metric),
                                getRenewalReportMetricUnit(metricName),
                                getRenewalReportMetricColor(metricName), metricName.equals(RenewalMetrics.HBA1C));
                    } catch (Exception e) {
                        log.error("Error in getting renewal report metric data", e);
                        return null;
                    }
                }).filter(Objects::nonNull).toList();

                renewalReportWidget.setSubTitle("You Have Made Incredible Progress");
                renewalReportWidget.setMetrics(metrics);
                return renewalReportWidget;
            }
        }

        return null;
    }

    private CompletableFuture<SfCoachCelebrationHomeWidget> getCoachCelebrationWidgetFuture(UserContext userContext,
                                                                                            CompletableFuture<RenewalJourneyResponse> renewalJourneyFuture,
                                                                                            CompletableFuture<ChronicCareTeam> assignedCareTeam) {
        return supplyAsync(() -> {
            try {
                return getCoachCelebrationWidget(userContext, renewalJourneyFuture, assignedCareTeam);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching CoachCelebrationWidget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfCoachCelebrationHomeWidget getCoachCelebrationWidget(UserContext userContext, CompletableFuture<RenewalJourneyResponse> renewalJourneyFuture, CompletableFuture<ChronicCareTeam> assignedCareTeam) throws ExecutionException, InterruptedException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        RenewalJourneyResponse renewalJourney = renewalJourneyFuture.get();

        if (renewalJourney != null && renewalJourney.isCoachCelebrationEnabled()) {
            SfCoachCelebrationHomeWidget coachCelebrationWidget = new SfCoachCelebrationHomeWidget();

            String actionUrl = "curefit://sfcoachcelebrationpage";
            Action action = Action.builder().actionType(ActionType.NAVIGATION).url(actionUrl).title("SEND THANK YOU CARD").build();

            coachCelebrationWidget.setTitle("THANK YOUR COACH");
            coachCelebrationWidget.setSubTitle("Your coach is always there for you!\nIt’s your turn to thank your coach");
            coachCelebrationWidget.setAction(action);

            Optional<String> image = resolveNullable(() -> {
                try {
                    return assignedCareTeam.get().getCoach().getAgentResponse().getDisplayImage();
                } catch (Exception e) {
                    return null;
                }
            });
            String coachImageUrl = image.orElse("/image/chroniccare/coach_female.png");
            coachCelebrationWidget.setCoachImageUrl(coachImageUrl);

            return coachCelebrationWidget;
        } else {
            return null;
        }
    }

    public CompletableFuture<SfBannerCarouselWidget> getSalesBannerWidgetFuture(UserContext userContext,
                                                                                 UserOnboardingActionWithContext onboardingAction,
                                                                                 CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                                                 RenewalJourneyResponse renewalJourney,
                                                                                 BundleProduct bundleProduct,
                                                                                 ActivePackResponse wellnessActivePackResponse) {
        return supplyAsync(() -> {
            try {
                return getSalesBannerWidgetV2(userContext, onboardingAction, cgmOnboardingStatusResponse, renewalJourney, bundleProduct, wellnessActivePackResponse);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfBannerCarouselWidget getDiscordBannerWidget() {
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();

        BannerItem bannerItem = new BannerItem();
        bannerItem.setImage("image/chroniccare/marketing/banners/home/<USER>/discord_banner.png");
        Action action = new Action("https://discord.gg/xsJ52TCmBT", "Navigate", ActionType.EXTERNAL_DEEP_LINK);
        bannerItem.setAction(action);

        bannerItemList.add(bannerItem);
        sfBannerCarouselWidget.setData(bannerItemList);
        sfBannerCarouselWidget.setLayoutProps(getDiscordBannerLayoutProps());
        return sfBannerCarouselWidget;
    }

    private SfBannerCarouselWidget getSpecialPackDelayedStartBanner() {
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        BannerItem bannerItem = new BannerItem();
        bannerItem.setImage("image/chroniccare/marketing/banners/home/<USER>/spl-pack-late-start-banner_v1.png");
        List<BannerItem> bannerItemList = List.of(bannerItem);
        sfBannerCarouselWidget.setData(bannerItemList);

        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", true);
        layoutProps.put("bannerHeight", 110);
        layoutProps.put("bannerWidth", 315);
        layoutProps.put("bannerOriginalHeight", 110);
        layoutProps.put("bannerOriginalWidth", 315);
        layoutProps.put("verticalPadding", 20);
        layoutProps.put("showPagination", false);
        layoutProps.put("autoScroll", false);
        sfBannerCarouselWidget.setLayoutProps(layoutProps);
        return sfBannerCarouselWidget;
    }

    SfBannerCarouselWidget getSalesBannerWidget(UserContext userContext, UserOnboardingActionWithContext onboardingAction,
                                                        CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                        RenewalJourneyResponse renewalJourney, BundleProduct bundleProduct,
                                                        ActivePackResponse wellnessActivePackResponse) throws IOException {
//        if (AppUtil.isSugarFitApp(userContext) && isSalesBannerCarouselEnabled(userContext, segmentationCacheClient)) {
//
//            HashMap<String, SfSalesBannerUrl> salesBannerConfig = appConfigCache.getConfig("SF_SALES_BANNER_CONFIG", new TypeReference<>() {
//            }, new HashMap<>());
//            if (salesBannerConfig != null) {
//                SfSalesBannerUrl homeBanners = salesBannerConfig.get("home");
//
//                if (homeBanners == null) return null;
//
//                SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
//                List<BannerItem> bannerItemList = new ArrayList<>();
//                MegaSaleData megaSaleData = chronicCareServiceHelper.getMegaSalesData(userContext);
//                if (renewalJourney != null && renewalJourney.getHomepageBanner() != null
//                        && isRenewalReportSupported(userContext)
//                        && !chronicCareServiceHelper.isSpecialSugarControlPlanPack(bundleProduct)) {
//                    String renewalBannerUrl = renewalJourney.getHomepageBanner().getAssetURL();
//                    String name = renewalJourney.getHomepageBanner().getName();
//                    String actionUrl = "curefit://renewsubscription?source=HOME_SALES_BANNER&bannerName=" + name;
//                    Map<String, String> eventData = new HashMap<>();
//                    eventData.put("bannerType", "Renewal");
//                    eventData.put("bannerName", name);
//                    Map<String, Object> analyticsData = new HashMap<>();
//                    analyticsData.put("eventKey", "widget_click");
//                    analyticsData.put("eventData", eventData);
//                    Action renewalPageAction = Action.builder().url(actionUrl).analyticsData(analyticsData).actionType(ActionType.NAVIGATION).title("RENEW SUBSCRIPTION").build();
//                    BannerItem renewalBannerItem = new BannerItem();
//                    renewalBannerItem.setImage(renewalBannerUrl);
//                    renewalBannerItem.setAction(renewalPageAction);
//                    bannerItemList.add(renewalBannerItem);
//                }
//
//                String firstBannerType = homeBanners.getMedium().getOrDefault("firstBannerType", "");
//
//                String sachinLaunchBannerKey = "sachinLaunch";
//                SfBannerCarouselWidget sachinBannerWidget = chronicCareServiceHelper.getEComBannerWidget(userContext, sachinLaunchBannerKey, false);
//                if (Objects.nonNull(sachinBannerWidget) && CollectionUtils.isNotEmpty(sachinBannerWidget.getData())) {
//                    BannerItem bannerItem = sachinBannerWidget.getData().get(0);
//                    bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url(ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext, segmentationCacheClient) ? "curefit://sfstorelp" : "curefit://sfecommerceplp").build());
//                    if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SACHIN")) {
//                        bannerItemList.add(0, bannerItem);
//                    } else {
//                        bannerItemList.add(bannerItem);
//                    }
//                }
//
//                if (isECommerceSupported(userContext, segmentationCacheClient)) {
//                    String ecommerceButtermilkSales = "attaLiquidate";
//                    SfBannerCarouselWidget buttermilkBannerWidget = chronicCareServiceHelper.getEComBannerWidget(userContext, ecommerceButtermilkSales, false);
//                    if (Objects.nonNull(buttermilkBannerWidget) && CollectionUtils.isNotEmpty(buttermilkBannerWidget.getData())) {
//                        BannerItem bannerItem = buttermilkBannerWidget.getData().get(0);
//                        bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfecommercepdp?productId=AATA_1_KG_PCK5").build());
//                        if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("ECOMMERCE_ATTA")) {
//                            bannerItemList.add(0, bannerItem);
//                        } else {
//                            bannerItemList.add(bannerItem);
//                        }
//                    }
//
//                    String ecommerceBhujiyaSales = "ecommerceBhujiyaSales";
//                    SfBannerCarouselWidget bhujiyaBannerWidget = chronicCareServiceHelper.getEComBannerWidget(userContext, ecommerceBhujiyaSales, false);
//                    if (Objects.nonNull(bhujiyaBannerWidget) && CollectionUtils.isNotEmpty(bhujiyaBannerWidget.getData())) {
//                        BannerItem bannerItem = bhujiyaBannerWidget.getData().get(0);
//                        bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfecommercepdp?productId=AATA_5_KG").build());
//                        if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("ECOMMERCE_BHUJIYA")) {
//                            bannerItemList.add(0, bannerItem);
//                        } else {
//                            bannerItemList.add(bannerItem);
//                        }
//                    }
//
//                    String ecomBannerUrl = Objects.nonNull(megaSaleData) ? "ecommerceMegaSales" :
//                            ChronicCareAppUtil.isEcommerceProductVariantsSupportedAppVersion(userContext)? "ecommerceSalesV2":
//                                    "ecommerceSales";
//                    SfBannerCarouselWidget eComBannerWidget = chronicCareServiceHelper.getEComBannerWidget(userContext, ecomBannerUrl, false);
//                    if (Objects.nonNull(eComBannerWidget) && CollectionUtils.isNotEmpty(eComBannerWidget.getData())) {
//                        if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("ECOMMERCE")) {
//                            bannerItemList.add(0, eComBannerWidget.getData().get(0));
//                        } else {
//                            bannerItemList.add(eComBannerWidget.getData().get(0));
//                        }
//                    }
//                }
//
//                if (isSmartScaleSellingSupportedAppVersion(userContext) && !chronicCareServiceHelper.isSmartScaleDelivered(userContext)) {
//                    String smartScaleBannerUrl = Objects.nonNull(megaSaleData) ? "smartScaleMegaSales" :
//                            "smartScaleSales";
//                    SfBannerCarouselWidget smartScaleWidget = chronicCareServiceHelper.getSmartScalePurchaseBannerWidget(userContext, smartScaleBannerUrl, false);
//                    if (Objects.nonNull(smartScaleWidget) && CollectionUtils.isNotEmpty(smartScaleWidget.getData())) {
//                        if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SMART_SCALE")) {
//                            bannerItemList.add(0, smartScaleWidget.getData().get(0));
//                        } else {
//                            bannerItemList.add(smartScaleWidget.getData().get(0));
//                        }
//                    }
//                }
//
//                if (onboardingAction == null || (onboardingAction.getPackRenewalContextActionWithContext().getAction().isActionPermitted() && !onboardingAction.getPackRenewalContextActionWithContext().getContext().getRenewed())) {
//                    if (isRenewalOfferRunning()) {
//                        String renewalBannerUrl = homeBanners.getMedium().get(Objects.nonNull(megaSaleData) ? "renewalMegaSales" : "renewal");
//                        Action renewalPageAction = Action.builder().url("curefit://renewsubscription").actionType(ActionType.NAVIGATION).title("RENEW SUBSCRIPTION").build();
//                        BannerItem renewalBannerItem = new BannerItem();
//                        renewalBannerItem.setImage(renewalBannerUrl);
//                        renewalBannerItem.setAction(renewalPageAction);
//                        bannerItemList.add(renewalBannerItem);
//                    }
//                } else {
//                    if (ChronicCareAppUtil.isDiaconUser(userContext, segmentationCacheClient)) {
//                        String diaconBannerImageUrl = homeBanners.getMedium().get("diacon");
//                        if (diaconBannerImageUrl != null) {
//                            BannerItem diaconBanner = new BannerItem();
//                            diaconBanner.setImage(diaconBannerImageUrl);
//                            bannerItemList.add(diaconBanner);
//                        }
//                    } else {
//                        if (isSalesCGMBannerEnabled(userContext, segmentationCacheClient, cgmOnboardingStatusResponse, bundleProduct)) {
//                            String cgmMediumBannerUrl = ChronicCareAppUtil.isBoosterCGMPackEnabledUser(userContext, segmentationCacheClient)
//                                    ? "/image/chroniccare/marketing/banners/home/<USER>/sales_cgm_booster_pack_v1.png"
//                                    : Objects.nonNull(megaSaleData)
//                                    ? homeBanners.getMedium().get("cgmMegaSales")
//                                    : homeBanners.getMedium().get("cgm");
//
//                            Action cgmRequestAction = new Action();
//                            cgmRequestAction.setActionType(ActionType.SF_OPS_REQUEST);
//                            cgmRequestAction.setTitle("RAISE A REQUEST");
//                            cgmRequestAction.setMeta(new SfOpsRequestMeta(REQUEST_NEW_CGM));
//                            Action showSalesBannerAction1 = Action.builder().title("BUY NEW CGM").url(ChronicCareServiceHelper.getCgmStorePageLink(userContext, segmentationCacheClient)).actionType(ActionType.NAVIGATION).build();
//                            BannerItem cgmBannerItem = new BannerItem();
//                            cgmBannerItem.setImage(cgmMediumBannerUrl);
//                            cgmBannerItem.setAction(showSalesBannerAction1);
//                            if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("CGM")) {
//                                bannerItemList.add(0, cgmBannerItem);
//                            } else {
//                                bannerItemList.add(cgmBannerItem);
//                            }
//                        }
//
//                        if (isReferralBottomBannerEnabledForUser(userContext, segmentationCacheClient)) {
//                            boolean isReferralChallengeBannerAdded = false;
//                            if (ChronicCareAppUtil.isReferralChallengeSupportedApp(userContext)) {
//                                String referralSupportedSegment = homeBanners.getMedium().get("referralChallengeSupportedSegment");
//                                String referralChallengeId = homeBanners.getMedium().get("referralChallengeId");
//                                if (StringUtils.isNotEmpty(referralSupportedSegment) && StringUtils.isNotEmpty(referralChallengeId)) {
//                                    if (ChronicCareAppUtil.isReferralChallengeSupportedSegment(userContext, segmentationCacheClient, referralSupportedSegment)) {
//                                        isReferralChallengeBannerAdded = true;
//                                        String referralMediumBannerUrl = homeBanners.getMedium().get("referralChallengeSales");
//                                        String actionUrl = "curefit://sfchallengedetailspage?challengeId=" + referralChallengeId + "&tabKey=ABOUT";
//                                        Action referralAction = Action.builder().title("REFER NOW").url(actionUrl).actionType(ActionType.NAVIGATION).build();
//                                        BannerItem referralBannerItem = new BannerItem();
//                                        referralBannerItem.setImage(referralMediumBannerUrl);
//                                        referralBannerItem.setAction(referralAction);
//
//                                        if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("REFERRAL_CHALLENGE")) {
//                                            bannerItemList.add(0, referralBannerItem);
//                                        } else {
//                                            bannerItemList.add(referralBannerItem);
//                                        }
//                                    }
//                                }
//                            }
//                            if (!isReferralChallengeBannerAdded) {
//                                String referralMediumBannerUrl = homeBanners.getMedium().get("referral");
//                                String referralMediumBannerUrl2k = homeBanners.getMedium().get("referral_2k");
//                                Action referralAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfreferralpage").build();
//                                BannerItem referralBannerItem = new BannerItem();
//                                referralBannerItem.setImage(ChronicCareAppUtil.isSugarfitPlusUser(userContext, segmentationCacheClient) ? referralMediumBannerUrl2k : referralMediumBannerUrl);
//                                referralBannerItem.setAction(referralAction);
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("REFERRAL")) {
//                                    bannerItemList.add(0, referralBannerItem);
//                                } else {
//                                    bannerItemList.add(referralBannerItem);
//                                }
//                            }
//
//                        }
//
//                        String wellnessBannerUrl = homeBanners.getMedium().get("wellness");
//                        String wellnessDeeplink = homeBanners.getMedium().getOrDefault("wellnessDeeplink", "curefit://chroniccarelive?pageId=chroniccarelive");
//                        if (wellnessBannerUrl != null && !wellnessBannerUrl.isEmpty()) {
//                            Action wellnessPageAction = Action.builder().actionType(ActionType.NAVIGATION).url(wellnessDeeplink).build();
//                            BannerItem wellnessBannerItem = new BannerItem();
//                            wellnessBannerItem.setImage(wellnessBannerUrl);
//                            wellnessBannerItem.setAction(wellnessPageAction);
//                            bannerItemList.add(wellnessBannerItem);
//                        }
//
//                        if (isSalesDiagnosticBannerEnabled(userContext, segmentationCacheClient)) {
//                            String diagnosticsLargeBannerUrl = homeBanners.getLarge().get("diagnostics");
//                            String diagnosticsMediumBannerUrl = homeBanners.getMedium().get("diagnostics");
//
//                            Action diagnosticRequestAction = new Action();
//                            diagnosticRequestAction.setActionType(ActionType.SF_OPS_REQUEST);
//                            diagnosticRequestAction.setTitle("RAISE A REQUEST");
//                            diagnosticRequestAction.setMeta(new SfOpsRequestMeta(REQUEST_DIAGNOSTICS));
//                            SfSalesBannerMeta sfSalesBannerMeta2 = new SfSalesBannerMeta(diagnosticsLargeBannerUrl, "Please raise a request and we will reach out to you within 24 hours for next steps.", diagnosticRequestAction);
//                            Action showSalesBannerAction2 = new Action();
//                            showSalesBannerAction2.setActionType(ActionType.SHOW_SF_SALES_BANNER_MODAL);
//                            showSalesBannerAction2.setMeta(sfSalesBannerMeta2);
//
//                            BannerItem diagnosticBannerItem = new BannerItem();
//                            diagnosticBannerItem.setImage(diagnosticsMediumBannerUrl);
//                            diagnosticBannerItem.setAction(showSalesBannerAction2);
//                            bannerItemList.add(diagnosticBannerItem);
//                        }
//                    }
//
//                    if (ChronicCareAppUtil.isWellnessAtCenterPrePurchaseEnabledUser(userContext, segmentationCacheClient, userOnboardingService, wellnessActivePackResponse)) {
//                        String wellnessAtCenterBannerUrl = Objects.nonNull(megaSaleData) ? "wellnessAtCenterMegaSales" : "wellnessAtCenterSales";
//                        SfBannerCarouselWidget wellnessCenterBannerWidget = chronicCareServiceHelper.getWellnessAtCenterBannerWidget(userContext, wellnessAtCenterBannerUrl, false);
//                        if (Objects.nonNull(wellnessCenterBannerWidget) && CollectionUtils.isNotEmpty(wellnessCenterBannerWidget.getData())) {
//                            if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("WELLNESS_AT_CENTER")) {
//                                bannerItemList.add(0, wellnessCenterBannerWidget.getData().get(0));
//                            } else {
//                                bannerItemList.add(wellnessCenterBannerWidget.getData().get(0));
//                            }
//                        }
//                    }
//                }
//
//                if (CollectionUtils.isEmpty(bannerItemList)) return null;
//
//                sfBannerCarouselWidget.setData(bannerItemList);
//                sfBannerCarouselWidget.setLayoutProps(getSalesBannerLayoutProps(true, Objects.nonNull(megaSaleData)));
//                return sfBannerCarouselWidget;
//            }
//        }
        return null;
    }

    SfBannerCarouselWidget getSalesBannerWidgetV2(UserContext userContext, UserOnboardingActionWithContext onboardingAction,
                                                CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                RenewalJourneyResponse renewalJourney, BundleProduct bundleProduct,
                                                ActivePackResponse wellnessActivePackResponse) throws IOException {
        if (AppUtil.isSugarFitApp(userContext) && isSalesBannerCarouselEnabled(userContext)) {

            SfHomepageBannerConfig sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {
            }, new SfHomepageBannerConfig());
            if (sfHomepageBannerConfig != null) {
                SfHomepageBannerConfig.BannerConfig saleBanners = sfHomepageBannerConfig.getSaleBanners();
                SfHomepageBannerConfig.BannerConfig megaSaleBanners = sfHomepageBannerConfig.getMegaSaleBanners();

                if (Objects.nonNull(saleBanners)) {
                    SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
                    BannerItem[] bannerItemList = new BannerItem[saleBanners.getClass().getDeclaredFields().length];
                    MegaSaleData megaSaleData = chronicCareServiceHelper.getMegaSalesDataV2(userContext);

                    if (renewalJourney != null && renewalJourney.getHomepageBanner() != null
                            && isRenewalReportSupported(userContext)
                            && !chronicCareServiceHelper.isSpecialSugarControlPlanPack(bundleProduct)) {
                        String renewalBannerUrl = renewalJourney.getHomepageBanner().getAssetURL(); // Changed URL here
                        String name = renewalJourney.getHomepageBanner().getName();
                        String actionUrl = STR."curefit://renewsubscription?source=HOME_SALES_BANNER&bannerName=\{name}";
                        Map<String, String> eventData = new HashMap<>();
                        eventData.put("bannerType", "Renewal");
                        eventData.put("bannerName", name);
                        Map<String, Object> analyticsData = new HashMap<>();
                        analyticsData.put("eventKey", "widget_click");
                        analyticsData.put("eventData", eventData);
                        Action renewalPageAction = Action.builder().url(actionUrl).analyticsData(analyticsData).actionType(ActionType.NAVIGATION).title("RENEW SUBSCRIPTION").build();
                        BannerItem renewalBannerItem = chronicCareServiceHelper.createBannerItem(renewalBannerUrl, null, renewalPageAction);
                        if (Objects.nonNull(saleBanners.getRenewalJourney())) {
                            bannerItemList[saleBanners.getRenewalJourney().getOrderIndex()] = renewalBannerItem;
                        } else {
                            bannerItemList[0] = renewalBannerItem;
                        }
                    }

                    if (saleBanners.getSachinEpisode() != null) {
                        String sachinLaunchBannerImage = saleBanners.getSachinEpisode().getImageUrl();
                        String sachinLaunchLottieUrl = saleBanners.getSachinEpisode().getLottieUrl();
                        if ((sachinLaunchBannerImage !=null && !sachinLaunchBannerImage.isEmpty()) || (sachinLaunchLottieUrl != null && !sachinLaunchLottieUrl.isEmpty())) {
                            BannerItem bannerItem = new BannerItem();
                            bannerItem.setImage(sachinLaunchBannerImage);
                            bannerItem.setLottieUrl(sachinLaunchLottieUrl);
                            bannerItem.setAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).url("https://youtu.be/L9DaGbIzS0w?si=GyIY1GtkVIYVIbPc").build());
                            bannerItemList[saleBanners.getSachinEpisode().getOrderIndex()] = bannerItem;
                        }
                    }

                    if (saleBanners.getSachinLaunch() != null) {
                        String sachinLaunchBannerImage = saleBanners.getSachinLaunch().getImageUrl();
                        String sachinLaunchLottieUrl = saleBanners.getSachinLaunch().getLottieUrl();
                        if ((sachinLaunchBannerImage !=null && !sachinLaunchBannerImage.isEmpty()) || (sachinLaunchLottieUrl != null && !sachinLaunchLottieUrl.isEmpty())) {
                            BannerItem bannerItem = new BannerItem();
                            bannerItem.setImage(sachinLaunchBannerImage);
                            bannerItem.setLottieUrl(sachinLaunchLottieUrl);
                            if (!ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
                                bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url(ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext) ? "curefit://sfstorelp" : "curefit://sfecommerceplp").build());
                            }
                            bannerItemList[saleBanners.getSachinLaunch().getOrderIndex()] = bannerItem;
                        }
                    }

                    if (isECommerceSupported(userContext)) {
                        String weightLossUrl = saleBanners.getWeightLoss() != null ? saleBanners.getWeightLoss().getImageUrl() : null;
                        String weightLossLottieUrl = saleBanners.getWeightLoss() != null ? saleBanners.getWeightLoss().getLottieUrl() : null;
                        if ((weightLossUrl !=null && !weightLossUrl.isEmpty()) || (weightLossLottieUrl !=null && !weightLossLottieUrl.isEmpty())) {
                            Action liquidateBannerAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfecommercepdp?productId=REVERSAL_ACCELERATOR_KIT").build();
                            BannerItem liquidateBannerItem = chronicCareServiceHelper.createBannerItem(weightLossUrl, weightLossLottieUrl, liquidateBannerAction);
                            bannerItemList[saleBanners.getWeightLoss().getOrderIndex()] = liquidateBannerItem;
                        }

                        String attaBannerUrl = saleBanners.getSpecificEcommerceProduct() != null ? saleBanners.getSpecificEcommerceProduct().getImageUrl() : null;
                        String attaLottieUrl = saleBanners.getSpecificEcommerceProduct() != null ? saleBanners.getSpecificEcommerceProduct().getLottieUrl() : null;
                        if ((attaBannerUrl !=null && !attaBannerUrl.isEmpty()) || (attaLottieUrl !=null && !attaLottieUrl.isEmpty())) {
                            Action liquidateBannerAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfecommercepdp?productId=PROTEIN_BUTTERMILK_350").build();
                            BannerItem liquidateBannerItem = chronicCareServiceHelper.createBannerItem(attaBannerUrl, attaLottieUrl, liquidateBannerAction);
                            bannerItemList[saleBanners.getSpecificEcommerceProduct().getOrderIndex()] = liquidateBannerItem;
                        }

                        String ecomBhujiyaBannerUrl = saleBanners.getEcommerceBhujiya() != null ? saleBanners.getEcommerceBhujiya().getImageUrl() : null;
                        String ecomBhujiyaLottieUrl = saleBanners.getEcommerceBhujiya() != null ? saleBanners.getEcommerceBhujiya().getLottieUrl() : null;
                        if ((ecomBhujiyaBannerUrl !=null && !ecomBhujiyaBannerUrl.isEmpty()) || (ecomBhujiyaLottieUrl !=null && !ecomBhujiyaLottieUrl.isEmpty())) {
                            Action bhujiyaBannerAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfecommercepdp?productId=BHUJIYA_150_GM").build();
                            BannerItem bhujiyaBannerItem = chronicCareServiceHelper.createBannerItem(ecomBhujiyaBannerUrl, ecomBhujiyaLottieUrl, bhujiyaBannerAction);
                            bannerItemList[saleBanners.getEcommerceBhujiya().getOrderIndex()] = bhujiyaBannerItem;
                        }

                        SfHomepageBannerConfig.ImageConfig ecomBanner = Objects.nonNull(megaSaleData) ? megaSaleBanners.getEcommerce() :
                                saleBanners.getEcommerce();
                        String ecomBannerImageUrl = ecomBanner != null ? ecomBanner.getImageUrl() : null;
                        String ecomBannerLottieUrl = ecomBanner != null ? ecomBanner.getLottieUrl() : null;
                        if ((ecomBannerImageUrl !=null && !ecomBannerImageUrl.isEmpty()) || (ecomBannerLottieUrl !=null && !ecomBannerLottieUrl.isEmpty())) {
                            Action ecomBannerAction = Action.builder().actionType(ActionType.NAVIGATION).url(ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext) ? "curefit://sfstorelifestylepage" : "curefit://sfecommerceplp").build();
                            BannerItem eComBannerItem = chronicCareServiceHelper.createBannerItem(ecomBannerImageUrl, ecomBannerLottieUrl, ecomBannerAction);
                            bannerItemList[ecomBanner.getOrderIndex()] = eComBannerItem;
                        }

                        if (isSmartScaleSellingSupportedAppVersion(userContext) && !chronicCareServiceHelper.isSmartScaleDelivered(userContext)) {
                            SfHomepageBannerConfig.ImageConfig smartScaleBanner = Objects.nonNull(megaSaleData) ? megaSaleBanners.getSmartScale() :
                                    saleBanners.getSmartScale();
                            String smartScaleBannerImageUrl = smartScaleBanner != null ? smartScaleBanner.getImageUrl() : null;
                            String smartScaleBannerLottieUrl = smartScaleBanner != null ? smartScaleBanner.getLottieUrl() : null;
                            if ((smartScaleBannerImageUrl !=null && !smartScaleBannerImageUrl.isEmpty()) || (smartScaleBannerLottieUrl !=null && !smartScaleBannerLottieUrl.isEmpty())) {
                                Action smartScaleAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfsmartscaleplp").build();
                                if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)
                                        && ChronicCareAppUtil.isCgmCombinedCartSupportedAppVersion(userContext)) {
                                    smartScaleAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfecommercepdp?productId=SMART_SCALE").build();
                                }
                                BannerItem smartScaleBannerItem = chronicCareServiceHelper.createBannerItem(smartScaleBannerImageUrl, smartScaleBannerLottieUrl, smartScaleAction);
                                bannerItemList[smartScaleBanner.getOrderIndex()] = smartScaleBannerItem;
                            }
                        }
                    }

                    if (onboardingAction == null || (onboardingAction.getPackRenewalContextActionWithContext().getAction().isActionPermitted() && !onboardingAction.getPackRenewalContextActionWithContext().getContext().getRenewed())) {
                        if (isRenewalOfferRunning()) {
                            SfHomepageBannerConfig.ImageConfig renewalBanner = Objects.nonNull(megaSaleData) ? megaSaleBanners.getRenewalSales() : saleBanners.getRenewalSales();
                            Action renewalPageAction = Action.builder().url("curefit://renewsubscription").actionType(ActionType.NAVIGATION).title("RENEW SUBSCRIPTION").build();
                            BannerItem renewalBannerItem = chronicCareServiceHelper.createBannerItem(renewalBanner.getImageUrl(), renewalBanner.getLottieUrl(), renewalPageAction);
                            bannerItemList[renewalBanner.getOrderIndex()] = renewalBannerItem;
                        }
                    } else {
                        if (isSalesCGMBannerEnabled(userContext, cgmOnboardingStatusResponse, bundleProduct)) {
                            SfHomepageBannerConfig.ImageConfig cgmMediumBanner = Objects.nonNull(megaSaleData)
                                    ? megaSaleBanners.getCgm()
                                    : saleBanners.getCgm();
                            if (Objects.nonNull(cgmMediumBanner) && Objects.nonNull(cgmMediumBanner.getImageUrl()) && !cgmMediumBanner.getImageUrl().isEmpty()) {
                                Action showSalesBannerAction1 = Action.builder().title("BUY NEW CGM").url(ChronicCareServiceHelper.getCgmStorePageLink(userContext)).actionType(ActionType.NAVIGATION).build();
                                BannerItem cgmBannerItem = chronicCareServiceHelper.createBannerItem(cgmMediumBanner.getImageUrl(), cgmMediumBanner.getLottieUrl(), showSalesBannerAction1);
                                bannerItemList[cgmMediumBanner.getOrderIndex()] = cgmBannerItem;
                            }
                        }

                        if (isReferralBottomBannerEnabledForUser(userContext)) {
                            boolean isReferralChallengeBannerAdded = false;
                            if (ChronicCareAppUtil.isReferralChallengeSupportedApp(userContext)) {
                                String referralSupportedSegment = sfHomepageBannerConfig.getMeta().get("referralChallengeSupportedSegment");
                                String referralChallengeId = sfHomepageBannerConfig.getMeta().get("referralChallengeId");
                                if (StringUtils.isNotEmpty(referralSupportedSegment) && StringUtils.isNotEmpty(referralChallengeId)) {
                                    if (ChronicCareAppUtil.isReferralChallengeSupportedSegment(userContext, referralSupportedSegment)) {
                                        if (Objects.nonNull(saleBanners.getReferralChallenge()) && Objects.nonNull(saleBanners.getReferralChallenge().getImageUrl()) && !saleBanners.getReferralChallenge().getImageUrl().isEmpty()) {
                                            isReferralChallengeBannerAdded = true;
                                            String referralMediumBannerUrl = saleBanners.getReferralChallenge().getImageUrl();
                                            String actionUrl = STR."curefit://sfchallengedetailspage?challengeId=\{referralChallengeId}&tabKey=ABOUT";
                                            Action referralAction = Action.builder().title("REFER NOW").url(actionUrl).actionType(ActionType.NAVIGATION).build();
                                            BannerItem referralBannerItem = chronicCareServiceHelper.createBannerItem(referralMediumBannerUrl, saleBanners.getReferralChallenge().getLottieUrl(), referralAction);
                                            bannerItemList[saleBanners.getReferralChallenge().getOrderIndex()] = referralBannerItem;
                                        }
                                    }
                                }
                            }
                            if (!isReferralChallengeBannerAdded) {
                                SfHomepageBannerConfig.ImageConfig referralMediumBanner = saleBanners.getReferralPage();
                                SfHomepageBannerConfig.ImageConfig referralMediumBanner2k = saleBanners.getReferralPage2k();
                                SfHomepageBannerConfig.ImageConfig referralBanner = ChronicCareAppUtil.isSugarfitPlusUser(userContext) ? referralMediumBanner2k : referralMediumBanner;
                                if (Objects.nonNull(referralBanner) && Objects.nonNull(referralBanner.getImageUrl()) && !referralBanner.getImageUrl().isEmpty()) {
                                    Action referralAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfreferralpage").build();
                                    BannerItem referralBannerItem = chronicCareServiceHelper.createBannerItem(referralBanner.getImageUrl(), referralBanner.getLottieUrl(), referralAction);
                                    bannerItemList[referralBanner.getOrderIndex()] = referralBannerItem;
                                }
                            }
                        }

                        SfHomepageBannerConfig.ImageConfig wellnessBanner = saleBanners.getWellness();
                        String wellnessDeeplink = "curefit://chroniccarelive?pageId=chroniccarelive";
                        if (wellnessBanner != null && wellnessBanner.getImageUrl() != null && !wellnessBanner.getImageUrl().isEmpty()) {
                            Action wellnessPageAction = Action.builder().actionType(ActionType.NAVIGATION).url(wellnessDeeplink).build();
                            BannerItem wellnessBannerItem = chronicCareServiceHelper.createBannerItem(wellnessBanner.getImageUrl(), wellnessBanner.getLottieUrl(), wellnessPageAction);
                            bannerItemList[wellnessBanner.getOrderIndex()] = wellnessBannerItem;
                        }

                        if (isSalesDiagnosticBannerEnabled(userContext)) {
                            if (Objects.nonNull(saleBanners.getDiagnostic()) && Objects.nonNull(saleBanners.getDiagnostic().getImageUrl()) && !saleBanners.getDiagnostic().getImageUrl().isEmpty()) {
                                String diagnosticsLargeBannerUrl = "image/chroniccare/marketing/banners/home/<USER>/diagnostic_banner_v2.png";
                                String diagnosticsMediumBannerUrl = saleBanners.getDiagnostic().getImageUrl();

                                Action diagnosticRequestAction = new Action();
                                diagnosticRequestAction.setActionType(ActionType.SF_OPS_REQUEST);
                                diagnosticRequestAction.setTitle("RAISE A REQUEST");
                                diagnosticRequestAction.setMeta(new SfOpsRequestMeta(REQUEST_DIAGNOSTICS));
                                SfSalesBannerMeta sfSalesBannerMeta2 = new SfSalesBannerMeta(diagnosticsLargeBannerUrl, "Please raise a request and we will reach out to you within 24 hours for next steps.", diagnosticRequestAction);
                                Action showSalesBannerAction2 = new Action();
                                showSalesBannerAction2.setActionType(ActionType.SHOW_SF_SALES_BANNER_MODAL);
                                showSalesBannerAction2.setMeta(sfSalesBannerMeta2);

                                BannerItem diagnosticBannerItem = chronicCareServiceHelper.createBannerItem(diagnosticsMediumBannerUrl, saleBanners.getDiagnostic().getLottieUrl(), showSalesBannerAction2);
                                bannerItemList[saleBanners.getDiagnostic().getOrderIndex()] = diagnosticBannerItem;
                            }
                        }


                        if (ChronicCareAppUtil.isWellnessAtCenterPrePurchaseEnabledUser(userContext, userOnboardingService, wellnessActivePackResponse)) {
                            SfHomepageBannerConfig.ImageConfig wellnessAtCenterBanner = Objects.nonNull(megaSaleData) ? megaSaleBanners.getWellnessAtCenter() : saleBanners.getWellnessAtCenter();
                            if (Objects.nonNull(wellnessAtCenterBanner) && Objects.nonNull(wellnessAtCenterBanner.getImageUrl()) && !wellnessAtCenterBanner.getImageUrl().isEmpty()) {
                                Action wellnessAtCenterAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfwellnessatcenterprepurchase").build();
                                BannerItem wellnessAtCenter = chronicCareServiceHelper.createBannerItem(wellnessAtCenterBanner.getImageUrl(), wellnessAtCenterBanner.getLottieUrl(), wellnessAtCenterAction);
                                if (!wellnessAtCenterBanner.getImageUrl().isEmpty()) {
                                    bannerItemList[wellnessAtCenterBanner.getOrderIndex()] = wellnessAtCenter;
                                }
                            }
                        }
                    }

                    if (CollectionUtils.isEmpty(Arrays.stream(bannerItemList).filter(Objects::nonNull).toList())) return null;

                    sfBannerCarouselWidget.setData(Arrays.stream(bannerItemList).filter(Objects::nonNull).toList());
                    sfBannerCarouselWidget.setLayoutProps(getSalesBannerLayoutProps(true, Objects.nonNull(megaSaleData)));
                    return sfBannerCarouselWidget;
                }
            }
        }
        return null;
    }

    CompletableFuture<List<HomeMenuItem>> getMoreMenuItemsFuture(UserContext userContext, CompletableFuture<AssignedAgentWidget> assignedAgentWidgetFuture, CompletableFuture<BaseWidgetNonVM> wellnessAtCenterWidgetFuture) {
        return supplyAsync(() -> getMoreMenuItems(userContext, assignedAgentWidgetFuture, wellnessAtCenterWidgetFuture), serviceInterfaces.getTaskExecutor());
    }

    private List<HomeMenuItem> getMoreMenuItems(UserContext userContext, CompletableFuture<AssignedAgentWidget> assignedAgentWidgetFuture, CompletableFuture<BaseWidgetNonVM> wellnessAtCenterWidgetFuture) {
        try {
            List<HomeMenuItem> moreMenuItems = new ArrayList<>();

            AssignedAgentWidget assignedAgentWidget = null;
            try {
                assignedAgentWidget = assignedAgentWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }

            if (assignedAgentWidget != null && assignedAgentWidget.getCoachData() != null) {
                AssignedAgentData coachData = assignedAgentWidget.getCoachData();
                HomeMenuItem menuItemCoach = new HomeMenuItem(MenuItemType.COACH, "PERSONAL COACH", coachData.getAgentName(), coachData.getAgentImageUrl(), "", coachData.getProfileAction(), "CONSULT");
                moreMenuItems.add(menuItemCoach);
            }

            if (isSugarFitApp(userContext) && assignedAgentWidget != null && assignedAgentWidget.getDoctorData() != null) {
                AssignedAgentData doctorData = assignedAgentWidget.getDoctorData();
                String instruction = "";
                String agentImageUrl = doctorData.getAgentImageUrl();
                if (!doctorData.getProfileAction().getIsEnabled()) {
                    if (doctorData.getAgentAssigned()) {
                        instruction = doctorData.getSubtitle();
                    } else {
                        agentImageUrl = SF_CARE_TEAM_DOCTOR_ASSIGNMENT_PLACEHOLDER_URL;
                    }
                }
                HomeMenuItem menuItemDoctor = new HomeMenuItem(MenuItemType.DOCTOR, "PERSONAL DOCTOR", doctorData.getAgentName(), agentImageUrl, instruction, doctorData.getProfileAction(), doctorData.getAgentAssigned() ? "CONSULT" : "");
                moreMenuItems.add(menuItemDoctor);
            }

            try {
                if (wellnessAtCenterWidgetFuture != null) {
                    BaseWidgetNonVM wellnessWidget = wellnessAtCenterWidgetFuture != null ? wellnessAtCenterWidgetFuture.get() : null;
                    if(wellnessWidget instanceof SfWellnessAtCenterTherapyListCardWidgetV2) {
                        SfWellnessAtCenterTherapyListCardWidgetV2 wellnessAtCenterTherapyListCardWidget = (SfWellnessAtCenterTherapyListCardWidgetV2) wellnessAtCenterWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                        if (wellnessAtCenterTherapyListCardWidget != null && CollectionUtils.isNotEmpty(wellnessAtCenterTherapyListCardWidget.getTherapies())) {
                            wellnessAtCenterTherapyListCardWidget.getTherapies().forEach(therapy -> {
                                HomeMenuItem menuItemCoach = new HomeMenuItem(MenuItemType.THERAPY, "AT CENTER", therapy.getTitle(), SfHomePageUtil.getWellnessAtCenterPopOverIcon(therapy.getProductCode()), "", therapy.getAction(), "BOOK");
                                moreMenuItems.add(menuItemCoach);
                            });
                        }
                    } else{
                        SfWellnessAtCenterTherapyListCardWidget wellnessAtCenterTherapyListCardWidget = (SfWellnessAtCenterTherapyListCardWidget) wellnessAtCenterWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                        if (wellnessAtCenterTherapyListCardWidget != null && CollectionUtils.isNotEmpty(wellnessAtCenterTherapyListCardWidget.getTherapies())) {
                            wellnessAtCenterTherapyListCardWidget.getTherapies().forEach(therapy -> {
                                HomeMenuItem menuItemCoach = new HomeMenuItem(MenuItemType.THERAPY, "AT CENTER", therapy.getTitle(), SfHomePageUtil.getWellnessAtCenterPopOverIcon(therapy.getProductCode()), "", therapy.getAction(), "BOOK");
                                moreMenuItems.add(menuItemCoach);
                            });
                        }
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }

            Action progressAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://goalsprogress").isEnabled(true).build();
            HomeMenuItem menuItemProgress = new HomeMenuItem(MenuItemType.PROGRESS, "VIEW YOUR OVERALL PROGRESS", "Progress", "image/chroniccare/more_menu_progress_icon.png", "", progressAction, "");
            moreMenuItems.add(menuItemProgress);

            return moreMenuItems;
        } catch (Exception e) {
            log.error("Exception in getting care team", e);
            exceptionReportingService.reportException("Exception in getting care team", e);
            return null;
        }
    }

    CompletableFuture<BookingModalConfig> getBookingModalConfigFuture(UserContext userContext, UserOnboardingActionWithContext onboardingActions, ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam, PatientDetail patientDetail) {
        return supplyAsync(() -> getBookingModalConfig(userContext, onboardingActions, activePackResponse, assignedCareTeam, patientDetail), serviceInterfaces.getTaskExecutor());
    }

    private BookingModalConfig getBookingModalConfig(UserContext userContext, UserOnboardingActionWithContext onboardingActions, ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam, PatientDetail patientDetail) {
        try {
            if (Objects.nonNull(activePackResponse) && chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
                return null;
            }

            BookingModalConfig bookingModalConfig = new BookingModalConfig();

            if (isUltraFitApp(userContext)) {
                return bookingModalConfig;
            }

            if (onboardingActions.getCoachCardActionWithContext().getAction().isActionPermitted()) {
                CardData coachCardData = getCoachCardData(userContext, onboardingActions, assignedCareTeam, activePackResponse);
                coachCardData.setCardMessage(isUltraFitApp(userContext) ? " is your Metabolic Coach. Book an appointment and start your journey!" : " is your Diabetes Coach. Book an appointment and start your journey!");
                bookingModalConfig.setCardData(coachCardData);
            } else if (!isUltraFitApp(userContext) && ChronicCareAppUtil.isCGMDplusIEnabled(userContext) && chronicCareServiceHelper.isCGMInstallationBookingAllowed(onboardingActions) && chronicCareServiceHelper.isCGMInstallationBookingPending(onboardingActions)) {
                CardData cgmCardData = getCGMInstallationCardData(userContext, onboardingActions);
                cgmCardData.setCardMessage("Learn how your body responds to your eating habits, book your CGM delivery and installation");
                bookingModalConfig.setCardData(cgmCardData);
            } else if (onboardingActions.getDiagnosticCardActionWithContext().getAction().isActionPermitted() && onboardingActions.getDiagnosticCardActionWithContext().getAction().getServiceableOnline()) {
                CardData diagnosticCardData = getDiagnosticCardData(userContext, onboardingActions, patientDetail);
                diagnosticCardData.setCardMessage(" a time to get your diagnostic tests done from the comfort of your home!");
                bookingModalConfig.setCardData(diagnosticCardData);
            } else if (!chronicCareServiceHelper.isRenewalProduct(activePackResponse.getBundleProduct()) && onboardingActions.getDoctorCardActionWithContext().getAction().isActionPermitted() && onboardingActions.getDoctorCardActionWithContext().getAction().isShowCard()) {
                CardData doctorCardData = getDoctorCardData(onboardingActions, assignedCareTeam, activePackResponse, userContext);
                doctorCardData.setCardMessage(" is your doctor on sugar.fit. Book an appointment and start your journey!");
                bookingModalConfig.setCardData(doctorCardData);
            }
            return bookingModalConfig;
        } catch (Exception e) {
            String message = String.format("Error in creating booking modal config for user :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
            return null;
        }
    }

    public CompletableFuture<AppAnnouncementData> getAppAnnouncementDataFuture(UserContext userContext,
                                                                                CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                                                ActivePackResponse activePackResponse,
                                                                                RenewalJourneyResponse renewalJourney,
                                                                                ActivePackResponse wellnessActivePackResponse) {
        return supplyAsync(() -> getAppAnnouncementData(userContext, cgmOnboardingStatus, activePackResponse, renewalJourney, wellnessActivePackResponse),
                serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<CsTicketResolutionNotificationData> getCsTicketResolutionNotificationFuture(UserContext userContext) {
        return supplyAsync(() -> chronicCareServiceHelper.getCsTicketResolutionNotification(userContext), serviceInterfaces.getTaskExecutor());
    }

    private AppAnnouncementData getAppAnnouncementData(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                       ActivePackResponse activePackResponse, RenewalJourneyResponse renewalJourney,
                                                       ActivePackResponse wellnessActivePackResponse) {
        try {
            String userId = userContext.getUserProfile().getUserId();
            if (ChronicCareAppUtil.isSalesInterstitialEnabled(userContext)) {
                AppAnnouncementData appAnnouncementData = new AppAnnouncementData();
                SfAppAnnouncementConfig announcementConfig = chronicCareServiceHelper.getAnnouncementConfig();
                if (announcementConfig != null && announcementConfig.isEnabled()) {
                    if (CollectionUtils.isNotEmpty(announcementConfig.getBanners())) {
                        for (int i = 0; i < announcementConfig.getBanners().size(); i++) {
                            SfAppAnnouncementConfig.Banner b = announcementConfig.getBanners().get(i);
                            if (Objects.nonNull(b)) {
                                String bannerImage = b.getBannerImage();
                                Action bannerAction = b.getBannerAction();
                                if (bannerAction != null
                                        && bannerAction.getUrl() != null
                                        && bannerAction.getUrl().contains("curefit://cgmstorepage")) {
                                    bannerAction.setUrl(ChronicCareServiceHelper.getCgmStorePageLink(userContext));
                                }
                                Integer skipDays = b.getSkipDays();
                                String type = b.getType();
                                if (Objects.nonNull(b.getSegmentName())
                                        && !b.getSegmentName().isEmpty()) {
                                    if (ChronicCareAppUtil.isUserExistsInSegment(userContext, b.getSegmentName())) {
                                        if (Objects.nonNull(type) && !type.isEmpty()) {
                                            if (type.equals("CGM")) {
                                                if (isSalesBannerCarouselEnabled(userContext) && isSalesCGMBannerEnabled(userContext, cgmOnboardingStatus, activePackResponse.getBundleProduct())) {
                                                    Banner banner = new Banner(bannerImage, bannerAction);
                                                    appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                    appAnnouncementData.setBanners(List.of(banner));
                                                    return appAnnouncementData;
                                                }
                                            } else if (type.equals("REFERRAL")) {
                                                if (isSalesBannerCarouselEnabled(userContext)) {
                                                    Banner banner = new Banner(bannerImage, bannerAction);
                                                    appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                    appAnnouncementData.setBanners(List.of(banner));
                                                    return appAnnouncementData;
                                                }
                                            } else if (type.equals("ECOMMERCE")) {
                                                if (ChronicCareAppUtil.isECommerceSupported(userContext)) {
                                                    Banner banner = new Banner(bannerImage, bannerAction);
                                                    appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                    appAnnouncementData.setBanners(List.of(banner));
                                                    return appAnnouncementData;
                                                }
                                            } else if (type.equals("ECOMMERCE_IMMUNITY")) {
                                                if (ChronicCareAppUtil.isEcomProteinSupportedApp(userContext)) {
                                                    Banner banner = new Banner(bannerImage, bannerAction);
                                                    appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                    appAnnouncementData.setBanners(List.of(banner));
                                                    return appAnnouncementData;
                                                }
                                            } else if (type.equals("SMART_SCALE")) {
                                                if (ChronicCareAppUtil.isSmartScaleSellingSupportedAppVersion(userContext) && !chronicCareServiceHelper.isSmartScaleDelivered(userContext)) {
                                                    Banner banner = new Banner(bannerImage, bannerAction);
                                                    appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                    appAnnouncementData.setBanners(List.of(banner));
                                                    return appAnnouncementData;
                                                }
                                            } else if (type.equals("REFERRAL_CHALLENGE")) {
                                                if (ChronicCareAppUtil.isReferralChallengeSupportedApp(userContext)) {
                                                    String challengeId = b.getChallengeId();
                                                    if (StringUtils.isNotEmpty(b.getSegmentName()) && StringUtils.isNotEmpty(challengeId)) {
                                                        if (ChronicCareAppUtil.isReferralChallengeSupportedSegment(userContext, b.getSegmentName())) {
                                                            Banner banner = new Banner(bannerImage, bannerAction);
                                                            appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                            appAnnouncementData.setBanners(List.of(banner));
                                                            return appAnnouncementData;
                                                        }
                                                    }
                                                }
                                            } else if (type.equals("WELLNESS_AT_CENTER")) {
                                                if (ChronicCareAppUtil.isWellnessAtCenterPrePurchaseEnabledUser(userContext, userOnboardingService, wellnessActivePackResponse)) {
                                                    Banner banner = new Banner(bannerImage, bannerAction);
                                                    appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                    appAnnouncementData.setBanners(List.of(banner));
                                                    return appAnnouncementData;
                                                }
                                            } else if (type.equals("MEGA_SALE")) {
                                                if (ChronicCareAppUtil.isMegaSaleSupportedAppVersion(userContext)) {
                                                    Banner banner = new Banner(bannerImage, bannerAction);
                                                    appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                    appAnnouncementData.setBanners(List.of(banner));
                                                    return appAnnouncementData;
                                                }
                                            } else if (type.equals("CGM_INSTALLATION")) {
                                                if (ChronicCareAppUtil.isCGMInstallationWebinarRunning()) {
                                                    Banner banner = new Banner(bannerImage, bannerAction);
                                                    appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                    appAnnouncementData.setBanners(List.of(banner));
                                                    return appAnnouncementData;
                                                }
                                            }
                                        } else {
                                            Banner banner = new Banner(bannerImage, bannerAction);
                                            appAnnouncementData.setSkipIntervalInDays(skipDays);
                                            appAnnouncementData.setBanners(List.of(banner));
                                            return appAnnouncementData;
                                        }
                                    }
                                } else {
                                    if (Objects.nonNull(type) && !type.isEmpty()) {
                                        if (type.equals("ECOMMERCE")) {
                                            if (ChronicCareAppUtil.isECommerceSupported(userContext)) {
                                                Banner banner = new Banner(bannerImage, bannerAction);
                                                appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                appAnnouncementData.setBanners(List.of(banner));
                                                return appAnnouncementData;
                                            }
                                        } else if (type.equals("SMART_SCALE")) {
                                            if (ChronicCareAppUtil.isSmartScaleSellingSupportedAppVersion(userContext) && !chronicCareServiceHelper.isSmartScaleDelivered(userContext)) {
                                                Banner banner = new Banner(bannerImage, bannerAction);
                                                appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                appAnnouncementData.setBanners(List.of(banner));
                                                return appAnnouncementData;
                                            }
                                        } else if (type.equals("WELLNESS_AT_CENTER")) {
                                            if (ChronicCareAppUtil.isWellnessAtCenterPrePurchaseEnabledUser(userContext, userOnboardingService, wellnessActivePackResponse)) {
                                                Banner banner = new Banner(bannerImage, bannerAction);
                                                appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                appAnnouncementData.setBanners(List.of(banner));
                                                return appAnnouncementData;
                                            }
                                        } else if (type.equals("MEGA_SALE")) {
                                            if (ChronicCareAppUtil.isMegaSaleSupportedAppVersion(userContext)) {
                                                Banner banner = new Banner(bannerImage, bannerAction);
                                                appAnnouncementData.setSkipIntervalInDays(skipDays);
                                                appAnnouncementData.setBanners(List.of(banner));
                                                return appAnnouncementData;
                                            }
                                        }
                                    } else {
                                        Banner banner = new Banner(bannerImage, bannerAction);
                                        appAnnouncementData.setSkipIntervalInDays(skipDays);
                                        appAnnouncementData.setBanners(List.of(banner));
                                        return appAnnouncementData;
                                    }
                                }
                            }
                        }
                    }

                }

//                if (renewalJourney != null && renewalJourney.getHomepageInterstitial() != null && isRenewalReportSupported(userContext)) {
//                    String name = renewalJourney.getHomepageInterstitial().getName();
//                    String actionUrl = "curefit://renewsubscription?source=INTERSTITIAL&interstitialName=" + name;
//                    Map<String, String> eventData = new HashMap<>();
//                    eventData.put("bannerType", "Renewal");
//                    eventData.put("bannerName", name);
//                    Map<String, Object> analyticsData = new HashMap<>();
//                    analyticsData.put("eventKey", "APP_ANNOUNCEMENT_POPUP_ACTION");
//                    analyticsData.put("eventData", eventData);
//                    Action bannerAction = Action.builder().title("RENEW SUBSCRIPTION").url(actionUrl)
//                            .analyticsData(analyticsData).actionType(ActionType.NAVIGATION).build();
//
//                    String bannerImage = renewalJourney.getHomepageInterstitial().getAssetURL();
//                    Banner banner = new Banner(bannerImage, bannerAction);
//                    appAnnouncementData.setSkipIntervalInDays(0);
//                    appAnnouncementData.setBanners(List.of(banner));
//                    return appAnnouncementData;
//                }

            }
            return null;
        } catch (Exception e) {
            exceptionReportingService.reportException("Exception in getAppAnnouncementData", e);
        }
        return null;
    }

    CompletableFuture<BaseWidgetNonVM> getBlogsWidgetFuture(UserContext userContext, String sessionId) {
        return supplyAsync(() -> {
            if (!ChronicCareAppUtil.isAppStoreUserId(userContext)) {
                return sugarFitBlogWidgetBuilder.buildBlogsWidget(userContext, sessionId);
            } else {
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<NPSModalData> getNPSDataFuture(UserContext userContext, ActivePackResponse activePackResponse) {
        return supplyAsync(() -> {
            try {
                if (!ChronicCareAppUtil.isNPSPeriodicLogicAddedAppVersion(userContext)) {
                    return null;
                }
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                List<UserSubscriptionEntry> subscriptionEntries = smsClient.searchSubscriptions(userId);
                if (subscriptionEntries != null && subscriptionEntries.getFirst() != null) {
                    Optional<UserSubscriptionEntry> activeSubscriptionOpt = subscriptionEntries.stream().filter(s -> s.getSubscriptionId().equals(activePackResponse.getBookingId())).findFirst();
                    if (activeSubscriptionOpt.isPresent()) {
                        UserSubscriptionEntry subscriptionEntry = activeSubscriptionOpt.get();
                        Date nuxCompletionDate = subscriptionEntry.getOnboardingStatusUpdateDate();
                        if (nuxCompletionDate != null) {
                            Date npsDismissDate = null;
                            String npsDismissAttr = "nps_dismiss_date";
                            UserAttributesResponse npsDismissAttrResp = userAttributesClient.getAttributes(userId, npsDismissAttr, getAppTenantFromUserContext(userContext), null);
                            if (npsDismissAttrResp != null && npsDismissAttrResp.getAttributes() != null
                                    && npsDismissAttrResp.getAttributes().containsKey(npsDismissAttr)
                                    && Objects.nonNull(npsDismissAttrResp.getAttributes().get(npsDismissAttr))) {
                                String response = String.valueOf(npsDismissAttrResp.getAttributes().get(npsDismissAttr));
                                if (response != null) {
                                    long timestamp = Long.parseLong(response);
                                    npsDismissDate = new Date(timestamp);
                                }
                            }
                            Date npsSubmitDate = null;
                            String npsSubmitAttr = "nps_submit_date";
                            UserAttributesResponse npsSubmitAttrResp = userAttributesClient.getAttributes(userId, npsSubmitAttr, getAppTenantFromUserContext(userContext), null);
                            if (npsSubmitAttrResp != null && npsSubmitAttrResp.getAttributes() != null
                                    && npsSubmitAttrResp.getAttributes().containsKey(npsSubmitAttr)
                                    && Objects.nonNull(npsSubmitAttrResp.getAttributes().get(npsSubmitAttr))) {
                                String response = String.valueOf(npsSubmitAttrResp.getAttributes().get(npsSubmitAttr));
                                if (response != null) {
                                    long timestamp = Long.parseLong(response);
                                    npsSubmitDate = new Date(timestamp);
                                }
                            }

                            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
                            Date currentDate = calendar.getTime();
                            long daysSinceNuxCompletion = SfDateUtils.getDifferenceDays(nuxCompletionDate, currentDate);
                            Long daysSinceLastNpsDismissal = npsDismissDate != null ? SfDateUtils.getDifferenceDays(npsDismissDate, currentDate) : null;
                            long milestoneForWhichLastSubmitted =  npsSubmitDate != null ? SfDateUtils.getDifferenceDays(nuxCompletionDate, npsSubmitDate) : 0;

                            NPSModalData npsModalData = new NPSModalData();
                            npsModalData.setTitle("How likely are you to recommend Sugarfit to a friend or colleague?");
                            npsModalData.setSubTitle("You can rate between 0 and 10");
                            npsModalData.setSkipIntervalInDays(30d);

                            if (daysSinceNuxCompletion > 300) {
                                if ((daysSinceNuxCompletion - 300) % 30 == 0 || (npsDismissDate != null && SfDateUtils.getDifferenceDays(npsDismissDate, currentDate) >= 15)) {
                                    return npsModalData;
                                }
                            }

                            int[] subscriptionMilestones = {300, 270, 180, 90, 45, 15};
                            for (int subscriptionMilestone : subscriptionMilestones) {
                                if (daysSinceNuxCompletion < subscriptionMilestone) {
                                    continue;
                                }
                                boolean npsNotSubmittedForMilestone = milestoneForWhichLastSubmitted < subscriptionMilestone;
                                boolean has15daysPassedAfterSkipping =  Objects.isNull(daysSinceLastNpsDismissal) || daysSinceLastNpsDismissal >= 15;
                                if (npsNotSubmittedForMilestone && has15daysPassedAfterSkipping){
                                    return npsModalData;
                                }
                            }
                        }
                    }
                }

            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getNPSDataFuture", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

}
