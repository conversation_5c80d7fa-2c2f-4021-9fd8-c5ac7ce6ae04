package com.curefit.cfapi.view.viewbuilders.trainerled;

import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import com.curefit.cfapi.pojo.util.ActionUtil;
import com.curefit.cfapi.view.viewmodels.BaseView;
import com.curefit.cfapi.widgets.common.tabbedview.TabbedViewWidget;
import com.curefit.cfapi.widgets.common.tabbedview.Tab;
import com.curefit.cfapi.widgets.trainerled.*;
import com.curefit.diyfs.pojo.intl.Trainer;
import com.curefit.diyfs.pojo.intl.AssetDescription;


@Component
public class TrainerDetailsViewBuilder {
    public BaseView buildView(
            Trainer trainer,
            Boolean isBookmarked,
            Boolean isGuest
    ) {
        var trainerId = trainer.getInstructorId();
        var trainerName = trainer.getName();
        var trainerDetails = new BaseView();
        var trainerProfileWidget = new TrainerProfileWidget(trainer, isBookmarked, isGuest);
        trainerDetails.addWidget(trainerProfileWidget);
        var tabbedViewWidget = new TabbedViewWidget();
        var overviewTab = new Tab("overview", "Overview", null);
        var aboutDescriptionWidget = new DescriptionWidget("About " + trainerName, trainer.getProfileInfo());
        overviewTab.addWidget(aboutDescriptionWidget);
        var accomplishments = "- " + trainer
                .getOtherDetails()
                .getExperiences()
                .stream()
                .map(AssetDescription::getDescription)
                .collect(Collectors.joining("\n- "));
        var accomplishmentsDescriptionWidget = new DescriptionWidget("Accomplishments", accomplishments);
        if (!trainer.getOtherDetails().getExperiences().isEmpty()) {
            overviewTab.addWidget(accomplishmentsDescriptionWidget);
        }
        var trainerCertificationWidget = new TrainerCertificationWidget();
        for (var certification: trainer.getOtherDetails().getCertifications()) {
            trainerCertificationWidget.addCertification(
                    certification.getUrl(),
                    certification.getDescription()
            );
        }
        overviewTab.addWidget(trainerCertificationWidget);
        var testimonialWidget = new TestimonialWidget("Testimonials");
        for (var testimonial: trainer.getTestimonials()) {
            testimonialWidget.addTestimonial(
                    testimonial.getBy(),
                    testimonial.getDisplayImageUrl(),
                    testimonial.getContent()
            );
        }
        if (!trainer.getTestimonials().isEmpty()) {
            overviewTab.addWidget(testimonialWidget);
        }
        var workoutsTab = new Tab(
                "workouts",
                "Workouts",
                "/trainer/workouts?id=" + trainerId
        );
        tabbedViewWidget.addTab(overviewTab);
        tabbedViewWidget.addTab(workoutsTab);

        trainerDetails.addWidget(tabbedViewWidget);

        if (!isBookmarked) {
            trainerDetails.addAction(ActionUtil.getTrainerBookmarkAction(trainerId, trainerName, isBookmarked, isGuest));
        }

        return trainerDetails;
    }
}
