package com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.view.viewmodels.chroniccare.RenewSubscriptionPageView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
public class DigiScorePageView {
    String pageTitle;
    List<BaseWidgetNonVM> widgets = new ArrayList<>();

    public DigiScorePageView() {

    }

    public void addWidget(BaseWidgetNonVM widget) {
        widgets.add(widget);
    }
}
