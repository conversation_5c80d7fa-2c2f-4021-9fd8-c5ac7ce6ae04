package com.curefit.cfapi.view.viewbuilders.chroniccare.blucon;

import com.curefit.cfapi.dto.sugarfit.SfCgmDeviceStatus;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.view.viewbuilders.chroniccare.SfHomePageViewBuilder;
import com.curefit.cfapi.view.viewmodels.chroniccare.blucon.SfBluconDetailsPageView;
import com.curefit.cfapi.widgets.chroniccare.UfExperimentEmptyWidget;
import com.curefit.cfapi.widgets.chroniccare.cgm.SfBluconDetailsWidget;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.sugarfit.chs.enums.DeviceStatus;
import com.sugarfit.chs.pojo.CgmOnboardingStatusResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class SfBluconDetailsPageBuilder {

    final ChronicCareServiceHelper chronicCareServiceHelper;
    final ExceptionReportingService exceptionReportingService;

    public SfBluconDetailsPageView buildView(UserContext userContext) {
        SfBluconDetailsPageView pageView = new SfBluconDetailsPageView();
        try {
            CompletableFuture<CgmOnboardingStatusResponse> cgmOnboardingStatusFuture = chronicCareServiceHelper.
                    getCgmOnboardingStatusFuture(Long.valueOf(userContext.getUserProfile().getUserId()), AppUtil.getAppTenantFromUserContext(userContext));
            CgmOnboardingStatusResponse cgmOnboardingStatusResponse = cgmOnboardingStatusFuture.get();
            if (cgmOnboardingStatusResponse != null) {
                boolean isOnBlucon = false;
                if (Objects.nonNull(cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse())) {
                    isOnBlucon = cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon();
                }
                if (isOnBlucon) {
                    SfCgmDeviceStatus sfCgmDeviceStatus = chronicCareServiceHelper.getCgmDeviceStatus(userContext, cgmOnboardingStatusResponse, null);
                    if (sfCgmDeviceStatus.getIsOnGoingDevice()
                            && Objects.nonNull(cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse())
                            && Objects.nonNull(cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse().getBluconDevice())
                            && cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse().getBluconDevice().getStatus() == DeviceStatus.BCN_DELIVERED) {
                        SfBluconDetailsWidget bluconDetailsWidget = getBluconDetailsWidget(userContext, cgmOnboardingStatusResponse);
                        pageView.addWidget(bluconDetailsWidget);
                        return pageView;
                    }
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        UfExperimentEmptyWidget emptyWidget = new UfExperimentEmptyWidget();
        emptyWidget.setTitle("No reading device");
        emptyWidget.setSubTitle("You don't have any reading device(Blucon) with you currently");
        pageView.addWidget(emptyWidget);
        return pageView;
    }

    private SfBluconDetailsWidget getBluconDetailsWidget(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus) throws ResourceNotFoundException {
        String os = userContext.getSessionInfo().getOsName();
        String loginDeeplink = "";
        if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink())) {
            loginDeeplink = cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink().get(os);
        }
        String passKey = "";
        if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice())) {
            passKey = cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice().getHardwareId();
        }

        SfBluconDetailsWidget bluconDetailsWidget = new SfBluconDetailsWidget();
        SfBluconDetailsWidget.Steps steps = bluconDetailsWidget.getSteps();

        SfBluconDetailsWidget.Download download = steps.getDownload();
        download.setAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).title("Download App")
                .url(os.equalsIgnoreCase("android") ? "https://play.google.com/store/apps/details?id=com.ambrosia.linkblucon"
                        : "https://apps.apple.com/us/app/linkblucon/id1200239770").build());

        SfBluconDetailsWidget.Registration registration = steps.getRegistration();
        SfBluconDetailsWidget.RegdSteps regdSteps = registration.getSteps();
        SfBluconDetailsWidget.Passkey passkey = regdSteps.getPasskey();
        passkey.setPasskey(passKey);
        SfBluconDetailsWidget.Signin signin = regdSteps.getSignin();
        signin.setAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).title("Sign-in").url(loginDeeplink).build());
        regdSteps.setPasskey(passkey);
        regdSteps.setSignin(signin);
        registration.setSteps(regdSteps);

        steps.setDownload(download);
        steps.setRegistration(registration);
        bluconDetailsWidget.setSteps(steps);
        return bluconDetailsWidget;
    }

}
