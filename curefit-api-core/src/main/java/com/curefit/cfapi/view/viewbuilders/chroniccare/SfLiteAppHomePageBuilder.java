package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.albus.common.BundleSellableProduct;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.chroniccare.sfliteapp.ModalNames;
import com.curefit.cfapi.service.DeviceService;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.view.viewbuilders.chroniccare.digitalapp.DigiTasksToDoCardsBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.RenewalAlertWidgetBuilderV2;
import com.curefit.cfapi.view.viewmodels.chroniccare.sfliteapp.SfLiteAppHomePageView;
import com.curefit.cfapi.widgets.chroniccare.AutoNavigationToNuxWidget;
import com.curefit.cfapi.widgets.chroniccare.SLHomeSugarLoggingWidget;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.GenericBannerWidget;
import com.curefit.cfapi.widgets.chroniccare.sfliteapp.DownloadSfAppWidget;
import com.curefit.cfapi.widgets.chroniccare.sfliteapp.WebinarCardWidget;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.pojo.UserMetricEntry;
import com.sugarfit.lms.entry.LMSClient;
import com.sugarfit.lms.entry.WebinarSessionEntry;
import com.sugarfit.lms.response.WebinarRecommendedPacks;
import com.sugarfit.lms.response.WebinarSessionFilterResponse;
import com.sugarfit.logging.client.LoggingClient;
import com.sugarfit.sms.client.SMSClient;
import com.sugarfit.sms.response.NUXStatusResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.curefit.cfapi.util.AppUtil.getAppTenantFromUserContext;
import static com.curefit.cfapi.util.DigitalAppUtil.*;
import static com.curefit.cfapi.util.SfHomePageUtil.GLUCOMETER_READING_METRIC;
import static java.util.concurrent.CompletableFuture.supplyAsync;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
@Slf4j
@RequiredArgsConstructor
public class SfLiteAppHomePageBuilder {
    final CHSClient chsClient;
    final SMSClient smsClient;
    final LMSClient lmsClient;
    final SegmentationCacheClient segmentationCacheClient;
    final UserAttributesClient userAttributesClient;
    final AppConfigCache appConfigCache;
    final ServiceInterfaces serviceInterfaces;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final DeviceService deviceService;
    final UserOnboardingService userOnboardingService;
    final ChronicCarePatientService chronicCarePatientService;
    final ExceptionReportingService exceptionReportingService;
    final SfHomePageViewBuilder sfHomePageViewBuilderV1;
    final SfLiveSessionWidgetBuilder sfLiveSessionWidgetBuilder;
    final SugarFitBlogWidgetBuilder sugarFitBlogWidgetBuilder;
    final SfHomeHeaderWidgetBuilder sfHomeHeaderWidgetBuilder;
    final RenewalAlertWidgetBuilderV2 renewalAlertWidgetBuilderV2;
    final SfKickstartJourneyWidgetV2Builder sfKickstartJourneyWidgetV2Builder;
    final RenewSubscriptionPageViewBuilder renewSubscriptionPageViewBuilder;
    @Qualifier("cfApiRedisKeyValueStore")
    final KeyValueStore cfApiRedisKeyValueStore;
    final DigiTasksToDoCardsBuilder digiTasksToDoCardsBuilder;
    final LoggingClient loggingClient;

    private static final String SUGARFIT_WEBINAR_2 = "SUGARFIT_WEBINAR_2";

    public SfLiteAppHomePageView buildView(UserContext userContext) throws Exception {
        SfLiteAppHomePageView result = new SfLiteAppHomePageView();

        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            String userIdString = userContext.getUserProfile().getUserId();
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);

            CompletableFuture<ActivePackResponse> activePackResponseFuture = userOnboardingService.getSugarFitActivePackResponseFuture(userIdString);
            ActivePackResponse activePackResponse = activePackResponseFuture.get();

            boolean purchasedWebinarPack = Objects.nonNull(activePackResponse) && Objects.nonNull(activePackResponse.getBundleProduct())
                    && SUGARFIT_WEBINAR_2.equals(activePackResponse.getBundleProduct().getProductCode());

            NUXStatusResponse nuxStatus = smsClient.getNuxStatusSalesApp(userId, true, timeZone);

//            boolean isFreemiumUser = Objects.isNull(nuxStatus) || Boolean.TRUE.equals(nuxStatus.getFreemium());
            boolean isFreeDigital = Objects.nonNull(activePackResponse) && Objects.nonNull(activePackResponse.getBundleProduct())
                    && (SUGARFIT_FREEMIUM.equals(activePackResponse.getBundleProduct().getProductCode())
                    || SUGARFIT_DIGITAL_TRIAL.equals(activePackResponse.getBundleProduct().getProductCode())
                    || SUGARFIT_DIGITAL.equals(activePackResponse.getBundleProduct().getProductCode()));

            // if active pack is there apart from webinar pack we show download our app
            if (Objects.nonNull(activePackResponse) && Objects.nonNull(activePackResponse.getBundleProduct()) && !isFreeDigital && !purchasedWebinarPack) {
                DownloadSfAppWidget downloadSfAppWidget = new DownloadSfAppWidget();
                downloadSfAppWidget.setBannerImgUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/sfapp-download-banner-2025-08-05-11:04.png");
                downloadSfAppWidget.setDownloadAppAction(new Action("https://onelink.to/t8ebvc", "Download Now", ActionType.EXTERNAL_DEEP_LINK));
                result.addWidget(downloadSfAppWidget);
                return result;
            }

            if (nuxStatus != null && !nuxStatus.getNuxCompleted()) {
                AutoNavigationToNuxWidget autoNavigationToNuxWidget = new AutoNavigationToNuxWidget();
                autoNavigationToNuxWidget.setPageName("nuxpage");
                result.addWidget(autoNavigationToNuxWidget);
                return result;
            }

            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            WebinarCardWidget.WebinarCard webinarCard = getWebinarCard(userContext, purchasedWebinarPack, patientDetail);
            if (Objects.nonNull(webinarCard)){
                WebinarCardWidget webinarCardWidget = new WebinarCardWidget();
                webinarCardWidget.setTitle("Join ");
                webinarCardWidget.setBoldTitle("Exclusive LIVE Webinar");
                webinarCardWidget.setWebinarCard(webinarCard);
                result.addWidget(webinarCardWidget);

                Map<String, Object> webinarModal = new HashMap<>();
                webinarModal.put("modalName", ModalNames.WEBINAR_PURCHASE_MODAL);
                webinarModal.put("modalData", new SfLiteAppHomePageView.WebinarPurchaseModalData("Take first step towards Diabetes management", webinarCard));
                Action showWebinarPurchaseModal = Action.builder().actionType(ActionType.SHOW_MODAL).title("Book Webinar").meta(webinarModal).build();
                result.setDisplayWebinarModal(showWebinarPurchaseModal);
            }

            result.addWidget(getSugarLoggingWidget(userContext));
            result.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/Banner1-2025-08-04-08:46.png"));
            result.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/Banner2-2025-08-04-08:47.png"));
            result.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/Banner3-2025-08-04-08:47.png"));
            result.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/Banner4-2025-08-04-08:47.png"));
            result.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/prepuchase_banner5-2025-03-06-18:26.png"));
//            result.setBuySfPackModalAction(getRecommendedPacksModalAction(userContext, patientDetail));
            return result;
        } catch (Exception e) {
            String errorMessage = "Error in fetching homepage";
            exceptionReportingService.reportException(errorMessage, e);
            throw e;
        }
    }

    public SLHomeSugarLoggingWidget getSugarLoggingWidget(UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            String latestGlucose = null;
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            ZoneId userZoneId = timeZone.toZoneId();
            List<String> metricNames = List.of(GLUCOMETER_READING_METRIC);
            Map<String, UserMetricEntry> metricsMap = serviceInterfaces.getChsClient().fetchLatestUserMetricBulk(userId,metricNames,appTenant,timeZone);
            if (metricsMap != null && metricsMap.containsKey(GLUCOMETER_READING_METRIC)) {
                UserMetricEntry entry = metricsMap.get(GLUCOMETER_READING_METRIC);
                if (entry != null) {
                    Date startTime = entry.getStartTime();
                    if (startTime != null) {
                        LocalDate lastUpdatedTime = startTime.toInstant().atZone(userZoneId).toLocalDate();
                        LocalDate today = LocalDate.now(userZoneId);
                        if (lastUpdatedTime.equals(today)) {
                            latestGlucose = entry.getValue();
                        }
                    }
                }
            }
            SLHomeSugarLoggingWidget sugarLoggingWidget = new SLHomeSugarLoggingWidget();
            if (latestGlucose != null) {
                sugarLoggingWidget.setTitle("Your Latest Sugar Reading");
                sugarLoggingWidget.setDescription("");
                sugarLoggingWidget.setSugarValue(Double.valueOf(latestGlucose));
                sugarLoggingWidget.setAction(Action.builder().url("sfcommunity://sugarlogging").actionType(ActionType.NAVIGATION).title("Log Sugar").build());
            } else {
                sugarLoggingWidget.setSugarValue(null);
                sugarLoggingWidget.setTitle("Log your Sugar Reading");
                sugarLoggingWidget.setDescription("We recommend you to log your sugar reading from your glucometer or any other device at least once a day to track your progress.");
                sugarLoggingWidget.setAction(Action.builder().url("sfcommunity://sugarlogging").actionType(ActionType.NAVIGATION).title("Log Sugar").build());
            }
            return sugarLoggingWidget;
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching sugar logging widget", e);
            return null;
        }
    }

    public Action getRecommendedPacksModalAction(UserContext userContext, PatientDetail patientDetail){
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            WebinarRecommendedPacks webinarRecommendedPacks = fetchRecommendedPacksFuture(userId).get();
            List<String> recommendedBundleCodes = Objects.nonNull(webinarRecommendedPacks) ? webinarRecommendedPacks.getProductCodes() : null;

            if (!recommendedBundleCodes.isEmpty()) {
                List<BundleSellableProduct> bundleSellableProducts = chronicCareServiceHelper.getBundleProductsByCodes(recommendedBundleCodes);
                Map<String, Object> packPurchaseModal = new HashMap<>();
                packPurchaseModal.put("modalName", ModalNames.PACK_PURCHASE_MODAL);
                packPurchaseModal.put("modalData", new SfLiteAppHomePageView.PackPurchaseModalData("What's Included",
                        renewSubscriptionPageViewBuilder.buildRecommendedPacks(bundleSellableProducts, userContext , patientDetail, true)));
                return Action.builder().actionType(ActionType.SHOW_MODAL).title("Start Today").meta(packPurchaseModal).build();
            }

        } catch (Exception e) {
            exceptionReportingService.reportException("Error in sf lite getRecommendedPacksModalAction", e);
        }
        return null;
    }

    public CompletableFuture<WebinarRecommendedPacks> fetchRecommendedPacksFuture(Long userId) {
        return supplyAsync(() -> {
            try {
                return lmsClient.fetchRecommendedPacks(userId);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public boolean isTenMinutesToLive(Date startTime) {
        return Objects.nonNull(startTime) && startTime.getTime() - System.currentTimeMillis() <= 10 * 60 * 1000 && startTime.getTime() - System.currentTimeMillis() > 0;
    }

    public boolean isLive(Date startTime, Integer durationInMins) {
        if (startTime == null || durationInMins == null) {
            return false;
        }

        long now = System.currentTimeMillis();
        long startMillis = startTime.getTime();
        long endMillis = startMillis + durationInMins * 60 * 1000;

        return now >= startMillis && now <= endMillis;
    }

    public WebinarCardWidget.WebinarCard getWebinarCard(UserContext userContext, boolean purchasedWebinarPack, PatientDetail patientDetail){
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());

            WebinarSessionFilterResponse webinarSessionFilterResponse = getWebinarSessionsFuture(userId).get();
            WebinarSessionEntry webinarSessionEntry = Objects.nonNull(webinarSessionFilterResponse) ? webinarSessionFilterResponse.getNextWebinarSession() : null;

            if (Objects.nonNull(webinarSessionEntry)){
                WebinarCardWidget.WebinarCard webinarCard = new WebinarCardWidget.WebinarCard();
                webinarCard.setWebinarBooked(purchasedWebinarPack);
                webinarCard.setWebinarId(webinarSessionEntry.getWebinarId());
                webinarCard.setWebinarSessionId(webinarSessionEntry.getId());
                webinarCard.setWebinarStartTime(webinarSessionEntry.getStartTime());
                webinarCard.setWebinarDurationInMins(webinarSessionEntry.getDuration());

                String  webinarCreativeUrl = "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/webinar-image%20(1)-2025-08-04-01:47.png", webinarName = "Diabetes Reversal Webinar",
                        webinarHost = "Shivtosh", webinarHostDesignation = "Co-founder Sugarfit", webinarUrl =  "" ;


                if (webinarSessionEntry.getMetadata().containsKey("webinarCreativeUrl")) {
                    webinarCreativeUrl = (String) webinarSessionEntry.getMetadata().get("webinarCreativeUrl");
                }

                if (webinarSessionEntry.getMetadata().containsKey("webinarName")) {
                    webinarName = (String) webinarSessionEntry.getMetadata().get("webinarName");
                }

                if (Objects.nonNull(webinarSessionEntry.getHost())) {
                    webinarHost = webinarSessionEntry.getHost();
                }

                if (webinarSessionEntry.getMetadata().containsKey("designation")) {
                    webinarHostDesignation = (String) webinarSessionEntry.getMetadata().get("designation");
                }

                if (webinarSessionEntry.getMetadata().containsKey("joinUrl")) {
                    webinarUrl = (String) webinarSessionEntry.getMetadata().get("joinUrl");
                }

                webinarCard.setImage(webinarCreativeUrl);
                webinarCard.setWebinarTitle(webinarName);
                webinarCard.setHostName(String.format("by %s • %s", webinarHost, webinarHostDesignation));

                Action primaryAction = new Action(webinarUrl, "Starting Soon", ActionType.NAVIGATION);
                Action.builder().actionType(ActionType.SHOW_ALERT_MODAL).title("Starting Soon").isEnabled(false).build();

                if (isTenMinutesToLive(webinarSessionEntry.getStartTime()) || isLive(webinarSessionEntry.getStartTime(), webinarSessionEntry.getDuration())){
                    primaryAction = new Action(webinarUrl, purchasedWebinarPack ? "Join Now": "Join FREE Webinar", ActionType.EXTERNAL_DEEP_LINK);
                    primaryAction.setIsEnabled(true);
                } else if (!purchasedWebinarPack) {
//                    String purchaseUrl = String.format("sfcommunity://carecartcheckout?patientId=%s&productCode=%s&productId=%s&title=Checkout",
//                            patientDetail.getId(), SUGARFIT_WEBINAR_2, SUGARFIT_WEBINAR_2);
//                    primaryAction = new Action(purchaseUrl, "Book Webinar at just ₹49", ActionType.NAVIGATION);
//                    primaryAction.setIsEnabled(true);
                    primaryAction = Action.builder().actionType(ActionType.SHOW_ALERT_MODAL).title("Starting Soon").isEnabled(false).build();
                }
                webinarCard.setPrimaryAction(primaryAction);
                return webinarCard;
            }

            return null;
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in sf lite getRecommendedPacksModalAction", e);
        }
        return null;
    }

    public CompletableFuture<WebinarSessionFilterResponse> getWebinarSessionsFuture(Long userId) {
        return supplyAsync(() -> {
            try {
                return lmsClient.filterWebinarSessions(userId);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }
}
