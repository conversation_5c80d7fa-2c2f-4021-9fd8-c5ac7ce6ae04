package com.curefit.cfapi.view.viewbuilders;

import com.curefit.base.enums.UserAgent;
import com.curefit.catalogv1.services.spi.ICatalogueService;
import com.curefit.center.dtos.CenterEntry;
import com.curefit.cfapi.model.internal.common.BreadCrumb;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.listingpages.VerticalMapping;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.ActionUtil;
import com.curefit.cfapi.util.CultUtil;
import com.curefit.cfapi.util.FitnessCenterUtil;
import com.curefit.cfapi.view.viewmodels.listingpages.ListingCardView;
import com.curefit.cfapi.view.viewmodels.listingpages.ListingPageView;
import com.curefit.cfapi.widgets.digital.SeoParams;
import com.curefit.cfapi.widgets.lisitngpages.ListingPageCardWidget;
import com.curefit.cfapi.widgets.lisitngpages.ListingPageDescriptionWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.cult.models.Address;
import com.curefit.cult.models.CultCenter;
import com.curefit.gymfit.client.GymfitClient;
import com.curefit.gymfit.dtos.Media;
import com.curefit.gymfit.models.Center;
import com.curefit.product.models.Product;
import com.curefit.product.models.diy.DIYPack;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
public class ListingPageViewBuilder {
    ICatalogueService catalogueService;
    GymfitClient gymfitClient;
    ServiceInterfaces serviceInterfaces;


    @Autowired
    public ListingPageViewBuilder(ICatalogueService service, GymfitClient gymfitClient, ServiceInterfaces serviceInterfaces) {
        this.catalogueService = service;
        this.gymfitClient = gymfitClient;
        this.serviceInterfaces = serviceInterfaces;
    }
    public ListingPageView buildView(String title, String description, String category, String subCategory, List<String> tags, UserAgent agent) throws BaseException {
        ListingPageView listingPage = new ListingPageView();
        ListingPageDescriptionWidget descriptionWidget = new ListingPageDescriptionWidget(title, description);
        List<ListingCardView> listingCards = this.getListingCards(tags, category, subCategory, agent);
        ListingPageCardWidget cardWidget = new ListingPageCardWidget(listingCards, category, subCategory, agent);
        listingPage.addWidget(descriptionWidget);
        listingPage.setBreadcrumbs(this.getBreadCrumbs(title, category, subCategory));
        listingPage.addWidget(cardWidget);
        return listingPage;
    }

    private ArrayList<BreadCrumb> getBreadCrumbs(String pageTitle, String category, String subCategory) {
        Map<String, VerticalMapping> verticalMappingMap = Map.of(
        "cult", new VerticalMapping("Cult", "/cult"),
        "live", new VerticalMapping("Live", "/live"),
                "center", new VerticalMapping("Cult Centers", "/cult/gym"),
        "centers", new VerticalMapping("Cult Centers", "/cult/gym"),
        "cultpass", new VerticalMapping("Cult Pass", "/cult/cult-pass"),
        "fitness", new VerticalMapping("Cult", "/live/fitness"),
        "mindfulness", new VerticalMapping("Cult", "/live/mindfulness")
        );
        ArrayList<BreadCrumb> breadCrumbs = new ArrayList<>();
        breadCrumbs.add(new BreadCrumb("Home", "/"));
        VerticalMapping categoryInfo = verticalMappingMap.get(category);
        breadCrumbs.add(new BreadCrumb(categoryInfo.getName(), categoryInfo.getLink()));
        VerticalMapping subCategoryInfo = verticalMappingMap.get(subCategory);
        breadCrumbs.add(new BreadCrumb(subCategoryInfo.getName(), subCategoryInfo.getLink()));
        breadCrumbs.add(new BreadCrumb(pageTitle, null));
        return  breadCrumbs;
    }

    public List<ListingCardView> getListingCards(List<String> tags, String category, String subCategory, UserAgent userAgent) throws BaseException {
        List<ListingCardView> cards = new ArrayList<>();
        switch (category) {
            case "cult":
                switch (subCategory) {
                    case "centers":
                    case "center":
                        List<CultCenter> centers = this.catalogueService.getCultCenters(tags);
                        centers.forEach(center -> {
                            try {
                                String centerServiceId = center.getCenterServiceId();
                                Long centerServiceIdLong = Long.parseLong(centerServiceId);
                                
                                CenterEntry centerEntry = this.serviceInterfaces.centerService.getCenterDetails(centerServiceIdLong, false, null, null).get();
                                
                                boolean isHybrid = CultUtil.isHybridGymCenter(centerEntry);
                                boolean isOperational = FitnessCenterUtil.isCenterOperational(centerEntry);
                                boolean isCenterActive = FitnessCenterUtil.isCenterActive(centerEntry);

                                if (isHybrid || !isOperational || !isCenterActive) {
                                    return;
                                }
                                
                                SeoParams seoParams = new SeoParams();
                                seoParams.setProductName(center.getName());
                                seoParams.setLocality(center.getLocality());
                                Action action = ActionUtil.getCultCenterBrowseAction(String.valueOf(center.getId()), null, seoParams, userAgent);
                                action.setTitle("EXPLORE");
                                
                                ArrayList<String> imageUrls = FitnessCenterUtil.getCardItemImageUrl(centerEntry);
                                String subTitle = this.getCultCenterAddress(center.getAddress());
                                
                                ListingCardView cardView = new ListingCardView(center.getName(), subTitle, action, imageUrls, new ArrayList<>());
                                cards.add(cardView);
                                
                            } catch (Exception err) {
                                serviceInterfaces.exceptionReportingService.reportException("Error processing gym while building page for center: ", err);
                            }
                        });
                        break;
                    case "cultpass":
                        List<Center> gyms = (List<Center>) this.gymfitClient.centerService().getCenters(tags, true);
                        gyms.forEach(gym -> {
                            String subTitle = this.getGymCenterAddress(gym.getAddress());
                            Action action = ActionUtil.getGymCenterBrowseAction(gym, userAgent);
                            action.setTitle("EXPLORE");
                            ArrayList<String> imageUrls = new ArrayList<>();
                            ArrayList<String> videoUrls = new ArrayList<>();
                            if (gym.getCenterMedia() != null && gym.getCenterMedia().getHeroMedia() != null) {
                                for (Media media : gym.getCenterMedia().getHeroMedia()) {
                                    if (media.getType() == Media.MediaType.VIDEO) {
                                        videoUrls.add("https://cdn-media.cure.fit" + media.getMediaUrl());
                                    } else {
                                        imageUrls.add(media.getMediaUrl());
                                    }

                                }
                            }
                            cards.add(new ListingCardView(gym.getName(), subTitle, action, imageUrls, videoUrls));
                        });
                        break;
                }
            case "live":
                switch (subCategory) {
                    case "fitness":
                    case "mindfulness":
                        List<Product> products = this.catalogueService.getProducts(tags);
                        products.forEach(product -> {
                            if (product == null) {
                                return;
                            }
                            String url = ActionUtil.mindDiyWebPack((DIYPack) product);
                            if (subCategory.equals("fitness")) {
                                url = ActionUtil.cultDiyWebPack((DIYPack) product);
                            }
                            Action action = new Action(url, ActionType.NAVIGATION);
                            action.setTitle("EXPLORE");
                            String imageUrl = ((DIYPack) product).getImageDetails().getHeroImage();
                            List<String> imageUrls = new ArrayList<>();
                            imageUrls.add(imageUrl);
                            List<String> videoUrls =  new ArrayList<>();
                            String contentId = ((DIYPack) product).getPackIntroContentId();
                            if (contentId != null) {
                                String introUrl = "https://cdn-media.cure.fit/video/" + contentId + ".mp4";
                                videoUrls.add(introUrl);
                            }
                            cards.add(new ListingCardView(product.getTitle(), product.getSubTitle(), action, imageUrls, videoUrls));
                        });
                    break;
                }
        }
        return cards;
    }

    private String getCultCenterAddress(Address address) {

        String addressString = address.getAddressLine1() + System.lineSeparator();
        if (address.getDisplayCityName() != null) {
            addressString += address.getDisplayCityName();
        } else if (address.getCity() != null){
            addressString += address.getCity().getName();
        }
        if (address.getState() != null) {
            addressString += ", " + address.getState();
        }
        if (address.getPinCode() != null) {
            addressString += " " + address.getPinCode();
        }
        return addressString;
    }

    private String getGymCenterAddress(com.curefit.gymfit.models.Address address) {

        String city = address.getCityId();
        if (address.getCity() != null) {
            city = address.getCity().getName();
        }
        String state = "";
        if (address.getCity() != null && address.getCity().getState() != null) {
            state = address.getCity().getState();
        }
        String addressLine1 = null;
        if (address.getAddressLine1() != null) {
            addressLine1 = address.getAddressLine1();
        }
        String addressLine2 = null;
        if (address.getAddressLine2() != null) {
            addressLine2 = address.getAddressLine2();
        }
        String addressLine = "";
        if (addressLine1 != null && addressLine2 != null) {
            addressLine = addressLine1 + ", " + addressLine2;
        } else if (addressLine1 != null) {
            addressLine = addressLine1;
        } else {
            addressLine = addressLine2;
        }
        String addressText = addressLine + System.lineSeparator() + city + ", " + state + " " + address.getPincode();
        return addressText;
    }
}
