package com.curefit.cfapi.view.viewbuilders.transform;

import com.curefit.cfapi.builder.vm.BuildWidgetResponse;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.PageContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.TransformUtil;
import com.curefit.cfapi.view.viewmodels.transform.MealPlanDataV2;
import com.curefit.cfapi.view.viewmodels.transform.MealPlanView;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.banner.BannerCarouselWidget;
import com.curefit.cfapi.widgets.transform.CalorieBreakdownWidget;
import com.curefit.cfapi.widgets.transform.MealDetailsWidget;
import com.curefit.cfapi.widgets.transform.MealDetailsWidgetV2;
import com.curefit.cfapi.widgets.transform.MealPlanEmptyStateWidget;
import com.curefit.mealplanner.common.enums.UserMealPlanStatus;
import com.curefit.mealplanner.common.pojo.DayMealPlanDetail;
import com.curefit.mealplanner.common.pojo.MealDetail;
import com.curefit.mealplanner.common.pojo.Tag;
import com.curefit.mealplanner.common.pojo.UserMealPlanPojo;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.text.CaseUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class MealPlanViewBuilder {
    public MealPlanView buildView(UserContext userContext, ServiceInterfaces serviceInterfaces, String subCategoryCode) throws Exception {
        MealPlanView mealPlanView = new MealPlanView();
        try {
            UserMealPlanPojo response = serviceInterfaces.mealPlannerClient.getUserMealPlan(Long.parseLong(userContext.getUserProfile().getUserId()), UserMealPlanStatus.PUBLISHED);
            if (AppUtil.isNewMealPlanSupported(userContext)) {
                mealPlanView = getMealPlanNewView(response, serviceInterfaces, userContext);
                mealPlanView.setAction(TransformUtil.getMealPlanModalAction(response.getDoList(), response.getDontList()));
            } else {
                mealPlanView.setPageTitle("Nutrition");
                List<DayMealPlanDetail> dayPlans = response.getDayPlans();
                LocalDate firstDate = Instant.ofEpochMilli(response.getCreatedAt()).atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate lastDate = firstDate.plusDays(14);
                LocalDate currentDate = LocalDate.now();
                int daysFromCurrentToLast = Period.between(currentDate, lastDate).getDays();
                if (daysFromCurrentToLast > 0) {
                    for (int dateCount = 0; dateCount < daysFromCurrentToLast; dateCount++) {
                        MealDetailsWidget mealWidget = new MealDetailsWidget();
                        LocalDate date = currentDate.plusDays(dateCount);
                        if (dateCount >= 7) {
                            for (int days = 0; days < 7; days++) {
                                if (dayPlans.get(days).getDayOfWeek() == date.getDayOfWeek()) {
                                    mealPlanView.addWidgetsWithDates(mealWidget.buildView(dayPlans.get(days), userContext), date.toString());
                                }
                            }
                        } else {
                            for (DayMealPlanDetail dayPlan : dayPlans) {
                                if (dayPlan.getDayOfWeek() == date.getDayOfWeek()) {
                                    mealPlanView.addWidgetsWithDates(mealWidget.buildView(dayPlan, userContext), date.toString());
                                }
                            }
                        }
                    }
                    CalorieBreakdownWidget calorieWidget = new CalorieBreakdownWidget();
                    mealPlanView.addWidgets(calorieWidget.buildView(serviceInterfaces, userContext, response));
                } else {
                    MealPlanEmptyStateWidget emptyStateWidget = new MealPlanEmptyStateWidget();
                    String title = "YOUR MEAL PLAN IS EXPIRED";
                    mealPlanView.addWidgets(emptyStateWidget.buildView(serviceInterfaces, userContext, title, subCategoryCode));
                }
                mealPlanView.setAction("curefit://eatrecipe", "EXPLORE RECIPES");
            }
        } catch (Exception e) {
            MealPlanEmptyStateWidget emptyStateWidget = new MealPlanEmptyStateWidget();
            String title = "YOUR MEAL PLAN IS YET TO BE PREPARED";
            mealPlanView.addWidgets(emptyStateWidget.buildView(serviceInterfaces, userContext, title, subCategoryCode));
        }

        return mealPlanView;
    }

    private MealPlanView getMealPlanNewView(UserMealPlanPojo response, ServiceInterfaces serviceInterfaces, UserContext userContext) {
        MealPlanView mealPlanView = new MealPlanView();
        mealPlanView.setPageTitle("Meal Plan");
        mealPlanView.setPageSubTitle(getPageSubTitle(response));
        Map<String, List<MealPlanDataV2>> result = new HashMap<>();
        Map<Integer, List<String>> mealNumberMealMap = mealNumberMealMap();

        for (DayMealPlanDetail dayMealPlanDetail : response.getDayPlans()) {
            for (MealDetail mealDetail : dayMealPlanDetail.getMealDetails()) {
                if (!CollectionUtils.isEmpty(mealDetail.getMealEntry().getComponents())) {
                    String mealName = mealNumberMealMap.get(mealDetail.getMealNumber()).get(1);
                    String id = dayMealPlanDetail.getDayOfWeek().name() + "_" + mealName;
                    MealPlanDataV2 mealPlanDataV2 = new MealPlanDataV2(convertComponentToMealComponent(mealDetail.getMealEntry().getComponents()), id);
                    result.computeIfAbsent(mealName, m -> new ArrayList<>()).add(mealPlanDataV2);
                }
            }
        }

        mealNumberMealMap.keySet().stream().sorted(Comparator.comparing(mealNumber -> mealNumberMealMap.get(mealNumber).get(0))).forEach(mealNumber -> {
            String mealName = mealNumberMealMap.get(mealNumber).get(1);
            if (result.containsKey(mealName)) {
                PageContext pageContext = new PageContext();
                WidgetContext widgetContext = new WidgetContext(pageContext);
                List<BaseWidget> finalWidgets = new ArrayList<>();
                MealDetailsWidgetV2 mealWidget = new MealDetailsWidgetV2();
                try {
                    List<BaseWidget> brandWidgets = serviceInterfaces.getWidgetBuilder()
                            .buildWidget(Constants.TRANSFORM_SPONSOR_WIDGET_ID, userContext, widgetContext);

                    if (brandWidgets != null && !brandWidgets.isEmpty() && brandWidgets.get(0) != null) {
                        finalWidgets.add(brandWidgets.get(0));
                    }
                } catch (ExecutionException e) {
                    throw new RuntimeException(e);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                List<BaseWidget> mealWidgets = mealWidget.buildView(result.get(mealName), mealName);
                mealWidgets.forEach(mealWidgetInfo -> {
                    finalWidgets.add(mealWidgetInfo);
                });
                mealPlanView.addWidgets(finalWidgets);
                mealPlanView.addMealType(mealName);
            }
        });

        return mealPlanView;
    }

    private String getPageSubTitle(UserMealPlanPojo response) {
        String result = response.getGoal().getDisplayValue();
        try {
            Tag cuisine = response.getTags().stream().filter(tag -> tag.getKey().equals("CUISINE")).findFirst().orElse(null);
            if (cuisine != null) {
                String cuisineValue = Arrays.stream(cuisine.getValue().split("_")).map(value -> CaseUtils.toCamelCase(value, true)).collect(Collectors.joining(" "));
                result = result + " • " + cuisineValue;
            }
            Tag pref = response.getTags().stream().filter(tag -> tag.getKey().equals("MEAL_PREF")).findFirst().orElse(null);
            if (pref != null) {
                String prefValue = Arrays.stream(pref.getValue().split("_")).map(value -> CaseUtils.toCamelCase(value, true)).collect(Collectors.joining(" "));
                result = result + " • " + prefValue;
            }
        } catch (Exception e) {
            log.error("Error while building sub title for mealPlanId: {}", response.getMealPlanId());
        }
        return result;
    }

    private Map<Integer, List<String>> mealNumberMealMap() {
        Map<Integer, List<String>> mealNumberMealMap = new HashMap<>();
        mealNumberMealMap.put(0, List.of("1", "DETOX"));
        mealNumberMealMap.put(1, List.of("2", "BREAKFAST"));
        mealNumberMealMap.put(4, List.of("3", "SNACK 1"));
        mealNumberMealMap.put(2, List.of("4", "LUNCH"));
        mealNumberMealMap.put(5, List.of("5", "SNACK 2"));
        mealNumberMealMap.put(3, List.of("6", "DINNER"));
        return mealNumberMealMap;
    }

    private List<MealPlanDataV2.MealComponent> convertComponentToMealComponent(List<com.curefit.mealplanner.common.pojo.Component> components) {
        Map<String, String> foodGroupToColorMap = foodGroupToColor();
        return components.stream().map(c -> MealPlanDataV2.MealComponent.builder()
                .id(c.getDishId())
                .title(c.getDisplayName())
                .description(c.getUnit() != null ? c.getQuantity().toString().toUpperCase() + " " + c.getUnit() : null)
                .foodGroup(c.getFoodGroup().name())
                .foodGroupColor(foodGroupToColorMap.get(c.getFoodGroup().name()))
                .unitImage("https://curefit-content.s3.ap-south-1.amazonaws.com/transform-coach/meal-plan-resources/app/" + c.getUnit().toLowerCase() + "V2.png")
                .build())
                .collect(Collectors.toList());
    }

    private Map<String, String> foodGroupToColor() {
        Map<String, String> foodGroupToColor = new HashMap<>();
        foodGroupToColor.put("PROTEIN", "#F481A3");
        foodGroupToColor.put("VEG", "#99EDCF");
        foodGroupToColor.put("CARBS", "#DAAC77");
        foodGroupToColor.put("FRUIT", "#F481A3");
        foodGroupToColor.put("FATS", "#E3D8B0");
        foodGroupToColor.put("BEVERAGE", "#8DB9ED");
        return foodGroupToColor;
    }

}