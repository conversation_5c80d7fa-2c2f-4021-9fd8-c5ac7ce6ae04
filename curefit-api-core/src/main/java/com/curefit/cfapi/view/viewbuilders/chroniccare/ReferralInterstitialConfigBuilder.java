package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.DeviceService;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.view.viewmodels.chroniccare.ReferralInterstitialConfig;
import com.curefit.cfapi.view.viewmodels.chroniccare.ReferralInterstitialConfig.ReferralInterstitialData;
import com.curefit.cfapi.view.viewmodels.chroniccare.ReferralInterstitialConfig.ReferralInterstitialType;
import com.curefit.cfapi.view.viewmodels.chroniccare.ReferralInterstitialConfig.UserMetric;
import com.curefit.cfapi.view.viewmodels.chroniccare.ReferralInterstitialConfig.UserMetricType;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.pojo.CGMDeviceInfo;
import com.sugarfit.chs.pojo.UserMetricEntry;
import com.sugarfit.chs.pojo.cgmstat.CgmStat;
import com.sugarfit.chs.pojo.cgmstat.DailySugarStat;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class ReferralInterstitialConfigBuilder {
    public static final double METABOLIC_SCORE_PERCENTAGE_CHANGE_MIN_VALUE = 1;
    public static final double TIME_IN_TARGET_PERCENTAGE_CHANGE_MIN_VALUE = 1;
    public static final double AVG_GLUCOSE_PERCENTAGE_CHANGE_MIN_VALUE = 1;
    public static final double HBA1C_PERCENTAGE_CHANGE_MIN_VALUE = 10;
    public static final Long MILISECONDS_OF_DAY = 24 * 60 * 60 * 1000L;
    private static final String DIAGNOSTIC_COMPLETED_RECENTLY = "sugarfit_diagnostic_completed_in_last_seven_days";
    private static final String SF_CGM_REFERRAL_INTERSTITIAL_ENABLED_USERS = "sugarfit_cgm_referral_interstitial_enabled_users";
    private static final String UF_CGM_REFERRAL_INTERSTITIAL_ENABLED_USERS = "ultrafit_cgm_referral_interstitial_enabled_users";
    private final CHSClient chsClient;
    private final ExceptionReportingService exceptionReportingService;
    private final ChronicCarePatientService chronicCarePatientService;
    private final UserOnboardingService userOnboardingService;
    private final ServiceInterfaces serviceInterfaces;
    private final DeviceService deviceService;
    private final AppConfigCache appConfigCache;

    public ReferralInterstitialConfig buildConfig(UserContext userContext) {
        if (ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
            return null;
        }
        if (!ChronicCareAppUtil.isSalesBannerCarouselEnabled(userContext)) {
            return null;
        }
        ReferralInterstitialConfig config = new ReferralInterstitialConfig();
        String userId = userContext.getUserProfile().getUserId();
        if (userId.equals("90177565") || userId.equals("25834249") || userId.equals("93182981") || userId.equals("93182993")) {
            return null;
        }
        HashMap<String, String> renewalConfig = new HashMap<>();
        try {
            renewalConfig = appConfigCache.getConfig("SF_REFERRAL_CONFIG", new TypeReference<>() {
            }, new HashMap<>());

        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);

        }
        try {
            config.setCgmInterstitialData(getCgmInterstitialData(userContext, renewalConfig));
            config.setDiagnosticInterstitialData(getDiagnosticInterstitialData(userContext, renewalConfig));
            config.setConsultationInterstitialData(getConsultationInterstitialData(userContext, renewalConfig));

            Action referralAction = new Action();
            referralAction.setTitle("REFER NOW");
            referralAction.setUrl("curefit://sfreferralpage");
            referralAction.setActionType(ActionType.NAVIGATION);
            config.setActionButtons(List.of(referralAction));
        } catch (Exception e) {
            log.error("Exception in fetching referral interstitial config", e);
            exceptionReportingService.reportException("Exception in fetching referral interstitial config", e);
            return null;
        }
        return config;
    }

    private ReferralInterstitialData getCgmInterstitialData(UserContext userContext, HashMap<String, String> renewalConfig) {
        String userId = userContext.getUserProfile().getUserId();
        ReferralInterstitialData cgmInterstitialData = new ReferralInterstitialData();
        cgmInterstitialData.setInterstitialType(ReferralInterstitialType.CGM);
        CgmStat cgmStat = getCgmStat(userContext);
        if (isCgmInterstitialEnabledForUser(userContext)) {
            Double metabolicScoreDifference = getMetabolicScorePercentageDifference(userContext, cgmStat);
            Double timeInTargetDifference = getTimeInTargetPercentageDifference(userContext, cgmStat);
            Double avgGlucoseDifference = getAvgGlucosePercentageDifference(userContext, cgmStat);
            List<UserMetric> userMetrics = new ArrayList<>();
            if (metabolicScoreDifference >= METABOLIC_SCORE_PERCENTAGE_CHANGE_MIN_VALUE) {
                userMetrics.add(getMetabolicMetric(metabolicScoreDifference, renewalConfig));
            }
            if (timeInTargetDifference >= TIME_IN_TARGET_PERCENTAGE_CHANGE_MIN_VALUE) {
                userMetrics.add(getTimeInTargetMetric(timeInTargetDifference, renewalConfig));
            }
            if (avgGlucoseDifference >= AVG_GLUCOSE_PERCENTAGE_CHANGE_MIN_VALUE) {
                userMetrics.add(getAvgGlucoseMetric(avgGlucoseDifference, renewalConfig));
            }
            if (CollectionUtils.isNotEmpty(userMetrics)) {
                cgmInterstitialData.setUserMetrics(userMetrics);
                cgmInterstitialData.setBackgroundImageUrl(AppUtil.isUltraFitApp(userContext)
                        ? renewalConfig.getOrDefault("ufReferralCustomInterstitialV2", "image/chroniccare/referral/uf_referral_custom_interstitial_v2.png")
                        : renewalConfig.getOrDefault("sfReferralCustomInterstitialV2", "image/chroniccare/referral/sf_referral_custom_interstitial_v2.png"));
            } else {
                cgmInterstitialData.setBackgroundImageUrl(AppUtil.isUltraFitApp(userContext)
                        ? renewalConfig.getOrDefault("ufReferralGenericInterstitialV1", "image/chroniccare/referral/uf_referral_generic_interstitial_v1.png")
                        : renewalConfig.getOrDefault("sfReferralGenericInterstitialV1", "image/chroniccare/referral/sf_referral_generic_interstitial_v1.png"));
            }

            if (getIsCgmEnded(userContext)) {
                cgmInterstitialData.setSkipDuration(3 * MILISECONDS_OF_DAY);
            } else {
                cgmInterstitialData.setSkipDuration(MILISECONDS_OF_DAY);
            }

            cgmInterstitialData.setCgmEnded(getIsCgmEnded(userContext));
            cgmInterstitialData.setShowInterstitial(true);
        }

        if (CollectionUtils.isNotEmpty(cgmInterstitialData.getUserMetrics())) {
            if (AppUtil.isUltraFitApp(userContext)) {
                cgmInterstitialData.setSubTitle(renewalConfig.getOrDefault("ufSubtitle", "#SHARETHESCIENCE"));
            } else {
                cgmInterstitialData.setSubTitle(renewalConfig.getOrDefault("sfSubtitle", "#SHAREGOODHEALTH"));
            }
        } else {
            if (AppUtil.isUltraFitApp(userContext)) {
                cgmInterstitialData.setSubTitle(renewalConfig.getOrDefault("ufSubtitle", "#SHARETHESCIENCE"));
            } else {
                cgmInterstitialData.setSubTitle(renewalConfig.getOrDefault("sfSubtitle", "#SHARETHELOVE"));
            }
        }

        return cgmInterstitialData;
    }

    private ReferralInterstitialData getDiagnosticInterstitialData(UserContext userContext, HashMap<String, String> renewalConfig) {
        String userId = userContext.getUserProfile().getUserId();
        ReferralInterstitialData diagnosticInterstitialData = new ReferralInterstitialData();
        diagnosticInterstitialData.setInterstitialType(ReferralInterstitialType.DIAGNOSTIC);
        diagnosticInterstitialData.setBackgroundImageUrl(AppUtil.isUltraFitApp(userContext)
                ? renewalConfig.getOrDefault("ufReferralCustomInterstitialV2", "image/chroniccare/referral/uf_referral_custom_interstitial_v2.png")
                : renewalConfig.getOrDefault("sfReferralCustomInterstitialV2", "image/chroniccare/referral/sf_referral_custom_interstitial_v2.png"));

        if (isDiagnosticRecentlyCompletedForUser(userContext)) {
            Double hba1cDifference = getHbA1cPercentageDifference(userContext);
            if (hba1cDifference >= HBA1C_PERCENTAGE_CHANGE_MIN_VALUE) {
                List<UserMetric> userMetrics = new ArrayList<>();
                userMetrics.add(getHB1ACMetric(hba1cDifference, renewalConfig));
                diagnosticInterstitialData.setUserMetrics(userMetrics);
                diagnosticInterstitialData.setShowInterstitial(true);
                diagnosticInterstitialData.setSkipDuration(15 * MILISECONDS_OF_DAY);

                if (AppUtil.isUltraFitApp(userContext)) {
                    diagnosticInterstitialData.setSubTitle(renewalConfig.getOrDefault("ufSubtitle", "#SHARETHESCIENCE"));
                } else {
                    diagnosticInterstitialData.setSubTitle(renewalConfig.getOrDefault("sfSubtitle", "#SHAREGOODHEALTH"));
                }
            }
        }

        return diagnosticInterstitialData;
    }

    private ReferralInterstitialData getConsultationInterstitialData(UserContext userContext, HashMap<String, String> renewalConfig) {
        ReferralInterstitialData consultationInterstitialData = new ReferralInterstitialData();
        consultationInterstitialData.setInterstitialType(ReferralInterstitialType.CONSULTATION);
        consultationInterstitialData.setBackgroundImageUrl(AppUtil.isUltraFitApp(userContext)
                ? renewalConfig.getOrDefault("ufReferralGenericInterstitialV1", "image/chroniccare/referral/uf_referral_generic_interstitial_v1.png")
                : renewalConfig.getOrDefault("sfReferralGenericInterstitialV1", "image/chroniccare/referral/sf_referral_generic_interstitial_v1.png"));
        consultationInterstitialData.setShowInterstitial(true);

        if (AppUtil.isUltraFitApp(userContext)) {
            consultationInterstitialData.setSubTitle(renewalConfig.getOrDefault("ufConsultationSubtitle", "#SHARETHESCIENCE"));
        } else {
            consultationInterstitialData.setSubTitle(renewalConfig.getOrDefault("sfConsultationSubtitle", "#SHARETHELOVE"));
        }

        return consultationInterstitialData;
    }

    private CgmStat getCgmStat(UserContext userContext) {
        CgmStat cgmStat = new CgmStat();
        String cgmDeviceId = "";
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezone = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);

        try {
            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            List<CGMDeviceInfo> deviceInfos = chsClient.fetchAllCgmDeviceInfo(userId, appTenant);

            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                deviceInfos = deviceInfos.stream().filter(cgmDeviceInfo -> {
                    if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) ||
                            "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) ||
                            null == cgmDeviceInfo.getStartedOn()) {
                        return false;
                    }
                    return cgmDeviceInfo.isAtleastOneReadingDone() || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState());
                }).collect(Collectors.toList());

                if (deviceInfos.size() > 0) {
                    cgmDeviceId = deviceInfos.get(deviceInfos.size() - 1).getDeviceId();

                    cgmStat = chsClient.fetchCgmStat(userId, patientDetail.getId(), cgmDeviceId, true,
                            AppUtil.getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezone));
                    cgmStat.setDailySugarStatMap(getSortedStatMap(cgmStat.getDailySugarStatMap(), userContext));
                }
            }
        } catch (Exception e) {
            log.error("Exception in fetchCgmStat", e);
            exceptionReportingService.reportException("Exception in fetchCgmStat", e);
        }

        return cgmStat;
    }

    private boolean getIsCgmEnded(UserContext userContext) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);

        try {
            List<CGMDeviceInfo> deviceInfos = chsClient.fetchAllCgmDeviceInfo(userId, appTenant);

            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                CGMDeviceInfo latestCgmDevice = deviceInfos.get(deviceInfos.size() - 1);

                if (latestCgmDevice.getCurrentState().equals("AGM_FINAL_READING_UPLOADED") ||
                        latestCgmDevice.getCurrentState().equals("AGM_ENDED")) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("Exception in cgm devices", e);
            exceptionReportingService.reportException("Exception in cgm devices", e);
        }
        return false;
    }

    public Map<String, DailySugarStat> getSortedStatMap(Map<String, DailySugarStat> dailySugarStatMap,
                                                        UserContext userContext) {
        if (MapUtils.isEmpty(dailySugarStatMap)) {
            // this is a hack to support logging till first reading
            String tz = userContext.getUserProfile().getTimezone();
            TimeZone timeZone = TimeZone.getTimeZone(tz);
            String todayDate = TimeUtil.formatDateInTimezone(timeZone, new Date(), TimeUtil.DEFAULT_TIME_FORMAT);
            DailySugarStat dailySugarStat = new DailySugarStat();
            dailySugarStat.setDate(todayDate);
            dailySugarStat.setPatientActivityImpactList(Lists.newArrayList());
            dailySugarStat.setUserMetricValues(Lists.newArrayList());
            dailySugarStatMap = Maps.newHashMap();
            dailySugarStatMap.put(todayDate, dailySugarStat);
            return dailySugarStatMap;
        }
        List<String> dayList = Lists.newArrayList(dailySugarStatMap.keySet());
        dayList.sort(Comparator.naturalOrder());
        LinkedHashMap<String, DailySugarStat> dailySugarStatLinkedHashMap = Maps.newLinkedHashMap();
        for (String day : dayList) {
            if (null == dailySugarStatMap.get(day) || CollectionUtils.isEmpty(dailySugarStatMap.get(day).getUserMetricValues())) {
                continue;
            }
            dailySugarStatLinkedHashMap.put(day, dailySugarStatMap.get(day));
        }
        return dailySugarStatLinkedHashMap;
    }

    private Double getMetabolicScorePercentageDifference(UserContext userContext, CgmStat cgmStat) {
        double difference = 0.0;

        if (cgmStat != null && cgmStat.getDailySugarStatMap() != null) {
            List<DailySugarStat> dailySugarStats = Lists.newArrayList(cgmStat.getDailySugarStatMap().values());

            if (dailySugarStats.size() > 0) {
                DailySugarStat endStat = null;
                DailySugarStat startStat = null;
                int currentIndex = 0;
                int size = dailySugarStats.size();
                while (currentIndex < size) {
                    if (dailySugarStats.get(currentIndex).getSugarScore() != null) {
                        endStat = dailySugarStats.get(currentIndex);
                        if (startStat == null) {
                            startStat = dailySugarStats.get(currentIndex);
                        }
                    }
                    currentIndex++;
                }

                if (startStat != null && endStat != null) {
                    double start = startStat.getSugarScore();
                    double end = endStat.getSugarScore();
                    difference = start != 0.0 ? (end - start) * 100 / start : 0.0;
                }
            }
        }

        return difference;
    };

    private Double getTimeInTargetPercentageDifference(UserContext userContext, CgmStat cgmStat) {
        double difference = 0.0;

        if (cgmStat != null && cgmStat.getDailySugarStatMap() != null) {
            List<DailySugarStat> dailySugarStats = Lists.newArrayList(cgmStat.getDailySugarStatMap().values());

            if (dailySugarStats.size() > 0) {
                DailySugarStat endStat = null;
                DailySugarStat startStat = null;
                int currentIndex = 0;
                int size = dailySugarStats.size();
                while (currentIndex < size) {
                    if (dailySugarStats.get(currentIndex).getSugarScore() != null) {
                        endStat = dailySugarStats.get(currentIndex);
                        if (startStat == null) {
                            startStat = dailySugarStats.get(currentIndex);
                        }
                    }
                    currentIndex++;
                }

                if (startStat != null && endStat != null) {
                    double start = 100 - startStat.getTimeOverRangeGlucosePercentage();
                    double end = 100 - endStat.getTimeOverRangeGlucosePercentage();
                    difference = end - start;
                }
            }
        }

        return difference;
    }

    private Double getAvgGlucosePercentageDifference(UserContext userContext, CgmStat cgmStat) {
        double difference = 0.0;

        if (cgmStat != null && cgmStat.getDailySugarStatMap() != null) {
            List<DailySugarStat> dailySugarStats = Lists.newArrayList(cgmStat.getDailySugarStatMap().values());

            if (dailySugarStats.size() > 0) {
                DailySugarStat endStat = null;
                DailySugarStat startStat = null;
                int currentIndex = 0;
                int size = dailySugarStats.size();
                while (currentIndex < size) {
                    if (dailySugarStats.get(currentIndex).getSugarScore() != null) {
                        endStat = dailySugarStats.get(currentIndex);
                        if (startStat == null) {
                            startStat = dailySugarStats.get(currentIndex);
                        }
                    }
                    currentIndex++;
                }

                if (startStat != null && endStat != null) {
                    double start = startStat.getAvgSugarValue();
                    double end = endStat.getAvgSugarValue();
                    difference = start != 0.0 ? (start - end) * 100 / start : 0.0;
                }
            }
        }

        return difference;
    }

    private Double getHbA1cPercentageDifference(UserContext userContext) {
        double difference = 0.0;
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            List<UserMetricEntry> userMetricEntries = chsClient.fetchHbA1C(userId, null, new Date().getTime());
            log.info("for user :: {}, total hba1c readings for user :: {}", userContext.getUserProfile().getUserId(), userMetricEntries.size());
            if (userMetricEntries.size() > 1) {
                userMetricEntries.sort(Comparator.comparingLong(x -> x.getStartTime().getTime()));
                double start = Double.parseDouble(userMetricEntries.get(0).getValue());
                log.info("for user :: {}, Start value :: {}", userContext.getUserProfile().getUserId(), start);
                double end = Double.parseDouble(userMetricEntries.get(userMetricEntries.size() - 1).getValue());
                log.info("for user :: {}, End value :: {}", userContext.getUserProfile().getUserId(), end);
                difference = start != 0.0 ? (start - end) * 100 / start : 0.0;
            }
        } catch (Exception e) {
            String errorMessage = String.format("Error is searching metric :: %s", e.getMessage());
            exceptionReportingService.reportException(errorMessage, e);
            log.error(errorMessage, e);
            return difference;
        }
        return difference;
    }

    private UserMetric getMetabolicMetric(Double metabolicScoreDifference, HashMap<String, String> renewalConfig) {
        UserMetric metabolicScore = new UserMetric();
        metabolicScore.setMetricType(UserMetricType.METABOLIC_SCORE);
        metabolicScore.setDisplayText("in Metabolic Score");
        metabolicScore.setMetricValue(String.format("%.1f", metabolicScoreDifference).concat("%"));
        metabolicScore.setIncreased(true);
        metabolicScore.setIconUrl(renewalConfig.getOrDefault("metabolicScoreIcon", "image/chroniccare/metabolic_score_icon.png"));

        return metabolicScore;
    }

    private UserMetric getHB1ACMetric(Double hba1c, HashMap<String, String> renewalConfig) {
        UserMetric metabolicScore = new UserMetric();
        metabolicScore.setMetricType(UserMetricType.HBA1C);
        metabolicScore.setDisplayText("in HbA1c");
        metabolicScore.setMetricValue(String.format("%.1f", hba1c).concat("%"));
        metabolicScore.setIncreased(false);
        metabolicScore.setIconUrl(renewalConfig.getOrDefault("hba1cIcon", "image/chroniccare/hba1c_icon.png"));

        return metabolicScore;
    }

    private UserMetric getTimeInTargetMetric(Double timeInTarget, HashMap<String, String> renewalConfig) {
        UserMetric metabolicScore = new UserMetric();
        metabolicScore.setMetricType(UserMetricType.TIME_IN_TARGET);
        metabolicScore.setDisplayText("in Time in target");
        metabolicScore.setMetricValue(String.format("%.1f", timeInTarget).concat("%"));
        metabolicScore.setIncreased(true);
        metabolicScore.setIconUrl(renewalConfig.getOrDefault("timeInTargetIcon", "image/chroniccare/time_in_target_icon.png"));

        return metabolicScore;
    }

    private UserMetric getAvgGlucoseMetric(Double avgGlucose, HashMap<String, String> renewalConfig) {
        UserMetric metabolicScore = new UserMetric();
        metabolicScore.setMetricType(UserMetricType.AVG_GLUCOSE);
        metabolicScore.setDisplayText("in Average glucose");
        metabolicScore.setMetricValue(String.format("%.1f", avgGlucose).concat("%"));
        metabolicScore.setIncreased(true);
        metabolicScore.setIconUrl(renewalConfig.getOrDefault("hba1cIcon", "image/chroniccare/hba1c_icon.png"));

        return metabolicScore;
    }

    boolean isDiagnosticRecentlyCompletedForUser(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            return userSegments.contains(DIAGNOSTIC_COMPLETED_RECENTLY);
        } catch (Exception e) {
            String message = String.format("Diagnostic completed in last 7 days user segment error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    boolean isCgmInterstitialEnabledForUser(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            return userSegments.contains(SF_CGM_REFERRAL_INTERSTITIAL_ENABLED_USERS)
                    || userSegments.contains(UF_CGM_REFERRAL_INTERSTITIAL_ENABLED_USERS);
        } catch (Exception e) {
            String message = String.format("cgm referral interstitial enabled user segment error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
            return false;
        }
    }
}
