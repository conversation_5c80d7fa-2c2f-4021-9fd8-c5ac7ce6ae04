package com.curefit.cfapi.view.viewmodels.sprinklr.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;


@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserDetailsRequest {
    @NotEmpty(message = "userId is required")
    String userId;
    Boolean skipMembershipDetails = false;
    Boolean skipGXSessions = false;
    Boolean skipPlaySessions = false;
    Boolean skipGXRevertedNoShows = false;
    Boolean skipPlayRevertedNoShows = false;
    int page = 1;
    int limit = 10;
}
