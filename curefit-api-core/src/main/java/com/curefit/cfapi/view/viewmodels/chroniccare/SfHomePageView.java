package com.curefit.cfapi.view.viewmodels.chroniccare;

import com.curefit.cfapi.dto.sugarfit.DiabeticProfileData;
import com.curefit.cfapi.dto.sugarfit.MegaSaleData;
import com.curefit.cfapi.dto.sugarfit.SfUserCGMConfig;
import com.curefit.cfapi.dto.sugarfit.UnlockFbvExpData;
import com.curefit.cfapi.dto.sugarfit.kickstart.SfKickStartData;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.chroniccare.support.SfSupportTicket;
import com.curefit.cfapi.view.viewmodels.chroniccare.onboarding.CardData;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class SfHomePageView {
    SfKickStartData kickStartData;
    List<BaseWidgetNonVM> widgets;
    ChronicCareUserDetails userDetails;
    List<HomeMenuItem> menuItems;
    SfUserCGMConfig cgmConfiguration;
    BookingModalConfig bookingModalConfig;
    AppUpdateData appUpdateData;
    AppAnnouncementData appAnnouncementData;
    ScratchCardData scratchCardData;
    @JsonProperty("sfNotificationTrayData")
    SfNotificationTrayData sfNotificationTrayData;
    NPSModalData npsModalData;
    CsTicketResolutionNotificationData csTicketResolutionNotificationData;
    MegaSaleData megaSaleData;
    DocPlusData docPlusData;
    DiabeticProfileData diabeticProfileData;
    boolean referralEnabled = false;
    boolean v2Enabled = false;
    boolean packHasExpired = false;

    long pageTTL = 180000L; // to auto sync page data after every 3 min
    long fitnessTTL = 1800000L; // to auto sync fitness data after every 30 min
    boolean showFaceScanFab;
    boolean showRagusAiFab;
    UnlockFbvExpData unlockFbvExpData;

    public SfHomePageView() {
        this.widgets = new ArrayList<>();
    }

    public void addWidget(BaseWidgetNonVM widget) {
        if (widget != null) {
            this.widgets.add(widget);
        }
    }

    public void addWidgets(ArrayList<? extends BaseWidgetNonVM> widgetList) {
        if (!CollectionUtils.isEmpty(widgetList)) {
            widgets.addAll(widgetList.stream().filter(Objects::nonNull).toList());
        }
    }

    public enum MenuItemType {
        COACH,
        DOCTOR,
        PROGRESS,
        THERAPY,
        CUSTOMER_SUPPORT;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HomeMenuItem {
        MenuItemType itemType;
        String displayType;
        String title;
        String imageUrl;
        String instruction;
        Action action;
        String actionTitle;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class BookingModalConfig {
        CardData cardData;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class AppUpdateData {
        Boolean showPopUp = false;
        Boolean isForceUpdate = false;
        Integer skipIntervalInDays = 1;
        String title = "Update Available!";
        String subTitle = "New version of the app is available with following changes:";
        String releaseNotes;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class AppAnnouncementData {
        Integer skipIntervalInDays = 365;
        List<Banner> banners;
        List<Action> actionButtons;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class SfNotificationTrayData {
        Double skipIntervalInSecs = (double) (60 * 60 * 24);
        SfTrayNotificationTypes notificationType;
        boolean dismissDisabled = false;
        String message;
        Action action;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class NPSModalData {
        Double skipIntervalInDays = 30d;
        String title;
        String subTitle;
    }

    public enum SfTrayNotificationTypes {
        EXPLORE_APP,
        DIAGNOSTIC_TEST,
        CGM_INSTALLATION,
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class CsTicketResolutionNotificationData {
        String title = "Your ticket is resolved!";
        String subTitle = "Your ticket has been resolved and we would love to hear your feedback.\nIf you have any further questions or concerns, please do not hesitate to reach out to us.";
        Long conversationId;
        SfSupportTicket ticket;
        Action viewedAction;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class Banner {
        String imageUrl;
        Action action;

        public Banner(String imageUrl, Action action) {
            this.imageUrl = imageUrl;
            this.action = action;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocPlusData {
        String title;
        String subTitle;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class ScratchCardData {
        Integer skipIntervalInDays = 365;
        ScratchCardForegroundData scratchCardForegroundData;
        ScratchCardBackgroundData scratchCardBackgroundData;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class ScratchCardForegroundData {
        String image;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Getter
    @Setter
    @NoArgsConstructor
    public static class ScratchCardBackgroundData {
        String image;
        Action action;

    }
}
