package com.curefit.cfapi.view.viewbuilders.memberships;

import com.cronutils.utils.StringUtils;
import com.curefit.center.client.CenterService;
import com.curefit.center.dtos.CenterEntry;
import com.curefit.center.enums.CenterStatus;
import com.curefit.center.enums.CenterVertical;
import com.curefit.cfapi.builder.vm.BuildWidgetResponse;
import com.curefit.cfapi.model.internal.exception.ServerException;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.atom.CFGenericAlertDialog;
import com.curefit.cfapi.pojo.vm.atom.CFGradientColorData;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewbuilders.fitso.ProductListWidget;
import com.curefit.cfapi.view.viewmodels.common.ProductListWidgetItem;
import com.curefit.cfapi.view.viewmodels.memberships.MembershipPageView;
import com.curefit.cfapi.view.viewmodels.memberships.PolicyPageView;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.FaqWidgetV2;
import com.curefit.cfapi.widgets.digital.CtaVerticalListWidgetView;
import com.curefit.cfapi.widgets.membership.*;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.cult.models.responses.MembershipActions;
import com.curefit.gymfit.dtos.MembershipStartDateEditRequest;
import com.curefit.gymfit.utils.Enums;
import com.curefit.location.models.City;
import com.curefit.membership.pojo.entry.Attribute;
import com.curefit.membership.pojo.entry.AuditLog;
import com.curefit.membership.pojo.entry.Benefit;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.requests.UnpauseRequest;
import com.curefit.membership.types.*;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.product.enums.ProductType;
import com.google.gson.Gson;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.curefit.cfapi.constants.Constants.GET_TRANSITION_DETAILS_PAGE_VIEW;
import static com.curefit.cfapi.pojo.app.action.ActionType.*;
import static com.curefit.cfapi.util.ActionUtil.gymfitUniversalCheckinAction;
import static com.curefit.cfapi.util.GymUtil.GYMFIT_CENTER_ID_KEY;
import static com.curefit.cfapi.util.MembershipUtil.*;
import static com.curefit.cfapi.util.PlayUtil.*;
import static com.curefit.cfapi.util.SelectPacksUtils.CREDIT_PILL_SVG;
import static com.curefit.cfapi.util.TimeUtil.UTC_IST_TIME_DIFF_IN_MILLI;
import static com.curefit.cfapi.view.viewbuilders.fitso.PlayUpgradePageBuilder.SLP_TO_PLAY;
import static com.curefit.cfapi.widgets.membership.MembershipBenefitsItem.UNLIMITED_MONTHLY_ACCESS_TICKETS;
import static com.curefit.cfapi.widgets.membership.MembershipBenefitsItem.UNLIMITED_STATIC_ACCESS_TICKETS;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class MembershipDetailPageViewBuilder {

    // deeplinks
    public static final String BASIC_SUPPORT_PAGE = AppUtil.SUPPORT_DEEP_LINK;
    public static final String FITNESS_ISSUE_ID_ACTIVE = AppUtil.SUPPORT_DEEP_LINK;
    static final List<Action> OK_EMPTY_NAVIGATION = List.of(Action.builder().actionType(EMPTY_ACTION).title("OK").build());

    // Images
    static final String ACCESSORIES_IMAGE = "/image/membership/accessories.png";
    static final String CHEVRON_RIGHT_ICON = "/image/membership/chevron_right.png";
    static final String CLOSE_ICON = "/image/membership/close_enclosed.png";
    static final String EDIT_ICON = "/image/membership/edit.png";
    static final String ERROR_ICON = "/image/membership/Error.png";
    static final String INFO_ICON = "/image/membership/info.png";
    static final String LIVE_SCHEDULE = "/image/membership/cast_white.png";
    static final String PAUSE_ACTIVE = "/image/membership/pause_active.png";
    static final String PAUSE_INACTIVE = "/image/membership/pause_inactive.png";
    static final String PLAY_ICON = "/image/membership/play.png";
    static final String POLICY_ICON = "/image/membership/policy.png";
    static final String RENEW_ICON = "/image/membership/renew.png";
    static final String SMART_WORKOUT_ICON = "/image/membership/smart_workout_plan_white.png";
    static final String TRANSFER_ACTIVE = "/image/membership/transfer_active.png";
    static final String TRANSFER_INACTIVE = "/image/membership/transfer_inactive.png";
    static final String UPGRADE_ACTIVE = "/image/membership/upgrade_active.png";
    static final String UPGRADE_INACTIVE = "/image/membership/upgrade_inactive.png";
    static final String WORKOUT_AT_HOME_ICON = "/image/membership/at_home_white.png";
    static final String GROUP_ICON = "/image/icons/cult/Group_icon.svg";
    static final String GYM_ICON = "/image/icons/cult/Gym_icon.svg";
    static final String LIVE_ICON = "/image/icons/cult/live_icon.svg";
    static final String WORKOUT_PLAN_GRADIENT_ICON = "/image/icons/cult/workout_benefit.png";
    static final String GROUP_BENEFIT_GRADIENT_ICON = "/image/icons/cult/group_benefit.png";
    static final String SMART_WORKOUT_GRADIENT_ICON = "/image/icons/cult/smartWorkoutPlan.png";
    static final String PLANE_ICON = "/image/membership/plane.png";
    static final String SWIMMING_POOL_ICON = "/image/membership/swimming_pool.png";
    static final String BADMINTON_ICON = "/image/membership/badminton_icon.png";
    static final String CALENDAR_ICON = "/image/icons/cult/calendar.png";
    // Elite plus specific
    static final String SWIMMING_POOL_ICON_EP = "/image/membership/swimming_ep.png";
    static final String PLANE_ICON_EP = "/image/membership/plane_ep.png";
    static final String WORKOUT_AT_HOME_ICON_EP = "/image/membership/workout_at_home_ep.png";
    static final String CULTSPORTS_ICON_EP = "/image/membership/cultsports_ep.png";
    static final String PAUSE_ENCLOSED_ICON_EP = "/image/membership/pause_enclosed_ep.png";
    static final String TRANSFER_MEMBERSHIP_ICON_EP = "/image/membership/change_ep.png";
    // Pro Plus specific
    static final String AT_HOME_ICON_PP = "/image/membership/at_home_pp.png";
    static final String CULTSPORTS_ICON_PP = "/image/membership/cultsports_pp.png";
    static final String GROUP_ICON_PP = "/image/membership/group_pp.png";
    static final String PAUSE_ENCLOSED_ICON_PP = "/image/membership/pause_pp.png";
    static final String TRANSFER_ICON_PP = "/image/membership/transfer_pp.png";

    static final String FITNESS_FAQS = AppUtil.SUPPORT_DEEP_LINK;

    // * pages
    static final String ONEPASS_ALL_CENTERS_DEEPLINK = "curefit://allgyms?centerType=ONEPASS";
    static final String ELITE_PURCHASE_PAGE = "curefit://fl_listpage?pageId=CultPassBlackSKU";
    static final String PRO_PURCHASE_PAGE = "curefit://fl_listpage?pageId=CultPassGoldSKU";
    static final String PLAY_PURCHASE_PAGE = "curefit://fl_listpage?pageId=CultPassFitsoSKU";
    static final String NEW_MEMBERSHIP_TRANSFER_PAGE = "curefit://get_transfer_details?membershipServiceId=";
    static final String NEW_MEMBERSHIP_UPGRADE_PAGE = "curefit://upgrade_membership?membershipServiceId=";
    static final String NEW_MEMBERSHIP_PAUSE_PAGE = "curefit://membership_pause_screen?&membershipId=";
    static final String SCHEDULE_PAGE_FITNESS = "curefit://classbookingv2?productType=FITNESS";
    static final String CENTER_QUICK_CHECKIN = "curefit://gymquickcheckin?centerId=";
    static final String LIVE_SCHEDULE_PAGE = "curefit://liveclassbooking?productType=FITNESS&isLiveBookingPage=true";
    static final String CREDIT_HISTORY_PAGE = "curefit://credit_history_page?membershipId=";
    static final String SELECT_PACK_COMPARISON_PAGE_DEEPLINK = "curefit://fl_pack_comparison?";
    static final String ONE_PASS_CENTER_PAGE = "curefit://onepasscenter?centerId=";
    static final String EDIT_START_DATE = "curefit://membership_pack_edit_start_date?membershipServiceId=";

    // Data
    Membership membership;
    MembershipState userMembershipState;
    FitnessCenterUtil.UserMembershipType userMembershipType;
    MembershipBenefitsItem membershipBenefitsItem;
    CenterEntry accessCenter;
    ProductType membershipProductType;
    City membershipCity;
    String orderId;
    Boolean isLiveBenefitOfMembership = false;
    String playSLPMembershipWorkoutId;
    Debugger debugger;
    Long verticalSpecificMembershipId;

    BaseWidget pauseAddonWidget; // So, we do not had to call same thing twice

    public MembershipPageView buildView(UserContext userContext, Map<String, String> queryParams, ServiceInterfaces interfaces, Boolean skipWidgets) throws Exception {

        MembershipPageView pageView = new MembershipPageView();
        List<CompletableFuture<BaseWidget>> pageWidgets = new ArrayList<>();

        String membershipServiceId = queryParams.get("membershipServiceId");
        String isLiveBenefit = queryParams.get("isLiveBenefit");

        debugger = Debugger.getDebuggerFromUserContext(userContext);

        membership = interfaces.membershipService.getMembershipById(
                Long.parseLong(membershipServiceId),
                userContext.getUserProfile().getUserId()
        ).get();

        if (Objects.isNull(membership)) {
            throw new Exception("Membership is not available for the membershipServiceId: " + membershipServiceId);
        }

        orderId = membership.getOrderId();
        String cityId = Objects.nonNull(membership.getMetadata()) && membership.getMetadata().containsKey("cityId")
                ? (String) membership.getMetadata().get("cityId")
                : userContext.getUserProfile().getCity().getCityId();
        verticalSpecificMembershipId = Objects.nonNull(membership.getMetadata()) && membership.getMetadata().containsKey("membershipId")
                ? Long.valueOf(String.valueOf(membership.getMetadata().get("membershipId")))
                : null;
        membershipCity = interfaces.cityCache.getCityById(cityId);
        isLiveBenefitOfMembership = (Objects.nonNull(isLiveBenefit) && "true".equalsIgnoreCase(isLiveBenefit));

        if (isLiveBenefitOfMembership) {
            userMembershipType = FitnessCenterUtil.UserMembershipType.HOME;
            membershipProductType = ProductType.CF_LIVE;
            membershipBenefitsItem = new MembershipBenefitsItem();
        } else {
            userMembershipType = FitnessCenterUtil.getUserMembershipType(membership, true, userContext, true);
            // will be null in case of non-select membership
            switch (userMembershipType) {
                case ELITE_SELECT, PRO_SELECT, ELITE_COMPLIMENTARY ->
                        accessCenter = getSelectCenterFromMembership(membership, interfaces.centerService);
                case LUX, LUX_GX, LUX_GROUP_CLASSES ->
                        accessCenter = getAccessCenterIdFromMembershipMetadata(membership, interfaces.centerService);
                case PLAY_SLP, PLAY_LITE, PLAY_LIMITED, PLAY_COMPLIMENTARY, PLAY_CITY_SLP, PLAY_SWIM_LIMITED, PLAY, PLAY_TT_LIMITED, PLAY_BADMINTON_LIMITED -> {
                    accessCenter = getAccessCenterForPlaySportsLevelPack(membership, interfaces.centerService);
                    playSLPMembershipWorkoutId = getWorkoutIdForPlaySportsLevelPack(membership);
                }
            }
            membershipBenefitsItem = MembershipUtil.getMembershipBenefitsItem(membership, interfaces.featureStateCache, userContext, userMembershipType, accessCenter);
            membershipProductType = setProductTypeFromProductId(membership);
        }
        userMembershipState = getMembershipState(membership, userContext);
        if (isLiveBenefitOfMembership && membership.getStatus().equals(Status.PAUSED))
            userMembershipState = MembershipState.ACTIVE;

        debugger.msg(membership);
        debugger.msg(userMembershipState);
        debugger.msg(userMembershipType);
        debugger.msg(membershipProductType);
        debugger.msg(membershipBenefitsItem);
        debugger.msg(accessCenter);
        debugger.msg(playSLPMembershipWorkoutId);
        debugger.msg("isLimitedOrEnterpriseMembership", String.valueOf(isLimitedOrEnterpriseMembership()));
        debugger.msg("isEnterpriseLimitedSessionPlayMembership", String.valueOf(isEnterpriseLimitedSessionPlayMembership(membership)));

        if (skipWidgets) {
            pageView.addWidgets(pageWidgets);
            pageView.setPageAction(null);
            return pageView;
        }

        CompletableFuture<BaseWidget> pauseAddonWidgetFuture = pauseAddonPurchaseBanner(interfaces, userContext);
        pauseAddonWidget = (Objects.nonNull(pauseAddonWidgetFuture)) ? pauseAddonWidgetFuture.get() : null;

        switch (userMembershipType) {
            case ELITE, ELITE_SELECT, ELITE_PLUS, PRO, PRO_PLUS, PRO_SELECT, LUX, LUX_GX, LUX_GROUP_CLASSES, PLAY,
                 PLAY_SLP, PLAY_CITY_SLP, PLAY_LITE, PLAY_LIMITED, ONEPASS, ELITE_LITE, ELITE_SELECT_LITE,
                 PRO_SELECT_LITE, PLAY_SWIM_LIMITED, PLAY_BADMINTON_LIMITED, PLAY_TT_LIMITED -> {

                pageView.setHeaderAction(getHelpAction(interfaces, userContext));
                pageView.setPageActions(getPageActions());
                pageWidgets.add(getMembershipDetailCard(userContext, interfaces));
                pageWidgets.add(getActionCardWidget(userContext, interfaces));
                pageWidgets.add(CompletableFuture.completedFuture(pauseAddonWidget));
                pageWidgets.add(getMembershipBenefitsWidget(interfaces));
                pageWidgets.add(getHowItWorksWidget(userContext));
                pageWidgets.add(getMembershipPolicyWidget());
                pageWidgets.add(getFaqWidget());

            }
            case ELITE_COMPLIMENTARY, HOME, PLAY_COMPLIMENTARY -> {

                pageView.setHeaderAction(getHelpAction(interfaces, userContext));
                pageView.setPageActions(getPageActions());
                pageWidgets.add(getMembershipDetailCard(userContext, interfaces));
                pageWidgets.add(getMembershipBenefitsWidget(interfaces));
                pageWidgets.add(getHowItWorksWidget(userContext));
                pageWidgets.add(getMembershipPolicyWidget());
                pageWidgets.add(getFaqWidget());

            }
            default -> {

                interfaces.exceptionReportingService.reportErrorMessage(
                        "Need to fix deeplink for membershipType:: " + userMembershipType +
                                ", productId: " + membership.getProductId() +
                                ", productType: " + membershipProductType
                );
                // to show at least some info to the user.
                pageWidgets.add(getMembershipDetailCard(userContext, interfaces));
                pageWidgets.add(getActionCardWidget(userContext, interfaces));
                pageView.setHeaderAction(getHelpAction(interfaces, userContext));

            }
        }

        pageView.addWidgets(pageWidgets);
        pageView.setPageAction(null);

        return pageView;

    }

    public MembershipPageView editStartDate(Map<String, String> queryParams, String membershipServiceId, UserContext userContext, ServiceInterfaces interfaces) throws Exception {

        MembershipPageView pageView = new MembershipPageView();
        List<CompletableFuture<BaseWidget>> widgetList = new ArrayList<>();
        pageView.setHeaderTitle("Date Change");

        String selectedStartDate = queryParams.getOrDefault("newStartDate", null);
        if (selectedStartDate.isEmpty()) selectedStartDate = null;

        String confirmStartDateChange = queryParams.getOrDefault("confirmStartDateChange", null);

        debugger = Debugger.getDebuggerFromUserContext(userContext);

        debugger.msg(selectedStartDate);
        debugger.msg(confirmStartDateChange);

        if (Objects.isNull(membershipServiceId)) {
            throw new Exception("membershipServiceId is not available in the query");
        }

        membership = interfaces.membershipService.getMembershipById(
                Long.parseLong(membershipServiceId),
                userContext.getUserProfile().getUserId()
        ).get();

        if (Objects.isNull(membership)) {
            throw new Exception("Membership is not available for the membershipServiceId: " + membershipServiceId);
        }


        Long originalStartDate = membership.getStart();
        List<AuditLog> auditLogs = interfaces.membershipService.getAuditLogByMembershipId(String.valueOf(membership.getId()));
        debugger.msg(auditLogs);
        if (Objects.nonNull(auditLogs)) {
            auditLogs = auditLogs.stream().filter(item -> item.getAttributeChanged().equalsIgnoreCase("start")).collect(Collectors.toList());
            if (auditLogs.size() >= 2) {
                pageView.setInitAction(getEditStartDateAllUsedErrorPopUp());
                return pageView;
            } else if (CollectionUtils.isNotEmpty(auditLogs)) {
                originalStartDate = Long.valueOf(auditLogs.getFirst().getOldValue());
            }
        }

        userMembershipType = FitnessCenterUtil.getUserMembershipType(membership, true, userContext, true);
        userMembershipState = getMembershipState(membership, userContext);

        debugger.msg("originalStartDate", String.valueOf(originalStartDate));
        debugger.msg(membership);
        debugger.msg(userMembershipType);
        debugger.msg(membership.getStatus());
        debugger.msg(userMembershipState);

        if (!userMembershipState.equals(MembershipState.UPCOMING)) {
            String message = "Your Membership is already started Can't change the date now!!!";
            if (userMembershipState.equals(MembershipState.EXPIRED) || userMembershipState.equals(MembershipState.CANCELLED)) {
                message = "Your Membership is Expired Can't change the date now!!!";
            }
            throw new BaseException(message, LogType.INFO, ErrorUtil.CFAPI_NAMESPACE, AppStatus.PRECONDITION_FAILED, String.valueOf(ErrorUtil.ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR), message);
        }

        switch (userMembershipType) {
            case ELITE_SELECT, PRO_SELECT, ELITE_SELECT_LITE ->
                    accessCenter = getSelectCenterFromMembership(membership, interfaces.centerService);
            case LUX, LUX_GX, LUX_GROUP_CLASSES ->
                    accessCenter = getAccessCenterIdFromMembershipMetadata(membership, interfaces.centerService);
            case PLAY_SLP, PLAY_LITE, PLAY_LIMITED, PLAY_COMPLIMENTARY ->
                    accessCenter = getAccessCenterForPlaySportsLevelPack(membership, interfaces.centerService);
        }

        Map<String, String> editMembershipData = getEarliestStartDateForMembership(membership, userMembershipType, interfaces, userContext, selectedStartDate, originalStartDate, accessCenter);
        debugger.msg(editMembershipData);

        String errorMessage = editMembershipData.getOrDefault("errorMessage", null);

        if (Objects.isNull(errorMessage) && Objects.nonNull(confirmStartDateChange) && Objects.nonNull(selectedStartDate) && Objects.equals(confirmStartDateChange, "true")) {
            long delta = 2 * UTC_IST_TIME_DIFF_IN_MILLI; // UTC-IST issue
            long selectedDateEpochMilli = TimeUtil.StringDateToEpoch(selectedStartDate, null) + delta;
            boolean isEditDateSuccess = true;
            try {
                changeStartDatePostConfirmation(interfaces, selectedDateEpochMilli, userContext, selectedStartDate);
            } catch (Exception e) {
                isEditDateSuccess = false;
                Action errorAction = getErrorAction(e.getMessage());
                pageView.setInitAction(errorAction);
                interfaces.exceptionReportingService.reportException(e);
                debugger.err(e);
            }
            if (isEditDateSuccess) {
                interfaces.membershipService.syncMembershipForUser(userContext.getUserProfile().getUserId());
                pageView.setInitAction(getEditStartDateSuccess(selectedDateEpochMilli, userContext));
            }
        } else {

            String earliestStartDate = editMembershipData.get("earliestStartDate");
            String finalStartDate = editMembershipData.get("finalStartDate");

            widgetList.add(getEditStartDateInfoWidget());
            widgetList.add(getDatePicker(earliestStartDate, finalStartDate, selectedStartDate, userContext));
            widgetList.add(getErrorInfoWidget(errorMessage));
            Action confirmationAction = getConfirmationEditStartDateAction(selectedStartDate, confirmStartDateChange, errorMessage);
            if (Objects.nonNull(confirmationAction)) pageView.setPageActions(List.of(confirmationAction));

        }

        pageView.addWidgets(widgetList);
        return pageView;
    }

    private void changeStartDatePostConfirmation(ServiceInterfaces interfaces, Long selectedDateEpochMilli, UserContext userContext, String selectedStartDate) throws Exception {
        switch (userMembershipType) {
            case PLAY, PLAY_SLP, PLAY_COMPLIMENTARY, PLAY_LIMITED, PLAY_LITE -> {
                List<Membership> memberships = interfaces.sportsApiService.moveMembershipById(membership.getId(), selectedDateEpochMilli, "In App", "user");
                debugger.msg(memberships);
            }
            case ELITE, ELITE_COMPLIMENTARY, ELITE_PLUS, ELITE_SELECT, ELITE_LITE, ELITE_SELECT_LITE -> {
                Object returnObj = interfaces.cultService.editMembershipStartDate(String.valueOf(membership.getId()), userContext.getUserProfile().getUserId(), selectedStartDate, "User", "In App");
                threadSleep();
                debugger.msg(returnObj);
            }
            case PRO, PRO_SELECT_LITE -> {
                Long membershipId = membership.getMetadata().containsKey("membershipId") ? Long.valueOf((Integer) membership.getMetadata().get("membershipId")) : null;
                if (Objects.isNull(membershipId)) throw new Exception("Something Went Wrong!!! membership not found");
                MembershipStartDateEditRequest request = new MembershipStartDateEditRequest();
                request.setStartDate(selectedStartDate);
                request.setAgent("User");
                request.setComment("In App");
                request.setAgentType(Enums.AgentType.USER);
                interfaces.gymfitClient.membershipService().editStartDate(request, String.valueOf(membershipId), false);
            }
            case LUX, LUX_GX, LUX_GROUP_CLASSES -> {
                MembershipStartDateEditRequest request = new MembershipStartDateEditRequest();
                request.setStartDate(selectedStartDate);
                request.setAgent("User");
                request.setComment("In App");
                request.setAgentType(Enums.AgentType.USER);
                interfaces.gymfitClient.membershipService().editStartDate(request, String.valueOf(membership.getId()), true);
            }
            default -> {
                List<Membership> memberships = interfaces.membershipService.moveMembershipById(membership.getId(), selectedDateEpochMilli / 1000, ConflictHandling.FAIL, "user", "In app");
                debugger.msg(memberships);
            }
        }
    }

    public MembershipPageView unPauseMembership(Map<String, String> queryParams, UserContext userContext, ServiceInterfaces interfaces) throws Exception {

        String membershipServiceId = queryParams.get("membershipServiceId");
        debugger = Debugger.getDebuggerFromUserContext(userContext);

        membership = interfaces.membershipService.getMembershipById(
                Long.parseLong(membershipServiceId),
                userContext.getUserProfile().getUserId()
        ).get();

        userMembershipType = FitnessCenterUtil.getUserMembershipType(membership, true, userContext, true);

        if (!userContext.getUserProfile().getUserId().equals(membership.getUserId())) {
            throw new Exception("Issue with Membership User Id mismatch");
        }

        debugger.msg(userMembershipType);
        debugger.msg(membership.getStatus());
        debugger.msg(membership.getActivePause());

        switch (userMembershipType) {
            case ELITE, ELITE_PLUS, ELITE_SELECT, ELITE_COMPLIMENTARY, ELITE_LITE, ELITE_SELECT_LITE -> {
                try {

                    if (Status.PURCHASED.equals(membership.getStatus()) && membership.getActivePause() != null) {
                        Object response = interfaces.cultService.cancelMembershipUpcomingPause(String.valueOf(membership.getId()), userContext.getUserProfile().getUserId()).get();
                        debugger.msg(response);
                    } else if (Status.PAUSED.equals(membership.getStatus())) {
                        Boolean response = interfaces.cultService.cancelMembershipOngoingPause(String.valueOf(membership.getId()), userContext.getUserProfile().getUserId()).get();
                        debugger.msg(response);
                    } else {
                        throw new ServerException("User trying to Unpause/resume an ongoing FITNESS membership", ErrorUtil.ErrorCodes.RESUME_MEMBERSHIP_ERROR);
                    }

                    threadSleep();
                    return MembershipPageView.builder().message("Membership Unpause Successful").build();

                } catch (Exception e) {

                    // sneaky throw
                    debugger.msg(e);
                    threadSleep();
                    return MembershipPageView.builder().message("Membership Unpause UnSuccessful").build();

                }
            }
            case PRO, PRO_PLUS, PRO_SELECT, LUX, LUX_GX, LUX_GROUP_CLASSES, PLAY, PLAY_SLP, PLAY_CITY_SLP, PLAY_LITE,
                 ONEPASS, PRO_SELECT_LITE -> {
                return useMembershipServiceToUnpause(userContext, interfaces);
            }
        }

        debugger.err("pause", "Not able to unpause unknown membership");
        return MembershipPageView.builder().message("Membership Unpause UnSuccessful").build();

    }

    private MembershipPageView useMembershipServiceToUnpause(UserContext userContext, ServiceInterfaces interfaces) throws ServerException {

        UnpauseRequest pauseRequest = UnpauseRequest.builder().membershipId(membership.getId()).gapHandling(GapHandling.FILL).agentId(userContext.getUserProfile().getUserId()).annotation("Unpause user request").build();

        try {

            if (Status.PURCHASED.equals(membership.getStatus()) && membership.getActivePause() != null) {
                Membership response = interfaces.membershipService.cancelMembershipUpcomingPause(pauseRequest);
                debugger.msg(response);
            } else if (Status.PAUSED.equals(membership.getStatus())) {
                Membership response = interfaces.membershipService.unPauseMembership(pauseRequest);
                debugger.msg(response);
            } else {
                throw new ServerException("User trying to Unpause/resume an ongoing membership", ErrorUtil.ErrorCodes.RESUME_MEMBERSHIP_ERROR);
            }

            return MembershipPageView.builder().message("Membership Unpause Successful").build();

        } catch (Exception e) {
            debugger.err(e);
            throw new ServerException("Unable to resume membership, Please try again.", ErrorUtil.ErrorCodes.RESUME_MEMBERSHIP_ERROR, HttpStatus.CONFLICT.value());
        }

    }

    private CompletableFuture<BaseWidget> getMembershipDetailCard(UserContext userContext, ServiceInterfaces interfaces) throws Exception {

        MembershipWidgetAurora widgetAurora = new MembershipWidgetAurora();
        widgetAurora.setLayoutProps(getVerticalPaddingLayout("0", "30"));
        MembershipCardItem membershipCard = new MembershipCardItem();
        membershipCard.setCardSize(335.0);

        setMembershipCardItemTitle(membershipCard);
        membershipCard.setMembershipState(userMembershipState.toString());
        membershipCard.setMembershipStateTextColor(getPrimaryColorForMembershipState(userMembershipState));
        membershipCard.setProgressBar(getProgressBarDateForMembership(membership, userMembershipState, userContext, true));
        membershipCard.setProductType(membershipProductType);
        List<Action> footerActions = getMembershipActions(userContext, interfaces);
        membershipCard.setFooterActions(footerActions);

        widgetAurora.setCardWithActions(!footerActions.isEmpty());
        widgetAurora.setData(Collections.singletonList(membershipCard));
        debugger.msg(widgetAurora);

        return CompletableFuture.completedFuture(widgetAurora);

    }

    private List<Action> getMembershipActions(UserContext userContext, ServiceInterfaces interfaces) throws Exception {

        List<Action> membershipActions = new ArrayList<>();

        // DO not add the condition here. add it inside each function as per the use case.
        Action transferAction = getMembershipTransferPageAction(interfaces, userContext);
        Action upgradeAction = getMembershipUpgradePageAction(interfaces, userContext);
        Action renewAction = getMembershipRenewAction(userContext, interfaces);
        Action pauseUnpauseAction = getPauseResumeAction(userContext);

        if (Objects.nonNull(renewAction)) membershipActions.add(renewAction);
        if (Objects.nonNull(pauseUnpauseAction)) membershipActions.add(pauseUnpauseAction);
        if (Objects.nonNull(transferAction)) membershipActions.add(transferAction);
        if (Objects.nonNull(upgradeAction)) membershipActions.add(upgradeAction);
        membershipActions.forEach(action -> action.setAnalyticsData(getCommonAnalyticsData()));

        debugger.msg(membershipActions);
        return membershipActions;

    }

    private Action getMembershipEditStartDateAction() {

        if (!shouldShowEditStartDateAction()) {
            return null;
        }
        Action changeStartDateAction = new Action(EDIT_START_DATE + membership.getId(), "CHANGE START DATE", ActionType.NAVIGATION);
        changeStartDateAction.setIconUrl(CALENDAR_ICON);
        changeStartDateAction.setAnalyticsData(getCommonAnalyticsData());

        switch (userMembershipType) {
            case ELITE, ELITE_PLUS, ELITE_SELECT, PRO, PRO_PLUS, PRO_SELECT, LUX, LUX_GX, LUX_GROUP_CLASSES, ELITE_LITE,
                 ELITE_SELECT_LITE, PRO_SELECT_LITE -> {
                if (!isLimitedOrEnterpriseMembership()) {
                    return changeStartDateAction;
                }
            }
            case PLAY, PLAY_SLP, PLAY_CITY_SLP -> {
                if (!isEnterpriseLimitedSessionPlayMembership(membership)) {
                    return changeStartDateAction;
                }
            }
        }

        return null;
    }

    private boolean shouldShowEditStartDateAction() {

        if (!MembershipState.UPCOMING.equals(userMembershipState)) {
            return false;
        }

        if (Objects.nonNull(membership.getMetadata()) && membership.getMetadata().containsKey("productSubType")) {
            return !ProductSubType.RECURRING.getValue().equals((String) membership.getMetadata().get("productSubType"));
        }

        return true;
    }

    private Action getMembershipRenewAction(UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        try {

            if (MembershipState.UPCOMING.equals(userMembershipState)) {
                return null;
            }

            boolean isRenewActionSupported = false;

            switch (membershipProductType) {
                case FITNESS, GYMFIT_FITNESS_PRODUCT ->
                        isRenewActionSupported = AppUtil.doesUserBelongToMasterBestPriceSegment(interfaces, userContext) ||
                                AppUtil.doesUserBelongToMasterRenewSegment(interfaces, userContext) ||
                                AppUtil.isRenewalForCenterLevelPricingSupported(userContext, interfaces, interfaces.environmentService);
                case PLAY ->
                        isRenewActionSupported = PlayUtil.doesUserBelongToPlayUFRSegment(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
            }

            debugger.msg("isRenewActionSupported", String.valueOf(isRenewActionSupported));
            if (!isRenewActionSupported) return null;

            switch (userMembershipType) {
                case ELITE, ELITE_PLUS, ELITE_COMPLIMENTARY, ELITE_LITE -> {

                    return Action.builder().title("RENEW").iconUrl(RENEW_ICON).actionType(NAVIGATION)
                            .url(ELITE_PURCHASE_PAGE)
                            .build();

                }
                case ELITE_SELECT, ELITE_SELECT_LITE -> {

                    String url = ELITE_PURCHASE_PAGE;
                    if (Objects.nonNull(accessCenter)) {
                        url = SELECT_PACK_COMPARISON_PAGE_DEEPLINK + "centerId=" + accessCenter.getId().toString() + "&selectedSku=center_select" + "&centerType=PRO";
                    }
                    return Action.builder().title("RENEW").iconUrl(RENEW_ICON).actionType(NAVIGATION)
                            .url(url)
                            .build();

                }
                case PRO, PRO_PLUS -> {

                    return Action.builder().title("RENEW").iconUrl(RENEW_ICON).actionType(NAVIGATION)
                            .url(PRO_PURCHASE_PAGE)
                            .build();

                }
                case PRO_SELECT, PRO_SELECT_LITE -> {

                    String url = PRO_PURCHASE_PAGE;
                    if (Objects.nonNull(accessCenter)) {
                        url = SELECT_PACK_COMPARISON_PAGE_DEEPLINK + "centerId=" + accessCenter.getId().toString() + "&selectedSku=center_select" + "&centerType=ELITE";
                    }
                    return Action.builder().title("RENEW").iconUrl(RENEW_ICON).actionType(NAVIGATION)
                            .url(url)
                            .build();

                }
                case PLAY, PLAY_LITE, PLAY_SLP, PLAY_COMPLIMENTARY, PLAY_LIMITED, PLAY_CITY_SLP -> {

                    return Action.builder().title("RENEW").iconUrl(RENEW_ICON).actionType(NAVIGATION)
                            .url(PLAY_PURCHASE_PAGE)
                            .build();

                }
            }
        } catch (Exception e) {
            throw new Exception("Unable to build renew Action for membership: " + membership.getId(), e);
        }

        return null;

    }

    private Action getMembershipTransferPageAction(ServiceInterfaces interfaces, UserContext userContext) {

        if (
            MembershipState.EXPIRED.equals(userMembershipState) ||
            MembershipState.CANCELLED.equals(userMembershipState) ||
            MembershipState.EXPIRING.equals(userMembershipState) ||
            isLimitedOrEnterpriseMembership() || isAllIndiaPackMembership(membership) ||
            !StringUtils.isNumeric(orderId)
        ) {
            return null;
        }

        switch (userMembershipType) {
            case ELITE, ELITE_SELECT, PRO_SELECT, ELITE_PLUS -> {

                Action getTransferCommonError = getCommonTransferErrors(userContext);
                if (Objects.nonNull(getTransferCommonError)) return getTransferCommonError;

                boolean canTransferPack = !isTransferredMembership(membership);
                debugger.msg("canTransferPack", String.valueOf(canTransferPack));
                if (!canTransferPack) {
                    switch (userMembershipType) {
                        case ELITE, ELITE_PLUS -> {
                            try {
                                MembershipActions membershipActions = interfaces.getCultService().getCanTransferMembershipData(
                                        String.valueOf(membership.getId()), membership.getUserId()
                                ).get();
                                debugger.msg(membershipActions);
                                if (Objects.nonNull(membershipActions) &&
                                        Objects.nonNull(membershipActions.getMembershipTransferable()) &&
                                        Objects.nonNull(membershipActions.getMembershipTransferable().getValue())) {

                                    if (membershipActions.getMembershipTransferable().getValue()) {
                                        canTransferPack = true;
                                    } else if (membershipActions.getMembershipTransferable().getShowErrorToUser()) {
                                        return transferCurrentlyUnavailable(membershipActions.getMembershipTransferable().getMessage());
                                    }

                                }
                            } catch (Exception e) {
                                debugger.err(e);
                                interfaces.exceptionReportingService.reportException(e);
                            }
                        }
                    }
                }
                debugger.msg("canTransferPack2", String.valueOf(canTransferPack));
                if (!canTransferPack) return null;

                return Action.builder().iconUrl(TRANSFER_ACTIVE).title("TRANSFER").actionType(NAVIGATION).url(NEW_MEMBERSHIP_TRANSFER_PAGE + membership.getId()).build();
            }
        }

        return null;
    }

    public Action getMembershipUpgradePageAction(ServiceInterfaces interfaces, UserContext userContext) throws ExecutionException, InterruptedException {

        if (
            MembershipState.EXPIRED.equals(userMembershipState) ||
            MembershipState.CANCELLED.equals(userMembershipState) ||
            MembershipState.EXPIRING.equals(userMembershipState) ||
            isLimitedOrEnterpriseMembership() ||
            isEnterpriseLimitedSessionPlayMembership(membership) ||
            !StringUtils.isNumeric(orderId)
        ) return null;

        String transitionUrl = GET_TRANSITION_DETAILS_PAGE_VIEW + membership.getId() + "&transitionType=UPGRADE";

        switch (userMembershipType) {
            case ELITE_SELECT -> {

                Action upgradeErrorAction = getCommonUpgradeErrors();
                if (Objects.nonNull(upgradeErrorAction)) return upgradeErrorAction;

                return Action.builder()
                        .url(AppUtil.isAppTransitionPageSupported(interfaces, userContext) ? transitionUrl :
                                NEW_MEMBERSHIP_UPGRADE_PAGE + membership.getId() + "&upgradeType=ELITE_SELECT_TO_ELITE&productId=" + membership.getProductId())
                        .iconUrl(UPGRADE_ACTIVE).actionType(NAVIGATION).title("UPGRADE").build();
            }
            case PRO -> {

                Action upgradeErrorAction = getCommonUpgradeErrors();
                if (Objects.nonNull(upgradeErrorAction)) return upgradeErrorAction;

                return Action.builder()
                        .url(AppUtil.isAppTransitionPageSupported(interfaces, userContext) ? transitionUrl :
                                NEW_MEMBERSHIP_UPGRADE_PAGE + membership.getId() + "&upgradeType=PRO_TO_ELITE&productId=" + membership.getProductId())
                        .iconUrl(UPGRADE_ACTIVE).actionType(NAVIGATION).title("UPGRADE").build();
            }
            case PRO_SELECT -> {

                Action upgradeErrorAction = getCommonUpgradeErrors();
                if (Objects.nonNull(upgradeErrorAction)) return upgradeErrorAction;

                if (AppUtil.isAppTransitionPageSupported(interfaces, userContext))
                    return Action.builder().url(transitionUrl).iconUrl(UPGRADE_ACTIVE).actionType(NAVIGATION).title("UPGRADE").build();

                List<Action> actionList = new ArrayList<>();

                Action upgradeToPro = Action.builder()
                        .url(NEW_MEMBERSHIP_UPGRADE_PAGE + membership.getId() + "&upgradeType=PRO_SELECT_TO_PRO&productId=" + membership.getProductId())
                        .actionType(NAVIGATION).title("PRO").variant("secondary").build();

                Action upgradeToElite = Action.builder()
                        .url(NEW_MEMBERSHIP_UPGRADE_PAGE + membership.getId() + "&upgradeType=PRO_SELECT_TO_ELITE&productId=" + membership.getProductId())
                        .actionType(NAVIGATION).title("ELITE").build();

                actionList.add(upgradeToElite);
                actionList.add(upgradeToPro);

                Action proSelectUpgradeDialog = CFGenericAlertDialog.getDialog(
                        "UPGRADE",
                        "Its Time To Upgrade!!!",
                        "Choose the Option in which you wants to upgrade your membership?",
                        actionList
                );

                proSelectUpgradeDialog.setAnalyticsData(getCommonAnalyticsData());
                proSelectUpgradeDialog.setIconUrl(UPGRADE_ACTIVE);
                return proSelectUpgradeDialog;

            }
            case PRO_PLUS -> {
                if (AppUtil.isAppTransitionPageSupported(interfaces, userContext)) {

                    Action upgradeErrorAction = getCommonUpgradeErrors();
                    if (Objects.nonNull(upgradeErrorAction)) return upgradeErrorAction;

                    return Action.builder().url(transitionUrl).iconUrl(UPGRADE_ACTIVE).actionType(NAVIGATION).title("UPGRADE").build();
                }
            }
            case ELITE -> {

                if (membershipCity.getCityId().equals(userContext.getUserProfile().getCity().getCityId()) &&
                        AppUtil.isUserInPlusCity(interfaces, userContext) &&
                        AppUtil.isAppTransitionPageSupported(interfaces, userContext)
                ) {

                    Action upgradeErrorAction = getCommonUpgradeErrors();
                    if (Objects.nonNull(upgradeErrorAction)) return upgradeErrorAction;

                    return Action.builder().url(transitionUrl).iconUrl(UPGRADE_ACTIVE).actionType(NAVIGATION).title("UPGRADE").build();
                }
            }
            case PLAY_SLP, PLAY_CITY_SLP -> {

                Action upgradeErrorAction = getCommonUpgradeErrors();
                if (Objects.nonNull(upgradeErrorAction)) return upgradeErrorAction;

                String url;
                if (userContext.getSessionInfo().getAppVersion() < PlayUtil.PLAY_UPGRADE_FLOW_NEW)
                    url = PlayUtil.PLAY_UPGRADE_MEMBERSHIP_DETAIL;
                else
                    url = PlayUtil.PLAY_UPGRADE_MEMBERSHIP_V2;

                List<NameValuePair> nameValuePairs = new ArrayList<>();
                nameValuePairs.add(new BasicNameValuePair("membershipServiceId", String.valueOf(membership.getId())));
                nameValuePairs.add(new BasicNameValuePair("membershipId", String.valueOf(membership.getId())));
                nameValuePairs.add(new BasicNameValuePair("productType", "PLAY"));
                nameValuePairs.add(new BasicNameValuePair("productId", membership.getProductId()));
                nameValuePairs.add(new BasicNameValuePair("packId", membership.getProductId()));
                nameValuePairs.add(new BasicNameValuePair("title", "Upgrade"));
                nameValuePairs.add(new BasicNameValuePair("upgradeType", SLP_TO_PLAY));
                nameValuePairs.add(new BasicNameValuePair("isMembershipServiceId", "true"));


                url += ActionUtil.formatQueryParamWithQuerySeparator(nameValuePairs);

                return Action.builder().url(url).iconUrl(UPGRADE_ACTIVE).actionType(NAVIGATION).title("UPGRADE").build();

            }
        }

        return null;

    }

    private Action getCommonTransferErrors(UserContext userContext) {
        if (TimeUtil.absDiffInDaysFromEpoch(membership.getEnd(), TimeUtil.now(userContext.getUserProfile().getTimezone())) < 30) {
            return transferNotAvailableLast30Days();
        } else if (MembershipState.UPCOMING.equals(userMembershipState)) {
            return transferNotAllowedUpcoming();
        } else if (MembershipState.PAUSED.equals(userMembershipState)) {
            return transferNotAllowedPaused();
        } else if (MembershipState.ACTIVE.equals(userMembershipState) && membership.getActivePause() != null) {
            return transferNotAllowedActivePaused();
        }
        return null;
    }

    private Action getCommonUpgradeErrors() {
        if (MembershipState.UPCOMING.equals(userMembershipState)) {
            return upgradeNotAllowedUpcoming();
        } else if (MembershipState.PAUSED.equals(userMembershipState)) {
            return upgradeNotAllowedPaused();
        } else if (MembershipState.ACTIVE.equals(userMembershipState) && membership.getActivePause() != null) {
            return upgradeNotAllowedActivePause();
        }
        return null;
    }

    private Action getPauseResumeAction(UserContext userContext) {

        if (
                Objects.isNull(membershipBenefitsItem.getPause()) ||
                        membershipBenefitsItem.getPause().getTotalTickets() == 0 ||
                        MembershipState.CANCELLED.equals(userMembershipState) ||
                        MembershipState.EXPIRED.equals(userMembershipState)
        ) return null;

        switch (userMembershipType) {
            case ELITE, ELITE_PLUS, ELITE_SELECT, PRO, PRO_PLUS, PRO_SELECT, LUX, LUX_GX, LUX_GROUP_CLASSES, PLAY,
                 PLAY_SLP, PLAY_CITY_SLP, ONEPASS, ELITE_LITE, ELITE_SELECT_LITE, PRO_SELECT_LITE -> {

                if (MembershipState.UPCOMING.equals(userMembershipState)) {
                    return pauseNotAllowedUpcoming();
                } else if (membershipBenefitsItem.getPause().getRemainingTickets() == 0) {
                    return pauseNotAllowedExhaust();
                }

                if (Status.PURCHASED.equals(membership.getStatus()) && membership.getActivePause() != null) {
                    return getNewCancelUpcomingPauseAlertDialogAction();
                } else if (MembershipState.PAUSED.equals(userMembershipState)) {
                    return getNewCancelOnGoingPauseAlertDialogAction();
                } else {
                    return getPauseOnlyMembershipAction(userContext);
                }

            }
        }

        return null;

    }

    private Action getPauseOnlyMembershipAction(UserContext userContext) {

        Action dontPauseCTA = new Action(POP_ACTION, "CANCEL");
        dontPauseCTA.setVariant("secondary");

        switch (userMembershipType) {
            case ELITE, ELITE_PLUS, ELITE_COMPLIMENTARY, ELITE_SELECT, ELITE_LITE, ELITE_SELECT_LITE -> {
                PackPauseResumeDetails pausePackData = CultUtil.getPackPauseResumeDetails(userContext, membership, ProductType.FITNESS);
                debugger.msg(pausePackData);
                if (Objects.nonNull(pausePackData))
                    debugger.msg("isPauseAllowed", String.valueOf(pausePackData.getIsPauseAllowed()));
                else debugger.err("pausePackData", "pause pack data is empty");
                if (Objects.nonNull(pausePackData) && pausePackData.getIsPauseAllowed()) {
                    if (AppUtil.IsAppPausePageFLActionSupported(userContext)) {
                        pausePackData.getMeta().put("onCompletionAction", onCompletionPauseAction());
                        pausePackData.getMeta().put("flAction", true);
                    }
                    Action packPauseResumeAction = new Action(NEW_MEMBERSHIP_PAUSE_PAGE + membership.getId() + "&disableAnimation=true", "PAUSE", NAVIGATION);
                    packPauseResumeAction.setVariant("primary");
                    if (Objects.nonNull(pauseAddonWidget)) {
                        pausePackData.getMeta().put("bottomWidgets", List.of(pauseAddonWidget));
                        pausePackData.getMeta().remove("bannerUrl");
                    }
                    packPauseResumeAction.setMeta(pausePackData.getMeta());
                    Action packPauseResumeDialogAction = CFGenericAlertDialog.getDialog(
                            "PAUSE",
                            "Pause your membership now?",
                            "pausing your membership cancels any upcoming classes",
                            List.of(dontPauseCTA, packPauseResumeAction)
                    );
                    packPauseResumeDialogAction.setAnalyticsData(getCommonAnalyticsData());
                    packPauseResumeDialogAction.setIconUrl(PAUSE_ACTIVE);
                    return packPauseResumeDialogAction;
                }
            }
            case PRO, PRO_PLUS, PRO_SELECT, LUX, LUX_GX, LUX_GROUP_CLASSES, PRO_SELECT_LITE -> {
                PackPauseResumeDetails pausePackData = GymUtil.getPackPauseResumeDetails(userContext, membership, ProductType.GYMFIT_FITNESS_PRODUCT, membership.getId());
                debugger.msg(pausePackData);
                if (Objects.nonNull(pausePackData))
                    debugger.msg("isPauseAllowed", String.valueOf(pausePackData.getIsPauseAllowed()));
                else debugger.err("pausePackData", "pause pack data is empty");
                if (Objects.nonNull(pausePackData) && pausePackData.getIsPauseAllowed()) {
                    if (AppUtil.IsAppPausePageFLActionSupported(userContext)) {
                        pausePackData.getMeta().put("onCompletionAction", onCompletionPauseAction());
                        pausePackData.getMeta().put("flAction", true);
                    }
                    Action packPauseResumeAction = new Action(NEW_MEMBERSHIP_PAUSE_PAGE + membership.getId() + "&disableAnimation=true", "PAUSE", NAVIGATION);
                    packPauseResumeAction.setVariant("primary");
                    packPauseResumeAction.setMeta(pausePackData.getMeta());
                    Action packPauseResumeDialogAction = CFGenericAlertDialog.getDialog(
                            "PAUSE",
                            "Pause your membership now?",
                            "pausing your membership cancels any upcoming classes",
                            List.of(dontPauseCTA, packPauseResumeAction)
                    );
                    packPauseResumeDialogAction.setAnalyticsData(getCommonAnalyticsData());
                    packPauseResumeDialogAction.setIconUrl(PAUSE_ACTIVE);
                    return packPauseResumeDialogAction;
                }
            }
            case PLAY, PLAY_SLP, PLAY_CITY_SLP -> {
                PackPauseResumeDetails pausePackData = CultUtil.getPackPauseResumeDetails(userContext, membership, ProductType.PLAY);
                if (Objects.nonNull(pausePackData))
                    debugger.msg("isPauseAllowed", String.valueOf(pausePackData.getIsPauseAllowed()));
                else
                    debugger.err("pausePackData", "pause pack data is empty");

                if (AppUtil.IsAppPlayPausePageFLActionSupported(userContext)) {
                    if (Objects.nonNull(pausePackData) && pausePackData.getIsPauseAllowed()) {
                        if (AppUtil.IsAppPausePageFLActionSupported(userContext)) {
                            pausePackData.getMeta().put("onCompletionAction", onCompletionPauseAction());
                            pausePackData.getMeta().put("flAction", true);
                        }
                        Action packPauseResumeAction = new Action(NEW_MEMBERSHIP_PAUSE_PAGE + membership.getId() + "&disableAnimation=true", "PAUSE", NAVIGATION);
                        packPauseResumeAction.setVariant("primary");
                        if (Objects.nonNull(pauseAddonWidget)) {
                            pausePackData.getMeta().put("bottomWidgets", List.of(pauseAddonWidget));
                            pausePackData.getMeta().remove("bannerUrl");
                        }
                        packPauseResumeAction.setMeta(pausePackData.getMeta());
                        Action packPauseResumeDialogAction = CFGenericAlertDialog.getDialog(
                                "PAUSE",
                                "Pause your membership now?",
                                "pausing your membership cancels any upcoming classes",
                                List.of(dontPauseCTA, packPauseResumeAction)
                        );
                        packPauseResumeDialogAction.setAnalyticsData(getCommonAnalyticsData());
                        packPauseResumeDialogAction.setIconUrl(PAUSE_ACTIVE);
                        return packPauseResumeDialogAction;
                    }
                } else {
                    if (Objects.nonNull(pausePackData) && pausePackData.getIsPauseAllowed()) {
                        Action packPauseResumeAction = new Action(PAUSE_CULT_MEMEBERSHIP, "PAUSE");
                        packPauseResumeAction.setVariant("primary");
                        packPauseResumeAction.setMeta(pausePackData.getMeta());

                        HashMap<String, Object> editPauseActionInMeta = new HashMap<>();
                        editPauseActionInMeta.put("numberOfPop", 1);
                        editPauseActionInMeta.put("action", packPauseResumeAction);
                        Action pausePopAction = Action.builder().actionType(MULTI_POP_THEN_NAVIGATION).title("PAUSE").meta(editPauseActionInMeta).build();
                        pausePopAction.setVariant("primary");

                        Action packPauseResumeDialogAction = CFGenericAlertDialog.getDialog(
                                "PAUSE",
                                "Pause your membership now?",
                                "pausing your membership cancels any upcoming classes",
                                List.of(dontPauseCTA, pausePopAction)
                        );
                        packPauseResumeDialogAction.setAnalyticsData(getCommonAnalyticsData());
                        packPauseResumeDialogAction.setIconUrl(PAUSE_ACTIVE);
                        return packPauseResumeDialogAction;
                    }
                }
            }
            case ONEPASS -> {
                PackPauseResumeDetails pausePackData = GymUtil.getPackPauseResumeDetails(userContext, membership, ProductType.GYMFIT_FITNESS_PRODUCT, membership.getId());
                debugger.msg(pausePackData);
                if (Objects.nonNull(pausePackData))
                    debugger.msg("isPauseAllowed", String.valueOf(pausePackData.getIsPauseAllowed()));
                else debugger.err("pausePackData", "pause pack data is empty");
                if (Objects.nonNull(pausePackData) && pausePackData.getIsPauseAllowed()) {
                    if (AppUtil.IsAppPausePageFLActionSupported(userContext)) {
                        pausePackData.getMeta().put("onCompletionAction", onCompletionPauseAction());
                        pausePackData.getMeta().put("flAction", true);
                    }
                    Action packPauseResumeAction = new Action(NEW_MEMBERSHIP_PAUSE_PAGE + membership.getId() + "&disableAnimation=true", "PAUSE", NAVIGATION);
                    packPauseResumeAction.setVariant("primary");
                    packPauseResumeAction.setMeta(pausePackData.getMeta());
                    packPauseResumeAction.setAnalyticsData(getCommonAnalyticsData());
                    packPauseResumeAction.setIconUrl(PAUSE_ACTIVE);
                    return packPauseResumeAction;
                }
            }
        }

        return null;

    }

    private CompletableFuture<BaseWidget> pauseAddonPurchaseBanner(ServiceInterfaces interfaces, UserContext userContext) throws ExecutionException, InterruptedException {

        // this widget is only for active memberships
        if (!MembershipState.ACTIVE.equals(userMembershipState) && !MembershipState.EXPIRING.equals(userMembershipState))
            return null;

        switch (userMembershipType) {
            case ELITE, ELITE_PLUS, ELITE_SELECT, ELITE_LITE, ELITE_SELECT_LITE -> {
                WidgetContext widgetContext = new WidgetContext();
                String widgetId = interfaces.environmentService.isProduction() || interfaces.environmentService.isAlpha() ? "426529a6-1be5-4502-b782-c3744cc407e8" : "4b3d2a38-32b7-4d8f-b3bd-0462b485db5a";
                CompletableFuture<BuildWidgetResponse> widgetPromise = interfaces.getWidgetBuilder().buildWidgets(Collections.singletonList(widgetId), userContext, widgetContext);
                try {
                    List<BaseWidget> baseWidgets = widgetPromise.get().getWidgets();
                    if (baseWidgets != null && !baseWidgets.isEmpty()) {
                        return CompletableFuture.completedFuture(baseWidgets.getFirst());
                    }
                    return null;
                } catch (Exception e) {
                    return null;
                }
            }
        }
        return null;
    }

    private Action getNewCancelOnGoingPauseAlertDialogAction() {

        HashMap<String, String> meta = new HashMap<>();
        meta.put("membershipServiceId", String.valueOf(membership.getId()));

        Action resumePauseAction = Action.builder().actionType(CANCEL_PACK_PAUSE).title("RESUME").meta(meta).analyticsData(getCommonAnalyticsData()).build();

        List<Action> actionList = new ArrayList<>();
        HashMap<String, Object> editPauseActionMeta = new HashMap<>();
        editPauseActionMeta.put("isEdit", true);
        editPauseActionMeta.put("membershipId", membership.getId());
        editPauseActionMeta.put("productType", "FITNESS");

        Action editPause = Action.builder().actionType(PAUSE_CULT_MEMEBERSHIP).meta(editPauseActionMeta).title("EDIT").analyticsData(getCommonAnalyticsData()).build();

        HashMap<String, Object> editPauseActionInMeta = new HashMap<>();
        editPauseActionInMeta.put("numberOfPop", 1);
        editPauseActionInMeta.put("action", editPause);
        Action editPausePopAction = Action.builder().actionType(MULTI_POP_THEN_NAVIGATION).title("EDIT").meta(editPauseActionInMeta).build();

        actionList.add(resumePauseAction);
        actionList.add(editPausePopAction);

        Action resumePauseDialogAction = CFGenericAlertDialog.getDialog(
                "MODIFY PAUSE",
                "You have paused your membership?",
                "Would you like to edit your pause days or resume it?",
                actionList
        );

        resumePauseDialogAction.setAnalyticsData(getCommonAnalyticsData());
        resumePauseDialogAction.setIconUrl(PLAY_ICON);
        return resumePauseDialogAction;

    }

    private Action getNewCancelUpcomingPauseAlertDialogAction() {

        HashMap<String, String> meta = new HashMap<>();
        meta.put("membershipServiceId", String.valueOf(membership.getId()));

        Action cancelPauseAction = Action.builder().actionType(CANCEL_PACK_PAUSE).title("YES").variant("secondary").meta(meta).build();
        Action notCancelPauseAction = Action.builder().actionType(EMPTY_ACTION).title("No").variant("primary").build();

        Action cancelPauseDialogAction = CFGenericAlertDialog.getDialog(
                "CANCEL\nPAUSE",
                "Cancel Upcoming Pause!!!",
                "Are you sure you want to cancel your upcoming pause?",
                List.of(cancelPauseAction, notCancelPauseAction)
        );

        cancelPauseDialogAction.setIconUrl(PLAY_ICON);
        return cancelPauseDialogAction;

    }

    private List<Action> getPageActions() {

        List<Action> pageActions = new ArrayList<>();

        Action primaryCta = null;
        Action secondaryCta = null;

        // write any CTA logic inside this condition
        // No CTA should be visible if the membership is not active
        if ((MembershipState.ACTIVE.equals(this.userMembershipState) || MembershipState.EXPIRING.equals(this.userMembershipState))) {
            switch (userMembershipType) {
                case ELITE, ELITE_PLUS, ELITE_COMPLIMENTARY -> {
                    primaryCta = Action.builder().title("BOOK A CLASS").actionType(NAVIGATION).url(SCHEDULE_PAGE_FITNESS).build();
                    secondaryCta = gymfitUniversalCheckinAction();
                    secondaryCta.setTitle("CHECK IN");
                    if (Objects.nonNull(membershipBenefitsItem.getOnePass())) {
                        secondaryCta = Action.builder().actionType(NAVIGATION).url(ONEPASS_ALL_CENTERS_DEEPLINK).title("VIEW GYMS").build();
                    }
                }
                case ELITE_SELECT, PRO_SELECT -> {

                    if (Objects.nonNull(accessCenter) && accessCenter.getVertical().equals(CenterVertical.CULT)) {
                        primaryCta = Action.builder().title("BOOK A CLASS").actionType(NAVIGATION).url(SCHEDULE_PAGE_FITNESS + "&centerServiceId=" + accessCenter.getId()).build();
                        secondaryCta = gymfitUniversalCheckinAction();
                        secondaryCta.setTitle("CHECK IN");
                    } else {
                        if (Objects.nonNull(accessCenter) && accessCenter.getMeta().containsKey(GYMFIT_CENTER_ID_KEY)) {
                            String centerId = accessCenter.getMeta().get(GYMFIT_CENTER_ID_KEY).toString();
                            if (Objects.nonNull(centerId)) {
                                String actionUrl = CENTER_QUICK_CHECKIN + centerId;
                                primaryCta = Action.builder().title("CHECK IN").actionType(NAVIGATION).url(actionUrl).build();
                            }
                        }

                        if (Objects.isNull(primaryCta)) {
                            primaryCta = gymfitUniversalCheckinAction();
                            primaryCta.setTitle("CHECK IN");
                        }
                    }
                }
                case ELITE_LITE, PRO, PRO_PLUS, ELITE_SELECT_LITE, PRO_SELECT_LITE -> {
                    primaryCta = gymfitUniversalCheckinAction();
                    primaryCta.setTitle("CHECK IN");
                }
                case HOME ->
                        primaryCta = Action.builder().title("BOOK A CLASS").actionType(NAVIGATION).url(LIVE_SCHEDULE_PAGE).build();
                case LUX -> {
                    primaryCta = gymfitUniversalCheckinAction();
                    primaryCta.setTitle("CHECK IN");
                    if (isMembershipHavingAnyCultOrGymBenefit()) {
                        secondaryCta = Action.builder().title("BOOK A CLASS").actionType(NAVIGATION).url(SCHEDULE_PAGE_FITNESS).build();
                    }
                }
                case LUX_GROUP_CLASSES -> {
                    primaryCta = Action.builder().title("BOOK A CLASS").actionType(NAVIGATION).url(SCHEDULE_PAGE_FITNESS + "&centerServiceId=" + accessCenter.getId()).build();
                }
                case PLAY, PLAY_COMPLIMENTARY -> primaryCta = getPlayClassBookingAction(null, "BOOK A CLASS");
                case PLAY_SLP, PLAY_LITE, PLAY_LIMITED, PLAY_CITY_SLP, PLAY_BADMINTON_LIMITED, PLAY_TT_LIMITED, PLAY_SWIM_LIMITED-> {
                    List<NameValuePair> nameValuePairs = new ArrayList<>();
                    if ((Objects.nonNull(accessCenter))) {
                        nameValuePairs.add(new BasicNameValuePair("centerId", String.valueOf(accessCenter.getId())));
                    }
                    if (Objects.nonNull(playSLPMembershipWorkoutId)) {
                        nameValuePairs.add(new BasicNameValuePair("workoutId", playSLPMembershipWorkoutId));
                    }
                    primaryCta = getPlayClassBookingAction(nameValuePairs, "BOOK A CLASS");
                }
                case ONEPASS ->
                        primaryCta = Action.builder().actionType(NAVIGATION).url(ONEPASS_ALL_CENTERS_DEEPLINK).title("CHECK IN").build();
            }
        }

        if (Objects.nonNull(primaryCta)) {
            primaryCta.setVariant("primary");
            pageActions.add(primaryCta);
            debugger.msg("primaryCta", primaryCta.toString());
        }
        if (Objects.nonNull(secondaryCta)) {
            secondaryCta.setVariant("secondary");
            pageActions.add(secondaryCta);
            debugger.msg("secondaryCta", secondaryCta.toString());
        }
        pageActions.forEach(action -> action.setAnalyticsData(getCommonAnalyticsData()));

        return pageActions;

    }

    private Action getHelpAction(ServiceInterfaces interfaces, UserContext userContext) {
        debugger.msg("Help Section Url", BASIC_SUPPORT_PAGE);
        return Action.builder().title("HELP").actionType(NAVIGATION).url(BASIC_SUPPORT_PAGE).analyticsData(getCommonAnalyticsData()).build();
    }

    private void setMembershipCardItemTitle(MembershipCardItem membershipCard) {

        switch (userMembershipType) {

            case HOME -> {
                String name = "cultpass HOME";
                if (!membership.getName().toLowerCase().contains("live") || !membership.getName().toLowerCase().contains("home")) {
                    membershipCard.setSubtitle(membership.getName());
                }
                membershipCard.setTitle(name);
            }
            case ELITE_SELECT, PRO_SELECT -> {
                membershipCard.setTitle("cultpass SELECT");
                membershipCard.setSubtitle((Objects.isNull(accessCenter) ? "" : accessCenter.getName()));
            }
            case LUX_GX -> {
                membershipCard.setTitle("Pilates");
            }
            case LUX, LUX_GROUP_CLASSES -> {
                membershipCard.setTitle("cultpass LUX");
                membershipCard.setSubtitle((Objects.isNull(accessCenter) ? "" : accessCenter.getName()));
            }
            case ELITE -> {
                if (CultUtil.isLimitedSessionEliteMembership(membership)) {
                    membershipCard.setTitle("cultpass ELITE (Limited Pack)");
                } else if (CultUtil.isCultpassXMembership(membership)) {
                    membershipCard.setTitle("ELITE + ONEPASS");
                } else if (isAllIndiaPackMembership(membership)) {
                    membershipCard.setTitle("cultpass All INDIA ELITE");
                }
            }
            case ELITE_LITE -> {
                List<String> splitTitle = new ArrayList<>(Arrays.stream(membership.getName().split(" ")).toList());
                splitTitle.removeIf(item -> item.toUpperCase().contains("LITE") || item.toUpperCase().contains("ELITE"));
                membershipCard.setTitle(splitTitle.stream().map(Object::toString).collect(Collectors.joining(" ")));

                membershipCard.setSubtitle2(new CFTextData() {{
                    setText("[H9,#FFFFFF,ELITE][P11,#FFFFFF, LITE,italic]");
                    setAlignment("start");
                    setIsRichText(true);
                    setTypeScale(UiUtils.TextTypeScales.H9);
                }});
            }
            case ELITE_SELECT_LITE, PRO_SELECT_LITE -> {
                List<String> splitTitle = new ArrayList<>(Arrays.stream(membership.getName().split(" ")).toList());
                splitTitle.removeIf(item -> item.toUpperCase().contains("LITE") || item.toUpperCase().contains("ELITE") || item.toUpperCase().contains("SELECT"));
                membershipCard.setTitle(splitTitle.stream().map(Object::toString).collect(Collectors.joining(" ")));

                membershipCard.setSubtitle2(new CFTextData() {{
                    setText("[H9,#FFFFFF,SELECT][P11,#FFFFFF, LITE,italic]");
                    setAlignment("start");
                    setIsRichText(true);
                    setTypeScale(UiUtils.TextTypeScales.H9);
                }});
            }
            case ELITE_PLUS -> {

                List<String> splitTitle = new ArrayList<>(Arrays.stream(membership.getName().split(" ")).toList());
                splitTitle.removeIf(item -> item.toUpperCase().contains("PLUS") || item.toUpperCase().contains("ELITE"));
                membershipCard.setTitle(splitTitle.stream().map(Object::toString).collect(Collectors.joining(" ")));

                membershipCard.setSubtitle2(new CFTextData() {{
                    setText("[H9,#CB8EFF,ELITE][P11,#CB8EFF, PLUS,italic]");
                    setAlignment("start");
                    setIsRichText(true);
                    setTypeScale(UiUtils.TextTypeScales.H9);
                }});

            }
            case PRO_PLUS -> {

                List<String> splitTitle = new ArrayList<>(Arrays.stream(membership.getName().split(" ")).toList());
                splitTitle.removeIf(item -> item.toUpperCase().contains("PLUS") || item.toUpperCase().contains("PRO"));
                membershipCard.setTitle(splitTitle.stream().map(Object::toString).collect(Collectors.joining(" ")));

                membershipCard.setSubtitle2(new CFTextData() {{
                    setText("[H9,#FF9F9E,PRO][P11,#FF9F9E, PLUS,italic]");
                    setAlignment("start");
                    setIsRichText(true);
                    setTypeScale(UiUtils.TextTypeScales.H9);
                }});

            }
            case PLAY_LITE -> membershipCard.setSubtitle("L  I  T  E");
            case PLAY -> membershipCard.setTitle("cultpass PLAY");
            case PLAY_CITY_SLP -> {
                playSLPMembershipWorkoutId = getWorkoutIdForPlaySportsLevelPack(membership);
                membershipCard.setTitle("cultpass %s".formatted(playSLPMembershipWorkoutId != null ? getWorkoutNameUsingID(playSLPMembershipWorkoutId) : "PLAY"));
            }
            case PLAY_BADMINTON_LIMITED -> {
                membershipCard.setTitle("cultpass Badminton (Limited Sessions)");
            }
            case PLAY_SWIM_LIMITED -> {
                membershipCard.setTitle("cultpass Swimming (Limited Sessions)");
            }
            case PLAY_TT_LIMITED -> {
                membershipCard.setTitle("cultpass Table Tennis (Limited Sessions)");
            }
            case PLAY_SLP, PLAY_LIMITED -> {
                if (Objects.nonNull(accessCenter)) {
                    membershipCard.setSubtitle(accessCenter.getName());
                }
            }
            case ONEPASS -> membershipCard.setTitle("ONEPASS");

        }

        if (Objects.isNull(membershipCard.getTitle())) {
            membershipCard.setTitle(membership.getName());
        }

    }

    private CompletableFuture<BaseWidget> getActionCardWidget(UserContext userContext, ServiceInterfaces interfaces) throws HttpException {

        if (MembershipState.EXPIRED.equals(userMembershipState) || MembershipState.CANCELLED.equals(userMembershipState))
            return null;

        CtaVerticalListWidgetView actionCard = new CtaVerticalListWidgetView();
        actionCard.setLayoutProps(getVerticalPaddingLayout("0", "30"));
        actionCard.setV2Widget(true);
        List<Action> cardActions = new ArrayList<>();

        boolean isHaveOnePassBenefit = Objects.nonNull(membershipBenefitsItem.getOnePass());

        String currentMonth = TimeUtil.getCurrentMonth();
        boolean isCurrentActiveMembership = isCurrentActiveMembership(membership, userContext);
        String thisMonthText = "This month (" + currentMonth + ")";

        if (!isCurrentActiveMembership) {
            LocalDate startDate = TimeUtil.getDateFromTime(membership.getStart(), userContext.getUserProfile().getTimezone()).toLocalDate();
            thisMonthText = startDate.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH);
        }

        Action editStartDateAction = getMembershipEditStartDateAction();
        debugger.msg(editStartDateAction);

        if (Objects.nonNull(editStartDateAction)) {
            editStartDateAction.setSubTitle("Edit Start Date");
            editStartDateAction.setTitle("Starts: " + TimeUtil.getTimeInFormatFromMillis(membership.getStart(), "yyyy-MM-dd", "Asia/Kolkata"));
            editStartDateAction.setIconUrl(CHEVRON_RIGHT_ICON);
            editStartDateAction.setImage(ACCESSORIES_IMAGE);
            cardActions.add(editStartDateAction);
        }

        if (Objects.nonNull(membershipBenefitsItem.getCenterAway()) && !isHaveOnePassBenefit) {
            cardActions.add(
                    Action.builder()
                            .title("Other Center sessions left: " + membershipBenefitsItem.getCenterAway().getRemainingTickets() + "/" + membershipBenefitsItem.getCenterAway().getTotalTickets())
                            .subTitle(membershipBenefitsItem.getCenterAway().getBenefitType() == BenefitType.MONTHLY ? thisMonthText : null)
                            .image(PLANE_ICON)
                            .build()
            );
        }

        if (Objects.nonNull(membershipBenefitsItem.getCult()) && !isHaveOnePassBenefit && !membershipBenefitsItem.getCult().getIsUnlimitedBenefit()) {
            String title = "Cult center sessions left: ";
            if (!isLimitedOrEnterpriseMembership() && (
                    FitnessCenterUtil.UserMembershipType.ELITE.equals(userMembershipType) ||
                            FitnessCenterUtil.UserMembershipType.ELITE_PLUS.equals(userMembershipType) ||
                            FitnessCenterUtil.UserMembershipType.ELITE_SELECT.equals(userMembershipType) ||
                            FitnessCenterUtil.UserMembershipType.ELITE_COMPLIMENTARY.equals(userMembershipType))
            ) {
                title = "Other Center sessions left: ";
            }
            cardActions.add(
                    Action.builder()
                            .title(title + membershipBenefitsItem.getCult().getRemainingTickets() + "/" + membershipBenefitsItem.getCult().getTotalTickets())
                            .subTitle(membershipBenefitsItem.getCult().getBenefitType() == BenefitType.MONTHLY ? thisMonthText : null)
                            .image(PLANE_ICON)
                            .build()
            );
        }

        if (Objects.nonNull(membershipBenefitsItem.getCultAway()) && !isHaveOnePassBenefit && !membershipBenefitsItem.getCultAway().getIsUnlimitedBenefit() &&
                !(CultUtil.isLimitedCenterEliteMembership(membership) || CultUtil.isLimitedSessionEliteMembership(membership))) {
            cardActions.add(
                    Action.builder()
                            .title("Other city sessions left: " + membershipBenefitsItem.getCultAway().getRemainingTickets() + "/" + membershipBenefitsItem.getCultAway().getTotalTickets())
                            .subTitle(membershipBenefitsItem.getCultAway().getBenefitType() == BenefitType.MONTHLY ? thisMonthText : null)
                            .image(PLANE_ICON)
                            .build()
            );
        }

        if (Objects.nonNull(membershipBenefitsItem.getLuxGx()) && !membershipBenefitsItem.getLuxGx().getIsUnlimitedBenefit()) {
            cardActions.add(
                    Action.builder()
                            .title("Lux group Session left: " + membershipBenefitsItem.getLuxGx().getRemainingTickets() + "/" + membershipBenefitsItem.getLuxGx().getTotalTickets())
                            .subTitle(membershipBenefitsItem.getLuxGx().getBenefitType() == BenefitType.MONTHLY ? thisMonthText : null)
                            .image(ACCESSORIES_IMAGE)
                            .build()
            );
        }

        if (Objects.nonNull(membershipBenefitsItem.getOnePass()) && !membershipBenefitsItem.getOnePass().getIsUnlimitedBenefit()) {
            String title = "OnePass sessions left: ";
            if (Objects.nonNull(membershipBenefitsItem.getCult()) || Objects.nonNull(membershipBenefitsItem.getCultAway())) {
                title = "OnePass / Cult sessions left: ";
            }
            cardActions.add(
                    Action.builder()
                            .title(title + membershipBenefitsItem.getOnePass().getRemainingTickets() + "/" + membershipBenefitsItem.getOnePass().getTotalTickets())
                            .subTitle(membershipBenefitsItem.getOnePass().getBenefitType() == BenefitType.MONTHLY ? thisMonthText : null)
                            .actionType(NAVIGATION)
                            .url(ONEPASS_ALL_CENTERS_DEEPLINK)
                            .iconUrl(CHEVRON_RIGHT_ICON)
                            .image(ACCESSORIES_IMAGE)
                            .build()
            );
        }

        if (Objects.nonNull(membershipBenefitsItem.getPlay()) && !membershipBenefitsItem.getPlay().getIsUnlimitedBenefit()) {
            cardActions.add(
                    Action.builder()
                            .title(
                                    (FitnessCenterUtil.UserMembershipType.ELITE_PLUS.equals(userMembershipType) ? "Swimming" : "Play") + " sessions left: " +
                                            membershipBenefitsItem.getPlay().getRemainingTickets() + "/" + membershipBenefitsItem.getPlay().getTotalTickets()
                            )
                            .image(SWIMMING_POOL_ICON)
                            .build()
            );
        }

        if (Objects.nonNull(membershipBenefitsItem.getPlayCorpLimited())) {
            Attribute accessSportWorkoutAttribute = getMembershipAttributeByKey(AttributeKeyType.ACCESS_SPORT_WORKOUT, membership);
            String workoutId = accessSportWorkoutAttribute.getAttrValue();
            cardActions.add(
                    Action.builder()
                            .title(getSportNameByWorkoutId(workoutId) + " Sessions left: " + membershipBenefitsItem.getPlayCorpLimited().getRemainingTickets() + "/" + membershipBenefitsItem.getPlayCorpLimited().getTotalTickets())
                            .image(workoutId.equals(SWIMMING) ? SWIMMING_POOL_ICON : BADMINTON_ICON)
                            .build()
            );
        }



        if (Objects.nonNull(membershipBenefitsItem.getPlayAway()) && !membershipBenefitsItem.getPlayAway().getIsUnlimitedBenefit()) {
            cardActions.add(
                    Action.builder()
                            .title("Other center sessions left: " + membershipBenefitsItem.getPlayAway().getRemainingTickets() + "/" + membershipBenefitsItem.getPlayAway().getTotalTickets())
                            .image(SWIMMING_POOL_ICON)
                            .build()
            );
        }

        if (Objects.nonNull(membershipBenefitsItem.getPlayLimited()) && !membershipBenefitsItem.getPlayLimited().getIsUnlimitedBenefit()) {
            cardActions.add(
                    Action.builder()
                            .title("Play sessions left: " + membershipBenefitsItem.getPlayLimited().getRemainingTickets() + "/" + membershipBenefitsItem.getPlayLimited().getTotalTickets())
                            .image(SWIMMING_POOL_ICON)
                            .build()
            );
        }

        if (Objects.nonNull(membershipBenefitsItem.getPlayOtherSports()) && !membershipBenefitsItem.getPlayOtherSports().getIsUnlimitedBenefit()) {
            cardActions.add(
                    Action.builder()
                            .title("%d/%d Other sports sessions left".formatted(membershipBenefitsItem.getPlayOtherSports().getRemainingTickets(), membershipBenefitsItem.getPlayOtherSports().getTotalTickets()))
                            .subTitle("This month (" + currentMonth + ")")
                            .image(BADMINTON_ICON)
                            .build()
            );
        }

        if (Objects.nonNull(membershipBenefitsItem.getCredits())) {
            cardActions.add(
                    Action.builder()
                            .title(membershipBenefitsItem.getCredits().getRemainingTickets() + " Credits available")
                            .subTitle("View transaction history")
                            .image(CREDIT_PILL_SVG)
                            .actionType(NAVIGATION)
                            .url(CREDIT_HISTORY_PAGE + membership.getId())
                            .iconUrl(CHEVRON_RIGHT_ICON)
                            .build()
            );
        }

        if (!isLimitedOrEnterpriseMembership() && Objects.nonNull(membershipBenefitsItem.getNoShow())) {
            cardActions.add(
                    Action.builder()
                            .title(membershipBenefitsItem.getNoShow().getUsedTickets() + "/" + membershipBenefitsItem.getNoShow().getTotalTickets() + " No-shows applied")
                            .subTitle(isCurrentActiveMembership ? "This month (" + currentMonth + ")" : null)
                            .image(CLOSE_ICON)
                            .build()
            );
        }

        cardActions.addAll(getOnePassCenterLevelSessionCard(interfaces.centerService, thisMonthText, userContext));

        Action pauseAction = getPauseResumeAction(userContext);

        if (Objects.nonNull(pauseAction)) {
            String dayString = membershipBenefitsItem.getPause().getRemainingTickets() == 0 ? "day" : "days";
            Integer totalRemainingPauseDays = membershipBenefitsItem.getPause().getRemainingTickets() - membershipBenefitsItem.getPause().getInProcessTickets();
            pauseAction.setTitle(totalRemainingPauseDays + "/" + membershipBenefitsItem.getPause().getTotalTickets() + " Pause " + dayString + " left");
            pauseAction.setImage(PAUSE_ACTIVE);
            pauseAction.setIconUrl(CHEVRON_RIGHT_ICON);
            cardActions.add(pauseAction);
        }

        if (cardActions.isEmpty()) return null;
        cardActions.forEach(action -> action.setAnalyticsData(getCommonAnalyticsData()));
        actionCard.setActions(cardActions);

        return CompletableFuture.completedFuture(actionCard);

    }

    private List<Action> getOnePassCenterLevelSessionCard(CenterService centerService, String thisMonthText, UserContext userContext) throws HttpException {
        List<Benefit> onePassCenterLevelBenefit = membership.getBenefits().stream().filter(
                item -> item.getName().toUpperCase().contains("ONEPASS") && !item.getName().equalsIgnoreCase("ONEPASS")
        ).toList();

        List<Action> onePassCenterLevelAction = new ArrayList<>();

        for (Benefit onepassBenefit : onePassCenterLevelBenefit) {
            if (
                    onepassBenefit.getMaxTickets() >= UNLIMITED_STATIC_ACCESS_TICKETS ||
                            (onepassBenefit.getType().equals(BenefitType.MONTHLY) && onepassBenefit.getMaxTickets() >= UNLIMITED_MONTHLY_ACCESS_TICKETS)
            ) continue;
            String centerServiceId = Arrays.stream(onepassBenefit.getName().split("ONEPASS")).toList().get(1);
            if (Objects.nonNull(centerServiceId)) {
                List<CenterEntry> centerList = centerService.getCachedCentersById(List.of(centerServiceId), false, null, null);
                if (Objects.nonNull(centerList) && CollectionUtils.isNotEmpty(centerList)) {
                    CenterEntry center = centerList.getFirst();
                    Integer gymfitCenterId = (Integer) center.getMeta().get(GYMFIT_CENTER_ID_KEY);
                    if (Objects.nonNull(gymfitCenterId) && center.getStatus().equals(CenterStatus.ACTIVE)) {
                        String url = ONE_PASS_CENTER_PAGE + gymfitCenterId;
                        StringBuilder centerName = new StringBuilder(center.getName());
                        if (centerName.length() > 15) {
                            centerName = new StringBuilder(centerName.substring(0, 15));
                            centerName.append("...");
                        }
                        int remainingTickets = onepassBenefit.getMaxTickets() - onepassBenefit.getTicketsUsed();
                        onePassCenterLevelAction.add(
                                Action.builder()
                                        .title(centerName + " session left: " + remainingTickets + "/" + onepassBenefit.getMaxTickets())
                                        .subTitle(isCurrentActiveMembership(membership, userContext) ? thisMonthText : null)
                                        .url(url).image(GYM_ICON).actionType(NAVIGATION).iconUrl(CHEVRON_RIGHT_ICON).build()
                        );
                    }
                }
            }
        }

        return onePassCenterLevelAction;
    }

    private CompletableFuture<BaseWidget> getMembershipBenefitsWidget(ServiceInterfaces interfaces) throws HttpException {

        ProductListWidget benefitListItemWidget = new ProductListWidget();
        benefitListItemWidget.setShowSquareIcons(true);
        List<ProductListWidgetItem> gridItems = new ArrayList<>();

        ProductListWidgetItem atCenterGroupClassesItem = ProductListWidgetItem.builder().title("At-center group classes").subTitle("Yoga, Dance fitness, Strength & more").icon(GROUP_ICON).build();
        ProductListWidgetItem smartWorkoutPlanItem = ProductListWidgetItem.builder().title("Smart workout plan").subTitle("Customised for your fitness goals").icon(SMART_WORKOUT_ICON).build();
        ProductListWidgetItem freeCreditItem = ProductListWidgetItem.builder().title("FREE Credits").subTitle("to access Other cult centers").icon(CREDIT_PILL_SVG).build();

        switch (userMembershipType) {

            case ELITE_SELECT -> {
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass ELITE Select").build());
                if (Objects.nonNull(accessCenter)) {
                    gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("at " + accessCenter.getName()).icon(GYM_ICON).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getCredits())) {
                    gridItems.add(freeCreditItem);
                }
                if (Objects.nonNull(membershipBenefitsItem.getCenterAway())) {
                    String title = (membershipBenefitsItem.getCenterAway().getBenefitType() == BenefitType.MONTHLY)
                            ? membershipBenefitsItem.getCenterAway().getTotalTickets() + " sessions/month"
                            : membershipBenefitsItem.getCenterAway().getTotalTickets() + " sessions";
                    gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("to access other cult centers in your city").icon(GROUP_ICON).build());
                }
                gridItems.add(atCenterGroupClassesItem);
                gridItems.add(smartWorkoutPlanItem);
            }
            case ELITE_LITE -> {
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass ELITE LITE").build());
                gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("All ELITE & PRO gyms in your city").icon(GYM_ICON).build());
                if (Objects.nonNull(membershipBenefitsItem.getCult())) {
                    gridItems.add(ProductListWidgetItem.builder().title("Cult access").subTitle("All Group Classes session in your city").icon(GYM_ICON).build());
                }
                gridItems.add(smartWorkoutPlanItem);
            }
            case ELITE_SELECT_LITE, PRO_SELECT_LITE -> {
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass SELECT LITE").build());
                if (Objects.nonNull(accessCenter)) {
                    gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("to gyms at " + accessCenter.getName()).icon(GYM_ICON).build());
                } else {
                    gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("to gyms at your preferred center").icon(GYM_ICON).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getCredits())) {
                    gridItems.add(freeCreditItem);
                }
                if (Objects.nonNull(membershipBenefitsItem.getCenterAway())) {
                    String title = (membershipBenefitsItem.getCenterAway().getBenefitType() == BenefitType.MONTHLY)
                            ? membershipBenefitsItem.getCenterAway().getTotalTickets() + " sessions/month"
                            : membershipBenefitsItem.getCenterAway().getTotalTickets() + " sessions";
                    gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("to access other cult centers in your city").icon(GROUP_ICON).build());
                }
                gridItems.add(smartWorkoutPlanItem);
            }
            case ELITE, ELITE_COMPLIMENTARY -> {

                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass ELITE").build());
                if (Objects.nonNull(membershipBenefitsItem.getOnePass())) {
                    benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass ELITE + OnePass").build());
                }

                if (CultUtil.isLimitedCenterEliteMembership(membership)) {

                    Optional<Attribute> accessCenterIdAttribute = membership.getAttributes().stream().filter(attr -> attr.getAttrKey().equals("accessCenterIds")).findFirst();

                    if (accessCenterIdAttribute.isPresent()) {
                        List<String> accessCenterIds = List.of(new Gson().fromJson(accessCenterIdAttribute.get().getAttrValue(), String[].class));
                        List<CenterEntry> centerEntries = interfaces.centerService.getCachedCentersById(accessCenterIds, false, null, null);
                        for (CenterEntry center : centerEntries) {
                            gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("at " + center.getName()).icon(GYM_ICON).build());
                        }
                    }

                }
                if (Objects.nonNull(membershipBenefitsItem.getCult()) && membershipBenefitsItem.getCult().getIsUnlimitedBenefit()) {
                    gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("All ELITE & PRO gyms in your city").icon(GYM_ICON).build());
                } else {
                    gridItems.add(ProductListWidgetItem.builder().title("Cult access").subTitle("All ELITE & PRO gyms in your city").icon(GYM_ICON).build());
                }

                gridItems.add(atCenterGroupClassesItem);
                gridItems.add(smartWorkoutPlanItem);

                if (Objects.nonNull(membershipBenefitsItem.getOnePass())) {
                    if (membershipBenefitsItem.getOnePass().getIsUnlimitedBenefit()) {
                        gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("at all OnePass centers").icon(GYM_ICON).build());
                    } else {
                        gridItems.add(ProductListWidgetItem.builder().title("OnePass access").subTitle("at all OnePass centers").icon(GYM_ICON).build());
                    }
                }
            }
            case PRO -> {
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass PRO").build());
                gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("to all PRO gyms in your city").icon(GYM_ICON).build());
                if (Objects.nonNull(membershipBenefitsItem.getCredits())) {
                    gridItems.add(freeCreditItem);
                }
                if (Objects.nonNull(membershipBenefitsItem.getCult())) {
                    String title = (membershipBenefitsItem.getCult().getBenefitType() == BenefitType.MONTHLY)
                            ? membershipBenefitsItem.getCult().getTotalTickets() + " sessions/month"
                            : membershipBenefitsItem.getCult().getTotalTickets() + " sessions";
                    if (membershipBenefitsItem.getCult().getIsUnlimitedBenefit()) title = "Unlimited access";
                    gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("to access ELITE cult centers in " + membershipCity.getName()).icon(GROUP_ICON).build());
                }
                gridItems.add(smartWorkoutPlanItem);
            }
            case PRO_SELECT -> {
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass PRO SELECT").build());
                if (Objects.nonNull(accessCenter)) {
                    gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("at " + accessCenter.getName()).icon(GYM_ICON).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getCredits())) {
                    gridItems.add(freeCreditItem);
                }
                if (Objects.nonNull(membershipBenefitsItem.getCult())) {
                    String title = (membershipBenefitsItem.getCult().getBenefitType() == BenefitType.MONTHLY)
                            ? membershipBenefitsItem.getCult().getTotalTickets() + " sessions/month"
                            : membershipBenefitsItem.getCult().getTotalTickets() + " sessions";
                    if (membershipBenefitsItem.getCult().getIsUnlimitedBenefit()) title = "Unlimited access";
                    gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("to access ELITE cult centers in " + membershipCity.getName()).icon(GROUP_ICON).build());
                }
                gridItems.add(smartWorkoutPlanItem);
            }
            case ELITE_PLUS -> {
                benefitListItemWidget.setHeader(Header.builder().textData(CFTextData.builder().text("Benefits of [H2,#CB8EFF,ELITE] [P5,#CB8EFF,PLUS,italic]").isRichText(true).typeScale("H3").build()).build());
                gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("All ELITE & PRO gyms in your city").icon(GYM_ICON).build());
                gridItems.add(atCenterGroupClassesItem);
                gridItems.add(smartWorkoutPlanItem);
                if (Objects.nonNull(membershipBenefitsItem.getPause())) {
                    gridItems.add(ProductListWidgetItem.builder().title("Extra Pause Days").subTitle("Take the well deserved break you need, stress free").icon(PAUSE_ENCLOSED_ICON_EP).titleGradientColor(getElitePlusGradient()).build());
                }
                gridItems.add(ProductListWidgetItem.builder().title("Membership Transfer").subTitle("Transfer your membership to other cult member").icon(TRANSFER_MEMBERSHIP_ICON_EP).titleGradientColor(getElitePlusGradient()).build());
                if (Objects.nonNull(membershipBenefitsItem.getCultAway())) {
                    gridItems.add(ProductListWidgetItem.builder().title("Other city access").subTitle("Workout at any center in India").icon(PLANE_ICON_EP).titleGradientColor(getElitePlusGradient()).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getPlay())) {
                    gridItems.add(ProductListWidgetItem.builder().title("Swim Sessions").subTitle("Access cult PLAY swim centers in your city").icon(SWIMMING_POOL_ICON_EP).titleGradientColor(getElitePlusGradient()).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getLive())) {
                    gridItems.add(ProductListWidgetItem.builder().title("1000+ workout videos").subTitle("Workout from anywhere").icon(WORKOUT_AT_HOME_ICON_EP).titleGradientColor(getElitePlusGradient()).build());
                }
                gridItems.add(ProductListWidgetItem.builder().title("upto 50% off on cultsports").subTitle("Get anything for your fitness needs").icon(CULTSPORTS_ICON_EP).titleGradientColor(getElitePlusGradient()).build());
            }
            case PRO_PLUS -> {
                benefitListItemWidget.setHeader(Header.builder().textData(CFTextData.builder().text("Benefits of [H2,#FF9F9E,PRO] [P5,#FF9F9E,PLUS,italic]").isRichText(true).typeScale("H3").build()).build());
                gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("to all PRO gyms in your city").icon(GYM_ICON).build());
                gridItems.add(smartWorkoutPlanItem);
                if (Objects.nonNull(membershipBenefitsItem.getPause())) {
                    gridItems.add(ProductListWidgetItem.builder().title("Extra Pause Days").subTitle("Take the well deserved break you need, stress free").icon(PAUSE_ENCLOSED_ICON_PP).titleGradientColor(getProPlusGradient()).build());
                }
                gridItems.add(ProductListWidgetItem.builder().title("Membership Transfer").subTitle("Transfer your membership to other cult member").icon(TRANSFER_ICON_PP).titleGradientColor(getProPlusGradient()).build());
                if (Objects.nonNull(membershipBenefitsItem.getCult())) {
                    gridItems.add(ProductListWidgetItem.builder().title("Elite center access").subTitle("Workout at ELITE center in " + membershipCity.getName()).icon(GROUP_ICON_PP).titleGradientColor(getProPlusGradient()).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getCredits())) {
                    gridItems.add(ProductListWidgetItem.builder().title("Get extra credits").subTitle("Workout at ELITE centers").icon(CREDIT_PILL_SVG).titleGradientColor(getProPlusGradient()).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getLive())) {
                    gridItems.add(ProductListWidgetItem.builder().title("1000+ workout videos").subTitle("Workout from anywhere").icon(AT_HOME_ICON_PP).titleGradientColor(getProPlusGradient()).build());
                }
                gridItems.add(ProductListWidgetItem.builder().title("upto 50% off on cultsports").subTitle("Get anything for your fitness needs").icon(CULTSPORTS_ICON_PP).titleGradientColor(getProPlusGradient()).build());
            }
            case HOME -> {
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass HOME").build());
                gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("Fitness, Dance & Meditation classes").icon(GYM_ICON).build());
                gridItems.add(ProductListWidgetItem.builder().title("Live energy meter & Leaderboard").subTitle("").icon(WORKOUT_AT_HOME_ICON).build());
                gridItems.add(ProductListWidgetItem.builder().title("Workout with friends & family").subTitle("").icon(GROUP_ICON).build());
                gridItems.add(ProductListWidgetItem.builder().title("Chromecast support").subTitle("").icon(LIVE_SCHEDULE).build());
            }
            case LUX, LUX_GX, LUX_GROUP_CLASSES -> {
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass LUX").build());
                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle((Objects.nonNull(accessCenter) ? "at " + accessCenter.getName() + ", all" : "ALL") + " ELITE & PRO gyms in your city").icon(GYM_ICON).build());
                    gridItems.add(atCenterGroupClassesItem);
                    gridItems.add(smartWorkoutPlanItem);
                } else if (Objects.nonNull(accessCenter)) {
                    String title = "Unlimited access";
                    if (Objects.nonNull(membershipBenefitsItem.getLuxGx())) title = "Access";
                    gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("at " + accessCenter.getName()).icon(GYM_ICON).build());
                }
            }
            case PLAY, PLAY_COMPLIMENTARY, PLAY_LITE -> {
                if (FitnessCenterUtil.UserMembershipType.PLAY_LITE.equals(userMembershipType)) {
                    benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass PLAY LITE").build());
                } else {
                    benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass PLAY").build());
                }
                if (Objects.nonNull(accessCenter)) {
                    gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("at " + accessCenter.getName()).icon(BADMINTON_ICON).build());
                } else if (Objects.nonNull(membershipBenefitsItem.getPlay())) {
                    String title = "Unlimited access";
                    if (!membershipBenefitsItem.getPlay().getIsUnlimitedBenefit()) title = "Play access";
                    gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("to all swimming, badminton & other sports in your city").icon(BADMINTON_ICON).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getPlayLimited())) {
                    gridItems.add(ProductListWidgetItem.builder().title(membershipBenefitsItem.getCult().getTotalTickets() + " sessions" + (membershipBenefitsItem.getPlayLimited().getBenefitType().equals(BenefitType.MONTHLY) ? "/ month" : "")).subTitle("to " + (Objects.nonNull(accessCenter) ? accessCenter.getName() : "your preferred center")).icon(GROUP_ICON).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getCult())) {
                    String title = membershipBenefitsItem.getCult().getTotalTickets() + " sessions" + (membershipBenefitsItem.getCult().getBenefitType().equals(BenefitType.MONTHLY) ? "/ month" : "");
                    if (membershipBenefitsItem.getCult().getIsUnlimitedBenefit()) title = "Unlimited access";
                    gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("at ELITE/PRO gyms & group classes in your city").icon(GROUP_ICON).build());
                }
            }
            case PLAY_SWIM_LIMITED, PLAY_BADMINTON_LIMITED, PLAY_TT_LIMITED -> {
                String workoutName = PlayUtil.getSportNameByWorkoutId(playSLPMembershipWorkoutId);
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass " + workoutName).build());
                String title = workoutName + " access";
                gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("to all centers in your city").icon(playSLPMembershipWorkoutId.equals(SWIMMING) ? SWIMMING_POOL_ICON : BADMINTON_ICON).padding(5.0).iconSize(40.0).build());
            }
            case PLAY_SLP, PLAY_LIMITED -> {
                String workoutName = getWorkoutNameUsingID(playSLPMembershipWorkoutId);
                if ("DEFAULT".equalsIgnoreCase(workoutName)) workoutName = "PLAY";
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass " + workoutName).build());
                String title = "Unlimited access";
                if (FitnessCenterUtil.UserMembershipType.PLAY_LIMITED.equals(userMembershipType)) title = "Play access";
                gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("to " + (Objects.nonNull(accessCenter) ? accessCenter.getName() : "your preferred center")).icon("SWIMMING".equalsIgnoreCase(workoutName) ? SWIMMING_POOL_ICON : BADMINTON_ICON).build());
                if (Objects.nonNull(membershipBenefitsItem.getPlayAway())) {
                    gridItems.add(ProductListWidgetItem.builder().title(membershipBenefitsItem.getPlayAway().getTotalTickets() + " sessions" + (membershipBenefitsItem.getPlayAway().getBenefitType().equals(BenefitType.MONTHLY) ? "/ month" : "")).subTitle("to access other play centers").icon(GROUP_ICON).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getCult())) {
                    String cultTitle = membershipBenefitsItem.getCult().getTotalTickets() + " sessions" + (membershipBenefitsItem.getCult().getBenefitType().equals(BenefitType.MONTHLY) ? "/ month" : "");
                    if (membershipBenefitsItem.getCult().getIsUnlimitedBenefit()) cultTitle = "Unlimited access";
                    gridItems.add(ProductListWidgetItem.builder().title(cultTitle).subTitle("at ELITE/PRO gyms & group classes in your city").icon(GROUP_ICON).build());
                }
            }
            case PLAY_CITY_SLP -> {
                String workoutName = getWorkoutNameUsingID(playSLPMembershipWorkoutId);
                if ("DEFAULT".equalsIgnoreCase(workoutName)) workoutName = "PLAY";
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of cultpass " + workoutName).build());
                String title = "Unlimited access";
                if (FitnessCenterUtil.UserMembershipType.PLAY_CITY_SLP.equals(userMembershipType))
                    title = "Unlimited %s access".formatted(workoutName);
                gridItems.add(ProductListWidgetItem.builder().title(title).subTitle("to all centers in %s".formatted(membershipCity.getName())).icon("SWIMMING".equalsIgnoreCase(workoutName) ? SWIMMING_POOL_ICON : BADMINTON_ICON).padding(5.0).build());
                if (Objects.nonNull(membershipBenefitsItem.getPlayOtherSports())) {
                    gridItems.add(ProductListWidgetItem.builder().title(membershipBenefitsItem.getPlayOtherSports().getTotalTickets() + " sessions" + (membershipBenefitsItem.getPlayOtherSports().getBenefitType().equals(BenefitType.MONTHLY) ? "/ month" : "")).subTitle("for other sports in %s".formatted(membershipCity.getName())).icon(GROUP_ICON).build());
                }
            }
            case ONEPASS -> {
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of OnePass").build());
                List<Benefit> onePassCenterLevelBenefit = membership.getBenefits().stream().filter(
                        item -> item.getName().toUpperCase().contains("ONEPASS") && !item.getName().equalsIgnoreCase("ONEPASS")
                ).toList();
                if (Objects.nonNull(membershipBenefitsItem.getOnePass()) && membershipBenefitsItem.getOnePass().getIsUnlimitedBenefit() && onePassCenterLevelBenefit.isEmpty()) {
                    gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("at all OnePass centers").icon(GYM_ICON).build());
                } else {
                    gridItems.add(ProductListWidgetItem.builder().title("OnePass access").subTitle("at all OnePass centers").icon(GYM_ICON).build());
                }
                gridItems.add(atCenterGroupClassesItem);
            }
            default -> {
                benefitListItemWidget.setHeader(Header.builder().title("Benefits of your membership").build());
                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    gridItems.add(ProductListWidgetItem.builder().title("Cult access").subTitle("All ELITE & PRO gyms in your city").icon(GYM_ICON).build());
                }
                if (Objects.nonNull(accessCenter)) {
                    gridItems.add(ProductListWidgetItem.builder().title("Unlimited access").subTitle("at " + accessCenter.getName()).icon(GYM_ICON).build());
                }
                if (Objects.nonNull(membershipBenefitsItem.getCredits())) {
                    gridItems.add(freeCreditItem);
                }
                if (isMembershipHavingAnyPlayBenefit()) {
                    gridItems.add(ProductListWidgetItem.builder().title("Play access").subTitle("Access to a variety of sports at Play Centers").icon(BADMINTON_ICON).build());
                }
            }

        }

        if (gridItems.isEmpty()) return null;
        debugger.msg(gridItems);
        benefitListItemWidget.setItems(gridItems);

        return CompletableFuture.completedFuture(benefitListItemWidget);

    }

    private CompletableFuture<BaseWidget> getMembershipPolicyWidget() {

        PolicyPageView policyPageView = getClassPolicies();
        if (Objects.isNull(policyPageView)) return null;
        CtaVerticalListWidgetView actionCard = new CtaVerticalListWidgetView();
        actionCard.setHasDivideBelow(true);
        actionCard.setV2Widget(true);

        Action policyAction = Action.builder().title("Class Policies").image(POLICY_ICON).meta(policyPageView).actionType(SHOW_CUSTOM_LIST_PAGE).iconUrl(CHEVRON_RIGHT_ICON).build();

        actionCard.setActions(Collections.singletonList(policyAction));

        return CompletableFuture.completedFuture(actionCard);

    }

    private PolicyPageView getClassPolicies() {

        FaqWidgetV2 faq = new FaqWidgetV2();
        faq.setWidgetVersion("v1");
        faq.setTitle("");
        FaqWidgetV2.Link cancellingCultClass = FaqWidgetV2.Link.builder().expanded(true).question("Cancelling/rescheduling my class").answer("You can cancel your booked classes up to 60 minutes before their start time from the homepage of the cult.fit app.").build();
        FaqWidgetV2.Link cancellingPlayClass = FaqWidgetV2.Link.builder().expanded(true).question("Cancelling/rescheduling my class").answer("You can cancel your booked classes up to 30 minutes before their start time from the homepage of the cult.fit app.").build();
        FaqWidgetV2.Link markCultAttendance = FaqWidgetV2.Link.builder().expanded(true).question("Marking my attendance").answer("Every time you attend a class at the centre you need to mark your attendance using a QR code. The QR code will be available on the app 20 minutes before the class starts. Not marking your attendance for a booked class is termed as no show and attracts a penalty.").build();
        FaqWidgetV2.Link markPlayAttendance = FaqWidgetV2.Link.builder().expanded(true).question("Marking my attendance").answer("Every time you attend a class at the enter you need to mark your attendance using a Facial Recognition system. You need to register on the FR tool the first time you attend your sports session. Not marking your attendance for a booked class is termed as a no-show and attracts a penalty.").build();
        FaqWidgetV2.Link cultNoShowPenalty = FaqWidgetV2.Link.builder().expanded(true).question("No show penalty").answer("Every time you miss a booked class you incur a no-show penalty and your membership reduces by 1 day. Please attend all the classes you book or cancel at least 60 minutes in advance so that akk skits can be optimally utilised by the members. The no-show penalty will be dismissed in case of missed class due to bad weather.").build();
        FaqWidgetV2.Link playNoShowPenalty = FaqWidgetV2.Link.builder().expanded(true).question("No show penalty").answer("Every time you miss a booked class you incur a no-show penalty and your membership reduces by 1 day. Please attend all the classes you book or cancel at least 60 minutes in advance so that akk skits can be optimally utilised by the members.").build();
        FaqWidgetV2.Link cultLastMinuteDropOut = FaqWidgetV2.Link.builder().expanded(true).question("Last minute dropout").answer("If you realise that you will be unable to attend a class less than 60 minutes before it starts, you can choose to dropout from the homepage of the cult.fit app. If another member uses your slot, we will waive the no-show penalty for you").build();
        FaqWidgetV2.Link cultGymSession = FaqWidgetV2.Link.builder().expanded(true).question("Gym sessions").answer("You can directly walk in to the gym to workout at the time that is convenient for you. No prior booking is needed. to enter the gym, you will need to checkin-in using the cult.fit app").build();
        FaqWidgetV2.Link playCultSession = FaqWidgetV2.Link.builder().expanded(true).question("Group classes at cult centers").answer("You can book a group class at any cult centre in your city. to enter the centre, you will need to check-in using the QR code on the cult.fit app. You can cancel your booked session start time or drop out until the last 60 minutes before the class start time, we will waive the no show penalty if another member uses your slot.").build();


        List<FaqWidgetV2.Link> links = new ArrayList<>();
        faq.setLayoutProps(getVerticalPaddingLayout("40", "40"));

        switch (userMembershipType) {

            case ELITE, ELITE_PLUS, ELITE_SELECT, ELITE_COMPLIMENTARY -> {
                links.add(cancellingCultClass);
                links.add(markCultAttendance);
                links.add(cultNoShowPenalty);
                links.add(cultLastMinuteDropOut);
                links.add(cultGymSession);
                faq.setLinks(links);
            }
            case ELITE_SELECT_LITE, PRO_SELECT_LITE, ELITE_LITE -> {
                links.add(cultGymSession);
                faq.setLinks(links);
            }
            case PRO, PRO_PLUS, PRO_SELECT -> {
                links.add(cultGymSession);
                links.add(cancellingCultClass);
                links.add(markCultAttendance);
                links.add(cultNoShowPenalty);
                links.add(cultLastMinuteDropOut);
                faq.setLinks(links);
            }
            case PLAY, PLAY_SLP, PLAY_CITY_SLP, PLAY_LITE, PLAY_COMPLIMENTARY, PLAY_LIMITED, PLAY_BADMINTON_LIMITED, PLAY_SWIM_LIMITED, PLAY_TT_LIMITED -> {
                links.add(cancellingPlayClass);
                links.add(markPlayAttendance);
                links.add(playNoShowPenalty);
                if (Objects.nonNull(membershipBenefitsItem.getCult())) {
                    links.add(playCultSession);
                    links.add(cultGymSession);
                }
                faq.setLinks(links);
            }

        }

        if (links.isEmpty()) return null;
        return new PolicyPageView(Collections.singletonList(faq));

    }

    private CompletableFuture<BaseWidget> getHowItWorksWidget(UserContext userContext) {

        ProductListWidget howItWorksWidget = new ProductListWidget();
        howItWorksWidget.setShowSquareIcons(true);
        howItWorksWidget.setHideSeparatorLines(true);
        howItWorksWidget.setHeader(Header.builder().title("How It Works").build());
        List<ProductListWidgetItem> list = new ArrayList<>();

        ProductListWidgetItem gymItem = new ProductListWidgetItem("Gyms: Visit a cult gym at any time & check-in via cult app & start your workout", GYM_ICON, 5.0);
        ProductListWidgetItem cultCenterItem = new ProductListWidgetItem("cult centers: Book a class that you like. Reach on time & enjoy the workout", GROUP_ICON, 5.0);
        ProductListWidgetItem sportItem = new ProductListWidgetItem("Play Sports: Book a sport that you like. Reach on time & enjoy the sport", GROUP_ICON, 5.0);

        switch (userMembershipType) {
            case ELITE, ELITE_PLUS, ELITE_COMPLIMENTARY, ELITE_SELECT -> {
                String centerText = "cult centers";
                String gymText = "cult Gym";

                if (Objects.nonNull(membershipBenefitsItem.getOnePass())) {
                    centerText = "centers";
                    gymText = "Gym";
                }

                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    list.add(new ProductListWidgetItem(centerText + ": Book a class that you like. Reach on time & enjoy the workout", GROUP_ICON, 5.0));
                    list.add(new ProductListWidgetItem("Gyms: Visit a " + gymText + " at any time & check-in via cult app & start your workout", GYM_ICON, 5.0));
                }
            }
            case ELITE_LITE, PRO_SELECT_LITE, ELITE_SELECT_LITE -> {
                list.add(gymItem);
            }
            case PRO, PRO_PLUS -> {
                list.add(gymItem);
                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    list.add(cultCenterItem);
                }
            }
            case PRO_SELECT -> {
                list.add(new ProductListWidgetItem("Gyms: Visit " + (Objects.nonNull(accessCenter) ? accessCenter.getName() : "your access center") + " at any time & check-in via cult app & start your workout", GYM_ICON, 5.0));
                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    list.add(cultCenterItem);
                }
            }
            case LUX, LUX_GROUP_CLASSES -> {
                if (Objects.nonNull(accessCenter)) {
                    String accessString = "unlimited access";
                    if (Objects.nonNull(membershipBenefitsItem.getLuxGx())) accessString = "access";
                    list.add(new ProductListWidgetItem("LUX center: Get " + accessString + " to " + accessCenter.getName(), GROUP_ICON, 5.0));
                }
                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    list.add(cultCenterItem);
                    list.add(gymItem);
                }
            }
            case LUX_GX -> {
                if (Objects.nonNull(accessCenter)) {
                    String accessString = "unlimited access";
                    if (Objects.nonNull(membershipBenefitsItem.getLuxGx())) accessString = "access";
                    list.add(new ProductListWidgetItem("Pilates center: Get " + accessString + " to " + accessCenter.getName(), GROUP_ICON, 5.0));
                }
                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    list.add(cultCenterItem);
                    list.add(gymItem);
                }
            }
            case PLAY, PLAY_LITE, PLAY_COMPLIMENTARY -> {
                list.add(sportItem);
                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    list.add(cultCenterItem);
                    list.add(gymItem);
                }
            }
            case PLAY_SLP, PLAY_LIMITED, PLAY_CITY_SLP -> {
                String workoutName = getWorkoutNameUsingID(playSLPMembershipWorkoutId);
                if ("DEFAULT".equalsIgnoreCase(workoutName)) workoutName = " Sports";
                list.add(new ProductListWidgetItem("Play " + workoutName + ": Book a slot at your preferred time. Reach on time & enjoy", GROUP_ICON, 5.0));
                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    list.add(cultCenterItem);
                    list.add(gymItem);
                }
            }
            case PLAY_BADMINTON_LIMITED, PLAY_SWIM_LIMITED, PLAY_TT_LIMITED -> {
                String workoutName = getWorkoutNameUsingID(playSLPMembershipWorkoutId);
                if ("DEFAULT".equalsIgnoreCase(workoutName)) workoutName = " Sports";
                list.add(new ProductListWidgetItem("Play " + workoutName + ": Book a slot at your preferred time. Reach on time & enjoy", GROUP_ICON, 5.0));
            }
            case ONEPASS -> {
                List<Benefit> onePassCenterLevelBenefit = membership.getBenefits().stream().filter(
                        item -> item.getName().toUpperCase().contains("ONEPASS") && !item.getName().equalsIgnoreCase("ONEPASS")
                ).toList();
                String gapFilling = "";
                if (Objects.nonNull(membershipBenefitsItem.getOnePass()) && membershipBenefitsItem.getOnePass().getIsUnlimitedBenefit() && onePassCenterLevelBenefit.isEmpty()) {
                    gapFilling = "Unlimited ";
                }
                list.add(new ProductListWidgetItem("OnePass centers: Get " + gapFilling + "access to OnePass centers", GYM_ICON, 5.0));
                list.add(new ProductListWidgetItem("OnePass classes: Book a class that you like. Reach the center on time & enjoy the workout", GROUP_ICON, 5.0));
            }
            default -> {
                if (isMembershipHavingAnyCultOrGymBenefit()) {
                    list.add(cultCenterItem);
                    list.add(gymItem);
                }
            }
        }

//        if (isMembershipHavingAnyPlayBenefit()) {
//            list.add(sportItem);
//        }
        // we do not show the Live benefit on IOS for a membership other than Home and Home addon membership. Apple T&C
        if (
                (Objects.nonNull(membershipBenefitsItem.getLive()) && userContext.getSessionInfo().getOsName().equalsIgnoreCase("android")) ||
                        isLiveBenefitOfMembership || userMembershipType.equals(FitnessCenterUtil.UserMembershipType.HOME)
        ) {
            list.add(new ProductListWidgetItem("Choose from the wide variety of online workouts and join in from anywhere.", LIVE_ICON, 5.0));
        }

        if (list.isEmpty()) return null;

        howItWorksWidget.setItems(list);
        howItWorksWidget.setLayoutProps(getVerticalPaddingLayout("0", "25"));

        return CompletableFuture.completedFuture(howItWorksWidget);

    }

    private CompletableFuture<BaseWidget> getFaqWidget() {

        FaqWidgetV2 faq = new FaqWidgetV2();
        faq.setFaqTitle("FAQs");
        faq.setTitle("FAQs");
        faq.setWidgetVersion("v1");
        faq.setOpacity(0.1);

        List<FaqWidgetV2.Link> links = new ArrayList<>();

        switch (userMembershipType) {

            case ELITE, ELITE_PLUS, ELITE_COMPLIMENTARY, ELITE_SELECT -> {
                faq.setAction(Action.builder().actionType(NAVIGATION).url(FITNESS_FAQS).build());

                if (Objects.nonNull(accessCenter)) {
                    links.add(FaqWidgetV2.Link.builder().question("what is cultpass ELITE SELECT?").answer("Cultpass ELITE SELECT is a membership pack where you get unlimited access to trainer ledGX sessions and Gym classes as you like in " + (Objects.nonNull(accessCenter) ? accessCenter.getName() : " your preferred center.")).build());
                } else if (isAllIndiaPackMembership(membership)) {
                    links.add(FaqWidgetV2.Link.builder().question("what is cultpass ALL INDIA ELITE?").answer("Cultpass all INDIA ELITE is a membership pack where you get unlimited access to trainer led group sessions and Gym classes in all centers across all cities in India").build());
                } else if (Objects.nonNull(membershipBenefitsItem.getOnePass())) {
                    links.add(FaqWidgetV2.Link.builder().question("what is cultpass ELITE + OnePass?").answer("Cultpass ELITE + OnePass is a membership pack where you get access to cult and onepass centers with trainer ledGX sessions and Gym classes as you like in the city.").build());
                } else {
                    links.add(FaqWidgetV2.Link.builder().question("what is cultpass ELITE?").answer("Cultpass ELITE is a membership pack where you get unlimited access to trainer ledGX sessions and Gym classes as you like in the city.").build());
                }

                links.add(FaqWidgetV2.Link.builder().question("What kind of workout formats are available at cult?").answer("You’ll find a wide variety of workout formats, such as - Boxing, Dance Fitness, Yoga, Adidas Strength+, and HRX.").build());
                links.add(FaqWidgetV2.Link.builder().question("How is the cult center different from a regular gym?").answer("Our non-conventional training facility offers a balanced mix of fun and effective workouts. Cult stands out with group sessions designed for holistic health and development.").build());

                if (Objects.nonNull(membershipBenefitsItem.getPause())) {
                    links.add(FaqWidgetV2.Link.builder().question("How does pause work?").answer("cultpass are built to give you the flexibility you need! Each cultpass comes with a fixed number of pause days. You can pause your cultpass as many times as you like until you exhaust the allocated pause days.").build());
                }
            }
            case ELITE_LITE, ELITE_SELECT_LITE, PRO_SELECT_LITE -> {
                if (Objects.nonNull(accessCenter)) {
                    links.add(FaqWidgetV2.Link.builder().question("what is cultpass SELECT LITE?").answer("Cultpass SELECT LITE is a membership pack where you get unlimited access to Gym classes as you like in " + (Objects.nonNull(accessCenter) ? accessCenter.getName() : " your preferred center.")).build());
                } else {
                    links.add(FaqWidgetV2.Link.builder().question("what is cultpass ELITE LITE?").answer("Cultpass ELITE LITE is a membership pack where you get unlimited access to Gym classes as you like in the city.").build());
                }
                links.add(FaqWidgetV2.Link.builder().question("How is the cult center different from a regular gym?").answer("Our non-conventional training facility offers a balanced mix of fun and effective workouts. Cult stands out with group sessions designed for holistic health and development.").build());
                if (Objects.nonNull(membershipBenefitsItem.getPause())) {
                    links.add(FaqWidgetV2.Link.builder().question("How does pause work?").answer("cultpass are built to give you the flexibility you need! Each cultpass comes with a fixed number of pause days. You can pause your cultpass as many times as you like until you exhaust the allocated pause days.").build());
                }
            }
            case PRO, PRO_PLUS, PRO_SELECT -> {
                if (Objects.nonNull(accessCenter)) {
                    links.add(FaqWidgetV2.Link.builder().question("All about cultpass PRO SELECT").answer("With cultpass PRO SELECT, you get unlimited access to " + accessCenter.getName() + ". At the gym, check in with the QR code, enjoy an optional body composition check, and enjoy your workout.").build());
                } else {
                    links.add(FaqWidgetV2.Link.builder().question("All about cultpass PRO").answer("With cultpass PRO, you get unlimited access to PRO gyms. At the gym, check in with the QR code, enjoy an optional body composition check, and enjoy your workout.").build());
                }

                links.add(FaqWidgetV2.Link.builder().question("What is a PRO gym?").answer("PRO gyms are full service gyms that offer you great value. They are a safe space to workout with top quality equipment and they have cult certified trainers present to assist you. Checkout pro gyms here.").build());
                links.add(FaqWidgetV2.Link.builder().question("How do I check-in to a gym? Is it mandatory?").answer("A unique QR code will be present on the app under the ‘Home’ tab -> ‘Checkin at gym’ section, please scan it on the device present at the reception or entry before entering the gym. Yes, it is mandatory to check-in.").build());
                links.add(FaqWidgetV2.Link.builder().question("Will there be trainers present at the gym to assist me??").answer("At every cultpass gym, there are cult-certified trainers present on the floor to guide you and assist you with your workouts.").build());
            }
            case HOME -> {
                links.add(FaqWidgetV2.Link.builder().question("What is a cultpass Home?").answer("cultpass Home gives unlimited access to workouts, health content, celebrity classes, progress tracking, and more—all from home.").build());
                links.add(FaqWidgetV2.Link.builder().question("Are At-home classes beginner-friendly?").answer("At-home classes are categorized by level: beginner, intermediate, and advanced. Consult a medical expert if you have an injury or medical condition.").build());
                links.add(FaqWidgetV2.Link.builder().question("Do I need equipment to attend At-home Classes?").answer("Most of the classes feature bodyweight workouts which means you won’t need equipment or free weights.").build());
                links.add(FaqWidgetV2.Link.builder().question("What are the types of workouts featured in At-home classes?").answer("Most of our workouts are no-equipment routines across fitness formats like strength, cardio, yoga, HRX and more. Wear comfy shoes and keep water and a napkin nearby.").build());
            }
            case PLAY, PLAY_SLP, PLAY_CITY_SLP, PLAY_LITE, PLAY_COMPLIMENTARY, PLAY_LIMITED -> {
                String unlimitedString = "Unlimited ";
                if (FitnessCenterUtil.UserMembershipType.PLAY_LIMITED.equals(userMembershipType)) unlimitedString = "";
                if (Objects.nonNull(accessCenter)) {
                    String workoutName = Objects.nonNull(playSLPMembershipWorkoutId) ? getWorkoutNameUsingID(playSLPMembershipWorkoutId) : "Play";
                    if ("DEFAULT".equalsIgnoreCase(workoutName)) workoutName = "Play";
                    links.add(FaqWidgetV2.Link.builder().question("What is Cult Pass " + workoutName + "?").answer("Cultpass " + workoutName + " is our flagship product which will give you " + unlimitedString + "access to " + accessCenter.getName()).build());
                } else {
                    links.add(FaqWidgetV2.Link.builder().question("What is Cult Pass Play?").answer("Cultpass Play is our flagship product which will give you " + unlimitedString + "access to play centers which includes various sports like Badminton, Swimming, Table Tennis etc within your city").build());
                }
                links.add(FaqWidgetV2.Link.builder().question("How is the attendance marked at the center?").answer("Attendance is marked using a Facial recognition-based (FR) system at the facility.").build());
                links.add(FaqWidgetV2.Link.builder().question("Are Cult Play coaches trained/certified?").answer("All Cult Play coaches are nationally/ internationally accredited and hold certifications for sports training.").build());
                links.add(FaqWidgetV2.Link.builder().question("Is coaching or guidance available with a sports session?").answer("Cult Play provides coaching for swimming and guidance for other sports.").build());
            }

        }

        if (links.isEmpty() && Objects.isNull(faq.getAction())) return null;
        faq.setLinks(links);
        faq.setLayoutProps(getVerticalPaddingLayout("30", "0"));

        return CompletableFuture.completedFuture(faq);

    }

    private CompletableFuture<BaseWidget> getEditStartDateInfoWidget() {
        InfoWidget infoWidget = new InfoWidget();
        infoWidget.setTitle(CFTextData.builder().text("Start date change").typeScale("H2").alignment("start").build());
        infoWidget.setSubtitle(CFTextData.builder().text("You can start your membership earlier or later, but the new start date cannot be beyond 30 days of the original start date. Start date can be changed upto 2 times.").typeScale("P5").alignment("start").opacity(0.6).maxLine("10").build());
        infoWidget.setLayoutProps(getVerticalPaddingLayout("10", "30"));
        return CompletableFuture.completedFuture(infoWidget);
    }

    private CompletableFuture<BaseWidget> getErrorInfoWidget(String error) {
        if (Objects.isNull(error)) return null;
        InfoWidget infoWidget = new InfoWidget();
        infoWidget.setSubtitle(CFTextData.builder().text(error).typeScale("P4").alignment("start").color("#FF5942").maxLine("10").build());
        infoWidget.setLayoutProps(getVerticalPaddingLayout("0", "30"));
        return CompletableFuture.completedFuture(infoWidget);
    }

    private CompletableFuture<BaseWidget> getDatePicker(String earliestStartDate, String finalStartDate, String selectedDate, UserContext userContext) {
        CtaVerticalListWidgetView datePickerWidget = new CtaVerticalListWidgetView();
        datePickerWidget.setV2Widget(true);
        datePickerWidget.setLayoutProps(getVerticalPaddingLayout("0", "20"));
        datePickerWidget.setActions(List.of(getDatePickerAction(earliestStartDate, finalStartDate, selectedDate, userContext)));
        return CompletableFuture.completedFuture(datePickerWidget);
    }

    private Action getDatePickerAction(String earliestStartDate, String finalStartDate, String selectedDate, UserContext userContext) {
        HashMap<String, String> meta = new HashMap<>();
        meta.put("startDate", earliestStartDate);
        meta.put("endDate", finalStartDate);
        meta.put("selectedDate", selectedDate);
        meta.put("membershipServiceId", String.valueOf(membership.getId()));
        String title = "Start date: " + TimeUtil.getNewAppDateFormat(membership.getStart(), userContext);
        if (Objects.nonNull(selectedDate)) {
            title = "New Start Date: " + TimeUtil.getNewAppDateFormat(selectedDate, userContext, null);
        }
        return Action.builder().actionType(SHOW_DATE_PICKER).title(title).image("/image/membership/accessories.png").iconUrl("/image/icons/cult/edit.png").meta(meta).build();
    }

    private Action getConfirmationEditStartDateAction(String newStartDate, String confirmStartDateChange, String errorMessage) {
        if (Objects.isNull(newStartDate) || Objects.isNull(confirmStartDateChange) || Objects.nonNull(errorMessage)) {
            return null;
        }
        HashMap<String, String> meta = new HashMap<>();
        meta.put("membershipServiceId", String.valueOf(membership.getId()));
        meta.put("confirmStartDateChange", "true");
        meta.put("newStartDate", newStartDate);
        return Action.builder().title("Confirm new start date").actionType(EDIT_PACK_START_DATE).variant("primary").meta(meta).build();
    }

    private HashMap<String, String> getCommonAnalyticsData() {

        HashMap<String, String> analyticsData = new HashMap<>();
        analyticsData.put("userMembershipType", String.valueOf(userMembershipType));
        analyticsData.put("userMembershipState", String.valueOf(userMembershipState));
        analyticsData.put("isLimitedOrEnterpriseMembership", String.valueOf(isLimitedOrEnterpriseMembership()));
        analyticsData.put("isEnterpriseLimitedSessionPlayMembership", String.valueOf(isEnterpriseLimitedSessionPlayMembership(membership)));

        if (Objects.nonNull(accessCenter)) {
            analyticsData.put("accessCenter", String.valueOf(accessCenter.getId()));
            analyticsData.put("accessCenterName", String.valueOf(accessCenter.getName()));
        }

        return analyticsData;

    }

    private Action getErrorAction(String msg) {
        if (Objects.isNull(msg)) msg = "Something Went Wrong!!!";
        else if (msg.length() > 200) msg = msg.substring(0, 200) + "...";
        msg = msg.replaceAll("[^a-zA-Z0-9\\s]", "");
        return CFGenericAlertDialog.getDialog(
                "Error", "Your Request can not be processed!!!", msg,
                List.of(Action.builder().actionType(POP_NAVIGATION).title("OK").build())
        );
    }

    private Action pauseNotAllowedExhaust() {
        Action pauseNotAvailableDialog = CFGenericAlertDialog.getDialog(
                "PAUSE",
                "Can't Cancel Anymore!!!",
                "You can't pause your membership. All membership pause days exhausted",
                OK_EMPTY_NAVIGATION
        );
        pauseNotAvailableDialog.setIconUrl(PAUSE_INACTIVE);
        return pauseNotAvailableDialog;
    }

    private Action pauseNotAllowedUpcoming() {
        Action pauseNotAvailableDialog = CFGenericAlertDialog.getDialog(
                "PAUSE",
                "Pause currently unavailable!!!",
                "Pause will be available once the membership is active.",
                OK_EMPTY_NAVIGATION
        );
        pauseNotAvailableDialog.setIconUrl(PAUSE_INACTIVE);
        return pauseNotAvailableDialog;
    }

    private Action upgradeNotAllowedPaused() {
        Action upgradeDisabledDialog = CFGenericAlertDialog.getDialog(
                "UPGRADE",
                "Membership has been paused!!!",
                "Membership has been paused. You can't upgrade your membership.",
                OK_EMPTY_NAVIGATION
        );
        upgradeDisabledDialog.setIconUrl(UPGRADE_INACTIVE);
        return upgradeDisabledDialog;
    }

    private Action upgradeNotAllowedActivePause() {
        Action upgradeDisabledDialog = CFGenericAlertDialog.getDialog(
                "UPGRADE",
                "Membership not allowed!!!",
                "Membership has upcoming pause. Cancel Pause to upgrade",
                OK_EMPTY_NAVIGATION
        );
        upgradeDisabledDialog.setIconUrl(UPGRADE_INACTIVE);
        return upgradeDisabledDialog;
    }

    private Action upgradeNotAllowedUpcoming() {
        Action upgradeDisabledDialog = CFGenericAlertDialog.getDialog(
                "UPGRADE",
                "Upgrade currently unavailable!!!",
                "Upgrade will be available once the membership is active",
                OK_EMPTY_NAVIGATION
        );
        upgradeDisabledDialog.setIconUrl(UPGRADE_INACTIVE);
        return upgradeDisabledDialog;
    }

    private Action transferNotAllowedPaused() {
        Action transferDisabledDialog = CFGenericAlertDialog.getDialog(
                "TRANSFER",
                "Transfer Not allowed!!!",
                "Membership has been paused. Unpause the membership to transfer.",
                OK_EMPTY_NAVIGATION
        );
        transferDisabledDialog.setIconUrl(TRANSFER_INACTIVE);
        return transferDisabledDialog;
    }

    private Action transferNotAllowedUpcoming() {
        Action transferDisabledDialog = CFGenericAlertDialog.getDialog(
                "TRANSFER",
                "Transfer currently unavailable!!!",
                "Transfer will be available once the membership is active",
                OK_EMPTY_NAVIGATION
        );
        transferDisabledDialog.setIconUrl(TRANSFER_INACTIVE);
        return transferDisabledDialog;
    }

    private Action transferNotAllowedActivePaused() {
        Action transferDisabledDialog = CFGenericAlertDialog.getDialog(
                "TRANSFER",
                "Transfer currently unavailable!!!",
                "Membership has upcoming pause. Cancel Pause to transfer.",
                OK_EMPTY_NAVIGATION
        );
        transferDisabledDialog.setIconUrl(TRANSFER_INACTIVE);
        return transferDisabledDialog;
    }

    private Action transferCurrentlyUnavailable(String message) {
        Action transferDisabledDialog = CFGenericAlertDialog.getDialog(
                "TRANSFER",
                "Transfer currently unavailable!!!",
                message,
                OK_EMPTY_NAVIGATION
        );
        transferDisabledDialog.setIconUrl(TRANSFER_INACTIVE);
        return transferDisabledDialog;
    }

    private Action transferNotAvailableLast30Days() {
        Action transferDisableAction = CFGenericAlertDialog.getDialog(
                "TRANSFER",
                "Transfer currently unavailable!!!",
                "Memberships with less than 30 remaining days are not eligible for transfer",
                OK_EMPTY_NAVIGATION
        );
        transferDisableAction.setIconUrl(TRANSFER_INACTIVE);
        return transferDisableAction;
    }

    private Action getEditStartDateSuccess(long selectedDateEpoch, UserContext userContext) {
        HashMap<String, Object> confirmActionMeta = new HashMap<>();
        confirmActionMeta.put("numberOfPop", 1);
        confirmActionMeta.put("action", refreshMembershipDetailPageAction());
        Action confirmAction = Action.builder().actionType(MULTI_POP_THEN_NAVIGATION).title("GOT IT").meta(confirmActionMeta).build();

        HashMap<String, Object> content = new HashMap<>();
        content.put("title", CFTextData.builder().text("Start date is successfully changed to").maxLine("2").typeScale("P1").letterSpacing(0).build());
        content.put("message", CFTextData.builder().text(TimeUtil.getNewAppDateFormat(selectedDateEpoch, userContext)).maxLine("4").typeScale("H2").letterSpacing(0).build());
        content.put("imageUrl", "/image/membership/tick-main.gif");
        content.put("actions", List.of(confirmAction));

        HashMap<String, Object> meta = new HashMap<>();
        meta.put("dismissible", false);
        meta.put("popupContent", content);

        return Action.builder().actionType(ActionType.SHOW_ALERT_POPUP).meta(meta).build();
    }

    private Action onCompletionPauseAction() {
        HashMap<String, Object> confirmActionMeta = new HashMap<>();
        confirmActionMeta.put("numberOfPop", 2);
        confirmActionMeta.put("action", refreshMembershipDetailPageAction());
        return Action.builder().actionType(MULTI_POP_THEN_NAVIGATION).title("GOT IT").meta(confirmActionMeta).build();
    }

    private Action getEditStartDateAllUsedErrorPopUp() {
        HashMap<String, Object> confirmActionMeta = new HashMap<>();
        confirmActionMeta.put("numberOfPop", 1);
        Action confirmAction = Action.builder().actionType(MULTI_POP_THEN_NAVIGATION).title("GOT IT").meta(confirmActionMeta).build();

        HashMap<String, Object> content = new HashMap<>();
        content.put("title", CFTextData.builder().text("you cant change your start date!!!").maxLine("2").typeScale("P1").letterSpacing(0).build());
        content.put("message", CFTextData.builder().text("you have already changed your start date twice.").maxLine("4").typeScale("P8").letterSpacing(0).build());
        content.put("actions", List.of(confirmAction));

        HashMap<String, Object> meta = new HashMap<>();
        meta.put("dismissible", false);
        meta.put("popupContent", content);

        return Action.builder().actionType(ActionType.SHOW_ALERT_POPUP).meta(meta).build();
    }

    private Action refreshMembershipDetailPageAction() {

        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("membershipServiceId", String.valueOf(membership.getId()));
        queryParams.put("skuType", String.valueOf(userMembershipType));

        HashMap<String, Object> meta = new HashMap<>();
        meta.put("queryParams", queryParams);
        return Action.builder().actionType(REFRESH_MEMBERSHIP_DETAIL_SCREEN).meta(meta).build();
    }

    private Boolean isMembershipHavingAnyCultOrGymBenefit() {
        return Objects.nonNull(membershipBenefitsItem.getCult()) ||
                Objects.nonNull(membershipBenefitsItem.getCredits()) ||
                Objects.nonNull(membershipBenefitsItem.getCultAway()) ||
                Objects.nonNull(membershipBenefitsItem.getCenterAway()) ||
                Objects.nonNull(membershipBenefitsItem.getGymfitGA()) ||
                Objects.nonNull(membershipBenefitsItem.getGymfitGX());
    }

    private Boolean isMembershipHavingAnyPlayBenefit() {
        return Objects.nonNull(membershipBenefitsItem.getPlay()) ||
                Objects.nonNull(membershipBenefitsItem.getPlayLimited()) ||
                Objects.nonNull(membershipBenefitsItem.getPlayAway());
    }

    private Boolean isLimitedOrEnterpriseMembership() {
        return isEnterpriseCultMembership(membership) ||
                CultUtil.isCultpassXMembership(membership) ||
                CultUtil.isLimitedCenterEliteMembership(membership) ||
                CultUtil.isLimitedSessionEliteMembership(membership) ||
                CultUtil.isJPMCMembership(membership);
    }

    private CFGradientColorData getElitePlusGradient() {
        return CFGradientColorData.builder().direction("DIAGONAL").gradientType("LINEAR").colors(new ArrayList<>() {{
            add("#9648FF");
            add("#EED3FF");
            add("#6A4693");
        }}).build();
    }

    private CFGradientColorData getProPlusGradient() {
        return CFGradientColorData.builder().direction("DIAGONAL").gradientType("LINEAR").colors(new ArrayList<>() {{
            add("#FF9F9E");
            add("#FFDADD");
            add("#FFBDBC");
            add("#FB857F");
        }}).build();
    }

    // Temporary fix: Introducing a slight delay to ensure the memberships cache is fully refreshed.
    // This addresses a known issue where the cache takes a few extra milliseconds to stabilize
    // after being refreshed by the Cult API. This approach ensures a smoother UX.
    // This will fix majority of refresh issue.
    private void threadSleep() {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException ignored) {
        }
    }

    private Map<String, Object> getVerticalPaddingLayout(String topPadding, String bottomPadding) {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", Spacing.builder().top(topPadding).bottom(bottomPadding).build());
        return layoutProps;
    }
}
