package com.curefit.cfapi.view.viewmodels.fitso.prebooking;

import com.curefit.product.enums.ProductType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FitsoUnpauseAndBookActionMeta {
    String membershipId;
    ProductType productType;
    String toastMessage;
    Boolean isUpcomingPause;
    Map<String, Long> slot;
}
