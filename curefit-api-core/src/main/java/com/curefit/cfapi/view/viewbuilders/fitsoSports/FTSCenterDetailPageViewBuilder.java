package com.curefit.cfapi.view.viewbuilders.fitsoSports;

import com.curefit.center.dtos.*;
import com.curefit.cfapi.builder.vm.BuildWidgetResponse;
import com.curefit.cfapi.model.internal.cult.DistanceRequest;
import com.curefit.cfapi.model.internal.cult.LocationPreference;
import com.curefit.cfapi.model.internal.cult.PackOfferDetails;
import com.curefit.cfapi.model.internal.userinfo.SessionInfo;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.atom.*;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.gymsCentersNearby.CardItemSubTitle;
import com.curefit.cfapi.view.viewbuilders.fitso.ProductListWidget;
import com.curefit.cfapi.view.viewmodels.OnboardingCardDetails;
import com.curefit.cfapi.view.viewmodels.common.ProductListWidgetItem;
import com.curefit.cfapi.view.viewmodels.fitness.CenterDetailsPageView;
import com.curefit.cfapi.view.viewmodels.gymfit.CentersPageView;
import com.curefit.cfapi.widgets.GymCentersNearbyWidget;
import com.curefit.cfapi.widgets.OnboardingWidget;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.WidgetHeader;
import com.curefit.cfapi.widgets.fitness.*;
import com.curefit.cfapi.widgets.fitso.PlayPackComparisonWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.gymfit.dtos.GymDemandPredictionAtHour;
import com.curefit.cfapi.widgets.fitso.prebooking.ActionWidget;
import com.curefit.gymfit.models.CenterSchedule;
import com.curefit.location.models.LatLong;
import com.curefit.membership.pojo.entry.Benefit;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.offers.dtos.OfferMini;
import com.curefit.offers.dtos.cult.ProductPriceResponse;
import com.curefit.offers.dtos.play.PlayProductPricesResponse;
import com.curefit.product.ProductPrice;
import com.curefit.product.models.cult.FitnessPack;
import com.curefit.sportsapi.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.curefit.cfapi.config.CircuitBreakerConfiguration.CircuitBreakerName.GYMFIT_SERVICE;
import static com.curefit.cfapi.pojo.app.action.ActionType.*;
import static com.curefit.cfapi.pojo.vm.atom.UiUtils.UIColors.STATUS_ALERT;
import static com.curefit.cfapi.util.FitnessCenterUtil.getCardSubTitles;
import static com.curefit.cfapi.util.PlayUtil.*;
import static com.curefit.cfapi.util.TimeUtil.DEFAULT_ZONE_ID;


@Slf4j
public class FTSCenterDetailPageViewBuilder {

    protected ServiceInterfaces interfaces;
    protected WidgetContext widgetContext;
    private boolean isSportsSelectedPrevious;
    private boolean isSportLevelPricingSupported;
    private boolean isSportCityLevelPricingSupported;
    private boolean isUserBelongToRenewD60;
    private CenterEntry centerEntry;
    String googleReviewsWidgetId;
    UserContext userContext;

    private String workoutId;

    public FTSCenterDetailPageViewBuilder(ServiceInterfaces interfaces, WidgetContext widgetContext) {
        this.interfaces = interfaces;
        this.widgetContext = new WidgetContext(widgetContext);
    }

    public CenterDetailsPageView buildView(UserContext userContext, FTSCenterInfo center) throws Exception {
        boolean isUpcomingCenter = false;

        this.userContext = userContext;
        googleReviewsWidgetId = UUID.randomUUID().toString();
        Map<String, String> queryParams = widgetContext.getQueryParams();
        workoutId = queryParams.get("workoutId") != null ? queryParams.get("workoutId") : null;
        isSportsSelectedPrevious = queryParams.containsKey("workoutId");
        FTSSportsInfo selectedSport = null;
        Boolean isNonMembershipUser = interfaces.segmentEvaluatorService.checkCondition(
                Collections.singletonList(PlayUtil.PLAY_NON_MEMBERSHIP_SEGMENT_ID), userContext).get() != null;
        boolean isStage = interfaces.environmentService.isStage();

        if (center.getSportsAtCenter() != null && center.getSportsAtCenter().size() == 1){
            workoutId = center.getSportsAtCenter().get(0).getId().toString();
            isSportsSelectedPrevious = true;
        }

        if (isSportsSelectedPrevious) {
            List<FTSSportsInfo> selectedSports = center.getSportsAtCenter().stream().filter(sportAtCenter -> workoutId.equals(String.valueOf(sportAtCenter.getId()))).toList();
            if (!selectedSports.isEmpty()) {
                selectedSport = selectedSports.get(0);
            }
        }

        isSportLevelPricingSupported = PlayUtil.isSportLevelPricingSupported(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
        isSportCityLevelPricingSupported = PlayUtil.isBelongToCityLevelSLP(interfaces, userContext);
        isUserBelongToRenewD60 = PlayUtil.isUserBelongToRenewCitySLPD60Membership(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
        try {
            centerEntry = interfaces.centerService.getCenterDetails(center.getId(), true, Collections.emptyMap(), null).get();
        } catch (Exception e) {
            log.info("Error in pack comparison widget", e);
        }
        if (center.getLaunchDate() != null) {
            Instant instant = Instant.ofEpochMilli(Long.parseLong(center.getLaunchDate()));
            LocalDateTime launchDate = LocalDateTime.ofInstant(instant, ZoneId.of(userContext.getUserProfile().getTimezone()));
            isUpcomingCenter = center.getLaunchDate() != null && launchDate.isAfter(TimeUtil.getDateNow(DEFAULT_ZONE_ID));
        }
        List<FitnessPack> selectPacks = interfaces.catalogueService.getNewPlaySelectPackByCenterId(widgetContext.getQueryParams().get("centerId"), true, userContext.getUserProfile().getUserId());
        List<FitnessPack> sportLevelPacks = Collections.emptyList();
        Map<String, List<FitnessPack>> allCityLevelSportPacks =  PlayUtil.getAllCityLevelSportPacks(interfaces, userContext);
        Map<String, List<FitnessPack>> allSportLevelPacks =  PlayUtil.getAllSportLevelPacksByCenterId(center.getId().toString(), interfaces, userContext);
        if (workoutId != null) {
            sportLevelPacks = PlayUtil.getSportSpecificPacks(allSportLevelPacks, workoutId);
        }

        CenterDetailsPageView centerPage = new CenterDetailsPageView();
        if (selectPacks != null && !selectPacks.isEmpty()) {
            centerPage.setHasSelectPack(true);
        }

        selectPacks = selectPacks.stream().filter(pack -> pack.getVisibility() != null && pack.getVisibility().contains("app")).toList();
        centerPage.setTitle(center.getName());
        List<Membership> playMemberships = PlayUtil.getCurrentPlayMemberships(userContext, interfaces);
        List<CenterHoliday> centerHolidays;
        try {
            centerHolidays = interfaces.centerService.getCenterHolidayDetails(center.getId(), centerEntry.getVertical().toString(), null, null);
        } catch (Exception e) {
            centerHolidays = new ArrayList<>();
            interfaces.exceptionReportingService.reportException("Error in getting Center Holidays", e);
        }
        List<CompletableFuture<BaseWidget>> contentWidgets = new ArrayList<>();
        contentWidgets.add(getCenterSummaryWidget(center, userContext));
        contentWidgets.add(getAddressWidget(userContext, centerEntry));
        contentWidgets.add(getTimingWidgetV2(centerEntry, centerHolidays, isUpcomingCenter, userContext));

        if (isSportCityLevelPricingSupported){
            if (workoutId == null && !center.getSportsAtCenter().isEmpty() && center.getSportsAtCenter().size() > 1) {
                CompletableFuture<BaseWidget> sportWidget = getSportsAvailableSLP(center.getSportsAtCenter(), center.getBookingStatus(), center.getId(),
                        null, null, isUpcomingCenter, playMemberships, userContext);
                if (sportWidget != null)
                    contentWidgets.add(sportWidget);
            }

            contentWidgets.add(getPackComparisonMarkerWidget());

            if (isUserBelongToRenewD60) {
                FitnessPackBrowseWidget fitnessPackBrowseWidget = new FitnessPackBrowseWidget();
                HashMap<String, String> playPackDetails = fitnessPackBrowseWidget.getPlayPackItemDetails(userContext, interfaces, new HashMap<>());

                if (isSportsSelectedPrevious) {
                    sportLevelPacks = PlayUtil.getSportSpecificPacks(allCityLevelSportPacks, workoutId);
                    HashMap<String, String> sportsPackDetails = PlayUtil.getSportsPackDetails(centerEntry, sportLevelPacks, userContext, interfaces);
                    contentWidgets.add(getPackComparisonWidgetV2(centerEntry, sportsPackDetails, playPackDetails, workoutId));
                } else {
                    contentWidgets.add(getPackComparisonWidgetV2WithWorkout(
                            interfaces,
                            center.getSportsAtCenter(),
                            centerEntry,
                            userContext,
                            allCityLevelSportPacks,
                            playPackDetails));
                }
            }

            if (center.getCenterOfferings() != null && !center.getCenterOfferings().isEmpty()) {
                contentWidgets.add(getCentersOfferingWidget(center.getCenterOfferings()));
            }

            if (center.getFacilities() != null && !center.getFacilities().isEmpty()) {
                contentWidgets.add(getSingleRowFacilitiesWidget(center.getFacilities()));
            }

            if (workoutId != null && !center.getSportsAtCenter().isEmpty()) {
                CompletableFuture<BaseWidget> sportWidget = getSportsAvailableSLP(center.getSportsAtCenter(), center.getBookingStatus(), center.getId(), workoutId,
                        allSportLevelPacks, isUpcomingCenter, playMemberships, userContext);
                if (sportWidget != null)
                    contentWidgets.add(sportWidget);
            }
        } else if (isSportLevelPricingSupported && userContext.getSessionInfo().getSessionData().getCityId().equals(centerEntry.getCity())) {
            HashMap<String, String> sportsPackDetails = PlayUtil.getSportsPackDetails(centerEntry, sportLevelPacks, userContext, interfaces);

            if (workoutId == null && !center.getSportsAtCenter().isEmpty() && center.getSportsAtCenter().size() > 1) {
                CompletableFuture<BaseWidget> sportWidget = getSportsAvailableSLP(center.getSportsAtCenter(), center.getBookingStatus(), center.getId(),
                        null, null, isUpcomingCenter, playMemberships, userContext);
                if (sportWidget != null)
                    contentWidgets.add(sportWidget);
            }
            List<FTSSportOffering> offeringsList = new ArrayList<>();
            if (center.getCenterOfferings() != null && !center.getCenterOfferings().isEmpty()) {
                offeringsList = center.getCenterOfferings();
                if (isSportsSelectedPrevious)
                    offeringsList = center.getCenterOfferings().stream().filter(ftsSportOffering -> workoutId.equals(String.valueOf(ftsSportOffering.getWorkoutId()))).toList();
                if (!offeringsList.isEmpty())
                    contentWidgets.add(getCentersOfferingWidget(offeringsList));
            }

            if (center.getFacilities() != null && !center.getFacilities().isEmpty()) {
                contentWidgets.add(getSingleRowFacilitiesWidget(center.getFacilities()));
            }

            contentWidgets.add(getPackComparisonMarkerWidget());
            if (isSportsSelectedPrevious) {
                contentWidgets.add(CompletableFuture.completedFuture(getPackComparisonWidget(centerEntry, center.getName(), workoutId, userContext, selectedSport, sportsPackDetails)));
            } else {
                contentWidgets.add(getPackComparisonWidgetWithWorkout(interfaces, center.getSportsAtCenter(), centerEntry, userContext, allSportLevelPacks));
            }

            if (workoutId != null && !center.getSportsAtCenter().isEmpty()) {
                CompletableFuture<BaseWidget> sportWidget = getSportsAvailableSLP(center.getSportsAtCenter(), center.getBookingStatus(), center.getId(), workoutId,
                        allSportLevelPacks, isUpcomingCenter, playMemberships, userContext);
                if (sportWidget != null)
                    contentWidgets.add(sportWidget);
            }

            if (workoutId != null){
                List<FTSSportOffering> offeringList = new ArrayList<>();
                if (center.getCenterOfferings() != null && !center.getCenterOfferings().isEmpty()) {
                    offeringList = center.getCenterOfferings().stream().filter(ftsSportOffering -> !workoutId.equals(String.valueOf(ftsSportOffering.getWorkoutId()))).toList();
                    if (!offeringList.isEmpty())
                        contentWidgets.add(getCentersOfferingWidget(offeringList));
                }
            }
        } else {
            if (!center.getSportsAtCenter().isEmpty()) {
                contentWidgets.add(getSportsAvailable(center.getSportsAtCenter(), center.getBookingStatus(), center.getId(),
                        isUpcomingCenter, playMemberships, userContext));
            }
            if (center.getFacilities() != null && !center.getFacilities().isEmpty()) {
                contentWidgets.add(getFacilitiesWidget(center.getFacilities()));
            }

            if (center.getCenterOfferings() != null && !center.getCenterOfferings().isEmpty()) {
                contentWidgets.add(getCentersOfferingWidget(center.getCenterOfferings()));
            }

            if (center.getBookingStatus() == BookingEligibility.SELECT_MEMBERSHIP) {
                Membership playMembership = playMemberships.get(0);
                Benefit playBenefit = playMembership.getBenefits().stream().filter(benefit -> benefit.getName().equals("PLAY")).collect(Collectors.toList()).get(0);
                Boolean isCurrentCenterAvailable = isPlayLiteCurrentCenterAvailable(playBenefit, playMembership, center.getId());
                if (!isCurrentCenterAvailable) {
                    contentWidgets.add(getMembershipWidget(playMembership, userContext));
                }
            } else if (isNonMembershipUser || isStage || playMemberships.isEmpty()) {
                List<String> widgetIds = new ArrayList<>();
                if (PlayUtil.isPlaySelectSupported(userContext, interfaces) && selectPacks != null && !selectPacks.isEmpty()) {
                    String tabWidgetId = PlayUtil.getSelectSKUTabWidgetId(interfaces);
                    widgetIds.add(PlayUtil.getPlayAndSelectComparisionWidgetId(interfaces));
                    widgetIds.add(tabWidgetId);
                    widgetContext.putQueryParam("isV2", "true");
                } else {
                    String playPackBrowseWidgetId = PlayUtil.getPlayPackBrowseWidgetId(interfaces);
                    widgetIds.add(playPackBrowseWidgetId);
                }

                CompletableFuture<BuildWidgetResponse> widgetResponse = interfaces.getWidgetBuilder().buildWidgets(widgetIds, userContext, widgetContext);

                if (widgetResponse != null && CollectionUtils.isNotEmpty(widgetResponse.get().getWidgets())) {
                    for (BaseWidget widget: widgetResponse.get().getWidgets()) {
                        widget.addSpacing("0", "10");
                        contentWidgets.add(CompletableFuture.completedFuture(widget));
                    }
                }
            }
        }

        if (center.getSafetyMeasures() != null && !center.getSafetyMeasures().isEmpty())
            contentWidgets.add(getSafetyMeasures(center.getSafetyMeasures()));

        if ((isSportLevelPricingSupported || isSportCityLevelPricingSupported) && userContext.getSessionInfo().getSessionData().getCityId().equals(centerEntry.getCity())) {
            ActionWidget actionWidget = new ActionWidget();
            List<Action> actions = new ArrayList<>();

            boolean hasSportLevelPacks = workoutId != null ? sportLevelPacks != null && !sportLevelPacks.isEmpty() : !allSportLevelPacks.isEmpty();
            Action buyNowAction = getBuyNowAction(hasSportLevelPacks || (isSportCityLevelPricingSupported && isUserBelongToRenewD60 && allCityLevelSportPacks != null && !allCityLevelSportPacks.isEmpty()), center);
            Action bookingAction = getCTAAction(center.getBookingStatus(), center.getId(), workoutId, isUpcomingCenter,
                    playMemberships, userContext, (isSportLevelPricingSupported  || isSportCityLevelPricingSupported) && buyNowAction != null);
            boolean isNotOperationalCenter = SelectPacksUtils.isActiveButNotOperationalLaunchedCenter(centerEntry, userContext);
            if (bookingAction != null && !isNotOperationalCenter)
                actions.add(bookingAction);
            if (buyNowAction != null) {
                actions.add(buyNowAction);
            }

            actionWidget.setActions(actions);
            Map<String, Object> layoutProps = new HashMap<>();
            layoutProps.put("spacing", Spacing.builder().top("30").bottom("0").build());
            actionWidget.setLayoutProps(layoutProps);
            centerPage.setActionsWidget(actionWidget);
        } else {
            centerPage.addAction(getCTAAction(center.getBookingStatus(), center.getId(), workoutId, isUpcomingCenter, playMemberships, userContext, false));
        }
        // centerPage.addAction(getSecondaryAction());

        contentWidgets.add(getGoogleReviewsWidget(center));
        contentWidgets.add(getNearByCenter(center, userContext, interfaces, workoutId));

        contentWidgets = contentWidgets.stream().filter(Objects::nonNull).toList();
        CompletableFuture<List<BaseWidget>> widgetsPromise = FutureUtil.allOf(contentWidgets);
        centerPage.addWidgets(widgetsPromise.get().stream().filter(Objects::nonNull).toList());
        centerPage.setScrollToWidgetId(widgetContext.getQueryParams().get("scrollToWidgetId"));
        return centerPage;
    }

    private CompletableFuture<BaseWidget> getPackComparisonWidgetV2WithWorkout(
            ServiceInterfaces interfaces,
            List<FTSSportsInfo> sportsAtCenter,
            CenterEntry centerEntry,
            UserContext userContext,
            Map<String, List<FitnessPack>> allSportsLevelPacks,
            HashMap<String, String> playPackDetails
    ) throws Exception {
        PlayPackComparisonWidget widget = new PlayPackComparisonWidget();
        List<PlayPackComparisonWidget.PlayWorkoutFilter> playWorkoutFilters = new ArrayList<>();
        List<PlayPackComparisonWidget.PackFilter> filters = new ArrayList<>();

        if (PlayUtil.isUserBelongToRenewPlayMembership(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext))
            widget.setTitle("Renew CultPass Play");
        else
            widget.setTitle("Buy Membership");

        PlayPackComparisonWidget.PackFilter sportsFilter = new PlayPackComparisonWidget.PackFilter("workouts", CentersPageView.FilterType.INLINE, CentersPageView.SelectionType.SINGLE_SELECT);
        for (FTSSportsInfo sportsInfo : sportsAtCenter) {
            PlayPackComparisonWidget.PlayWorkoutFilter workoutFilter = new PlayPackComparisonWidget.PlayWorkoutFilter();
            workoutFilter.setId(sportsInfo.getId().toString());
            workoutFilter.setTitle(sportsInfo.getName());
            List<FitnessPack> sportsPacks = PlayUtil.getSportSpecificPacks(allSportsLevelPacks, sportsInfo.getId().toString());
            if (sportsPacks == null)
                continue;


            HashMap<String, String> sportsPackDetails = PlayUtil.getSportsPackDetails(centerEntry, sportsPacks, userContext, this.interfaces);
            BaseWidget packWidget = getPackComparisonWidgetV2(centerEntry, sportsPackDetails, playPackDetails, sportsInfo.getId().toString()).get();

            if (packWidget == null)
                continue;
            workoutFilter.setWidget(packWidget);
            playWorkoutFilters.add(workoutFilter);
        }
        if (playWorkoutFilters.isEmpty())
            return null;
        sportsFilter.setWorkoutFilterValues(playWorkoutFilters);
        filters.add(sportsFilter);
        widget.setWorkoutFilters(filters);
        return CompletableFuture.completedFuture(widget);
    }

    protected CompletableFuture<BaseWidget> getPackComparisonWidgetV2(CenterEntry center, HashMap<String,String> sportPackDetail, HashMap<String,String> playPackDetail, String workoutId) throws Exception {
        PackComparisonWidgetV2 widget = new PackComparisonWidgetV2();
        if(sportPackDetail == null || sportPackDetail.isEmpty() || playPackDetail == null)
            return CompletableFuture.completedFuture(null);

        String workoutName = PlayUtil.getWorkoutShortNameUsingID(workoutId);
        String workoutTagUrl = PlayUtil.getWorkoutTagUrlUsingIDForCitySLP(workoutId);

        //for Sport Pack
        PackComparisonWidgetV2.PackCardData sportCardData = new PackComparisonWidgetV2.PackCardData();
        sportCardData.setTagUrl(workoutTagUrl);
        sportCardData.setBackgroundImage("/image/icons/fitsoImages/city_slp/sports_bg.png");
        sportCardData.setBackgroundColorGradient(List.of("#F6746D", "#D769B6", "#AB77FF"));
        String selectBuyDeeplink = PlayUtil.getPackComparisonPageDeeplinkForCitySLP(workoutId);
        Action selectCardAction = new Action(selectBuyDeeplink, "BUY %s".formatted(workoutName), ActionType.NAVIGATION);
        HashMap<String, String> analyticsData = new HashMap<>();
        analyticsData.put("buttonTitle", "BUY %s".formatted(workoutName));
        analyticsData.put("deeplink", selectBuyDeeplink);
        analyticsData.put("widgetType", "PACK_COMPARISON_WIDGET_V2");
        analyticsData.putAll(getAnalyticsDataForCenter(center));
        selectCardAction.setAnalyticsData(analyticsData);
        sportCardData.setWidgetAction(selectCardAction);
        sportCardData.setBlocks(getPackComparisonBlocks(
                PlayUtil.getSportNameByWorkoutId(workoutId),
                sportPackDetail.get("minPrice"),
                sportPackDetail.getOrDefault("topPackDuration", ""),
                sportPackDetail.getOrDefault("pauseDays", ""),
                true,
                sportPackDetail.getOrDefault("otherSportSessions", "5")
        ));

        //for PLAY PACK
        PackComparisonWidgetV2.PackCardData playPackCardData = new PackComparisonWidgetV2.PackCardData();
        playPackCardData.setBackgroundImage("/image/icons/fitsoImages/city_slp/play_bg.png");
        playPackCardData.setBackgroundColorGradient(List.of("#2CA278", "#44FFBC", "#2CA278"));
        playPackCardData.setTagUrl("/image/icons/fitsoImages/city_slp/play_tag.png");
        if(playPackDetail == null) {
            playPackDetail = new HashMap<>();
        }
        String playBuyDeeplink = SPORTS_PACK_COMPARISON_PAGE_DEEPLINK + "selectedSku=PLAY&centerType=PLAY&workoutId=" + workoutId;
        Action playPackBuyAction = new Action(playBuyDeeplink, "BUY PLAY", ActionType.NAVIGATION);
        HashMap<String, String> playAnalyticsData = new HashMap<>();
        playAnalyticsData.put("buttonTitle", "BUY PLAY");
        playAnalyticsData.put("centerId", center.getId().toString());
        playAnalyticsData.put("deeplink", playBuyDeeplink);
        playAnalyticsData.put("widgetType", "PACK_COMPARISON_WIDGET_V2");
        playPackBuyAction.setAnalyticsData(playAnalyticsData);
        playPackCardData.setWidgetAction(playPackBuyAction);
        playPackCardData.setWidgetAction(playPackBuyAction);
        playPackCardData.setBlocks(getPackComparisonBlocks(
                workoutName,
                playPackDetail.getOrDefault("minPrice", ""),
                playPackDetail.getOrDefault("topPackDuration",""),
                playPackDetail.getOrDefault("pauseDays",""),
                false,
                "Unlimited"));

        if (sportCardData.getBlocks().size() != playPackCardData.getBlocks().size()) {
            this.interfaces.exceptionReportingService.reportWarningMessage("Data Missing Warning: Size of the City Pack and Select Pack blocks are not equal for center id: " + center.getId() + ", center name: " + center.getName());
        }
        widget.setPackCardData(List.of(sportCardData, playPackCardData));
        widget.addSpacing("20","40");
        if (isSportsSelectedPrevious) {
            if (PlayUtil.isUserBelongToRenewPlayMembership(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext))
                widget.setTitle("Renew CultPass Play");
            else
                widget.setTitle("Buy Membership");
        }
        return CompletableFuture.completedFuture(widget);
    }

    protected List<PackComparisonWidgetV2.Blocks> getPackComparisonBlocks(String workoutName, String priceTag, String monthShownOnCard, String minPauseDays, boolean isSportsPack, String otherSession) {
        List<PackComparisonWidgetV2.Blocks> blocks = new ArrayList<>();
        if (isSportsPack) {
            blocks.add(PackComparisonWidgetV2.Blocks.builder().header("Unlimited access to").description(workoutName).hasDivideBelow(true).build());
        } else {
            blocks.add(PackComparisonWidgetV2.Blocks.builder().header("Unlimited access to").description("All Sports").hasDivideBelow(true).build());
        }
        if (isSportsPack) {
            blocks.add(PackComparisonWidgetV2.Blocks.builder()
                    .header("Upto").description(otherSession + " other sports sessions in any center").descriptionSize(22 * 3.0).hasDivideBelow(true).build());
        } else {
            blocks.add(PackComparisonWidgetV2.Blocks.builder()
                    .header("").description(otherSession + " sports sessions in any center").descriptionSize(22 * 3.0).hasDivideBelow(true).build());
        }
        if (Objects.nonNull(monthShownOnCard) && Objects.nonNull(minPauseDays)) {
            blocks.add(PackComparisonWidgetV2.Blocks.builder()
                    .description("%s pause days".formatted(minPauseDays)).hasDivideBelow(true)
                    .footer(PackComparisonWidgetV2.Footer.builder().text("in " + monthShownOnCard + " months")
                            .build()).build());
        }
        if (Objects.nonNull(priceTag)) {
            blocks.add(PackComparisonWidgetV2.Blocks.builder().header("Starting from").description(priceTag).hasDivideBelow(false).descriptionTypeScale("P3").build());
        }
        blocks.getLast().setHasDivideBelow(false);
        return blocks;
    }

    private Map<String, String> getAnalyticsDataForCenter(CenterEntry center){
        return Map.of(
                "pageId", "play_center",
                "centerServiceId", center.getId().toString(),
                "centerType", "PLAY",
                "centerName", center.getName()
        );
    }

    protected CompletableFuture<BaseWidget> getGoogleReviewsWidget(FTSCenterInfo center) throws HttpException {
        GoogleReviewWidget widget = new GoogleReviewWidget();
        TopCenterReviewResponse reviewResponse = interfaces.reviewService.getTopGoogleReviews(center.getId(), null ,null);
        if(reviewResponse == null || reviewResponse.getReviews().isEmpty() || reviewResponse.getAverageRating() < 4.0) {
            return CompletableFuture.completedFuture(null);
        }
        DecimalFormat df = new DecimalFormat("#.#");
        List<GoogleReviewWidget.ReviewData> reviewDataList = new ArrayList<>();
        for (CenterReviewResponse centerReviewResponse : reviewResponse.getReviews()) {
            String description = centerReviewResponse.getReviewComment();
            String iconUrl = "image/luxury/rating_star.svg";
            double rating = centerReviewResponse.getRating().doubleValue();
            String name = null;
            String avatarUrl = null;
            if(centerReviewResponse.getReviewer() != null) {
                name = centerReviewResponse.getReviewer().getDisplayName();
                avatarUrl = centerReviewResponse.getReviewer().getProfilePhotoUrl();
            }
            String dateString = GymUtil.getDateString(centerReviewResponse.getReviewDate());
            GoogleReviewWidget.ReviewData r = new GoogleReviewWidget.ReviewData(name,avatarUrl,rating,description,dateString,iconUrl);
            reviewDataList.add(r);
        }
        widget.setReviewsList(reviewDataList);
        widget.setTitle("Google reviews");
        widget.setLogoUrl("/image/luxury/google_logo.svg");
        if(reviewResponse.getNumberOfReview() != null) {
            widget.setNumOfReviews("(" + reviewResponse.getNumberOfReview().toString() + ")");
        }
        if(reviewResponse.getAverageRating() != null) {
            widget.setAverageRating(df.format(reviewResponse.getAverageRating().doubleValue()));
        }
        widget.setIconUrl("image/luxury/rating_star.svg");
        widget.addSpacing("0","60");
        widget.setWidgetId(googleReviewsWidgetId);
        return CompletableFuture.completedFuture(widget);
    }

    private CompletableFuture<BaseWidget> getCentersOfferingWidget(List<FTSSportOffering> centerOfferings) {
        ProductListWidget centersOfferingWidget = new ProductListWidget();

        centersOfferingWidget.setType("SMALL");

        Header header = new Header();
        header.setTitle("What this center offers");
        header.setColor("#000000");
        centersOfferingWidget.setHeader(header);
        centersOfferingWidget.setNoTopPadding(true);
        centersOfferingWidget.setHideSeparatorLines(true);
        centersOfferingWidget.setShowSquareIcons(true);
        centersOfferingWidget.setCollapsable(false);

        for(FTSSportOffering descriptiveText: centerOfferings) {
            ProductListWidgetItem item = new ProductListWidgetItem();
            item.setSubTitle(descriptiveText.getDescription());
            item.setTopSpacing("20");
            item.setIcon(descriptiveText.getImageUrl());
            centersOfferingWidget.appendListItem(item);
        }
        centersOfferingWidget.addSpacing("0", "40");

        return CompletableFuture.completedFuture(centersOfferingWidget);
    }

    private Action getBuyNowAction(
            boolean hasSportLevelPacks,
            FTSCenterInfo center
    ) {
        Action buyAction = new Action(ActionType.SCROLL_TO_WIDGET);
        buyAction.setTitle("BUY NOW");
        HashMap<String, Object> meta = new HashMap<>();
        meta.put("widgetId", CENTER_DETAIL_PACK_COMPARISON_MARKER);
        meta.put("scrollPosition", "TOP");
        meta.put("extraOffset", -100.0);
        if (isSportsSelectedPrevious)
            meta.put("message","Please choose a membership plan to proceed");
        else
            meta.put("message","Select a sport and membership plan to proceed");
        buyAction.setMeta(meta);
        buyAction.setAnalyticsData(getAnalyticsDataForBuyNow(center));
        buyAction.setVariant("primary");
        if (hasSportLevelPacks) {
            return buyAction;
        }

        return null;
    }

    private Boolean isSwimmingAvailable(List<FTSSportsInfo> sportsAtCenter) {
        for (FTSSportsInfo model : sportsAtCenter) {
            if (model.getName().contains("swimming") || model.getId().toString().equals(SWIMMING))
                return true;
        }

        return false;
    }

    private CompletableFuture<BaseWidget> getNearByCenter(FTSCenterInfo currentCenter, UserContext userContext, ServiceInterfaces interfaces, String workoutId) throws Exception {
        GymCentersNearbyWidget gymCentersNearbyWidget = new GymCentersNearbyWidget();
        gymCentersNearbyWidget.setTitleAction(new Action("", ActionType.NAVIGATION));
        gymCentersNearbyWidget.setSkuType(CentersPageView.SkuTypeValue.PLAY);
        WidgetContext context = new WidgetContext(this.widgetContext);
        context.putQueryParam("centerLocation", currentCenter.getAddress().getLatitude() + "," + currentCenter.getAddress().getLongitude());
        context.putQueryParam("centerID", currentCenter.getId().toString());
        context.putQueryParam("centerCityId", currentCenter.getAddress().getCityId());
        if (workoutId != null)
            context.putQueryParam("workoutId", workoutId);

        List<BaseWidget> widgetList = gymCentersNearbyWidget.buildView(interfaces, userContext, context);
        if (widgetList == null)
            return null;
        GymCentersNearbyWidget widget = (GymCentersNearbyWidget) widgetList.getFirst();
        widget.setTitleAction(null);

        if (!AppUtil.isWeb(userContext))
            widget.setSeeAllAction(null);

        widget.setSeeAllAction(PlayUtil.getPlayAllCentersPageAction(userContext, workoutId));


        widget.setTitle("Nearby Centers");
        widget.addSpacing("0", "70");

        return CompletableFuture.completedFuture(widget);
    }


    private Action getCardAction(Long id) {
        return new Action(CENTER_PAGE_DEEPLINK + "?" + "centerId=" + id, "EXPLORE", ActionType.NAVIGATION);
    }

    String getCardTopTitle(FTSCenterInfo centerEntry) {
        return centerEntry.getDistanceFromSearchOrigin() + " KM" + " • " + centerEntry.getLocality();
    }

    protected CompletableFuture<BaseWidget> getCenterSummaryWidget(FTSCenterInfo center, UserContext userContext) throws HttpException {
        CenterSummaryWidget widget = new CenterSummaryWidget();
        widget.setTitle(center.getName());
        if (center.getCenterMedia() != null)
            if (isSportsSelectedPrevious) {
                widget.setMedia(getFilteredMedia(center.getCenterMedia()));
            } else
                widget.setMedia(center.getCenterMedia());
        else {
            FTSMedia defaultMedia = new FTSMedia();
            defaultMedia.setMediaType(FTSMedia.MediaType.IMAGE);
            defaultMedia.setMediaUrl(DEFAULT_CENTER_IMAGE);
            ArrayList<FTSMedia> mediaList = new ArrayList<>();
            mediaList.add(defaultMedia);
            widget.setMedia(mediaList);

        }
        TopCenterReviewResponse reviewResponse = interfaces.reviewService.getTopGoogleReviews(center.getId(), null ,null);
        if(reviewResponse != null && reviewResponse.getAverageRating() != null && reviewResponse.getAverageRating() >= 4.0 && reviewResponse.getNumberOfReview() != null) {
            DecimalFormat df = new DecimalFormat("#.#");
            widget.setIconUrl("image/luxury/rating_star.svg");
            widget.setLogoUrl("/image/luxury/google_logo.svg");
            widget.setNumOfReviews(reviewResponse.getNumberOfReview().toString() + " Reviews");
            widget.setRating(df.format(reviewResponse.getAverageRating().doubleValue()));
            Action action = new Action(ActionType.SCROLL_TO_WIDGET);
            HashMap<String, Object> meta = new HashMap<>();
            meta.put("widgetId", googleReviewsWidgetId);
            meta.put("scrollPosition", "MIDDLE");
            action.setMeta(meta);
            widget.setReviewAction(action);
        }
        List<CardItemSubTitle> sportsAvailable = FitnessCenterUtil.getCardSubTitles(centerEntry, true, interfaces, userContext, null);
        widget.setPremiumTag(getPlayTagData(sportsAvailable));

        //added support of CFTag in v10.93 that show sports in tags
        if (userContext.getSessionInfo().getAppVersion() < TAG_SUPPORT_IN_CENTER_SUMMARY_WIDGET) {
            widget.setSubTitleList(sportsAvailable);
            widget.setTagIconUrl(PlayUtil.PLAY_TAG_URL);
        }

        widget.setNewFlow(true);

        widget.setHasDivideBelow(false);
        widget.setIsV2Supported(true);

        if (center.getLaunchDate() != null) {
            Instant instant = Instant.ofEpochMilli(Long.parseLong(center.getLaunchDate()));
            LocalDateTime launchDate = LocalDateTime.ofInstant(instant, ZoneId.of(userContext.getUserProfile().getTimezone()));
            if (center.getLaunchDate() != null && launchDate.isAfter(TimeUtil.getDateNow(DEFAULT_ZONE_ID))) {
                try {
                    SimpleDateFormat formatter = new SimpleDateFormat("dd MMM");
                    Date launch = new Date();
                    launch.setTime(instant.getEpochSecond() * 1000);
                    String strDate = formatter.format(launch);
                    widget.setLaunchingDate("OPENS ON " + strDate.toUpperCase());
                } catch (Exception e) {
                    widget.setCenterTag("COMING SOON");
                }
            }
        }
        widget.addSpacing("0", "30");
        return CompletableFuture.completedFuture(widget);
    }

    private CFTagData getPlayTagData(List<CardItemSubTitle> sportsAvailable){
        CFTagData tagData = new CFTagData();
        tagData.setTitle(new CFTextData(sportsAvailable.isEmpty() ? "PLAY" : sportsAvailable.getFirst().getText(),
                UiUtils.UIColors.COLOR_WHITE,
                "1",
                UiUtils.TextAlignment.CENTER,
                UiUtils.TextTypeScales.P6
        ));
        tagData.setTagType("CAPSULE");
        tagData.setVerticalPadding(5.0);
        tagData.setHorizontalPadding(10.0);
        tagData.setPrefixImage(new CFImageData("image/icons/fitsoImages/play_premium_icon.svg", "CIRCLE", 12, 12));
        tagData.setBgGradient(new CFGradientData(new ArrayList<>(){{
            add("3C3938");
            add("000000");
        }}, GradientDirection.HORIZONTAL));

        return tagData;
    }

    private Collection<FTSMedia> getFilteredMedia(List<FTSMedia> centerMedia) {
        return centerMedia.stream().filter(media -> media.getName() != null && media.getName().equalsIgnoreCase(PlayUtil.getWorkoutNameUsingID(workoutId))).toList();
    }

    private List<CardItemSubTitle> getCardSubTitles(List<String> sportsList) {
        ArrayList<CardItemSubTitle> subTitle = new ArrayList<>();

        if (sportsList == null)
            return subTitle;

        for (int position = 0; position < sportsList.size(); position++) {
            subTitle.add(new CardItemSubTitle(sportsList.get(position)));
            if (position == 1)
                break;
        }
        if (sportsList.size() > 2)
            subTitle.add(new CardItemSubTitle("+" + (sportsList.size() - 2)));
        return subTitle;
    }

    protected CompletableFuture<BaseWidget> getAddressWidget(FTSAddress center) {
        MapWidgetV2 widget = new MapWidgetV2();

        StringBuilder address = new StringBuilder();
        address.append(center.getAddressLine1());
        if (center.getAddressLine2() != null && !center.getAddressLine2().equalsIgnoreCase("null"))
            address.append(center.getAddressLine2());

        widget.setAddress(address.toString());
        widget.setAction(center.getMapUrl());
        widget.setTitle(center.getLocality());
        widget.addSpacing("0", "40");
        return CompletableFuture.completedFuture(widget);
    }

    protected CompletableFuture<BaseWidget> getAddressWidget(UserContext userContext, CenterEntry center) {

        String cityId = userContext.getUserProfile().getCity() != null ? userContext.getUserProfile().getCity().getCityId() : "";
        String userId = userContext.getUserProfile().getUserId();
        SessionInfo sessionInfo = userContext.getSessionInfo();
        MapWidgetV2 widget = new MapWidgetV2();
        widget.setAction(center.getMapUrl());

        LocationPreference locationPreference = interfaces.localityProviderService.getUserLocationPreference(interfaces, userContext, cityId, userId);
        if (locationPreference != null) {
            LatLong latlong = interfaces.localityProviderService.getUserPreferenceLatLon(sessionInfo, locationPreference);
            if (latlong != null && latlong.getLatitude() != null && latlong.getLongitude() != null) {
                Map<Long, Double> centerDrivingDistanceMapping = new HashMap<>();
                DistanceRequest distanceRequest = new DistanceRequest(latlong.getLongitude(), latlong.getLatitude(), center.getLongitude(), center.getLatitude());
                Double drivingDistance = interfaces.localityProviderService.getDrivingDistanceToCenter(distanceRequest);
                if (drivingDistance != null) centerDrivingDistanceMapping.put(center.getId(), drivingDistance);
                widget.setTitle(FitnessCenterUtil.getCardTopTitle(center, latlong.getLatitude(), latlong.getLongitude(), false, centerDrivingDistanceMapping));
            } else {
                widget.setTitle(center.getLocality());
            }
        } else {
            widget.setTitle(center.getLocality());
        }
        widget.addSpacing("0", "25");
        widget.setHasDivideBelow(true);
        return CompletableFuture.completedFuture(widget);
    }

    protected CompletableFuture<BaseWidget> getTimingWidgetV2(CenterEntry center, List<CenterHoliday> centerHolidays, boolean isUpcomingCenter, UserContext userContext) throws BaseException {
        Boolean isCultpassMember = AppUtil.isUserCultpassMember(userContext, interfaces.getEnvironmentService());
        Collection<CenterScheduleEntry> scheduleSlots = this.interfaces.scheduleService.getCenterScheduleByCenterId(center.getId(), null, null);
        CompletableFuture<Map<String, Map<String, GymDemandPredictionAtHour>>> heatMapRawDataFuture = null;
        if (isCultpassMember) {
            heatMapRawDataFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return interfaces.circuitBreakerConfiguration.get(GYMFIT_SERVICE).executeCallable(() -> interfaces.gymOvercrowdingService.getGymPredictedDemand(center.getId()));
                } catch (Exception e) {
                    return null;
                }
            }, interfaces.getTaskExecutor());
        }
        boolean isV3TimingWidgetSupported = AppUtil.isCenterTimingWidgetV3Supported(userContext);
        List<CenterScheduleEntry> scheduleStatusList = new ArrayList<>(scheduleSlots);
        CenterTimingWidget widget = new CenterTimingWidget(scheduleStatusList, center, isUpcomingCenter, centerHolidays, heatMapRawDataFuture, isV3TimingWidgetSupported);
        widget.addSpacing("0", "50");
        return CompletableFuture.completedFuture(widget);
    }

    private CompletableFuture<BaseWidget> getSportsAvailable(
            Collection<FTSSportsInfo> sportsAtCenter,
            BookingEligibility status,
            Long centerId,
            boolean isUpcomingCenter,
            List<Membership> playMemberships,
            UserContext userContext
    ) {
        List<OnboardingCardDetails> data = new ArrayList<>();
        for (FTSSportsInfo model : sportsAtCenter) {
            data.add(new OnboardingCardDetails(
                    model.getName(),
                    model.getDescription() != null ? model.getDescription() : "",
                    3,
                    model.getSportMedia().get(0).getMediaUrl(),
                    getCTAAction(status, centerId, model.getId().toString(), isUpcomingCenter, playMemberships, userContext, false),
                    null,
                    null,
                    null,
                    null,
                    null,
                    null, null,
                    null,
                    null,
                    null,
                    "",
                    null,
                    null,
                    null,
                    null,
                    null
            ));
        }

        WidgetHeader header = new WidgetHeader();
        header.setTitle("Sports available at center");
        OnboardingWidget widget = new OnboardingWidget();
        widget.setHeader(header);
        widget.setCardData(data);

        widget.addSpacing("0", "70");
        return CompletableFuture.completedFuture(widget);
    }

    private CompletableFuture<BaseWidget> getSportsAvailableSLP(
            Collection<FTSSportsInfo> sportsAtCenter,
            BookingEligibility status,
            Long centerId,
            String sportId,
            Map<String, List<FitnessPack>> allSportLevelPacks,
            boolean isUpcomingCenter,
            List<Membership> playMemberships,
            UserContext userContext
    ) {
        List<CircularCarouselItem> data = new ArrayList<>();
        for (FTSSportsInfo model : sportsAtCenter) {
            if (sportId != null && Integer.parseInt(sportId) == model.getId()) {
                continue;
            }
            CircularCarouselItem item = new CircularCarouselItem();
            item.setTitle(model.getName());
            item.setIsCircular(true);
            item.setImage(model.getSportMedia().get(0).getMediaUrl());

            if (sportId != null) {
                String url = CENTER_PAGE_DEEPLINK + "?centerId=" + centerId + "&workoutId=" + model.getId().toString();
                Action action = new Action(POP_THEN_NAVIGATE);
                Map<String, Object> actionMeta = new HashMap<>();
                Action secondAction = new Action(url, NAVIGATION);
                if (PlayUtil.isActionJsonSupported(userContext)) {
                    actionMeta.put("actionJson", secondAction);
                } else {
                    actionMeta.put("action", secondAction);
                }
                action.setMeta(actionMeta);
                item.setAction(action);
                try {
                    List<FitnessPack> sportsLevelPacks = PlayUtil.getSportSpecificPacks(allSportLevelPacks, model.getId().toString());
                    HashMap<String, String> sportsPackDetails = PlayUtil.getSportsPackDetails(centerEntry, sportsLevelPacks, userContext, interfaces);
                    if (sportsPackDetails != null && sportsPackDetails.containsKey("minPrice")) {
                        String[] sportPackMin = sportsPackDetails.getOrDefault("minPrice", "").split("/");
                        item.setSubTitle("Starting at " + (sportPackMin.length > 0 ? sportPackMin[0] : null));
                    }
                } catch (Exception e) {
                    log.info("min pack error", e);
                }
            } else {
                List<NameValuePair> nameValuePairs = new ArrayList<>();
                nameValuePairs.add(new BasicNameValuePair("workoutId",  model.getId().toString()));
                item.setAction(PlayUtil.getPlayClassBookingAction(nameValuePairs, ""));
            }

            data.add(item);
        }
        if (data.isEmpty())
            return null;

        CircularCarouselWidget widget = new CircularCarouselWidget();
        widget.setHeaderTitle("Other sports at this center");
        if(sportId == null) {
            if (playMemberships != null && !playMemberships.isEmpty())
                widget.setHeaderTitle("Book a session at this center");
            else
                widget.setHeaderTitle("Book a free trial at this center");
        }
        widget.setImageSize(70);
        widget.setMaxHeight(130);
        widget.setItemSpacing(25);
        widget.setItemTitleVariant("paragraph4Text");
        widget.setFontSize(UiUtils.TextTypeScales.P3);
        widget.setArrangeItemsFromStart(true);
        widget.setItems(data);

        widget.addSpacing("0", "20");
        return CompletableFuture.completedFuture(widget);
    }

    private CompletableFuture<BaseWidget> getMembershipWidget(Membership playMembership, UserContext userContext) throws Exception {
        BuyMembershipWidget widget;
        widget = new BuyMembershipWidget(false);
        widget.setFooter("");
        widget.addSpacing("0", "70");
        widget.addPlayMembershipCardData(
                PlayUtil.getRequestUpgradeAction(interfaces, "UPGRADE PASS", playMembership, userContext),
                "",
                null);
        return CompletableFuture.completedFuture(widget);
    }

    private PackOfferDetails getOfferDetails(FitnessPack packProduct, PlayProductPricesResponse packOffersV3) {
        ProductPrice price = packProduct.getPrice();
        List<OfferMini> offers = new ArrayList<OfferMini>();

        Map<String, ProductPriceResponse> priceMap = packOffersV3.getPriceMap();
        if (priceMap != null) {
            ProductPriceResponse productOffer = priceMap.get(packProduct.getProductId());
            if (productOffer != null) {
                // Update Price.
                price = new ProductPrice();
                price.setMrp(BigDecimal.valueOf(productOffer.getProduct().getPrice().getMrp()));
                price.setListingPrice(BigDecimal.valueOf(productOffer.getProduct().getPrice().getSellingPrice()));
                price.setCurrency("INR");

                // Update Offers.
                List<String> productOfferOfferIds = productOffer.getOfferIds();
                for (String productOfferId : productOfferOfferIds) {
                    offers.add(packOffersV3.getOfferMap().get(productOfferId));
                }
            }
        }

        return new PackOfferDetails(price, offers);
    }

    protected CompletableFuture<BaseWidget> getTimingWidget(Collection<FTSCenterSchedule> schedulesList, boolean isUpcomingCenter, String timingInfo) {
        Collection<CenterSchedule> schedules = new ArrayList<>();

        for (FTSCenterSchedule model : schedulesList) {
            CenterSchedule schedule = new CenterSchedule();
            schedule.setCenterId(model.getCenterId());
            schedule.setDayOfWeek(model.getDayOfWeek());
            schedule.setFromTime(model.getFromTime());
            schedule.setToTime(model.getToTime());
            schedule.setCenterServiceScheduleId(model.getCenterServiceScheduleId());

            schedules.add(schedule);
        }

        CenterTimingWidget widget = new CenterTimingWidget(schedules, isUpcomingCenter, timingInfo);
        widget.addSpacing("0", "10");
        return CompletableFuture.completedFuture(widget);
    }

    private CompletableFuture<BaseWidget> getFacilitiesWidget(Collection<FTSFacilities> centerFacilities) {
        FacilitiesWidget widget = new FacilitiesWidget();
        if (centerFacilities.isEmpty()) {
            return CompletableFuture.completedFuture(null);
        } else {
            widget.setFacilities(centerFacilities);
        }
        widget.addSpacing("0", "40");
        return CompletableFuture.completedFuture(widget);
    }

    private CompletableFuture<BaseWidget> getSingleRowFacilitiesWidget(List<FTSFacilities> facilities) {
        CenterOfferingsWidget widget = new CenterOfferingsWidget();
        List<Facility> facilityList = new ArrayList<>();
        for (FTSFacilities model : facilities) {
            Facility facility = new Facility(model, 4);
            facilityList.add(facility);
        }
        if (facilityList.isEmpty())
            return CompletableFuture.completedFuture(null);

        widget.setTitle("Facilities");
        widget.setFacilitiesList(facilityList);
        widget.addSpacing("0", "60");
        return CompletableFuture.completedFuture(widget);
    }

    private CompletableFuture<BaseWidget> getSafetyMeasures(Collection<FTSFacilities> safetyMeasures) {
        CircularImageCarouselWidgetView widget = new CircularImageCarouselWidgetView();
        widget.setTitle("Safety Measures");
        widget.setEnableFadingRing(false);
        widget.setInterSpacing("10");
        widget.addSpacing("0", "70");

        List<CircularCarouselItem> itemList = new ArrayList<>();
        for (FTSFacilities model : safetyMeasures) {
            CircularCarouselItem item = new CircularCarouselItem();
            item.setImage(model.getImageUrl());
            item.setTitle(model.getName());

            itemList.add(item);
        }

        widget.setItems(itemList);

        return CompletableFuture.completedFuture(widget);
    }

    private Action getAction(Boolean isMembershipActive, Boolean isTrialActive, Long centerId) {
        if (isMembershipActive) {
            return new Action(BOOKING_DEEPLINK + "centerId=" + centerId, "BOOK NOW", ActionType.NAVIGATION);
        } else if (isTrialActive) {
            //Give trial option
            return new Action(BOOKING_DEEPLINK + "centerId=" + centerId, "TRY FOR FREE", ActionType.NAVIGATION);
        } else {
            //Give buy membership option
            return new Action(MEMBERSHIP_LANDING_PAGE_DEEPLINK, "BUY MEMBERSHIP", ActionType.NAVIGATION);
        }
    }

    private Action getSportsAction(Boolean isMembershipActive, Boolean isTrialActive, Long centerId, Long sportId) {
        if (isMembershipActive) {
            return new Action(BOOKING_DEEPLINK + "centerId=" + centerId + "&sportId=" + sportId + "&workoutId=" + sportId, "BOOK NOW", ActionType.NAVIGATION);
        } else if (isTrialActive) {
            //Give trial option
            return new Action(BOOKING_DEEPLINK + "centerId=" + centerId + "&sportId=" + sportId + "&workoutId=" + sportId, "TRY FOR FREE", ActionType.NAVIGATION);
        } else {
            //Give buy membership option
            return new Action(MEMBERSHIP_LANDING_PAGE_DEEPLINK + "&sportId" + sportId, "BUY MEMBERSHIP", ActionType.NAVIGATION);
        }
    }

    private Action getCTAAction(
            BookingEligibility eligibility,
            Long centerId,
            String sportId,
            boolean isUpcomingCenter,
            List<Membership> playMemberships,
            UserContext userContext,
            boolean isSecondaryVariant
    ) {
        StringBuilder bookingUrl = new StringBuilder();
        bookingUrl.append(BOOKING_DEEPLINK);
        if (centerId != null)
            bookingUrl.append("centerId=").append(centerId);
        if (sportId != null) {
            bookingUrl.append("&sportId=").append(sportId);
            bookingUrl.append("&workoutId=").append(sportId);
        }
        Action ctaAction = null;

        switch (eligibility) {
            case PAUSE, MEMBERSHIP, OTHER_BENEFITS, UPCOMING_MEMBERSHIP, UPCOMING_PAUSE,
                 EXPIRED, TRIAL_EXHAUSTED, OTHER_BENEFITS_EXHAUSTED, SPORT_CENTER_SELECT_MEMBERSHIP,
                 PLAY_AWAY_BENEFITS_EXHAUSTED, PLAY_AWAY_BENEFITS, LIMITED_AVAILABLE, LIMITED_EXPIRED_SESSION, LIMITED_SESSION_UNAVAILABLE, PLAY_ENTERPRISE_CITY_SPORT:
                ctaAction = new Action(bookingUrl.toString(), "BOOK NOW", ActionType.NAVIGATION);
                break;
            case TRIAL:
                ctaAction = new Action(bookingUrl.toString(), "TRY FOR FREE", ActionType.NAVIGATION);
                break;
            case SELECT_MEMBERSHIP, UPCOMING_PLAY_SELECT_PAUSE, PLAY_SELECT_PAUSE:
                Membership playMembership = playMemberships.get(0);
                Benefit playBenefit = playMembership.getBenefits().stream().filter(benefit -> benefit.getName().equals("PLAY")).collect(Collectors.toList()).get(0);
                Boolean isCurrentCenterAvailable = isPlayLiteCurrentCenterAvailable( playBenefit, playMembership, centerId);

                List<NameValuePair> nameValuePairs = new ArrayList<>();
                nameValuePairs.add(new BasicNameValuePair("pageFrom", "sport_center"));
                if (centerId != null) {
                    nameValuePairs.add(new BasicNameValuePair("centerId", String.valueOf(centerId)));
                }
                if (sportId != null) {
                    nameValuePairs.add(new BasicNameValuePair("sportId", String.valueOf(sportId)));
                    nameValuePairs.add(new BasicNameValuePair("workoutId", String.valueOf(sportId)));
                }

                if (isCurrentCenterAvailable) {
                    ctaAction = PlayUtil.getPlayBookNowCTAAction(false, false, nameValuePairs);
                } else {
                    Action supportAction = PlayUtil.getRequestUpgradeAction(interfaces, "UPGRADE PASS", playMembership, userContext);
                    ctaAction = supportAction;
                }
                break;
        }
        if (isUpcomingCenter) {
            if (isSportLevelPricingSupported)
                return null;
            else
                return new Action(MEMBERSHIP_LANDING_PAGE_DEEPLINK, "BUY NOW", ActionType.NAVIGATION);
        }

        if (ctaAction != null && isSecondaryVariant) {
            ctaAction.setVariant("secondary");
        }

        return ctaAction;
    }

    private CompletableFuture<BaseWidget> getPackComparisonWidgetWithWorkout(
            ServiceInterfaces interfaces,
            List<FTSSportsInfo> sportsAtCenter,
            CenterEntry centerEntry,
            UserContext userContext,
            Map<String, List<FitnessPack>> allSportsLevelPacks
    ) throws Exception {
        PlayPackComparisonWidget widget = new PlayPackComparisonWidget();
        List<PlayPackComparisonWidget.PlayWorkoutFilter> playWorkoutFilters = new ArrayList<>();
        List<PlayPackComparisonWidget.PackFilter> filters = new ArrayList<>();

        if (PlayUtil.isUserBelongToRenewPlayMembership(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext))
            widget.setTitle("Renew CultPass Play");
        else
            widget.setTitle("Get Unlimited Access");

        widget.addSpacing("0", "40");
        PlayPackComparisonWidget.PackFilter sportsFilter = new PlayPackComparisonWidget.PackFilter("workouts", CentersPageView.FilterType.INLINE, CentersPageView.SelectionType.SINGLE_SELECT);
        for (FTSSportsInfo sportsInfo : sportsAtCenter) {
            PlayPackComparisonWidget.PlayWorkoutFilter workoutFilter = new PlayPackComparisonWidget.PlayWorkoutFilter();
            workoutFilter.setId(sportsInfo.getId().toString());
            workoutFilter.setTitle(sportsInfo.getName());

            List<FitnessPack> sportsPacks = PlayUtil.getSportSpecificPacks(allSportsLevelPacks, sportsInfo.getId().toString());
            HashMap<String, String> sportsPackDetails = PlayUtil.getSportsPackDetails(centerEntry, sportsPacks, userContext, this.interfaces);
            BaseWidget packWidget = getPackComparisonWidget(centerEntry, centerEntry.getName(), sportsInfo.getId().toString(), userContext, sportsInfo, sportsPackDetails);
            if (packWidget == null)
                continue;
            workoutFilter.setWidget(packWidget);
            playWorkoutFilters.add(workoutFilter);
        }
        if (playWorkoutFilters.isEmpty()) return null;
        sportsFilter.setWorkoutFilterValues(playWorkoutFilters);
        filters.add(sportsFilter);
        widget.setWorkoutFilters(filters);
        return CompletableFuture.completedFuture(widget);
    }

    protected BaseWidget getPackComparisonWidget(
            CenterEntry center,
            String centerName,
            String workoutId,
            UserContext userContext,
            FTSSportsInfo selectedSport,
            HashMap<String, String> sportsPackDetails
    ) {
        try {

            PackComparisonWidget widget = new PackComparisonWidget();
            String workoutName = "";
            if (selectedSport != null) {
                workoutName = selectedSport.getName();
            }

            String cityName = userContext.getUserProfile() != null && userContext.getUserProfile().getCity() != null ? userContext.getUserProfile().getCity().getName() : "";

            FitnessPackBrowseWidget fitnessPackBrowseWidget = new FitnessPackBrowseWidget();

            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("centerId", String.valueOf(center.getId()));
            HashMap<String, String> playPackDetails = fitnessPackBrowseWidget.getPlayPackItemDetails(userContext, interfaces, queryParams);

            if (playPackDetails == null || sportsPackDetails == null || sportsPackDetails.isEmpty())
                return null;

            Action pack1Action = getPackAction(center.getId().toString(), workoutId, "PLAY_SPORT");
            pack1Action.setAnalyticsData(getAnalyticsDataForPackComparison(center, "PLAY_SPORT", sportsPackDetails.getOrDefault("minPrice", "")));

            Action pack2Action = getPackAction(center.getId().toString(), workoutId, "PLAY");
            pack2Action.setAnalyticsData(getAnalyticsDataForPackComparison(center, "PLAY", playPackDetails.getOrDefault("minPrice", "")));

            List<PackComparisonWidget.PackCardItem> packCardItemList = new ArrayList<>();
            List<PackComparisonWidget.PackFeatureItem> packFeatureItemList = new ArrayList<>();

            String centerPackCardName = workoutName + " at " + centerName;
            PackComparisonWidget.PackCardItem centerPackCard = new PackComparisonWidget.PackCardItem(PlayUtil.getSportTagUrlForComparisonPage(workoutId),
                    centerPackCardName, sportsPackDetails.get("minPrice"), pack1Action);
            packCardItemList.add(centerPackCard);

            PackComparisonWidget.PackCardItem playPackCard = new PackComparisonWidget.PackCardItem(PLAY_SKU_TAB_TAG_URL_SVG, "Play all sports at all centers", playPackDetails.get("minPrice"), pack2Action);
            packCardItemList.add(playPackCard);

            for (int i = 0; i < 4; i++) {
                PackComparisonWidget.PackFeatureItem featureItem = new PackComparisonWidget.PackFeatureItem();
                HashMap<String, String> featureMap = new HashMap<>();
                String title = null;
                if (i == 0) {
                    featureMap = getFeatureMap(
                            "/image/cl_pricing/greentick.svg",
                            "/image/cl_pricing/greentick.svg",
                            null,
                            null,
                            null,
                            null,
                            null,
                            null
                    );
                    title = "Unlimited " + workoutName + " at this center";
                } else if (i == 1) {
                    String awayTickets = sportsPackDetails.get("awayCitySessions");
                    String header1 = awayTickets != null ? awayTickets : "2";
                    featureMap = getFeatureMap(
                            null,
                            null,
                            header1,
                            " sessions",
                            "\nper month",
                            "Unlimited",
                            null,
                            "\nsessions"
                    );
                    title = "Other sports at all centres in " + cityName;
                } else if (i == 2) {
                    String header2 = playPackDetails.getOrDefault("awayCitySessions", "2");
                    featureMap = getFeatureMap(
                            "/image/cl_pricing/cross_app.svg",
                            null,
                            null,
                            null,
                            null,
                            header2,
                            " sessions",
                            "\nper month"
                    );
                    title = "Gym / Group class access";
                } else if (i == 3) {
                    featureMap = getFeatureMap(
                            null,
                            null,
                            sportsPackDetails.getOrDefault("pauseDays", "30"),
                            null,
                            "\nDays",
                            playPackDetails.getOrDefault("pauseDays", "45"),
                            null,
                            "\nDays"
                    );
                    title = "Pause days (for 12 months)";
                } else {
                    featureMap = getFeatureMap(
                            null,
                            null,
                            "Unlimited",
                            null,
                            "\nsessions",
                            "Unlimited",
                            null,
                            "\nsessions"
                    );
                    title = "At-home LIVE workouts";
                }
                featureItem.setTitle(title);
                featureItem.setFeatures(featureMap);
                packFeatureItemList.add(featureItem);
            }
            if (!isSportsSelectedPrevious) {
                PackComparisonWidget.PackFeatureItem featureItem = new PackComparisonWidget.PackFeatureItem();
                featureItem.setTitle("Starting at");
                String[] sportPackMin = sportsPackDetails.getOrDefault("minPrice", "").split("/");
                String[] playPackMin = playPackDetails.getOrDefault("minPrice", "").split("/");
                featureItem.setFeatures(getFeatureMap(null, null,
                        sportPackMin.length > 0 ? sportPackMin[0] : null, null, "\nper month",
                        playPackMin.length > 0 ? playPackMin[0] : null, null, "\nper month"));
                packFeatureItemList.add(0, featureItem);
            }
            widget.setPackFeatures(packFeatureItemList);
            widget.setPackWidgetHeight(100.0 * packFeatureItemList.size());
            if (isSportsSelectedPrevious) {
                if (PlayUtil.isUserBelongToRenewPlayMembership(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext))
                    widget.setTitle("Renew CultPass Play");
                else
                    widget.setTitle("Get Unlimited Access");
                widget.setPackCardList(packCardItemList);
                if (centerPackCardName.length() > 40) {
                    widget.setPackCardListSize(205.0);
                }
            }
            widget.setPack1Action(pack1Action);
            widget.setPack2Action(pack2Action);
            widget.setPack1tagUrl(PlayUtil.getWorkoutTagUrlForComparison(workoutId));
            widget.setPack2tagUrl(PLAY_SKU_TAB_ICON_URL);
            widget.addSpacing("0", "40");
            return widget;
        }catch (Exception e){
            return null;
        }
    }

    private Action getPackAction(String centerId, String workoutId, String sku) {
        return new Action(
                SPORTS_PACK_COMPARISON_PAGE_DEEPLINK
                        + "centerId=" + centerId
                        + "&workoutId=" + workoutId
                        + "&selectedSku=" + sku,
                "BUY",
                ActionType.NAVIGATION);
    }

    private Map<String, String> getAnalyticsDataForPackComparison(CenterEntry center, String sku, String minPrice) {
        return Map.of(
                "pageId", "play_center",
                "widgetId", "PACK_COMPARISON_WIDGET",
                "centerId", center != null ? center.getId().toString() : "",
                "centerType", "PLAY",
                "sku", sku,
                "pricePerMonth", minPrice
        );
    }

    private CompletableFuture<BaseWidget> getPackComparisonMarkerWidget(){
        MarkerWidget markerWidget = new MarkerWidget();
        markerWidget.setWidgetId(CENTER_DETAIL_PACK_COMPARISON_MARKER);

        return CompletableFuture.completedFuture(markerWidget);
    }

    private Map<String, String> getAnalyticsDataForBuyNow(FTSCenterInfo center){
        return Map.of(
                "pageId", "fitso_sport_center",
                "centerServiceId", center != null ? center.getId().toString(): "",
                "centerType", "PLAY",
                "centerName", center != null ? center.getName() : ""
        );
    }

    private HashMap<String, String> getFeatureMap(
            String icon1Url,
            String icon2Url,
            String header1,
            String pack1SubHeader1,
            String pack1SubHeader2,
            String header2,
            String pack2SubHeader1,
            String pack2SubHeader2
    ) {
        HashMap<String, String> featureMap = new HashMap<>();
        featureMap.put("icon1Url", icon1Url);
        featureMap.put("icon2Url", icon2Url);
        featureMap.put("header1", header1);
        featureMap.put("header2", header2);
        featureMap.put("pack1SubHeader1", pack1SubHeader1);
        featureMap.put("pack1SubHeader2", pack1SubHeader2);
        featureMap.put("pack2SubHeader1", pack2SubHeader1);
        featureMap.put("pack2SubHeader2", pack2SubHeader2);
        return featureMap;
    }
}
