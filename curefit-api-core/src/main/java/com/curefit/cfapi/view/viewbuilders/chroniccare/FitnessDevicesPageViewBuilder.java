package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.view.viewmodels.chroniccare.FitnessDevicesPageView;
import com.curefit.cfapi.widgets.chroniccare.FitnessAppsAndDevicesWidget;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.pojo.UserAttributeEntry;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.sugarfit.fitness.client.SFFitnessClient;
import com.sugarfit.fitness.pojo.CultClassAccessMapEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.curefit.cfapi.util.AppUtil.getAppTenantFromUserContext;
import static com.curefit.cfapi.util.AppUtil.isUltraFitApp;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class FitnessDevicesPageViewBuilder {
    public static final String CULT_MEMBERS_SEGMENT = "cult_members";
    public static final String CULTLIVE_MEMBERS_SEGMENT = "cultlive_members";
    public static final String GYMFIT_MEMBERS_SEGMENT = "Gym.fit Members";
    final SegmentationCacheClient segmentationCacheClient;
    final SFFitnessClient sfFitnessClient;
    final UserAttributesClient userAttributesClient;
    final ExceptionReportingService exceptionReportingService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final ServiceInterfaces serviceInterfaces;

    public FitnessDevicesPageView buildView(UserContext userContext) {
        FitnessDevicesPageView result = new FitnessDevicesPageView();
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());

            FitnessAppsAndDevicesWidget fitnessAppsAndDevicesWidget = new FitnessAppsAndDevicesWidget();

            boolean showConnectWithCult = isUltraFitApp(userContext) && getShowSyncWithCultFitness(userId);
            fitnessAppsAndDevicesWidget.setCultFitnessSyncEnabled(isCultFitnessSyncEnabled(userId));
            fitnessAppsAndDevicesWidget.setShowConnectWithCult(showConnectWithCult);
            fitnessAppsAndDevicesWidget.setCultFitnessSyncPermissionSet(getCultFitnessSyncPermissionSet(userId));
            fitnessAppsAndDevicesWidget.setResearchStudyAccessEnabled(isResearchAccessGranted(userContext));
            fitnessAppsAndDevicesWidget.setFitnessDeviceConfig(chronicCareServiceHelper.getFitnessDeviceSyncMeta(userContext));

            result.addWidget(fitnessAppsAndDevicesWidget);

            return result;
        } catch (Exception e) {
            String msg = String.format("Failed to get fitness devices page for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
        }
        return null;
    }

    private boolean isCultFitnessSyncEnabled(Long userId) {
        try {
            CultClassAccessMapEntry response = sfFitnessClient.findCultClassAccessPermission(userId);
            return response != null && response.getIsPermitted();
        } catch (Throwable e) {
            exceptionReportingService.reportException("Failed fetching flag to get cult fitness enabled", e);
            return false;
        }
    }

    private boolean getShowSyncWithCultFitness(Long userId) {
        try {
            Map<Boolean, Long> permission = sfFitnessClient.checkIfCultClassAccessPermissionSet(userId);

            if (permission.containsKey(false) && permission.get(false) != null && permission.get(false) != 0) {
                String cultUserId = String.valueOf(permission.get(false));
                return isSyncWithCultFitnessEnabled(cultUserId);
            } else return permission.containsKey(true);
        } catch (Throwable e) {
            exceptionReportingService.reportException("Failed fetching flag to show sync with cult fitness", e);
            return false;
        }
    }

    private boolean getCultFitnessSyncPermissionSet(Long userId) {
        try {
            Map<Boolean, Long> permission = sfFitnessClient.checkIfCultClassAccessPermissionSet(userId);
            return permission.containsKey(true);
        } catch (Throwable e) {
            exceptionReportingService.reportException("Failed fetching flag to show sync with cult fitness", e);
            return false;
        }
    }

    private boolean isSyncWithCultFitnessEnabled(String userId) {
        try {
            SegmentSet<String> userSegments = segmentationCacheClient.getUserSegments(userId);
            return  userSegments.contains(CULT_MEMBERS_SEGMENT) ||
                    userSegments.contains(CULTLIVE_MEMBERS_SEGMENT) ||
                    userSegments.contains(GYMFIT_MEMBERS_SEGMENT);
        } catch (Exception e) {
            String message = String.format("Cult fitness sync enabled segment error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    private boolean isResearchAccessGranted(UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            List<UserAttributeEntry> userAttributesResponse = userAttributesClient.findAttributes(userId, "", "research-studies-access-granted", appTenant, null);
            if (CollectionUtils.isEmpty(userAttributesResponse) || userAttributesResponse.get(0) == null || userAttributesResponse.get(0).getAttrValue() == null) {
                return false;
            } else {
                return Boolean.parseBoolean(userAttributesResponse.get(0).getAttrValue().toString());
            }
        } catch (Exception e) {
            log.error(String.format("Exception in fetching user attributes for userId %s", userContext.getUserProfile().getUserId()));
            exceptionReportingService.reportException("Exception in fetching user attributes", e);
        }
        return true;
    }
}

