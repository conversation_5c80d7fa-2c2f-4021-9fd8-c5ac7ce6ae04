package com.curefit.cfapi.view.viewbuilders.fitso;

import com.curefit.cfapi.model.internal.cult.LocationPreference;
import com.curefit.cfapi.model.internal.cult.LocationPreferenceType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.view.TextStyle;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.pojo.vm.widget.WidgetMetric;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.PlayUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.view.viewmodels.fitness.ShowInstructionModalData;
import com.curefit.cfapi.view.viewmodels.fitso.MembershipBenefitInfo;
import com.curefit.cfapi.view.viewmodels.fitso.bookingdetail.FitsoPostRequestResponseView;
import com.curefit.cfapi.view.viewmodels.fitso.prebooking.FitsoPreBookingPageView;
import com.curefit.cfapi.view.viewmodels.fitso.prebooking.FitsoPreBookingPageWebView;
import com.curefit.cfapi.view.viewmodels.fitso.prebooking.FitsoUnpauseAndBookActionMeta;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.common.*;
import com.curefit.cfapi.pojo.vm.widget.Text.TextItem;
import com.curefit.cfapi.pojo.vm.widget.Text.TextItemParent;
import com.curefit.cfapi.widgets.common.banner.BannerCarouselWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.fitness.FacilitiesWidget;
import com.curefit.cfapi.widgets.fitness.Facility;
import com.curefit.cfapi.widgets.fitness.MapWidgetV2;
import com.curefit.cfapi.widgets.fitness.TextPointerListWidget;
import com.curefit.cfapi.widgets.fitso.prebooking.*;
import com.curefit.gymfit.dtos.Media;
import com.curefit.location.models.LatLong;
import com.curefit.location.util.LocationUtil;
import com.curefit.membership.pojo.entry.Benefit;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.product.enums.ProductType;
import com.curefit.sportsapi.pojo.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.curefit.cfapi.pojo.app.action.ActionType.SHOW_CULT_INSTRUCTION_MODAL;
import static com.curefit.cfapi.util.PlayUtil.SWIMMING_WORKOUT_ID;
import static com.curefit.cfapi.util.TimeUtil.ordinal;
import static com.curefit.cfapi.pojo.vm.atom.UiUtils.UIColors.*;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class FitsoPrebookingPageBuilder {
    public FitsoPreBookingPageView buildView(
            UserContext userContext,
            FTSPreBookingInfo preBookingInfo,
            LocationPreference locationPreference,
            Map<String, String> queryParams,
            boolean isFlutterSupportSectionEnabled,
            ServiceInterfaces interfaces,
            boolean isFirstTimeUser,
            Double centerDrivingDistance
    ) throws Exception {
        boolean isFormatSummaryV2Supported = PlayUtil.isFormatSummaryWidgetV2Supported(userContext);
        FitsoPreBookingPageView page = new FitsoPreBookingPageView();
        page.setWidgets(new ArrayList<>());

        if (isFormatSummaryV2Supported) {
            page.setWorkoutFormat(PlayUtil.getWorkoutNameWithSpeedoSupport(preBookingInfo.getWorkoutDetails()));
        }

        page.addWidget(getFormatSummaryWidget(userContext, preBookingInfo, queryParams, isFormatSummaryV2Supported));

        // Show waitlist widget for waitlist classes.
        if (isWaitlistClass(preBookingInfo)) {
            page.addWidget(getWaitlistWidget(preBookingInfo, userContext, queryParams, interfaces));
        }

//        if (isFormatSummaryV2Supported) {
//            page.addWidget(getCenterMapWidget(preBookingInfo.getCenterDetails(), locationPreference, centerDrivingDistance));
//            page.addWidget(getCenterTimingWidget(preBookingInfo.getSlotDetails(), queryParams, userContext));
//        }

        if (preBookingInfo.getWorkoutDetails().getId() == SWIMMING_WORKOUT_ID && preBookingInfo.getIsConsecutiveBooking() != null && preBookingInfo.getIsConsecutiveBooking()) {
            page.addWidget(getSwimRegulationWidget());
        }

        if(preBookingInfo.getWorkoutDetails().getPreWorkoutGears() != null && !preBookingInfo.getWorkoutDetails().getPreWorkoutGears().isEmpty()) {
            page.addWidget(getPreWorkoutGearWidget(preBookingInfo.getWorkoutDetails(), userContext, isFirstTimeUser));
        }

        if(preBookingInfo.getCenterDetails().getFacilities() != null && !preBookingInfo.getCenterDetails().getFacilities().isEmpty()) {
            page.addWidget(getCenterFacilitiesWidget(preBookingInfo.getCenterDetails(), userContext, isFirstTimeUser));
        }

        if (isFormatSummaryV2Supported) {
            page.addWidget(getCenterMapWidget(preBookingInfo.getCenterDetails(), locationPreference, centerDrivingDistance));
        }

        if (preBookingInfo.getWorkoutDetails().getId() == SWIMMING_WORKOUT_ID)
            page.addWidget(PlayUtil.getSpeedoBannerWidget());

        if (preBookingInfo.getProductArenaCategoryId() !=  null) {
            page.setWorkoutFormat(PlayUtil.getWorkoutNameWithSpeedoAndLevelSupport(preBookingInfo.getWorkoutDetails(), Integer.parseInt(preBookingInfo.getProductArenaCategoryId().toString())));
            if (preBookingInfo.getProductArenaCategoryId() == 12) {
                page.addWidget(getLevelBannerWidget("image/icons/fitsoImages/learn_swim_pre_booking.png"));
            } else if (preBookingInfo.getProductArenaCategoryId() == 13) {
                page.addWidget(getLevelBannerWidget("image/icons/fitsoImages/lane_swim_pre_booking.png"));
            }
        }

        if (preBookingInfo.getWorkoutDetails().getOtherNotes() != null && !preBookingInfo.getWorkoutDetails().getOtherNotes().isEmpty())
            page.addWidget(getWorkoutSessionNote(preBookingInfo.getWorkoutDetails().getOtherNotes()));

        ActionWidget pageAction = getPageActions(preBookingInfo, queryParams, userContext, isFlutterSupportSectionEnabled, interfaces);
        if (showInstructionModal(preBookingInfo, isFirstTimeUser, pageAction)){
            //Bottom sheet Action
            Action action = new Action();
            action.setActionType(SHOW_CULT_INSTRUCTION_MODAL);
            action.setTitle(pageAction.getActions().get(0).getTitle());
            HashMap<String, Object> meta = new HashMap<>();
            ShowInstructionModalData modalData = new ShowInstructionModalData();
            modalData.setWidget(openInstructionBottomSheetForFirstTimeUser(preBookingInfo.getInstructions()));
            modalData.setAction(pageAction.getActions().get(0));
            meta.put("payload", modalData);
            action.setMeta(meta);

            pageAction.getActions().set(0, action);
            page.setActionsWidget(pageAction);
        } else
            page.setActionsWidget(getPageActions(preBookingInfo, queryParams, userContext, isFlutterSupportSectionEnabled, interfaces));

        return  page;
    }

    private BaseWidget getLevelBannerWidget(String imageUrl) {
        BannerCarouselWidget bannerCarouselWidget = new BannerCarouselWidget();
        bannerCarouselWidget.setMaxNumBanners(1);
        bannerCarouselWidget.setMinNumBanners(0);
        bannerCarouselWidget.setTemplateId("sportsPageBanners");
        bannerCarouselWidget.setWidgetType(WidgetType.BANNER_CAROUSEL_WIDGET);
        BannerItem bannerItem = new BannerItem();
        bannerItem.setImage(imageUrl);
        bannerCarouselWidget.setData(List.of(bannerItem));
        Map<String, Object> layoutProps = new HashMap<>();
        Spacing spacing = new Spacing();
        spacing.setTop("30");
        spacing.setBottom("0");
        bannerCarouselWidget.setSpacing(spacing);
        layoutProps.put("roundedCorners", false);
        layoutProps.put("bannerHeight", 1662);
        layoutProps.put("bannerWidth", 335);
        layoutProps.put("bannerOriginalHeight", 3324);
        layoutProps.put("bannerOriginalWidth", 750);
        layoutProps.put("showPagination", false);
        layoutProps.put("edgeToEdge", true);
        layoutProps.put("spacing", spacing);
        bannerCarouselWidget.setLayoutProps(layoutProps);
        return bannerCarouselWidget;
    }

    private BaseWidget getSwimRegulationWidget() {
        BannerCarouselWidget bannerWidget = new BannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();
        BannerItem bannerItem = new BannerItem();
        bannerItem.setImage("image/icons/fitsoImages/cultswim_session_regulation.png");
        bannerWidget.setHasDivideBelow(true);
        bannerItemList.add(bannerItem);
        bannerWidget.setData(bannerItemList);
        bannerWidget.setLayoutProps(getBannerProps());
        return bannerWidget;
    }

    private Map<String, Object> getBannerProps() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", false);
        layoutProps.put("bannerHeight", 167);
        layoutProps.put("bannerWidth", 335);
        layoutProps.put("bannerOriginalHeight", 167);
        layoutProps.put("bannerOriginalWidth", 335);
        layoutProps.put("verticalPadding", 40);
        layoutProps.put("edgeToEdge", false);
        return layoutProps;
    }

    private BaseWidgetNonVM openInstructionBottomSheetForFirstTimeUser(List<String> descriptions){
        DescriptionWidget descriptionWidget = new DescriptionWidget();
        descriptionWidget.setTitle("Instructions to be followed");
        descriptionWidget.setHasDividerBelow(false);
        descriptionWidget.setItems(descriptions);
        return descriptionWidget;
    }

    private BaseWidget getFormatSummaryWidget(UserContext userContext, FTSPreBookingInfo preBookingInfo, Map<String, String> queryParams, boolean isFormatSummaryV2Supported) {
        FormatSummaryWidget formatSummaryWidget = new FormatSummaryWidget();

        formatSummaryWidget.setTitle(PlayUtil.getWorkoutNameWithSpeedoSupport(preBookingInfo.getWorkoutDetails()));
        String imageUrl = getImageUrlSportBanner(preBookingInfo);

        formatSummaryWidget.setMediaDetails(getMediaDetailsForWorkout(imageUrl));
        formatSummaryWidget.setSubTitleList(new ArrayList<>());

        if (isFormatSummaryV2Supported) {
            formatSummaryWidget.setIsV2Widget(true);
            formatSummaryWidget.setCenterName(preBookingInfo.getCenterDetails().getName());
            formatSummaryWidget.setNavigateAction(new Action(preBookingInfo.getCenterDetails().getMapUrl(), "NAVIGATE", ActionType.EXTERNAL_DEEP_LINK));

            String tz = userContext.getUserProfile().getTimezone();
            Long bookingStartTime = Long.valueOf(queryParams.get("bookingTimestamp"));
            String boookingDayString = TimeUtil.getTimeInFormatFromMillis(bookingStartTime, "EEE d MMM, ", tz);
            FTSSlotInfo slotInfo = preBookingInfo.getSlotDetails();
            log.info("Play Slot Details: Start Hr:%d, Start Min: %d, End Hr: %d, End Min: %d, Duration: %d, Slot Id: %d".formatted(
                    slotInfo.getStartHour(),
                    slotInfo.getStartMin(),
                    slotInfo.getEndHour(),
                    slotInfo.getEndMin(),
                    slotInfo.getDuration(),
                    slotInfo.getSlotId()
            ));
            String timingDetails = boookingDayString + getTimeFromHourAndMin(slotInfo.getStartHour(), slotInfo.getStartMin(), 0L, false)
                    +  "-" + getTimeFromHourAndMin(slotInfo.getStartHour(), slotInfo.getStartMin(), slotInfo.getDuration(), true);
            formatSummaryWidget.setTimingDetails(timingDetails);

            Map<String, Object> layoutProps = new HashMap<>();
            layoutProps.put("spacing", Spacing.builder().top("0").bottom("0").build());
            formatSummaryWidget.setLayoutProps(layoutProps);
            //formatSummaryWidget.
        } else {
            formatSummaryWidget.setIsV2Widget(false);
        }

        return formatSummaryWidget;
    }

    private static String getImageUrlSportBanner(FTSPreBookingInfo preBookingInfo) {
        String imageUrl;
        if (preBookingInfo.getProductArenaCategoryId() == null)
            return preBookingInfo.getWorkoutDetails().getImage();

        if (preBookingInfo.getProductArenaCategoryId() == 12)
            imageUrl = preBookingInfo.getWorkoutDetails().getId() == SWIMMING_WORKOUT_ID ? "image/icons/fitsoImages/learn_to_swim_banner.png" : preBookingInfo.getWorkoutDetails().getImage();
        else if (preBookingInfo.getProductArenaCategoryId() == 13)
            imageUrl = preBookingInfo.getWorkoutDetails().getId() == SWIMMING_WORKOUT_ID ? "image/icons/fitsoImages/lane_swim_banner.png" : preBookingInfo.getWorkoutDetails().getImage();
        else
            imageUrl = preBookingInfo.getWorkoutDetails().getImage();
        return imageUrl;
    }

    private List<WorkoutMediaData> getMediaDetailsForWorkout(String url) {
        List<WorkoutMediaData> mediaList = new ArrayList<WorkoutMediaData>();
        mediaList.add(new WorkoutMediaData(
            new Media(Media.MediaType.IMAGE, url, null, null, null, null, null), null
        ));

        return mediaList;
    }

    private BaseWidget getCenterMapWidget(FTSCenterInfo centerInfo, LocationPreference locationPreference, Double centerDrivingDistance) {
        String title = "";
        String address = centerInfo.getFullAddress1();

        if (centerDrivingDistance != null) {
            title += String.format("%.2f KM • ", centerDrivingDistance);
        } else {
            if (locationPreference != null) {
                LocationPreferenceType preferenceType = locationPreference.getPreferredLocationType();
                if (preferenceType == LocationPreferenceType.current_location || preferenceType == LocationPreferenceType.coordinates) {
                    LatLong coordinates = locationPreference.getCoordinates();
                    if (coordinates != null) {
                        Float distance = LocationUtil.getDistanceFromLatLonInKm(coordinates.getLatitude(), coordinates.getLongitude(), centerInfo.getLatitude().floatValue(), centerInfo.getLongitude().floatValue());
                        title += String.format("%.2f KM • ", distance);
                    }
                }
            }
        }

        title += centerInfo.getName();
        MapWidgetV2 mapWidget = new MapWidgetV2();
        mapWidget.setTitle(title);
        mapWidget.setAddress(address);
        mapWidget.setAction(centerInfo.getMapUrl());

        WidgetMetric widgetMetric = new WidgetMetric();
        widgetMetric.setIsWidgetImpressionRequired(false);
        widgetMetric.setIsSampledWidgetImpressionRequired(false);
        mapWidget.setWidgetMetric(widgetMetric);

        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", Spacing.builder().top("25").bottom("0").build());
        mapWidget.setLayoutProps(layoutProps);
        mapWidget.setHasDivideBelow(true);

        mapWidget.setPageId("fitso_prebookclass");

        return mapWidget;
    }

    private BaseWidget getPreWorkoutGearWidget(FTSSportsInfo sportsInfo, UserContext userContext, boolean isFirstTimeUser) {
        ProductGridWidgetV2 productGridWidgetV2 = new ProductGridWidgetV2();

        if (sportsInfo.getNotes() != null && !sportsInfo.getNotes().isEmpty()) {
            if (isFirstTimeUser)
                productGridWidgetV2.setFooter("Entry for trials is allowed to those 18 years and above. " + sportsInfo.getNotes().get(0));
            else
                productGridWidgetV2.setFooter(sportsInfo.getNotes().get(0));

        }

        List<InfoCard> infoCards = new ArrayList<>();
        List<String> infoCardsTitle = new ArrayList<>();
        for (FTSPreWorkoutGear preWorkoutGear : sportsInfo.getPreWorkoutGears()) {
            infoCards.add(new InfoCard(preWorkoutGear.getProductName(), preWorkoutGear.getProductIcon(), 60));
            infoCardsTitle.add(preWorkoutGear.getProductName());
        }

        Header header = new Header();

        header.setTitle("What to bring");
        header.setSubTitle(PlayUtil.getDeprecatedSubTitle(infoCardsTitle, 1));

        productGridWidgetV2.setHeader(header);


        productGridWidgetV2.setItems(infoCards);
        if (sportsInfo.getId().toString().equals(PlayUtil.SWIMMING)) {
            productGridWidgetV2.setCollapsibleProperties(new CollapsibleProperties(false, false));
            productGridWidgetV2.setFooterAction(PlayUtil.getFooterActionForWhatToBring());
            productGridWidgetV2.setFooterActionColor(COLOR_WHITE);
        } else
            productGridWidgetV2.setCollapsibleProperties(new CollapsibleProperties(true, !isFirstTimeUser));

        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", Spacing.builder().top("15").bottom("0").build());
        productGridWidgetV2.setLayoutProps(layoutProps);
        productGridWidgetV2.setPaddingDivider(true);

        WidgetMetric widgetMetric = new WidgetMetric();
        widgetMetric.setIsWidgetImpressionRequired(false);
        widgetMetric.setIsSampledWidgetImpressionRequired(false);
        productGridWidgetV2.setWidgetMetric(widgetMetric);

        productGridWidgetV2.setPageId("fitso_prebookclass");


        return productGridWidgetV2;
    }

    private BaseWidget getCenterFacilitiesWidget(FTSCenterInfo centerInfo, UserContext userContext, boolean isFirstTimeUser) {
        List<Facility> facilitiesList = new ArrayList<>();
        List<String> facilitiesListTitle = new ArrayList<>();
        for (FTSFacilities facility : centerInfo.getFacilities()) {
            facilitiesList.add(new Facility(facility));
            facilitiesListTitle.add(facility.getName());
        }

        String title = "Center Facilities";
        String subTitle = PlayUtil.getDeprecatedSubTitle(facilitiesListTitle, 2);

        FacilitiesWidget facilitiesWidget = new FacilitiesWidget(
                title,
                subTitle,
                facilitiesList,
                new CollapsibleProperties(true, !isFirstTimeUser),
                true,
                false,
                "fitso_prebookclass"
        );

        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", Spacing.builder().top("20").bottom("0").build());
        facilitiesWidget.setLayoutProps(layoutProps);

        WidgetMetric widgetMetric = new WidgetMetric();
        widgetMetric.setIsWidgetImpressionRequired(false);
        widgetMetric.setIsSampledWidgetImpressionRequired(false);
        facilitiesWidget.setWidgetMetric(widgetMetric);

        return facilitiesWidget;
    }

    private BaseWidget getCenterTimingWidget(FTSSlotInfo slotInfo, Map<String, String> queryParams, UserContext userContext) {
        String tz = userContext.getUserProfile().getTimezone();
        Long bookingStartTime = Long.valueOf(queryParams.get("bookingTimestamp"));
        String boookingDayString = TimeUtil.getTimeInFormatFromMillis(bookingStartTime, "EEE d MMM, ", tz);

        CenterTimingWidget centerTimingWidget = new CenterTimingWidget();
        String title = boookingDayString + getTimeFromHourAndMin(slotInfo.getStartHour(), slotInfo.getStartMin(), 0L, false)
                + "-" + getTimeFromHourAndMin(slotInfo.getStartHour(), slotInfo.getStartMin(), slotInfo.getDuration(), true);
        centerTimingWidget.setTitle(title);

        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", Spacing.builder().top("15").bottom("0").build());
        centerTimingWidget.setLayoutProps(layoutProps);

        return centerTimingWidget;
    }

    private BaseWidget getWorkoutSessionNote(List<FTSCultBenefits> notes){
        TextPointerListWidget textListWidget = new TextPointerListWidget();
        Header header = new Header();
        header.setTitle("Notes");
        textListWidget.setHeader(header);
        textListWidget.setHasDividerTop(true);
        textListWidget.addSpacing("25", "70");

        ArrayList<TextItemParent> cultBenefitList = new ArrayList<>();

        for (FTSCultBenefits benefit : notes) {

            ArrayList<TextItem> itemList = new ArrayList<>();
            TextItem item = new TextItem();
            item.setText(benefit.getDescription());
            itemList.add(item);

            TextItemParent itemParent = new TextItemParent(itemList, null, benefit.getIcon(), null, null, null, null);
            cultBenefitList.add(itemParent);
        }

        textListWidget.setList(cultBenefitList);

        return textListWidget;
    }

    private String getTimeFromHourAndMin(Long hour, Long min, Long duration, boolean showAMPM) {
        min += duration;
        if(min >= 60) {
           hour += min / 60;
           min -= 60 * (min / 60);
        }

        String hourString = hour > 12 ? String.valueOf(hour - 12) : String.valueOf(hour);

        String minuteString = min < 10 ? "0" + String.valueOf(min) : String.valueOf(min);

        String ampmValue = hour >= 12 && hour < 24 ? "PM" : "AM";
        String ampmString = showAMPM ? " " + ampmValue : "";;

        return hourString + ":" + minuteString + ampmString;
    }

    private ActionWidget getPageActions(
            FTSPreBookingInfo preBookingInfo,
            Map<String, String> queryParams,
            UserContext userContext,
            boolean isFlutterSupportSectionEnabled,
            ServiceInterfaces serviceInterfaces
    ) {
        ActionWidget actionWidget = new ActionWidget();
        List<Action> actions = new ArrayList<>();
        boolean isPlaySelectSupported = PlayUtil.isPlaySelectSupported(userContext, serviceInterfaces);
        boolean isSportsLevelPricingSupported = PlayUtil.isSportLevelPricingSupported(serviceInterfaces.segmentEvaluatorService,
                serviceInterfaces.environmentService, userContext);

        if (preBookingInfo.getProductArenaCategoryId() != null)
            queryParams.put("productArenaCategoryId", preBookingInfo.getProductArenaCategoryId().toString());

        switch (preBookingInfo.getStatus()) {
            case TRIAL, MEMBERSHIP, OTHER_BENEFITS, SPORT_CENTER_SELECT_MEMBERSHIP, SPORT_CITY_SELECT_MEMBERSHIP, PLAY_ENTERPRISE_CITY_SPORT:
                String actionTitle = "CONFIRM & BOOK";
                // Change action title for waitlist classes.
                if (isWaitlistClass(preBookingInfo)) {
                    actionTitle = "JOIN WAITLIST";
                }

                Action confirmClassAction = new Action(
                        ActionType.FITSO_BOOK_CLASS, actionTitle
                );
                confirmClassAction.setMeta(getMetaWithSlot(queryParams));
                confirmClassAction.setVariant("primary");

                actions.add(confirmClassAction);
                break;
            case TRIAL_EXHAUSTED:
                actionWidget.setTitle("You have used your free trial for " + preBookingInfo.getWorkoutDetails().getName());


                Action buyPackAction = PlayUtil.getBuyPackPageAction(isSportsLevelPricingSupported, isPlaySelectSupported, "primary");
                actions.add(buyPackAction);
                break;
            case OTHER_BENEFITS_EXHAUSTED:
                String sessionText =  preBookingInfo.getMembership().getMaxTickets() == null || preBookingInfo.getMembership().getMaxTickets() > 1 ? " sessions" : " session";
                String ticketsUsed = preBookingInfo.getMembership().getMaxTickets() != null ? String.valueOf(preBookingInfo.getMembership().getMaxTickets()) : "";
                String actionWidgetTitle = "You have used your " + ticketsUsed + sessionText;
                actionWidget.setTitle(actionWidgetTitle);

                Action buyPackActionOther = PlayUtil.getBuyPackPageAction(isSportsLevelPricingSupported, isPlaySelectSupported, "primary");
                actions.add(buyPackActionOther);
                break;
            case PURCHASE_PLAY_PACK:
                Action buyPackActionOthers = PlayUtil.getBuyPackPageAction(isSportsLevelPricingSupported, isPlaySelectSupported, "primary");
                actions.add(buyPackActionOthers);
                break;
            case PLAY_CITY_LIMITED_MEMBERSHIP:
                String sessionsText =  preBookingInfo.getMembership().getMaxTickets() == null || preBookingInfo.getMembership().getMaxTickets() > 1 ? " sessions left" : " session left";
                List<Benefit> playBenefit = preBookingInfo.getMembership().getBenefits().stream().filter(benefit -> benefit.getName().equalsIgnoreCase("PLAY")).toList();
                String ticketLeft = String.valueOf(playBenefit.getFirst().getMaxTickets() - playBenefit.getFirst().getTicketsUsed());
                String actionsWidgetTitle = ticketLeft + " of " + playBenefit.getFirst().getMaxTickets() + sessionsText;
                actionWidget.setTitle(actionsWidgetTitle);

                Action confirmBookingAction = new Action(ActionType.FITSO_BOOK_CLASS, "CONFIRM & BOOK");
                confirmBookingAction.setMeta(getMetaWithSlot(queryParams));
                confirmBookingAction.setVariant("primary");
                actions.add(confirmBookingAction);
                break;
            case EXPIRED:
                TextStyle style = new TextStyle();
                style.setColor("#FF5942");
                actionWidget.setStyle(style);

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM");
                LocalDateTime expiryDateInTz = TimeUtil.getDateFromTime(preBookingInfo.getMembership().getEndTime(), userContext.getUserProfile().getTimezone());
                String membershipExpiryDate = ordinal(expiryDateInTz.getDayOfMonth()) + " " + expiryDateInTz.format(formatter);

                String expiredActionWidgetTitle = "CULTPASS PLAY EXPIRED ON " + membershipExpiryDate;
                actionWidget.setTitle(expiredActionWidgetTitle);

                Action renewAction = PlayUtil.getMembershipLandingPageAction("RENEW CULTPASS");
                actions.add(renewAction);
                break;
            case PAUSE:
                TextStyle pauseStyle = new TextStyle();
                pauseStyle.setColor("#F7C744");
                actionWidget.setStyle(pauseStyle);

                DateTimeFormatter pauseFormatter = DateTimeFormatter.ofPattern("MMM");
                LocalDateTime pauseExpiryDateInTz = TimeUtil.getDateFromTime(preBookingInfo.getMembership().getActivePauseEndTime() - 1000, userContext.getUserProfile().getTimezone());
                String pauseMembershipEndDate = ordinal(pauseExpiryDateInTz.getDayOfMonth()) + " " + pauseExpiryDateInTz.format(pauseFormatter);

                String pauseActionWidgetTitle = "MEMBERSHIP PAUSED TILL " + pauseMembershipEndDate;
                actionWidget.setTitle(pauseActionWidgetTitle);

                actions.add(getUnpauseAndBookAction(preBookingInfo, false, queryParams));
                break;
            case UPCOMING_PAUSE:
                TextStyle upcomingPausestyle = new TextStyle();
                upcomingPausestyle.setColor("#F7C744");
                actionWidget.setStyle(upcomingPausestyle);

                DateTimeFormatter upcomingPauseformatter = DateTimeFormatter.ofPattern("MMM");
                LocalDateTime upcomingPauseExpiryDateInTz = TimeUtil.getDateFromTime(preBookingInfo.getMembership().getActivePauseEndTime() - 1000, userContext.getUserProfile().getTimezone());
                String upcomingPauseMembershipEndDate = ordinal(upcomingPauseExpiryDateInTz.getDayOfMonth()) + " " + upcomingPauseExpiryDateInTz.format(upcomingPauseformatter);

                String upcomingPauseActionWidgetTitle = "MEMBERSHIP PAUSED TILL " + upcomingPauseMembershipEndDate;
                actionWidget.setTitle(upcomingPauseActionWidgetTitle);

                actions.add(getUnpauseAndBookAction(preBookingInfo, true, queryParams));
                break;
            case UPCOMING_MEMBERSHIP:
                DateTimeFormatter upMemFormatter = DateTimeFormatter.ofPattern("MMM");
                LocalDateTime upExpiryDateInTz = TimeUtil.getDateFromTime(preBookingInfo.getMembership().getStartTime(), userContext.getUserProfile().getTimezone());
                String membershipStartDate = ordinal(upExpiryDateInTz.getDayOfMonth()) + " " + upExpiryDateInTz.format(upMemFormatter);

                String upMemActionWidgetTitle = "Your membership will start on " + membershipStartDate;
                actionWidget.setTitle(upMemActionWidgetTitle);

                String supportUrl = isFlutterSupportSectionEnabled ? AppUtil.SUPPORT_DEEP_LINK : "curefit://support";
                actions.add(new Action(supportUrl ,"CONTACT SUPPORT", ActionType.NAVIGATION));
                break;
            case SELECT_MEMBERSHIP:
                List <FTSCenterInfo> eligibleCenters = preBookingInfo.getApplicableCenters();
                boolean isEligibleToBook = false;
                Long currentCenterId = preBookingInfo.getCenterDetails().getId();
                for (FTSCenterInfo centerInfo: eligibleCenters) {
                    Long applicableCenterId = centerInfo.getId();
                    if (currentCenterId.equals(applicableCenterId)) {
                        isEligibleToBook = true;
                    }
                }

                if (isEligibleToBook) {
                    String selectActionTitle = "CONFIRM & BOOK";
                    Action selectClassAction = new Action(
                            ActionType.FITSO_BOOK_CLASS, selectActionTitle
                    );
                    selectClassAction.setMeta(getMetaWithSlot(queryParams));
                    selectClassAction.setVariant("primary");

                    actions.add(selectClassAction);
                } else {
                    handleSelectNotEligibleAction(actionWidget, actions, userContext, serviceInterfaces, eligibleCenters);
                }
                break;
            case PLAY_SELECT_PAUSE, UPCOMING_PLAY_SELECT_PAUSE:
                List <FTSCenterInfo> pauseEligibleCenters = preBookingInfo.getApplicableCenters();
                boolean isEligibleToPause = false;
                Long pauseCurrentCenterId = preBookingInfo.getCenterDetails().getId();
                for (FTSCenterInfo centerInfo: pauseEligibleCenters) {
                    Long applicableCenterId = centerInfo.getId();
                    if (pauseCurrentCenterId.equals(applicableCenterId)) {
                        isEligibleToPause = true;
                    }
                }

                if (isEligibleToPause) {
                    TextStyle selctPauseStyle = new TextStyle();
                    selctPauseStyle.setColor("#F7C744");
                    actionWidget.setStyle(selctPauseStyle);

                    DateTimeFormatter selectPauseFormatter = DateTimeFormatter.ofPattern("MMM");
                    LocalDateTime selectPauseExpiryDateInTz = TimeUtil.getDateFromTime(preBookingInfo.getMembership().getActivePauseEndTime() - 1000, userContext.getUserProfile().getTimezone());
                    String selectPauseMembershipEndDate = ordinal(selectPauseExpiryDateInTz.getDayOfMonth()) + " " + selectPauseExpiryDateInTz.format(selectPauseFormatter);

                    String selectPauseActionWidgetTitle = "MEMBERSHIP PAUSED TILL " + selectPauseMembershipEndDate;
                    actionWidget.setTitle(selectPauseActionWidgetTitle);

                    if (preBookingInfo.getStatus() == BookingEligibility.UPCOMING_PLAY_SELECT_PAUSE) {
                        actions.add(getUnpauseAndBookAction(preBookingInfo, true, queryParams));
                    } else {
                        actions.add(getUnpauseAndBookAction(preBookingInfo, false, queryParams));
                    }
                } else {
                    handleSelectNotEligibleAction(actionWidget, actions, userContext, serviceInterfaces, pauseEligibleCenters);
                }
                break;
            case NO_SHOW_BLOCKED:
                if (PlayUtil.isPlayNoShowV2Supported(userContext)) {
                    actionWidget.setTitle("Bookings for this day are blocked due to no show penalty");
                    Action noShowaction = PlayUtil.getPlayNoShowPolicyModalAction("VIEW NO-SHOW POLICY");
                    actions.add(noShowaction);
                } else {
                    actionWidget.setTitle("Bookings for this day are blocked due to no show penalty. Please update the app for more details.");
                    Action noShowaction = AppUtil.getUpdateAppAction(userContext, "UPDATE APP");
                    actions.add(noShowaction);
                }
                break;
            case SLOT_CAPACITY_FULL:
                actionWidget.setTitle("This slot is full. Please try another time.");
                Action confirmBooking = new Action(ActionType.FITSO_BOOK_CLASS, "CONFIRM & BOOK");
                confirmBooking.setMeta(getMetaWithSlot(queryParams));
                confirmBooking.setVariant("primary");
                confirmBooking.setDisabled(true);
                confirmBooking.setIsEnabled(false);
                actions.add(confirmBooking);
                break;

            case PLAY_AWAY_BENEFITS_EXHAUSTED:
                Integer maxAwayTickets = PlayUtil.getPlayMaxTicketsUsed(preBookingInfo.getMembership().getBenefits());
                actionWidget.setTitle("You have exhausted your " + maxAwayTickets + " other center sessions");

                Action awayUpgradeAction = PlayUtil.getSLPUpgradeAction(
                        userContext,
                    preBookingInfo.getMembership().getProductId(),
                    String.valueOf(preBookingInfo.getMembership().getId())
                );
                awayUpgradeAction.setTitle("UPGRADE NOW");
                actions.add(awayUpgradeAction);
                break;

            case PLAY_AWAY_BENEFITS:
                int currentAwayTicketCount = PlayUtil.getPlayAwayTicketsUsed(preBookingInfo.getMembership().getBenefits()) + 1;
                String awayActionWidgetTitle = "You are booking your " + ordinal(currentAwayTicketCount) + " other center session";
                actionWidget.setTitle(awayActionWidgetTitle);


                String awayActionTitle = "CONFIRM & BOOK";
                // Change action title for waitlist classes.
                if (isWaitlistClass(preBookingInfo)) {
                    awayActionTitle = "JOIN WAITLIST";
                }
                Action awayConfirmClassAction = new Action(
                    ActionType.FITSO_BOOK_CLASS, awayActionTitle
                );
                awayConfirmClassAction.setMeta(getMetaWithSlot(queryParams));
                awayConfirmClassAction.setVariant("primary");
                actions.add(awayConfirmClassAction);

                break;

            case LIMITED_AVAILABLE:
                MembershipBenefitInfo benefitInfo = PlayUtil.getLimitedSessionTicketRemainingCount(preBookingInfo.getMembership().getBenefits());
                Integer limitedAvailableSession = benefitInfo.getTicketRemaining();

                String limitedActionTitle = "CONFIRM & BOOK";
                // Change action title for waitlist classes.
                if (isWaitlistClass(preBookingInfo)) {
                    limitedActionTitle = "JOIN WAITLIST";
                }

                Action limitedConfirmClassAction = new Action(
                        ActionType.FITSO_BOOK_CLASS, limitedActionTitle
                );
                limitedConfirmClassAction.setMeta(getMetaWithSlot(queryParams));
                limitedConfirmClassAction.setVariant("primary");

                actions.add(limitedConfirmClassAction);

                String sessionString = limitedAvailableSession != null && limitedAvailableSession > 1 ? limitedAvailableSession + " SESSIONS" : limitedAvailableSession + " SESSION";
                String limitedActionWidgetTemplate = limitedAvailableSession != null && limitedAvailableSession <= 5 ? "ONLY LAST " + sessionString + " LEFT" : null;
                actionWidget.setTitle(limitedActionWidgetTemplate);
                TextStyle limitedTitleStyle = new TextStyle();
                limitedTitleStyle.setColor("#F7C744");
                actionWidget.setStyle(limitedTitleStyle);
                break;
            case LIMITED_SESSION_UNAVAILABLE:
                actionWidget.setTitle("You don't have access to this slot");
                Action limitedUnavailablePackAction = PlayUtil.getBuyPackPageAction(isSportsLevelPricingSupported, isPlaySelectSupported, "primary");
                actions.add(limitedUnavailablePackAction);
                TextStyle limitedSessionStyle = new TextStyle();
                limitedSessionStyle.setColor("#F7C744");
                actionWidget.setStyle(limitedSessionStyle);
                break;
            case LIMITED_EXPIRED_SESSION:
                actionWidget.setTitle("ALL SESSIONS EXHAUSTED");
                Action limitedExpiredPackAction = PlayUtil.getBuyPackPageAction(isSportsLevelPricingSupported, isPlaySelectSupported, "primary");
                actions.add(limitedExpiredPackAction);
                TextStyle limitedExpiredStyle = new TextStyle();
                limitedExpiredStyle.setColor("#F7C744");
                actionWidget.setStyle(limitedExpiredStyle);
                break;
            case PLAY_OTHER_SPORTS_BENEFITS:
                MembershipBenefitInfo cslpBenefitInfo = PlayUtil.getCityLevelPackSessionRemainingCount(preBookingInfo.getMembership().getBenefits());
                String title = "CONFIRM & BOOK";
                if (isWaitlistClass(preBookingInfo)) {
                    title = "JOIN WAITLIST";
                }
                Action bookingAction = new Action(ActionType.FITSO_BOOK_CLASS, title);
                bookingAction.setMeta(getMetaWithSlot(queryParams));
                bookingAction.setVariant("primary");
                actions.add(bookingAction);

                String textTemplate = "%d/%d other sports sessions left".formatted(cslpBenefitInfo.getTicketRemaining(), cslpBenefitInfo.getTotalTickets());
                actionWidget.setTitle(textTemplate);
                TextStyle styles = new TextStyle();
                styles.setColor(Objects.equals(cslpBenefitInfo.getTicketRemaining(), cslpBenefitInfo.getTotalTickets()) ? "#ffffff" : "#F7C744");
                actionWidget.setStyle(styles);
                break;
            case PLAY_OTHER_SPORTS_BENEFITS_EXHAUSTED:
                MembershipBenefitInfo cslpBenefitExhausted = PlayUtil.getCityLevelPackSessionRemainingCount(preBookingInfo.getMembership().getBenefits());
                String otherSportSessionsText =  cslpBenefitExhausted.getTotalTickets() == null || cslpBenefitExhausted.getTotalTickets() > 1 ? " sessions" : " session";
                String actionsTitle = "You have used your %d/%d %s".formatted(cslpBenefitExhausted.getTotalTickets(), cslpBenefitExhausted.getTotalTickets(), otherSportSessionsText);
                actionWidget.setTitle(actionsTitle);
                TextStyle expiredStyle = new TextStyle();
                expiredStyle.setColor("#FF5942");
                actionWidget.setStyle(expiredStyle);

                Action upgradeToPlayAction = PlayUtil.getBuyPackPageAction(true, false, "primary");
                actions.add(upgradeToPlayAction);
                break;
        }

        actionWidget.setActions(actions);
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", Spacing.builder().top("30").bottom("0").build());
        actionWidget.setLayoutProps(layoutProps);

        return actionWidget;
    }

    private Boolean showInstructionModal(FTSPreBookingInfo preBookingInfo, boolean isFirstTimeUser, ActionWidget pageAction){
        if (isFirstTimeUser && pageAction.getActions().size() == 1 && preBookingInfo.getInstructions() != null && !preBookingInfo.getInstructions().isEmpty()) {
            return switch (preBookingInfo.getStatus()) {
                case TRIAL_EXHAUSTED, UPCOMING_MEMBERSHIP, SLOT_CAPACITY_FULL, NO_SHOW_BLOCKED, EXPIRED,
                        OTHER_BENEFITS_EXHAUSTED, LIMITED_SESSION_UNAVAILABLE, LIMITED_EXPIRED_SESSION, PLAY_AWAY_BENEFITS_EXHAUSTED, PLAY_OTHER_SPORTS_BENEFITS_EXHAUSTED ->
                        false;
                default -> true;
            };
        }
        return false;
    }

    private void handleSelectNotEligibleAction(ActionWidget actionWidget, List<Action> actions, UserContext userContext, ServiceInterfaces serviceInterfaces, List<FTSCenterInfo> eligibleCenters) {
        String selectActionWidgetTitle = "ACCESS TO " + eligibleCenters.get(0).getName().toUpperCase() + " ONLY";
        actionWidget.setTitle(selectActionWidgetTitle);

        String selectActionTitle = "SWITCH CENTER";
        List<Membership> playMemberships = new ArrayList<>();
        try {
            playMemberships = PlayUtil.getCurrentPlayMemberships(userContext, serviceInterfaces);
        } catch (Exception ex) {}
        // Fetching membership to verify cityId of center / membership is same as users current cityId.
        Membership playMembership = playMemberships.get(0);
        String playMembershipCity = (String) playMembership.getMetadata().get("cityId");
        String userCityId = userContext.getUserProfile().getCity().getCityId();
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        if (playMembershipCity.equals(userCityId)) {
            nameValuePairs.add(new BasicNameValuePair("centerId", String.valueOf(eligibleCenters.get(0).getId())));
        }

        Action switchCenterAction = PlayUtil.getPlayClassBookingAction(nameValuePairs, selectActionTitle);
        switchCenterAction.setVariant("secondary");
        switchCenterAction.setActionType(ActionType.RESET_NAVIGATION);
        actions.add(switchCenterAction);

        Action supportAction = PlayUtil.getRequestUpgradeAction(serviceInterfaces, "UPGRADE PASS", playMembership, userContext);
        supportAction.setVariant("primary");
        actions.add(supportAction);
    }

    public Map<String, Object> getMetaWithSlot(Map<String, String> queryParams) {
        Map<String, Object> meta = new HashMap<>();

        Map<String, Long> slotMeta = new HashMap<>();
        slotMeta.put("centerId", Long.valueOf(queryParams.get("centerId")));
        slotMeta.put("workoutId", Long.valueOf(queryParams.get("workoutId")));
        slotMeta.put("slotId", Long.valueOf(queryParams.get("slotId")));
        slotMeta.put("bookingTimestamp", Long.valueOf(queryParams.get("bookingTimestamp")));
        if (queryParams.containsKey("productArenaCategoryId"))
            slotMeta.put("productArenaCategoryId", Long.valueOf(queryParams.get("productArenaCategoryId")));
        meta.put("slot", slotMeta);

        return meta;
    }

    private boolean isWaitlistClass(FTSPreBookingInfo preBookingInfo) {
        return preBookingInfo.getWaitlistInfo() != null && preBookingInfo.getWaitlistInfo().getIsWaitlistAvailable() != null && preBookingInfo.getWaitlistInfo().getIsWaitlistAvailable();
    }

    private Action getUnpauseAndBookAction(FTSPreBookingInfo preBookingInfo, boolean isUpcomingPause, Map<String, String> queryParams) {
        String actionTitle = "UNPAUSE AND BOOK";

        // Change action title for waitlist classes.
        if (isWaitlistClass(preBookingInfo)) {
            actionTitle = "UNPAUSE & JOIN WAITLIST";
        }

        Action unpauseAction = new Action(ActionType.FITSO_RESUME_MEMBERSHIP_BOOK_CLASS, actionTitle);

        FitsoUnpauseAndBookActionMeta fitsoUnpausAndBookActionMeta = new FitsoUnpauseAndBookActionMeta();
        fitsoUnpausAndBookActionMeta.setMembershipId(preBookingInfo.getMembership().getId().toString());
        fitsoUnpausAndBookActionMeta.setProductType(ProductType.PLAY);
        fitsoUnpausAndBookActionMeta.setIsUpcomingPause(isUpcomingPause);
        fitsoUnpausAndBookActionMeta.setToastMessage("Membership unpaused successfully");

        Map<String, Long> slotMeta = new HashMap<>();
        slotMeta.put("centerId", Long.valueOf(queryParams.get("centerId")));
        slotMeta.put("workoutId", Long.valueOf(queryParams.get("workoutId")));
        slotMeta.put("slotId", Long.valueOf(queryParams.get("slotId")));
        slotMeta.put("bookingTimestamp", Long.valueOf(queryParams.get("bookingTimestamp")));
        slotMeta.put("productArenaCategoryId", Long.valueOf(queryParams.get("productArenaCategoryId")));
        fitsoUnpausAndBookActionMeta.setSlot(slotMeta);

        unpauseAction.setMeta(fitsoUnpausAndBookActionMeta);

        return unpauseAction;
    }
    private BaseWidget getWaitlistWidget(
            FTSPreBookingInfo preBookingInfo,
            UserContext userContext,
            Map<String, String> queryParams,
            ServiceInterfaces interfaces
    ) throws Exception {
        WaitlistInfoMeta waitlistInfo = this.getWaitlistInfo(preBookingInfo);
        WaitlistConfirmationTimePicker timePicker = this.getWaitlistTimePicker(preBookingInfo, userContext, queryParams, interfaces);

        WaitlistWidget waitlistWidget = new WaitlistWidget();
        waitlistWidget.setHeaderText("WAITLIST");
        waitlistWidget.setWaitlistCount(preBookingInfo.getWaitlistInfo().getWaitlistedUserCount() + 1);
        waitlistWidget.setWaitlistInfo(waitlistInfo);
        waitlistWidget.setTimePicker(timePicker);

        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", Spacing.builder().top("20").bottom("0").build());
        waitlistWidget.setLayoutProps(layoutProps);

        waitlistWidget.setProductType("PLAY");
        waitlistWidget.setSlotId(Long.valueOf(queryParams.get("slotId")));
        waitlistWidget.setWorkoutId(Long.valueOf(queryParams.get("workoutId")));
        waitlistWidget.setBookingTimestamp(Long.valueOf(queryParams.get("bookingTimestamp")));
        waitlistWidget.setCenterId(Long.valueOf(queryParams.get("centerId")));

        return waitlistWidget;
    }

    private WaitlistInfoMeta getWaitlistInfo(FTSPreBookingInfo preBookingInfo) {
        Integer waitlistCount = preBookingInfo.getWaitlistInfo().getWaitlistedUserCount() + 1;
        String title = "WL upto #" + preBookingInfo.getWaitlistInfo().getWaitlistConfirmThreshold() + " usually gets confirmed";
        Action infoAction = new Action("curefit://listpage?pageId=waitlisthiw", ActionType.NAVIGATION);
        String waitlistPrediction = "You will be informed in case of cancellation";

        WaitlistInfoMeta waitlistInfoMeta = new WaitlistInfoMeta();
        waitlistInfoMeta.setInfoAction(infoAction);
        if (preBookingInfo.getWaitlistInfo().getWaitlistConfirmThreshold() > 0)
            waitlistInfoMeta.setTitle(title);
        waitlistInfoMeta.setWaitlistCount(waitlistCount);
        waitlistInfoMeta.setWaitlistCnfProbability(
                PlayUtil.getWaitListProbability(
                        waitlistCount,
                        preBookingInfo.getWaitlistInfo().getWaitlistConfirmThreshold())
        );

        return waitlistInfoMeta;
    }

    private WaitlistConfirmationTimePicker getWaitlistTimePicker(
            FTSPreBookingInfo preBookingInfo,
            UserContext userContext,
            Map<String, String> queryParams,
            ServiceInterfaces serviceInterfaces
    ) throws Exception {
//        const classStartTime = TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.startTime, cultClass.timezone as Timezone, TimeUtil.HH_MM_SS_DATE_FORMAT)
        Long startTimeInEpoch = Long.valueOf(queryParams.get("bookingTimestamp"));

        WaitlistExtensionSlots waitlistExtensionSlots = PlayUtil.getTimeSlotsForWaitlistExtensionWidget(preBookingInfo.getWaitlistInfo().getWlNotificationSlots(), userContext.getUserProfile().getTimezone(), startTimeInEpoch);
        if (waitlistExtensionSlots.getTimeRemainingToClass() <= preBookingInfo.getWaitlistInfo().getWlNotificationTime()) {
            List <Integer> timeSlots = waitlistExtensionSlots.getNotificationTimeSlots();
            if (timeSlots.size() > 0) {
                FitsoPostRequestResponseView cancellationView = PlayUtil.updateWaitlistNotificationTime(userContext, timeSlots.get(timeSlots.size() - 1), null, serviceInterfaces);
                if (cancellationView != null) {
                    preBookingInfo.getWaitlistInfo().setWlNotificationTime(timeSlots.get(timeSlots.size() - 1));
                }
            }
        }

        Boolean isWaitlistTimePreSelected = preBookingInfo.getWaitlistInfo().getWlNotificationTime() != null;
        Long wlConfirmTime = isWaitlistTimePreSelected ? startTimeInEpoch - (preBookingInfo.getWaitlistInfo().getWlNotificationTime() * 60000) : null;

        List<WorkoutWaitlistTimingSlot> timeSlot = waitlistExtensionSlots.getNotificationTimeSlots().stream().map(time -> {
            WorkoutWaitlistTimingSlot timingSlot = new WorkoutWaitlistTimingSlot();
            timingSlot.setTitle(time + " mins before the class");
            timingSlot.setTime(time);
            timingSlot.setIsSelected(preBookingInfo.getWaitlistInfo().getWlNotificationTime() == time);
            timingSlot.setIsRecommended(PlayUtil.PLAY_WAITLIST_EXTENSION_RECOMMENDED_TIME == time);
            return timingSlot;
        }).collect(Collectors.toList());


        WaitlistConfirmationTimePicker waitlistConfirmationTimePicker = new WaitlistConfirmationTimePicker();
        waitlistConfirmationTimePicker.setTitle(isWaitlistTimePreSelected ? "Edit waitlist confirmation time" : "Pick waitlist confirmation time");
        waitlistConfirmationTimePicker.setDescription("The closer you are to the class time, higher the chances of confirmation");
        waitlistConfirmationTimePicker.setTimeSlots(timeSlot);

        waitlistConfirmationTimePicker.setCollapsibleProperties(new CollapsibleProperties(true, isWaitlistTimePreSelected));
        waitlistConfirmationTimePicker.setClassTimeInEpochs(startTimeInEpoch);
        waitlistConfirmationTimePicker.setCollapsedDescription("Waitlist status will be confirmed by " + TimeUtil.getTimeInFormatFromMillis(wlConfirmTime,"hh:mm a", userContext.getUserProfile().getTimezone()));

        return waitlistConfirmationTimePicker;
    }

    public FitsoPreBookingPageWebView buildWebView(
            UserContext userContext,
            FTSPreBookingInfo preBookingInfo,
            Map<String, String> queryParams,
            boolean isFirstTimeUser
    ) throws Exception {

        FitsoPreBookingPageWebView page = new FitsoPreBookingPageWebView();
        page.setWidgets(new ArrayList<>());

        Long startTimeInEpoch = Long.valueOf(queryParams.get("bookingTimestamp"));
        page.setDate(TimeUtil.getTimeInFormatFromMillis(startTimeInEpoch, "yyyy-MM-dd", userContext.getUserProfile().getTimezone()));
        page.setFormattedStartTime(TimeUtil.getTimeInFormatFromMillis(startTimeInEpoch, "hh:mm aa", userContext.getUserProfile().getTimezone()).toUpperCase());

        FitsoCommonWebBuilder commonWebBuilder = new FitsoCommonWebBuilder();
        ProductGridWidget preWorkoutGearWidget = commonWebBuilder.getPreWorkoutGearWebWidget(preBookingInfo.getWorkoutDetails());
        if(preWorkoutGearWidget != null)
            page.getWidgets().add(preWorkoutGearWidget);
        page.setCenter(preBookingInfo.getCenterDetails());
        page.setWorkout(preBookingInfo.getWorkoutDetails());
        page.setStatus(preBookingInfo.getStatus());
        page.setActionTitle("CONFIRM & BOOK");
        return  page;
    }


}
