package com.curefit.cfapi.view.viewbuilders.order;

import com.curefit.center.dtos.CenterEntry;
import com.curefit.cfapi.model.internal.order.OrderCheckoutRequestV2;
import com.curefit.cfapi.model.internal.order.OrderCheckoutResponse;
import com.curefit.cfapi.model.internal.order.OrderMeta;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.FitnessCenterUtil;
import com.curefit.cfapi.util.MembershipUtil;
import com.curefit.cfapi.util.OrderUtil;
import com.curefit.location.models.City;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.oms.models.order.Order;
import com.curefit.oms.models.order.OrderCreate;
import com.curefit.pms.pojo.customPacks.augments.AugmentedPackEntry;
import com.curefit.pms.requests.PackTransitionPricingRequest;
import com.curefit.product.enums.ProductType;
import com.curefit.product.models.Product;
import com.curefit.userservice.pojo.entry.UserEntry;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class OrderCheckoutViewBuilder {

    public OrderCheckoutResponse buildOrderCheckoutPageView(OrderCheckoutRequestV2 request, UserContext userContext, ServiceInterfaces interfaces) throws Exception {

        OrderCheckoutResponse checkForPackTransition = checkForPackTransition(request, userContext, interfaces);
        if (Objects.nonNull(checkForPackTransition)) return checkForPackTransition;

        throw new Exception("Not Implemented");
    }

    public OrderCheckoutResponse checkForPackTransition(OrderCheckoutRequestV2 request, UserContext userContext, ServiceInterfaces interfaces) throws Exception  {
        if (CollectionUtils.isNotEmpty(request.getOrderProducts()) &&
            request.getOrderProducts().getFirst().getOption() != null &&
            request.getOrderProducts().getFirst().getOption().getTransitionRequest() != null
        ) {
            PackTransitionPricingRequest transitionRequest = request.getOrderProducts().getFirst().getOption().getTransitionRequest();
            CenterEntry centerTo = null;
            if (request.getPreferredCenterServiceId() != null) {
                centerTo = interfaces.centerService.getCenterDetails(Long.valueOf(request.getPreferredCenterServiceId()), false, null, null).get();
            }
            String centerId = null;
            String centerServiceId = null;
            if (Objects.nonNull(centerTo)) {
                centerServiceId = String.valueOf(centerTo.getId());
                centerId = FitnessCenterUtil.getCenterId(centerTo);
            }
            AugmentedPackEntry packEntry = interfaces.pricingService.getTransitionPack(transitionRequest);
            Long membershipServiceId = transitionRequest.getMembershipServiceId();
            Membership membership = interfaces.membershipService.getMembershipById(membershipServiceId, userContext.getUserProfile().getUserId()).get();
            City cityFrom = MembershipUtil.getCityIdFromMembership(membership, userContext, interfaces.cityCache, interfaces.catalogueServicePMS);
            OrderCreate orderCreate = OrderUtil.getPackTransitionOrder(transitionRequest, cityFrom.getCityId(), userContext, packEntry, centerId, centerServiceId);
            return getResponseFromOrderCreate(orderCreate, userContext, interfaces, packEntry.getId(), packEntry.getProduct().getProductType());
        }
        return null;
    }

    public OrderCheckoutResponse getResponseFromOrderCreate(OrderCreate orderCreate, UserContext userContext, ServiceInterfaces interfaces, String productId, ProductType productType) throws Exception {
        Order baseOrder = interfaces.omsOrderService.createOrder(orderCreate);
        OrderCheckoutResponse orderCheckoutResponse = new OrderCheckoutResponse();
        double payable = baseOrder.getTotalPayable() != null
                ? baseOrder.getTotalPayable()
                : baseOrder.getTotalAmountPayable();

        OrderMeta.Price price = new OrderMeta.Price();
        price.setListingPrice(payable);
        price.setMrp(payable);

        UserEntry user = interfaces.userServiceClient.getUser(userContext.getUserProfile().getUserId()).get();
        OrderMeta meta = new OrderMeta();
        meta.setOrderId(baseOrder.getOrderId());
        meta.setCustomerName(user.getFirstName() + " " + user.getLastName());
        meta.setCustomerEmail(user.getEmail());
        meta.setCustomerPhone(user.getPhone());
        meta.setCustomerGender(user.getGender());
        if (user.getBirthday() != null) {
            LocalDate birthLocalDate = user.getBirthday().toInstant().atZone(ZoneId.of(userContext.getUserProfile().getTimezone())).toLocalDate();
            LocalDate today = LocalDate.now();
            meta.setCustomerAge(Period.between(birthLocalDate, today).getYears());
        }
        meta.setPrice(price);
        meta.setOrderType("OTHER");
        meta.setProductType(productType);
        meta.setProductId(productId);

        List<String> productIds = Optional.ofNullable(baseOrder.getProductSnapshots())
                .orElse(Collections.emptyList())
                .stream()
                .map(Product::getProductId)
                .collect(Collectors.toList());
        meta.setProductIds(productIds);

        meta.setTotalAmountPayable(baseOrder.getTotalAmountPayable());
        meta.setTotalFitCashPayable(baseOrder.getTotalFitCashPayable());
        orderCheckoutResponse.setOrderMeta(meta);
        return orderCheckoutResponse;
    }
}
