package com.curefit.cfapi.view.viewmodels.chroniccare.sfliteapp;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.view.viewmodels.chroniccare.RenewSubscriptionPageView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.sfliteapp.WebinarCardWidget;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class SfLiteAppHomePageView {
    long pageTTL = 300000L; // to auto sync page data after every 5 min
    List<BaseWidgetNonVM> widgets;
    Action displayWebinarModal;
    Action buySfPackModalAction;

    public SfLiteAppHomePageView() {
        this.widgets = new ArrayList<>();
    }

    public void addWidget(BaseWidgetNonVM widget) {
        if (widget != null) {
            this.widgets.add(widget);
        }
    }

    public void addWidgets(ArrayList<? extends BaseWidgetNonVM> widgetList) {
        if (!CollectionUtils.isEmpty(widgetList)) {
            widgets.addAll(widgetList.stream().filter(Objects::nonNull).toList());
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WebinarPurchaseModalData{
        String title;
        WebinarCardWidget.WebinarCard cardData;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PackPurchaseModalData{
        String title;
        List<RenewSubscriptionPageView.PackData> recommendedPacks;
    }
}
