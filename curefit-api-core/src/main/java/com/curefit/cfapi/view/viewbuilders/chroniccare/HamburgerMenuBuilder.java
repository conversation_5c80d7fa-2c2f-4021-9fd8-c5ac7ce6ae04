package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.albus.response.ActivePackResponse;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.dto.sugarfit.SfCgmDeviceStatus;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.chroniccare.DevOptionItem;
import com.curefit.cfapi.pojo.chroniccare.sfliteapp.ModalNames;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.util.DigitalAppUtil;
import com.curefit.cfapi.view.viewmodels.chroniccare.HamburgerMenu;
import com.curefit.cfapi.view.viewmodels.chroniccare.sfliteapp.SfLiteAppHomePageView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.HamburgerMenuCard;
import com.curefit.cfapi.widgets.chroniccare.HamburgerProfileCard;
import com.curefit.cfapi.widgets.chroniccare.SfHamburgerAccordionList;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.enums.DeviceStatus;
import com.sugarfit.chs.pojo.CGMDeviceInfo;
import com.sugarfit.chs.pojo.CgmOnboardingStatusResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.curefit.cfapi.util.ChronicCareAppUtil.*;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class HamburgerMenuBuilder {
    public static final String SF_DEV_TOOLS_USER_SEGMENT_NAME = "sugarfit_dev_enabled_users";
    public static final String UF_DEV_TOOLS_USER_SEGMENT_NAME = "ultrafit_dev_enabled_users";
    public static final String REFERRAL_USER_SEGMENT_NAME = "sugarfit_referral_enabled_users";
    public static final String UF_REFERRAL_USER_SEGMENT_NAME = "ultrafit_referral_enabled_users";
    final CHSClient chsClient;
    final SegmentationCacheClient segmentationCacheClient;
    final UserOnboardingService userOnboardingService;
    final ExceptionReportingService exceptionReportingService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final ServiceInterfaces serviceInterfaces;

    boolean isDevToolsEnabledForUser(UserContext userContext, String userId, EnvironmentService environmentService) {
        // String segmentName = AppUtil.isUltraFitApp(userContext) ? UF_DEV_TOOLS_USER_SEGMENT_NAME : SF_DEV_TOOLS_USER_SEGMENT_NAME;
        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            if (environmentService.isStage() || environmentService.isAlpha()) {
                return true;
            }
            return userSegments.contains(SF_DEV_TOOLS_USER_SEGMENT_NAME) || userSegments.contains(UF_DEV_TOOLS_USER_SEGMENT_NAME);
        } catch (Exception e) {
            String message = String.format("Dev tools user segment error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    boolean isSfReferralEnabledForUser(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();

        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            return userSegments.contains(REFERRAL_USER_SEGMENT_NAME);
        } catch (Exception e) {
            String message = String.format("Referral user segment error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    boolean isUfReferralEnabledForUser(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            return userSegments.contains(UF_REFERRAL_USER_SEGMENT_NAME);
        } catch (Exception e) {
            String message = String.format("Referral user segment error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    public HamburgerMenu buildView(UserContext userContext, boolean salesApp) {
        HamburgerMenu hamburgerMenuPage = new HamburgerMenu();

        var user = userContext.getUserEntryCompletableFuture().join();

        ArrayList<BaseWidgetNonVM> result = new ArrayList<>();

        hamburgerMenuPage.addWidget(getProfileCard(user));

        if (salesApp) {
            result.add(getHamburgerCard(ActionType.NAVIGATION, "sfcommunity://accountsettings", "Personal Details", "/image/chroniccare/hamburger/navigation.png"));
            result.add(getHamburgerCard(ActionType.NAVIGATION, "sfcommunity://sugarlogjournal", "Sugar Logs", "/image/chroniccare/hamburger/navigation.png"));
            result.add(getSalesAppHelp());
            result.add(getHamburgerCard(ActionType.OPEN_WEBPAGE, "https://static.cure.fit/sugarfit-privacy.html","Privacy Policy", "/image/chroniccare/hamburger/navigation.png"));
            result.add(getHamburgerCard(ActionType.OPEN_WEBPAGE, "https://static.cure.fit/sugarfit-tnc.html","Terms and Conditions", "/image/chroniccare/hamburger/navigation.png"));
            result.add(getHamburgerCard(ActionType.OPEN_WEBPAGE, "https://static.cure.fit/cgm-info-disclaimer-v2.html", "Medical Disclaimer", "/image/chroniccare/hamburger/navigation.png"));

            if(isDevToolsEnabledForUser(userContext, userContext.getUserProfile().getUserId(), serviceInterfaces.environmentService)){
                result.add(getDevOption());
            }

            SfHamburgerAccordionList sfHamburgerAccordionList = new SfHamburgerAccordionList();
            sfHamburgerAccordionList.setSections(result);
            hamburgerMenuPage.addWidget(sfHamburgerAccordionList);
            return hamburgerMenuPage;
        }

        if (ChronicCareAppUtil.isAccountDetailsV2Supported(userContext)) {
            result.add(getAccountDetailsV2(userContext));
        } else{
            result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://accountsettings", "Account", "/image/chroniccare/hamburger/account.png"));
        }
        if (ChronicCareAppUtil.isSfAppLocalisationSupportedUser(userContext)) {
            result.add(getHamburgerCard(ActionType.SHOW_APP_LANGUAGE_SELECTION_MODAL, "", "Change App Language", "/image/chroniccare/hamburger/language_v2.png"));
        }
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            Boolean sfPackPurchased = this.userOnboardingService.getIfSugarFitPackActive(userContext.getUserProfile().getUserId());
            List<CGMDeviceInfo> cgmDeviceInfos = chsClient.fetchAllCgmDeviceInfo(userId, appTenant);
            CgmOnboardingStatusResponse cgmOnboardingStatusResponse = chsClient.fetchOnboardingStatus(userId, null, appTenant);
            boolean isNonCGMProduct = false;

            if (sfPackPurchased) {
                CompletableFuture<Optional<ActivePackResponse>> activePackResponseFuture = userOnboardingService.getSugarFitActivePackFuture(serviceInterfaces, userContext.getUserProfile().getUserId());
                Optional<ActivePackResponse> activePackResponseOptional = activePackResponseFuture.get();
                ActivePackResponse activePackResponse = activePackResponseOptional.orElse(null);
                if (activePackResponse != null) {
                    isNonCGMProduct = ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(), cgmOnboardingStatusResponse);
                }

                if (!isNonCGMProduct){
                    result.add(getHamburgerCard(ActionType.NAVIGATION,"curefit://cgmtabbedpage","CGM Insights", "/image/chroniccare/hamburger/cgm_insights_2.png"));

                    if (ChronicCareAppUtil.isBluconSupportedApp(userContext)) {
                        SfCgmDeviceStatus sfCgmDeviceStatus = chronicCareServiceHelper.getCgmDeviceStatus(userContext, cgmOnboardingStatusResponse, null);
                        if (Objects.nonNull(cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse())
                                && cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon()
                                && sfCgmDeviceStatus.getIsOnGoingDevice()
                                && Objects.nonNull(cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse())
                                && Objects.nonNull(cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse().getBluconDevice())
                                && cgmOnboardingStatusResponse.getBluconOnboardingStatusResponse().getBluconDevice().getStatus() == DeviceStatus.BCN_DELIVERED) {
                            result.add(getHamburgerCard(ActionType.NAVIGATION,"curefit://sfblucondetailspage","Blucon Integration", "/image/chroniccare/hamburger/cgm_insights_2.png"));
                        }
                    }
                }

                HamburgerMenuCard dietCard = getDietPlanCard(userContext);
                if (DigitalAppUtil.isDigitalAppUser(Objects.nonNull(activePackResponse) ? activePackResponse.getBundleProduct(): null, false)) {
                    if (Objects.nonNull(dietCard)) {
                        result.add(dietCard);
                    }
                } else {
                    result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://goalsprogress", "Progress", "/image/chroniccare/hamburger/progress_v2.png"));
                }

                if ((AppUtil.isUltraFitApp(userContext) || ChronicCareAppUtil.isSfExperimentEnabledUser(userContext)) && !CollectionUtils.isEmpty(cgmDeviceInfos)) {
                    result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://experimenttabs?index=0", "Experiments", "/image/chroniccare/hamburger/experiments.png"));
                }
                if (!AppUtil.isUltraFitApp(userContext)) {
                    result.add(getMedicalRecordsCard());
                }
                if (ChronicCareAppUtil.isECommerceSupported(userContext)) {
                    result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfecommerceorderspage", "Order History", "/image/chroniccare/hamburger/cart.png"));
                }
                if (!ChronicCareAppUtil.isAppStoreUserId(userContext)) {
                    if(!AppUtil.isUltraFitApp(userContext) && isSFInvoiceSupportedAppVersion(userContext)) {
                        result.add(getSubscriptionCard());
                    } else {
                        result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://subscription", "Subscription", "/image/chroniccare/hamburger/subscription.png"));
                    }
                }
                result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://fitnessdevices", "App Permissions", "/image/chroniccare/hamburger/fitness_apps_and_devices.png"));

                if (ChronicCareAppUtil.isRecommendedFitnessPlanEnabled(userContext) && !ChronicCareAppUtil.isSfWellnessHeroCardSupported(userContext)) {
                    result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfrecommendedfitnessplantabpage", "30 days Fitness Plan", "/image/chroniccare/hamburger/weight_lifter.png"));
                }

                if (!DigitalAppUtil.isDigitalAppUser(Objects.nonNull(activePackResponse) && Objects.nonNull(activePackResponse.getBundleProduct()) ? activePackResponse.getBundleProduct(): null, false)
                        && ChronicCareAppUtil.isPollsEnabled(userContext)) {
                    result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfpollspage", "Polls", "/image/chroniccare/hamburger/polls_v6.png"));
                }
                if (!DigitalAppUtil.isDigitalAppUser(Objects.nonNull(activePackResponse) && Objects.nonNull(activePackResponse.getBundleProduct()) ? activePackResponse.getBundleProduct(): null, false)
                        && ChronicCareAppUtil.isChallengesEnabled(userContext)) {
                    result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfchallengestabbedpage", "Challenges", "/image/chroniccare/hamburger/challenges.png"));
                }
                if (AppUtil.isUltraFitApp(userContext)) {
                    if (isUfReferralEnabledForUser(userContext) && !isInternationalSugarfitUser(userContext)) {
                        result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfreferralpage", "Referrals", "/image/chroniccare/hamburger/referrals.png"));
                    }
                } else {
                    if (isSfReferralEnabledForUser(userContext) && !isInternationalSugarfitUser(userContext)) {
                        result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfreferralpage", "Referrals", "/image/chroniccare/hamburger/referrals.png"));
                    }
                }
            } else {
                Optional<ActivePackResponse> expiredPackResponse = userOnboardingService.getSugarFitExpiredPack(userContext.getUserProfile().getUserId());
                // expired pack should not be a trial pack.
                if (expiredPackResponse.isPresent() && (!expiredPackResponse.get().getBundleProduct().getIsTrialProduct())) {
                    // case when pack has expired.
                    isNonCGMProduct = ChronicCareAppUtil.isNonCGMProduct(expiredPackResponse.get().getBundleProduct(), cgmOnboardingStatusResponse);
                    if (!isNonCGMProduct){
                        result.add(getHamburgerCard(ActionType.NAVIGATION,"curefit://cgmtabbedpage","CGM Insights", "/image/chroniccare/hamburger/cgm_insights_2.png"));
                        if (AppUtil.isUltraFitApp(userContext) && !CollectionUtils.isEmpty(cgmDeviceInfos)) {
                            result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://experimenttabs?index=0", "Experiments", "/image/chroniccare/hamburger/experiments.png"));
                        }
                    }
                    if (!AppUtil.isUltraFitApp(userContext)) {
                        result.add(getMedicalRecordsCard());
                    }
                }

                boolean isFreemium = chronicCareServiceHelper.isSfFreemiumUser(userContext);
                if (isFreemium && ChronicCareAppUtil.isECommerceSupported(userContext)) {
                    result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfecommerceorderspage", "Order History", "/image/chroniccare/hamburger/cart.png"));
                }
                if (isFreemium && !AppUtil.isUltraFitApp(userContext)) {
                    result.add(getMedicalRecordsCard());
                }
                result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://fitnessdevices", "App Permissions", "/image/chroniccare/hamburger/fitness_apps_and_devices.png"));
                HamburgerMenuCard dietCard = getDietPlanCard(userContext);
                if (DigitalAppUtil.isDigitalAppUser(expiredPackResponse.isPresent() && Objects.nonNull(expiredPackResponse.get().getBundleProduct()) ? expiredPackResponse.get().getBundleProduct(): null, isFreemium) && Objects.nonNull(dietCard)) {
                    result.add(dietCard);
                }
                if (!DigitalAppUtil.isDigitalAppUser(expiredPackResponse.isPresent() && Objects.nonNull(expiredPackResponse.get().getBundleProduct()) ? expiredPackResponse.get().getBundleProduct(): null, isFreemium)
                        && isFreemium && ChronicCareAppUtil.isPollsEnabled(userContext) && chronicCareServiceHelper.hasAnyPollsAvailableForUser(userContext)) {
                    result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfpollspage", "Polls", "/image/chroniccare/hamburger/polls_v6.png"));
                }
                if (!DigitalAppUtil.isDigitalAppUser(expiredPackResponse.isPresent() && Objects.nonNull(expiredPackResponse.get().getBundleProduct()) ? expiredPackResponse.get().getBundleProduct(): null, isFreemium)
                        && isFreemium && ChronicCareAppUtil.isChallengesEnabled(userContext)) {
                    result.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfchallengestabbedpage", "Challenges", "/image/chroniccare/hamburger/challenges.png"));
                }
            }
            result.add(getHelp(userContext));
            if(isDevToolsEnabledForUser(userContext, userContext.getUserProfile().getUserId(), serviceInterfaces.environmentService)){
                result.add(getDevOption());
            }

            SfHamburgerAccordionList sfHamburgerAccordionList = new SfHamburgerAccordionList();
            sfHamburgerAccordionList.setSections(result);
            hamburgerMenuPage.addWidget(sfHamburgerAccordionList);
        } catch (Exception e) {
            String msg = String.format("Failed to get hamburger menu for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
        }

        return hamburgerMenuPage;
    }

    private HamburgerMenuCard getHamburgerCard(ActionType actionType, String url, String title, String iconUrl) {
        HamburgerMenuCard card = new HamburgerMenuCard();
        var action = new Action(url, actionType);
        card.setTitle(title);
        card.setAction(action);
        card.setIconUrl(iconUrl);
        return card;
    }

    private HamburgerMenuCard getLogoutCard() {
        HamburgerMenuCard card = new HamburgerMenuCard();
        Dictionary meta = new Hashtable();
        meta.put("title","Logout");
        meta.put("subTitle","Do you want to Logout ?");
        var action = new Action("LOGOUT", ActionType.SHOW_LOGOUT_ALERT);
        card.setTitle("Logout");
        card.setAction(action);
        action.setMeta(meta);
        return card;
    }

    private HamburgerMenuCard getSalesAppHelp() {
        HamburgerMenuCard card = new HamburgerMenuCard();
        var action = new Action(ActionType.SHOW_ALERT_MODAL);

        Map<String, Object> helpAction = new HashMap<>();
        helpAction.put("message", "You can reach out to us at\<EMAIL>\n\nClick on Yes to write an email.");
        helpAction.put("positiveButtonText", "Yes");
        helpAction.put("positiveButtonAction", new Action(ActionType.EXTERNAL_DEEP_LINK, "mailto:<EMAIL>"));
        helpAction.put("negativeButtonText", "No");
        action.setMeta(helpAction);

        card.setTitle("Help");
        card.setAction(action);
        card.setIconUrl("/image/chroniccare/hamburger/navigation.png");
        return card;
    }

    private HamburgerProfileCard getProfileCard(UserEntry user) {
        HamburgerProfileCard card = new HamburgerProfileCard();

        Action action = new Action("curefit://accountsettings", ActionType.NAVIGATION);
        card.setAction(action);
        card.setTitle(user.getFirstName());
        card.setProfilePictureUrl(user.getProfilePictureUrl());
        return card;
    }

    private HamburgerMenuCard getDietPlanCard(UserContext userContext) {
        boolean isDietPlanGenerated = false;
        String dietPlanPdfUrl = null;
        if (ChronicCareAppUtil.dynamicDietPlanDownloadSupportedVersion(userContext)) {
            isDietPlanGenerated = serviceInterfaces.getChronicCareServiceHelper().isDietPlanPdfGeneratedForForDigitalAppUser(serviceInterfaces, userContext);
        } else {
            dietPlanPdfUrl =  serviceInterfaces.getChronicCareServiceHelper().getPlanPdfIfDietPlanAvailableForDigitalAppUser(serviceInterfaces, userContext);
        }

        if (dietPlanPdfUrl != null || isDietPlanGenerated) {
            HamburgerMenuCard card = new HamburgerMenuCard();
            card.setTitle("Diet Plan");
            card.setIconUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/restaurant-2025-03-14-12:33.png");
            Action action = new Action();
            if (ChronicCareAppUtil.dynamicDietPlanDownloadSupportedVersion(userContext)) {
                action.setActionType(ActionType.OPEN_DIET_PLAN_PDF);
            } else {
                Map<String, Object> actionMeta = new HashMap<>();
                action.setActionType(ActionType.OPEN_ONLINE_PDF);
                actionMeta.put("fileUrl", dietPlanPdfUrl);
                actionMeta.put("fileTitle", "Diet Plan");
                actionMeta.put("documentType", "DIET_PLAN");
                action.setMeta(actionMeta);
            }
            card.setAction(action);
            return card;
        }
        return null;
    }

    private HamburgerMenuCard getMedicalRecordsCard() {
        HamburgerMenuCard card = new HamburgerMenuCard();

        List<HamburgerMenuCard> sections = new ArrayList<>();
        sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfconsultationspage", "Consultations", "/image/chroniccare/hamburger/navigation.png"));
        sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfreportspage", "Reports", "/image/chroniccare/hamburger/navigation.png"));
        sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfwellnesscenterbookingstabpage", "At Center Sessions", "/image/chroniccare/hamburger/navigation.png"));
        card.setIsExpandable(true);
        card.setTitle("Medical Records");
        card.setIconUrl("/image/chroniccare/hamburger/medical_records.png");
        card.setData(sections);

        return card;
    }

    private HamburgerMenuCard getSubscriptionCard() {
        HamburgerMenuCard card = new HamburgerMenuCard();

        List<HamburgerMenuCard> sections = new ArrayList<>();
        sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://subscription", "Details", "/image/chroniccare/hamburger/navigation.png"));
        sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sforderspage", "Purchases", "/image/chroniccare/hamburger/navigation.png"));
        card.setIsExpandable(true);
        card.setTitle("Subscription");
        card.setIconUrl("/image/chroniccare/hamburger/subscription.png");
        card.setData(sections);

        return card;
    }

    private HamburgerMenuCard getAccountDetailsV2(UserContext userContext) {
        HamburgerMenuCard card = new HamburgerMenuCard();

        List<HamburgerMenuCard> sections = new ArrayList<>();
        sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://accountsettings", "Personal Details", "/image/chroniccare/hamburger/navigation.png"));
        sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://contactdetails", "Contact Details", "/image/chroniccare/hamburger/navigation.png"));
        if (ChronicCareAppUtil.isJuspayPaymentPageSupportedAppVersion(userContext)) {
            sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://juspaymanagement", "Payment Settings", "/image/chroniccare/hamburger/navigation.png"));
        }
        card.setIsExpandable(true);
        card.setTitle("Account");
        card.setIconUrl("/image/chroniccare/hamburger/account.png");
        card.setData(sections);

        return card;
    }

    private HamburgerMenuCard getHelp(UserContext userContext) {
        HamburgerMenuCard card = new HamburgerMenuCard();
        List<HamburgerMenuCard> sections = new ArrayList<>();

        if (isCSFaqsSupportedAppVersion(userContext)) {
            sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfgenericpage?pageId=sfsupportfaqs&pageTitle=FAQs","FAQs", "/image/chroniccare/hamburger/navigation.png"));
        }
        if (isCsTicketSupportedVersion(userContext)) {
            sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://sfsupportpage","My Queries", "/image/chroniccare/hamburger/navigation.png"));
        } else {
            sections.add(getHamburgerCard(ActionType.NAVIGATION, "curefit://helpandsupport", "Support", "/image/chroniccare/hamburger/navigation.png"));
        }
        sections.add(getHamburgerCard(ActionType.OPEN_WEBPAGE, "https://static.cure.fit/cgm-info-disclaimer-v2.html", "Medical Disclaimer", "/image/chroniccare/hamburger/navigation.png"));
        sections.add(getHamburgerCard(ActionType.OPEN_WEBPAGE, "https://static.cure.fit/sugarfit-privacy.html","Privacy Policy", "/image/chroniccare/hamburger/navigation.png"));
        sections.add(getHamburgerCard(ActionType.OPEN_WEBPAGE, "https://static.cure.fit/sugarfit-tnc.html","Terms and Conditions", "/image/chroniccare/hamburger/navigation.png"));

        card.setIsExpandable(true);
        card.setTitle("Help");
        card.setIconUrl("/image/chroniccare/hamburger/help_and_support.png");
        card.setData(sections);
        return card;
    }

    private HamburgerMenuCard getDevOption() {
        HamburgerMenuCard card = new HamburgerMenuCard();
        Action action = new Action();
        action.setActionType(ActionType.NAVIGATION);
        action.setUrl("curefit://devoptions");
        List<DevOptionItem> devOptions= new ArrayList<>(
                Arrays.asList(
                        new DevOptionItem("Stage server",  "SERVER_SWITCH"),
                        new DevOptionItem("Analytics Inspector",  "APP_INSPECTOR_SWITCH")
                ));
        Dictionary meta = new Hashtable();
        meta.put("devOptions",devOptions);
        action.setMeta(meta);
        String title="Dev Options";
        card.setTitle(title);
        card.setAction(action);
        card.setIconUrl("/image/chroniccare/hamburger/dev_options.png");
        return card;
    }
}

