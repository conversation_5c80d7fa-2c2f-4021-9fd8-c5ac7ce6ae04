package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.poll.SfPollCardWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.sugarfit.poll.client.PollSupportClient;
import com.sugarfit.poll.enums.PollStatus;
import com.sugarfit.poll.enums.Status;
import com.sugarfit.poll.pojo.PollEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class SfPollSurveyBuilder {
    final ExceptionReportingService exceptionReportingService;
    final PollSupportClient pollSupportClient;
    final ServiceInterfaces serviceInterfaces;

    public List<BaseWidgetNonVM> buildView(UserContext userContext) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            Set<String> relevantSegments = serviceInterfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.SF_POLL_CLIENT);
            List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(relevantSegments));
            List<PollEntry> activeSurveys = pollSupportClient.fetchActiveSurvey(userId, 0, 20, "createdOn", "DESC", userSegments).getElements();
            List<PollEntry> surveys = new ArrayList<>();
            if (activeSurveys != null) surveys.addAll(activeSurveys);
            if (!CollectionUtils.isEmpty(surveys)) {
                List<BaseWidgetNonVM> pollWidgets = surveys.stream().map(p -> {
                    try {
                        return buildWidget(userContext, p);
                    } catch (BaseException e) {
                        exceptionReportingService.reportException(e);
                        return null;
                    }
                }).toList();
                if (!CollectionUtils.isEmpty(pollWidgets)) {
                    return pollWidgets;
                }
            }
        } catch (Exception exception) {
            exceptionReportingService.reportException(exception);
        }

        return null;
    }

    public BaseWidgetNonVM buildWidget(UserContext userContext, PollEntry pollEntry) throws BaseException {
        if(pollEntry.getStatus() != null && pollEntry.getStatus().equals(Status.PUBLISHED)) {
            boolean isCompleted = pollEntry.getPollStatus() != null && pollEntry.getPollStatus().equals(PollStatus.COMPLETED);
            if(isCompleted) {
                return null;
            }
            String actionTitle = "Take Survey";
            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            Date currentTime = calendar.getTime();
            String pageUrl = "curefit://sfpolldetailspage?pollId=" + pollEntry.getId();
            List<String> userImages = new ArrayList<>();
            boolean haveStartTime = pollEntry.getStartTime() != null;
            boolean haveEndTime = pollEntry.getExpiryTime() != null;
            PollEntry detailedPollEntry = pollSupportClient.fetchPoll(Long.valueOf(userContext.getUserProfile().getUserId()), pollEntry.getId());

            if (detailedPollEntry.getPollCompletedUserIds() != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(detailedPollEntry.getPollCompletedUserIds())) {
                for (int i = 0; i < detailedPollEntry.getPollCompletedUserIds().size(); i++) {
                    String votedUserId = String.valueOf(detailedPollEntry.getPollCompletedUserIds().get(i));
                    try {
                        UserEntry userEntry = this.serviceInterfaces.userServiceClient.getUser(votedUserId).get();
                        String userProfilePic = userEntry.getProfilePictureUrl();
                        if (userProfilePic != null && !userProfilePic.isEmpty()) {
                            // Only 2 pictures to display on UI
                            if (userImages.size() < 3) {
                                userImages.add(userProfilePic);
                                if (userImages.size() == 2) break;
                            }
                        }
                    } catch (Exception e) {
                        // Ignore
                    }
                }
            }

            if (!haveStartTime || !haveEndTime ||
                    currentTime.after(pollEntry.getStartTime()) && currentTime.before(pollEntry.getExpiryTime())) {
                SfPollCardWidget pollCardWidget = new SfPollCardWidget();
                pollCardWidget.setPollId(pollEntry.getId());
                pollCardWidget.setPollType(pollEntry.getPollType());
                pollCardWidget.setTitle(pollEntry.getName());
                pollCardWidget.setVotedUserImages(userImages);
                pollCardWidget.setTime(pollEntry.getStartTime());
                pollCardWidget.setVotesCount(pollEntry.getVoteCount());
                pollCardWidget.setCardAction(Action.builder().actionType(ActionType.NAVIGATION).url(pageUrl).title(actionTitle).build());
                return pollCardWidget;
            }
        }
        return null;
    }
}