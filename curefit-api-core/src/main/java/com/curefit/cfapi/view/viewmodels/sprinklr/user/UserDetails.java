package com.curefit.cfapi.view.viewmodels.sprinklr.user;

import com.curefit.cfapi.view.viewmodels.heimdall.GetHeimdallMongoAuditLogsResponse;
import com.curefit.cfapi.view.viewmodels.sprinklr.activePacks.MembershipDetail;
import com.curefit.cfapi.view.viewmodels.sprinklr.sessions.Session;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;


@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserDetails {
    String userId;

    List<Session> gxSessions;
    Integer gxNoShowCount; // number of no-shows in the current calendar month
    GetHeimdallMongoAuditLogsResponse revertedGXNoShows;

    List<Session> playSessions;
    Integer playNoShowCount;
    GetHeimdallMongoAuditLogsResponse revertedPlayNoShows;

    List<MembershipDetail> memberships;
}
