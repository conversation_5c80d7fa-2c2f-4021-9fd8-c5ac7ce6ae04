package com.curefit.cfapi.view.viewbuilders.gymfit;

import com.curefit.center.dtos.AmenityEntry;
import com.curefit.center.dtos.CenterEntry;
import com.curefit.center.dtos.CenterScheduleEntry;
import com.curefit.center.dtos.ContactEntry;
import com.curefit.center.enums.CenterContactType;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionIcon;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.atom.Alignment;
import com.curefit.cfapi.pojo.vm.atom.BoxFit;
import com.curefit.cfapi.pojo.vm.atom.CFContainerData;
import com.curefit.cfapi.pojo.vm.atom.CFGenericAlertDialog;
import com.curefit.cfapi.pojo.vm.atom.CFIconData;
import com.curefit.cfapi.pojo.vm.atom.CFImageData;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.Debugger;
import com.curefit.cfapi.util.ErrorUtil;
import com.curefit.cfapi.util.GymUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.view.viewmodels.gymfit.CenterScheduleWalkinPageView;
import com.curefit.cfapi.widgets.fitness.CenterTimingWidget;
import com.curefit.cfapi.widgets.fitness.StringListWidget;
import com.curefit.cfapi.widgets.fitso.bookingconfirmation.DropDownInfo;
import com.curefit.cfapi.widgets.fitso.bookingconfirmation.OrderConfirmationFooter;
import com.curefit.cfapi.widgets.fitso.bookingconfirmation.OrderConfirmationMeta;
import com.curefit.cfapi.widgets.fitso.bookingconfirmation.OrderConfirmationWidget;
import com.curefit.cfapi.widgets.fitso.bookingconfirmation.TextInfoItem;
import com.curefit.cfapi.widgets.fitso.bookingdetail.BookingActionListWidget;
import com.curefit.cfapi.widgets.fitso.bookingdetail.ExpandableButtonAction;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.gymfit.dtos.CancelCenterVisitRequest;
import com.curefit.gymfit.dtos.CenterVisitRequest;
import com.curefit.gymfit.models.Pitch;
import com.curefit.gymfit.models.PitchStatus;
import com.curefit.gymfit.utils.Enums;
import com.curefit.userservice.pojo.entry.UserEntry;
import org.apache.commons.collections.CollectionUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.curefit.cfapi.constants.Constants.HOMETAB;
import static com.curefit.cfapi.pojo.app.action.ActionType.NAVIGATION;

public class CenterWalkin {
    Debugger debugger;
    CenterEntry center;
    Collection<AmenityEntry> amenities;
    CenterScheduleWalkinPageView pageView;
    Collection<CenterScheduleEntry> scheduleSlots = null;
    public CenterScheduleWalkinPageView buildCenterScheduleWalkinPageView(
        ServiceInterfaces interfaces,
        Map<String, String> queryParams,
        UserContext userContext
    ) throws Exception {

        String centerServiceId = queryParams.getOrDefault("centerServiceId", null);
        debugger = Debugger.getDebuggerFromUserContext(userContext);

        if (Objects.isNull(centerServiceId)) {
            String message = "QueryParams missing";
            throw new BaseException(message, LogType.INFO, ErrorUtil.CFAPI_NAMESPACE, AppStatus.BAD_REQUEST, "BAD_REQUEST", message);
        }

        center = interfaces.centerService.getCachedCentersById(List.of(centerServiceId), false, null, null).getFirst();
        if (GymUtil.isGXonlyCenter(center)) {
            String message = "Can not Schedule Walkin for this center";
            throw new BaseException(message, LogType.ERROR, ErrorUtil.CFAPI_NAMESPACE, AppStatus.INTERNAL_SERVER_ERROR, "INTERNAL_SERVER_ERROR", message);
        }

        amenities = center.getAmenities();
        debugger.msg(amenities);
        pageView = new CenterScheduleWalkinPageView();

        getScheduleWalkinPageHeaderWidget();
        getScheduleWalkinPageDescriptionWidget();
        getScheduleWalkinPageAlertDescription();
        getScheduleWalkinPageActionList();

        pageView.setTitle(CFTextData.builder().text("Center Tour").typeScale(UiUtils.TextTypeScales.P3).build());

        return pageView;
    }

    public Action ScheduleWalkinCompleteActionBuilder(
        ServiceInterfaces interfaces,
        UserContext userContext,
        Map<String, String> queryParams
    ) throws Exception {

        String centerServiceId = queryParams.getOrDefault("centerServiceId", null);
        debugger = Debugger.getDebuggerFromUserContext(userContext);

        if (Objects.isNull(centerServiceId)) {
            String message = "QueryParams missing";
            throw new BaseException(message, LogType.INFO, ErrorUtil.CFAPI_NAMESPACE, AppStatus.BAD_REQUEST, "BAD_REQUEST", message);
        }

        center = interfaces.centerService.getCachedCentersById(List.of(centerServiceId), false, null, null).getFirst();
        CenterEntry linkedCenter = center;
        if (Objects.nonNull(center.getLinkedCenterId())) {
            linkedCenter = interfaces.centerService.getCachedCentersById(List.of(String.valueOf(center.getLinkedCenterId())), false, null, null).getFirst();
        }
        debugger.msg("center", center);
        debugger.msg("linkedCenter", linkedCenter);

        UserEntry user = userContext.getUserEntryCompletableFuture().get();

        CenterVisitRequest request = new CenterVisitRequest();

        if (Objects.nonNull(GymUtil.getGymCenterId(center))) {
            request.setCenterServiceId(String.valueOf(center.getId()));
            request.setCenterId(GymUtil.getGymCenterId(center));
        } else if (Objects.nonNull(GymUtil.getGymCenterId(linkedCenter))) {
            request.setCenterServiceId(String.valueOf(linkedCenter.getId()));
            request.setCenterId(GymUtil.getGymCenterId(linkedCenter));
        } else {
            String message = "Can not Schedule Walkin for this center";
            throw new BaseException(message, LogType.ERROR, ErrorUtil.CFAPI_NAMESPACE, AppStatus.INTERNAL_SERVER_ERROR, "INTERNAL_SERVER_ERROR", message);
        }

        request.setUserId(userContext.getUserProfile().getUserId());
        String firstName = Optional.ofNullable(user.getFirstName()).orElse(" ");
        String lastName = Optional.ofNullable(user.getLastName()).orElse("");
        request.setUserName(STR."\{firstName} \{lastName}");

        if (GymUtil.isProCenter(center)) {
            request.setCenterCategory(Enums.CenterCategory.SILVER);
        } else {
            request.setCenterCategory(Enums.CenterCategory.GOLD);
        }

        request.setPhoneNumber(user.getPhone());
        debugger.msg(request);

        Pitch pitch = interfaces.gymfitClient.pitchService().scheduleCenterVisit(request);
        debugger.msg(pitch);

        return getScheduleWalkinCompleteAction(interfaces, pitch, userContext);
    }

    public Action cancelCenterSchedule(ServiceInterfaces serviceInterfaces, CancelCenterVisitRequest cancelCenterVisitRequest) throws Exception {
        Pitch pitch = serviceInterfaces.gymfitClient.pitchService().cancelCenterVisit(cancelCenterVisitRequest);

        if (pitch.getStatus().equals(PitchStatus.CANCELLED)) {
            Action confirmAction = Action.builder().actionType(NAVIGATION).url(HOMETAB).title("GOT IT").build();
            CFGenericAlertDialog alertDialog = new CFGenericAlertDialog();
            alertDialog.setTitle(CFTextData.builder().text("Request Completed").typeScale(UiUtils.TextTypeScales.P1).maxLine("2").build());
            alertDialog.setMessage(CFTextData.builder().text("Schedule Cancel Success").typeScale(UiUtils.TextTypeScales.P8).maxLine("4").build());
            alertDialog.setDismissAction(confirmAction);
            alertDialog.setShowDismissIcon(true);
            alertDialog.setActions(List.of(confirmAction));
            HashMap<String, CFGenericAlertDialog> meta = new HashMap<>();
            meta.put("popupContent", alertDialog);
            return Action.builder().actionType(ActionType.SHOW_ALERT_POPUP).meta(meta).build();
        }

        throw new Exception("Something Went Wrong");
    }

    public Action getPostCenterSchedulePageAction(ServiceInterfaces interfaces, UserContext userContext, CenterEntry thisCenter) {
        center = thisCenter;
        HashMap<String, Object> meta = new HashMap<>();
        meta.put("showTransition", true);
        meta.put("pageName", "Center Tour");
        meta.put("widgets", List.of(getPostCenterSchedulePageHeader(interfaces), getCenterScheduleActions(interfaces, userContext)));
        HashMap<String, Object> analyticsData = new HashMap<>();
        analyticsData.put("pageId", "post_center_tour_schedule_page");
        analyticsData.put("centerServiceId", center.getId());
        analyticsData.put("pageName", "Center Tour");
        return Action.builder().actionType(ActionType.SHOW_CUSTOM_LIST_PAGE).meta(meta).analyticsData(analyticsData).build();
    }

    private StringListWidget getPostCenterSchedulePageHeader(ServiceInterfaces interfaces) {
        StringListWidget stringListWidget = new StringListWidget();
        stringListWidget.setCrossAlignment(Alignment.CENTER);
        stringListWidget.setCardBackground(CFImageData.builder().url("/image/icon/bg-img.png").width(375.0).height(450.0).boxFit(BoxFit.FILL).build());
        stringListWidget.setHorizontalPadding(40.0);
        stringListWidget.setPaddingInBetween(List.of(350, 10, 5, 10));
        List<CFTextData> textDataList = new ArrayList<>();
        textDataList.add(CFTextData.builder().text("Center Tour").typeScale(UiUtils.TextTypeScales.H9).build());
        textDataList.add(CFTextData.builder().text(getCenterName()).typeScale(UiUtils.TextTypeScales.Inter16F700W).maxLine("2").prefixIcon(CFIconData.builder().code(UiUtils.IconCodes.LOCATION_SMALL).size(20.0).build()).build());
        textDataList.add(CFTextData.builder().text(getCenterAddress()).typeScale(UiUtils.TextTypeScales.P8).opacity(0.6).alignment(Alignment.CENTER.toString()).maxLine("2").build());

        try {
            scheduleSlots = interfaces.scheduleService.getCenterScheduleByCenterId(center.getId(), null, null);
        } catch (Exception e) {
            debugger.err(e);
        }

        List<CenterScheduleEntry> scheduleStatusList = Objects.isNull(scheduleSlots) ? new ArrayList<>() : new ArrayList<>(scheduleSlots);

        if (CollectionUtils.isNotEmpty(scheduleStatusList)) {
            textDataList.add(CFTextData.builder().text("OPEN • " + new CenterTimingWidget().getTodaySchedule(scheduleStatusList)).typeScale(UiUtils.TextTypeScales.TAGTEXT).color(UiUtils.UIColors.STATUS_POSITIVE).build());
        }
        stringListWidget.setStringList(textDataList);
        return stringListWidget;
    }

    private String getDate(Date date, String tz) {
        Calendar createdCalender = TimeUtil.getCalendarInstance(date, tz);
        return STR."\{TimeUtil.getDayOfWeekShortName(createdCalender.get(Calendar.DAY_OF_WEEK))}, \{createdCalender.get(Calendar.DAY_OF_MONTH)} \{Objects.requireNonNull(TimeUtil.getMonthShortName(createdCalender.get(Calendar.MONTH))).toUpperCase()}";
    }

    private Action getScheduleWalkinCompleteAction(ServiceInterfaces interfaces, Pitch pitch, UserContext userContext) throws BaseException {

        OrderConfirmationWidget orderConfirmationWidget = getOrderConfirmationWidget(pitch, userContext, interfaces);
        debugger.msg(orderConfirmationWidget);
        BookingActionListWidget bookingActionListWidget = getCenterScheduleActions(interfaces, userContext);
        debugger.msg(bookingActionListWidget);

        HashMap<String, Object> meta = new HashMap<>();
        meta.put("widgets", List.of(orderConfirmationWidget, bookingActionListWidget));
        meta.put("popAction", Action.builder().actionType(ActionType.NAVIGATION).url(HOMETAB).build());
        return Action.builder().actionType(ActionType.PUSH_AND_NAVIGATION).url(Constants.ORDER_CONFIRMATION_PAGE).meta(meta).analyticsData(getCommonAnalytics()).build();
    }

    private Action getCancelCenterScheduleAction(UserContext userContext) {
        HashMap<String, String> meta = new HashMap<>();
        meta.put("apiUrl", "/v2/gymfit/walkin/cancel");
        meta.put("userId", userContext.getUserProfile().getUserId());
        meta.put("centerServiceId", String.valueOf(center.getId()));
        meta.put("centerId", GymUtil.getGymCenterId(center));
        return Action.builder().actionType(ActionType.DYNAMIC_API_CALL).meta(meta).analyticsData(getCommonAnalytics()).build();
    }

    private OrderConfirmationWidget getOrderConfirmationWidget(Pitch pitch, UserContext userContext, ServiceInterfaces interfaces) throws BaseException {

        long createdTimeEpoch = pitch.getCreatedAt().getTime();
        String tz = userContext.getUserProfile().getTimezone();
        Date twoDaysPlusCreatedTimeDate = TimeUtil.addDays(Date.from(Instant.ofEpochMilli(createdTimeEpoch)), tz, 2);
        String pitchTime = STR."\{getDate(Date.from(Instant.ofEpochMilli(createdTimeEpoch)), tz)} - \{getDate(twoDaysPlusCreatedTimeDate, tz)}";
        debugger.msg(pitchTime);

        try {
            scheduleSlots = interfaces.scheduleService.getCenterScheduleByCenterId(center.getId(), null, null);
        } catch (Exception e) {
            debugger.err(e);
        }

        List<CenterScheduleEntry> scheduleStatusList = Objects.isNull(scheduleSlots) ? new ArrayList<>() : new ArrayList<>(scheduleSlots);
        debugger.msg(scheduleStatusList);

        OrderConfirmationWidget orderConfirmationWidget = OrderConfirmationWidget.builder()
                .audioUrl(Constants.CLASSBOOKING_AUDIO)
                .footer(OrderConfirmationFooter.builder().title(new CenterTimingWidget().getTodaySchedule(scheduleStatusList)).build())
                .orderMeta(OrderConfirmationMeta.builder().title(getCenterName()).isDropDown(true)
                        .dropDownInfo(List.of(DropDownInfo.builder().icon(UiUtils.IconCodes.LOCATION_SMALL)
                                .subTitle(getCenterAddress())
                                .cardAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).title("NAVIGATE").analyticsData(getCommonAnalytics()).url(center.getMapUrl()).build())
                                .build())).build())
                .textInfoItems(List.of(
                        TextInfoItem.builder().title("Drop in anytime within 48 hours").styleProps(new HashMap<>(){{
                            put("variant", UiUtils.TextTypeScales.Paragraph4Text);
                        }}).build(),
                        TextInfoItem.builder().title(pitchTime).styleProps(new HashMap<>(){{
                            put("variant", UiUtils.TextTypeScales.Header1Text);
                        }}).build()
                ))
                .styleProps(new HashMap<>(){{
                    put("variant", "paragraph4Text");
                }})
                .build();
        if (Objects.isNull(center.getMapUrl())) {
            orderConfirmationWidget.setOrderMeta(null);
        }
        orderConfirmationWidget.setWidgetType(WidgetType.ORDER_CONFIRMATION_INFO_WIDGET);

        return  orderConfirmationWidget;

    }

    private BookingActionListWidget getCenterScheduleActions(ServiceInterfaces interfaces, UserContext userContext) {

        BookingActionListWidget bookingActionListWidget = new BookingActionListWidget();
        bookingActionListWidget.setHideDivider(true);

        List<ExpandableButtonAction> bookingList = new ArrayList<>();
        if (Objects.nonNull(center.getMapUrl())) {
            bookingList.add(ExpandableButtonAction.builder().title("Navigate").trailingIcon(ActionIcon.CHEVRON_RIGHT).leadingIcon(ActionIcon.NAVIGATE_RIGHT)
                    .titleType("NORMAL").action(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK)
                            .analyticsData(getCommonAnalytics()).url(center.getMapUrl()).build()).build());
        }

        try {
            List<ContactEntry> contactDetails = interfaces.centerService.findContactsByCenterAndType(center.getId(), (GymUtil.isLuxGym(center)) ? CenterContactType.CENTER_EMPLOYEE_CONTACT : CenterContactType.CENTER_VIRTUAL_CONTACT, null, null);
            if (Objects.nonNull(contactDetails) && CollectionUtils.isNotEmpty(contactDetails)) {
                String number = contactDetails.getFirst().getPhoneNumber();
                bookingList.add(ExpandableButtonAction.builder().title("Call Center Manager").trailingIcon(ActionIcon.CHEVRON_RIGHT).leadingIcon(ActionIcon.CALL_NEW).titleType("NORMAL").action(new Action(number, "", ActionType.MAKE_CALL)).build());
            }
        } catch (BaseException e) { debugger.err(e); }
        bookingActionListWidget.setBookingActions(bookingList);

        bookingList.add(ExpandableButtonAction.builder().title("Cancel Center Tour").trailingIcon(ActionIcon.CHEVRON_RIGHT).leadingIcon(ActionIcon.NO_SHOW).titleType("NORMAL").action(getCancelCenterScheduleAction(userContext)).build());
        bookingActionListWidget.setBookingActions(bookingList);

        return bookingActionListWidget;
    }

    private void getScheduleWalkinPageHeaderWidget() {
        StringListWidget stringListWidget = new StringListWidget();

        stringListWidget.setCardBackground(CFImageData.builder().url("/image/icon/schedulebanner2.png").width(350.0).height(350.0).boxFit(BoxFit.FILL).build());
        List<CFTextData> stringList = new ArrayList<>();
        stringList.add(
            CFTextData.builder().text(getCenterName()).typeScale(UiUtils.TextTypeScales.H4).prefixIcon(
                CFIconData.builder().code(UiUtils.IconCodes.LOCATION).size(15.0).build()
            ).containerData(
                CFContainerData.builder().color(UiUtils.UIColors.COLOR_WHITE).opacity(0.16).borderRadius(List.of(5.0, 5.0, 5.0, 5.0)).paddingLTRB(List.of(20.0, 10.0, 20.0, 10.0)).build()
            ).build()
        );
        stringList.add(CFTextData.builder().text("WHAT TO EXPECT").typeScale(UiUtils.TextTypeScales.H6).build());
        stringList.add(CFTextData.builder().text("Simply show up. We’ll handle the rest.").typeScale(UiUtils.TextTypeScales.P5).build());

        stringListWidget.setStringList(stringList);
        stringListWidget.setCrossAlignment(Alignment.CENTER);
        stringListWidget.setPaddingInBetween(List.of(250, 50, 5, 10));
        debugger.msg(stringListWidget);

        pageView.addWidget(stringListWidget);
    }

    private void getScheduleWalkinPageDescriptionWidget() {
        StringListWidget stringListWidget = new StringListWidget();
        List<CFTextData> stringList = new ArrayList<>();
        stringList.add(descriptionTextData(
            "Meet your center manager and discuss your goals",
            "/image/icon/User.png"
        ));
        stringList.add(descriptionTextData(
            "Tour the Gym + Meet trainers",
            "/image/icon/Barbell.png"
        ));
        stringList.add(descriptionTextData(
            "Get your tailored membership plan",
            "/image/icon/Certificate.png"
        ));
        stringListWidget.setStringList(stringList);
        stringListWidget.setHorizontalPadding(20.0);
        stringListWidget.setPaddingInBetween(List.of(0, 30, 30, 0));
        stringListWidget.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("30", "0"));
        debugger.msg(stringListWidget);

        pageView.addWidget(stringListWidget);
    }

    private CFTextData descriptionTextData(String text, String prefixImgUrl) {
        CFImageData prefixImg = CFImageData.builder().url(prefixImgUrl).height(20.0).width(20.0)
                .containerData(CFContainerData.builder().marginLTRB(List.of(0.0, 0.0, 20.0, 0.0)).build()).build();
        return CFTextData.builder().text(text).typeScale(UiUtils.TextTypeScales.P2).maxLine("3").graphicsCrossAlignment(Alignment.CENTER).prefixImage(prefixImg).build();
    }

    private void getScheduleWalkinPageAlertDescription() {
        String description = null;

        if (CollectionUtils.isNotEmpty(amenities) && amenities.stream().anyMatch(item -> item.getName().toLowerCase().contains("parking"))) {
            description = "Free parking is available at the selected location";
        }

        pageView.setAlertDescription(description);
    }

    private String getCenterName() {
        String centerName = center.getName();
        if (centerName.length() > 25) centerName = STR."\{centerName.substring(0, 25)}...";
        return centerName;
    }

    private String getCenterAddress() {
        return (center.getFullAddress1() != null ? center.getFullAddress1() : "") +
                (center.getFullAddress2() != null ? center.getFullAddress2() : "");
    }

    private HashMap<String, Object> getCommonAnalytics() {
        HashMap<String, Object> analyticsData = new HashMap<>();
        analyticsData.put("flow","Confirm_schedule_visit");
        if (Objects.nonNull(center)) {
            analyticsData.put("centerServiceId", center.getId());
            analyticsData.put("centerId", GymUtil.getGymCenterId(center));
        }
        return analyticsData;
    }

    private void getScheduleWalkinPageActionList() {

        List<Action> actionList = new ArrayList<>();
        HashMap<String, Boolean> meta = new HashMap<String, Boolean>(){{
            put("schedule", true);
        }};
        Action action = Action.builder().actionType(ActionType.EMPTY_ACTION).title("CONFIRM AND BOOK").meta(meta).build();
        actionList.add(action);
        debugger.msg(actionList);

        pageView.setActions(actionList);

    }
}
