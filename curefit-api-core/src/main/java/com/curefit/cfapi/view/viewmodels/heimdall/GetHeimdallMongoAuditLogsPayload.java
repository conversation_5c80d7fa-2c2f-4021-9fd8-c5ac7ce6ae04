package com.curefit.cfapi.view.viewmodels.heimdall;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Map;

@ToString
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GetHeimdallMongoAuditLogsPayload {
    Condition condition;
    Boolean totalCountRequired;
    int start;
    int count;

    // Custom constructor to build payload with the specified parameters
    public GetHeimdallMongoAuditLogsPayload(Long userId, String action, String entityName, String afterDate) {
        this.condition = Condition.builder()
                .userId(userId)
                .action(action)
                .entityName(entityName)
                .createdDate(Map.of("$gte", afterDate))
                .build();
        this.totalCountRequired = true;
        this.count = 100;
        this.start = 0;
    }

    @ToString
    @Getter
    @Setter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Condition {
        Long userId;
        String action;
        String entityName;
        Map<String, String> createdDate;
    }
}