package com.curefit.cfapi.view.viewbuilders.chroniccare.digitalapp;

import com.curefit.albus.booking.ActiveConsultationResponse;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.chronic.UserOnboardingActionWithContext;
import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.dto.ChronicCareTeam;
import com.curefit.cfapi.dto.sugarfit.CGMActivationStatus;
import com.curefit.cfapi.dto.sugarfit.CGMGraphWidgetAndConfig;
import com.curefit.cfapi.dto.sugarfit.SfCgmDeviceStatus;
import com.curefit.cfapi.dto.sugarfit.digitalapp.DigiTasksToDoCard;
import com.curefit.cfapi.model.internal.chroniccare.FitnessDeviceSyncMeta;
import com.curefit.cfapi.model.internal.meta.chroniccare.SfOpsRequestMeta;
import com.curefit.cfapi.model.internal.meta.chroniccare.SfSalesBannerMeta;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.SfHomeHeaderWidgetBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.SfHomePageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.SfHomePageViewBuilderV2;
import com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp.SfDigiHomePageView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.*;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.*;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.diyfs.pojo.LiveClass;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.shifu.pojo.goal.summary.GoalWithData;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sugarfit.catalog.client.CatalogClient;
import com.sugarfit.catalog.pojo.BookComposition;
import com.sugarfit.catalog.pojo.Entry.BookEntry;
import com.sugarfit.challenges.client.ChallengesClient;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.pojo.CgmOnboardingStatusResponse;
import com.sugarfit.chs.pojo.CgmUserRequestStatus;
import com.sugarfit.chs.pojo.UserMetricEntry;
import com.sugarfit.chs.pojo.cgmstat.CgmStat;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalScansForDayResponse;
import com.sugarfit.fitness.client.SFFitnessClient;
import com.sugarfit.fitness.enums.SessionType;
import com.sugarfit.fitness.enums.SortOrder;
import com.sugarfit.fitness.enums.UserEventType;
import com.sugarfit.fitness.pojo.LiveSessionConfigEntry;
import com.sugarfit.fitness.pojo.SessionLogResponse;
import com.sugarfit.fitness.pojo.UserSessionLogEntry;
import com.sugarfit.fitness.pojo.UserSessionLogFetchRequest;
import com.sugarfit.fitness.pojo.SessionLogResponse;
import com.sugarfit.indus.IndusClient;
import com.sugarfit.indus.entry.ConfigEntry;
import com.sugarfit.indus.enums.ConfigType;
import com.sugarfit.logging.client.LoggingClient;
import com.sugarfit.logging.enums.NutritionType;
import com.sugarfit.logging.pojo.response.NutritionConsumption;
import com.sugarfit.logging.pojo.response.NutritionConsumptionResponse;
import com.sugarfit.poll.client.PollSupportClient;
import com.sugarfit.sms.client.SMSClient;
import com.sugarfit.sms.entry.UserTaskDaySummary;
import com.sugarfit.sms.response.NUXStatusResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.curefit.cfapi.pojo.app.action.ActionType.NAVIGATION;
import static com.curefit.cfapi.pojo.chroniccare.SfOpsRequestType.REQUEST_DIAGNOSTICS;
import static com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper.*;
import static com.curefit.cfapi.util.AppUtil.getAppTenantFromUserContext;
import static com.curefit.cfapi.util.ChronicCareAppUtil.*;
import static com.curefit.cfapi.util.DigitalAppUtil.*;
import static com.curefit.cfapi.util.SfHomePageUtil.*;
import static java.util.concurrent.CompletableFuture.supplyAsync;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
@Slf4j
@RequiredArgsConstructor
public class DigiHomePageBuilder {
    final CHSClient chsClient;
    final SMSClient smsClient;
    final PollSupportClient pollSupportClient;
    final ChallengesClient challengesClient;
    final IndusClient indusClient;
    final UserAttributesClient userAttributesClient;
    final AppConfigCache appConfigCache;
    final ServiceInterfaces serviceInterfaces;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final UserOnboardingService userOnboardingService;
    final ChronicCarePatientService chronicCarePatientService;
    final ExceptionReportingService exceptionReportingService;
    final SfHomePageViewBuilder sfHomePageViewBuilderV1;
    final SfHomePageViewBuilderV2 sfHomePageViewBuilderV2;
    final SfHomeHeaderWidgetBuilder sfHomeHeaderWidgetBuilder;
    final LoggingClient loggingClient;
    final CatalogClient catalogClient;
    final SFFitnessClient sfFitnessClient;

    @Qualifier("cfApiRedisKeyValueStore")
    final KeyValueStore cfApiRedisKeyValueStore;
    final DigiTasksToDoCardsBuilder digiTasksToDoCardsBuilder;
    final DigiPrePurchasePageBuilder digiPrePurchasePageBuilder;


    public SfDigiHomePageView buildView(UserContext userContext, String cgmDeviceId, String sessionId, Integer cgmConfigVersion) throws Exception {
        SfDigiHomePageView result = new SfDigiHomePageView();
        String userIdString = userContext.getUserProfile().getUserId();
        Long userId = Long.valueOf(userIdString);
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timezone = userContext.getUserProfile().getTimezone() != null ? TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()) : TimeZone.getTimeZone("Asia/Kolkata");
        Long currentTimeEpoch = Calendar.getInstance(getUserTimezone(userContext)).getTimeInMillis();

        NUXStatusResponse nuxStatus = smsClient.getNUXStatus(userId, false, timezone);
        if (nuxStatus != null && !nuxStatus.getFreemium() && hasNonDigitalActivePacksAvailable(nuxStatus)) {
            AutoNavigationToNuxWidget widget = new AutoNavigationToNuxWidget();
            widget.setPageName("chroniccarehomepage");
            result.addWidget(widget);
            return result;
        }

        if (nuxStatus != null) {
            if (!nuxStatus.getNuxCompleted()) {
                AutoNavigationToNuxWidget autoNavigationToNuxWidget = new AutoNavigationToNuxWidget();
                autoNavigationToNuxWidget.setPageName("sfdigitalappnux");
                result.addWidget(autoNavigationToNuxWidget);
                return result;
            } else if (nuxStatus.getFreemium()) {
                AutoNavigationToNuxWidget autoNavigationToNuxWidget = new AutoNavigationToNuxWidget();
                if(ChronicCareAppUtil.isInternationalSugarfitUser(userContext)){
                    autoNavigationToNuxWidget.setPageName("digiprepurchasepage");
                } else {
                    autoNavigationToNuxWidget.setPageName("sfexperiencepage");
                }
                result.addWidget(autoNavigationToNuxWidget);
                return result;
            }
        }

        if (Objects.nonNull(nuxStatus) && Objects.nonNull(nuxStatus.getActivePack())) {
            addActivePackWidgets(userContext, userId, appTenant, timezone, currentTimeEpoch, cgmDeviceId, nuxStatus, cgmConfigVersion, result);
        } else {
            addExpiredPackWidgets(userContext, userId, appTenant, currentTimeEpoch, result);
        }
        return result;
    }

    private void addActivePackWidgets(UserContext userContext,Long userId,AppTenant appTenant,TimeZone timezone,
                                      Long currentTimeEpoch,String cgmDeviceId,NUXStatusResponse nuxStatus,
                                      Integer cgmConfigVersion,SfDigiHomePageView result) throws ExecutionException, InterruptedException, TimeoutException, BaseException {
        CompletableFuture<CgmOnboardingStatusResponse> cgmOnboardingStatusFuture = chronicCareServiceHelper.getCgmOnboardingStatusFuture(userId, appTenant);
        CgmOnboardingStatusResponse cgmOnboardingStatus = cgmOnboardingStatusFuture.get();

        CompletableFuture<ActivePackResponse> activePackResponseFuture = userOnboardingService.getSugarFitActivePackResponseFuture(String.valueOf(userId));
        ActivePackResponse activePackResponse = activePackResponseFuture.get();
        SfCgmDeviceStatus cgmDeviceStatus = null;
        CompletableFuture<CgmStat> cgmStatFuture = null;
        CompletableFuture<CGMGraphWidgetV2> cgmGraphWidgetFuture = null;
        if (Objects.nonNull(activePackResponse) && Objects.nonNull(cgmOnboardingStatus) && !ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
            cgmDeviceStatus = SfHomePageViewBuilderV2.getCgmDeviceStatus(userContext, cgmOnboardingStatus, cgmDeviceId, activePackResponse.getBundleProduct(), chronicCareServiceHelper,serviceInterfaces.getSfAlbusClient());
            cgmStatFuture = chronicCareServiceHelper.getCgmStatFuture(userId, cgmDeviceStatus.getCgmDeviceId(), true, appTenant, timezone);
            cgmGraphWidgetFuture = sfHomePageViewBuilderV2.getCGMGraphWidgetFuture(userContext, cgmDeviceStatus, cgmStatFuture, cgmOnboardingStatus, false, activePackResponse.getBundleProduct());
        }
        boolean isTrialPack = DigitalAppUtil.isTrialPackUser(activePackResponse.getBundleProduct());
        boolean isTrialPackExpired = DigitalAppUtil.isTrialPackExpired(userContext, activePackResponse);

        CompletableFuture<UserTaskDaySummary> userTaskDaySummaryCompletableFuture = chronicCareServiceHelper.getDigiTasksToDoSummary(userContext, currentTimeEpoch);
        CompletableFuture<Map<String, UserMetricEntry>> digiRiskScoreFuture = chronicCareServiceHelper.getDigiRiskScoreFuture(userContext);
        CompletableFuture<SessionLogResponse> digiLiveSessionsFuture = chronicCareServiceHelper.getDigiLiveSessionsFuture(userContext);
        CompletableFuture<FaceBasedVitalScansForDayResponse> faceBasedVitalScansForDayResponseFuture = chronicCareServiceHelper.getFbvScanForDayDataFuture(userId, timezone);

        CompletableFuture<DigiUpgradeTrialWidget> digiUpgradeTrialWidgetFuture = getDigiUpgradeTrialWidgetFuture(userContext, activePackResponse);
        CompletableFuture<DigiJourneyProgressWidget> digiJourneyProgressWidgetFuture = getDigiJourneyProgressWidgetFuture(userContext, digiRiskScoreFuture, digiLiveSessionsFuture, userTaskDaySummaryCompletableFuture, activePackResponse);
        CompletableFuture<DigiSessionCarouselWidget> digiSessionsCarouselWidgetFuture = getDigiSessionCarouselWidgetFuture(userContext, isTrialPackExpired, activePackResponse);
        CompletableFuture<DigiLessonsCarouselWidget> digiLessonsCarouselWidgetFuture = chronicCareServiceHelper.getDigiLessonsCarouselWidgetFuture(userContext, isTrialPackExpired);
        CompletableFuture<UserOnboardingActionWithContext> onboardingActionsFuture = userOnboardingService.getUserOnboardingActionsFuture(userContext, nuxStatus.getActivePack().getSubscriptionId());
        CompletableFuture<PatientDetail> patientDetailFuture = chronicCarePatientService.getChronicCarePatientForUserIdFuture(userContext);
        CompletableFuture<ActivePackResponse> expiredPackResponseFuture = userOnboardingService.getSugarFitExpiredPackFuture(String.valueOf(userId));
        CompletableFuture<ChronicCareTeam> assignedCareTeamFuture = chronicCareServiceHelper.getAssignedCareTeamFuture(userContext, null, Objects.nonNull(expiredPackResponseFuture.get()) ? expiredPackResponseFuture.get().getBundleProduct() : null);
        CompletableFuture<List<LiveClass>> liveClassesFuture = chronicCareServiceHelper.getLiveClassesCompletableFuture(userContext);
        CompletableFuture<CgmUserRequestStatus> cgmUserRequestStatusFuture = chronicCareServiceHelper.getCgmUserRequestStatusFuture(userId, appTenant);
        CompletableFuture<DigiTasksToDoWidget> digiTasksToDoWidgetCompletableFuture = getDigiTasksToDoWidgetFuture(userContext, userTaskDaySummaryCompletableFuture, isTrialPackExpired);
        CompletableFuture<DigiAskRagusFreeQuestionWidget> digiAskRagusFreeQuestionWidgetFuture = getDigiAskRagusFreeQuestionWidgetFuture(userContext);
        CompletableFuture<DigitalAppHomeProgressWidget> digitalAppHomeProgressWidgetFuture = getDigitalAppHomeProgressWidgetFuture(userContext);
        CompletableFuture<HomeDiabetesCareHubWidget> homeDiabetesCareHubWidgetFuture = getHomeDiabetesCareHubWidgetFuture(userContext, activePackResponse);
        CompletableFuture<HomeConnectYourDeviceWidget> homeConnectYourDeviceWidgetFuture = getHomeConnectYourDeviceWidgetFuture(userContext, cgmOnboardingStatus);
        CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture = chronicCareServiceHelper.getConsultationBookingsFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> activeCardsWidgetFuture = sfHomePageViewBuilderV2.getActiveCardsWidgetFuture(userContext, patientDetailFuture.get(), activePackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, null, liveClassesFuture, faceBasedVitalScansForDayResponseFuture.get());
        CompletableFuture<BaseWidgetNonVM> smartScaleIntegrationBannerWidgetFuture = chronicCareServiceHelper.getSmartScaleIntegrationBannerWidgetFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> smartScaleStatsWidgetCompletableFuture = chronicCareServiceHelper.getSmartScaleStatsWidgetCompletableFuture(userContext);
        CompletableFuture<DAProgramJourneyWidget> digiProgramJourneyWidgetFuture = chronicCareServiceHelper.getDAProgramJourneyWidgetFuture(userContext);
        CompletableFuture<OnboardingJourneyWidget> onboardingJourneyWidgetFuture = null;
        if (ChronicCareAppUtil.isInternationalSugarfitUser(userContext) && ChronicCareServiceHelper.isCoachConsultationSupportedDigiInternationalUser(activePackResponse)) {
            onboardingJourneyWidgetFuture = getOnboardingJourneyWidgetFuture(activePackResponse);
        }

        DAProgramJourneyWidget daProgramJourneyWidget = null;
        DigiUpgradeTrialWidget digiUpgradeTrialWidget = null;
        DigiJourneyProgressWidget digiJourneyProgressWidget = null;
        DigiSessionCarouselWidget digiSessionsCarouselWidget = null;
        DigiLessonsCarouselWidget digiLessonsCarouselWidget = null;
        DigiTasksToDoWidget digiTasksToDoWidget = null;
        OnboardingJourneyWidget onboardingJourneyWidget = null;

        try {
            if (Objects.nonNull(onboardingJourneyWidgetFuture)) {
                onboardingJourneyWidget = onboardingJourneyWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching onboardingJourneyWidget", e);
        }

        try {
            digiSessionsCarouselWidget = digiSessionsCarouselWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching DigiSessionCarouselWidget", e);
        }
        try {
            digiLessonsCarouselWidget = digiLessonsCarouselWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching digiLessonsCarouselWidget", e);
        }
        try {
            digiTasksToDoWidget = digiTasksToDoWidgetCompletableFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching digiTasksToDoWidget", e);
        }

        HomeDiabetesCareHubWidget homeDiabetesCareHubWidget = null;
        DigitalAppHomeProgressWidget digitalAppHomeProgressWidget = null;
        HomeConnectYourDeviceWidget homeConnectYourDeviceWidget = null;

        if (isTrialPack) {
            try {
                digiUpgradeTrialWidget = digiUpgradeTrialWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (Objects.nonNull(digiUpgradeTrialWidget)) {
                    result.addWidget(digiUpgradeTrialWidget);
                    result.setPackPurchaseModalData(chronicCareServiceHelper.getDigiPackPurchaseModalData(userContext));
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching DigiUpgradeTrialWidget", e);
            }
        } else {
            try {
                digiJourneyProgressWidget = digiJourneyProgressWidgetFuture.get(3000, TimeUnit.MILLISECONDS);
                if (Objects.nonNull(digiJourneyProgressWidget)) {
                    result.addWidget(digiJourneyProgressWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching DigiJourneyProgressWidget", e);
            }
        }


        if (Objects.nonNull(onboardingJourneyWidget)) {
            result.addWidget(onboardingJourneyWidget);
            result.addWidget(getHomePageDividerWidget());
        }

        if (Objects.nonNull(digiSessionsCarouselWidget)) {
            result.addWidget(digiSessionsCarouselWidget);
        }

        if (Objects.nonNull(digiLessonsCarouselWidget)) {
            result.addWidget(digiLessonsCarouselWidget);
            result.addWidget(getHomePageDividerWidget());
        }

        try {
            BaseWidgetNonVM activeCardWidget = activeCardsWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (null != activeCardWidget) {
                result.addWidget(activeCardWidget);
                result.addWidget(getHomePageDividerWidget());
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching activeCardWidget", e);
        }

        if(!ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
            boolean isNonCGMProduct = ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(), cgmOnboardingStatus);
            CGMActivationStatus cgmActivationStatus = sfHomePageViewBuilderV2.getCGMActivationStatus(userContext, null, onboardingActionsFuture.get(), liveClassesFuture,cgmOnboardingStatus,consultationBookingsFuture);
            CGMGraphWidgetAndConfig cgmGraphWidgetAndConfig = sfHomePageViewBuilderV2.getCGMGraphWidgetAndCgmConfigurations(userContext, cgmActivationStatus,
                    cgmGraphWidgetFuture, isNonCGMProduct, cgmDeviceStatus,
                    cgmOnboardingStatus, patientDetailFuture.get().getId(), activePackResponse,
                    assignedCareTeamFuture.get(), cgmUserRequestStatusFuture.get(), cgmConfigVersion);

            if (Objects.nonNull(cgmGraphWidgetAndConfig) && Objects.nonNull(cgmGraphWidgetAndConfig.getUserCGMConfiguration())) {
                result.addWidget(cgmGraphWidgetAndConfig.getCgmGraphWidget());
                result.setCgmConfiguration(cgmGraphWidgetAndConfig.getUserCGMConfiguration());
            }
        }

        try {
            if (ChronicCareAppUtil.smartScaleStatsWidgetSupported(userContext)) {
                BaseWidgetNonVM smartScaleWidget = smartScaleStatsWidgetCompletableFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (smartScaleWidget != null) {
                    result.addWidget(smartScaleWidget);
                }
            } else {
                BaseWidgetNonVM scaleIntegrationBanner = smartScaleIntegrationBannerWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (scaleIntegrationBanner != null) {
                    result.addWidget(scaleIntegrationBanner);
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching smartScaleIntegrationBannerWidgetFuture", e);
        }

        if (Objects.nonNull(digiTasksToDoWidget)) {
            result.addWidget(digiTasksToDoWidget);
            result.addWidget(getHomePageDividerWidget());
        }

        if (isTrialPack && ChronicCareAppUtil.isRagusAiSupported(userContext) && ChronicCareAppUtil.isRagusAIChatEnabledForUser(userContext)) {
            try {
                DigiAskRagusFreeQuestionWidget digiAskRagusFreeQuestionWidget = digiAskRagusFreeQuestionWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (Objects.nonNull(digiAskRagusFreeQuestionWidget)) {
                    result.addWidget(digiAskRagusFreeQuestionWidget);
                }
                result.addWidget(getHomePageDividerWidget());
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching DigiAskRagusFreeQuestionWidget", e);
            }
        }

        try {
            digitalAppHomeProgressWidget = digitalAppHomeProgressWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching HomeProgressWidget", e);
        }

        if(!ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
            try {
                homeDiabetesCareHubWidget = homeDiabetesCareHubWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching HomeProgressWidget", e);
            }
        }

        try {
            homeConnectYourDeviceWidget = homeConnectYourDeviceWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching HomeProgressWidget", e);
        }

        if (Objects.nonNull(digitalAppHomeProgressWidget)) {
            result.addWidget(digitalAppHomeProgressWidget);
            result.addWidget(getHomePageDividerWidget());
        }
        if (Objects.nonNull(homeDiabetesCareHubWidget)) {
            result.addWidget(homeDiabetesCareHubWidget);
            result.addWidget(getHomePageDividerWidget());
        }

        if (Objects.nonNull(homeConnectYourDeviceWidget)) {
            result.addWidget(homeConnectYourDeviceWidget);
            result.addWidget(getHomePageDividerWidget());
        }
        result.setShowRagusAiFab(ChronicCareAppUtil.isRagusAiSupported(userContext) && ChronicCareAppUtil.isRagusAIChatEnabledForUser(userContext));
        addDietPlanData(userContext, userId, timezone, result);

        if (!isTrialPack) {
            try {
                daProgramJourneyWidget = digiProgramJourneyWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (Objects.nonNull(daProgramJourneyWidget)) {
                    result.addWidget(daProgramJourneyWidget);
                    result.addWidget(getHomePageDividerWidget());
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching activeCardWidget", e);
            }
        }

        if (!ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
            result.addWidget(getDigiReferralWidget());
        }
        result.addWidget(getDigiQuoteWidget(userContext));
        result.setTrialPackHasExpired(isTrialPackExpired);
        try {
            UserAttributesResponse digitalFtueSeenAttributeResponse = userAttributesClient.getAttributes(userId, DIGITAL_FTUE_SEEN_ATTRIBUTE, getAppTenantFromUserContext(userContext), null);
                if (!(Objects.nonNull(digitalFtueSeenAttributeResponse) && Objects.nonNull(digitalFtueSeenAttributeResponse.getAttributes())
                        && digitalFtueSeenAttributeResponse.getAttributes().containsKey(DIGITAL_FTUE_SEEN_ATTRIBUTE)
                        && Objects.nonNull(digitalFtueSeenAttributeResponse.getAttributes().get(DIGITAL_FTUE_SEEN_ATTRIBUTE))))
                {
                    result.setShowOathScreen(true);
                }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching DigiTasksToDoCardsWidget", e);
        }
    }

    private void addExpiredPackWidgets(UserContext userContext, Long userId, AppTenant appTenant,
                                       Long currentTimeEpoch, SfDigiHomePageView result) throws ExecutionException, InterruptedException, TimeoutException {
        CompletableFuture<CgmOnboardingStatusResponse> cgmOnboardingStatusFuture = chronicCareServiceHelper.getCgmOnboardingStatusFuture(userId, appTenant);
        CgmOnboardingStatusResponse cgmOnboardingStatus = cgmOnboardingStatusFuture.get();

        CompletableFuture<ActivePackResponse> sugarFitExpiredPackFuture = userOnboardingService.getSugarFitExpiredPackFuture(String.valueOf(userId));
        ActivePackResponse expiredPackResponse = sugarFitExpiredPackFuture.get();
        boolean isTrialPack = DigitalAppUtil.isTrialPackUser(expiredPackResponse.getBundleProduct());
        boolean isTrialPackExpired = DigitalAppUtil.isTrialPackExpired(userContext, expiredPackResponse);

        CompletableFuture<UserTaskDaySummary> userTaskDaySummaryCompletableFuture = chronicCareServiceHelper.getDigiTasksToDoSummary(userContext, currentTimeEpoch);
        CompletableFuture<Map<String, UserMetricEntry>> digiRiskScoreFuture = chronicCareServiceHelper.getDigiRiskScoreFuture(userContext);
        CompletableFuture<SessionLogResponse> digiLiveSessionsFuture = chronicCareServiceHelper.getDigiLiveSessionsFuture(userContext);

        CompletableFuture<DigiUpgradeTrialWidget> digiUpgradeTrialWidgetFuture = getDigiUpgradeTrialWidgetFuture(userContext, expiredPackResponse);
        CompletableFuture<DigiJourneyProgressWidget> digiJourneyProgressWidgetFuture = getDigiJourneyProgressWidgetFuture(userContext, digiRiskScoreFuture, digiLiveSessionsFuture, userTaskDaySummaryCompletableFuture, expiredPackResponse);
        CompletableFuture<DAProgramJourneyWidget> digiProgramJourneyWidgetFuture = chronicCareServiceHelper.getDAProgramJourneyWidgetFuture(userContext);
        CompletableFuture<DigiSessionCarouselWidget> digiSessionsCarouselWidgetFuture = getDigiSessionCarouselWidgetFuture(userContext, isTrialPackExpired, expiredPackResponse);
        CompletableFuture<DigiLessonsCarouselWidget> digiLessonsCarouselWidgetFuture = chronicCareServiceHelper.getDigiLessonsCarouselWidgetFuture(userContext, isTrialPackExpired);
//        CompletableFuture<UserOnboardingActionWithContext> onboardingActionsFuture = userOnboardingService.getUserOnboardingActionsFuture(userContext, nuxStatus.getActivePack().getSubscriptionId());
        CompletableFuture<PatientDetail> patientDetailFuture = chronicCarePatientService.getChronicCarePatientForUserIdFuture(userContext);
        CompletableFuture<List<LiveClass>> liveClassesFuture = chronicCareServiceHelper.getLiveClassesCompletableFuture(userContext);
        CompletableFuture<DigiTasksToDoWidget> digiTasksToDoWidgetCompletableFuture = getDigiTasksToDoWidgetFuture(userContext, userTaskDaySummaryCompletableFuture, isTrialPackExpired);
        CompletableFuture<DigiAskRagusFreeQuestionWidget> digiAskRagusFreeQuestionWidgetFuture = getDigiAskRagusFreeQuestionWidgetFuture(userContext);
        CompletableFuture<DigitalAppHomeProgressWidget> digitalAppHomeProgressWidgetFuture = getDigitalAppHomeProgressWidgetFuture(userContext);
        CompletableFuture<HomeDiabetesCareHubWidget> homeDiabetesCareHubWidgetFuture = getHomeDiabetesCareHubWidgetFuture(userContext, expiredPackResponse);
        CompletableFuture<HomeConnectYourDeviceWidget> homeConnectYourDeviceWidgetFuture = getHomeConnectYourDeviceWidgetFuture(userContext, cgmOnboardingStatus);
        CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture = chronicCareServiceHelper.getConsultationBookingsFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> activeCardsWidgetFuture = sfHomePageViewBuilderV2.getActiveCardsWidgetFuture(userContext, patientDetailFuture.get(), sugarFitExpiredPackFuture, consultationBookingsFuture, cgmOnboardingStatus, null, liveClassesFuture, null);
        CompletableFuture<BaseWidgetNonVM> smartScaleIntegrationBannerWidgetFuture = chronicCareServiceHelper.getSmartScaleIntegrationBannerWidgetFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> smartScaleStatsWidgetCompletableFuture = chronicCareServiceHelper.getSmartScaleStatsWidgetCompletableFuture(userContext);

        DigiUpgradeTrialWidget digiUpgradeTrialWidget = null;
        DigiJourneyProgressWidget digiJourneyProgressWidget = null;
        DAProgramJourneyWidget daProgramJourneyWidget = null;
        DigiSessionCarouselWidget digiSessionsCarouselWidget = null;
        DigiLessonsCarouselWidget digiLessonsCarouselWidget = null;
        DigiTasksToDoWidget digiTasksToDoWidget = null;
        try {
            digiSessionsCarouselWidget = digiSessionsCarouselWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching DigiSessionCarouselWidget", e);
        }
        try {
            digiLessonsCarouselWidget = digiLessonsCarouselWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching digiLessonsCarouselWidget", e);
        }
        try {
            digiTasksToDoWidget = digiTasksToDoWidgetCompletableFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching digiTasksToDoWidget", e);
        }

        HomeDiabetesCareHubWidget homediabetescarehubWidget = null;
        DigitalAppHomeProgressWidget digitalAppHomeProgressWidget = null;
        HomeConnectYourDeviceWidget homeConnectYourDeviceWidget = null;


        if (isTrialPack) {
            try {
                digiUpgradeTrialWidget = digiUpgradeTrialWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (Objects.nonNull(digiUpgradeTrialWidget)) {
                    result.addWidget(digiUpgradeTrialWidget);
                    result.setPackPurchaseModalData(chronicCareServiceHelper.getDigiPackPurchaseModalData(userContext));
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching DigiUpgradeTrialWidget", e);
            }
        } else {
            try {
                digiJourneyProgressWidget = digiJourneyProgressWidgetFuture.get(3000, TimeUnit.MILLISECONDS);
                if (Objects.nonNull(digiJourneyProgressWidget)) {
                    result.addWidget(digiJourneyProgressWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching DigiJourneyProgressWidget", e);
            }
        }

        if (Objects.nonNull(digiSessionsCarouselWidget)) {
            result.addWidget(digiSessionsCarouselWidget);
        }

        if (Objects.nonNull(digiLessonsCarouselWidget)) {
            result.addWidget(digiLessonsCarouselWidget);
            result.addWidget(getHomePageDividerWidget());
        }

        try {
            BaseWidgetNonVM activeCardWidget = activeCardsWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (null != activeCardWidget) {
                result.addWidget(activeCardWidget);
                result.addWidget(getHomePageDividerWidget());
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching activeCardWidget", e);
        }

        try {
            if (ChronicCareAppUtil.smartScaleStatsWidgetSupported(userContext)) {
                BaseWidgetNonVM smartScaleWidget = smartScaleStatsWidgetCompletableFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (smartScaleWidget != null) {
                    result.addWidget(smartScaleWidget);
                }
            } else {
                BaseWidgetNonVM scaleIntegrationBanner = smartScaleIntegrationBannerWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (scaleIntegrationBanner != null) {
                    result.addWidget(scaleIntegrationBanner);
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching smartScaleIntegrationBannerWidgetFuture", e);
        }

        if (Objects.nonNull(digiTasksToDoWidget)) {
            result.addWidget(digiTasksToDoWidget);
            result.addWidget(getHomePageDividerWidget());
        }

        try {
            digitalAppHomeProgressWidget = digitalAppHomeProgressWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching HomeProgressWidget", e);
        }

        if(!ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
            try {
                homediabetescarehubWidget = homeDiabetesCareHubWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching HomeProgressWidget", e);
            }
        }

        try {
            homeConnectYourDeviceWidget = homeConnectYourDeviceWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching HomeProgressWidget", e);
        }

        if (Objects.nonNull(digitalAppHomeProgressWidget)) {
            result.addWidget(digitalAppHomeProgressWidget);
            result.addWidget(getHomePageDividerWidget());
        }
        if (Objects.nonNull(homediabetescarehubWidget)) {
            result.addWidget(homediabetescarehubWidget);
            result.addWidget(getHomePageDividerWidget());
        }
        if (Objects.nonNull(homeConnectYourDeviceWidget)) {
            result.addWidget(homeConnectYourDeviceWidget);
            result.addWidget(getHomePageDividerWidget());
        }
        result.setShowRagusAiFab(ChronicCareAppUtil.isRagusAiSupported(userContext) && ChronicCareAppUtil.isRagusAIChatEnabledForUser(userContext));

        if(!isTrialPack) {
            daProgramJourneyWidget = digiProgramJourneyWidgetFuture.get(3000, TimeUnit.MILLISECONDS);
            if (Objects.nonNull(daProgramJourneyWidget)) {
                result.addWidget(daProgramJourneyWidget);
                result.addWidget(getHomePageDividerWidget());
            }
        }

        if (!ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
            result.addWidget(getDigiReferralWidget());
        }
        result.addWidget(getDigiQuoteWidget(userContext));
        result.setTrialPackHasExpired(isTrialPackExpired);
    }

    private void addDietPlanData(UserContext userContext, Long userId, TimeZone timezone, SfDigiHomePageView result){
        try {
            if (chronicCareServiceHelper.isDietPlanRequestedByDigitalAppUser(serviceInterfaces, userContext)){
                UserAttributesResponse digitalDietPlanSeen = userAttributesClient.getAttributes(userId, DIGITAL_DIET_PLAN_SEEN_ATTRIBUTE, getAppTenantFromUserContext(userContext), null);
                if (!(Objects.nonNull(digitalDietPlanSeen) && Objects.nonNull(digitalDietPlanSeen.getAttributes())
                        && digitalDietPlanSeen.getAttributes().containsKey(DIGITAL_DIET_PLAN_SEEN_ATTRIBUTE)
                        && Objects.nonNull(digitalDietPlanSeen.getAttributes().get(DIGITAL_DIET_PLAN_SEEN_ATTRIBUTE)))) {
                    UserAttributesResponse digitalDietPlanShowTime = userAttributesClient.getAttributes(userId, DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE, getAppTenantFromUserContext(userContext), null);
                    long showTime =  Long.parseLong((String) digitalDietPlanShowTime.getAttributes().get(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE));
                    Calendar calendar = Calendar.getInstance(timezone);
                    long currentTimeMillis = calendar.getTime().getTime();
                    long currentTimeSeconds = currentTimeMillis / 1000;
                    if (currentTimeSeconds > showTime) {
                        try {
                            String dietPlanPdfUrl = chronicCareServiceHelper.getUserDietPlanPdfUrl(userContext);
                            if (dietPlanPdfUrl == null) {
                                return;
                            }
                            SfDigiHomePageView.DietCardData card = new SfDigiHomePageView.DietCardData();
                            Action action = new Action();
                            Map<String, Object> actionMeta = new HashMap<>();
                            actionMeta.put("fileUrl", dietPlanPdfUrl);
                            actionMeta.put("fileTitle", "Diet Plan");
                            actionMeta.put("documentType", "DIET_PLAN");
                            action.setActionType(ActionType.OPEN_ONLINE_PDF);
                            action.setMeta(actionMeta);
                            card.setAction(action);
                            result.setDietCardData(card);
                        } catch (Exception e) {
                            exceptionReportingService.reportException("Error in adding dietcard", e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in getting digitalDietPlanShowTime ", e);
        }
    }

    private CompletableFuture<DigitalAppHomeProgressWidget> getDigitalAppHomeProgressWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getDigitalAppHomeProgressWidget(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getDigitalAppHomeProgressWidget", e);
                log.error("Error in getting Digital app home progress widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<HomeDiabetesCareHubWidget> getHomeDiabetesCareHubWidgetFuture(UserContext userContext, ActivePackResponse activePackResponse) {
        return supplyAsync(() -> {
            try {
                return getHomeDiabetesCareHubWidget(userContext, activePackResponse);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getHomeProgressWidgetFuture", e);
                log.error("Error in getting diabetes care hub widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public HomeDiabetesCareHubWidget getHomeDiabetesCareHubWidget(UserContext userContext, ActivePackResponse activePackResponse) throws Exception {
        HomeDiabetesCareHubWidget homeDiabetesCareHubWidget = new HomeDiabetesCareHubWidget();
        List<ConfigEntry> categories = indusClient.getConfig(ConfigType.PRODUCT_CATEGORY);
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
//        CompletableFuture<CgmOnboardingStatusResponse> cgmOnboardingStatusFuture = chronicCareServiceHelper.getCgmOnboardingStatusFuture(userId, getAppTenantFromUserContext(userContext));
//        Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());
        ArrayList<String> categoriesFromList = new ArrayList<>();
        categoriesFromList.add("FOOD");
//        boolean isNfcUser = chronicCareServiceHelper.getUserNfcStatus(userContext);
//        List<CGMDeviceInfo> deviceInfos = cgmOnboardingStatusFuture.get().getCgmDeviceInfos();
//        if (isNfcUser && (CollectionUtils.isEmpty(deviceInfos) || (CollectionUtils.isNotEmpty(deviceInfos) && "AGM_ENDED".equals(deviceInfos.getLast().getCurrentState())))) {
//            categoriesFromList.add("CGM_STORE");
//        }
        String diagnosticsLargeBannerUrl = "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/diagnostics_upsell-2025-04-27-21:13.png";
        Action diagnosticRequestAction = new Action();
        diagnosticRequestAction.setActionType(ActionType.SF_OPS_REQUEST);
        diagnosticRequestAction.setTitle("RAISE A REQUEST");
        diagnosticRequestAction.setMeta(new SfOpsRequestMeta(REQUEST_DIAGNOSTICS));
        SfSalesBannerMeta sfSalesBannerMeta2 = new SfSalesBannerMeta(diagnosticsLargeBannerUrl, "Please raise a request and we will reach out to you within 24 hours for next steps.", diagnosticRequestAction);
        Action showSalesBannerAction2 = new Action();
        showSalesBannerAction2.setActionType(ActionType.SHOW_SF_SALES_BANNER_MODAL);
        showSalesBannerAction2.setMeta(sfSalesBannerMeta2);
        categoriesFromList.add("CGM_STORE");
        List<HomeDiabetesCareHubWidget.CareHubItems> items = Arrays.asList(
                new HomeDiabetesCareHubWidget.CareHubItems("Nutritionist", "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/care_hub_nutritionist-2025-02-12-17:54.png", Action.builder().actionType(NAVIGATION).url(chronicCareServiceHelper.getDigitalAppDatePickerUrl(SUGARFIT_LONG_COACH_CONSULT, null, activePackResponse)).build()),
                new HomeDiabetesCareHubWidget.CareHubItems("Doctor", "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/care_hub_doctor-2025-02-12-17:54.png", Action.builder().actionType(NAVIGATION).url(chronicCareServiceHelper.getDigitalAppDatePickerUrl(SUGARFIT_DOCTOR_CONSULT, null, activePackResponse)).build()),
                new HomeDiabetesCareHubWidget.CareHubItems("Psychologist", "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/care_hub_pyschologist-2025-02-12-17:55.png", Action.builder().actionType(NAVIGATION).url(chronicCareServiceHelper.getPsychologistAgentDatePickerUrl(SUGARFIT_PSYCHOLOGIST_CONSULT, activePackResponse)).build()),
//                new HomeDiabetesCareHubWidget.CareHubItems("Diagnostics", "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/care_hub_diagnostics-2025-02-12-17:54.png", Action.builder().actionType(NAVIGATION).url("curefit://sfdiagnosticstestlistpage").build())
                new HomeDiabetesCareHubWidget.CareHubItems("Diagnostics", "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/care_hub_diagnostics-2025-02-12-17:54.png", showSalesBannerAction2)

        );

        List<HomeDiabetesCareHubWidget.CareHubItems> careHubItemsList = new ArrayList<>(items);

        if (CollectionUtils.isNotEmpty(categories)) {
            Map<String, ConfigEntry> confiMap = categories.stream()
                    .collect(Collectors.toMap(ConfigEntry::getType, Function.identity()));

            for (String cat: categoriesFromList) {
                ConfigEntry configEntry = confiMap.get(cat);
                if (configEntry != null) {
                    HomeDiabetesCareHubWidget.CareHubItems item = new HomeDiabetesCareHubWidget.CareHubItems();
                    String categoryId = configEntry.getType();
                    String imageUrl = "";
                    try {
                        Map<String, Object> imageUrls = configEntry.getConfigDetail().getImageUrls();
                        if (imageUrls != null && !imageUrls.isEmpty()) {
                            imageUrl = (String) new ArrayList<>(imageUrls.values()).get(0);
                        }
                    } catch (Exception e) {
                        // Ignore exception
                    }
                    if(Objects.equals(cat, "CGM_STORE")){
                        item.setTitle(configEntry.getTitle());
                        item.setImageUrl(imageUrl);
                        item.setCardClickAction(Action.builder()
                                .actionType(NAVIGATION)
                                .url("curefit://sfecommercepdp?category=CGM_STORE")
                                .build());
                    }else{
                        item.setImageUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/care_hub_active_food-2025-02-12-17:54.png");
                        item.setTitle("Active Food");
                        item.setCardClickAction(Action.builder()
                                .actionType(NAVIGATION)
                                .url("curefit://sfstorecategorypage?categoryId=" + categoryId)
                                .build());
                    }
                    careHubItemsList.add(item);
                }
            }
        }

        homeDiabetesCareHubWidget.setData(careHubItemsList);
        return homeDiabetesCareHubWidget;
    }

    private String getDigitalAppGoalLabel(String goalName) {
        return switch (goalName) {
            case "CALORIES-EATEN" -> "Calorie";
            case "FASTING-GLUCOSE" -> "Latest Sugar";
            case "STEPS" -> "Steps";
            case "WEIGHT" -> "Weight";
            default -> goalName;
        };
    }

    public static String getDigitalAppGoalUnit(String goalName) {
        return switch (goalName) {
            case "STEPS" -> "steps";
            case "CALORIES-EATEN" -> "cal";
            case "WEIGHT" -> "kg";
            case "FASTING-GLUCOSE" -> "mg/dL";
            default -> "";
        };
    }
    static double safeParse(String value) {
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            return 0; // Default to zero if parsing fails
        }
    }

    public static String getDigitalAppGoalLevel(String goalName, String currentValue, String goalValue) {
        return switch (goalName) {
            case "CALORIES-EATEN" -> {
                double current = safeParse(currentValue);
                double goal = safeParse(goalValue);
                double percentage = (goal == 0) ? 0 : (current / goal) * 100;

                if (percentage >= 90) yield "NORMAL";
                else if (percentage >= 50) yield "MEDIUM";
                else yield "HIGH";
            }
            case "STEPS" -> {
                double current = safeParse(currentValue);
                double goal = safeParse(goalValue);
                double percentage = (goal == 0) ? 0 : (current / goal) * 100;

                if (percentage <= 30) yield "HIGH";
                else if (percentage <= 60) yield "MEDIUM";
                else yield "NORMAL";
            }
            case "FASTING-GLUCOSE" -> {
                double current = safeParse(currentValue);

                if (current < 80) yield "HIGH";
                else if (current <= 140) yield "NORMAL";
                else yield "MEDIUM";
            }
            case "WEIGHT" -> {
                double current = safeParse(currentValue);
                if (current > 40 && current < 100) yield "NORMAL";
                else yield "";
            }
            default -> "";
        };
    }

    public static boolean isGoalIsAchieved(String goalName, String currentValue, String goalValue) {
        return switch (goalName) {
            case "CALORIES-EATEN" -> {
                double current = safeParse(currentValue);
                double goal = safeParse(goalValue);
                double percentage = (goal == 0) ? 0 : (current / goal) * 100;
                yield percentage >= 90 && percentage <= 110;
            }
            case "STEPS" -> {
                double current = safeParse(currentValue);
                double goal = safeParse(goalValue);
                double percentage = (goal == 0) ? 0 : (current / goal) * 100;
                yield percentage >= 100;
            }
            case "FASTING-GLUCOSE" -> {
                double current = safeParse(currentValue);
                yield current > 80 && current < 140;
            }
            case "WEIGHT" -> {
                double current = safeParse(currentValue);
                yield current > 40 && current < 100;
            }
            default -> false;
        };
    }

    public DigitalAppHomeProgressWidget getDigitalAppHomeProgressWidget(UserContext userContext)
            throws ExecutionException, InterruptedException, HttpException {

        DigitalAppHomeProgressWidget homeProgressWidget = new DigitalAppHomeProgressWidget();

        CompletableFuture<List<DigitalAppHomeProgressWidget.ProgressItem>> progressItemsFuture = getDigitalAppHomeProgressDataFuture(userContext);
        List<DigitalAppHomeProgressWidget.ProgressItem> progressItems = progressItemsFuture.get();

        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
        String timeZoneString = userContext.getUserProfile().getTimezone();
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneString);
        long activityTime = TimeUtil.getStartOfDay(new Date(), String.valueOf(timeZone)).getTime();

        NutritionConsumptionResponse data = loggingClient.fetchNutrientsConsumption(userId, activityTime, appTenant, timeZone);

        Float consumedCals = 0F;    // Fallback value
        Float requiredCals = 0F; // Fallback value

        NutritionConsumption calsData = data.getNutritionConsumptions().stream()
                .filter(el -> el.getNutritionType() == NutritionType.Calorie)
                .findFirst()
                .orElse(null);

        if (calsData != null) {
            consumedCals = calsData.getConsumed() != null ? calsData.getConsumed() : consumedCals;
            requiredCals = calsData.getRequired() != null ? calsData.getRequired() : requiredCals;
        }

        String bodyImageColor = requiredCals>0 ?getDigitalAppGoalLevel("CALORIES-EATEN", String.valueOf(consumedCals), String.valueOf(requiredCals)) :"DEFAULT";

        List<NutritionConsumption> nutritionList = data.getNutritionConsumptions().stream()
                .filter(el -> el.getNutritionType() != NutritionType.Calorie)
                .toList();

        int progressGoalCount = (int) progressItems.stream()
                .filter(item -> item.getCurrentValue() != null && !item.getCurrentValue().equals("--") &&
                        !Objects.equals(item.getCurrentValue(), "") && Double.parseDouble(item.getCurrentValue()) > 0)
                .count();

        // Creating GoalCard
        DigitalAppHomeProgressWidget.ProgressCard goalCard = DigitalAppHomeProgressWidget.ProgressCard.builder()
                .type("GOAL")
                .data(progressItems)
                .build();

        // Creating CalorieCard
        DigitalAppHomeProgressWidget.ProgressCard calorieCard = DigitalAppHomeProgressWidget.ProgressCard.builder()
                .type("CALORIE")
                .nutritionList(nutritionList)
                .calories(consumedCals)
                .goal(requiredCals)
                .bodyImageColor(bodyImageColor)
                .gradientColor(getProgressGradientColor(consumedCals > 0 && requiredCals > 0 ? getDigitalAppGoalLevel("CALORIES-EATEN", String.valueOf(consumedCals), String.valueOf(requiredCals)):"DEFAULT"))
                .build();

        // Setting data as a list of goalCard and calorieCard
        homeProgressWidget.setData(List.of(goalCard, calorieCard));
        boolean hasAnyNonAchievedGoal = progressItems.stream().anyMatch(g -> !g.isGoalAchieved());
        if (!hasAnyNonAchievedGoal) {
            homeProgressWidget.setGoalAchievedText(" goals achieved \uD83C\uDF89");
        } else {
            homeProgressWidget.setGoalAchievedText(" vitals logged");
        }
        homeProgressWidget.setProgressGoalCount(progressGoalCount);
        homeProgressWidget.setGoalCardBgGradientColor(getGoalCardBgGradientColors(progressGoalCount == progressItems.size()));
        if (consumedCals>0 && requiredCals>0){
            homeProgressWidget.setCalorieCardBgGradientColor(getCalorieCardBgGradientColor(getDigitalAppGoalLevel("CALORIES-EATEN", String.valueOf(consumedCals), String.valueOf(requiredCals))));
        } else {
            homeProgressWidget.setCalorieCardBgGradientColor(getCalorieCardBgGradientColor("DEFAULT"));
        }
        FitnessDeviceSyncMeta fitnessDeviceSyncMeta = chronicCareServiceHelper.getFitnessDeviceSyncMeta(userContext);
        homeProgressWidget.setActiveDeviceId(fitnessDeviceSyncMeta.getActiveDeviceId());

        if (fitnessDeviceSyncMeta.getLastSyncedTime() != null) {
            homeProgressWidget.setLastSyncedTime(fitnessDeviceSyncMeta.getLastSyncedTime());
            homeProgressWidget.setLastSyncedDisplayTime(fitnessDeviceSyncMeta.getLastSyncedDisplayTime());
        }

        homeProgressWidget.setTitleAction(Action.builder()
                .title("View Details")
                .actionType(ActionType.NAVIGATION)
                .url("curefit://sfdigiprogressdashboardpage")
                .build());

        return homeProgressWidget;
    }

    private CompletableFuture<List<DigitalAppHomeProgressWidget.ProgressItem>> getDigitalAppHomeProgressDataFuture(UserContext userContext) {
        return supplyAsync(() -> {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            String userTimeZone = userContext.getUserProfile().getTimezone();
            Date startDate = TimeUtil.getEndOfDay(Calendar.getInstance().getTime(), userTimeZone);

            Calendar currentTime = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            currentTime.set(Calendar.HOUR_OF_DAY, 23);
            currentTime.set(Calendar.MINUTE, 59);
            currentTime.set(Calendar.SECOND, 59);
            currentTime.set(Calendar.MILLISECOND, 999);

            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            dateFormat.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
            List<DigitalAppHomeProgressWidget.ProgressItem> progressItems = new ArrayList<>();
            try {
                List<GoalWithData> goalWithLatestData = chronicCareServiceHelper.getShifuRecentGoals(userContext,
                        userId, startDate, true, userTimeZone);
                for (String goalName : PROGRESS_GOALS) {
                    GoalWithData goal = goalWithLatestData.stream()
                            .filter(goalWithData -> goalWithData.getGoal().getMeta().getUniqueKey().equals(goalName))
                            .findAny()
                            .orElse(null);
                    if (goal != null) {
                        DigitalAppHomeProgressWidget.ProgressItem item = new DigitalAppHomeProgressWidget.ProgressItem();
                        //keep initially zero
                        item.setCurrentValue("0");
                        item.setGoalValue("0");
                        if (goalName.equals("FASTING-GLUCOSE")) {
                            AppTenant appTenant = getAppTenantFromUserContext(userContext);
                            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
                            ZoneId userZoneId = timeZone.toZoneId();
                            List<String> metricNames = List.of(GLUCOMETER_READING_METRIC);
                            Map<String, UserMetricEntry> metricsMap = serviceInterfaces.getChsClient().fetchLatestUserMetricBulk(userId,metricNames,appTenant,timeZone);
                            if (metricsMap != null && metricsMap.containsKey(GLUCOMETER_READING_METRIC)) {
                                UserMetricEntry entry = metricsMap.get(GLUCOMETER_READING_METRIC);
                                if (entry != null) {
                                    Date startTime = entry.getStartTime();
                                    if (startTime != null) {
                                        LocalDate lastUpdatedTime = startTime.toInstant().atZone(userZoneId).toLocalDate();
                                        LocalDate today = LocalDate.now(userZoneId);
                                        if (lastUpdatedTime.equals(today)) {
                                            item.setCurrentValue(entry.getValue());
                                        }
                                    }
                                }
                            }
                        }
                        if (goalName.equals("WEIGHT")) {
                            AppTenant appTenant = getAppTenantFromUserContext(userContext);
                            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
                            List<String> metricNames = List.of(WEIGHT_METRIC);
                            Map<String, UserMetricEntry> metricsMap = serviceInterfaces.getChsClient().fetchLatestUserMetricBulk(userId,metricNames,appTenant,timeZone);
                            if (metricsMap != null && metricsMap.containsKey(WEIGHT_METRIC)) {
                                UserMetricEntry entry = metricsMap.get(WEIGHT_METRIC);
                                if (entry != null) {
                                    item.setCurrentValue(entry.getValue());
                                }
                            }
                        }
                        if (goal.getMetricValuePojo() != null && goal.getMetricValuePojo().getValue() != null && !goalName.equals("FASTING-GLUCOSE") && !goalName.equals("WEIGHT")) {
                            item.setCurrentValue(getFormattedRecentValue(goal.getMetricValuePojo().getValue(),
                                    goal.getGoal().getMeta().getUniqueKey()));
                        }
                        if(Objects.equals(item.getCurrentValue(), "0") && (goalName.equals("FASTING-GLUCOSE") || goalName.equals("WEIGHT"))){
                            item.setCurrentValue("--");
                        }
                        if (goal.getTarget() != null) {
                            item.setGoalValue(getTargetValue(goal.getTarget().getLow(), goal.getTarget().getHigh(),
                                    goal.getGoal().getMeta().getUniqueKey()));
                        }
                        item.setType(goal.getGoal().getMeta().getUniqueKey());
                        item.setDisplayName(getDigitalAppGoalLabel(goalName));
                        item.setUnit(getDigitalAppGoalUnit(goalName));
                        item.setLogAction(getGoalsLogAction(goalName, userContext, chronicCareServiceHelper));
                        item.setCardClickAction(Action.builder().actionType(ActionType.NAVIGATION)
                                .url("curefit://sfdigiprogressdashboardpage")
                                .build());
                        if (!Objects.equals(item.getCurrentValue(), "--") && item.getCurrentValue() != null && !Objects.equals(item.getCurrentValue(), "") && Double.parseDouble(item.getCurrentValue()) > 0){
                            item.setLevel(getDigitalAppGoalLevel(goalName,item.getCurrentValue(),item.getGoalValue()));
                            item.setGradientColor(getProgressGradientColor(item.getLevel()));
                        } else {
                            item.setGradientColor(getProgressGradientColor("DEFAULT"));
                        }
                        if (Objects.nonNull(item.getGoalValue()) && Objects.nonNull(item.getCurrentValue()) && (!item.getCurrentValue().equals("--"))) {
                            item.setGoalAchieved(isGoalIsAchieved(goalName, item.getCurrentValue(), item.getGoalValue()));
                        }
                        progressItems.add(item);
                    }
                }

            } catch (Exception e) {
                this.exceptionReportingService.reportException("Error in getting recent goals data ==>", e);
            }
            return progressItems;
        } , serviceInterfaces.getTaskExecutor());
    }

    CompletableFuture<DigiUpgradeTrialWidget> getDigiUpgradeTrialWidgetFuture(UserContext userContext, ActivePackResponse activePackResponse) {
        return supplyAsync(() -> {
            try {
                return getDigiUpgradeTrialWidget(userContext, activePackResponse);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getDigiUpgradeTrialWidgetFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    DigiUpgradeTrialWidget getDigiUpgradeTrialWidget(UserContext userContext, ActivePackResponse activePackResponse){
           DigiUpgradeTrialWidget digiUpgradeTrialWidget = new DigiUpgradeTrialWidget();
           digiUpgradeTrialWidget.setTitleImg("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/sugar-control-2025-07-31-18:17.png");
           Date currentDate = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext)).getTime();
           Date packStartDate = new Date(activePackResponse.getStartDate());
           digiUpgradeTrialWidget.setDaysUsed(SfDateUtils.getDifferenceDays(packStartDate, currentDate));
           digiUpgradeTrialWidget.setTotalDays(activePackResponse.getBundleProduct().getDuration());
           digiUpgradeTrialWidget.setUpgradeAction(new Action("", ActionType.SHOW_DIGI_PACKS_PURCHASE_MODAL));
           return digiUpgradeTrialWidget;
    }

    CompletableFuture<DigiJourneyProgressWidget> getDigiJourneyProgressWidgetFuture(UserContext userContext, CompletableFuture<Map<String, UserMetricEntry>> scoreResponseFuture,
                                                                                    CompletableFuture<SessionLogResponse> userSessionsRespFuture, CompletableFuture<UserTaskDaySummary> userTaskDaySummaryFuture,
                                                                                    ActivePackResponse activePackResponse) {
        return supplyAsync(() -> {
            try {
                return getDigiJourneyProgressWidget(userContext, scoreResponseFuture, userSessionsRespFuture, userTaskDaySummaryFuture, activePackResponse);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getDigiJourneyProgressWidgetFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    DigiJourneyProgressWidget getDigiJourneyProgressWidget(UserContext userContext, CompletableFuture<Map<String, UserMetricEntry>> scoreResponseFuture,
                                                           CompletableFuture<SessionLogResponse> userSessionsRespFuture, CompletableFuture<UserTaskDaySummary> userTaskDaySummaryFuture,
                                                           ActivePackResponse activePackResponse){
        try {
            DigiJourneyProgressWidget digiJourneyProgressWidget = new DigiJourneyProgressWidget();
            if (DigitalAppUtil.isDigitalUsaSupportedVersion(userContext)) {
                digiJourneyProgressWidget.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfgenericpage?pageId=digiprogramlearn").build());
            }
            Map<String, UserMetricEntry> scoreResponse = scoreResponseFuture.get();
            SessionLogResponse userSessionsResp = userSessionsRespFuture.get();
            UserTaskDaySummary userTaskDaySummary = userTaskDaySummaryFuture.get();
            // default scores
            DigiJourneyProgressWidget.DonutProgress riskScore = new DigiJourneyProgressWidget.DonutProgress(0L, 100L, "Risk Score", "#E02D3C");
            DigiJourneyProgressWidget.DonutProgress sessionProgress = new DigiJourneyProgressWidget.DonutProgress(0L, 1L, "Sessions", "#6893B3");
            DigiJourneyProgressWidget.DonutProgress lessonProgress = new DigiJourneyProgressWidget.DonutProgress(0L, 1L, "Lessons", "#7168B3");
            DigiJourneyProgressWidget.DonutProgress tasksProgress = new DigiJourneyProgressWidget.DonutProgress(0L, 1L, "Tasks", "#9768B3");
            DigiJourneyProgressWidget.DayProgress dayProgress = new DigiJourneyProgressWidget.DayProgress(1, 30);

            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            String userTimeZone = userContext.getUserProfile().getTimezone();
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);

            try {
//            Map<String, UserMetricEntry> scoreResp = serviceInterfaces.getChsClient().getProfileRiskScoreForUserId(userId);
                if (Objects.nonNull(scoreResponse) && scoreResponse.containsKey("diabetes_risk_score")) {
                    UserMetricEntry userMetricEntry = scoreResponse.get("diabetes_risk_score");
                    riskScore.setScore(Long.parseLong(userMetricEntry.getValue()));
                    String diabetesRiskLevel = digiPrePurchasePageBuilder.getDiabetesRiskScoreLevel(Double.valueOf(userMetricEntry.getValue()));
                    riskScore.setProgressColor(digiPrePurchasePageBuilder.getDiabetesRiskScoreColor(diabetesRiskLevel));
                }
            }catch (Exception e){
                serviceInterfaces.getExceptionReportingService().reportException(e);
            }

            try {
                Date userDayStart = TimeUtil.getStartOfDay(new Date(), userTimeZone);
                Date userDayEnd = TimeUtil.getEndOfDay(new Date(), userTimeZone);
                UserSessionLogFetchRequest userSessionLogFetchRequest = new UserSessionLogFetchRequest();
                userSessionLogFetchRequest.setEventType(UserEventType.JOINED_SESSION);
                userSessionLogFetchRequest.setSessionType(SessionType.LIVE_YT_SESSION);
                userSessionLogFetchRequest.setSortOrder(SortOrder.ASC);
                userSessionLogFetchRequest.setFromDate(userDayStart);
                userSessionLogFetchRequest.setToDate(userDayEnd);
                userSessionLogFetchRequest.setUserId(userId);
//            SessionLogResponse userSessionsResp = sfFitnessClient.fetchUserSessions(userSessionLogFetchRequest);
                if(Objects.nonNull(userSessionsResp)){
                    sessionProgress.setScore(userSessionsResp.getAttendedLiveSessions());
                    sessionProgress.setTotal(userSessionsResp.getTotalLiveSessions());
                }
            }catch (Exception e){
                serviceInterfaces.getExceptionReportingService().reportException(e);
            }

            try {
//            UserTaskDaySummary userTaskDaySummary = serviceInterfaces.getSmsClient().getUserTaskDaySummary(userId, new Date().getTime());
                List<DigiTasksToDoCard> cards = digiTasksToDoCardsBuilder.buildCards(userContext, userTaskDaySummary, false, false);
                if (Objects.nonNull(userTaskDaySummary)) {
                    tasksProgress.setTotal((long) cards.size());
                    tasksProgress.setScore(cards.stream().filter(t -> Boolean.TRUE.equals(t.getCompleted())).count());
                }
            }catch (Exception e){
                serviceInterfaces.getExceptionReportingService().reportException(e);
            }

            try {
                BookComposition activeBook = catalogClient.getActiveBookForUserId(userId, appTenant, timeZone);
                if (Objects.nonNull(activeBook)) {
                    boolean isCompleted = Boolean.TRUE.equals(activeBook.getUserChapterTrackingEntry().getIsCompleted());
                    lessonProgress.setScore(isCompleted ? 1L : 0L);
                    lessonProgress.setTotal(1L);
                }
            }catch (Exception e){
                serviceInterfaces.getExceptionReportingService().reportException(e);
            }

            try {
                List<BookComposition> allUserBooks = catalogClient.getAllBookForUserId(userId, appTenant, timeZone);
                if (Objects.nonNull(allUserBooks)) {
                    int booksCompleted = (int) allUserBooks.stream()
                            .filter(userBook -> Objects.nonNull(userBook.getUserChapterTrackingEntry()) && Boolean.TRUE.equals(userBook.getUserChapterTrackingEntry().getIsCompleted()))
                            .count();
                    dayProgress.setCurrentDay(booksCompleted + 1);
//                    dayProgress.setTotalDays(Math.max(allUserBooks.size(), 30));
                    dayProgress.setTotalDays(Math.toIntExact(activePackResponse.getBundleProduct().getDuration()));
                }
            }catch (Exception e){
                serviceInterfaces.getExceptionReportingService().reportException(e);
            }

            digiJourneyProgressWidget.setFitScore(riskScore);
            digiJourneyProgressWidget.setSessionProgress(sessionProgress);
            digiJourneyProgressWidget.setLessonProgress(lessonProgress);
            digiJourneyProgressWidget.setTasksProgress(tasksProgress);
            digiJourneyProgressWidget.setDayProgress(dayProgress);
            return digiJourneyProgressWidget;
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching DigiJourneyProgressWidget", e);
        }
        return null;
    }

    CompletableFuture<DigiSessionCarouselWidget> getDigiSessionCarouselWidgetFuture(UserContext userContext, boolean isTrialPackExpired, ActivePackResponse activePackResponse) {
        return supplyAsync(() -> {
            try {
                return getDigiSessionCarouselWidget(userContext, isTrialPackExpired, activePackResponse);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getDigiSessionCarouselWidgetFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    DigiSessionCarouselWidget getDigiSessionCarouselWidget(UserContext userContext, boolean isTrialPackExpired, ActivePackResponse activePackResponse){
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String userTimeZone = userContext.getUserProfile().getTimezone();
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
        BookEntry bookEntry = null;

        try {
            BookComposition book = catalogClient.getActiveBookForUserId(userId, appTenant, timeZone);
            if (book != null) {
                bookEntry = book.getBookEntry();
            }
        }catch (Exception e){
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }

        try {
            DigiSessionCarouselWidget digiSessionCarouselWidget = new DigiSessionCarouselWidget();
            digiSessionCarouselWidget.setTitle("Today's");
            digiSessionCarouselWidget.setBoldTitle(" Expert Sessions");

            Date userDateNow = Calendar.getInstance(TimeZone.getTimeZone(userTimeZone)).getTime();
            Date userDayStart = TimeUtil.getStartOfDay(new Date(), userTimeZone);
            Date userDayEnd = TimeUtil.getEndOfDay(new Date(), userTimeZone);

            UserSessionLogFetchRequest userSessionLogFetchRequest = new UserSessionLogFetchRequest();
            userSessionLogFetchRequest.setEventType(UserEventType.JOINED_SESSION);
            userSessionLogFetchRequest.setSessionType(SessionType.LIVE_YT_SESSION);
            userSessionLogFetchRequest.setSortOrder(SortOrder.ASC);
            userSessionLogFetchRequest.setFromDate(userDayStart);
            userSessionLogFetchRequest.setToDate(userDayEnd);
            userSessionLogFetchRequest.setUserId(userId);
            SessionLogResponse userSessionsResp = sfFitnessClient.fetchUserSessions(userSessionLogFetchRequest, appTenant, timeZone);
            List<UserSessionLogEntry> userSessionLogEntries = Objects.nonNull(userSessionsResp) ? userSessionsResp.getUserSessionLogs() : null;

            List<Long> joinedSessionIds = new ArrayList<>();
            if(Objects.nonNull(userSessionLogEntries)) {
                for (UserSessionLogEntry entry : userSessionLogEntries) {
                    joinedSessionIds.add(entry.getSessionId());
                }
            }

            List<LiveSessionConfigEntry> liveSessions = Objects.nonNull(userSessionsResp) ? userSessionsResp.getLiveSessionConfigs() : null;

            if(Objects.nonNull(liveSessions)){
                for (LiveSessionConfigEntry liveSessionEntry: liveSessions) {
                    if (ChronicCareAppUtil.isInternationalSugarfitUser(userContext)
                            && Objects.nonNull(liveSessionEntry.getMetaData())
                            && !Boolean.TRUE.equals(liveSessionEntry.getMetaData().getIsCoachSession())) {
                        continue; // Skip doctor sessions for international users
                    }
                    DigiSessionCarouselWidget.SessionCard sessionCard = getSessionCard(liveSessionEntry, isTrialPackExpired);
                    if (Objects.nonNull(sessionCard) && userDateNow.getTime() < liveSessionEntry.getEndTime().getTime()) {
                        digiSessionCarouselWidget.addSessionCard(sessionCard);
                    } else if (Objects.nonNull(sessionCard) && joinedSessionIds.contains(liveSessionEntry.getId())) {
                        sessionCard.setCompleted(true);
                        if (Objects.nonNull(bookEntry)) {
                            sessionCard.setUpsellingData(chronicCareServiceHelper
                                    .updateActionsInUpsellingDataByAddingDynamicParams(bookEntry.getUpsellingData(), activePackResponse));
                        }
                        digiSessionCarouselWidget.addSessionCard(sessionCard);
                    }
                }
            }

            if (Objects.nonNull(digiSessionCarouselWidget.getSessionCards())) {
                List<DigiSessionCarouselWidget.SessionCard> sortedCards = digiSessionCarouselWidget.getSessionCards().stream()
                        .sorted(Comparator.comparing(DigiSessionCarouselWidget.SessionCard::isCompleted))
                        .toList();
                digiSessionCarouselWidget.setSessionCards(sortedCards);
            }

            return CollectionUtils.isNotEmpty(digiSessionCarouselWidget.getSessionCards()) ? digiSessionCarouselWidget : null;
        }catch (Exception e){
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }

        return null;
    }

    private static DigiSessionCarouselWidget.SessionCard getSessionCard(LiveSessionConfigEntry sessionConfigEntry, boolean isTrialPackExpired) {
        if(Objects.nonNull(sessionConfigEntry) && Objects.nonNull(sessionConfigEntry.getMetaData())){
            LiveSessionConfigEntry.MetaData sessionDetails = sessionConfigEntry.getMetaData();
            DigiSessionCarouselWidget.SessionCard sessionCard = new DigiSessionCarouselWidget.SessionCard();
            sessionCard.setSessionId(sessionConfigEntry.getId());
            sessionCard.setCardBgImg(sessionDetails.getLargeImageUrl());
            sessionCard.setStartTime(sessionConfigEntry.getStartTime().getTime());
            sessionCard.setEndTime(sessionConfigEntry.getEndTime().getTime());
            sessionCard.setSessionTitle(sessionDetails.getSessionName());
            sessionCard.setSessionHostName(sessionDetails.getTrainerName());
            sessionCard.setSessionHostDesignation(sessionDetails.getDesignation());
            sessionCard.setSessionHostImg(sessionDetails.getSmallImageUrl());
            sessionCard.setViewingUsersImg("http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/Frame%202147201235-2025-03-11-18:21.png");
            sessionCard.setViewsText("560+ views");
            sessionCard.setIsCoachSession(sessionDetails.getIsCoachSession());

            sessionCard.setCardImgUrl(sessionDetails.getLargeImageUrl());

            Action joinSessionAction = new Action(ActionType.EXTERNAL_DEEP_LINK, "Join Now");
            joinSessionAction.setUrl(sessionConfigEntry.getRecordingUrl());
            if(isTrialPackExpired){
                joinSessionAction.setActionType(ActionType.OPEN_WEBPAGE);
                Map<String, Object> meta = new HashMap<>();
                meta.put("trialSessionTimeout", 180000L);
                meta.put("modalHeaderTitle", "Your trial pack has expired!\nUpgrade it now.");
                joinSessionAction.setMeta(meta);
            }
            sessionCard.setJoinSessionAction(joinSessionAction);

            return sessionCard;
        }

        return null;
    }



    CompletableFuture<DigiTasksToDoWidget> getDigiTasksToDoWidgetFuture(UserContext userContext, CompletableFuture<UserTaskDaySummary> userTaskDaySummaryFuture, boolean isPackExpired) {
        return supplyAsync(() -> {
            try {
                UserTaskDaySummary userTaskDaySummary = userTaskDaySummaryFuture.get();
                return getDigiTasksToDoWidget(userContext, userTaskDaySummary, isPackExpired);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getDigiTasksToDoWidgetFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    DigiTasksToDoWidget getDigiTasksToDoWidget(UserContext userContext, UserTaskDaySummary userTaskDaySummary, boolean isPackExpired) {
        DigiTasksToDoWidget digiTasksToDoWidget = new DigiTasksToDoWidget();
        Action seeMoreAction = Action.builder().title("View all tasks").actionType(ActionType.NAVIGATION).url("curefit://sfdigitaskstodopage?index=0").build();
        List<DigiTasksToDoCard> cards = digiTasksToDoCardsBuilder.buildCards(userContext, userTaskDaySummary, isPackExpired, true);
        if (CollectionUtils.isEmpty(userTaskDaySummary.getPendingTasks())
                && CollectionUtils.isEmpty(userTaskDaySummary.getCompletedTasks())) {
            return null;
        }
        digiTasksToDoWidget.setCards(cards);
//        boolean fbvCardPresent = cards.stream().anyMatch(card -> card.getUserTodoTaskType().equals(UserTaskType.FBV));
//        boolean dietCardPresent = cards.stream().anyMatch(card -> card.getUserTodoTaskType().equals(UserTaskType.DIET_PLAN));
//        boolean fbvCardCompleted = cards.stream().anyMatch(card -> card.getUserTodoTaskType().equals(UserTaskType.FBV) && Boolean.TRUE.equals(card.getCompleted()));
//        boolean dietCardCompleted = cards.stream().anyMatch(card -> card.getUserTodoTaskType().equals(UserTaskType.DIET_PLAN) && Boolean.TRUE.equals(card.getCompleted()));
        digiTasksToDoWidget.setTotal((long) cards.size());
        digiTasksToDoWidget.setCompleted(cards.stream().filter(t -> Boolean.TRUE.equals(t.getCompleted())).count());
        digiTasksToDoWidget.setSeeMoreAction(seeMoreAction);
        digiTasksToDoWidget.setNumberOfCardsToShow(5L);
        return digiTasksToDoWidget;
    }

    CompletableFuture<DigiAskRagusFreeQuestionWidget> getDigiAskRagusFreeQuestionWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getDigiAskRagusFreeQuestionWidget(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getDigiAskRagusFreeQuestionWidgetFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    DigiAskRagusFreeQuestionWidget getDigiAskRagusFreeQuestionWidget(UserContext userContext){
        DigiAskRagusFreeQuestionWidget digiAskRagusFreeQuestionWidget = new DigiAskRagusFreeQuestionWidget();
        digiAskRagusFreeQuestionWidget.setTitle("Ask a");
        digiAskRagusFreeQuestionWidget.setBoldTitle(" Free Question");
        digiAskRagusFreeQuestionWidget.setTypeWriterText("Ask a question with Ragus, your AI Diabetes expert");
        digiAskRagusFreeQuestionWidget.setSubmitAction(new Action("curefit://ragusaichatpage", ActionType.NAVIGATION));

        return digiAskRagusFreeQuestionWidget;
    }

    DigiReferralWidget getDigiReferralWidget(){
        DigiReferralWidget digiReferralWidget = new DigiReferralWidget();
        digiReferralWidget.setTitle("Refer &");
        digiReferralWidget.setBoldTitle(" Earn");
        digiReferralWidget.setReferralCreativeUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/referral_creative-2025-02-25-17:06.png");
        digiReferralWidget.setReferralCreativeAction(new Action("curefit://sfreferralpage", ActionType.NAVIGATION));
        return digiReferralWidget;
    }

    DigiQuoteWidget getDigiQuoteWidget(UserContext userContext){
        try {
            DigiQuoteWidget quoteWidget = new DigiQuoteWidget();
            List<String> quotes = appConfigCache.getConfig("SF_HOMEPAGE_QUOTES", new TypeReference<>() {
            }, new ArrayList<>());

            if (CollectionUtils.isEmpty(quotes)) {
                return null;
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
            Date currentTime = Calendar.getInstance(getUserTimezone(userContext)).getTime();
            String redisKey = "SF_QUOTE" + dateFormat.format(currentTime);
            String redisValue = this.cfApiRedisKeyValueStore.get(redisKey);
            if (redisValue == null || redisValue.isEmpty()) {
                int randomQuoteIndex = (int) (Math.random() * (quotes.size() + 1));
                String quote = quotes.get(randomQuoteIndex);
                quoteWidget.setQuote(quote);
                this.cfApiRedisKeyValueStore.set(redisKey, quote, 60 * 60 * 24); // cache quote for one day
            } else {
                quoteWidget.setQuote(redisValue);
            }

            return quoteWidget;
        } catch (Exception e) {
            exceptionReportingService.reportException("Exception in getDigiQuoteWidget", e);
            return null;
        }
    }

    private CompletableFuture<HomeConnectYourDeviceWidget> getHomeConnectYourDeviceWidgetFuture(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus) {
        return supplyAsync(() -> {
            try {
                return getHomeConnectYourDeviceWidget(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getHomeProgressWidgetFuture", e);
                log.error("Error in getting diabetes care hub widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<OnboardingJourneyWidget> getOnboardingJourneyWidgetFuture(ActivePackResponse activePackResponse) {
        return supplyAsync(() -> {
            try {
                return getOnboardingJourneyWidget(activePackResponse);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getOnboardingJourneyWidget", e);
                log.error("Error in getting onboarding journey widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public OnboardingJourneyWidget getOnboardingJourneyWidget(ActivePackResponse activePackResponse) throws Exception {
        return getOnboardingJourney(chronicCareServiceHelper, activePackResponse);
    }

    public HomeConnectYourDeviceWidget getHomeConnectYourDeviceWidget(UserContext userContext) throws Exception {
        return getHomeConnectYourDevice(userContext,chronicCareServiceHelper);
    }

    public DigiSpacerWidget getHomePageDividerWidget() {
        DigiSpacerWidget spacerWidget = new DigiSpacerWidget();
        spacerWidget.setBackgroundColor(HOME_DIVIDER_COLOR);
        return spacerWidget;
    }

    private boolean hasNonDigitalActivePacksAvailable(NUXStatusResponse response) {
        if (response.getActivePack() != null || response.getUpcomingPack() != null || response.getExpiredPack() != null) {
            NUXStatusResponse.NUXPackResponse activePack = response.getActivePack();
            NUXStatusResponse.NUXPackResponse upcomingPack = response.getUpcomingPack();
            NUXStatusResponse.NUXPackResponse expiredPack = response.getExpiredPack();
            NUXStatusResponse.NUXPackResponse givenPack = Objects.nonNull(activePack)  ? activePack : Objects.nonNull(upcomingPack) ? upcomingPack : expiredPack;
            return Objects.nonNull(givenPack) && !DigitalAppUtil.isDigitalAppUser(givenPack.getProductCode(), false);
        }
        return false;
    }

}
