package com.curefit.cfapi.view.viewbuilders.chroniccare.digitalapp;

import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ChronicCareAppUtil;
import com.curefit.cfapi.util.DigitalAppUtil;
import com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp.DALessonsJourneyPageView;
import com.curefit.commons.client.exception.HttpException;
import com.sugarfit.catalog.client.CatalogClient;
import com.sugarfit.catalog.pojo.BookComposition;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.TimeZone;

@Getter
@Setter
@Slf4j
@Component
@RequiredArgsConstructor
public class DALessonsJourneyPageViewBuilder {

    final CatalogClient catalogClient;
    final ServiceInterfaces serviceInterfaces;

    public DALessonsJourneyPageView buildView(UserContext userContext) throws HttpException {
        DALessonsJourneyPageView page = new DALessonsJourneyPageView();
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            List<BookComposition> allUserBooks = catalogClient.getAllBookForUserId(userId, appTenant, timeZone);
            page.setAllUserBooks(allUserBooks);
            Action bookAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://digibookpage?appendedAppside=true").build();
            Action videoAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://singlevideoplayer?appendedAppside=true").build();
            if (ChronicCareAppUtil.isInternationalSugarfitUser(userContext) && DigitalAppUtil.isDigitalUsaSupportedVersion(userContext)) {
                videoAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfdigiinteractivevideopage?appendedAppside=true").build();
            }
            page.setBookAction(bookAction);
            page.setVideoAction(videoAction);
        } catch (Exception e){
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return page;
    }
}
