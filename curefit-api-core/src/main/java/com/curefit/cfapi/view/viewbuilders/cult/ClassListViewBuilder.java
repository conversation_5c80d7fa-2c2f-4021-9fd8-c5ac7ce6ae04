package com.curefit.cfapi.view.viewbuilders.cult;

import com.curefit.center.dtos.CenterEntry;
import com.curefit.cfapi.builder.vm.BuildWidgetResponse;
import com.curefit.cfapi.builder.vm.WidgetBuilder;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.cult.*;
import com.curefit.cfapi.model.internal.exception.CenterNotSelectedException;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.ActionMeta;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.common.ClassState;
import com.curefit.cfapi.pojo.cult.*;
import com.curefit.cfapi.pojo.common.ClassViewWaitlistInfo;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGame;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.cult.CultBusiness;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.view.viewmodels.cult.*;
import com.curefit.cfapi.widgets.FreeTextWidget;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.banner.BannerCarouselWidget;
import com.curefit.cfapi.pojo.common.Label;
import com.curefit.cfapi.widgets.fitness.BasicProgressCardWidget;
import com.curefit.cfapi.widgets.fitness.HabitGameProgressWidget;
import com.curefit.cfapi.widgets.fitso.NoshowBookingBlockedWidget;
import com.curefit.cfapi.widgets.hometab.InAppNotificationWidget;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.cult.models.*;
import com.curefit.cult.models.responses.CultBookingWaitlistResponse;
import com.curefit.diyfs.pojo.LiveClass;
import com.curefit.diyfs.pojo.LiveClassSlot;
import com.curefit.diyfs.pojo.enums.LiveFitWorkoutFormat;
import com.curefit.location.models.LatLong;
import com.curefit.location.service.PincodePlaceDetailsService;
import com.curefit.logging.models.ActivityStoreAttribute;
import com.curefit.logging.models.ActivityTypeDS;
import com.curefit.logging.models.request.ActivityStoreAttributeSearchRequest;
import com.curefit.logging.models.request.DateQueryRange;
import com.curefit.logging.models.request.SortField;
import com.curefit.membership.pojo.entry.Benefit;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.pojo.entry.Attribute;
import com.curefit.alfred.enums.OrderSource;
import com.curefit.product.enums.ProductType;


import com.curefit.userservice.pojo.entry.UserEntry;
import com.curefit.cfapi.pojo.app.action.ActionIcon;
import com.curefit.cfapi.pojo.app.action.Action;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.curefit.cfapi.config.CircuitBreakerConfiguration.CircuitBreakerName.CULT_SERVICE;
import static com.curefit.cfapi.constants.Constants.TEST_USERS_SEGMENT_FOR_TEST_CENTERS;
import static com.curefit.cfapi.util.AppUtil.TRIAL_INFO_PAGE_POP_UP_COUNT_KEY_GX;
import static com.curefit.cfapi.util.LiveUtil.INTERACTIVE_ICON_URL;

@Component
@Slf4j
public class ClassListViewBuilder {

    private final ServiceInterfaces serviceInterfaces;

    private final PincodePlaceDetailsService pincodePlaceDetailsService;
    private final RollbarService rollbarService;
    private final CultBusiness cultBusiness;
    private final WidgetBuilder widgetBuilder;

    // Cache for whitelisted center IDs to avoid repeated fetches
    private List<String> testCenterIds;

    public static final String ONBOARDING_RECOMMENDED_LEVEL_ATTRIBUTE = "onboarding_recommended_level";
    public static final Integer DEFAULT_MAX_EXPANDED_CENTERS = 3;
    private static final String HOME_CENTER_ID = "-5";
    private static final String HOME_CENTER_ID_MIND = "-6";
    private static final String HOME_CENTER_ID_EAT = "-7";

    private static final Map<Integer, Integer> FILTERS_PRIORITY_MAP = new HashMap<>();
    private static final List<Integer> CULT_SPORT_CATEGORY_STAGE_WORKOUT_CATEGORY = List.of(69);
    private static final List<Integer> CULT_SPORT_CATEGORY_PROD_WORKOUT_CATEGORY = List.of(65);
    private static final List<Integer> BOOTCAMP_CATEGORY_STAGE_WORKOUT_CATEGORY = List.of(77);
    private static final List<Integer> BOOTCAMP_CATEGORY_PROD_WORKOUT_CATEGORY = List.of(77);

    private static final int MAX_SORT_PREFERENCE = 1000;

    private static final String CULT_AREA_TOOL_TIP = "cult_area_tool_tip";

    public static final Map<Integer, String> CLASS_SCHEDULE_LEVEL_WIDGET_ID_MAP = new HashMap<>();

    AppConfigCache appConfigCache;

    static {
        CLASS_SCHEDULE_LEVEL_WIDGET_ID_MAP.put(1, "18469940-e571-4fff-b168-a2e63aa2065a_l1");
        CLASS_SCHEDULE_LEVEL_WIDGET_ID_MAP.put(2, "18469940-e571-4fff-b168-a2e63aa2065a_l2");
    }

    static {
        // Order :: Dance, Strength, Burn, Yoga, Boxing
        FILTERS_PRIORITY_MAP.put(56, 1); // Dance
        FILTERS_PRIORITY_MAP.put(69, 2); // Strength
        FILTERS_PRIORITY_MAP.put(37, 2); // HRX
        FILTERS_PRIORITY_MAP.put(9, 3); // s & c
        FILTERS_PRIORITY_MAP.put(66, 4); // Burn
        FILTERS_PRIORITY_MAP.put(5, 5); // Yoga
        FILTERS_PRIORITY_MAP.put(8, 6); // Boxing
        FILTERS_PRIORITY_MAP.put(0, 100); // Default
    }

    @Autowired
    public ClassListViewBuilder(
            ServiceInterfaces serviceInterfaces,
            RollbarService rollbarService,
            CultBusiness cultBusiness,
            WidgetBuilder widgetBuilder,
            PincodePlaceDetailsService pincodePlaceDetailsService,
            AppConfigCache appConfigCache
    ) {
        this.serviceInterfaces = serviceInterfaces;
        this.rollbarService = rollbarService;
        this.cultBusiness = cultBusiness;
        this.widgetBuilder = widgetBuilder;
        this.pincodePlaceDetailsService = pincodePlaceDetailsService;
        this.appConfigCache = appConfigCache;
    }


    public ClassListView buildView(
            UserContext userContext,
            ProductType productType,
            CultClassesScheduleResponse classesResponse,
            Boolean centerViewOnly,
            Boolean filterApplied,
            CultBookingWaitlistResponse rescheduleClassBookingResponse,
            OrderSource orderSource,
            List<SquadClassBookingLite> squadData,
            String cultCityName,
            String filterWorkoutId,
            Boolean isCultUnbound,
            Boolean isPilateGymPage,
            Map<String, String> queryParams
    ) throws Exception {
        List<Integer> favouriteCenters = null;
        Map<String, Object> settingMap = new HashMap<>();
        if (classesResponse.getUserSettings() != null && !classesResponse.getUserSettings().isEmpty()) {
            settingMap = classesResponse.getUserSettings().stream()
                    .collect(Collectors.toMap(PreferenceSetting::getKey, PreferenceSetting::getValue));

            if (settingMap.containsKey("USER_BOOKING_V2_FAVOURITE_CENTER")) {
                favouriteCenters = (List<Integer>) settingMap.get("USER_BOOKING_V2_FAVOURITE_CENTER");
            }
        }
        Map<String, ClassByDateV2> classByDateMap = new HashMap<>();
        Map<String, ClassByTimeV2> classByDateTimeMap = new HashMap<>();
        Map<String, ClassByCenter> classByDateTimeCenterMap = new HashMap<>();
        Map<Long, Integer> centerPositionByIdMap = new HashMap<>();
//        Map<Long, CultCenter> centerInfoByIdMap = new HashMap<>();
        NoshowBookingBlockedWidget dayWiseWidget = null;
        if (classesResponse.getNoShowDetails() != null) {
            dayWiseWidget = getNoShowBookingBlockedWidget(classesResponse.getNoShowDetails(), userContext);
        }

        // removed international app check
        List<String> classDates = classesResponse.getDates();

        for (String date : classDates) {
            ClassByDateV2 classByDateV2 = new ClassByDateV2();
            classByDateV2.setId(date);
            classByDateV2.setClassByTimeList(new ArrayList<>());
            if (classesResponse.getNoShowDetails() != null && classesResponse.getNoShowDetails().getBookingBlockedDates().contains(date)) {
                classByDateV2.setDayWiseWidget(dayWiseWidget);
            }
            classByDateMap.put(date, classByDateV2);
        }

        for (int i = 0; i < classesResponse.getCenters().size(); i++) {
            CultCenter center = classesResponse.getCenters().get(i);
            centerPositionByIdMap.put(center.getId(), i);
//            centerInfoByIdMap.put(center.getId(), center);
        }




        boolean showSportCategoryInfoWidget = false;

        String cityId = userContext.getUserProfile().getCity() != null ? userContext.getUserProfile().getCity().getCityId() : cultCityName;
        // TODO: update cult schedule response to include location preference
        LocationPreference locationPreference = serviceInterfaces.localityProviderService.getUserLocationPreference(serviceInterfaces, userContext, cityId, userContext.getUserProfile().getUserId());

        CultSummary cultAndMindSummary = (CultSummary) userContext.getRequestCache().getRequestFuture(RequestType.CULT_MIND_SUMMARY, userContext).get();

        Map<Long, Workout> workoutMap = getWorkoutMap(classesResponse.getWorkouts());

        Map<String, List<UserEntry>> squadsDataByClass = processSquadData(userContext, squadData);

        Boolean showBootcampToolTip = TransformUtil.isBootcampToolTipVisible(userContext.getUserProfile().getUserId(), serviceInterfaces.defaultRedisKeyValueStore);

        boolean workoutLevelToolTip = false;
        Integer recommendationLevel = null;
        ClassListView scheduleResponse = new ClassListView();
        List<Membership> currentFitnessMemberships = FitnessCenterUtil.getCurrentFitnessMemberships(userContext, serviceInterfaces, false, false);
        boolean isUserHavingAnyCultMembership = CollectionUtils.isNotEmpty(MembershipUtil.getCultMembershipsOnly(currentFitnessMemberships));
        boolean isTrialFlowEnabled = AppUtil.isTrialInfoPageFlow(userContext, serviceInterfaces);
        if ((!isUserHavingAnyCultMembership && isTrialFlowEnabled && !isPilateGymPage) || AppUtil.doesUserBelongToM1RecommendationSegment(userContext, serviceInterfaces.segmentEvaluatorService, serviceInterfaces.environmentService)) {
            try {
                if (!isUserHavingAnyCultMembership) {
                    scheduleResponse.setAutoSheetOpen(queryParams.containsKey("autoScrollIndex"));
                    scheduleResponse.setAutoScrollIndex(queryParams.containsKey("autoScrollIndex") ? Integer.parseInt(queryParams.get("autoScrollIndex")) : 0);
                    List<CompletableFuture<BaseWidget>> widgetList = AppUtil.widgetCompletableFuture(serviceInterfaces, userContext, "eda44599-45b9-4a32-b421-3fca2a9f4a0e");
                    if (CollectionUtils.isNotEmpty(widgetList)) {
                        FreeTextWidget sneakPeakExpandedWidget = (FreeTextWidget) widgetList.get(0).get();
                        scheduleResponse.setSneakPeakExpandedWidgets(sneakPeakExpandedWidget.getJsonObject().get("sneakPeakExpandedWidgets"));
                    }
                }

                if (Objects.isNull(scheduleResponse.getSneakPeakExpandedWidgets())) {
                    scheduleResponse.setSneakPeakExpandedWidgets(CultUtil.getSneakPeakExpandedWidgets());
                }

                recommendationLevel = CultUtil.getUserRecommendationLevel(userContext.getUserProfile().getUserId(), serviceInterfaces.userAttributesClient);
                scheduleResponse.setShowSneakPeak(true);
                scheduleResponse.setSneakPeakCollapsedWidgets(CultUtil.getSneakPeakCollapsedWidgets());
            } catch (Exception error) {
                log.error("Error in showing sneak peak", error);
            }
        }
        log.debug("recommendationTest:- recommendationLevel: {}", recommendationLevel);

        boolean isTestUser = AppUtil.isUserPartOfTestSegment(RequestType.PLATFORM_SEGMENTS, userContext);
        List<Long> centerWhitelistedForTestUsers = AppUtil.testCenterIdsList(serviceInterfaces);
        // Using the cached testCenterIds that was initialized when the bean was created
        Map<String, String> centerIdToServiceId = classesResponse.getCenters().stream()
                .collect(Collectors.toMap(center -> center.getId().toString(), CultCenter::getCenterServiceId));

        for (CultClass classDetail : classesResponse.getClasses()) {
            String centerID = classDetail.getCenterID();
            String centerServiceId = centerIdToServiceId.get(centerID);
            if (!isTestUser) {
                if (centerWhitelistedForTestUsers.contains(Long.valueOf(centerServiceId))) {
                    continue;
                }
            }
            List<Long> baddyFitWorkout = CultUtil.getBaddyFitWorkoutIds(serviceInterfaces.environmentService);
            boolean isBaddyFitWorkout = baddyFitWorkout.contains(classDetail.getWorkoutID());
            if (isBaddyFitWorkout) {
                continue;
            }

            List<Long> bootcampWorkout = CultUtil.getBootcampWorkoutIds(serviceInterfaces.environmentService);
            boolean isBootcampWorkout = bootcampWorkout.contains(classDetail.getWorkoutID());
            if (isBootcampWorkout) {
                continue;
            }

            ClassByDateV2 classByDate = classByDateMap.get(classDetail.getDate());
            if (classByDate != null) {
                String dateTimeKey = classDetail.getDate() + "_" + classDetail.getStartTime();
                ClassByTimeV2 classByTime = classByDateTimeMap.get(dateTimeKey);
                if (classByTime == null) {
                    classByTime = new ClassByTimeV2();
                    classByTime.setId(classDetail.getStartTime());
                    classByTime.setBookedCenterPosition(-1);
                    classByTime.setUniqueFavouriteCenters(new HashSet<>());
                    classByTime.setMaxExpandedCenters(DEFAULT_MAX_EXPANDED_CENTERS);
                    classByTime.setCenterWiseClasses(new ArrayList<>());
                    classByTime.setIsLive(false);
                    classByDateTimeMap.put(dateTimeKey, classByTime);
                    classByDate.getClassByTimeList().add(classByTime);
                }
                int centerId = Integer.parseInt(classDetail.getCenterID());
                if (favouriteCenters != null && favouriteCenters.contains(centerId)) {
                    classByTime.getUniqueFavouriteCenters().add(centerId);
                }
                String centerKey = dateTimeKey + "_" + centerId;
                ClassByCenter classByCenter = classByDateTimeCenterMap.get(centerKey);
                if (classByCenter == null) {
                    classByCenter = new ClassByCenter();
                    classByCenter.setCenterId(centerId);
                    classByCenter.setClasses(new ArrayList<>());
                    classByCenter.setDistance(null);
                    classByDateTimeCenterMap.put(centerKey, classByCenter);
                    classByTime.getCenterWiseClasses().add(classByCenter);

//                    if (locationPreference != null) {
//                        LocationPreferenceType locationType = locationPreference.getPreferredLocationType();
//                        if (locationType != null && (locationType.equals(LocationPreferenceType.coordinates) || locationType.equals(LocationPreferenceType.current_location))) {
//                            CultCenter currentCenter = centerInfoByIdMap.get((long) centerId);
//                            if (currentCenter.getDistance() != null) {
//                                classByCenter.setDistance(String.format("%.1f km", currentCenter.getDistance()));
//                            }
//                        }
//                    }
                    if (classDetail.getCreditCost() != null) {
                        classByCenter.setCreditCost(classDetail.getCreditCost());
                    }

                    classByTime.getCenterWiseClasses().sort(Comparator.comparingInt(a -> centerPositionByIdMap.get((long) a.getCenterId())));
                }

                boolean showWorkoutLevelToolTip = false;
                try {
                    if ((Objects.equals(classDetail.getWorkoutID(), CultUtil.DANCE_FITNESS_REVAMP_WORKOUT_ID) || Objects.equals(classDetail.getWorkoutID(), CultUtil.STRENGTH_REVAMP_WORKOUT_ID)) && !workoutLevelToolTip) {
                        workoutLevelToolTip = true;
                        showWorkoutLevelToolTip = CultUtil.isCultFormatImpressionCompleted(userContext.getUserProfile().getUserId(), classDetail.getWorkoutID(), serviceInterfaces.defaultRedisKeyValueStore)
                                && CultUtil.checkStateOfCultFormatImpressionCount(userContext.getUserProfile().getUserId(), classDetail.getWorkoutID(), serviceInterfaces.defaultRedisKeyValueStore);
                        if (showWorkoutLevelToolTip) {
                            CultUtil.increaseCultFormatImpressionCount(userContext.getUserProfile().getUserId(), classDetail.getWorkoutID(), serviceInterfaces.defaultRedisKeyValueStore);
                        }
                    }
                } catch (Exception error) {
                    log.error("Error while forming new dance fitness/strength tool tip", error);
                    rollbarService.error(error, "Error while forming new dance fitness/strength tool tip");
                }

                int classWorkoutLevel = CultUtil.getWorkoutRecommendationLevel(workoutMap.get(classDetail.getWorkoutID()).getName());
                Boolean isClassRecommended = null;
                if (recommendationLevel != null) {
                    isClassRecommended = recommendationLevel.equals(classWorkoutLevel);
                }
                log.debug("recommendationTest:- recommendationLevel: {}, isClassRecommended: {}, classWorkoutLevel: {}, workoutId: {}, workoutMap: {}", recommendationLevel, isClassRecommended, classWorkoutLevel, classDetail.getWorkoutID(), workoutMap);

                // boolean getWaitlistCnfProbabilityColor = AppUtil.isAppNewWaitlistColorCodingSupported(userContext, serviceInterfaces.segmentEvaluatorService, serviceInterfaces.environmentService);
                log.debug("classDetails {}", classDetail);
                // need to check once

                classByCenter.getClasses().add(
                        getClassView(
                                classDetail,
                                workoutMap,
                                squadsDataByClass,
                                showBootcampToolTip,
                                showWorkoutLevelToolTip,
                                isClassRecommended,
                                null,
                                userContext,
                                serviceInterfaces)
                );
                if (classDetail.getWlConfirmationThreshold() != null && classDetail.getIsWaitlistAvailable()) {
                    classByDate.setWlOnboarding(true);
                }

                if (!showSportCategoryInfoWidget && BooleanUtils.isTrue(classByCenter.getClasses().getLast().getIsNewClass())) {
                    showSportCategoryInfoWidget = true;
                }

                // Logic for calculating maxExpandedCenters
                // Case 1: Area/Near Me view -> always show DEFAULT_MAX_EXPANDED_CENTERS in expanded state
                // Case 2: Favourites View -> Always show all centers as expanded in favourites view
                // Note: booked class slot should always be in expanded state

                // Get the booked class slot center position in center ranking map, so that we can expand the slots upto booked center position
                if (classDetail.getBookingNumber() != null) {
                    classByTime.setBookedCenterPosition(centerPositionByIdMap.get((long) centerId));
                }

                // To show booked class center in expanded state, expand the timeslot upto bookedCenterPosition if bookedCenterPosition is greater than maxExpandedCenters
                classByTime.setMaxExpandedCenters(
                        Stream.of(
                                classByTime.getUniqueFavouriteCenters().size(),
                                classByTime.getMaxExpandedCenters(),
                                classByTime.getBookedCenterPosition() + 1
                        ).max(Integer::compare).orElse(DEFAULT_MAX_EXPANDED_CENTERS)
                );
            }
        }

//        Map<String, CultClassRecommendationWidgetView> cultRecommendationByDateMap = null;
//        if (cultClassRecommendationDataPromise != null) {
//            cultRecommendationByDateMap = (Map<String, CultClassRecommendationWidgetView>) cultClassRecommendationDataPromise;
//        }
//        boolean isCultClassRecommendationAvailable = false;
        String tz = userContext.getUserProfile().getTimezone();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(TimeZone.getTimeZone(tz));
        String currentDate = sdf.format(new Date());
        sdf.applyPattern("HH");
        String currentHour = sdf.format(new Date());
        sdf.applyPattern("mm");
        String currentMin = sdf.format(new Date());
//        Object bookingScreenNudges = null;
//        if (bookingScreenNudgeEternalPromise != null) {
//            bookingScreenNudges = bookingScreenNudgeEternalPromise;
//        }
        for (String date : classDates) {
            ClassByDateV2 classByDate = classByDateMap.get(date);
            List<ClassByTimeV2> classesByTime = classByDate.getClassByTimeList();
            List<LiveFitWorkoutFormat> formats = classByDate.getFormats();
            if (classByDate.getTimeSlots() == null) {
                classByDate.setTimeSlots(new ArrayList<>());
            }
            List<String> timeSlots = classByDate.getTimeSlots();
            classesByTime.sort(Comparator.comparing(ClassByTimeV2::getId));

            if (formats != null) {
                classByDate.setFormats(formats.stream().distinct().collect(Collectors.toList()));
            }

            if (!classesByTime.isEmpty() && classesByTime.getFirst() != null) {
                int classStartTime = Integer.parseInt(classesByTime.getFirst().getId().split(":")[0]);
                boolean isHalfAnHourSlot = classStartTime >= 13;
                for (int i = classStartTime; i < 24; i++) {
                    timeSlots.add(String.format("%02d:00:00", i));
                    if (isHalfAnHourSlot) {
                        timeSlots.add(String.format("%02d:30:00", i));
                    }
                }
                if (date.equals(currentDate)) {
                    for (ClassByTimeV2 classPerSlot : classesByTime) {
                        String[] classTime = classPerSlot.getId().split(":");
                        if (Integer.parseInt(classTime[0]) < Integer.parseInt(currentHour) || (classTime[0].equals(currentHour) && Integer.parseInt(classTime[1]) <= Integer.parseInt(currentMin))) {
                            classPerSlot.setIsLive(true);
                        }
                    }
                }
            }
            if (BooleanUtils.isTrue(classByDateMap.get(date).getWlOnboarding())) {
                classByDateMap.get(date).setWlOnboardingText("Swipe left to see how likely you are to get a confirmed waitlist");
                classByDateMap.get(date).setWlOnboardingIcon("/image/icons/cult/pointed-finger.png");
            }
        }

        String centerName = null;
        if (centerViewOnly && !classesResponse.getCenters().isEmpty()) {
            centerName = classesResponse.getCenters().getFirst().getName() + (classesResponse.getCenters().size() > 1 ? " + " + (classesResponse.getCenters().size() - 1) + " more" : "");
        }

        Map<String, Object> shutdownScheduleDetailsPromise = cultBusiness.getCenterUnderMaintenance(userContext, classesResponse.getShutdownSchedule(), true, serviceInterfaces);
        ClassListView.ShutdownScheduleDetails shutdownScheduleDetails = (ClassListView.ShutdownScheduleDetails) shutdownScheduleDetailsPromise.get("shutdownScheduleDetails");
        boolean showCenterShutdownCard = (boolean) shutdownScheduleDetailsPromise.get("showCenterShutdownCard");
        boolean showCenterShutdownWarningIcon = (boolean) shutdownScheduleDetailsPromise.get("showCenterShutdownWarningIcon");

        List<CenterScheduleDetailCard> centerShutdownDetailCards = (List<CenterScheduleDetailCard>) shutdownScheduleDetailsPromise.get("centerShutdownDetailCards");

        String headerTitle = null;
        if (AppUtil.isTVApp(orderSource.toString())) {
            headerTitle = "Class Schedule";
        } else if (AuthUtil.isGuestUser(userContext) && (classesResponse.getUserSettings() == null || locationPreference!= null && locationPreference.getPreferredLocationType() == null)) {
            headerTitle = GymUtil.getDefaultLocality(cityId, serviceInterfaces);
        } else if (centerName != null && !centerName.isEmpty()) {
            headerTitle = centerName;
        } else {
            ClassListView.SchedulePageHeader header = settingsToSchedulePageHeader(productType, settingMap, showCenterShutdownWarningIcon, centerShutdownDetailCards, filterApplied, locationPreference, userContext);
            scheduleResponse.setHeader(header);
        }

        if (headerTitle != null) {
            scheduleResponse.setHeader(new ClassListView.SchedulePageHeader(headerTitle));
        }
        scheduleResponse.setClassByDateMap(classByDateMap);
        scheduleResponse.setCenterInfoMap(getCenterInfoMap(userContext, classesResponse.getCenters(), productType));
        scheduleResponse.setDays(getAvailableWorkoutDays(userContext, classDates, classByDateMap));
        scheduleResponse.setTimeSlotOptions(getTimeSlotOptions());

        List<ClassListView.WorkoutCategoryFilter> workoutCategoryFilters = getAvailableWorkoutCategoriesFilter(classesResponse.getWorkoutCategories(), true, false);
        scheduleResponse.setWorkoutCategoryFilters(workoutCategoryFilters);
        scheduleResponse.setLiveClassFormatFilter(new ArrayList<>());
        scheduleResponse.setNudgeDetails(new ArrayList<>());

        // build notification widget
        CultSchedulePageNotificationParams builderParams = new CultSchedulePageNotificationParams();
        builderParams.setAnnouncementService(serviceInterfaces.announcementService);
        builderParams.setCultService(serviceInterfaces.cultService);
        builderParams.setShutdownNotificationData(showCenterShutdownCard ? shutdownScheduleDetails : null);
        builderParams.setFitClubNotificationData(null);
        builderParams.setUserContext(userContext);
        builderParams.setCenterNotificationResponse(classesResponse.getCenterNotificationResponse());
        InAppNotificationWidget notificationWidget = new InAppNotificationWidget();
        scheduleResponse.setNotificationWidget(notificationWidget.buildViewForCultBookingPage(builderParams));

        if (scheduleResponse.getDays().isEmpty() || classesResponse.getClasses().isEmpty()) {
            if (classesResponse.getShutdownSchedule() != null && !classesResponse.getShutdownSchedule().isEmpty() && AppUtil.isCenterShutdownSupported(userContext)) {
                scheduleResponse.setMessage("Centres in your selection are temporarily not operational. Please select another centre.");
            } else {
                scheduleResponse.setMessage("Classes aren't yet scheduled for this date. Do stay tuned.");
            }
        }
        scheduleResponse.setEmptyClassesMessage("Classes aren't yet scheduled for this date. Do stay tuned.");

        if (rescheduleClassBookingResponse != null) {
            CultClass rescheduleClassInfo = rescheduleClassBookingResponse.getBooking().getCultClass();
            String classStartTime = rescheduleClassInfo.getFormattedStartTime();
            String classEndTime = rescheduleClassInfo.getFormattedEndTime();

            scheduleResponse.setRescheduleClassInfo(new ClassListView.RescheduleClassInfo(
                    "RESCHEDULING FOR",
                    rescheduleClassInfo.getWorkout().getName(),
                    String.format("%s, %s - %s, %s",
                            TimeUtil.formatDateString(rescheduleClassInfo.getDate(), "yyyy-MM-dd", "EEE dd MMM"),
                            classStartTime, classEndTime, rescheduleClassInfo.getCenter().getName())
            ));
        }
        boolean isCultTrialUser = cultAndMindSummary.getTrialEligibility() != null && cultAndMindSummary.getTrialEligibility().isCult();
        if (isCultTrialUser && !queryParams.containsKey("autoScrollIndex")) {
            scheduleResponse.setUserType(ClassListView.UserType.TRIAL_USER);
        }

        if(!isUserHavingAnyCultMembership && isTrialFlowEnabled && !isPilateGymPage) {
            try {
                UserTrialEligibilityResponse userTrialEligibilityResponse = (UserTrialEligibilityResponse) userContext.getRequestCache().getRequestFuture(RequestType.GET_GX_TRIALS, userContext).get();
                UserTrialEligibility trialEligibility = userTrialEligibilityResponse.getTrialEligibility();
                if (trialEligibility!=null) {
                    int maxTrialCount = trialEligibility.getMaxCount();
                    int usedTrials = trialEligibility.getConsumptionCount();
                    int trialsLeft = maxTrialCount - usedTrials;
                    // TRIAL INFO POP UP
//                    Action trialInfoPageAction = FitnessCenterUtil.getTrialsInfoPageAction(serviceInterfaces, userContext, maxTrialCount, trialsLeft,"GROUP", true);
//                    if (!queryParams.containsKey("workoutId")) {
//                        boolean showTrialInfoPopup = FitnessCenterUtil.showTrialInfoPagePopup(serviceInterfaces, userContext, TRIAL_INFO_PAGE_POP_UP_COUNT_KEY_GX);
//                        if (showTrialInfoPopup) scheduleResponse.setInitAction(trialInfoPageAction);
//                    }
                    scheduleResponse.setTagData(FitnessCenterUtil.getTrialTagData(serviceInterfaces, userContext, maxTrialCount, trialsLeft,"GROUP"));
                }
            } catch (Exception e) {
                log.error("error in userTrialEligibilityResponse ", e);
            }
        }

        String widgetId = serviceInterfaces.environmentService.isProduction() || serviceInterfaces.environmentService.isAlpha() ? CultUtil.CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_PROD : CultUtil.CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_STAGE;
        CompletableFuture<BuildWidgetResponse> widgetResponseFuture = widgetBuilder.buildWidgets(new ArrayList<>(List.of(widgetId)), userContext, new WidgetContext());
        BuildWidgetResponse widgetResponse = widgetResponseFuture.get();
        if (widgetResponse != null && !widgetResponse.getWidgets().isEmpty()) {
            if (widgetResponse.getWidgets().getFirst().getWidgetType().equals(WidgetType.BANNER_CAROUSEL_WIDGET)) {
                BannerCarouselWidget widget = (BannerCarouselWidget) widgetResponse.getWidgets().getFirst();
                widget.setData(widget.getData().stream().map(banner -> {
                    if (banner.getAction().getActionType().equals(ActionType.SHOW_CULT_CENTER_REOPEN_MODAL)) {
                        boolean allCentersShutdown = classesResponse.getCenters().stream().noneMatch(center -> "OPEN".equals(center.getStatus()));
                        boolean noClassPresent = classesResponse.getClasses().isEmpty();
                        if (allCentersShutdown && noClassPresent) {
                            return CultUtil.getCenterReopenBanner(banner, classesResponse.getCenters());
                        } else {
                            return null;
                        }
                    }
                    return banner;
                }).filter(Objects::nonNull).collect(Collectors.toList()));

                if (!widget.getData().isEmpty()) {
                    scheduleResponse.setBannerWidget(widget);
                }
            }
        }

        Membership creditBasedMembership = FitnessCenterUtil.getActiveCreditBasedMembership(currentFitnessMemberships);
        if (creditBasedMembership != null) {
            Benefit creditBenefit = creditBasedMembership.getBenefits().stream().filter(benefit -> "ACCESS_CREDITS".equals(benefit.getName())).findFirst().orElse(null);
            if (creditBenefit != null) {
                int userCreditsLeft = creditBenefit.getMaxTickets() - creditBenefit.getTicketsUsed();
                Action creditDetailAction = new Action();
                creditDetailAction.setActionType(ActionType.NAVIGATION);
                creditDetailAction.setUrl("curefit://credit_history_page?membershipId=" + creditBasedMembership.getId());
                Map<String, Long> analyticsData = new HashMap<>();
                analyticsData.put("creditsLeft", Long.valueOf(userCreditsLeft));
                analyticsData.put("membershipId", creditBasedMembership.getId());
                creditDetailAction.setAnalyticsData(analyticsData);
                creditDetailAction.setViaDeepLink(true);
                Map<String, Boolean> creditDetailActionMeta = new HashMap<>();
                creditDetailActionMeta.put("viaDeeplink", true);
                creditDetailAction.setMeta(creditDetailActionMeta);


                scheduleResponse.setCreditsDetail(new ClassListView.CreditDetail(
                        "CREDIT" + (userCreditsLeft == 1 ? "" : "S"),
                        userCreditsLeft,
                        creditDetailAction
                ));
            }
        }

        Membership selectMembership = FitnessCenterUtil.getActiveCultSelectMembership(currentFitnessMemberships);
        boolean isCultSelectPack = selectMembership != null && selectMembership.getMetadata() != null && !selectMembership.getMetadata().containsKey("jpmcMembership");
        Membership activePROMembership = FitnessCenterUtil.getActiveProMembership(currentFitnessMemberships);
        if (isCultSelectPack && selectMembership.getMetadata().containsKey("cityId") && selectMembership.getMetadata().get("cityId").equals(userContext.getUserProfile().getCity().getCityId())) {
            List<String> selectCenters = selectMembership.getAttributes().stream().filter(a -> "ACCESS_CENTER".equals(a.getAttrKey())).map(Attribute::getAttrValue).toList();
            List<String> scheduleCentersList = classesResponse.getCenters().stream().map(CultCenter::getCenterServiceId).toList();
            boolean isOnlySelectCenterPresent = scheduleCentersList.size() == 1 && selectCenters.contains(scheduleCentersList.getFirst());
            if (!isOnlySelectCenterPresent) {
                try {
                    BannerCarouselWidget ticketBanner = CultUtil.getTicketBanner(selectMembership, userContext, true);
                    if (ticketBanner != null) {
                        scheduleResponse.setBannerWidget(ticketBanner);
                    }
                } catch (Exception e) {
                    log.error("Error while showing Select remaining ticket banner", e);
                }
            }
        } else if (activePROMembership != null) {
            try {
                BannerCarouselWidget ticketBanner = CultUtil.getTicketBanner(activePROMembership, userContext, false);
                if (ticketBanner != null) {
                    scheduleResponse.setBannerWidget(ticketBanner);
                }
            } catch (Exception e) {
                log.error("Error while showing ticket banner", e);
            }
        }

        Membership limitedEliteMembership = FitnessCenterUtil.getActiveLimitedEliteMembership(currentFitnessMemberships);
        boolean isLimitedElitePack = limitedEliteMembership != null;
        if (isLimitedElitePack) {
            scheduleResponse.setSessionsRemainingWidget(getSessionRemainingWidget(limitedEliteMembership, userContext));
        }

        String entity = "booking_v3";
        String state = "coachmark";
        boolean isShown = serviceInterfaces.getFeatureStateCache().match(userContext.getUserProfile().getUserId(), entity, state).get();
        if (!isShown) {
            scheduleResponse.setShowCoachmark(true);
            serviceInterfaces.getFeatureStateCache().set(userContext.getUserProfile().getUserId(), entity, state);
        }

        if (AppUtil.isAppCLPv2Supported(userContext, serviceInterfaces)) {
            if (creditBasedMembership != null) {
                String featureEntity = "credit_onboarding";
                String featureState = "onboarded";
                boolean isFeatureShown = serviceInterfaces.getFeatureStateCache().match(userContext.getUserProfile().getUserId(), featureEntity, featureState).get();
                if (!isFeatureShown) {
                    scheduleResponse.setShowCreditOnboarding(true);
                    serviceInterfaces.getFeatureStateCache().set(userContext.getUserProfile().getUserId(), featureEntity, featureState);
                }
            }
        }

        // hardcoding to remove WL Onboarding
        boolean isWaitlistOnboardingEnabled = false;
        // Logic for onboarding announcement.
        if (isWaitlistOnboardingEnabled) {
            String announcementId = "waitlist_onboarding_1";
            boolean waitlistOnboardingAnnouncement = serviceInterfaces.announcementService.shouldShowAnnouncement(userContext, announcementId, null);
            if (waitlistOnboardingAnnouncement) {
                Action dismissAction = new Action();
                dismissAction.setActionType(ActionType.REST_API);
                dismissAction.setTitle("Got it");
                dismissAction.setMeta(new ActionMeta("/user/announcement/" + announcementId, "POST", Collections.singletonMap("state", "DISMISSED")));

                scheduleResponse.setWaitListAnnouncementAction(dismissAction);
                scheduleResponse.setWlOnboardingTitle("Jump up the queue whenever someone cancels their slot");
                scheduleResponse.setWlOnboardingSubtitle("Filled slots have a waitlist queue.");
                scheduleResponse.setWlOnboardingTooltip("What is waitlist?");
                scheduleResponse.setWlOnboardingAnimation(AppUtil.isNewWaitListProbabilitySupported(userContext) ? null : "/image/cultpass/waitlist_onboarding_6.json");
                scheduleResponse.setWlOnboardingImage("/image/cultpass/waitlist_onboarding_6.png");
            } else {
                String existingUserAnnouncementId = "waitlist_onboarding_2";
                boolean existingUserAnnouncement = serviceInterfaces.announcementService.shouldShowAnnouncement(userContext, existingUserAnnouncementId, null);
                if (existingUserAnnouncement) {

                    Action dismissAction = new Action();
                    dismissAction.setActionType(ActionType.REST_API);
                    dismissAction.setTitle("Got it");
                    dismissAction.setMeta(new ActionMeta("/user/announcement/" + existingUserAnnouncementId, "POST", Collections.singletonMap("state", "DISMISSED")));

                    scheduleResponse.setWaitListAnnouncementAction(dismissAction);
                    scheduleResponse.setWlOnboardingTooltip("What is waitlist?");
                    scheduleResponse.setWlOnboardingTitle("Jump up the queue whenever someone cancels their slot");
                    scheduleResponse.setWlOnboardingSubtitle("Filled slots have a waitlist queue.");
                    scheduleResponse.setWlOnboardingAnimation(AppUtil.isNewWaitListProbabilitySupported(userContext) ? null : "/image/cultpass/waitlist_onboarding_6.json");
                    scheduleResponse.setWlOnboardingImage("/image/cultpass/waitlist_onboarding_6.png");
                    scheduleResponse.setWlShowBottomSheet(true);
                }
            }
        }

        if (AppUtil.isAppNewWaitlistColorCodingSupported(userContext, serviceInterfaces.getSegmentEvaluatorService(), serviceInterfaces.environmentService)) {
            String waitlistOnboardingEntity = "waitlist_onboarding_v2";
            String onboardingState = "onboarded";
            boolean isOnboardingShown = serviceInterfaces.getFeatureStateCache().match(userContext.getUserProfile().getUserId(), waitlistOnboardingEntity, onboardingState).get();
            if (!isOnboardingShown) {


                ActivityStoreAttributeSearchRequest activityStoreAttributeSearchRequest = new ActivityStoreAttributeSearchRequest();
                activityStoreAttributeSearchRequest.setUserId(Collections.singletonList(userContext.getUserProfile().getUserId()));
//                activityStoreAttributeSearchRequest.setScore({"$gte": 1});  // TODO: need to check
                activityStoreAttributeSearchRequest.setActivityType(ActivityTypeDS.CULT_CLASS);

                DateQueryRange dateQueryRange = new DateQueryRange();
                dateQueryRange.setLte(currentDate);
                activityStoreAttributeSearchRequest.setDateRange(dateQueryRange);

                SortField sortField = new SortField();
                sortField.setField("date");
                sortField.setOrder(-1); // DESC
                activityStoreAttributeSearchRequest.setSortFields(Collections.singletonList(sortField));
                activityStoreAttributeSearchRequest.setLimit(1);

                List<ActivityStoreAttribute> activityStoreAttributeEntries = serviceInterfaces.loggingService.getActivityStoreAttribute(activityStoreAttributeSearchRequest);

                ActivityStoreAttribute lastAttendedSession = activityStoreAttributeEntries.isEmpty() ? null : activityStoreAttributeEntries.getFirst();
                String title = "Important update";
                String description = "Your waitlist experience just got better";
                if (lastAttendedSession == null) {
                    title = null;
                    description = "Jump up the queue whenever someone cancels their slot";
                }
                serviceInterfaces.getFeatureStateCache().set(userContext.getUserProfile().getUserId(), waitlistOnboardingEntity, onboardingState);
                Action waitListOnboardingAction = getWaitListOnboardingAction(title, description);
                scheduleResponse.setWaitListOnboardingAction(waitListOnboardingAction);
            }
        }

        Map<String, String> infoWidget = getInfoWidget(classesResponse.getWorkouts(), showSportCategoryInfoWidget);
        if (infoWidget != null) {
            scheduleResponse.setInfoWidget(infoWidget);
        }

        if (recommendationLevel != null && CLASS_SCHEDULE_LEVEL_WIDGET_ID_MAP.containsKey(recommendationLevel)) {
            String recommendationLevelWidgetId = CLASS_SCHEDULE_LEVEL_WIDGET_ID_MAP.get(recommendationLevel);
            CompletableFuture<BuildWidgetResponse> recommendationLevelWidgetFuture = widgetBuilder.buildWidgets(Collections.singletonList(recommendationLevelWidgetId), userContext, new WidgetContext());
            BuildWidgetResponse recommendationLevelWidgetResponse = recommendationLevelWidgetFuture.get();
            log.debug("recommendationTest:: widgetId: {}, widgetResponse: {}, recommendationLevel: {}", widgetId, recommendationLevelWidgetResponse, recommendationLevel);
            if (recommendationLevelWidgetResponse != null && !recommendationLevelWidgetResponse.getWidgets().isEmpty()) {
                scheduleResponse.setBannerWidget((BannerCarouselWidget) recommendationLevelWidgetResponse.getWidgets().getFirst());
            }
        }

        if (StringUtils.isNotBlank(filterWorkoutId)) {
            scheduleResponse.setLocalitySelectionAction( new Action("curefit://localityselector?pageFromType=CLASS_BOOKING&filterWorkoutId=" + filterWorkoutId, ActionType.NAVIGATION));
        } else if (isCultUnbound) {
            scheduleResponse.setLocalitySelectionAction( new Action("curefit://localityselector?pageFromType=CLASS_BOOKING&isCultUnbound=" + isCultUnbound, ActionType.NAVIGATION));
        }

        if (userContext.getSessionInfo().getAppVersion() >= 10.98f) {
            EnergyStreakGame userGame = serviceInterfaces.userService.getUsersGame(userContext, null, serviceInterfaces);
            if (userGame != null) {
                HabitGameProgressWidget habitGameProgressWidget = new HabitGameProgressWidget();
                WidgetContext newWidgetContext = new WidgetContext();
                Map<String,String> newQueryParams = new HashMap<>();
                if (userGame.getIsMultiPlayer() != null) newQueryParams.put("isMultiplayerGame",userGame.getIsMultiPlayer().toString());
                newQueryParams.put("isSchedulePage","true");
                newWidgetContext.setQueryParams(newQueryParams);
                habitGameProgressWidget.buildView(serviceInterfaces, userContext, newWidgetContext);
                scheduleResponse.setBannerWidget(habitGameProgressWidget);
            }
        }

        if (isCultUnbound) {
            String unboundWidgetId = CultUtil.CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_UNBOUND;
            String unboundBannerId = CultUtil.CLASS_SCHEDULE_PAGE_BANNER_ID_UNBOUND;
            CompletableFuture<BuildWidgetResponse> unboundWidgetResponseFuture = widgetBuilder.buildWidgets(new ArrayList<>(List.of(unboundWidgetId)), userContext, new WidgetContext());
            BuildWidgetResponse unboundWidgetResponse = unboundWidgetResponseFuture.get();
            if (unboundWidgetResponse != null && !unboundWidgetResponse.getWidgets().isEmpty()) {
                if (unboundWidgetResponse.getWidgets().getFirst().getWidgetType().equals(WidgetType.BANNER_CAROUSEL_WIDGET)) {
                    BannerCarouselWidget widget = (BannerCarouselWidget) unboundWidgetResponse.getWidgets().getFirst();
                    widget.setData(widget.getData().stream().map(banner -> {
                        if (banner.getBannerIdentifier() != null && banner.getBannerIdentifier().equals(unboundBannerId)) {
                            return banner;
                        } else {
                            return null;
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toList()));

                    if (!widget.getData().isEmpty()) {
                        scheduleResponse.setBannerWidget(widget);
                    }
                }
            }
        }

        return scheduleResponse;
    }

    private CultClassView getClassView(
            CultClass cultClass,
            Map<Long, Workout> workoutNameMap,
            Map<String, List<UserEntry>> squadsData,
            Boolean isBootcampToolTipVisible,
            Boolean showWorkoutLevelToolTip,
            Boolean isRecommendedWorkout,
            Boolean isMiniGoalModalNotShownForEligibleUser,
            UserContext userContext,
            ServiceInterfaces serviceInterfaces
    ) {
        CultClassView tempClassView = new CultClassView();
        tempClassView.setId(cultClass.getId().toString());
        tempClassView.setProductType(ProductType.FITNESS);
        tempClassView.setDate(cultClass.getDate());
        tempClassView.setStartTime(cultClass.getStartTime());
        tempClassView.setEndTime(cultClass.getEndTime());
        tempClassView.setWorkoutId(cultClass.getWorkoutCategoryID());
        tempClassView.setWorkoutCategoryId(cultClass.getWorkoutCategoryID());
        tempClassView.setIsRecommendedWorkout(isRecommendedWorkout);
        tempClassView.setCenterID(Integer.valueOf(cultClass.getCenterID()));
        tempClassView.setAvailableSeats((int) Math.min(cultClass.getCultAppAvailableSeats(), 2));
        tempClassView.setWorkoutName(workoutNameMap.get(cultClass.getWorkoutID()).getName().toUpperCase());
        tempClassView.setState(ClassState.AVAILABLE);
        tempClassView.setWaitlistInfo(new ClassViewWaitlistInfo());
        tempClassView.setIsPulseEnabled(false);

        Action seatUnavailableAction = new Action();
        seatUnavailableAction.setTitle("Class Booking");
        seatUnavailableAction.setSubTitle("All slots for the class are booked");
        seatUnavailableAction.setActionType(ActionType.SHOW_CLASS_BOOKING_ERROR_MODAL);

        if (CultUtil.isBootcampClass(cultClass.getWorkoutID(), serviceInterfaces.environmentService)) {
            tempClassView.setState(ClassState.BOOTCAMP);
            Action tempAction = new Action();
            tempAction.setUrl("curefit://fl_listpage?pageId=transform_bootcamp&disableAnimation=true");
            tempClassView.setAction(tempAction);
            tempClassView.setCardAction(TransformUtil.getBootcampModalAction(userContext, serviceInterfaces));

            Action dismissAction = new Action();
            dismissAction.setActionType(ActionType.REST_API);

            ActionMeta dismissActionMeta = new ActionMeta();
            dismissActionMeta.setMethod("POST");
            dismissActionMeta.setUrl("/transform/bootcampDismissToolTip");

            Map<String, String> dismissActionMetaBody = new HashMap<>();
            dismissActionMetaBody.put("state", "CLOSED");
            dismissActionMetaBody.put("productType", "BOOTCAMP");
            dismissActionMeta.setBody(dismissActionMetaBody);
            dismissAction.setMeta(dismissActionMeta);


            Action cacheAction = new Action();
            cacheAction.setActionType(ActionType.REST_API);

            ActionMeta cacheActionMeta = new ActionMeta();
            cacheActionMeta.setMethod("POST");
            cacheActionMeta.setUrl("/transform/bootcampImpressionCount");

            Map<String, String> cacheActionMetaBody = new HashMap<>();
            cacheActionMetaBody.put("productType", "BOOTCAMP");
            cacheActionMeta.setBody(cacheActionMetaBody);
            cacheAction.setMeta(cacheActionMeta);

            BootcampClassData bootcampClassData = new BootcampClassData();
            bootcampClassData.setDismissAction(dismissAction);
            bootcampClassData.setCacheAction(cacheAction);
            bootcampClassData.setToolTipTitle("Exclusive Program");
            bootcampClassData.setShowToolTip(isBootcampToolTipVisible);
            bootcampClassData.setIconImageUrl("image/transform/sparkle_icon.png");
            tempClassView.setBootcampClassData(bootcampClassData);

        }
        else if (cultClass.getBookingNumber() != null) {
            if (cultClass.getPulseDeviceName() != null && !cultClass.getPulseDeviceName().isEmpty()) {
                tempClassView.setPulseDeviceName(CultUtil.pulsifyDeviceName(cultClass, false));
            }
            tempClassView.setIsBooked(true);
            tempClassView.setState(ClassState.BOOKED);
            tempClassView.setAction(new Action("curefit://cultclass?bookingNumber=" + cultClass.getBookingNumber(), ActionType.NAVIGATION));
            tempClassView.setCardAction(new Action("curefit://cultclass?bookingNumber=" + cultClass.getBookingNumber(), ActionType.NAVIGATION));
        }
        else if (cultClass.getWlBookingNumber() != null) {
            tempClassView.setState(ClassState.WAITLISTED);
            ClassViewWaitlistInfo classViewWaitlistInfo = new ClassViewWaitlistInfo();
            classViewWaitlistInfo.setWaitlistNumber(cultClass.getWaitlistNumber());

            if (cultClass.getWlConfirmationThreshold() != null) {
                classViewWaitlistInfo.setWaitlistCnfProbability(CultUtil.getWaitlistCnfProbability(cultClass));
                classViewWaitlistInfo.setWaitlistColor(CultUtil.getWaitlistCnfProbabilityColor(cultClass, serviceInterfaces, userContext));
            }

            tempClassView.setWaitlistInfo(classViewWaitlistInfo);

            tempClassView.setAction(new Action("curefit://cultclass?bookingNumber=" + cultClass.getWlBookingNumber() + "&isWaitlistBooking=true", ActionType.NAVIGATION));
            tempClassView.setCardAction(new Action("curefit://cultclass?bookingNumber=" + cultClass.getWlBookingNumber() + "&isWaitlistBooking=true", ActionType.NAVIGATION));
        }
        else if ( CultUtil.isClassAvailableForWaitlist(cultClass) ) {
            tempClassView.setState(ClassState.WAITLIST_AVAILABLE);
            ClassViewWaitlistInfo classViewWaitlistInfo = new ClassViewWaitlistInfo();
            classViewWaitlistInfo.setWaitlistedUserCount(cultClass.getWaitlistedUserCount() + 1);

            if (cultClass.getWlConfirmationThreshold() != null) {
                classViewWaitlistInfo.setConfirmationProbability("Upto #" + cultClass.getWlConfirmationThreshold() + " usually gets confirmed");
                classViewWaitlistInfo.setConfProbabilityTextStart("Upto ");
                classViewWaitlistInfo.setConfProbabilityTextEnd(" usually gets confirmed");
                classViewWaitlistInfo.setConfProbabilityTextMiddle("#" + cultClass.getWlConfirmationThreshold());
                classViewWaitlistInfo.setWaitlistCnfProbability(CultUtil.getWaitlistCnfProbability(cultClass));
                classViewWaitlistInfo.setWaitlistColor(CultUtil.getWaitlistCnfProbabilityColor(cultClass, serviceInterfaces, userContext));
            }
            tempClassView.setWaitlistInfo(classViewWaitlistInfo);
        } else if ( tempClassView.getAvailableSeats() <= 0 && cultClass.getIsWaitlistFull()) {
            tempClassView.setState(ClassState.WAITLIST_FULL);
            Action cardAction = new Action();
            cardAction.setTitle("Waitlist Full");
            cardAction.setSubTitle("Waitlist for this class is currently full");
            cardAction.setActionType(ActionType.SHOW_CLASS_BOOKING_ERROR_MODAL);
            tempClassView.setCardAction(cardAction);

        } else if ( tempClassView.getAvailableSeats() <= 0 ) {
            tempClassView.setState(ClassState.SEAT_NOT_AVAILABLE);
            tempClassView.setCardAction(seatUnavailableAction);
        }  else if ( !cultClass.getAllowTrial() && cultClass.getAmount().equals(0L) ) {
            tempClassView.setState(ClassState.SEAT_NOT_AVAILABLE);
            tempClassView.setCardAction(seatUnavailableAction);
        } else if ( !cultClass.getAllowPPC()) {
            tempClassView.setState(ClassState.SEAT_NOT_AVAILABLE);
            tempClassView.setCardAction(seatUnavailableAction);
        }

        if ( workoutNameMap.get(cultClass.getWorkoutID()).getIsNewWorkout() && (tempClassView.getState().equals(ClassState.AVAILABLE) || tempClassView.getState().equals(ClassState.WAITLIST_AVAILABLE)) ) {
            tempClassView.setIsNewClass(true);
        }

        if ( tempClassView.getState().equals(ClassState.AVAILABLE) || tempClassView.getState().equals(ClassState.WAITLIST_AVAILABLE ) ) {
            Action cardAction = new Action();
            cardAction.setActionType(ActionType.NAVIGATION);
            cardAction.setUrl("curefit://prebookclass?classId="+cultClass.getId());
            tempClassView.setCardAction(cardAction);
        }

        if ( BooleanUtils.isTrue(isMiniGoalModalNotShownForEligibleUser) ) {
            tempClassView.setCardAction(CultUtil.getMiniGoalIntroCustomBottomSheetAction(tempClassView.getCardAction(), userContext));
            serviceInterfaces.featureStateCache.set(userContext.getUserProfile().getUserId(), "mini_goal_info_key", "shown");
        }

        if (cultClass.getWorkoutID().equals(CultUtil.DANCE_FITNESS_REVAMP_WORKOUT_ID) && showWorkoutLevelToolTip) {
            tempClassView.setClassLevelToolTip(createClassLevelToolTip("/cult/cultClassDismissToolTip", "New Edition", "New Playlist", "New curated playlist."));
        }

        if (cultClass.getWorkoutID().equals(CultUtil.STRENGTH_REVAMP_WORKOUT_ID) && showWorkoutLevelToolTip) {
            tempClassView.setClassLevelToolTip(createClassLevelToolTip("/cult/cultClassDismissToolTipStrength", "Newly Launched Format", "New Format", "Newly Launched Format"));
        }

        if (squadsData != null && squadsData.containsKey(cultClass.getId().toString())) {
            List<String> avatarUrls = new ArrayList<>();
            List<String> namesList = new ArrayList<>();
            List<UserEntry> users = squadsData.get(cultClass.getId().toString());
            if (users.size() > 5) {
                users = users.subList(0, 5);
            }

            for (UserEntry user : users) {
                if (user != null) {
                    avatarUrls.add(user.getProfilePictureUrl() != null ? user.getProfilePictureUrl() : "");
                    namesList.add(user.getFirstName() != null ? user.getFirstName() : "");
                }
            }

            SquadsListInClass squadJoining = new SquadsListInClass();
            squadJoining.setAvatarUrlList(avatarUrls);
            squadJoining.setNamesList(namesList);
            squadJoining.setSuffixText("joining");
            tempClassView.setSquadJoining(squadJoining);
        }

        return tempClassView;
    }

    private static Action getWaitListOnboardingAction(String title, String description) {
        Action waitListOnboardingAction = new Action();
        waitListOnboardingAction.setActionType(ActionType.SHOW_WAITLIST_INFO_ONBOARDING_MODAL);
        Map<String, String> waitListOnboardingActionMeta = new HashMap<>();
        waitListOnboardingActionMeta.put("title", title);
        waitListOnboardingActionMeta.put("description", description);
        waitListOnboardingActionMeta.put("lowWaitlistImage", "/image/mem-exp/white_waitlist_onboarding.png");
        waitListOnboardingActionMeta.put("lowWaitlistDescription", "Indicates more than 90% chance of class confirmation");
        waitListOnboardingActionMeta.put("lowWaitlistTitle", "White");
        waitListOnboardingActionMeta.put("lowWaitlistColor", "#FFFFFF");
        waitListOnboardingActionMeta.put("highWaitlistImage", "/image/mem-exp/orange_waitlist_onboarding.png");
        waitListOnboardingActionMeta.put("highWaitlistDescription", "Indicates 50% chance of confirmation");
        waitListOnboardingActionMeta.put("highWaitlistTitle", "Orange");
        waitListOnboardingActionMeta.put("highWaitlistColor", "#FFB876");
        waitListOnboardingActionMeta.put("primaryActionText", "GOT IT");

        waitListOnboardingAction.setMeta(waitListOnboardingActionMeta);
        return waitListOnboardingAction;
    }

    private NoshowBookingBlockedWidget getNoShowBookingBlockedWidget(ScheduleNoShowDetails noShowDetails, UserContext userContext) {
        String tz = userContext.getUserProfile().getTimezone();
        String bookingDate = TimeUtil.formatDateString(noShowDetails.getLastNoShowClass().getClassDate(), "yyyy-MM-dd", "MMM dd");
        String bookingTime = TimeUtil.formatTimeString(noShowDetails.getLastNoShowClass().getClassStartTime(), "HH:mm:ss", "hh:mm a");
        List<String> blockedDates = new ArrayList<>();
        StringBuilder blockedDatesString = new StringBuilder();

        for (String blockedDate : noShowDetails.getBookingBlockedDates()) {
            blockedDates.add(TimeUtil.formatDateString(blockedDate, "yyyy-MM-dd", "MMM dd"));
        }

        for (int i = 0; i < blockedDates.size(); i++) {
            if (i == blockedDates.size() - 1 && i > 0) {
                blockedDatesString.append(" and ");
            } else if (i > 0) {
                blockedDatesString.append(", ");
            }
            blockedDatesString.append(blockedDates.get(i));
        }

        String title = "Uh oh! " + TimeUtil.ordinal(noShowDetails.getNoShowCount()) + " no-show. Bookings will be blocked on " + blockedDatesString;
        String subtitle = "We missed you at " + noShowDetails.getLastNoShowClass().getWorkoutName() + " class at " + bookingTime + " on " + bookingDate;

        return getNoshowBookingBlockedWidget(title, subtitle);
    }

    private @NotNull NoshowBookingBlockedWidget getNoshowBookingBlockedWidget(String title, String subtitle) {
        NoshowBookingBlockedWidget dayWiseWidget = new NoshowBookingBlockedWidget();
        dayWiseWidget.setWidgetType(WidgetType.NOSHOW_BOOKING_BLOCKED_WIDGET);
        dayWiseWidget.setTitle(title);
        dayWiseWidget.setSubtitle(subtitle);
        dayWiseWidget.setIcon(ActionIcon.LOCK);
        dayWiseWidget.setAction(new Action("curefit://webview?uri=https://support.cult.fit/support/solutions/articles/25000030016-what-is-the-new-no-show-policy-and-how-does-the-policy-work-", "KNOW MORE", ActionType.NAVIGATION));
        return dayWiseWidget;
    }

    private Map<Long, Workout> getWorkoutMap(List<Workout> workouts) {
        Map<Long, Workout> workoutMap = new HashMap<>();
        for (Workout workout : workouts) {
            workoutMap.put(workout.getId(), workout);
        }
        return workoutMap;
    }

    public Map<String, List<UserEntry>> processSquadData(UserContext userContext, List<SquadClassBookingLite> cultPeersFutureClasses) {
        try {
            Map<String, List<UserEntry>> userEntryGroupByClassId = new HashMap<>();
            Map<String, Integer> userCountByClassId = new HashMap<>();
            if (cultPeersFutureClasses != null && !cultPeersFutureClasses.isEmpty()) {
                List<String> userIds = new ArrayList<>();

                // Get the first 5 unique users for each class
                for (SquadClassBookingLite booking : cultPeersFutureClasses) {
                    String classId = booking.getClassId().toString();
                    String bookingUserId = booking.getUserId().toString();
                    if (userCountByClassId.getOrDefault(classId, 0) < 5) {
                        userCountByClassId.put(classId, userCountByClassId.getOrDefault(classId, 0) + 1);
                        userIds.add(bookingUserId);
                    }
                }

                // Get the user details for the first 5 unique users for each class
                List<String> uniqueUserIds = userIds.stream().distinct().toList();
                Map<String, UserEntry> usersMap = CommunityUtil.getBulkUsersEntryMap(uniqueUserIds, serviceInterfaces.userServiceClient);
                for (SquadClassBookingLite booking : cultPeersFutureClasses) {
                    String classId = booking.getClassId().toString();
                    String bookingUserId = booking.getUserId().toString();
                    userEntryGroupByClassId.computeIfAbsent(classId, k -> new ArrayList<>()).add(usersMap.get(bookingUserId));
                }
                return userEntryGroupByClassId;
            }
        } catch (Exception e) {
            log.error("processSquadData failure for user: {}", userContext.getUserProfile().getUserId());
        }
        return null;
    }

    public ClassListView.SchedulePageHeader settingsToSchedulePageHeader(
            ProductType productType,
            Map<String, Object> settingMap,
            boolean showCenterShutdownWarningIcon,
            List<CenterScheduleDetailCard> centerShutdownDetailCards,
            Boolean filterApplied,
            LocationPreference locationPreference,
            UserContext userContext
    ) throws ExecutionException, InterruptedException, CenterNotSelectedException {

        String title;
        ClassListView.Tooltip toolTip = null;

        if (settingMap == null || settingMap.isEmpty()) {
            throw new CenterNotSelectedException("Please select a preference");
        } else {
            if (locationPreference != null) {

                if (settingMap.containsKey("USER_BOOKING_V2_FAVOURITE_CENTER")) {
                    title = "Favourites (" + ((List<?>) settingMap.get("USER_BOOKING_V2_FAVOURITE_CENTER")).size() + ")";
                } else {
                    LocationPreferenceType locationType = locationPreference.getPreferredLocationType();
                    if (locationType == null) {
                        throw new CenterNotSelectedException("Please select a preference");
                    }
                    else {
                        switch (locationType) {
                            case LocationPreferenceType.locality:
                                title = locationPreference.getLocality();
                                break;
                            case LocationPreferenceType.coordinates:
                                LatLong coordinatesLatLong = locationPreference.getCoordinates();
                                if (coordinatesLatLong == null || locationPreference.getCoordinateName() == null) {
                                    throw new CenterNotSelectedException("Please select a preference");
                                }
                                title = locationPreference.getCoordinateName();
                                break;
                            case LocationPreferenceType.current_location:
                                LatLong latLong = locationPreference.getCoordinates();
                                if (latLong == null) {
                                    throw new CenterNotSelectedException("Please select a preference");
                                }
                                String placeTitle = "Near You";
                                return createHeader(placeTitle, toolTip, showCenterShutdownWarningIcon, centerShutdownDetailCards);

                            default:
                                throw new CenterNotSelectedException("Please select a preference");
                        }
                    }
                }
            } else {

                if (settingMap.containsKey("USER_BOOKING_V2_SETTINGS_STATE") &&
                        "system_generated".equals(settingMap.get("USER_BOOKING_V2_SETTINGS_STATE")) &&
                        settingMap.containsKey("USER_BOOKING_V2_LOCALITY")) {
                    toolTip = new ClassListView.Tooltip();
                    toolTip.setText("We have selected an area for your convenience. You can always edit it here.");
                    Action closeAction = new Action();
                    closeAction.setActionType(ActionType.REST_API);
                    ActionMeta meta = new ActionMeta();
                    meta.setMethod("POST");
                    meta.setUrl("/user/inAppNotification/" + CULT_AREA_TOOL_TIP);
                    meta.setBody(Map.of("state", "CLOSED", "productType", productType));
                    closeAction.setMeta(meta);
                    toolTip.setCloseAction(closeAction);
                }

                if (settingMap.containsKey("USER_BOOKING_V2_FAVOURITE_CENTER")) {
                    title = "Favourites (" + ((List<?>) settingMap.get("USER_BOOKING_V2_FAVOURITE_CENTER")).size() + ")";
                } else if (settingMap.containsKey("USER_BOOKING_V2_LAT_LONG")) {
                    title = String.valueOf(settingMap.get("USER_BOOKING_V2_LAT_LONG")); //check later
                } else if (settingMap.containsKey("USER_BOOKING_V2_LOCALITY")) {
                    Long localityId = Long.valueOf((Integer) settingMap.get("USER_BOOKING_V2_LOCALITY"));
                    CultLocality locality = serviceInterfaces.cultService.localityById(localityId, null).get();
                    String localityName = locality.getName();
                    return createHeader(localityName, toolTip, showCenterShutdownWarningIcon, centerShutdownDetailCards);
                } else {
                    throw new CenterNotSelectedException("Please select a preference");
                }
            }
        }

        if (filterApplied) {
            toolTip = new ClassListView.Tooltip();
            toolTip.setTitle("FILTER APPLIED");
            toolTip.setText("Showing workouts based on the recommendation. You can change it from here.");
        }

        return createHeader(title, toolTip, showCenterShutdownWarningIcon, centerShutdownDetailCards);
    }

    private ClassListView.SchedulePageHeader createHeader(String title, ClassListView.Tooltip toolTip, boolean showCenterShutdownWarningIcon, List<CenterScheduleDetailCard> centerShutdownDetailCards) {
        ClassListView.SchedulePageHeader header = new ClassListView.SchedulePageHeader();
        header.setTitle(title);
        header.setToolTip(toolTip);

        if (showCenterShutdownWarningIcon) {
            Action action = new Action();
            action.setTitle("KNOW MORE");
            action.setIconUrl(CultUtil.CENTER_MAINTENANCE_WARNING);
            action.setActionType(ActionType.SHOW_CAROUSEL_LIST);
            Map<String, Object> meta = new HashMap<>();
            meta.put("type", "CENTER_SHUTDOWN");
            meta.put("bundleProducts", centerShutdownDetailCards);
            action.setMeta(meta);
            header.setAction(action);
        }

        return header;
    }

    public Map<String, ClassListView.CenterInfo> getCenterInfoMap(UserContext userContext, List<CultCenter> centers, ProductType productType) {
        String tz = userContext.getUserProfile().getTimezone();
        Map<String, ClassListView.CenterInfo> centerInfoMap = new HashMap<>();

        for (CultCenter center : centers) {
            centerInfoMap.put(center.getId().toString(), new ClassListView.CenterInfo(center.getName(), null, TimeUtil.getDaysFromToday(center.getLaunchDate(), tz) < 15));
        }

        centerInfoMap.put(HOME_CENTER_ID, new ClassListView.CenterInfo("At Home", LiveUtil.classFormatTitle(userContext, productType), false));

        centerInfoMap.put(HOME_CENTER_ID_MIND, new ClassListView.CenterInfo("At Home", "mind.live", false));

        centerInfoMap.put(HOME_CENTER_ID_EAT, new ClassListView.CenterInfo("At Home", "eat.live", false));

        return centerInfoMap;
    }

    public List<ClassListView.Day> getAvailableWorkoutDays(UserContext userContext, List<String> classDates, Map<String, ClassByDateV2> classByDateMap) {
        List<String> dates;
        if (classDates.isEmpty() && !classByDateMap.isEmpty()) {
            dates = TimeUtil.getDays(userContext.getUserProfile().getTimezone(), 4, false, "YYYY-MM-DD");
        } else {
            dates = classDates;
        }

        List<ClassListView.Day> days = dates.stream().map(ClassListView.Day::new).toList();

        return days.stream().filter(day -> classByDateMap.containsKey(day.getId()) && !classByDateMap.get(day.getId()).getClassByTimeList().isEmpty()).collect(Collectors.toList());
    }

    private List<ClassListView.TimeSlotOption> getTimeSlotOptions() {
        List<ClassListView.TimeSlotOption> options = new ArrayList<>();
        options.add(new ClassListView.TimeSlotOption("morning", "morning", "Morning", "06:00:00"));
        options.add(new ClassListView.TimeSlotOption("evening", "evening", "Evening", "16:00:00"));
        return options;
    }

    public List<ClassListView.WorkoutCategoryFilter> getAvailableWorkoutCategoriesFilter(
            List<CultWorkoutCategory> workoutCategories,
            Boolean isCultSportCategorySupported,
            Boolean isBootcampCategorySupported) {

        List<ClassListView.WorkoutCategoryFilter> workoutFilters = workoutCategories.stream()
                .map(workoutCategory -> new ClassListView.WorkoutCategoryFilter(workoutCategory.getId(), workoutCategory.getName(), workoutCategory.getName()))
                .collect(Collectors.toList());

        if (!isCultSportCategorySupported) {
            List<Integer> sportCategoryIds = serviceInterfaces.environmentService.isStage() ? CULT_SPORT_CATEGORY_STAGE_WORKOUT_CATEGORY : CULT_SPORT_CATEGORY_PROD_WORKOUT_CATEGORY;

            workoutFilters = workoutFilters.stream()
                    .filter(workoutCategory -> !sportCategoryIds.contains( workoutCategory.getId()))
                    .collect(Collectors.toList());
        }

        if (!isBootcampCategorySupported) {
            List<Integer> bootcampCategoryIds = serviceInterfaces.environmentService.isStage() ? BOOTCAMP_CATEGORY_STAGE_WORKOUT_CATEGORY : BOOTCAMP_CATEGORY_PROD_WORKOUT_CATEGORY;

            workoutFilters = workoutFilters.stream()
                    .filter(workoutCategory -> !bootcampCategoryIds.contains( workoutCategory.getId()))
                    .collect(Collectors.toList());
        }

        workoutFilters.sort(Comparator.comparingInt(a -> FILTERS_PRIORITY_MAP.getOrDefault(a.getId(), FILTERS_PRIORITY_MAP.get(0))));

        // Shift 'Others' filter to last
        int othersIndex = -1;
        for (int i = 0; i < workoutFilters.size(); i++) {
            if ("Others".equals(workoutFilters.get(i).getName())) {
                othersIndex = i;
                break;
            }
        }
        if (othersIndex != -1) {
            workoutFilters.add(workoutFilters.remove(othersIndex));
        }

        workoutFilters = workoutFilters.stream().filter(Objects::nonNull).collect(Collectors.toList());

        return workoutFilters;
    }

    private BasicProgressCardWidget getSessionRemainingWidget(Membership limitedEliteMembership, UserContext userContext) {
        BasicProgressCardWidget progressCardWidget = new BasicProgressCardWidget();

        boolean isAwayCity = !limitedEliteMembership.getMetadata().get("cityId").equals(userContext.getUserProfile().getCity().getCityId());
        Benefit cultBenefit = limitedEliteMembership.getBenefits().stream().filter(a -> isAwayCity ? "CULT_AWAY".equals(a.getName()) : "CULT".equals(a.getName())).findFirst().orElse(null);
        double progressVal = (double) (cultBenefit.getTicketsUsed() != null ? cultBenefit.getTicketsUsed() : 0) / (cultBenefit.getMaxTickets() != null ? cultBenefit.getMaxTickets() : 1);
        String color1 = UiUtils.getProgressColor(progressVal, false);
        String color2 = UiUtils.getProgressColor(progressVal, true);

        progressCardWidget.setText(CultUtil.getLimitedEliteSessionsLeftText(limitedEliteMembership, userContext, true));
        progressCardWidget.setBgColor(color1);
        progressCardWidget.setTextColor("");
        progressCardWidget.setTrialType(cultBenefit.getMaxTickets() > 6 ? "duration" : "session");

        BasicProgressCardWidget.ProgressBar progressBar = new BasicProgressCardWidget.ProgressBar();
        progressBar.setCompleted(cultBenefit.getTicketsUsed());
        progressBar.setTotal(cultBenefit.getMaxTickets());
        progressBar.setProgressBarColor(cultBenefit.getMaxTickets() > 6 ? color1 : color2);
        progressBar.setProgressBarBackgroundColor("transparent");

        progressCardWidget.setProgressBar(progressBar);

        return progressCardWidget;
    }

    public Map<String, String> getInfoWidget(List<Workout> workouts, boolean isSportCategorySupported) {
        boolean isNewWorkoutPresent = false;

        for (Workout workout : workouts) {
            if (workout.getIsNewWorkout()) {
                isNewWorkoutPresent = true;
                break;
            }
        }

        if (isNewWorkoutPresent && isSportCategorySupported) {
            Map<String, String> infoWidget = new HashMap<>();
            infoWidget.put("widgetType", "CULT_SCHEDULE_INFO_WIDGET");
            infoWidget.put("title", "We have added new workouts to the schedule");
            infoWidget.put("tagName", "NEW");
            return infoWidget;
        }
        return null;
    }

    public String getButtonType(LiveClass classResponse, LiveClassSlot slotResponse, UserContext userContext, boolean doesUserBelongToSchedulePageLabelExperiment) {
        if (LiveUtil.isVodSession(classResponse.getPreferredStreamType()) && isFreshClassLabel(classResponse, slotResponse)) {
            if (doesUserBelongToSchedulePageLabelExperiment) {
                return "LABEL_NEW_TEST";
            } else {
                return "LABEL_NEW_CONTROL";
            }
        }
        List<Label> labels = getLabels(classResponse, slotResponse, userContext, doesUserBelongToSchedulePageLabelExperiment);
        return labels.stream()
                .map(l -> "LABEL_" + l.getTitle().toUpperCase().replace(" ", "_"))
                .collect(Collectors.joining());
    }

    public List<Label> getLabels(LiveClass classResponse, LiveClassSlot slotResponse, UserContext userContext, boolean doesUserBelongToSchedulePageLabelExperiment) {
        List<Label> labels = new ArrayList<>();

        if (isSpecialClass(classResponse)) {
            Label label = new Label();
            label.setTitle("New Program");
            label.setBackgroundColor("statusBlue");
            label.setIcon("masterclass");
            label.setTextColor("white");
            labels.add(label);
        } else if (LiveUtil.isConsideredInteractiveSessionForPresentation(classResponse.getPreferredStreamType()) && LiveFitWorkoutFormat.AMA.equals(classResponse.getFormat())) {
            Label label = new Label();
            label.setTitle("AMA");
            label.setBackgroundColor("statusGreen");
            label.setIcon("chat");
            label.setTextColor("white");
            labels.add(label);
        } else if (LiveUtil.isConsideredInteractiveSessionForPresentation(classResponse.getPreferredStreamType())) {
            boolean isClassSlotIconUrlSupported = AppUtil.isClassSlotIconUrlSupported(userContext, serviceInterfaces.environmentService);
            Label label = new Label();
            label.setTitle("INTERACTIVE");
            label.setBackgroundColor("statusGreen");
            label.setIcon(isClassSlotIconUrlSupported ? null : "live-interactive");
            label.setTextColor("white");
            label.setIconUrl(isClassSlotIconUrlSupported ? INTERACTIVE_ICON_URL : null);
            labels.add(label);
        } else if (doesUserBelongToSchedulePageLabelExperiment && LiveUtil.isVodSession(classResponse.getPreferredStreamType()) && isFreshClassLabel(classResponse, slotResponse)) {
            Label label = new Label();
            label.setTitle("NEW");
            label.setBackgroundColor("statusBlue");
            label.setTextColor("white");
            labels.add(label);
        }

        return labels;
    }

    public boolean isFreshClassLabel(LiveClass classResponse, LiveClassSlot slotResponse) {
        if (slotResponse.getIsPremier()) {
            return true;
        }
        if (classResponse.getFirstTelecastTimeEpoch() == null) {
            return false;
        }
        long dayDiff = TimeUtil.absDiffInDaysFromEpoch(slotResponse.getScheduledTimeEpoch(), classResponse.getFirstTelecastTimeEpoch());
        return dayDiff < 1;
    }

    public boolean isSpecialClass(LiveClass classResponse) {
        return classResponse.getTitle().toLowerCase().contains("dance-a-then");
    }

    private ClassLevelToolTip createClassLevelToolTip(String url, String tooltip, String title, String subtitle) {
        Action dismissAction = new Action();
        dismissAction.setActionType(ActionType.REST_API);
        dismissAction.setTitle("Got it");
        ActionMeta dismissActionMeta = new ActionMeta();
        dismissActionMeta.setMethod("POST");
        dismissActionMeta.setUrl(url);
        dismissActionMeta.setBody(Collections.singletonMap("state", "DISMISSED"));
        dismissAction.setMeta(dismissActionMeta);

        ClassLevelToolTip classLevelToolTip = new ClassLevelToolTip();
        classLevelToolTip.setAnnouncementAction(dismissAction);
        classLevelToolTip.setOnboardingTooltip(tooltip);
        classLevelToolTip.setOnboardingTitle(title);
        classLevelToolTip.setOnboardingSubtitle(subtitle);
        classLevelToolTip.setOnboardingImage("/image/cultpass/waitlist_onboarding_6.png");
        classLevelToolTip.setShowBottomSheet(false);

        return classLevelToolTip;
    }

}
