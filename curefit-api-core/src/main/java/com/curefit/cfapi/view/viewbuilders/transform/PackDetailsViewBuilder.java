package com.curefit.cfapi.view.viewbuilders.transform;

import com.curefit.cfapi.model.internal.cult.CultPackBrowseProductType;
import com.curefit.cfapi.model.internal.cult.FitnessPackCategoryItem;
import com.curefit.cfapi.model.internal.cult.FitnessPackHeader;
import com.curefit.cfapi.model.internal.cult.HeaderImageSize;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.TransformUtil;
import com.curefit.cfapi.view.viewmodels.transform.PackDetailsScreenView;
import com.curefit.cfapi.view.viewmodels.transform.ThemeType;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.fitness.FitnessPackBrowseWidget;
import com.curefit.cfapi.widgets.transform.PackDetailsWidget;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class PackDetailsViewBuilder {

    public PackDetailsScreenView buildView(ServiceInterfaces serviceInterfaces, UserContext userContext, String subCategoryCode) throws Exception {
        PackDetailsScreenView packDetailsScreenView = new PackDetailsScreenView();
        ThemeType themeType = TransformUtil.getCurrentTheme(userContext).getThemeType();
        packDetailsScreenView.setThemeType(themeType);
        packDetailsScreenView.setTitle("Explore Packs");
        List<BaseWidget> widgets = new ArrayList<>();
        FitnessPackBrowseWidget fitnessPackBrowseWidget = new FitnessPackBrowseWidget();
        List<FitnessPackCategoryItem> items = new ArrayList<>();
        FitnessPackCategoryItem transformItem = new FitnessPackCategoryItem();
        transformItem.setProductType(CultPackBrowseProductType.TRANSFORM_WL);
        FitnessPackHeader fitnessPackHeader = new FitnessPackHeader();
        fitnessPackHeader.setHeaderTitle("Transform PLUS");
        fitnessPackHeader.setHeaderSubTitle("Weight management");
        fitnessPackHeader.setHeaderImage("image/transform/pack_details_final.png");
        fitnessPackHeader.setImageSize(HeaderImageSize.LARGE);
        transformItem.setHeader(fitnessPackHeader);
        items.add(transformItem);
        fitnessPackBrowseWidget.setItems(items);
        fitnessPackBrowseWidget.setLayoutProps(getLayoutSpacing("70", "50"));
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("isTransformPlusProduct", "true");
        List<BaseWidget> packBrowseWidget = fitnessPackBrowseWidget.buildCoachFitnessPackBrowseWidget(serviceInterfaces, userContext, queryParams);
        PackDetailsWidget packDetailsWidget = new PackDetailsWidget();
        if (!CollectionUtils.isEmpty(packBrowseWidget)) {
            widgets.add(packBrowseWidget.get(0));
            packDetailsWidget.setLayoutProps(getLayoutSpacing("0", "70"));
        } else {
            packDetailsWidget.setLayoutProps(getLayoutSpacing("90", "70"));
        }
        widgets.add(packDetailsWidget.buildView(serviceInterfaces, userContext, null).get(0));
        packDetailsScreenView.setWidgets(widgets);
        Action action = TransformUtil.getTransformPlusExperimentAction(userContext, serviceInterfaces);
        packDetailsScreenView.setAction(action);
        return packDetailsScreenView;
    }

    public Map<String, Object> getLayoutSpacing(String top, String bottom) {
        Spacing spacing = new Spacing(top, bottom);
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", spacing);
        return layoutProps;
    }
}
