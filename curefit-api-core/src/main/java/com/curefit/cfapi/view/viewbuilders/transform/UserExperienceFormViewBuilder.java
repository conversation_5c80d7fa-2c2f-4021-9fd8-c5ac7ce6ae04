package com.curefit.cfapi.view.viewbuilders.transform;


import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.TransformUtil;
import com.curefit.cfapi.view.viewmodels.transform.UserExperienceFormData;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class UserExperienceFormViewBuilder {
    public UserExperienceFormData buildView(ServiceInterfaces serviceInterfaces, UserContext userContext, String subCategoryCode) throws Exception {
        UserExperienceFormData userPreferenceFormData = new UserExperienceFormData();
        List<UserExperienceFormData.Page> pageList = new ArrayList<>();
        String userName = userContext.getUserEntryCompletableFuture().get().getFirstName();
        if(userName == null){
            userName = "Guest";
        }
        UserExperienceFormData.Page introPage = new UserExperienceFormData.Page();
        introPage.setPageType(UserExperienceFormData.PageType.INTRO_SCREEN);
        introPage.setTitle("Weight loss made simple, flexible & permanent!");
        introPage.setDescription("CULT TRANSFORM PLUS");
        introPage.setSubtitle("Experts you need. Results you want. Food you love.");
        introPage.setImageUrl("image/transform/habit_coach_icon.png");
        introPage.setIsPrimaryButton(true);
        introPage.setPageId("INTRO");
        Action introPageAction = new Action(ActionType.EMPTY_ACTION, "GET STARTED");
        introPageAction.setSubTitle("Expect to lose 2 to 3 kgs per month");
        introPage.setAction(introPageAction);
        pageList.add(introPage);

        String message1 = "A commitment to the goal is the first step towards achieving it. Let's work on it together, " + userName + ".";
        String message2 = userName + ", I know that was not sustainable. We focus on losing weight by eating the right portion sizes of simple home-cooked meals.";
        String message3 = userName + ", there is always a first time. You have landed on the right spot to kickstart your weight loss journey.";
        String message4 = userName + ", We’ll meet you where you are. We create personalized, progressive plans to help you reach your weight loss goals.";

        UserExperienceFormData.Page firstPage = new UserExperienceFormData.Page();
        firstPage.setPageType(UserExperienceFormData.PageType.SINGLE_SELECT_SCREEN);
        firstPage.setTitle("Why do you want to lose weight ?");
        firstPage.setSubtitle("This will help us keep you motivated towards your goal.");
        firstPage.setItems(Arrays.asList(getItem("Feel better in my body", "1", message1), getItem("Be healthier", "2", message1), getItem("Get in shape", "3", message1), getItem("Fit in my old clothes", "4", message1), getItem("Be more energetic", "5", message1), getItem("Live longer", "6", message1), getItem("Prevent lifestyle diseases", "7", message1)));
        firstPage.setCoachTitle("Mindset Coach");
        firstPage.setCoachSubtitle("Transform Plus");
        firstPage.setCoachProfileImage("image/transform/user_experience_form/lifestyle_coach_avatar.png");
        firstPage.setPageId("LOST_WEIGHT_GOAL");
        firstPage.setIsPrimaryButton(false);
        firstPage.setAction(new Action(ActionType.EMPTY_ACTION, "NEXT"));
        pageList.add(firstPage);

        UserExperienceFormData.Page secondPage = new UserExperienceFormData.Page();
        secondPage.setPageType(UserExperienceFormData.PageType.SINGLE_SELECT_SCREEN);
        secondPage.setTitle("Which of these weight loss methods have you tried before?");
        secondPage.setItems(Arrays.asList(getItem("Intermittent fasting", "1", message2), getItem("Low-carb diet", "2", message2), getItem("Calorie counting", "3", message2), getItem("Fad diets", "4", message2), getItem("Gymming", "5", message2), getItem("None of the above", "6", message3)));
        secondPage.setCoachTitle("Nutritionist");
        secondPage.setCoachSubtitle("Transform Plus");
        secondPage.setCoachProfileImage("image/transform/user_experience_form/nutritionist_avatar.png");
        secondPage.setIsPrimaryButton(false);
        secondPage.setPageId("LOST_WEIGHT_METHOD");
        secondPage.setAction(new Action(ActionType.EMPTY_ACTION, "NEXT"));
        pageList.add(secondPage);


        UserExperienceFormData.Page thirdPage = new UserExperienceFormData.Page();
        thirdPage.setPageType(UserExperienceFormData.PageType.SINGLE_SELECT_SCREEN);
        thirdPage.setTitle("How much weight do you want to lose?");
        thirdPage.setItems(Arrays.asList(getItem("< 5 kgs", "1", message4), getItem("5-10 kgs", "2", message4), getItem("More than 10 kgs", "3", message4)));
        thirdPage.setCoachTitle("Fitness Coach");
        thirdPage.setCoachSubtitle("Transform Plus");
        thirdPage.setCoachProfileImage("image/transform/user_experience_form/fitness_coach_avatar.png");
        thirdPage.setPageId("WORKOUT_FREQUENCY");
        thirdPage.setIsPrimaryButton(false);
        thirdPage.setAction(new Action(ActionType.EMPTY_ACTION, "NEXT"));
        pageList.add(thirdPage);

        UserExperienceFormData.Page fourthPage = new UserExperienceFormData.Page();
        fourthPage.setPageType(UserExperienceFormData.PageType.PRESENTATION_SCREEN);
        fourthPage.setTitle("We've got your goals.\n" + "Now, let's crush them together!");
        fourthPage.setSubtitle("Here is what “Future you” \n" + "has to say to you!");
        fourthPage.setIsPrimaryButton(true);
        fourthPage.setLottieUrl("/image/transform/lottie/loadingscreen/theo-rocket.json");
        fourthPage.setPageId("PRESENTATION");
        fourthPage.setAction(new Action(ActionType.EMPTY_ACTION, "SEE THE MESSAGE ->"));
        pageList.add(fourthPage);

        UserExperienceFormData.Page fifthPage = new UserExperienceFormData.Page();
        fifthPage.setPageType(UserExperienceFormData.PageType.RESPONSE_SCREEN);
        fifthPage.setIsPrimaryButton(true);
        fifthPage.setCoachTitle("Future You");
        fifthPage.setCoachSubtitle("Online");
        fifthPage.setPageId("MESSAGE");
        fifthPage.setCoachProfileImage("image/transform/user_experience_form/your_avatar_final.png");
        String userTimeZone = userContext.getUserProfile().getTimezone();
        ZonedDateTime zdtStart = ZonedDateTime.ofInstant(Instant.now(), ZoneId.of(userTimeZone));
        Date date = Date.from(zdtStart.toInstant());
        String dateString = new SimpleDateFormat("d MMM yyyy").format(date);
        fifthPage.setDescription("Hi there,\n" +
                "It’s me, future " + userName + "! \uD83D\uDC4B \n" +
                "\n" +
                "Thanks to the commitment you made on " + dateString + "! \uD83D\uDDD3️\n" +
                "\n" +
                "I have excellent news. I have never felt this better in my body before. I’m healthy, in great shape, and more confident about myself.  \uD83D\uDE01 \n" +
                "\n" +
                "Thanks to the choices you’re making.  \uD83D\uDCAA\uD83D\uDCAA");
        fifthPage.setAction(new Action(TransformUtil.combinedWeightLossClpDeepLink(), "VIEW PLANS ->", ActionType.RESET_NAVIGATION));
        pageList.add(fifthPage);

        userPreferenceFormData.setPages(pageList);
        return userPreferenceFormData;
    }

    private UserExperienceFormData.Item getItem(String title, String id, String message) {
        UserExperienceFormData.Item item = new UserExperienceFormData.Item();
        item.setTitle(title);
        item.setId(id);
        item.setMessage(message);
        return item;
    }
}