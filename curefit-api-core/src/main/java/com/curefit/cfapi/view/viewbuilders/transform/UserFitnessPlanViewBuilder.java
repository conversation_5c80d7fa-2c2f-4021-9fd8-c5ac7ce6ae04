package com.curefit.cfapi.view.viewbuilders.transform;

import com.curefit.albus.agent.AgentAssignmentRequest;
import com.curefit.albus.common.agent.UserActiveAgentResponse;
import com.curefit.albus.request.ConsultationSearchRequestParamModel;
import com.curefit.albus.response.CarefitConsultationOrderResponse;
import com.curefit.base.enums.Tenant;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.CDNUtil;
import com.curefit.cfapi.util.LiveUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.util.TransformUtil;
import com.curefit.cfapi.view.viewmodels.transform.FitnessPlanDetail;
import com.curefit.cfapi.view.viewmodels.transform.FitnessPlanEmptyMessage;
import com.curefit.cfapi.view.viewmodels.transform.FitnessPlanSportsModalView;
import com.curefit.cfapi.view.viewmodels.transform.UserFitnessPlanPageView;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.digital.NowLiveSessionWidget;
import com.curefit.cfapi.widgets.digital.NowLiveWidget;
import com.curefit.cfapi.widgets.transform.FitnessPlanSGTItem;
import com.curefit.cfapi.widgets.transform.FitnessPlanSGTWidget;
import com.curefit.cfapi.widgets.transform.FitnessPlanV2Item;
import com.curefit.cfapi.widgets.transform.FitnessPlanWidgetV2;
import com.curefit.common.data.exception.BaseException;
import com.curefit.diyfs.pojo.enums.LiveFitWorkoutFormat;
import com.curefit.diyfs.pojo.enums.LiveWorkoutIntensityLevel;
import com.curefit.hercules.pojo.Exercise;
import com.curefit.hercules.pojo.Media;
import com.curefit.mealplanner.common.enums.FitnessPlanWorkoutStatus;
import com.curefit.product.models.diy.DIYProduct;
import com.curefit.shifu.enums.fitness.FitnessPlanStatus;
import com.curefit.shifu.enums.fitness.WorkoutType;
import com.curefit.shifu.pojo.SgtSlotEntry;
import com.curefit.shifu.pojo.fitness.FitnessPlanEntry;
import com.curefit.shifu.pojo.fitness.WorkoutDetailEntry;
import com.curefit.shifu.pojo.fitness.WorkoutResource;
import com.curefit.shifu.request.GetSGTSlotsRequest;
import com.curefit.ufs.pojo.ExerciseEntry;
import com.curefit.ufs.pojo.UserWodEntry;
import com.curefit.ufs.pojo.UserWodReportV2;
import com.curefit.ufs.pojo.WorkoutSetEntry;
import com.curefit.ufs.pojo.enums.BlockType;
import com.curefit.ufs.tps.pojo.enums.PreWodDuration;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Setter
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
public class UserFitnessPlanViewBuilder {

    private static String DEFAULT_WORKOUT_IMAGE_URL = "/hercules/production/assets/movements/images/BICYCLE_CRUNCHES_v1628055506703_61b3ffed-1d0f-4c13-9355-536608dfe7e8.jpg";

    public UserFitnessPlanPageView buildView(ServiceInterfaces serviceInterfaces, UserContext userContext, String subCategoryCode) throws ExecutionException, InterruptedException {
        FitnessPlanEntry fitnessPlanEntry = null;
        try {
            fitnessPlanEntry = serviceInterfaces.shifuClient.getFitnessPlanForUser(Long.valueOf(userContext.getUserProfile().getUserId()), FitnessPlanStatus.PUBLISHED);
        } catch (Exception e) {
            log.error("Error in getting fitness plan for user ID: " + userContext.getUserProfile().getUserId(), e);
        }

        UserFitnessPlanPageView userFitnessPlanPageView = new UserFitnessPlanPageView();
        userFitnessPlanPageView.setCurrentDate(getCurrentDate(userContext.getUserProfile().getTimezone()));

        List<FitnessPlanDetail> fitnessPlanDetailList = new ArrayList<>();

        FitnessPlanDetail emptyFitnessPlanDetail = getFitnessPlanEmptyStateIfRequired(serviceInterfaces, userContext, fitnessPlanEntry, subCategoryCode);

        if (emptyFitnessPlanDetail != null) {
            userFitnessPlanPageView.setPlanList(Collections.singletonList(emptyFitnessPlanDetail));
            return userFitnessPlanPageView;
        }

        // Populating fitness plan from fitnessPlanEntry
        List<String> diyProductIds = fitnessPlanEntry.getWorkoutDetailEntryList().stream()
                .map(workoutDetailEntry -> {
                    if (workoutDetailEntry.getResource() == null)
                        return null;
                    return workoutDetailEntry.getResource().getLiveClassProductId();
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<String, DIYProduct> diyProductMap = serviceInterfaces.diyfsService.getDIYFitnessProductsByProductIds(
                userContext.getUserProfile().getUserId(),
                diyProductIds,
                Tenant.CUREFIT_APP
        ).stream().collect(Collectors.toMap(DIYProduct::getProductId, Function.identity()));

        for (WorkoutDetailEntry workoutDetailEntry : fitnessPlanEntry.getWorkoutDetailEntryList()) {
            FitnessPlanDetail fitnessPlanDetail = new FitnessPlanDetail();
            fitnessPlanDetail.setDate(TimeUtil.formatDateInTimezone(TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()),
                    new Date(workoutDetailEntry.getDate()), TimeUtil.DEFAULT_TIME_FORMAT));
            if (workoutDetailEntry.getType().equals(WorkoutType.LIVE)) {
                fitnessPlanDetail.setWidgets(Collections.singletonList(getNowLiveWidgetForDIYProduct(diyProductMap.get(workoutDetailEntry.getResource().getLiveClassProductId()))));
            } else if (workoutDetailEntry.getType().equals(WorkoutType.WOD)) {
                fitnessPlanDetail.setWidgets(Collections.singletonList(getNowLiveWidgetForWod(serviceInterfaces, workoutDetailEntry)));
            } else {
                fitnessPlanDetail.setEmptyMessage(getMessageForRestDay());
            }
            fitnessPlanDetailList.add(fitnessPlanDetail);
        }

        userFitnessPlanPageView.setPlanList(fitnessPlanDetailList);

        return userFitnessPlanPageView;
    }

    public UserFitnessPlanPageView buildViewV2(ServiceInterfaces serviceInterfaces, UserContext userContext, String subCategoryCode) throws Exception {
        Date currentDate = new Date();
        String currentDayWorkoutType = null;
        FitnessPlanEntry fitnessPlanEntry = null;
        FitnessPlanWorkoutStatus currentDayFitnessPlanWorkoutStatus = FitnessPlanWorkoutStatus.SCHEDULED;
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            fitnessPlanEntry = serviceInterfaces.shifuClient.getFitnessPlanForUser(userId, FitnessPlanStatus.PUBLISHED);
        } catch (Exception e) {
            log.error("Error in getting fitness plan for userId: " + userId, e);
        }

        UserFitnessPlanPageView userFitnessPlanPageView = new UserFitnessPlanPageView();
        userFitnessPlanPageView.setCurrentDate(getCurrentDate(userContext.getUserProfile().getTimezone()));
        FitnessPlanDetail emptyFitnessPlanDetail = getFitnessPlanEmptyStateIfRequired(serviceInterfaces, userContext, fitnessPlanEntry, subCategoryCode);
        if (emptyFitnessPlanDetail != null) {
            // SGT Slots for first time user and expired fitness plan
            List<String> dates = new ArrayList<>();
            dates.add(getCurrentDate(userContext.getUserProfile().getTimezone()));
            List<SgtSlotEntry> sgtSlots = serviceInterfaces.shifuClient.getSGTSlots(new GetSGTSlotsRequest(dates));
            Map<String, List<SgtSlotEntry>> dateSlotsMap = sgtSlots.stream().collect(Collectors.groupingBy(SgtSlotEntry::getSlotDate));
            emptyFitnessPlanDetail.setSgtClasses(getSGTWidgets(dateSlotsMap.get(getCurrentDate(userContext.getUserProfile().getTimezone())), true, userContext));
            userFitnessPlanPageView.setSeparatorText(" MEANWHILE ");

            userFitnessPlanPageView.setPlanList(Collections.singletonList(emptyFitnessPlanDetail));
            userFitnessPlanPageView.setCurrentDayNumber("0");
            return userFitnessPlanPageView;
        }

        Map<String, DIYProduct> diyProductMap = getDIYProductMap(fitnessPlanEntry, serviceInterfaces, userId);

        List<String> dates = new ArrayList<>();
        for (WorkoutDetailEntry workoutDetailEntry : fitnessPlanEntry.getWorkoutDetailEntryList()) {
            String date = TimeUtil.formatDateInTimezone(TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()), new Date(workoutDetailEntry.getDate()), TimeUtil.DEFAULT_TIME_FORMAT);
            dates.add(date);
        }
        List<SgtSlotEntry> sgtSlots = serviceInterfaces.shifuClient.getSGTSlots(new GetSGTSlotsRequest(dates));
        Map<String, List<SgtSlotEntry>> dateSlotsMap = sgtSlots.stream().collect(Collectors.groupingBy(SgtSlotEntry::getSlotDate));

        // Populating fitness plan from fitnessPlanEntry
        List<FitnessPlanDetail> fitnessPlanDetailList = new ArrayList<>();
        for (WorkoutDetailEntry workoutDetailEntry : fitnessPlanEntry.getWorkoutDetailEntryList()) {
            FitnessPlanDetail fitnessPlanDetail = new FitnessPlanDetail();
            fitnessPlanDetail.setDate(TimeUtil.formatDateInTimezone(TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()), new Date(workoutDetailEntry.getDate()), TimeUtil.DEFAULT_TIME_FORMAT));
            fitnessPlanDetail.setType(workoutDetailEntry.getType().name());

            if (Objects.equals(fitnessPlanDetail.getDate(), userFitnessPlanPageView.getCurrentDate())) {
                userFitnessPlanPageView.setCurrentDayNumber(getDayNumber(workoutDetailEntry.getDayNumber()));
                currentDayFitnessPlanWorkoutStatus = workoutDetailEntry.getStatus();
            }

            if (workoutDetailEntry.getType().equals(WorkoutType.LIVE)) {
                fitnessPlanDetail.setBaseWidgets(getNowLiveWidgetForDIYProductV2(diyProductMap.get(workoutDetailEntry.getResource().getLiveClassProductId()), workoutDetailEntry));
            } else if (workoutDetailEntry.getType().equals(WorkoutType.WOD) || workoutDetailEntry.getType().equals(WorkoutType.GYM_WOD)) {
                fitnessPlanDetail.setBaseWidgets(getNowLiveWidgetForWodV2(serviceInterfaces, workoutDetailEntry));
            } else if (workoutDetailEntry.getType().equals(WorkoutType.CULT_GX)) {
                fitnessPlanDetail.setBaseWidgets(getNowLiveWidgetForCultGX(serviceInterfaces, workoutDetailEntry));
            } else if (workoutDetailEntry.getType().equals(WorkoutType.SPORT)) {
                fitnessPlanDetail.setBaseWidgets(getNowLiveWidgetForSports(serviceInterfaces, workoutDetailEntry));
            } else {
                fitnessPlanDetail.setEmptyMessage(getMessageForRestDay());
            }

            if (userFitnessPlanPageView.getCurrentDate().equals(fitnessPlanDetail.getDate())) {
                currentDayWorkoutType = fitnessPlanDetail.getType();
                fitnessPlanDetail.setSgtClasses(getSGTWidgets(dateSlotsMap.get(fitnessPlanDetail.getDate()), true, userContext));
            } else if (currentDate.getTime() <= workoutDetailEntry.getDate()) {
                fitnessPlanDetail.setSgtClasses(getSGTWidgets(dateSlotsMap.get(fitnessPlanDetail.getDate()), false, userContext));
            }

            fitnessPlanDetailList.add(fitnessPlanDetail);
        }

        if (userFitnessPlanPageView.getCurrentDayNumber() == null || userFitnessPlanPageView.getCurrentDayNumber().isEmpty()) {
            userFitnessPlanPageView.setCurrentDayNumber("0");
            userFitnessPlanPageView.setSwapWithTodayActive(false);
        } else {
            userFitnessPlanPageView.setSwapWithTodayActive(currentDayFitnessPlanWorkoutStatus == FitnessPlanWorkoutStatus.SCHEDULED);
        }

        if(WorkoutType.CULT_GX.name().equalsIgnoreCase(currentDayWorkoutType)){
            userFitnessPlanPageView.setSwapWithTodayActive(false);
        }

        if (userContext.getSessionInfo().getAppVersion() <= 10.27f) {
            userFitnessPlanPageView.setSwapWithTodayActive(false);
        }

        userFitnessPlanPageView.setFitnessPlanId(fitnessPlanEntry.getId());
        userFitnessPlanPageView.setPlanList(fitnessPlanDetailList);
        userFitnessPlanPageView.setSeparatorText(" OR ");
        setRedoFlagForFitnessPlan(userFitnessPlanPageView, currentDayWorkoutType, currentDayFitnessPlanWorkoutStatus);

        return userFitnessPlanPageView;
    }

    public void setRedoFlagForFitnessPlan(UserFitnessPlanPageView userFitnessPlanPageView, String currentDayType, FitnessPlanWorkoutStatus currentDayFitnessPlanWorkoutStatus) {
        if (userFitnessPlanPageView != null && userFitnessPlanPageView.getCurrentDate() != null
                && userFitnessPlanPageView.getPlanList() != null && currentDayType != null) {

            String currentDate = userFitnessPlanPageView.getCurrentDate();
            List<FitnessPlanDetail> planList = userFitnessPlanPageView.getPlanList();

            for (FitnessPlanDetail planDetail : planList) {
                setRedoAllowedForType(planDetail, currentDayType, currentDate, currentDayFitnessPlanWorkoutStatus);
            }
        }
    }

    private void setRedoAllowedForType(FitnessPlanDetail planDetail, String currentDayWorkoutType, String currentDate, FitnessPlanWorkoutStatus currentDayFitnessPlanWorkoutStatus) {
        String thisDayWorkoutType = planDetail.getType();

        if (planDetail.getDate().compareTo(currentDate) >= 0) {
            planDetail.setIsRedoAllowed(false);
            return;
        }

        // redo will not applicable if currentDayFitnessPlanWorkoutStatus is completed
        if (currentDayFitnessPlanWorkoutStatus.equals(FitnessPlanWorkoutStatus.COMPLETED)) {
            planDetail.setIsRedoAllowed(false);
            return;
        }

        switch (currentDayWorkoutType) {
            case "GYM_WOD":
                planDetail.setIsRedoAllowed(thisDayWorkoutType.equals("GYM_WOD"));
                break;

            case "WOD":
                planDetail.setIsRedoAllowed(thisDayWorkoutType.equals("WOD"));
                break;

            default:
                planDetail.setIsRedoAllowed(!thisDayWorkoutType.equals("WOD") && !thisDayWorkoutType.equals("GYM_WOD"));
                break;
        }
    }

    private Map<String, DIYProduct> getDIYProductMap(FitnessPlanEntry fitnessPlanEntry, ServiceInterfaces serviceInterfaces, Long userId) {
        List<String> diyProductIds = fitnessPlanEntry.getWorkoutDetailEntryList().stream()
                .filter(workoutDetailEntry -> workoutDetailEntry.getResource() != null && StringUtils.hasLength(workoutDetailEntry.getResource().getLiveClassProductId()))
                .map(workoutDetailEntry -> workoutDetailEntry.getResource().getLiveClassProductId())
                .distinct()
                .collect(Collectors.toList());

        return serviceInterfaces.diyfsService.getDIYFitnessProductsByProductIds(
                userId.toString(),
                diyProductIds,
                Tenant.CUREFIT_APP
        ).stream().collect(Collectors.toMap(DIYProduct::getProductId, Function.identity()));
    }

    private List<Action> getPageActions(ServiceInterfaces serviceInterfaces, UserContext userContext, String subCategoryCode) throws ExecutionException, InterruptedException {
        List<Action> pageActions = new ArrayList<>();
        Action scheduleCallAction = TransformUtil.getTransformFitnessCoachCall(serviceInterfaces, userContext, subCategoryCode);
        if (scheduleCallAction != null) {
            scheduleCallAction.setTitle("SCHEDULE A CALL");
            pageActions.add(scheduleCallAction);
        }
        Action connectWhatsAppAction = new Action("https://wa.me/" + TransformUtil.fitnessCoachWhatsAppBusinessAccountNumber, "CONNECT ON WHATSAPP", ActionType.OPEN_WEBPAGE);
        pageActions.add(connectWhatsAppAction);

        return pageActions;
    }

    private String getCurrentDate(String timezone) {
        Date now = new Date();
        return TimeUtil.formatDateInTimezone(TimeZone.getTimeZone(timezone), now, TimeUtil.DEFAULT_TIME_FORMAT);
    }

    private FitnessPlanDetail getFitnessPlanEmptyStateIfRequired(ServiceInterfaces serviceInterfaces, UserContext userContext, FitnessPlanEntry fitnessPlanEntry, String subCategoryCode) throws ExecutionException, InterruptedException {
        Date currentDate = new Date();

        FitnessPlanDetail fitnessPlanDetail = null;
        FitnessPlanEmptyMessage fitnessPlanEmptyMessage = null;
        List<Action> pageActions = new ArrayList<>();

        if (fitnessPlanEntry == null) {
            fitnessPlanEmptyMessage = getEmptyMessage(true, true);
        } else if (CollectionUtils.isEmpty(fitnessPlanEntry.getWorkoutDetailEntryList())
                || new Date(fitnessPlanEntry.getWorkoutDetailEntryList().get(fitnessPlanEntry.getWorkoutDetailEntryList().size() - 1).getDate()).before(currentDate)) {
            if (checkFitnessCoachCallScheduledOrRecentlyCompleted(serviceInterfaces, userContext.getUserProfile().getUserId(), userContext)) {
                fitnessPlanEmptyMessage = getEmptyMessage(false, false);
            } else {
                fitnessPlanEmptyMessage = getEmptyMessage(false, true);
                pageActions = getPageActions(serviceInterfaces, userContext, subCategoryCode);
            }
        }

        if (fitnessPlanEmptyMessage != null) {
            Date date = TimeUtil.getStartOfDay(currentDate, userContext.getUserProfile().getTimezone());
            fitnessPlanDetail = new FitnessPlanDetail();
            fitnessPlanDetail.setDate(TimeUtil.formatDateInTimezone(TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()),
                    date, TimeUtil.DEFAULT_TIME_FORMAT));

            fitnessPlanDetail.setEmptyMessage(fitnessPlanEmptyMessage);
            fitnessPlanDetail.setPageActions(pageActions);
        }

        return fitnessPlanDetail;
    }

    private FitnessPlanEmptyMessage getEmptyMessage(boolean planNotCreated, boolean sessionNotDone) {
        if (planNotCreated) {
            return FitnessPlanEmptyMessage.builder()
                    .header("YOUR FITNESS PLAN IS YET TO BE CREATED")
                    .description("Your Fitness Coach will update the plan soon")
                    .imageUrl("/image/transform/theo-phone-standing.png")
                    .build();
        }

        if (sessionNotDone) {
            return FitnessPlanEmptyMessage.builder()
                    .header("YOUR FITNESS PLAN HAS EXPIRED")
                    .description("Connect with your fitness coach to get your fitness plan updated")
                    .imageUrl("/image/transform/theo-phone-standing.png")
                    .build();
        }

        return FitnessPlanEmptyMessage.builder()
                .header("YOUR FITNESS PLAN HAS EXPIRED")
                .description("Your Fitness Coach will update the plan soon")
                .imageUrl("/image/transform/theo-phone-standing.png")
                .build();
    }

    private NowLiveWidget getNowLiveWidgetForWod(ServiceInterfaces serviceInterfaces, WorkoutDetailEntry workoutDetailEntry) throws ExecutionException, InterruptedException {
        NowLiveWidget nowLiveWidget = new NowLiveWidget();
        nowLiveWidget.setWidgetType(WidgetType.NOW_LIVE_WIDGET_V2);

        nowLiveWidget.setLayoutProps(getLayoutProps());
        nowLiveWidget.setTitle("WORKOUT OF THE DAY");
        nowLiveWidget.setWidgets(Collections.singletonList(getSessionCardWidgetForWod(serviceInterfaces, workoutDetailEntry.getResource(), workoutDetailEntry.getType())));

        return nowLiveWidget;
    }

    private List<BaseWidget> getNowLiveWidgetForWodV2(ServiceInterfaces serviceInterfaces, WorkoutDetailEntry workoutDetailEntry) throws Exception {
        FitnessPlanWidgetV2 fitnessPlanWidgetV2 = new FitnessPlanWidgetV2();
        FitnessPlanV2Item item = new FitnessPlanV2Item();
        WorkoutResource workoutResource = workoutDetailEntry.getResource();
        String workoutTitle = "";

        UserWodReportV2 userWodReportV2 = serviceInterfaces.ufsService.userWod().getUserWodReportV2(workoutResource.getWodId()).get();
        UserWodEntry userWodEntry = userWodReportV2.getUserWod();
        if (workoutDetailEntry.getWorkoutDisplayInfo() != null) {
            String primary = workoutDetailEntry.getWorkoutDisplayInfo().getPrimary();
            String secondary = workoutDetailEntry.getWorkoutDisplayInfo().getSecondary();
            if (!primary.isEmpty()) {
                workoutTitle = primary;
            }
            if (!secondary.isEmpty()) {
                workoutTitle.concat(" & " + secondary);
            }
            if (!primary.isEmpty() || !secondary.isEmpty()) {
                workoutTitle.concat(" workout");
            }
        }

        item.setWorkoutName(workoutDetailEntry.getType().equals(WorkoutType.WOD) ? "Home workout" : "Gym Workout");
        item.setWorkoutTitle(workoutTitle.isEmpty() ? userWodEntry.getWorkoutName() : workoutTitle);
        item.setWorkoutSubTitle("WORKOUT OF THE DAY");
        item.setAction(getActionForWod(workoutResource, workoutDetailEntry.getType()));
        item.setWorkoutDuration(getDurationStringFromPreWodDuration(workoutResource.getWodDuration()));
        item.setWorkoutImageUrl(getWodImageUrlForThumbnail(serviceInterfaces, userWodEntry));
        if (workoutDetailEntry.getStatus() != null) {
            item.setStatusImage("https://curefit-content.s3.ap-south-1.amazonaws.com/transform-coach/fitness-plan-resources/app/" + workoutDetailEntry.getStatus().name().toLowerCase() + ".png");
            item.setFitnessPlanWorkoutStatus(workoutDetailEntry.getStatus().toString());
        }
        item.setDayNumber(getDayNumber(workoutDetailEntry.getDayNumber()));
        item.setWorkoutType(workoutDetailEntry.getType());
        return fitnessPlanWidgetV2.buildView(item);
    }

    private List<BaseWidget> getNowLiveWidgetForCultGX(ServiceInterfaces serviceInterfaces, WorkoutDetailEntry workoutDetailEntry) throws Exception {
        FitnessPlanWidgetV2 fitnessPlanWidgetV2 = new FitnessPlanWidgetV2();
        FitnessPlanV2Item item = new FitnessPlanV2Item();
        WorkoutResource workoutResource = workoutDetailEntry.getResource();

        item.setWorkoutName("Group Workout");
        if (workoutResource.getWorkoutName() != null) {
            item.setWorkoutTitle(getCultGXWorkoutTitleMap().get(workoutResource.getWorkoutName()));
        }
        item.setWorkoutSubTitle(workoutResource.getWorkoutName());
        item.setWorkoutDuration("50 Mins");
        item.setAction(getActionForCultGX());
        item.setWorkoutImageUrl("https://curefit-content.s3.ap-south-1.amazonaws.com/transform-coach/fitness-plan-resources/app/" + workoutResource.getWorkoutName().toLowerCase() + ".png");
        if (workoutDetailEntry.getStatus() != null) {
            item.setStatusImage("https://curefit-content.s3.ap-south-1.amazonaws.com/transform-coach/fitness-plan-resources/app/" + workoutDetailEntry.getStatus().name().toLowerCase() + ".png");
            item.setFitnessPlanWorkoutStatus(workoutDetailEntry.getStatus().toString());
        }
        item.setDayNumber(getDayNumber(workoutDetailEntry.getDayNumber()));
        item.setWorkoutType(workoutDetailEntry.getType());
        return fitnessPlanWidgetV2.buildView(item);
    }

    private List<BaseWidget> getSGTWidgets(List<SgtSlotEntry> slots, boolean isCurrentDate, UserContext userContext) throws Exception {
        if (CollectionUtils.isEmpty(slots)) {
            return null;
        }

        Date currentDate = new Date();
        List<FitnessPlanSGTItem> fitnessPlanSGTItems = new ArrayList<>();

        slots.sort(Comparator.comparing(SgtSlotEntry::getFromTime));
        for (SgtSlotEntry slot : slots) {
            String status = "UPCOMING";
            DateFormat dateFormat1 = new SimpleDateFormat("hh:mm:ss");
            DateFormat dateFormat2 = new SimpleDateFormat("hh:mm aa");
            Date startingTime = dateFormat1.parse(slot.getFromTime());
            String slotTiming = dateFormat2.format(startingTime);

            if (isCurrentDate) {
                Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()));
                calendar.setTime(currentDate);
                calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(slot.getFromTime().substring(0, 2)));
                calendar.set(Calendar.MINUTE, Integer.parseInt(slot.getFromTime().substring(3, 5)));
                calendar.set(Calendar.SECOND, Integer.parseInt(slot.getFromTime().substring(6, 8)));
                Date slotDate = calendar.getTime();
                if (slotDate.getTime() <= currentDate.getTime()) {
                    long minDiff = (currentDate.getTime() - slotDate.getTime()) / (1000L * 60L);
                    if (minDiff <= 10) {
                        status = "ACTIVE";
                    } else {
                        continue;
                    }
                } else {
                    long minDiff = (slotDate.getTime() - currentDate.getTime()) / (1000L * 60L);
                    if (minDiff <= 5) {
                        status = "ACTIVE";
                    }
                }
            }

            FitnessPlanSGTItem fitnessPlanSGTItem = FitnessPlanSGTItem.builder()
                    .workoutName(slot.getWorkoutInfo().getWorkoutName())
                    .coachName(slot.getCoachName())
                    .workoutImageUrl(slot.getWorkoutInfo().getWorkoutImageUrl())
                    .slotStatus(status)
                    .slotTiming(slotTiming)
                    .zoomLink(slot.getZoomLink())
                    .slotId(slot.getId().toString())
                    .coachId(slot.getCoachId().toString())
                    .build();
            fitnessPlanSGTItems.add(fitnessPlanSGTItem);
        }

        List<BaseWidget> sgtWidgets = new ArrayList<>();
        for (FitnessPlanSGTItem fitnessPlanSGTItem : fitnessPlanSGTItems) {
            FitnessPlanSGTWidget fitnessPlanSGTWidget = new FitnessPlanSGTWidget();
            sgtWidgets.addAll(fitnessPlanSGTWidget.buildView(fitnessPlanSGTItem));
        }
        return sgtWidgets;
    }

    private String getDayNumber(Long dayNumber) {
        return dayNumber != null ? String.valueOf(dayNumber) : null;
    }

    private Map<String, String> getCultGXWorkoutTitleMap() {
        return Map.of("STRENGTH", "HRX • S&C",
                "BOXING", "BOXING BAG WORKOUT",
                "DANCE", "DANCE FITNESS",
                "BURN", "BURN",
                "YOGA", "YOGA");
    }

    private List<BaseWidget> getNowLiveWidgetForSports(ServiceInterfaces serviceInterfaces, WorkoutDetailEntry workoutDetailEntry) throws Exception {
        FitnessPlanWidgetV2 fitnessPlanWidgetV2 = new FitnessPlanWidgetV2();
        FitnessPlanV2Item item = new FitnessPlanV2Item();
        WorkoutResource workoutResource = workoutDetailEntry.getResource();

        item.setWorkoutName("Sports");
        item.setWorkoutTitle(workoutResource.getWorkoutName());
        item.setWorkoutSubTitle(getSportsWidgetSubTitle());
        item.setAction(getActionForSports());
        item.setWorkoutImageUrl("https://curefit-content.s3.ap-south-1.amazonaws.com/transform-coach/fitness-plan-resources/app/" + workoutResource.getWorkoutName().toLowerCase() + ".png");
        if (workoutDetailEntry.getStatus() != null) {
            item.setStatusImage("https://curefit-content.s3.ap-south-1.amazonaws.com/transform-coach/fitness-plan-resources/app/" + workoutDetailEntry.getStatus().name().toLowerCase() + ".png");
            item.setFitnessPlanWorkoutStatus(workoutDetailEntry.getStatus().toString());
        }
        item.setDayNumber(getDayNumber(workoutDetailEntry.getDayNumber()));
        item.setWorkoutType(workoutDetailEntry.getType());
        return fitnessPlanWidgetV2.buildView(item);
    }

    private String getSportsWidgetSubTitle() {
        List<String> subTitles = List.of("The longer you stay active in sports, the more your muscles stay stronger",
                "Consistent sports activity fosters lasting improvements in overall fitness.",
                "Continued engagement in sports elevates your overall fitness prowess.",
                "Through continued sports involvement, your resilience and agility improve.",
                "Sustained participation in sports nurtures enduring mental and physical strength.");
        return subTitles.get(new Random().nextInt(subTitles.size()));
    }

    private Action getActionForSports() {
        Action action = new Action();
        action.setActionType(ActionType.FITNESS_PLAN_SPORTS_MODAL);
        action.setTitle("MARK AS DONE");
        FitnessPlanSportsModalView fitnessPlanSportsModalView = new FitnessPlanSportsModalView();
        fitnessPlanSportsModalView.setQuestion("How much time did you play");
        fitnessPlanSportsModalView.setOptions(List.of("30 mins", "45 mins", "1 hour", "2 hours", ">3 hours"));
        fitnessPlanSportsModalView.setCta("SUBMIT");
        HashMap<String, Object> meta = new HashMap<>();
        meta.put("fitnessPlanSportsModalView", fitnessPlanSportsModalView);
        action.setMeta(meta);
        return action;
    }

    private Action getActionForCultGX() {
        return new Action("curefit://classbookingv2?productType=FITNESS", "BOOK CULT CLASS", ActionType.NAVIGATION);
    }

    NowLiveSessionWidget getSessionCardWidgetForWod(ServiceInterfaces interfaces, WorkoutResource workoutResource, WorkoutType type) throws ExecutionException, InterruptedException {
        NowLiveSessionWidget sessionWidget = new NowLiveSessionWidget();
        UserWodReportV2 userWodReportV2 = interfaces.ufsService.userWod().getUserWodReportV2(workoutResource.getWodId()).get();
        UserWodEntry userWodEntry = userWodReportV2.getUserWod();
        sessionWidget.setTrainers("WORKOUT OF THE DAY");
        sessionWidget.setTitle(userWodEntry.getWorkoutName());
        sessionWidget.setSubTitle(getDurationStringFromPreWodDuration(workoutResource.getWodDuration()));
        sessionWidget.setDuration(getDurationStringFromPreWodDuration(workoutResource.getWodDuration()));
        sessionWidget.setImage(getWodImageUrlForThumbnail(interfaces, userWodEntry));
        Action wodAction = getActionForWod(workoutResource, type);
        sessionWidget.setAction(wodAction);
        sessionWidget.setCardAction(wodAction);
        sessionWidget.setPlay(true);
        return sessionWidget;
    }

    private String getWodImageUrlForThumbnail(ServiceInterfaces interfaces, UserWodEntry userWodEntry) {
        try {
            WorkoutSetEntry mainWorkoutSetEntry = userWodEntry.getSets().stream().filter(workoutSetEntry -> workoutSetEntry.getBlockType().equals(BlockType.WORKOUT)).findFirst().orElse(null);

            if (mainWorkoutSetEntry == null || mainWorkoutSetEntry.getExercises() == null || mainWorkoutSetEntry.getExercises().isEmpty()) {
                return DEFAULT_WORKOUT_IMAGE_URL;
            }

            ExerciseEntry mainWorkoutExercise = mainWorkoutSetEntry.getExercises().get((int) (Math.random() * mainWorkoutSetEntry.getExercises().size()));
            Exercise exerciseDetail = interfaces.herculesService.exerciseClient().getById(mainWorkoutExercise.getExerciseId());
            List<Media> thumbnailImageMediaList = exerciseDetail.getMedia().stream().filter(media -> media.getType().contains("THUMBNAIL")).toList();

            if (CollectionUtils.isEmpty(thumbnailImageMediaList)) {
                return DEFAULT_WORKOUT_IMAGE_URL;
            }

            for (Media media : thumbnailImageMediaList) {
                if (media.getUrl() != null && !media.getUrl().contains("Rest")) {
                    return CDNUtil.getCdnUrl(media.getUrl());
                }
            }
        } catch (BaseException e) {
            log.error("Error while getting image url for wod id {}", userWodEntry.getId(), e);
        }
        return DEFAULT_WORKOUT_IMAGE_URL;
    }

    Action getActionForWod(WorkoutResource workoutResource, WorkoutType type) {
        if (workoutResource.getTenantId() == null) {
            workoutResource.setTenantId(WorkoutType.GYM_WOD.equals(type) ? 10L : 2L);
        }
        Instant startTime = Instant.now();
        return new Action(
                "curefit://fitnessplanpage?cycleDay=" + workoutResource.getCycleDay()
                        + "&mesoCycleId=" + workoutResource.getMesoCycleId() + "&microCycleSequence="
                        + workoutResource.getMicroCycleSequence() + "&fullScreenMode=true&source=TRANSFORM&tenantId=" + workoutResource.getTenantId() + "&fromFlutter=true&wodStartTime=" + startTime.toEpochMilli(),
                "START NOW",
                ActionType.NAVIGATION
        );
    }

    private NowLiveWidget getNowLiveWidgetForDIYProduct(DIYProduct diyProduct) {
        NowLiveWidget nowLiveWidget = new NowLiveWidget();
        nowLiveWidget.setWidgetType(WidgetType.NOW_LIVE_WIDGET_V2);

        nowLiveWidget.setLayoutProps(getLayoutProps());
        nowLiveWidget.setTitle("LIVE CLASS");
        nowLiveWidget.setWidgets(Collections.singletonList(getSessionCardWidgetForDIYProduct(diyProduct)));

        return nowLiveWidget;
    }

    private List<BaseWidget> getNowLiveWidgetForDIYProductV2(DIYProduct diyProduct, WorkoutDetailEntry workoutDetailEntry) throws Exception {
        FitnessPlanWidgetV2 fitnessPlanWidgetV2 = new FitnessPlanWidgetV2();
        FitnessPlanV2Item item = new FitnessPlanV2Item();
        item.setWorkoutName("Video Class");
        if (diyProduct != null) {
            item.setWorkoutTitle(diyProduct.getTitle());
            item.setWorkoutSubTitle(diyProduct.getTrainerName());
            item.setWorkoutDuration(getSubtitleForDIYProductCard(diyProduct.getFormat(), diyProduct.getDuration(), diyProduct.getIntensityLevel()));
            if (diyProduct.getImageDetails() != null) {
                item.setWorkoutImageUrl(diyProduct.getImageDetails().getThumbnailImage());
            }
            item.setAction(getActionForDIYProduct(diyProduct));
        }
        if (workoutDetailEntry.getStatus() != null) {
            item.setStatusImage("https://curefit-content.s3.ap-south-1.amazonaws.com/transform-coach/fitness-plan-resources/app/" + workoutDetailEntry.getStatus().name().toLowerCase() + ".png");
            item.setFitnessPlanWorkoutStatus(workoutDetailEntry.getStatus().toString());
        }
        item.setDayNumber(getDayNumber(workoutDetailEntry.getDayNumber()));
        item.setWorkoutType(workoutDetailEntry.getType());
        return fitnessPlanWidgetV2.buildView(item);
    }

    NowLiveSessionWidget getSessionCardWidgetForDIYProduct(DIYProduct diyProduct) {
        NowLiveSessionWidget sessionWidget = new NowLiveSessionWidget();
        sessionWidget.setTitle(diyProduct.getTitle());

        if (diyProduct.getImageDetails() != null) {
            sessionWidget.setImage(diyProduct.getImageDetails().getThumbnailImage());
        }

        sessionWidget.setTrainers(diyProduct.getTrainerName());
        sessionWidget.setSubTitle(getSubtitleForDIYProductCard(diyProduct.getFormat(),
                diyProduct.getDuration(),
                diyProduct.getIntensityLevel()));

        sessionWidget.setRenderPlay(false);
        sessionWidget.setVideoDisabled(false);
        sessionWidget.setPlay(true);

        sessionWidget.setCardAction(getCardActionForDIYProduct(diyProduct));
        sessionWidget.setAction(getActionForDIYProduct(diyProduct));

        return sessionWidget;
    }

    String getSubtitleForDIYProductCard(LiveFitWorkoutFormat format, Long duration, LiveWorkoutIntensityLevel intensityLevel) {
        List<String> subTitleStrings = new ArrayList<>();
        if (format != null) {
            subTitleStrings.add(format.name());
        }
        if (intensityLevel != null) {
            subTitleStrings.add(intensityLevel.name());
        }
        subTitleStrings.add(LiveUtil.getFormattedTimeString(duration));

        return String.join(" \u2022 ", subTitleStrings);
    }

    Action getCardActionForDIYProduct(DIYProduct diyProduct) {
        return new Action(
                "curefit://liveclassdetail?liveClassId=" + diyProduct.getProductId() + "&bookingNumber=" + diyProduct.getProductId()
                        + "&productType=LIVE_FITNESS&isDIY=true",
                ActionType.NAVIGATION
        );
    }

    Action getActionForDIYProduct(DIYProduct diyProduct) {
        return LiveUtil.getDIYSessionAction(diyProduct, TransformUtil.tenant, null, WidgetType.NOW_LIVE_SESSION_WIDGET.name());
    }

    private Object getLayoutProps() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("spacing", Spacing.builder().top("0").bottom("70").build());
        return layoutProps;
    }

    private FitnessPlanEmptyMessage getMessageForRestDay() {
        return FitnessPlanEmptyMessage.builder()
                .header("It’s your day off. Sleep well, Drink enough water. See you at the next workout!")
                .description("It’s your Rest Day")
                .imageUrl("/image/transform/rest-day-smiley.png")
                .build();
    }

    String getDurationStringFromPreWodDuration(PreWodDuration preWodDuration) {
        return switch (preWodDuration) {
            case LESS_THAN_15_MINS -> "< 15 Mins";
            case FIFTEEN_TO_THIRTY_MINS -> "15-30 Mins";
            case THIRTY_TO_FORTY_FIVE_MINS -> "30-45 Mins";
            case FORTY_FIVE_TO_SIXTY_MINS -> "45-60 Mins";
            case MORE_THAN_SIXTY_MINS -> "> 60 Mins";
        };
    }

    private Boolean checkFitnessCoachCallScheduledOrRecentlyCompleted(ServiceInterfaces interfaces, String userId, UserContext userContext) throws ExecutionException, InterruptedException {
        List<UserActiveAgentResponse> coachDetailsList = (List<UserActiveAgentResponse>) userContext.getRequestCache().getRequestFuture(RequestType.GET_TRANSFORM_COACHES, TransformUtil.plusSubCategoryCode, userContext, true).get();
        // pick nutritionist coachId
        Long fitnessCoachId = null;
        for (UserActiveAgentResponse coach : coachDetailsList) {
            if (TransformUtil.fitnessCoachAgentType.equals(coach.getAgentType())) {
                fitnessCoachId = coach.getAgent().getId();
                break;
            }
        }

        ConsultationSearchRequestParamModel requestParamModel = new ConsultationSearchRequestParamModel();
        requestParamModel.setCustomerId(userId);
        requestParamModel.setDoctorId(fitnessCoachId);
        requestParamModel.setStartTimeEpoch(Instant.now().minus(14, ChronoUnit.DAYS).toEpochMilli());
        List<CarefitConsultationOrderResponse> consultations = interfaces.transformClient.searchCareFitConsultationOrdersWithStatusList(requestParamModel, userId, List.of("COMPLETED", "SCHEDULED"));

        return consultations.size() > 0;
    }

}
