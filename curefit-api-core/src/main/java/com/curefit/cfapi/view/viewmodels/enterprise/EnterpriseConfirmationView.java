package com.curefit.cfapi.view.viewmodels.enterprise;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EnterpriseConfirmationView {

    public static EnterpriseConfirmationView EMPTY_VIEW = new EnterpriseConfirmationView();

    private Action action;
    private List<BaseWidgetNonVM> widgets;

    public EnterpriseConfirmationView addWidget(BaseWidgetNonVM widget) {
        if (widgets == null) {
            widgets = new ArrayList<>();
        }
        widgets.add(widget);
        return this;
    }
}
