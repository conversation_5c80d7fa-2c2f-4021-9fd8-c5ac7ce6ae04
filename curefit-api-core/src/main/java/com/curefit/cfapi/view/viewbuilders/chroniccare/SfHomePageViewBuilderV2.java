package com.curefit.cfapi.view.viewbuilders.chroniccare;

import com.curefit.albus.booking.ActiveConsultationResponse;
import com.curefit.albus.common.BundleProduct;
import com.curefit.albus.common.PatientPreferredAgentResponse;
import com.curefit.albus.common.enums.CGMReaderType;
import com.curefit.albus.common.pojo.DiagnosticJourneyResponse;
import com.curefit.albus.common.pojo.kickstarter.*;
import com.curefit.albus.common.pojo.kickstarter.KSCGMJourneyContext.TierOneDeliveryAndInstallationContext;
import com.curefit.albus.common.pojo.kickstarter.KSCGMJourneyContext.TierTwoNFCDeliveryAndSelfInstallationContext;
import com.curefit.albus.common.pojo.kickstarter.KSCGMJourneyContext.TierTwoNonNFCDeliveryAndInstallationContext;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.SelfInstallationActions;
import com.curefit.albus.response.UserMembershipInfo;
import com.curefit.albus.response.actions.UserPreferencePojo;
import com.curefit.albus.response.chronic.UserOnboardingActionWithContext;
import com.curefit.albus.response.group_class.ActiveGroupClassOrderResponse;
import com.curefit.albus.service.AlbusClient;
import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.dto.ChronicCareTeam;
import com.curefit.cfapi.dto.sugarfit.*;
import com.curefit.cfapi.dto.sugarfit.kickstart.SfKickStartCGMCard;
import com.curefit.cfapi.dto.sugarfit.kickstart.SfKickStartCard;
import com.curefit.cfapi.dto.sugarfit.kickstart.SfKickStartData;
import com.curefit.cfapi.model.internal.chroniccare.FitnessDeviceSyncMeta;
import com.curefit.cfapi.model.internal.chroniccare.SfAppScratchCardConfig;
import com.curefit.cfapi.model.internal.chroniccare.SfHomepageBannerConfig;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionMeta;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.chroniccare.ecommerce.SfECommerceProduct;
import com.curefit.cfapi.pojo.chroniccare.experiencecenter.SfWellnessTherapyProduct;
import com.curefit.cfapi.pojo.chroniccare.nux.DiagnosticActionMeta;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.DeviceService;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.ChronicCarePatientService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.digitalapp.DigiTasksToDoCardsBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.RenewalAlertWidgetBuilderV2;
import com.curefit.cfapi.view.viewbuilders.chroniccare.thingstodo.ThingsToDoCardsBuilder;
import com.curefit.cfapi.view.viewmodels.chroniccare.ActiveCard;
import com.curefit.cfapi.view.viewmodels.chroniccare.ActiveCardStatus;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfAppUpdateConfig;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfHomePageView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.*;
import com.curefit.cfapi.widgets.chroniccare.cgm.SfBluconJourneyWidget;
import com.curefit.cfapi.widgets.chroniccare.challenges.SfChallengeCardWidgetV2;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DAProgramJourneyWidget;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DigiLessonsCarouselWidget;
import com.curefit.cfapi.widgets.chroniccare.ecommerce.SfDiabetesStoreHomeDarkWidget;
import com.curefit.cfapi.widgets.chroniccare.diagnosticTestStore.SfDoctorRecommendedTestWidget;
import com.curefit.cfapi.widgets.chroniccare.ecommerce.SfDiabetesStoreHomeWidget;
import com.curefit.cfapi.widgets.chroniccare.ecommerce.SfEComProductCardWidget;
import com.curefit.cfapi.widgets.chroniccare.experiencecenter.SfWellnessAtCenterTherapyListCardWidgetV2;
import com.curefit.cfapi.widgets.chroniccare.renewal.SfRenewalReportHomeWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.diyfs.pojo.LiveClass;
import com.curefit.iris.models.NotificationMeta;
import com.curefit.ollivander.common.exception.OllivanderClientException;
import com.curefit.ollivander.common.pojo.response.center.CenterBaseResponse;
import com.curefit.ollivander.common.pojo.response.center.CenterResponseV2;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.sfalbus.response.DeviceShipmentResponse;
import com.curefit.sfalbus.response.kickstarter.KSJourneyV2Response;
import com.curefit.shifu.pojo.goal.summary.GoalWithData;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.userservice.pojo.entry.DeviceDetailEntry;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sugarfit.challenges.client.ChallengesClient;
import com.sugarfit.challenges.pojo.ChallengesEntry;
import com.sugarfit.challenges.pojo.ChallengesUserResponseEntry;
import com.sugarfit.challenges.pojo.UserChallengeSummary;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.enums.DeviceModel;
import com.sugarfit.chs.enums.DeviceStatus;
import com.sugarfit.chs.enums.ReadingMode;
import com.sugarfit.chs.pojo.*;
import com.sugarfit.chs.pojo.blucon.BluconEventEntry;
import com.sugarfit.chs.pojo.cgmstat.CgmStat;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalScansForDayResponse;
import com.sugarfit.indus.IndusClient;
import com.sugarfit.indus.reponse.ProductResponse;
import com.sugarfit.indus.request.ProductSearchRequest;
import com.sugarfit.logging.client.LoggingClient;
import com.sugarfit.poll.client.PollSupportClient;
import com.sugarfit.poll.enums.PollType;
import com.sugarfit.poll.pojo.PollEntry;
import com.sugarfit.sms.client.SMSClient;
import com.sugarfit.sms.entry.UserRecommendationLogEntry;
import com.sugarfit.sms.entry.UserSubscriptionEntry;
import com.sugarfit.sms.enums.MetricThresholdLimit;
import com.sugarfit.sms.pojo.renewal_journey.RenewalJourneyResponse;
import com.sugarfit.sms.response.NUXStatusResponse;
import com.sugarfit.sms.response.UserTodoDaySummary;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.curefit.cfapi.pojo.app.action.ActionType.NAVIGATION;
import static com.curefit.cfapi.util.AppUtil.*;
import static com.curefit.cfapi.util.ChronicCareAppUtil.*;
import static com.curefit.cfapi.util.DigitalAppUtil.DIGITAL_AGENT_CENTER_ID;
import static com.curefit.cfapi.util.DigitalAppUtil.isDigitalAppUser;
import static com.curefit.cfapi.util.SfHomePageUtil.*;
import static com.curefit.cfapi.view.viewmodels.chroniccare.ActiveCardStatus.StatusType.*;
import static com.curefit.cfapi.view.viewmodels.chroniccare.SfHomePageView.*;
import static java.util.concurrent.CompletableFuture.supplyAsync;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
@Slf4j
@RequiredArgsConstructor
public class SfHomePageViewBuilderV2 {
    final CHSClient chsClient;
    final SMSClient smsClient;
    final PollSupportClient pollSupportClient;
    final ChallengesClient challengesClient;
    final IndusClient indusClient;
    final SegmentationCacheClient segmentationCacheClient;
    final UserAttributesClient userAttributesClient;
    final AppConfigCache appConfigCache;
    final ServiceInterfaces serviceInterfaces;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final OngoingExperimentsViewBuilder ongoingExperimentsViewBuilder;
    final ThingsToDoCardsBuilder thingsToDoCardsBuilder;
    final DeviceService deviceService;
    final UserOnboardingService userOnboardingService;
    final ChronicCarePatientService chronicCarePatientService;
    final ExceptionReportingService exceptionReportingService;
    final SfHomePageViewBuilder sfHomePageViewBuilderV1;
    final SfLiveSessionWidgetBuilder sfLiveSessionWidgetBuilder;
    final SugarFitBlogWidgetBuilder sugarFitBlogWidgetBuilder;
    final SfHomeHeaderWidgetBuilder sfHomeHeaderWidgetBuilder;
    final RenewalAlertWidgetBuilderV2 renewalAlertWidgetBuilderV2;
    final SfKickstartJourneyWidgetV2Builder sfKickstartJourneyWidgetV2Builder;
    @Qualifier("cfApiRedisKeyValueStore")
    final KeyValueStore cfApiRedisKeyValueStore;
    final DigiTasksToDoCardsBuilder digiTasksToDoCardsBuilder;
    final LoggingClient loggingClient;

    public SfHomePageView buildView(UserContext userContext, String cgmDeviceId, String sessionId, Integer cgmConfigVersion) throws Exception {
        SfHomePageView result = new SfHomePageView();
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            NUXStatusResponse nuxStatus = smsClient.getNUXStatus(userId, true, timeZone);

            SfAppUpdateConfig config = chronicCareServiceHelper.getSfAppUpdateConfig();

            if (chronicCareServiceHelper.isAppUnderMaintenance()) {
                result.addWidget(chronicCareServiceHelper.getMaintenanceBanner(false));
                return result;
            }

            if (shouldShowAppUpdatePopUp(userContext, config)) {
                result.setAppUpdateData(getAppUpdateData(userContext, config));
            }

//            if (nuxStatus != null && nuxStatus.getFreemium() && isFreemiumSupportedApp(userContext)) {
//                if (!nuxStatus.getNuxCompleted()) {
//                    result.addWidget(new AutoNavigationToNuxWidget());
//                } else {
//                    navigateUserToFreemiumHomePage(result);
//                }
//                return result;
//            }

            // Handling for Digital pack first time onboarding
            if ((Objects.nonNull(nuxStatus) && nuxStatus.getFreemium())
                    || (Objects.nonNull(nuxStatus) && nuxStatus.getActivePack() != null
                    && DigitalAppUtil.isDigitalAppUser(nuxStatus.getActivePack().getProductCode(), nuxStatus.getFreemium()))) {
                if (!isDigitalPackSupportedApp(userContext)) {
                    result.setAppUpdateData(getAppForceUpdateDataForNonDigitalApp());
                } else {
                    if (!nuxStatus.getNuxCompleted()) {
                        AutoNavigationToNuxWidget autoNavigationToNuxWidget = new AutoNavigationToNuxWidget();
                        autoNavigationToNuxWidget.setPageName("sfdigitalappnux");
                        result.addWidget(autoNavigationToNuxWidget);
                    } else {
                        AutoNavigationToNuxWidget autoNavigationToNuxWidget = new AutoNavigationToNuxWidget();
                        autoNavigationToNuxWidget.setPageName("sfdigihomepage");
                        result.addWidget(autoNavigationToNuxWidget);
                    }
                }
                return result;
            }

            // Handing for paid packs
            if (nuxStatus != null && nuxStatus.getActivePack() != null) {
                if (!nuxStatus.getFreemium() && !nuxStatus.getNuxCompleted()) {
                    result.addWidget(SfHomePageUtil.getPackPurchaseGreetingWidget(userContext));
                } else {
                    Long activePackBookingId = nuxStatus.getActivePack().getSubscriptionId();
                    if (ChronicCareAppUtil.isOldHomeBannerSupportedNewHomepageUser(userContext)) {
                        result.setV2Enabled(false);
                    } else {
                        result.setV2Enabled(true);
                    }
                    addPremiumHomePageWidgets(result, userContext, cgmDeviceId, cgmConfigVersion, sessionId, activePackBookingId);
                }
            } else {
                if (nuxStatus != null && nuxStatus.getUpcomingPack() != null) {
                    sfHomePageViewBuilderV1.addPackUpcomingPageWidgets(result, userContext, sessionId);
                } else if (nuxStatus != null && nuxStatus.getExpiredPack() != null) {
                    addPackExpiredPageWidgets(result, userContext, cgmDeviceId, cgmConfigVersion, sessionId);
                } else {
                    String errorMessage = "No active/upcoming/expired pack found for user";
                    exceptionReportingService.reportWarning(new Exception(errorMessage));
                    navigateUserToFreemiumHomePage(result);
                }
            }

            result.setUserDetails(getUserDetails(userContext));
            return result;
        } catch (Exception e) {
            String errorMessage = "Error in fetching homepage";
            exceptionReportingService.reportException(errorMessage, e);
            throw e;
        }
    }

    private void addPremiumHomePageWidgets(SfHomePageView result, UserContext userContext, String cgmDeviceId,
                                           Integer cgmConfigVersion, String sessionId, Long packBookingId) throws Exception {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        KSJourneyResponse kickStarterJourney = serviceInterfaces.getSfAlbusClient().getKickStarterJourney(userId,
                userContext.getUserProfile().getTimezone());
        boolean isAppLocked = kickStarterJourney != null && kickStarterJourney.isAppLocked();

        if (isAppLocked) {
            addKickStartPageData(result, userContext, kickStarterJourney, cgmDeviceId, cgmConfigVersion, packBookingId);
        } else {
            addAllUnlockedPageWidgets(result, userContext, kickStarterJourney, cgmDeviceId, cgmConfigVersion, sessionId, packBookingId);
        }
    }

    public void addPackExpiredPageWidgets(SfHomePageView result, UserContext userContext, String cgmDeviceId, Integer cgmConfigVersion, String sessionId) throws Exception {
        String userIdString = userContext.getUserProfile().getUserId();
        Long userId = Long.valueOf(userIdString);
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timezone = userContext.getUserProfile().getTimezone() != null ? TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()) : TimeZone.getTimeZone("Asia/Kolkata");
        CompletableFuture<PatientDetail> patientDetailFuture = chronicCarePatientService.getChronicCarePatientForUserIdFuture(userContext);
        CompletableFuture<ActivePackResponse> expiredPackResponseFuture = userOnboardingService.getSugarFitExpiredPackFuture(userIdString);
        CompletableFuture<ActivePackResponse> wellnessActivePackResponseFuture = userOnboardingService.getSugarFitWellnessActivePackResponseFuture(userIdString);
        CgmOnboardingStatusResponse cgmOnboardingStatus = chsClient.fetchOnboardingStatus(userId, null, getAppTenantFromUserContext(userContext));
        CompletableFuture<ChronicCareTeam> assignedCareTeamFuture = chronicCareServiceHelper.getAssignedCareTeamFuture(userContext, null, Objects.nonNull(expiredPackResponseFuture.get()) ? expiredPackResponseFuture.get().getBundleProduct() : null);
        CompletableFuture<RenewalJourneyResponse> renewalJourneyFuture = chronicCareServiceHelper.getRenewalJourneyFuture(userContext);
        ActivePackResponse expiredPackResponse = expiredPackResponseFuture.get();
        CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture = chronicCareServiceHelper.getConsultationBookingsFuture(userContext);
        CompletableFuture<UserOnboardingActionWithContext> onboardingActions = userOnboardingService.getUserOnboardingActionsFuture(userContext, expiredPackResponse != null ? expiredPackResponse.getBookingId() : null);
        CompletableFuture<CgmUserRequestStatus> cgmUserRequestStatusFuture = chronicCareServiceHelper.getCgmUserRequestStatusFuture(userId, appTenant); // TODO: move to onboarding status

        SfCgmDeviceStatus cgmDeviceStatus = expiredPackResponse != null  && (chronicCareServiceHelper.isQuickCommerceCGMPack(expiredPackResponse.getBundleProduct()) || chronicCareServiceHelper.isShopifyCGMPack(expiredPackResponse.getBundleProduct())) ? getCgmDeviceStatus(userContext, cgmOnboardingStatus, cgmDeviceId, expiredPackResponse.getBundleProduct(), chronicCareServiceHelper,serviceInterfaces.getSfAlbusClient()) : getExpiredPackCgmDeviceStatus(cgmOnboardingStatus, cgmDeviceId);
        CompletableFuture<CgmStat> cgmStatFuture = null;
        if (!(ChronicCareAppUtil.disableCgmGraphOnHomeScreen(userContext) && cgmOnboardingStatus.isCgmOperational())) {
            cgmStatFuture = chronicCareServiceHelper.getCgmStatFuture(userId, cgmDeviceStatus.getCgmDeviceId(), true, appTenant, timezone);
        }
        CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture = null;
        ActivePackResponse wellnessActivePackResponse = wellnessActivePackResponseFuture.get();
        CompletableFuture<MegaSaleData> megaSalesBannerDataFuture = chronicCareServiceHelper.getMegaSalesDataFuture(userContext, cgmOnboardingStatus, expiredPackResponse != null ? expiredPackResponse.getBundleProduct() : null, wellnessActivePackResponse, false);
        CompletableFuture<SfBannerCarouselWidget> salesBannerWidgetFuture = sfHomePageViewBuilderV1.getSalesBannerWidgetFuture(userContext, onboardingActions.get(), cgmOnboardingStatus, renewalJourneyFuture.get(), expiredPackResponse != null ? expiredPackResponse.getBundleProduct() : null, wellnessActivePackResponse);
        CompletableFuture<CGMGraphWidgetV2> cgmGraphWidgetFuture = getCGMGraphWidgetFuture(userContext, cgmDeviceStatus, cgmStatFuture, cgmOnboardingStatus, true, expiredPackResponse.getBundleProduct());
        CompletableFuture<BaseWidgetNonVM> diabetesStoreHomeWidgetFuture = getDiabetesStoreWidgetFuture(userContext, expiredPackResponse);
        CompletableFuture<BaseWidgetNonVM> blogsWidgetFuture = getBlogsWidgetFuture(userContext, sessionId, expiredPackResponse);
        CompletableFuture<BaseWidgetNonVM> healthCheckupWidgetFuture = getHealthCheckupWidgetFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> wellnessAtCenterWidgetFuture = null;
        CompletableFuture<SfLiveSessionWidget> liveSessionWidgetFuture = getLiveSessionWidgetFuture(userContext, chronicCareServiceHelper.getLiveClassesCompletableFuture(userContext), true);
        CompletableFuture<AssignedAgentWidget> assignedAgentsWidgetFuture = sfHomePageViewBuilderV1.getAssignedAgentsWidgetFuture(userContext, onboardingActions.get(), expiredPackResponse, assignedCareTeamFuture.get(), consultationBookingsFuture, null);

        boolean isWellnessAtCenterActiveUser = ChronicCareAppUtil.isWellnessAtCenterActiveUser(userContext, wellnessActivePackResponse);
        if (AppUtil.isSugarFitApp(userContext)) {
            if (isWellnessAtCenterActiveUser) {
                wellnessAtCenterWidgetFuture = getSfWellnessAtCenterWidgetFuture(userContext);
                groupClassBookingsFuture = chronicCareServiceHelper.getGroupClassBookingsFuture(userContext);
            }
        }
        CompletableFuture<BaseWidgetNonVM> activeCardsWidgetFuture = getActiveCardsWidgetFuture(userContext, patientDetailFuture.get(), expiredPackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, groupClassBookingsFuture, null, null);

        try {
            MegaSaleData megaSaleData = megaSalesBannerDataFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (ChronicCareAppUtil.isMegaSaleSupportedAppVersion(userContext) && Objects.nonNull(megaSaleData)) {
                result.setMegaSaleData(megaSaleData);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching ", e);
        }

        try {
            BaseWidgetNonVM activeCardWidget = activeCardsWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (null != activeCardWidget) {
                result.addWidget(activeCardWidget);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching activeCardWidget", e);
        }


        sfHomePageViewBuilderV1.addRenewalAlertWidget(userContext, result, null, assignedCareTeamFuture, expiredPackResponse == null ? null : expiredPackResponse.getBundleProduct(), cgmOnboardingStatus);

        if (wellnessAtCenterWidgetFuture != null) {
            try {
                BaseWidgetNonVM wellnessAtCenterWidget = wellnessAtCenterWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (wellnessAtCenterWidget != null) result.addWidget(wellnessAtCenterWidget);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching wellnessAtCenterWidgetFuture", e);
            }
        }


        boolean isNonCGMProduct = expiredPackResponse == null || ChronicCareAppUtil.isNonCGMProduct(expiredPackResponse.getBundleProduct(), cgmOnboardingStatus);
        CGMActivationStatus cgmActivationStatus = getCGMActivationStatus(userContext, null, onboardingActions.get(), null,cgmOnboardingStatus,consultationBookingsFuture);
        CGMGraphWidgetAndConfig cgmGraphWidgetAndConfig = getCGMGraphWidgetAndCgmConfigurations(userContext, cgmActivationStatus, cgmGraphWidgetFuture,
                isNonCGMProduct, cgmDeviceStatus, cgmOnboardingStatus, patientDetailFuture.get().getId(),
                expiredPackResponse, assignedCareTeamFuture.get(), cgmUserRequestStatusFuture.get(), cgmConfigVersion);
        if (Objects.nonNull(cgmGraphWidgetAndConfig) && Objects.nonNull(cgmGraphWidgetAndConfig.getUserCGMConfiguration())) {
            result.setCgmConfiguration(cgmGraphWidgetAndConfig.getUserCGMConfiguration());
            result.addWidget(cgmGraphWidgetAndConfig.getCgmGraphWidget());
        }

        if (!AppUtil.isUltraFitApp(userContext)) {
            result.addWidget(new ActivityLoggingWidget());
        }

        try {
            if (!chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(expiredPackResponse.getBundleProduct())) {
                result.addWidget(liveSessionWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching LiveSessionWidget", e);
        }

        try {
            BaseWidgetNonVM diabetesStoreHomeWidget = diabetesStoreHomeWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            result.addWidget(diabetesStoreHomeWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching diabetesStoreHomeWidget", e);
        }

        try {
            if (assignedAgentsWidgetFuture != null) {
                AssignedAgentWidget assignedAgentsWidget = assignedAgentsWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (null != assignedAgentsWidget) {
                    result.addWidget(assignedAgentsWidget);
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching assignedAgentsWidget", e);
        }

        try {
            BaseWidgetNonVM blogsWidget = blogsWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            result.addWidget(blogsWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching blogsWidget", e);
        }

        try {
            BaseWidgetNonVM healthCheckupWidget = healthCheckupWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            result.addWidget(healthCheckupWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching DoctorHealthCheckupWidget", e);
        }

        result.setPackHasExpired(true);
        result.addWidget(getQuoteWidget(userContext));

    }


    private void addSfHomeHeaderWidget(SfHomePageView result, UserContext userContext, UserOnboardingActionWithContext onboardingActions,
                                       CgmOnboardingStatusResponse cgmOnboardingStatus,
                                       RenewalJourneyResponse renewalJourney,
                                       BundleProduct bundleProduct,
                                       ActivePackResponse wellnessActivePackResponse,
                                       CompletableFuture<List<HomeMenuItem>> moreMenuItemsFuture,
                                       CompletableFuture<ChronicCareTeam> assignedCareTeamFuture
    ) {
        SfHomeHeaderWidget homeHeaderWidget = sfHomeHeaderWidgetBuilder.buildView(userContext, onboardingActions, cgmOnboardingStatus, renewalJourney, bundleProduct, wellnessActivePackResponse, moreMenuItemsFuture, assignedCareTeamFuture);
        if (homeHeaderWidget != null) {
            result.addWidget(homeHeaderWidget);
        }
    }

    private void addKickStartPageData(SfHomePageView result, UserContext userContext, KSJourneyResponse kickStarterJourney, String cgmDeviceId, Integer cgmConfigVersion, Long packBookingId) throws ExecutionException, InterruptedException, ResourceNotFoundException {
        String userIdString = userContext.getUserProfile().getUserId();
        Long userId = Long.valueOf(userIdString);
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timezone = userContext.getUserProfile().getTimezone() != null ? TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()) : TimeZone.getTimeZone("Asia/Kolkata");
        boolean isExperienceCentre = isExperienceCentreManager(userContext);

        CompletableFuture<CgmOnboardingStatusResponse> cgmOnboardingStatusFuture = chronicCareServiceHelper.getCgmOnboardingStatusFuture(userId, appTenant);
        CompletableFuture<PatientDetail> patientDetailFuture = chronicCarePatientService.getChronicCarePatientForUserIdFuture(userContext);
        CompletableFuture<ActivePackResponse> activePackResponseFuture = userOnboardingService.getSugarFitActivePackResponseFuture(userIdString);
        CompletableFuture<List<LiveClass>> liveClassesFuture = chronicCareServiceHelper.getLiveClassesCompletableFuture(userContext);
        CompletableFuture<ChronicCareTeam> assignedCareTeamFuture = chronicCareServiceHelper.getAssignedCareTeamFuture(userContext, null,  Objects.nonNull(activePackResponseFuture.get()) ? activePackResponseFuture.get().getBundleProduct() : null);
        CompletableFuture<CgmUserRequestStatus> cgmUserRequestStatusFuture = chronicCareServiceHelper.getCgmUserRequestStatusFuture(userId, appTenant); // TODO: move to onboarding status
        ActivePackResponse activePackResponse = activePackResponseFuture.get();

        CgmOnboardingStatusResponse cgmOnboardingStatus = cgmOnboardingStatusFuture.get();
        SfCgmDeviceStatus cgmDeviceStatus = getCgmDeviceStatus(userContext, cgmOnboardingStatus, cgmDeviceId, activePackResponse.getBundleProduct(), chronicCareServiceHelper,serviceInterfaces.getSfAlbusClient());
        CompletableFuture<CgmStat> cgmStatFuture = chronicCareServiceHelper.getCgmStatFuture(userId, cgmDeviceStatus.getCgmDeviceId(), true, appTenant, timezone);
        CompletableFuture<UserOnboardingActionWithContext> onboardingActionsFuture = userOnboardingService.getUserOnboardingActionsFuture(userContext, packBookingId);
        UserOnboardingActionWithContext onboardingActions = null;
        if (Objects.nonNull(onboardingActionsFuture.get())) {
            onboardingActions = onboardingActionsFuture.get();
        }
        CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture = chronicCareServiceHelper.getConsultationBookingsFuture(userContext);
        CompletableFuture<SfKickStartData> kickStartDataFuture = getKickStartDataFuture(userContext, activePackResponse, kickStarterJourney, liveClassesFuture, assignedCareTeamFuture.get(), patientDetailFuture.get(), cgmOnboardingStatus, null, onboardingActions, consultationBookingsFuture);
        CompletableFuture<CGMGraphWidgetV2> cgmGraphWidgetFuture = getCGMGraphWidgetFuture(userContext, cgmDeviceStatus, cgmStatFuture, cgmOnboardingStatus, false, activePackResponse.getBundleProduct());
        CompletableFuture<CsTicketResolutionNotificationData> csTicketResolutionNotificationFuture = sfHomePageViewBuilderV1.getCsTicketResolutionNotificationFuture(userContext);
        try {
            SfKickStartData kickStartData = kickStartDataFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (kickStartData != null) {
                result.setKickStartData(kickStartData);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching kickStartData", e);
        }

        boolean shouldHideCgmGraph = false;
        if (!isExperienceCentre) {
            try {
                SfCgmDeviceStatus sfCgmDeviceStatus = getCgmDeviceStatus(userContext, cgmOnboardingStatus, null, activePackResponse.getBundleProduct(), chronicCareServiceHelper,serviceInterfaces.getSfAlbusClient());
                if (!sfCgmDeviceStatus.getIsOnGoingDevice()
                        && sfCgmDeviceStatus.getShouldShowScanCardInsideCGMGraph()
                        && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                        && cgmOnboardingStatus.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon()
                        && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice())
                        && cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice().getStatus() == DeviceStatus.BCN_DELIVERED) {
                    shouldHideCgmGraph = true;
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching SfBluconJourneyWidget", e);
            }
        }

        boolean isNonCGMProduct = ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(), cgmOnboardingStatus);
        if (!shouldHideCgmGraph) {
            CGMActivationStatus cgmActivationStatus = getCGMActivationStatus(userContext, kickStarterJourney, null, liveClassesFuture,cgmOnboardingStatus,consultationBookingsFuture);
            CGMGraphWidgetAndConfig cgmGraphWidgetAndConfig = getCGMGraphWidgetAndCgmConfigurations(userContext, cgmActivationStatus,
                    cgmGraphWidgetFuture, isNonCGMProduct, cgmDeviceStatus,
                    cgmOnboardingStatus, patientDetailFuture.get().getId(),
                    activePackResponse, assignedCareTeamFuture.get(),
                    cgmUserRequestStatusFuture.get(), cgmConfigVersion);
            if (Objects.nonNull(cgmGraphWidgetAndConfig) && Objects.nonNull(cgmGraphWidgetAndConfig.getUserCGMConfiguration())) {
                result.setCgmConfiguration(cgmGraphWidgetAndConfig.getUserCGMConfiguration());
            }
        }

        if (!isExperienceCentre) {
            try {
                CsTicketResolutionNotificationData csTicketResolutionNotificationData = csTicketResolutionNotificationFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (csTicketResolutionNotificationData != null) {
                    result.setCsTicketResolutionNotificationData(csTicketResolutionNotificationData);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching appAnnouncementData", e);
            }
        }
    }

    private SfWellnessAtCenterTherapyListCardWidgetV2 getSfWellnessAtCenterWidgetV2(UserContext userContext) throws HttpException {
        try {
            SfWellnessAtCenterTherapyListCardWidgetV2 widget = new SfWellnessAtCenterTherapyListCardWidgetV2();
            Header header = new Header();
            header.setTitle("Visit our\n");
            header.setBoldTitle("Diabetes Reversal Clinic");
            header.setSeemore(Action.builder().url("curefit://sfwellnessatcenterclp").actionType(ActionType.NAVIGATION).build());
            widget.setHeader(header);
            List<SfWellnessTherapyProduct> therapies = ChronicCareServiceHelper.getAllWellnessAtCenterProducts(serviceInterfaces, userContext);
            widget.setTherapies(therapies.stream().peek(t -> t.setImages(t.getThumbnailImages())).toList());
            return widget;

        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    private CompletableFuture<BaseWidgetNonVM> getSfWellnessAtCenterWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getSfWellnessAtCenterWidgetV2(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getExperimentHomeWidget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }


    private CompletableFuture<SfHomeProgressWidget> getHomeProgressWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getHomeProgressWidget(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getHomeProgressWidgetFuture", e);
                log.error("Error in getting home progress widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<ScratchCardData> getScratchCardDataFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getScratchCardData(userContext);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private ScratchCardData getScratchCardData(UserContext userContext) throws Exception {
        try {
            SfAppScratchCardConfig scratchCardConfig = chronicCareServiceHelper.getScratchCardConfig();
            if (scratchCardConfig != null && scratchCardConfig.isEnabled()) {
                ScratchCardData scratchCardData = new ScratchCardData();
                scratchCardData.setSkipIntervalInDays(scratchCardConfig.getSkipIntervalInDays());
                scratchCardData.setScratchCardForegroundData(scratchCardConfig.getForegroundData());
                scratchCardData.setScratchCardBackgroundData(scratchCardConfig.getBackgroundData());
                if (Objects.nonNull(scratchCardConfig.getSegment()) && !scratchCardConfig.getSegment().isEmpty()) {
                    SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                            .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
                    if (userSegments.contains(scratchCardConfig.getSegment())) {
                        return scratchCardData;
                    }
                } else {
                    return scratchCardData;
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Exception in getScratchCardData", e);
        }
        return null;
    }

    private Boolean isGoalProgressing(SfHomeProgressWidget.ProgressItem progressItem, Double goalTargetLow, Double goalTargetHigh) {
        double currentValue = Double.parseDouble(progressItem.getCurrentValue());
        if(progressItem.getGoalValue() == null && currentValue > 0) return true;
        return switch (progressItem.getType()) {
            case "STEPS" ->
                    progressItem.getGoalValue() != null && currentValue >= Double.parseDouble(progressItem.getGoalValue());
            case "CALORIES-EATEN", "WEIGHT" ->
                    progressItem.getGoalValue() != null && currentValue <= Double.parseDouble(progressItem.getGoalValue());
            case "FASTING-GLUCOSE" ->
                    progressItem.getGoalValue() != null && currentValue >= goalTargetLow && Double.parseDouble(progressItem.getCurrentValue()) <= goalTargetHigh;
            default -> false;
        };
    }

    public SfHomeProgressWidget getHomeProgressWidget(UserContext userContext) throws ExecutionException, InterruptedException {
        SfHomeProgressWidget homeProgressWidget = new SfHomeProgressWidget();
        CompletableFuture<List<SfHomeProgressWidget.ProgressItem>> progressItemsFuture = getHomeProgressDataFuture(userContext);

        List<SfHomeProgressWidget.ProgressItem> progressItems = progressItemsFuture.get();

        int progressGoalCount = 0, emptyGoalCount = progressItems.size();
        for(var item : progressItems) {
            if(item.getCurrentValue() != null && !Objects.equals(item.getCurrentValue(), "") && Double.parseDouble(item.getCurrentValue()) > 0) {
                if(item.getIsProgressing()) {
                    progressGoalCount++;
                }
                emptyGoalCount--;
            }
        }

        if(emptyGoalCount >= 3) {
            homeProgressWidget.setProgressText("Log your journey to see progress!");
        } else if(progressGoalCount >= 2) {
            homeProgressWidget.setProgressText("You're off to a great start! Keep going!");
        } else {
            homeProgressWidget.setProgressText("Don't worry, You've got this!");
        }

        homeProgressWidget.setData(progressItems);

        FitnessDeviceSyncMeta fitnessDeviceSyncMeta = chronicCareServiceHelper.getFitnessDeviceSyncMeta(userContext);
        homeProgressWidget.setActiveDeviceId(fitnessDeviceSyncMeta.getActiveDeviceId());
        if (fitnessDeviceSyncMeta.getLastSyncedTime() != null) {
            homeProgressWidget.setLastSyncedTime(fitnessDeviceSyncMeta.getLastSyncedTime());
            homeProgressWidget.setLastSyncedDisplayTime(fitnessDeviceSyncMeta.getLastSyncedDisplayTime());
        }

        homeProgressWidget.setTitleAction(Action.builder().title("View Details").actionType(ActionType.NAVIGATION)
                .url("curefit://goalsprogress").build());

        return homeProgressWidget;
    }

    private String getGoalLabel(String goalName) {
        return switch (goalName) {
            case "CALORIES-EATEN" -> "CALORIE\nEATEN";
            case "FASTING-GLUCOSE" -> "LATEST\nSUGAR";
            case "STEPS" -> "TOTAL\nSTEPS";
            case "WEIGHT" -> "CURRENT\nWEIGHT";
            default -> goalName;
        };
    }

    static double safeParse(String value) {
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            return 0; // Default to zero if parsing fails
        }
    }

    public static String getDigitalAppGoalLevel(String goalName, String currentValue, String goalValue) {
        return switch (goalName) {
            case "CALORIES-EATEN" -> {
                double current = safeParse(currentValue);
                double goal = safeParse(goalValue);
                double percentage = (goal == 0) ? 0 : (current / goal) * 100;

                if (percentage >= 90) yield "NORMAL";
                else if (percentage >= 50) yield "MEDIUM";
                else yield "HIGH";
            }
            case "STEPS" -> {
                double current = safeParse(currentValue);
                double goal = safeParse(goalValue);
                double percentage = (goal == 0) ? 0 : (current / goal) * 100;

                if (percentage <= 30) yield "HIGH";
                else if (percentage <= 60) yield "MEDIUM";
                else yield "NORMAL";
            }
            case "FASTING-GLUCOSE" -> {
                double current = safeParse(currentValue);

                if (current < 80) yield "HIGH";
                else if (current <= 140) yield "NORMAL";
                else yield "MEDIUM";
            }
            case "WEIGHT" -> "NORMAL";
            default -> "";
        };
    }


    private CompletableFuture<List<SfHomeProgressWidget.ProgressItem>> getHomeProgressDataFuture(UserContext userContext) {
        return supplyAsync(() -> {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            String userTimeZone = userContext.getUserProfile().getTimezone();
            Date startDate = TimeUtil.getEndOfDay(Calendar.getInstance().getTime(), userTimeZone);

            Calendar currentTime = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            currentTime.set(Calendar.HOUR_OF_DAY, 23);
            currentTime.set(Calendar.MINUTE, 59);
            currentTime.set(Calendar.SECOND, 59);
            currentTime.set(Calendar.MILLISECOND, 999);

            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            dateFormat.setTimeZone(ChronicCareAppUtil.getUserTimezone(userContext));
            List<SfHomeProgressWidget.ProgressItem> progressItems = new ArrayList<>();
            try {
                List<GoalWithData> goalWithLatestData = chronicCareServiceHelper.getShifuRecentGoals(userContext,
                        userId, startDate, true, userTimeZone);
                for (String goalName : PROGRESS_GOALS) {
                    GoalWithData goal = goalWithLatestData.stream()
                            .filter(goalWithData -> goalWithData.getGoal().getMeta().getUniqueKey().equals(goalName))
                            .findAny()
                            .orElse(null);
                    if (goal != null) {
                        SfHomeProgressWidget.ProgressItem item = new SfHomeProgressWidget.ProgressItem();
                        if (goalName.equals("FASTING-GLUCOSE")) {
                            AppTenant appTenant = getAppTenantFromUserContext(userContext);
                            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
                            ZoneId userZoneId = timeZone.toZoneId();
                            List<String> metricNames = List.of(GLUCOMETER_READING_METRIC);
                            Map<String, UserMetricEntry> metricsMap = serviceInterfaces.getChsClient().fetchLatestUserMetricBulk(userId,metricNames,appTenant,timeZone);
                            if (metricsMap != null && metricsMap.containsKey(GLUCOMETER_READING_METRIC)) {
                                UserMetricEntry entry = metricsMap.get(GLUCOMETER_READING_METRIC);
                                if (entry != null) {
                                    Date startTime = entry.getStartTime();
                                    if (startTime != null) {
                                        LocalDate lastUpdatedTime = startTime.toInstant().atZone(userZoneId).toLocalDate();
                                        LocalDate today = LocalDate.now(userZoneId);
                                        if (lastUpdatedTime.equals(today)) {
                                            item.setCurrentValue(entry.getValue());
                                        }
                                    }
                                }
                            }
                        }
                        if (goal.getMetricValuePojo() != null && goal.getMetricValuePojo().getValue() != null && !goalName.equals("FASTING-GLUCOSE")) {
                            item.setCurrentValue(getFormattedRecentValue(goal.getMetricValuePojo().getValue(),
                                    goal.getGoal().getMeta().getUniqueKey()));
                        }
                        if (goal.getTarget() != null) {
                            item.setGoalValue(getTargetValue(goal.getTarget().getLow(), goal.getTarget().getHigh(),
                                    goal.getGoal().getMeta().getUniqueKey()));
                        }
                        item.setType(goal.getGoal().getMeta().getUniqueKey());
                        item.setDisplayName(getGoalLabel(goalName));
                        item.setUnit(getWellnessGoalUnitByType(goalName));
                        item.setChartColor(getProgressChartColor(goalName));
                        item.setLogAction(getGoalsLogAction(goalName, userContext, chronicCareServiceHelper));
                        if (goalName.equals("CALORIES-EATEN")) {
                            if(item.getCurrentValue() != null && !Objects.equals(item.getCurrentValue(), "") && Double.parseDouble(item.getCurrentValue()) > 0) {
                                item.setCardClickAction(Action.builder().actionType(ActionType.NAVIGATION)
                                        .url("curefit://sfmealactivityslotsdetailpage")
                                        .build());
                            } else {
                                item.setCardClickAction(Action.builder().actionType(ActionType.SHOW_SF_MEAL_SLOT_PICKER_MODAL).build());
                            }
                        } else if (goalName.equals("FASTING-GLUCOSE")) {
                            if (item.getCurrentValue() != null && !Objects.equals(item.getCurrentValue(), "") && Double.parseDouble(item.getCurrentValue()) > 0) {
                                item.setCardClickAction(Action.builder().url("curefit://goalsprogress?pageKey=JOURNAL&childPageKey=SUGAR").actionType(ActionType.NAVIGATION).build());
                            } else {
                                item.setCardClickAction(Action.builder().title("SUGARLEVEL").actionType(ActionType.SHOW_SF_SUGAR_LOGGING_MODE_SELECTION_MODAL).url("curefit://sugarlogging").build());
                            }
                        } else {
                            item.setCardClickAction(Action.builder().actionType(ActionType.NAVIGATION)
                                    .url("curefit://goalsprogress?goalType=" + goal.getGoal().getMeta().getUniqueKey())
                                    .build());
                        }
                        if (item.getCurrentValue() != null && !Objects.equals(item.getCurrentValue(), "") && Double.parseDouble(item.getCurrentValue()) > 0) {
                            boolean isTargetNonEmpty = goal.getTarget() != null;
                            item.setIsProgressing(isGoalProgressing(item, isTargetNonEmpty ? goal.getTarget().getLow() : null, isTargetNonEmpty ? goal.getTarget().getHigh(): null));
                        }
                        progressItems.add(item);
                    }
                }

            } catch (Exception e) {
                this.exceptionReportingService.reportException("Error in getting recent goals data ==>", e);
            }
            return progressItems;
        } , serviceInterfaces.getTaskExecutor());
    }



    private void addAllUnlockedPageWidgets(SfHomePageView result, UserContext userContext, KSJourneyResponse kickStarterJourney, String cgmDeviceId, Integer cgmConfigVersion, String sessionId, Long packBookingId) throws ResourceNotFoundException, ExecutionException, InterruptedException, TimeoutException {
        String userIdString = userContext.getUserProfile().getUserId();
        Long userId = Long.valueOf(userIdString);
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timezone = userContext.getUserProfile().getTimezone() != null ? TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()) : TimeZone.getTimeZone("Asia/Kolkata");
        Long currentTimeEpoch = Calendar.getInstance(getUserTimezone(userContext)).getTimeInMillis();
        boolean isSugarFit = isSugarFitApp(userContext);
        boolean isExperienceCentre = isExperienceCentreManager(userContext);

        CompletableFuture<CgmOnboardingStatusResponse> cgmOnboardingStatusFuture = chronicCareServiceHelper.getCgmOnboardingStatusFuture(userId, appTenant);
        CompletableFuture<UserTodoDaySummary> userTodoDaySummaryFuture = chronicCareServiceHelper.getUserTodoDaySummaryFuture(userContext, currentTimeEpoch);
        CompletableFuture<PatientDetail> patientDetailFuture = chronicCarePatientService.getChronicCarePatientForUserIdFuture(userContext);
        CompletableFuture<ActivePackResponse> activePackResponseFuture = userOnboardingService.getSugarFitActivePackResponseFuture(userIdString);
        CompletableFuture<ActivePackResponse> wellnessActivePackResponseFuture = userOnboardingService.getSugarFitWellnessActivePackResponseFuture(userIdString);
        CompletableFuture<UserOnboardingActionWithContext> onboardingActionsFuture = userOnboardingService.getUserOnboardingActionsFuture(userContext, packBookingId);
        CompletableFuture<List<LiveClass>> liveClassesFuture = chronicCareServiceHelper.getLiveClassesCompletableFuture(userContext);
        CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture = chronicCareServiceHelper.getConsultationBookingsFuture(userContext);
        CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture = null;
        CompletableFuture<DiagnosticJourneyResponse> diagnosticJourneyResponseCompletableFuture = chronicCareServiceHelper.getDiagnosticJourneyStatusFuture(userContext);
        CompletableFuture<ChronicCareTeam> assignedCareTeamFuture = chronicCareServiceHelper.getAssignedCareTeamFuture(userContext, null,  Objects.nonNull(activePackResponseFuture.get()) ? activePackResponseFuture.get().getBundleProduct() : null);
        CompletableFuture<RenewalJourneyResponse> renewalJourneyFuture = chronicCareServiceHelper.getRenewalJourneyFuture(userContext);
        CompletableFuture<CgmUserRequestStatus> cgmUserRequestStatusFuture = chronicCareServiceHelper.getCgmUserRequestStatusFuture(userId, appTenant); // TODO: move to onboarding status
        CompletableFuture<SfDiagnosticNudgeWidget> sfDiagnosticNudgeWidgetCompletableFuture = getSfDiagnosticNudgeWidgetFuture(userContext, activePackResponseFuture.get(), diagnosticJourneyResponseCompletableFuture);
        CompletableFuture<FaceBasedVitalScansForDayResponse> faceBasedVitalScansForDayResponseFuture = chronicCareServiceHelper.getFbvScanForDayDataFuture(userId, timezone);
        CompletableFuture<BaseWidgetNonVM> smartScaleIntegrationBannerWidgetFuture = chronicCareServiceHelper.getSmartScaleIntegrationBannerWidgetFuture(userContext);
        CompletableFuture<BaseWidgetNonVM> smartScaleStatsWidgetCompletableFuture = chronicCareServiceHelper.getSmartScaleStatsWidgetCompletableFuture(userContext);

        ActivePackResponse activePackResponse = activePackResponseFuture.get();
        addKickStartDataForUnlockedPage(result, kickStarterJourney, activePackResponse);
        CgmOnboardingStatusResponse cgmOnboardingStatus = cgmOnboardingStatusFuture.get();
        SfCgmDeviceStatus cgmDeviceStatus = getCgmDeviceStatus(userContext, cgmOnboardingStatus, cgmDeviceId, activePackResponse.getBundleProduct(), chronicCareServiceHelper,serviceInterfaces.getSfAlbusClient());
        CompletableFuture<CgmStat> cgmStatFuture = null;
        if (!(ChronicCareAppUtil.disableCgmGraphOnHomeScreen(userContext) && cgmOnboardingStatus.isCgmOperational())) {
            cgmStatFuture = chronicCareServiceHelper.getCgmStatFuture(userId, cgmDeviceStatus.getCgmDeviceId(), true, appTenant, timezone);
        }
        CompletableFuture<SfFlashbackHomeWidget> flashbackHomeWidgetFuture = null;
        CompletableFuture<BaseWidgetNonVM> wellnessAtCenterWidgetFuture = null;
        CompletableFuture<DigiLessonsCarouselWidget> digiLessonsCarouselWidgetFuture = null;
        CompletableFuture<DAProgramJourneyWidget> digiProgramJourneyWidgetFuture = null;

        if (ChronicCareAppUtil.hasDigitalContentAccess(activePackResponse.getBundleProduct())) {
            digiLessonsCarouselWidgetFuture = chronicCareServiceHelper.getDigiLessonsCarouselWidgetFuture(userContext, false);
            digiProgramJourneyWidgetFuture = chronicCareServiceHelper.getDAProgramJourneyWidgetFuture(userContext);
        }

        ActivePackResponse wellnessActivePackResponse = wellnessActivePackResponseFuture.get();
        boolean isWellnessAtCenterActiveUser = ChronicCareAppUtil.isWellnessAtCenterActiveUser(userContext, wellnessActivePackResponse);
        if (isSugarFit) {
            flashbackHomeWidgetFuture = sfHomePageViewBuilderV1.getFlashbackHomeWidgetFuture(userContext);
            if (isWellnessAtCenterActiveUser) {
                groupClassBookingsFuture = chronicCareServiceHelper.getGroupClassBookingsFuture(userContext);
            }
        }
        CompletableFuture<BaseWidgetNonVM> activeCardsWidgetFuture = getActiveCardsWidgetFuture(userContext, patientDetailFuture.get(), activePackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, groupClassBookingsFuture, liveClassesFuture, faceBasedVitalScansForDayResponseFuture.get());

        UserOnboardingActionWithContext onboardingActions = onboardingActionsFuture.get();
        if (Objects.isNull(onboardingActions)) {
            exceptionReportingService.reportWarning(new Exception("UserOnboardingActionWithContext is null"));
            return;
        }

        if (AppUtil.isSugarFitApp(userContext)) {
            if (isWellnessAtCenterActiveUser) {
                wellnessAtCenterWidgetFuture = getSfWellnessAtCenterWidgetFuture(userContext);
            }
        }

        RenewalJourneyResponse renewalJourney = renewalJourneyFuture.get();
        CompletableFuture<SfKickStartAppUnlockedWidget> kickStartWidgetFuture = getKickStartWidgetFuture(userContext, activePackResponse, kickStarterJourney, assignedCareTeamFuture.get(), patientDetailFuture.get(), liveClassesFuture, cgmOnboardingStatus, null,onboardingActions,consultationBookingsFuture);
        CompletableFuture<SfThingsToDoWidget> thingsToDoWidgetFuture = getThingsToDoWidgetFuture(userContext, userTodoDaySummaryFuture, onboardingActions, assignedCareTeamFuture, activePackResponse, patientDetailFuture, cgmOnboardingStatus, faceBasedVitalScansForDayResponseFuture.get());
        CompletableFuture<SfHomeFBVWidget> sfHomeFBVWidgetCompletableFuture = chronicCareServiceHelper.getSfHomeFBVWidgetFuture(userContext, cgmOnboardingStatus, activePackResponse, faceBasedVitalScansForDayResponseFuture.get());
        CompletableFuture<CGMGraphWidgetV2> cgmGraphWidgetFuture = getCGMGraphWidgetFuture(userContext, cgmDeviceStatus, cgmStatFuture, cgmOnboardingStatus, false, activePackResponse.getBundleProduct());
        CompletableFuture<AppAnnouncementData> appAnnouncementDataFuture = sfHomePageViewBuilderV1.getAppAnnouncementDataFuture(userContext, cgmOnboardingStatus, activePackResponse, renewalJourney, wellnessActivePackResponse);
        CompletableFuture<ScratchCardData> getScratchCardDataFuture = getScratchCardDataFuture(userContext);
        CompletableFuture<CsTicketResolutionNotificationData> csTicketResolutionNotificationFuture = sfHomePageViewBuilderV1.getCsTicketResolutionNotificationFuture(userContext);
//        CompletableFuture<BaseWidgetNonVM> wellnessAtGlanceWidgetFuture = sfHomePageViewBuilderV1.getWellnessAtGlanceWidgetFuture(userContext);
        CompletableFuture<SfHomeProgressWidget> homeProgressWidgetFuture = getHomeProgressWidgetFuture(userContext);
        CompletableFuture<List<BaseWidgetNonVM>> challengeWidgetsFuture = sfHomePageViewBuilderV1.getChallengeWidgetsFuture(userContext);
        CompletableFuture<List<BaseWidgetNonVM>> mentalHealthPollWidgetFuture = getMentalHealthPollWidgetsFuture(userContext);
        CompletableFuture<SfLiveSessionWidget> liveSessionWidgetFuture = getLiveSessionWidgetFuture(userContext, liveClassesFuture, false);
        CompletableFuture<AssignedAgentWidget> assignedAgentsWidgetFuture = sfHomePageViewBuilderV1.getAssignedAgentsWidgetFuture(userContext, onboardingActions, activePackResponse, assignedCareTeamFuture.get(), consultationBookingsFuture, diagnosticJourneyResponseCompletableFuture);
        CompletableFuture<SfRenewalReportHomeWidget> renewalReportHomeWidgetFuture = sfHomePageViewBuilderV1.getRenewalReportHomeWidgetFuture(userContext, renewalJourneyFuture);
        CompletableFuture<BaseWidgetNonVM> diabetesStoreHomeWidgetFuture = getDiabetesStoreWidgetFuture(userContext, activePackResponse);
        CompletableFuture<BaseWidgetNonVM> blogsWidgetFuture = getBlogsWidgetFuture(userContext, sessionId, activePackResponse);
        CompletableFuture<List<HomeMenuItem>> moreMenuItemsFuture = sfHomePageViewBuilderV1.getMoreMenuItemsFuture(userContext, assignedAgentsWidgetFuture, wellnessAtCenterWidgetFuture);
        CompletableFuture<SfMasterLiveClassWidget> sfMasterLiveClassWidgetCompletableFuture = null;
        CompletableFuture<NPSModalData> npsModalDataCompletableFuture = getNPSDataFuture(userContext, activePackResponse);
        CompletableFuture<SfBannerCarouselWidget> sfHomeRagusAIChatBannerWidgetCompletableFuture = chronicCareServiceHelper.getRagusAiBannerWidget(userContext);

        if (isDiabeticProfileDataSupported(userContext)){
            CompletableFuture<DiabeticProfileData> diabeticProfileDataFuture = chronicCareServiceHelper.getDiabeticProfileDataFuture(userContext, timezone, cgmOnboardingStatus);
            DiabeticProfileData diabeticProfileData = diabeticProfileDataFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            if (diabeticProfileData != null) {
                result.setDiabeticProfileData(diabeticProfileData);
            }
        }

        if (isKickstarterV2Supported(userContext, serviceInterfaces, chronicCareServiceHelper, activePackResponse.getBundleProduct())) {
            try {
                KSJourneyV2Response ksJourneyV2Response = serviceInterfaces.getSfAlbusClient().getKickStarterJourneyV2(userId, userContext.getUserProfile().getTimezone());
                CompletableFuture<SfKickstartJourneyWidgetV2> kickStartV2WidgetFuture = getKickStartV2WidgetFuture(userContext, activePackResponse, ksJourneyV2Response, liveClassesFuture, assignedCareTeamFuture.get(), patientDetailFuture.get(), cgmOnboardingStatus, faceBasedVitalScansForDayResponseFuture.get(),onboardingActions,consultationBookingsFuture);
                SfKickstartJourneyWidgetV2 sfKickstartJourneyWidgetV2 = kickStartV2WidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                if (sfKickstartJourneyWidgetV2 != null) {
                    result.addWidget(sfKickstartJourneyWidgetV2);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in sfKickstartJourneyWidgetV2", e);
            }
        }


        if (Objects.nonNull(activePackResponse) && chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
            sfMasterLiveClassWidgetCompletableFuture = sfHomePageViewBuilderV1.getMasterLiveClassWidgetFuture(userContext, activePackResponse.getBundleProduct().getProductCode(), liveClassesFuture.get());
        }
//        CompletableFuture<BaseWidgetNonVM> experimentsWidgetFuture = null;
//        if (ChronicCareAppUtil.isSfExperimentEnabledUser(userContext)) {
//            experimentsWidgetFuture = sfHomePageViewBuilderV1.getExperimentsWidgetFuture(userContext, cgmOnboardingStatus);
//        }
        MegaSaleData megaSaleData = null;
        if (ChronicCareAppUtil.isOldHomeBannerSupportedNewHomepageUser(userContext)) {
            if (ChronicCareServiceHelper.isOnboardingWalkthroughBannerEnabledUser(activePackResponse, userContext, chronicCareServiceHelper)) {
                CompletableFuture<SfBannerCarouselWidget> onboardingWalkthroughBannerWidgetFuture = chronicCareServiceHelper.getOnboardingWalkthroughBannerWidgetFuture(userContext);
                BaseWidgetNonVM onboardingWalkthroughBannerWidget = onboardingWalkthroughBannerWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                result.addWidget(onboardingWalkthroughBannerWidget);
            } else {
                try {
                    CompletableFuture<MegaSaleData> megaSalesBannerDataFuture = chronicCareServiceHelper.getMegaSalesDataFuture(userContext, cgmOnboardingStatus, activePackResponse != null ? activePackResponse.getBundleProduct() : null, wellnessActivePackResponse, false);
                    CompletableFuture<SfBannerCarouselWidget> salesBannerWidgetFuture = sfHomePageViewBuilderV1.getSalesBannerWidgetFuture(userContext, onboardingActions, cgmOnboardingStatus, renewalJourneyFuture.get(), activePackResponse != null ? activePackResponse.getBundleProduct() : null, wellnessActivePackResponse);
                    megaSaleData = megaSalesBannerDataFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (ChronicCareAppUtil.isMegaSaleSupportedAppVersion(userContext) && Objects.nonNull(megaSaleData)) {
                        result.setMegaSaleData(megaSaleData);
                    } else {
                        BaseWidgetNonVM salesBannerWidget = salesBannerWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                        if (salesBannerWidget != null) {
                            result.addWidget(salesBannerWidget);
                        }
                    }
                } catch (Exception e) {
                    exceptionReportingService.reportException("Error in fetching ", e);
                }
            }
        } else {
            addSfHomeHeaderWidget(result, userContext, onboardingActions, cgmOnboardingStatus, renewalJourney, activePackResponse.getBundleProduct(), wellnessActivePackResponse, moreMenuItemsFuture, assignedCareTeamFuture);
        }

        try {
            BaseWidgetNonVM activeCardWidget = activeCardsWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            result.addWidget(activeCardWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching activeCardWidget", e);
        }

        boolean isRenewalAllowed = resolveNullable(() -> onboardingActions.getPackRenewalContextActionWithContext().getAction().isActionPermitted()).orElse(false);
        if (isRenewalAllowed) {
            sfHomePageViewBuilderV1.addRenewalAlertWidget(userContext, result, onboardingActions, assignedCareTeamFuture, activePackResponse.getBundleProduct(), cgmOnboardingStatus);
        } else if (chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
            BaseWidgetNonVM renewalWidget = renewalAlertWidgetBuilderV2.build(userContext, onboardingActions, activePackResponse.getBundleProduct().getProductCode());
            if (renewalWidget != null) {
                result.addWidget(renewalWidget);
            }
        }

        if (!isKickstarterV2Supported(userContext, serviceInterfaces, chronicCareServiceHelper, activePackResponse.getBundleProduct())) {
            try {
                SfKickStartAppUnlockedWidget kickStartWidget = kickStartWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                result.addWidget(kickStartWidget);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching kickStartWidget", e);
            }
        }

        try {
            SfBannerCarouselWidget readerDelayBanner = chronicCareServiceHelper.getCGMReaderDeliveryDelayBannerWidget(userContext);
            if (Objects.nonNull(readerDelayBanner)) {
                result.addWidget(readerDelayBanner);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching readerDelayBanner", e);
        }

        try {
            SfBannerCarouselWidget ragusAiBanner = sfHomeRagusAIChatBannerWidgetCompletableFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
            if (Objects.nonNull(ragusAiBanner)) {
                result.addWidget(ragusAiBanner);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching ragusAiBanner", e);
        }

        try {
            if (isSugarFit
                    && ChronicCareAppUtil.isSugarControl7DSessionWidgetSupported(userContext)
                    && chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())) {
                result.addWidget(getSugarControlSessionWidget());
                BaseWidgetNonVM dietPlanWidgetForSugarControl = getSugarControlDietPlanWidget(userContext);
                result.addWidget(dietPlanWidgetForSugarControl);
            } else if (isSugarFit && !isExperienceCentre && sfMasterLiveClassWidgetCompletableFuture != null) {
                SfMasterLiveClassWidget sfMasterLiveClassWidget = sfMasterLiveClassWidgetCompletableFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (sfMasterLiveClassWidget != null) {
                    result.addWidget(sfMasterLiveClassWidget);
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching sfMasterLiveClassWidget", e);
        }

        try {
            if (Objects.nonNull(digiLessonsCarouselWidgetFuture)) {
                DigiLessonsCarouselWidget digiLessonsCarouselWidget = digiLessonsCarouselWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (digiLessonsCarouselWidget != null) {
                    result.addWidget(digiLessonsCarouselWidget);
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching digiLessonsCarouselWidget", e);
        }

        SfHomeProgressWidget homeProgressWidget = null;
        CGMGraphWidgetAndConfig cgmGraphWidgetAndConfig = null;
        BaseWidgetNonVM diabetesStoreHomeWidget = null;
        SfHomeFBVWidget sfHomeFBVWidget = null;
        BaseWidgetNonVM smartScaleWidget = null;

        try {
            sfHomeFBVWidget = sfHomeFBVWidgetCompletableFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching sfHomeFBVWidget", e);
        }

        try {
            homeProgressWidget = homeProgressWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching HomeProgressWidget", e);
        }

        try {
            diabetesStoreHomeWidget = diabetesStoreHomeWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching diabetesStoreHomeWidget", e);
        }

        try {
            SfDiagnosticNudgeWidget diagnosticNudgeWidget = sfDiagnosticNudgeWidgetCompletableFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            result.addWidget(diagnosticNudgeWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching SfDiagnosticNudgeWidget", e);
        }

        if (!isExperienceCentre && isSugarFit) {
            try {
                if (ChronicCareAppUtil.smartScaleStatsWidgetSupported(userContext)) {
                    BaseWidgetNonVM scaleWidget = smartScaleStatsWidgetCompletableFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (scaleWidget != null) {
                        smartScaleWidget = scaleWidget;
                    }
                } else {
                    BaseWidgetNonVM scaleIntegrationBanner = smartScaleIntegrationBannerWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (scaleIntegrationBanner != null) {
                        smartScaleWidget = scaleIntegrationBanner;
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching smartScaleIntegrationBannerWidgetFuture", e);
            }
        }

        boolean shouldHideCgmGraph = false;
        if (!isExperienceCentre && isSugarFit) {
            try {
                SfCgmDeviceStatus sfCgmDeviceStatus = getCgmDeviceStatus(userContext, cgmOnboardingStatus, null, activePackResponse.getBundleProduct(), chronicCareServiceHelper, serviceInterfaces.getSfAlbusClient());
                if (!sfCgmDeviceStatus.getIsOnGoingDevice()
                        && sfCgmDeviceStatus.getShouldShowScanCardInsideCGMGraph()
                        && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                        && cgmOnboardingStatus.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon()
                        && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice())
                        && cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice().getStatus() == DeviceStatus.BCN_DELIVERED) {
                    SfBluconJourneyWidget bluconJourneyWidget = getBluconJourneyWidget(userContext, onboardingActions, cgmOnboardingStatus, patientDetailFuture.get().getId());
                    if (Objects.nonNull(bluconJourneyWidget))
                        result.addWidget(bluconJourneyWidget);
                    shouldHideCgmGraph = true;
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching SfBluconJourneyWidget", e);
            }
        }

        boolean isNonCGMProduct = ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(), cgmOnboardingStatus);
        if (!shouldHideCgmGraph) {
            CGMActivationStatus cgmActivationStatus = getCGMActivationStatus(userContext, null, onboardingActions, liveClassesFuture, cgmOnboardingStatus,consultationBookingsFuture);
            cgmGraphWidgetAndConfig = getCGMGraphWidgetAndCgmConfigurations(userContext, cgmActivationStatus,
                    cgmGraphWidgetFuture, isNonCGMProduct, cgmDeviceStatus,
                    cgmOnboardingStatus, patientDetailFuture.get().getId(), activePackResponse,
                    assignedCareTeamFuture.get(), cgmUserRequestStatusFuture.get(), cgmConfigVersion);
            if (Objects.nonNull(cgmGraphWidgetAndConfig) && Objects.nonNull(cgmGraphWidgetAndConfig.getUserCGMConfiguration())) {
                result.setCgmConfiguration(cgmGraphWidgetAndConfig.getUserCGMConfiguration());
            }
        }

        if (isSugarFit && !isExperienceCentre) {
            try {
                SfFlashbackHomeWidget flashbackHomeWidget = flashbackHomeWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (null != flashbackHomeWidget) {
                    result.addWidget(flashbackHomeWidget);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching flashbackHomeWidget", e);
            }
        }

//        SfBannerCarouselWidget glucorxInstallationDelayBanner = null;
//        try {
//             glucorxInstallationDelayBanner = chronicCareServiceHelper.getGlucoRxInstallationDelayBannerWidget(userContext);
//        } catch (Exception e) {
//            exceptionReportingService.reportException("Error in fetching readerDelayBanner", e);
//        }

        if (cgmDeviceStatus.getIsOnGoingDevice() || cgmDeviceStatus.getShouldShowScanCardInsideCGMGraph()) {

            if (Objects.nonNull(cgmGraphWidgetAndConfig) && Objects.nonNull(cgmGraphWidgetAndConfig.getUserCGMConfiguration())) {
                result.addWidget(cgmGraphWidgetAndConfig.getCgmGraphWidget());
            }
            if (ChronicCareAppUtil.isTwentyTwentyFiveSaleLive(userContext, megaSaleData)) {
                result.addWidget(diabetesStoreHomeWidget);
            }
            if (Objects.nonNull(sfHomeFBVWidget)) {
                result.addWidget(sfHomeFBVWidget);
            }
            if (Objects.nonNull(smartScaleWidget)) {
                result.addWidget(smartScaleWidget);
            }
            if (Objects.nonNull(homeProgressWidget)) {
                result.addWidget(homeProgressWidget);
            }
        } else {
            if (Objects.nonNull(sfHomeFBVWidget)) {
                result.addWidget(sfHomeFBVWidget);
            }
            if (Objects.nonNull(smartScaleWidget)) {
                result.addWidget(smartScaleWidget);
            }
            if (Objects.nonNull(homeProgressWidget)) {
                result.addWidget(homeProgressWidget);
            }
            if (ChronicCareAppUtil.isTwentyTwentyFiveSaleLive(userContext, megaSaleData)) {
                result.addWidget(diabetesStoreHomeWidget);
            }
            if (Objects.nonNull(cgmGraphWidgetAndConfig) && Objects.nonNull(cgmGraphWidgetAndConfig.getUserCGMConfiguration())) {
                result.addWidget(cgmGraphWidgetAndConfig.getCgmGraphWidget());
            }
        }

        try {
            SfThingsToDoWidget thingsToDoWidget = thingsToDoWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            result.addWidget(thingsToDoWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching thingsToDoWidget", e);
        }

        try {
            if (Objects.nonNull(digiProgramJourneyWidgetFuture)) {
                DAProgramJourneyWidget daProgramJourneyWidget = digiProgramJourneyWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (daProgramJourneyWidget != null) {
                    result.addWidget(daProgramJourneyWidget);
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching daProgramJourneyWidget", e);
        }

        if (!isExperienceCentre && isSugarFit) {
            if(isChallengeAndPollsWidgetV2Supported(userContext)){
                try {
                    SfHomeWellnessWidget homeWellnessWidget = new SfHomeWellnessWidget();
                    List<BaseWidgetNonVM> challengeWidgets = getChallengeWidgetsV2Future(userContext).get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (CollectionUtils.isNotEmpty(challengeWidgets)) homeWellnessWidget.setChallengeWidgets(new ArrayList<>(challengeWidgets));

                    List<BaseWidgetNonVM> pollWidgets = getPollCardWidgetsV2Future(userContext).get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (CollectionUtils.isNotEmpty(pollWidgets)) homeWellnessWidget.setPollWidgets(new ArrayList<>(pollWidgets));

                    if(homeWellnessWidget.getChallengeWidgets()!=null || homeWellnessWidget.getPollWidgets()!=null){
                        result.addWidget(homeWellnessWidget);
                    }

                } catch (Exception e) {
                    exceptionReportingService.reportException("Error in fetching homeWellnessWidget", e);
                }
            } else {
                try {
                    List<BaseWidgetNonVM> challengeWidgets = challengeWidgetsFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (CollectionUtils.isNotEmpty(challengeWidgets)) result.addWidgets(new ArrayList<>(challengeWidgets));
                } catch (Exception e) {
                    exceptionReportingService.reportException("Error in fetching challengeWidgets", e);
                }
                try {
                    List<BaseWidgetNonVM> mentalHealthPollWidgets = mentalHealthPollWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                    if (CollectionUtils.isNotEmpty(mentalHealthPollWidgets)) result.addWidgets(new ArrayList<>(mentalHealthPollWidgets));
                } catch (Exception e) {
                    exceptionReportingService.reportException("Error in fetching getMentalHealthPollWidgets", e);
                }
            }
        }

        try {
            if (!chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(activePackResponse.getBundleProduct())) {
                result.addWidget(liveSessionWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS));
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching LiveSessionWidget", e);
        }

//        try {
//            BaseWidgetNonVM wellnessAtGlanceWidget = wellnessAtGlanceWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
//            result.addWidget(wellnessAtGlanceWidget);
//        } catch (Exception e) {
//            exceptionReportingService.reportException("Error in fetching WellnessAtGlanceWidget", e);
//        }

        if (isSugarFit && !isExperienceCentre && isRenewalReportSupported(userContext)
                && !chronicCareServiceHelper.isRenewalReportDisabledUser(userContext)) {
            try {
                SfRenewalReportHomeWidget renewalReportHomeWidget = renewalReportHomeWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                result.addWidget(renewalReportHomeWidget);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching renewalReportWidget", e);
            }
        }

        try {
            if (assignedAgentsWidgetFuture != null) {
                AssignedAgentWidget assignedAgentsWidget = assignedAgentsWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                result.addWidget(assignedAgentsWidget);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching assignedAgentsWidget", e);
        }

        if (!ChronicCareAppUtil.isTwentyTwentyFiveSaleLive(userContext, megaSaleData)) {
            result.addWidget(diabetesStoreHomeWidget);
        }

//        try {
//            if (experimentsWidgetFuture != null) {
//                BaseWidgetNonVM experimentHomeWidget = experimentsWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
//                if (experimentHomeWidget != null) result.addWidget(experimentHomeWidget);
//            }
//        } catch (Exception e) {
//            exceptionReportingService.reportException("Error in fetching experimentHomeWidget", e);
//        }

        if (wellnessAtCenterWidgetFuture != null) {
            try {
                BaseWidgetNonVM wellnessAtCenterWidget = wellnessAtCenterWidgetFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                result.addWidget(wellnessAtCenterWidget);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching recommendedFitnessPlanWidget", e);
            }
        }

        try {
            BaseWidgetNonVM blogsWidget = blogsWidgetFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
            result.addWidget(blogsWidget);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching blogsWidget", e);
        }

        result.addWidget(getQuoteWidget(userContext));

        try {
            List<HomeMenuItem> moreMenuItems = moreMenuItemsFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
            if (moreMenuItems != null) result.setMenuItems(moreMenuItems);
        } catch (Exception e) {
            exceptionReportingService.reportException("Error in fetching homepage moreMenuItems", e);
        }

        if (!isExperienceCentre && shouldShowAppAnnouncement()) {
            try {
                AppAnnouncementData appAnnouncementData = appAnnouncementDataFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (appAnnouncementData != null) {
                    result.setAppAnnouncementData(appAnnouncementData);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching appAnnouncementData", e);
            }
        }


        if (!isExperienceCentre && isSugarFit) {
            try {
                ScratchCardData scratchCardData = getScratchCardDataFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (scratchCardData != null) {
                    result.setScratchCardData(scratchCardData);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching scratchCardData", e);
            }
        }

        if (!isExperienceCentre && !chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(activePackResponse.getBundleProduct())) {
            SfNotificationTrayData sfNotificationTrayData = new SfNotificationTrayData();
            if (ChronicCareAppUtil.isFirstCGMInstallationPending(userContext)) {
                sfNotificationTrayData.setNotificationType(SfTrayNotificationTypes.CGM_INSTALLATION);
                sfNotificationTrayData.setSkipIntervalInSecs((double) 8 * 60 * 60);
                sfNotificationTrayData.setMessage("CGM installation webinar");
                sfNotificationTrayData.setDismissDisabled(true);
                sfNotificationTrayData.setAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK)
                        .title("Join Now").url("https://zoom.us/j/95006603083?pwd=IYiieGTraXMI3nJ76k8iaZncDbQONz.1").build());
                result.setSfNotificationTrayData(sfNotificationTrayData);
            } else if (hasPrescribedDiagnosticTest(userContext, serviceInterfaces.getSfAlbusClient(),activePackResponse) && isSfDaignosticStoreTestSupportedUser(userContext, serviceInterfaces)){
                sfNotificationTrayData.setNotificationType(SfTrayNotificationTypes.DIAGNOSTIC_TEST);
                sfNotificationTrayData.setSkipIntervalInSecs((double) 14 * 24 * 60 * 60);
                sfNotificationTrayData.setMessage("Doctor has prescribed few tests");
                sfNotificationTrayData.setAction(Action.builder().actionType(ActionType.NAVIGATION)
                        .title("View").url("curefit://sfdiagnosticstestlistpage").build());
                result.setSfNotificationTrayData(sfNotificationTrayData);
            } else if (ChronicCareAppUtil.shouldShowExploreAppNotificationTray(userContext, activePackResponse.getBundleProduct(), chronicCareServiceHelper)){
                sfNotificationTrayData.setNotificationType(SfTrayNotificationTypes.EXPLORE_APP);
                sfNotificationTrayData.setSkipIntervalInSecs((double) 365 * 24 * 60 * 60);
                sfNotificationTrayData.setMessage("New here? Explore what’s new in our app");
                sfNotificationTrayData.setAction(Action.builder().actionType(ActionType.NAVIGATION)
                        .title("Explore").url("curefit://singlevideoplayer?videoUrl=https://cdn-videos.cure.fit/www-curefit-com/video/upload/image/chroniccare/explore-app_v1.mp4&title=Explore?&thumbnailUrl=").build());
                result.setSfNotificationTrayData(sfNotificationTrayData);
            }
        }

        if (!isExperienceCentre && npsModalDataCompletableFuture.get() != null) {
            NPSModalData npsModalData = npsModalDataCompletableFuture.get();
            result.setNpsModalData(npsModalData);
        }

        result.setReferralEnabled(ChronicCareAppUtil.isHomeHeaderReferralEnabled(userContext));

        boolean isCgmActive = cgmOnboardingStatus.getCgmDeviceInfos().stream().anyMatch(CGMDeviceInfo::isOngoing);
        if (!isCgmActive && ChronicCareAppUtil.isFaceBasedVitalsEnabledForUser(userContext, cgmOnboardingStatus, activePackResponse, faceBasedVitalScansForDayResponseFuture.get())){
            result.setShowFaceScanFab(true);
            if(!chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(activePackResponse.getBundleProduct())
                    && !chronicCareServiceHelper.isUnlockFbvExpModalViewed(userContext)){
                UnlockFbvExpData unlockFbvExpData = new UnlockFbvExpData();
                unlockFbvExpData.setLottieUrl("http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/unlock-animation-2025-01-07-17:42.json");
                unlockFbvExpData.setImageUrl("http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/face-scan-boy%202-2025-01-21-11:23.png");
                unlockFbvExpData.setTitle("Face Based Scan Unlocked");
                unlockFbvExpData.setSubTitle("Your CGM period is now complete. You can find your insights within the health records section of the sugar.fit app.");
                unlockFbvExpData.setShowModal(true);
                unlockFbvExpData.setUnlockModalAction(Action.builder().actionType(ActionType.SHOW_UNLOCK_FBV_EXP_MODAL)
                        .title("Start Face Scan").url("curefit://sfscanfacepage").build());

                result.setUnlockFbvExpData(unlockFbvExpData);

            }
        }

        if (!isExperienceCentre) {
            try {
                CsTicketResolutionNotificationData csTicketResolutionNotificationData = csTicketResolutionNotificationFuture.get(WIDGET_SHORT_TIMEOUT, TimeUnit.MILLISECONDS);
                if (csTicketResolutionNotificationData != null) {
                    result.setCsTicketResolutionNotificationData(csTicketResolutionNotificationData);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching appAnnouncementData", e);
            }
        }
    }

    public CompletableFuture<List<BaseWidgetNonVM>> getChallengeWidgetsV2Future(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getChallengeWidgetsV2(userContext);
            } catch (BaseException e) {
                exceptionReportingService.reportException(e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public List<BaseWidgetNonVM> getChallengeWidgetsV2(UserContext userContext) throws BaseException {
        if (ChronicCareAppUtil.isChallengesEnabled(userContext)) {
            List<BaseWidgetNonVM> challengeCardWidgetArrayList = new ArrayList<>();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            List<UserChallengeSummary> challenges = challengesClient.fetchActiveChallengeSummaryForUser(userId);

            if (CollectionUtils.isNotEmpty(challenges)) {
                List<SfChallengeCardWidgetV2> challengeCardWidgets = challenges.stream().map(userChallengeSummary -> {
                    ChallengesEntry challengesEntry = userChallengeSummary.getChallengesEntry();
                    try {
                        List<ChallengesUserResponseEntry> userEntries = challengesClient.fetchUserResponsesEntries(userId, challengesEntry.getId());
                        return ChronicCareServiceHelper.buildChallengeCardWidgetV2(userContext, userChallengeSummary, userEntries, serviceInterfaces);
                    } catch (BaseException e) {
                        log.error("error in fetching challenges user response", e);
                        return null;
                    }
                }).toList();
                if (CollectionUtils.isNotEmpty(challengeCardWidgets)) {
                    challengeCardWidgetArrayList.addAll(challengeCardWidgets);
                    return challengeCardWidgetArrayList;
                }
            }
            return null;
        }
        return null;
    }

    public CompletableFuture<List<BaseWidgetNonVM>> getPollCardWidgetsV2Future(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getPollCardWidgetsV2(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public List<BaseWidgetNonVM> getPollCardWidgetsV2(UserContext userContext) throws Exception {
        if (ChronicCareAppUtil.isPollsEnabled(userContext)) {
            SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            Set<String> relevantSegments = serviceInterfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.SF_POLL_CLIENT);
            List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(relevantSegments));
            CompletableFuture<List<PollEntry>> activePollsFuture = sfHomePageViewBuilderV1.getActivePollsFuture(userContext, userSegments);
            CompletableFuture<List<PollEntry>> participatedPollsFuture = sfHomePageViewBuilderV1.getUserParticipatedPollsFuture(userContext);
            CompletableFuture<List<PollEntry>> activeSurveysFuture = sfHomePageViewBuilderV1.getActiveSurveysFuture(userContext, userSegments);
            List<PollEntry> polls = new ArrayList<>();
            if (activePollsFuture.get() != null) polls.addAll(activePollsFuture.get());
            if (participatedPollsFuture.get() != null) polls.addAll(participatedPollsFuture.get());
            if (activeSurveysFuture.get() != null) polls.addAll(activeSurveysFuture.get());

            if (CollectionUtils.isNotEmpty(polls)) {
                List<CompletableFuture<BaseWidgetNonVM>> pollWidgetFutures = polls.stream().map(poll -> supplyAsync(() -> {
                    try {
                        return ChronicCareServiceHelper.buildPollWidgetV2(pollSupportClient, serviceInterfaces.userServiceClient, userContext, poll);
                    } catch (BaseException e) {
                        e.printStackTrace();
                        return null;
                    }
                }, serviceInterfaces.getTaskExecutor())).toList();
                List<BaseWidgetNonVM> pollWidgets = pollWidgetFutures.stream().map(pollWidgetFuture -> {
                    try {
                        return pollWidgetFuture.get();
                    } catch (Exception e) {
                        return null;
                    }
                }).filter(Objects::nonNull).toList();
                if (CollectionUtils.isNotEmpty(pollWidgets)) {
                    return pollWidgets;
                }
            }
        }
        return null;
    }

    public void navigateUserToFreemiumHomePage(SfHomePageView result) {
        AutoNavigationToNuxWidget widget = new AutoNavigationToNuxWidget();
        widget.setPageName("sffreemiumhomepage");
        result.addWidget(widget);
    }

    private void addKickStartDataForUnlockedPage(SfHomePageView result, KSJourneyResponse kickStarterJourney , ActivePackResponse activePackResponse) {
        SfKickStartData kickStartData = new SfKickStartData();
        kickStartData.setAppLocked(kickStarterJourney.isAppLocked());
        kickStartData.setWellnessLocked(kickStarterJourney.isWellnessLocked());
        kickStartData.setKSCompleted(kickStarterJourney.isKSCompleted());
        if (!chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct()) && !chronicCareServiceHelper.isShopifyCGMPack(activePackResponse.getBundleProduct())){
            kickStartData.setShowOathScreen(kickStarterJourney.isShowOathScreen());
        }
        result.setKickStartData(kickStartData);
    }

    public CompletableFuture<SfDiagnosticNudgeWidget> getSfDiagnosticNudgeWidgetFuture(UserContext userContext, ActivePackResponse activePackResponse, CompletableFuture<DiagnosticJourneyResponse> diagnosticJourneyResponseCompletableFuture) {
        return supplyAsync(() -> {
            try {
                if (IsJourneyDateCrossed270Days(userContext, activePackResponse)) {
                    List<UserMembershipInfo> membershipInfos = activePackResponse.getUserMembershipInfos();
                    for (UserMembershipInfo info : membershipInfos) {
                        //CF1069 is productCode for Q3 Diagnostics
                        if ("CF1069".equals(info.getProductCode()) && info.getTicketsConsumed() < info.getTickets()) {
                            if (Objects.nonNull(diagnosticJourneyResponseCompletableFuture) && Objects.nonNull(diagnosticJourneyResponseCompletableFuture.get())) {
                                DiagnosticJourneyResponse diagnosticJourneyResponse = diagnosticJourneyResponseCompletableFuture.get();
                                Action bookingAction = null;
                                if (diagnosticJourneyResponse.isAllowBooking()) {
                                    PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
                                    UserPreferencePojo userAddressIdPreference = serviceInterfaces.sfAlbusClient.getUserPreference(userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "addressId");
                                    String addressId = userAddressIdPreference.getPreferenceTypeValues().getFirst();
                                    String diagnosticProductCode = diagnosticJourneyResponse.getProductCode();
                                    String provider = diagnosticJourneyResponse.getPartner();
                                    bookingAction = Action.builder().isEnabled(true)
                                            .title("Unlock Now").actionType(ActionType.NAVIGATION)
                                            .url("curefit://selectCareDateV1?patientId=" + patientDetail.getId()
                                                    + "&productId=" + diagnosticProductCode + "&type=" + "DIAGNOSTICS"
                                                    + "&category=AT_HOME_SLOT&nextAction=checkout&productCodes=" + diagnosticProductCode
                                                    + "&addressId=" + addressId
                                                    + "&parentBookingId=" + activePackResponse.getBookingId() + "&diagnosticsProvider=" + provider).build();
                                    return getSfDiagnosticNudgeWidget(bookingAction);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching SfRenewalReportHomeWidget", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    public SfDiagnosticNudgeWidget getSfDiagnosticNudgeWidget(Action bookingAction) {
        try {
            SfDiagnosticNudgeWidget pendingTaskWidget = new SfDiagnosticNudgeWidget();
            pendingTaskWidget.setWidgetTitle("Pending Task");
            pendingTaskWidget.setTitle("Book your last FREE Check-up");
            pendingTaskWidget.setSubTitle("Know your health insights today.");
            pendingTaskWidget.setAction(bookingAction);

            return pendingTaskWidget;
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
        }
        return null;
    }

    public Boolean IsJourneyDateCrossed270Days(UserContext userContext, ActivePackResponse activePackResponse) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            List<UserSubscriptionEntry> subscriptionEntries = smsClient.searchSubscriptions(userId);

            if (subscriptionEntries != null && !subscriptionEntries.isEmpty()) {
                Optional<UserSubscriptionEntry> activeSubscriptionOpt = subscriptionEntries.stream()
                        .filter(s -> s.getSubscriptionId().equals(activePackResponse.getBookingId()))
                        .findFirst();

                if (activeSubscriptionOpt.isPresent()) {
                    UserSubscriptionEntry subscriptionEntry = activeSubscriptionOpt.get();
                    Date nuxCompletionDate = subscriptionEntry.getOnboardingStatusUpdateDate();
                    if (nuxCompletionDate != null) {
                        long diffInMillis = new Date().getTime() - nuxCompletionDate.getTime();
                        long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);

                        return diffInDays > 270;
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("error: " + e);
            log.error(e.getLocalizedMessage(), e);
        }
        return false;
    }

    private CompletableFuture<NPSModalData> getNPSDataFuture(UserContext userContext, ActivePackResponse activePackResponse) {
        return supplyAsync(() -> {
            try {
                if (!ChronicCareAppUtil.isNPSPeriodicLogicAddedAppVersion(userContext)) {
                    return null;
                }
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                List<UserSubscriptionEntry> subscriptionEntries = smsClient.searchSubscriptions(userId);
                if (subscriptionEntries != null && subscriptionEntries.getFirst() != null) {
                    Optional<UserSubscriptionEntry> activeSubscriptionOpt = subscriptionEntries.stream().filter(s -> s.getSubscriptionId().equals(activePackResponse.getBookingId())).findFirst();
                    if (activeSubscriptionOpt.isPresent()) {
                        UserSubscriptionEntry subscriptionEntry = activeSubscriptionOpt.get();
                        Date nuxCompletionDate = subscriptionEntry.getOnboardingStatusUpdateDate();
                        if (nuxCompletionDate != null) {
                            Date npsDismissDate = null;
                            String npsDismissAttr = "nps_dismiss_date";
                            UserAttributesResponse npsDismissAttrResp = userAttributesClient.getAttributes(userId, npsDismissAttr, getAppTenantFromUserContext(userContext), null);
                            if (npsDismissAttrResp != null && npsDismissAttrResp.getAttributes() != null
                                    && npsDismissAttrResp.getAttributes().containsKey(npsDismissAttr)
                                    && Objects.nonNull(npsDismissAttrResp.getAttributes().get(npsDismissAttr))) {
                                String response = String.valueOf(npsDismissAttrResp.getAttributes().get(npsDismissAttr));
                                if (response != null) {
                                    long timestamp = Long.parseLong(response);
                                    npsDismissDate = new Date(timestamp);
                                }
                            }
                            Date npsSubmitDate = null;
                            String npsSubmitAttr = "nps_submit_date";
                            UserAttributesResponse npsSubmitAttrResp = userAttributesClient.getAttributes(userId, npsSubmitAttr, getAppTenantFromUserContext(userContext), null);
                            if (npsSubmitAttrResp != null && npsSubmitAttrResp.getAttributes() != null
                                    && npsSubmitAttrResp.getAttributes().containsKey(npsSubmitAttr)
                                    && Objects.nonNull(npsSubmitAttrResp.getAttributes().get(npsSubmitAttr))) {
                                String response = String.valueOf(npsSubmitAttrResp.getAttributes().get(npsSubmitAttr));
                                if (response != null) {
                                    long timestamp = Long.parseLong(response);
                                    npsSubmitDate = new Date(timestamp);
                                }
                            }

                            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
                            Date currentDate = calendar.getTime();
                            long daysSinceNuxCompletion = SfDateUtils.getDifferenceDays(nuxCompletionDate, currentDate);
                            Long daysSinceLastNpsDismissal = npsDismissDate != null ? SfDateUtils.getDifferenceDays(npsDismissDate, currentDate) : null;
                            long milestoneForWhichLastSubmitted =  npsSubmitDate != null ? SfDateUtils.getDifferenceDays(nuxCompletionDate, npsSubmitDate) : 0;

                            NPSModalData npsModalData = new NPSModalData();
                            npsModalData.setTitle("How likely are you to recommend Sugarfit to a friend or colleague?");
                            npsModalData.setSubTitle("You can rate between 0 and 10");
                            npsModalData.setSkipIntervalInDays(30d);

                            if (daysSinceNuxCompletion > 300) {
                                if ((daysSinceNuxCompletion - 300) % 30 == 0 || (npsDismissDate != null && SfDateUtils.getDifferenceDays(npsDismissDate, currentDate) >= 15)) {
                                    return npsModalData;
                                }
                            }

                            int[] subscriptionMilestones = {300, 270, 180, 90, 45, 15};
                            for (int subscriptionMilestone : subscriptionMilestones) {
                                if (daysSinceNuxCompletion < subscriptionMilestone) {
                                    continue;
                                }
                                boolean npsNotSubmittedForMilestone = milestoneForWhichLastSubmitted < subscriptionMilestone;
                                boolean has15daysPassedAfterSkipping =  Objects.isNull(daysSinceLastNpsDismissal) || daysSinceLastNpsDismissal >= 15;
                                if (npsNotSubmittedForMilestone && has15daysPassedAfterSkipping){
                                    return npsModalData;
                                }
                            }
                        }
                    }
                }

            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getNPSDataFuture", e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<SfKickStartAppUnlockedWidget> getKickStartWidgetFuture(UserContext userContext, ActivePackResponse activePackResponse,
                                                                                     KSJourneyResponse kickStarterJourney,
                                                                                     ChronicCareTeam chronicCareTeam, PatientDetail patientDetail,
                                                                                     CompletableFuture<List<LiveClass>> liveClassesFuture,
                                                                                     CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                                                     FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse,UserOnboardingActionWithContext onboardingActions,CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) {
        return supplyAsync(() -> {
            try {
                SfKickStartAppUnlockedWidget widget = new SfKickStartAppUnlockedWidget();
                SfKickStartData kickStartData = new SfKickStartData();
                kickStartData.setAppLocked(kickStarterJourney.isAppLocked());
                kickStartData.setWellnessLocked(kickStarterJourney.isWellnessLocked());
                kickStartData.setKSCompleted(kickStarterJourney.isKSCompleted());
                kickStartData.setTotalStepsCount(kickStarterJourney.getTotalStepsCount());
                if (!chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct()) && !chronicCareServiceHelper.isShopifyCGMPack(activePackResponse.getBundleProduct())){
                        kickStartData.setShowOathScreen(kickStarterJourney.isShowOathScreen());
                }
                if (CollectionUtils.isEmpty(kickStarterJourney.getPendingProfiles()) || chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct()) || chronicCareServiceHelper.isShopifyCGMPack(activePackResponse.getBundleProduct())) {
                    return null;
                }

                int completedStepCount = kickStarterJourney.getTotalStepsCount() - kickStarterJourney.getPendingProfiles().size();
                kickStartData.setCompletedStepsCount(completedStepCount);

                List<KSProfiles> pendingProfiles = kickStarterJourney.getPendingProfiles();
                if (CollectionUtils.isNotEmpty(pendingProfiles)) {
                    pendingProfiles.forEach(profile -> addKickstartCardForProfile(userContext, kickStartData, kickStarterJourney, profile, SfKickStartCard.Status.PENDING, activePackResponse, chronicCareTeam, patientDetail, liveClassesFuture, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse,onboardingActions,consultationBookingsFuture));
                }
//                if (ChronicCareAppUtil.isKickStarterAllCardsSupported(userContext)) {
//                    List<KSProfiles> activeProfiles = kickStarterJourney.getActiveProfiles();
//                    if (CollectionUtils.isNotEmpty(activeProfiles)) {
//                        activeProfiles.forEach(profile -> addKickstartCardForProfile(userContext, kickStartData, kickStarterJourney, profile, SfKickStartCard.Status.COMPLETED, activePackResponse, chronicCareTeam, patientDetail, liveClassesFuture));
//                    }
//                    List<KSProfiles> completedProfiles = kickStarterJourney.getCompletedProfiles();
//                    if (CollectionUtils.isNotEmpty(completedProfiles)) {
//                        completedProfiles.forEach(profile -> addKickstartCardForProfile(userContext, kickStartData, kickStarterJourney, profile, SfKickStartCard.Status.COMPLETED, activePackResponse, chronicCareTeam, patientDetail, liveClassesFuture));
//                    }
//                }
                widget.setKickStartData(kickStartData);
                return widget;
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getKickStartDataFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<SfKickstartJourneyWidgetV2> getKickStartV2WidgetFuture(UserContext userContext, ActivePackResponse activePackResponse,
                                                                                     KSJourneyV2Response kickStarterJourney,
                                                                                     CompletableFuture<List<LiveClass>> liveClassesFuture,
                                                                                     ChronicCareTeam chronicCareTeam, PatientDetail patientDetail,
                                                                                     CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                                                     FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse, UserOnboardingActionWithContext onboardingActions,CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) {
        return supplyAsync(() -> {
            try {
                return sfKickstartJourneyWidgetV2Builder.buildView(userContext, activePackResponse, kickStarterJourney, chronicCareTeam, patientDetail, liveClassesFuture, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse,onboardingActions,consultationBookingsFuture);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getKickStartV2Widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<SfKickStartData> getKickStartDataFuture(UserContext userContext, ActivePackResponse activePackResponse,
                                                                      KSJourneyResponse kickStarterJourney,
                                                                      CompletableFuture<List<LiveClass>> liveClassesFuture,
                                                                      ChronicCareTeam chronicCareTeam, PatientDetail patientDetail,
                                                                      CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                                      FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse,
                                                                      UserOnboardingActionWithContext onboardingActions,
                                                                      CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) {
        return supplyAsync(() -> {
            try {
                return getKickStartData(userContext, activePackResponse, kickStarterJourney, liveClassesFuture, chronicCareTeam, patientDetail, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse, onboardingActions, consultationBookingsFuture);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getKickStartDataFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfKickStartData getKickStartData(UserContext userContext, ActivePackResponse activePackResponse, KSJourneyResponse kickStarterJourney,
                                             CompletableFuture<List<LiveClass>> liveClassesFuture, ChronicCareTeam chronicCareTeam,
                                             PatientDetail patientDetail, CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                             FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse,
                                             UserOnboardingActionWithContext onboardingActions,
                                             CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) {
        if (chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct()) || chronicCareServiceHelper.isShopifyCGMPack(activePackResponse.getBundleProduct())){
            return null;
        }

        SfKickStartData kickStartData = new SfKickStartData();
        kickStartData.setAppLocked(kickStarterJourney.isAppLocked());
        kickStartData.setWellnessLocked(kickStarterJourney.isWellnessLocked());
        kickStartData.setKSCompleted(kickStarterJourney.isKSCompleted());
        kickStartData.setTotalStepsCount(kickStarterJourney.getTotalStepsCount());
        kickStartData.setCompletedStepsCount(kickStarterJourney.getCompletedStepsCount());
        kickStartData.setShowOathScreen(kickStarterJourney.isShowOathScreen());

        if (isKickstarterV2Supported(userContext, serviceInterfaces, chronicCareServiceHelper, activePackResponse.getBundleProduct())) {
            try {
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                KSJourneyV2Response ksJourneyV2Response = serviceInterfaces.getSfAlbusClient().getKickStarterJourneyV2(userId, userContext.getUserProfile().getTimezone());
                SfKickstartJourneyWidgetV2 sfKickstartJourneyWidgetV2 = sfKickstartJourneyWidgetV2Builder.buildView(userContext, activePackResponse, ksJourneyV2Response, chronicCareTeam, patientDetail, liveClassesFuture, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse,onboardingActions,consultationBookingsFuture);
                if (sfKickstartJourneyWidgetV2 != null) {
                    kickStartData.setSfKickstartJourneyWidgetV2(sfKickstartJourneyWidgetV2);
                }
                return kickStartData;
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in sfKickstartJourneyWidgetV2", e);
                return kickStartData;
            }
        }

        List<KSProfiles> completedProfiles = kickStarterJourney.getCompletedProfiles();
        if (CollectionUtils.isNotEmpty(completedProfiles)) {
            completedProfiles.forEach(profile -> addKickstartCardForProfile(userContext, kickStartData, kickStarterJourney, profile, SfKickStartCard.Status.COMPLETED, activePackResponse, chronicCareTeam, patientDetail, liveClassesFuture, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse, onboardingActions, consultationBookingsFuture));
        }

        List<KSProfiles> activeProfiles = kickStarterJourney.getActiveProfiles();
        if (CollectionUtils.isNotEmpty(activeProfiles)) {
            activeProfiles.forEach(profile -> addKickstartCardForProfile(userContext, kickStartData, kickStarterJourney, profile, SfKickStartCard.Status.ACTIVE, activePackResponse, chronicCareTeam, patientDetail, liveClassesFuture, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse, onboardingActions, consultationBookingsFuture));
        }

        List<KSProfiles> pendingProfiles = kickStarterJourney.getPendingProfiles();
        if (CollectionUtils.isNotEmpty(pendingProfiles)) {
            pendingProfiles.forEach(profile -> addKickstartCardForProfile(userContext, kickStartData, kickStarterJourney, profile, SfKickStartCard.Status.PENDING, activePackResponse, chronicCareTeam, patientDetail, liveClassesFuture, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse, onboardingActions,consultationBookingsFuture));
        }

        return kickStartData;
    }

    private void addKickstartCardForProfile(UserContext userContext, SfKickStartData kickStartData, KSJourneyResponse kickStarterJourney,
                                            KSProfiles profile, SfKickStartCard.Status status, ActivePackResponse activePackResponse,
                                            ChronicCareTeam chronicCareTeam, PatientDetail patientDetail, CompletableFuture<List<LiveClass>> liveClassFuture,
                                            CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                            FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse, UserOnboardingActionWithContext onboardingActions,CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) {
        switch (profile) {
            case COACH ->
                    kickStartData.addKickStartCard(getCoachCardData(userContext, kickStarterJourney, profile, status, activePackResponse, chronicCareTeam, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse));
            case ONBOARDING_DIAGNOSTIC ->
                    kickStartData.addKickStartCard(getDiagnosticCardData(userContext, kickStarterJourney, profile, status, activePackResponse, patientDetail));
            case DOCTOR ->
                    kickStartData.addKickStartCard(getDoctorCardData(userContext, kickStarterJourney, profile, status, activePackResponse, chronicCareTeam, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse));
            case CGM ->
                    kickStartData.addKickStartCard(getCgmCardData(userContext, kickStarterJourney, profile, status, patientDetail, liveClassFuture,cgmOnboardingStatusResponse,onboardingActions,consultationBookingsFuture));
        }
    }

    public CompletableFuture<BaseWidgetNonVM> getActiveCardsWidgetFuture(UserContext userContext, PatientDetail patientDetail, CompletableFuture<ActivePackResponse> activePackResponseFuture,
                                                                         CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture, CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                                         CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture,
                                                                         CompletableFuture<List<LiveClass>> liveClassesFuture, FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        return supplyAsync(() -> {
            try {
                return getActiveCardsWidget(userContext, patientDetail, activePackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, groupClassBookingsFuture, liveClassesFuture, faceBasedVitalScansForDayResponse);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getActiveCardsWidget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfActiveCardsWidget getActiveCardsWidget(UserContext userContext, PatientDetail patientDetail,
                                                     CompletableFuture<ActivePackResponse> activePackResponseFuture,
                                                     CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture,
                                                     CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                     CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture,
                                                     CompletableFuture<List<LiveClass>> liveClassesFuture,
                                                     FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse
    ) throws ExecutionException, InterruptedException {
        Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
        Date currentTime = calendar.getTime();

        CompletableFuture<List<ActiveCard>> consultationActiveCards = chronicCareServiceHelper.getConsultationActiveCardsFuture(userContext, activePackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, groupClassBookingsFuture, faceBasedVitalScansForDayResponse);
        CompletableFuture<List<ActiveCard>> diagnosticActiveCards = chronicCareServiceHelper.getDiagnosticActiveCardsFuture(userContext, patientDetail);
        CompletableFuture<List<ActiveCard>> liveClassActiveCards = null;
        if (Objects.nonNull(activePackResponseFuture)
                && Objects.nonNull(activePackResponseFuture.get()) && Objects.nonNull(activePackResponseFuture.get().getBundleProduct())
                && !DigitalAppUtil.isDigitalAppUser(activePackResponseFuture.get().getBundleProduct())) {
            liveClassActiveCards = chronicCareServiceHelper.getLiveActiveCardsFuture(userContext, liveClassesFuture);
        }
        CompletableFuture<List<ActiveCard>> phleboBookingCards = chronicCareServiceHelper.getPhleboActiveCardsFuture(userContext);
        CompletableFuture<ActiveCard> cgmDeliveryCard = chronicCareServiceHelper.getCgmDeliveryActiveCardFuture(userContext);
        CompletableFuture<List<ActiveCard>> ecommerceOrders = chronicCareServiceHelper.getECommerceOrderActiveCardsFuture(userContext);
        CompletableFuture<List<ActiveCard>> audioCallTasks = chronicCareServiceHelper.getAudioCallUpcomingCardsFuture(userContext);

        List<ActiveCard> upcomingCards = new ArrayList<>();
        List<ActiveCard> completedCards = new ArrayList<>();
        Set<ActiveCardStatus.StatusType> upcomingActiveCardStatus = Set.of(SCHEDULED, UPCOMING, STARTED);
        Set<ActiveCardStatus.StatusType> completedActiveCardStatus = Set.of(COMPLETED, REPORT_GENERATED, SAMPLE_COLLECTED);
        Set<ActiveCardStatus.StatusType> missedActiveCardStatus = Set.of(MISSED);
        Date consultationCompletedCutOff = DateUtils.addDays(currentTime, -1);
        Date diagnosticCompletedCutOff = DateUtils.addDays(currentTime, -2);
        for (ActiveCard activeCard : consultationActiveCards.get()) {
            if (activeCard.getEndDate().after(consultationCompletedCutOff)) {
                if (upcomingActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    upcomingCards.add(activeCard);
                } else if (completedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    if (activeCard.getStartDate().after(consultationCompletedCutOff)) {
                        completedCards.add(activeCard);
                    }
                } else if (missedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    if (activeCard.getEndDate().after(currentTime)) {
                        completedCards.add(activeCard);
                    }
                }
            }
        }
        for (ActiveCard activeCard : diagnosticActiveCards.get()) {
            if (activeCard.getEndDate().after(diagnosticCompletedCutOff)) {
                if (upcomingActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    upcomingCards.add(activeCard);
                } else if (completedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    completedCards.add(activeCard);
                } else if (missedActiveCardStatus.contains(activeCard.getStatus().getTitle())) {
                    completedCards.add(activeCard);
                }
            }
        }
        for (ActiveCard activeCard : phleboBookingCards.get()) {
            if (activeCard.getEndDate() != null && activeCard.getEndDate().after(currentTime)) {
                upcomingCards.add(activeCard);
            }
        }
        if (Objects.nonNull(cgmDeliveryCard.get())) {
            upcomingCards.add(cgmDeliveryCard.get());
        }
        if (Objects.nonNull(liveClassActiveCards)) {
            upcomingCards.addAll(liveClassActiveCards.get());
        }
        if (CollectionUtils.isNotEmpty(ecommerceOrders.get())) {
            upcomingCards.addAll(ecommerceOrders.get());
        }
        if (CollectionUtils.isNotEmpty(audioCallTasks.get())) {
            upcomingCards.addAll(audioCallTasks.get());
        }
        upcomingCards.sort(Comparator.comparingLong(a -> Math.abs(a.getStartDate().getTime() - currentTime.getTime())));
        completedCards.sort(Comparator.comparingLong(a -> a.getStartDate().getTime()));
        Collections.reverse(completedCards);
        List<ActiveCard> combinedCards = new ArrayList<>();
        combinedCards.addAll(upcomingCards);
//        combinedCards.addAll(completedCards);
        SfActiveCardsWidget widget = new SfActiveCardsWidget();
        widget.setActiveCards(combinedCards);
        if (CollectionUtils.isEmpty(combinedCards)) {
            return null;
        }
        return widget;
    }

    private SfKickStartCard getCoachCardData(UserContext userContext, KSJourneyResponse kickStarterJourney, KSProfiles profile,
                                             SfKickStartCard.Status status, ActivePackResponse activePackResponse,
                                             ChronicCareTeam chronicCareTeam, CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                             FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        try {
            SfKickStartCard coachCard = new SfKickStartCard();

            KSConsultationContext coachContext = kickStarterJourney.getCoachContext();
            PatientPreferredAgentResponse coach = chronicCareTeam.getCoach();
            UserEntry user = userContext.getUserEntryCompletableFuture().get();
            String userName = user.getFirstName() != null ? user.getFirstName() : "";

            coachCard.setProfile(profile);
            coachCard.setStatus(status);

            if (coachContext.getIsCompleted()) {
                coachCard.setCompleted(true);
                coachCard.setCardTitle("Your coach consult is completed");
                coachCard.setImageUrl(getAgentImageFromResponse(coach.getAgentResponse()));
                return coachCard;
            }

            coachCard.setHeaderTitle(coach.getAgentResponse().getName());
            coachCard.setCardTitle(String.format("Hi %s", userName));
            coachCard.setCardSubtitle("Welcome aboard! Book a consultation with your coach to unlock personalized guidance");
            coachCard.setImageUrl(getAgentImageFromResponse(coach.getAgentResponse()));

            Action coachCardAction;
            boolean isEnabled = coachContext.getIsBookingAllowed();
            if (isEnabled) {
                Long coachCenterId = coach.getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
                Long coachUserId = coach.getAgentResponse().getId();
                String coachConsultationProduct = coachContext.getProductCode();
                coachCardAction = Action.builder().isEnabled(true)
                        .title("Book Coach Consult")
                        .actionType(ActionType.NAVIGATION)
                        .url(getAgentDatePickerUrl(coachConsultationProduct, coachCenterId, coachUserId, activePackResponse)).build();
            } else {
                coachCardAction = getDisabledActionForToastMessage(coachContext.getReasonForProhibition(), null);
            }

            String profileUrl = String.format("curefit://chroniccaredoctorprofile?doctorId=%s&productId=%s&disableBooking=%s", coachContext.getAgentId(), coachContext.getProductCode(), !isEnabled);
            Action profileAction = Action.builder().isEnabled(true).title("Know your coach").actionType(ActionType.NAVIGATION).url(profileUrl).build();

            coachCard.setPrimaryAction(coachCardAction);
            coachCard.setHeaderAction(profileAction);

            if ((status.equals(SfKickStartCard.Status.ACTIVE) || status.equals((SfKickStartCard.Status.COMPLETED))) && Objects.nonNull(coachContext.getActiveConsultationResponse())) {
                ActiveCard coachActiveCard = chronicCareServiceHelper.getConsultationActiveCardV2(userContext, activePackResponse, coachContext.getActiveConsultationResponse(), user, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse);
                coachActiveCard.getBookingDetails().setTitle("Coach consult booked");
                coachCard.setActiveCard(coachActiveCard);
            }

            if (Objects.nonNull(coachContext.getActiveConsultationResponse()) && MISSED_CONSULTATION_STATES.contains(coachContext.getActiveConsultationResponse().getStatus())) {
                coachCard.setMissed(true);
                coachCard.setMissedTitle("You have missed your consult");
            }
            return coachCard;
        } catch (Exception e) {
            log.error("Error in getting coach card data", e);
            return null;
        }
    }

    private SfKickStartCard getDiagnosticCardData(UserContext userContext, KSJourneyResponse kickStarterJourney, KSProfiles profile, SfKickStartCard.Status status, ActivePackResponse activePackResponse, PatientDetail patientDetail) {
        try {
            SfKickStartCard diagnosticCard = new SfKickStartCard();
            Header header = new Header();
            header.setTitle("Instructions");
            Action diagnosticCardAction;
            KSDiagnosticContext diagnosticContext = kickStarterJourney.getDiagnosticContext();

            diagnosticCard.setStatus(status);
            diagnosticCard.setProfile(profile);

            if (diagnosticContext.getIsCompleted()) {
                diagnosticCard.setCompleted(true);
                diagnosticCard.setCardTitle("Your diagnostics is done");
                diagnosticCard.setImageUrl("/image/chroniccare/kickstart_diagnostics_v1.png");
                return diagnosticCard;
            }

            diagnosticCard.setFullWidthImage(true);
            diagnosticCard.setCardTitle("Know your body");
            diagnosticCard.setCardSubtitle("Know your HbA1c, blood sugar and 70+ parameters");
            diagnosticCard.setImageUrl("/image/chroniccare/kickstart_diagnostics_v1.png");
            boolean isEnabled = diagnosticContext.getIsAllowed();
            if (isEnabled) {
                UserPreferencePojo userAddressIdPreference = serviceInterfaces.sfAlbusClient.getUserPreference(userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "addressId");
                String addressId = userAddressIdPreference.getPreferenceTypeValues().getFirst();
                String diagnosticProductCode = diagnosticContext.getProductCode();
                String provider = diagnosticContext.getPartner();
                if (ChronicCareAppUtil.isUserExperiencePartTwoReleased(userContext)) {
                    diagnosticCardAction = Action.builder().isEnabled(true)
                            .title("Book Blood Test").actionType(ActionType.SHOW_VERIFY_PROFILE_DETAILS_MODALS)
                            .meta(DiagnosticActionMeta.builder().header(header).completionPageUrl("curefit://selectCareDateV1?patientId=" + patientDetail.getId()
                                    + "&productId=" + diagnosticProductCode + "&type=" + "DIAGNOSTICS"
                                    + "&category=AT_HOME_SLOT&nextAction=checkout&productCodes=" + diagnosticProductCode
                                    + "&addressId=" + addressId
                                    + "&parentBookingId=" + activePackResponse.getBookingId() + "&diagnosticsProvider=" + provider).build())
                            .build();
                } else {
                    diagnosticCardAction = Action.builder().isEnabled(true)
                            .title("Book Blood Test").actionType(ActionType.NAVIGATION)
                            .meta(DiagnosticActionMeta.builder().header(header).build())
                            .url("curefit://selectCareDateV1?patientId=" + patientDetail.getId()
                                    + "&productId=" + diagnosticProductCode + "&type=" + "DIAGNOSTICS"
                                    + "&category=AT_HOME_SLOT&nextAction=checkout&productCodes=" + diagnosticProductCode
                                    + "&addressId=" + addressId
                                    + "&parentBookingId=" + activePackResponse.getBookingId() + "&diagnosticsProvider=" + provider).build();
                }

                diagnosticCard.setHeaderTitle(chronicCareServiceHelper.getServiceableUserAddress(addressId));
                Map<String, String> meta = new HashMap<>();
                meta.put("pageName", "chroniccarehomepage");
                diagnosticCard.setHeaderAction(Action.builder().actionType(ActionType.SF_EDIT_ADDRESS).title("Change Address").meta(meta).build());
                diagnosticCard.setPrimaryAction(diagnosticCardAction);
                return diagnosticCard;
            } else if (diagnosticContext.getPartner().equalsIgnoreCase("3P") && !Boolean.TRUE.equals(diagnosticContext.getIsScheduled())) {
                Map<String, String> meta = new HashMap<>();
                meta.put("pageName", "chroniccarehomepage");
                UserPreferencePojo userAddressIdPreference = serviceInterfaces.sfAlbusClient.getUserPreference(userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "addressId");
                String addressId = userAddressIdPreference.getPreferenceTypeValues().getFirst();
                diagnosticCard.setHeaderTitle(chronicCareServiceHelper.getServiceableUserAddress(addressId));
                diagnosticCard.setCardSubtitle(diagnosticContext.getReasonForProhibition());
                diagnosticCard.setHeaderAction(Action.builder().actionType(ActionType.SF_EDIT_ADDRESS).title("Change Address").meta(meta).build());
                diagnosticCard.setPrimaryAction(Action.builder().isEnabled(false).title("Book Blood Test").actionType(ActionType.NAVIGATION).build());
                return diagnosticCard;
            }

            if (status.equals(SfKickStartCard.Status.ACTIVE) || status.equals(SfKickStartCard.Status.COMPLETED)) {
                UserPreferencePojo userAddressIdPreference = serviceInterfaces.sfAlbusClient.getUserPreference(userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "addressId");
                String addressId = userAddressIdPreference.getPreferenceTypeValues().getFirst();
                diagnosticCard.setHeaderTitle(chronicCareServiceHelper.getServiceableUserAddress(addressId));

                if (Objects.nonNull(diagnosticContext.getDiagnosticBookingInfo())) {
                    ActiveCard diagnosticActiveCard = chronicCareServiceHelper.getDiagnosticActiveCardV2(userContext, diagnosticContext.getDiagnosticBookingInfo());
                    diagnosticActiveCard.getBookingDetails().setTitle("Blood test scheduled");
                    diagnosticCard.setActiveCard(diagnosticActiveCard);
                } else if (Objects.nonNull(diagnosticContext.getHouseMdOrder())) {
                    ActiveCard diagnosticActiveCard = chronicCareServiceHelper.getDiagnosticActiveCardV3(userContext, diagnosticContext.getHouseMdOrder());
                    diagnosticActiveCard.getBookingDetails().setTitle("Blood test scheduled");
                    diagnosticCard.setActiveCard(diagnosticActiveCard);
                }

                return diagnosticCard;
            }
            return null;
        } catch (Exception e) {
            log.error("Error in getting diagnostic card data", e);
            return null;
        }
    }

    private SfKickStartCard getDoctorCardData(UserContext userContext, KSJourneyResponse kickStarterJourney, KSProfiles profile,
                                              SfKickStartCard.Status status, ActivePackResponse activePackResponse,
                                              ChronicCareTeam chronicCareTeam, CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                              FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        try {
            SfKickStartCard doctorCard = new SfKickStartCard();

            KSConsultationContext doctorContext = kickStarterJourney.getDoctorContext();
            UserEntry user = userContext.getUserEntryCompletableFuture().get();

            doctorCard.setStatus(status);
            doctorCard.setProfile(profile);

            if (doctorContext.getIsCompleted()) {
                PatientPreferredAgentResponse doctor = chronicCareTeam.getDoctor();
                doctorCard.setCompleted(true);
                doctorCard.setCardTitle("Your doctor consult is completed");
                doctorCard.setImageUrl(getAgentImageFromResponse(doctor.getAgentResponse()));
                return doctorCard;
            }

            Action doctorCardAction;
            String doctorName = "";
            String cardSubtitle = "";
            String profileImage = "";
            boolean isEnabled = doctorContext.getIsBookingAllowed();
            if (isEnabled) {
                PatientPreferredAgentResponse doctor = chronicCareTeam.getDoctor();
                Long doctorCenterId = getDoctorCenterIdForPaidUser(doctor);
                Action doctorBookingAction = Action.builder().isEnabled(true).title("Book Doctor Consult").actionType(ActionType.NAVIGATION).url(getAgentDatePickerUrl(doctorContext.getProductCode(), doctorCenterId, doctorContext.getAgentId(), activePackResponse)).isPrimaryButton(true).build();
                doctorCardAction = chronicCareServiceHelper.getModifiedDoctorConsultActionBasedOnCgmCompletion(userContext, activePackResponse, doctorBookingAction);
                doctorName = getDoctorName(doctor.getAgentResponse().getName());
                profileImage = getAgentImageFromResponse(doctor.getAgentResponse());
                cardSubtitle = "Book an appointment to consult your doctor!";
            } else {
                if (doctorContext.getAgentId() != null) {
                    PatientPreferredAgentResponse doctor = chronicCareTeam.getDoctor();
                    String toastMessage = doctorContext.getReasonForProhibition();
                    doctorCardAction = getDisabledActionForToastMessage(toastMessage, "Book Doctor Consult");
                    doctorName = getDoctorName(doctor.getAgentResponse().getName());
                    profileImage = getAgentImageFromResponse(doctor.getAgentResponse());
                    cardSubtitle = toastMessage;
                } else {
                    doctorCardAction = getDisabledActionForToastMessage(DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT, "Book Doctor Consult");
                    profileImage = DOCTOR_ASSIGNMENT_PLACEHOLDER_URL;
                    cardSubtitle = DOCTOR_ASSIGNMENT_PLACEHOLDER_TEXT;
                }
            }

            doctorCard.setHeaderTitle(doctorName);
            doctorCard.setCardTitle("Consult your doctor");
            doctorCard.setCardSubtitle(cardSubtitle);
            doctorCard.setImageUrl(profileImage);
            doctorCard.setPrimaryAction(doctorCardAction);

            if (doctorContext.getAgentId() != null) {
                String profileUrl = String.format("curefit://chroniccaredoctorprofile?doctorId=%s&productId=%s&disableBooking=%s", doctorContext.getAgentId(), doctorContext.getProductCode(), !isEnabled);
                Action profileAction = Action.builder().isEnabled(true).title("Know your doctor").actionType(ActionType.NAVIGATION).url(profileUrl).build();
                profileAction = chronicCareServiceHelper.getModifiedDoctorConsultActionBasedOnCgmCompletion(userContext, activePackResponse, profileAction);
                doctorCard.setHeaderAction(profileAction);
            }

            if ((status.equals(SfKickStartCard.Status.ACTIVE) || status.equals((SfKickStartCard.Status.COMPLETED))) && Objects.nonNull(doctorContext.getActiveConsultationResponse())) {
                ActiveCard doctorActiveCard = chronicCareServiceHelper.getConsultationActiveCardV2(userContext, activePackResponse, doctorContext.getActiveConsultationResponse(), user, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse);
                doctorActiveCard.getBookingDetails().setTitle("Doctor consult booked");
                doctorCard.setActiveCard(doctorActiveCard);
            }

            if (Objects.nonNull(doctorContext.getActiveConsultationResponse()) && MISSED_CONSULTATION_STATES.contains(doctorContext.getActiveConsultationResponse().getStatus())) {
                doctorCard.setMissed(true);
                doctorCard.setMissedTitle("You have missed your consult");
            }

            return doctorCard;
        } catch (Exception e) {
            log.error("Error in getting doctor card data", e);
            return null;
        }
    }

    private SfKickStartCard getCgmCardData(UserContext userContext, KSJourneyResponse kickStarterJourney, KSProfiles profile, SfKickStartCard.Status status, PatientDetail patientDetail, CompletableFuture<List<LiveClass>> liveClassFuture, CgmOnboardingStatusResponse cgmOnboardingStatusResponse, UserOnboardingActionWithContext onboardingActions,CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) {
        CGMTierAndNFCStatus cgmTierAndNFCStatus = resolveNullable(() -> kickStarterJourney.getCgmContext().getCgmTierAndNFCStatus()).orElse(null);
        if (Objects.isNull(cgmTierAndNFCStatus)) {
            return null;
        }

        try {
            if (cgmTierAndNFCStatus.equals(CGMTierAndNFCStatus.TIER_1)) {
                return getTierOneCgmCardData(userContext, kickStarterJourney, profile, status);
            } else if (cgmTierAndNFCStatus.equals(CGMTierAndNFCStatus.TIER_2_NFC)) {
                return getTierTwoNfcCgmCardData(userContext, kickStarterJourney, profile, status, liveClassFuture, cgmOnboardingStatusResponse,onboardingActions, patientDetail.getId(),consultationBookingsFuture );
            } else {
                return getTierTwoNonNfcCgmCardData(userContext, kickStarterJourney, profile, status, liveClassFuture, cgmOnboardingStatusResponse, onboardingActions, patientDetail.getId(), consultationBookingsFuture);
            }
        } catch (Exception e) {
            log.error("Error in getting cgm data", e);
            exceptionReportingService.reportException(e);
            return null;
        }
    }

    private SfKickStartCGMCard getTierOneCgmCardData(UserContext userContext, KSJourneyResponse kickStarterJourney, KSProfiles profile, SfKickStartCard.Status status) throws HttpException {
        UserNfcStatusResponse userNfcStatusResponse = chsClient.getUserNfcStatus(Long.parseLong(userContext.getUserProfile().getUserId()));

        SfKickStartCGMCard cgmCard = new SfKickStartCGMCard();
        TierOneDeliveryAndInstallationContext context = kickStarterJourney.getCgmContext().getTierOneDeliveryAndInstallationContext();
        boolean allowBooking = Objects.nonNull(context) && context.isAllowBooking();
        boolean allowInstallation = Objects.nonNull(context) && context.isAllowInstallation();

        cgmCard.setProfile(profile);
        cgmCard.setStatus(status);
        cgmCard.setImageUrl("image/chroniccare/kickstart_cgm_v1.png");
        cgmCard.setHeaderTitle("Continuous Glucose Monitor");

        List<Action> actions = new ArrayList<>();
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());

        if (userNfcStatusResponse == null || !userNfcStatusResponse.getUserNfcStatus()) {
            cgmCard.setCardTitle("Experience the CGM");
            cgmCard.setCardSubtitle("Ready to install your first CGM? Book installation slot.");
        }
        else if (Objects.nonNull(context) && CollectionUtils.isEmpty(context.getPhleboTaskResponseList())) {
            cgmCard.setCardTitle("Experience the CGM");
            cgmCard.setCardSubtitle(allowBooking ? "Ready to install your first CGM? Book delivery and installation slot." : context.getReasonForProhibition());

            Action cgmCardAction = chronicCareServiceHelper.getPhleboTaskRecommendedSlotsPageRedirectAction("Book Installation Slot", allowBooking, false, null);
            actions.add(cgmCardAction);
        } else {
            cgmCard.setCardTitle("Experience the CGM");
            cgmCard.setCardSubtitle("Ready to install your first CGM? Book delivery and installation slot.");
        }

        if (allowInstallation) {
            Map<String, Object> cgmActionMeta = new HashMap<>();
            cgmActionMeta.put("isCgmAction", true);
            actions.add(Action.builder().isEnabled(true).title("Activate Sensor").meta(cgmActionMeta).build());
        }

        cgmCard.setCgmActions(actions);

        DeviceShipmentResponse deviceShipmentResponse = serviceInterfaces.getSfAlbusClient().getDeviceShipmentResponse(userId);
        if ((status.equals(SfKickStartCard.Status.ACTIVE) || status.equals(SfKickStartCard.Status.COMPLETED)) && Objects.nonNull(context) && (CollectionUtils.isNotEmpty(context.getPhleboTaskResponseList()) || (deviceShipmentResponse != null && deviceShipmentResponse.getOrderId() != null))) {
            if (CollectionUtils.isNotEmpty(context.getPhleboTaskResponseList())) {
                ActiveCard cgmActiveCard = chronicCareServiceHelper.getUpcomingPhleboCard(userContext, context.getPhleboTaskResponseList().getFirst(), null);
                cgmActiveCard.getBookingDetails().setTitle("CGM is on the way");
                cgmCard.setActiveCard(cgmActiveCard);
            }
            else {
                ActiveCard cgmDeliveryActiveCard = chronicCareServiceHelper.getCgmDeliveryActiveCard(userContext);
                if (Objects.nonNull(cgmDeliveryActiveCard)) {
                    cgmCard.setActiveCard(cgmDeliveryActiveCard);
                }
            }
        }

        if (Objects.nonNull(context) && CollectionUtils.isNotEmpty(context.getPhleboTaskResponseList())) {
            context.getPhleboTaskResponseList().forEach(task -> {
                long currentTime = Calendar.getInstance(getUserTimezone(userContext)).getTimeInMillis();
                if (Objects.nonNull(task.getEndTime()) && task.getEndTime().getTime() < currentTime) {
                    cgmCard.setMissed(true);
                    cgmCard.setMissedTitle("You have missed your cgm installation");
                }
            });
        }

        return cgmCard;
    }

    private SfKickStartCGMCard getTierTwoNfcCgmCardData(UserContext userContext, KSJourneyResponse kickStarterJourney, KSProfiles profile, SfKickStartCard.Status status, CompletableFuture<List<LiveClass>> liveClassFuture,CgmOnboardingStatusResponse cgmOnboardingStatusResponse, UserOnboardingActionWithContext onboardingActions, Long patientId , CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) throws ExecutionException, InterruptedException {
        SfKickStartCGMCard cgmCard = new SfKickStartCGMCard();

        TierTwoNFCDeliveryAndSelfInstallationContext context = kickStarterJourney.getCgmContext().getTierTwoNFCDeliveryAndSelfInstallationContext();
        cgmCard.setProfile(profile);
        cgmCard.setStatus(status);
        cgmCard.setImageUrl("image/chroniccare/kickstart_cgm_v1.png");

        boolean allowCGMActivation = resolveNullable(context::isAllowInstallation).orElse(false);
        boolean allowConsultationSlotBooking = resolveNullable(context::isAllowSelfInstallationBooking).orElse(false);
        boolean isCGMWebinarBookingCompleted = false;
        List<ActiveCard> cgmActiveCards = null;
        if (liveClassFuture != null) {
            cgmActiveCards = chronicCareServiceHelper.getCGMWebinarLiveActiveCards(userContext, liveClassFuture);
        }
        if (CollectionUtils.isNotEmpty(cgmActiveCards)) {
            // condition is for webinar booked
            isCGMWebinarBookingCompleted = Objects.nonNull(cgmActiveCards.getFirst());
        } else if (Objects.nonNull(consultationBookingsFuture)) {
            // condition is for 1-1 consultation booked
            AtomicBoolean hasPhleboVideoCallBooking = new AtomicBoolean(false);
            if (consultationBookingsFuture.isDone() && CollectionUtils.isNotEmpty(consultationBookingsFuture.get())) {
                consultationBookingsFuture.get().forEach(c -> {
                    if (Objects.nonNull(c.getProductCode()) && isPhleboConsultation(c.getProductCode())) {
                        hasPhleboVideoCallBooking.set(true);
                    }
                });
            }
            isCGMWebinarBookingCompleted = hasPhleboVideoCallBooking.get();
        }

        if (allowCGMActivation || allowConsultationSlotBooking) {
            List<Action> actions = new ArrayList<>();
            if (allowCGMActivation && isCGMWebinarBookingCompleted) {
                if(Objects.nonNull(cgmOnboardingStatusResponse)) {
                    Map<String, Object> meta = new HashMap<>();
                    meta.put("primary", true);
                    List<DeviceModel> availableCgmDevices = cgmOnboardingStatusResponse.getPendingDevices();
                    Action watchVideoAction = chronicCareServiceHelper.getCgmWatchVideoAction(availableCgmDevices);
                    Map<String, Object> givenMeta = (Map<String, Object>) watchVideoAction.getMeta();
                    if (givenMeta == null) {
                        givenMeta = new HashMap<>();
                    }
                    givenMeta.putAll(meta);
                    watchVideoAction.setMeta(givenMeta);
                    actions.add(watchVideoAction);
                }
            }
            if (allowConsultationSlotBooking && !isCGMWebinarBookingCompleted) {
                Map<String, Object> meta = new HashMap<>();
                meta.put("primary", true);
                try {
                    String phleboConsultProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
                    Action bookAction = chronicCareServiceHelper.getBookCGMInstallationAction(userContext,phleboConsultProductId);
                    bookAction.setMeta(meta);
                    actions.add(bookAction);
                } catch (Exception e) {
                    exceptionReportingService.reportException(e);
                }
            }
            if (isCGMWebinarBookingCompleted) {
                Map<String, Object> cgmActionMeta = new HashMap<>();
                cgmActionMeta.put("isCgmAction", true);
                actions.add(Action.builder().isEnabled(true).title("Activate Sensor").meta(cgmActionMeta).build());
            }

            cgmCard.setCgmActions(actions);
        }

        if (CollectionUtils.isNotEmpty(cgmActiveCards) && Objects.nonNull(cgmActiveCards.getFirst())) {
            cgmCard.setActiveCard(cgmActiveCards.getFirst());
            cgmCard.setCardTitle("CGM Installation");
            cgmCard.setCardSubtitle("Your CGM Installation slot is booked. Please join the session on time.");
        } else {
            String cardTitle = "";
            String cardSubTitle = "";
            if (!context.isDelivered()) {
                cardTitle = "Your CGM is on the way";
                cardSubTitle = "This step will get unlocked once you receive your CGM. You can install it once gets delivered.";
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                DeviceShipmentResponse deviceShipmentResponse = serviceInterfaces.getSfAlbusClient().getDeviceShipmentResponse(userId);
                if (Objects.nonNull(deviceShipmentResponse) && Objects.nonNull(deviceShipmentResponse.getOrderDate())) {
                    ActiveCard cgmDeliveryActiveCard = chronicCareServiceHelper.getCgmDeliveryActiveCard(userContext);
                    if (Objects.nonNull(cgmDeliveryActiveCard)) {
                        cgmCard.setActiveCard(cgmDeliveryActiveCard);
                    }
                }
            } else {
                cardTitle = "Experience the CGM";
                cardSubTitle = "Ready to install your CGM? Book CGM video installation slot for support";
            }
            cgmCard.setHeaderTitle("Continuous Glucose Monitor");
            cgmCard.setCardTitle(cardTitle);
            cgmCard.setCardSubtitle(cardSubTitle);
        }
        return cgmCard;
    }

    private SfKickStartCGMCard getTierTwoNonNfcCgmCardData(UserContext userContext, KSJourneyResponse kickStarterJourney, KSProfiles profile, SfKickStartCard.Status status, CompletableFuture<List<LiveClass>> liveClassFuture, CgmOnboardingStatusResponse cgmOnboardingStatusResponse, UserOnboardingActionWithContext onboardingActions, Long patientId, CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) throws ExecutionException, InterruptedException {
        SfKickStartCGMCard cgmCard = new SfKickStartCGMCard();
        TierTwoNonNFCDeliveryAndInstallationContext context = kickStarterJourney.getCgmContext().getTierTwoNonNFCDeliveryAndInstallationContext();
        cgmCard.setProfile(profile);
        cgmCard.setStatus(status);
        cgmCard.setImageUrl("image/chroniccare/kickstart_cgm_v1.png");
        cgmCard.setCardTitle("Experience the CGM");
        cgmCard.setCardSubtitle("Ready to install your CGM? Book CGM video installation slot for support");

        if (Objects.nonNull(context) && !context.isDelivered()) {
            String cardTitle = "Your CGM is on the way";
            String cardSubTitle = "This step will get unlocked once you receive your CGM. You can install it once gets delivered.";
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            DeviceShipmentResponse deviceShipmentResponse = serviceInterfaces.getSfAlbusClient().getDeviceShipmentResponse(userId);
            if (Objects.nonNull(deviceShipmentResponse) && Objects.nonNull(deviceShipmentResponse.getOrderDate())) {
                ActiveCard cgmDeliveryActiveCard = chronicCareServiceHelper.getCgmDeliveryActiveCard(userContext);
                if (Objects.nonNull(cgmDeliveryActiveCard)) {
                    cgmCard.setActiveCard(cgmDeliveryActiveCard);
                }
            }

            cgmCard.setCardTitle(cardTitle);
            cgmCard.setCardSubtitle(cardSubTitle);
            return cgmCard;
        }

        if (Objects.nonNull(context) && context.getReaderType().equals(CGMReaderType.AMBROSIA)) {
            cgmCard.setCompleted(true);
            cgmCard.setCardTitle("Your CGM is delivered");
            return cgmCard;
        }

        if (Objects.nonNull(context) && context.getReaderType().equals(CGMReaderType.LIBRE_READER)) {
            boolean allowCGMActivation = resolveNullable(context::isAllowInstallation).orElse(false);
            boolean allowConsultationSlotBooking = resolveNullable(context::isAllowSelfInstallationBooking).orElse(false);
            boolean isBookingCompleted = false;
            List<ActiveCard> cgmActiveCards = null;
            if (liveClassFuture != null) {
                cgmActiveCards = chronicCareServiceHelper.getCGMWebinarLiveActiveCards(userContext, liveClassFuture);
            }
            if (CollectionUtils.isNotEmpty(cgmActiveCards)) {
                isBookingCompleted = Objects.nonNull(cgmActiveCards.getFirst());
            } else if (Objects.nonNull(consultationBookingsFuture)) {
                AtomicBoolean hasPhleboVideoCallBooking = new AtomicBoolean(false);
                if (consultationBookingsFuture.isDone() && CollectionUtils.isNotEmpty(consultationBookingsFuture.get())) {
                    consultationBookingsFuture.get().forEach(c -> {
                        if (Objects.nonNull(c.getProductCode()) && isPhleboConsultation(c.getProductCode())) {
                            hasPhleboVideoCallBooking.set(true);
                        }
                    });
                }
                isBookingCompleted = hasPhleboVideoCallBooking.get();
            }

            if (allowCGMActivation || allowConsultationSlotBooking) {
                List<Action> actions = new ArrayList<>();
                if (allowCGMActivation && isBookingCompleted) {
                   if(Objects.nonNull(cgmOnboardingStatusResponse)){
                       Map<String, Object> meta = new HashMap<>();
                       meta.put("primary", true);
                       List<DeviceModel> availableCgmDevices = cgmOnboardingStatusResponse.getPendingDevices();
                       Action watchVideoAction = chronicCareServiceHelper.getCgmWatchVideoAction(availableCgmDevices);
                       actions.add(watchVideoAction);
                   }
                }
                if (allowConsultationSlotBooking && !isBookingCompleted) {
                    Map<String, Object> meta = new HashMap<>();
                    meta.put("primary", true);
                    try {
                        String phleboConsultProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
                        Action bookAction = chronicCareServiceHelper.getBookCGMInstallationAction(userContext,phleboConsultProductId);
                        bookAction.setMeta(meta);
                        actions.add(bookAction);
                    } catch (Exception e) {
                        //Ignore
                    }
                }

                if (ChronicCareAppUtil.isUserExperiencePartTwoReleased(userContext)) {
                    boolean hasGlucoRxCgm = false;
                    CgmOnboardingStatusResponse cgmOnboardingStatus = null;
                    try {
                        cgmOnboardingStatus = chsClient.fetchOnboardingStatus(Long.valueOf(userContext.getUserProfile().getUserId()), null,
                                getAppTenantFromUserContext(userContext));
                        if (Objects.nonNull(cgmOnboardingStatus)) {
                            hasGlucoRxCgm = cgmOnboardingStatus.getPendingDevices()
                                    .stream()
                                    .anyMatch(device -> device == DeviceModel.GLUCO_RX);
                        }
                    } catch (Exception e) {
                        log.error("Error in getting cgm onboarding status", e);
                    }


                    Map<String, Object> meta = new HashMap<>();
                    meta.put("secondary", true);
                    Action activateAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfcgmreaderinstallpage").title("Activate Now").build();
                    if (hasGlucoRxCgm) {
                        Map<String, Object> cgmActionMeta = new HashMap<>();
                        cgmActionMeta.put("cgms", cgmOnboardingStatus.getPendingDevices());
                        activateAction = Action.builder().actionType(ActionType.SHOW_CGM_TYPE_SELECTION_MODAL).title("ACTIVATE NOW").meta(cgmActionMeta).build();
                    }
                    activateAction.setMeta(meta);
                    actions.add(activateAction);
                }

                cgmCard.setCgmActions(actions);
            }

            if (CollectionUtils.isNotEmpty(cgmActiveCards) && Objects.nonNull(cgmActiveCards.getFirst())) {
                cgmCard.setActiveCard(cgmActiveCards.getFirst());
                cgmCard.setCardTitle("CGM Installation");
                cgmCard.setCardSubtitle("Your CGM Installation slot is booked. Please join the session on time.");
            } else {
                cgmCard.setHeaderTitle("Continuous Glucose Monitor");
                cgmCard.setCardTitle("Experience the CGM");
                cgmCard.setCardSubtitle("Ready to install your CGM? Book CGM video installation slot for support");
            }
        }

        return cgmCard;
    }

    CompletableFuture<SfThingsToDoWidget> getThingsToDoWidgetFuture(UserContext userContext,
                                                                    CompletableFuture<UserTodoDaySummary> userTodoDaySummaryFuture,
                                                                    UserOnboardingActionWithContext onboardingActions,
                                                                    CompletableFuture<ChronicCareTeam> assignedCareTeamFuture,
                                                                    ActivePackResponse activePackResponse,
                                                                    CompletableFuture<PatientDetail> patientDetailCompletableFuture,
                                                                    CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                                    FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        return supplyAsync(() -> {
            try {
                UserTodoDaySummary userTodoDaySummary = userTodoDaySummaryFuture.get();
                ChronicCareTeam assignedCareTeam = assignedCareTeamFuture.get();
                PatientDetail patientDetail = patientDetailCompletableFuture.get();
                return getThingsToDoWidget(userContext, userTodoDaySummary, onboardingActions, assignedCareTeam,
                        activePackResponse, patientDetail, cgmOnboardingStatusResponse, faceBasedVitalScansForDayResponse);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getKickStartDataFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    SfThingsToDoWidget getThingsToDoWidget(UserContext userContext, UserTodoDaySummary userTodoDaySummary,
                                           UserOnboardingActionWithContext onboardingActions,
                                           ChronicCareTeam assignedCareTeam, ActivePackResponse activePackResponse,
                                           PatientDetail patientDetail, CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                           FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        SfThingsToDoWidget thingsToDoWidget = new SfThingsToDoWidget();

        if (CollectionUtils.isEmpty(userTodoDaySummary.getPendingTodoList()) && CollectionUtils.isEmpty(userTodoDaySummary.getCompletedTodoList())) {
            return null;
        }

        if (CollectionUtils.isNotEmpty(userTodoDaySummary.getLoggingTodoList())) {
            if (!userTodoDaySummary.getIsLoggingCompleted()) {
                thingsToDoWidget.addCard(thingsToDoCardsBuilder.getLoggingItemCard(userTodoDaySummary.getLoggingTodoList(), userContext, userTodoDaySummary.getIsLoggingCompleted()));
            }
        }

        if (CollectionUtils.isNotEmpty(userTodoDaySummary.getPendingTodoList())) {
            userTodoDaySummary.getPendingTodoList().forEach(pendingTask -> {
                switch (pendingTask.getUserTodoTaskType()) {
                    case BOOK_COACH_CONSULT ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getBookCoachConsultCard(pendingTask, assignedCareTeam, activePackResponse));
                    case BOOK_DOCTOR_CONSULT ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getBookDoctorConsultCard(pendingTask, userContext, assignedCareTeam, activePackResponse));
                    case PSYCHOLOGIST_CONSULT ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getBookPsychologistConsultCard(pendingTask, activePackResponse));
                    case BOOK_DIAGNOSTICS ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getBookDiagnosticsCard(userContext, pendingTask, activePackResponse, patientDetail));
                    case FBV_SCAN -> thingsToDoWidget.addCard(thingsToDoCardsBuilder.getFaceScanCard(pendingTask, userContext, cgmOnboardingStatusResponse, activePackResponse, faceBasedVitalScansForDayResponse));
                    case CELEBRATE_YOUR_COACH ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getCoachCelebrationCard(pendingTask, assignedCareTeam));
                    case JOIN_CHALLENGE ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getJoinChallengeCard(userContext, pendingTask));
                    case VIEW_DIAGNOSTIC_REPORT ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getDiagnosticReportCard(pendingTask));
                    case FITNESS_DEVICE_SYNC ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getFitnessSyncCard(userContext, pendingTask));
                    case OFFLINE_ACCESS ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getOfflineAccessCard(pendingTask));
                    case SAVE_COACH_NUMBER ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getSaveCoachContactCard(userContext, pendingTask));
                    case INTERVENTIONS ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getInterventionCard(userContext, pendingTask, false));
                    case LIVE_CLASS ->
                            thingsToDoWidget.addCard(thingsToDoCardsBuilder.getInterventionCard(userContext, pendingTask, true));
                }
            });
        }

        Action seeMoreAction = Action.builder().title("View all tasks").actionType(ActionType.NAVIGATION).url("curefit://sfthingstodopage?index=0").build();

        thingsToDoWidget.reverseCards();
        thingsToDoWidget.setTotal((long) userTodoDaySummary.getTotalTodoCount());
        thingsToDoWidget.setCompleted((long) userTodoDaySummary.getCompletedTodoCount());
        thingsToDoWidget.setSeeMoreAction(seeMoreAction);
        thingsToDoWidget.setNumberOfCardsToShow(5L);
        return thingsToDoWidget;
    }

    public static SfCgmDeviceStatus getCgmDeviceStatus(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus, String cgmDeviceId, BundleProduct bundleProduct, ChronicCareServiceHelper chronicCareServiceHelper ,AlbusClient albusClient) {
        SfCgmDeviceStatus deviceStatus = new SfCgmDeviceStatus(cgmDeviceId, false, false);
        boolean isSensorJustInitialized = false;
//        boolean isTierTwoNonNfcWithSupportedApp = false;
        Boolean isOnGoingDevice = deviceStatus.getIsOnGoingDevice();
        boolean shouldShowScanCardInsideCGMGraph;
        DeviceModel deviceModel = null;
        CGMDeviceInfo latestCgmDeviceInfo = null;
        List<CGMDeviceInfo> deviceInfos = cgmOnboardingStatus.getCgmDeviceInfos();
        if (cgmDeviceId == null || cgmDeviceId.isEmpty() || cgmDeviceId.equals(CGM_ACTIVATION_DUMMY_DEVICE_ID)) {
            cgmDeviceId = null;
            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                deviceInfos = deviceInfos.stream().filter(cgmDeviceInfo -> {
                    if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) || "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) || null == cgmDeviceInfo.getStartedOn()) {
                        return false;
                    }
                    return cgmDeviceInfo.isAtleastOneReadingDone() || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState());
                }).toList();

                if (!deviceInfos.isEmpty()) {
                    isSensorJustInitialized = true;
                    cgmDeviceId = deviceInfos.getLast().getDeviceId();
                    isOnGoingDevice = deviceInfos.getLast().isOngoing();
                    deviceModel = cgmOnboardingStatus.isCgmOperational() ? deviceInfos.getLast().getDeviceModel(): null;
                    latestCgmDeviceInfo = deviceInfos.getLast();
                }

//                if(isSfPhotoReaderSupportedUser(userContext, albusClient, chronicCareServiceHelper))
//                {
//                    isTierTwoNonNfcWithSupportedApp = true;
//                }
            }
            shouldShowScanCardInsideCGMGraph = (isSensorJustInitialized && cgmOnboardingStatus.isEnableFirstInAppReading()) || cgmActivationAllowed(userContext, cgmOnboardingStatus) || (!isOnGoingDevice && (chronicCareServiceHelper.isQuickCommerceCGMPack(bundleProduct)));
        } else {
            String finalCgmDeviceId = cgmDeviceId;
            CGMDeviceInfo deviceInfo = deviceInfos.stream().filter(cgmDeviceInfo -> "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState()) && cgmDeviceInfo.getDeviceId().equals(finalCgmDeviceId)).findFirst().orElse(null);
            if (deviceInfo != null) {
                isSensorJustInitialized = true;
            }
            CGMDeviceInfo currentDevice = deviceInfos.stream().filter(cgmDeviceInfo -> cgmDeviceInfo.getDeviceId().equals(finalCgmDeviceId)).findFirst().orElse(null);
            if (currentDevice != null) {
                isOnGoingDevice = currentDevice.isOngoing();
                deviceModel = currentDevice.getDeviceModel();
                latestCgmDeviceInfo = currentDevice;
            }
//            if(isSfPhotoReaderSupportedUser(userContext, albusClient, chronicCareServiceHelper))
//            {
//                isTierTwoNonNfcWithSupportedApp = true;
//            }
            shouldShowScanCardInsideCGMGraph = isSensorJustInitialized && cgmOnboardingStatus.isEnableFirstInAppReading();
        }
        deviceStatus.setCgmDeviceId(cgmDeviceId);
        deviceStatus.setIsOnGoingDevice(isOnGoingDevice);
        deviceStatus.setCgmDeviceModel(deviceModel);
        deviceStatus.setLatestCgmDeviceInfo(latestCgmDeviceInfo);
        deviceStatus.setShouldShowScanCardInsideCGMGraph(shouldShowScanCardInsideCGMGraph);
        return deviceStatus;
    }

    public CompletableFuture<CGMGraphWidgetV2> getCGMGraphWidgetFuture(UserContext userContext, SfCgmDeviceStatus cgmDeviceStatus, CompletableFuture<CgmStat> cgmStatFuture, CgmOnboardingStatusResponse cgmOnboardingStatus, boolean isPackExpired, BundleProduct bundleProduct) {
        return supplyAsync(() -> {
            try {
                return getCGMGraphWidget(userContext, cgmDeviceStatus, Objects.nonNull(cgmStatFuture) ? cgmStatFuture.get() : null, cgmOnboardingStatus, isPackExpired, bundleProduct);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in getting cgm graph widget", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public SfBluconJourneyWidget getBluconJourneyWidget(UserContext userContext, UserOnboardingActionWithContext onboardingActions, CgmOnboardingStatusResponse cgmOnboardingStatus, Long patientId) throws ResourceNotFoundException {
        boolean allowConsultationSlotBooking = false;
        if (onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse() != null
                && onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions() != null) {
            allowConsultationSlotBooking = onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions().equals(SelfInstallationActions.ALLOW_BOOKING_FOR_BLUCON);
        }
        String os = userContext.getSessionInfo().getOsName();
        String loginDeeplink = "";
        if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink())) {
            loginDeeplink = cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink().get(os);
        }
        String passKey = "";
        if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice())) {
            passKey = cgmOnboardingStatus.getBluconOnboardingStatusResponse().getBluconDevice().getHardwareId();
        }

        SfBluconJourneyWidget bluconJourneyWidget = new SfBluconJourneyWidget();
        SfBluconJourneyWidget.Steps steps = bluconJourneyWidget.getSteps();

        SfBluconJourneyWidget.Booking booking = steps.getBooking();
        booking.setEnabled(allowConsultationSlotBooking);
        booking.setCompleted(!allowConsultationSlotBooking);
        if (booking.isCompleted()) {
            booking.setTitle("Your video consult is booked");
        }
      try {
          String phleboConsultProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
          Action bookSlotAction = chronicCareServiceHelper.getBookCGMInstallationByPhleboAction(patientId, "Book Consultation Slot", phleboConsultProductId);
          booking.setAction(bookSlotAction);
      } catch (Exception e) {
          exceptionReportingService.reportException(e);
      }
        SfBluconJourneyWidget.Download download = steps.getDownload();
        download.setEnabled(booking.isCompleted());
        download.setAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).title("Download App")
                .url(os.equalsIgnoreCase("android") ? "https://play.google.com/store/apps/details?id=com.ambrosia.linkblucon"
                        : "https://apps.apple.com/us/app/linkblucon/id1200239770").build());

        SfBluconJourneyWidget.Registration registration = steps.getRegistration();
        registration.setEnabled(booking.isCompleted());
        SfBluconJourneyWidget.RegdSteps regdSteps = registration.getSteps();
        SfBluconJourneyWidget.Passkey passkey = regdSteps.getPasskey();
        passkey.setPasskey(passKey);
        SfBluconJourneyWidget.Signin signin = regdSteps.getSignin();
        signin.setAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).title("Sign-in").url(loginDeeplink).build());
        regdSteps.setPasskey(passkey);
        regdSteps.setSignin(signin);
        registration.setSteps(regdSteps);

        steps.setBooking(booking);
        steps.setDownload(download);
        steps.setRegistration(registration);
        bluconJourneyWidget.setSteps(steps);
        return bluconJourneyWidget;
    }

    public CGMGraphWidgetV2 getCGMGraphWidget(UserContext userContext, SfCgmDeviceStatus cgmDeviceStatus, CgmStat cgmStat, CgmOnboardingStatusResponse cgmOnboardingStatus, boolean isPackExpired, BundleProduct bundleProduct) throws IOException {
        boolean hasOnGoingCGM = cgmOnboardingStatus.getCgmDeviceInfos().stream().anyMatch(CGMDeviceInfo::isOngoing);

        if (Objects.nonNull(cgmStat)){
            cgmStat.setDailySugarStatMap(getSortedStatMap(cgmStat.getDailySugarStatMap(),userContext));
        }
        if ((!isPackExpired || chronicCareServiceHelper.isQuickCommerceCGMPack(bundleProduct))
                && cgmDeviceStatus.getShouldShowScanCardInsideCGMGraph()
                && (cgmActivationAllowed(userContext, cgmOnboardingStatus) || (!cgmDeviceStatus.getIsOnGoingDevice() && (chronicCareServiceHelper.isQuickCommerceCGMPack(bundleProduct))))) {
            cgmStat.setCgmDeviceId(CGM_ACTIVATION_DUMMY_DEVICE_ID);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(getUserTimezone(userContext));
        Calendar cal = Calendar.getInstance(getUserTimezone(userContext));
        Date lastReadingDate = cal.getTime();
        try {
            if (Objects.nonNull(cgmOnboardingStatus.getLastReadingDate())) {
                lastReadingDate = cgmOnboardingStatus.getLastReadingDate();
                cal.add(Calendar.DATE, -HIDE_GRAPH_AFTER_DAYS_OF_COMPLETION);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        if (!isPackExpired
                && (Objects.isNull(cgmOnboardingStatus.getLastReadingDate()) || (Objects.nonNull(cgmOnboardingStatus.getLastReadingDate()) && lastReadingDate.before(cal.getTime())))
                && !(hasOnGoingCGM || cgmActivationAllowed(userContext, cgmOnboardingStatus) || cgmDeviceStatus.getShouldShowScanCardInsideCGMGraph())) {
            return null;
        }
        if (isPackExpired && !cgmOnboardingStatus.isAtleastOneCGMCompleted() && !cgmDeviceStatus.getShouldShowScanCardInsideCGMGraph()){
            return null;
        }

        List<CGMGraphWidgetV2.CGMActionSelection> cgmSelectionList = getCGMActionSelectionList(userContext, true, cgmDeviceStatus, cgmOnboardingStatus, bundleProduct);
        boolean showScanButtonOnCgmGraphWidget = !cgmOnboardingStatus.isEnableFirstInAppReading();
        CGMGraphWidgetV2.CGMGraphTopDisclaimer cgmGraphTopDisclaimer = getCgmTopInstruction(cgmOnboardingStatus, userContext);
        CGMGraphWidgetV2.CGMPurchaseInfo cgmPurchaseInfo = getCgmPurchaseInfo(cgmOnboardingStatus, cgmStat, userContext);
        return buildCGMGraphWidget(userContext, cgmStat, cgmSelectionList, showScanButtonOnCgmGraphWidget, cgmGraphTopDisclaimer, cgmPurchaseInfo, cgmOnboardingStatus);
    }

    public CGMGraphWidgetV2 buildCGMGraphWidget(UserContext userContext, CgmStat cgmStat,
                                                List<CGMGraphWidgetV2.CGMActionSelection> cgmSelectionList,
                                                boolean showScanButtonOnCgmGraphWidget,
                                                CGMGraphWidgetV2.CGMGraphTopDisclaimer cgmGraphTopDisclaimer,
                                                CGMGraphWidgetV2.CGMPurchaseInfo cgmPurchaseInfo,
                                                CgmOnboardingStatusResponse cgmOnboardingStatus) {
        if (isSfPhotoReaderSupportedUser(userContext,serviceInterfaces.getSfAlbusClient(), chronicCareServiceHelper)
                && cgmOnboardingStatus.isDeviceInOrderedState()) {
            return null;
        }
        String userId = userContext.getUserProfile().getUserId();
        String graphVersion = isLightWeightGraphEnabledForUser(userContext) ? "v2" : "v1";
        boolean isSkiaEngineEnabled = isSkiaEngineEnabled(userContext);
        CGMGraphWidgetV2 cgmGraphWidget;
        if (Objects.nonNull(cgmStat) && Objects.nonNull(cgmStat.getCurrentReadingMode()) && cgmStat.getCurrentReadingMode() == ReadingMode.APP) {
            cgmGraphWidget = new CGMGraphWidgetV2(userContext, cgmStat, CGMGraphWidget.IN_APP_SCAN_TEXT, cgmSelectionList, showScanButtonOnCgmGraphWidget, graphVersion, cgmGraphTopDisclaimer, cgmPurchaseInfo, isSkiaEngineEnabled);
        } else {
            cgmGraphWidget = new CGMGraphWidgetV2(userContext, cgmStat, CGMGraphWidget.PHLEBO_SCAN_TEXT, cgmSelectionList, showScanButtonOnCgmGraphWidget, graphVersion, cgmGraphTopDisclaimer, cgmPurchaseInfo, isSkiaEngineEnabled);
        }

        if (ChronicCareAppUtil.isSfExperimentEnabledUser(userContext)) {
            CGMGraphWidgetV2.ExperimentsData experimentsData = new CGMGraphWidgetV2.ExperimentsData();
            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            experimentsData.setOnGoingExperiments(ongoingExperimentsViewBuilder.buildView(userContext, calendar.getTime()));
            cgmGraphWidget.setExperimentsData(experimentsData);
        }

        if (isDiabeticProfileDataSupported(userContext)) {
            try {
                Optional<UserRecommendationLogEntry> res = this.serviceInterfaces.getSmsClient().getLatestUserRecommendationLog(Long.valueOf(userId));
                if (Objects.nonNull(res) && res.isPresent() && res.get().getDiabeticProfile() != null) {
                    boolean isDiabeticProfileHigh = res.get().getDiabeticProfile().equals(MetricThresholdLimit.HIGH);

                    Action buyCgmAction = new Action("curefit://sfecommercepdp?category=CGM_STORE", "Buy More CGM",ActionType.NAVIGATION);
                    Action continueCgmJourneyAction = new Action(isDiabeticProfileHigh ? "curefit://renewsubscription" : "curefit://sfrenewaljourneypackspage", null,ActionType.NAVIGATION);
                    continueCgmJourneyAction.setImage(isDiabeticProfileHigh ? "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/cgm-insights-sales-banner-diabetic-2025-06-26-16:16.png" : "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/cgm-insights-sales-banner-nondiabetic-2025-06-20-18:29.png");

                    cgmGraphWidget.setDaysLeftToExpire(chronicCareServiceHelper.getDaysLeftToCgmExpiry(cgmOnboardingStatus, cgmStat));
                    cgmGraphWidget.setBuyCgmAction(buyCgmAction);
                    cgmGraphWidget.setContinueCgmJourneyAction(continueCgmJourneyAction);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
//                log.error("SGM_CGM_PACK_ERROR: " + userId, e);
            }
        }

        cgmGraphWidget.setWidgetType(WidgetType.CHRONIC_CARE_CGM_GRAPH_WIDGET_V3);
        if (isSfPhotoReaderSupportedUser(userContext,serviceInterfaces.getSfAlbusClient(), chronicCareServiceHelper)){
            cgmGraphWidget.setNonNfcTierTwo(true);
        }
        return cgmGraphWidget;
    }

    public List<CGMGraphWidgetV2.CGMActionSelection> getCGMActionSelectionList(UserContext userContext, boolean isHomePage, SfCgmDeviceStatus cgmDeviceStatus, CgmOnboardingStatusResponse cgmOnboardingStatusResponse, BundleProduct bundleProduct) {
        List<CGMDeviceInfo> cgmDeviceInfos = cgmOnboardingStatusResponse.getCgmDeviceInfos();
        List<CGMGraphWidgetV2.CGMActionSelection> cgmSelectionList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
            cgmDeviceInfos = cgmDeviceInfos.stream().filter(cgmDeviceInfo -> {
                if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) || "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) || null == cgmDeviceInfo.getStartedOn()) {
                    return false;
                }
                return isHomePage ? (cgmDeviceInfo.isAtleastOneReadingDone() || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState())) : cgmDeviceInfo.isAtleastOneReadingDone();
            }).collect(Collectors.toList());
            for (int idx = 0; idx < cgmDeviceInfos.size(); idx++) {
                CGMDeviceInfo cgmDeviceInfo = cgmDeviceInfos.get(idx);
                CGMGraphWidgetV2.CGMActionSelection cgmSelection = new CGMGraphWidgetV2.CGMActionSelection();
                String title = "CGM #" + (idx + 1);
                String subTitle;
                if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) || "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) || null == cgmDeviceInfo.getStartedOn()) {
                    continue;
                }
                if (cgmDeviceInfo.isOngoing()) {
                    subTitle = "(Ongoing)";
                } else {
                    String startDate = TimeUtil.formatDateInTimezone(TimeUtil.IST_TIMEZONE, cgmDeviceInfo.getStartedOn(), "MMM d");
                    String endDate = TimeUtil.formatDateInTimezone(TimeUtil.IST_TIMEZONE, cgmDeviceInfo.getEndDate(), "MMM d");
                    subTitle = "(" + startDate + " - " + endDate + ")";
                }
                cgmSelection.setTitle(title);
                cgmSelection.setId(cgmDeviceInfo.getDeviceId());
                cgmSelection.setSubTitle(subTitle);
                cgmSelection.setActionType("CGM_DATA");
                cgmSelectionList.add(cgmSelection);
            }
        }
        if (cgmActivationAllowed(userContext, cgmOnboardingStatusResponse) || (!cgmDeviceStatus.getIsOnGoingDevice() && (chronicCareServiceHelper.isQuickCommerceCGMPack(bundleProduct)))) {
            CGMGraphWidgetV2.CGMActionSelection cgmSelection = new CGMGraphWidgetV2.CGMActionSelection();
            String title = "CGM #" + (cgmDeviceInfos.size() + 1);
            cgmSelection.setTitle(title);
            cgmSelection.setSubTitle("");
            cgmSelection.setId(CGM_ACTIVATION_DUMMY_DEVICE_ID);
            cgmSelection.setActionType("CGM_DATA");
            cgmSelectionList.add(cgmSelection);
        }
        return cgmSelectionList;
    }

    private CGMGraphWidgetV2.CGMGraphTopDisclaimer getCgmTopInstruction(CgmOnboardingStatusResponse cgmOnboardingStatus, UserContext userContext) {
        boolean isOnBlucon = false;
        boolean isOnGlucoRx= false;
        if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())) {
            isOnBlucon = cgmOnboardingStatus.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon();
        }
        List<CGMDeviceInfo> deviceInfoList = cgmOnboardingStatus.getCgmDeviceInfos();

        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            CGMDeviceInfo latestCgmDevice = deviceInfoList.get(deviceInfoList.size() - 1);
            isOnGlucoRx = DeviceModel.GLUCO_RX.equals(latestCgmDevice.getDeviceModel());
            Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
            if (latestCgmDevice.getCurrentState().equals("AGM_CRASHED")) {
                long diffInMillis = calendar.getTime().getTime() - latestCgmDevice.getEndDate().getTime();
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                if (diffInHours <= 72) {
                    return CGMGraphWidgetV2.getCgmCrashedDisclaimer(userContext);
                }
            } else if (latestCgmDevice.getCurrentState().equals("AGM_FINAL_READING_UPLOADED") || latestCgmDevice.getCurrentState().equals("AGM_ENDED")) {
                long diffInMillis = calendar.getTime().getTime() - latestCgmDevice.getEndDate().getTime();
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                if (diffInHours <= 72 && isUltraFitApp(userContext)) {
                    return CGMGraphWidgetV2.getCgmEndedDisclaimer(userContext);
                }
            } else if (latestCgmDevice.getEndDate() == null && latestCgmDevice.getStartedOn() != null && (latestCgmDevice.getCurrentState().equals("AGM_FIRST_READING_UPLOADED") || latestCgmDevice.getCurrentState().equals("AGM_READING_UPLOADED"))) {
                long diffInMillis = calendar.getTime().getTime() - latestCgmDevice.getStartedOn().getTime();
                long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);

                if (diffInHours <= 24 && !isOnGlucoRx) {
                    return CGMGraphWidgetV2.get24HoursDisclaimer(userContext);
                } else if (isOnBlucon) {
                    try {
                        BluconEventEntry bluconEventEntry = chsClient.fetchLatestBluconEvent(
                                Long.valueOf(userContext.getUserProfile().getUserId()), AppUtil.getAppTenantFromUserContext(userContext));
                        if (Objects.nonNull(bluconEventEntry)) {
                            String os = userContext.getSessionInfo().getOsName();
                            String loginDeeplink = "";
                            if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())
                                    && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink())) {
                                loginDeeplink = cgmOnboardingStatus.getBluconOnboardingStatusResponse().getLoginDeepLink().get(os);
                            }
                            switch (bluconEventEntry.getEventName()) {
                                case "BLUCON_CONNECTION" -> {
                                    if (bluconEventEntry.getResult().equalsIgnoreCase("Connected")) {
                                        return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                                    } else if (bluconEventEntry.getResult().equalsIgnoreCase("Disconnected")) {
                                        return CGMGraphWidgetV2.getBluconDisconnectedDisclaimer(userContext, loginDeeplink);
                                    }
                                }
                                case "BLUCON_BATTERY_STATUS" -> {
                                    if (bluconEventEntry.getResult().equalsIgnoreCase("low-battery")) {
                                        return CGMGraphWidgetV2.getBluconBatteryLowDisclaimer(userContext);
                                    }
                                }
                                case "BAD_READINGS" -> {
                                    return CGMGraphWidgetV2.getBluconDisplacedDisclaimer(userContext);
                                }
                                case "PATCH_ERROR" -> {
                                    if (bluconEventEntry.getResult().equalsIgnoreCase("Patch not found")
                                            || bluconEventEntry.getResult().equalsIgnoreCase("Patch reading error")) {
                                        return CGMGraphWidgetV2.getBluconDisplacedDisclaimer(userContext);
                                    }
                                }
                                case "LINKBLUCON_CRASH" -> {
                                    return CGMGraphWidgetV2.getBluconDisconnectedDisclaimer(userContext, loginDeeplink);
                                }
                                case "logout" -> {
                                    return CGMGraphWidgetV2.getBluconLoggedOutDisclaimer(userContext, loginDeeplink);
                                }
                                default -> {
                                    return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                                }
                            }
                        } else {
                            return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                        }
                    } catch (Exception e) {
                        exceptionReportingService.reportException(e);
                        return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                    }
                } else if (isOnGlucoRx) {
                    try {
                        BluconEventEntry bluconEventEntry = chsClient.fetchLatestBluconEvent(
                                Long.valueOf(userContext.getUserProfile().getUserId()), AppUtil.getAppTenantFromUserContext(userContext));
                        if (Objects.nonNull(bluconEventEntry)) {
                            switch (bluconEventEntry.getResult()) {
                                case "SENSOR_STATUS_NOT_EXIST" -> {
                                    return CGMGraphWidgetV2.getGlucoRxNotExistDisclaimer(userContext);
                                }
                                case "SENSOR_STATUS_STABLING" -> {
                                    return CGMGraphWidgetV2.getGlucoRxStablingDisclaimer(userContext);
                                }
                                case "SENSOR_MALFUNCTION", "SENSOR_STATUS_DATA_NOT_VALID" , "SENSOR_STATUS_REPLACE_1", "SENSOR_STATUS_REPLACE_0" -> {
                                    return CGMGraphWidgetV2.getGlucoRxCrashedDisclaimer(userContext);
                                }
                                case "SENSOR_STATUS_NEW_SENSOR" -> {
                                    return CGMGraphWidgetV2.getGlucoRxSensorDisclaimer(userContext);
                                }
                                case "SENSOR_STATUS_WARMUP" -> {
                                    return CGMGraphWidgetV2.getGlucoRxWarmupDisclaimer(userContext);
                                }
                                case "SENSOR_STATUS_EXPIRATION" -> {
                                    return CGMGraphWidgetV2.getGlucoRxExpiredDisclaimer(userContext);
                                }
                                case "DEVICE_BATTERY_LOW" -> {
                                    return CGMGraphWidgetV2.getGlucoRxBatteryLowDisclaimer(userContext);
                                }
                                default -> {
                                    return CGMGraphWidgetV2.getGlucoRxNormalDisclaimer(userContext);
                                }
                            }
                        } else {
                            return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                        }
                    } catch (Exception e) {
                        exceptionReportingService.reportException(e);
                        return CGMGraphWidgetV2.getBluconConnectedDisclaimer(userContext);
                    }
                }
            }
        }

        return null;
    }

    private CGMGraphWidgetV2.CGMPurchaseInfo getCgmPurchaseInfo(CgmOnboardingStatusResponse cgmOnboardingStatusResponse, CgmStat cgmStat, UserContext userContext) throws IOException {
        CGMGraphWidgetV2.CGMPurchaseInfo cgmPurchaseInfo = new CGMGraphWidgetV2.CGMPurchaseInfo();
        boolean showCgmPurchaseCard = false;
        String metabolicScoreText = "";
        List<CGMDeviceInfo> deviceInfoList = cgmOnboardingStatusResponse.getCgmDeviceInfos();

        if (CollectionUtils.isNotEmpty(deviceInfoList)) {
            CGMDeviceInfo latestCgmDevice = deviceInfoList.get(deviceInfoList.size() - 1);
            if (latestCgmDevice.getCurrentState().equals("AGM_FINAL_READING_UPLOADED") || latestCgmDevice.getCurrentState().equals("AGM_ENDED")) {
                showCgmPurchaseCard = true;

                SfHomepageBannerConfig sfHomepageBannerConfig = null;
                try {
                    sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {}, new SfHomepageBannerConfig());
                } catch (Exception e) {
                    log.error("Error in fetching sales config", e);
                }
                if (sfHomepageBannerConfig != null && sfHomepageBannerConfig.getSaleBanners() != null && sfHomepageBannerConfig.getSaleBanners().getCgmSmall() != null) {
                    String bannerUrl = sfHomepageBannerConfig.getSaleBanners().getCgmSmall().getImageUrl();
                    if (bannerUrl != null && !bannerUrl.isEmpty()) {
                        cgmPurchaseInfo.setCgmBannerImage(bannerUrl);
                    }
                }
                if (Objects.nonNull(cgmStat) && cgmStat.getAverageMetabolicScore() >= MIN_METABOLIC_SCORE_FOR_CGM_INFO)
                    metabolicScoreText = "You have had great progress. Buy another CGM & get more insights.";
                else metabolicScoreText = "Unlock more insights by buying another CGM.";
            }
        }

        cgmPurchaseInfo.setMetabolicScoreText(metabolicScoreText);
        cgmPurchaseInfo.setShowCgmPurchaseCard(showCgmPurchaseCard);
        cgmPurchaseInfo.setBtnAction(Action.builder().title("BUY NEW CGM").url(ChronicCareServiceHelper.getCgmStorePageLink(userContext)).actionType(ActionType.NAVIGATION).build());
        return cgmPurchaseInfo;
    }

    private CGMGraphWidgetV2.BluconReturnModalData getBluconReturnModalData(UserContext userContext) {
        try {
            List<NotificationMeta> notificationMetaList = serviceInterfaces.inAppNotificationsService.getActiveUserAndAppId(userContext.getUserProfile().getUserId(),
                    "SUGARFIT");
            NotificationMeta notificationMeta = null;
            Optional<NotificationMeta> cgmEndedNotificationOpt = notificationMetaList.stream().filter(notification ->
                    !Objects.isNull(notification.getTaskId()) && notification.getTaskId().contains("CGM-END")).findFirst();
            Optional<NotificationMeta> cgmEndingNotificationOpt;
            if (cgmEndedNotificationOpt.isPresent()) {
                notificationMeta = cgmEndedNotificationOpt.get();
            } else {
                cgmEndingNotificationOpt = notificationMetaList.stream().filter(notification ->
                        !Objects.isNull(notification.getTaskId()) && notification.getTaskId().contains("BLUCON-END-REMINDER")).findFirst();
                if (cgmEndingNotificationOpt.isPresent()) {
                    notificationMeta = cgmEndingNotificationOpt.get();
                }
            }

            if (Objects.nonNull(notificationMeta)) {
                CGMGraphWidgetV2.BluconReturnModalData bluconReturnModalData = new CGMGraphWidgetV2.BluconReturnModalData();
                bluconReturnModalData.setShowModal(true);
                bluconReturnModalData.setTitle("IMPORTANT INFORMATION");
                bluconReturnModalData.setSubTitle(cgmEndedNotificationOpt.isPresent() ?
                        "Your CGM has ended.\nPlease make note of the following things" :
                        "Your CGM will end soon.\nPlease make note of the following things");

                Action viewedAction = new Action();
                viewedAction.setActionType(ActionType.REST_API);
                viewedAction.setTitle("I understand");
                ActionMeta closeActionMeta = new ActionMeta();
                closeActionMeta.setBody(new SfNoShowPenaltyBuilder.CloseActionState());
                closeActionMeta.setMethod("POST");
                closeActionMeta.setUrl("/user/inAppNotification/" + notificationMeta.getNotificationId());
                viewedAction.setMeta(closeActionMeta);
                bluconReturnModalData.setViewedAction(viewedAction);
                return bluconReturnModalData;
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public CGMActivationStatus getCGMActivationStatus(UserContext userContext, KSJourneyResponse kickStarterJourney, UserOnboardingActionWithContext onboardingActions,
                                                      CompletableFuture<List<LiveClass>> liveClassFuture,
                                                      CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                      CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture) {
        CGMActivationStatus cgmActivationStatus = new CGMActivationStatus();
        try {
            boolean isCGMWebinarBookingCompleted = false;
            List<ActiveCard> cgmActiveCards = null;
            if (liveClassFuture != null) {
               cgmActiveCards = chronicCareServiceHelper.getCGMWebinarLiveActiveCards(userContext, liveClassFuture);
            }
            if (CollectionUtils.isNotEmpty(cgmActiveCards)) {
                // condition is for webinar booked
                isCGMWebinarBookingCompleted = Objects.nonNull(cgmActiveCards.getFirst());
            } else if (Objects.nonNull(consultationBookingsFuture)) {
                // condition is for 1-1 consultation booked
                AtomicBoolean hasPhleboVideoCallBooking = new AtomicBoolean(false);
                if (consultationBookingsFuture.isDone() && CollectionUtils.isNotEmpty(consultationBookingsFuture.get())) {
                    consultationBookingsFuture.get().forEach(c -> {
                        if (Objects.nonNull(c.getProductCode()) && isPhleboConsultation(c.getProductCode())) {
                            hasPhleboVideoCallBooking.set(true);
                        }
                    });
                }
                isCGMWebinarBookingCompleted = hasPhleboVideoCallBooking.get();
            }

            boolean isTierOneUser = false;
            UserPreferencePojo userPreferencePojo = serviceInterfaces.getSfAlbusClient().getUserPreference(userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "cgmServiceAvailable");
            if (Objects.nonNull(userPreferencePojo) && CollectionUtils.isNotEmpty(userPreferencePojo.getPreferenceTypeValues())) {
                isTierOneUser = Boolean.parseBoolean(userPreferencePojo.getPreferenceTypeValues().get(0));
            }
            cgmActivationStatus.setTierOneUser(isTierOneUser);
            if (kickStarterJourney != null) {
                boolean allowTierOneActivation = resolveNullable(() -> kickStarterJourney.getCgmContext().getTierOneDeliveryAndInstallationContext().isAllowInstallation()).orElse(false);
                boolean allowTierTwoNFCActivation = resolveNullable(() -> kickStarterJourney.getCgmContext().getTierTwoNFCDeliveryAndSelfInstallationContext().isAllowInstallation()).orElse(false);
                boolean allowTierTwoNonNFCActivation = resolveNullable(() -> kickStarterJourney.getCgmContext().getTierTwoNonNFCDeliveryAndInstallationContext().isAllowInstallation()).orElse(false);
                boolean allowCGMActivation = allowTierOneActivation || allowTierTwoNFCActivation || allowTierTwoNonNFCActivation || isCGMWebinarBookingCompleted;

                boolean allowTierTwoNFCConsultationSlotBooking = resolveNullable(() -> kickStarterJourney.getCgmContext().getTierTwoNFCDeliveryAndSelfInstallationContext().isAllowSelfInstallationBooking()).orElse(false);
                boolean allowTierTwoNonNFCConsultationSlotBooking = resolveNullable(() -> kickStarterJourney.getCgmContext().getTierTwoNonNFCDeliveryAndInstallationContext().isAllowSelfInstallationBooking()).orElse(false);
                boolean allowConsultationSlotBooking = !isCGMWebinarBookingCompleted && (allowTierTwoNFCConsultationSlotBooking || allowTierTwoNonNFCConsultationSlotBooking);

                String cgmConsultationProductId = resolveNullable(() -> kickStarterJourney.getCgmContext().getTierTwoNFCDeliveryAndSelfInstallationContext().getProductCode()).orElse(null);
                boolean isFirstCoachConsultNotBooked = resolveNullable(() -> kickStarterJourney.getCoachContext().getIsBookingAllowed()).orElse(false);
                String coachConsultationProductId = resolveNullable(() -> kickStarterJourney.getCoachContext().getProductCode()).orElse(null);

                cgmActivationStatus.setNfcUser(chronicCareServiceHelper.getUserNfcStatus(userContext));
                cgmActivationStatus.setAllowCGMActivation(allowCGMActivation);
                cgmActivationStatus.setAllowCGMConsultationSlotBooking(allowConsultationSlotBooking);
                cgmActivationStatus.setCgmConsultationProductId(cgmConsultationProductId);
                cgmActivationStatus.setFirstCoachConsultNotBooked(isFirstCoachConsultNotBooked);
                cgmActivationStatus.setCoachConsultationProductId(coachConsultationProductId);
            } else {
                boolean allowCGMActivation = false;
                boolean allowConsultationSlotBooking = false;
                boolean hasGlucoRxCgm = cgmOnboardingStatus.getPendingDevices()
                        .stream()
                        .anyMatch(device -> device == DeviceModel.GLUCO_RX);
                if (onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse() != null
                        && onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions() != null) {
                    allowCGMActivation = onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse()
                            .getSelfInstallationActions().equals(SelfInstallationActions.ALLOW_SELF_ACTIVATION) || isCGMWebinarBookingCompleted;
                    allowConsultationSlotBooking = (onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse()
                            .getSelfInstallationActions().equals(SelfInstallationActions.ALLOW_BOOKING_FOR_CGM) || hasGlucoRxCgm) && !isCGMWebinarBookingCompleted;
                }
                String cgmConsultationProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
                boolean isFirstCoachConsultNotBooked = onboardingActions.getCoachCardActionWithContext().getAction().isActionPermitted();
                String coachConsultationProductId = onboardingActions.getCoachCardActionWithContext().getContext()
                        .getProductCodes().stream().findFirst().get();
                cgmActivationStatus.setNfcUser(chronicCareServiceHelper.getUserNfcStatus(userContext));
                cgmActivationStatus.setAllowCGMActivation(allowCGMActivation);
                cgmActivationStatus.setAllowCGMConsultationSlotBooking(allowConsultationSlotBooking);
                cgmActivationStatus.setCgmConsultationProductId(cgmConsultationProductId);
                cgmActivationStatus.setFirstCoachConsultNotBooked(isFirstCoachConsultNotBooked);
                cgmActivationStatus.setCoachConsultationProductId(coachConsultationProductId);
            }
        } catch (Exception e) {
            log.error("Error in CGMActivationStatus", e);
        }
        return cgmActivationStatus;
    }

    public CGMGraphWidgetAndConfig getCGMGraphWidgetAndCgmConfigurations(UserContext userContext, CGMActivationStatus cgmActivationStatus,
                                                                         CompletableFuture<CGMGraphWidgetV2> cgmGraphWidgetFuture,
                                                                         Boolean isNonCGMProduct, SfCgmDeviceStatus cgmDeviceStatus,
                                                                         CgmOnboardingStatusResponse cgmOnboardingStatus, Long patientId,
                                                                         ActivePackResponse activePackResponse, ChronicCareTeam assignedCareTeam,
                                                                         CgmUserRequestStatus cgmUserRequestStatus, Integer cgmConfigVersion) {
        try {
            CGMGraphWidgetV2 cgmGraphWidget = cgmGraphWidgetFuture.get();
            SfUserCGMConfig userCGMConfiguration = new SfUserCGMConfig();

            if (null != cgmGraphWidget) {
                boolean isOnBlucon = false;
                if (Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())) {
                    isOnBlucon = cgmOnboardingStatus.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon();
                    if (isOnBlucon) {
                        cgmGraphWidget.setBluconReturnModalData(getBluconReturnModalData(userContext));
                    }
                }

                if (cgmDeviceStatus.getShouldShowScanCardInsideCGMGraph()) {
                    ScanSensorCardWidget scanSensorCardWidget = buildSensorCardWidgetForCgmGraph(userContext, cgmActivationStatus, cgmDeviceStatus, cgmOnboardingStatus, cgmUserRequestStatus, patientId, activePackResponse);
                    cgmGraphWidget.setShowScanSensorCardWidget(true);
                    cgmGraphWidget.setSensorCardWidget(scanSensorCardWidget);
                    cgmGraphWidget.setNavToInsight(false);
                }
                userCGMConfiguration.setOnBlucon(isOnBlucon);
                userCGMConfiguration.setCgmReadingMode(Objects.nonNull(cgmGraphWidget.getCgmGraphData()) ? cgmGraphWidget.getCgmGraphData().getCurrentReadingMode(): ReadingMode.APP);
            } else {
                ReadingModeResponse readingModeResponse = getLatestReadingModeForUser(userContext);
                userCGMConfiguration.setCgmReadingMode(readingModeResponse == null ? null : readingModeResponse.getReadingMode());
            }
            userCGMConfiguration.setAvailableCgmDevices(cgmOnboardingStatus.getPendingDevices());
            if (cgmActivationStatus.isAllowCGMActivation()) {
                userCGMConfiguration.setSelfInstallationCardTitle("Ready to install your next CGM?");
             if(Objects.nonNull(cgmOnboardingStatus)){
                 List<DeviceModel> availableCgmDevices = cgmOnboardingStatus.getPendingDevices();
                 Action watchVideoAction = chronicCareServiceHelper.getCgmWatchVideoAction(availableCgmDevices);
                 userCGMConfiguration.setSelfInstallationWatchVideoAction(watchVideoAction);
             }
            } else if (cgmActivationStatus.isAllowCGMConsultationSlotBooking()) {
                userCGMConfiguration.setSelfInstallationCardTitle("Ready to install your next CGM?");
                try {
                    String phleboConsultProductId = cgmActivationStatus.getCgmConsultationProductId();
                    userCGMConfiguration.setSelfInstallationWatchVideoAction(chronicCareServiceHelper.getBookCGMInstallationAction(userContext, phleboConsultProductId));
                } catch (Exception e) {
                    exceptionReportingService.reportException(e);
                }

            } else if (!cgmDeviceStatus.getIsOnGoingDevice() && chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct())) {
                userCGMConfiguration.setSelfInstallationCardTitle("Do you have a CGM and want to start using it?");
                Action cgmAddAction = Action.builder().actionType(ActionType.SF_ADD_A_CGM_TO_ACCOUNT).title("Yes, Let's do now").build();
                userCGMConfiguration.setSelfInstallationWatchVideoAction(cgmAddAction);
            }

            if (activePackResponse != null && cgmGraphWidget != null) {
                boolean isFirstCoachConsultNotBooked = cgmActivationStatus.isFirstCoachConsultNotBooked();
                if (isFirstCoachConsultNotBooked) {
                    cgmGraphWidget.setShowCGMInsightsLocked(true);
                    cgmGraphWidget.setCgmInsightsTitle("Book coach consult to unlock");
                    if (assignedCareTeam.getCoach() != null && assignedCareTeam.getCoach().getAgentResponse() != null && assignedCareTeam.getCoach().getAgentResponse().getId() != null) {
                        try {
                            String consultationProduct = cgmActivationStatus.getCoachConsultationProductId();
                            Long centerId = assignedCareTeam.getCoach().getAgentResponse().getAgentCenterMapping().stream().findFirst().get()
                                    .getCenterId();
                            cgmGraphWidget.setCoachId(String.valueOf(assignedCareTeam.getCoach().getAgentResponse().getId()));
                            Action action = Action.builder()
                                    .isEnabled(true)
                                    .title("Book Coach Consultation")
                                    .actionType(ActionType.NAVIGATION)
                                    .url(getAgentDatePickerUrl(consultationProduct, centerId, assignedCareTeam.getCoach().getAgentResponse().getId(), activePackResponse))
                                    .build();
                            cgmGraphWidget.setCgmInsightsPrimaryAction(action);
                        } catch (Exception exec) {
                            String message = "CGM Insights Locked, Exception: ";
                            log.error(message + exec.getMessage());
                            exceptionReportingService.reportException(message, exec);
                        }
                    }
                } else {
                    cgmGraphWidget.setShowCGMInsightsLocked(false);
                }
            }

            userCGMConfiguration.setNewSensorActivationAllowed(cgmOnboardingStatus.isNewSensorActivationAllowed());
            userCGMConfiguration.setEnableActivation(cgmOnboardingStatus.isEnableActivation());
            userCGMConfiguration.setDeviceModelEnableActivation(cgmOnboardingStatus.getDeviceModelEnableActivation());
            userCGMConfiguration.setDeviceModelNewSensorActivationAllowed(cgmOnboardingStatus.getDeviceModelNewSensorActivationAllowed());
            userCGMConfiguration.setCgmDeviceModel(cgmDeviceStatus.getCgmDeviceModel());
            userCGMConfiguration.setLatestCgmDeviceInfo(cgmDeviceStatus.getLatestCgmDeviceInfo());
            List<CGMDeviceInfo> cgmDeviceInfos = cgmOnboardingStatus.getCgmDeviceInfos();
            if (CollectionUtils.isNotEmpty(cgmDeviceInfos)) {
                cgmDeviceInfos.forEach(cgmDeviceInfo -> {
                    if (cgmDeviceInfo.getDeviceId().equals(cgmDeviceStatus.getCgmDeviceId())) {
                        if (cgmDeviceInfo.isOngoing()) {
                            userCGMConfiguration.setLastScannedHexIndex(cgmDeviceInfo.getLastScannedHexIndex());
                            userCGMConfiguration.setLastCgmSyncTime(Objects.nonNull(cgmDeviceInfo.getLastMetricDate()) ? cgmDeviceInfo.getLastMetricDate().getTime(): null);
                            userCGMConfiguration.setCgmDeviceHardwareId(getModifiedHardwareIdToSendToApp(userContext, cgmDeviceInfo.getHardwareId(), cgmDeviceInfo.getDeviceModel()));
                        }
                    }
                });
            }

            boolean appScanningEnabled = cgmOnboardingStatus.isEnableInAppScanning() && isInAppScanningEnabled(userContext) && cgmDeviceStatus.getIsOnGoingDevice();
            DeviceDetailEntry device = null;
            NfcLocationConfigEntry nfcLocationConfigEntry = null;
            userCGMConfiguration.setShowScanButton(appScanningEnabled);
            if (appScanningEnabled || cgmActivationAllowed(userContext, cgmOnboardingStatus)) {
                try {
                    device = deviceService.getDeviceByDeviceId(userContext.getSessionInfo().getDeviceId(),
                            AppUtil.getAppTenantFromUserContext(userContext)).get();
                    nfcLocationConfigEntry = chsClient.fetchNfcLocation(device.getOsName(), device.getBrand(), device.getDeviceModel());
                } catch (Exception e) {
                    exceptionReportingService.reportException(e);
                }
                if (!Objects.isNull(nfcLocationConfigEntry)) {
                    userCGMConfiguration.setNfcLocationVideoUrl(nfcLocationConfigEntry.getLocationUri());
                }
                userCGMConfiguration.setDeviceNFCLocationInfo(getDeviceNFCLocationInfo(userContext, device));
            }

            if (appScanningEnabled && cgmOnboardingStatus.isShowCgmData() && cgmOnboardingStatus.isShowScanReadingPrompt() && (DeviceModel.ABBOTT_LIBRE_PRO.equals(cgmDeviceStatus.getCgmDeviceModel()) || DeviceModel.ABBOTT_LIBRE.equals(cgmDeviceStatus.getCgmDeviceModel()))) {
                String redisKey = "CGM_INAPP_READING_PROMPT_" + userContext.getUserProfile().getUserId();
                String redisValue = this.cfApiRedisKeyValueStore.get(redisKey);
                if (redisValue == null || redisValue.isEmpty()) {
                    SfUserCGMConfig.ReadingAlert readingAlert = new SfUserCGMConfig.ReadingAlert();
                    readingAlert.setShowScanReadingPrompt(true);
                    userCGMConfiguration.setReadingAlert(readingAlert);
                    this.cfApiRedisKeyValueStore.set(redisKey, "TRUE", SIX_HOURS_IN_SECONDS);
                }
            }

            boolean status = false;
            SfUserCGMConfig.DisclaimerAlert disclaimerAlert = new SfUserCGMConfig.DisclaimerAlert();
            if (cgmGraphWidget != null) {
                if (!isNonCGMProduct && !isUltraFitApp(userContext)) {
                    status = !(Optional.ofNullable(cgmGraphWidget.getCgmGraphData().getDisclaimerAccepted()).orElse(true));
                }
            }
            disclaimerAlert.setShowDisclaimerAlertPrompt(status);
            userCGMConfiguration.setDisclaimerAlert(disclaimerAlert);

            if (cgmConfigVersion == null || cgmConfigVersion < userCGMConfiguration.getVersion()) {
                userCGMConfiguration.setCgmReaderModalConfig(new SfUserCGMConfig.CgmReaderModalConfig(userContext));
                userCGMConfiguration.setGenericNFCLocationInfo(new SfUserCGMConfig.GenericNFCLocationInfo());
                userCGMConfiguration.setVoiceCommandEnabled(true);
                userCGMConfiguration.setScanModeText("It's time to scan your CGM sensor to get real time glucose readings");
                userCGMConfiguration.setScanButtonText("SCAN SENSOR");
                userCGMConfiguration.setActivationModeText("Once installed, scan your sensor to activate it.");
                userCGMConfiguration.setActivationButtonText("ACTIVATE SENSOR");
                userCGMConfiguration.setInitAppModeInNFCDeviceText("Your sensor has been activated. You can scan your sensor soon");
                userCGMConfiguration.setInitAppModeInNonNFCDeviceText("Once your sensor has been installed, you should use NFC enabled device to take reading.");
                userCGMConfiguration.setInitPhleboModeText("Phlebo will do the reading for you");
                userCGMConfiguration.setReadingTimeout(40000);
                userCGMConfiguration.setScanRetryCount(5);
            }

            return new CGMGraphWidgetAndConfig(cgmGraphWidget, userCGMConfiguration);
        } catch (Exception e) {
            log.error("Error in getting cgm graph widget and config", e);
            exceptionReportingService.reportException("Error in getting cgm graph widget and config", e);
            return null;
        }
    }

    private ScanSensorCardWidget buildSensorCardWidgetForCgmGraph(UserContext userContext,
                                                                  CGMActivationStatus cgmActivationStatus,
                                                                  SfCgmDeviceStatus cgmDeviceStatus,
                                                                  CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                                  CgmUserRequestStatus cgmUserRequestStatus,
                                                                  Long patientId, ActivePackResponse activePackResponse) {
        boolean hasGlucoRxCgm = cgmOnboardingStatus.getPendingDevices()
                .stream()
                .anyMatch(device -> device == DeviceModel.GLUCO_RX);
        boolean isCurrentCgmGlucoRx = DeviceModel.GLUCO_RX.equals(cgmDeviceStatus.getCgmDeviceModel());
        ScanSensorCardWidget scanSensorCardWidget = new ScanSensorCardWidget();
        if (cgmActivationAllowed(userContext, cgmOnboardingStatus)) {
            scanSensorCardWidget.setOpsRequestAllowed(false);
            scanSensorCardWidget.setCardDescription("Once installed, scan your sensor to activate it.");
            scanSensorCardWidget.setCgmActionButtonTitle("ACTIVATE SENSOR");
        } else {
            scanSensorCardWidget.setCardDescription("It's time to scan your CGM sensor to get real time glucose readings");
            scanSensorCardWidget.setCgmActionButtonTitle("SCAN SENSOR");
        }

        if (cgmActivationStatus.isAllowCGMActivation() || cgmActivationStatus.isAllowCGMConsultationSlotBooking()) {
            List<Action> actions = new ArrayList<>();
            if (cgmActivationStatus.isAllowCGMActivation()) {
               if(Objects.nonNull(cgmOnboardingStatus)){
                   Map<String, Object> meta = new HashMap<>();
                   meta.put("primary", true);
                   List<DeviceModel> availableCgmDevices = cgmOnboardingStatus.getPendingDevices();
                   Action watchVideoAction = chronicCareServiceHelper.getCgmWatchVideoAction(availableCgmDevices);
                   Map<String, Object> givenMeta = (Map<String, Object>) watchVideoAction.getMeta();
                   if (givenMeta == null) {
                       givenMeta = new HashMap<>();
                   }
                   givenMeta.putAll(meta);
                   watchVideoAction.setMeta(givenMeta);
                   actions.add(watchVideoAction);
               }
            }
            if (cgmActivationStatus.isAllowCGMConsultationSlotBooking()) {
                Map<String, Object> meta = new HashMap<>();
                meta.put("primary", true);
                try {
                    String phleboConsultProductId = cgmActivationStatus.getCgmConsultationProductId();
                    Action bookSlotAction = chronicCareServiceHelper.getBookCGMInstallationByPhleboAction(patientId, "BOOK INSTALLATION SLOT", phleboConsultProductId);
                    bookSlotAction.setMeta(meta);

                    Action bookInstallationWebinarAction = chronicCareServiceHelper.getBookCGMInstallationWebinarAction("BOOK INSTALLATION SLOT");
                    bookInstallationWebinarAction.setMeta(meta);

                    if (isCgmInstallationWebinarSupportedApp(userContext)) {
//                        if (!cgmActivationStatus.isTierOneUser() || chronicCareServiceHelper.isQuickCommerceOrShopifyPackUser(userContext)) {
//                            actions.add(bookSlotAction);
//                        } else {
//                            actions.add(bookInstallationWebinarAction);
//                        }
                        actions.add(bookInstallationWebinarAction);
                        if (!cgmActivationStatus.isNfcUser() && !cgmActivationStatus.isTierOneUser()) {
                            if (hasGlucoRxCgm) {
                                Map<String, Object> cgmActionMeta = new HashMap<>();
                                cgmActionMeta.put("cgms", cgmOnboardingStatus.getPendingDevices());
                                Action activateMarkingActionForTier2NonNfc = Action.builder().actionType(ActionType.SHOW_CGM_TYPE_SELECTION_MODAL).title("ACTIVATE NOW").meta(cgmActionMeta).build();
                                actions.add(activateMarkingActionForTier2NonNfc);
                            } else if (ChronicCareAppUtil.isUserExperiencePartTwoReleased(userContext)) {
                                // Show Libre Reader manual activation for Non-NFC Tier2 user
                                Action activateMarkingActionForTier2NonNfc = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfcgmreaderinstallpage").title("ACTIVATE NOW").build();
                                actions.add(activateMarkingActionForTier2NonNfc);
                            }
                        }
                    } else {
                        actions.add(bookSlotAction);
                    }
                } catch (Exception e) {
                   exceptionReportingService.reportException(e);
                }
            } else if (hasGlucoRxCgm && !cgmActivationStatus.isNfcUser() && !cgmActivationStatus.isTierOneUser()) {
                // For GlucoRx CGM, if user is not NFC user and not Tier1 user, show action to activate CGM
                Map<String, Object> cgmActionMeta = new HashMap<>();
                cgmActionMeta.put("cgms", cgmOnboardingStatus.getPendingDevices());
                Action activateMarkingActionForTier2NonNfc = Action.builder().actionType(ActionType.SHOW_CGM_TYPE_SELECTION_MODAL).title("ACTIVATE NOW").meta(cgmActionMeta).build();
                actions.add(activateMarkingActionForTier2NonNfc);
            }

            if (cgmActivationStatus.isNfcUser() || (hasGlucoRxCgm && !(!cgmActivationStatus.isNfcUser() && !cgmActivationStatus.isTierOneUser()))) {
                Map<String, Object> cgmActionMeta = new HashMap<>();
                cgmActionMeta.put("isCgmAction", true);
                actions.add(Action.builder().title(scanSensorCardWidget.getCgmActionButtonTitle()).meta(cgmActionMeta).build());
//                if (isCurrentCgmGlucoRx && !cgmActivationAllowed(userContext, cgmOnboardingStatus)){
//                    return null;
//                }
            }
            scanSensorCardWidget.setActions(actions);
        } else if (!cgmDeviceStatus.getIsOnGoingDevice() && chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct())) {
            scanSensorCardWidget.setCardDescription("Do you have a CGM and want to start using it?");
            Map<String, Object> meta = new HashMap<>();
            meta.put("primary", true);
            Action cgmAddAction = Action.builder().actionType(ActionType.SF_ADD_A_CGM_TO_ACCOUNT).title("Yes, Let's do now").meta(meta).build();
            scanSensorCardWidget.setActions(List.of(cgmAddAction));
        }
        return scanSensorCardWidget;
    }

    private ReadingModeResponse getLatestReadingModeForUser(UserContext userContext) {
        try {
            return chsClient.fetchCgmReadingMode(Long.valueOf(userContext.getUserProfile().getUserId()), AppUtil.getAppTenantFromUserContext(userContext));
        } catch (Exception e) {
            String message = String.format("Error in fetching latest reading mode for user :: %s", e.getMessage());
            log.error(message, e);
            return null;
        }
    }

    private SfUserCGMConfig.DeviceNFCLocationInfo getDeviceNFCLocationInfo(UserContext userContext, DeviceDetailEntry device) {
        SfUserCGMConfig.DeviceNFCLocationInfo deviceNFCLocationInfo = new SfUserCGMConfig.DeviceNFCLocationInfo();
        String userId = userContext.getUserProfile().getUserId();
        String deviceId = userContext.getSessionInfo().getDeviceId();
        try {
            if (StringUtils.isEmpty(deviceId)) {
                return deviceNFCLocationInfo;
            }
            if (null == device) {
                log.error("Device info is null for userId: {}", userId);
                return deviceNFCLocationInfo;
            }
            // API throws 404
//            List<DeviceNfcLocationInfo> infos = serviceInterfaces.getSfAlbusClient().getDeviceNFCLocationInfo(device.getBrand(), device.getDeviceModel());
//            if (!CollectionUtils.isEmpty(infos)) {
//                deviceNFCLocationInfo.setImage(infos.get(0).getS3Url());
//            }
            deviceNFCLocationInfo.setImage("");
        } catch (Exception e) {
            log.error("Error while fetching deviceNFCInfo for userId {}: {}", userId, e.getMessage());
        }
        return deviceNFCLocationInfo;
    }

    private Long getDoctorCenterIdForPaidUser(PatientPreferredAgentResponse doctor) throws OllivanderClientException {
        if (doctor.getAgentResponse().getAgentCenterMapping().size() > 1) {
            List<CenterResponseV2> experienceCenters = this.serviceInterfaces.ollivanderCenterClient.getSugarfitExperienceCenters();
            Set<Long> experienceCenterIds;
            if (CollectionUtils.isNotEmpty(experienceCenters)) {
                experienceCenterIds = experienceCenters.stream().map(CenterBaseResponse::getId).collect(Collectors.toSet());
            } else {
                experienceCenterIds = new HashSet<>();
            }
            return doctor.getAgentResponse().getAgentCenterMapping().stream()
                    .filter(center -> !(experienceCenterIds.contains(center.getCenterId()) || DIGITAL_AGENT_CENTER_ID.equals(center.getCenterId())))
                    .findFirst().get().getCenterId();
        } else {
            return doctor.getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
        }
    }

    private CompletableFuture<SfLiveSessionWidget> getLiveSessionWidgetFuture(UserContext userContext, CompletableFuture<List<LiveClass>> liveClassesFuture, boolean afterPackExpiry) {
        return supplyAsync(() -> sfLiveSessionWidgetBuilder.buildView(userContext, liveClassesFuture, afterPackExpiry), serviceInterfaces.getTaskExecutor());
    }

    private CompletableFuture<BaseWidgetNonVM> getDiabetesStoreWidgetFuture(UserContext userContext, ActivePackResponse packResponse) {
        return supplyAsync(() -> getDiabetesStoreWidget(userContext, packResponse), serviceInterfaces.getTaskExecutor());
    }

    private BaseWidgetNonVM getDiabetesStoreWidget(UserContext userContext, ActivePackResponse packResponse) {
        if (ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
            return null;
        }

        SfDiabetesStoreHomeWidget widget = new SfDiabetesStoreHomeWidget();
        MegaSaleData megaSaleData = null;
        try {
            CompletableFuture<MegaSaleData> megaSalesBannerDataFuture = chronicCareServiceHelper.getMegaSalesDataFuture(userContext, null, null, null, true);
            megaSaleData = megaSalesBannerDataFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            // Ignore
        }
        if (ChronicCareAppUtil.isTwentyTwentyFiveSaleLive(userContext, megaSaleData)) {
            widget = new SfDiabetesStoreHomeDarkWidget();
        }
        try {
            ProductSearchRequest productSearchRequest = new ProductSearchRequest();
            productSearchRequest.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            productSearchRequest.setSearchTags(List.of("HOMEPAGE_PRODUCT"));
            List<ProductResponse> products = indusClient.filterProductsResponses(productSearchRequest);
            if (CollectionUtils.isNotEmpty(products)) {
                widget.setHeader(Header.builder().title("Your ")
                        .boldTitle(chronicCareServiceHelper.isPCOSPack(packResponse.getProductCode()) ? "Store" : "Diabetes Store")
                        .seemore(new Action(ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext) ? "curefit://sfstorelifestylepage" : "curefit://sfecommerceplp", ActionType.NAVIGATION)).build());
                products = chronicCareServiceHelper.filterProductsBasedOnUser(userContext, products, false);

                List<SfEComProductCardWidget> resultProducts = new LinkedList<>();
                Map<String, LinkedList<ProductResponse>> productsBySubcategory = new LinkedHashMap<>();
                products.forEach(productResponse -> {
                    if (!productsBySubcategory.containsKey(productResponse.getProductEntry().getSubCategory())) {
                        productsBySubcategory.put(productResponse.getProductEntry().getSubCategory(), new LinkedList<>());
                    }
                    productsBySubcategory.get(productResponse.getProductEntry().getSubCategory()).add(productResponse);
                });

                productsBySubcategory.forEach((k, v) -> {
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(v)) {
                        resultProducts.add(buildEcomProductCard(userContext, v.get(0)));
                    }
                });

                if (!resultProducts.isEmpty()) {
                    widget.setProductWidgets(resultProducts.stream().sorted(Comparator.comparingInt(SfEComProductCardWidget::getSortOrder)).toList());
                    return widget;
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    private SfEComProductCardWidget buildEcomProductCard(UserContext userContext, ProductResponse productResponse) {
        try {
            List<ProductResponse> products = indusClient.getVariants(productResponse.getProductEntry().getProductCode());
            List<ProductResponse> sortedProducts = chronicCareServiceHelper.sortEcomProductsByOffer(products, userContext);
            Optional<ProductResponse> baseProductOpt = products.stream().filter(p -> Boolean.TRUE.equals(p.getProductEntry().getIsBaseVariant())).findFirst();
            ProductResponse baseProduct = baseProductOpt.orElse(null);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(sortedProducts)) {
                SfAppScratchCardConfig scratchCardConfig = chronicCareServiceHelper.getScratchCardConfig();
                SfECommerceProduct parentProduct = ChronicCareAppUtil.convertProductResponseToProduct(sortedProducts.get(0), baseProduct, null, this.serviceInterfaces, scratchCardConfig);
                List<SfECommerceProduct> appFacingProducts = new ArrayList<>();
                sortedProducts.forEach(pd -> {
                    SfECommerceProduct appFacingProduct = ChronicCareAppUtil.convertProductResponseToProduct(pd, baseProduct, null, this.serviceInterfaces, scratchCardConfig);
                    appFacingProducts.add(appFacingProduct);
                });
                if (Objects.nonNull(parentProduct)) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    String cloneString = null;
                    SfECommerceProduct parentProductClone = null;
                    try {
                        cloneString = objectMapper.writeValueAsString(parentProduct);
                        parentProductClone = objectMapper.readValue(cloneString, SfECommerceProduct.class);
                        if (baseProduct != null && baseProduct.getProductEntry() != null) {
                            parentProductClone.setTitle(baseProduct.getProductEntry().getTitle());
                        }
                        parentProductClone.addProductVariants(appFacingProducts);
                        SfEComProductCardWidget productCardWidget = new SfEComProductCardWidget();
                        productCardWidget.setProduct(parentProductClone);
                        productCardWidget.setNavAction(Action.builder().actionType(NAVIGATION)
                                .url("curefit://sfecommercepdp?productId=" + parentProductClone.getProductCode()).build());
                        return productCardWidget;
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        } catch (HttpException e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    CompletableFuture<BaseWidgetNonVM> getBlogsWidgetFuture(UserContext userContext, String sessionId, ActivePackResponse packResponse) {
        return supplyAsync(() -> {
            boolean isPCOSPackUser = Objects.nonNull(packResponse) && chronicCareServiceHelper.isPCOSPack(packResponse.getProductCode());
            if (!ChronicCareAppUtil.isAppStoreUserId(userContext) && !isPCOSPackUser) {
                return sugarFitBlogWidgetBuilder.buildSfBlogsHomeWidget(userContext, sessionId);
            } else {
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    CompletableFuture<BaseWidgetNonVM> getHealthCheckupWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            if (isSfDaignosticStoreTestSupportedUser(userContext, serviceInterfaces)) {
                return buildDoctorRecommendedTestWidget(userContext);
            } else {
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private BaseWidgetNonVM buildDoctorRecommendedTestWidget(UserContext userContext) {
        try {
            SfDoctorRecommendedTestWidget widget = new SfDoctorRecommendedTestWidget();
            String userId = userContext.getUserProfile().getUserId();
            Optional<ActivePackResponse> expiredPackResponse = userOnboardingService.getSugarFitExpiredPack(userContext.getUserProfile().getUserId());
            // expired pack should not be a trial pack.
            if (expiredPackResponse.isPresent() && (!expiredPackResponse.get().getBundleProduct().getIsTrialProduct())) {
                return chronicCareServiceHelper.buildDiagnosticsWidgetForPack(widget,userId, (long) -1,true, "Time for your Health Check!");
            }
        } catch (Exception e) {
            log.error("Error while building doctor recommended test widget", e);
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    private SfQuoteWidget getQuoteWidget(UserContext userContext) {
        try {
            SfQuoteWidget quoteWidget = new SfQuoteWidget();
            List<String> quotes = appConfigCache.getConfig("SF_HOMEPAGE_QUOTES", new TypeReference<>() {
            }, new ArrayList<>());

            if (CollectionUtils.isEmpty(quotes)) {
                return null;
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
            Date currentTime = Calendar.getInstance(getUserTimezone(userContext)).getTime();
            String redisKey = "SF_QUOTE" + dateFormat.format(currentTime);
            String redisValue = this.cfApiRedisKeyValueStore.get(redisKey);
            if (redisValue == null || redisValue.isEmpty()) {
                int randomQuoteIndex = (int) (Math.random() * (quotes.size() + 1));
                String quote = quotes.get(randomQuoteIndex);
                quoteWidget.setQuote(quote);
                this.cfApiRedisKeyValueStore.set(redisKey, quote, 60 * 60 * 24); // cache quote for one day
            } else {
                quoteWidget.setQuote(redisValue);
            }

            return quoteWidget;
        } catch (Exception e) {
            return null;
        }
    }

    private SfCgmDeviceStatus getExpiredPackCgmDeviceStatus(CgmOnboardingStatusResponse cgmOnboardingStatus, String cgmDeviceId) {
        SfCgmDeviceStatus deviceStatus = new SfCgmDeviceStatus(cgmDeviceId, false, false);
        List<CGMDeviceInfo> deviceInfos = cgmOnboardingStatus.getCgmDeviceInfos();
        if (cgmDeviceId == null || cgmDeviceId.isEmpty() || cgmDeviceId.equals(CGM_ACTIVATION_DUMMY_DEVICE_ID)) {
            cgmDeviceId = null;
            if (CollectionUtils.isNotEmpty(deviceInfos)) {
                deviceInfos = deviceInfos.stream().filter(cgmDeviceInfo -> {
                    if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) || "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) || null == cgmDeviceInfo.getStartedOn()) {
                        return false;
                    }
                    return cgmDeviceInfo.isAtleastOneReadingDone() || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState());
                }).toList();

                if (deviceInfos.size() > 0) {
                    cgmDeviceId = deviceInfos.get(deviceInfos.size() - 1).getDeviceId();
                }
            }
        }
        deviceStatus.setCgmDeviceId(cgmDeviceId);
        return deviceStatus;
    }

    private CompletableFuture<List<BaseWidgetNonVM>> getMentalHealthPollWidgetsFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getMentalHealthPollWidgets(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private List<BaseWidgetNonVM> getMentalHealthPollWidgets(UserContext userContext) throws Exception {
        if (ChronicCareAppUtil.isPollsEnabled(userContext)) {
            SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(Set.of()));
            CompletableFuture<List<PollEntry>> activePollsFuture = sfHomePageViewBuilderV1.getActivePollsFuture(userContext, userSegments);
            CompletableFuture<List<PollEntry>> participatedPollsFuture = sfHomePageViewBuilderV1.getUserParticipatedPollsFuture(userContext);
            List<PollEntry> polls = new ArrayList<>();
            if (activePollsFuture.get() != null) polls.addAll(activePollsFuture.get());
            if (participatedPollsFuture.get() != null) polls.addAll(participatedPollsFuture.get());
            if (CollectionUtils.isNotEmpty(polls)) {
                List<CompletableFuture<BaseWidgetNonVM>> pollWidgetFutures = polls.stream().map(poll -> supplyAsync(() -> {
                    try {
                        if(poll.getPollType() == PollType.DDS) {
                            if(isMentalHealthEnabled(userContext)) {
                                return ChronicCareServiceHelper.buildMentalHealthPollWidget(userContext, poll);
                            } else {
                                return null;
                            }
                        } else {
                            return null;
                        }
                    } catch (BaseException e) {
                        e.printStackTrace();
                        return null;
                    }
                }, serviceInterfaces.getTaskExecutor())).toList();
                List<BaseWidgetNonVM> pollWidgets = pollWidgetFutures.stream().map(pollWidgetFuture -> {
                    try {
                        return pollWidgetFuture.get();
                    } catch (Exception e) {
                        return null;
                    }
                }).filter(Objects::nonNull).toList();
                if (CollectionUtils.isNotEmpty(pollWidgets)) {
                    return pollWidgets;
                }
            }
        }
        return null;
    }

    private BaseWidgetNonVM getSugarControlSessionWidget() {
        SfSugarControlLiveSessionWidget sugarControlLiveSessionWidget = new SfSugarControlLiveSessionWidget();
        sugarControlLiveSessionWidget.setHeader(Header.builder().title("Today's ").boldTitle("Expert Sessions").build());
        sugarControlLiveSessionWidget.setSessionLive(ChronicCareAppUtil.isSugarControl7DSessionRunning());
        sugarControlLiveSessionWidget.setCardTitle("Ask the Expert");
        sugarControlLiveSessionWidget.setCardSubTitle("Q&A on Diet & Diabetes");
        sugarControlLiveSessionWidget.setAction(Action.builder().title(ChronicCareAppUtil.isSugarControl7DSessionRunning()
                        ? "Join Session" : "Starts at 12:00pm").actionType(ActionType.EXTERNAL_DEEP_LINK)
                .url("https://zoom.us/j/99187075417?pwd=zmCiKUFiiSbjOM2BT5CwNOnPlJIpof.1")
                .isEnabled(ChronicCareAppUtil.isSugarControl7DSessionRunning()).build());
        sugarControlLiveSessionWidget.setCoachImage("http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/coachImage-2025-03-07-13:08.png");
        return sugarControlLiveSessionWidget;
    }

    private BaseWidgetNonVM getSugarControlDietPlanWidget(UserContext userContext) {
        String dietPlanPdfUrl = chronicCareServiceHelper.getUserDietPlanPdfUrl(userContext);
        SfSugarControlDietPlanWidget sfSugarControlDietPlanWidget = new SfSugarControlDietPlanWidget();
        sfSugarControlDietPlanWidget.setHeader(Header.builder().title("Your ").boldTitle("Diet Plan").build());
        sfSugarControlDietPlanWidget.setDishImage(StringUtils.isEmpty(dietPlanPdfUrl) ? "http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/food-image-2-2025-03-07-13:25.png" : "http://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/food-image-1-2025-03-07-13:25.png");
        sfSugarControlDietPlanWidget.setCardTitle(StringUtils.isEmpty(dietPlanPdfUrl) ? "Get your curated Diet plan" : "Your Personalised Diet Plan");
        sfSugarControlDietPlanWidget.setPdfUrl(dietPlanPdfUrl);
        if (StringUtils.isEmpty(dietPlanPdfUrl)) {
            sfSugarControlDietPlanWidget.setAction(Action.builder().title("Genereate Now").actionType(ActionType.NAVIGATION).url("curefit://sfdietplanpreferencespage").build());
        }
        return sfSugarControlDietPlanWidget;
    }

}