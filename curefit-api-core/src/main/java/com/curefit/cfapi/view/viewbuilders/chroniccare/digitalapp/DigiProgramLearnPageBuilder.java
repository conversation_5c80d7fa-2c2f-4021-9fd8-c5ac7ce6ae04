package com.curefit.cfapi.view.viewbuilders.chroniccare.digitalapp;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfGenericPageView;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.GenericBannerWidget;
import com.curefit.cfapi.widgets.chroniccare.support.SfSupportFAQsWidget;
import com.curefit.configstore.sdk.AppConfigCache;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
@Component
@RequiredArgsConstructor
public class DigiProgramLearnPageBuilder {
    final ExceptionReportingService exceptionReportingService;
    public SfGenericPageView buildView(UserContext userContext) {
        SfGenericPageView page = new SfGenericPageView();

        try {
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/1-2025-07-31-18:04.png"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/2-2025-07-31-18:05.png"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/3-2025-07-31-18:05.png"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/4-2025-07-31-18:05.png"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/5-2025-07-31-18:05.png"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/6-2025-07-31-18:05.png"));
            page.addWidget(new GenericBannerWidget("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/7-2025-07-31-18:06.png"));

            page.setPageTitle("");
            return page;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            return null;
        }
    }
}
