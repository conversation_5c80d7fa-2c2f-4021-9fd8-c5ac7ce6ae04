package com.curefit.cfapi.controller.cultstore;

import com.curefit.aakashvani.client.AakashVaniClient;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping(value = "limechat")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class LimechatWebhookController {
    AakashVaniClient aakashVaniClient;
    RollbarService rollbarService;

    @Async
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 12000, multiplier = 5))
    @PostMapping(value = {"/webhook"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> receiveLimechatWebhook(@RequestBody String rawPayload) throws Exception {
        try {
            aakashVaniClient.receiveLimechatWebhook(rawPayload);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            log.error("Error processing Limechat webhook: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Recover
    public ResponseEntity<Void> recoverFromAllExceptions(Exception e, String rawPayload) {
        log.error("Error processing Limechat webhook after all retries: {}", e.getMessage(), e);
        rollbarService.error(e, "Error processing Limechat webhook after all retries");
        return new ResponseEntity<>(HttpStatus.OK);
    }
}

