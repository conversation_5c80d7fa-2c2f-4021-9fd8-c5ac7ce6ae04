package com.curefit.cfapi.controller;

import com.curefit.cfapi.constants.FitnessPlannerConstants;
import com.curefit.cfapi.service.GuidedWorkoutsService;
import com.curefit.cfapi.view.viewmodels.common.GenericSuccessBody;
import com.curefit.cfapi.view.viewmodels.digital.LevelCompletionPageView;
import com.curefit.cfapi.view.viewmodels.digital.LevelDetailPageView;
import com.curefit.cfapi.view.viewmodels.fitnessplanner.*;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.fitnessplanner.FitnessPlannerService;
import com.curefit.cfapi.view.viewmodels.fitnessplanner.planinfo.FitnessPlanInfo;
import com.curefit.cfapi.view.viewmodels.guidedWorkoutsPlayback.GuidedAlternativeExerciseView;
import com.curefit.cfapi.view.viewmodels.guidedWorkoutsPlayback.GuidedExerciseView;
import com.curefit.cfapi.widgets.fitnessplanner.LogEditEntryWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.mediagateway.client.spi.MediaGatewayClient;
import com.curefit.ufs.assessment.pojos.AssessmentReport;
import com.curefit.ufs.pojo.request.EditPlanUsingEditFlowRequest;
import com.curefit.ufs.pojo.request.ModifyUserWodRequest;
import com.curefit.ufs.pojo.response.ExerciseSearchResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@RestController
@Slf4j
@RequestMapping(value = "/fitnessplanner")
public class FitnessPlannerController {

    private MediaGatewayClient mediaGatewayClient;
    private HttpServletRequest request;
    private HttpServletResponse response;
    private FitnessPlannerService fitnessPlannerService;
    private GuidedWorkoutsService guidedWorkoutsService;

    @Autowired
    public FitnessPlannerController(
            HttpServletRequest request,
            HttpServletResponse response,
            FitnessPlannerService fitnessPlannerService,
            GuidedWorkoutsService guidedWorkoutsService,
            MediaGatewayClient mediaGatewayClient
    ) {
        this.request = request;
        this.response = response;
        this.fitnessPlannerService = fitnessPlannerService;
        this.guidedWorkoutsService = guidedWorkoutsService;
        this.mediaGatewayClient = mediaGatewayClient;
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/planStatus"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody getPlanCreationStatus(@RequestParam String token) throws BaseException {
        logRequest("getPlanCreationStatus requested");
        return this.fitnessPlannerService.getPlanCreationStatus(token);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/checkUserPlanStatus"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody getCheckPlanCreationStatusUser() throws BaseException {
        logRequest("getCheckPlanCreationStatus requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getCheckPlanCreationStatusUser(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/checkPlanStatus/{formId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody getCheckPlanCreationStatus(@PathVariable String formId) throws BaseException {
        logRequest("getCheckPlanCreationStatus requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getCheckPlanCreationStatus(userContext, formId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/checkEditPlanStatus/{planId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody getPlanStatusByEditLogId(@PathVariable String planId) throws BaseException {
        logRequest("getPlanStatusByEditLogId requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getPlanStatusByEditLogId(userContext, planId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/mesoCycleId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessPlannerMesoCycleIdResponse getCurrentMesoCycleId() throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getCurrentMesoCycleId(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/productpage"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessPlanProductPageView getFitnessPlanProductPageDetails(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String productId = queryParams.get("productId");
        String bookingId = queryParams.get("bookingId");
        if(!StringUtils.isEmpty(bookingId)) {
            return this.fitnessPlannerService.getProductPageAfterPlanGeneration(userContext, productId, bookingId);
        } else {
            return this.fitnessPlannerService.getPrePurchaseProductPage(userContext);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/plan"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessPlanProductPageView getFitnessPlan(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getFitnessPlanDetails(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/assessmentPlan"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public LevelDetailPageView getFitnessAssessmentPlan(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getFitnessAssessmentPlanDetails(userContext, queryParams, false);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/assessmentResult"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public LevelDetailPageView getFitnessAssessmentResult(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getFitnessAssessmentResult(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/assessmentCompletion"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public LevelCompletionPageView getFitnessAssessmentCompletionPage(@RequestParam Map<String, String> queryParams) {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getFitnessAssessmentCompletion(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/planInfo"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessPlanInfo getPersonalizedPageContent(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Map<String, String> headers = new HashMap<>();
        headers.put("x-request-id",request.getHeader("x-request-id"));
        return this.fitnessPlannerService.getPersonalizedPageContent(userContext, headers, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/editPlan"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public WorkoutPreferencePageView getWorkoutPreferences(@RequestParam Map<String, String> queryParams) throws BaseException, JsonProcessingException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Map<String, String> headers = new HashMap<>();
        headers.put("x-request-id",request.getHeader("x-request-id"));
        return this.fitnessPlannerService.getEditPlanDetails(userContext, headers, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/preferenceOption"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public WorkoutPreferenceOptions[] getPreferenceOptions(@RequestBody WorkoutPreferencesResponse workoutPreferencesResponse) throws BaseException, JsonProcessingException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Map<String, String> headers = new HashMap<>();
        headers.put("x-request-id",request.getHeader("x-request-id"));
        return this.fitnessPlannerService.preferenceOptions(userContext ,workoutPreferencesResponse, headers);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/submitEditPlan"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody updatePreferences(@RequestBody WorkoutPreferencesResponse workoutPreferencesResponse) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Map<String, String> headers = new HashMap<>();
        headers.put("x-request-id",request.getHeader("x-request-id"));
        return this.fitnessPlannerService.updatePreferences(userContext ,workoutPreferencesResponse, headers);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/changeWorkoutsPerDay"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody changeWorkoutDays(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Short WorkoutsPerDay = Short.parseShort(queryParams.get("workoutsPerDay"));
        return this.fitnessPlannerService.changeWorkoutDays(userContext, WorkoutsPerDay);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/changeIntensity"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody changeIntensity(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Short intensity = Short.parseShort(queryParams.get("intensity"));
        return this.fitnessPlannerService.changeWorkoutIntensity(userContext, intensity);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/swapWod"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody swapWod(@RequestBody FitnessPlannerSwapWodBody fitnessPlannerSwapWodBody) throws BaseException {
        logRequest(fitnessPlannerSwapWodBody.toString());
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.swapWod(userContext, fitnessPlannerSwapWodBody.getCurrentWodId(), fitnessPlannerSwapWodBody.getExchangeWodId());
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/skipWod"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody skipWod(@RequestBody FitnessPlannerSkipWodBody fitnessPlannerSkipWodBody) throws BaseException {
        logRequest(fitnessPlannerSkipWodBody.toString());
        return this.fitnessPlannerService.skipWod(fitnessPlannerSkipWodBody.getWodId());
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/startWod"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessPlannerStartWodResponse startWod(@RequestBody FitnessPlannerStartWodBody fitnessPlannerStartWodBody) throws BaseException {
        logRequest(fitnessPlannerStartWodBody.toString());
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.startWod(userContext, fitnessPlannerStartWodBody);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/stopWod"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public WodFeedbackView stopWod(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest("getStopWod requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.stopWod(queryParams, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/submitExerciseLogs"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody submitExerciseLogs(@RequestBody List<FPExerciseLog> exerciseLogs) throws BaseException {
        logRequest("submitLogs");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.guidedWorkoutsService.submitLogs(userContext, exerciseLogs);
        return new GenericSuccessBody(true);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/keyExercises"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessKeyExerciseLoggingView getKeyExercisesForLogging(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest("getKeyExercisesForLogging ");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getKeyExercisesForLogging(queryParams, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/completeWod/v2"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public WodCompleteView completeWodV2(@RequestBody FitnessPlannerCompleteWodBody fitnessPlannerCompleteWodBody, @RequestParam  Map<String, String> queryParams) throws BaseException {
        logRequest(fitnessPlannerCompleteWodBody.toString());
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.completeWodV2(
                userContext,
                fitnessPlannerCompleteWodBody.getWodId(),
                fitnessPlannerCompleteWodBody.getStartTime(),
                fitnessPlannerCompleteWodBody.getWodDuration(),
                fitnessPlannerCompleteWodBody.getWodName(),
                queryParams
        );
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/completeWod"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody completeWod(@RequestBody FitnessPlannerCompleteWodBody fitnessPlannerCompleteWodBody) throws BaseException {
        logRequest(fitnessPlannerCompleteWodBody.toString());
        return this.fitnessPlannerService.completeWod(
                fitnessPlannerCompleteWodBody.getWodId(),
                fitnessPlannerCompleteWodBody.getStartTime(),
                fitnessPlannerCompleteWodBody.getWodDuration(),
                fitnessPlannerCompleteWodBody.getFeedback(),
                fitnessPlannerCompleteWodBody.getExerciseIds()
        );
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/submitWodFeedback"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody sendWodFeedback(@RequestBody FitnessPlannerSendFeedback fitnessPlannerSendFeedback) throws BaseException {
        logRequest(fitnessPlannerSendFeedback.toString());
        return this.fitnessPlannerService.sendWodFeedback(
                fitnessPlannerSendFeedback.getWodId(),
                fitnessPlannerSendFeedback.getRating(),
                fitnessPlannerSendFeedback.getLabels(),
                fitnessPlannerSendFeedback.getComment()
        );
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/submitWodFeedback/v2"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody sendWodFeedbackV2(@RequestBody FitnessPlannerSendFeedbackV2 fitnessPlannerSendFeedbackV2) {
        logRequest(fitnessPlannerSendFeedbackV2.toString());
        try {
          return this.fitnessPlannerService.sendWodFeedbackV2(
                  (UserContext) request.getAttribute("userContext"),
              fitnessPlannerSendFeedbackV2.getWodId(),
              fitnessPlannerSendFeedbackV2.getFeedback(),
              fitnessPlannerSendFeedbackV2.getExerciseIds()
          );
        } catch (Exception e) {
          return new GenericSuccessBody(false);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/exerciseEntry/{exerciseEntryId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ExerciseDetailsView getExerciseEntryView(@PathVariable String exerciseEntryId, @RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        Long oldExerciseId = null;
        String oldExerciseIdString = queryParams.get("oldExerciseId");
        if(oldExerciseIdString != null && !oldExerciseIdString.equals(""))
            oldExerciseId = Long.valueOf(oldExerciseIdString);
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getExerciseEntryView(exerciseEntryId, oldExerciseId, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/exerciseEntry/v2/{exerciseEntryId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GuidedAlternativeExerciseView getExerciseEntryViewV2(@PathVariable String exerciseEntryId, @RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        Long oldExerciseId = null;
        String oldExerciseIdString = queryParams.get("oldExerciseId");
        if(oldExerciseIdString != null && !oldExerciseIdString.equals(""))
            oldExerciseId = Long.valueOf(oldExerciseIdString);
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.guidedWorkoutsService.getExerciseDetailsView(exerciseEntryId, oldExerciseId, userContext);
    }


    @RequestMapping(method = RequestMethod.GET, value = {"/exerciseEntry/{exerciseEntryId}/log"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public LogEditEntryWidget getExerciseEntryLogView(@PathVariable String exerciseEntryId) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getExerciseEntryLogView(Long.valueOf(exerciseEntryId));
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/exerciseEntry/{exerciseEntryId}/log"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public LogEditEntryWidget postExerciseEntryLog(@PathVariable String exerciseEntryId, @RequestBody LogEditEntryWidget logEditEntryWidget) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.postExerciseEntryView(logEditEntryWidget);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/exerciseEntry/{exerciseEntryId}/addExerciseExecutionEntry"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public LogEditEntryWidget addExerciseExecutionEntry(@PathVariable String exerciseEntryId, @RequestBody LogEditEntryWidget logEditEntryWidget) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.addExerciseExecutionEntry(logEditEntryWidget);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/exerciseEntry/{exerciseEntryId}/deny"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody denyWodExercise(@PathVariable String exerciseEntryId) throws BaseException {
        logRequest();
        return this.fitnessPlannerService.denyWodExercise(exerciseEntryId);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/exerciseEntry/{exerciseEntryId}/replace"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object replaceWodExercise(@PathVariable Long exerciseEntryId, @RequestBody ReplaceWodExerciseBody replaceWodExerciseBody) throws BaseException {
        logRequest();
        return this.fitnessPlannerService.replaceWodExercise(exerciseEntryId, replaceWodExerciseBody);
    }

//    @RequestMapping(method = RequestMethod.GET, value = {"/wodExerciseDetails/{cycleDay}"}, produces = MediaType.APPLICATION_JSON_VALUE)
//    @ResponseBody
//    public ExerciseDetailsView getWodExerciseDetails(@PathVariable Integer cycleDay,@RequestParam Map<String, String> queryParams) throws BaseException {
//        logRequest();
//        UserContext userContext = (UserContext) request.getAttribute("userContext");
//        return this.fitnessPlannerService.getWodExerciseDetailsView(userContext, cycleDay, queryParams);
//    }

//    @RequestMapping(method = RequestMethod.GET, value = {"/exerciseDetails/{exerciseId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
//    @ResponseBody
//    public ExerciseDetailsView getExerciseDetails(@PathVariable String exerciseId,@RequestParam Map<String, String> queryParams) throws BaseException {
//        logRequest();
//        UserContext userContext = (UserContext) request.getAttribute("userContext");
//        return this.fitnessPlannerService.getExerciseDetailsView(exerciseId, queryParams);
//    }



    @RequestMapping(method = RequestMethod.GET, value = {"/alternativeExercises/{exerciseEntryId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ExerciseDetailsView getAlternativeExercises(@PathVariable String exerciseEntryId, @RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getAlternativeDetailsView(userContext, exerciseEntryId, queryParams);
    }


    @RequestMapping(method = RequestMethod.GET, value = {"/calibrationScreen"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CalibrationScreenView getCalibrationScreenView(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest("getCalibrationScreenView called. " + queryParams.toString());
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getCalibrationScreenView(userContext, queryParams).join();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/report/{wodId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessReportView getWodReport(@PathVariable Long wodId, @RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getWodReport(userContext, wodId, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"updateUserWodReport/{wodReportId}"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody updateUserWodReport(@PathVariable Long wodReportId, @RequestBody UserWodReportBody userWodReportBody) throws BaseException {
        logRequest(wodReportId + "MediaGateway data imageUrl- " + userWodReportBody.getImageUrl() + " filter - " + userWodReportBody.getImageFilter());
        String imageUrl = userWodReportBody.getImageUrl();
        if(imageUrl != null && imageUrl.length() > 0) {
            try {
                imageUrl = mediaGatewayClient.validateAndGetDestinationUrl(imageUrl).getUrl();
            } catch (Exception e) {
                imageUrl = null;
                log.error("Error while fetching new url from media-gateway. e: {}", e.getMessage());
                logRequest("Error while fetching new url from media-gateway " + e.getMessage());
            }
        }
        return this.fitnessPlannerService.updateUserWodReport(wodReportId, imageUrl, userWodReportBody.getImageFilter());
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/redoWod"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody redoWod(@RequestBody FitnessPlannerSwapWodBody fitnessPlannerSwapWodBody) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.redoWod(userContext, fitnessPlannerSwapWodBody.getCurrentWodId(), fitnessPlannerSwapWodBody.getExchangeWodId());
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/editWod"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody editWod(@RequestBody FitnessPlannerEditWodBody fitnessPlannerEditWodBody) throws BaseException {
        logRequest();
        return this.fitnessPlannerService.editWod(
                fitnessPlannerEditWodBody.getUserId(),
                fitnessPlannerEditWodBody.getUserWodId(),
                fitnessPlannerEditWodBody.getUpdateEntireMesoCycle(),
                fitnessPlannerEditWodBody.getProgressionType()
        );
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/editOptions"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public EditWodModal nextScreenEditWodOptions(@RequestBody FitnessPlannerEditWodOptionsBody editWodOptionsBody) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.nextScreenEditWodOptions(editWodOptionsBody, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/editWodV2"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public EditWodResponse editWodV2(@RequestBody EditPlanUsingEditFlowRequest editFlowRequest) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.editWodV2(editFlowRequest, userContext.getUserProfile().getUserId());
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/missWod"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody dropWod(@RequestBody FitnessPlannerCompleteWodBody fitnessPlannerCompleteWodBody) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.missWod(
                userContext,
                fitnessPlannerCompleteWodBody.getWodId(),
                fitnessPlannerCompleteWodBody.getStartTime(),
                fitnessPlannerCompleteWodBody.getWodDuration(),
                fitnessPlannerCompleteWodBody.getReasons()
        );
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/guidedExerciseDetails/{cycleDay}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GuidedExerciseView getGuidedWorkoutsView(@PathVariable Integer cycleDay, @RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.guidedWorkoutsService.getGuidedWorkoutsCarouselView(userContext, cycleDay, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/markEngaged"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody markEngaged(@RequestBody FitnessPlannerStartWodBody fitnessPlannerStartWodBody) throws BaseException {
        logRequest(fitnessPlannerStartWodBody.toString());
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.markEngaged(userContext, fitnessPlannerStartWodBody.getWodId());
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/searchExercise"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<ExerciseSearchResponse> searchExercise(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String searchText = queryParams.get("searchText");
        String sectionToSearch = queryParams.get("sectionHeader");
        String trainerId = queryParams.get("trainerId");
        Map<String, String> headers = new HashMap<>();
        headers.put("x-request-id",request.getHeader("x-request-id"));
        return this.fitnessPlannerService.getExerciseForSection(userContext, headers, searchText, sectionToSearch, trainerId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/searchExercise/v2"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SearchExercisesPageView searchExerciseV2(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException, JsonProcessingException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String searchText = queryParams.get("searchText");
        String sectionToSearch = queryParams.get("sectionHeader");
        String appliedFilters = queryParams.get("appliedFilters");
        String pageFrom = queryParams.get("pageFrom");
        String sessionId = queryParams.get("sessionId") != null ? queryParams.get("sessionId") : null;
        ZoneId istZone = ZoneId.of("Asia/Kolkata");
        LocalDateTime now = LocalDateTime.now(Clock.system(istZone));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String currDate = now.format(formatter);
        String date = queryParams.get("date") != null ? queryParams.get("date"): currDate;
        Map<String, String> headers = new HashMap<>();
        headers.put("x-request-id",request.getHeader("x-request-id"));
        return this.fitnessPlannerService.getSearchExercisePage(userContext, headers, searchText, sectionToSearch, appliedFilters, sessionId, date, pageFrom);
    }


    @RequestMapping(method = RequestMethod.POST, value = {"/addExercise"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody addExercise(@RequestBody ModifyUserWodRequest modifyUserWodRequest) throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Map<String, String> headers = new HashMap<>();
        headers.put("x-request-id",request.getHeader("x-request-id"));
        return this.fitnessPlannerService.addExerciseInWod(modifyUserWodRequest, headers);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/exercisesForLogging"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessKeyExerciseLoggingView getExercisesForLogging(@RequestBody FitnessPlannerExerciseLogBody fitnessPlannerExerciseLogBody) throws BaseException {
        logRequest("getExercisesForLogging ");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getExercisesForLogging(fitnessPlannerExerciseLogBody.getExerciseIds(), userContext);
    }

    @RequestMapping(method= RequestMethod.GET, value={"postCheckinNuxScreen"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FPPostCheckInNuxJourneyResponse postCheckinNuxScreen(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getPostCheckinNuxScreen(userContext, queryParams);
    }

    @RequestMapping(method= RequestMethod.GET, value={"postCheckinAutoAssessmentScreen"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FPPostCheckInNuxJourneyResponse postCheckinAutoAssessmentScreen(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.postCheckinAutoAssessmentScreen(userContext, queryParams);
    }

    @RequestMapping(method= RequestMethod.PUT, value={"optOutOfPlanCreation"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GenericSuccessBody updateCreatePlanPushOptOutAttribute(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.updateCreatePlanPushOptOutAttribute(userContext, queryParams);
    }

    @RequestMapping(method= RequestMethod.GET, value={"userAssessmentReport"}, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserAssessmentReportResponse userAssessmentReport(@RequestParam Map<String, String> queryParams) throws BaseException {
        logRequest();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.fitnessPlannerService.getUserAssessmentReport(userContext, queryParams);
    }

    private void logRequest(){
        logRequest(null);
    }

    private void logRequest(String msg){
        try {
            StringBuilder sb = new StringBuilder(FitnessPlannerConstants.LOG_FITNESS_PLANNER);
            sb.append(FitnessPlannerConstants.LOG_USER_ID).append(((UserContext) request.getAttribute("userContext")).getUserProfile().getUserId());
            sb.append(FitnessPlannerConstants.LOG_SPACE);
            sb.append(FitnessPlannerConstants.LOG_REQUEST_ID).append(request.getHeader("x-request-id"));
            sb.append(FitnessPlannerConstants.LOG_SPACE);

            sb.append(FitnessPlannerConstants.LOG_URL).append(request.getMethod());
            sb.append(FitnessPlannerConstants.LOG_SPACE);
            sb.append(request.getRequestURL());
            sb.append("?");
            sb.append(request.getQueryString());

            sb.append(FitnessPlannerConstants.LOG_SPACE);
            if (msg != null) {
                sb.append(FitnessPlannerConstants.LOG_SPACE);
                sb.append(FitnessPlannerConstants.LOG_MESSAGE).append(msg);
            }
            log.info(sb.toString());
        } catch (Exception e){
            log.info("Fitness planner - log exception");
        }
    }
}
