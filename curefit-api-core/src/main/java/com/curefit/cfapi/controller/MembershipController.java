package com.curefit.cfapi.controller;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.view.viewbuilders.memberships.MembershipDetailPageViewBuilder;
import com.curefit.cfapi.view.viewmodels.memberships.MembershipPageView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


@RestController
@Slf4j
@RequestMapping(value = "membership")
public class MembershipController {

    private final ServiceInterfaces serviceInterfaces;
    private final HttpServletRequest request;

    @Autowired
    MembershipController(
            HttpServletRequest request,
            ServiceInterfaces serviceInterfaces
    ) {
        this.request = request;
        this.serviceInterfaces = serviceInterfaces;
    }


    @RequestMapping(method = RequestMethod.GET, value = {"/details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public MembershipPageView getMembershipDetail(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        MembershipDetailPageViewBuilder builder = new MembershipDetailPageViewBuilder();
        return builder.buildView(userContext, queryParams, serviceInterfaces, false);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/unpause"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public MembershipPageView getMembershipUnpause(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return new MembershipDetailPageViewBuilder().unPauseMembership(queryParams, userContext, serviceInterfaces);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/changeStartDate/{membershipServiceId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public MembershipPageView getEditStartDate(@RequestParam Map<String, String> queryParams, @PathVariable String membershipServiceId) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return new MembershipDetailPageViewBuilder().editStartDate(queryParams, membershipServiceId, userContext, serviceInterfaces);
    }
}
