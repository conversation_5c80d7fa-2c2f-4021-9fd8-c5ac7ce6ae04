package com.curefit.cfapi.controller;


import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.model.internal.exception.ClientException;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.ApiKeyService;
import com.curefit.cfapi.service.rashi.RashiService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ErrorUtil;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.RuntimeBaseException;
import com.curefit.iris.models.DecodeUnsubscriptionTokenResponse;
import com.curefit.iris.services.spi.UnsubscriptionService;
import com.curefit.userservice.client.UserServiceClient;
import com.curefit.userservice.pojo.entry.UserAttributeEntry;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.curefit.userservice.pojo.enums.UserAttributeName;
import com.curefit.userservice.pojo.response.UsersResponse;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import response.EmailResponse;

import javax.servlet.http.HttpServletRequest;
import java.util.Locale;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/communication")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommunicationUnsubscriptionController {
    HttpServletRequest request;
    UserServiceClient userServiceClient;
    UnsubscriptionService unsubscriptionService;
    ApiKeyService apiKeyService;
    RashiService rashiService;
    public CommunicationUnsubscriptionController(
            UserServiceClient userServiceClient,
            HttpServletRequest request,
            UnsubscriptionService unsubscriptionService,
            ApiKeyService apiKeyService,
            RashiService rashiService
    ) {
        this.userServiceClient = userServiceClient;
        this.request = request;
        this.unsubscriptionService = unsubscriptionService;
        this.apiKeyService = apiKeyService;
        this.rashiService = rashiService;
    }
  
    @GetMapping("/unsubscribe")
    @ResponseBody
    public ResponseEntity<UserAttributeEntry> unsubscribe(@RequestParam Map<String, String> queryParams,
                                                          @RequestHeader(value = "api-key", required = false) String apiKey) throws BaseException, Exception {
        String token = queryParams.get("token");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        DecodeUnsubscriptionTokenResponse response = null;
        try {
            response = this.unsubscriptionService.decodeEmailToken(token);
        } catch (BaseException e) {
            log.error("Error while decoding token", e);
        }
        String email = null;
        if (response != null) {
            email = response.getEmail();
        }
        UsersResponse usersResponse = null;

        AppTenant tenant = AppUtil.getTenantFromReq(request, apiKeyService);
        try {
            usersResponse = this.userServiceClient.getByEmail(email, tenant);
        } catch (RuntimeBaseException e) {
            log.error("Error while fetching user details", e);
        }
        log.info("UnsubscriptionService: tenant: {} email: {}", tenant, email);

        if (usersResponse == null || usersResponse.getUsers() == null) {
            throw new Error("Invalid token for communication unsubscription");
        }
        UserEntry userEntry = usersResponse.getUsers().get(email);

        if (ObjectUtils.isEmpty(userEntry)) {
            log.error("User with token {} and email {} not found in user service", token, email);
            throw new Error("User against this email does not exist in user-service");
        }

        Long userId = userEntry.getId();
        UserAttributeEntry userAttributeEntry = UserAttributeEntry.builder()
                .userId(userId.toString())
                .attrName(UserAttributeName.IS_SUBSCRIBED)
                .attrValue("FALSE")
                .comment("User has decided to unsubscribe from communication")
                .build();
        String agentId = null;
        if (AppUtil.isInternationalWeb(apiKey, apiKeyService)) {
            agentId = "livefit";
        }
        if (AppUtil.isWeb(userContext)) {
            agentId = "curefit";
        }

        // Directly mark user attribute issubscribed in Rashi, to bypass user-service user attribute construct as it is not multi tenant
        com.curefit.rashi.pojo.UserAttributeEntry userAttributeEntryRashi = null;
        if (!tenant.equals(AppTenant.CUREFIT)) {
            log.info("UnsubscriptionService: Calling rashi for marking issubscribed for user {} and tenant {}",
                    userId, tenant);
         userAttributeEntryRashi = this.rashiService.createOrUpdateUserAttributeEntry(userId,
                  UserAttributeName.IS_SUBSCRIBED.getValue().toLowerCase(), "FALSE", tenant);
         userAttributeEntry.setLastModifiedOn(userAttributeEntryRashi.getLastModifiedOn());
         userAttributeEntry.setCreatedOn(userAttributeEntryRashi.getCreatedOn());
         userAttributeEntry.setComment("User has decided to unsubscribe from communication. " +
                 "Directly marked in Rashi user attribute skipping user service attributes for this tenant");
        } else {
            this.userServiceClient.createOrUpdateUserAttributes(userAttributeEntry, agentId);
        }
       return new ResponseEntity<>(userAttributeEntry, HttpStatus.OK);
    }

    @GetMapping("/email")
    @ResponseBody
    public ResponseEntity<EmailResponse> getEmail(@RequestParam Map<String, String> queryParams) throws ClientException {
        String token = queryParams.get("token");
        DecodeUnsubscriptionTokenResponse response = null;
        try {
            response = this.unsubscriptionService.decodeEmailToken(token);
        } catch (BaseException e) {
            log.error("Error while decoding token", e);
        }
        String email = null;
        if (response != null) {
            email = response.getEmail();
        }
        if (email == null) {
            throw new ClientException("Invalid token", ErrorUtil.ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR);
        }
        EmailResponse emailResponse = new EmailResponse();
        emailResponse.setEmail(email);
        return new ResponseEntity<>(emailResponse, HttpStatus.OK);
    }
}
