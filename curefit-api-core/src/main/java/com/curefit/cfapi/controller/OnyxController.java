package com.curefit.cfapi.controller;

import com.curefit.cfapi.config.ConverseConfiguration;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.onyx.OnyxWorkoutCatalog;
import com.curefit.cfapi.pojo.onyx.OnyxCoachUpsellResponse;
import com.curefit.cfapi.service.converse.ConversationService;
import com.curefit.cfapi.service.onyx.CoachSubscriptionDetails;
import com.curefit.cfapi.service.onyx.OnyxService;
import com.curefit.cfapi.service.onyx.UserWorkoutPlanDetailsClient;
import com.curefit.cfs.client.CFSClient;
import com.curefit.cfs.pojo.UserFormEntry;
import com.curefit.cfs.pojo.UserFormResponseEntry;
import com.curefit.common.data.exception.BaseException;

import com.curefit.converse.pojo.*;
import com.curefit.onyx.pojo.entry.CFSOnyxFormEntry;
import com.curefit.onyx.pojo.entry.CFSUserFormEntry;
import com.curefit.onyx.pojo.entry.Coach;
import com.curefit.onyx.pojo.entry.CreateNewUserForm;
import com.curefit.onyx.pojo.entry.OnboardingUserBasicInfo;
import com.curefit.onyx.pojo.entry.OnyxCoachMembership;
import com.curefit.onyx.pojo.entry.UserStreakDetails;
import com.curefit.onyx.pojo.entry.UserToSelectedCoachMapping;
import com.curefit.onyx.pojo.entry.metrics.Session;
import com.curefit.converse.chat.pojo.TwilioAccessTokenRequest;
import com.curefit.converse.enums.CommMode;
import com.curefit.converse.enums.ParticipantAccessLevel;
import com.curefit.converse.enums.ServiceProvider;
import com.curefit.onyx.pojo.entry.activity.Activity;
import com.curefit.onyx.pojo.entry.conversation.TwilioAccessToken;
import com.curefit.onyx.pojo.entry.conversation.TwilioConversationToUserMap;
import com.curefit.onyx.pojo.enums.DifficultyLevel;
import com.curefit.userservice.pojo.entry.UserEntry;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;


@RestController
@Slf4j
@RequestMapping(value = "onyx")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class OnyxController {
    OnyxService onyxService;
    ConversationService conversationService;
    CFSClient cfsClient;
    HttpServletRequest request;
    ConverseConfiguration converseConfiguration;

    @Autowired
    public OnyxController(final OnyxService onyxService,
                          final HttpServletRequest request,
                          final ConversationService conversationService,
                          final CFSClient cfsClient,
                          final ConverseConfiguration converseConfiguration) {
        this.onyxService = onyxService;
        this.request = request;
        this.conversationService = conversationService;
        this.cfsClient = cfsClient;
        this.converseConfiguration = converseConfiguration;
    }

    @GetMapping(value = {"/allworkouts"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public OnyxWorkoutCatalog AllWorkoutresponse(
        @RequestParam(defaultValue = "Muscle") final List<String> goals,
        @RequestParam(defaultValue = "BEGINNER") final DifficultyLevel intensity,
        @RequestParam(defaultValue = "5") final int count
    ){
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.onyxService.getAllOnyxWorkout(goals, intensity, count, userContext.getUserProfile().getUserId());
    }

    @GetMapping(value = {"/streak/details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public UserStreakDetails getStreakDetails() throws BaseException {
       UserContext userContext = (UserContext) request.getAttribute("userContext");
       String userId = userContext.getUserProfile().getUserId();
       String timezone = userContext.getUserProfile().getTimezone();
       return this.onyxService.getWeeklyStreakDetails(userId, timezone);
    }

    @ResponseBody
    @PostMapping(value = {"/saveWorkoutSession"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Session createSession(
            @RequestBody Session requestBody
    ) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        requestBody.setUserId(userContext.getUserProfile().getUserId());
        return this.onyxService.saveWorkoutSession(requestBody);
    }

    @ResponseBody
    @PostMapping(value = {"/onboarding-user-basic-info"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public OnboardingUserBasicInfo createOnboardingUserBasicInfo(
            @RequestBody OnboardingUserBasicInfo requestBody
    ) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        requestBody.setUserId(userContext.getUserProfile().getUserId());
        return this.onyxService.saveOnboardingUserBasicInfo(requestBody);
    }

    @ResponseBody
    @PostMapping(value = {"/onboarding/submit"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public OnboardingUserBasicInfo createOnboardingUserForm(
            @RequestBody OnboardingUserBasicInfo requestBody
    ) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        requestBody.setUserId(userContext.getUserProfile().getUserId());
        return this.onyxService.saveOnboardingUserBasicInfo(requestBody);
    }

    @GetMapping(value = {"/onboarding-user-basic-info"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public OnboardingUserBasicInfo getOnboardingUserBasicInfo() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.onyxService.getOnboardingUserBasicInfo(userContext.getUserProfile().getUserId());
    }

    @ResponseBody
    @PostMapping(value = {"/new-user-form"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public UserFormEntry createNewUserForm(
            @RequestBody CreateNewUserForm requestBody
    ) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        requestBody.setUserId(userContext.getUserProfile().getUserId());
        return this.onyxService.createNewUserForm(requestBody);
    }

    // deprecated api use /form/submit
    @ResponseBody
    @PostMapping(value = {"/cfs-user-form-response"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CFSUserFormEntry submitCFSUserForm(@RequestBody @Valid final CFSUserFormEntry
                                                    requestBody) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        requestBody.setUserId(userContext.getUserProfile().getUserId());
        return this.onyxService.submitCFSUserForm(requestBody);
    }

    @ResponseBody
    @PostMapping(value = {"/form/submit"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CFSUserFormEntry submitCfsUserForm(@RequestBody @Valid final CFSUserFormEntry
                                                      requestBody) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        requestBody.setUserId(userContext.getUserProfile().getUserId());
        return this.onyxService.submitCFSUserForm(requestBody);
    }

    @GetMapping(value = {"/cfs-form-id"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CFSOnyxFormEntry getFormById(@RequestParam("formId") final String formId) throws BaseException {
        return this.onyxService.getFormById(formId);
    }
    //deferred signup
    @GetMapping(value = {"/coach/form"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CFSOnyxFormEntry getCfsFormById(@RequestParam("id") final String id) throws BaseException {
        return this.onyxService.getFormById(id);
    }

    @GetMapping(value = {"/cfs-user-form-response"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public UserFormResponseEntry getUserFormResponse(@RequestParam("formId")
                                                         final String formId) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.cfsClient.getLastCompletedUserFormResponse(userContext.getUserProfile().getUserId(), formId);
    }

    @GetMapping(value = {"/coach-upsell"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public OnyxCoachUpsellResponse getOnyxCoachUpsell()  {
        return this.onyxService.getOnyxCoachUpsellResponse();
    }

    @GetMapping(value = {"/all-coaches"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Coach> getAllCoaches() throws BaseException {
        return this.onyxService.getAllCoaches();
    }

    @GetMapping(value = {"/coach"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Coach getCoach(@RequestParam("coachId") final String coachId) throws BaseException {
        return this.onyxService.getCoach(coachId);
    }

    //deferred signup
    @GetMapping(value = {"/coach/details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Coach getCoachDetails(@RequestParam("id") final String id) throws BaseException {
        return this.onyxService.getCoach(id);
    }

    @GetMapping(value = {"/coach-membership"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public OnyxCoachMembership getActiveMembership() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.onyxService.getActiveOnyxCoachMembership(userContext.getUserProfile().getUserId());
    }

    /**
     * creates access token against userIdentity and returns the token-created.
     * @return token-string
     */
    @PostMapping(value = {"/access-token"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TwilioAccessToken> createAccessToken(
            @RequestParam(defaultValue = "false") final Boolean refresh
    ) throws BaseException,
            ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        UserEntry userEntry = userContext.getUserEntryCompletableFuture().get();
        String userId = userContext.getUserProfile().getUserId();
        String token = onyxService.getAccessToken(userId);
        if (refresh == false && token != null) {
            TwilioAccessToken twilioAccessToken = new TwilioAccessToken();
            twilioAccessToken.setToken(token);
            twilioAccessToken.setUserId(userId);
            twilioAccessToken.setUserIdentityEmailAddress(userEntry.getEmail());
            return new ResponseEntity<>(twilioAccessToken, HttpStatus.OK);
        }
        TwilioAccessTokenRequest
                accessTokenRequest = new TwilioAccessTokenRequest(converseConfiguration.getChatServiceId(),
                userEntry != null
                        ? userEntry.getEmail() : null, converseConfiguration.getTtl(), converseConfiguration.getPnSid(), "onyx");
        String accessTokenJwt = conversationService.createAccessToken(accessTokenRequest);
        if(StringUtils.isEmpty(accessTokenJwt)) {
            log.info("access Token not generated for accessTokenRequest:" + accessTokenRequest);
            return null;
        }

        TwilioAccessToken accessToken = TwilioAccessToken.builder()
                                                        .token(accessTokenJwt)
                                                        .userId(userId)
                                                        .userIdentityEmailAddress(userEntry != null
                                                                                        ? userEntry.getEmail() : null)
                                                        .build();
        return new ResponseEntity<>(onyxService.saveAccessToken(accessToken), HttpStatus.OK);
    }

    /**
     * creates conversation, add participants to it, and sends first system generated message.
     * @return TwilioConversationToUserMap
     */
    @PostMapping(value = "/conversation-participant-message")
    public ResponseEntity<TwilioConversationToUserMap> createConversationWithParticipants()
            throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        TwilioConversationToUserMap existingTwilioConversationToUserMap =
                onyxService.getConversationToUserMap(userContext.getUserProfile().getUserId());
        if(!ObjectUtils.isEmpty(existingTwilioConversationToUserMap)) {
            return new ResponseEntity<>(
                    existingTwilioConversationToUserMap, HttpStatus.OK);
        }

        OnyxCoachMembership onyxCoachMembership = onyxService.getActiveOnyxCoachMembership(
                                                            userContext.getUserProfile().getUserId());
        if(ObjectUtils.isEmpty(onyxCoachMembership)) {
            log.error("Chat can not be created for user. No active membership found for user: "
                        + userContext.getUserProfile().getUserId());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        ConversationRequest conversationEntry = new ConversationRequest();
        conversationEntry.setUniqueName("onyx-coach-" + userContext.getUserProfile().getUserId()
                                                + "-" + onyxCoachMembership.getActiveCoachId());
        if(onyxCoachMembership.getActiveCoachDetails() != null
                && onyxCoachMembership.getActiveCoachDetails().getName() != null) {
            conversationEntry.setFriendlyName(onyxCoachMembership.getActiveCoachDetails().getName());
        } else {
            conversationEntry.setFriendlyName("Coach");
        }
        conversationEntry.setCommMode(CommMode.CHAT);
        conversationEntry.setTenant("onyx");
        conversationEntry.setServiceProvider(ServiceProvider.TWILIO);

        List<RoomParticipantEntry> roomParticipantEntries = new ArrayList<>();

        RoomParticipantEntry roomParticipantEntryObserver = new RoomParticipantEntry();
        roomParticipantEntryObserver.setAccessLevel(ParticipantAccessLevel.OBSERVER);
        ParticipantEntry participantEntryObserver = new ParticipantEntry();
        participantEntryObserver.setAccessLevel(ParticipantAccessLevel.OBSERVER);
        participantEntryObserver.setTenant("onyx");
        participantEntryObserver.setParticipantType("NON_CHAT");

        String coachContactNumber = onyxCoachMembership.getActiveCoachDetails().getUserChatPhoneNumber();
        if(coachContactNumber == null) {
            log.info("Coach does not contains contact info. to chat with client");
            return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
        }
        participantEntryObserver.setParticipantRefId(coachContactNumber);

        roomParticipantEntryObserver.setParticipantEntry(participantEntryObserver);

        RoomParticipantEntry roomParticipantEntryController = new RoomParticipantEntry();
        roomParticipantEntryController.setAccessLevel(ParticipantAccessLevel.CONTROLLER);
        ParticipantEntry participantEntryController = new ParticipantEntry();
        participantEntryController.setAccessLevel(ParticipantAccessLevel.CONTROLLER);
        participantEntryController.setTenant("onyx");
        participantEntryController.setParticipantType("CHAT");
        //user's email address
        String participantRefId;
        if(userContext.getUserEntryCompletableFuture() != null
                && userContext.getUserEntryCompletableFuture().get().getEmail() != null) {
            participantRefId = userContext.getUserEntryCompletableFuture().get().getEmail();
        } else {
            participantRefId = userContext.getUserProfile().getUserId();
        }

        participantEntryController.setParticipantRefId(participantRefId);
        roomParticipantEntryController.setParticipantEntry(participantEntryController);

        roomParticipantEntries.add(roomParticipantEntryObserver);
        roomParticipantEntries.add(roomParticipantEntryController);

        conversationEntry.setParticipants(roomParticipantEntries);

        RoomEntry roomEntry =
                conversationService.createConversationWithParticipants(conversationEntry);

        String messageText;
        if(userContext.getUserEntryCompletableFuture() != null) {
            messageText = "Welcome to Onyx Coach Chat for User: " +
                            userContext.getUserEntryCompletableFuture().get().getFirstName() + " " +
                            userContext.getUserEntryCompletableFuture().get().getLastName();
        } else {
            messageText = "Welcome to Onyx Coach Chat for User: " + userContext.getUserProfile().getUserId();
        }
        TwilioMessage message = conversationService.sendMessage(roomEntry.getServiceProviderRefId(), messageText,
                                                            "user-filter", "");

        log.info("ConversationId" +roomEntry.getServiceProviderRefId() + "Message sent: " +message.toString());
        TwilioConversationToUserMap twilioConversationToUserMap = TwilioConversationToUserMap.builder()
                .conversationId(roomEntry.getServiceProviderRefId())
                .userId(userContext.getUserProfile().getUserId())
                .build();
        return new ResponseEntity<>(onyxService.saveConversationWithParticipants(twilioConversationToUserMap), HttpStatus.OK);
    }

    /**
     * @return message
     * @throws BaseException
     */
    @PostMapping(value = {"/converse-message"})
    public TwilioMessage sendMessage(@RequestBody final ConversationMessage conversationMessage) throws BaseException {
        return conversationService.sendMessage(conversationMessage.getConversationId(),
                                                conversationMessage.getBody(),
                                                conversationMessage.getAuthor(),
                                                conversationMessage.getAttributes());
    }

    /**
     * @return list of recommended coaches
     * @throws BaseException
     */
    @GetMapping(value = {"/recommend-coaches"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Coach> getRecommendedCoaches() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.onyxService.getRecommendedCoaches(userContext.getUserProfile().getUserId());
    }

    //deferred signup
    // userId not used for recommendations
    @GetMapping(value = {"/coach/list/recommended"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Coach> getRecommendedCoachesList() throws BaseException {
        return this.onyxService.getRecommendedCoaches(null);
    }

    /**
     * @return coach plan
     * @throws BaseException
     */
    @GetMapping(value = {"/coach-plan"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<UserWorkoutPlanDetailsClient> getPlan(
            @RequestParam(defaultValue = "0") final int offset,
            @RequestParam(defaultValue = "3") final int limit,
            @RequestParam(defaultValue = "startDate") final String sortBy,
            @RequestParam(defaultValue = "DESC") final String sortOrder
    ) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = userContext.getUserProfile().getUserId();
        String timezone = userContext.getUserProfile().getTimezone();
        String startTimeZone = "T00:00:00Z";
        LocalDate localdate = LocalDate.now(ZoneId.of(timezone));
        String startDate = LocalDate.now(ZoneId.of(timezone)).minusDays(180) + startTimeZone;
        String endDate = LocalDate.now(ZoneId.of(timezone)) + startTimeZone;
        DayOfWeek dayOfWeek = DayOfWeek.of(LocalDate.now(ZoneId.of(timezone)).get(ChronoField.DAY_OF_WEEK));
        Boolean fetchNexWeekPlan = dayOfWeek == DayOfWeek.SUNDAY;
        if (fetchNexWeekPlan) {
            endDate = LocalDate.now(ZoneId.of(timezone)).plusDays(7) + startTimeZone;
        }
        return this.onyxService.getUserPlan(userId, startDate, endDate, offset, limit, sortBy, sortOrder);
    }

    /**
     * @return workout-completed
     * @throws BaseException
     */
    @PostMapping(value = "/workout-completed")
    public Activity completeWorkoutEvent(
            @RequestBody Activity requestBody
    ) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = userContext.getUserProfile().getUserId();
        requestBody.setUserId(userId);
        return this.onyxService.completeWorkoutEvent(requestBody);
    }

    /**
     *
     * @param coachId coachId
     * @return UserToSelectedCoachMapping
     * @throws BaseException
     */
    @ResponseBody
    @PostMapping(value = {"/user-coach-map"}, produces = MediaType.APPLICATION_JSON_VALUE)
      public UserToSelectedCoachMapping createUserToSelectedCoachMapping(
            @RequestParam(defaultValue = "false") final String coachId
    ) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        List<String> selectedCoaches = new ArrayList<>();
        selectedCoaches.add(coachId);
        return onyxService.createUserToSelectedCoachMapping(UserToSelectedCoachMapping.builder()
                                                            .userId(userContext.getUserProfile().getUserId())
                                                            .selectedCoachTrainerIds(selectedCoaches).build());
    }

    /**
     * @return coach plan
     * @throws BaseException
     */
    @GetMapping(value = {"/coach-subscription-details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CoachSubscriptionDetails getUserPlan() throws Exception {
        return this.onyxService.getCoachSubscriptionDetails();
    }
}
