package com.curefit.cfapi.controller;

import com.curefit.cfapi.model.internal.userinfo.SessionData;
import com.curefit.cfapi.model.internal.userinfo.SessionInfo;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.diy.DigitalInSessionRequestBody;
import com.curefit.cfapi.pojo.status.DetectedCity;
import com.curefit.cfapi.service.ApiKeyService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.diyfs.client.spi.DiyfsService;
import com.curefit.diyfs.pojo.PlaybackMetric;
import com.curefit.diyfs.pojo.InSessionResponse;
import com.curefit.diyfs.pojo.ScoreMetric;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@Slf4j
@RequestMapping(value = "liveInSession")
public class DigitalInSessionController {
    DiyfsService diyfsService;
    private HttpServletRequest request;
    private ApiKeyService apiKeyService;

    @Autowired
    public DigitalInSessionController(
            HttpServletRequest request,
            DiyfsService diyfsService,
            ApiKeyService apiKeyService
    ) {
        this.diyfsService = diyfsService;
        this.request = request;
        this.apiKeyService = apiKeyService;
    }

    @PostMapping("/playbackMetrics")
    public ResponseEntity<InSessionResponse> postPlaybackMetric(@RequestBody DigitalInSessionRequestBody requestBody) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        PlaybackMetric playBackmetric = requestBody.getPlaybackMetric();
        playBackmetric.setUserId(userContext.getUserProfile().getUserId());
        playBackmetric.setSelectedCityId(userContext.getUserProfile().getCity().getCityId());
        playBackmetric.setSelectedCountryId(userContext.getUserProfile().getCity().getCountryId());
        playBackmetric.setTenant(AppUtil.callSource(userContext.getSessionInfo().getApiKey(), apiKeyService));
        playBackmetric.setAppTenant(AppUtil.getAppTenantFromUserContext(userContext));
        SessionInfo sessionInfo = userContext.getSessionInfo();
        Float appVersion = userContext.getSessionInfo().getAppVersion();
        SessionData sessionData = sessionInfo.getSessionData();
        DetectedCity detectedCity = sessionData.getDetectedCity();
        if (detectedCity != null) {
            playBackmetric.setDetectedCityId(detectedCity.getCity());
            playBackmetric.setDetectedCountryId(detectedCity.getCountry());
        }
        return new ResponseEntity<>(this.diyfsService.updatePlaybackMetrics(playBackmetric, requestBody.getResponseRequired(), appVersion), HttpStatus.OK);
    }

    @PostMapping("/updateScoreMetrics")
    public ResponseEntity<InSessionResponse> postScoreMetric(@RequestBody DigitalInSessionRequestBody requestBody) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Float appVersion = userContext.getSessionInfo().getAppVersion();
        ScoreMetric scoreMetric = requestBody.getScoreMetric();
        scoreMetric.setUserId(userContext.getUserProfile().getUserId());
        return new ResponseEntity<>(this.diyfsService.updateScoreMetrics(scoreMetric, requestBody.getResponseRequired(), appVersion), HttpStatus.OK);
    }
}
