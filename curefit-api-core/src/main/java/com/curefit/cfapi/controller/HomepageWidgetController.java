package com.curefit.cfapi.controller;

import com.curefit.cfapi.dto.UpdateHomeWidgetStatusRequest;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.HomePageWidget.HomeScreenWidgetService;
import com.curefit.cfapi.widgets.base.HomeScreenBaseWidget;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;

@RestController
@Slf4j
@AllArgsConstructor
@RequestMapping(value = "/home-page-widget")
public class HomepageWidgetController {
    private HttpServletRequest request;
    private HomeScreenWidgetService homeScreenWidgetService;

    @RequestMapping(method = RequestMethod.GET, value = {"/getHomeScreenWidget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public HomeScreenBaseWidget getHomeScreenWidget(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return homeScreenWidgetService.getHomeScreenWidget(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/updateHomeWidgetStatus"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean updateHomeWidgetStatus(@Valid @RequestBody UpdateHomeWidgetStatusRequest request) {
        UserContext userContext = (UserContext) this.request.getAttribute("userContext");
        return homeScreenWidgetService.updateHomeWidgetStatus(userContext, request.getStatus());
    }

}
