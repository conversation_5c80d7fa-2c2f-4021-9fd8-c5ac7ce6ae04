package com.curefit.cfapi.controller;

import com.curefit.catalogv1.services.filter.ElitePackFilter;
import com.curefit.center.dtos.CenterEntry;
import com.curefit.center.dtos.LocalityEntry;
import com.curefit.cfapi.model.Analytics.BackgroundWearableTaskEvent;
import com.curefit.cfapi.model.internal.exception.BadRequestException;
import com.curefit.cfapi.model.internal.exception.ClientException;
import com.curefit.cfapi.model.internal.exception.ServerException;
import com.curefit.cfapi.model.internal.fitness.request.RedirectionRequest;
import com.curefit.cfapi.model.internal.fitness.response.HybridGymBookingResponse;
import com.curefit.cfapi.model.internal.fitness.response.ImagesGalleryResponse;
import com.curefit.cfapi.model.internal.fitness.response.RedirectionResponse;
import com.curefit.cfapi.model.internal.fitness.response.TrialResponse;
import com.curefit.cfapi.model.internal.userinfo.Session;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.habit.EditHabitRequestBody;
import com.curefit.cfapi.pojo.habit.SaveHabitRequestBody;
import com.curefit.cfapi.pojo.wearable.HealthWearableData;
import com.curefit.cfapi.service.CFAnalytics.CFAnalytics;
import com.curefit.cfapi.service.LocalitySelectorService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.UserService;
import com.curefit.cfapi.service.fitness.*;
import com.curefit.cfapi.service.seo.SEOService;
import com.curefit.cfapi.util.ActionUtil;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.ErrorUtil;
import com.curefit.cfapi.util.SelectPacksUtils;
import com.curefit.cfapi.view.viewbuilders.fitness.CenterSearchPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.fitness.CreditTransactionPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.fitness.GetPackTransitionDetailsPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.fitness.PackComparisonPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.fitness.PackTransitionPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.fitness.TransferUpgradePageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.fitso.PlayUpgradePageBuilder;
import com.curefit.cfapi.view.viewmodels.fitness.*;
import com.curefit.cfapi.widgets.activityAttributeLogging.EncapsulatorWidget;
import com.curefit.cfapi.widgets.activityAttributeLogging.GymActivityLoggingWidget;
import com.curefit.cfapi.widgets.fitness.CultHabitsWidget;
import com.curefit.cfapi.widgets.fitness.FitnessCLPCentersWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.logging.models.request.BulkSaveActivityPayload;
import com.curefit.logging.models.request.SaveActivityPayload;
import com.curefit.logging.models.response.ActivityResponse;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.types.MembershipType;
import com.curefit.pms.pojo.customPacks.OfflineFitnessPack;
import com.curefit.pms.requests.PackSearchRequest;
import com.curefit.product.enums.ProductType;
import com.curefit.shifu.enums.FrequencyType;
import com.curefit.shifu.enums.HabitType;
import com.curefit.shifu.enums.TimeOfDay;
import com.curefit.shifu.pojo.*;
import com.curefit.location.models.City;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.util.ajax.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ExecutionException;

@RestController
@Slf4j
@RequestMapping(value = "fitness")
public class FitnessController {
    private HttpServletRequest request;
    private TrialService trialService;
    private CenterDetailsService centerDetailsService;
    private LocalitySelectorService localitySelectorService;
    private final HybridGymService hybridGymService;
    private SEOService seoService;
    private CxPostureFeedbackService cxPostureFeedbackService;
    private UserService userService;
    private ImagesGalleryService imagesGalleryService;

    private ServiceInterfaces interfaces;

    private FitnessCLPService fitnessCLPService;

    private PreferenceService preferenceService;
    private CFAnalytics cfAnalytics;
    private PastActivityService pastActivityService;


    @Autowired
    public FitnessController(
            HttpServletRequest request,
            TrialService trialService,
            CenterDetailsService centerDetailsService,
            LocalitySelectorService localitySelectorService,
            HybridGymService hybridGymService,
            SEOService seoService,
            CxPostureFeedbackService cxPostureFeedbackService,
            UserService userService,
            ImagesGalleryService imagesGalleryService,
            ServiceInterfaces interfaces,
            FitnessCLPService fitnessCLPService,
            PreferenceService preferenceService,
            CFAnalytics cfAnalytics,
            PastActivityService pastActivityService
    ) {
        this.request = request;
        this.trialService = trialService;
        this.localitySelectorService = localitySelectorService;
        this.centerDetailsService = centerDetailsService;
        this.hybridGymService = hybridGymService;
        this.seoService = seoService;
        this.cxPostureFeedbackService = cxPostureFeedbackService;
        this.userService = userService;
        this.imagesGalleryService = imagesGalleryService;
        this.interfaces = interfaces;
        this.fitnessCLPService = fitnessCLPService;
        this.preferenceService = preferenceService;
        this.cfAnalytics = cfAnalytics;
        this.pastActivityService = pastActivityService;
    }

    @GetMapping(value = {"/trials"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public TrialResponse getTrailData(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return trialService.getAllTrialWidgets(userContext);
    }

    @GetMapping(value = {"/center/{centerId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CenterDetailsPageView getCenterDetailsPage(@PathVariable String centerId, @RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        CenterDetailsPageView centerDetailsPageView = centerDetailsService.getCenterDetailsPageView(userContext, centerId, queryParams);

        if (centerDetailsPageView != null && centerDetailsPageView.getIsCenterOperational() != null && !centerDetailsPageView.getIsCenterOperational()) {
            try {
                Long centerServiceId = Long.parseLong(centerId);
                CenterEntry center = interfaces.centerService.getCenterDetails(centerServiceId, true, null, null).get();

                if (center != null && center.getLocality() != null) {
                    String localityName = center.getLocality();

                    if (userContext.getUserProfile() != null && userContext.getUserProfile().getCity() != null) {
                        String cityId = userContext.getUserProfile().getCity().getCityId();

                        if (cityId != null) {
                            LocalityEntry localityEntry = interfaces.localityProviderService.getLocalityFromName(cityId, localityName);

                            if (localityEntry != null && localityEntry.getName() != null && localityEntry.getId() != null) {
                                City cityData = userContext.getUserProfile().getCity();
                                Action localityNavAction = ActionUtil.getAreaPageNavigationAction(userContext, cityData, localityEntry.getName(), localityEntry.getId());
                                centerDetailsPageView.addAction(localityNavAction);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                interfaces.exceptionReportingService.reportException("Error constructing locality navigation for non-operational center: " + centerId, e);
            }
        }

        if (queryParams.containsKey("seoPageId") ) {
            String seoPageId= queryParams.get("seoPageId");
            centerDetailsPageView.setSeoDataForOperationalCenter(seoService.getMetaDataById(seoPageId));
        }
        return centerDetailsPageView;
    }

    @GetMapping(value = {"/hybridGym"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public HybridGymBookingResponse getHybridGymBookingData(@RequestParam Map<String, String> queryParams) throws ExecutionException, InterruptedException, BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return hybridGymService.getHybridGymBookingWidgets(userContext, queryParams);
    }

    @GetMapping(value = {"/centerImagesGallery/{centerId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ImagesGalleryResponse getCenterImagesGalleryPageData(@PathVariable String centerId, @RequestParam Map<String, String> queryParams) throws ExecutionException, InterruptedException, BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return imagesGalleryService.getImagesGalleryPageWidgets(userContext,centerId, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/locality"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getLocalitySelectorData(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return localitySelectorService.getLocalitySelectorPageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/detailedFeedback"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CxPostureFeedbackPageView getCxPostureFeedbackDetailsPage(@RequestParam Map<String, String> queryParams) throws Exception {
        System.out.println("detailedFeedback");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return cxPostureFeedbackService.getCxPostureFeedbackPageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/personalizedFeedbacks"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CxPostureFeedbackPageView getPostureFeedbackMovementListPage(@RequestParam Map<String, String> queryParams) throws Exception {
        System.out.println("getPostureFeedbackMovementListPage");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return cxPostureFeedbackService.getPostureFeedbackMovementListPageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/userEnergyStreak"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public EnergyActivityStreak getUserEnergyStreak() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.userService.getUserEnergyStreak(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/userEnergyStreak/reset"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean resetUserEnergyStreak() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.userService.resetUserEnergyStreak(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/userEnergyStreak/resetOn"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean resetOnboarding(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String gameId = queryParams.get("gameId");
        return this.userService.resetOnboarding(userContext, gameId);
    }
    @RequestMapping(method = RequestMethod.GET, value = {"/testWidget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getWidget(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        EncapsulatorWidget xx = new EncapsulatorWidget();
        return xx.buildView(interfaces, userContext, new WidgetContext());
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/userEnergyStreakV2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public EnergyActivityStreak getUserEnergyStreakV2() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.userService.getUserEnergyStreakV2(userContext, interfaces);
    }

    @GetMapping(value = {"/center"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CenterDetailsPageView getCentersDetailPage(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Session session = (Session) request.getAttribute("session");
        long centerId = queryParams.get("centerId") != null ? Long.parseLong(queryParams.get("centerId")) : 0L;
        SelectPackCenterPageViewBuilder builder = new SelectPackCenterPageViewBuilder(interfaces, queryParams);
        try {
            CenterEntry center = interfaces.centerService.getCenterDetails(centerId, true, null, null).get();
            WidgetContext widgetContext = new WidgetContext();
            widgetContext.setQueryParams(queryParams);
            CityMismatchConfirmationModal cityModalData =
                    SelectPacksUtils.handlePageCityMismatch(
                            userContext, session, center.getCity(),
                            ((queryParams.containsKey("canUpdateCity") && Boolean.parseBoolean(queryParams.get("canUpdateCity")))
                            || !AppUtil.isAppCityMismatchModalSupported(userContext)), interfaces
                    );
            if (Objects.nonNull(cityModalData)) {
                CenterDetailsPageView pageView = new CenterDetailsPageView();
                pageView.setCityModalData(cityModalData);
                return pageView;
            }
            return builder.buildView(userContext, center, widgetContext);
        } catch (Exception e){
            interfaces.exceptionReportingService.reportException("Unable to fetch center detail for: " + centerId, e);
            throw new ServerException("Unable to fetch center detail, Please try again.", ErrorUtil.ErrorCodes.CENTER_DETAIL_ERROR, HttpStatus.CONFLICT.value());
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/getHabits"} , produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public  Object getHabitsForCult(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        CultHabitsWidget habits = new CultHabitsWidget();
        return habits.buildView(interfaces,userContext,queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/patchUserAction"} , produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SaveDataHabitPage updateUserAction(@RequestBody EditHabitRequestBody editHabitRequestBody) throws Exception {
        try {
            UserActionEntry userActionEntry = new UserActionEntry();
            userActionEntry.setId(editHabitRequestBody.getId());
            userActionEntry.setScheduledTime(getLocalTime(editHabitRequestBody.getCurrent()));
            userActionEntry.setTenant("CULT");
            UserActionEntry userActionEntryFinal = interfaces.shifuClient.updateUserActionSchedule(userActionEntry);

            SaveDataHabitPage updatePage = new SaveDataHabitPage();
            updatePage.setHeader("HABIT Updated");
            updatePage.setSubHeader("We will be sending reminders for this habit on updated time.");
            Action action2 = new Action();
            action2.setActionType(ActionType.MOVE_BACK);
            updatePage.setAction(action2);
            updatePage.setType("edit");
            return updatePage;

        } catch(Exception error) {
            interfaces.exceptionReportingService.reportException(error);
            return null;
        }
    }

    @GetMapping(value = "/clp", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessCLPCentersWidget getFitnessCLPWidget(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        WidgetContext widgetContext = new WidgetContext();
        widgetContext.setQueryParams(queryParams);
        return this.fitnessCLPService.getFitnessCLPCentersWidget(userContext, widgetContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/skuPacks"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public PackComparisonPageView getPacksOfCenterAndSKU(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Session session = (Session) request.getAttribute("session");

        WidgetContext widgetContext = new WidgetContext();
        widgetContext.setQueryParams(queryParams);
        PackComparisonPageViewBuilder builder = new PackComparisonPageViewBuilder(interfaces, widgetContext);

        return builder.buildView(userContext, session);
    }

    @RequestMapping(method = RequestMethod.POST,value = {"/healthWearableData"})
    public ActivityResponse SaveWearableData(@RequestBody HealthWearableData healthWearableData) throws Exception{


        UserContext userContext = (UserContext) request.getAttribute("userContext");

        try {

            String userId = userContext.getUserProfile().getUserId();
            log.info("Wearable Data service : {}", userId);
            log.info("Wearable Data service : {}", healthWearableData.toString());
            cfAnalytics.sendEvent(
                    BackgroundWearableTaskEvent.builder()
                    .flag(healthWearableData.getFlag())
                            .platform(healthWearableData.getPlatform())
                            .date(healthWearableData.getDate())
                            .dataUpdatedTime(Instant.now().toString())
                            .classId(healthWearableData.getClassId())
                    .build(), userContext, true,true,false,false);
            BulkSaveActivityPayload payload = new BulkSaveActivityPayload();
            List<SaveActivityPayload> activities = new ArrayList<>();

            SaveActivityPayload activityPayload = new SaveActivityPayload();
            activityPayload.setActivityMsgType("CLASS_WORKOUT_METRIC");
            ObjectMapper mapper = new ObjectMapper();
            activityPayload.setActivityData(mapper.valueToTree(healthWearableData));

            log.info("Wearable Data service : {}",activityPayload.toString());

            activities.add(activityPayload);
            payload.setActivities(activities);


            ActivityResponse activityResponse = interfaces.loggingService.putBulkActivities(userId, payload);

            log.info("Wearable Data service activity response : {}",activityResponse.toString());

            return activityResponse;

        }catch (Exception e){

            log.error("Wearable Data service : {}",e);
            cfAnalytics.sendEvent(
                    BackgroundWearableTaskEvent.builder()
                            .flag(healthWearableData.getFlag())
                            .platform(healthWearableData.getPlatform())
                            .date(healthWearableData.getDate())
                            .dataUpdatedTime(Instant.now().toString())
                            .classId(healthWearableData.getClassId())
                            .build(), userContext, true,true,false,false);
        }

        return null;

    }

    @RequestMapping(method = RequestMethod.POST,value = {"/submitBulkClassWearableData"})
    public ActivityResponse SaveBulkWearableData(@RequestBody List<HealthWearableData> metricsDataList) throws Exception{


        UserContext userContext = (UserContext) request.getAttribute("userContext");

        try {

            String userId = userContext.getUserProfile().getUserId();

            BulkSaveActivityPayload payload = new BulkSaveActivityPayload();
            List<SaveActivityPayload> activities = new ArrayList<>();

            for (HealthWearableData healthWearableData : metricsDataList) {
                SaveActivityPayload activityPayload = new SaveActivityPayload();
                activityPayload.setActivityMsgType("CLASS_WORKOUT_METRIC");
                ObjectMapper mapper = new ObjectMapper();
                activityPayload.setActivityData(mapper.valueToTree(healthWearableData));
                activities.add(activityPayload);
            }

            payload.setActivities(activities);


            ActivityResponse activityResponse = interfaces.loggingService.putBulkActivities(userId, payload);

            return activityResponse;

        }catch (Exception e){
            log.error("Wearable Data service : {}",e);
        }

        return null;

    }

    @RequestMapping(method = RequestMethod.POST, value = {"/postHabit"} ,  produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SaveDataHabitPage saveUserHabitForCult(
            @RequestBody SaveHabitRequestBody saveHabitRequestBody
    ) throws  Exception {
        try {
            UserContext userContext = (UserContext) request.getAttribute("userContext");

            //get all skills for cult
            String query = String.format("tenant.eq:%s", "CULT");
            List<PillarEntryResponse> cultPillars = interfaces.shifuClient.searchPillarByTenentv2(query);
            log.info("Habit building post api cultpilars {}",cultPillars);
            PillarEntryResponse pillar = cultPillars.get(0);
            List<SkillEntryResponse> skills = pillar.getSkills();
            log.info("Habit building post api skills {}",skills);
            //get all current user actions
            String query2 = String.format("tenant.eq:%s;userId.eq:%s;status.eq:%s", "CULT",Long.parseLong(userContext.getUserProfile().getUserId()),"ACTIVE");
            List<UserActionEntry> userActionEntries = interfaces.shifuClient.searchUserActions(query2,0,10,null,null);
            log.info("Habit building post api userActionEntries {}",userActionEntries);
            Map<String, UserActionEntry> userActionMap = new HashMap<>();
            userActionEntries.forEach(userAction -> {
                userActionMap.put(userAction.getSkill().getId().toString(), userAction);
            });

            SaveDataHabitPage updatePage = new SaveDataHabitPage();

            List<UserActionEntry> finalUserActionEntries = new ArrayList<>();
            if(userActionMap.get(saveHabitRequestBody.getHabitID()) != null && !saveHabitRequestBody.getIsSelected()) {
                finalUserActionEntries = userActionEntries.stream().filter(entry -> !entry.getSkill().getId().toString().equals(saveHabitRequestBody.getHabitID())).toList();

                //response to be sent
                updatePage.setHeader("HABIT DELETED");
                updatePage.setSubHeader("We won't be sending reminders for this habit.");
                Action action2 = new Action();
                action2.setActionType(ActionType.MOVE_BACK);
                updatePage.setAction(action2);
                updatePage.setType("delete");

            } else {
                finalUserActionEntries = userActionEntries;
                UserActionEntry currentAction = new UserActionEntry();
                SkillEntry skill = new SkillEntry();
                for(int i=0 ; i<skills.size() ; i++) {
                    if(skills.get(i).getId().toString().equals(saveHabitRequestBody.getHabitID())) {
                        skill.setId(skills.get(i).getId());
                        skill.setType(skills.get(i).getType());
                        List<SkillActionEntry> skillActionResponse = new ArrayList<>();
                        for(int j=0 ; j<skills.get(i).getSkillActions().size() ; j++) {
                            SkillActionResponse curr = skills.get(i).getSkillActions().get(j);
                            SkillActionEntry entry = new SkillActionEntry();
                            entry.setText(curr.getText());
                            entry.setSkillId(curr.getSkillId());
                            skillActionResponse.add(entry);
                        }
                        skill.setSkillActions(skillActionResponse);
                        skill.setName(skills.get(i).getName());
                        skill.setMeta(skills.get(i).getMeta());
                        skill.setDescription(skills.get(i).getDescription());
                        skill.setType(skills.get(i).getType());
                        skill.setTenant(skills.get(i).getTenant());
                        break;
                    }
                }

                String query3 = String.format("tenant.eq:%s;skillId.eq:%s", "CULT",skill.getId());
                SkillAnchorMappingEntry anchorEntry = interfaces.shifuClient.getAnchorsForSkill(query3).get(0);
                currentAction.setAnchor(anchorEntry.getAnchor().getText());


                LocalTime time = getLocalTime(saveHabitRequestBody.getResponse());
                SimpleDateFormat parseFormat = new SimpleDateFormat("HH:mm");
                SimpleDateFormat displayFormat = new SimpleDateFormat("hh:mm a");
                Date curr =parseFormat.parse(time.toString());
                currentAction.setText(skill.getSkillActions().get(0).getText() + " at "+ displayFormat.format(curr));


                currentAction.setSkill(skill);
                Calendar cal = Calendar.getInstance();
                Date expiryDate = new Date();
                cal.setTime(expiryDate);
                cal.add(Calendar.DAY_OF_MONTH, 365);
                currentAction.setExpiryDate(cal.getTime());
                UserActionFrequency  frequency = new UserActionFrequency();
                frequency.setType(FrequencyType.DAILY);

                List<Integer> values= new ArrayList<>(){{
                    add(0);
                    add(1);
                    add(2);
                    add(3);
                    add(4);
                    add(5);
                    add(6);
                }};

                frequency.setValues(values);
                currentAction.setFrequency(frequency);
                currentAction.setScheduledTime(getLocalTime(saveHabitRequestBody.getResponse()));
//                currentAction.setInputType();
                if(saveHabitRequestBody.getResponse().get(2).equals(0)) {
                    currentAction.setTimeOfDay(TimeOfDay.MORNING);
                } else if(saveHabitRequestBody.getResponse().get(2).equals(1)
                        && (saveHabitRequestBody.getResponse().get(0) <= 5 ||
                        saveHabitRequestBody.getResponse().get(0) == 12)) {
                    currentAction.setTimeOfDay(TimeOfDay.AFTERNOON);
                } else {
                    currentAction.setTimeOfDay(TimeOfDay.EVENING);
                }
                currentAction.setType(HabitType.HABIT);
                InputTypeEntry inputEntry = new InputTypeEntry();
                inputEntry.setId(Long.valueOf(1));
                currentAction.setInputType(inputEntry);
                finalUserActionEntries.add(currentAction);


                //response to be sent
                updatePage.setHeader("HABIT CREATED");
                String subHeader = skill.getMeta().get("saveHabitSubHeader") != null
                                    ? (String) skill.getMeta().get("saveHabitSubHeader")
                                   : "We will be sending reminders for this habit.";
                updatePage.setSubHeader(subHeader);
                Action action2 = new Action();
                action2.setActionType(ActionType.MOVE_BACK);
                updatePage.setAction(action2);
                updatePage.setUrl("/image/mem-exp/lottie/Confirmation.json");
                updatePage.setType("create");
                updatePage.setShowNextHabit(true);
            }

            UserCheckInPayload checkInPayload = new UserCheckInPayload();
            checkInPayload.setUserId(Long.parseLong(userContext.getUserProfile().getUserId()));
            checkInPayload.setMotivatorId(Long.parseLong(userContext.getUserProfile().getUserId()));
            checkInPayload.setUserActions(finalUserActionEntries);
            checkInPayload.setTenant("CULT");
            UserCheckInPayload userCheckInPayload = interfaces.shifuClient.saveUserCheckin(checkInPayload);

            return updatePage;
        } catch(Exception err) {
            log.error("Habit Building post api cult error {}",err);
            log.info(String.valueOf(err));
            interfaces.exceptionReportingService.reportException(err);
            return null;
        }
    }

    LocalTime getLocalTime(List<Integer> values) {
        String s = "";
        if(values.get(2) == 1) {
            s += "" + (values.get(0) != 12 ? values.get(0) + 12 : values.get(0)) + ":" +
                    (values.get(1) >=10 ? values.get(1) : "0"+values.get(1)) + ":" + "00";
        } else if(values.get(0) != 12) {
            s += "" + (values.get(0) >=10 ? values.get(0) : "0"+values.get(0)) +
                    ":" +(values.get(1) >=10 ? values.get(1) : "0"+values.get(1))+":" + "00";
        } else {
            s += "" + "00" + ":" +(values.get(1) >=10 ? values.get(1) : "0"+values.get(1))+":" + "00";
        }

        return LocalTime.parse(s);
    }

    @GetMapping(value = {"/centerSearch"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CenterSearchPageView getCenterSearchPageView(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return CenterSearchPageViewBuilder.buildView(userContext, queryParams, interfaces);
    }

    @PostMapping(value = {"/preference/redirection"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RedirectionResponse getRedirectionLink(
            @RequestBody RedirectionRequest redirectionRequest
    ) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Action action = new Action(PreferenceService.DEFAULT_PREFERENCE_DEEPLINK, ActionType.NAVIGATION);
        RedirectionResponse response = RedirectionResponse.builder().action(action).build();
        String userId = userContext.getUserProfile().getUserId();
        if (!userContext.getSessionInfo().getIsUserLoggedIn()) {
            log.info("User not logged in - redirecting to default link {}", PreferenceService.DEFAULT_PREFERENCE_DEEPLINK);
            return response;
        }
        try {
            return this.preferenceService.getRedirectionLinkBasedOnPreference(redirectionRequest, userId);
        } catch (Exception e) {
            String errMsg = e.getMessage();
            log.error("Error in deciding preference for user " + userId, errMsg);
            return response;
        }
    }

    /*
     * local controllers: only applicable for non-production environment
     */

    @RequestMapping(value = "/pack/elite", method = RequestMethod.POST)
    @ResponseBody
    @Profile(value = {"local"})
    public List<OfflineFitnessPack> getElitePacks(@RequestBody(required = false) ElitePackFilter requestBody) throws Exception {
        return interfaces.catalogueServicePMS.getElitePacks(requestBody);
    }

    @RequestMapping(value = "/pms/search", method = RequestMethod.POST)
    @ResponseBody
    @Profile(value = {"local"})
    public List<OfflineFitnessPack> searchPMSPacks(@RequestBody PackSearchRequest requestBody) throws Exception {
        try {
            return interfaces.offlineFitnessPackService.searchPacks(requestBody).get();
        } catch (Exception e) {
            log.error("Error in searching packs from PMS {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    @RequestMapping(value = "/pms/cache/search", method = RequestMethod.POST)
    @ResponseBody
    @Profile(value = {"local"})
    public List<OfflineFitnessPack> searchCachedPMSPacks(@RequestBody PackSearchRequest requestBody) throws Exception {
        try {
            return interfaces.offlineFitnessPackService.searchCachedPacks(requestBody).get();
        } catch (Exception e) {
            log.error("Error in searching packs from PMS {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/credits/transaction"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CreditsTransactionHistoryPageView getCreditsTransaction(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        CreditTransactionPageViewBuilder builder = new CreditTransactionPageViewBuilder();
        return builder.buildView(userContext,queryParams, this.interfaces);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/pastClasses"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PastActivityResponse getPastClassesPaginated(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.pastActivityService.getActivityResponse(this.interfaces, userContext, Integer.parseInt(queryParams.get("offset")));
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/upgrade/membership"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UpgradeMembershipPageView UpgradeMembershipDetail(@RequestParam Map<String, String> queryParams) throws Exception {

        // getting all the data
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Session session = (Session) request.getAttribute("session");
        String membershipServiceId = queryParams.getOrDefault("membershipServiceId", null);
        String productId = queryParams.getOrDefault("productId", null);
        String productType = queryParams.getOrDefault("productType", null);
        String selectedCenterId = queryParams.getOrDefault("selectedCenterId", null);
        String selectedWorkoutId = queryParams.getOrDefault("selectedWorkoutId", null);
        Boolean canUpdateCity = (queryParams.containsKey("canUpdateCity") && Boolean.parseBoolean(queryParams.get("canUpdateCity")));
        String upgradeType = queryParams.getOrDefault("upgradeType", null);

        boolean isPlayMembership = productType != null && productType.equals(ProductType.PLAY.toString());
        if (isPlayMembership && (Objects.isNull(membershipServiceId) || Objects.isNull(productId))) {
            throw new ClientException("Unable to fetch play upgrade details for the user: " + userContext.getUserProfile().getUserId() + ", with queryParams: " + queryParams, ErrorUtil.ErrorCodes.UPGRADE_DETAIL_ERROR);
        }
        // throw error in case membershipServiceId or productId or upgradeType is null
        // throw error in case updateType is not valid
        if (!isPlayMembership && (Objects.isNull(membershipServiceId) || Objects.isNull(productId) || Objects.isNull(upgradeType) || !TransferUpgradePageViewBuilder.validUpgradeTypes.contains(upgradeType))) {
            throw new ClientException("Unable to fetch upgrade Details for the user: " + userContext.getUserProfile().getUserId() + ", with queryParams: " + queryParams, ErrorUtil.ErrorCodes.UPGRADE_DETAIL_ERROR);
        }

        // getting the necessary data from services
        Membership membership = interfaces.membershipService.getMembershipById(Long.parseLong(membershipServiceId), userContext.getUserProfile().getUserId()).get();

        // throw error in case membership is coming null from membership service
        if (Objects.isNull(membership)) {
            throw new ClientException("Membership is coming null for membershipId: " + membershipServiceId + ", user: " + userContext.getUserProfile().getUserId(), ErrorUtil.ErrorCodes.UPGRADE_DETAIL_ERROR);
        }

        // throw error in case userId present for membership is different from the current userId
        if (!Objects.equals(membership.getUserId(), userContext.getUserProfile().getUserId())) {
            throw new ClientException("UserId mismatch for membership: " + membershipServiceId + ", user: " + userContext.getUserProfile().getUserId(), ErrorUtil.ErrorCodes.UPGRADE_DETAIL_ERROR);
        }

        if (isPlayMembership){
            return PlayUpgradePageBuilder.upgradePlayPageBuildView(userContext, session, productId, upgradeType, canUpdateCity, membership, interfaces);
        } else
            return TransferUpgradePageViewBuilder.upgradePageBuildView(userContext, session, productId, upgradeType, canUpdateCity, membership, interfaces);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/transfer/getDetails"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GetTransferDetailPageView GetTransferMembershipFormDetail(@RequestParam Map<String, String> queryParams) throws Exception {
        try {
            // get all the data
            UserContext userContext = (UserContext) request.getAttribute("userContext");
            String membershipServiceId = queryParams.getOrDefault("membershipServiceId", null);

            // base cases
            if (Objects.isNull(membershipServiceId)) {
                throw new Exception("QueryParams does not contains membershipServiceId");
            }

            // page view builder
            return TransferUpgradePageViewBuilder.getTransferDetailPageViewBuilder(membershipServiceId, userContext, interfaces);
        } catch (Exception e) {
            // throw exception in case of any error
            interfaces.exceptionReportingService.reportException(e);
            throw new Exception("Unable to fetch Get transfer Detail page info", e);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/transfer/membership"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TransferMembershipPageView GetTransferMembershipDetail(@RequestParam Map<String, String> queryParams) throws Exception {
        // get all the data
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String membershipServiceId = queryParams.getOrDefault("membershipServiceId", null);
        String cityIdTo = queryParams.getOrDefault("cityIdTo", null);
        String centerServiceIdTo = queryParams.getOrDefault("centerServiceIdTo", null);

        // base cases
        if (Objects.isNull(membershipServiceId) || Objects.isNull(cityIdTo) || Objects.isNull(centerServiceIdTo)) {
            throw new Exception("QueryParams is not proper, queryParams present: " + JSON.toString(queryParams));
        }

        // page view builder
        return TransferUpgradePageViewBuilder.transferPageBuildView(userContext, membershipServiceId, cityIdTo, Long.valueOf(centerServiceIdTo), interfaces);
    }

    @RequestMapping(value = "/transition/membership", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Profile(value = {"local"})
    public PackTransitionPageView packTransition(
            @RequestParam Map<String, String> queryParams
    ) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        PackTransitionPageViewBuilder builder = new PackTransitionPageViewBuilder();
        return builder.packTransitionPageView(queryParams, interfaces, userContext);
    }

    @RequestMapping(value = "/transition/details", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Profile(value = {"local"})
    public PackTransitionPageView getPackTransitionDetails(
            @RequestParam Map<String, String> queryParams
    ) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        GetPackTransitionDetailsPageViewBuilder builder = new GetPackTransitionDetailsPageViewBuilder();
        return builder.getPackTransitionDetailsPageView(queryParams, interfaces, userContext);
    }

}
