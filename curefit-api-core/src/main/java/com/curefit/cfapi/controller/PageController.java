package com.curefit.cfapi.controller;

import com.curefit.cfapi.builder.vm.BuildWidgetResponse;
import com.curefit.cfapi.builder.vm.PageBuilder;
import com.curefit.cfapi.builder.vm.WidgetBuilder;
import com.curefit.cfapi.cache.VMCache;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.MdcTag;
import com.curefit.cfapi.model.internal.vm.page.PageContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.model.mongo.WidgetInstance;
import com.curefit.cfapi.pojo.vm.Image.Image;
import com.curefit.cfapi.pojo.vm.page.PageView;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.UserService;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.community.pojo.APIStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.curefit.cfapi.constants.StringConstants.*;

@RestController
@Slf4j
@RequestMapping(value = "page")
public class PageController {

    private final VMCache vmCache;
    private final PageBuilder pageBuilder;
    private ServiceInterfaces serviceInterfaces;
    private HttpServletRequest request;
    private WidgetBuilder widgetBuilder;
    private RestTemplate restTemplate;
    final ExceptionReportingService exceptionReportingService;
    private UserService userService;

    private final Set<String> supportedPageIds = new HashSet<String>() {{
        add("fitnesspacksupport");
        add("hometab");
        add("homefit");
        add("testhomepage");
        add("home-intl");
        add("tl-home");
        add("tl-trainers");
        add("tl-collections");
        add("TV_Fitness");
        add("TV_Mindfulness");
        add("TV_FitnessIntl");
        add("TV_MindfulnessIntl");
        add(FITNESS_HUB_PAGE_ID);
        add("CultPassBlackSKU");
        add("CultPassGoldSKU");
        add("CultPassLiveSKU");
        add("SKUPurchasePage");
        add("transformcoach");
        add("storeaurora");
        add("storeclp");
        add("cultsport");
        add("cultrowclp");
        add("cultrowlist");
        add("discover");
        add("cp_coach_clp");
        add("support");
        add("cultsport-pdp");
        add("hometab-hyper-p");
        add("CultPassFitsoSKU");
        add("CultPassPlayAndLite");
        add("CultPassPlayAndSport");
        add("playhub_slp");
        add("transform_plus_pre");
        add("transform_pre");
        add("transform_maintenance");
        add("cultbootcamp");
        add("cultsport-wishlist");
        add(PLAY_HUB_PAGE_ID);
        add("fitnesshub_nux_center_level");
        add("cultpassMYCULT-Member");
        add("fitnesshub_nux");
        add("cultpassELITE-Member");
        add("cultpassPRO-Member");
        add("cultpassLIVE-Members_NEW");
        add("LiveHomeCLP_Trial_Flutter");
        add("Guidance_for_LIVE_trial_users");
        add("cultpassPLAY-Member");
        add("xp_bottom_sheet");
        add("membership_pause_screen");
        add("post_membership_pause");
        add("hpsports");
        add("enterpriseclp");
        add("what_to_carry");
        addAll(Constants.CULT_UNBOUND_PAGE_ID_TO_WORKOUT_ID_MAP.keySet());
    }};

    @Value("${external.cfapi-node.host}")
    String cfApiNodeHost;

    @Value("${external.cfapi-node.port}")
    int cfApiNodePort;

    @Autowired
    public PageController(
            VMCache vmCache,
            PageBuilder pageBuilder,
            ServiceInterfaces serviceInterfaces,
            HttpServletRequest request,
            ExceptionReportingService exceptionReportingService,
            WidgetBuilder widgetBuilder,
            RestTemplate restTemplate,
            UserService userService
    ) {
        this.vmCache = vmCache;
        this.pageBuilder = pageBuilder;
        this.serviceInterfaces = serviceInterfaces;
        this.request = request;
        this.widgetBuilder = widgetBuilder;
        this.restTemplate = restTemplate;
        this.userService = userService;
        this.exceptionReportingService = exceptionReportingService;

    }

    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST} , value = {"/{pageId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PageView> findById(@PathVariable String pageId, @RequestParam Map<String, String> queryParams, @RequestBody(required = false) String body, HttpMethod method, HttpServletRequest request) throws Exception {
        MDC.put(MdcTag.PAGE_ID.toString(), pageId);
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        // ToDo:: Remove this cultsport-pdp-partial call after analysing the perf
        if (AppUtil.isCultSportWebApp(userContext)) {
            if (pageId.contains("cultsport-pdp-partial")) {
                pageId = "cultsport-pdp";
            }
        }

        if (this.isPageImplemented(userContext, pageId)) {
            // Storing the request body in user context for reference in widget build view
            if (body != null) {
                userContext.setReqbody(body);
            }
            userContext.setRequestQueryParams(queryParams);
            PageContext pageContext = new PageContext();
            pageContext.setQueryParams(queryParams);
            pageContext.setPageId(pageId);
            ResponseEntity<PageView> ret = new ResponseEntity<>(this.pageBuilder.getPage(pageId, userContext, pageContext), HttpStatus.OK);
            return ret;
        } else {
            return this.callNodePageImpl(request, method, body);
        }
    }


    @RequestMapping(method = RequestMethod.POST,value={"/toggleAutomatedBooking"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public APIStatus toggleAutomatedBooking(@RequestBody Object payload) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = userContext.getUserProfile().getUserId();
        try {
            Boolean response = serviceInterfaces.cultService.toggleKickstarterPlanActive(userId);
            log.error("UserService::Automatedbooking toggle button response " + response);
            return new APIStatus(response);
        } catch (Exception error) {
            log.error("UserService::Automatedbooking toggle button failed " + error.toString());
            return new APIStatus(false);
        }
    }

    private boolean isPageImplemented(UserContext userContext, String pageId) {
        if (pageId.equals(STORE_CLP_PAGE_ID)) {
            // we are only serving the flutter storeclp from cf-java
            return AppUtil.isFromFlutterAppFlow(userContext);
        }
        if (supportedPageIds.contains(pageId) || pageId.toLowerCase().contains("sale") || AppUtil.isCultSportWebApp(userContext)) {
            return true;
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/widgets/ids"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<BuildWidgetResponse> getWidgetsByIds(@RequestBody() String[] widgetIds, @RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        userContext.setRequestQueryParams(queryParams);
        WidgetContext widgetContext = new WidgetContext();
        widgetContext.setQueryParams(queryParams);
        if (queryParams != null && queryParams.containsKey("pageId")) {
            MDC.put(MdcTag.PAGE_ID.toString(), queryParams.get("pageId"));
            widgetContext.getPageContext().setPageId(queryParams.get("pageId"));
        }
        return new ResponseEntity<>(this.widgetBuilder
                .buildWidgets(Arrays.asList(widgetIds), userContext,
                widgetContext).get(), HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/widget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<BaseWidget> getWidgetById(@RequestParam Map<String, String> queryParams) throws Exception {
        if (queryParams != null && queryParams.get("widgetId") != null) {
            UserContext userContext = (UserContext) request.getAttribute("userContext");
            userContext.setRequestQueryParams(queryParams);
            String widgetId = queryParams.get("widgetId");
            WidgetContext widgetContext = new WidgetContext();
            widgetContext.setWidgetId(widgetId);
            widgetContext.setQueryParams(queryParams);

            List<BaseWidget> widgets = this.widgetBuilder.buildWidget(widgetId, userContext, widgetContext);
            BaseWidget widget = CollectionUtils.isNotEmpty(widgets) ? widgets.get(0) : null;
            return new ResponseEntity<>(widget, HttpStatus.OK);
        }
        return new ResponseEntity<>(null, HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/widgetPreview", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getWidgetPreview(@RequestBody WidgetInstance widgetInstance, @RequestParam Map<String, String> queryParams) throws Exception {
        if (widgetInstance == null || StringUtils.isEmpty(widgetInstance.getWidgetId())) {
            return new ResponseEntity<>("{}", HttpStatus.OK);
        }
        WidgetContext widgetContext = new WidgetContext();
        widgetContext.setWidgetId(widgetInstance.getWidgetId());
        widgetContext.setQueryParams(queryParams);
        widgetContext.putQueryParam("widgetId", widgetInstance.getWidgetId());
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        List<BaseWidget> widgets = this.widgetBuilder.buildWidget(widgetInstance, userContext, widgetContext);
        return new ResponseEntity<>(CollectionUtils.isNotEmpty(widgets) ? widgets.get(0) : "{}", HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/images/format"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<Image>> getMerchandiseFormatImages(@RequestBody() Map data) throws Exception {
        return new ResponseEntity<>(this.vmCache.getFormatImages((data.get("productType")).toString(),(data.get("formatIds")).toString(), (data.get("uiTarget")).toString()), HttpStatus.OK);
    }

    ResponseEntity callNodePageImpl(HttpServletRequest request, HttpMethod method, String body) throws URISyntaxException {
        String requestUrl = request.getRequestURI();

        // Construct headers
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.set(headerName, request.getHeader(headerName));
        }
        String authToken = (String) request.getAttribute("authToken");
        if (ObjectUtils.isNotEmpty(authToken)) {
            headers.set("at", authToken);
        }
        headers.set("Host", cfApiNodeHost);

        // Construct pass-through request
        URI uri = new URI("http", null, cfApiNodeHost, cfApiNodePort, "/nodeimpl" + requestUrl, request.getQueryString(), null);
        HttpEntity<String> httpEntity = new HttpEntity<>(body, headers);

        try {
            var response = restTemplate.exchange(uri, method, httpEntity, String.class);
            return ResponseEntity.status(response.getStatusCode())
                    .body(response.getBody());
        } catch (HttpStatusCodeException e) {
            this.exceptionReportingService.reportException(e);
            return ResponseEntity.status(e.getRawStatusCode())
                    .body(e.getResponseBodyAsString());
        }

    }
}