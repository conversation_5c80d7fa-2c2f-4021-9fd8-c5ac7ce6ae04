package com.curefit.cfapi.controller;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.atom.CFGenericAlertDialog;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.fitness.BestPackService;
import com.curefit.cfapi.service.gymfit.GymCheckinService;
import com.curefit.cfapi.service.gymfit.GymfitService;
import com.curefit.cfapi.service.gymfit.LuxGymService;
import com.curefit.cfapi.view.viewbuilders.gymfit.CenterWalkin;
import com.curefit.cfapi.view.viewmodels.gymfit.CenterScheduleWalkinPageView;
import com.curefit.cfapi.view.viewmodels.gymfit.CentersPageView;
import com.curefit.cfapi.view.viewmodels.gymfit.GymCheckinView;
import com.curefit.cfapi.view.viewmodels.gymfit.LuxGymPageView;
import com.curefit.cfapi.view.viewmodels.gymfit.NearbyCenterCreditsView;
import com.curefit.gymfit.dtos.CancelCenterVisitRequest;
import com.curefit.pms.enums.ProductSubType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.curefit.cfapi.pojo.app.action.ActionType.MULTI_POP_THEN_NAVIGATION;

@RestController
@Slf4j
@RequestMapping(value = "gymfit")
public class GymfitController {
    private final HttpServletRequest request;
    private final GymfitService gymfitService;

    private final LuxGymService luxGymService;

    private final GymCheckinService gymCheckinService;

    private final ServiceInterfaces serviceInterfaces;
    private final BestPackService bestPackService;

    @Autowired
    public GymfitController(
            HttpServletRequest request,
            GymfitService gymfitService,
            ServiceInterfaces serviceInterfaces,
            LuxGymService luxGymService,
            GymCheckinService gymCheckinService,
            BestPackService bestPackService
        ) {
        this.request = request;
        this.gymfitService = gymfitService;
        this.luxGymService = luxGymService;
        this.gymCheckinService = gymCheckinService;
        this.serviceInterfaces = serviceInterfaces;
        this.bestPackService = bestPackService;
    }

    @GetMapping(value = {"/search"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CentersPageView getCentersPageView(@RequestParam Map<String, String> queryParams,
                                              @RequestParam(value = "skuType", required = false) String skuType,
                                              @RequestParam(value = "centerType", required = false) String[] centerTypes) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.gymfitService.getCentersPageView(userContext, queryParams, centerTypes);
    }

    @GetMapping(value = {"/nearbycredits"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public NearbyCenterCreditsView getNearbyCreditsPageView(@RequestParam Map<String, String> queryParams,
        @RequestParam(value = "centerType", required = false) String[] centerTypes) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.gymfitService.getNearbyCreditsPageView(userContext, queryParams, centerTypes);
    }

    @GetMapping(value = {"/luxdiscovery"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public LuxGymPageView getLuxCentersPageView(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.luxGymService.getLuxCentersPageView(userContext, queryParams);
    }
    @GetMapping(value = {"/universalCheckin"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public GymCheckinView getUniversalGymCheckinView(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        WidgetContext widgetContext = new WidgetContext();
        widgetContext.setQueryParams(queryParams);
        return this.gymCheckinService.getUniversalGymCheckinView(userContext, widgetContext);
    }

    @PostMapping(value = {"/bestPack"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Action getBestProPackForUser(@RequestParam Map<String, String> queryParams) throws Exception {
        String startDate = queryParams.get("startDate");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return bestPackService.getBestGymfitPack(userContext, ProductSubType.GENERAL,startDate);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/getGymOnboardingAction"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Action checkOnboardingCenter(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.gymfitService.getGymOnboardingAction(userContext, queryParams);
    }

    @GetMapping(value = {"/walkin"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public CenterScheduleWalkinPageView getCenterWalkinView(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return new CenterWalkin().buildCenterScheduleWalkinPageView(serviceInterfaces, queryParams, userContext);
    }

    @GetMapping(value = {"/walkin/schedule"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Action getCenterWalkinScheduleView(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return new CenterWalkin().ScheduleWalkinCompleteActionBuilder(serviceInterfaces, userContext, queryParams);
    }

    @PostMapping(value = {"/walkin/cancel"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Action cancelCenterWalkinSchedule(@RequestBody CancelCenterVisitRequest cancelCenterVisitRequest) throws Exception {
        return new CenterWalkin().cancelCenterSchedule(serviceInterfaces, cancelCenterVisitRequest);
    }

}