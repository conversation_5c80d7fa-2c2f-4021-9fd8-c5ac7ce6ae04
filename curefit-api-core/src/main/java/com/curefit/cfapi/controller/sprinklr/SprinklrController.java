package com.curefit.cfapi.controller.sprinklr;

import com.curefit.center.dtos.CenterEntry;
import com.curefit.center.dtos.CenterSearchFilters;
import com.curefit.center.dtos.CenterSearchRequestV2;
import com.curefit.cfapi.constants.SprinklrConstants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.UserService;
import com.curefit.cfapi.service.sprinklr.SprinklrService;
import com.curefit.cfapi.util.StringUtil;
import com.curefit.cfapi.view.viewmodels.sprinklr.activePacks.ActivePackResponse;
import com.curefit.cfapi.view.viewmodels.sprinklr.center.Center;
import com.curefit.cfapi.view.viewmodels.sprinklr.center.SprinklrCenterSearchRequest;
import com.curefit.cfapi.view.viewmodels.sprinklr.sessions.SessionsResponse;
import com.curefit.cfapi.view.viewmodels.sprinklr.user.UserDetails;
import com.curefit.cfapi.view.viewmodels.sprinklr.user.UserDetailsRequest;
import com.curefit.cfapi.view.viewmodels.sprinklr.user.UserResponse;
import com.curefit.cfapi.view.viewmodels.sprinklr.webhook.SprinklrWebhookResponse;
import com.curefit.common.data.exception.BaseException;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrCommentWebhookRequest;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrMessageWebhookRequest;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrTicketWebhookRequest;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.curefit.userservice.pojo.response.UsersResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequiredArgsConstructor
@Validated
@RequestMapping(value = "/sprinklr/customerSupport")
public class SprinklrController {

    private final HttpServletRequest request;
    private final UserService userService;
    private final SprinklrService sprinklrService;
    private final ServiceInterfaces interfaces;


    @GetMapping("/sessions")
    @ResponseBody
    public SessionsResponse getSessionsInfo(@RequestParam Map<String, String> queryParams) throws Exception {
        return this.sprinklrService.getSessionsInfo(queryParams);
    }

    @GetMapping("/activePacks")
    @ResponseBody
    public ActivePackResponse getActivePacks(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.sprinklrService.getActivePacksInfo(userContext, queryParams, false);
    }

    @GetMapping("/fetchUserDetails/{id}")
    public UserResponse fetchUserDetailsByUserId(@PathVariable(value = "id") String userId) throws Exception {
        return this.sprinklrService.getUserDetailsResponse(request, userId);
    }

    @GetMapping("/fetchUserDetailsByEmail/{email}")
    public ResponseEntity<?> fetchUserDetailsByEmail(@PathVariable(value = "email") String email) throws Exception {
        UsersResponse usersResponse = this.userService.getUserByEmail(URLDecoder.decode(email, StandardCharsets.UTF_8));
        UserEntry userEntry = usersResponse.getUsers().get(email);
        if (ObjectUtils.isEmpty(userEntry)) {
            String errorMsg = String.format("User with email: %s not found in cult system", email);
            log.error(errorMsg);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorMsg);
        }
        return new ResponseEntity<>(this.sprinklrService.getUserDetailsResponse(request, String.valueOf(userEntry.getId())), HttpStatus.OK);
    }

    @GetMapping("/fetchCenterById/{id}")
    public Center fetchCenterById(@PathVariable("id") Long centerServiceRefId) throws BaseException, ExecutionException, InterruptedException {
        CenterEntry centerDetails = this.interfaces.centerService.getCenterDetails(centerServiceRefId, true, Collections.emptyMap(), null).get();
        Center centerDetailsResponse = this.sprinklrService.makeCenterPayload(centerDetails);
        return centerDetailsResponse;
    }

    @PostMapping("/searchCenters")
    public List<Center> searchCenters(@RequestBody SprinklrCenterSearchRequest sprinklrCenterSearchRequest) throws BaseException, ExecutionException, InterruptedException {
        CenterSearchRequestV2 centerSearchRequestV2 = new CenterSearchRequestV2();
        if (sprinklrCenterSearchRequest.getReferenceCenterId() != null) {
            // find centers near given center id
            CenterEntry centerEntry = this.interfaces.centerService.getCenterDetails(sprinklrCenterSearchRequest.getReferenceCenterId(), true, Collections.emptyMap(), null).get();
            centerSearchRequestV2.setLatitude(centerEntry.getLatitude());
            centerSearchRequestV2.setLongitude(centerEntry.getLongitude());
            centerSearchRequestV2.setRadius((double) (50 * 1000D));
            centerSearchRequestV2.setSortBy(CenterSearchFilters.SortBy.DISTANCE);
        } else {
            // find center by text search
            if (!StringUtil.isNullOrEmpty(sprinklrCenterSearchRequest.getSearchText())) {
                centerSearchRequestV2.setSearchStr(sprinklrCenterSearchRequest.getSearchText());
            }
            log.info("[SPRINKLR] Test data: {} {}", centerSearchRequestV2, sprinklrCenterSearchRequest.getCenterTypes().get(0));
            log.info("[SPRINKLR] Test data1: {}", SprinklrConstants.SPRINKLR_CENTER_TYPE_MAP.get(sprinklrCenterSearchRequest.getCenterTypes().get(0)));
            if (!CollectionUtils.isEmpty(sprinklrCenterSearchRequest.getCenterTypes()) && SprinklrConstants.SPRINKLR_CENTER_TYPE_MAP.get(sprinklrCenterSearchRequest.getCenterTypes().get(0)) != null) {
                centerSearchRequestV2.setTypes(SprinklrConstants.SPRINKLR_CENTER_TYPE_MAP.get(sprinklrCenterSearchRequest.getCenterTypes().get(0)));
            }
            log.info("[SPRINKLR] Test data: {} {}", centerSearchRequestV2, sprinklrCenterSearchRequest.getCenterTypes().get(0));
        }

        List<CenterEntry> centers = this.interfaces.centerService.searchAllCenters(centerSearchRequestV2, Collections.emptyMap(), null).get();
        List<Center> centersResponse = new ArrayList<>();
        for (CenterEntry centerData : centers) {
            centersResponse.add(this.sprinklrService.makeCenterPayload(centerData));
        }
        return centersResponse;
    }

    @PostMapping("/caseUpdateWebhook")
    public SprinklrWebhookResponse caseUpdateWebhook(@RequestBody JsonNode webhookPayload) {
        if (Objects.isNull(webhookPayload) || webhookPayload.isEmpty()) {
            log.info("sprinklr case webhook is empty");
        } else {
            SprinklrTicketWebhookRequest sprinklrTicketWebhookRequest = new ObjectMapper().convertValue(webhookPayload, new TypeReference<>() {
            });
            this.sprinklrService.processSprinklrWebhook(sprinklrTicketWebhookRequest);
        }
        return SprinklrWebhookResponse.builder().acknowledgement(true).message("Successfully").build();
    }

    @PostMapping("/commentWebhook")
    public SprinklrWebhookResponse commentWebhook(@RequestBody JsonNode webhookPayload) {
        if (Objects.isNull(webhookPayload) || webhookPayload.isEmpty()) {
            log.info("sprinklr Comment webhook is empty");
        } else {
            SprinklrCommentWebhookRequest sprinklrCommentWebhookRequest = new ObjectMapper().convertValue(webhookPayload, new TypeReference<>() {
            });
            this.sprinklrService.processSprinklrCommentWebhook(sprinklrCommentWebhookRequest);
        }
        return SprinklrWebhookResponse.builder().acknowledgement(true).message("Successfully").build();
    }

    @PostMapping("/messageWebhook")
    public SprinklrWebhookResponse messageWebhook(@RequestBody JsonNode webhookPayload) {
        if (Objects.isNull(webhookPayload) || webhookPayload.isEmpty()) {
            log.debug("sprinklr message webhook is empty");
        } else {
            SprinklrMessageWebhookRequest sprinklrMessageWebhookRequest = new ObjectMapper().convertValue(webhookPayload, new TypeReference<>() {
            });
            this.sprinklrService.processSprinklrMessageWebhook(sprinklrMessageWebhookRequest);
        }
        return SprinklrWebhookResponse.builder().acknowledgement(true).message("Successfully").build();
    }

    @GetMapping("/userDetails")
    public UserDetails getUserDetails(@ModelAttribute(value = "userDetailsRequest") @Valid UserDetailsRequest userDetailsRequest) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.sprinklrService.getUserDetails(userDetailsRequest, userContext);
    }

}
