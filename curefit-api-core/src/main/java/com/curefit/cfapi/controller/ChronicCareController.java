package com.curefit.cfapi.controller;

import com.curefit.albus.common.BundleSellableProduct;
import com.curefit.albus.request.devicePhleboTask.InputFieldsConfig;
import com.curefit.albus.request.devicePhleboTask.PhleboMarkActionRequest;
import com.curefit.albus.request.devicePhleboTask.PhleboTaskSearchRequest;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.AssetResponse;
import com.curefit.albus.response.ConsultationSummaryResponse;
import com.curefit.albus.response.device.PhleboActions;
import com.curefit.albus.response.device.PhleboTask;
import com.curefit.albus.response.device.PhlebotomistUser;
import com.curefit.cfapi.dto.InAppCGMReading;
import com.curefit.cfapi.dto.PatientCGMReadingMode;
import com.curefit.cfapi.dto.PatientInterventionLoggingRequest;
import com.curefit.cfapi.dto.sugarfit.*;
import com.curefit.cfapi.model.internal.chroniccare.*;
import com.curefit.cfapi.model.internal.chroniccare.sfbadges.SfBadgeEntry;
import com.curefit.cfapi.model.internal.exception.BadRequestException;
import com.curefit.cfapi.model.internal.userinfo.SugarfitAppLanguagePreference;
import com.curefit.cfapi.model.internal.userinfo.TabType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.chroniccare.*;
import com.curefit.cfapi.pojo.chroniccare.actvitylog.SfActivityLogPayload;
import com.curefit.cfapi.pojo.chroniccare.actvitylog.SfCreateDishPayload;
import com.curefit.cfapi.pojo.chroniccare.actvitylog.SfCreateFavMealPayload;
import com.curefit.cfapi.pojo.chroniccare.actvitylog.SfMedication;
import com.curefit.cfapi.pojo.chroniccare.capi.CAPIRequest;
import com.curefit.cfapi.pojo.chroniccare.challenges.SfChallengeEntrySubmissionData;
import com.curefit.cfapi.pojo.chroniccare.challenges.SfChallengeUserEntry;
import com.curefit.cfapi.pojo.chroniccare.daNux.DANuxDataUpdatePayload;
import com.curefit.cfapi.pojo.chroniccare.diagnosticteststore.SfDiagnosticStoreTest;
import com.curefit.cfapi.pojo.chroniccare.experiencecenter.SfExperienceCenterKSSaveRequest;
import com.curefit.cfapi.pojo.chroniccare.freemium.FreemiumItemsRequest;
import com.curefit.cfapi.pojo.chroniccare.support.*;
import com.curefit.cfapi.pojo.chroniccare.support.automation.CSChat;
import com.curefit.cfapi.pojo.chroniccare.support.automation.CSConversation;
import com.curefit.cfapi.pojo.chroniccare.support.automation.TicketReplyRequest;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.UserService;
import com.curefit.cfapi.service.chroniccare.CAPIService;
import com.curefit.cfapi.service.chroniccare.ChronicCareService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.view.viewbuilders.chroniccare.PollResultsData;
import com.curefit.cfapi.view.viewbuilders.chroniccare.activitylog.meallogging.SfMealActivityDetailPageView;
import com.curefit.cfapi.view.viewbuilders.chroniccare.activitylog.meallogging.SfMealSlotResponse;
import com.curefit.cfapi.view.viewmodels.SupportPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.ActivityCustomDishPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.ActivityMealFavouritesPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.ActivityMealLogPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.ActivitySearchPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.meallogging.MealActivitySearchPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.blucon.SfBluconDetailsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.challenges.SfChallengeDetailsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.challenges.SfChallengeLeaderboardPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.challenges.SfChallengesListPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.diagnosticstoretest.SfDiagnosticTDPView;
import com.curefit.cfapi.view.viewmodels.chroniccare.diagnosticstoretest.SfDiagnosticTestInstructionView;
import com.curefit.cfapi.view.viewmodels.chroniccare.diagnosticstoretest.SfDiagnosticTestSuccessPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.ecommerce.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.experiencecenter.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.experiencecentre.FBVExperienceHistoryPage;
import com.curefit.cfapi.view.viewmodels.chroniccare.medicalRecords.SfConsultationDetailPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.medicalRecords.SfConsultationsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.medicalRecords.SfReportsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.nux.NuxPreferencesPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.nux.PackStartDateSelection;
import com.curefit.cfapi.view.viewmodels.chroniccare.nux.SetupJourneyPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.phleboApp.PhleboAppCaptureTaskPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.phleboApp.PhleboAppHomePageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.phleboApp.PhleboAppInventoryPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.polls.SfPollsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.renewal.SfCoachCelebrationPage;
import com.curefit.cfapi.view.viewmodels.chroniccare.renewal.SfRenewalUserReportPage;
import com.curefit.cfapi.view.viewmodels.chroniccare.renewal.SfUserReportCongratsPage;
import com.curefit.cfapi.view.viewmodels.chroniccare.sales.SfMegaSalesCLP;
import com.curefit.cfapi.view.viewmodels.chroniccare.sfliteapp.SfLiteAppHomePageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.smartglucometer.SmartGlucometerDashboardPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.smartglucometer.SmartGlucometerOnboardingPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.smartglucometer.SmartGlucometerTimelinePageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.smartscale.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.support.SfSupportPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.support.SfSupportTicketsListPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.support.SfTicketDetailsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.support.automation.SfCSConversationPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.vitals.SfVitalMetricDetailsPageViewV2;
import com.curefit.cfapi.view.viewmodels.digitalApp.DAFreemiumNuxPageView;
import com.curefit.cfapi.view.viewmodels.fbv.FBVOnboardingRequest;
import com.curefit.cfapi.view.viewmodels.fbv.FBVOnboardingResponse;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.*;
import com.curefit.cfapi.widgets.chroniccare.activitylog.SfActivityDishItemWidget;
import com.curefit.cfapi.widgets.chroniccare.challenges.SfChallengeCardWidget;
import com.curefit.cfapi.widgets.chroniccare.ecommerce.SfEComThingsToBuyListWidget;
import com.curefit.cfapi.widgets.chroniccare.freemium.FreemiumHomeTrendingEventWidget;
import com.curefit.cfapi.widgets.chroniccare.freemium.FreemiumSearchHistoryItemWidget;
import com.curefit.cfapi.widgets.chroniccare.freemiumdiscover.SfDiscoverBlogCardWidget;
import com.curefit.cfapi.widgets.chroniccare.sfexperience.SfExperienceFBVWidget;
import com.curefit.cfapi.widgets.chroniccare.support.SfSupportRecentTicketsWidget;
import com.curefit.cfapi.widgets.chroniccare.wellness.SfWellnessLiveSessionsWidgetView;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.common.data.model.entity.Gender;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.converse.pojo.RoomMetricPojos.RoomMetricsRequest;
import com.curefit.ehr.exception.EHRClientException;
import com.curefit.metricservice.exceptions.MetricClientException;
import com.curefit.ollivander.common.exception.OllivanderClientException;
import com.curefit.plan.models.ScheduledPlanItem;
import com.curefit.pojo.PatientActivityLoggingRequest;
import com.curefit.pojo.PatientActivityLoggingResponse;
import com.curefit.polaris.exception.PolarisClientException;
import com.curefit.sfalbus.request.phlebo_task.*;
import com.curefit.sfalbus.response.CGMStat;
import com.curefit.shifu.pojo.UserActivityEntry;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.curefit.userservice.pojo.response.UsersResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.sugarfit.ambrosia.ActivityComparisonRequest;
import com.sugarfit.ambrosia.pojo.DishEntry;
import com.sugarfit.ambrosia.pojo.UserDishSuggestionEntry;
import com.sugarfit.ambrosia.pojo.UserMealPlanFeedback;
import com.sugarfit.ambrosia.pojo.UserMealPlanState;
import com.sugarfit.ambrosia.pojo.external.PatientActivityLoggingCompareEntry;
import com.sugarfit.ambrosia.pojo.masterData.FoodgroupEntry;
import com.sugarfit.ambrosia.pojo.masterData.MeasurementUnitEntry;
import com.sugarfit.ambrosia.pojo.pdf.UserMealPlanPdfResponse;
import com.sugarfit.catalog.pojo.Entry.UserInteractionEntry;
import com.sugarfit.catalog.pojo.request.UserChapterTrackingRequest;
import com.sugarfit.catalog.pojo.response.ExperiencePageCGMDataResponse;
import com.sugarfit.catalog.pojo.response.InfoBitesResponse;
import com.sugarfit.challenges.pojo.ChallengesEntry;
import com.sugarfit.chs.pojo.BaseResponse;
import com.sugarfit.chs.pojo.*;
import com.sugarfit.chs.pojo.badge.UserBadgeProgressEntry;
import com.sugarfit.chs.pojo.blucon.BluconEventEntry;
import com.sugarfit.chs.pojo.careplix.CareplixFaceBasedVitalsRequest;
import com.sugarfit.chs.pojo.careplix.CareplixScanStatusUpdateRequest;
import com.sugarfit.chs.pojo.cgminsights.BgInsightDetailedResponse;
import com.sugarfit.chs.pojo.cgminsights.BgInsightsSummary;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalUploadRequest;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalsRequest;
import com.sugarfit.chs.pojo.fitnessData.RawHealthDataEntry;
import com.sugarfit.chs.pojo.glucometerReadings.GlucometerReadingEntry;
import com.sugarfit.chs.pojo.mbscore.MetabolicScoreResponse;
import com.sugarfit.chs.pojo.smartScale.SmartScaleDataUploadRequest;
import com.sugarfit.chs.pojo.userProfile.KickstarterAssessment;
import com.sugarfit.chs.pojo.userProfile.UserProfileEntry;
import com.sugarfit.experiment.pojo.*;
import com.sugarfit.experiment.pojo.result.UserExperimentResult;
import com.sugarfit.fitness.pojo.UserSessionLogEntry;
import com.sugarfit.chs.pojo.DefaultSuccessResponse;
import com.sugarfit.indus.pojo.OrderCart;
import com.sugarfit.indus.reponse.OfferResponse;
import com.sugarfit.indus.reponse.OrderDetailedResponse;
import com.sugarfit.indus.reponse.ProductResponse;
import com.sugarfit.indus.reponse.ServiceabilityCheckResponse;
import com.sugarfit.indus.request.ProductSearchRequest;
import com.sugarfit.indus.request.ReviewRequest;
import com.sugarfit.lexicon.request.*;
import com.sugarfit.lexicon.response.*;
import com.sugarfit.lms.dtos.EverWebinarJoinStatusResponse;
import com.sugarfit.lms.dtos.LeadSquaredPlainRequest;
import com.sugarfit.lms.dtos.WebinarJoinRequest;
import com.sugarfit.lms.dtos.WebinarJoinResponse;
import com.sugarfit.lms.entry.LeadEntry;
import com.sugarfit.lms.referral.entry.*;
import com.sugarfit.lms.request.CustomRenewalPackRequest;
import com.sugarfit.lms.request.RPOrderCreateRequest;
import com.sugarfit.lms.request.RPOrderStatusUpdateRequest;
import com.sugarfit.lms.response.RPOrderCreateResponse;
import com.sugarfit.lms.referral.pojo.ReferralSummary;
import com.sugarfit.logging.enums.ActivityType;
import com.sugarfit.logging.pojo.BaseActivityLogEntry;
import com.sugarfit.logging.pojo.request.ActivityLogComparisonRequest;
import com.sugarfit.logging.pojo.response.ComparableActivityLogResponse;
import com.sugarfit.nest.pojo.*;
import com.sugarfit.nest.request.SmartBotPostEntry;
import com.sugarfit.poll.pojo.CompletePollEntry;
import com.sugarfit.poll.pojo.PollEntry;
import com.sugarfit.poll.pojo.QuestionEntry;
import com.sugarfit.poll.pojo.VoteEntry;
import com.sugarfit.sms.entry.UserTaskResponseEntry;
import com.sugarfit.sms.pojo.renewal_journey.RenewalActionPayload;
import com.sugarfit.sms.pojo.renewal_journey.ReportCardPDFResponse;
import com.sugarfit.talktube.enums.ParticipantType;
import com.sugarfit.talktube.pojo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import request.ActivateSpecialPlanRequest;
import request.VitalAddData;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ExecutionException;

@RestController
@Slf4j
@RequestMapping(value = "/chroniccare")
@AllArgsConstructor
public class ChronicCareController {

    private HttpServletRequest request;
    private ChronicCareService chronicCareService;
    private UserService userService;
    private ChronicCareServiceHelper chronicCareServiceHelper;
    private CAPIService capiService;
    private ExceptionReportingService exceptionReportingService;


    @RequestMapping(method = RequestMethod.GET, value = {"onboarding/status-update"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Object updateOnboardingStatus(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String status = queryParams.get("status");
        return this.chronicCareService.updateOnboardingStatus(userContext, status);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/purchase"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getChronicCarePurchasePage(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("getChronicCarePurchasePage requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String typeOfDiabetes = queryParams.get("type");
        String productCode = queryParams.get("productCode");
        productCode = productCode == null ? typeOfDiabetes : productCode;
        return this.chronicCareService.getPurchasePage(userContext, productCode, typeOfDiabetes);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/user/whatsAppNumber/fetch"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String getWhatsappNumber() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getWhatsAppNumber(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/user/delete-account-enabled"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean isDeleteAccountEnabled() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.isDeleteAccountEnabled(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/user/whatsAppNumber/update"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String updateWhatsappNumber(@RequestParam String whatsAppNumber) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateWhatsAppNumber(userContext, whatsAppNumber);
    }


    @RequestMapping(method = RequestMethod.POST, value = {"/user/app-language/update"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean updateAppLanguagePreference(@RequestParam String language) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateAppLanguagePreference(userContext, language);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/translations/update", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean updateTranslations(@RequestBody TranslationDataEntry translationDataEntry) throws BaseException, MetricClientException {
        return chronicCareService.updateTranslations(translationDataEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/translations/fetch", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public TranslationDataResponse getTranslations(@RequestParam String gender) throws BaseException, MetricClientException {
        return chronicCareService.getTranslations(gender);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/user/callingNumber/fetch"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String getCallingNumber() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCallingNumber(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/user/callingNumber/update"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PatientDetail updateCallingNumber(@RequestParam String callingNumber) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateCallingNumber(userContext, callingNumber);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/congrats-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CongratsPageView getCongratsPage(@RequestParam Map<String, String> queryParams) throws Exception {
        log.info("getCongratsPage requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String type = queryParams.get("type");
        String consultationType = queryParams.getOrDefault("consultationType", null);
        String productId = queryParams.getOrDefault("productId", null);
        String bookingId = queryParams.getOrDefault("bookingId", null);
        return this.chronicCareService.getCongratsPage(userContext, type, consultationType, productId, bookingId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/diagnostic-success/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfDiagnosticTestSuccessPageView getDiagnosticTestSuccessPage(@RequestParam Map<String, String> queryParams) throws Exception {
        String orderId = queryParams.getOrDefault("orderId", null);
        return this.chronicCareService.getDiagnosticTestSuccessPage(orderId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/productDescriptionPage"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ProductDescriptionPageView getProductDescriptionPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("getProductDescriptionPage requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String type = queryParams.get("type");
        return this.chronicCareService.getProductDescriptionPage(userContext, type);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/preferenceform"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getPreferenceFormPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("Preference Form Page Requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        UserEntry user = this.userService.getUser(userContext.getUserProfile().getUserId()).join();
        Number pageNumber = Integer.parseInt(queryParams.get("pageNumber"));
        return this.chronicCareService.getPreferenceForm(userContext, user, pageNumber);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugarreading"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getSugarReadingPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("SugarReadingPage requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSugarJournalReadingsPageView(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugarinsights"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getSugarReadingInsightsPage(@RequestParam Map<String, String> queryParams) throws BaseException, JsonProcessingException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        log.info("SugarInsightsPage requested, by user :: {}", userContext.getUserProfile().getUserId());
        return this.chronicCareService.getSugarJournalInsightsPageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/addsugarreading"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object addSugarReading(@RequestBody SugarReadingBody sugarReading) throws BaseException, MetricClientException {
        log.info("addSugarReading ");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        chronicCareService.addSugarReading(userContext, sugarReading);
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugarfit-nux-status"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SFNuxStatusResponse getSugarfitNuxStatus() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.getSugarfitNuxStatus(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/pack-start-date-selection"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PackStartDateSelection getPackStartDateSelectionPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.getPackStartDateSelectionPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/nux-preferences-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public NuxPreferencesPageView getNuxPreferencesPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.getNuxPreferencesPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/setup-journey-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SetupJourneyPageView getSetupJourneyPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.getSetupJourneyPage(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/adduserpreference"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object addUserPreference(@RequestBody UserPreference userPreference) {
        log.info("add user preference");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.addUserPreference(userContext, userPreference);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/adduserAddresspreference"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ChronicCareSaveUserAddressResponse addUserAddressPreference(@RequestBody UserPreference userPreference) {
        log.info("add user Address preference");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.addUserAddressPreference(userContext, userPreference);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/serviceablityForm"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceabilityFormResponse getServiceabilityCheckForm(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        log.info("getServiceabilityCheckForm requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String productCode = queryParams.get("productCode");
        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        return this.chronicCareService.getServiceablityForm(userContext, productCode, user);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/serviceabilityAndPatient"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceabilityAndPatientResponse serviceabilityAndPatient(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("serviceabilityAndPatient requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userName = queryParams.get("name");
        String pinCode = queryParams.get("pinCode");
        String productCode = queryParams.get("productCode");
        return this.chronicCareService.serviceabilityAndCreatePatient(userContext, pinCode, userName, productCode);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/serviceabilityAndPatient/v2"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceabilityAndPatientResponse serviceabilityAndPatientV2(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("serviceabilityAndPatientV2 requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userName = queryParams.get("name");
        String addressId = queryParams.get("addressId");
        String productCode = queryParams.get("productCode");
        return this.chronicCareService.serviceabilityAndCreatePatientV2(userContext, addressId, userName, productCode);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/editPatientDobGender"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PatientDetail editPatientDobGender(@RequestBody PatientDobGender patientDetails) throws Exception {
        log.info("editPatientDobGender requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Gender gender = patientDetails.getGender();
        Date dateOfBirth = patientDetails.getDateOfBirth();
        return this.chronicCareService.editGenderDobPatient(userContext, gender, dateOfBirth);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/patient-home-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ChronicCareHomePageView patientHomePage(@RequestParam Map<String, String> queryParams) throws Exception {
        log.info("Patient Home Page requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String cgmDeviceId = queryParams.get("cgmDeviceId");
        return this.chronicCareService.getChronicCareHomePageView(userContext, cgmDeviceId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugarfit-home-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfHomePageView sugarFitHomePage(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSugarFitHomePageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugarfit-home-page/v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfHomePageView sugarFitHomePageV2(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSugarFitHomePageViewV2(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/digi-home-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfDigiHomePageView digitalAppHomePage(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDigitalAppHomePage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-lite/homepage"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfLiteAppHomePageView sfLiteAppHomePage(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.sfLiteAppHomePage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-lite/mark-webinar-join"})
    public boolean sfLiteMarkWebinarJoin(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.sfLiteMarkWebinarJoin(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/user/app-language/preferences"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<SugarfitAppLanguagePreference> getAppLanguagePreferences() throws BaseException {
        log.info("Get User Preferences");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getAppLanguagePreferences(userContext);
    }
    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-photo-reading-modal/details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CGMPhotoReadingModalDetails getCgmPhotoReadingModalDetails() throws BaseException {
        return this.chronicCareService.getCgmPhotoReadingModalDetails();
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/kickstart/oath"})
    public void updateKickStartOath() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        chronicCareService.updateKickStartOath(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/getBottomTabs"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<TabType> getBottomTabs(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("Get Bottom Tabs requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getBottomTabs(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/get-bottom-tabs/v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public HashMap getBottomTabsV2(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("Get Bottom Tabs requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getBottomTabsV2(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/callback-request"})
    public Object saveUserCallbackRequest(@RequestBody CallbackRequest callbackRequest) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        log.info(String.format("Received User callback request for number :: %s, date :: %s", callbackRequest.getPhNumber(), callbackRequest.getDate()));
        chronicCareService.saveUserCallbackRequest(callbackRequest, userContext);
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/pre-purchase-promise-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getPrePurchasePromisePage(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        log.info("Pre purchase promise page requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String packType = queryParams.get("packType");
        return this.chronicCareService.getChronicCarePrePurchasePromisePage(userContext, packType);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/diabetestypeselection"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getDiabetesTypeSelection(@RequestParam Map<String, String> queryParams) throws ResourceNotFoundException {
        log.info("Diabetes Type Selection page requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDiabetesTypeSelectionPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/agm-insight-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getAGMInsightPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("AGM Insight Page requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String cgmDeviceId = queryParams.get("cgmDeviceId");
        return this.chronicCareService.getAgmInsightPageView(userContext, cgmDeviceId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/hamburger-menu"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getAccountViewPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("hamburger-menu Page requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getHamburgerMenuPageView(queryParams, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/patient-diet-plan-v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ChronicCareNutritionPageView getDietPlanV2(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String date = queryParams.get("date");
        return this.chronicCareService.getPatientDietPlanV2(userContext, date);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/patient-diet-plan/pdf"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String getMealPlanPdf() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMealPlanPdf(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/patient-diet-plan/pdf/v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String getMealPlanPdfV2() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMealPlanPdfV2(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/patient-diet-plan/latest/pdf"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserMealPlanPdfResponse getLatestMealPlanPdf(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Long epochTime = Long.parseLong(queryParams.get("epochTime"));
        return this.chronicCareService.getLatestMealPlanPdf(userContext, epochTime);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/patient-diet-plan/state"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserMealPlanState getUserMealPlanState(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getUserMealPlanState(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/patient-diet-plan-feedback"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserMealPlanFeedback getDietPlanFeedback(@RequestBody UserMealPlanFeedback userMealPlanFeedback) throws EHRClientException, PolarisClientException, ResourceNotFoundException, HttpException {
        log.info("Patient diet plan feedback");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.logDietPlanFeedback(userContext, userMealPlanFeedback);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/meal/food-groups"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<FoodgroupEntry> getFoodGroups() throws BaseException {
        log.info("Food group master data requested");
        return this.chronicCareService.getFoodGroupsData();
    }

    @RequestMapping(method = RequestMethod.POST, value = {
            "/assign-doctor"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object assignDoctor(@RequestBody DoctorAssignRequest doctorAssignRequest) {
        log.info("assign doctor manually:");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.assignDoctor(userContext, doctorAssignRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/patient-activity"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<PatientActivityLoggingResponse> logPatientActivity(@RequestBody List<PatientActivityLoggingRequest> patientLoggingRequests) throws EHRClientException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        if (CollectionUtils.isEmpty(patientLoggingRequests)) {
            return Lists.newArrayList();
        }
        return this.chronicCareService.logPatientActivity(patientLoggingRequests, userContext);
    }

    @RequestMapping(method = RequestMethod.PUT, value = {"/patient-activity"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<CGMStat.PatientActivityImpact> logPatientActivityV2(@RequestBody List<PatientActivityLoggingRequest> patientActivityLoggingRequests) throws EHRClientException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        if (CollectionUtils.isEmpty(patientActivityLoggingRequests)) {
            return Lists.newArrayList();
        }
        return this.chronicCareService.logPatientActivityV2(patientActivityLoggingRequests, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/patient-scheduled-intervention"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<ScheduledPlanItem> getPatientScheduledInterventions(@RequestParam Long startTime, @RequestParam Long endTime) throws EHRClientException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPatientScheduledInterventions(userContext, startTime, endTime);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/log-scheduled-intervention"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ScheduledPlanItem logPatientActivity(@RequestBody PatientInterventionLoggingRequest loggingRequest) throws EHRClientException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.logScheduledIntervention(loggingRequest, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/cgm-nfc-reading"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserNFCReadingResponse inAppCgmReading(@RequestBody InAppCGMReading inAppCGMReading) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.recordInAppCGMReading(inAppCGMReading, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/cgm-ble-reading"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GlucoRxReadingResponse inAppCgmBleReading(@RequestBody InAppCGMBleReading inAppCGMBleReading) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.recordInAppCGMBleReading(inAppCGMBleReading, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/cgm-ble-event"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public BluconEventEntry createGlucoRxEvent(@RequestBody BluconEventEntry entry) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.createGlucoRxEvent(entry, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/app-rollbar-alert"})
    public void raiseAppAlert(@RequestParam Map<String, String> queryParams) {
        String type = queryParams.get("type");
        String msg = queryParams.get("msg");
        exceptionReportingService.reportException(String.format("Type: %s | Err: %s", type, msg), new Error(String.format("Type: %s | Err: %s", type, msg)));
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/libre-sensor-install"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ActivateDeviceResponse libreSensorInstall(@RequestBody ActivateDeviceRequest activateDeviceRequest) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.libreSensorInstall(userContext, activateDeviceRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/v1/goals-habit-page", "v1/goals/pages/habit"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Deprecated(forRemoval = true, since = "SF-APP 1.28")
    public Object getGoalsHabitPage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        int year = queryParams.containsKey("year") ? Integer.parseInt(queryParams.get("year")) : Calendar.getInstance().get(Calendar.YEAR);
        // here in old API month is already in range 0-11
        int month = queryParams.containsKey("month") ? Integer.parseInt(queryParams.get("month")) : Calendar.getInstance().get(Calendar.MONTH);
        return this.chronicCareService.getGoalsHabitPageView(userContext, year, month);
    }

    @RequestMapping(method = RequestMethod.GET, value = "v2/goals/pages/habit", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GoalsHabitPageView getGoalsHabitPageV2(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        log.info("Goals page API params :: " + queryParams);
        int year = queryParams.containsKey("year") ? Integer.parseInt(queryParams.get("year")) : Calendar.getInstance().get(Calendar.YEAR);
        // here in new API month is in range 1-12
        int month = queryParams.containsKey("month") ? Integer.parseInt(queryParams.get("month")) - 1 : Calendar.getInstance().get(Calendar.MONTH);
        GoalsHabitPageView goalsHabitPageView = this.chronicCareService.getGoalsHabitPageView(userContext, year, month);
        return goalsHabitPageView;
    }

    @RequestMapping(method = RequestMethod.PUT, value = {"/update-cgm-reading-mode"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ReadingModeResponse updatePatientCGMReadingMode(@RequestBody PatientCGMReadingMode patientCGMReadingMode) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updatePatientCGMReadingMode(patientCGMReadingMode, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/voice-logging/config"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getVoiceLoggingConfig() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getVoiceLoggingConfig(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity-meallist"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<MealsSearchResponse> getMealList(@RequestParam String meals) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMealList(userContext, meals);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"freemium/search"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FreemiumSearchPageView freemiumSearch(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.freemiumSearch(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/freemium/items"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<FreemiumSearchHistoryItemWidget> getFreemiumItemById(@RequestBody FreemiumItemsRequest freemiumItemsRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFreemiumItemById(userContext, freemiumItemsRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/activity"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean logPatientActivityV3(@RequestBody SfActivityLogPayload sfActivityLogPayload) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.logPatientActivityV3(sfActivityLogPayload, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/activity/meal"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MealLogResponse logPatientMealActivity(@RequestBody SfActivityLogPayload sfActivityLogPayload) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.logPatientMealActivity(sfActivityLogPayload, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/activity/medicine/add-to-prescription"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean addMedicineToPrescription(@RequestBody SfMedication sfMedication) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addMedicineToPrescription(sfMedication, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/activity/medicine/add"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfMedication addCustomMedicine(@RequestBody SfMedication sfMedication) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addCustomMedicine(sfMedication, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity/medication/empty"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getEmptyMedication(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getEmptyMedication(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getPatientActivity(@RequestParam Map<String, String> queryParams) throws BaseException, EHRClientException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPatientActivity(queryParams, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity/search-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ActivitySearchPageView getActivitySearchPageView(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getActivitySearchPageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity/search-page-v3"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MealActivitySearchPageView getMealActivitySearchPageView(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMealActivitySearchPageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity/meal/log-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ActivityMealLogPageView getMealLoggingPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.getMealLoggingPage(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity/meal/favourites-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ActivityMealFavouritesPageView getMealFavouritesPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMealFavouritesPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/activity/meal/fav-meal"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean createFavMeal(@RequestBody SfCreateFavMealPayload sfCreateFavMealPayload) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.createFavMeal(sfCreateFavMealPayload, userContext);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/activity/meal/fav-meal",}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean deleteFavMeal(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deleteFavMeal(queryParams, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity/meal/custom-dish-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ActivityCustomDishPageView getCustomDishPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.getCustomDishPage(queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/activity/meal/custom-dish"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfActivityDishItemWidget createCustomDish(@RequestBody SfCreateDishPayload sfCreateDishPayload) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.createCustomDish(sfCreateDishPayload, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/activity/meal/custom-dish/suggest"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean suggestIngredient(@RequestBody UserDishSuggestionEntry userDishSuggestionEntry) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.suggestIngredient(userDishSuggestionEntry, userContext);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/activity/meal/custom-dish",}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean deleteCustomDish(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deleteCustomDish(queryParams, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity/meal/measurement-units"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<MeasurementUnitEntry> getDishMeasurementUnits() throws BaseException {
        return this.chronicCareService.getDishMeasurementUnits();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/goals-home-page", "v1/goals/pages/home"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getGoalsHomePage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getGoalsHomePageView(userContext, queryParams);

    }

    @RequestMapping(method = RequestMethod.GET, value = {"/v2/goals-update-page", "v1/goals/pages/update"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getGoalsUpdatePage(@RequestParam Map<String, String> queryParams)
            throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getGoalsUpdatePageView(userContext, queryParams);

    }

    @RequestMapping(method = RequestMethod.POST, value = {"/update-user-goal", "/v1/goals/user/update"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object updateUserGoal(@RequestBody UserGoalBody userGoalBody) throws ResourceNotFoundException, HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateUserGoal(userContext, userGoalBody);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/v1/subscription/future/start-date"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object updateStartDate(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Long startDate = Long.parseLong(queryParams.get("startDate"));
        chronicCareService.updateFuturePackStartDate(userContext, startDate);
        return Map.of("success", true);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activitylogs"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getActivityLogsPage(@RequestParam Map<String, String> queryParams) throws ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getActivityLogsPageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/v2/activitylogs"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getActivityLogsPageV2(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getActivityLogsPageViewV2(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/v3/activitylogs"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getActivityLogsPageV3(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getActivityLogsPageViewV3(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/update-intervention-activity", "/v2/intervention/response"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserActivityEntry updateInterventionActivity(@RequestBody InterventionsStatusUpdateRequest req) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateInterventionActivity(userContext, req);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/disclaimer",}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean updateDisclaimer(@RequestBody HomepageDisclaimer item) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateDisclaimer(userContext, item);
    }

    @RequestMapping(method = RequestMethod.PUT, value = {"/ragus-migration-disclaimer",}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean updateRagusMigrationDisclaimer() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateRagusMigrationDisclaimer(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sensor-request"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CGMRequestResponse sensorRequest(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.requestSensor(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sensor-install-request"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CGMRequestResponse sensorInstallationRequest(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.requestInstallation(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/ops-request"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public OpsRequestResponse opsRequest(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.opsRequest(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/updateTimezone"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfTimezoneUpdateResponse updateTimezone(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateTimezone(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/referral/benefits"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<ReferralBenefitEntry> fetchReferralBenefits() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchReferralBenefits(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/referral/rewards"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<ReferralRewardEntry> fetchReferralRewards() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchReferralRewards(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/referral/create-bulk"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<UserReferralEntry> createBulkReferral(@RequestBody List<UserReferralEntry> userReferralEntry) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.createBulkReferral(userReferralEntry, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/referral/create"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserReferralEntry createUserReferral(@RequestBody UserReferralEntry userReferralEntry) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.createUserReferral(userReferralEntry, userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/referral/createUsingCode"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserReferralEntry createUserReferralUsingCode(@RequestBody UserReferralEntry userReferralEntry) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.createUserReferralUsingCode(userReferralEntry, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/referral/fetchCode"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserReferralCodeMapEntry fetchUserReferralCode() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchUserReferralCode(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/referral/validateCode"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserReferralCodeMapEntry validateUserReferralCode(@RequestParam("code") String code) throws Exception {
        return this.chronicCareService.validateUserReferralCode(code);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/referral/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfReferralPageView fetchReferralPage() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchReferralPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/referral/fetchMyReferralSummary"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ReferralSummary getReferralSummary() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getReferralSummary(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/referral/fetchMyReferrals"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<UserReferralEntry> fetchUserReferrals() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchUserReferrals(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/referral/addReward"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserReferralRewardEntry addUserReferralReward(@RequestBody UserReferralRewardEntry userReferralRewardEntry) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addUserReferralReward(userReferralRewardEntry, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/subscription-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SubscriptionPageView getSubscriptionPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSubscriptionPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/renew-subscription-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RenewSubscriptionPageView getRenewSubscriptionPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("getCongratsPage requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRenewSubscriptionPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/renewal-journey-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RenewalJourneyPacksPageView getRenewalJourneyPacksPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRenewalJourneyPacksPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/renew-subscription-success-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RenewSubscriptionSuccessPageView getRenewSubscriptionSuccessPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRenewSubscriptionSuccessPage(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/renew-subscription/custom-pack"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean addCustomRenewalPack(@RequestBody CustomRenewalPackRequest customRenewalPackRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addCustomRenewalPack(userContext, customRenewalPackRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/renew-subscription/apply-offer"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean applyRenewalOffer(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.applyRenewalOffer(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/success-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SubscriptionPageView getSuccessPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        log.info("getCongratsPage requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSuccessPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/fitness-device-data/v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public BaseResponse uploadDeviceData(@RequestBody RawHealthDataEntry req) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        req.setDeviceId(userContext.getSessionInfo().getDeviceId());
        req.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        return this.chronicCareService.addRawHealthData(req);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/vital-page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public VitalPageView getVitalPage(@RequestParam Map<String, String> queryParams) throws ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        log.info(" API params :: " + queryParams);
        return this.chronicCareService.getVitalPageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/health-page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public HealthPageView getHealthPage(@RequestParam Map<String, String> queryParams) throws ResourceNotFoundException, HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getHealthPageView(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/add-fitness"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean addFitnessData(@RequestBody VitalAddData req) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addFitnessData(userContext, req);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/update-device", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ActiveDeviceEntry updateActiveDevice(@RequestParam Map<String, String> queryParams) throws ResourceNotFoundException, HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateActiveDevice(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/fitness-device-config", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessDeviceSyncMeta fitnessDeviceConfig(@RequestParam Map<String, String> queryParams) throws ResourceNotFoundException, HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fitnessDeviceConfig(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm/scored-meals"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CGMScoredMealsPage getCGMScoredMealsPage(@RequestParam Map<String, String> queryParams) throws ResourceNotFoundException, HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCGMScoredMealsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/impact-page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ImpactPageView getImpactPage(@RequestParam Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getImpactPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/get-more-activities", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<PatientActivityLoggingCompareEntry> getMoreAcitivtyItems(@RequestParam Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMoreAcitivtyItems(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/impact-page/v2", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ImpactPageView getImpactPageV2(@RequestParam Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getImpactPageV2(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/get-more-activities/v2", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<ComparableActivityLogResponse<BaseActivityLogEntry>> getMoreAcitivtyItemsV2(@RequestParam Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMoreAcitivtyItemsV2(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/impact-result-page", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ImpactCompareResultPageView getImpactCompareResultPage(@RequestBody ActivityComparisonRequest activityComparisonRequest) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getImpactCompareResultPage(userContext, activityComparisonRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/impact-result-page/v2", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ImpactCompareResultPageView getImpactCompareResultPageV2(@RequestBody ActivityLogComparisonRequest activityComparisonRequest) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getImpactCompareResultPageV2(userContext, activityComparisonRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-timeline-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CGMTimelinePageView getCGMTimelinePage(@RequestParam Map<String, String> queryParams) throws HttpException, ResourceNotFoundException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCGMTimelinePage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/fetch-latest-metabolic-score"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MetabolicScoreResponse fetchLatestMetabolicScore() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchLatestMetabolicScore(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/fetch-latest-insight-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public BgInsightsSummary fetchLatestInsightsSummary(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchLatestInsightsSummary(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/fetch-insight-details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public BgInsightDetailedResponse fetchCgmInsightDetails(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchCgmInsightDetails(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiment/static-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public StaticData fetchExperimentStaticData() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchExperimentStaticData(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/experiment/collection"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ExperimentCollectionPageView getExperimentCollection(@RequestBody UserExperimentCollectionFilterRequest filterRequest) throws EHRClientException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.getExperimentCollection(userContext, filterRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiment/myexperiments"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MyExperimentsPageView getMyExperimentsPage(@RequestParam Long dateTime) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Date date = new Date(dateTime);
        return chronicCareService.getMyExperimentsPage(userContext, date);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiment/ongoing-experiments"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<UfExperimentCardWidget> getOngoingExperiments(@RequestParam Long dateTime) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Date date = new Date(dateTime);
        return chronicCareService.getOngoingExperiments(userContext, date);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiment/fetch"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Experiment fetchExperiment(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchExperiment(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/experiment/join"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserExperiment joinExperiment(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.joinExperiment(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/experiment/unjoin"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserExperiment unjoinExperiment(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.unjoinExperiment(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/experiment/mark-activity"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserExperimentActionResponse markExperimentActivity(@RequestBody UserExperimentAction userExperimentAction) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markExperimentActivity(userContext, userExperimentAction);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/experiment/undo-activity"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserExperimentActionResponse undoExperiment(@RequestParam Map<String, String> queryParams, @RequestParam Long experimentDay) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.undoExperiment(userContext, queryParams, experimentDay);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiment-result-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ExperimentResultPageView getExperimentResultPage(@RequestParam Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getExperimentResultPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiment-result"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserExperimentResult fetchRawExperimentResult(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchRawExperimentResult(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/allow-cult-fitness"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean allowCultFitness() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.allowCultFitness(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/hide-cult-fitness"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean hideCultFitness() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.hideCultFitness(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/fitness-devices"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessDevicesPageView fetchFitnessAppsAndDevicesPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchFitnessAppsAndDevicesPage(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/update-research-access"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean updateResearchAccess(@RequestParam Boolean accessGranted) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateResearchAccess(userContext, accessGranted);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/wellness-glance-widget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfWellnessAtGlanceWidget fetchWellnessAtGlanceWidget() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchWellnessAtGlanceWidget(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/mail-glucose-readings"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String mailGlucoseReadings() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.mailGlucoseReadings(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/help-and-support"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SupportPageView getHelpAndSupportPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getHelpAndSupportPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugar-logging-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SugarLoggingPageView getSugarLoggingPage(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSugarLoggingPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/save-sugar-reading"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GlucometerReadingEntry saveGlucometerReadings(@RequestBody GlucometerReadingEntry insertRequest) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.saveGlucometerReadings(userContext, insertRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/bulk-save-sugar-reading"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<GlucometerReadingEntry> bulkSaveGlucometerReadings(@RequestBody List<GlucometerReadingEntry> insertRequest) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.bulkSaveGlucometerReadings(userContext, insertRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/save-raw-sugar-readings"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<GlucometerReadingEntry> bulkSaveRawGlucometerReadings(@RequestBody GlucometerRawDataRequest requestPayload) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.bulkSaveRawGlucometerReadings(userContext, requestPayload);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/delete-sugar-reading"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DefaultSuccessResponse deleteGlucometerReadings(@RequestParam Map<String, String> queryParams) throws HttpException {
        return this.chronicCareService.deleteGlucometerReadings(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/v2/sugar-journal-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SugarJournalV2PageView getSugarJournalV2page(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSugarJournalV2page(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugar-log-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SugarLogListWidget getSugarLogWidgetData(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSugarLogWidgetData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugar-lite-log-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SLSugarLogListWidget getSLSugarLogWidgetData(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSLSugarLogWidgetData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-store-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CgmStorePageView getCgmStorePage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCgmStorePage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/referral/interstitials"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ReferralInterstitialConfig getReferralInterstitials() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getReferralInterstitials(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugar-tread-deviation-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GlucoseTrendDeviationWidget getTrendDeviationWidgetData(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getTrendDeviationWidgetData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugar-tread-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GlucoseTrendWidget getTrendWidgetData(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getTrendWidgetData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugar-lite-tread-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SLGlucoseTrendWidget getSLTrendWidgetData(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSLTrendWidgetData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sugar-reading-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GlucoseAverageReadingWidget getMonthAverageReadingWidgetData(@RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMonthAverageReadingWidgetData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/consultations-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfConsultationsPageView getConsultationsPage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getConsultationsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/consultation-detail-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfConsultationDetailPageView getConsultationsPage(Long bookingId) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getConsultationDetailPage(userContext, bookingId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/reports-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfReportsPageView getReportsPage() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getReportsPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/orders-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfOrdersPageView getOrdersPage() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getOrdersPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/order-invoice"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getOrderInvoice(@RequestParam Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getOrderInvoice(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/email-document"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean getEmailDocument(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getEmailDocument(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/reports/upload/get-signed-url"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public AssetResponse getUrlToUploadReport(String category) throws ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getUrlToUploadReport(userContext, category);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/reports/upload/confirm"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void confirmReportUpload(@RequestParam Map<String, String> queryParams) throws ResourceNotFoundException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.confirmReportUpload(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/reports/email/diagnostics"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void emailDiagnosticReport(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.emailDiagnosticReport(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/reports/email/prescription"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void emailDoctorPrescription(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.emailDoctorPrescription(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/reports/view"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String downloadReport(@RequestParam String s3Prefix) {
        return this.chronicCareService.downloadReport(s3Prefix);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/reports/delete-user-report",}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean deleteUserReport(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deleteUserReport(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/catalog/bundle/{productCode}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public BundleSellableProduct searchBundleProduct(@PathVariable(name = "productCode") String productCode) throws BaseException {
        return this.chronicCareServiceHelper.getBundleProductByCode(productCode)
                .orElseThrow(() -> new BaseException(
                        String.format("No product found with code :: %s", productCode),
                        AppStatus.NOT_FOUND,
                        LogType.WARNING));
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/webinar/join-request"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public WebinarJoinResponse handleWebinarJoinRequest(@RequestBody WebinarJoinRequest webinarJoinRequest,
                                                        @RequestHeader(name = "User-Agent", required = false) String userAgent)
            throws BaseException {
        return chronicCareService.handleWebinarJoinRequest(webinarJoinRequest, userAgent);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/webinar/pack-purchase-voice"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchWebinarPackPurchaseVoice(@RequestParam String customerName)
            throws BaseException {
        return chronicCareService.fetchWebinarPackPurchaseVoice(customerName);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/webinar/ew-join-status"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public EverWebinarJoinStatusResponse fetchEverWebinarJoinStatus(@RequestParam String phoneNumber, @RequestParam String webinarCode)
            throws BaseException {
        return chronicCareService.fetchEverWebinarJoinStatus(phoneNumber, webinarCode);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity/meal/time-slots"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MealTimeSlotTransformResponse getMealTimeSlots(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMealTimeSlots(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/nutrients-contributor-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public NutrientContributorPageView getNutrientsContributorPage(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getNutrientsContributorPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/nutrition-log-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public NutritionLogsPageView getNutritionPage(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getNutritionPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/nutrition-intake-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public NutritionIntakeDetailWidget getNutritionIntakeWidgetData(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getNutritionIntakeWidgetData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/nutrition-insights-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public NutritionInsightsWidget getNutritionInsightsWidget(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getNutritionInsightsWidget(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/nutrition-log-list-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public NutritionLogListWidget getNutritionLogListWidget(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getNutritionLogListWidget(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/meal-log",}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean deleteMealLog(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deleteMealLog(queryParams, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/face-based-vitals/scan-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FBVScanPageView getFaceBasedVitalsScanPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFaceBasedVitalsScanPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/face-based-vitals/scan-page-v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FBVScanPageView getFaceBasedVitalsScanPageV2() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFaceBasedVitalsScanPageV2(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/face-based-vitals/log-scan-status"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void updateFaceScanStatus(@RequestBody CareplixScanStatusUpdateRequest payload) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.updateFaceScanStatus(userContext, payload);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/face-based-vitals/submit"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<UserMetricEntry> addFaceBasedVitalsData(@RequestBody FaceBasedVitalUploadRequest uploadRequest) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addFaceBasedVitalsData(userContext, uploadRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/face-based-vitals/submit-v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CareplixFaceBasedVitalSaveResponse addFaceBasedVitalsDataV2(@RequestBody CareplixFaceBasedVitalsRequest uploadRequest) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addFaceBasedVitalsDataV2(userContext, uploadRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/face-based-vitals/logs"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FaceBasedVitalLogsPage getFaceBasedVitalLogsPage(@RequestBody FaceBasedVitalsRequest fbvRequest) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFaceBasedVitalLogsPage(userContext, fbvRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/face-based-vitals/experience/history"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FBVExperienceHistoryPage getFBVExperienceLogsPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFBVExperienceLogsPage(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/face-based-vitals/trends"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FaceBasedVitalTrendsPage getFaceBasedVitalTrendsPage(@RequestBody FaceBasedVitalsRequest fbvRequest) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFaceBasedVitalTrendsPage(userContext, fbvRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/face-based-vitals/onboarding"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FBVOnboardingResponse getFbvOnboardingData() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFbvOnboardingData(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/face-based-vitals/onboarding"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FBVOnboardingResponse updateFbvOnboardingData(@RequestBody FBVOnboardingRequest fbvOnboardingRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateFbvOnboardingData(userContext, fbvOnboardingRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/fitness-journal-v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessJournalV2PageView getFitnessJournalV2PageView() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFitnessJournalV2PageView(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/fitness-weekly-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessWeeklyStatWidget getFitnessWeeklyStatWidgetData(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFitnessWeeklyStatWidgetData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/fitness-insight-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessWalkInsightsWidget getFitnessWalkInsightsWidget(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFitnessWalkInsightsWidget(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/fitness-live-class-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessLiveClassWidget getFitnessLiveClassWidget(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFitnessLiveClassWidget(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/fitness-log-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FitnessLogListWidget getFitnessLogListWidget(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFitnessLogListWidget(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium-nux-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FreemiumNuxPageView getFreemiumNuxPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFreemiumNuxPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/da-freemium-nux-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DAFreemiumNuxPageView getDAFreemiumNuxPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDAFreemiumNuxPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/update-da-freemium-nux-status"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean updateDAFreemiumNuxData(@RequestBody List<DANuxDataUpdatePayload> payload) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateDANUXData(userContext, payload);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/update-nux-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void updateNUXData(@RequestBody Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.updateNUXData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/update-nux-email"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean updateNUXEmail(@RequestBody Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateNUXEmail(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium-onboarding-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FreemiumOnboardingPageView getFreemiumOnboardingPage() throws HttpException, IOException {
        return this.chronicCareService.getFreemiumOnboardingPage();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium/homepage"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfFreemiumHomePageView getFreemiumHomePage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFreemiumHomePage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium/homepage/discover"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<BaseWidgetNonVM> getFreemiumHomeDiscoverWidgets(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFreemiumHomeDiscoverWidgets(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/accreditations-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfAccreditationsPageView getAccreditationsPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getAccreditationsPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/testimonials-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfTestimonialsPageView getTestimonialsPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getTestimonialsPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium/packs-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FreemiumPackListPageView getFreemiumPacksPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFreemiumPacksPage(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/infoBite/mark-view"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void markInfoBiteViewed(@RequestParam String infoBiteId) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.markInfoBiteViewed(userContext, infoBiteId);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/freemium/schedule-free-coach-call"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean scheduleFreemiumCoachCall() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.scheduleFreeCoachCall(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/freemium/lead"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean createFreemiumLead(@RequestBody LeadEntry leadEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.createFreemiumLead(userContext, leadEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experience-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfExperiencePageView getExperiencePage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getExperiencePage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experience-page/cgm-graph"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ExperiencePageCGMDataResponse getExperienceCgmGraph() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getExperienceCgmGraph();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experience-page/face-based-vitals"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfExperienceFBVWidget getExperienceFBVWidgetData() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getExperienceFBVWidgetData(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommunityPageView getCommunityPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCommunityPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/save-post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry savePost(@RequestBody PostEntry postEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.savePost(userContext, postEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/signed-url-post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchPostsFileUploadPath(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.fetchPostsFileUploadPath(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/signed-url-comment"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchCommentsFileUploadPath(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.fetchCommentsFileUploadPath(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/fetch-post-images"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchPostFiles(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.fetchPostFiles(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/fetch-comment-images"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchCommentFiles(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.fetchCommentFiles(queryParams);
    }


    @RequestMapping(method = RequestMethod.GET, value = {"/community/post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry getPostbyId(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.getPostbyId(queryParams);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/community/post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry deletePost(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deletePost(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/trending-questions"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<PostEntry> fetchTrendingPosts() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchTrendingPosts(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/expert-questions"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<PostEntry> fetchExpertPosts() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchExpertPosts(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/all-questions"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<PostEntry> fetchAllPostsWithExpertPosts(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchAllPostsWithExpertPosts(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/fetch-comments"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<CommentEntry> fetchComments(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchComments(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/community/comment",}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommentEntry deleteComment(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deleteComment(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/save-comment",}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommentEntry saveComments(@RequestBody CommentEntry commentEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.saveComments(userContext, commentEntry);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/mark-spam-post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry markSpamPost(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markSpamPost(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/mark-unspam-post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry unspamPost(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.unspamPost(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/mark-spam-comment"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry markSpamComment(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markSpamComment(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/mark-unspam-comment"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry unspamComment(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.unspamComment(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/like-post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry likePost(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.likePost(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/unlike-post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry unlikePost(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.unlikePost(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/like-comment"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommentEntry likeComment(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.likeComment(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/unlike-comment"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommentEntry unlikeComment(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.unlikeComment(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/discover-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfFreemiumDiscoverPageView getFreemiumDiscoverPage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFreemiumDiscoverPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/blog/details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfFreemiumBlogDetailsPageView getFreemiumBlogDetailsPage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFreemiumBlogDetailsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/blog/list"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfFreemiumBlogListPageView getFreemiumBlogListPage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFreemiumBlogListPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/blog/save"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void saveBlogForUser(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.saveBlogForUser(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/blog/unsave"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void unsaveBlogForUser(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.unsaveBlogForUser(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/blog/add-comment"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommentEntry addCommentOnBLog(@RequestBody CommentEntry commentEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addCommentOnBLog(userContext, commentEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/questions-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MyQuestionsPageView getMyQuestionsPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMyQuestionsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/answers-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public AnswersPageView getAnswersPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getAnswersPageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/community/fetch-user-info"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommunityUserInfo fetchUserInfo(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchUserInfo(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/community/mark-latest-comment"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry markLastestComment(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markLastestComment(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium/webinar-widget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public FreemiumHomeTrendingEventWidget getWebinarWidget() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getWebinarWidget(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/coach-community-session"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfCoachCommunitySessionWidget getCoachCommunitySessionWidget(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        WidgetContext widgetContext = new WidgetContext();
        widgetContext.setQueryParams(queryParams);
        return this.chronicCareService.getCoachCommunitySessionWidget(userContext, widgetContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium/event-talk-widget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfHomeEventsTalkWidget getEventsTalkWidget(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String type = queryParams.getOrDefault("type", "");
        return this.chronicCareService.getEventsTalkWidget(userContext, type);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium/webinar-details-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfWebinarDetailsPageView getWebinarDetailsPage() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getWebinarDetailsPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium/journal-widget-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfHomeJournalWidget getHomeJournalWidgetData() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getHomeJournalWidgetData(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium/journal-update-weight"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean updateWeight(@RequestParam Map<String, String> queryParams) throws BaseException, MetricClientException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateWeight(userContext, queryParams);

    }

    @RequestMapping(method = RequestMethod.GET, value = {"/freemium/get-deep-link"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String getDeepLinkForSharing(@RequestParam Map<String, String> queryParams) throws BaseException, MetricClientException {
        return this.chronicCareService.getDeepLinkForSharing(queryParams);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/delete-workout"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean deleteWorkout(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deleteWorkout(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/badges/list"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public BadgesPageView getBadgesPageView() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getBadgesPageView(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/lsq-lead-push"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object lsqLeadPushApi(@RequestBody List<LeadSquaredPlainRequest> requestBody, HttpServletRequest request) throws BaseException {
        return this.chronicCareService.lsqLeadPushApi(requestBody, request);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/web/event/capi"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object logCAPIServerEvents(@RequestBody CAPIRequest requestBody) throws BaseException {
        try {
            String ip = AppUtil.getClientIpAddr(request);
            requestBody.setClientIpAddress(ip);
            capiService.logCAPIServerEvent(requestBody);
            return Map.of("success", true);
        } catch (Exception e) {
            String message = String.format("Error in capi :: %s", e.getMessage());
            exceptionReportingService.reportException(message, e);
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/lead-push"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object partnerLeadPushApi(@RequestBody Map<String, String> requestBody) throws BaseException {
        return this.chronicCareService.partnerLeadPushApi(requestBody);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sns-event"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean pushSnsEvent(@RequestBody SNSEventBody snsEventBody) {
        return this.chronicCareService.pushSnsEvent(snsEventBody);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo/home-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PhleboHomepageView phelboHomePage(@RequestParam Map<String, String> queryParams) throws Exception {
        log.info("Phelbo Home Page requested");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPhleboHomepageView(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo/fetch-user-info"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UsersResponse getUserInfoByPhone(@RequestParam Map<String, String> queryParams) throws Exception {
        return this.chronicCareService.getUserInfoByPhone(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/get-serviceable-addressId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String getServiceableAddressId() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getServiceableAddressId(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/badge/achieved"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfBadgeEntry fetchAchievedBadges(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchAchievedBadges(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/badge/brokenStreak"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<SfBadgeEntry> fetchBrokenStreaks() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchBrokenStreaks(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/badge/brokenStreak/markRead"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserBadgeProgressEntry markBrokenStreaksRead(@RequestBody Map<String, String> body) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markBrokenStreaksRead(userContext, body);
    }

    // APIs for customer support
    @RequestMapping(method = RequestMethod.GET, value = {"/support"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfSupportPageView getSupportPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSupportPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/support/generic-topics"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<String> getGenericSupportTopics(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.getGenericSupportTopics();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/support/recent-tickets"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfSupportRecentTicketsWidget getRecentTickets(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRecentTickets(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/support/tickets"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfSupportTicketsListPageView getAllSupportTickets(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getAllSupportTickets(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/support/ticket"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfTicketDetailsPageView getTicketDetailsPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getTicketDetailsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/support/ticket/create"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfSupportTicket createSupportTicket(@RequestBody SfSupportTicketRequest supportTicket) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.createSupportTicket(userContext, supportTicket);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/support/ticket/reply"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfTicketDetailsPageView.Chat replyToSupportTicket(@RequestBody SfSupportTicketReply reply) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.replyToSupportTicket(userContext, reply);
    }

    @RequestMapping(method = RequestMethod.PUT, value = {"/support/ticket/update-status"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean updateSupportTicketStatus(@RequestBody TicketUpdateParams params) throws BaseException {
        Long ticketId = params.getTicketId();
        int status = params.getStatus();
        return this.chronicCareService.updateSupportTicketStatus(ticketId, status);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/support/ticket/get-signed-url"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getSignedUrlForSupportTicket(@RequestParam Map<String, String> queryParams) {
        int fileCount = Integer.parseInt(queryParams.getOrDefault("fileCount", "1"));
        return this.chronicCareService.getSignedUrlForSupportTicket(fileCount);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/support/ticket/v2/conversation"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfCSConversationPageView getCSTicketConversation(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String category = queryParams.getOrDefault("category", "Others");
        String convIdStr = queryParams.getOrDefault("conversationId", null);
        Long conversationId = null;
        if (Objects.nonNull(convIdStr)) {
            conversationId = Long.parseLong(convIdStr);
        }
        Integer pageNumber = Integer.parseInt(queryParams.getOrDefault("pageNumber", "0"));
        return this.chronicCareService.getCSTicketConversation(userContext, category, conversationId);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/support/ticket/v2/reply"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<CSChat> replyToCSConversation(@RequestBody TicketReplyRequest reply) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.replyToCSConversation(userContext, reply);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/support/ticket/v2/status"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CSConversation.Status getCSTicketStatus(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCSTicketStatus(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/generic/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfGenericPageView getGenericPageData(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getGenericPageData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/onboarding/walkthrough"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfOnboardingWalkthroughView getOnboardingWalkthroughSteps() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getOnboardingWalkthroughSteps(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/walkthrough/screens"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public WalkthroughScreenView getWalkthroughScreens() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getWalkthroughScreens(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/consultation-summary/{consultationId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ConsultationSummaryResponse getConsultationSummary(@PathVariable Long consultationId) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getConsultationSummary(consultationId, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/community/filter"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommunityFilterResponse filterCommunities(CommunityFilterRequest communityFilterRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.filterCommunities(userContext, communityFilterRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/user-communities/filter"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommunityFilterResponse filterUserCommunities(CommunityFilterRequest communityFilterRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.filterUserCommunities(userContext, communityFilterRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/chat/seen-community-welcome-modal"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean seenCommunityWelcome() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markSeenCommunityWelcomeModal(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/chat/seen-send-intro-modal"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean seenCommunitySendIntroModal() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markSeenCommunitySendIntroModal(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/user-intro-templates/filter"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CommunityIntroTemplatesData fetchUserIntroductionTemplates() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchUserIntroductionTemplates(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/user-intro-reply-templates/filter"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MessageTemplateFilterResponse filterUserIntroductionReplyTemplates(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.filterUserIntroductionReplyTemplates(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/fetch-nudges"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public NudgesResponse fetchChatNudges() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchChatNudges(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/community/unreadMessage"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<Long, UnreadMessageInfoEntry> fetchUnreadMessageInfo(UnreadMessageInfoFilterRequest filterRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchUnreadMessageInfo(userContext, filterRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/community/message/filter"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MessageFilterResponse filterChatMessages(MessageFilterRequest messageFilterRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.filterChatMessages(userContext, messageFilterRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/chat/community/message/send"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MessageEntry sendChatMessage(@RequestBody SendMessageRequest sendMessageRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.sendChatMessage(userContext, sendMessageRequest);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/chat/community/message/delete"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MessageEntry deleteChatMessage(@RequestBody DeleteMessageRequest deleteMessageRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deleteChatMessage(userContext, deleteMessageRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/chat/community/message/star"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public StarredMessageEntry starChatMessage(@RequestBody StarredMessageEntry starredMessageEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.starChatMessage(userContext, starredMessageEntry);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/chat/community/message/unstar"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean unStarChatMessage(@RequestBody StarredMessageEntry starredMessageEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.unStarChatMessage(userContext, starredMessageEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/community/filter-starred-messages"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<StarredMessageEntry> filterStarredMessages(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.filterStarredMessages(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/chat/community/reaction/add"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MessageEntry addChatReaction(@RequestBody MessageReactionEntry messageReactionEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addChatReaction(userContext, messageReactionEntry);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/chat/community/reaction/delete"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MessageEntry deleteChatReaction(@RequestBody MessageReactionEntry messageReactionEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deleteChatReaction(userContext, messageReactionEntry);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/chat/community/unreadMessage/lastReadMessage"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void updateLastReadMessage(@RequestBody UnreadMessageInfoEntry messageInfoEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.updateLastReadMessage(userContext, messageInfoEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/community/notification-preferance"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MessageNotificationPreferenceEntry fetchCommunityNotificationPreference(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchCommunityNotificationPreference(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/chat/community/notification-preferance"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public MessageNotificationPreferenceEntry saveCommunityNotificationPreference(@RequestBody MessageNotificationPreferenceEntry notificationPreferenceEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.saveCommunityNotificationPreference(userContext, notificationPreferenceEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/community/media/uploadPath"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchChatMediaUploadPath(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchChatMediaUploadPath(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/community/media/downloadPath"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchChatMediaDownloadPath(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchChatMediaDownloadPath(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/slot-booking-action/coach"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Action fetchSlotBookingAction() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchSlotBookingAction(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/coach-availability"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CoachAvailabilityStatus fetchCoachAvailabilityStatus(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchCoachAvailabilityStatus(userContext, queryParams);
    }

    //Ekin care
    @RequestMapping(method = RequestMethod.GET, value = {"/datepicker/v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfSlotBookingPageView getSlotBookingDatePickerV2(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSlotBookingDatePickerV2(userContext, queryParams);
    }

    // Community Poll
    @RequestMapping(method = RequestMethod.GET, value = {"/poll/polls"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfPollsPageView getAllPolls(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getAllPolls(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/poll/poll"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PollEntry getPollById(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPollById(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/poll/poll-result"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PollResultsData fetchPollResult(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchCompletedPoll(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/poll/answer"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public QuestionEntry answerOnPoll(@RequestBody SfPollAnswer pollAnswer) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.answerOnPoll(userContext, pollAnswer);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/poll/complete"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CompletePollEntry completePoll(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.completePoll(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/poll/blog-widget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfDiscoverBlogCardWidget getBlogWidget(@RequestParam Map<String, String> queryParams) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getBlogWidget(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/polls/group-widget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<BaseWidgetNonVM> getPollGroupWidget() throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPollGroupWidget(userContext);
    }

    //Challenges
    @RequestMapping(method = RequestMethod.GET, value = {"/challenges/challenges"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfChallengesListPageView getChallengesListPage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getChallengesListPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/challenges/details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfChallengeDetailsPageView getChallengeDetailsPage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getChallengeDetailsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/challenges/rejected-details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<SfChallengeUserEntry> getRejectedChallengeDetails(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRejectedChallengeDetails(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/challenges/has-todays-entry"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean challengeHasTodaysEntry(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.challengeHasTodaysEntry(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/challenges/upload-img"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean uploadChallengeEntry(@RequestBody SfChallengeUserEntry challengeEntry) throws BaseException, ParseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.uploadChallengeEntry(userContext, challengeEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/challenges/get-signed-url"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> getSignedUrlForChallengeEntry(@RequestParam Map<String, String> queryParams) throws BaseException {
        int fileCount = Integer.parseInt(queryParams.getOrDefault("fileCount", "1"));
        return this.chronicCareService.getSignedUrlForChallengeEntry(fileCount);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/challenges/join"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ChallengesEntry joinChallenge(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Long challengeId = Long.parseLong(queryParams.getOrDefault("challengeId", null));
        return this.chronicCareService.joinChallenge(userContext, challengeId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat/challenges/pending-entry"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfChallengeCardWidget getChatWidgetForPendingChallenge(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getChatWidgetForPendingChallenge(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/challenges/leaderboard"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfChallengeLeaderboardPageView getLeaderboardPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Long challengeId = Long.parseLong(queryParams.getOrDefault("challengeId", null));
        int pageNumber = Integer.parseInt(queryParams.getOrDefault("pageNumber", String.valueOf(0)));
        Long coachId = null;
        if (queryParams.containsKey("coachId")) {
            coachId = Long.parseLong(queryParams.getOrDefault("coachId", null));
        }
        return this.chronicCareService.getLeaderboardPage(userContext, challengeId, coachId, pageNumber);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/challenges/submission-success"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfChallengeEntrySubmissionData getChallengeSubmissionData(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getChallengeSubmissionData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/challenges/group-widget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<BaseWidgetNonVM> getChallengeGroupWidget() throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getChallengeGroupWidget(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/consultation/penalty-notification"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfNoShowPenaltyData checkForCoachBookingPenaltyNotification(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.checkForCoachBookingPenaltyNotification(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/self-installation/cgm-delivery-notification"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SFCGMDeliveryData checkForCGMDeliveryNotification(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.checkForCGMDeliveryNotification(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/consultation/penalty-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfNoShowPenaltyData getCoachBookingPenaltyData(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCoachBookingPenaltyData(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm/blucon-details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfBluconDetailsPageView getBluconDetailsPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getBluconDetailsPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/diagnostic-store/tests"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfDiagnosticStoreTest getDiagnosticStoreTests(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDiagnosticStoreTests(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/plp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfECommercePLPView getECommercePLP(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getECommercePLP(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/pdp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfECommercePDPView getECommercePDP(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getECommercePDP(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/pdp/v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfECommercePDPView getECommercePDPV2(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getECommercePDPV2(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/diagnostic/tdp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfDiagnosticTDPView getDiagnosticTDP(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDiagnosticTDP(userContext,queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/diagnostic/test/instructions"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfDiagnosticTestInstructionView getDiagnosticTestInstructions(@RequestBody List<String> productCodes) throws BaseException {
        return this.chronicCareService.getDiagnosticTestInstructions(productCodes);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/web/pdp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfEcommerceWebPDPView getECommerceWebPDP(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getECommerceWebPDP(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/web/cgm-products"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<ProductResponse> getECommerceWebCGMProducts() throws BaseException {
        return this.chronicCareService.getCoachSellableCGMs();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/product-groups"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getECommerceProductGroups(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getECommerceProductGroups(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/configs"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getECommerceConfigs(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.getECommerceConfigs();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/orders"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfECommerceOrdersPageView getECommerceOrdersPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getECommerceOrdersPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/order"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfECommerceOrderDetailsView getECommerceOrderDetails(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getECommerceOrderDetails(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/order-success"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfECommerceOrderSuccessPageView getECommerceOrderSuccessPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getECommerceOrderSuccessPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/things-to-buy"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfEComThingsToBuyListWidget getECommerceThingsToBuy(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getECommerceThingsToBuy(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/serviceability/check"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ServiceabilityCheckResponse getECommerceThingsToBuy(@RequestParam Long pincode) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.ecommerceServiceabilityCheck(userContext, pincode);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ecommerce/order/detailed"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public OrderDetailedResponse getEcommerceDetailedOrder(@RequestParam Long cfOrderId) throws HttpException {
        return this.chronicCareService.ecommerceServiceabilityCheck(cfOrderId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiencecenter/clp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfExperienceCenterCLPView getExperienceCenterCLP(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getExperienceCenterCLP(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiencecenter/select-center"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfExperienceCenterSelectPageView getExperienceCenterList(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getExperienceCenterList(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/post-room-metrics"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean uploadRoomMetrics(@RequestBody RoomMetricsRequest roomMetricsRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.uploadRoomMetrics(userContext, roomMetricsRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/widget/recommended-fitness-plan"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfRecommendedFitnessPlanWidget getRecommendedFitnessPlanWidget() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRecommendedFitnessPlanWidget(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/recommended-fitness-plan/details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfRecommendedFitnessPlanPageView getRecommendedFitnessPlanDetails(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRecommendedFitnessPlanDetails(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/user/cgm/metrics/nfcStatus"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void updateUserDeviceNfcStatus(@RequestBody UserNfcStatusRequest nfcStatusRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.updateUserDeviceNfcStatus(userContext, nfcStatusRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/polls-survey/group-widget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<BaseWidgetNonVM> getPollSurveyGroupWidget() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPollSurveyGroupWidget(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/polls-survey/answer"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public QuestionEntry answerPollSurvey(@RequestBody VoteEntry voteEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.answerPollSurvey(userContext, voteEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/activity-details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getActivityDetails(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Long activityId = Long.valueOf(queryParams.get("activityId"));
        ActivityType activityType = ActivityType.valueOf(queryParams.get("activityType"));
        return this.chronicCareService.getActivityDetails(userContext, activityId, activityType);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experience-center/kickstarter/config"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<FormQuestionEntry> getExperienceCenterKickStarterConfig(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.getExperienceCenterKickStarterConfig();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experience-center/kickstarter/form"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public KickstarterAssessment getExperienceCenterKickStarterForm(@RequestParam Map<String, String> queryParams) throws BaseException {
        String phoneNumber = queryParams.get("phoneNumber");
        String countryCode = queryParams.getOrDefault("countryCode", "+91");
        return this.chronicCareService.getExperienceCenterKickStarterForm(phoneNumber, countryCode);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/experience-center/kickstarter/form"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<UserProfileEntry> saveExperienceCenterKickStarterForm(@RequestBody SfExperienceCenterKSSaveRequest ksSaveRequest) throws BaseException {
        return this.chronicCareService.saveExperienceCenterKickStarterForm(ksSaveRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-install-videos"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getCGMInstallationVideosData(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCGMInstallationVideosData(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/experience-centre/food-compare"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, JsonNode> getFoodCompareData(@RequestBody List<String> keys) throws BaseException {
        return this.chronicCareService.getFoodCompareData(keys);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/nux/is-diabetes-type-2-selected"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean isDiabetesType2Selected(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.isDiabetesType2Selected(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/coach/get-contact-details-action"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Action getContactDetailsAction(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getContactDetailsAction(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/coach/save-contact-details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean saveContactDetailsOnServer(@RequestBody SFContactDetails contactDetails) throws BaseException, JsonProcessingException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.saveContactDetailsOnServer(userContext, contactDetails);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experience-center/feedback-form"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PollEntry getFeedbackForm(@RequestParam Long pollId) throws BaseException {
        return this.chronicCareService.getFeedbackForm(pollId);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/experience-center/feedback-form/response"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<QuestionEntry> saveFeedbackResponse(@RequestParam Long userId, @RequestParam Long pollId, @RequestBody List<VoteEntry> voteEntries) throws BaseException {
        return this.chronicCareService.saveFeedbackResponse(pollId, userId, voteEntries);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/login/is-plain-text-input"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean isPlainTextInput(@RequestParam String phoneNumber) throws BaseException {
        return this.chronicCareService.isPlainTextInput(phoneNumber);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-installation/slots"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfCGMInstallationBookingPage getCGMInstallationSlots(@RequestParam Map<String, Object> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCGMInstallationSlots(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-installation/recommended-slots"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfRecommendedSlotPage getRecommendedCGMInstallationSlots(@RequestParam Map<String, Object> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRecommendedCGMInstallationSlots(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/cgm-installation/book"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean bookCgmInstallation(@RequestBody UserPhleboTaskBookingRequest requestPayload) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.bookCgmInstallation(userContext, requestPayload);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/cgm-installation/reschedule"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean bookCgmInstallation(@RequestBody UserPhleboTaskRescheduleRequest requestPayload) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.rescheduleCgmInstallation(userContext, requestPayload);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/cgm-installation/cancel"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean cancelCgmInstallation(@RequestBody UserPhleboTaskCancelRequest requestPayload) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.cancelCgmInstallation(userContext, requestPayload);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/coach/consultation-missed-notification"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SFConsultMissedModalData checkForWelcomeCallMissedNotification(@RequestParam Map<String, String> queryParams) {
//        Welcome Call Only
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.checkForWelcomeCallMissedNotification(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/consultation/join/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfConsultationDeeplinkPage getConsultationJoinDeeplinkPage() throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getConsultationJoinDeeplinkPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/coach-celebration/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfCoachCelebrationPage getCoachCelebrationPage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCoachCelebrationPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/coach-celebration/submit"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CoachAppreciationSuccessModalData submitCoachAppreciation(@RequestBody RenewalActionPayload renewalActionPayload) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.submitCoachAppreciation(userContext, renewalActionPayload);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/renewal/report/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfRenewalUserReportPage getRenewalUserReportPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRenewalUserReportPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/renewal/report/congrats-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfUserReportCongratsPage getUserReportCongratsPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getUserReportCongratsPage(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/renewal/journey/update"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void updateRenewalJourney(@RequestBody RenewalActionPayload renewalActionPayload) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.updateRenewalJourney(userContext, renewalActionPayload);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/renewal/report/pdf"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ReportCardPDFResponse getUserReportPdf(@RequestParam Map<String, String> queryParams) throws HttpException {
        return this.chronicCareService.getUserReportPdf(queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/request-callback"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfGenericSuccessModal requestCallback(@RequestBody Map<String, Object> requestPayload) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.requestCallback(userContext, requestPayload);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/flashback/report/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfFlashbackReportPage getFlashbackReportPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFlashbackReportPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/flashback/report/congrats-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfFlashbackReportCongratsPage getFlashbackReportCongratsPage() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getFlashbackReportCongratsPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/flashback/report/pdf"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ReportCardPDFResponse getFlashbackReportPdf(@RequestParam Map<String, String> queryParams) throws HttpException {
        return this.chronicCareService.getFlashbackReportPdf(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/meal-activity/slots-details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfMealActivityDetailPageView getMealActivityDetailsPage(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMealActivityDetailsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/meal-activity/signed-img-url"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> getMealActivitySignedUploadURL(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.getMealActivitySignedUploadURL(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/meal-activity/fetch-img-url"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchMealActivityDownloadURL(@RequestParam Map<String, String> queryParams) {
        return this.chronicCareService.fetchMealActivityDownloadURL(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-photo-readings/signed-img-url"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchCgmReaderPhotoDownloadURL(@RequestParam Map<String, String> queryParams) throws BaseException {
        return this.chronicCareService.fetchCgmReaderPhotoDownloadURL(queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/cgm-photo-readings/publish", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<UserReadingsDocumentsEntry> publishCgmReaderPhotos(@RequestBody List<UserReadingsDocumentsEntry> userReadingsDocumentsEntries) throws BaseException {
        return this.chronicCareService.publishCgmReaderPhotos(userReadingsDocumentsEntries);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-photo-readings/fetch-img-url"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> fetchReadingsImgDownloadURL(@RequestParam Map<String, String> queryParams) {
        return this.chronicCareService.fetchReadingsImgDownloadURL(queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/meal-activity/add-fav"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean addMealActivityToFavourite(@RequestParam Map<String, String> queryParams) {
        return this.chronicCareService.addMealActivityToFavourite(queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/meal-activity/remove-fav"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean removeMealActivityFromFavourite(@RequestParam Map<String, String> queryParams) {
        return this.chronicCareService.removeMealActivityFromFavourite(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/meal-activity/dish-entry"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DishEntry getDetailedDishEntry(@RequestParam Map<String, String> queryParams) {
        return this.chronicCareService.getDetailedDishEntry(queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/meal-activity/time-slots"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfMealSlotResponse getMealActivitySlots(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMealActivitySlots(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/meal-activity/image-logging-disabled"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean isImageMealLoggingDisabled(@RequestParam Map<String, String> queryParams) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.isImageMealLoggingDisabled(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiencecenter/wellness/pre-purchase"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfWellnessAtCenterPrePurchaseView getWellnessAtCenterPrePurchaseView(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getWellnessAtCenterPrePurchaseView(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiencecenter/wellness/clp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfWellnessAtCenterCLPView getWellnessAtCenterCLP(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getWellnessAtCenterCLP(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiencecenter/wellness/pdp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfWellnessAtCenterPDPView getWellnessAtCenterPDP(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getWellnessAtCenterPDP(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiencecenter/wellness/bookings"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfWellnessCenterBookingPageView getWellnessCenterBookings(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getWellnessCenterBookings(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sales/clp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfMegaSalesCLP getMegaSalesCLP(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMegaSalesCLP(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/cgm-status"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean updateCGMStatusToBackend(@RequestBody CgmStatusUpdateRequest cgmStatusUpdateRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateCGMStatusToBackend(userContext, cgmStatusUpdateRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/webhook/razorpay-payout")
    public void processRazorpayPayoutEvents(@RequestBody JsonNode requestJson,
                                            @RequestHeader(name = "X-Razorpay-Signature", required = false) String webhookSignature) throws HttpException {
        chronicCareService.processRazorpayPayoutEvents(requestJson, webhookSignature);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/webhook/razorpay-upi-subscription")
    public void processRazorpaySubscriptionEvents(@RequestBody JsonNode requestJson,
                                                  @RequestHeader(name = "X-Razorpay-Signature", required = false) String webhookSignature) throws HttpException {
        chronicCareService.processRazorpaySubscriptionEvents(requestJson, webhookSignature);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/webhook/razorpay-events")
    public void processRazorpayEvents(@RequestBody JsonNode requestJson,
                                                  @RequestHeader(name = "X-Razorpay-Signature", required = false) String webhookSignature) throws HttpException {
        chronicCareService.processRazorpayEvents(requestJson, webhookSignature);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/webhook/shopify-events")
    public void processShopifyEvents(@RequestBody JsonNode requestJson,
                                      @RequestHeader(name = "x-shopify-topic") String shopifyTopic) throws HttpException {
        chronicCareService.processShopifyEvents(requestJson, shopifyTopic);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo-app/phlebotomist-info"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PhlebotomistUser getPhlebotomistInfo(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPhlebotomistInfo(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo-app/user-info"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfPhelboAppUserInfo getPhleboAppUserInfo(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPhleboAppUserInfo(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/phlebo-app/mark-phlebo-action"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean markPhleboAppPhleboAction(@RequestBody PhleboMarkActionRequest actionRequest) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markPhleboAppPhleboAction(userContext, actionRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/phlebo-app/update-inventory"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean updatePhleboAppInventory(@RequestBody List<InputFieldsConfig> inventoryRequest, @RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updatePhleboAppInventory(userContext, inventoryRequest, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/phlebo-app/capture-tasks-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean capturePhleboAppTasksData(@RequestBody List<InputFieldsConfig> captureDataRequest, @RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.capturePhleboAppTasksData(userContext, captureDataRequest, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/phlebo-app/trigger-phone-call"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean triggerPhleboAppPhoneCall(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.triggerPhleboAppPhoneCall(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/phlebo-app/send-phlebo-location"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean sendPhleboAppPhleboLocation(@RequestBody PhleboLocation phleboLocation) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.sendPhleboAppPhleboLocation(userContext, phleboLocation);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/phlebo-app/reschedule-trip"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PhleboTask reschedulePhleboAppTrip(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.reschedulePhleboAppTrip(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo-app/allowed-actions"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<PhleboActions> getPhleboAppAllowedActions(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPhleboAppAllowedActions(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/phlebo-app/home-page-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PhleboAppHomePageView getPhleboAppHomePageData(@RequestBody PhleboTaskSearchRequest requestPayload) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPhleboAppHomePageData(userContext, requestPayload);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo-app/inventory-page-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PhleboAppInventoryPageView getPhleboAppInventoryPageData(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPhleboAppInventoryPageData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo-app/task-details-page-data"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PhleboAppCaptureTaskPageView getPhleboAppCaptureTasksPageData(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPhleboAppCaptureTasksPageData(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo/live-location"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PhleboLocation getPhleboLiveLocation(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPhleboLiveLocation(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo/profile"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserEntry getPhleboProfile(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getPhleboProfile(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo-app/get-signed-url-path"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> getSignedUrlPathForPhleboAppAttachment(@RequestParam Map<String, String> queryParams) throws BaseException {
        int fileCount = Integer.parseInt(queryParams.getOrDefault("fileCount", "1"));
        return this.chronicCareService.getSignedUrlPathForPhleboAppAttachment(fileCount);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo-app/get-signed-url-value"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> getSignedUrlPathValueForPhleboAppAttachment(@RequestParam Map<String, String> queryParams) throws BaseException {
        String fileName = queryParams.getOrDefault("fileName", "");
        return this.chronicCareService.getSignedUrlValueForPhleboAppAttachment(fileName);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/active-cgm-details"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfCgmDeviceStatus getActiveCGMDetails(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getActiveCGMDetails(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/cgm-reading-file"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DefaultSuccessResponse uploadCgmReadingFile(@RequestParam("file") MultipartFile readingFile, @RequestParam("deviceId") String deviceId) throws BaseException, IOException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.uploadCgmReadingFile(userContext, readingFile, deviceId);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/diet-plan-preferences"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DefaultSuccessResponse saveDietPlanParameters(@RequestBody UserMealPlanPreference dietPlanParameters) throws BaseException, IOException {
        long startTime = new Date().toInstant().toEpochMilli();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.chronicCareService.saveDietPlanParameters(userContext, dietPlanParameters);
        return new DefaultSuccessResponse(true, startTime, new Date().toInstant().toEpochMilli());
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/diet-plan-preferences/v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseAsync saveDietPlanParametersV2(@RequestBody UserMealPlanPreference dietPlanParameters) throws BaseException, IOException {
        long startTime = new Date().toInstant().toEpochMilli();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return  this.chronicCareService.saveDietPlanParametersV2(userContext, dietPlanParameters);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/page/cgm-webinars"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfCgmWebinarListPage getCgmWebinarListPage() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getCgmWebinarListPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/live-classes/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfLiveClassListPageView getLiveClassesPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getLiveClassesPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/phlebo-app/cgm-configuration"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfUserCGMConfig getUserCGMConfiguration(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getUserCGMConfiguration(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/voip/status"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public VoipInstanceStatusEntry updateVoipCallStatus(@RequestBody VoipInstanceStatusEntry voipStatus) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateVoipCallStatus(userContext, voipStatus);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/voip/join/token"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public VoipJoinTokenResponse getVoipJoinToken(@RequestParam Long voipInstanceId) throws BaseException {
        return this.chronicCareService.getVoipJoinToken(voipInstanceId, ParticipantType.RECEIVER);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/voip/end"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public VoipInstanceEntry endVoipCall(@RequestBody VoipEndRequest voipEndRequest) throws BaseException {
        return this.chronicCareService.endVoip(voipEndRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/voip/attribute/upsert"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserDeviceVoipAttributeEntry upsertUserDeviceVoipAttribute(@RequestBody UserDeviceVoipAttributeEntry userDeviceVoipAttributeEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        userDeviceVoipAttributeEntry.setUserId(userId);
        return this.chronicCareService.upsertUserDeviceVoipAttribute(userDeviceVoipAttributeEntry);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sugar-control-plan/activate"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DefaultSuccessResponse activateSugarControlPlan(@RequestBody ActivateSpecialPlanRequest activateSpecialPlanRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.activateSugarControlPlan(userContext, activateSpecialPlanRequest.getActivationCode(), activateSpecialPlanRequest.getUserEntry());
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-scale/plp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfECommercePLPView getSmartScalePLP(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartScalePLP(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-scale/onboarding"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartScaleOnboardingResponse getSmartScaleOnboardingData() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartScaleOnboardingData(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/smart-scale/onboarding"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartScaleOnboardingResponse updateSmartScaleOnboardingData(@RequestBody SmartScaleOnboardingRequest smartScaleOnboardingRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateSmartScaleOnboardingData(userContext, smartScaleOnboardingRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-scale/vitals-list-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartScaleVitalsListPageView getSmartScaleVitalsListPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartScaleVitalsListPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-scale/vital-tabs-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartScaleVitalsDetailsTabView getSmartScaleVitalDetailTabsPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartScaleVitalDetailTabs(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-scale/vital-details-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartScaleVitalsDetailsPageView getSmartScaleVitalDetailsPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartScaleVitalDetailsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-scale/timeline-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartScaleVitalsTimelinePageView getSmartScaleVitalsTimelinePage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartScaleVitalsTimelinePage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-scale/settings"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartScaleSettingsPageView getSmartScaleSettingsPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartScaleSettingsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/smart-scale/delete-reading",}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean deleteSmartScaleReading(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.deleteSmartScaleReading(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/smart-scale/readings"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean pushSmartScaleDataToServer(@RequestBody SmartScaleDataUploadRequest smartScaleDataUploadRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.pushSmartScaleDataToServer(userContext, smartScaleDataUploadRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-glucometer/dashboard/"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartGlucometerDashboardPageView getSmartGlucometerDashboardPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartGlucometerDashboardPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-glucometer/settings"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartScaleSettingsPageView getSmartGlucometerSettingsPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartGlucometerSettingsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-glucometer/onboarding"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartGlucometerOnboardingPageView getSmartGlucometerOnboardingPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartGlucometerOnboardingPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-glucometer/resync"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartGlucometerOnboardingPageView getSmartGlucometerReSyncPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartGlucometerOnboardingPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/smart-glucometer/timeline"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SmartGlucometerTimelinePageView getSmartGlucometerTimelinePage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getSmartGlucometerTimelinePage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/gpt/post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry sendSFGPTPost(@RequestBody SmartBotPostEntry postEntry) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.sendSFGPTPost(userContext, postEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/gpt/post"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PostEntry getSFGPTResponse(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String postId = queryParams.get("postId");
        if (postId.isEmpty()) {
            throw new BadRequestException("Post ID is missing");
        }
        return this.chronicCareService.getSFGPTResponse(userContext, Long.valueOf(postId));
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/gpt/glucose-readings/upload"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean uploadGPTGlucoseReadings(@RequestBody CGMBGReadingsEvent cgmbgReadingsEvent) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.uploadGPTGlucoseReadings(userContext, cgmbgReadingsEvent);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/gpt/glucose-readings/insights"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public GPTGlucoseInsights fetchGPTInsights(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.fetchGPTInsights(userContext, queryParams.get("deviceId"));
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/things-to-do/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfThingsToDoPageView getThingsToDoPage(@RequestParam Map<String, String> queryParams) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getThingsToDoPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/nps/submit"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean submitNPS(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.submitNPS(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/nps/dismiss"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean dismissNPS(@RequestParam String npsDismissDate) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.dismissNPS(userContext, npsDismissDate);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/product/reviews"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfEcommerceProductReviewsPage getProductReviewsPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getProductReviewsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/store/lp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfStoreLPView getStoreLandingPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getStoreLandingPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/store/categories-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfStoreCategoryPageView getStoreCategoriesPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getStoreCategoriesPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/store/lifestyles-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfStoreLifestylePageView getStoreLifestylesPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getStoreLifestylesPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/store/lifestyles-page/v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfStoreLifestylePageView getStoreLifestylesPageV2(@RequestBody ProductSearchRequest productSearchRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getStoreLifestylesPageV2(userContext, productSearchRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/store/order/update-address"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean updateStoreOrderAddress(@RequestBody Map<String, String> requestBody) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateStoreOrderAddress(userContext, requestBody);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/store/order/cancel"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean cancelStoreOrder(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.cancelStoreOrder(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/store/search"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfStoreSearchPageView getStoreSearchPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getStoreSearchPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/store/search/opened"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean setSearchOpened(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.setSearchOpened(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/store/coupons"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<OfferResponse> getUserCoupons(@RequestBody OrderCart orderCart) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getUserCoupons(userContext, orderCart);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/user/active-pack"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ActivePackResponse getActivePackOnUser() {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.getActivePackOnUser(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/rp-subscription/order/create"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RPOrderCreateResponse createSubscriptionOrder(@RequestBody RPOrderCreateRequest rpOrderCreateRequest) throws HttpException {
        return chronicCareService.createSubscriptionOrder(rpOrderCreateRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/rp-subscription/order/confirm"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, Boolean> confirmSubscriptionOrder(@RequestBody RPOrderStatusUpdateRequest rpOrderStatusUpdateRequest) throws HttpException {
        return chronicCareService.confirmSubscriptionOrder(rpOrderStatusUpdateRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/store/reviews/add-review"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfStoreProductAddReviewPage getStoreProductAddReviewPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.getStoreProductAddReviewPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/store/reviews/get-signed-url"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getSignedUrlForStoreProductReview(@RequestParam Map<String, String> queryParams) throws BaseException {
        int fileCount = Integer.parseInt(queryParams.getOrDefault("fileCount", "1"));
        return this.chronicCareService.getSignedUrlForStoreProductReview(fileCount);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/store/reviews/add-review"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean addStoreProductReview(@RequestBody ReviewRequest reviewRequest) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.addStoreProductReview(userContext, reviewRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/cgm/mark-delivery"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean markCgmDelivery(@RequestBody Map<String, String> requestBody) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.markCgmDelivery(userContext, requestBody);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-installation/installed"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean markCgmInstallationViaReader() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.markCgmInstallationViaReader(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/renewal-cancellation-reason"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean saveRenewalCancellationReason(@RequestParam String cancellationReason) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.saveUserRenewalCancelReason(userContext, cancellationReason);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/digital/diet-plan-show-time"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean saveDietPlanShowTime(@RequestParam Long time) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.saveDietPlanShowTime(userContext, time);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/digital/diet-plan-seen"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean saveDietPlanSeen() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.saveDietPlanSeen(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cgm-installation/add-cgm"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean addCGMToAccount() throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.addCGMToAccount(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/live-classes/update-level"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfWellnessLiveSessionsWidgetView updateFitnessLevelForLiveClasses(@RequestBody Map<String, String> requestBody) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.updateFitnessLevelForLiveClasses(userContext, requestBody);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/live-classes/widget"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfWellnessLiveSessionsWidgetView getWellnessLiveSessionsWidget(@RequestBody Map<String, String> requestBody) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return chronicCareService.getWellnessLiveSessionsWidget(userContext, requestBody);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/rashi-event"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean pushRashiEvent(@RequestBody SfRashiEventBody sfRashiEventBody) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.pushRashiEvent(userContext, sfRashiEventBody);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/chat-stories"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<InfoBitesResponse> getChatStories() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getChatStories(userContext);
    }

    // TODO: remove API after UI deployment
    @RequestMapping(method = RequestMethod.GET, value = {"/agent-profile/all"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<PublicAgentResponse> agentProfiles(@RequestParam Map<String, String> queryParams) throws ExecutionException, InterruptedException, OllivanderClientException {
        return this.chronicCareService.agentPublicResponse(new ArrayList<>(), queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/agent-profile/all/v2"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<PublicAgentResponse> agentProfilesV2(@RequestParam Map<String, String> queryParams, @RequestBody List<Long> centerIds) throws ExecutionException, InterruptedException, OllivanderClientException {
        return this.chronicCareService.agentPublicResponse(centerIds, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/agent/slots"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfDatesAvailableWidget agentSlots(@RequestParam Map<String, String> queryParams) {
        Long agent = Long.parseLong(queryParams.get("agentId"));
        Long centerId = null;
        if (queryParams.containsKey("centerId")) {
            centerId = Long.parseLong(queryParams.get("centerId"));
        }
        String productCode = queryParams.getOrDefault("productCode", "SFEXP001");
        return this.chronicCareService.getAgentSlots(agent, productCode, centerId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-exp/area/all"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfExpCenterAreaResponse getSFEXPCenterAreaList(@RequestParam Map<String, String> queryParams) throws OllivanderClientException {
        String city = queryParams.getOrDefault("city", null);
        return this.chronicCareService.getSfExpCenterAreaResponse(city);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-exp/city/all"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfExpCenterCityResponse getSFEXPCenterCityList(@RequestParam Map<String, String> queryParams) throws OllivanderClientException {
        return this.chronicCareService.getSfExpCenterCityResponse();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/self-patient"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PatientDetail getSelfPatient(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getOrCreateChronicCarePatientForUserId(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/ask"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ChatResponse askSFGPT(@RequestBody ChatRequest request) throws HttpException {
        return this.chronicCareService.askGPT(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-gpt/chat/all"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<ChatResponse> getAllGPTChats(@RequestParam Map<String, String> queryParams) throws HttpException {
        String sessionId = queryParams.get("sessionId");
        return this.chronicCareService.getAllChats(sessionId);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/ask/init"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map<String, String> askSFGPTV2(@RequestBody SugarGptDTO sugarGptDTO) throws HttpException {
        return this.chronicCareService.askGPTV2(sugarGptDTO);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-gpt/ask/fetch"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SugarGPTResponse getGPTV2(@RequestParam Map<String, String> queryParams) throws HttpException {
        Long sessionId = Long.valueOf(queryParams.get("sessionId"));
        String prevRunId = queryParams.get("prevRunId");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return this.chronicCareService.getGPTV2(userId, sessionId, prevRunId );
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-gpt/chat/all/v2"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SessionChatResponse getAllGPTChatsV2(@RequestParam Map<String, String> queryParams) throws HttpException {
        String sessionId = queryParams.get("sessionId");
        String page = queryParams.get("page");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getAllChatsV2(userContext, sessionId, page);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/chat/feedback"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean saveRagusChatFeedback(@RequestBody ChatFeedbackDTO chatFeedback) throws HttpException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.saveRagusChatFeedback(userContext, chatFeedback);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/extract/cgm"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ChatResponse extractCGMReport(@RequestBody CGMRequest request) throws HttpException {
        return this.chronicCareService.extractCGM(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/extract/cgm/v2"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String extractCGMInsightsV2(@RequestBody CGMRequest request) throws HttpException {
        return this.chronicCareService.extractCGMInsightsV2(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/extract/cgm/v3"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseAsync extractGPTCGMInsightsV3(@RequestBody CGMRequest request) throws HttpException {
        return this.chronicCareService.extractGPTCGMInsightsV3(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/extract/diagnostic"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ChatResponse extractDiagnosticReport(@RequestBody DiagnosticsReportRequest request) throws HttpException {
        return this.chronicCareService.extractDiagnostic(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/extract/diagnostic/v2"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ChatResponse extractDiagnosticReportV2(@RequestBody DiagnosticsReportRequest request) throws HttpException {
        return this.chronicCareService.extractDiagnosticV2(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/extract/diagnostic/v3"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseAsync extractGPTDiagnosticV3(@RequestBody DiagnosticsReportRequest request) throws HttpException {
        return this.chronicCareService.extractGPTDiagnosticV3(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-gpt/message"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ChatResponse getMessageResponse(@RequestParam Map<String, String> queryParams) throws HttpException {
        String id = queryParams.getOrDefault("id", null);
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getMessage(id, userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-gpt/diagnostic-report/fetch"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DiagnosticReportResponse getGPTDiagnosticReport(@RequestParam Map<String, String> queryParams) throws HttpException {
        String id = queryParams.getOrDefault("id", null);
        String fileUrl = queryParams.getOrDefault("fileUrl", null);
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getGPTDiagnosticReport(userContext, id, fileUrl);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-gpt/cgm-insights/fetch"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public CGMReportResponse getGPTCGMReport(@RequestParam Map<String, String> queryParams) throws HttpException {
        String id = queryParams.getOrDefault("id", null);
        String fileUrl = queryParams.getOrDefault("fileUrl", null);
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getGPTCGMReport(userContext, id, fileUrl);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-gpt/diet-plan/fetch"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SugarGPTResponse getGPTMealPlanResponse(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String id = queryParams.getOrDefault("id", null);
        return this.chronicCareService.getGPTMealPlanResponse(userContext, id);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/sf-gpt/signed-url"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SignedURLResponse getGPTSignedURL(@RequestBody SignedGetURLRequest request) throws HttpException {
        return this.chronicCareService.getGPTSignedURL(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/sf-gpt/signed-url/upload"}, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SignedPostURLResponse getGPTSignedPostUrl(@RequestBody SignedPostURLRequest request) throws HttpException {
        return this.chronicCareService.getGPTSignedPostURL(request);
    }


    @RequestMapping(method = RequestMethod.POST, value = {"/face-based-vitals/viewed-unlock-exp"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean viewedUnlockFbvExp() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markSeenUnlockFbvExpModal(userContext);
    }



    // Digital App
    @RequestMapping(method = RequestMethod.GET, value = {"/digital/tasks-to-do/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DigiTasksToDoPageView getDigiTasksToDoPage(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDigiTasksToDoPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/digital/tasks-journey/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DATasksJourneyPageView getDATaskJourneyPage(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDATaskJourneyPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/digital/lessons-journey/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DALessonsJourneyPageView getDALessonsJourneyPage() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDALessonsJourneyPage(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/digital/diet-plan-journey/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DADietPlanJourneyPageView getDADietPlanJourneyPage() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDADietPlanJourneyPage(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/digital/tasks-to-do/mark"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserTaskResponseEntry updateDigiTasksToDoActivity(@RequestBody UserTaskResponseEntry req) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.updateDigiTasksToDoActivity(userContext, req);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/digital/live-session/log-join"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserSessionLogEntry logLiveSessionJoin(@RequestParam Map<String, String> queryParams) throws Exception {
        String sessionId = queryParams.get("sessionId");
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.logLiveSessionJoin(userContext, sessionId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/digital/book/book"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DigiBookPageView getDigiBookPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDigiBookPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/digital/book/chapter-completed"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean markDigiChapterCompleted(@RequestBody UserChapterTrackingRequest requestBody) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markDigiChapterCompleted(userContext, requestBody);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/digital/book/book-completed"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean markDigiBookCompleted(@RequestBody UserChapterTrackingRequest requestBody) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markDigiBookCompleted(userContext, requestBody);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/digital/book/page-viewed"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean markDigiPageViewed(@RequestBody UserChapterTrackingRequest requestBody) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.markDigiPageViewed(userContext, requestBody);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/digital/book/user-interaction"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean saveUserInteractionOnDigiPage(@RequestBody UserInteractionEntry requestBody) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.saveUserInteractionOnDigiPage(userContext, requestBody);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/digital/book/user-interaction"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserInteractionEntry getUserInteractionOnDigiPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getUserInteractionOnDigiPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/digital/progress"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DAProgressPageView getDAProgressPageView(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDAProgressPageView(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/digital/pre-purchase"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DigiPrePurchasePageView getDigiPrePurchasePage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDigiPrePurchasePage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/digital/score-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public DigiScorePageView getDigiScorePage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDigiScorePage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/ragus-ai/config"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RagusAiConfig getRagusAiConfigs(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getRagusAiConfig(userContext);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/digital/ftue-seen"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public boolean digitalFTUESeenByUser() throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.digitalFTUESeenByUser(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/metric/details-page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfVitalMetricDetailsPageViewV2 getVitalMetricDetailsPage(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getVitalMetricDetailsPage(userContext, queryParams);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/user/diet-preference-metrics"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfUserDietPreferenceMetrics getUserDietPreferenceMetrics(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getUserDietPreferenceMetrics(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/user/diet-preference-metrics-v2"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<SfUserDietPlanPreferenceSteps> getUserDietPreferenceSteps(@RequestParam Map<String, String> queryParams) throws BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getUserDietPreferenceSteps(userContext);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/deeplink/page"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public SfDynamicDeeplinkPage getDynamicDeeplinkAction(@RequestParam Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.chronicCareService.getDynamicDeeplinkAction(userContext, queryParams);
    }

}
