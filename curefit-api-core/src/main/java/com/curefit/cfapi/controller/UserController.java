package com.curefit.cfapi.controller;

import com.curefit.albus.common.BundleProduct;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.enums.Tenant;
import com.curefit.base.enums.UserAgent;
import com.curefit.base.enums.VerticalType;
import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.builder.vm.BuildWidgetResponse;
import com.curefit.cfapi.builder.vm.RequestCache;
import com.curefit.cfapi.builder.vm.WidgetBuilder;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.CfsFormCache;
import com.curefit.cfapi.cache.FeatureStateCache;
import com.curefit.cfapi.cache.MePageSideBarPageConfigService;
import com.curefit.cfapi.constants.AppDeeplink;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.chroniccare.SfInAppChatConfig;
import com.curefit.cfapi.model.internal.common.CityDataResponseV2;
import com.curefit.cfapi.model.internal.exception.UnauthorizedException;
import com.curefit.cfapi.model.internal.parq.ParqFormResponse;
import com.curefit.cfapi.model.internal.slack.SlackAuthRequest;
import com.curefit.cfapi.model.internal.userinfo.*;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.model.mongo.EnergyStreakGamePageConfig;
import com.curefit.cfapi.pojo.announcement.AnnouncementView;
import com.curefit.cfapi.pojo.announcement.AnnouncementViewType;
import com.curefit.cfapi.pojo.announcement.OnDemandAnnouncement;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.autoLaunch.AutoLaunchAction;
import com.curefit.cfapi.pojo.datalake.DatalakeConfig;
import com.curefit.cfapi.pojo.device.ManageDevicesResponse;
import com.curefit.cfapi.pojo.feedback.Feedback;
import com.curefit.cfapi.pojo.intervention.Intervention;
import com.curefit.cfapi.pojo.status.*;
import com.curefit.cfapi.pojo.user.IngressConfig;
import com.curefit.cfapi.pojo.user.UserFormAutoLaunchConfig;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGame;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGameConfig;
import com.curefit.cfapi.pojo.vm.action.CareAction;
import com.curefit.cfapi.pojo.vm.mepage.MePageSideBarSection;
import com.curefit.cfapi.repository.EnergyStreakGamesData;
import com.curefit.cfapi.service.*;
import com.curefit.cfapi.service.Games.MultiPlayerGameService;
import com.curefit.cfapi.service.autoLaunchService.AllAutoLaunchEvaluators;
import com.curefit.cfapi.service.chroniccare.ChronicCareService;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.service.cswatch.CSWatchService;
import com.curefit.cfapi.service.cultsport.CultsportFeedbackService;
import com.curefit.cfapi.service.hamlet.HamletHelper;
import com.curefit.cfapi.service.slack.SlackAuthService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.util.reporting.ReportingUtil;
import com.curefit.cfapi.view.viewbuilders.User.ProfileBuilder;
import com.curefit.cfapi.view.viewmodels.User.FitBitInfo;
import com.curefit.cfapi.view.viewmodels.User.HealthMetricWidget;
import com.curefit.cfapi.view.viewmodels.User.PreferenceDetail;
import com.curefit.cfapi.view.viewmodels.common.CityView;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.community.NotificationTypeRequest;
import com.curefit.cfapi.widgets.hometab.SplashScreenData;
import com.curefit.cfapi.widgets.membership.MembershipWidgetAurora;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.cult.enums.PARQStates;
import com.curefit.cult.models.CultSummary;
import com.curefit.cult.models.PARQStatus;
import com.curefit.cult.services.impl.CultServiceImpl;
import com.curefit.cultsport.feedback.common.models.FeedbackRequest;
import com.curefit.fitcash.model.WalletBalance;
import com.curefit.hamlet.models.response.UserAllocation;
import com.curefit.location.models.City;
import com.curefit.location.models.CityAndCountry;
import com.curefit.location.models.Country;
import com.curefit.location.service.CityCache;
import com.curefit.math.enums.DataType;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.metricservice.exceptions.MetricClientException;
import com.curefit.product.enums.ProductType;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.pojo.UserAttributeEntry;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.segmentation.pojo.SegmentUserListEntry;
import com.curefit.socialservice.enums.NotificationCenterEntryType;
import com.curefit.userservice.pojo.entry.SubUserRelationEntry;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sugarfit.sms.response.NUXStatusResponse;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.util.TextUtils;
import org.joda.time.DateTime;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import response.QuickActionScoreResponse;
import response.SlackRegisterUserResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.curefit.cfapi.constants.Constants.*;
import static com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper.SUGARFIT_PC;
import static com.curefit.cfapi.util.AppUtil.isNewGymFeedbackSupported;
import static com.curefit.cfapi.util.ChronicCareAppUtil.*;
import static com.curefit.cfapi.util.FitnessCenterUtil.getCurrentFitnessMemberships;
import static com.curefit.cfapi.util.UserStreakUtil.USER_STREAK_FEEDBACK_FORM_ID;

@Slf4j
@RestController
@RequestMapping(value = "/user")
@FieldDefaults(level = AccessLevel.PRIVATE)
@ResponseStatus(HttpStatus.OK)
public class UserController {
    List<String> AREA_SELECTOR_ENABLED_CITIES = List.of("Mumbai");
    List<String> AREA_SELECTOR_ANNOUNCEMENT_IDS = List.of("mumbai_split");
    HttpServletRequest request;
    HttpServletResponse response;
    EnvironmentService environmentService;
    SessionBusiness sessionBusiness;
    HamletHelper hamletHelper;
    UserService userService;
    DeviceService deviceService;
    CityCache cityCache;
    NavBarService navBarService;
    InterventionService interventionService;
    CfApiFeedbackService feedbackService;
    CultsportFeedbackService cultsportFeedbackService;
    MePageSideBarPageConfigService mePageSideBarPageConfigService;
    AppLayoutBuilder appLayoutBuilder;
    AnnouncementService announcementService;
    CultServiceImpl cultServiceImpl;
    DefaultPageService defaultPageService;
    ServiceInterfaces serviceInterfaces;
    ApiKeyService apiKeyService;
    SlackAuthService slackAuthService;
    UserOnboardingService userOnboardingService;
    FeatureStateCache featureStateCache;
    ChronicCareService chronicCareService;
    CfsFormCache cfsFormCache;
    WidgetBuilder widgetBuilder;
    ResourceLoader resourceLoader;
    EnergyStreakGamesData energyStreakGamesData;
    AppConfigCache appConfigCache;
    ObjectMapper objectMapper;
    UserAttributesClient userAttributesClient;
    PromUtil promUtil;
    UserAgentService userAgentService;
    CookieService cookieService;
    CSWatchService csWatchService;
    ExceptionReportingService exceptionReportingService;
    AllAutoLaunchEvaluators allAutoLaunchEvaluators;
    UserSegmentClient userSegmentClient;
    @Autowired
    public UserController(
            HttpServletRequest request,
            HttpServletResponse response,
            EnvironmentService environmentService,
            SessionBusiness sessionBusiness,
            HamletHelper hamletHelper,
            UserService userService,
            DeviceService deviceService,
            CityCache cityCache,
            NavBarService navBarService,
            InterventionService interventionService,
            CfApiFeedbackService feedbackService,
            MePageSideBarPageConfigService mePageSideBarPageConfigService,
            AppLayoutBuilder appLayoutBuilder,
            AnnouncementService announcementService,
            CultServiceImpl cultServiceImpl,
            DefaultPageService defaultPageService,
            ServiceInterfaces serviceInterfaces,
            ApiKeyService apiKeyService,
            SlackAuthService slackAuthService,
            UserOnboardingService userOnboardingService,
            FeatureStateCache featureStateCache,
            ChronicCareService chronicCareService,
            CfsFormCache cfsFormCache,
            WidgetBuilder widgetBuilder,
            ResourceLoader resourceLoader,
            ObjectMapper objectMapper,
            EnergyStreakGamesData energyStreakGamesData,
            AppConfigCache appConfigCache,
            UserAttributesClient userAttributesClient,
            PromUtil promUtil, UserAgentService userAgentService, CookieService cookieService,
            CSWatchService csWatchService,
            CultsportFeedbackService cultsportFeedbackService,
            AllAutoLaunchEvaluators allAutoLaunchEvaluators,
            ExceptionReportingService exceptionReportingService,
            UserSegmentClient userSegmentClient
    ) {
        this.request = request;
        this.response = response;
        this.environmentService = environmentService;
        this.sessionBusiness = sessionBusiness;
        this.hamletHelper = hamletHelper;
        this.userService = userService;
        this.deviceService = deviceService;
        this.cityCache = cityCache;
        this.navBarService = navBarService;
        this.interventionService = interventionService;
        this.feedbackService = feedbackService;
        this.mePageSideBarPageConfigService = mePageSideBarPageConfigService;
        this.appLayoutBuilder = appLayoutBuilder;
        this.announcementService = announcementService;
        this.cultServiceImpl = cultServiceImpl;
        this.defaultPageService = defaultPageService;
        this.serviceInterfaces = serviceInterfaces;
        this.apiKeyService = apiKeyService;
        this.slackAuthService = slackAuthService;
        this.userOnboardingService = userOnboardingService;
        this.featureStateCache = featureStateCache;
        this.chronicCareService = chronicCareService;
        this.cfsFormCache = cfsFormCache;
        this.widgetBuilder = widgetBuilder;
        this.resourceLoader = resourceLoader;
        this.energyStreakGamesData = energyStreakGamesData;
        this.appConfigCache = appConfigCache;
//        this.countryService = countryService;
        this.objectMapper = objectMapper;
        this.userAttributesClient = userAttributesClient;
        this.promUtil = promUtil;
        this.userAgentService = userAgentService;
        this.cookieService = cookieService;
        this.csWatchService = csWatchService;
        this.cultsportFeedbackService = cultsportFeedbackService;
        this.exceptionReportingService = exceptionReportingService;
        this.allAutoLaunchEvaluators = allAutoLaunchEvaluators;
        this.userSegmentClient = userSegmentClient;
    }
    
    @PostMapping("/addToSegment/{segmentId}")
    public Map<String, Action> addToSegment(@PathVariable String segmentId, @RequestParam(required = false) String successListPageId) throws HttpException, UnauthorizedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = userContext.getUserProfile().getUserId();
        if (StringUtils.isBlank(userId) || userId.equals("0")) throw new UnauthorizedException("User not authorized. User ID is blank or zero.");
        else if (!StringUtils.isNumeric(segmentId)) throw new IllegalArgumentException("Invalid segmentId format");
        SegmentUserListEntry segmentUserListEntry = new SegmentUserListEntry();
        segmentUserListEntry.setUserId(Long.valueOf(userId));
        segmentUserListEntry.setSegmentId(Long.valueOf(segmentId));
        userSegmentClient.addUsersToSegment(segmentUserListEntry, AppTenant.CUREFIT);
        Map<String, Action> response = new HashMap<>();
        if (StringUtils.isBlank(successListPageId)) successListPageId = "curefit://hometab";
        else {
            successListPageId = "curefit://listpage?pageId="+ successListPageId;
        }
        // For our use case changing the action type to REFRESH_LIST_PAGE, in this case no use of successListPageId as this function will refresh the current list page.
        response.put("action", new Action(successListPageId, ActionType.REFRESH_LIST_PAGE));
        return response;
    }
    
    
    @RequestMapping(value = "/cultSummaryTest", method = RequestMethod.POST)
    @ResponseBody
    public CultSummary getCultSummaryTest(
            @CookieValue(value = "deviceId", required = false) String deviceIdCookie,
            @RequestBody UserStatusRequestBody requestBody,
            @RequestParam(value = "apikey", required = false) String apikeyQueryParam,
            @RequestParam Map<String, String> allParams)
            throws IOException, ExecutionException, InterruptedException, BaseException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        CultSummary cultSummary = cultServiceImpl.getCultSummary(userContext.getUserProfile().getUserId());
        return cultSummary;
    }

    @RequestMapping(method = RequestMethod.GET, value = {
            "/quickAction/{userId}"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public QuickActionScoreResponse quickAction(@PathVariable String userId) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        UserProfile userProfile = new UserProfile();
        userProfile.setCity(cityCache.getCityById("Bangalore"));
        userProfile.setUserId(userId);
        userContext.setUserProfile(userProfile);
        userContext.setRequestCache(new RequestCache(this.serviceInterfaces));
        return this.serviceInterfaces.quickActionService.computeScore(userContext, userId);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/experiments"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Object getExperiments() throws ExecutionException, InterruptedException {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.serviceInterfaces.hamletHelper.getUserAllocations(userContext, Collections.emptyList()).get();
    }

    @RequestMapping(value = "/announcementTest", method = RequestMethod.POST)
    @ResponseBody
    public AnnouncementView getAnnouncementTest(
            @CookieValue(value = "deviceId", required = false) String deviceIdCookie,
            @RequestBody UserStatusRequestBody requestBody,
            @RequestParam(value = "apikey", required = false) String apikeyQueryParam,
            @RequestParam Map<String, String> allParams)
            throws IOException, ExecutionException, InterruptedException, BaseException {
        Session session = this.userService.createDeviceLoginIfNoSession(requestBody, deviceIdCookie, apikeyQueryParam);
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = session.getUserId();
        CompletableFuture<UserAllocation> hamletUserExperimentPromise = this.hamletHelper
                .getUserAllocations(userContext, Collections.emptyList());
        UserProfile cfUserProfile = userContext.getUserProfile();
        cfUserProfile.setHamletUserExperimentPromise(hamletUserExperimentPromise);
        CompletableFuture<AnnouncementView> announcementPromise = this.announcementService
                .getActiveAnnouncement(userContext);
        return announcementPromise.join();
    }

    @RequestMapping(value = "/feedbackTest", method = RequestMethod.POST)
    @ResponseBody
    public Feedback getFeedbackTest(
            @CookieValue(value = "deviceId", required = false) String deviceIdCookie,
            @RequestBody UserStatusRequestBody requestBody,
            @RequestParam(value = "apikey", required = false) String apikeyQueryParam,
            @RequestParam Map<String, String> allParams)
            throws IOException, ExecutionException, InterruptedException, BaseException {
        Session session = this.userService.createDeviceLoginIfNoSession(requestBody, deviceIdCookie, apikeyQueryParam);
        String userId = session.getUserId();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        List<String> allUserIds = new ArrayList<>();
        allUserIds.add(userId); // TODO: Add subUSerIds to this list
        CompletableFuture<Feedback> feedbackPromise = this.feedbackService.getFeedbackToShow(allUserIds, userContext);
        return feedbackPromise.join();
    }

    @RequestMapping(value = "/interventionTest", method = RequestMethod.POST)
    @ResponseBody
    public Intervention getInterventionTest(
            @CookieValue(value = "deviceId", required = false) String deviceIdCookie,
            @RequestBody UserStatusRequestBody requestBody,
            @RequestParam(value = "apikey", required = false) String apikeyQueryParam,
            @RequestParam Map<String, String> allParams)
            throws IOException, ExecutionException, InterruptedException, BaseException {
        Session session = this.userService.createDeviceLoginIfNoSession(requestBody, deviceIdCookie, apikeyQueryParam);
        String userId = session.getUserId();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        List<String> allUserIds = new ArrayList<>();
        allUserIds.add(userId); // TODO: Add subUSerIds to this list
        CompletableFuture<Intervention> interventionPromise = this.interventionService
                .getInterventionToShow(userContext);
        return interventionPromise.join();
    }

    @RequestMapping(value = "/background-details", method = RequestMethod.GET)
    @ResponseBody
    public BackgroundDetails getBackgroundDetails(
    ) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return userService.getBackgroundDetails(userContext, serviceInterfaces, null);
    }

    @RequestMapping(value = "/background-details", method = RequestMethod.POST)
    @ResponseBody
    public BackgroundDetails getBackgroundDetailsWithBody(
            @RequestBody NotificationTypeRequest notificationTypeRequest
    ) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        List<NotificationCenterEntryType> notifTypes = notificationTypeRequest.getNotifTypes();
        return userService.getBackgroundDetails(userContext, serviceInterfaces, notifTypes);
    }

    @RequestMapping(value = "/clearNotificationsCounter", method = RequestMethod.PUT)
    public ResponseEntity clearNotificationsCounter(
    ) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        userService.clearNotificationsCounter(userContext.getUserProfile().getUserId());
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @RequestMapping(value = "/clearNotificationsCounterV2", method = RequestMethod.POST)
    public ClearNotifResponse clearNotificationsCounterV2(
            @RequestBody NotificationTypeRequest notificationTypeRequest,
            HttpServletRequest request
    ) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        List<NotificationCenterEntryType> notifTypes = notificationTypeRequest.getNotifTypes();
        return userService.clearNotificationsCounterV2(userContext.getUserProfile().getUserId(), notifTypes);
    }

    @RequestMapping(value = "/status", method = RequestMethod.POST)
    @ResponseBody
    public UserStatusResponse getStatus(
            @CookieValue(value = "deviceId", required = false) String deviceIdCookie,
            @RequestBody(required = false) UserStatusRequestBody requestBody,
            @RequestParam(value = "apikey", required = false) String apikeyQueryParam,
            @RequestHeader(value = "osname", required = false) String osName,
            @RequestHeader(value = "codepushversion", required = false) String codePushVersion,
            @RequestHeader(value = "api-key", required = false) String apiKey,
            @RequestParam(value = "webclientId", required = false) String webclientId,
            @RequestParam(value = "firebaseInstanceId", required = false) String firebaseInstanceId,
            @RequestParam(required = false) Map<String, String> allParams)
            throws IOException, ExecutionException, InterruptedException, BaseException {
        ReportingUtil.userStatusHitCounter.increment();
        if (requestBody == null) {
            requestBody = new UserStatusRequestBody();
        }
        // Populate web client and firebase instance identifiers from params or headers if present
        String webClientIdHeaderCamel = request.getHeader("webClientId");
        String webClientIdHeaderKebab = request.getHeader("web-client-id");
        String firebaseInstanceIdHeaderCamel = request.getHeader("firebaseInstanceId");
        String firebaseInstanceIdHeaderKebab = request.getHeader("firebase-instance-id");

        if (requestBody.getWebClientId() == null || requestBody.getWebClientId().isEmpty()) {
            String resolvedWebClientId = webclientId != null && !webclientId.isEmpty()
                    ? webclientId
                    : (webClientIdHeaderCamel != null && !webClientIdHeaderCamel.isEmpty()
                        ? webClientIdHeaderCamel
                        : webClientIdHeaderKebab);
            requestBody.setWebClientId(resolvedWebClientId);
        }
        if (requestBody.getFirebaseInstanceId() == null || requestBody.getFirebaseInstanceId().isEmpty()) {
            String resolvedFirebaseInstanceId = firebaseInstanceId != null && !firebaseInstanceId.isEmpty()
                    ? firebaseInstanceId
                    : (firebaseInstanceIdHeaderCamel != null && !firebaseInstanceIdHeaderCamel.isEmpty()
                        ? firebaseInstanceIdHeaderCamel
                        : firebaseInstanceIdHeaderKebab);
            requestBody.setFirebaseInstanceId(resolvedFirebaseInstanceId);
        }
        Session session = this.userService.createDeviceLoginIfNoSession(requestBody, deviceIdCookie, apikeyQueryParam);
        if (StringUtils.isNotBlank(session.getDeviceId()) && session.getDeviceId().contains("s:")) {
            serviceInterfaces.exceptionReportingService
                    .reportWarningMessage("Device Id has been multi-encoded with initial value = "
                            + session.getDeviceId() + "and deviceIdCookie = " + deviceIdCookie);
        }
        String userId = session.getUserId();
        UserAgent userAgent = session.getUserAgent();
        String ip = AppUtil.getClientIpAddr(request);
        if (apiKey == null) {
            apiKey = request.getHeader("apikey");
        }
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        AppTenant tenant = AppUtil.getAppTenantFromUserContext(userContext);
        if (tenant == AppTenant.CUREFIT) {
            this.serviceInterfaces.tata3pServiceHelper.linkUser(userId);
        }
        boolean isTVApp = AppUtil.isTVApp(apiKey, apiKeyService);
        Boolean istest = Objects.equals(request.getHeader("test"), "true");
        Boolean addCfApiDatalakeEndpoint = TEST_USER_IDS.contains(userId);

        Float clientVersion = NumberUtils.isParsable(request.getHeader("clientversion"))
                ? Float.parseFloat(request.getHeader("clientversion"))
                : null;
        Float appVersion = NumberUtils.isParsable(request.getHeader("appversion"))
                ? Float.parseFloat(request.getHeader("appversion"))
                : null;
        Float latitude = NumberUtils.isParsable(request.getHeader("lat")) ? Float.parseFloat(request.getHeader("lat"))
                : null;
        Float longitude = NumberUtils.isParsable(request.getHeader("lon")) ? Float.parseFloat(request.getHeader("lon"))
                : null;
        Boolean isInternationalApp = AppUtil.isInternationalApp(userContext);
        Boolean isSugarFitOrUltraFitApp = AppUtil.isSugarFitOrUltraFitApp(userContext);
        Boolean isMindFitApp = AppUtil.isMindFitApp(userContext);
        Boolean isCultWatchApp = AppUtil.isCultWatchApp(userContext);
        Boolean isCultSportApp = AppUtil.isCultSportWebApp(userContext);
        Boolean isWebClp = AppUtil.isCenterLevelPricingSupportedWeb(this.serviceInterfaces, this.environmentService, userContext) || AppUtil.isCenterLevelPricingSupportedWebV2(this.serviceInterfaces, this.environmentService, userContext);
        boolean isPlayVisibilityExp = PlayUtil.isPlayHomePageExperimentSupported(userContext, this.environmentService, this.serviceInterfaces.segmentEvaluatorService);
        boolean isPlayVisibilityControlExp = PlayUtil.isPlayHomePageExperimentControlBucket(userContext, this.environmentService, this.serviceInterfaces.segmentEvaluatorService);

        String passedCityId = requestBody.getCityId();
        String advertiserId = requestBody.getAdvertiserId();

        boolean isWeb = AppUtil.isWeb(userContext);

        log.debug("Is CultWatch App? " + isCultWatchApp);

        if (!userContext.getSessionInfo().getIsUserLoggedIn()) {
            userContext.setUserEntryCompletableFuture(this.userService.getUser(userId));
            userContext.setRequestCache(new RequestCache(this.serviceInterfaces));
        }
        UserEntry user = userContext.getUserEntryCompletableFuture().join();
        if (user.getId() == null) {
            this.serviceInterfaces.exceptionReportingService.reportErrorMessage(
                    String.format("UserId NotReturned for req userid: %s", userId != null ? userId : "falsy userid"));
        }

        // To enable internal users to use international app without VPN
        // passedCityId will be set to session as city
        if (isInternationalApp && user.getIsInternalUser()) {
            if (passedCityId == null) {
                City cityUSA = this.cityCache.getDefaultCityForCountry("US");
                if (cityUSA != null) {
                    passedCityId = cityUSA.getCityId();
                }
            }
            session.getSessionData().setIsLocationServiceable(true);
        }

        // update consumeSalt in session if not present
        if (session.getSessionData().getTlaSemusnoc() == null) {
            session.getSessionData().setTlaSemusnoc(UUID.randomUUID().toString());
        }

        CompletableFuture<UserAllocation> hamletUserExperimentPromise = userContext.getUserProfile()
                .getHamletUserExperimentPromise();
        if (hamletUserExperimentPromise == null) {
            hamletUserExperimentPromise = this.hamletHelper.getUserAllocations(userContext, Collections.emptyList());
            userContext.getUserProfile().setHamletUserExperimentPromise(hamletUserExperimentPromise);
        }

        CompletableFuture<BuildWidgetResponse> cultsportNavWidgets = isCultSportApp ? this.widgetBuilder.buildWidgets(
                List.of(AppUtil.getCultsportSideNavBarWidgetId(this.environmentService, userContext)), userContext,
                new WidgetContext()) : null;
        CompletableFuture<List<ActiveDIYPackViewV1>> diySubscriptionsPromise = isCultSportApp || !AppUtil.isUserCultLiveMember(userContext)
                ? null : this.userService.getDIYActivePacks(userContext);
        FindPreferredCityResponse updateCityResponse = this.userService.findPreferredCity(tenant, latitude, longitude,
                ip, session, passedCityId, userContext, serviceInterfaces.cfAnalytics);
        userContext.setPreferredCity(updateCityResponse);
        City city = updateCityResponse.getCity();
        Boolean isCityChangeToBeNotified = updateCityResponse.getIsCityChangeToBeNotified();

        // For not logged in user, to pass on the timezone and city information from
        // detected city
        if (userContext.getUserProfile() == null) {
            UserProfile userProfile = new UserProfile();
            userProfile.setUserId(session.getUserId());
            userProfile.setCity(city);
            userContext.setUserProfile(userProfile);
        }

        DetectedCity detectedCity = new DetectedCity();
        detectedCity.setCity(updateCityResponse.getDetectedCityName());
        detectedCity.setCountry(updateCityResponse.getDetectedCountryCode());
        detectedCity.setLocation(updateCityResponse.getDetectedLocation());
        detectedCity.setPincode(updateCityResponse.getDetectedPostalCode());

        session.getSessionData().setCityId(city.getCityId());
        session.getSessionData().setIsCityManuallySelected(updateCityResponse.getIsCityManuallySelected());
        session.getSessionData().setDetectedCity(detectedCity);

        try {
            if (Objects.nonNull(userId) && AppUtil.isSugarFitOrUltraFitApp(userContext) && Objects.nonNull(detectedCity.getCity())) {
                UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
                userAttributeEntry.setUserId(Long.valueOf(userId));
                userAttributeEntry.setAppTenant(tenant);
                userAttributeEntry.setAttribute("sf_ip_detected_city");
                userAttributeEntry.setAttrValue(detectedCity.getCity());
                userAttributeEntry.setDataType(DataType.STRING);
                this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, tenant);
            }
        } catch (Exception e) {
            log.info("Error in updating detected city to rashi in java for user::" + userId);
        }
        LocationPreferenceData currentGearLocationPref = session.getSessionData().getGearLocationPreference();
        if (isCultSportApp && currentGearLocationPref != null
                && StringUtils.isNotBlank(updateCityResponse.getDetectedPostalCode())
                && StringUtils.isBlank(currentGearLocationPref.getPincode())) {
            currentGearLocationPref.setPincode(updateCityResponse.getDetectedPostalCode());
            session.getSessionData().setGearLocationPreference(currentGearLocationPref);
        }
        session.getSessionData().setDetectedCity(detectedCity);
        // Updates city & slat if needed and also used to update session update time to
        // extend the session ttl
        CompletableFuture<Session> updateSessionPromise = istest ? null
                : this.sessionBusiness.updateSessionData(session.getAt(), session.getSessionData(),
                session.getIsNotLoggedIn(), isCityChangeToBeNotified);

        // update sidebar config
        List<CareAction> careMappedActions = CareUtil.addCareMappedActions(appVersion, city.getCityId(),
                user.getIsInternalUser());
        List<MePageSideBarSection> sideBar = this.mePageSideBarPageConfigService.getMePageResponse(userContext,
                careMappedActions);

        // fill countries data
        List<Country> countries = this.cityCache.listCountries(Tenant.fromString(tenant.toString()));
        List<CountryData> countriesData = countries.stream().map(country -> {
            CountryData countryData = new CountryData();
            countryData.setCountryId(country.getCountryId());
            countryData.setName(country.getName());
            countryData.setCountryCallingCode(country.getCountryCallingCode());
            countryData.setPhoneLoginSupported(country.getPhoneLoginSupported());
            countryData.setFlagImage(country.getFlagImage());
            countryData.setPhoneNumberMaxLength(country.getPhoneNumberMaxLength());
            return countryData;
        }).collect(Collectors.toList());

        if (!AppUtil.isDubaiEnabled(userContext)) {
            countriesData = countriesData.stream().filter(country -> country.countryId.equals("IN"))
                    .collect(Collectors.toList());
        }

        SleepConfig sleepConfig = AppUtil.getSleepConfig(appVersion, osName);
        List<String> allUserIds = new ArrayList<>();
        allUserIds.add(userId);
        if (user.getSubUserRelations() != null) {
            for (SubUserRelationEntry relation : user.getSubUserRelations()) {
                allUserIds.add(relation.getSubUserId());
            }
        }
        String currency = userContext.getUserProfile().getCity().getCountry().getCurrencyCode();
        serviceInterfaces.getTaskExecutor().submit(() -> {
            // do api call
            return this.serviceInterfaces.fitcashService.depositFitcashSeeding(userId, currency);
        });
        CompletableFuture<Intervention> interventionPromise = isCultSportApp ? null
                : this.interventionService.getInterventionToShow(userContext);
        CompletableFuture<Feedback> feedbackPromise = isCultSportApp ? null
                : this.feedbackService.getFeedbackToShow(allUserIds, userContext);
        CompletableFuture<FeedbackRequest> cultsportFeedbackRequestPromise = isCultSportApp && userContext.getSessionInfo().getIsUserLoggedIn()
                ? CompletableFuture.supplyAsync(() -> {
            try {
                return this.cultsportFeedbackService.getCultsportFeedbackToShow(allUserIds.get(0), userContext);
            } catch (Exception err) {
                this.serviceInterfaces.exceptionReportingService.reportException("Failed to fetch feedback for cultsport ",
                        err);
            }
            return null;
        }, this.serviceInterfaces.getTaskExecutor()) : null;

        CompletableFuture<AnnouncementView> announcementPromise = isCultSportApp ? null
                : this.announcementService.getSingleActiveAnnouncement(userContext, true, null, false);

        CompletableFuture<String> defaultPageIdPromise = this.defaultPageService.getDefaultPageId(userContext);

        UserStatusResponse ret = new UserStatusResponse();
        ret.setUser(new UserStatusView(user, session.getIsNotLoggedIn()));
        ret.setSession(new SessionView(session, null));
        ret.setIsV2universalGymCheckin(true);
        AppLayout appLayout = null;

        Boolean shouldToggleAuroraThemeOnWeb = false;

        if (isWeb && !isCultSportApp) {
            shouldToggleAuroraThemeOnWeb = AppUtil.shouldAppendAndDisplayFitnessHubTab(userContext,
                    hamletUserExperimentPromise, serviceInterfaces.getSegmentEvaluatorService(), environmentService,
                    apiKeyService);
        }

        ret.setShowNewAuroraTheme(shouldToggleAuroraThemeOnWeb);
        ret.setLimitGxExperienceEnabled(AppUtil.doesUserBelongToLimitedGxExperiment(serviceInterfaces, userContext));

        if (isCultSportApp) {
            ret.setSideBar(sideBar);
            ret.setAppTabs(new ArrayList<>());
            ret.setTlaSemusnoc(session.getSessionData().getTlaSemusnoc());
        } else if (!isTVApp) {
            CompletableFuture<VerticalsWithHighlightInformation> verticalInfoPromise = this.navBarService
                    .getUserAndSupportedVerticalsInfo(userContext, city, session.getUserId(),
                            hamletUserExperimentPromise,
                            serviceInterfaces.getSegmentEvaluatorService());
            ret.setRedirectUri(AuthUtil.getRedirectUrlIfNeeded(userContext, this.slackAuthService, requestBody, request,
                    session.getAt(), session.getIsNotLoggedIn()));
            ret.setPlanTabItem(AppUtil.getPlanTabs(osName, appVersion, false, user.getIsInternalUser()));
            ret.setGoalPlanExists(false);
            ret.setSideBar(sideBar);

            VerticalsWithHighlightInformation verticalInfo = verticalInfoPromise.get();
            List<VerticalType> supportedVerticals = verticalInfo.getVerticals().stream()
                    .map(VerticalInfo::getVerticalType).collect(Collectors.toList());
            try {
                CompletableFuture<AppLayout> var = this.appLayoutBuilder.fetchPageLayout(userContext,
                        supportedVerticals, allParams);
                if (var == null) {
                    log.error("AppLayoutBuilder returns null value");
                }
                appLayout = var.get();
            } catch (Exception err) {
                this.serviceInterfaces.exceptionReportingService.reportException("Failed to fetch app layout ", err);
            }

            ret.setVerticals(verticalInfo.getVerticals());
            ret.setIsMoreToBeHighlighted(verticalInfo.getIsMoreToBeHighlighted());

            List<TabType> appTabs = new ArrayList<>();

            if (AppUtil.isInternationalTLApp(userContext)) {
                appTabs.add(TabType.TLHOME);
                appTabs.add(TabType.TRAINERS);
                appTabs.add(TabType.WORKOUTS);
                appTabs.add(TabType.COLLECTIONS);
            } else if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                ret.setSocialLoginSupportedForChronicCare(true);
                Boolean sfPackPurchased = this.userOnboardingService.getIfSugarFitPackActive(userId);
                boolean isFreemium = this.serviceInterfaces.chronicCareServiceHelper.isSfFreemiumUser(userContext);

                BundleProduct activeBundle = null;
                Optional<ActivePackResponse> activePackResponseOptional = this.userOnboardingService.getSugarFitActivePack(userId);
                if (activePackResponseOptional.isPresent()) {
                    activeBundle = activePackResponseOptional.get().getBundleProduct();
                }

                if (DigitalAppUtil.isDigitalAppUser(activeBundle, isFreemium)){
                    appTabs.add(TabType.SF_DIGI_HOME);
                    appTabs.add(TabType.SF_DIGI_PROGRESS);
                    appTabs.add(TabType.SF_LOGGING);
                    appTabs.add(TabType.SF_FITNESS);
                    if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)) {
                        appTabs.add(TabType.SF_STORE);
                    } else {
                        appTabs.add(TabType.SF_COMMUNITY);
                    }
                } else if (sfPackPurchased) {
                    if (serviceInterfaces.getChronicCareServiceHelper().isSpecialSugarControlPlanPack(activeBundle)) {
                        appTabs.add(TabType.SF_HOME);
                        appTabs.add(TabType.SF_LOGGING);
                        appTabs.add(TabType.SF_STORE);
                    } else {
                        appTabs.add(TabType.SF_HOME);
                        if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)) {
                            appTabs.add(TabType.SF_STORE);
                        } else {
                            appTabs.add(TabType.SF_NUTRITION);
                        }
                        appTabs.add(TabType.SF_LOGGING);
                        appTabs.add(TabType.SF_FITNESS);

                        if (isInAppChatEnabledForUser(userContext, activeBundle, this.serviceInterfaces.chronicCareServiceHelper)) {
                            appTabs.add(TabType.SF_CHAT);
                        } else if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)
                                && this.serviceInterfaces.chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(activeBundle)) {
                            appTabs.add(TabType.SF_NUTRITION);
                        } else {
                            appTabs.add(TabType.SF_GOALS);
                        }
                    }
                } else {
                    appTabs.add(TabType.SF_HOME);
                    BundleProduct expiredBundle = null;
                    Optional<ActivePackResponse> sugarFitExpiredPack = this.userOnboardingService.getSugarFitExpiredPack(userContext.getUserProfile().getUserId());
                    if (sugarFitExpiredPack.isPresent() && sugarFitExpiredPack.get().getBundleProduct() != null) {
                        expiredBundle = sugarFitExpiredPack.get().getBundleProduct();
                    }
                    Boolean sfPackExpired = sugarFitExpiredPack.isPresent() && sugarFitExpiredPack.get().getBundleProduct() != null && (!sugarFitExpiredPack.get().getBundleProduct().getIsTrialProduct());
                    if (sfPackExpired) {
                        if (serviceInterfaces.getChronicCareServiceHelper().isSpecialSugarControlPlanPack(expiredBundle)) {
                            appTabs.add(TabType.SF_HOME);
                            appTabs.add(TabType.SF_LOGGING);
                            appTabs.add(TabType.SF_STORE);
                        } else {
                            if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)) {
                                appTabs.add(TabType.SF_STORE);
                            } else {
                                appTabs.add(TabType.SF_NUTRITION);
                            }
                            appTabs.add(TabType.SF_LOGGING);
                            appTabs.add(TabType.SF_FITNESS);

                            if (isInAppChatEnabledForUser(userContext, expiredBundle, this.serviceInterfaces.chronicCareServiceHelper)) {
                                appTabs.add(TabType.SF_CHAT);
                            } else if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)
                                    && this.serviceInterfaces.chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(expiredBundle)) {
                                appTabs.add(TabType.SF_NUTRITION);
                            } else {
                                appTabs.add(TabType.SF_GOALS);
                            }
                        }
                    }
                }
                String language = this.chronicCareService.getSugarfitAppLanguage(userContext);
                if (language != null) {
                    ret.setSugarfitAppLanguage(language);
                }
                if (ChronicCareAppUtil.isSfAppLocalisationSupportedUser(userContext)) {
                    ret.setSugarfitAppLanguageEnabled(true);
                }
            } else if (isMindFitApp) {
                appTabs.add(TabType.PLAN);
                appTabs.add(TabType.MIND);
            } else if (isCultWatchApp) {
                appTabs.add(TabType.WATCH_HOME);
                appTabs.add(TabType.WATCH_FITNESS);
                if (AppUtil.isUserPartOfChallengeExp(this.serviceInterfaces.segmentEvaluatorService, this.environmentService, userContext)) {
                    appTabs.add(TabType.WATCH_CHALLENGE);
                }
                appTabs.add(TabType.WATCH_SETTINGS);
            } else {
                if (isPlayVisibilityExp) {
                    appTabs.add(TabType.HOMETABHUB);
                } else {
                    appTabs.add(TabType.PLAN);
                }
                appTabs.addAll(supportedVerticals.stream().map(verticalType -> TabType.valueOf(verticalType.toString()))
                        .collect(Collectors.toList()));
            }

            ret.setAppTabs(appTabs); // appTabs can only handle 5 tabs

            if (!isPlayVisibilityExp) {
                ret.setTabScreenSelectedIndex(AppUtil.getSelectedTabIndex(userAttributesClient, userId, userContext, ret, serviceInterfaces));
            } else {
                AppUtil.getHomeTabPreferredPageId(ret, userAttributesClient, userId, userContext);
            }

            ret.setAppLayout(appLayout);
            ret.setTlaSemusnoc(session.getSessionData().getTlaSemusnoc());
            // Microapp configuration
            Map<String, MicroAppConfig> microAppConfig = new HashMap<>();
            microAppConfig.put("GEAR", new MicroAppConfig(true)); // Gear microapp enabled for all
            ret.setMicroapps(microAppConfig);

        } else {
            try {
                appLayout = this.appLayoutBuilder.fetchPageLayoutForTV(apiKey).get();
                ret.setMembershipDetails(serviceInterfaces.userService.getActiveMembershipDetails(userContext, userId));
            } catch (Exception err) {
                this.serviceInterfaces.exceptionReportingService.reportException("Failed to fetch app layout ", err);
            }
        }
        ret.setAppLayout(appLayout);
        ret.setCityId(city.getCityId());
        ret.setCityName(city.getName());

        ret.setDetectedCity(detectedCity);

        ret.setTimezone(city.getTimezone());
        ret.setCountryId(city.getCountryId());
        ret.setShowCitySelection(
                updateCityResponse.getShowCitySelection() && !isSugarFitOrUltraFitApp && !isMindFitApp);
        ret.setCountriesData(countriesData);
        ret.setSleepConfig(sleepConfig);
        ret.setCityRepresentativeLat(city.getRepresentativeLatLong().getLatitude());
        ret.setCityRepresentativeLon(city.getRepresentativeLatLong().getLongitude());
        String defaultPageId = defaultPageIdPromise.join();
        ret.setDefaultPageId(defaultPageId);
        ret.setIsPartOfCenterLevelPricingWebSegment(isWebClp);
        ret.setMaxTrialCount(AppUtil.doesUserBelongToTrialReductionSegment(userContext, serviceInterfaces) ? Constants.MAX_TRIAL_COUNT_EXPERIMENT : Constants.MAX_TRIAL_COUNT);

        if (!isCultSportApp) {
            ret.setActiveMembershipPresent(MembershipUtil.activeMembershipPresent(serviceInterfaces, userContext));
        }

        EnergyStreakGame game = null;
        String gameCompleteKey = null;
        try {
            EnergyStreakGamePageConfig gamePageConfig = this.userService.getGameConfig();
            EnergyStreakGameConfig gameConfig = gamePageConfig.getData();
            if (gameConfig != null && gameConfig.getIsActive()) {
                for (int i = 0; i < gameConfig.getGames().size(); i++) {
                    if (gameConfig.getGames().get(i).getIsActive() && AppUtil.doesUserBelongToGameSegmentV2(gameConfig.getGames().get(i), serviceInterfaces, userContext)) {
                        game = gameConfig.getGames().get(i);
                        gameCompleteKey = MultiPlayerGameService.getGameCompletionKey(game);
                        break;
                    }
                }
            }

        } catch (Exception e) {
            log.error("Unable find and decide user's game for energy streak V2 due to" + e.getMessage());
            serviceInterfaces.exceptionReportingService
                    .reportException(new Exception("Unable to find energy V2 streak game due to" + e.getMessage()));
        }
        AnnouncementView announcementView = announcementPromise != null ? announcementPromise.join() : null;
        Intervention intervention = interventionPromise != null ? interventionPromise.join() : null;
        boolean isCitySplitAnnouncement = announcementView != null
                && announcementView.getType() == AnnouncementViewType.NEW_FEATURE
                && AREA_SELECTOR_ANNOUNCEMENT_IDS.contains(announcementView.getAnnouncementId());
        UserStatusResponse.AreaSelectionData areaSelectionData = new UserStatusResponse.AreaSelectionData();
        if (city.getParentCityId() != null) {
            ret.setCityId(city.getParentCityId());
        }
        if (isCitySplitAnnouncement) {
            areaSelectionData.setDismissAction(announcementView.getDismissAction());
            areaSelectionData.setIsAreaSelectionEnabled(true);
            announcementView = null;
        }
        if (isCitySplitAnnouncement && city.getParentCityId() != null
                && AREA_SELECTOR_ENABLED_CITIES.contains(city.getParentCityId()) && !ret.getShowCitySelection()) {
            areaSelectionData.setShowAreaSelection(true);
        }
        ret.setAreaSelectionData(areaSelectionData);
        Feedback feedback = feedbackPromise != null ? feedbackPromise.join() : null;
        if (announcementView != null) {
            ret.setAnnouncementData(announcementView);
        } else if (intervention != null) {
            ret.setIntervention(new InterventionMetaView(intervention.getInterventionId(), intervention.getType()));
        } else if (isCultSportApp) {
            ret.setFeedbackId(cultsportFeedbackRequestPromise != null && cultsportFeedbackRequestPromise.get() != null
                    ? cultsportFeedbackRequestPromise.get().getId()
                    : null);
        } else if (isSugarFitOrUltraFitApp && feedback != null && SUGARFIT_PC.contains(feedback.getItemId())) {
            ret.setFeedbackId(feedback.getFeedbackId());
        }

        ret.setActivePackSubscribed(true);

        if (updateSessionPromise != null) {
            updateSessionPromise.join();
        }

        // Awaiting for User Experiment (Hamlet) and segment data
        Map<String, String> analyticsData = null;
        try {
            analyticsData = this.userService.addAnalyticsData(userContext, tenant, hamletUserExperimentPromise);
        } catch (Exception err) {
            this.serviceInterfaces.exceptionReportingService.reportException("Failed to add experiment data ", err);
        }

        // Datalake config
        DatalakeConfig config = AppUtil.getDatalakeConfig(environmentService, session, analyticsData, user,
                clientVersion, tenant, addCfApiDatalakeEndpoint, userContext.getSessionInfo().getOrderSource());
        ret.setDatalakeConfig(config);

        ret.setDefaultPageId(ret.getActivePackSubscribed() ? ret.getDefaultPageId() : "CULT");
        // Contact sync config
        ContactSyncConfig contactSyncConfig = new ContactSyncConfig();
        contactSyncConfig.setIsEnabled(!isSugarFitOrUltraFitApp || !isMindFitApp);
        contactSyncConfig.setBatchSize(100);
        ret.setContactSyncConfig(contactSyncConfig);

        // Removing unnecessary experimentation flag call for cultsport website
        if (!isCultSportApp) {
            ret.setShowNewFitStoreAddressFlow(true);
            ret.setShowNewFitStoreCartFlow(true);
            ret.setShowFlutterWellnessCLP(true);
            ret.setShowFlutterStoreCLP(true);
            ret.setShowATTPromptForIOS(true);
            ret.setShowFlutterHomeCLP(true);
            ret.setShowFlutterSupportSection(AppUtil.isFlutterSupportSectionEnabled(userContext));
            if (AppUtil.isApiCachingEnabled(userContext)) {
                ret.setApiCacheEnabled(true);
                ret.setApiCacheConfig(AppUtil.getApiCacheConfig());
            } else {
                ret.setApiCacheEnabled(false);
            }
            ret.setUseFlutterSearchScreen(AppUtil.doesUserBelongToWellnessFlutterSearchScreenExperiment(
                    this.serviceInterfaces, this.environmentService, userContext));
            ret.setUseStoreFlutterPLP(AppUtil.doesUserBelongToStoreFlutterPLPExperiment(this.serviceInterfaces,
                    this.environmentService, userContext));
            ret.setShowFlutterTherapyPages(
                    AppUtil.checkIfUserPartOfMindTherapyAuroraExperiment(userContext, this.serviceInterfaces));
            ret.setUseStoreFlutterPDP(AppUtil.doesUserBelongToStoreFlutterPDPExperiment(this.serviceInterfaces,
                    this.environmentService, userContext));
            ret.setShowDiscoverCLP(true);
            ret.setShowFlutterBottomTabs(true);
            ret.setIsFlutterAllCentersPageEnabled(AppUtil.isFlutterAllCentersPageEnabled(userContext));
            ret.setIsFlutterBookingPageEnabled(AppUtil.isFlutterBookingPageEnabled(this.environmentService, userContext,
                    this.hamletHelper, this.serviceInterfaces.userServiceClient));
            ret.setShowDiscoverWithSearch(AppUtil.isDiscoverWithSearchSupported(this.serviceInterfaces,
                    this.environmentService, userContext));
            ret.setShowFlutterCommunity(
                    AppUtil.isCommunityExperienceEnabled(this.serviceInterfaces, this.environmentService, userContext));
            ret.setShowMindFitCLP(AppUtil.isMindFitCLPEnabled(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));
            ret.setEnableStoreWebviewForApp(AppUtil.isStoreWebViewForAppEnabled(
                    userContext));
            ret.setEnableEquipmentTabWebviewForApp(AppUtil.isEquipmentBottomTabSupported(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));
            ret.setEnableCachingForStoreWebview(
                    AppUtil.isCachingEnabledForStoreWebview(this.serviceInterfaces.segmentEvaluatorService,
                            this.environmentService, userContext));
            ret.setEnableShimmerLoaderForStoreWebview(
                    AppUtil.isShimmerLoaderEnabledForStoreWebview(this.serviceInterfaces.segmentEvaluatorService,
                            this.environmentService, userContext, ret.getEnableStoreWebviewForApp(), request));
            ret.setEnableCardHeroAnimation(AppUtil.isGymCardHeroAnimationEnabled(this.serviceInterfaces,
                    this.environmentService, userContext));
            ret.setShowAuroraProfileSection(AppUtil.isFlutterProfileSectionEnabled(userContext));
            if (ret.getEnableStoreWebviewForApp()) {
                boolean isUserEligibleForCsShopifyWebviewInApp = (boolean) userContext.getRequestCache().getRequestFuture(RequestType.CS_SHOPIFY_WEBVIEW_ELIGIBILITY, userContext).get();
                String redirectionUrl;
                if (isUserEligibleForCsShopifyWebviewInApp) {
                    redirectionUrl = "https://cultstore.com";
                    ret.setStoreWebviewRedirectionAction(
                            Action.builder().actionType(ActionType.CS_SHOPIFY_MULTIPASS).url(redirectionUrl).build());
                } else {
                    redirectionUrl = environmentService.isAlpha() ? "https://alpha-cult-fit.cultsport.com"
                            : "https://cult-fit.cultsport.com";
                    ret.setStoreWebviewRedirectionAction(
                            Action.builder().actionType(ActionType.SSO_STORE_WEB).url(redirectionUrl).build());
                }
                add1PCSStoreWebviewConfigToResponse(ret, appVersion, isUserEligibleForCsShopifyWebviewInApp);

            }
            if (ret.getEnableEquipmentTabWebviewForApp()) {
                boolean isUserEligibleForCsShopifyWebviewInApp = false;
//                boolean isUserEligibleForCsShopifyWebviewInApp = (boolean) userContext.getRequestCache().getRequestFuture(RequestType.CS_SHOPIFY_WEBVIEW_ELIGIBILITY, userContext).get();
//                if (isUserEligibleForCsShopifyWebviewInApp) {
//                    String redirectionUrl = "https://cultstore.com/collections/gym-equipment";
//                    String gearRedirectionUrl = "https://cultstore.com/collections/apparel";
//                    Map<String, Object> meta = new HashMap<>();
//                    meta.put("gearRedirectionUrl", gearRedirectionUrl);
//                    meta.put("cfUserId", userId);
//                    meta.put("isLoggedIn", !session.getIsNotLoggedIn());
//                    ret.setEquipmentTabWebviewRedirectionAction(
//                            Action.builder().meta(meta).actionType(ActionType.CS_SHOPIFY_MULTIPASS).url(redirectionUrl).build());
//                } else {
                String redirectionUrl = environmentService.isAlpha() ? "https://alpha-cult-fit.cultsport.com/lp/equipment"
                        : "https://cult-fit.cultsport.com/lp/equipment";
                String gearRedirectionUrl = environmentService.isAlpha() ? "https://alpha-cult-fit.cultsport.com/lp/sportswear" : "https://cult-fit.cultsport.com/lp/sportswear";
                Map<String, String> mp = new HashMap<>();
                mp.put("gearRedirectionUrl", gearRedirectionUrl);
                ret.setEquipmentTabWebviewRedirectionAction(
                        Action.builder().meta(mp).actionType(ActionType.SSO_STORE_WEB).url(redirectionUrl).build());
//                }
                add1PCSStoreWebviewConfigToResponse(ret, appVersion, isUserEligibleForCsShopifyWebviewInApp);

            }
            ret.setIngressConfig(getIngressConfig(userContext));
            ret.setEnableNoShowAutomatedException(AppUtil.isAutomatedNoShowExceptionSupported(
                    this.serviceInterfaces.segmentEvaluatorService, this.environmentService, userContext));
            Boolean isParqNeeded = !isCultSportApp && !isCultWatchApp && !isSugarFitOrUltraFitApp
                    && ret.getActiveMembershipPresent() && BooleanUtils.isFalse(session.getIsNotLoggedIn())
                    && isPARQNeeded(userContext);
            ret.setParqNeeded(isParqNeeded);
// Removed the createProfilePending field and its related logic as the feature is permanently disabled.
        }
        if (isCultSportApp) {
            // Experiments
            Map<String, Boolean> csExperiments = new HashMap<>();
            boolean useZoomablePDPCarousel = AppUtil.setUseZoomablePDPCarousel(
                    this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext);
            boolean useCheckoutPageForGuest = AppUtil.useCheckoutPageForGuest(
                    this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext);
            boolean showSlidableImages = AppUtil.showSlidableImages(
                    this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext);
            boolean newApparelPdpEnabled = (Boolean) userContext.getRequestCache()
                    .getRequestFuture(RequestType.GET_CULTSPORT_APPAREL_PDP_EXP, userContext, "").get();
            boolean isNewAddressFlowSupported = AppUtil.isNewAddressFlowEnabled(serviceInterfaces.segmentEvaluatorService, serviceInterfaces.environmentService, userContext);
            csExperiments.put("newApparelPdpEnabled", newApparelPdpEnabled);
            csExperiments.put("isDesktop", AppUtil.isDesktop(userContext));
            csExperiments.put("isDeliveryFeeEnabled",
                    AppUtil.isDeliveryFeeEnabled(this.serviceInterfaces.segmentEvaluatorService,
                            this.environmentService, userContext));
            csExperiments.put("useCheckoutPageForGuest", useCheckoutPageForGuest);
            csExperiments.put("showSlidableImages", showSlidableImages);
            csExperiments.put("showWishlist", AppUtil.shouldEnableWishlist(
                    serviceInterfaces.segmentEvaluatorService, serviceInterfaces.environmentService, userContext));
            csExperiments.put("isSnapmintEnabledForUser", (Boolean) userContext.getRequestCache()
                    .getRequestFuture(RequestType.GET_CULTSPORT_SNAPMINT_EXPERIMENT_ELIGIBILITY, userContext).get());
            csExperiments.put("isNewAddressFlowSupported", isNewAddressFlowSupported); // address confirmation modal flag
            ret.setCsExperiments(csExperiments);
            ret.setIsCultEmployee(UserService.isCultEmployee(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));
            ret.setUseZoomablePDPCarousel(useZoomablePDPCarousel);
            ret.setUseCheckoutPageForGuest(useCheckoutPageForGuest);
            ret.setShowSlidableImages(showSlidableImages);
            ret.setCsNewLoginFlow(AppUtil.shouldUseNewLoginFlow(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));
            ret.setUseNewCheckoutFlow(AppUtil.shouldUseNewCheckoutFlow(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));
            ret.setShowNewPriceUIInPDP(false);
            ret.setShowImageZoomInPDP(AppUtil.isImageZoomUIInPDP(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));
            ret.setShowMultivariantImageInPDP(true);
            ret.setShowNewOrderFlow(true);
            ret.setShowWhatsappConsent(AppUtil.showWhatsappConsent(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));
            ret.setShowSEOFooterInPLP(AppUtil.showSEOFooterInPLP(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));
            ret.setShowNewVoucherFlow(true);
            ret.setShowNewSearchPage(AppUtil.shouldUseNewSearchPage(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));
            ret.setUseNewPLPPage(AppUtil.shouldUseNewPLPPage(this.serviceInterfaces.segmentEvaluatorService,
                    this.environmentService, userContext));

            // Static
            ret.setIsInternalUser(user.getIsInternalUser());
        }

        // Fire and Forget to capture location in mongo
        if (!istest) {
            this.deviceService.updateSessionInformationPlatform(session.getDeviceId(), session.getSessionData(),
                    clientVersion, codePushVersion, latitude, longitude, advertiserId, tenant, ret.getTimezone(),
                    userContext, ip, detectedCity);
        }
        if (requestBody.getBranchParams() != null) {
            log.debug("Branch parameters for: " + userContext.getUserProfile().getUserId() + " are: "
                    + requestBody.getBranchParams().getCampaign() + " : "
                    + requestBody.getBranchParams().getDeeplinkPath());
            Action branchRedirectAction = this.getRedirectAction(requestBody.getBranchParams(),
                    userContext.getUserProfile().getUserId(), userContext, isPlayVisibilityExp, isPlayVisibilityControlExp);
            ret.setBranchRedirectAction(branchRedirectAction);
        }
        ret.setEnablePerfTracking(checkEnablePerfTracking());
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            ret.setSfLoggingTabMenuList(getSfLoggingTabMenu(userContext, chronicCareService.getChronicCareServiceHelper(), serviceInterfaces));
            ret.setConnectYourDevice(getHomeConnectYourDevice(userContext,chronicCareService.getChronicCareServiceHelper()));
        }

        ret.setNavWidgets(new ArrayList<>());
        // Adding side nav widget for cultsport
        if (cultsportNavWidgets != null) {
            try {
                if (cultsportNavWidgets.get() != null
                        && CollectionUtils.isNotEmpty(cultsportNavWidgets.get().getWidgets())) {
                    ret.setNavWidgets(cultsportNavWidgets.get().getWidgets());
                } else {
                }
            } catch (Exception ex) {
                log.error("Error while updating nav widget", ex);
            }
        }

        // sf freemium check
        if (AppUtil.isSugarFitApp(userContext)) {
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            NUXStatusResponse response = serviceInterfaces.getSmsClient()
                    .getNUXStatus(Long.valueOf(userContext.getUserProfile().getUserId()), true, timeZone);
            boolean freemiumUser = response != null && response.getFreemium();
            boolean freemiumNuxCompleted = freemiumUser && response.getNuxCompleted();
            ret.setIsSugarFitFreemiumUser(freemiumUser);
            boolean hasActivePack = response.getActivePack() != null;
            ret.setSugarFitDigitalUser(DigitalAppUtil.isDigitalAppUser(hasActivePack ? response.getActivePack().getProductCode(): null, response.getFreemium()));
            ret.setFreemiumNuxCompleted(freemiumNuxCompleted
                    && !ChronicCareAppUtil.isPhleboAgent(userContext));
            if (freemiumUser) {
                ret.setSugarFitFreemiumExperienceMaxDay(
                        serviceInterfaces.getChronicCareServiceHelper().getFreemiumExperinceConfigDay(userId));
                HashMap<String, String> videoConfig = new HashMap<>();
                // check if in segment
                boolean allowedUser = serviceInterfaces.getChronicCareServiceHelper().isFreemiumWellnessAllowed(userContext);
                videoConfig.put("allowedUser", String.valueOf(allowedUser));
                // config for non segment user
                if (!allowedUser) {
                    Integer timeAllowed = serviceInterfaces.getChronicCareServiceHelper().getFreemiumMaxVideoTime();
                    videoConfig.put("maxTimeInSec", String.valueOf(timeAllowed));
                }
                ret.setSfFreemiumVideoConfig(videoConfig);
            }
        }

        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            BundleProduct activeBundle = null;
            Optional<ActivePackResponse> activePackResponseOptional = this.userOnboardingService.getSugarFitActivePack(userId);
            if (activePackResponseOptional.isPresent()) {
                activeBundle = activePackResponseOptional.get().getBundleProduct();
            }
            SfInAppChatConfig inAppChatConfig = new SfInAppChatConfig();
            inAppChatConfig
                    .setChatEnabled(isInAppChatEnabledForUser(userContext, activeBundle, serviceInterfaces.chronicCareServiceHelper));
            ret.setSugarfitChatConfig(inAppChatConfig);
        }

        // For Transform BottomTab Experiment
        if (TransformUtil.isTransformCombinedTabSupported(serviceInterfaces, userContext)) {
            ret.setIsTransformCombinedTabEnabled(true);
        }
        ret.setIsFirstTimeTransformVisit(!AppUtil.isTransformTabVisited(serviceInterfaces, userContext));

        ret.setChangeTabFitnessToPlay(AppUtil.doesUserBelongToOnlyPlayMemberSegment(serviceInterfaces, userContext));
        ret.setHideDiscoverTab(AppUtil.showPlaySportsBottomTab(userContext, serviceInterfaces.environmentService,
                serviceInterfaces.segmentEvaluatorService));

        ret.setIsV2GetUserMeEnabled(AppUtil.doesUserBelongToNewUserMe(userContext, serviceInterfaces));

        if (isCultWatchApp) {
            ret.setCsWatchAppUpdateInfo(this.csWatchService.getAppUpdatePopUIInfo(userContext));
        }
        try {
            List<ActiveDIYPackViewV1> diySubscriptions = diySubscriptionsPromise != null ? diySubscriptionsPromise.get(300, TimeUnit.MILLISECONDS) : new ArrayList();
            ret.setDiySubscriptions(diySubscriptions);
        } catch (Exception err) {
            // report only 10% of errors
            if (new Random().nextInt(100) < 10) {
                this.serviceInterfaces.exceptionReportingService.reportWarning("Failed to add diySubscriptions data ", err);
            }
        }

        try {
            ret.setDisplayChatbot(AppUtil.isAppChatBotExperimentSupported(userContext, serviceInterfaces.segmentEvaluatorService, serviceInterfaces.environmentService));
        } catch (Exception ignored) {
        }

        if (!AppUtil.isAutoLaunchGlobalKeySet(userContext, serviceInterfaces)) {
            log.debug("Auto launch testing: stating auto launch");
            List<AutoLaunchAction> autoLaunchActions = this.allAutoLaunchEvaluators.getAllAutoLaunchActions(feedback, userContext);
            autoLaunchActions.sort(Comparator.comparingInt(AutoLaunchAction::getPriority));
            if (!autoLaunchActions.isEmpty()) {
                log.debug("Auto launch testing: checking auto launch {}", autoLaunchActions.getFirst());
                AutoLaunchAction selectedAutoLaunch = autoLaunchActions.getFirst();
                if (selectedAutoLaunch.getPageId().equals("FeedbackAutoLaunch")) {
                    if (!autoLaunchActions.stream().filter(element -> element.getPageId().equals("EnergyStreakGameV1AutoLaunch")).toList().isEmpty()) {
                        Action action = selectedAutoLaunch.getFlutterAction();
                        String url = action.getUrl();
                        url = url + "&endRoute=activity_streak_game";
                        action.setUrl(url);
                        selectedAutoLaunch.setFlutterAction(action);
                    }
                    if (!autoLaunchActions.stream().filter(element -> element.getPageId().equals("EnergyStreakGameV2AutoLaunch")).toList().isEmpty()) {
                        Action action = selectedAutoLaunch.getFlutterAction();
                        String url = action.getUrl();
                        url = url + "&endRoute=activity_streak_game_v2";
                        action.setUrl(url);
                        selectedAutoLaunch.setFlutterAction(action);
                    }
                }
                ret.setFlutterAction(selectedAutoLaunch.getFlutterAction());
                selectedAutoLaunch.onExecute();
                AppUtil.setAutoLaunchGlobalKey(userContext, serviceInterfaces, String.valueOf(System.currentTimeMillis()));
            }
        }

        return ret;
    }

    public void add1PCSStoreWebviewConfigToResponse(UserStatusResponse ret, Float appVersion, boolean isUserEligibleForCsShopifyWebviewInApp) {
        String cachedStoreWebviewUrl;
        if (isUserEligibleForCsShopifyWebviewInApp) {
            cachedStoreWebviewUrl = "https://cultstore.com";
        } else {
            String alphaCachedStoreWebviewUrl = "https://alpha-cult-fit.cultsport.com/?comingFromFlutter=true&passQueryParams=true&hideHeader=true&isWebview=true&appVersion="
                    + appVersion + "&isShimmerEnabled=true&disableAnalytics=true";
            String prodCachedStoreWebviewUrl = "https://cult-fit.cultsport.com/?comingFromFlutter=true&passQueryParams=true&hideHeader=true&isWebview=true&appVersion="
                    + appVersion + "&isShimmerEnabled=true&disableAnalytics=true";
            cachedStoreWebviewUrl = environmentService.isAlpha()
                    ? alphaCachedStoreWebviewUrl : prodCachedStoreWebviewUrl;
        }
        ret.setStoreWebviewConfig(StoreWebviewConfig.builder().pageNames(new ArrayList<>() {
            {
                add("api.juspay.in");
            }
        }).pathNames(new ArrayList<>() {
            {
                add("/checkout");
            }
        }).shimmerEnabledPageNames(new ArrayList<>() {
            {
                add("alpha-cult-fit.cultsport.com");
                add("cult-fit.cultsport.com");
                add("cultstore.com");
            }
        }).cachedWebviewUrl(cachedStoreWebviewUrl).newJuspayUrl("public.releases.juspay.in").build());
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/location"})
    public Session updateUserLocation(@RequestBody UserLocationUpdateRequest userLocationUpdateRequest)
            throws IOException, ExecutionException, InterruptedException {
        Session session = (Session) this.request.getAttribute("session");
        if (userLocationUpdateRequest == null)
            return session;
        return this.userService.updateUserLocation(userLocationUpdateRequest, session, sessionBusiness);
    }

    private IngressConfig getIngressConfig(UserContext userContext) {
        IngressConfig ingressConfig = new IngressConfig();
        ingressConfig
                .setIngressSegment(AppUtil.getIngressSegment(this.environmentService, userContext, this.hamletHelper));
        ingressConfig.setFitnessIngressThreshold(
                AppUtil.getIngressFitnessCLPVisitThreshold(this.environmentService, userContext, this.hamletHelper));
        ingressConfig.setHomeIngressThreshold(
                AppUtil.getIngressHomeCLPVisitThreshold(this.environmentService, userContext, this.hamletHelper));
        ingressConfig.setIngressTrackedRoutes(getIngressTrackedRoutes());

        return ingressConfig;
    }

    private List<String> getIngressTrackedRoutes() {
        return List.of(
                "fl_allgyms",
                "fl_localityselector",
                "fl_classbookingv2",
                "fl_prebookclass");
    }

    public String getSegmentNameByFormId(String formId) {
        Optional<UserFormAutoLaunchConfig> matchingLaunchConfig = userFormAutoLaunchConfigList.stream()
                .filter(userFormAutoLaunchConfig -> userFormAutoLaunchConfig.getFormId().equals(formId))
                .findFirst();
        return matchingLaunchConfig.map(UserFormAutoLaunchConfig::getSegmentIdIn).orElse(null);
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/status/cfsFormAction"})
    public Boolean dismissCfsAutoPopup(
            @CookieValue(value = "deviceId", required = false) String deviceIdCookie,
            @RequestBody(required = false) UserStatusRequestBody requestBody,
            @RequestParam(value = "apikey", required = false) String apikeyQueryParam) throws Exception {

        UserContext userContext = (UserContext) request.getAttribute("userContext");
        if (userContext.getSessionInfo().getAppVersion()>=10.94f) {
            Session session = this.userService.createDeviceLoginIfNoSession(requestBody, deviceIdCookie, apikeyQueryParam);
            log.info("cfs_autolaunch_testing: Session started");
            String userId = session.getUserId();
            String formId = requestBody.getFormId();
            log.info("cfs_autolaunch_testing: Fetched the userId and formId");
            SegmentUserListEntry segmentUserListEntry = new SegmentUserListEntry();
            segmentUserListEntry.setUserId(Long.valueOf(userId));
            log.info("cfs_autolaunch_testing: Successfully set the userId");
            String segmentName = getSegmentNameByFormId(formId);
            if (segmentName == null) {
                return true;
            }
            segmentUserListEntry.setSegmentName(segmentName);
            log.info("cfs_autolaunch_testing: Resetting CfsFormCache for userId:" + userId + ", formId:" + formId);
            //CompletableFuture<Boolean> promise = this.cfsFormCache.purgeActiveCfsFormActionForUser(userId, formId);
            return this.cfsFormCache.purgeUserId(segmentUserListEntry);
        }
        return true;
    }

    private boolean checkEnablePerfTracking() {
        Random random = new Random();
        int probabilityIndex = random.nextInt(1000);
        return probabilityIndex == 1;
    }

    private Action getRedirectAction(
            BranchParams branchParams,
            String userId,
            UserContext userContext,
            boolean isPlayVisibilityExp,
            boolean isPlayVisibilityExpControl
    ) throws BaseException {
        Action redirectAction = new Action();
        redirectAction.setActionType(ActionType.NAVIGATION);
        if (!TextUtils.isEmpty(branchParams.getDeeplinkPath())) {
            redirectAction.setUrl(branchParams.getDeeplinkPath());
            return redirectAction;
        }
        if (!TextUtils.isEmpty(branchParams.getCampaign())) {
            String campaignName = branchParams.getCampaign();
            String lowerCaseCampaignName = campaignName.toLowerCase();
            String deeplinkUrl = "";
            if (lowerCaseCampaignName.contains("live_pt")) {
                deeplinkUrl = "curefit://cultfitclp?selectedTab=LivePT&pageId=cult";
            } else if (lowerCaseCampaignName.contains("transform")) {
                deeplinkUrl = TransformUtil.combinedWeightLossClpDeepLink();
            } else if (lowerCaseCampaignName.contains("live")) {
                deeplinkUrl = "curefit://fl_listpage?pageId=CultPassLiveSKU";
            } else if (lowerCaseCampaignName.contains("unified")) {
                if (!isPlayVisibilityExp && !isPlayVisibilityExpControl) {
                    deeplinkUrl = "curefit://tabpage?pageId=fitnesshub";
                    AppUtil.updateValueInUserAttribute(userAttributesClient, userId, AppUtil.homePagePreferenceKey, String.valueOf(TabType.FITNESSHUB), userContext, "setting the Home Page preference");
                }
            } else if (lowerCaseCampaignName.contains("black") || lowerCaseCampaignName.contains("elite")) {
                deeplinkUrl = "curefit://fl_listpage?pageId=CultPassBlackSKU";
                AppUtil.updateValueInUserAttribute(userAttributesClient, userId, AppUtil.homePagePreferenceKey, String.valueOf(TabType.FITNESSHUB), userContext, "setting the Home Page preference");
            } else if (lowerCaseCampaignName.contains("gold") || lowerCaseCampaignName.contains("pro")) {
                deeplinkUrl = "curefit://fl_listpage?pageId=CultPassGoldSKU";
                AppUtil.updateValueInUserAttribute(userAttributesClient, userId, AppUtil.homePagePreferenceKey, String.valueOf(TabType.FITNESSHUB), userContext, "setting the Home Page preference");
            } else if (lowerCaseCampaignName.contains("cult_play")) {
                deeplinkUrl = "curefit://fl_listpage?pageId=CultPassFitsoSKU";
                AppUtil.updateValueInUserAttribute(userAttributesClient, userId, AppUtil.homePagePreferenceKey, String.valueOf(TabType.SPORTS), userContext, "setting the Home Page preference");
            } else {
                Pattern pattern = Pattern.compile("curefit://\\S+", Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(campaignName);
                if (matcher.find()) {
                    deeplinkUrl = matcher.group(0);
                }
            }
            redirectAction.setUrl(deeplinkUrl);
            return redirectAction;
        }
        log.warn("Branch: Returning null for user: " + userId + " campaign: " + branchParams.getCampaign() + " : "
                + branchParams.getDeeplinkPath());
        return null;
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/slackAuth"})
    public SlackRegisterUserResponse slackAuth(@RequestBody() SlackAuthRequest slackAuthRequest,
                                               HttpServletRequest request) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        var slackAppChannelUrl = this.slackAuthService.registerUser(userContext, slackAuthRequest);
        SlackRegisterUserResponse response = new SlackRegisterUserResponse();
        response.setSlackAppChannelUrl(slackAppChannelUrl);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/state/{entity}/set/{state}"})
    public Boolean setFeatureState(HttpServletRequest request, @PathVariable String entity, @PathVariable String state)
            throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.featureStateCache.set(userContext.getUserProfile().getUserId(), entity, state).get();
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/state/{entity}/get"})
    public Map<String, String> getFeatureState(HttpServletRequest request, @PathVariable String entity)
            throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String value = this.featureStateCache.get(userContext.getUserProfile().getUserId(), entity).get();
        return Map.of(entity, value);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/state/{entity}/match/{state}"})
    public Boolean matchFeatureState(HttpServletRequest request, @PathVariable String entity,
                                     @PathVariable String state) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.featureStateCache.match(userContext.getUserProfile().getUserId(), entity, state).get();
    }

    @RequestMapping(method = RequestMethod.DELETE, value = {"/state/{entity}/unset"})
    public Boolean unsetFeatureState(HttpServletRequest request, @PathVariable String entity) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return this.featureStateCache.unset(userContext.getUserProfile().getUserId(), entity).get();
    }

    // This is specifically used for onyx app, updating user-device info in device
    // table and extending validity of access token
    @RequestMapping(method = RequestMethod.POST, value = {"/v2/status"})
    public Boolean getStatus(
            @CookieValue(value = "deviceId", required = false) String deviceIdCookie,
            @RequestBody(required = false) UserStatusRequestBody requestBody,
            @RequestParam(value = "apikey", required = false) String apikeyQueryParam,
            @RequestHeader(value = "codepushversion", required = false) String codePushVersion)
            throws BaseException, ExecutionException, InterruptedException, IOException {
        Session session = this.userService
                .createDeviceLoginIfNoSession(requestBody, deviceIdCookie, apikeyQueryParam);
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String ip = AppUtil.getClientIpAddr(request);
        Float clientVersion = NumberUtils.isParsable(request.getHeader("clientversion"))
                ? Float.parseFloat(request.getHeader("clientversion"))
                : null;
        AppTenant tenant = AppUtil.getAppTenantFromUserContext(userContext);

        this.sessionBusiness.updateSessionData(session.getAt(), session.getSessionData(), session.getIsNotLoggedIn(),
                false);
        this.deviceService.updateSessionInformationPlatform(session.getDeviceId(), session.getSessionData(),
                clientVersion, codePushVersion, null, null, null, tenant, null, userContext, ip, new DetectedCity());

        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/device/bulk/logout"})
    public Boolean logoutDevicesforUser(HttpServletRequest request) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = userContext.getUserProfile().getUserId();
        AppTenant tenant = AppUtil.getAppTenantFromUserContext(userContext);
        return this.deviceService.bulkLogoutDeviceAndExpireSessions(userId, tenant);
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/device/logout"})
    public Boolean logoutDeviceforUser(
            HttpServletRequest request,
            @RequestParam(value = "deviceId", required = false) String deviceId) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = userContext.getUserProfile().getUserId();
        AppTenant tenant = AppUtil.getAppTenantFromUserContext(userContext);
        return this.deviceService.logoutDeviceAndExpireSession(deviceId, userId, tenant);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/active/sessions"})
    public Object getActiveSessionsForUser(HttpServletRequest request) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = userContext.getUserProfile().getUserId();
        AppTenant tenant = AppUtil.getAppTenantFromUserContext(userContext);
        if (!AppUtil.isAppVersionBelow(userContext, 1.7f, 10.10f)) {
            ManageDevicesResponse manageDevicesResponse = new ManageDevicesResponse();
            manageDevicesResponse.setDevices(this.deviceService.getActiveSessionsForUser(userId, tenant));
            String deviceId = request.getHeader("deviceId");
            if (AppUtil.isUserFlaggedForDeviceRegistration(userContext.getUserEntryCompletableFuture().get())
                    || AppUtil.isCurrDeviceRegistrationAllowed(deviceId, userContext.getUserEntryCompletableFuture().get())
            ) {
                manageDevicesResponse.setShowRegistrationButton(true);
            }
            return manageDevicesResponse;
        } else {
            return this.deviceService.getActiveSessionsForUser(userId, tenant);
        }
    }

    @PutMapping(value = "/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public UserEntry updateByUserId(@RequestBody UserEntry userEntry) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = userContext.getUserProfile().getUserId();
        return userService.updateByUserId(userId, userEntry);
    }

    @RequestMapping(method = RequestMethod.GET, value = {
            "/fetchOnDemandAnnouncement"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public OnDemandAnnouncement fetchAnnouncementOnDemand(
            @RequestParam(defaultValue = "") final String announcementId) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        List<String> announcementIds = new ArrayList<>();
        announcementIds.add(announcementId);
        AnnouncementView announcementView = this.announcementService
                .getSingleActiveAnnouncement(userContext, false, announcementIds, true).join();
        OnDemandAnnouncement onDemandAnnouncement = new OnDemandAnnouncement();
        if (announcementView != null) {
            Action action = new Action();
            action.setActionType(ActionType.SHOW_ANNOUNCEMENT);
            action.setMeta(announcementView);
            onDemandAnnouncement.setAction(action);
        }
        return onDemandAnnouncement;
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/event"})
    public void sendUserActivityRashiEvent(
            HttpServletRequest request,
            @RequestParam(value = "eventName", required = false) String eventName,
            @RequestBody JSONObject requestBody) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        String userId = userContext.getUserProfile().getUserId();
        userService.sendUserActivityRashiEvent(userId, eventName, requestBody);
    }

    public boolean isCreateProfilePending(UserContext userContext) throws ExecutionException, InterruptedException {
        try {
            UserEntry userEntry = userContext.getUserEntryCompletableFuture().get();
            return (userEntry.getFirstName() == null && userEntry.getLastName() == null)
                    || userEntry.getBirthday() == null
                    || userEntry.getGender() == null;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isPARQNeeded(UserContext userContext) throws BaseException {
        UserAttributesResponse response = serviceInterfaces.userAttributesCacheClient.getAttributes(
                Long.parseLong(userContext.getUserProfile().getUserId()), Collections.singletonList("parq_status"),
                AppTenant.CUREFIT);
        if (response == null || response.getAttributes() == null
                || response.getAttributes().get("parq_status") == null) {
            PARQStatus parqStatus = null;
            try {
                parqStatus = this.cultServiceImpl
                        .getPARQStatusByUserId(userContext.getUserProfile().getUserId()).get();
            } catch (Exception e) {
                log.error("Failed to fetch parq_status for user {}", userContext.getUserProfile().getUserId(), e);
                exceptionReportingService.reportException("Failed to fetch parq_status for user", e);
                //Don't show parq popup if we are unable to fetch parq status
                return false;
            }
            if (parqStatus == null) {
                return true;
            }
            return parqStatus.getStatus() != PARQStates.SUCCESSFUL && parqStatus.getStatus() != PARQStates.NON_PARQ;
        } else {
            String parqStatus = response.getAttributes().get("parq_status").toString().toUpperCase();
            return !PARQStates.SUCCESSFUL.name().equalsIgnoreCase(parqStatus)
                    && !PARQStates.NON_PARQ.name().equalsIgnoreCase(parqStatus);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/fillPARQ"})
    public ResponseEntity fillUserPARQ(HttpServletRequest request) {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        this.cultServiceImpl.approvePARQV2(Long.parseLong(userContext.getUserProfile().getUserId()),
                "Filling parq for " + userContext.getUserProfile().getUserId(),
                userContext.getUserProfile().getUserId());
        return new ResponseEntity(true, HttpStatus.OK);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/parqForm"})
    public ParqFormResponse getPARQForm(HttpServletRequest request) throws Exception {
        Resource resource = resourceLoader.getResource("classpath:parq-form.json");
        ParqFormResponse response = objectMapper.readValue(resource.getInputStream(), ParqFormResponse.class);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/updateCityPreference"})
    public LocationUtil.CityResponse updateCityPreference(
            HttpServletRequest request,
            @RequestBody JSONObject body
    ) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Session session = (Session) request.getAttribute("session");
        String selectedCityId = body.get("cityId").toString();
        LocationUtil.CityResponse cityResponse = userService.updateCity(userContext, session, selectedCityId, this.serviceInterfaces);
        UserContext updatedUserContext = userService.getUserContextFromReq(request).get();
        try {
            AppLayout var = this.appLayoutBuilder
                    .fetchPageLayout(updatedUserContext, cityResponse.getSupportedVerticals(), null).get();
            if (var == null)
                log.error("AppLayoutBuilder returns null value");
            cityResponse.setAppLayout(var);
        } catch (Exception err) {
            this.serviceInterfaces.exceptionReportingService.reportException("Failed to fetch app layout ", err);
        }

        return cityResponse;
    }

    @RequestMapping(method = RequestMethod.POST, value = {"/locationPreference"})
    public LocationUtil.LocationStatusResponse setLocationPreference(
            HttpServletRequest request,
            @RequestBody LocationUtil.LocationPreferenceRequestEntity preferenceRequestEntity) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        SessionInfo session = userContext.getSessionInfo();
        String cityId = session.getSessionData().getCityId();
        String userId = userContext.getUserProfile().getUserId();
        return this.userService.setLocationPreference(userContext, userId, cityId, preferenceRequestEntity,
                serviceInterfaces, sessionBusiness);
    }

    @RequestMapping(method = RequestMethod.GET, value = {"/cities"})
    public CityDataResponseV2 getCitiesV2(
            HttpServletRequest request) throws Exception {

        UserContext userContext = (UserContext) request.getAttribute("userContext");
        Tenant tenant = AppUtil.getTenantFromUserContext(userContext);

        Float lat = NumberUtils.isParsable(request.getHeader("lat")) ? Float.parseFloat(request.getHeader("lat"))
                : null;
        Float lon = NumberUtils.isParsable(request.getHeader("lon")) ? Float.parseFloat(request.getHeader("lon"))
                : null;

        List<City> cities = cityCache.listCities(tenant);
        cities = cities.stream()
                .filter(city -> city.getCountryId().equals("IN"))
                .collect(Collectors.toList());

        Map<String, List<City>> cityMap = new HashMap<>();
        AppUtil.generateCityListWithParentCityIdAsKey(cityMap, cities);

        String selectedCityId = Optional.ofNullable(userContext)
                .map(UserContext::getSessionInfo)
                .map(SessionInfo::getSessionData)
                .map(SessionData::getCityId)
                .orElse(null);

        Membership currentCultMembership = null;
        List<Membership> membershipList = (Objects.isNull(userContext.getUserProfile())
                || Objects.isNull(userContext.getUserProfile().getUserId())) ? new ArrayList<>()
                : getCurrentFitnessMemberships(userContext, serviceInterfaces, false, false);
        if (!membershipList.isEmpty()) {
            String timezone = userContext.getUserProfile().getCity().getTimezone();
            membershipList = AppUtil.getCurrentMemberships(CultUtil.getCultMemberships(membershipList), timezone);
        } else
            membershipList = new ArrayList<>();
        if (!membershipList.isEmpty()) {
            currentCultMembership = membershipList.get(0);
        }

        String CityId = (currentCultMembership != null && currentCultMembership.getMetadata().get("cityId") != null)
                ? (String) currentCultMembership.getMetadata().get("cityId")
                : null;

        List<CityView> cityViews = new ArrayList<>();
        AppUtil.generateCityViewFromCityMap(
                cityMap,
                cityViews,
                selectedCityId,
                currentCultMembership,
                CityId);

        CityDataResponseV2 cityDataResponseV2 = new CityDataResponseV2();
        CityView selectedCity = null;

        Map<String, List<CityView>> countryToCitiesMap = new HashMap<>();
        for (CityView cityView : cityViews) {
            String countryId = cityView.getCountryId();
            if (cityView.getIsSelected())
                selectedCity = cityView;
            List<CityView> citiesInCountry = countryToCitiesMap.getOrDefault(countryId, new ArrayList<>());
            citiesInCountry.add(cityView);
            countryToCitiesMap.put(countryId, citiesInCountry);
        }

        cityDataResponseV2.setSelectedCity(selectedCity);
        cityDataResponseV2.setCountriesFromMap(countryToCitiesMap);
        cityDataResponseV2.setCityTextInfo(new CityDataResponseV2.CityTextInfo(
                "POPULAR CITY",
                "OTHERS"));

        String ip = AppUtil.getClientIpAddr(request);
        CityAndCountry detectedCityAndCountry = AppUtil.detectCityAndCountryAndIp(Tenant.fromString(tenant.toString()),
                ip, lat, lon, serviceInterfaces);
        String detectedCountryId = detectedCityAndCountry.getCountry() != null
                ? detectedCityAndCountry.getCountry().getCountryId()
                : null;

        if (detectedCountryId != null && detectedCityAndCountry.getCity() != null) {
            cityDataResponseV2.setDetectedInfo(
                    new CityDataResponseV2.DetectedInfo(
                            detectedCityAndCountry.getCity().getCityId(),
                            detectedCountryId));
        }

        cityDataResponseV2.sortCountries(detectedCountryId);

        return cityDataResponseV2;

    }

    @RequestMapping(method = RequestMethod.GET, value = {"/me"})
    public UserMeResponse getUserMe(
            HttpServletRequest request,
            @RequestHeader(value = "osname", required = false) String osName,
            @RequestHeader(value = "codepushversion", required = false) String codepushversion,
            @RequestParam(value = "feedback", required = false) String getFeedbackId,
            @RequestParam(value = "expanded", required = false) String expanded,
            @RequestParam(value = "apikey", required = false) String apikeyQueryParam,
            @RequestParam(value = "showSleepSetting", required = false) Boolean showSleepSetting
    ) throws Exception {

        UserMeResponse userMeResponse = new UserMeResponse();
        try {
            userMeResponse.setGenders(userMeResponse.getGenders());
            UserContext userContext = (UserContext) request.getAttribute("userContext");
            Session session = (Session) request.getAttribute("session");
            UserAgent userAgent = session.getUserAgent();
            Float appVersion = NumberUtils.isParsable(request.getHeader("appversion"))
                    ? Float.parseFloat(request.getHeader("appversion"))
                    : null;
            String userId = session.getUserId();

            if (!Objects.equals(userAgent, UserAgent.APP) && !AuthUtil.isAuthTokenCookiePresent(session.getAt(), request, cookieService)) {
                log.info("Cookie not found for user accessing through browser");
                AuthUtil.setCookies(
                        serviceInterfaces.exceptionReportingService,
                        userAgentService, apiKeyService, session.getSt(), session.getAt(),
                        session.getDeviceId(), apikeyQueryParam, request, response
                );
            }

            if (Objects.isNull(session.getSessionData().getTlaSemusnoc())) {
                session.getSessionData().setTlaSemusnoc(UUID.randomUUID().toString());
                this.sessionBusiness.updateSessionData(session.getAt(), session.getSessionData(), session.getIsNotLoggedIn(), true);
            } else {
                this.sessionBusiness.updateSessionData(session.getAt(), session.getSessionData(), session.getIsNotLoggedIn(), false);
            }

            City userCity = userContext.getUserProfile().getCity();

            Mono<FitBitInfo> fitBitInfoMono = null;
            if (!AuthUtil.isGuestUser(userContext)) {
                try {
                    fitBitInfoMono = serviceInterfaces.cfApiNodeService.callNodeEndpointReactiveGet("/user/fitbit/isAuthenticated",
                                    null,
                                    userContext,
                                    new ParameterizedTypeReference<FitBitInfo>() {},
                                    500)
                            .onErrorResume(TimeoutException.class, error -> {
                                this.exceptionReportingService.reportWarning("Timeout while fetching FitBit authentication status for user: " + userContext.getUserProfile().getUserId(), error);
                                return Mono.empty();
                            });
                } catch (Exception e) {
                    this.exceptionReportingService.reportException("Error while fetching FitBit authentication status for user: " + userContext.getUserProfile().getUserId(), e);
                }
            }

            CompletableFuture<String> feedbackFuture = CompletableFuture.completedFuture(null);
            try {
                feedbackFuture = Objects.equals(getFeedbackId, "true") ?
                        feedbackService.getFeedbackToShow(Collections.singletonList(userId), userContext)
                                .thenApply(feedback -> Objects.nonNull(feedback) && Objects.nonNull(feedback.getFeedbackId()) ?
                                        feedback.getFeedbackId() : null) :
                        CompletableFuture.completedFuture(null);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error fetching feedback", e);
            }

            CompletableFuture<PreferenceDetail> cultPreferenceFuture = CompletableFuture.completedFuture(null);
            CompletableFuture<List<BaseWidget>> membershipItemsFuture = CompletableFuture.completedFuture(null);
            if (Objects.equals(osName.toLowerCase(), "android") || Objects.equals(osName.toLowerCase(), "ios")) {
                membershipItemsFuture = CompletableFuture.supplyAsync(() -> {
                    try {
                        return new MembershipWidgetAurora().setCanShowBenefitCards(true).buildView(serviceInterfaces, userContext, new WidgetContext());
                    } catch (Exception e) {
                        exceptionReportingService.reportException("Error building MembershipWidgetAurora", e);
                        return null;
                    }
                }, serviceInterfaces.getTaskExecutor()).thenApply(widgetObjects -> {
                    if (CollectionUtils.isEmpty(widgetObjects)) {
                        return new ArrayList<>();
                    }
                    MembershipWidgetAurora membershipWidget = (MembershipWidgetAurora) widgetObjects.getFirst();
                    return membershipWidget.getData().stream().map(item -> {
                        if (!ProductType.GYM_PT_PRODUCT.equals(item.getProductType())) {
                            item.setCtaAction(null);
                        }
                        return (BaseWidget) item;
                    }).collect(Collectors.toList());
                });

                cultPreferenceFuture = CompletableFuture.supplyAsync(() -> {
                    try {
                        return CultUtil.getClassRemindersPreference(userContext, Long.valueOf(userCity.getCultCityId()), true, serviceInterfaces);
                    } catch (Exception e) {
                        exceptionReportingService.reportException("Error fetching feedback", e);
                        return null;
                    }
                }, serviceInterfaces.getTaskExecutor());
            }

            CompletableFuture<HealthMetricWidget> healthMetricWidgetFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return ProfileBuilder.getHealthMetricWidget(serviceInterfaces.metricClient, userContext);
                } catch (MetricClientException e) {
                    exceptionReportingService.reportException("Error fetching HealthMetricWidget", e);
                    return null;
                }
            }, serviceInterfaces.getTaskExecutor());

            float fitcashBalance = 0f;
            try {
                CompletableFuture<WalletBalance> fitcashBalanceFuture = CompletableFuture.supplyAsync(() ->
                        serviceInterfaces.fitcashService.getBalance(userId, userContext.getUserProfile().getCity().getCountry().getCurrencyCode()), serviceInterfaces.getTaskExecutor());

                // Get results
                WalletBalance fitcashBalanceResponse = fitcashBalanceFuture.get();
                fitcashBalance = (float) (fitcashBalanceResponse.getBalance() / 100);
            } catch (Exception e){
                fitcashBalance = 0f;
                serviceInterfaces.exceptionReportingService.reportException(e);
            }

            String feedBackId = feedbackFuture.get();

            FitBitInfo fitBitInfo = Objects.nonNull(fitBitInfoMono) ? fitBitInfoMono.block() : null;
            if (Objects.isNull(fitBitInfo) || Objects.isNull(fitBitInfo.getIsAuthenticated())) {
                fitBitInfo = new FitBitInfo();
                fitBitInfo.setIsAuthenticated(false);
            }
            fitBitInfo.setAuthenticated(fitBitInfo.getIsAuthenticated());
            fitBitInfo.setUrl(fitBitInfo.getIsAuthenticated() ? "curefit://fitbit/logout" : AppUtil.fitbitLoginUrl(userId));

            UserEntry userEntry = userContext.getUserEntryCompletableFuture().get();
            SleepConfig sleepConfig = AppUtil.getSleepConfig(appVersion, osName);
            userMeResponse.setUser(new UserView(userEntry, session.getIsNotLoggedIn(), null, null, feedBackId, fitcashBalance));
            userMeResponse.setSession(new SessionView(session, null));
            userMeResponse.setCityName(userCity.getName());
            userMeResponse.setFitBitInfo(fitBitInfo);
            userMeResponse.setSleepConfig(sleepConfig);
            userMeResponse.setCareEnabled(CareUtil.isCareSupported(osName, appVersion, userCity.getCityId()));
            userMeResponse.setCareMappedActions(CareUtil.addCareMappedActions(appVersion, userCity.getCityId(), userEntry.getIsInternalUser()));

            if (Objects.equals(osName.toLowerCase(), "android") || Objects.equals(osName.toLowerCase(), "ios")) {
                PreferenceDetail cultPreference = cultPreferenceFuture.get();
                userMeResponse.setWidgets(
                        ProfileBuilder.addMeWidgets(
                                request, userEntry, AppUtil.isInternationalApp(userContext),
                                userCity, fitcashBalance, serviceInterfaces, membershipItemsFuture,
                                osName, showSleepSetting, fitBitInfo, cultPreference, expanded,
                                healthMetricWidgetFuture
                        )
                );
            }

        } catch (Exception e) {
            throw new Exception("Something went wrong: " + e.getMessage(), e);
        }

        return userMeResponse;
    }

    /**
     * Endpoint to check SOS button visibility for a user
     */
    @RequestMapping(method = RequestMethod.GET, value = { "/sosButtonVisibility/{userId}" })
    public Map<String, Object> checkSOSButtonVisibility(@PathVariable String userId) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        boolean isVisible = userService.shouldShowSOSButton(serviceInterfaces, userContext);
        Map<String, Object> response = new HashMap<>();
        response.put("userId", userId);
        response.put("isSOSButtonVisible", isVisible);
        return response;
    }

    @RequestMapping(method = RequestMethod.GET, value = { "/splash-screen" })
    public SplashScreenData getSplashScreen(HttpServletRequest request) throws Exception {
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        return userService.getSplashScreenData(userContext, serviceInterfaces);
    }
}