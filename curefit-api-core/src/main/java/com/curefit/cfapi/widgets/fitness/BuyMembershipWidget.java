package com.curefit.cfapi.widgets.fitness;

import com.curefit.center.dtos.CenterEntry;
import com.curefit.cfapi.model.internal.cult.CultPackBrowseProductType;
import com.curefit.cfapi.model.internal.cult.PackItem;
import com.curefit.cfapi.model.internal.cult.PackOfferDetails;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.common.BrandLogoTypes;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.CultUtil;
import com.curefit.cfapi.util.OrderUtil;
import com.curefit.cfapi.util.PlayUtil;
import com.curefit.cfapi.view.viewmodels.OnBoardingFooterWeb;
import com.curefit.cfapi.view.viewmodels.OnboardingCardDetails;
import com.curefit.cfapi.view.viewmodels.OnboardingFooter;
import com.curefit.cfapi.view.viewmodels.OnboardingTitleList;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.offers.dtos.OfferMini;
import com.curefit.offers.dtos.ProductTaxBreakup;
import com.curefit.offers.dtos.play.PlayProductPricesResponse;
import com.curefit.offers.enums.AddonType;
import com.curefit.offers.types.Addon;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.pms.pojo.customPacks.augments.AugmentedOfflineFitnessPack;
import com.curefit.product.enums.ProductType;
import com.curefit.product.models.cult.FitnessPack;
import com.curefit.sportsapi.pojo.FTSSportsInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.curefit.cfapi.pojo.app.action.ActionType.NAVIGATION;
import static com.curefit.cfapi.service.appConfig.EnterpriseConfig.CULT_ELITE_SKU_PURCHASE_URL_WEB;
import static com.curefit.cfapi.service.appConfig.EnterpriseConfig.CULT_PRO_SKU_PURCHASE_URL_WEB;
import static com.curefit.cfapi.service.appConfig.EnterpriseConfig.ELITE_SKU_BUY_PAGE_URL;
import static com.curefit.cfapi.service.appConfig.EnterpriseConfig.PRO_SKU_BUY_PAGE_URL;
import static com.curefit.cfapi.util.PlayUtil.ALL_PLAY_LITE_CENTERS_DEEPLINK;
import static com.curefit.cfapi.util.PlayUtil.PLAY_COMPARISION_V2_IMAGE_URL;
import static com.curefit.cfapi.util.PlayUtil.PLAY_PACKS_PAGE_DEEPLINK;
import static com.curefit.cfapi.util.PlayUtil.PLAY_PRO_ICON_IMAGE_URL;
import static com.curefit.cfapi.util.PlayUtil.SINGLE_SPORT_PAGE_DEEPLINK;

@Getter
@Setter
@Slf4j
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BuyMembershipWidget extends BaseWidget {

    boolean isWeb;
    CultPackBrowseProductType productType;
    List<OnboardingCardDetails> cardDataList;
    String title = "Buy Cultpass";
    List<DividerText> dividerTextList;
    List<FooterText> footerTextList;
    String footer = "";
    String dividerTextTitle;
    String footerTitle = "";
    String bannerUrl;
    boolean isNewBuyWidget = false;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        this.isWeb = AppUtil.isWeb(userContext);

        if (cardDataList == null)
            cardDataList = new ArrayList<>();

        if (productType != null && productType.equals(CultPackBrowseProductType.PLAY)){
            OnboardingCardDetails widget = getPlayBuyMembershipWidget(CultPackBrowseProductType.PLAY, userContext, interfaces);
            this.isNewBuyWidget = true;
            if (widget != null) {
                addSpacing("0", "20");
                setTitle("Buy access to all centers");
                cardDataList.add(widget);
            }else
                return null;
        }
        if (productType != null && productType.equals(CultPackBrowseProductType.PLAY_SELECT)){
            OnboardingCardDetails widget = getPlayBuyMembershipWidget(CultPackBrowseProductType.PLAY_SELECT, userContext, interfaces);
            this.isNewBuyWidget = true;
            if (widget != null) {
                addSpacing("0", "20");
                setTitle("Buy access to any one center");
                cardDataList.add(widget);
            }else
                return null;
        }
        if (productType != null && productType.equals(CultPackBrowseProductType.PLAY_SPORT)){
            List<OnboardingCardDetails> cardList = getSportsLevelPackCards(productType, userContext, interfaces);
            this.title = "Get access to one sport at one center";
            cardDataList.addAll(cardList);
        }
        if (productType != null && productType.equals(CultPackBrowseProductType.ELITE_PLUS)){
            addSkuPlusMembershipCardData(productType, userContext, interfaces);
            this.title = "";
            this.isNewBuyWidget = true;
            this.addSpacing("10", "20");
        }
        if (productType != null && productType.equals(CultPackBrowseProductType.PRO_PLUS)){
            addSkuPlusMembershipCardData(productType, userContext, interfaces);
            this.title = "";
            this.isNewBuyWidget = true;
            this.addSpacing("10", "20");
        }
        return Collections.singletonList(this);
    }

    private List<OnboardingCardDetails> getSportsLevelPackCards(CultPackBrowseProductType productType, UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        List<OnboardingCardDetails> cardList = new ArrayList<>();
        List<FTSSportsInfo> citySports = interfaces.sportsApiService.getCityWorkoutsInfo(userContext.getUserProfile().getCity().getCityId(), true).get();

        for (FTSSportsInfo sportsInfo : citySports) {
            List<FitnessPack> citySportsPack = interfaces.catalogueService.getPlayProductCenterActivityByCity(userContext.getUserProfile().getCity().getCityId(), sportsInfo.getId().toString(), true, userContext.getUserProfile().getUserId());
            if (citySportsPack.isEmpty())
                continue;
            citySportsPack = citySportsPack.stream().filter(pack -> pack.getVisibility() != null && pack.getVisibility().contains("app")).toList();
            String minPrice = PlayUtil.getMinPriceOfSportLevelPack(null, citySportsPack, userContext, interfaces);
            String url = SINGLE_SPORT_PAGE_DEEPLINK + "sportId=" + sportsInfo.getId().toString();
            Action action = new Action(url, NAVIGATION);
            OnboardingCardDetails widget = new OnboardingCardDetails();
            widget.setGradientEnabled(true);
            widget.setTitle(sportsInfo.getName());
            widget.setDescription("Starting at Rs. " + minPrice +"/mo*");
            widget.setImage(sportsInfo.getSportMedia().get(0).getMediaUrl());
            widget.setAction(action);
            widget.setDisableIcon(true);
            widget.setTitleSize(16.0);
            cardList.add(widget);
        }
        return cardList;
    }

    public BuyMembershipWidget(boolean isWeb) {
        this.cardDataList = new ArrayList<>();
        this.dividerTextList = new ArrayList<>();
        this.isWeb = isWeb;
        this.footer = "We have two tiers of gyms, ELITE and PRO. ELITE gyms are more premium gyms than PRO gyms.";
        this.footerTextList = new ArrayList<>();
    }

    public void addFreeTrialCardData(CenterEntry center, Action freeTrialAction, Integer trialCount) {
        final String membershipType = "FREE";
        final String FREE_TRIAL_TITLE = "Book a class for free";
        final String FREE_TRIAL_DESCRIPTION = "You can use the free trial at this center to try out various formats and classes";

//        Action freeTrialAction = new Action(actionType, "TRY FOR FREE");
//        if(actionType == ActionType.WEB_SHOW_GYM_QUICK_CHECKIN_MODAL){
//            HashMap<String, Object> meta = new HashMap<>();
//            String centerId = center.getMeta().get(GYMFIT_CENTER_ID_KEY).toString();
//            meta.put("centerId", centerId);
//            freeTrialAction.setMeta(meta);
//        }
        String trials = "02";
        if(trialCount != null){
            trials =  trialCount.toString();
        }

        cardDataList.add(new OnboardingCardDetails(FREE_TRIAL_TITLE, FREE_TRIAL_DESCRIPTION, null, null,freeTrialAction ,
                new OnBoardingFooterWeb(trials ,"Free Trial", "Classes"),null, null, membershipType,null,null,null,null,null,null,"", null, null, null, null, null));
    }

    public  void addRequestCallBackCardData(){
        final String CALL_BACK_TITLE = "Questions? We're Here for You!";
        final String FREE_TRIAL_DESCRIPTION = "We’re just one click away! Let our fitness experts guide you through your next step.";
        final String HYBRID_LEAD_GEN = "https://forms.gle/85temzeeTtDceKrx9";
        final String CALL_BACK_ACTION_TITLE = "REQUEST CALLBACK";
        Action callbackAction = new Action(HYBRID_LEAD_GEN, CALL_BACK_ACTION_TITLE, ActionType.NAVIGATION);
        cardDataList.add(new OnboardingCardDetails(CALL_BACK_TITLE, FREE_TRIAL_DESCRIPTION, null, null, callbackAction,
                new OnBoardingFooterWeb("","", ""),null, null, null,null,null,null,null,null,null,"", null, null, null, null, null));
    }

    public void addPlayFreeTrialCardData(Action freeTrialAction, Integer trialCount) {
        final String membershipType = "FREE";
        final String FREE_TRIAL_TITLE = "Book a session for free";
        final String FREE_TRIAL_DESCRIPTION = "You can use the free trial to try out sports offer at this centre";

        String trials = "01";
        if(trialCount != null){
            trials =  trialCount.toString();
        }

        cardDataList.add(new OnboardingCardDetails(FREE_TRIAL_TITLE, FREE_TRIAL_DESCRIPTION, null, null,freeTrialAction ,
                new OnBoardingFooterWeb(trials ,"Free Trial", "Per Sport"),null, null, membershipType,null,null,null,null,null,null,"", null, null, null, null, null));
    }

    public void addPlayBookingCardData(Action action) {
        final String membershipType = "FREE";
        final String FREE_TRIAL_TITLE = "Book a session now";
        final String FREE_TRIAL_DESCRIPTION = "You can use your play pass to access any centre in the city.";

        cardDataList.add(new OnboardingCardDetails(FREE_TRIAL_TITLE, FREE_TRIAL_DESCRIPTION, null, null, action ,
                new OnBoardingFooterWeb("" ,"UNLIMITED", "SESSIONS"),null, null, membershipType,null,null,null,null,null,null,"", null, null, null, null, null));
    }

    public void addLuxAppDownloadCardData(Boolean isLuxMember) {
        final String membershipType = "LUX";
        final String LUX_APP_DOWNLOAD_TITLE = isLuxMember ? "Download cult.fit app" : "Buy membership on the cult.fit app or at the gym" ;
        final String LUX_APP_DOWNLOAD_DESCRIPTION = isLuxMember ? "Download the app from play store / app store for easy access" : "Download the app from play store / app store. Also you can walk in to the gym & see facilities & buy!";
        cardDataList.add(new OnboardingCardDetails(LUX_APP_DOWNLOAD_TITLE, LUX_APP_DOWNLOAD_DESCRIPTION, null, null,new Action(ActionType.SHOW_DOWNLOAD_APP_CTA, "TRY FOR FREE") ,
                null,null, null, membershipType,null,null,null, null, null, null,"", null, null, null, null, null));
    }


    public void addLuxMembershipCardData(Boolean isWeb, Action ctaClickAction) {
        if (isWeb) {
            final String membershipType = "LUX";
            final String LUX_CHECK_IN_TITLE = "Check In using QR Code";
            final String LUX_CHECK_IN_DESCRIPTION = "Generate QR Code and scan it at the gym entrance for access";
            cardDataList.add(new OnboardingCardDetails(LUX_CHECK_IN_TITLE, LUX_CHECK_IN_DESCRIPTION, null, null, ctaClickAction ,
                    null,null, null, membershipType,null,null,null, null, null, null, "", null, null, null, null, null));
        }
    }


    public void addProMembershipCardData(PackItem proPackItem, Boolean isPartOfGstSplitSegment, Boolean isPartOfCLPSegment, Boolean isProSelectPackEnabled, boolean isSKuPlusFlow, ProductSubType productSubType, Boolean isPartOfLimitedGXExperiment, boolean doesUserBelongToLiteBaseSegment) {
        Boolean isPartOfCLPWebSegment = isPartOfCLPSegment && this.isWeb;
        final String membershipType = "PRO";
        String PRO_TITLE = isPartOfCLPWebSegment && isProSelectPackEnabled ? "PRO" : "Cultpass PRO";
        String accessString = isPartOfLimitedGXExperiment ? "Access to " : "Unlimited access to ";
        String PRO_DESCRIPTION = isPartOfCLPWebSegment && isProSelectPackEnabled ? accessString :  accessString + "this & ALL other PRO gyms in your city";
        final String PRO_IMAGE_URL = "image/skuTitlesV3/pro.png";
        final String PRO_IMAGE_V2_URL = "image/icons/PRO_CARD_v2.png";
        final BrandLogoTypes BRAND_LOGO = BrandLogoTypes.cultPassPro;
        OnBoardingFooterWeb webFooter;
        final List<String> PRO_DESCRIPTION_LIST = new ArrayList<>();
        PRO_DESCRIPTION_LIST.add("• This gym");
        PRO_DESCRIPTION_LIST.add("• All other PRO gyms in your city");
        this.isNewBuyWidget = true;
        this.bannerUrl = isSKuPlusFlow ? null : "/image/cultpass/cult_pass_black.png";
        OnboardingTitleList PRO_TITLE_LIST = new OnboardingTitleList(isPartOfLimitedGXExperiment ? "" : "UNLIMITED","Get","access to");
        this.footerTitle=null;

        OnboardingFooter appFooter;
        if(isPartOfGstSplitSegment && proPackItem.getPriceBreakup() != null){
            double totalDuration = 0;
            String[] durationStringArr = proPackItem.getDuration().split("[+]");
            for (String duration : durationStringArr) {
                totalDuration = totalDuration + Double.parseDouble(duration);
            }
             ProductTaxBreakup monthlyBreakup = SkuPackUtil.getMonthlyPriceBreakup(proPackItem.getPriceBreakup(), totalDuration);
            String superScript = isPartOfCLPWebSegment && isProSelectPackEnabled ? "Per month" : "per month + taxes";
            String subScript = isPartOfCLPWebSegment && isProSelectPackEnabled ? "Onwards" : "onwards";
             webFooter = new OnBoardingFooterWeb(proPackItem.getCurrencyUnit() + Math.round(monthlyBreakup.getBasePrice()), superScript, subScript);
            appFooter = new OnboardingFooter(proPackItem.getCurrencyUnit() + proPackItem.getPerMonthPriceWithoutDuration() + "/month*", "+ taxes","Starting at");
        }else{
            String superScript = isPartOfCLPWebSegment && isProSelectPackEnabled ? "Per month" : "per month";
            String subScript = isPartOfCLPWebSegment && isProSelectPackEnabled ? "Onwards" : "onwards";
            webFooter = new OnBoardingFooterWeb(proPackItem.getCurrencyUnit() + proPackItem.getPerMonthPriceWithoutDuration(), superScript, subScript);
            appFooter = new OnboardingFooter(proPackItem.getCurrencyUnit() + proPackItem.getPerMonthPriceWithoutDuration() + "/month*", "","Starting at");
        }

        String subtitle = "";
        Action proSkuViewAction;
        if(isPartOfCLPWebSegment && isProSelectPackEnabled) {
            PRO_DESCRIPTION = isPartOfLimitedGXExperiment ? "Access to " : "Unlimited access to ";
            subtitle = "All PRO centers";
            proSkuViewAction = new Action(this.isWeb ? "select-widget" : PRO_SKU_BUY_PAGE_URL, "BUY NOW" ,ActionType.SCROLL_TO_WIDGET);

            HashMap<String, String> params = new HashMap<>();
            params.put("widgetId", "select-widget");
            params.put("packPreference", "PRO");
            proSkuViewAction.setMeta(params);
        } else if (!isWeb && doesUserBelongToLiteBaseSegment) {
            subtitle = "";
            proSkuViewAction = new Action("curefit://fl_listpage?pageId=ProTypeComp", "BUY NOW" , ActionType.NAVIGATION);
        } else {
            proSkuViewAction = new Action(
                this.isWeb
                    ? CULT_PRO_SKU_PURCHASE_URL_WEB
                    : isSKuPlusFlow
                        ? "curefit://fl_listpage?pageId=SKUPurchasePage&selectedTab=gold&scrollToWidgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play&widgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play"
                        : PRO_SKU_BUY_PAGE_URL,
                "BUY NOW" ,ActionType.NAVIGATION);
        }

        if(isWeb){
            /* Add Pro Plus Membership Card - overrides
             * ProductSubType.PLUS means PRO+
             * */
            PRO_TITLE = "PRO";
            PRO_DESCRIPTION = "Access to";
            subtitle = "All PRO centers in City";
            if( ProductSubType.PLUS.equals(productSubType)) {
                PRO_TITLE = "PRO PLUS";
                PRO_DESCRIPTION = "Access to";
                subtitle = "All PRO Centers in India";
                // Always scroll to bottom select widget, if elite+
                proSkuViewAction = new Action("select-widget", "BUY NOW", ActionType.SCROLL_TO_WIDGET);
                HashMap<String, String> params = new HashMap<>();
                params.put("widgetId", "select-widget");
                params.put("packPreference", "PRO_PLUS");
                proSkuViewAction.setMeta(params);
            }
        }


        cardDataList.add(new OnboardingCardDetails(PRO_TITLE, PRO_DESCRIPTION, null, PRO_IMAGE_URL, proSkuViewAction,
                this.isWeb ?  webFooter : appFooter ,null, BRAND_LOGO, membershipType,PRO_DESCRIPTION_LIST,PRO_TITLE_LIST,PRO_IMAGE_V2_URL,
                null, null, null, subtitle, null, null, null,
                isSKuPlusFlow || doesUserBelongToLiteBaseSegment ? "#86868626" : null, isSKuPlusFlow || doesUserBelongToLiteBaseSegment? "#BEBEC299" : null));
    }

    public void addEliteMembershipCardData(
        PackItem elitePackItem, Boolean isPartOfGstSplitSegment, Boolean isPartOfCLPSegment,
        Boolean isEliteSelectPackEnabled, boolean isSKuPlusFlow, ProductSubType productSubType,
        boolean isPartOfLimitedGXExperiment, boolean doesUserBelongToLiteBaseSegment
    ) {
        Boolean isPartOfWebCLPSegment = isPartOfCLPSegment && this.isWeb;
        final String membershipType = "ELITE";
        String ELITE_TITLE = isPartOfWebCLPSegment && isEliteSelectPackEnabled ? "ELITE" : "Cultpass ELITE";
        String accessString = isPartOfLimitedGXExperiment ? "Access to " : "Unlimited access to ";
        String ELITE_DESCRIPTION = isPartOfWebCLPSegment && isEliteSelectPackEnabled ? accessString : this.isWeb ? accessString + "cult group classes & all ELITE & PRO gyms"
                : accessString +" at-centre group classes, this gym & all other ELITE & PRO gyms.";

        final String ELITE_IMAGE_URL = "image/skuTitlesV3/elite.png";
        final String ELITE_IMAGE_V2_URL = "image/icons/ELITE_CARD_v2.png";
        final BrandLogoTypes BRAND_LOGO = BrandLogoTypes.cultPassElite;
        final List<String> ELITE_DESCRIPTION_LIST = new ArrayList<>();
        ELITE_DESCRIPTION_LIST.add("• All gyms in your city");
        ELITE_DESCRIPTION_LIST.add("• All group class centers in your city");
        OnboardingTitleList ELITE_TITLE_LIST = new OnboardingTitleList(isPartOfLimitedGXExperiment ? "" : "UNLIMITED","Get","access to");
        this.isNewBuyWidget = true;

        if (!isWeb && doesUserBelongToLiteBaseSegment) {
            this.bannerUrl = "/image/cultpass/cult_pass_black.png";
            this.footerTitle = null;
        } else if (!isSKuPlusFlow) {
            this.addFooterDetails();
            this.bannerUrl = "/image/cultpass/cult_pass_black.png";
        } else {
            this.footerTitle = null;
        }

        Action eliteSkuActionWeb; // TODO: find where is this used, else refactor in future
        Action eliteSkuViewAction;

        String subtitle = "";
        if(isEliteSelectPackEnabled && isPartOfWebCLPSegment) { // only for web
            ELITE_DESCRIPTION = isPartOfLimitedGXExperiment? "Access to " : "Unlimited access to ";
            subtitle = "All centers in City";
            eliteSkuViewAction = new Action("select-widget", "BUY NOW" , ActionType.SCROLL_TO_WIDGET);

            HashMap<String, String> params = new HashMap<>();
            params.put("widgetId", "select-widget");
            params.put("packPreference", "ELITE");
            eliteSkuViewAction.setMeta(params);
        } else if (!isWeb && doesUserBelongToLiteBaseSegment) {
            subtitle = "";
            eliteSkuViewAction = new Action("curefit://fl_listpage?pageId=eliteTypeComp", "BUY NOW" , ActionType.NAVIGATION);
        } else {
            subtitle = "";
            eliteSkuViewAction = new Action(this.isWeb
                    ? CULT_ELITE_SKU_PURCHASE_URL_WEB
                    : isSKuPlusFlow
                        ? "curefit://fl_listpage?pageId=SKUPurchasePage&selectedTab=black&scrollToWidgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play&widgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play"
                        : ELITE_SKU_BUY_PAGE_URL
                , "BUY NOW" , ActionType.NAVIGATION);
        }


        if(this.isWeb && ProductSubType.PLUS.equals(productSubType)){
            /* Add Elite Plus Membership Card
             * */
            ELITE_TITLE = "ELITE PLUS";
            ELITE_DESCRIPTION = "Access to";
            subtitle = "All centers in India";
            ELITE_DESCRIPTION_LIST.add("• Access to All centers in India");

            // Always scroll to bottom select widget, if elite+
            eliteSkuViewAction = new Action("select-widget", "BUY NOW" , ActionType.SCROLL_TO_WIDGET);
            HashMap<String, String> params = new HashMap<>();
            params.put("widgetId", "select-widget");
            params.put("packPreference", "ELITE_PLUS");
            eliteSkuViewAction.setMeta(params);
        }

        OnboardingFooter appFooter;

        OnBoardingFooterWeb webFooter;
        if(isPartOfGstSplitSegment && elitePackItem.getPriceBreakup() != null){
            double totalDuration = 0;
            String[] durationStringArr = elitePackItem.getDuration().split("[+]");
            for (String duration : durationStringArr) {
                totalDuration = totalDuration + Double.parseDouble(duration);
            }
            ProductTaxBreakup monthlyBreakup = SkuPackUtil.getMonthlyPriceBreakup(elitePackItem.getPriceBreakup(), totalDuration);
            String superScript = isPartOfWebCLPSegment && isEliteSelectPackEnabled ? "Per month" : "per month + taxes";
            String subScript = isPartOfWebCLPSegment && isEliteSelectPackEnabled ? "Onwards" : "onwards";
            webFooter = new OnBoardingFooterWeb(elitePackItem.getCurrencyUnit() + Math.round(monthlyBreakup.getBasePrice()), superScript, subScript);
            appFooter = new OnboardingFooter(elitePackItem.getCurrencyUnit() + elitePackItem.getPerMonthPriceWithoutDuration() + "/month*", "+ taxes","Starting at");
        }else{
            String superScript = isPartOfWebCLPSegment && isEliteSelectPackEnabled ? "Per month" : "per month";
            String subScript = isPartOfWebCLPSegment && isEliteSelectPackEnabled ? "Onwards" : "onwards";
            webFooter = new OnBoardingFooterWeb(elitePackItem.getCurrencyUnit() + elitePackItem.getPerMonthPriceWithoutDuration(), superScript, subScript);
            appFooter = new OnboardingFooter(elitePackItem.getCurrencyUnit() + elitePackItem.getPerMonthPriceWithoutDuration() + "/month*", "","Starting at");
        }

        cardDataList.add(new OnboardingCardDetails(
                ELITE_TITLE,
                ELITE_DESCRIPTION,
                null,
                ELITE_IMAGE_URL,
                eliteSkuViewAction,
                this.isWeb ?  webFooter : appFooter,
                null,
                BRAND_LOGO,
                membershipType,
                ELITE_DESCRIPTION_LIST,
                ELITE_TITLE_LIST,
                ELITE_IMAGE_V2_URL,
                null,
                null,
                null,
                subtitle,
                null,
                null,
                 null,
                isSKuPlusFlow || doesUserBelongToLiteBaseSegment ? "#FFD579" : null,
                isSKuPlusFlow || doesUserBelongToLiteBaseSegment? "#FFD783" : null
        ));
    }

    public void addSelectMembershipCardDataV2(CenterEntry center, String minPrice) {
        OnboardingFooter appFooter = new OnboardingFooter(minPrice, "+ taxes","Starting at");
        Action clickAction = new Action("curefit://fl_listpage?pageId=selectTypeComp&centerServiceId=" + center.getId(), "BUY NOW", NAVIGATION);
        final List<String> descriptionList = new ArrayList<>();
        descriptionList.add("• Get 2 session/month at other center in your city");
        String centerName = center.getName();
        if (centerName.length() > 15) centerName = centerName.substring(0, 15) + "...";
        OnboardingTitleList titleList = new OnboardingTitleList("","Get","access to " + centerName);
        cardDataList.add(new OnboardingCardDetails(
                null,
                "",
                null,
                null,
                clickAction,
                appFooter,
                null,
                null,
                null,
                descriptionList,
                titleList,
                "/image/icons/select_icon.png",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "#808080",
                "#808080"
        ));
    }

    public void addLuxMembershipCardData(CenterEntry center, String minPrice) {
        OnboardingFooter appFooter = new OnboardingFooter(minPrice, "+ taxes","Starting at");
        Action clickAction = new Action("curefit://fl_listpage?pageId=luxTypeComp&centerServiceId=" + (CultUtil.isHybridCultCenter(center) ? center.getLinkedCenterId() : center.getId()) + "&dividePack=true", "BUY NOW", NAVIGATION);
        final List<String> descriptionList = new ArrayList<>();
        descriptionList.add("• Check-in via your phone and start your workout");
        String centerName = center.getName();
        if (centerName.length() > 15) centerName = centerName.substring(0, 15) + "...";
        OnboardingTitleList titleList = new OnboardingTitleList("","Get","access to this " + centerName);
        cardDataList.add(new OnboardingCardDetails(
                null,
                "",
                null,
                null,
                clickAction,
                appFooter,
                null,
                null,
                null,
                descriptionList,
                titleList,
                "/image/luxury/lux_card_tag.svg",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "#660E8A",
                "#e6e6e6"
        ));
    }

    public void addSkuPlusMembershipCardData(CultPackBrowseProductType productType, UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                productType.equals(CultPackBrowseProductType.ELITE_PLUS) ? ProductType.FITNESS : ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.PLUS, false,
                userContext, null
        );

        FitnessPackBrowseWidget fitnessPackBrowseWidget = new FitnessPackBrowseWidget();
        Integer minPerMonthPrice = null;
        for (AugmentedOfflineFitnessPack pack: packs) {
            HashMap<String, Object> pricingResponse = fitnessPackBrowseWidget.getPackDetailsMap(userContext, interfaces, pack, null, null);
            int perMonthPrice = (int) Math.round(Double.parseDouble((String) pricingResponse.get("perMonthPriceWithoutDuration")));
            minPerMonthPrice = Objects.isNull(minPerMonthPrice) ? perMonthPrice : Math.min(minPerMonthPrice, perMonthPrice);
        }
        OnboardingFooter appFooter = new OnboardingFooter(minPerMonthPrice != null ? "₹" + minPerMonthPrice + " / month" : "", "+ taxes","Starting at");
        String pageId = productType.equals(CultPackBrowseProductType.ELITE_PLUS) ? "cultpassElitePlusSKU" : "cultpassProPlusSKU";
        Action clickAction = new Action("curefit://fl_listpage?pageId=" + pageId, "BUY NOW", NAVIGATION);
        final List<String> descriptionList = new ArrayList<>();
        descriptionList.add("Including extra pause days, intercity sessions, and more");
        OnboardingTitleList titleList = new OnboardingTitleList("Get 2x benefits","","");
        this.cardDataList.add(new OnboardingCardDetails(
                null,
                "",
                null,
                null,
                clickAction,
                appFooter,
                null,
                null,
                null,
                descriptionList,
                titleList,
                productType.equals(CultPackBrowseProductType.ELITE_PLUS) ? "/image/icons/ep_card_tag.svg" : "/image/icons/pp_card_tag.svg",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                productType.equals(CultPackBrowseProductType.ELITE_PLUS) ? "#8F55A9"  :"#F6746D",
                "#FFFFFF"
        ));
    }

    public void addSelectMembershipCardData(PackItem selectPackItem, Boolean isPartOfGstSplitSegment, CenterEntry center, String version, Boolean isPartOfLimitedGXExperiment) {
        final String membershipType = "SELECT";
        final String SELECT_TITLE = "SELECT";
        final String SELECT_DESCRIPTION = isPartOfLimitedGXExperiment ? "Access to " : "Unlimited access to ";
        final String SELECT_IMAGE_URL = "";
        final String SELECT_IMAGE_V2_URL = "";

        Integer maxCenterCredit =  selectPackItem.getOfferCredits();

        final BrandLogoTypes BRAND_LOGO = BrandLogoTypes.cultpassSelectPartial;
        final List<String> SELECT_DESCRIPTION_LIST = new ArrayList<>();
        SELECT_DESCRIPTION_LIST.add("• This gyms in your city");
        OnboardingTitleList ELITE_TITLE_LIST = new OnboardingTitleList(isPartOfLimitedGXExperiment ? "" : "UNLIMITED","Get","access to");
        this.addFooterDetails();
        this.isNewBuyWidget = true;
        this.bannerUrl = "/image/cultpass/cult_pass_black.png";

        Action eliteSkuActionWeb = new Action("curefit://fitness/cultpass-elite?widgetId=cdaf7867-b716-4d8c-84de-a994e6da4fda", "BUY NOW", ActionType.SCROLL_TO_WIDGET);
        Action eliteSkuViewAction = new Action(this.isWeb ? "select-widget" : ELITE_SKU_BUY_PAGE_URL, "BUY NOW" , ActionType.SCROLL_TO_WIDGET);
        Action secondaryAction = Objects.equals(version, "WEB_CLP_V2") &&  maxCenterCredit != null ? new Action("how-credit-work-widget", "+ up to "+maxCenterCredit+" sessions at other centers", ActionType.SCROLL_TO_WIDGET): null;

        HashMap<String, String> params = new HashMap<>();
        params.put("widgetId", "select-widget");
        params.put("packPreference", "SELECT");
        eliteSkuViewAction.setMeta(params);
        OnboardingFooter appFooter;

        OnBoardingFooterWeb webFooter;
        if(isPartOfGstSplitSegment && selectPackItem.getPriceBreakup() != null){
            double totalDuration = 0;
            String[] durationStringArr = selectPackItem.getDuration().split("[+]");
            for (String duration : durationStringArr) {
                totalDuration = totalDuration + Double.parseDouble(duration);
            }
            ProductTaxBreakup monthlyBreakup = SkuPackUtil.getMonthlyPriceBreakup(selectPackItem.getPriceBreakup(), totalDuration);
            webFooter = new OnBoardingFooterWeb(selectPackItem.getCurrencyUnit() + Math.round(monthlyBreakup.getBasePrice()), "Per month", "Onwards");
            appFooter = new OnboardingFooter(selectPackItem.getCurrencyUnit() + selectPackItem.getPerMonthPriceWithoutDuration() + "/month*", "+ taxes","Starting at");
        }else{
            webFooter = new OnBoardingFooterWeb(selectPackItem.getCurrencyUnit() + selectPackItem.getPerMonthPriceWithoutDuration(), "Per month", "Onwards");
            appFooter = new OnboardingFooter(selectPackItem.getCurrencyUnit() + selectPackItem.getPerMonthPriceWithoutDuration() + "/month*", "","Starting at");

        }

        String subtitle = center.getName();

        cardDataList.add(new OnboardingCardDetails(SELECT_TITLE, SELECT_DESCRIPTION, null, SELECT_IMAGE_URL, eliteSkuViewAction,
                this.isWeb ?  webFooter : appFooter, null, BRAND_LOGO, membershipType,SELECT_DESCRIPTION_LIST,ELITE_TITLE_LIST,SELECT_IMAGE_V2_URL, null, null, null, subtitle, null, null, secondaryAction, null, null));

    }
    public void addPlayMembershipCardData(ServiceInterfaces interfaces, PackItem playPack, Boolean isPartOfGstSplitSegment) {
        final String membershipType = "PLAY";
        final String TITLE = "Cultpass PLAY";
        final String DESCRIPTION = "Unlimited access to swimming, badminton and other sports.";
        final BrandLogoTypes BRAND_LOGO = BrandLogoTypes.cultPassPlay;
        OnboardingTitleList TITLE_LIST = new OnboardingTitleList("UNLIMITED","Get","access to");
        this.isNewBuyWidget = true;
        Action eliteSkuViewAction = new Action(this.isWeb ? PlayUtil.getPlayReasonToBuyWebWidgetUrl(interfaces) : ELITE_SKU_BUY_PAGE_URL, "BUY NOW" , ActionType.NAVIGATION);
        OnboardingFooter appFooter = new OnboardingFooter(playPack.getCurrencyUnit() + playPack.getPerMonthPriceWithoutDuration() + "/month*", "+ taxes","Starting at");

        OnBoardingFooterWeb webFooter;
        if(isPartOfGstSplitSegment && playPack.getPriceBreakup() != null){
            double totalDuration = 0;
            String[] durationStringArr = playPack.getDuration().split("[+]");
            for (String duration : durationStringArr) {
                totalDuration = totalDuration + Double.parseDouble(duration);
            }
            ProductTaxBreakup monthlyBreakup = SkuPackUtil.getMonthlyPriceBreakup(playPack.getPriceBreakup(), totalDuration);
            webFooter = new OnBoardingFooterWeb(playPack.getCurrencyUnit() + Math.round(monthlyBreakup.getBasePrice()), "per month + taxes", "onwards");
        }else{
            webFooter = new OnBoardingFooterWeb(playPack.getCurrencyUnit() + playPack.getPerMonthPriceWithoutDuration(), "per month", "onwards");
        }

        cardDataList.add(
                new OnboardingCardDetails(
                        TITLE,
                        DESCRIPTION,
                        null,
                        PLAY_COMPARISION_V2_IMAGE_URL,
                        eliteSkuViewAction,
                        this.isWeb ?  webFooter : appFooter,
                        null,
                        BRAND_LOGO,
                        membershipType,
                        null,
                        TITLE_LIST,
                        PLAY_PRO_ICON_IMAGE_URL,
                        null,
                        null,
                        null,
                        "",
                        null,
                        null,
                        null,
                        null,
                        null
                ));
    }

    public void addPlayMembershipCardData(Action action, String currencyUnit, String price) {
        final String membershipType = "PLAY";
        final String PLAY_DESCRIPTION = "Get UNLIMITED access to all sports at all centers in your city & home workouts";
        final String PLAY_IMAGE_URL = PlayUtil.PLAY_PRO_ICON_IMAGE_URL;

        cardDataList.add(
                new OnboardingCardDetails(
                        null,
                        PLAY_DESCRIPTION,
                        3,
                        PLAY_IMAGE_URL,
                        action,
                        new OnboardingFooter("UPGRADE PASS", null,null),
                        null,
                        null,
                        membershipType,null,null,null, 80, 64, false, "", null, null, null,null,null));
    }

    private OnboardingCardDetails getPlayBuyMembershipWidget(CultPackBrowseProductType productType, UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        boolean isPartOfGstSplitSegment = AppUtil.doesUserBelongToGstSplitSegment(interfaces, interfaces.getEnvironmentService(), userContext);
        List<FitnessPack> packList = null;
        if (productType == CultPackBrowseProductType.PLAY)
            packList = interfaces.catalogueService.getNewPlayPacksByCity(userContext.getUserProfile().getCity().getCityId(), userContext.getUserProfile().getUserId());
        if (productType == CultPackBrowseProductType.PLAY_SELECT)
            packList = interfaces.catalogueService.getNewPlaySelectPacksByCity(userContext.getUserProfile().getCity().getCityId(), true, userContext.getUserProfile().getUserId());

        if (packList == null || packList.isEmpty()) return null;
        packList = packList.stream().filter(pack -> pack.getVisibility() != null && pack.getVisibility().contains("app")).collect(Collectors.toList());
        List<String> productIds = packList.stream().map(pack -> pack.getProductId()).collect(Collectors.toList());
        PlayProductPricesResponse playProductPricesResponse = (PlayProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                ProductType.PLAY, userContext, productIds, userContext.getUserProfile().getCity().getCityId(), null
        ).get();
        Map<String, ProductTaxBreakup> taxBreakupMap = null;
        if (isPartOfGstSplitSegment)
            taxBreakupMap = interfaces.offerService.getTaxBreakUpForPlayProducts(playProductPricesResponse);

        packList.sort(Comparator.comparing(FitnessPack::getNumDays));

        if (!packList.isEmpty()) {
            long minPrice = 0;

            for (FitnessPack pack: packList) {
                PackOfferDetails offerDetails = PlayUtil.getOfferDetails(pack, playProductPricesResponse);
                List<OfferMini> offerMinis = offerDetails.getOffers();

                Integer packDuration = pack.getDuration();

                List <OfferMini> filterOfferMinis = new ArrayList<>();
                for (OfferMini offer: offerMinis) {
                    if (offer.getAddons() != null) {
                        filterOfferMinis.add(offer);
                        for (Addon addon : offer.getAddons()) {
                            if(addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.CULT_PLAY_EXTENSION
                                    && addon.getConfig() != null && addon.getConfig().getCount() != null){
                                packDuration = packDuration + addon.getConfig().getCount();
                                break;
                            }
                        }
                    } else if (offer.getDescription() != null) {
                        filterOfferMinis.add(offer);
                    }
                }

                offerDetails.setOffers(filterOfferMinis);

                long offerPrice = offerDetails.getPrice().getListingPrice() != null
                        ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();

                ProductTaxBreakup priceBreakup = null;
                if (isPartOfGstSplitSegment) {
                    priceBreakup = taxBreakupMap.get(pack.getProductId());
                    offerPrice = (long) priceBreakup.getBasePrice();
                }

                double numberOfMonths = Math.floor((packDuration.doubleValue()/30)*2)/2.0;
                long perMonthPrice = Math.round(Math.ceil((double) offerPrice / numberOfMonths));
                if (minPrice == 0 || minPrice >= perMonthPrice) {
                    minPrice = perMonthPrice;
                }
            }

            if (productType == CultPackBrowseProductType.PLAY) {
                final List<String> DESCRIPTION_LIST = new ArrayList<>();
                DESCRIPTION_LIST.add("• All sports at all centers in your city & home workouts.");
                OnboardingTitleList TITLE_LIST = new OnboardingTitleList("UNLIMITED","Get","access to");
                return addPlayCenterMembershipCardData(
                        null,
                        TITLE_LIST,
                        DESCRIPTION_LIST,
                        "Get UNLIMITED access to all sports at all centers in your city & home workouts",
                        PlayUtil.PLAY_PRO_ICON_IMAGE_URL,
                        new Action(PLAY_PACKS_PAGE_DEEPLINK, ActionType.NAVIGATION),
                        OrderUtil.RUPEE_SYMBOL,
                        String.valueOf(minPrice),
                        productType);
            }
            if (productType == CultPackBrowseProductType.PLAY_SELECT) {
                final List<String> DESCRIPTION_LIST = new ArrayList<>();
                DESCRIPTION_LIST.add("• Any one center of your choice.");
                OnboardingTitleList TITLE_LIST = new OnboardingTitleList("UNLIMITED","Get","access to any one center of your choice.");
                return addPlayCenterMembershipCardData(
                        null,
                        TITLE_LIST,
                        DESCRIPTION_LIST,
                        "Get UNLIMITED access to any one center of your choice.",
                        PlayUtil.PLAY_LITE_ICON_IMAGE_URL,
                        new Action(ALL_PLAY_LITE_CENTERS_DEEPLINK, ActionType.NAVIGATION),
                        OrderUtil.RUPEE_SYMBOL,
                        String.valueOf(minPrice),
                        productType);
            }
        }else
            return null;

        return null;
    }
    public OnboardingCardDetails addPlayCenterMembershipCardData(
        String title,
        OnboardingTitleList titleList,
        List<String> descriptionList,
        String description,
        String imageUrl,
        Action action,
        String currencyUnit,
        String price,
        CultPackBrowseProductType productType
    ) {
        final String membershipType = "PLAY";

        String body = null, suffix = null, prefix = null;
        if (productType.equals(CultPackBrowseProductType.PLAY_SELECT)) {
            body = "Up to 20% Off";
        } else {
            body = currencyUnit + price + "/month*";
            suffix = "+ taxes";
            prefix = "Starting at";
        }
        return new OnboardingCardDetails(
                title,
                description,
                3,
                imageUrl,
                action,
                new OnboardingFooter(body, suffix, prefix),
                null,
                null,
                membershipType,
                descriptionList,
                titleList,
                imageUrl,
                80,
                64,
                false,
                "",
                null,
                null,
                null,
                null,
                null
        );
    }

    public void setFooter(String footer){
        this.footer = footer;
    }

    public void setTitle(String title){
        this.title = title;
    }

    public void addFooterDetails(){
        this.footerTitle ="What are PRO and ELITE Gyms?";
        this.footerTextList.add(new FooterText("Best in class Partner Gyms","image/icons/PRO_TAG_v2.png"));
        this.footerTextList.add(new FooterText("Premium gyms like gold’s, cult gyms, etc","image/icons/ELITE_TAG_v2.png"));
    }

    public void addDivider(String text, String image) {
        dividerTextList.add(new DividerText(text, image));
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public class DividerText {
        String text;
        String image;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public class FooterText {
        String text;
        String image;
    }
}
