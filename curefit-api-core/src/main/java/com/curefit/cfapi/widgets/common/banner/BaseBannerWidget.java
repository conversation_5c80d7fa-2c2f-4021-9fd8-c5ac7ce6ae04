package com.curefit.cfapi.widgets.common.banner;

import com.curefit.base.enums.AppTenant;
import com.curefit.base.enums.UserAgent;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.meta.SelectionModalData;
import com.curefit.cfapi.model.internal.meta.SelectionModalDataOption;
import com.curefit.cfapi.model.internal.meta.SelectionModalMeta;
import com.curefit.cfapi.model.internal.userinfo.SessionInfo;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.model.mongo.WidgetTemplate;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionMeta;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.banner.BannerType;
import com.curefit.cfapi.service.EnterpriseUtils;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.appConfig.EnterpriseConfig;
import com.curefit.cfapi.util.ActionUtil;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.CDNUtil;
import com.curefit.cfapi.util.OrderUtil;
import com.curefit.cfapi.util.PlayUtil;
import com.curefit.cfapi.util.StringUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.util.UrlPathBuilder;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem.BannerStoryType;
import com.curefit.cfapi.widgets.enterprise.TimeOfDayType;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.RuntimeBaseException;
import com.curefit.commons.util.Serializer;
import com.curefit.product.enums.ProductType;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.common.collect.Lists;
import fit.cult.enterprise.dto.corporate.Corporate;
import fit.cult.enterprise.dto.corporate.CorporateBenefits;
import fit.cult.enterprise.dto.corporate.CorporateDetails;
import fit.cult.enterprise.dto.corporate.CorporateMetrics;
import fit.cult.enterprise.dto.program.ProgramType;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.curefit.cfapi.constants.Constants.OLD_TEMPLATE_ID_PARAM_NAME;
import static com.curefit.cfapi.constants.Constants.PMS_TEMPLATE_ID_PARAM_NAME;
import static com.curefit.cfapi.constants.StringConstants.PER_MONTH_PRICE_VARIABLE;
import static com.curefit.cfapi.constants.StringConstants.PER_MONTH_PRICE_VARIABLE_REGEX;
import static com.curefit.cfapi.service.appConfig.EnterpriseConfig.ENTERPRISE_CLP_URL;
import static com.curefit.cfapi.util.UserAttributeUtil.*;
import static com.curefit.cfapi.widgets.fitness.SkuPackUtil.getStartingPerMonthPrice;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class AddPatientActionMetaRequestParams {
    String patientRelation;
    String formUserType;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class AddPatientActionMeta {
        AddPatientActionMetaRequestParams reqParams;
        String title;
        String url;
        Action action;
}

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor // Important: Add this for Jackson
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonDeserialize(as = LottieText.class)
class LottieText {
    String originalText;
    String finalText;
}

@Slf4j
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class BaseBannerWidget extends BaseWidget {
    Integer maxNumBanners;
    Integer minNumBanners = 0;
    protected Integer ttlViewCount;
    protected Integer ttlExpiryInMs;
    List<BannerItem> data;
    Header header;
    Footer footer;
    ProductType productType;
    List<Action> footerActions;
    Integer bannerScrollSpeed = 3000;

    @JsonIgnore
    static ThreadLocal<CorporateDetails> TL_CORP_DETAILS = ThreadLocal.withInitial(() -> null);

    protected void decorateLayoutProps (Map<String, Object> layoutProps) {}

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {

        String widgetId =  widgetContext.getQueryParams().get("widgetId");

        // NOTE: build in series to avoid consuming too many threads
        List<BannerItem> bannerItems = this.data.stream()
                .map(bannerItem -> {
                    try {
                        return this.buildBannerItem(userContext, bannerItem, interfaces,  widgetContext);
                    } catch (Exception e) {
                        RuntimeBaseException ex = new RuntimeBaseException(e);
                        ex.setContext(Map.of("bannerId", bannerItem.getBannerIdentifier()));
                        interfaces.exceptionReportingService.reportException(ex);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        this.fillDefaultPriorityAndDedupeId(bannerItems);
        bannerItems = this.dedupeBannerItems(bannerItems, false);

        // Checking Enterprise Corporate explore story item exist
        if (!bannerItems.isEmpty()) {
            List<BannerItem> finalBannerItems = bannerItems;
            int corpExploreCardIndex = IntStream.range(0, bannerItems.size()).filter(i -> finalBannerItems.get(i).getStoryType() != null && finalBannerItems.get(i).getStoryType().equals(BannerStoryType.CORP_EXPLORE_CARD.name())).findFirst().orElse(-1);
            if (corpExploreCardIndex != -1) {
                log.debug("BaseBannerWidget corp explore story item index {}", corpExploreCardIndex);
                if (AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_ZOOM_ELITE_PRO_METRIC_WIDGET_ROLL_OUT_APP_VERSION)) {
                    Corporate corporate = interfaces.getEnterpriseClient().getCorporate(userContext.getUserProfile().getUserId());
                    if (corporate != null) {
                        manipulateCorpExploreBanner(corporate, bannerItems.get(corpExploreCardIndex), true);
                    }
                    else {
                        corporate = EnterpriseUtils.getCorporateFromRashiEvent(interfaces, userContext);
                        if(corporate != null) {
                            manipulateCorpExploreBanner(corporate, bannerItems.get(corpExploreCardIndex), false);
                        } else {
                            bannerItems.remove(corpExploreCardIndex);
                        }
                    }
                }
                else {
                    bannerItems.remove(corpExploreCardIndex);
                }
            }
        }

        if(AppUtil.isEnterpriseCorpSupported(userContext)) {
            if (!bannerItems.isEmpty() && bannerItems.get(0).getStoryType() != null) {

                List<BannerItem> finalBannerItems = bannerItems;
                int haveMetricStoryItem = IntStream.range(0, bannerItems.size()).filter(i -> finalBannerItems.get(i).getStoryType() != null && EnterpriseConfig.supportedMetricStoryType.contains(finalBannerItems.get(i).getStoryType())).findFirst().orElse(-1);
                if(haveMetricStoryItem != -1) {
                    List<Integer> cultLiveBanners = new ArrayList<>();
                    List<Integer> bannersToBeRemoved = new ArrayList<>();
                    List<Integer> wellnessBanners = new ArrayList<>();
                    List<Integer> workoutSessionBanners = new ArrayList<>();
                    List<Integer> metricSessionBanners = new ArrayList<>();

                    boolean skipCultLiveBanners = false;
                    boolean haveCultLiveBenefit = false;
                    CorporateBenefits cultBenefit = EnterpriseUtils.getCultLiveBenefit(userContext,interfaces);
                    try{
                        Pair<BigDecimal, BigDecimal> cultLivePrices = EnterpriseUtils.getCultLivePrices(cultBenefit, interfaces, userContext);
                        if (cultLivePrices != null) {
                            haveCultLiveBenefit = true;
                        }
                    } catch (Exception e) {
                        log.error("Exception in checking cult live benefits found for the user", e);
                    }

                    for (int i = 0; i < bannerItems.size(); i++) {
                        BannerItem item = bannerItems.get(i);
                        try {
                            if (BannerStoryType.CULT_LIVE.name().equals(item.getStoryType())) {
                                if(haveCultLiveBenefit) {
                                    cultLiveBanners.add(i);
                                    if (!skipCultLiveBanners) {
                                        manipulateCultLiveBanner(userContext, item, interfaces);
                                    }
                                    skipCultLiveBanners = true;
                                }
                                else {
                                    bannersToBeRemoved.add(i);
                                }
                            }
                            if (BannerStoryType.WORKOUT_SESSION.name().equals(item.getStoryType())) {
                                boolean removeBanner = manipulateWorkoutSessionBanner(userContext, item, interfaces, i);
                                if (removeBanner) {
                                    bannersToBeRemoved.add(i);
                                }
                                else {
                                    workoutSessionBanners.add(i);
                                }
                            }
                            if(BannerStoryType.WELLNESS_BENEFIT.name().equals(item.getStoryType())) {
                                boolean removeBanner = manipulateWellnessBanner(userContext, interfaces);
                                if(removeBanner) {
                                    bannersToBeRemoved.add(i);
                                }
                                else {
                                    wellnessBanners.add(i);
                                }
                            }
                            if(BannerStoryType.WORKOUT_METRIC.name().equals(item.getStoryType())) {
                                boolean removeBanner = manipulateMetricBanner(userContext, item, interfaces, i);
                                if(removeBanner) {
                                    bannersToBeRemoved.add(i);
                                } else {
                                    metricSessionBanners.add(i);
                                }
                            }

                        } catch (Exception e) {
                            log.error("Error in adding enterprise data to banner at index " + i, e);
                        }
                    }
                    if(workoutSessionBanners.size() > 0 || metricSessionBanners.size() > 0) {
                        // Remove all cult live banners if at least 1 workout session banner is there
                        bannersToBeRemoved.addAll(cultLiveBanners);
                    }

                    if(workoutSessionBanners.size() > 0 || cultLiveBanners.size() > 0 || metricSessionBanners.size() > 0) {
                        // Remove wellness banner if at least 1 workout session or cult live banner is there
                        bannersToBeRemoved.addAll(wellnessBanners);
                    }
                    Collections.sort(bannersToBeRemoved);
                    for (int i = bannersToBeRemoved.size() - 1; i >=0; i--) {
                        bannerItems.remove(bannersToBeRemoved.get(i).intValue());
                    }

                    if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_ZOOM_ELITE_PRO_METRIC_WIDGET_ROLL_OUT_APP_VERSION) && metricSessionBanners.size() > 0) {
                        footerActions = null;
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(bannerItems)) {
            return null;
        }

        bannerItems.forEach(bannerItem -> {
            if (!AppUtil.shouldEnableImpressionCapping(userContext)) {
                bannerItem.setImpressionCap(null);
            }
        });

        try {
            bannerItems = removeExhaustedBannerItems(bannerItems, userContext);
        } catch (Exception e) {
            String errorMsg = String.format("Error while removing impression capped exhausted banner items for widgetId %s ", widgetId);
            log.error(errorMsg, e);
            interfaces.exceptionReportingService.reportException(errorMsg, e);
        }

        bannerItems = bannerItems.subList(0, bannerItems.size() > this.maxNumBanners ? this.maxNumBanners : bannerItems.size());

        this.data = bannerItems;
        this.data.forEach(banner -> {
            var contentMetric = banner.getContentMetric();
            if(contentMetric != null)
                contentMetric.setContentUrl(banner.getImage());
        });

        if (this.templateId != null && fillTemplateDetails(interfaces, userContext) == null) {
            return null;
        }
        if (header != null && "YEAR-END-REPORT".equals(header.getTitle())) {
            String firstName = StringUtils.EMPTY;
            try {
                UserEntry user = userContext.getUserEntryCompletableFuture().get();
                firstName = user.getFirstName() + "'s ";
            } catch (Exception e) {
                // eat
            }
            header.setTitle(firstName + "2020 fitness report");
        }

        if (header != null && header.getSubTitle() != null) {
            if(header.getTitle() != null && header.getSubTitle().equals("CULT_UNBOUND")) {
                header.setSubTitle("");
                Long noOfSeats = interfaces.cultServiceImpl.getCultUnboundSeatAvailability(Integer.parseInt(header.getTitle().trim()));
                if (noOfSeats != null) {
                    header.setTitle(STR."\{noOfSeats} Early Bird Slots Left");
                }
            }
        }

        return Collections.singletonList(this);
    }

    public BaseBannerWidget fillTemplateDetails(ServiceInterfaces interfaces, UserContext userContext) {
        SessionInfo sessionInfo = userContext.getSessionInfo();
        WidgetTemplate widgetTemplate = interfaces.widgetTemplateCache.getWidgetTemplateSync(this.templateId, AppUtil.bypassCache(userContext));
        if (widgetTemplate == null) {
            return null;
        }
        if (UserAgent.APP.equals(sessionInfo.getUserAgent()) || UserAgent.MBROWSER.equals(sessionInfo.getUserAgent())) {
            this.layoutProps = widgetTemplate.getTemplateData().getApp();
        } else {
            this.layoutProps = widgetTemplate.getTemplateData().getWeb();
        }

        if (this.layoutProps == null) {
            return null;
        }
        if(this.layoutProps instanceof Map) {
            this.decorateLayoutProps((Map<String, Object>)this.layoutProps);
        }
        return this;
    }

    public List<BannerItem> removeExhaustedBannerItems(List<BannerItem> bannerItems, UserContext userContext) throws ExecutionException, InterruptedException {
        if (bannerItems.size() <= minNumBanners) {
            return bannerItems;
        }
        int maximumToBeRemoved = bannerItems.size() - minNumBanners;
        Set<String> exhaustedBanners = new HashSet<>((Set<String>) userContext.getRequestCache().getRequestFuture(RequestType.WIDGET_IMPRESSION_CAP_DATA, userContext).get());
        if (exhaustedBanners.size() > 0) {
            log.info("exhaustedBanners - {}", exhaustedBanners.toString());
        }
        List<BannerItem> finalBannerItems = new ArrayList<>();
        for (int i = bannerItems.size()-1 ; i >=0 ; i--) {
            String contentId = bannerItems.get(i).getContentMetric() != null
                    ? StringUtils.isNotEmpty(bannerItems.get(i).getContentMetric().getContentId()) ? bannerItems.get(i).getContentMetric().getContentId() : bannerItems.get(i).getDedupeId()
                    : bannerItems.get(i).getDedupeId();
            if (StringUtils.isEmpty(contentId) || !exhaustedBanners.contains(contentId) || maximumToBeRemoved == 0) {
                finalBannerItems.add(0,bannerItems.get(i));
            } else {
                maximumToBeRemoved--;
            }
        }
        return finalBannerItems;
    }

    private void manipulateBannerAction(UserContext userContext, BannerItem banner, ServiceInterfaces interfaces) throws UnsupportedEncodingException, ExecutionException, InterruptedException, BaseException {
        if (banner.getActionV2() != null) {
            Action actionV2 = ActionUtil.getVMActionFromActionV2(banner.getActionV2(), userContext, interfaces);
            if (actionV2 != null) {
                banner.setAction(actionV2);
            }
        }

        if (banner.getAction() != null && CollectionUtils.isNotEmpty(banner.getAction().getSegmentIds())) {
            Segment segment = interfaces.segmentEvaluatorService.checkCondition(banner.getAction().getSegmentIds(),
                    userContext).get();
            if (segment == null) {
                banner.setAction(null);
            }
        }

        if (banner.getVideoUrl() != null) {
            if (banner.getAction() != null) {
                String relativeUrl = "curefit-content/" + banner.getVideoUrl();
                String url = "curefit://videoplayer?videoUrl=" + URLEncoder.encode(relativeUrl, "UTF-8") +
                        "&absoluteVideoUrl=" + URLEncoder.encode(CDNUtil.getCdnUrl(relativeUrl), "UTF-8");
            }
            banner.setVideoS3Key(banner.getVideoUrl());
            banner.setVideoUrl(UrlPathBuilder.getPrefixedVideoPath(banner.getVideoUrl()));
        }

        // Adding Widget Id to Meta
        if (banner.getAction() != null && ActionType.SCROLL_TO_WIDGET.equals(banner.getAction().getActionType())) {
            if (banner.getAction().getMeta() != null) {
                ActionMeta meta = (ActionMeta) banner.getAction().getMeta();
                meta.setWidgetId(banner.getAction().getWidgetId());
            } else {
                ActionMeta actionMeta = new ActionMeta();
                actionMeta.setWidgetId(banner.getAction().getWidgetId());
                banner.getAction().setMeta(actionMeta);
            }
            banner.getAction().setWidgetId(null);
        }  else if (banner.getAction() != null && ActionType.BOOK_LIVE_PT_SESSION.equals(banner.getAction().getActionType())) {
            banner.setAction(this.getLivePTSessionBookAction(userContext, interfaces));
        }  else if (banner.getAction() != null && ActionType.BOOK_LIVE_SGT_SESSION.equals(banner.getAction().getActionType())) {
            banner.setAction(ActionUtil.liveSGTBookingPageAction());
        }
        if (banner.getAction() != null && banner.getAction().getUrl() != null
                && (banner.getAction().getUrl().contains(OLD_TEMPLATE_ID_PARAM_NAME) ||
                    banner.getAction().getUrl().contains(PMS_TEMPLATE_ID_PARAM_NAME)
                )) {
            var action = this.getPackIdUrlFromTemplate(interfaces, userContext,
                    banner.getAction());
            // It's just not be configured for certain cities hence with this check we are preventing it from failing
            if (action != null) {
                banner.setAction(action);
            } else {
                banner.setAction(null);
            }
        }

        if (banner.getAction() != null && banner.getAction().getUrl() != null
                && banner.getAction().getUrl().indexOf("fitso_classbooking") != -1
                && banner.getAction().getUrl().indexOf("checkPlaySelectPack")  != -1) {
            var action = PlayUtil.getPlayClassBookingPageAction(banner.getAction(), interfaces, userContext);
            banner.setAction(action);
        }


        if (banner.getTimer() != null && banner.getTimer().getAction() != null && banner.getTimer().getAction().getUrl() != null
                && (banner.getTimer().getAction().getUrl().contains(OLD_TEMPLATE_ID_PARAM_NAME) ||
                    banner.getTimer().getAction().getUrl().contains(PMS_TEMPLATE_ID_PARAM_NAME)
                )) {
                var action =  this.getPackIdUrlFromTemplate(interfaces, userContext,
                        banner.getTimer().getAction());
            // It's just not be configured for certain cities hence with this check we are preventing it from failing
            banner.getTimer().setAction(action);
        }

        if (banner.getAction() != null) {
            if (banner.getAction().getUrl() != null) {
                banner.getAction().setUrl(StringUtil.replaceTemplateVariables(banner.getAction().getUrl(), userContext, interfaces));
            }
            if (banner.getAction().getTitle() != null) {
                banner.getAction().setTitle(StringUtil.replaceTemplateVariables(banner.getAction().getTitle(), userContext, interfaces));
            }
        }
    }

    private Action getLivePTSessionBookAction(UserContext userContext, ServiceInterfaces interfaces) throws ExecutionException, InterruptedException {
        if (!userContext.getSessionInfo().getIsUserLoggedIn()) {
            return this.getShowLoginModalAction();
        }
        Action nextAction = ActionUtil.livePTBookingPageAction();

        var patientsList= (List<PatientDetail>) userContext.getRequestCache().getRequestFuture(RequestType.GET_ALL_PATIENTS, userContext).get();
        var selfPatient= CollectionUtils.isNotEmpty(patientsList) ? patientsList.stream()
                .filter(patient -> "self".equals(patient.getRelationship())).findFirst().orElse(null)
                : null;
        var userInfo= userContext.getUserEntryCompletableFuture().get();
        if (selfPatient != null && selfPatient.getGender() != null) {
            nextAction.setUrl(nextAction.getUrl() + "&patientId=" +  selfPatient.getId());
            return nextAction;
        }
        if (userInfo.getGender() != null && (selfPatient == null || selfPatient.getGender() != null)) {
            var gender =  "male".equals(userInfo.getGender()) ? "Male" : "Female";
            nextAction.setUrl(nextAction.getUrl() + "&gender=" + gender);
            return nextAction;
        }
        else {
            return this.getGenderSelectionModalAction(nextAction);
        }
    }

    private Action getShowLoginModalAction() {
        Action action = new Action();
        action.setActionType(ActionType.SHOW_ALERT_MODAL);
        Action metaAction = new Action();
        metaAction.setTitle("Login Required!");
        metaAction.setSubTitle("Please login to continue");
        metaAction.setActions(Collections.singletonList(new Action(null, "Login", ActionType.LOGOUT)));
        action.setMeta(metaAction);
        return action;
    }

    private  Action getGenderSelectionModalAction(Action nextAction) {
        List<SelectionModalDataOption> options = new ArrayList<SelectionModalDataOption>();
        SelectionModalDataOption maleGenderOption = new SelectionModalDataOption();
        maleGenderOption.setDisplayText("Male");
        maleGenderOption.setValue("Male");
        SelectionModalDataOption femaleGenderOption = new SelectionModalDataOption();
        femaleGenderOption.setDisplayText("Female");
        femaleGenderOption.setValue("Female");
        options.add(maleGenderOption);
        options.add(femaleGenderOption);
        SelectionModalMeta selectionModalMeta = new SelectionModalMeta();
        SelectionModalData selectionModalData = new SelectionModalData();
        selectionModalData.setKey("gender");
        selectionModalData.setType("gender");
        selectionModalData.setOptions(options);
        selectionModalData.setTitle("Please select your gender");
        selectionModalMeta.setData(Collections.singletonList(selectionModalData));
        selectionModalMeta.setToastMessage("Please select all the fields");
        nextAction.setTitle("PROCEED");
        selectionModalMeta.setAction(nextAction);
        Action action = new Action();
        action.setActionType(ActionType.SHOW_GENDER_SELECTION_MODAL);
        action.setMeta(selectionModalMeta);
        return action;
    }

    private void manipulateBannerTimer(UserContext userContext, BannerItem banner, ServiceInterfaces interfaces) {
        if (banner.getTimer() != null && banner.getTimer().getTimerEndTimeWithTz() != null) {
            String timezone = userContext.getUserProfile().getCity().getTimezone();
            long timerEpochMilli = TimeUtil.getMillisFromDateTimezone(banner.getTimer().getTimerEndTimeWithTz().getDate(), timezone);
            if (timerEpochMilli > TimeUtil.now(timezone)) {
                banner.getTimer().setTimerEndTime(timerEpochMilli);
            } else {
                // removing banner timer, if timerEndtime is less than current time
                banner.setTimer(null);
            }
        }
        // Temp fix: Remove the timer if there is no timer end time given as app is rendering empty layout in this case
        if (banner.getTimer() != null && banner.getTimer().getTimerEndTimeWithTz() == null) {
            banner.setTimer(null);
        }
    }

    private void manipulateBannerImage(UserContext userContext, BannerItem banner) {
        if (UserAgent.MBROWSER.equals(userContext.getSessionInfo().getUserAgent())) {
            banner.setImage(UrlPathBuilder.prefixSlash(banner.getMwebImage()));
        } else if (UserAgent.DESKTOP.equals(userContext.getSessionInfo().getUserAgent())) {
            banner.setImage(UrlPathBuilder.prefixSlash(banner.getWebImage()));
        } else {
            if (banner.getBannerType() == null || !banner.getBannerType().equals(BannerType.CRM_ASSET)) {
                banner.setImage(UrlPathBuilder.prefixSlash(banner.getImage()));
            }
        }
    }

    String getUserBucket(UserContext userContext, String experimentId) throws ExecutionException, InterruptedException {
        var userAllocation =  userContext.getUserProfile().getHamletUserExperimentPromise().get();
        var userAssignment = userAllocation.getAssignmentsMap().get(experimentId);
        return userAssignment != null && userAssignment.getBucket() != null ? userAssignment.getBucket().getBucketId() : null;
    }

    List<BannerItem> getPersonalizedBanners(UserContext userContext, ServiceInterfaces interfaces,
        String experimentId, int maxNumBanners, List<BannerExperiment> experiments, WidgetContext widgetContext
    ) throws BaseException, ExecutionException, InterruptedException {
        var attributesToFetch = new ArrayList(PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY.values());
        attributesToFetch.addAll(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP.values());
        var bucket = this.getUserBucket(userContext, experimentId);
        if(bucket != null) {
            var userBannerExperiment = experiments
                .stream()
                .filter(experiment -> experiment.getBucketId().equals(bucket))
                .findFirst()
                .get();
            var userAttributesResponse =  interfaces.userAttributesCacheClient.getAttributes(
                    Long.valueOf(userContext.getUserProfile().getUserId()),
                    attributesToFetch, AppUtil.getAppTenantFromUserContext(userContext));
            var userAttributes = userAttributesResponse.getAttributes();
            var bannerVariants = userBannerExperiment.getVariants();
            var sortDimensions = userBannerExperiment.getDimensions().stream().filter(
                    dimension -> IntelligentDimension.CATEGORY_AFFINITY.equals(dimension) ||
                            WORKOUT_FORMAT_AFFINITY.contains(dimension)
            ).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(sortDimensions)) {
                bannerVariants = this.sortByCategoryOrFormatAffinity(sortDimensions, bannerVariants, userAttributes);
            }
            return bannerVariants
                .stream()
                .filter(bannerVariant -> {
                    for (IntelligentDimension dimension:
                            DIRECT_ATTRIBUTE_EQUALITY_CHECK) {
                        if(userBannerExperiment.getDimensions().contains(dimension)
                                && !this.checkRashiAttribute(bannerVariant, dimension, userAttributes)) {
                            return false;
                        }
                    }
                    if (TimeUtil.isWeekday(new Date(), userContext.getUserProfile().getCity().getTimezone())) {
                        for (IntelligentDimension dimension:
                                WEEKDAY_WORKOUT_TIME_AFFINITY) {
                            if(userBannerExperiment.getDimensions().contains(dimension)
                                    && !this.checkRashiAttribute(bannerVariant, dimension, userAttributes)) {
                                return false;
                            }
                        }
                    } else {
                        for (IntelligentDimension dimension:
                                WEEKEND_WORKOUT_TIME_AFFINITY) {
                            if(userBannerExperiment.getDimensions().contains(dimension)
                                    && !this.checkRashiAttribute(bannerVariant, dimension, userAttributes)) {
                                return false;
                            }
                        }
                    }
                    if(userBannerExperiment.getDimensions().contains(IntelligentDimension.TIME_OF_DAY)
                            && !this.checkIfTimeOfDaySatisfied(userContext, bannerVariant)) {
                        return false;
                    }
                    if(userBannerExperiment.getDimensions().contains(IntelligentDimension.AGE)
                            && !this.checkIfAgeSatisfied(userContext, bannerVariant, userAttributes)) {
                        return false;
                    }
                    if(userBannerExperiment.getDimensions().contains(IntelligentDimension.GENDER)
                            && !this.checkIfGenderSatisfied(bannerVariant, userAttributes)) {
                        return false;
                    }

                    return true;
                })
                .map(bannerVariant -> this.bannerVariantToBannerItem(bannerVariant))
                .filter(bannerItem -> this.checkLiveBannerCondition(bannerItem, userContext, widgetContext))
                .limit(maxNumBanners)
                .collect(Collectors.toList());
        } else {
            return null;
        }
    }


    private BannerItem bannerVariantToBannerItem(BannerVariant bannerVariant) {
        BannerItem bannerItem = new BannerItem();
        bannerItem.setAction(bannerVariant.getAction());
        bannerItem.setActionV2(bannerVariant.getActionV2());
        bannerItem.setImage(bannerVariant.getImage());
        bannerItem.setMwebImage(bannerVariant.getMwebImage());
        bannerItem.setWebImage(bannerVariant.getWebImage());
        bannerItem.setVideoUrl(bannerVariant.getVideoUrl());
        bannerItem.setLoopVideo(bannerVariant.getLoopVideo());
        bannerItem.setMuteVideo(bannerVariant.getMuteVideo());
        bannerItem.setContentMetric(bannerVariant.getContentMetric());
        bannerItem.setLiveAllowedPlatforms(bannerVariant.getLiveAllowedPlatforms());

        var usedIntelligentDimensions = bannerVariant.getDimensionValues().stream()
                .map(dimensionValue -> dimensionValue.getDimension().toString())
                .collect(Collectors.joining(","));
        bannerItem.getContentMetric().setUsedIntelligentDimensions(usedIntelligentDimensions);
        return  bannerItem;
    }

    private boolean checkIfTimeOfDaySatisfied(UserContext userContext, BannerVariant bannerVariant) {
        var todValueToCheck = this.getDimensionValue(bannerVariant.getDimensionValues(),
                IntelligentDimension.TIME_OF_DAY);
        // If there is no condition value specified in banner variant then no need to evaluate
        if(todValueToCheck == null) {
            return true;
        }
        var timeRange = todValueToCheck.split("-");
        var startTimeHourMin = timeRange[0].split(":");
        var endTimeHourMin = timeRange[1].split(":");

        var startTime = Integer.valueOf(startTimeHourMin[0]) * 60 + Integer.valueOf(startTimeHourMin[1]);
        var endTime = Integer.valueOf(endTimeHourMin[0]) * 60 + Integer.valueOf(endTimeHourMin[1]);

        var hourMin = TimeUtil.getHourMinNow(userContext.getUserProfile().getCity().getTimezone());
        var currentTime = hourMin.getHour() * 60 + hourMin.getMin();
        if(startTime <= currentTime && currentTime < endTime) {
            return true;
        } else {
            return false;
        }
    }

    private boolean checkIfAgeSatisfied(UserContext userContext, BannerVariant bannerVariant, Map<String, Object> userAttributes) {
        var ageValueToCheck = this.getDimensionValue(bannerVariant.getDimensionValues(),
                IntelligentDimension.AGE);
        // If there is no condition value specified in banner variant then no need to evaluate
        if(ageValueToCheck == null) {
            return true;
        }
        var ageRange = ageValueToCheck.split("-");
        var birthday = (String)userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP.get(IntelligentDimension.BIRTHDAY));
        if(birthday == null) {
            if (ageRange[0].equalsIgnoreCase("default"))
                return true;
            else
                return false;
        } else {
            if (ageRange[0].equalsIgnoreCase("default"))
                return false;
            LocalDate now = TimeUtil.getDateNow(userContext.getUserProfile().getTimezone()).toLocalDate();
            LocalDate birthdayDate = TimeUtil.getDateOnlyFromString(birthday,
                    userContext.getUserProfile().getTimezone(), "yyyy-MM-dd'T'HH:mm:ss.SSSZZZZZ").toLocalDate();
            int age = TimeUtil.getYearsInBetween(birthdayDate, now);
            if(Integer.valueOf(ageRange[0]) <= Integer.valueOf(age) &&
                    Integer.valueOf(age) <= Integer.valueOf(ageRange[1])) {
                return true;
            } else {
                return false;
            }
        }
    }

    private boolean checkIfGenderSatisfied(BannerVariant bannerVariant, Map<String, Object> userAttributes) {
        var conditionValue = this.getDimensionValue(bannerVariant.getDimensionValues(), IntelligentDimension.GENDER);
        // If there is no condition value specified in banner variant then no need to evaluate
        if(conditionValue == null) {
            return true;
        }
        var rashiAttributeValue = (String)userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP.get(IntelligentDimension.GENDER));
        if (rashiAttributeValue == null) {
            rashiAttributeValue  = (String)userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP.get(IntelligentDimension.GENDER_PREDICTED));
        }
        var finalValue = rashiAttributeValue == null ? "default"  : rashiAttributeValue.toLowerCase();
        return conditionValue.equalsIgnoreCase(finalValue);
    }

    private boolean checkRashiAttribute(BannerVariant bannerVariant, IntelligentDimension dimension, Map<String, Object> userAttributes) {
        var conditionValue = this.getDimensionValue(bannerVariant.getDimensionValues(), dimension);
        // If there is no condition value specified in banner variant then no need to evaluate
        if(conditionValue == null) {
            return true;
        }
        var rashiAttributeValue = (String)userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP.get(dimension));
        var finalValue = rashiAttributeValue == null ? "default"  : rashiAttributeValue.toLowerCase();
        return conditionValue.equalsIgnoreCase(finalValue);
    }

    private boolean checkBannerValidAccordingToAffinity(BannerVariant bannerVariant, IntelligentDimension dimension,
                                                        Map<String, Object> userAttributes){
        String dimensionValue = this.getDimensionValue(bannerVariant.getDimensionValues(), dimension);
        if(dimensionValue == null || "DEFAULT".equalsIgnoreCase(dimensionValue)) {
            return true;
        } else {
            if(WORKOUT_FORMAT_AFFINITY.contains(dimension)) {
                var affinityValue = (String)userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP.get(dimension));
                if (affinityValue != null && this.getAffinityValueBasedOnArrayIndex(dimensionValue, dimension,
                        userAttributes) != null) {
                    return true;
                } else {
                    return false;
                }
            } else if(IntelligentDimension.CATEGORY_AFFINITY.equals(dimension)) {
                var affinityValue = userAttributes.get(PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY.get(ProductType.valueOf(dimensionValue)));
                if (affinityValue != null) {
                    return true;
                } else {
                    return false;
                }
            } else {
                // Not supported config
                return false;
            }
        }
    }

    private Float getAffinityValueBasedOnArrayIndex(String entityToSearch, IntelligentDimension dimension,
                                                  Map<String, Object> userAttributes) {
        var rashiValue = (String)userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP.get(dimension));
        if(rashiValue == null) {
            return null;
        }
        List<String> rashiValues = null;
        try {
            rashiValues = Serializer.deserialize(rashiValue, Lists.newArrayList().getClass());
            return rashiValues.indexOf(entityToSearch) == -1 ? null : 100F - rashiValues.indexOf(entityToSearch);
        } catch (IOException e) {
            log.error("Exception deserializing ", e);
            return null;
        }
    }

    private Float getAffinityValueForCategoryAffinity(ProductType productType, Map<String, Object> userAttributes) {
        var value = (String)userAttributes.get(PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY.get(productType));
        return value != null ? Float.valueOf(value) : null;
    }
    private int compareBannerAccordingToAffinity(BannerVariant bannerVariantA, BannerVariant bannerVariantB,
                                                 IntelligentDimension dimension, Map<String, Object> userAttributes) {
        var dimensionValueA = this.getDimensionValue(bannerVariantA.getDimensionValues(), dimension);
        var dimensionValueB = this.getDimensionValue(bannerVariantB.getDimensionValues(), dimension);
        if(dimensionValueA != null && dimensionValueB == null)
            return -1;
        if(dimensionValueA == null && dimensionValueB != null)
            return 1;
        if(dimensionValueA == null && dimensionValueB == null)
            return 0;

        Float affinityValueA = null, affinityValueB = null;
        if(WORKOUT_FORMAT_AFFINITY.contains(dimension)) {
             affinityValueA = this.getAffinityValueBasedOnArrayIndex(dimensionValueA, dimension, userAttributes);
             affinityValueB = this.getAffinityValueBasedOnArrayIndex(dimensionValueB, dimension, userAttributes);
        } else if(IntelligentDimension.CATEGORY_AFFINITY.equals(dimension)) {
             affinityValueA = this.getAffinityValueForCategoryAffinity(ProductType.valueOf(dimensionValueA), userAttributes);
             affinityValueB = this.getAffinityValueForCategoryAffinity(ProductType.valueOf(dimensionValueB), userAttributes);
        }
        if(affinityValueA != null && affinityValueB == null) {
            return -1;
        }
        if(affinityValueA == null && affinityValueB != null) {
            return 1;
        }
        if(affinityValueA != null && affinityValueB != null) {
            return affinityValueA > affinityValueB? -1 :
                    affinityValueA< affinityValueB? 1 : 0 ;
        }
        return  0;

    }
    private List<BannerVariant> sortByCategoryOrFormatAffinity(List<IntelligentDimension> dimensions, List<BannerVariant> bannerVariants,
                                                               Map<String, Object> userAttributes) {
        bannerVariants = bannerVariants.stream().filter(bannerVariant -> {
            for (IntelligentDimension dimension:
                 dimensions) {
                if(!this.checkBannerValidAccordingToAffinity(bannerVariant, dimension, userAttributes)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());

        bannerVariants.sort((bannerVariantA,bannerVariantB) -> {
            int result = 0;
            for (IntelligentDimension dimension:
                    dimensions) {
                result = compareBannerAccordingToAffinity(bannerVariantA, bannerVariantB, dimension, userAttributes);
                if(result != 0)
                    return result;

            }
            return result;
        });
        return bannerVariants;
    }

    private boolean checkLiveBannerCondition(BannerItem bannerItem, UserContext userContext, WidgetContext widgetContext) {
        Map<String, String> queryParams = widgetContext.getQueryParams();
        UserAgent ua = userContext.getSessionInfo().getUserAgent();
        String osName = userContext.getSessionInfo().getOsName();
        LivePlatform livePlatform  = UserAgent.DESKTOP.equals(ua) || UserAgent.MBROWSER.equals(ua)
                ? LivePlatform.LIVE_WEB : osName.equals("android") ? LivePlatform.LIVE_ANDROID :
                LivePlatform.LIVE_IOS;
        // If condition is specified check and return accordingly
        if(CollectionUtils.isNotEmpty(bannerItem.getLiveAllowedPlatforms())) {
            return bannerItem.getLiveAllowedPlatforms().contains(livePlatform);
        }

        // If any live page the condition is not specified treat it as invalid
        if("TRUE".equals(queryParams.get("isLivePage"))) {
            if (CollectionUtils.isEmpty(bannerItem.getLiveAllowedPlatforms())) {
                return false;
            }
        }
        return true;
    }

    private String getDimensionValue(List<DimensionValue> dimensionValues, IntelligentDimension dimension) {
        var value  = dimensionValues
                .stream()
                .filter(dimensionValue -> dimensionValue.getDimension().equals(dimension))
                .findFirst();
        return value.isPresent() ? value.get().getValue() : null;
    }

    private boolean manipulateWorkoutSessionBanner(UserContext userContext, BannerItem bannerItem,
                                                   ServiceInterfaces interfaces, int itemIndex) throws JsonProcessingException {
        if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_ZOOM_ELITE_PRO_METRIC_WIDGET_ROLL_OUT_APP_VERSION)) {
            return true;
        }

        if (TL_CORP_DETAILS.get() == null) {
            String userId = userContext.getUserProfile().getUserId();
            TL_CORP_DETAILS.set(interfaces.enterpriseClient.getCorporateDetails(userId));
        }
        if (TL_CORP_DETAILS.get() == null || TL_CORP_DETAILS.get().getMetrics() == null) {
            log.info("Either corp details or corp metrics are null");
            return true;
        }
        CorporateMetrics metrics = TL_CORP_DETAILS.get().getMetrics();
        boolean isProdOrAlpha = interfaces.getEnvironmentService().isProduction()
                || interfaces.getEnvironmentService().isAlpha();
        switch (itemIndex) {
            case 0 -> {
                int threshold = isProdOrAlpha ? EnterpriseConfig.WORKOUT_SESSION_MIN_THRESHOLD
                        : EnterpriseConfig.WORKOUT_SESSION_MIN_STAGE_THRESHOLD;
                Integer sessions = metrics.getNumberOfSessions();
                if (sessions == null || sessions < threshold) {
                    return true;
                }
                bannerItem.setTopTitle("Your colleagues attended");
                bannerItem.setDescription("workout sessions in the last 30 days");
                bannerItem.setTitle((sessions < 10) ? "0" + sessions : String.valueOf(sessions));
            }
            case 1 -> {
                int threshold = isProdOrAlpha ? EnterpriseConfig.WORKOUT_TIME_MIN_THRESHOLD
                        : EnterpriseConfig.WORKOUT_TIME_MIN_STAGE_THRESHOLD;
                Integer workoutTime = metrics.getWorkoutTimeInMinutes();
                if (workoutTime == null || workoutTime < threshold) {
                    return true;
                }
                bannerItem.setTopTitle("Your colleagues spent");
                bannerItem.setDescription("working out in the last 30 days");
                long minutes = workoutTime / 60;
                if (minutes > 60) {
                    bannerItem.setTitle(String.format("%02d", minutes / 60) + ":"
                            + String.format("%02d", minutes % 60));
                    bannerItem.setSubTitle("Hrs");
                } else {
                    bannerItem.setTitle(String.format("%02d", minutes) + ":"
                            + String.format("%02d", 0));
                    bannerItem.setSubTitle("Mins");
                }
            }
            case 2 -> {
                int threshold = isProdOrAlpha ? EnterpriseConfig.WORKOUT_KCAL_MIN_THRESHOLD
                        : EnterpriseConfig.WORKOUT_KCAL_MIN_STAGE_THRESHOLD;
                Long kiloCals = metrics.getKiloCaloriesBurnt();
                if (kiloCals == null || kiloCals < threshold) {
                    return true;
                }
                bannerItem.setTopTitle("Your colleagues burnt");
                bannerItem.setDescription("in the last 30 days");
                bannerItem.setTitle(String.valueOf(kiloCals));
                bannerItem.setSubTitle("kCal");
            }
        }
        return false;
    }

    private boolean manipulateMetricBanner(UserContext userContext, BannerItem bannerItem,
                                           ServiceInterfaces interfaces, int itemIndex) throws Exception {

        if(!AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_ZOOM_ELITE_PRO_METRIC_WIDGET_ROLL_OUT_APP_VERSION)) {
            return true;
        }

        if (TL_CORP_DETAILS.get() == null) {
            String userId = userContext.getUserProfile().getUserId();
            TL_CORP_DETAILS.set(interfaces.enterpriseClient.getCorporateDetails(userId, "7"));
        }
        if (TL_CORP_DETAILS.get() == null || TL_CORP_DETAILS.get().getAverageMetricsForDuration() == null) {
            log.info("Either corp details or corp metrics are null");
            return true;
        }
        CorporateMetrics metrics = TL_CORP_DETAILS.get().getAverageMetricsForDuration();
        boolean isProdOrAlpha = interfaces.getEnvironmentService().isProduction()
                || interfaces.getEnvironmentService().isAlpha();
        switch (itemIndex) {
            case 8 -> {
                boolean isCultLiveMembershipActive = EnterpriseUtils.verifyUserCultLiveMembership(userContext, interfaces);
                bannerItem.setStoryType(BannerStoryType.CENTER_INFO_CARD.name());
                if(!isCultLiveMembershipActive) {
                    bannerItem.setImage(null);
                    bannerItem.setVideoUrl(EnterpriseConfig.CORP_CLP_INTRO_VIDEO);
                    return false;
                }
                boolean isWeekendDay = EnterpriseUtils.isWeekendDay(userContext.getUserProfile().getTimezone());
                boolean isNeverWorkedOut = TL_CORP_DETAILS.get().isNeverWorkedOut();
                boolean isActivityInGivenDuration = TL_CORP_DETAILS.get().isActivityInGivenDuration();
                TimeOfDayType timeOfDayType = EnterpriseUtils.getTimesOfDay(userContext.getUserProfile().getTimezone());
                String userName = userContext.getUserEntryCompletableFuture().get().getFirstName();
                bannerItem.setAction(new Action(EnterpriseConfig.CULT_LIVE_CLP_MEMBER_URL, "BOOK LIVE CLASS", ActionType.NAVIGATION));
                bannerItem.setLottieUrl(EnterpriseConfig.TIME_OF_DAY_TYPE_STORY_LOTTIE.get(timeOfDayType));
                switch (timeOfDayType) {
                    case MORNING -> {
                        bannerItem.setTitle("Rise & Shine, " + userName + "!");
                        bannerItem.getAction().setTitle("OKAY, LET ME TRY!");
                        if(isWeekendDay) {
                            bannerItem.setDescription("Too busy for exercise on workdays? Let's make the weekend count.");
                            bannerItem.getAction().setTitle("BOOK LIVE CLASS");
                        } else if(isNeverWorkedOut) {
                            bannerItem.setDescription("A workout can help increase your productivity by 2X");
                        } else if(isActivityInGivenDuration) {
                            bannerItem.setDescription("You're killing it! \nFancy another awesome workout to start the day?");
                        } else {
                            bannerItem.setDescription("Feeling sluggish? Remember, every little step counts.");
                        }
                    }
                    case AFTERNOON -> {
                        bannerItem.setTitle("Busy day, " + userName + "!");
                        bannerItem.getAction().setTitle("I NEED IT BADLY");
                        if(isWeekendDay) {
                            bannerItem.setDescription("Too busy for exercise on workdays? Let's make the weekend count.");
                            bannerItem.getAction().setTitle("BOOK LIVE CLASS");
                        } else {
                            bannerItem.setDescription("Quick, desk exercises to give you a good stretch.");
                            bannerItem.getAction().setUrl(EnterpriseConfig.DESK_EXERCISES_PROGRAM_URL);
                        }
                    }
                    case EVENING -> {
                        bannerItem.setTitle("Evening, " + userName + "!");
                        if(isWeekendDay) {
                            bannerItem.setDescription("Too busy for exercise on workdays? Let's make the weekend count.");
                            bannerItem.getAction().setTitle("BOOK LIVE CLASS");
                        } else if(isNeverWorkedOut) {
                            bannerItem.setDescription("Yoga? Dance? Weights? Just stretches? Anything works!");
                            bannerItem.getAction().setTitle("SOUNDS INTERESTING!");
                        } else if(isActivityInGivenDuration) {
                            bannerItem.setDescription("You're killing it!\n How about a good workout to cap the day?");
                            bannerItem.getAction().setTitle("Yes, I'm in!");
                        } else {
                            bannerItem.setDescription("Tough day at work?\n Quick exercise to wind down maybe?");
                            bannerItem.getAction().setTitle("Yes, I'm in!");
                        }
                    }
                    case NIGHT -> {
                        bannerItem.setTitle("Good night, " + userName + "!");
                        bannerItem.setDescription("Indulge in a quick meditation session to help you relax");
                        bannerItem.getAction().setTitle("Yes, I need this!");
                        bannerItem.getAction().setUrl(EnterpriseConfig.MEDITATION_CLP_URL);
                    }
                }
            }
            case 9 -> {
                int threshold = isProdOrAlpha ? EnterpriseConfig.METRIC_AVG_WORKOUTS_MIN_THRESHOLD
                        : EnterpriseConfig.WORKOUT_SESSION_MIN_STAGE_THRESHOLD;
                Integer sessions = metrics.getNumberOfSessions();
                if (sessions == null || sessions < threshold) {
                    return true;
                }
                bannerItem.setDescription("Last week, your peers did");
                bannerItem.setTitle((sessions < 10) ? "0" + sessions : String.valueOf(sessions));
                bannerItem.setSubTitle("workouts");
                bannerItem.setMetricAvgCaption("on avg");
            }
            case 10 -> {
                int threshold = isProdOrAlpha ? EnterpriseConfig.METRIC_AVG_MINUTE_TIME_MIN_THRESHOLD
                        : EnterpriseConfig.WORKOUT_TIME_MIN_STAGE_THRESHOLD;
                Integer workoutTime = metrics.getWorkoutTimeInMinutes();
                if (workoutTime == null || workoutTime < threshold) {
                    return true;
                }
                bannerItem.setDescription("Last week, your peers trained for");
                long minutes = workoutTime / 60;
                if (minutes > 60) {
                    bannerItem.setTitle(String.format("%02d", minutes / 60) + ":"
                            + String.format("%02d", minutes % 60));
                    bannerItem.setSubTitle("hrs");
                } else {
                    bannerItem.setTitle(String.format("%02d", minutes) + ":"
                            + String.format("%02d", 0));
                    bannerItem.setSubTitle("mins");
                }
                bannerItem.setMetricAvgCaption("on avg");
            }
            case 11 -> {
                int threshold = isProdOrAlpha ? EnterpriseConfig.METRIC_AVG_KCAL_MIN_THRESHOLD
                        : EnterpriseConfig.WORKOUT_KCAL_MIN_STAGE_THRESHOLD;
                Long kiloCals = metrics.getKiloCaloriesBurnt();
                if (kiloCals == null || kiloCals < threshold) {
                    return true;
                }
                bannerItem.setDescription("Last week, your peers burnt");
                bannerItem.setTitle(String.valueOf(kiloCals));
                bannerItem.setSubTitle("kCal");
                bannerItem.setMetricAvgCaption("on avg");
            }
        }
        if (itemIndex != 8) {
            bannerItem.setMetricCTACaptionRegular("If they can, ");
            bannerItem.setMetricCTACaptionBold("you can too! \uD83D\uDCAA\uD83C\uDFFB");
            bannerItem.setAction(new Action(EnterpriseConfig.CULT_LIVE_CLP_MEMBER_URL, "BOOK LIVE CLASS", ActionType.NAVIGATION));
        }
        return false;
    }


    private boolean manipulateWellnessBanner(UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        CorporateBenefits cultBenefit = EnterpriseUtils.getWellnessBenefit(userContext, interfaces);
        if (cultBenefit == null) {
            return true;
        }
        return false;
    }

    private void manipulateCorpExploreBanner(Corporate corporate, BannerItem bannerItem, boolean isCorpActivated) {
        bannerItem.setCorpCompanyLogo(corporate.getLogoUrl());
        bannerItem.setCorpCompanyName(corporate.getName());
        bannerItem.setCorpSponsoredByCaption("Sponsored by");
        if (isCorpActivated) {
            bannerItem.setCorpImage(EnterpriseConfig.CORP_ACTIVATE_STORY_IMAGE);
            bannerItem.setCorpImageSizeRatio("280:126");
            bannerItem.setAction(new Action(ENTERPRISE_CLP_URL,"EXPLORE NOW", ActionType.NAVIGATION));
        } else {
            bannerItem.setCorpImage(EnterpriseConfig.CORP_UNLOCK_STORY_IMAGE);
            bannerItem.setCorpImageSizeRatio("280:155");
            bannerItem.setAction(new Action(ENTERPRISE_CLP_URL,"UNLOCK NOW", ActionType.NAVIGATION));
        }
    }

    private void manipulateCultLiveBanner(UserContext userContext, BannerItem bannerItem, ServiceInterfaces interfaces)
            throws Exception {
        bannerItem.setTitle("cultpass");
        bannerItem.setCultGradientTitle("LIVE");

        CorporateBenefits cultBenefit = EnterpriseUtils.getCultLiveBenefit(userContext, interfaces);
        Pair<BigDecimal, BigDecimal> cultLivePrices = EnterpriseUtils.getCultLivePrices(cultBenefit, interfaces, userContext);
        if (cultLivePrices == null) {
            log.info("No cult live benefits found for the user {}", userContext.getUserProfile().getUserId());
            return;
        }
        BigDecimal costPrice = cultLivePrices.getLeft();
        BigDecimal offerPrice = cultLivePrices.getRight();
        if (offerPrice.compareTo(costPrice) != 0) {
            bannerItem.setCultCostPrice("₹" + costPrice.longValue());
        }
        String buttonTitle;
        String actionUrl;
        if (cultBenefit.getProgramType() == ProgramType.CULT_LIVE || cultBenefit.getProgramType() == ProgramType.COUPON_BASED_CULT_LIVE) {
            bannerItem.setDescription("Access your FREE membership");
            buttonTitle = EnterpriseConfig.BUTTON_TITLE_BOOK_CLASS;
            actionUrl = EnterpriseConfig.CULT_LIVE_CLP_MEMBER_URL;
        } else {
            String offerId = cultBenefit.getDetails().getOfferId();
            Pair<Integer, Integer> consumptions = EnterpriseUtils.getUserConsumptions(userContext, interfaces, offerId);
            if (consumptions != null && consumptions.getRight() > 0) {
                bannerItem.setDescription("Access your FREE membership");
                buttonTitle = EnterpriseConfig.BUTTON_TITLE_BOOK_CLASS;
                actionUrl = EnterpriseConfig.CULT_LIVE_CLP_MEMBER_URL;
            } else {
                bannerItem.setDescription("Memberships starting at ");
                bannerItem.setCultOfferPrice("₹" + offerPrice.longValue());
                buttonTitle = EnterpriseConfig.BUTTON_TITLE_BUY_NOW;
                actionUrl = EnterpriseConfig.CULT_LIVE_SKU_PURCHASE_URL;
            }
        }
        Action action = new Action(actionUrl, buttonTitle, ActionType.NAVIGATION);
        action.setVariant(EnterpriseConfig.ACTION_VARIANT_PRIMARY);
        footerActions = Collections.singletonList(action);
    }

    protected List<BannerItem> buildBannerItem(
            UserContext userContext, BannerItem banner, ServiceInterfaces interfaces, WidgetContext widgetContext
    ) throws Exception {
        Map<String, String> queryParams = widgetContext.getQueryParams();
        if (banner == null)
            return null;
        boolean isDebugEnabled = AppUtil.isDebugEnabled(queryParams);
        // Checks the banner eligibility
        Date displayTime = new Date();
        TimeZone timeZone = TimeZone.getTimeZone(ZoneId.systemDefault());
        if (queryParams.get("displayTime") != null) {
            displayTime = ObjectUtils.defaultIfNull(TimeUtil.parseDateTime(queryParams.get("displayTime"), timeZone, null), displayTime);
        }
        if (!banner.validateBaseDataItemCondition(displayTime)) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(banner.getSegmentIds())) {
            Segment segment = interfaces.segmentEvaluatorService.checkCondition(banner.getSegmentIds(), userContext, isDebugEnabled).get();
            if (isDebugEnabled) {
                log.info("BaseBannerWidget:buildBannerItem:: userId:{}, bannerId:{}, segmentIds:{}, evaluated to :{}",
                        userContext.getUserProfile().getUserId(), banner.getBannerIdentifier(), banner.getSegmentIds(), segment != null);
            }
            if (segment == null) {
                return  null;
            }
        }
        if(!this.checkLiveBannerCondition(banner, userContext, widgetContext)) {
            return null;
        }

        if (banner.getTitle() != null && !banner.getTitle().equals("")) {
            boolean hasPerMonthPriceTag = banner.getTitle().contains(PER_MONTH_PRICE_VARIABLE);
            if (hasPerMonthPriceTag && banner.getSkuProductType() != null) {
                String perMonthPrice = getStartingPerMonthPrice(userContext, interfaces, widgetContext, banner.getSkuProductType());
                if (perMonthPrice != null) {
                    banner.setTitle(banner.getTitle().replaceAll(PER_MONTH_PRICE_VARIABLE_REGEX, "" + OrderUtil.RUPEE_SYMBOL + perMonthPrice));
                }
                // TODO for discount
            }
        }

        banner.setMeta(populateLottieDelegates(banner.getMeta(), interfaces, userContext));

        // Check if any personalized banner available, if not uses the default config
        List<BannerItem> bannerItems = null;
        if(banner.getBannerExperimentId() != null) {
            bannerItems = this.getPersonalizedBanners(userContext, interfaces, banner.getBannerExperimentId(),
                    banner.getMaxNumberOfPersonalizedBanners(),
                    banner.getBannerExperiments(),
                    widgetContext);
        }
        if(CollectionUtils.isEmpty(bannerItems)) {
            BannerItem bannerItem = interfaces.bannerBuilder.buildBanner(banner, userContext, widgetContext);
            if (bannerItem != null) {
                bannerItems = Collections.singletonList(bannerItem);
            } else {
                return null;
            }
        }
        for (BannerItem bannerItem : bannerItems) {
            this.manipulateBannerAction(userContext, bannerItem, interfaces);
            this.manipulateBannerTimer(userContext, bannerItem, interfaces);
            this.manipulateBannerImage(userContext, bannerItem);
        }
        return bannerItems;
    }

    public List<BannerItem> dedupeBannerItems(List<BannerItem> bannerItems, boolean shuffle) {
        bannerItems.sort((b1, b2) -> {
            if (b1.getPriority().equals(b2.getPriority())) {
                return b1.getDedupeId().compareTo(b2.getDedupeId());
            }
            return b1.getPriority().compareTo(b2.getPriority());
        });
        List<BannerItem> finalBannerItems = new ArrayList<>();
        for (int i = 0; i < bannerItems.size(); i++) {
            List<BannerItem> sameDedupeIdBannerItems = new ArrayList<>(List.of(bannerItems.get(i)));
            int j;
            for (j = i+1; j < bannerItems.size()
                    && bannerItems.get(i).getDedupeId() != null
                    && bannerItems.get(j).getDedupeId() != null
                    && StringUtils.equals(bannerItems.get(i).getDedupeId(), bannerItems.get(j).getDedupeId())
                    && bannerItems.get(i).getPriority().equals(bannerItems.get(j).getPriority()); j++) {
                sameDedupeIdBannerItems.add(bannerItems.get(j));
            }
            int dedupeLimit = sameDedupeIdBannerItems.get(0).getDedupeLimit() == null ? 1 : sameDedupeIdBannerItems.get(0).getDedupeLimit();
            if (BooleanUtils.isTrue(shuffle)) {
                Collections.shuffle(sameDedupeIdBannerItems);
            }
            finalBannerItems.addAll(sameDedupeIdBannerItems.subList(0, Math.min(dedupeLimit, sameDedupeIdBannerItems.size())));
            for ( ; j < bannerItems.size()
                    && bannerItems.get(i).getDedupeId() != null
                    && bannerItems.get(j).getDedupeId() != null
                    && StringUtils.equals(bannerItems.get(i).getDedupeId(), bannerItems.get(j).getDedupeId()); j++) {
                sameDedupeIdBannerItems.add(bannerItems.get(j));
            }
            i += sameDedupeIdBannerItems.size() - 1;
        }
        return finalBannerItems;
    }

    public void fillDefaultPriorityAndDedupeId(List<BannerItem> bannerItems) {
        for(int i = 0; i < bannerItems.size(); i++) {
            var bannerItem = bannerItems.get(i);
            if (StringUtils.isEmpty(bannerItem.getDedupeId()) && bannerItem.getContentMetric() != null) {
                bannerItem.setDedupeId(bannerItem.getContentMetric().getContentId());
            }
            if (bannerItem.getPriority() == null) {
                bannerItem.setPriority(Integer.MAX_VALUE - 10000 + i);
            }
        }
    }
    private Map<String, Object> populateLottieDelegates(Map<String, Object> meta, ServiceInterfaces serviceInterfaces, UserContext userContext) {
        try {
            if(Objects.isNull(meta) || meta.isEmpty() || !meta.containsKey(AppUtil.BANNER_LOTTIE_DELEGATES_KEY) || !(meta.get(AppUtil.BANNER_LOTTIE_DELEGATES_KEY) instanceof List)) return meta;
            ObjectMapper objectMapper = new ObjectMapper();

            String jsonString = objectMapper.writeValueAsString(meta.get(AppUtil.BANNER_LOTTIE_DELEGATES_KEY));
            List<LottieText> delegates = objectMapper.readValue(jsonString, new TypeReference<List<LottieText>>() {});

            List<LottieText> populatedDelegates = delegates.stream().map(item -> {
                String property = item.getFinalText();
                String rashiValue = null;
                if (Objects.nonNull(property) && property.startsWith("{{") && property.endsWith("}}")) {
                    property = property.substring(2, property.length() - 2);
                     rashiValue = fetchRashiValue(property, serviceInterfaces, userContext);
                }
                return new LottieText(item.getOriginalText(), rashiValue != null ? rashiValue : item.getFinalText());
            }).toList();

            meta.put(AppUtil.BANNER_LOTTIE_DELEGATES_KEY, populatedDelegates);
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
            log.error("Error populating lottie delegates: {}", e.getMessage());
        }
        return meta;
    }

    private String fetchRashiValue(String prop, ServiceInterfaces serviceInterfaces, UserContext userContext) {
        try {
            UserAttributesResponse userAttributesResponse = serviceInterfaces.userAttributesCacheClient.getAttributes(Long.valueOf(userContext.getUserProfile().getUserId()), List.of(prop), AppTenant.CUREFIT);
            if (userAttributesResponse != null && userAttributesResponse.getAttributes() != null && userAttributesResponse.getAttributes().containsKey(prop)) {
                return userAttributesResponse.getAttributes().get(prop).toString();
            }
        } catch (Exception e){
            serviceInterfaces.exceptionReportingService.reportException(e);
        }
        return null;
    }

}
