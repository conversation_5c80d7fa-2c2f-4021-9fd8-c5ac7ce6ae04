package com.curefit.cfapi.widgets.hometab.upcoming;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.MdcTag;
import com.curefit.cfapi.model.internal.vm.TechOwner;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.items.CardListContentType;
import com.curefit.cfapi.pojo.vm.items.UpcomingItem;
import com.curefit.cfapi.pojo.vm.items.UpcomingItemMetaInfo;
import com.curefit.cfapi.service.EnterpriseUtils;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.appConfig.EnterpriseConfig;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.widgets.enterprise.EnterpriseInteractiveClassItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import fit.cult.enterprise.dto.corporate.CorporateDetails;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EnterpriseUpcomingBuilder {

    public List<UpcomingItem> getEnterpriseUpcomingClasses(ServiceInterfaces interfaces, UserContext context, CardListContentType cardListContentType) {

        if(!AppUtil.isEnterpriseFeatureSupported(context, AppUtil.ENTERPRISE_ZOOM_ELITE_PRO_METRIC_WIDGET_ROLL_OUT_APP_VERSION)) {
            return Collections.emptyList();
        }

        boolean isMeetingSegmentUser = AppUtil.doesUserBelongsToMeetingUsersSegment(interfaces, context);
        if(!isMeetingSegmentUser) {
            return Collections.emptyList();
        }

        if (cardListContentType == CardListContentType.TRAY || cardListContentType == CardListContentType.UPCOMING_TRAY) {
            EnterpriseInteractiveClassItem item = EnterpriseUtils.getUserLatestZoomMeeting(context, interfaces, true);
            if (item != null) {
                List<UpcomingItem> upcomingItems = new ArrayList<>();
                String itemDate = Instant.ofEpochSecond(item.getEpochStartTime().longValue()).atZone(ZoneId.systemDefault()).toLocalDate().toString();
                UpcomingItem upcomingItem = new UpcomingItem();
                UpcomingItemMetaInfo metaInfo = new UpcomingItemMetaInfo();
                metaInfo.setTitle(item.getTitle());
                metaInfo.setCenterName("Zoom Session");
                metaInfo.setTenant("enterprise");
                upcomingItem.setUpcomingItemMetaInfo(metaInfo);
                upcomingItem.setTitle(item.getTitle());
                upcomingItem.setSubTitle(item.getDescription());
                upcomingItem.setAction(item.getRightAction());
                upcomingItem.setDate(itemDate);
//                upcomingItem.setItemTitle(item.getTitle());
                upcomingItem.setTimestamp(item.getEpochStartTime().longValue() * 1000L);

                String timezone = context.getUserProfile().getCity().getTimezone();
                Timestamp ts = new Timestamp(item.getEpochStartTime().longValue() * 1000L);
                Date date = new Date(ts.getTime());
                String startTime;
                if (cardListContentType == CardListContentType.UPCOMING_TRAY) {
                    startTime = TimeUtil.formatDateInTimezone(TimeZone.getTimeZone(timezone), date, "h:mm a");
                } else {
                    startTime = TimeUtil.formatDateInTimezone(TimeZone.getTimeZone(timezone), date, "hh:mm aa, EEE, d MMM");
                }
                upcomingItem.setSubTitle(startTime.toUpperCase() + " | Zoom Session");

                if (upcomingItem.getAction() == null) {
                    boolean isProdOrAlpha = interfaces.getEnvironmentService().isProduction()
                            || interfaces.getEnvironmentService().isAlpha();
                    Action action = new Action(isProdOrAlpha ? EnterpriseConfig.ENTERPRISE_CLP_ZOOM_SESSION_WIDGET_URL_PROD : EnterpriseConfig.ENTERPRISE_CLP_ZOOM_SESSION_WIDGET_URL_STAGE, "", ActionType.NAVIGATION);
                    action.setIconUrl("/image/upcoming-notification/video.png");
                    upcomingItem.setAction(action);
                } else {
                    upcomingItem.getAction().setIconUrl("/image/upcoming-notification/video.png");
                }
                upcomingItems.add(upcomingItem);
                return upcomingItems;
            }
        }
        return Collections.emptyList();
    }
}

