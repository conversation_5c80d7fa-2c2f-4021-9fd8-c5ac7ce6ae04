package com.curefit.cfapi.widgets.hometab.upcoming;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.manageoptions.ManageOptionPayload;
import com.curefit.cfapi.model.internal.manageoptions.ManageOptions;
import com.curefit.cfapi.model.internal.meta.CultManageOptionsMeta;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionIcon;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.items.CardListContentType;
import com.curefit.cfapi.pojo.vm.items.UpcomingItem;
import com.curefit.cfapi.pojo.vm.items.UpcomingItemMetaInfo;
import com.curefit.cfapi.service.ProductService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.ActionUtil;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.CultUtil;
import com.curefit.cfapi.util.FutureUtil;
import com.curefit.cfapi.util.TimelineHelper;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.client.cultApi.CultWorkout;
import com.curefit.cult.enums.BookingLabel;
import com.curefit.cult.enums.WatilistBookingState;
import com.curefit.cult.models.BulkBookingResponse;
import com.curefit.cult.models.CultBooking;
import com.curefit.hercules.pojo.SimpleWod;
import com.curefit.logging.models.ActivityState;
import com.curefit.product.enums.ProductType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
public class CultUpcomingBuilderV2 {
    public List<UpcomingItem> getCultUpcomingClasses(ServiceInterfaces interfaces, UserContext userContext, CardListContentType cardListContentType, LocalDate startDate, LocalDate endDate, boolean removeCacheKey) {
        try {
            Map<String, BulkBookingResponse> bulkBookings = getUserBookings(interfaces, userContext);
            if (MapUtils.isEmpty(bulkBookings)) {
                return Collections.emptyList();
            }
            return this.buildUpcomingItemFromBookings(interfaces, userContext, cardListContentType, bulkBookings);
        } catch (Exception e) {
            log.error("Unable to build cult widgets for timeline. User ID : " + userContext.getUserProfile().getUserId(), e);
            return Collections.emptyList();
        }
    }

    public Map<String, BulkBookingResponse> getUserBookings(ServiceInterfaces interfaces, UserContext userContext) throws Exception {
        Map<String, BulkBookingResponse> bookingResponseMap =  (Map<String, BulkBookingResponse>)userContext.getRequestCache().getRequestFuture(RequestType.GET_CULT_UPCOMING_BOOKINGS, userContext).get();
        return bookingResponseMap;
    }

    private List<UpcomingItem> buildUpcomingItemFromBookings(ServiceInterfaces interfaces, UserContext userContext, CardListContentType cardListContentType, Map<String, BulkBookingResponse> bulkBookings) throws ExecutionException, InterruptedException {
        userContext.getRequestCache().getRequestFuture(RequestType.CULT_UNBOUND_WORKOUT_IDS, userContext);
        String userId = userContext.getUserProfile().getUserId();
        List<UpcomingItem> upcomingItems = new ArrayList<>();
        BulkBookingResponse bulkBookingResponse = bulkBookings.get(userId);
        List<CultBooking> bookings = bulkBookingResponse.getBookings();
        List<CultBooking> waitlists = bulkBookingResponse.getWaitlists();

        if (CollectionUtils.isNotEmpty(bookings)) {
            List<CompletableFuture<UpcomingItem>> upcomingItemPromises = new ArrayList<>();

            for (CultBooking booking : bookings) {
                upcomingItemPromises.add(CompletableFuture.supplyAsync(() -> {
                    try {
                        return this.bookingToUpcomingItem(interfaces, userContext, cardListContentType, booking);
                    } catch (BaseException | ExecutionException | InterruptedException e) {
                        e.printStackTrace();
                    }
                    return null;
                }, interfaces.getTaskExecutor()));
            }
            CompletableFuture<List<UpcomingItem>> upcomingItemsPromise = FutureUtil.allOf(upcomingItemPromises);
            upcomingItems = upcomingItemsPromise.get().stream().filter(Objects::nonNull).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(waitlists)) {
            List<CompletableFuture<UpcomingItem>> upcomingWaitlistItemPromises = new ArrayList<>();
            for (CultBooking booking : waitlists) {
                upcomingWaitlistItemPromises.add(CompletableFuture.supplyAsync(() -> {
                    try {
                        return this.bookingToUpcomingItem(interfaces, userContext, cardListContentType, booking);
                    } catch (BaseException | ExecutionException | InterruptedException e) {
                        e.printStackTrace();
                    }
                    return null;
                }, interfaces.getTaskExecutor()));
            }
            CompletableFuture<List<UpcomingItem>> upcomingWaitlistItemsPromise = FutureUtil.allOf(upcomingWaitlistItemPromises);
            upcomingItems.addAll(upcomingWaitlistItemsPromise.get().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        }

        return upcomingItems.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private UpcomingItem bookingToUpcomingItem(ServiceInterfaces interfaces, UserContext userContext, CardListContentType cardListContentType, CultBooking booking) throws BaseException, ExecutionException, InterruptedException {
        String timezone = userContext.getUserProfile().getCity().getTimezone();
        ActivityState status = TimelineHelper.getCultStatus(booking);
        boolean isDroppedOutBooking = BookingLabel.DROPPED_OUT.equals(booking.getLabel());
        boolean isAppNewWaitlistColorCodingSupported = AppUtil.isAppNewWaitlistColorCodingSupported(userContext, interfaces.segmentEvaluatorService, interfaces.environmentService);
        if (status.equals(ActivityState.TODO) || booking.getState().equals(WatilistBookingState.PENDING) || booking.getState().equals(WatilistBookingState.REJECTED) || isDroppedOutBooking) {
            CultWorkout workout = interfaces.getCatalogueService().getCultWorkout(booking.getCultClass().getWorkoutID().toString());
           String focusArea = "";
            UpcomingItem upcomingItem = new UpcomingItem();
            try {
                if(StringUtils.isBlank(booking.getCultClass().getWodId())){
                    log.info("nullWodIdForCultClass::bookingToUpcomingItem:: fetch focus area, wodId is null for classId: {}", booking.getCultClass().getId());
                } else {
                    SimpleWod wod = interfaces.simpleWodCache.getSimpleWodById(booking.getCultClass().getWodId()).get();
                    if (wod != null && wod.getFocus() != null) {
                        focusArea = wod.getFocus();
                    }
                }
            } catch (Exception e) {
                interfaces.exceptionReportingService.reportException(e);
            }
            upcomingItem.setWorkoutID(booking.getCultClass().getWorkoutID());
            if (cardListContentType == CardListContentType.UPCOMING_TRAY) {
                String workoutName = workout.getName() + " " + focusArea;
                upcomingItem.setCollapsedTitle(TimelineHelper.buildCultTitle(userContext, cardListContentType, booking, workoutName));
                upcomingItem.setTitle(workoutName);
                UpcomingItemMetaInfo upcomingItemMetaInfo = new UpcomingItemMetaInfo();
                upcomingItemMetaInfo.setTitle(workoutName);
                upcomingItemMetaInfo.setCenterName(booking.getCenter().getName());
                upcomingItemMetaInfo.setTenant("cult");
                upcomingItemMetaInfo.setStartTime(booking.getCultClass().getStartTime());
                upcomingItemMetaInfo.setEndTime(booking.getCultClass().getEndTime());
                upcomingItemMetaInfo.setClassId(String.valueOf(booking.getCultClass().getId()));
                upcomingItem.setUpcomingItemMetaInfo(upcomingItemMetaInfo);
            } else {
                upcomingItem.setTitle(TimelineHelper.buildCultTitle(userContext, cardListContentType, booking, workout.getName() + " " + focusArea));
            }

            upcomingItem.setSubTitle(TimelineHelper.buildCultSubTitle(userContext, cardListContentType, booking));
            Action bookingDetailAction = null;
            if (!isDroppedOutBooking) {
                bookingDetailAction = buildBookingDetailAction(booking);
                upcomingItem.setAction(bookingDetailAction);
            }
            upcomingItem.setDate(booking.get_Class().getDate());
            upcomingItem.setTimestamp(TimelineHelper.getClassStartDate(userContext, booking).atZone(ZoneId.of(timezone)).toInstant().toEpochMilli());
            upcomingItem.setMeta(null);

            // to add waitlist no. title
            if (booking.getState().equals(WatilistBookingState.PENDING)) {
                upcomingItem.setAdditionalTitle("WAITLIST #" + booking.getWaitlistNumber().toString());
                if (booking.getWlConfirmationThreshold() != null) {
                    if (isAppNewWaitlistColorCodingSupported) {
                        if (booking.getWaitlistNumber() < booking.getWlConfirmationThreshold()) {
                            upcomingItem.setAdditionalTitleColor("#FFFFFF");
                        } else {
                            upcomingItem.setAdditionalTitleColor("#FFB876");
                        }
                    } else {
                        if (booking.getWaitlistNumber() < booking.getWlConfirmationThreshold()) {
                            upcomingItem.setAdditionalTitleColor("#F7C744");
                        } else {
                            upcomingItem.setAdditionalTitleColor("#FF6B74");
                        }
                    }
                }
            } else if (booking.getState().equals(WatilistBookingState.REJECTED)) {
                upcomingItem.setAdditionalTitle("WAITLIST REJECTED");
                if (isAppNewWaitlistColorCodingSupported) {
                    upcomingItem.setAdditionalTitleColor("#FFB876");
                } else {
                    upcomingItem.setAdditionalTitleColor("#FF6B74");
                }
            }

            List<ManageOptionPayload> enabledOptions = getCultManageOptions(userContext, ProductType.FITNESS, booking, interfaces.getProductService(), interfaces);

            if (CollectionUtils.isNotEmpty(enabledOptions) && cardListContentType == CardListContentType.UPCOMING_TRAY) {
                Action shareAction = getCultAction(userContext, booking, "SHARE", workout, interfaces, enabledOptions);
                Action cancelAction = getCultAction(userContext, booking, "CANCEL", workout, interfaces, enabledOptions);
                Action dropoutAction = getCultAction(userContext, booking, "DROPOUT", workout, interfaces, enabledOptions);
                List<Action> allActions = new ArrayList<>();

                if (shareAction != null) {
                    shareAction.setTitle("INVITE");
                    shareAction.setVariant("tertiary");
                    shareAction.setIcon(ActionIcon.SHARE_2);
                    upcomingItem.setPrimaryAction(shareAction);

                    Action inviteAction = shareAction.clone();
                    inviteAction.setTitle("Invite Friends");
                    allActions.add(inviteAction);
                }
                List<String> cultUnboundWorkoutIds = (List<String>) userContext.getRequestCache().getRequestFuture(RequestType.CULT_UNBOUND_WORKOUT_IDS, userContext).get();
                boolean isCultUnboundBooking = booking.getCultClass() != null && booking.getCultClass().getWorkoutID() != null && cultUnboundWorkoutIds.contains(booking.getCultClass().getWorkoutID().toString());
                boolean isPilatesBooking = booking.getCultClass() != null && booking.getCultClass().getWorkoutID() != null && Constants.CULT_PILATES_WORKOUT_IDS.contains(booking.getCultClass().getWorkoutID());
                if (cancelAction != null && !cancelAction.getDisabled() && !isCultUnboundBooking) {
                    String cancelClassTitleInMoreOptions;
                    if (StringUtils.isNotEmpty(booking.getWlBookingNumber())) {
                        cancelAction.setTitle("LEAVE WAITLIST");
                        cancelClassTitleInMoreOptions = "Leave waitlist";
                    } else {
                        cancelAction.setTitle("CANCEL");
                        cancelClassTitleInMoreOptions = "Cancel Class";
                    }
                    cancelAction.setVariant("tertiary");
                    cancelAction.setIcon(ActionIcon.NO_SHOW);
                    upcomingItem.setSecondaryAction(cancelAction);

                    Action cancelClassInMoreOptions = cancelAction.clone();
                    cancelClassInMoreOptions.setTitle(cancelClassTitleInMoreOptions);
                    allActions.add(cancelClassInMoreOptions);
                } else if (dropoutAction != null && !isPilatesBooking) {
                    if (isMockDropoutEnabled(booking) && cancelAction != null) {
                        log.info("dropoutBooking in mock");
                        cancelAction.setTitle("DROPOUT");
                        cancelAction.setVariant("tertiary");
                        cancelAction.setActionType(ActionType.CANCEL_CULT_CLASS);
                        cancelAction.setIcon(ActionIcon.NO_SHOW);
                        CultManageOptionsMeta cultManageOptionsMeta = new CultManageOptionsMeta();
                        if (booking.getCultClass() != null) {
                            cultManageOptionsMeta.setClassId(booking.getCultClass().getId().toString());
                        }
                        cultManageOptionsMeta.setProductType(ProductType.FITNESS);
                        cultManageOptionsMeta.setBookingNumber(booking.getBookingNumber());
                        cultManageOptionsMeta.setCancelWithoutAlert(true);
                        cancelAction.setMeta(cultManageOptionsMeta);
                        upcomingItem.setSecondaryAction(cancelAction);

                        Action classCancelAction = cancelAction.clone();
                        classCancelAction.setTitle("Dropout");
                        allActions.add(classCancelAction);

                    } else {
                        dropoutAction.setTitle("DROPOUT");
                        dropoutAction.setVariant("tertiary");
                        dropoutAction.setIcon(ActionIcon.NO_SHOW);
                        upcomingItem.setSecondaryAction(dropoutAction);

                        Action classDropoutAction = dropoutAction.clone();
                        classDropoutAction.setTitle("Dropout");
                        allActions.add(classDropoutAction);
                    }
                }
                if (isIVRApplicable(userContext, booking)) {
                    Action ivrAction = getCultAction(userContext, booking, "IVR", workout, interfaces, enabledOptions);
                    allActions.add(ivrAction);
                }
                if (bookingDetailAction != null) {
                    Action viewDetailAction = bookingDetailAction.clone();
                    viewDetailAction.setTitle("View Details");
                    viewDetailAction.setIcon(ActionIcon.CHEVRON_RIGHT);
                    allActions.add(viewDetailAction);
                }
                upcomingItem.setMoreActions(allActions);
            }
            return upcomingItem;
        }
        return null;
    }

    private Action buildBookingDetailAction(CultBooking booking) {
        Action action;
        if (booking.getState().equals(WatilistBookingState.REJECTED)) {
            action = new Action();
            action.setActionType(ActionType.SHOW_ALERT_MODAL);
            action.setIconUrl("/image/upcoming-notification/waitlist_not_confirmed.png");

            Action metaAction = new Action();
            metaAction.setTitle("");
            metaAction.setSubTitle("Waitlist was not confirmed due to unavailability of open slots.");
            metaAction.setActions(Collections.singletonList(new Action(null, "Okay", ActionType.HIDE_ALERT_MODAL)));
            action.setMeta(metaAction);
        } else if (booking.getLabel() != null && booking.getLabel().equals(BookingLabel.DROPPED_OUT)) {
            action = new Action();
            action.setIconUrl("/image/upcoming-notification/waitlist_not_confirmed.png");
        } else {
            List<NameValuePair> params = Collections.singletonList(new BasicNameValuePair("bookingNumber", StringUtils.isNotEmpty(booking.getBookingNumber()) ? booking.getBookingNumber() : booking.getWlBookingNumber()));
            action = ActionUtil.cultMindClassAction(ProductType.FITNESS, params);

            if (StringUtils.isNotEmpty(booking.getWlBookingNumber()) && booking.getState().equals(WatilistBookingState.PENDING)) {
                action.setIconUrl("/image/upcoming-notification/filled_time.png");
            } else if (booking.getState().equals(WatilistBookingState.CANCELLED) || booking.getState().equals(WatilistBookingState.DROPPED_OUT)) {
                action.setIconUrl("/image/upcoming-notification/waitlist_not_confirmed.png");
            } else {
                action.setIconUrl("/image/upcoming-notification/schedule_confirm.png");
            }
        }
        return action;
    }

    private List<ManageOptionPayload> getCultManageOptions(UserContext userContext, ProductType productType, CultBooking booking, ProductService productService, ServiceInterfaces interfaces) {
        ManageOptions cultManageOptions;
        if (StringUtils.isNotEmpty(booking.getWlBookingNumber())) {
            cultManageOptions = productService.getCultWaitlistManageOptions(productType, booking);
        } else {
            cultManageOptions = productService.getCultManageOptions(userContext, productType, booking, interfaces, null);
        }
        return cultManageOptions.getOptions().stream().filter(manageOptionPayload ->
                manageOptionPayload.isEnabled() || manageOptionPayload.isShowDisabled()).collect(Collectors.toList());
    }

    private boolean isIVRApplicable(UserContext userContext, CultBooking booking) {
        return AppUtil.isCallReminderSupported(userContext) && booking.getIvrConfig() != null && BooleanUtils.isNotFalse(booking.getIvrConfig().getIsIVRApplicable()) &&
                !booking.getState().equals(WatilistBookingState.REJECTED) && !booking.getState().equals(WatilistBookingState.CANCELLED);
    }

    private Action getCultAction(UserContext userContext, CultBooking booking, String actionType, CultWorkout workout, ServiceInterfaces interfaces, List<ManageOptionPayload> enabledOptions) {
        Action action = null;
        switch (actionType) {
            case "IVR":
                List<NameValuePair> params = Collections.singletonList(new BasicNameValuePair("bookingNumber", StringUtils.isNotEmpty(booking.getBookingNumber()) ? booking.getBookingNumber() : booking.getWlBookingNumber()));
                action = ActionUtil.cultMindClassAction(ProductType.FITNESS, params);
                break;
            case "SHARE":
                action = CultUtil.getInviteBuddyLazyLoadAction(userContext, ProductType.FITNESS, booking, workout);
                break;
            case "CANCEL":
                action = ActionUtil.cancelClassAction(enabledOptions, booking, userContext, interfaces, null);
                break;
            case "DROPOUT":
                Action dropoutAction = ActionUtil.classDropoutAction(enabledOptions);
                if (dropoutAction != null) {
                    action = dropoutAction;
                }
                break;
        }
        return action;
    }

    private boolean isMockDropoutEnabled(CultBooking cultBooking) {

        String givenDateTimeStr = cultBooking.getCultClass().getDate() + " " + cultBooking.getCultClass().getStartTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime classDateTime = LocalDateTime.parse(givenDateTimeStr, formatter);

        // Get current date and time
        LocalDateTime currentDateTime = LocalDateTime.now();

        // Calculate time difference
        long minutesDifference = ChronoUnit.MINUTES.between(currentDateTime, classDateTime);

        log.debug("dropoutBooking {} minutesDifference {}", cultBooking, minutesDifference);
        return (cultBooking.getCultClass().getCenter().getCityID() == 3 && minutesDifference > 30 && minutesDifference <= 60);
    }

}
