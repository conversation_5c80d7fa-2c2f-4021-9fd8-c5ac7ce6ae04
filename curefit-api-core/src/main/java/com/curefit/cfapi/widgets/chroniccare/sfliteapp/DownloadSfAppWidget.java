package com.curefit.cfapi.widgets.chroniccare.sfliteapp;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import static com.curefit.cfapi.pojo.vm.widget.WidgetType.DOWNLOAD_SF_APP_WIDGET;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DownloadSfAppWidget  extends BaseWidgetNonVM {
    String bannerImgUrl;
    Action downloadAppAction;
    public DownloadSfAppWidget() {
        super(DOWNLOAD_SF_APP_WIDGET);
    }
}
