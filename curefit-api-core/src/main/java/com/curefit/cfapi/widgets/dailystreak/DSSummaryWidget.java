package com.curefit.cfapi.widgets.dailystreak;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.constants.AppDeeplink;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.StreakGradients;
import com.curefit.cfapi.util.UserStreakUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.uas.responses.StreakResponse;
import lombok.*;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DSSummaryWidget extends BaseWidget {
    CFTextData message;
    boolean showDSSummaryWidget;
    int currentStreakCount;
    CFTextData streakCountText;
    String imageUrl;
    List<String> gradientColors;
    Action action;
    Integer bestStreakCount;
    String icon;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext,
                                      WidgetContext widgetContext) throws Exception {
        setWidgetType(WidgetType.DS_HOME_PAGE_SUMMARY_WIDGET);
        setShowDSSummaryWidget(false);
        try {
            Object response = userContext.getRequestCache().getMexRequestFuture(RequestType.USER_STREAK_DETAILS, userContext).get();
            StreakResponse userStreakData = null;
            if(response instanceof StreakResponse) userStreakData = (StreakResponse) response;

            if (UserStreakUtil.showUserStreakSummaryWidget(interfaces, userContext, userStreakData) != Boolean.TRUE) {
                return null;
            }

            setMessage(CFTextData.builder()
                    .text("No rest days left. Workout today to keep your streak going!")
                    .color("#FFFFFF")
                    .typeScale("P5")
                    .maxLine("3")
                    .opacity(0.8)
                    .build());
            setShowDSSummaryWidget(true);
            setGradientColors(StreakGradients.HOME_PAGE_WARNING);
            setIcon("image/daily-streak/fire.png");
            setImageUrl(getCuroImage());
            setCurrentStreakCount(userStreakData.getCurrentStreakCount());
            setBestStreakCount(userStreakData.getBestStreakCount());
            setAction(Action.builder().actionType(ActionType.NAVIGATION).url(AppDeeplink.USER_STREAK_MAIN_PAGE.getDeeplinkString()).build());
            setStreakCountText(CFTextData.builder()
                    .text("[H2,#FFFFFF,"+ (userStreakData.getCurrentStreakCount()) +"] [P2,#FFFFFF,day] [P2,#FFFFFF,streak]")
                    .color("#FFFFFF")
                    .build());
            return Collections.singletonList(this);
        } catch (Exception e){
            String message = String.format("Failed to get DSSummaryWidget for user: %s :: %s", userContext.getUserProfile().getUserId(), e.getMessage());
            interfaces.exceptionReportingService.reportException(message, e);
            return null;
        }
    }

    private String getCuroImage(){
        List<String> expandedWidgetImageSet = List.of(
                "/image/daily-streak-v0/expanded_hs_rest_0_1.png",
                "/image/daily-streak-v0/expanded_hs_rest_0_2.png"
        );
        LocalDate date = LocalDate.now();
        int currentDay = date.getDayOfWeek().getValue() - 1;
        int index = currentDay % expandedWidgetImageSet.size();
        return expandedWidgetImageSet.get(index);
    }

}
