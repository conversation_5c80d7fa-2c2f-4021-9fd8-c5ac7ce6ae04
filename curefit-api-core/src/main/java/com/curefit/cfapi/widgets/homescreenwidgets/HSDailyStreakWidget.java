package com.curefit.cfapi.widgets.homescreenwidgets;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.constants.AppDeeplink;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.service.HomePageWidget.HomeScreenWidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.widgets.base.HomeScreenBaseWidget;
import com.curefit.uas.responses.StreakResponse;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
@Setter
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HSDailyStreakWidget extends HomeScreenBaseWidget {
    String message;
    Integer currentStreakCount;
    String streakCountTitle;
    String deeplink;

    @Override
    public List<HomeScreenBaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        setHomeScreenWidgetType(HomeScreenWidgetType.DAILY_STREAK_WIDGET);
        try {
            Object response = userContext.getRequestCache().getMexRequestFuture(RequestType.USER_STREAK_DETAILS, userContext).get();
            StreakResponse userStreakData = null;
            if(response instanceof StreakResponse) userStreakData = (StreakResponse) response;
            if(Objects.isNull(userStreakData)) return null;

            Long streakRepairExpiryEpoch = UserStreakUtil.streakRepairExpiredEpoch(userContext, interfaces);
            Long streakRepairConsumedEpoch = UserStreakUtil.streakRepairConsumedEpoch(userContext, interfaces);

            StreakStateType stateType = StreakStateDeterminer.determineStreakStateType(userStreakData, streakRepairExpiryEpoch, streakRepairConsumedEpoch);
            CuroState curoState = CuroStateFactory.getCuroStateForType(stateType);

            setCurrentStreakCount(userStreakData.getCurrentStreakCount());
            setMessage(curoState.getHomescreenWidgetMessage());
            setAssets(Map.of("backgroundImage", curoState.getHomescreenWidgetImageUrl(), "streakIcon", (userStreakData.isStreakPaused() || userStreakData.getCurrentStreakCount() == 0) ? "image/daily-streak/fire_gray.png" : "image/daily-streak/fire.png"));
            setStreakCountTitle(String.format("%s streak",(currentStreakCount <= 1 ? "day" : "days")));
            setDeeplink(AppDeeplink.USER_STREAK_MAIN_PAGE.getDeeplinkString());

        } catch (Exception e){
            String message = String.format("HSDailyStreakWidget :: Failed to build widget for user : %s", e.getMessage());
            interfaces.exceptionReportingService.reportException(message, e);
            return null;
        }
        return Collections.singletonList(this);
    }
}
