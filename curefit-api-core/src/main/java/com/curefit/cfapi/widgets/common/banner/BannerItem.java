package com.curefit.cfapi.widgets.common.banner;

import com.curefit.base.models.DateTimezone;
import com.curefit.cfapi.model.internal.vm.action.ActionV2;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.banner.BannerType;
import com.curefit.cfapi.view.viewmodels.transform.StoryItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class TimerStyle {
    String backgroundColor;
    String foregroundColor;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class DimensionValue {
    IntelligentDimension dimension;
    String value;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class BannerVariant {
    List<DimensionValue> dimensionValues;
    String image;
    String videoUrl;
    String videoS3Key;
    Boolean loopVideo;
    Boolean muteVideo;
    String mwebImage;
    String webImage;
    Action action;
    ActionV2 actionV2;
    ContentMetric contentMetric;
    List<LivePlatform> liveAllowedPlatforms;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class BannerExperiment {
    List<IntelligentDimension> dimensions;
    List<BannerVariant> variants;
    String bucketId;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class TimerItem {
    DateTimezone timerEndTimeWithTz;
    TimerStyle style;
    String privateOfferId;
    String title;
    String timerTimeText;
    Action action;
    Object timerEndTime;
    Object timerStyle;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class ImpressionCap {
    Integer count;
    Integer interval;
    ImpressionExpiry expiry;
}

enum ImpressionExpiry {
    MONTH,
    QUARTER,
    WEEK
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString(of = {"image", "title", "description", "storyType", "topTitle", "subTitle"})
public class BannerItem extends BaseDataItemWithMetric {
    String title;
    String image;
    String mwebImage;
    String webImage;
    String videoUrl;
    String videoS3Key;
    Boolean loopVideo;
    Boolean muteVideo;
    Action action;
    ActionV2 actionV2;
    String offerId;
    String offerText;
    String skuTitle;
    String skuProductType;
    TimerItem timer;
    int recommendationRate;
    String tagText;
    String ctaText;
    Integer dedupeLimit;
    String dedupeId;

    ImpressionCap impressionCap;
    Integer priority;
    String bannerIdentifier;
    String bannerExperimentId;
    int bannerExperimentVersion;
    int maxNumberOfPersonalizedBanners;
    int experimentSamplePercentage;
    List<BannerExperiment> bannerExperiments;
    List<LivePlatform> liveAllowedPlatforms;
    String description;
    Number duration;
    protected BannerType bannerType;
    String storyType;
    List<StoryItem> storyTextItems;
    StoryItem blurItemData;
    String lottieUrl;
    LottieStyle lottieStyle;
    boolean pauseAutoScroll;
    String subTitle;
    String topTitle;
    String cultCostPrice;
    String cultOfferPrice;
    String cultGradientTitle;
    String categoryText;
    String corpImage;
    String corpCompanyLogo;
    String corpCompanyName;
    String corpSponsoredByCaption;
    String corpImageSizeRatio;
    String metricAvgCaption;
    String metricCTACaptionRegular;
    String metricCTACaptionBold;
    String crmAssetId;
    BannerItemFooter footer;
    boolean ellipticalTagText;
    boolean showFullTime;
    boolean showDays;
    IndividualCardHeader individualCardHeader;
    Map<String, Object> meta;

    public enum BannerStoryType {
        CULT_LIVE,
        WELLNESS_BENEFIT,
        WORKOUT_SESSION,
        CORP_EXPLORE_CARD,
        CENTER_INFO_CARD,
        WORKOUT_METRIC
    }

    public enum LottieStyle {
        ORIGINAL("ORIGINAL"),
        OVERLAP("OVERLAP");

        private final String stringValue;

        LottieStyle(final String s) {
            stringValue = s;
        }

        @JsonValue
        public String toString() {
            return stringValue;
        }
    }
}
