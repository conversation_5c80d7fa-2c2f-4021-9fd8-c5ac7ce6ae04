package com.curefit.cfapi.widgets.community;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.community.SquadGoalStateHelper;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.userservice.pojo.entry.UserEntry;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@Builder
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class GoalSummaryWidget extends BaseWidget  {
    String badgeTitle;
    String badgeImage;
    String badgeLottie;
    String startDate;
    String endDate;
    Integer earnedReward;
    Integer totalReward;
    List<SquadGoalProgress> squadScoringDetails;
    Integer weekOffset;
    Integer weekFromStart;
    String bodyImage;
    String bodyLottie;
    String title;
    String bodyLeading;
    String bodyTrailing;
    String dividerImage;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext,
                                      WidgetContext widgetContext) throws Exception {
        Map<String, String> queryParams = widgetContext.getQueryParams();
        if(queryParams.get("title") != null){
            setTitle(queryParams.get("title"));
        }
        return Collections.singletonList(this);
    }
}