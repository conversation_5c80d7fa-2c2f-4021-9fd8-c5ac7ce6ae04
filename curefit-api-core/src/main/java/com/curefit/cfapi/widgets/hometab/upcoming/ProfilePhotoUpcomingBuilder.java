package com.curefit.cfapi.widgets.hometab.upcoming;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionIcon;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.items.CardListContentType;
import com.curefit.cfapi.pojo.vm.items.UpcomingItem;
import com.curefit.cfapi.pojo.vm.items.UpcomingItemMetaInfo;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.common.data.exception.BaseException;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.userservice.pojo.entry.UserEntry;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutionException;

import static com.curefit.cfapi.constants.FitnessPlannerConstants.HEADSTART_PILOT_CHECKINS_SEGMENT;
import static com.curefit.cfapi.constants.FitnessPlannerConstants.POST_CHECKIN_PROFILE;
import static com.curefit.cfapi.constants.FitnessPlannerConstants.ADD_USER;;

@Slf4j
public class ProfilePhotoUpcomingBuilder {
    public List<UpcomingItem> getProfilePhotoUpcoming(ServiceInterfaces interfaces, UserContext userContext, CardListContentType cardListContentType, LocalDate startDate, LocalDate endDate) {
        try {
            String userId = userContext.getUserProfile().getUserId();
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                    RequestType.PLATFORM_SEGMENTS,
                    userContext).get();
            if (userSegments.contains(HEADSTART_PILOT_CHECKINS_SEGMENT) && AppUtil.isUploadProfilePicturePageSupported(userContext)) {
                UserEntry userEntry = interfaces.userService.getUser(userId).get();
                if (userEntry.getProfilePictureUrl() == null) {
                    UpcomingItem profilePhotoUpcoming = new UpcomingItem();
                    profilePhotoUpcoming.setTitle("Add Profile Picture");
                    profilePhotoUpcoming.setImageUrl(POST_CHECKIN_PROFILE);

                    Action uploadProfileImageAction = new Action();
                    uploadProfileImageAction.setActionType(ActionType.NAVIGATION);
                    uploadProfileImageAction.setTitle("ADD PICTURE");
                    uploadProfileImageAction.setUrl("curefit://uploadprofilepicture");
                    uploadProfileImageAction.setIconUrl(ADD_USER);
                    uploadProfileImageAction.setVariant("tertiary");

                    profilePhotoUpcoming.setAction(uploadProfileImageAction);

                    Date startOfDay = TimeUtil.getStartOfDay(new Date(), ZoneId.systemDefault().toString());
                    profilePhotoUpcoming.setDate(startOfDay.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().toString());
                    profilePhotoUpcoming.setImageUrl(ADD_USER);
                    profilePhotoUpcoming.setTimestamp(startOfDay.toInstant().toEpochMilli());
                    profilePhotoUpcoming.setSubTitle("to help trainers find you at gym");
                    profilePhotoUpcoming.setFooters(new ArrayList<>());
                    profilePhotoUpcoming.setCollapsedTitle("Add Profile Picture");
                    profilePhotoUpcoming.setCollapsedSubTitle("to help trainers find you at gym");
                    profilePhotoUpcoming.setSecondaryAction(profilePhotoUpcoming.getAction());

                    UpcomingItemMetaInfo upcomingItemMetaInfo = new UpcomingItemMetaInfo();
                    upcomingItemMetaInfo.setTitle("ADD PICTURE");
                    upcomingItemMetaInfo.setCenterName("to help trainers find you at gym");
                    upcomingItemMetaInfo.setTenant("gymPt");

                    profilePhotoUpcoming.setUpcomingItemMetaInfo(upcomingItemMetaInfo);

                    return Collections.singletonList(profilePhotoUpcoming);
                }
            }
            return Collections.emptyList();
        } catch (BaseException | ExecutionException | InterruptedException e) {
            log.error("Unable to build profile photo upload widget for userId: " + userContext.getUserProfile().getUserId(), e);
            return Collections.emptyList();
        }
    }
}
