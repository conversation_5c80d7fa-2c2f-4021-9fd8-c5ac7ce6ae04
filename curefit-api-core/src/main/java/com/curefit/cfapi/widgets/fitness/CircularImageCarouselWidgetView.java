package com.curefit.cfapi.widgets.fitness;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.ActionUtil;
import com.curefit.cfapi.util.GymPtUtil;
import com.curefit.cfapi.util.GymUtil;
import com.curefit.cfapi.util.PlayUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.banner.Header;
import com.curefit.common.data.exception.BaseException;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class CircularImageCarouselWidgetView extends BaseWidget {
    String title;
    List<CircularCarouselItem> items;
    Integer imageSize;
    Integer borderRadius;
    String itemTitleVariant;
    boolean enableFadingRing;
    String interSpacing;
    String titleAlignment;
    Object containerStyle;
    Action seeMore;
    Header header;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        if (!(imageSize != null && imageSize > 0)) {
            imageSize = null;
        }

        if (!(borderRadius != null && borderRadius > 0)) {
            borderRadius = null;
        }

        if (TextUtils.isEmpty(itemTitleVariant)) {
            itemTitleVariant = null;
        }
        List<CircularCarouselItem> finalItems = new ArrayList<>();
        SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
        try {
            for (CircularCarouselItem item : items) {
                if (item.getActionV2() != null) {
                    Action actionV2 = ActionUtil.getVMActionFromActionV2(item.getActionV2(), userContext, interfaces);
                    if (actionV2 != null) {
                        item.setAction(actionV2);
                    }
                }
                if (!item.getTitle().contains(GymPtUtil.EXPLORE_PT_QA_TITLE) && !item.getTitle().contains(GymPtUtil.BOOK_PT_QA_TITLE)) {
                    finalItems.add(item);
                } else if (checkEligibilityAndManipulatePTItems(item, userSegments, interfaces, userContext)) {
                    finalItems.add(item);
                }
                manipulateItemAction(item, userContext, interfaces);
            }
        } catch (BaseException e) {
            e.printStackTrace();
        }
        this.setItems(finalItems);
        setWidgetType(WidgetType.CIRCULAR_IMAGE_CAROUSEL_WIDGET);
        return Collections.singletonList(this);
    }

    private boolean checkEligibilityAndManipulatePTItems(CircularCarouselItem item, SegmentSet<String> userSegments, ServiceInterfaces interfaces, UserContext userContext) {
        if (item.getTitle().contains(GymPtUtil.BOOK_PT_QA_TITLE)) {
            if (userSegments.contains(GymPtUtil.GYM_PT_ACTIVE_MEMBERS_SEGMENTS)) {
                Action bookPTAction = GymPtUtil.getBookPTSessionAction(interfaces, userContext);
                if (bookPTAction != null) {
                    item.setTitle("Book Personal\n" + "Training");
                    item.setAction(bookPTAction);
                    return true;
                }
            }
        } else if (item.getTitle().contains(GymPtUtil.EXPLORE_PT_QA_TITLE)) {
            if (!userSegments.contains(GymPtUtil.GYM_PT_ACTIVE_MEMBERS_SEGMENTS) && userSegments.contains(GymPtUtil.MEMBERS_WITH_ATLEAST_1_GYM_CHECKIN_D_60)) {
                if (GymPtUtil.hasUserBookedOrAttendedPTTrial(userSegments)) {
                    item.setTitle("Buy Personal\n" + "Training");
                    item.setAction(GymPtUtil.getPickAPackAction());
                } else {
                    item.setTitle("Book Personal\n" + "Training");
                    item.setAction(GymPtUtil.getAllTrainersAtCenterAction());
                }
                if (GymPtUtil.isUserExpiredPTMember(userSegments)) {
                    item.setTitle("Renew Personal\n" + "Training");
                }
                return true;
            }
        }
        return false;
    }

    private void manipulateItemAction(CircularCarouselItem item, UserContext userContext, ServiceInterfaces interfaces) {
        if (item.getAction() != null && item.getAction().getUrl() != null
                && item.getAction().getUrl().indexOf("fitso_classbooking") != -1 && item.getAction().getUrl().indexOf("checkPlaySelectPack") != -1) {
            var action = PlayUtil.getPlayClassBookingPageAction(item.getAction(), interfaces, userContext);
            item.setAction(action);
        }

        if (item.getTitle().contains("Gym Check-in")) {
            Action completionAction = item.getAction();
            Action uploadProfilePicModalV2Action = GymUtil.getUploadProfilePicModalV2Action(interfaces, userContext, completionAction, log);
            if (uploadProfilePicModalV2Action != null) {
                item.setAction(uploadProfilePicModalV2Action);
            }
        }
    }
}
