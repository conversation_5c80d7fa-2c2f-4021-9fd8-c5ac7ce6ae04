package com.curefit.cfapi.widgets.chroniccare;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SfHomeScanFaceWidget extends BaseWidgetNonVM {
    private String title = "A healthy selfie";
    private String description;
    private String subTitle = "Health vitals in 60 secs";
    private List<ScanItem> scanItems;
    private Action showHistoryAction;
    private Action scanFaceAction;
    private String scanExhaustedText = "Knowledge is power. Come back to get your vitals tomorrow!";
    private String bgImageUrl = "image/chroniccare/face_scan_home_background.png";
    private int scansAllowedForDay;
    private int scansDoneToday;
    private boolean scanExhausted;

    @Getter
    @Setter
    @NoArgsConstructor
    public static class ScanItem {
        ScanItemStatus status;
        String scanItemText;

        public ScanItem(ScanItemStatus status, String scanItemText) {
            this.status = status;
            this.scanItemText = scanItemText;
        }
    }

    public enum ScanItemStatus {
        MISSED("MISSED"),
        PENDING("PENDING"),
        DONE("DONE");

        String value;

        ScanItemStatus(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }
    }

    public SfHomeScanFaceWidget() {
        super(WidgetType.SF_HOME_SCAN_FACE_WIDGET);
    }
}


