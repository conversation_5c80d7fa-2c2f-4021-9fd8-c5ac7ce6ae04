package com.curefit.cfapi.widgets.chroniccare;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SfSugarControlDietPlanWidget extends BaseWidgetNonVM {
    String cardTitle;
    String dishImage;
    Action action;
    String pdfUrl;

    public SfSugarControlDietPlanWidget() {
        super(WidgetType.SF_SUGAR_CONTROL_DIET_PLAN_WIDGET);
    }
}
