package com.curefit.cfapi.widgets.hometab;

import com.curefit.base.enums.AppTenant;
import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.constants.StringConstants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.model.mongo.PrimaryQuickActionConfig;
import com.curefit.cfapi.model.mongo.PrimaryQuickActionNewLogicConfig;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.service.SegmentEvaluatorService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.hamlet.HamletHelper;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.hamlet.models.pojo.Payload;
import com.curefit.hamlet.models.response.UserAllocation;
import com.curefit.hamlet.types.Bucket;
import com.curefit.location.models.City;
import com.curefit.membership.pojo.entry.Benefit;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.product.enums.ProductType;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.curefit.cfapi.pojo.app.action.ActionType.NAVIGATION;
import static com.curefit.cfapi.util.ActionUtil.formatQueryParamWithQuerySeparator;
import static java.util.Map.entry;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class Discover {
    Action action;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@ToString
class QuickAction{
    AddActivityCard quickAction;
    Integer priority;
    boolean toShow;
    List<String> requiredMemberships;
    String requiredSegment;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class CityOverride {
    String city;
    List<ProductType> offerings;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
class DiscoverConfig {
    @JsonProperty("enabled")
    Boolean enabled;
    @JsonProperty("title")
    String title;
    @JsonProperty("iconUrl")
    String iconUrl;
    @JsonProperty("action")
    String action;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
class IconConfig {
    @JsonProperty("enabled")
    Boolean enabled;
    @JsonProperty("iconType")
    String iconType;
    @JsonProperty("productType")
    String productType;
    @JsonProperty("iconTitle")
    String iconTitle;
    @JsonProperty("imageUrl")
    String imageUrl;
    @JsonProperty("lottieUrl")
    String lottieUrl;
    @JsonProperty("iconSubtitle")
    String iconSubtitle;
    @JsonProperty("iconColor")
    String iconColor;
}

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
class QuickActionConfig {
    @JsonProperty("enabled")
    Boolean enabled;
    @JsonProperty("discoverConfig")
    DiscoverConfig discoverConfig;
    @JsonProperty("applyOnSegmentId")
    String applyOnSegmentId;
    @JsonProperty("layoutType")
    String layoutType;
    @JsonProperty("fileType")
    String fileType;
    @JsonProperty("iconConfigList")
    List<IconConfig> iconConfigList;
}



@Getter
@Setter
@Slf4j
public class AddActivityWidget extends BaseWidget {
    Integer imageHeight;
    List<QuickAction> quickActionList = new ArrayList<>();
    ActivityCardProps activityCardProps;
    List<AddActivityCard> cards = new ArrayList<>();

    List<ProductType> offerings;
    List<CityOverride> cityOverrides;
    List<QuickActionConfig> quickActionConfigList;

    String fileType = "IMAGE";
    int numOfRows = 2;
    int numOfCol = 3;
    String titleFontSize = "P5";

    String quickActionType = "CAROUSEL";

    Action discover = null;

    @JsonIgnore
    Map<String, IconConfig> iconConfigMap = new HashMap<>();

    @JsonIgnore
    Set productTypesInConfig = new HashSet<>();

    @JsonIgnore
    Set iconTypesInConfig = new HashSet<>();

    @JsonIgnore
    Boolean isUnifiedCLPExperimentEnabled = false;

    @JsonIgnore
    Boolean isUnifiedClPMember = false;

    @JsonIgnore
    Boolean isUserCultpassBlackMember = false;

    @JsonIgnore
    Boolean isUserCultpassGoldMember = false;

    @JsonIgnore
    Boolean isUserCultpassLiveMember = false;

    @JsonIgnore
    Boolean isUserCultpassPlayMember = false;

    @JsonIgnore
    QuickActionConfig quickActionConfig = null;

    Boolean isFlutterHomePage = false;
    Number imageSize = 76;

//    private void populateCardsList() {
//        quickActionList.add(new QuickAction(""));
//    }

    private static final Map<ProductType, String> INDIA_APP_MAP = Map.ofEntries(
            entry(ProductType.DIY_FITNESS, "CultAtHome"),
            entry(ProductType.DIY_MEDITATION, "Meditation_Live"),
            entry(ProductType.LIVE, "CultAtHome")
    );

    private static final Map<ProductType, String> INTERNATIONAL_APP_MAP = Map.ofEntries(
            entry(ProductType.DIY_FITNESS, "CultLiveIntlPage"),
            entry(ProductType.DIY_MEDITATION, "MindLiveIntlPage")
    );

    public static final Map<AppTenant, Map<ProductType, String>> TENANT_TO_PAGE_MAP = Map.ofEntries(
            entry(AppTenant.CUREFIT, INDIA_APP_MAP),
            entry(AppTenant.LIVEFIT, INTERNATIONAL_APP_MAP)
    );

    private static final Map<String, String> SUPPLEMENT_EXP_SEGMENT = Map.ofEntries(
            entry("a60c44ba-f576-439c-b46a-68cca37f04da", "Vitamins & Supplements"),
            entry("7924e28b-8fd2-4f1b-972a-32c49eb3da1f", "Supplements"),
            entry("62ea985f-0187-42dd-8c62-eac1bd73658b", "whole.fit")
    );

    private List<String> PRIMARY_CARD_TYPES = new ArrayList<>();
    private Map<String,QuickAction> primaryQuickActionConfigs = new HashMap<>();

    public static void updateStylesForStreak(AddActivityWidget addActivityWidget) {
        addActivityWidget.setActivityCardProps(
                ActivityCardProps.builder()
                        .cardHeight(140.0)
                        .crossAxisSpacing(10.0)
                        .horizontalPadding(10.0)
                        .imageProps(ActivityCardProps.ImageProps.builder()
                                .imageHeight(45)
                                .imageWidth(45)
                                .build())
                        .build()
        );
    }

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws ExecutionException, InterruptedException, HttpException {


            this.numOfCol = 4;
            this.numOfRows = 2;
            this.titleFontSize = "P5";


            if(CultUtil.isPilatesOnlyMembership(interfaces.membershipService, userContext)) {
                this.numOfCol = 4;
                this.numOfRows = 1;
            }


        try {
            List<PrimaryQuickActionNewLogicConfig.PrimaryQuickActionsList> configData = interfaces.primaryQuickActionNewLogicConfigService.getPageConfigData();
            if (configData != null) {
                configData.forEach((item) -> {
                    primaryQuickActionConfigs.put(item.getTitle(), new QuickAction(null,item.getPriority(),item.getToShow(),item.getRequiredMemberships(), item.getRequiredSegment()));
                });
            }
        } catch (Exception e) {
            log.error("Error while fetching quick action configs ", e);
        }

        try {
            List<PrimaryQuickActionConfig.PrimaryQuickActionConfigItem> configData = interfaces.primaryQuickActionsConfigService.getPageConfigData();
            if (configData != null) {
                configData.forEach((item) -> {
                    if (AppUtil.doesUserExistInSegment(userContext, item.getSegmentId())) {
                        PRIMARY_CARD_TYPES = item.getQuickActionTypes();
                    }
                });
            }
        } catch (Exception e) {
            log.error("Error while fetching quick action configs ", e);
        }

        City city = userContext.getUserProfile().getCity();

        // Unified CLP Check
        if (userContext.getUserProfile().getHamletUserExperimentPromise() == null) {
            userContext.getUserProfile().setHamletUserExperimentPromise(interfaces.hamletHelper.getUserAllocations(userContext, Collections.emptyList()));
        }
        CompletableFuture<UserAllocation> hamletUserExperimentPromise = userContext.getUserProfile().getHamletUserExperimentPromise();

        SegmentEvaluatorService segmentEvaluatorService = interfaces.getSegmentEvaluatorService();
        this.isUnifiedCLPExperimentEnabled = AppUtil.isFitnessCLPSupported(interfaces.environmentService, segmentEvaluatorService, userContext, interfaces.apiKeyService);
        this.isUnifiedClPMember = segmentEvaluatorService.checkCondition(Arrays.asList("c5feb3a1-3b34-4987-b1b0-b0cbda617a46", "95dd511f-0aaf-4155-b565-a577dc6ebb49"), userContext).get() != null;
        this.isUserCultpassBlackMember = isUnifiedClPMember && segmentEvaluatorService.checkCondition(Collections.singletonList("66b2f821-2e84-4a28-836b-c8e7c9c7c4f5"), userContext).get() != null;
        this.isUserCultpassGoldMember = isUnifiedClPMember && segmentEvaluatorService.checkCondition(Collections.singletonList("b5d8691a-3d9c-48f6-a02c-2973d7abdb46"), userContext).get() != null;
        this.isUserCultpassLiveMember = isUnifiedClPMember && segmentEvaluatorService.checkCondition(Collections.singletonList("5244bab7-c090-4ad4-b72b-d1d7368b4304"), userContext).get() != null;
        this.isUserCultpassPlayMember = isUnifiedClPMember && segmentEvaluatorService.checkCondition(Collections.singletonList("fc6a5d9d-8354-4913-a987-953f61b444f7"), userContext).get() != null;
        this.isFlutterHomePage = AppUtil.doesUserBelongToNewHomeAuroraCLP(interfaces, interfaces.getEnvironmentService(), userContext);

        this.quickActionConfig = isUnifiedClPMember
                && AppUtil.isQuickActionV3Supported(userContext, interfaces)
                && this.quickActionConfigList != null ?
                this.quickActionConfigList.stream()
                        .filter(config -> {
                            try {
                                return config.getEnabled()
                                        && config.getApplyOnSegmentId() != null
                                        && !config.getApplyOnSegmentId().isEmpty()
                                        && segmentEvaluatorService.checkCondition(Collections.singletonList(config.getApplyOnSegmentId()), userContext).get() != null;
                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            } catch (ExecutionException e) {
                                throw new RuntimeException(e);
                            }
                        }).findFirst().orElse(null) : null;

        this.iconConfigMap = this.getIconConfigMap();

        List<ProductType> offerings = this.getAvailableOfferings(city);
        List<ProductType> finalSortedOfferings = AppUtil.isInternationalApp(userContext) ? this.sortOfferingsIntl(offerings, city.getAvailableOfferings()) : this.sortOfferings(userContext, offerings, interfaces);
        SegmentSet<String> userSegments = getUserSegments(userContext);

        List<AddActivityCard> finalcards = getPrimaryCardsByNewLogic(interfaces,userContext,this.quickActionConfig,primaryQuickActionConfigs);
        List<AddActivityCard> activityCardsPreferrence = new ArrayList<>(finalSortedOfferings.parallelStream().flatMap(productType -> {
            try {
                if (this.quickActionConfig == null || this.productTypesInConfig.contains(productType.toString())) {
                    List<AddActivityCard> addActivityCards = this.getActivityCardsForType(productType, userContext, interfaces, userSegments);
                    if (addActivityCards != null) {
                        return addActivityCards.stream();
                    }
                }
            } catch (Exception err) {
                interfaces.exceptionReportingService.reportException("error in getActivityCardForType", err);
            }
            return Stream.empty();
        }).filter(Objects::nonNull).collect(Collectors.toList()));

        for(AddActivityCard addActivityCard: activityCardsPreferrence){
            boolean flag = true;
           for (AddActivityCard card: finalcards){
               if(addActivityCard.getTitle().equals(card.getTitle())){
                   flag = false;
               }
           }
           if(flag){
               finalcards.add(addActivityCard);
           }
        }
        if(AppUtil.isUserPartOfFixedQuickActionsExp(userContext,interfaces)){
            this.cards = finalcards;
        }
        else{
            this.cards = getPrimaryCards(userContext, interfaces);
            this.cards.addAll(activityCardsPreferrence);
        }

        if (this.quickActionConfig != null) {
            this.fileType = this.quickActionConfig.getFileType() != null ? this.quickActionConfig.getFileType() : this.fileType;
            this.quickActionType = this.quickActionConfig.getLayoutType() != null ? this.quickActionConfig.getLayoutType() : this.quickActionType;
            this.quickActionType = this.quickActionType.equals("LIST") ? "CAROUSEL" : this.quickActionType;
            if (this.quickActionConfig.getDiscoverConfig() != null && this.quickActionConfig.getDiscoverConfig().getEnabled()) {
                Action action = new Action();
                action.setActionType(NAVIGATION);
                action.setUrl(this.quickActionConfig.getDiscoverConfig().getAction());
                action.setTitle(this.quickActionConfig.getDiscoverConfig().getTitle());
                this.discover = action;
            }
        }
        this.appendActivityCardProps(widgetContext);
        return Collections.singletonList(this);
    }
    private void appendActivityCardProps(WidgetContext widgetContext) {
        if (widgetContext.getQueryParams().get("isUserStreakPage") != null && widgetContext.getQueryParams().get("isUserStreakPage").equals("true")) {
            this.setActivityCardProps(
                    ActivityCardProps.builder()
                            .cardHeight(155.0)
                            .crossAxisSpacing(10.0)
                            .horizontalPadding(10.0)
                            .typeScale("P5")
                            .imageProps(ActivityCardProps.ImageProps.builder()
                                    .imageHeight(45)
                                    .imageWidth(45)
                                    .build())
                            .build());
        }
    }

    private List<AddActivityCard> getPrimaryCards(UserContext userContext, ServiceInterfaces serviceInterfaces) throws ExecutionException, InterruptedException {
        List<AddActivityCard> primaryCards = new ArrayList<>();

        PRIMARY_CARD_TYPES.forEach((cardType) -> {
            try {
                AddActivityCard activityCard = getActivityCard(cardType, userContext, serviceInterfaces);
                if (activityCard != null) primaryCards.add(activityCard);
            } catch (Exception e) {
                log.error("Error while fetching primary quick actions ", e);
            }
        });

        return primaryCards;
    }

    private List<AddActivityCard> getPrimaryCardsByNewLogic(ServiceInterfaces serviceInterfaces, UserContext userContext, QuickActionConfig quickActionConfig, Map<String,QuickAction> primaryActionsConfig) throws ExecutionException, InterruptedException, HttpException {
        List<QuickAction> primaryCards = new ArrayList<>();
        List<String> benefits = List.of("GYMFIT_GA", "GYMFIT_GX", "CULT", "PLAY", "LUX", "CF_LIVE", "COACH_TRANSFORM", "COACH_PT", "COACH_NC","COACH_FC","LIFT",
                "COACH_BOOTCAMP_NUTRITION","COACH_PLUS","GYMFIT_PERSONAL_TRAINING","LUX_GX", "LUX_GROUP_CLASSES");
        List<IconConfig> iconConfigList = new ArrayList<>();
        if(quickActionConfig != null){
            iconConfigList = quickActionConfig.getIconConfigList();
        }
        List<Membership> membershipsOfUser = MembershipUtil.activeMembershipPresentForBenefits(serviceInterfaces,userContext,benefits);
        List<String> memberBenefits = new ArrayList<>();
        for(Membership membership: membershipsOfUser){
            memberBenefits.addAll(membership.getBenefits().stream().map(Benefit::getName).toList());
        }
        for(IconConfig quickAction : iconConfigList){
            switch (quickAction.getIconTitle()){
                case "book a-cult class": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard classBookingCard = getBookClassCard(userContext, serviceInterfaces);
                    QuickAction bookingQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                    bookingQuickAction.setQuickAction(classBookingCard);
                    if(bookingQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                        bookingQuickAction.setToShow(true);
                    }
                    primaryCards.add(bookingQuickAction);
                    break;
                }
                case "book PT-session": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard ptActivityCard = new AddActivityCard();
                    ptActivityCard = this.buildQuickActionCard("BOOK_PT", ptActivityCard);
                    Action bookPTAction = GymPtUtil.getBookPTSessionAction(serviceInterfaces, userContext);
                    if (bookPTAction != null) {
                        ptActivityCard.setAction(bookPTAction);
                        QuickAction ptQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                        ptQuickAction.setQuickAction(ptActivityCard);
                        if(ptQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                            ptQuickAction.setToShow(true);
                        }
                        primaryCards.add(ptQuickAction);
                    }
                    break;
                }
                case "play-sports": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    List<AddActivityCard> playCards = this.buildPlayCard(userContext, serviceInterfaces);
                    if(playCards != null && !playCards.isEmpty()){
                        QuickAction bookingQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                        bookingQuickAction.setQuickAction(playCards.getFirst());
                        if(bookingQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                            bookingQuickAction.setToShow(true);
                        }
                        primaryCards.add(bookingQuickAction);
                    }

                    break;
                }
                case "strength-tracker": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard strengthTrackerCard = this.buildStrengthTrackerCard();
                    if(strengthTrackerCard.getTitle() != null){
                        QuickAction strengthTrackerQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                        strengthTrackerQuickAction.setQuickAction(strengthTrackerCard);
                        if(strengthTrackerQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                            strengthTrackerQuickAction.setToShow(true);
                        }
                        primaryCards.add(strengthTrackerQuickAction);
                    }

                    break;
                }
                case "clubs at-cult": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard cultCommunity = this.buildCultCommunityCard();
                    if(cultCommunity.getTitle() != null){
                        QuickAction cultCommunityQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                        cultCommunityQuickAction.setQuickAction(cultCommunity);
                        if (cultCommunityQuickAction.getRequiredSegment() != null) {
                            cultCommunityQuickAction.setToShow(
                                    AppUtil.doesUserExistInSegment(userContext, cultCommunityQuickAction.getRequiredSegment()));
                        } else if(cultCommunityQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                            cultCommunityQuickAction.setToShow(true);
                        }
                        primaryCards.add(cultCommunityQuickAction);
                    }

                    break;
                }
                case "personal-training plan": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    if(!AppUtil.isMemberPartOfPtProgramming(userContext)){
                        break;
                    }
                    AddActivityCard personalTrainingPlanCard = this.buildPersonalTrainingPlanCard();
                    if(personalTrainingPlanCard.getTitle() != null && quickAction.getIconTitle() != null){
                        QuickAction ptQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                        ptQuickAction.setQuickAction(personalTrainingPlanCard);
                        if(ptQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                            ptQuickAction.setToShow(true);
                        }
                        primaryCards.add(ptQuickAction);
                    }

                    break;
                }
                case "PT-progress": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    if(!AppUtil.isMemberPartOfPtHubExperiment(userContext)){
                        break;
                    }
                    AddActivityCard personalTrainingHubCard = this.buildPersonalTrainingHubCard();
                    if(personalTrainingHubCard.getTitle() != null && quickAction.getIconTitle() != null){
                        QuickAction ptQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                        ptQuickAction.setQuickAction(personalTrainingHubCard);
                        if(ptQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                            ptQuickAction.setToShow(true);
                        }
                        primaryCards.add(ptQuickAction);
                    }

                    break;
                }
                case "view-my squad": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard squadQuickAction = this.buildSquadQuickAction();
                    if(squadQuickAction.getTitle() != null){
                        QuickAction bookingQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                        bookingQuickAction.setQuickAction(squadQuickAction);
                        if(bookingQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                            bookingQuickAction.setToShow(true);
                        }
                        primaryCards.add(bookingQuickAction);
                    }

                    break;
                }
                case "weight-loss": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    List<AddActivityCard> coachCards = this.buildCoachCard(serviceInterfaces, userContext);
                    if(coachCards != null && !coachCards.isEmpty()){
                        QuickAction bookingQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                        bookingQuickAction.setQuickAction(coachCards.getFirst());
                        if(bookingQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                            bookingQuickAction.setToShow(true);
                        }
                        primaryCards.add(bookingQuickAction);
                    }

                    break;
                }
                case "checkin-at gym": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard gymCheckinCard = getGymCheckinCard(userContext, serviceInterfaces);
                    QuickAction gymCheckinQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                    gymCheckinQuickAction.setQuickAction(gymCheckinCard);
                    if(gymCheckinQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                        gymCheckinQuickAction.setToShow(true);
                    }
                    primaryCards.add(gymCheckinQuickAction);
                    break;
                }
                case "workout-at home": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard liveFitnessCard = getLiveFitnessCard(ProductType.LIVE,userContext,serviceInterfaces);
                    QuickAction liveFitnessCardQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                    liveFitnessCardQuickAction.setQuickAction(liveFitnessCard);
                    if(liveFitnessCardQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                        liveFitnessCardQuickAction.setToShow(true);
                    }
                    primaryCards.add(liveFitnessCardQuickAction);
                    break;
                }
                case "view all-gyms": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard exploreGymsCard = getExploreGymCard();
                    QuickAction exploreGymsCardQuickAction= primaryActionsConfig.get(quickAction.getIconTitle());
                    exploreGymsCardQuickAction.setQuickAction(exploreGymsCard);
                    if(exploreGymsCardQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                        exploreGymsCardQuickAction.setToShow(true);
                    }
                    primaryCards.add(exploreGymsCardQuickAction);
                    break;
                }
                case "smart workout-plan": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard workoutPlanCard = getWorkoutPlanCard(userContext, serviceInterfaces);
                    QuickAction workoutPlanCardQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                    workoutPlanCardQuickAction.setQuickAction(workoutPlanCard);
                    if(workoutPlanCardQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                        workoutPlanCardQuickAction.setToShow(true);
                    }
                    primaryCards.add(workoutPlanCardQuickAction);
                    break;
                }
                case "fitness-report": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    AddActivityCard fitnessReportCard = getFitnessReportCard();
                    QuickAction fitnessReportCardQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                    fitnessReportCardQuickAction.setQuickAction(fitnessReportCard);
                    if(fitnessReportCardQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                        fitnessReportCardQuickAction.setToShow(true);
                    }
                    primaryCards.add(fitnessReportCardQuickAction);
                    break;
                }
                case "meditate-at home": {
                    if(!quickAction.getEnabled()){
                        break;
                    }
                    List<AddActivityCard> diyMeditationCard = this.buildDIYMeditationCard(ProductType.DIY_MEDITATION, userContext);
                    if(diyMeditationCard != null && !diyMeditationCard.isEmpty()){
                        QuickAction diyMeditationCardQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                        diyMeditationCardQuickAction.setQuickAction(diyMeditationCard.getFirst());
                        if(diyMeditationCardQuickAction.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)){
                            diyMeditationCardQuickAction.setToShow(true);
                        }
                        primaryCards.add(diyMeditationCardQuickAction);
                    }
                    break;
                }
                case "book a-pilates class": {
                    if (!quickAction.getEnabled()) {
                        break;
                    }
                    AddActivityCard pilatesBookingCard = this.getPilatesCard(userContext, serviceInterfaces);
                    QuickAction pilatesBookingQuickAction = primaryActionsConfig.get(quickAction.getIconTitle());
                    pilatesBookingQuickAction.setQuickAction(pilatesBookingCard);
                    if (pilatesBookingCard.getAction() != null) {
                        pilatesBookingQuickAction.setToShow(true);
                    }
                    primaryCards.add(pilatesBookingQuickAction);
                    break;
                }
                case "view my-center": {
                    if (!quickAction.getEnabled()) {
                        break;
                    }
                    AddActivityCard viewMyCenterCard = this.getViewMyCenter(userContext, serviceInterfaces);
                    QuickAction viewPilatesCenter = primaryActionsConfig.get(quickAction.getIconTitle());
                    viewPilatesCenter.setQuickAction(viewMyCenterCard);
                    if (viewPilatesCenter.getRequiredMemberships().stream().anyMatch(memberBenefits::contains)) {
                        viewPilatesCenter.setToShow(true);
                    }
                    primaryCards.add(viewPilatesCenter);
                    break;
                }
            }
        }
        primaryCards.sort(Comparator.comparingInt(QuickAction::getPriority));
        List<AddActivityCard> finalCardsToShow = new ArrayList<>();
        for (QuickAction quickAction: primaryCards){
            if(quickAction.isToShow()){
                finalCardsToShow.add(quickAction.getQuickAction());
            }
        }
        return finalCardsToShow;
    }

    AddActivityCard getActivityCard(String cardType, UserContext userContext, ServiceInterfaces serviceInterfaces) throws ExecutionException, InterruptedException {

        switch (cardType) {
            case "FITNESS" : {
                AddActivityCard classBookingCard = getBookClassCard(userContext, serviceInterfaces);
                return classBookingCard;
            }
            case "GYMFIT_FITNESS_PRODUCT" : {
                AddActivityCard gymCheckInCard = getGymCheckinCard(userContext, serviceInterfaces);
                if (AppUtil.isUserAlreadyCheckedIn(userContext, userContext.getUserProfile().getUserId())) {
                    gymCheckInCard.setTitle("view\nQR code");
                    return gymCheckInCard;
                } else {
                    return gymCheckInCard;
                }
            }
            case "WORKOUT_PLAN" : {
                AddActivityCard workoutPlanCard = getWorkoutPlanCard(userContext, serviceInterfaces);
                if (this.isFlutterHomePage) {
                    return workoutPlanCard;
                }
                return null;
            }
            case "EXPLORE_GYMS" : {
                AddActivityCard exploreGymsCard = getExploreGymCard();
                if (this.isFlutterHomePage) {
                    return exploreGymsCard;
                }
                return null;
            }
            case "LIVE" : {
                AddActivityCard diyFitnessCard = getLiveFitnessCard(ProductType.DIY_FITNESS, userContext, serviceInterfaces);
                return diyFitnessCard;
            }
            case "EMPTY_SLOT" : {
                AddActivityCard emptySlot = new AddActivityCard();
                emptySlot.setTitle("EMPTY_SLOT");
                return emptySlot;
            }
        }
        return null;
    }

    private SegmentSet<String> getUserSegments(UserContext userContext) throws ExecutionException, InterruptedException {
        SegmentSet<String> userPlatformSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();
        return userPlatformSegments;
    }

    private List<ProductType> sortOfferingsIntl(List<ProductType> offerings, List<ProductType> offeringsOrder) {
        Comparator<ProductType> comparator = Comparator.comparing(off -> offeringsOrder.indexOf(off));
        offerings.sort(comparator);
        return offerings;
    }

    private List<ProductType> sortOfferings(UserContext userContext, List<ProductType> offerings, ServiceInterfaces interfaces) {
        try {
            String userId = userContext.getMockSegmentUserId() != null ? userContext.getMockSegmentUserId() : userContext.getUserProfile().getUserId();
            var quickActionScoreResponse = interfaces.quickActionService.computeScore(userContext, userId);
            var finalOfferings = offerings.stream().filter(a -> quickActionScoreResponse.getScoreMap().containsKey(a)).collect(Collectors.toList());
            finalOfferings.sort((a, b) -> {
                var scoreA = quickActionScoreResponse.getScoreMap().get(a);
                var scoreB = quickActionScoreResponse.getScoreMap().get(b);
                return scoreA.getFinalScore() > scoreB.getFinalScore() ? -1 : 1;
            });
            return finalOfferings;
        } catch (BaseException | InterruptedException | ExecutionException e) {
            log.error("Error while sorting quick action ", e);
            return offerings;
        }
    }

    private Map<String, IconConfig> getIconConfigMap() {
        Map<String, IconConfig> iconConfigMap = new HashMap<>();
        if (this.quickActionConfig != null) {
            for (IconConfig iconConfig : this.quickActionConfig.getIconConfigList()) {
                if (iconConfig.getEnabled()) {
                    iconConfigMap.put(iconConfig.getIconType(), iconConfig);
                    this.productTypesInConfig.add(iconConfig.getProductType());
                    this.iconTypesInConfig.add(iconConfig.getIconType());
                }
            }
        }
        return iconConfigMap;
    }


    private List<ProductType> getAvailableOfferings(City city) {
        List<ProductType> offerings = this.offerings;
        if (CollectionUtils.isNotEmpty(cityOverrides)) {
            Optional<CityOverride> override = cityOverrides.stream().filter(cityOverride ->
                    cityOverride.getCity().equals(city.getCityId())).findAny();
            if (override.isPresent()) {
                offerings = override.get().getOfferings();
            }
        }
        // overlap offering with available offering in cities
        return offerings.stream().filter(offering -> city.getAvailableOfferings().contains(offering)).collect(Collectors.toList());
    }

    private String getPageId(ProductType productType, UserContext userContext) {
        AppTenant tenant = AppUtil.getAppTenantFromUserContext(userContext);
        Map<ProductType, String> PageMap = TENANT_TO_PAGE_MAP.get(tenant);
        return PageMap.get(productType);
    }

    private String getQuickActionTitle(String title) {
        if (title.contains("-")) {
            String[] splitTitle = title.split("-", 2);
            return splitTitle[0] + "\n" + splitTitle[1];
        } else
            return title;
    }

    private AddActivityCard buildQuickActionCard(String iconType, AddActivityCard addActivityCard) {
        IconConfig iconConfig = this.iconConfigMap.get(iconType);
        addActivityCard.setTitle(this.getQuickActionTitle(iconConfig.getIconTitle()));
        addActivityCard.setColor(iconConfig.getIconColor());
        addActivityCard.setIcon(iconConfig.getImageUrl());
        addActivityCard.setLottie(iconConfig.getLottieUrl());
        addActivityCard.setSubTitle("");
        return addActivityCard;
    }

    private List<AddActivityCard> buildFitStartCard(UserContext userContext) {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("FITSTART")) {
            addActivityCard = this.buildQuickActionCard("FITSTART", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Explore FitStart");
            } else {
                addActivityCard.setTitle("EXPLORE");
                addActivityCard.setSubTitle("FitStart");
            }
            addActivityCard.setIcon("/image/addActivity/widgeticon/fitstart.png");
            addActivityCard.setColor("#F46DA0");
        }
        Action action = new Action();
        action.setActionType(NAVIGATION);
        action.setUrl("curefit://listpage?pageId=Fitstart_sale");
        addActivityCard.setAction(action);
        if (!PRIMARY_CARD_TYPES.contains("FITSTART"))  return List.of(addActivityCard);
        return List.of();
    }

    private String getDIYWidgetId(UserContext userContext) {
        if (AppUtil.isInternationalApp((userContext))) {
            return StringConstants.FITNESS_DIY_SERIES_WIDGET_ID_INTL;
        }
        return AppUtil.isDiyWodSupported(userContext) ? StringConstants.FITNESS_WOD_WIDGET_ID : StringConstants.FITNESS_DIY_SERIES_WIDGET_ID;
    }

    private List<AddActivityCard> buildDIYFitnessCard(ProductType productType, UserContext userContext, ServiceInterfaces interfaces) throws ExecutionException, InterruptedException {
        AddActivityCard addActivityCard = new AddActivityCard();
        boolean isIntlWidgetNamesSupported = AppUtil.isIntlWidgetNamesSupported(userContext);

        if (this.iconConfigMap.containsKey("DIY_FITNESS")) {
            addActivityCard = this.buildQuickActionCard("DIY_FITNESS", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Workout at home");
            } else {
                addActivityCard.setTitle(isIntlWidgetNamesSupported ? "Workout" : "WORKOUT");
                addActivityCard.setSubTitle(isIntlWidgetNamesSupported ? "Library" : "at home");
            }
            addActivityCard.setIcon(this.getActivityIcon("book_a_class.png"));
            addActivityCard.setColor("#8080B3");
        }

        List<NameValuePair> parameters = new ArrayList<>();
        String baseUrl = "curefit://tabpage";

        AppTenant tenant = AppUtil.getAppTenantFromUserContext(userContext);
        if (tenant == AppTenant.LIVEFIT) {
            parameters.add(new BasicNameValuePair("pageId", this.getPageId(productType, userContext)));
            baseUrl = "curefit://listpage";
        } else {
            if (isUnifiedCLPExperimentEnabled) {
                if (isUserCultpassLiveMember) {
                    boolean isFitnessMemberDarkThemeSupported = AppUtil.isFitnessMemberDarkThemeSupported(interfaces.segmentEvaluatorService, userContext);
                    parameters.add(new BasicNameValuePair("pageId", "fitnesshub"));
                    parameters.add(new BasicNameValuePair("selectedTab", isFitnessMemberDarkThemeSupported ? "cultpassLIVE-Members_NEW" : "CultAtHome"));
                } else {
                    baseUrl = "curefit://listpage";
                    parameters.add(new BasicNameValuePair("pageId", "CultPassLiveSKU"));
                }
            } else {
                parameters.add(new BasicNameValuePair("pageId", "live"));
                parameters.add(new BasicNameValuePair("selectedTab", this.getPageId(productType, userContext)));
            }
        }
        parameters.add(new BasicNameValuePair("widgetId", this.getDIYWidgetId(userContext)));
        addActivityCard.setAction(ActionUtil.cultAtHomeClpAction(baseUrl, parameters));

        if (!PRIMARY_CARD_TYPES.contains("DIY_MEDITATION")) return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildDIYMeditationCard(ProductType productType, UserContext userContext) {
        AddActivityCard addActivityCard = new AddActivityCard();
        boolean isIntlWidgetNamesSupported = AppUtil.isIntlWidgetNamesSupported(userContext);

        if (this.iconConfigMap.containsKey("DIY_MEDITATION")) {
            addActivityCard = this.buildQuickActionCard("DIY_MEDITATION", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Meditate at home");
            } else {
                addActivityCard.setTitle(isIntlWidgetNamesSupported ? "Guided" : "MEDITATE");
                addActivityCard.setSubTitle(isIntlWidgetNamesSupported ? "Meditation" : "at home");
            }
            addActivityCard.setIcon(this.getActivityIcon("at_home.png"));
            addActivityCard.setColor("#F46DA0");
        }
        String widgetId = AppUtil.isDiyWodSupported(userContext) ? StringConstants.MIND_WOD_WIDGET_ID : StringConstants.MIND_DIY_SERIES_WIDGET_ID;
        List<NameValuePair> parameters = new ArrayList<>();
        String baseUrl = "curefit://tabpage";

        AppTenant tenant = AppUtil.getAppTenantFromUserContext(userContext);
        if (tenant == AppTenant.LIVEFIT) {
            parameters.add(new BasicNameValuePair("pageId", this.getPageId(productType, userContext)));
            baseUrl = "curefit://listpage";
        } else {
            if (isUnifiedCLPExperimentEnabled) {
                baseUrl = "curefit://listpage";
                parameters.add(new BasicNameValuePair("pageId", "Meditation_Live"));
            } else {
                parameters.add(new BasicNameValuePair("pageId", "live"));
                parameters.add(new BasicNameValuePair("selectedTab", this.getPageId(productType, userContext)));
            }
        }
        parameters.add(new BasicNameValuePair("widgetId", widgetId));
        addActivityCard.setAction(ActionUtil.mindAtHomeClpAction(baseUrl, parameters));

        if (!PRIMARY_CARD_TYPES.contains("DIY_MEDITATION")) return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildFoodCard() {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.isFlutterHomePage) {
            addActivityCard.setTitle("Order meals");
        } else {
            addActivityCard.setTitle("ORDER");
            addActivityCard.setSubTitle("meals");
        }
        addActivityCard.setIcon(this.getActivityIcon("cook_at_home.png"));
        addActivityCard.setColor("#FFA522");
        addActivityCard.setAction(ActionUtil.eatFitClpAction(new ArrayList<>()));

        return List.of(addActivityCard);
    }

    private List<AddActivityCard> buildFoodMarketplaceCard(UserContext userContext, ServiceInterfaces interfaces) {

        Boolean isServiceable;
        try {
            isServiceable = interfaces.segmentEvaluatorService.checkCondition(Arrays.asList("cef7a265-e026-4142-a4cb-ba8e1b858fab"), userContext).get() != null;
            isServiceable |= interfaces.foodMarketplace.getFoodMarketplacePresence(userContext);
        } catch (Exception e) {
            log.error("SUPRESS::AddActivity::FoodMP, Segment evaluation failed", e);
            isServiceable = false;
        }
        if (!isServiceable) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("FOOD_MARKETPLACE")) {
            addActivityCard = this.buildQuickActionCard("FOOD_MARKETPLACE", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Order meals");
            } else {
                addActivityCard.setTitle("ORDER");
                addActivityCard.setSubTitle("meals");
            }
            addActivityCard.setIcon(this.getActivityIcon("cook_at_home.png"));
            addActivityCard.setColor("#FFA522");
        }
        addActivityCard.setAction(ActionUtil.getFoodMarketplaceListingNavigationAction());

        if (!PRIMARY_CARD_TYPES.contains("FOOD_MARKETPLACE"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildRecipeCard(UserContext userContext) {
        AddActivityCard addActivityCard = new AddActivityCard();
        boolean isIntlWidgetNamesSupported = AppUtil.isIntlWidgetNamesSupported(userContext);
        if (this.iconConfigMap.containsKey("RECIPE")) {
            addActivityCard = this.buildQuickActionCard("RECIPE", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Cook at home");
            } else {
                addActivityCard.setTitle(isIntlWidgetNamesSupported ? "Healthy" : "COOK");
                addActivityCard.setSubTitle(isIntlWidgetNamesSupported ? "Recipes" : "at home");
            }
            addActivityCard.setIcon(this.getActivityIcon("cook_at_home.png"));
            addActivityCard.setColor("#FFA522");
        }
        addActivityCard.setAction(ActionUtil.recipeClpAction());
        if (!PRIMARY_CARD_TYPES.contains("RECIPE"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildConsultationCard(UserContext userContext, ServiceInterfaces serviceInterfaces) throws ExecutionException, InterruptedException {
        boolean isTCEligible = serviceInterfaces.getSegmentEvaluatorService().checkCondition(Collections.singletonList("a81f327a-3c68-4a39-9533-88460240e116"), userContext).get() != null;

        if (!isTCEligible) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("CONSULTATION")) {
            addActivityCard = this.buildQuickActionCard("CONSULTATION", addActivityCard);
        } else {
            return null;
//            if (this.isFlutterHomePage) {
//                addActivityCard.setTitle("Consult doctor");
//            } else {
//                addActivityCard.setTitle("CONSULT");
//                addActivityCard.setSubTitle("doctor");
//            }
//            addActivityCard.setIcon(this.getActivityIcon("program.png"));
//            addActivityCard.setColor("#45C6CF");
        }
        addActivityCard.setAction(ActionUtil.consultationClpAction(userContext));
        if (!PRIMARY_CARD_TYPES.contains("CONSULTATION"))  return List.of(addActivityCard);
        return List.of();
    }


    private List<AddActivityCard> buildGearCard(UserContext userContext, ServiceInterfaces interfaces) {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("GEAR")) {
            addActivityCard = this.buildQuickActionCard("GEAR", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Buy sportswear");
            } else {
                addActivityCard.setTitle("BUY");
                addActivityCard.setSubTitle("sportswear");
            }
            addActivityCard.setIcon(this.getActivityIcon("sports_wear.png"));
            addActivityCard.setColor("#7FB486");
        }
        if (AppUtil.doesUserBelongToNewStoreAuroraCLP(interfaces, interfaces.environmentService, userContext)) {
            addActivityCard.setAction(ActionUtil.fluterStoreQuickAction());
        } else {
            addActivityCard.setAction(ActionUtil.gearClpAction());
        }
        if (!PRIMARY_CARD_TYPES.contains("GEAR"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildTherapyCard(UserContext userContext, ServiceInterfaces interfaces) throws ExecutionException, InterruptedException {
        boolean isTherapyEligible = interfaces.segmentEvaluatorService.checkCondition(List.of("3ae678e4-54e7-4a2e-9a9c-a0b9f2dc8a45"), userContext).get() != null;
        if (!isTherapyEligible) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("THERAPY")) {
            addActivityCard = this.buildQuickActionCard("THERAPY", addActivityCard);
        } else {
            return null;
//            if (this.isFlutterHomePage) {
//                addActivityCard.setTitle("Book therapy");
//            } else {
//                addActivityCard.setTitle("BOOK");
//                addActivityCard.setSubTitle("therapy");
//            }
//            addActivityCard.setIcon(this.getActivityIcon("therapy_icon.png"));
//            addActivityCard.setColor("#D67B9D");
        }
        if (AppUtil.checkIfUserPartOfMindTherapyAuroraExperiment(userContext, interfaces)) {
            addActivityCard.setAction(ActionUtil.mindTherapyAuroraClpAction(userContext));
        } else {
            addActivityCard.setAction(ActionUtil.mindTherapyClpAction(userContext));
        }
        if (!PRIMARY_CARD_TYPES.contains("THERAPY"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildDiagnosticCard(UserContext userContext) {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("DIAGNOSTICS")) {
            addActivityCard = this.buildQuickActionCard("DIAGNOSTICS", addActivityCard);
        } else {
            return null;
//            if (this.isFlutterHomePage) {
//                addActivityCard.setTitle("Book lab tests");
//            } else {
//                addActivityCard.setTitle("BOOK");
//                addActivityCard.setSubTitle("Lab Tests");
//            }
//            addActivityCard.setIcon(this.getActivityIcon("diagnostics.png"));
//            addActivityCard.setColor("#E4B337");
        }
        addActivityCard.setAction(ActionUtil.daignosticClpAction(userContext));
        if (!PRIMARY_CARD_TYPES.contains("DIAGNOSTICS"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildCultLiveFitnessCard(UserContext userContext, ProductType productType, ServiceInterfaces interfaces) throws ExecutionException, InterruptedException {
        AddActivityCard addActivityCard = getLiveFitnessCard(productType, userContext, interfaces);
        if (!PRIMARY_CARD_TYPES.contains("LIVE"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildMindCard(UserContext userContext) {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("MIND")) {
            addActivityCard = this.buildQuickActionCard("MIND", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Join mind.live");
            } else {
                addActivityCard.setTitle("JOIN");
                addActivityCard.setSubTitle("mind.live");
            }
            addActivityCard.setIcon(this.getActivityIcon("at_home.png"));
            addActivityCard.setColor("#F46DA0");
        }
        NameValuePair nameValuePair = new BasicNameValuePair("pageFrom", "addActivity");
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        nameValuePairs.add(nameValuePair);
        addActivityCard.setAction(LiveUtil.getLiveClassBookingPageAction(userContext, ProductType.MIND, nameValuePairs));

        if (!PRIMARY_CARD_TYPES.contains("MIND"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildWholeFitCard(UserContext userContext, ServiceInterfaces interfaces) {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("ORDER_VITAMINS")) {
            addActivityCard = this.buildQuickActionCard("ORDER_VITAMINS", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Order vitamins");
                addActivityCard.setIcon(this.getActivityIcon("order_vitamins.png"));
            } else {
                addActivityCard.setTitle("ORDER");
                if (StringUtils.isEmpty(addActivityCard.getSubTitle())) {
                    addActivityCard.setSubTitle("vitamins");
                }
                addActivityCard.setIcon("/image/addActivity/widgeticon/whole-fit.png");
            }
            addActivityCard.setColor("#7FB486");
        }
        addActivityCard.setAction(ActionUtil.supplementDetailPageAction(userContext));
        if (!PRIMARY_CARD_TYPES.contains("MIND"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildLivePTCard(UserContext userContext, ServiceInterfaces serviceInterfaces, boolean isLivePTUser) throws ExecutionException, InterruptedException {
        boolean isLivePTEligible = serviceInterfaces.getSegmentEvaluatorService().checkCondition(Collections.singletonList("302097de-b21c-433b-b901-e74177ef3602"), userContext).get() != null;

        if (!isLivePTEligible) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("LIVE_PERSONAL_TRAINING")) {
            addActivityCard = this.buildQuickActionCard("LIVE_PERSONAL_TRAINING", addActivityCard);
        } else {
            return null;
//            if (this.isFlutterHomePage) {
//                addActivityCard.setTitle("Book online PT");
//            } else {
//                addActivityCard.setTitle("BOOK");
//                addActivityCard.setSubTitle("online PT");
//            }
//            addActivityCard.setIcon(this.getActivityIcon("live_pt_icon.png"));
//            addActivityCard.setColor("#ab79ff");
        }
        if (isLivePTUser) {
            addActivityCard.setAction(ActionUtil.livePTBookingPageAction());
        } else {
            addActivityCard.setAction(ActionUtil.livePtClpAction(isUnifiedCLPExperimentEnabled));
        }
        if (!PRIMARY_CARD_TYPES.contains("LIVE_PERSONAL_TRAINING"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildLiveSGTCard(UserContext userContext, HamletHelper hamletHelper, EnvironmentService environmentService) {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("LIVE_SGT")) {
            addActivityCard = this.buildQuickActionCard("LIVE_SGT", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Book online GX");
            } else {
                if (CultUtil.isLiveSGTMember(userContext))
                    addActivityCard.setTitle("BOOK FREE");
                else addActivityCard.setTitle("BOOK");
                addActivityCard.setSubTitle("online GX");
            }
            addActivityCard.setIcon(this.getActivityIcon("live_schedule.png"));
            addActivityCard.setColor("#7E93B4");
        }
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        String baseUrl = "curefit://tabpage";
        if (isUnifiedCLPExperimentEnabled) {
            if (isUserCultpassLiveMember) {
                nameValuePairs.add(new BasicNameValuePair("pageId", "fitnesshub"));
                nameValuePairs.add(new BasicNameValuePair("selectedTab", "CultAtHome"));
            } else {
                baseUrl = "curefit://listpage";
                nameValuePairs.add(new BasicNameValuePair("pageId", "CultPassLiveSKU"));
            }
        } else {
            addActivityCard.setAction(ActionUtil.liveSGTBookingPageAction());
        }
        addActivityCard.setAction(ActionUtil.cultAtHomeClpAction(baseUrl, nameValuePairs));
        if (!PRIMARY_CARD_TYPES.contains("LIVE_SGT"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildCoachCard(ServiceInterfaces interfaces, UserContext userContext) {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (TransformUtil.isTransformCombinedTabSupported(interfaces, userContext)) {
            addActivityCard.setAction(TransformUtil.getTransformTabDeepLink("", userContext));
        } else {
            addActivityCard.setAction(ActionUtil.coachPageAction());
        }
        if (!AppUtil.isWeightLossCombinedClpSupported(userContext)) {
            return null;
        } else if (CultUtil.isCoachMember(userContext) || CultUtil.isTransformPlusMember(userContext) || CultUtil.isCultBootcampMember(userContext)) {
            if (this.iconConfigMap.containsKey("COACH")) {
                addActivityCard = this.buildQuickActionCard("COACH", addActivityCard);
            } else {
                if (this.isFlutterHomePage) {
                    addActivityCard.setTitle("Weight loss");
                    addActivityCard.setIcon(this.getActivityIcon("weight_loss_coach.png"));
                } else {
                    addActivityCard.setTitle("WEIGHT");
                    addActivityCard.setSubTitle("loss");
                    addActivityCard.setIcon("/image/addActivity/widgeticon/coach_icon_update.png");
                    addActivityCard.setColor("#7E93B4");
                }
            }
        } else {
            if (this.iconConfigMap.containsKey("TRANSFORM")) {
                addActivityCard = this.buildQuickActionCard("TRANSFORM", addActivityCard);
            } else {
                if (this.isFlutterHomePage) {
                    String flutterTitle = CultUtil.isTransformBootcampMember(userContext) || CultUtil.isCultBootcampMember(userContext) ? "Lose weight" : "Weight loss coaching";
                    addActivityCard.setTitle(flutterTitle);
                } else {
                    addActivityCard.setTitle("LOSE");
                    addActivityCard.setSubTitle("weight");
                }
                addActivityCard.setIcon(this.getActivityIcon("weight_loss_new.png"));
            }
            addActivityCard.setColor("#7E93B4");
            if (TransformUtil.isTransformCombinedTabSupported(interfaces, userContext)) {
                addActivityCard.setAction(TransformUtil.getTransformTabDeepLink("", userContext));
            } else if (AppUtil.isWeightLossCombinedClpSupported(userContext)) {
                addActivityCard.setAction(ActionUtil.weightLossPageAction());
            }
        }

        if (!PRIMARY_CARD_TYPES.contains("COACH") && !PRIMARY_CARD_TYPES.contains("TRANSFORM"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildGymfitCard(UserContext userContext, ServiceInterfaces serviceInterfaces) throws ExecutionException, InterruptedException {
        AddActivityCard gymCheckInCard = getGymCheckinCard(userContext, serviceInterfaces);
        AddActivityCard exploreGymsCard = getExploreGymCard();
        AddActivityCard workoutPlanCard = getWorkoutPlanCard(userContext, serviceInterfaces);

        List<AddActivityCard> ret = new ArrayList<>();

        log.debug("Checking for already logged in user {}", userContext.getUserProfile().getUserId());
        if (!PRIMARY_CARD_TYPES.contains("GYMFIT_FITNESS_PRODUCT")) {
            if (AppUtil.isUserAlreadyCheckedIn(userContext, userContext.getUserProfile().getUserId())) {
                gymCheckInCard.setTitle("view\nQR code");
                ret.add(gymCheckInCard);
            } else if (AppUtil.isGymfitCheckinQASupported(serviceInterfaces, userContext)) {
                ret.add(gymCheckInCard);
            }
        }
        if (!PRIMARY_CARD_TYPES.contains("EXPLORE_GYMS") && this.isFlutterHomePage && AppUtil.isExploreGymsQASupported(serviceInterfaces, userContext)) {
            ret.add(exploreGymsCard);
        }
        if (!PRIMARY_CARD_TYPES.contains("WORKOUT_PLAN") && this.isFlutterHomePage && AppUtil.isWorkoutPlanQASupported(serviceInterfaces, userContext)) {
            ret.add(workoutPlanCard);
        }
        return ret;
    }

    private List<AddActivityCard> buildNutritionistCard(UserContext userContext, ServiceInterfaces serviceInterfaces) throws ExecutionException, InterruptedException {
        boolean isNCEligible = serviceInterfaces.getSegmentEvaluatorService().checkCondition(Collections.singletonList("74833cb0-4367-41f6-846d-008179f0b2d8"), userContext).get() != null;

        if (!isNCEligible) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("NUTRITIONIST_CONSULTATION")) {
            addActivityCard = this.buildQuickActionCard("NUTRITIONIST_CONSULTATION", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Consult dietician");
                addActivityCard.setIcon(this.getActivityIcon("weight_loss_coach_2.png"));
            } else {
                addActivityCard.setTitle("CONSULT");
                addActivityCard.setSubTitle("dietician");
                addActivityCard.setIcon("/image/addActivity/widgeticon/cook_at_home.png");
            }
            addActivityCard.setColor("#8080B3");
        }
        addActivityCard.setAction(AppUtil.isNCV3Supported(userContext) ? ActionUtil.nutritionistV3Action() : ActionUtil.nutritionistV2Action());
        if (!PRIMARY_CARD_TYPES.contains("NUTRITIONIST_CONSULTATION"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildBeauticianCard(UserContext userContext) {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("SKIN_HAIR")) {
            addActivityCard = this.buildQuickActionCard("SKIN_HAIR", addActivityCard);
        } else {
            return null;
//            if (this.isFlutterHomePage) {
//                addActivityCard.setTitle("Consult Cosmetologist");
//            } else {
//                addActivityCard.setTitle("CONSULT");
//                addActivityCard.setSubTitle("cosmetologist");
//            }
//            addActivityCard.setIcon("/image/addActivity/widgeticon/beautician_icon.png");
//            addActivityCard.setColor("#ffa7a7");
        }
        addActivityCard.setAction(ActionUtil.careClpSkinCareTab(userContext));
        if (!PRIMARY_CARD_TYPES.contains("SKIN_HAIR"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildUltrafitCard() {
        if (!this.isFlutterHomePage) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("ULTRAFIT")) {
            addActivityCard = this.buildQuickActionCard("ULTRAFIT", addActivityCard);
        } else {
            return null;
//            addActivityCard.setTitle("Buy CGM Sensor");
//            addActivityCard.setIcon(getActivityIcon("buy_cgm_sensor.png"));
//            addActivityCard.setColor("#ffa7a7");
        }
        addActivityCard.setAction(ActionUtil.ultrafitNavAction());
        if (!PRIMARY_CARD_TYPES.contains("ULTRAFIT"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildCultSportHomeGymCard(SegmentSet<String> userSegments) {
        if (!this.isFlutterHomePage || userSegments.contains(StringConstants.SEGMENT_NAME_QUICK_ACTION_CULT_SPORT_HOME_GYM_EXCLUSION)) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("CULTSPORT_HOME_GYM")) {
            addActivityCard = this.buildQuickActionCard("CULTSPORT_HOME_GYM", addActivityCard);
        } else {
            addActivityCard.setTitle("Setup Home Gym");
            addActivityCard.setIcon(getActivityIcon("workout_at_home.png"));
            addActivityCard.setColor("#ffa7a7");
        }
        addActivityCard.setAction(ActionUtil.cultSportHomeGymNavAction());
        if (!PRIMARY_CARD_TYPES.contains("CULTSPORT_HOME_GYM"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildCultSportCycleCard(SegmentSet<String> userSegments) {
        if (!this.isFlutterHomePage || userSegments.contains(StringConstants.SEGMENT_NAME_QUICK_ACTION_CULT_SPORT_CYCLES_EXCLUSION)) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("CULTSPORT_CYCLE")) {
            addActivityCard = this.buildQuickActionCard("CULTSPORT_CYCLE", addActivityCard);
        } else {
            return null;
//            addActivityCard.setTitle("Buy Cycles");
//            addActivityCard.setIcon(getActivityIcon("cycle.png"));
//            addActivityCard.setColor("#8080B3");
        }
        addActivityCard.setAction(ActionUtil.cultSportCyclesNavAction());
        if (!PRIMARY_CARD_TYPES.contains("CULTSPORT_CYCLE"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildCultCard(UserContext userContext, ServiceInterfaces serviceInterfaces) throws ExecutionException, InterruptedException {

        AddActivityCard classBookingCard = getBookClassCard(userContext, serviceInterfaces);
        AddActivityCard unpauseMembershipCard = getUnpauseMembershipCard();
        AddActivityCard fitnessReportCard = getFitnessReportCard();

        List<AddActivityCard> ret = new ArrayList<>();
        if (!PRIMARY_CARD_TYPES.contains("FITNESS")) {
            ret.add(classBookingCard);
        }
        if (!PRIMARY_CARD_TYPES.contains("UNPAUSE_MEMBERSHIP") && this.isFlutterHomePage && AppUtil.isUnpauseMembershipQASupported(serviceInterfaces, userContext)) {
            ret.add(unpauseMembershipCard);
        }
        if (!PRIMARY_CARD_TYPES.contains("FITNESS_REPORT") && this.isFlutterHomePage && AppUtil.isFitnessReportQASupported(serviceInterfaces, userContext)) {
            ret.add(fitnessReportCard);
        }

        return ret;
    }

    private AddActivityCard buildStrengthTrackerCard() {
        AddActivityCard strengthTrackerCard = new AddActivityCard();
        if(this.iconConfigMap.containsKey("STRENGTH_TRACKER")){
            this.buildQuickActionCard("STRENGTH_TRACKER", strengthTrackerCard);
            Action action = new Action();
            action.setActionType(NAVIGATION);
            action.setUrl("curefit://strength_tracker_screen");
            strengthTrackerCard.setAction(action);
        }
        return strengthTrackerCard;
    }

    private AddActivityCard buildCultCommunityCard() {
        AddActivityCard cultCommunityCard = new AddActivityCard();
        if(this.iconConfigMap.containsKey("CULT_COMMUNITIES")){
            this.buildQuickActionCard("CULT_COMMUNITIES", cultCommunityCard);
            Action action = new Action();
            action.setActionType(NAVIGATION);
            action.setUrl("curefit://webview?uri=https://clubs.cult.fit/&hideHeader=true");
            cultCommunityCard.setAction(action);
        }
        return cultCommunityCard;
    }

    private AddActivityCard buildPersonalTrainingPlanCard() {
        AddActivityCard personalTrainingPlanCard = new AddActivityCard();
        if(this.iconConfigMap.containsKey("PERSONAL_TRAINING_PLAN")){
            this.buildQuickActionCard("PERSONAL_TRAINING_PLAN", personalTrainingPlanCard);
            Action action = new Action();
            action.setActionType(NAVIGATION);
            action.setUrl("curefit://pt_tracker");
            personalTrainingPlanCard.setAction(action);
        }
        return personalTrainingPlanCard;
    }

    private AddActivityCard buildPersonalTrainingHubCard() {
        AddActivityCard personalTrainingHubCard = new AddActivityCard();
        if(this.iconConfigMap.containsKey("PERSONAL_TRAINING_HUB")){
            this.buildQuickActionCard("PERSONAL_TRAINING_HUB", personalTrainingHubCard);
            Action action = new Action();
            action.setActionType(NAVIGATION);
            action.setUrl("curefit://pt_progress_hub");
            personalTrainingHubCard.setAction(action);
        }
        return personalTrainingHubCard;
    }

    private AddActivityCard buildSquadQuickAction() {
        AddActivityCard squadQuickAction = new AddActivityCard();
        if(this.iconConfigMap.containsKey("SQUADS")){
            this.buildQuickActionCard("SQUADS", squadQuickAction);
            Action action = new Action();
            action.setActionType(NAVIGATION);
            NameValuePair nameValuePair = new BasicNameValuePair("pageFrom", "homeTab");
            List<NameValuePair> nameValuePairs = new ArrayList<>();
            nameValuePairs.add(nameValuePair);
            String queryParams = formatQueryParamWithQuerySeparator(nameValuePairs);
            action.setUrl(STR."curefit://squads_feed\{queryParams}");
            squadQuickAction.setAction(action);
        }
        return squadQuickAction;
    }

    private List<AddActivityCard> buildPlayCard(UserContext userContext, ServiceInterfaces serviceInterfaces) throws ExecutionException, InterruptedException {
        if (!AppUtil.isCultPassPlaySupported(userContext, serviceInterfaces.environmentService, serviceInterfaces.segmentEvaluatorService)) {
            return null;
        }
        AddActivityCard classBookingCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("PLAY")) {
            classBookingCard = this.buildQuickActionCard("PLAY", classBookingCard);
        } else {
            if (this.isFlutterHomePage) {
                classBookingCard.setTitle("Play sports");
            } else {
                classBookingCard.setTitle("PLAY");
                classBookingCard.setSubTitle("sports");
            }
            classBookingCard.setIcon(this.getActivityIcon("play_sports.png"));
            classBookingCard.setColor("#8080B3");
        }

        NameValuePair nameValuePair = new BasicNameValuePair("pageFrom", "addActivity");
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        nameValuePairs.add(nameValuePair);


        classBookingCard.setAction(ActionUtil.getPlayUnifiedClpAction(isUserCultpassPlayMember, serviceInterfaces, userContext));


        List<AddActivityCard> ret = new ArrayList<>();
        if (!PRIMARY_CARD_TYPES.contains("CULTSPORT_CYCLE"))  ret.add(classBookingCard);
        return ret;
    }

    private List<AddActivityCard> buildSugarfitPill() {

        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("BUNDLE")) {
            addActivityCard = this.buildQuickActionCard("BUNDLE", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Diabetes reversal");
            } else {
                addActivityCard.setTitle("DIABETES");
                addActivityCard.setSubTitle("reversal");
            }
            addActivityCard.setIcon(this.getActivityIcon("program.png"));
            addActivityCard.setColor("#ffa7a7");
        }
        addActivityCard.setAction(ActionUtil.sugarfitWebViewAction());
        if (!PRIMARY_CARD_TYPES.contains("BUNDLE"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildCosmeticsCard() {

        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("COSMETICS")) {
            addActivityCard = this.buildQuickActionCard("COSMETICS", addActivityCard);
        } else {
            return null;
//            if (this.isFlutterHomePage) {
//                addActivityCard.setTitle("Order cosmetics");
//            } else {
//                addActivityCard.setTitle("ORDER");
//                addActivityCard.setSubTitle("cosmetics");
//            }
//            addActivityCard.setIcon("/image/addActivity/widgeticon/beautician_icon.png");
//            addActivityCard.setColor("#ffa7a7");
        }
        addActivityCard.setAction(ActionUtil.getCosmeticsNavAction());
        if (!PRIMARY_CARD_TYPES.contains("COSMETICS"))  return List.of(addActivityCard);
        return List.of();

    }

    private List<AddActivityCard> buildCultRowPill(UserContext userContext, ServiceInterfaces interfaces) {
        Boolean isSupported;
        try {
            isSupported = interfaces.segmentEvaluatorService.checkCondition(Arrays.asList("4b130ea7-7ccd-472a-94bd-2c3dfa0ab7d6"), userContext).get() != null;
        } catch (Exception e) {
            log.error("SUPRESS::AddActivity::CULTROW, Segment evaluation failed", e);
            isSupported = false;
        }
        if (!isSupported) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("CULT_ROW")) {
            addActivityCard = this.buildQuickActionCard("CULT_ROW", addActivityCard);
        } else {
            return null;
//            if (this.isFlutterHomePage) {
//                addActivityCard.setTitle("cultRow");
//            } else {
//                addActivityCard.setTitle("CULTROW");
//                addActivityCard.setSubTitle("CLASS");
//            }
//            addActivityCard.setIcon(this.getActivityIcon("cult_row.png"));
//            addActivityCard.setColor("#FFA522");
        }
        addActivityCard.setAction(ActionUtil.getCultROWCLPNavAction());

        if (!PRIMARY_CARD_TYPES.contains("CULT_ROW"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildCultBootcampCard(ServiceInterfaces interfaces, UserContext userContext) {
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("CULT_BOOTCAMP")) {
            addActivityCard = this.buildQuickActionCard("CULT_BOOTCAMP", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Weight loss Bootcamp");
            } else {
                return null;
            }
            addActivityCard.setIcon(this.getActivityIcon("weight_loss_bootcamp.png"));
            addActivityCard.setColor("#7E93B4");
        }
        if (AppUtil.doesUserBelongsToTransformNewBootcampSegment(userContext)) {
            addActivityCard.setAction(ActionUtil.bootcampPostPurchasePageAction());
        } else if (!AppUtil.isCultBootcampActionVisibleToUser(userContext) || !AppUtil.isBootcampPrePurchaseSupported(userContext)) {
            return null;
        } else {
            addActivityCard.setAction(ActionUtil.bootcampPrePurchaseAction());
        }
        if (TransformUtil.isTransformCombinedTabSupported(interfaces, userContext)) {
            addActivityCard.setAction(TransformUtil.getTransformTabDeepLink(TransformUtil.bootcampSubCategoryCode, userContext));
        }
        if (!PRIMARY_CARD_TYPES.contains("CULT_BOOTCAMP"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildLiftCard(UserContext userContext) {
        if (!AppUtil.doesUserBelongsToLiftSegment(userContext)) {
            return null;
        }
        AddActivityCard addActivityCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("LIFT")) {
            addActivityCard = this.buildQuickActionCard("LIFT", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Break your PRs");
            } else {
                return null;
            }
            addActivityCard.setIcon(this.getActivityIcon("lift.png"));
            addActivityCard.setColor("#7E93B4");
        }
        addActivityCard.setAction(ActionUtil.liftPostPurchaseAction());
        if (!PRIMARY_CARD_TYPES.contains("LIFT"))  return List.of(addActivityCard);
        return List.of();
    }

    private List<AddActivityCard> buildGymPtCard(SegmentSet<String> userSegments, UserContext userContext, ServiceInterfaces interfaces) {
        AddActivityCard PTActivityCard = new AddActivityCard();
        // Active PT member
        if (userSegments.contains(GymPtUtil.GYM_PT_ACTIVE_MEMBERS_SEGMENTS)) {
            if (this.iconConfigMap.containsKey("BOOK_PT")) {
                PTActivityCard = this.buildQuickActionCard("BOOK_PT", PTActivityCard);
                Action bookPTAction = GymPtUtil.getBookPTSessionAction(interfaces, userContext);
                if (bookPTAction != null) {
                    PTActivityCard.setAction(bookPTAction);
                    return Collections.singletonList(PTActivityCard);
                }
            }
        }
        // All other users -> Non PT member visiting PT Gym, Expired members or Trial members
        else if (userSegments.contains(GymPtUtil.MEMBERS_WITH_ATLEAST_1_GYM_CHECKIN_D_60)) {
            List<AddActivityCard> ptCards = new ArrayList<>();
            if (GymPtUtil.isMemberPartOfAssessmentQASegment(userSegments)) {
                AddActivityCard knowYourBodyCard = new AddActivityCard();
                if (this.iconConfigMap.containsKey("EXPLORE_PT")) {
                    knowYourBodyCard = this.buildQuickActionCard("EXPLORE_PT", knowYourBodyCard);
                    knowYourBodyCard.setAction(GymPtUtil.getAssessmentPageAction());
                    if (!PRIMARY_CARD_TYPES.contains("EXPLORE_PT"))  ptCards.add(knowYourBodyCard);
                }
            }
            if (this.iconConfigMap.containsKey("BOOK_PT")) {
                boolean hasBookedOrAttendedTrial = GymPtUtil.hasUserBookedOrAttendedPTTrial(userSegments);
                PTActivityCard = this.buildQuickActionCard("BOOK_PT", PTActivityCard);
                String title = null;
                if (GymPtUtil.isUserExpiredPTMember(userSegments)) {
                    title = "renew personal-training";
                } else if (hasBookedOrAttendedTrial) {
                    title = "buy personal-training";
                }
                if (title != null) {
                    PTActivityCard.setTitle(getQuickActionTitle(title));
                }
                if (hasBookedOrAttendedTrial) {
                    PTActivityCard.setAction(GymPtUtil.getPickAPackAction());
                } else {
                    PTActivityCard.setAction(GymPtUtil.getAllTrainersAtCenterAction());
                }
                if (!PRIMARY_CARD_TYPES.contains("BOOK_PT"))  ptCards.add(PTActivityCard);
                return ptCards;
            }
        }
        return null;
    }

    AddActivityCard getBookClassCard(UserContext userContext, ServiceInterfaces serviceInterfaces) {
        AddActivityCard classBookingCard = new AddActivityCard();
        Bucket bucket = serviceInterfaces.environmentService.isProduction() ? AppUtil.getUserBucket(userContext, serviceInterfaces.hamletHelper, "1078") : null;

        if (this.iconConfigMap.containsKey("FITNESS")) {
            classBookingCard = this.buildQuickActionCard("FITNESS", classBookingCard);
        } else {
            if (this.isFlutterHomePage) {
                classBookingCard.setTitle("Book cult class");
                if (bucket != null) {
                    Optional<Payload> payload = bucket.getPayload().stream().filter(p -> Objects.equals(p.getKey(), "cultfit-callout")).findAny();
                    AddActivityCard finalClassBookingCard = classBookingCard;
                    payload.ifPresent(value -> finalClassBookingCard.setTitle((String) value.getValue()));
                }
            } else {
                classBookingCard.setTitle("BOOK");
                classBookingCard.setSubTitle("cult class");
            }
            classBookingCard.setIcon(this.getActivityIcon("book_a_class.png"));
            classBookingCard.setColor("#8080B3");
        }

        NameValuePair nameValuePair = new BasicNameValuePair("pageFrom", "addActivity");
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        nameValuePairs.add(nameValuePair);

        if (CultUtil.isCultOfflineEnabled(userContext, serviceInterfaces.environmentService)) {
            classBookingCard.setAction(ActionUtil.getClassBookingAction(userContext, ProductType.FITNESS, nameValuePairs));
        } else {
            classBookingCard.setAction(LiveUtil.getLiveClassBookingPageAction(userContext, ProductType.FITNESS, nameValuePairs));
        }

        return classBookingCard;
    }

    AddActivityCard getFitnessReportCard() {
        AddActivityCard fitnessReportCard = new AddActivityCard();

        if (this.iconConfigMap.containsKey("FITNESS_REPORT")) {
            fitnessReportCard = this.buildQuickActionCard("FITNESS_REPORT", fitnessReportCard);
        } else {
            if (this.isFlutterHomePage) {
                fitnessReportCard.setTitle("View fitness report");
            } else {
                fitnessReportCard.setTitle("VIEW");
                fitnessReportCard.setSubTitle("fitness report");
            }
            fitnessReportCard.setIcon(this.getActivityIcon("fitness_report.png"));
            fitnessReportCard.setColor("#8080B3");
        }

        fitnessReportCard.setAction(ActionUtil.fitnessReportQuickAction());
        return fitnessReportCard;
    }

    AddActivityCard getUnpauseMembershipCard() {
        AddActivityCard unpauseMembershipCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("UNPAUSE_MEMBERSHIP")) {
            unpauseMembershipCard = this.buildQuickActionCard("UNPAUSE_MEMBERSHIP", unpauseMembershipCard);
        } else {
            if (this.isFlutterHomePage) {
                unpauseMembershipCard.setTitle("Unpause membership");
            } else {
                unpauseMembershipCard.setTitle("UNPAUSE");
                unpauseMembershipCard.setSubTitle("membership");
            }
            unpauseMembershipCard.setIcon(this.getActivityIcon("membership_unpause_2.png"));
            unpauseMembershipCard.setColor("#8080B3");
        }

        unpauseMembershipCard.setAction(ActionUtil.unpauseMembershipQuickAction());
        return unpauseMembershipCard;
    }

    AddActivityCard getGymCheckinCard(UserContext userContext, ServiceInterfaces serviceInterfaces) {

        AddActivityCard gymCheckInCard = new AddActivityCard();

        if (this.iconConfigMap.containsKey("GYMFIT_FITNESS_PRODUCT")) {
            gymCheckInCard = this.buildQuickActionCard("GYMFIT_FITNESS_PRODUCT", gymCheckInCard);
        } else {
            if (this.isFlutterHomePage)
                gymCheckInCard.setTitle("Checkin at gym");
            else {
                gymCheckInCard.setTitle("WORKOUT");
                gymCheckInCard.setSubTitle("at gym");
            }
            gymCheckInCard.setIcon(this.getActivityIcon("gym_checkin.png"));
            gymCheckInCard.setColor("#333747");
        }
        gymCheckInCard.setAction(ActionUtil.unifiedCLPGymfitCLPAction(userContext, isUserCultpassGoldMember, isUserCultpassBlackMember));

        Action uploadProfilePicModalV2Action = GymUtil.getUploadProfilePicModalV2Action(serviceInterfaces, userContext, gymCheckInCard.getAction(), log);

        if(uploadProfilePicModalV2Action != null){
            gymCheckInCard.setAction(uploadProfilePicModalV2Action);
        }
        if (AppUtil.isUserAlreadyCheckedIn(userContext, userContext.getUserProfile().getUserId())) {
            gymCheckInCard.setTitle("view\nQR code");
        }
        return gymCheckInCard;
    }

    AddActivityCard getExploreGymCard() {
        AddActivityCard exploreGymsCard = new AddActivityCard();

        if (this.iconConfigMap.containsKey("EXPLORE_GYMS")) {
            exploreGymsCard = this.buildQuickActionCard("EXPLORE_GYMS", exploreGymsCard);
        } else {
            if (this.isFlutterHomePage)
                exploreGymsCard.setTitle("View nearby gyms");
            else {
                exploreGymsCard.setTitle("VIEW");
                exploreGymsCard.setSubTitle("nearby gyms");
            }
            exploreGymsCard.setIcon(this.getActivityIcon("nearby_gyms.png"));
            exploreGymsCard.setColor("#333747");
        }
        exploreGymsCard.setAction(ActionUtil.exploreGymsQuickAction());
        return exploreGymsCard;
    }

    AddActivityCard getWorkoutPlanCard(UserContext userContext, ServiceInterfaces serviceInterfaces) {
        AddActivityCard workoutPlanCard = new AddActivityCard();
        if (this.iconConfigMap.containsKey("WORKOUT_PLAN")) {
            workoutPlanCard = this.buildQuickActionCard("WORKOUT_PLAN", workoutPlanCard);
        } else {
            if (this.isFlutterHomePage)
                workoutPlanCard.setTitle("View workout plan");
            else {
                workoutPlanCard.setTitle("VIEW");
                workoutPlanCard.setSubTitle("workout plan");
            }
            workoutPlanCard.setIcon(this.getActivityIcon("workout_plan.png"));
            workoutPlanCard.setColor("#333747");
        }

        workoutPlanCard.setAction(ActionUtil.workoutPlanQuickAction(userContext, serviceInterfaces));
        return workoutPlanCard;
    }

    AddActivityCard getLiveFitnessCard (ProductType productType, UserContext userContext, ServiceInterfaces interfaces) throws ExecutionException, InterruptedException {
        AddActivityCard addActivityCard = new AddActivityCard();
        boolean isIntlWidgetNamesSupported = AppUtil.isIntlWidgetNamesSupported(userContext);
        if (this.iconConfigMap.containsKey("LIVE")) {
            addActivityCard = this.buildQuickActionCard("LIVE", addActivityCard);
        } else {
            if (this.isFlutterHomePage) {
                addActivityCard.setTitle("Workout at Home");
            } else {
                addActivityCard.setTitle(isIntlWidgetNamesSupported ? "Live" : "WORKOUT");
                addActivityCard.setSubTitle(isIntlWidgetNamesSupported ? "Workouts" : "at Home");
            }
            addActivityCard.setIcon(this.getActivityIcon("live_schedule.png"));
            addActivityCard.setColor("#6C82FF");
        }

        AppTenant tenant = AppUtil.getAppTenantFromUserContext(userContext);
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        NameValuePair nameValuePair = new BasicNameValuePair("pageFrom", "addActivity");
        nameValuePairs.add(nameValuePair);
        addActivityCard.setAction(LiveUtil.getLiveClassBookingPageAction(userContext, ProductType.FITNESS, nameValuePairs));
        if (tenant == AppTenant.LIVEFIT) {
            addActivityCard.setAction(LiveUtil.getLiveClassBookingPageAction(userContext, ProductType.FITNESS, nameValuePairs));
        } else {
            String baseUrl = "curefit://tabpage";
            if (isUnifiedCLPExperimentEnabled) {
                if (isUserCultpassLiveMember) {
                    boolean isFitnessMemberDarkThemeSupported = AppUtil.isFitnessMemberDarkThemeSupported(interfaces.segmentEvaluatorService, userContext);
                    nameValuePairs.add(new BasicNameValuePair("pageId", "fitnesshub"));
                    nameValuePairs.add(new BasicNameValuePair("selectedTab", isFitnessMemberDarkThemeSupported ? "cultpassLIVE-Members_NEW" : "CultAtHome"));
                } else {
                    baseUrl = "curefit://listpage";
                    nameValuePairs.add(new BasicNameValuePair("pageId", "CultPassLiveSKU"));
                }
            } else {
                nameValuePairs.add(new BasicNameValuePair("pageId", "live"));
                nameValuePairs.add(new BasicNameValuePair("selectedTab", this.getPageId(productType, userContext)));
            }
            addActivityCard.setAction(ActionUtil.cultAtHomeClpAction(baseUrl, nameValuePairs));
        }
        return addActivityCard;
    }

    private AddActivityCard getPilatesCard(UserContext userContext, ServiceInterfaces serviceInterfaces) throws ExecutionException, InterruptedException, HttpException {
        AddActivityCard workoutPlanCard = new AddActivityCard();
        workoutPlanCard = this.buildQuickActionCard("PILATES", workoutPlanCard);
        workoutPlanCard.setAction(ActionUtil.pilatesSchedulePageAction(userContext, serviceInterfaces));
        return workoutPlanCard;
    }

    private AddActivityCard getViewMyCenter(UserContext userContext, ServiceInterfaces serviceInterfaces) throws ExecutionException, InterruptedException, HttpException {
        AddActivityCard viewMyCenterCard = new AddActivityCard();
        viewMyCenterCard = this.buildQuickActionCard("FITNESS", viewMyCenterCard);
        viewMyCenterCard.setAction(ActionUtil.pilatesCenterAction(userContext, serviceInterfaces));
        return viewMyCenterCard;
    }

    private String getActivityIcon(String iconName) {
        if (this.isFlutterHomePage) {
            return "/image/mem-exp/quick-action/" + iconName;
        } else {
            return "/image/addActivity/widgeticon/" + iconName;
        }
    }

    private List<AddActivityCard> getActivityCardsForType(
            ProductType productType, UserContext userContext, ServiceInterfaces interfaces,
            SegmentSet<String> userSegments) throws ExecutionException, InterruptedException {

        switch (productType) {
            case FOOD_MARKETPLACE:
                return AppUtil.isFoodMarketplaceSupported(userContext, interfaces.environmentService) ? this.buildFoodMarketplaceCard(userContext, interfaces) : null;
            case FITNESS:
                return this.buildCultCard(userContext, interfaces);
            case DIY_FITNESS:
                // TODO add a unified clp check
                return this.buildDIYFitnessCard(productType, userContext, interfaces);
            case LIVE:
                // TODO add a unified clp check
                return this.buildCultLiveFitnessCard(userContext, productType, interfaces);
            case MIND:
                return this.buildMindCard(userContext);
            case DIY_MEDITATION:
                // TODO add a unified clp check
                return this.buildDIYMeditationCard(productType, userContext);
            case RECIPE:
                return this.buildRecipeCard(userContext);
            case COACH:
                return AppUtil.isCoachSupported(userContext) ? this.buildCoachCard(interfaces, userContext) : null;
            case CONSULTATION:
                return this.buildConsultationCard(userContext, interfaces);
            case GEAR:
                return this.buildGearCard(userContext, interfaces);
            case THERAPY:
                return this.buildTherapyCard(userContext, interfaces);
            case DIAGNOSTICS:
                return this.buildDiagnosticCard(userContext);
            case WHOLE_FIT:
                return this.buildWholeFitCard(userContext, interfaces);
            case LIVE_PERSONAL_TRAINING:
                // TODO add a unified clp check
                boolean isLivePTSupported = AppUtil.isLivePTSupported(interfaces.environmentService, userContext);
                boolean isLivePTUser = CultUtil.isLivePTUser(userContext);
                return isLivePTSupported ? this.buildLivePTCard(userContext, interfaces, isLivePTUser) : null;
            case LIVE_SGT:
                boolean isLiveSGTSupported = AppUtil.isLiveSGTSupported(interfaces.environmentService, userContext);
                boolean isLiveSGTMember = CultUtil.isLiveSGTMember(userContext);
                return isLiveSGTSupported ? this.buildLiveSGTCard(userContext, interfaces.hamletHelper, interfaces.environmentService) : null;
            case GYMFIT_FITNESS_PRODUCT:
                // TODO add a unified clp check
                return this.buildGymfitCard(userContext, interfaces);
            case NUTRITIONIST_CONSULTATION:
                return this.buildNutritionistCard(userContext, interfaces);
            case SKIN_HAIR:
                return AppUtil.isSkinHairSupported(userContext) ? this.buildBeauticianCard(userContext) : null;
            case BUNDLE: // this is for sugar.fit
                return this.buildSugarfitPill();
            case COSMETICS:
                return this.buildCosmeticsCard();
            case CULT_ROW:
                return this.buildCultRowPill(userContext, interfaces);
            case ULTRAFIT:
                return this.buildUltrafitCard();
            case PLAY:
                return this.buildPlayCard(userContext, interfaces);
            case CULTSPORT_CYCLE:
                return this.buildCultSportCycleCard(userSegments);
            case CULTSPORT_HOME_GYM:
                return this.buildCultSportHomeGymCard(userSegments);
            case CULT_BOOTCAMP:
                return this.buildCultBootcampCard(interfaces, userContext);
            case LIFT:
                return this.buildLiftCard(userContext);
            case GYM_PT_PRODUCT:
                return this.buildGymPtCard(userSegments, userContext, interfaces);
        }
        return null;
    }
}