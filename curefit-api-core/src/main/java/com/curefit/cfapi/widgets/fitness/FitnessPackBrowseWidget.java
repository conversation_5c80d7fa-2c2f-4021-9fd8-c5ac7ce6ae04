package com.curefit.cfapi.widgets.fitness;

import com.curefit.albus.common.BundleSellableProduct;
import com.curefit.albus.response.bundle.GenericBundleOrderResponse;
import com.curefit.alfred.models.order.OrderProduct;
import com.curefit.alfred.models.order.OrderProductOption;
import com.curefit.base.enums.Tenant;
import com.curefit.base.enums.UserAgent;
import com.curefit.center.dtos.CenterByExternalIdResponse;
import com.curefit.center.dtos.CenterEntry;
import com.curefit.center.dtos.CenterWithExternalId;
import com.curefit.center.enums.CenterVertical;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.model.internal.cult.*;
import com.curefit.cfapi.model.internal.diy.LiveOrderPayload;
import com.curefit.cfapi.model.internal.meta.AnalyticsData;
import com.curefit.cfapi.model.internal.userinfo.SessionInfo;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.userinfo.UserProfile;
import com.curefit.cfapi.model.internal.vm.MdcTag;
import com.curefit.cfapi.model.internal.vm.TechOwner;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.cult.services.spi.CultService;
import com.curefit.diyfs.pojo.RequestSource;
import com.curefit.gymfit.models.*;
import com.curefit.location.models.City;
import com.curefit.offers.client.OfferService;
import com.curefit.offers.dtos.GetOffersResponse;
import com.curefit.offers.dtos.LiveProductDiscountRequest;
import com.curefit.offers.dtos.LiveProductPricesResponse;
import com.curefit.offers.dtos.OfferMini;
import com.curefit.offers.dtos.ProductTaxBreakup;
import com.curefit.offers.dtos.care.CareProductDiscountRequest;
import com.curefit.offers.dtos.care.CareProductPricesResponse;
import com.curefit.offers.dtos.cult.CultProductPricesResponse;
import com.curefit.offers.dtos.cult.ProductPriceResponse;
import com.curefit.offers.dtos.gymfit.GymFitProductPricesResponse;
import com.curefit.offers.dtos.play.PlayProductPricesResponse;
import com.curefit.offers.enums.AddonType;
import com.curefit.offers.enums.OrderSource;
import com.curefit.offers.types.Addon;
import com.curefit.offers.types.Offer;
import com.curefit.offers.types.Prices;
import com.curefit.offers.types.UILabels;
import com.curefit.offers.types.UserInfo;
import com.curefit.ollivander.common.pojo.request.agent.AgentSearchRequestParam;
import com.curefit.ollivander.common.pojo.response.agent.AgentResponse;
import com.curefit.ollivander.common.pojo.response.resource.ResourceServiceMappingResponse;
import com.curefit.personaltrainer.pojo.entry.SessionEntry;
import com.curefit.pms.entries.BenefitEntry;
import com.curefit.pms.enums.*;
import com.curefit.pms.pojo.ExhaustivePackBenefit;
import com.curefit.pms.pojo.customPacks.augments.AugmentedOfflineFitnessPack;
import com.curefit.pms.requests.augments.AugmentContext;
import com.curefit.pms.requests.augments.AugmentedPackSearchRequest;
import com.curefit.pms.requests.augments.PackAugmentRequest;
import com.curefit.pms.responses.ExtraChargesResponse;
import com.curefit.product.enums.*;
import com.curefit.pms.pojo.Restriction;
import com.curefit.pms.pojo.customPacks.OfflineFitnessPack;
import com.curefit.pms.requests.PackSearchRequest;
import com.curefit.product.ProductPrice;
import com.curefit.product.enums.Status;
import com.curefit.product.models.cult.FitnessPack;
import com.curefit.product.models.cult.PackBenefit;
import com.curefit.product.models.diy.CFLiveProduct;
import com.curefit.product.models.diy.SubscriptionOptions;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.TextUtils;
import org.slf4j.MDC;
import org.springframework.data.domain.Page;
import reactor.util.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.curefit.cfapi.constants.StringConstants.COACH_MEMBERS_PLATFORM_SEGMENT_ID;
import static com.curefit.cfapi.util.CultUtil.OFFERS_TO_BE_SHOWN_UPFRONT;
import static com.curefit.cfapi.util.GymPtUtil.getLastAttendedPTSessionForUser;
import static com.curefit.cfapi.util.GymPtUtil.getTrainerLevel;
import static com.curefit.cfapi.util.GymUtil.GYMFIT_CENTER_ID_KEY;
import static com.curefit.cfapi.util.PlayUtil.ALL_CENTERS_DEEPLINK;
import static com.curefit.cfapi.util.StringUtil.pluralizeStringIfRequired;
import static com.curefit.cfapi.util.TimeUtil.DEFAULT_ZONE_ID;
import static com.curefit.gymfit.utils.Constants.ACCESS_CREDITS;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)

@AllArgsConstructor
@NoArgsConstructor
@ToString
public class FitnessPackBrowseWidget extends BaseWidget {
    @Getter
    static
    class LiveDurationStringResponse {
        private final String liveDurationString;
        private final boolean isDoubleDigit;

        LiveDurationStringResponse(String durationString, boolean isDoubleDigit) {
            this.liveDurationString = durationString;
            this.isDoubleDigit = isDoubleDigit;
        }
    }

    List<FitnessPackCategoryItem> items;
    List<BundleSellableProduct> bundleItems = null;
    MembershipPackWidgetData expandedMembershipPack = null;
    MembershipPackWidgetData collapsedMembershipPack = null;
    String subCategoryCode;
    Boolean isV2 = false;
    Boolean isPCOSPack = false;
    String filterId;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        Map<String, String> queryParams = widgetContext.getQueryParams();
        FitnessPackCategoryItem item = this.items.get(0);
        boolean isPartOfGstSplitSegment = AppUtil.doesUserBelongToGstSplitSegment(interfaces, interfaces.getEnvironmentService(), userContext);

        if (item.getProductType() == CultPackBrowseProductType.TRANSFORM_WL) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.TRANSFORM.toString());
            return this.buildCoachFitnessPackBrowseWidget(interfaces, userContext, queryParams);
        }
        if (item.getProductType() == CultPackBrowseProductType.CULT_UNLIMITED) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.CULT.toString());
            return this.buildCultFitnessPackBrowseWidget(interfaces, userContext, queryParams, isPartOfGstSplitSegment, ProductSubType.GENERAL);
        }
        if (item.getProductType() == CultPackBrowseProductType.LIVE) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.LIVE.toString());
            return this.buildLivePackBrowseWidget(item, interfaces, userContext, queryParams, isPartOfGstSplitSegment);
        }
        if (item.getProductType() == CultPackBrowseProductType.GYM_PACK) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.CULT.toString());
            return this.buildGymFitnessPackBrowseWidget(interfaces, userContext, queryParams, isPartOfGstSplitSegment, ProductSubType.GENERAL);
        }
        if (item.getProductType() == CultPackBrowseProductType.LUX_PACK) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.CULT.toString());
            return this.buildLuxPackBrowseWidget(interfaces, userContext, queryParams, isPartOfGstSplitSegment);
        }

        if (item.getProductType() == CultPackBrowseProductType.GYM_PT_PRODUCT) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.CULT.toString());
            return this.buildGymPTPackBrowseWidget(interfaces, userContext, queryParams);
        }

        if (item.getProductType() == CultPackBrowseProductType.PRO_SELECT) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.CULT.toString());
            return this.buildProSelectPackBrowseWidget(interfaces, userContext, queryParams, isPartOfGstSplitSegment);
        }

        if (item.getProductType() == CultPackBrowseProductType.ELITE_SELECT) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.CULT.toString());
            return this.buildEliteSelectPackBrowseWidget(interfaces, userContext, queryParams, isPartOfGstSplitSegment);
        }

        if (item.getProductType() == CultPackBrowseProductType.PRO_PLUS) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.CULT.toString());
            return this.buildGymFitnessPackBrowseWidget(interfaces, userContext, queryParams, isPartOfGstSplitSegment, ProductSubType.PLUS);
        }
        if (item.getProductType() == CultPackBrowseProductType.ELITE_PLUS) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.CULT.toString());
            return this.buildCultFitnessPackBrowseWidget(interfaces, userContext, queryParams, isPartOfGstSplitSegment, ProductSubType.PLUS);
        }
        if (item.getProductType() == CultPackBrowseProductType.ELITE_LITE) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.CULT.toString());
            return this.buildCultFitnessPackBrowseWidget(interfaces, userContext, queryParams, isPartOfGstSplitSegment, ProductSubType.LITE);
        }

        if (item.getProductType() == CultPackBrowseProductType.PLAY
                || item.getProductType() == CultPackBrowseProductType.PLAY_SELECT
                || item.getProductType() == CultPackBrowseProductType.PLAY_SPORT
                || item.getProductType() == CultPackBrowseProductType.PLAY_CITY_SPORT
                || item.getProductType() == CultPackBrowseProductType.PLAY_SWIM
                || item.getProductType() == CultPackBrowseProductType.PLAY_BADMINTON
                || item.getProductType() == CultPackBrowseProductType.PLAY_SQUASH
                || item.getProductType() == CultPackBrowseProductType.PLAY_TENNIS
                || item.getProductType() == CultPackBrowseProductType.PLAY_TT) {
            MDC.put(MdcTag.SLA_OWNER.toString(), TechOwner.PLAY.toString());
            return this.buildPlayPackWidget(interfaces, userContext, queryParams, isPartOfGstSplitSegment);
        }

        return null;
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    private List<BaseWidget> buildProSelectPackBrowseWidget(ServiceInterfaces interfaces, UserContext userContext, Map<String, String> queryParams, boolean isPartOfGstSplitSegment) throws Exception {
        long centerId = Long.parseLong(queryParams.get("centerId"));

        CenterEntry center = interfaces.centerService.getCenterDetails(centerId, false, null, null).get();
        boolean isWeb = AppUtil.isWeb(userContext);

        String cityId = userContext.getUserProfile().getCity().getCityId();
        Integer cultCityId = userContext.getUserProfile().getCity().getCultCityId();
        String centerCityId = center.getCity();
        if (Objects.nonNull(centerCityId)) {
            City centerCity = interfaces.cityCache.getCityById(centerCityId);
            if (Objects.nonNull(centerCity.getCityId()) && Objects.nonNull(centerCity.getCultCityId())) {
                cityId = centerCity.getCityId();
                cultCityId = centerCity.getCultCityId();
            }
        }
        List<AugmentedOfflineFitnessPack> gymfitPacks = userContext.getRequestCache().getAugmentedPackList(
            ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.GENERAL, false,
            userContext, Math.toIntExact(center.getId())
        );
        gymfitPacks.sort(Collections.reverseOrder(Comparator.comparing(pack -> pack.getProduct().getDurationInDays())));


        for (FitnessPackCategoryItem item : this.items) {
            if (CollectionUtils.isEmpty(gymfitPacks)) {
                continue;
            }

            List<String> productIds = gymfitPacks.stream().map(OfflineFitnessPack::getId).collect(Collectors.toList());
            UserEntry user = userContext.getUserEntryCompletableFuture().get();
            UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
            GymFitProductPricesResponse gymFitProductPricesResponse = (GymFitProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.GYMFIT_FITNESS_PRODUCT, userContext, productIds, cityId, String.valueOf(centerId)
            ).get();
            Map<String, ProductTaxBreakup> taxBreakupMap = null;
            if (isPartOfGstSplitSegment) {
                taxBreakupMap = interfaces.offerService.getTaxBreakUpForGymProducts(gymFitProductPricesResponse);
            }
            List<PackItem> packs = new ArrayList<PackItem>();

            List<PackOfferDetails> packOfferDetails = new ArrayList<PackOfferDetails>();
            int packCount = 0;

            //logic for remove 24month pack from top
            int distance = (int) gymfitPacks.stream().filter(pack -> pack.getProduct().getDurationInDays() >= 730).count();
            if (distance > 0) { // if 24 months pack exists in list
                Collections.rotate(gymfitPacks, distance * -1); // to rotate in reverse manner
            }
            for (AugmentedOfflineFitnessPack pack : gymfitPacks) {
                PackOfferDetails offerDetails = this.getOfferDetails(pack, gymFitProductPricesResponse);
                List<OfferMini> offerMinis = offerDetails.getOffers();

                Integer duration = pack.getProduct().getDurationInDays().intValue();
                Integer packDuration = duration;

                // Remove No cost emi offers.
                offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

                // Remove fit-club from offers as there is a separate widget
                List<OfferMini> filterOfferMinis = new ArrayList<OfferMini>();
                for (OfferMini offer : offerMinis) {
                    if (offer.getAddons() != null) {
                        filterOfferMinis.add(offer);
                        for (Addon addon : offer.getAddons()) {
                            if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.GYMFIT_EXTENSION
                                    && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                                packDuration = packDuration + addon.getConfig().getCount();
                                break;
                            }
                        }
                    } else if (offer.getDescription() != null) {
                        filterOfferMinis.add(offer);
                    }
                }

                offerDetails.setOffers(filterOfferMinis);
                packOfferDetails.add(offerDetails);

                long platformFeeValue = getPlatformFeeValue(pack.getAugments().getExtraCharges());

                Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                        ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();
                offerPrice += (!isPartOfGstSplitSegment ? platformFeeValue : 0);


                ProductTaxBreakup priceBreakup = null;
                if (isPartOfGstSplitSegment) {
                    priceBreakup = taxBreakupMap.get(pack.getId());
                    offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
                }


                Integer totalDuration = duration;

                String currency = offerDetails.getPrice().getCurrency();
                String cultPackCostPrice = offerDetails.getPrice().getMrp().compareTo(offerDetails.getPrice().getListingPrice()) != 0
                        ? String.valueOf(offerDetails.getPrice().getMrp().longValue() + (!isPartOfGstSplitSegment ? platformFeeValue : 0)) : null;
                double numberOfMonths = Math.floor((packDuration.doubleValue() / 30) * 2) / 2.0;
                String perMonthPriceWithoutDuration = "" + Math.round((Math.ceil(offerPrice.doubleValue()) / numberOfMonths));
                String perMonthPrice = perMonthPriceWithoutDuration + "/mo*";

                String currencyUnit = currency.equals("INR") ? OrderUtil.RUPEE_SYMBOL : currency;

                String[] PackTextGradientArray = Arrays.stream(PackTextGradient.class.getEnumConstants()).map(Enum::name).toArray(String[]::new);
                String textGradientVariant = PackTextGradientArray[packCount % PackTextGradientArray.length];

                long totalMonthsRounded = Math.round(Math.floor(totalDuration / 30));

                String extendedDurationString = null;
                String baseDurationString = null;
                String packName = pack.getTitle();
                if (packName.contains("+")) {
                    String[] splittedPackNames = packName.split("\\+");
                    baseDurationString = splittedPackNames[0].trim();
                    extendedDurationString = splittedPackNames[1].trim().split(" ")[0].trim();
                }
                String durationString = extendedDurationString != null ? baseDurationString + "+" + extendedDurationString
                        : "" + totalMonthsRounded;
                Boolean isDoubleDigit = extendedDurationString != null;
                String durationUnit = duration > 59 ? "MONTHS" : "MONTH";

                String actionUrl = ActionUtil.getPreCheckoutPageUrl(pack.getId());

                if (queryParams.containsKey("centerId") && queryParams.get("centerId") != null)
                    actionUrl += "&centerId=" + centerId;

                if (isWeb) {
                    actionUrl = ActionUtil.getPackDetailAction(pack.getProductType(), pack.getId(), null, null, null, UserAgent.DESKTOP);
                }
                Action action = new Action(actionUrl, "VIEW", ActionType.NAVIGATION);
                HashMap<String, String> meta = new HashMap<>();
                meta.put("selectedSku", "PRO SELECT");
                action.setMeta(meta);

                List<BenefitEntry> centerBenefit = pack.getProduct().getBenefits().stream()
                        .filter(benefit -> benefit.getName().equals(ACCESS_CREDITS))
                        .toList();

                int credits = 0;
                PackActionOffer packActionOffer = null;
                if(!centerBenefit.isEmpty()) {
                    credits = centerBenefit.get(0).getTickets();
                    HashMap<String, String> analyticsData = new HashMap<>();
                    analyticsData.put("packCredit", String.valueOf(credits));
                    analyticsData.put("pageId", "fl_pack_comparison");
                    Action offerAction =  SelectPacksUtils.getShowCreditInfoModalAction(analyticsData, true);
                    offerAction.setTitle(" " + credits + " Credit" + ((Objects.equals(credits, 1)) ? "" : "s") + "  ");
                    packActionOffer = new PackActionOffer(
                            offerAction, null, "to access other centers (up to " + credits + " sessions)", null);
                    action.setAnalyticsData(analyticsData);
                }

                CFTextData additionalPrice = getAdditionalPricetext(pack.getAugments().getExtraCharges(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);
                String additionalPriceWeb = getAdditionalPriceAmount(pack.getAugments().getExtraCharges(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);
                PackItem cultPackItem = new PackItem(
                        item.getProductType(),
                        cultPackCostPrice,
                        String.valueOf(offerPrice),
                        perMonthPrice,
                        perMonthPriceWithoutDuration,
                        currencyUnit,
                        textGradientVariant,
                        null,
                        packActionOffer != null? List.of(packActionOffer) : null,
                        action,
                        durationString,
                        durationUnit,
                        isDoubleDigit,
                        false,
                        null,
                        "normal",
                        null,
                        null,
                        false,
                        priceBreakup,
                        null,
                        null,
                        String.valueOf(numberOfMonths),
                        credits!=0 ? credits : null,
                        packDuration,
                        isPartOfGstSplitSegment ? additionalPrice : null,
                        additionalPriceWeb,
                        pack
                );

                packs.add(cultPackItem);
                packCount += 1;

            }

            if (CollectionUtils.isEmpty(packs)) {
                continue;
            }

            List<PackItem> finalPacks = this.removeCommonOffersAndAddOffers(userContext, packs, packOfferDetails, null, "PRO_SELECT");

            FitnessPackHeader header = item.getHeader();
            if (header == null)
                header = new FitnessPackHeader();

            if (queryParams.containsKey("packsTopPadding"))
                item.setTopSpacing(queryParams.get("packsTopPadding"));

            if (header.getHeaderSubTitle() == null || header.getHeaderSubTitle().isEmpty()) {
                header.setHeaderSubTitle(null);
            }
            if (header.getHeaderImage() == null || header.getHeaderImage().isEmpty()) {
                item.setSimplifyHeader(true);
                header.setHeaderImage(null);
            }

            Boolean hideHeaderFadeOffset = false;
            Double headerImageAspectRatio = 4.78;

            if (expandedMembershipPack == null) {
                this.expandedMembershipPack = new MembershipPackWidgetData(
                        finalPacks,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            } else {
                this.collapsedMembershipPack = new MembershipPackWidgetData(
                        finalPacks,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            }

            item.setPacks(packs);
        }

        if (this.items != null) {
            this.isV2 = true;
            return Collections.singletonList(this);
        }

        return null;
    }

    private List<BaseWidget> buildEliteSelectPackBrowseWidget(ServiceInterfaces interfaces, UserContext userContext, Map<String, String> queryParams, boolean isPartOfGstSplitSegment) throws Exception {

        long centerId = 403; //this is inactive center id
        if (queryParams.containsKey("centerId"))
            centerId = Long.parseLong(queryParams.get("centerId"));
        else {
            if (this.items != null) {
                this.isV2 = true;
                return Collections.singletonList(this);
            }
        }

        UserProfile userProfile = userContext.getUserProfile();
        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);

        CenterEntry center = SelectPacksUtils.getCenterDetail(centerId, interfaces);

        String cityId = userProfile.getCity().getCityId();
        Integer cultCityId = userProfile.getCity().getCultCityId();
        String centerCityId = center.getCity();
        if (Objects.nonNull(centerCityId)) {
            City centerCity = interfaces.cityCache.getCityById(centerCityId);
            if (Objects.nonNull(centerCity.getCityId()) && Objects.nonNull(centerCity.getCultCityId())) {
                cityId = centerCity.getCityId();
                cultCityId = centerCity.getCultCityId();
            }
        }
        List<AugmentedOfflineFitnessPack> packList = userContext.getRequestCache().getAugmentedPackList(
                ProductType.FITNESS, ProductSubType.GENERAL, false,
                userContext, Math.toIntExact(centerId)
        );

        packList.sort(Collections.reverseOrder(Comparator.comparing(pack -> pack.getProduct().getDurationInDays())));
        boolean isWeb = AppUtil.isWeb(userContext);
        for (FitnessPackCategoryItem item : this.items) {
            if (CollectionUtils.isEmpty(packList)) {
                continue;
            }
            //logic for remove 24month pack from top
            int distance = (int) packList.stream().filter(pack -> pack.getProduct().getDurationInDays() >= 730).count();
            if (distance > 0) { // if 24 months pack exists in list
                Collections.rotate(packList, distance * -1); // to rotate in reverse manner
            }
            List<String> productIds = packList.stream().map(pack -> pack.getId()).collect(Collectors.toList());
            CultProductPricesResponse cultPackProductPricesResponse;
            cultPackProductPricesResponse = (CultProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.FITNESS, userContext, productIds, cityId, String.valueOf(centerId)
            ).get();

            Map<String, ProductTaxBreakup> taxBreakupMap = null;
            if (isPartOfGstSplitSegment)
                taxBreakupMap = interfaces.offerService.getTaxBreakUpForCultProducts(cultPackProductPricesResponse);
            List<PackItem> packs = new ArrayList<>();

            List<PackOfferDetails> packOfferDetails = new ArrayList<>();
            int packCount = 0;

            HashMap<PackItem, Integer> validity = new HashMap<>();
            for (AugmentedOfflineFitnessPack pack : packList) {
                PackOfferDetails offerDetails = this.getOfferDetails(pack, cultPackProductPricesResponse);
                List<OfferMini> offerMinis = offerDetails.getOffers();

                Integer packDuration = pack.getProduct().getDurationInDays().intValue();

                // Remove No cost emi offers.
                offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

                List<OfferMini> filterOfferMinis = new ArrayList<>();
                for (OfferMini offer : offerMinis) {
                    if (offer.getAddons() != null) {
                        filterOfferMinis.add(offer);
                        for (Addon addon : offer.getAddons()) {
                            if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.CULT_EXTENSION
                                    && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                                packDuration = packDuration + addon.getConfig().getCount();
                                break;
                            }
                        }
                    } else if (offer.getDescription() != null) {
                        filterOfferMinis.add(offer);
                    }
                }

                offerDetails.setOffers(filterOfferMinis);
                packOfferDetails.add(offerDetails);

                long platformFeeValue = getPlatformFeeValue(pack.getAugments().getExtraCharges());

                Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                        ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();
                offerPrice += (!isPartOfGstSplitSegment ? platformFeeValue : 0);

                ProductTaxBreakup priceBreakup = null;
                if (isPartOfGstSplitSegment) {
                    priceBreakup = taxBreakupMap.get(pack.getId());
                    offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
                }

                String packCostPrice = offerDetails.getPrice().getMrp().compareTo(offerDetails.getPrice().getListingPrice()) != 0
                        ? String.valueOf(offerDetails.getPrice().getMrp().longValue() + (!isPartOfGstSplitSegment ? platformFeeValue : 0)) : null;
                double numberOfMonths = Math.floor((packDuration.doubleValue() / 30) * 2) / 2.0;
                String perMonthPriceWithoutDuration = "" + Math.round((Math.ceil(offerPrice.doubleValue()) / numberOfMonths));
                String perMonthPrice = perMonthPriceWithoutDuration + "/mo*";

                String currencyUnit = OrderUtil.RUPEE_SYMBOL;

                String[] PackTextGradientArray = Arrays.stream(PackTextGradient.class.getEnumConstants()).map(Enum::name).toArray(String[]::new);
                String textGradientVariant = PackTextGradientArray[packCount % PackTextGradientArray.length];

                long totalMonthsRounded = Math.round(Math.floor(pack.getProduct().getDurationInDays().longValue() / 30));

                String extendedDurationString = null;
                String baseDurationString = null;
                String packName = pack.getTitle();
                if (packName.contains("+")) {
                    String[] splittedPackNames = packName.split("\\+");
                    baseDurationString = splittedPackNames[0].trim();
                    extendedDurationString = splittedPackNames[1].trim().split(" ")[0].trim();
                }
                String durationString = extendedDurationString != null ? baseDurationString + "+" + extendedDurationString
                        : "" + totalMonthsRounded;
                Boolean isDoubleDigit = extendedDurationString != null;

                String durationUnit = (pack.getProduct().getDurationInDays().intValue() / 30) > 1 ? "MONTHS" : "MONTH";

                String url = ActionUtil.getPreCheckoutPageUrl(pack.getId());
                if (queryParams.containsKey("centerId") && queryParams.get("centerId") != null)
                    url += "&centerId=" + centerId;
                if (isWeb) {
                    Object gymCenter = center.getMeta().get(FitnessCenterUtil.GYMFIT_CENTER_ID_KEY);
                    Object cultCenter = center.getMeta().get(FitnessCenterUtil.CULT_CENTER_ID_KEY);
                    String cultCenterId = "";
                    if (cultCenter != null) {
                        cultCenterId = cultCenter.toString();
                        log.info("fitness pack browse widget: gymCenter " + cultCenterId);

                    } else if (gymCenter != null) {
                        cultCenterId = gymCenter.toString();
                        log.info("fitness pack browse widget: cultCenter " + cultCenterId);
                    }
                    queryParams.put("cultCenterId", cultCenterId);

                    String urlbuilder = "curefit://cultpack?";
                    StringBuilder strBuilder = new StringBuilder(urlbuilder);
                    strBuilder.append("productId=" + pack.getId() + "&");
                    strBuilder.append("centerServiceId=" + (queryParams.containsKey("cultCenterId") ? queryParams.get("cultCenterId") : "") + "&");
                    String finalUrl = strBuilder.toString();
                    url = finalUrl;
                }
                Action action = new Action(url, "VIEW", ActionType.NAVIGATION);
                HashMap<String, String> meta = new HashMap<>();
                meta.put("selectedSku", "ELITE SELECT");
                action.setMeta(meta);

                List<BenefitEntry> packBenefits =  pack.getProduct().getBenefits();
                Integer awayCredits = null;
                if(!CollectionUtils.isEmpty(packBenefits)) {
                    for(BenefitEntry benefit: packBenefits) {
                        if(benefit.getName().equals(ACCESS_CREDITS)) {
                            awayCredits = benefit.getTickets();
                        }
                    }
                }

                PackActionOffer packActionOffer = null;
                if(awayCredits != null && awayCredits > 0){
                    HashMap<String, String> analyticsData = new HashMap<>();
                    analyticsData.put("packCredit", String.valueOf(awayCredits));
                    analyticsData.put("pageId", "fl_pack_comparison");
                    Action offerAction =  SelectPacksUtils.getShowCreditInfoModalAction(analyticsData, false);
                    offerAction.setTitle(" "+awayCredits+" Credit" + ((Objects.equals(awayCredits, 1)) ? "" : "s")  + "  ");
                    packActionOffer = new PackActionOffer(
                            offerAction,null, "to access other centers (up to " + awayCredits + " sessions)",null);
                    action.setAnalyticsData(analyticsData);
                }

                CFTextData additionalPrice = getAdditionalPricetext(pack.getAugments().getExtraCharges(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);
                String additionalPriceWeb = getAdditionalPriceAmount(pack.getAugments().getExtraCharges(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);

                PackItem packItem = new PackItem(
                        item.getProductType(),
                        packCostPrice,
                        String.valueOf(offerPrice),
                        perMonthPrice,
                        perMonthPriceWithoutDuration,
                        currencyUnit,
                        textGradientVariant,
                        null,
                        packActionOffer != null ? List.of(packActionOffer) : null,
                        action,
                        durationString,
                        durationUnit,
                        isDoubleDigit,
                        false,
                        null,
                        "normal",
                        null,
                        null,
                        false,
                        priceBreakup,
                        null,
                        null,
                        String.valueOf(numberOfMonths),
                        awayCredits,
                        packDuration,
                        isPartOfGstSplitSegment ? additionalPrice : null,
                        additionalPriceWeb,
                        pack
                );
                validity.put(packItem, pack.getProduct().getDurationInDays().intValue());

                packs.add(packItem);
                packCount += 1;
            }


            if (CollectionUtils.isEmpty(packs)) {
                continue;
            }

            packs = this.removeCommonOffersAndAddOffers(userContext, packs, packOfferDetails, null, "ELITE_SELECT");

            for (Map.Entry<PackItem, Integer> entry : validity.entrySet()) {
                PackItem currentPack = entry.getKey();
                List<PackOffer> offers = new ArrayList<>();
                offers.addAll(currentPack.getOffers());
                currentPack.setOffers(offers);
            }

            FitnessPackHeader header = item.getHeader();
            if (header == null)
                header = new FitnessPackHeader();

            if (queryParams.containsKey("packsTopPadding"))
                item.setTopSpacing(queryParams.get("packsTopPadding"));

            if (header.getHeaderSubTitle() == null || header.getHeaderSubTitle().isEmpty()) {
                header.setHeaderSubTitle(null);
            }
            if (header.getHeaderImage() == null || header.getHeaderImage().isEmpty()) {
                item.setSimplifyHeader(true);
                header.setHeaderImage(null);
            }

            Boolean hideHeaderFadeOffset = false;
            Double headerImageAspectRatio = 4.78;

            if (expandedMembershipPack == null) {
                this.expandedMembershipPack = new MembershipPackWidgetData(
                        packs,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            } else {
                this.collapsedMembershipPack = new MembershipPackWidgetData(
                        packs,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            }

            item.setPacks(packs);
        }

        if (this.items != null) {
            this.isV2 = true;
            return Collections.singletonList(this);
        }

        return null;
    }

    private List<BaseWidget> buildPlayPackWidget(ServiceInterfaces interfaces, UserContext userContext, Map<String, String> queryParams, boolean isPartOfGstSplitSegment) throws Exception {
        UserProfile userProfile = userContext.getUserProfile();
        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
        boolean isWeb = AppUtil.isWeb(userContext);
        Long centerId = queryParams.get("centerId") != null ? Long.parseLong(queryParams.get("centerId")) : null;

        CenterEntry selectedCenter = null;
        if (centerId != null) {
            selectedCenter = interfaces.centerService.getCenterDetails(centerId, false, null, null).get();
        }

        for (FitnessPackCategoryItem item : this.items) {
            List<FitnessPack> playPacks = this.getPlayPacksBySKU(item.getProductType(), queryParams, userContext,
                    interfaces);
            if (playPacks == null || CollectionUtils.isEmpty(playPacks)) {
                continue;
            }

            List<String> productIds = playPacks.stream().map(pack -> pack.getProductId()).collect(Collectors.toList());
            PlayProductPricesResponse playProductPricesResponse = (PlayProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.PLAY, userContext, productIds, userProfile.getCity().getCityId(), null
            ).get();
            Map<String, ProductTaxBreakup> taxBreakupMap = null;
            if (isPartOfGstSplitSegment)
                taxBreakupMap = interfaces.offerService.getTaxBreakUpForPlayProducts(playProductPricesResponse);
            List<PackItem> packs = new ArrayList<>();

            List<PackOfferDetails> packOfferDetails = new ArrayList<>();
            int packCount = 0;

            HashMap<PackItem, Integer> validity = new HashMap<>();
            for (FitnessPack pack : playPacks) {
                PackOfferDetails offerDetails = this.getPlayOfferDetails(pack, playProductPricesResponse);
                List<OfferMini> offerMinis = offerDetails.getOffers();

                Integer packDuration = pack.getDuration();

                // Remove No cost emi offers.
                offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");


                List<OfferMini> filterOfferMinis = new ArrayList<>();
                for (OfferMini offer : offerMinis) {
                    if (offer.getAddons() != null) {
                        filterOfferMinis.add(offer);
                        for (Addon addon : offer.getAddons()) {
                            if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.CULT_PLAY_EXTENSION
                                    && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                                packDuration = packDuration + addon.getConfig().getCount();
                                break;
                            }
                        }
                    } else if (offer.getDescription() != null) {
                        filterOfferMinis.add(offer);
                    }
                }

                if (item.getProductType() == CultPackBrowseProductType.PLAY
                        && isPackNotSupportedDueToCenterTermination(packDuration, selectedCenter)) {
                    continue;
                }

                offerDetails.setOffers(filterOfferMinis);
                packOfferDetails.add(offerDetails);

                long offerPrice = offerDetails.getPrice().getListingPrice() != null
                        ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();

                ProductTaxBreakup priceBreakup = null;
                if (isPartOfGstSplitSegment) {
                    priceBreakup = taxBreakupMap.get(pack.getProductId());
                    offerPrice = (long) priceBreakup.getBasePrice();
                }

                String packCostPrice = offerDetails.getPrice().getMrp().compareTo(offerDetails.getPrice().getListingPrice()) != 0
                        ? String.valueOf(offerDetails.getPrice().getMrp().longValue()) : null;
                double numberOfMonths = Math.round(packDuration.doubleValue() * 0.0328767);
                String perMonthPriceWithoutDuration = "" + Math.round((Math.ceil((double) offerPrice) / numberOfMonths));
                String perMonthPrice = perMonthPriceWithoutDuration + "/mo*";

                String currencyUnit = OrderUtil.RUPEE_SYMBOL;

                String[] PackTextGradientArray = Arrays.stream(PackTextGradient.class.getEnumConstants()).map(Enum::name).toArray(String[]::new);
                String textGradientVariant = PackTextGradientArray[packCount % PackTextGradientArray.length];

                long totalMonthsRounded = Math.round(Math.floor(pack.getDuration() / 30));

                String extendedDurationString = null;
                String baseDurationString = null;
                String packName = pack.getTitle();
                if (packName.contains("+")) {
                    String[] splittedPackNames = packName.split("\\+");
                    baseDurationString = splittedPackNames[0].trim();
                    extendedDurationString = splittedPackNames[1].trim().split(" ")[0].trim();
                }
                String durationString = extendedDurationString != null ? baseDurationString + "+" + extendedDurationString
                        : "" + totalMonthsRounded;
                Boolean isDoubleDigit = extendedDurationString != null;

                String durationUnit = (pack.getDuration() / 30) > 1 ? "MONTHS" : "MONTH";

                Action action = getPlaypackPageAction(item.getProductType(), pack, queryParams, userContext, isWeb, true);

                CFTextData additionalPrice = getAdditionalPricetext(pack.getExtraCharges() != null ? pack.getExtraCharges() : new ArrayList<>(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);
                String additionalPriceWeb = getAdditionalPriceAmount(pack.getExtraCharges() != null ? pack.getExtraCharges() : new ArrayList<>(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);

                PackItem packItem = new PackItem(
                        item.getProductType(),
                        packCostPrice,
                        String.valueOf(offerPrice),
                        perMonthPrice,
                        perMonthPriceWithoutDuration,
                        currencyUnit,
                        textGradientVariant,
                        null,
                        null,
                        action,
                        durationString,
                        durationUnit,
                        isDoubleDigit,
                        false,
                        null,
                        "normal",
                        null,
                        null,
                        false,
                        priceBreakup,
                        null,
                        null,
                        String.valueOf(numberOfMonths),
                        null,
                        packDuration,
                        isPartOfGstSplitSegment ? additionalPrice : null,
                        additionalPriceWeb,
                        null
                );
                validity.put(packItem, pack.getNumDays());

                packs.add(packItem);
                packCount += 1;

            }


            if (CollectionUtils.isEmpty(packs)) {
                continue;
            }

            packs = this.removeCommonOffersAndAddOffers(userContext, packs, packOfferDetails, null, "PLAY");

            for (Map.Entry<PackItem, Integer> entry : validity.entrySet()) {
                PackItem currentPack = entry.getKey();
                Long validityDuration = Long.valueOf(entry.getValue());
                String unit = validityDuration < 30 ? " Days" : (Math.round(Math.floor(validityDuration / 30)) > 1 ? " Months" : " Month");
                validityDuration = validityDuration < 30 ? validityDuration : Math.round(Math.floor(validityDuration / 30));

                List<PackOffer> offers = new ArrayList<>();
//                offers.add(new PackOffer("Valid for "+ validityDuration + unit, null, null));
                offers.addAll(currentPack.getOffers());
                currentPack.setOffers(offers);
            }

            FitnessPackHeader header = item.getHeader();
            if (header.getHeaderTitle() == null || header.getHeaderTitle().isEmpty()) {
                header.setHeaderTitle(null);
            }
            if (header.getHeaderSubTitle() == null || header.getHeaderSubTitle().isEmpty()) {
                header.setHeaderSubTitle(null);
            }
            if (header.getHeaderImage() == null || header.getHeaderImage().isEmpty()) {
                item.setSimplifyHeader(true);
                header.setHeaderImage(null);
            }

            Boolean hideHeaderFadeOffset = false;
            Double headerImageAspectRatio = 4.78;

            if (expandedMembershipPack == null) {
                this.expandedMembershipPack = new MembershipPackWidgetData(
                        packs,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            } else {
                this.collapsedMembershipPack = new MembershipPackWidgetData(
                        packs,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            }

            item.setPacks(packs);
        }

        if (this.items != null) {
            this.isV2 = true;
            return Collections.singletonList(this);
        }

        return null;
    }

    private List<BaseWidget> buildGymPTPackBrowseWidget(ServiceInterfaces interfaces, UserContext userContext, Map<String, String> queryParams) throws Exception {
        if (!queryParams.containsKey("agentId") || !queryParams.containsKey("centerId") || !queryParams.containsKey("filterValue")) {

            SessionEntry sessionEntry = getLastAttendedPTSessionForUser(interfaces, userContext);
            if (sessionEntry == null) {
                return null;
            }
            Long trainerId = Long.parseLong(sessionEntry.getIdentityId());
            Long centerId = Long.parseLong(sessionEntry.getCenterId());

            AgentResponse trainerDetails = null;

            AgentSearchRequestParam requestParam = new AgentSearchRequestParam();
            requestParam.setIdentityId(trainerId);

            try {
                Page<AgentResponse> agentResponsePage = interfaces.ollivanderAgentClient.getAllAgentsPaginated(0, 10, requestParam);
                if (!agentResponsePage.isEmpty()) {
                    trainerDetails = agentResponsePage.getContent().get(0);
                    queryParams.put("agentId", trainerDetails.getId().toString());
                }
                String trainerLevel = "L5";
                if (trainerDetails.getResourceServiceMapping() != null) {
                    List<ResourceServiceMappingResponse> resourceServiceMappingResponses = trainerDetails.getResourceServiceMapping().stream().filter(serviceMapping -> serviceMapping.getSubServiceType() != null && serviceMapping.getSubServiceType().getGroupType().equals(com.curefit.cfapi.util.GymPtUtil.GYM_PT_SERVICE_GROUP_TYPE)).collect(Collectors.toList());
                    if (!org.apache.commons.collections.CollectionUtils.isEmpty(resourceServiceMappingResponses) && resourceServiceMappingResponses.get(0).getSubServiceTypeCode() != null) {
                        trainerLevel = getTrainerLevel(resourceServiceMappingResponses.get(0).getSubServiceTypeCode());
                        queryParams.put("filterValue", trainerLevel);
                    }
                }
                queryParams.put("centerId", centerId.toString());
            } catch (BaseException e) {
                log.info(" Fetch-details failed for trainer id " + trainerId);
            }
        }

        if (!queryParams.containsKey("agentId") || !queryParams.containsKey("centerId") || !queryParams.containsKey("filterValue")) {
            return null;
        }


        try {
            CenterEntry center = interfaces.centerService.getCenterDetails(Long.parseLong(queryParams.get("centerId")), false, null, null).get();
            String gymCenterId = center.getMeta().containsKey(GYMFIT_CENTER_ID_KEY) ? center.getMeta().get(GYMFIT_CENTER_ID_KEY).toString() : null;
            String lastSessionCityId = center.getCity();

            if (gymCenterId == null || lastSessionCityId == null) {
                return null;
            }

            SegmentSet<String> userPlatformSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                    RequestType.PLATFORM_SEGMENTS,
                    userContext).get();

            for (FitnessPackCategoryItem item : this.items) {
                List<GymfitPtProduct> gymPTPacks = interfaces.catalogueService.getGymfitPtPacksByCenter(gymCenterId, true, Listing.ListingType.PT_PACK);
                if (gymPTPacks.size() == 0) {
                    gymPTPacks.addAll(interfaces.catalogueService.getGymfitPtPacksByCity(lastSessionCityId, true, Listing.ListingType.PT_PACK));
                }

                gymPTPacks = gymPTPacks.stream().filter(ptProduct -> queryParams.containsKey("filterValue") &&
                        ptProduct.getProductCategory().endsWith(queryParams.get("filterValue"))).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(gymPTPacks)) {
                    continue;
                }
                //Collections.reverse(gymPTPacks);
                List<String> productIds = gymPTPacks.stream().map(pack -> pack.getProductId()).collect(Collectors.toList());
                UserEntry user = userContext.getUserEntryCompletableFuture().get();
                UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
                GymFitProductPricesResponse gymFitProductPricesResponse = (GymFitProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                        ProductType.GYMFIT_FITNESS_PRODUCT, userContext, productIds, lastSessionCityId, center != null ? center.getId().toString() : null
                ).get();

                List<PackItem> packs = new ArrayList<PackItem>();

                List<PackOfferDetails> packOfferDetails = new ArrayList<PackOfferDetails>();
                int packCount = 0;

                gymPTPacks.sort(Comparator.comparing(pack -> pack.getDurationInDays()));

                HashMap<PackItem, Integer> validity = new HashMap<>();
                for (GymfitPtProduct pack : gymPTPacks) {
                    PackOfferDetails offerDetails = this.getOfferDetails(pack, gymFitProductPricesResponse);
                    List<OfferMini> offerMinis = offerDetails.getOffers();

                    List<ListingCategoryRestriction> listingCategoryRestrictions = (List<ListingCategoryRestriction>) pack.getListingCategory().getRestrictions();
                    Integer numberOfSessions = listingCategoryRestrictions.get(0).getRestrictionCount();
                    Integer duration = numberOfSessions;

                    offerDetails.setOffers(offerMinis);
                    packOfferDetails.add(offerDetails);

                    Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                            ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();

                    String currency = offerDetails.getPrice().getCurrency();
                    String ptPackCostPrice = offerDetails.getPrice().getMrp().compareTo(offerDetails.getPrice().getListingPrice()) != 0
                            ? String.valueOf(offerDetails.getPrice().getMrp().longValue()) : null;

                    String perMonthPriceWithoutDuration = "" + Math.round(Math.ceil(offerPrice.doubleValue() / Math.floor(numberOfSessions)));
                    String perMonthPrice = perMonthPriceWithoutDuration + "/session";

                    String currencyUnit = currency.equals("INR") ? OrderUtil.RUPEE_SYMBOL : currency;

                    String[] PackTextGradientArray = Arrays.stream(PackTextGradient.class.getEnumConstants()).map(Enum::name).toArray(String[]::new);
                    String textGradientVariant = PackTextGradientArray[packCount % PackTextGradientArray.length];

                    long totalMonthsRounded = Math.round(Math.floor(numberOfSessions));
                    String durationString = totalMonthsRounded + "";
                    String durationUnit = duration > 1 ? "SESSIONS" : "SESSION";

                    var patientList = (List<PatientDetail>) userContext.getRequestCache().getRequestFuture(RequestType.GET_ALL_PATIENTS, userContext).get();
                    PatientDetail selfPatient = CollectionUtils.isNotEmpty(patientList) ? patientList.stream().filter(patientDetail -> patientDetail.getRelationship().toLowerCase().equals("self")).findFirst().orElse(null) : null;
                    Long patientId = selfPatient != null ? selfPatient.getId() : null;

                    String url = "curefit://pt_checkout";
                    String doctorId = queryParams.getOrDefault("agentId", null);
                    Long centerId = center.getId();
                    List<NameValuePair> nameValuePairs = new ArrayList<>();
                    nameValuePairs.add(new BasicNameValuePair("productId", pack.getProductId()));
                    nameValuePairs.add(new BasicNameValuePair("productCode", pack.getProductId()));
                    nameValuePairs.add(new BasicNameValuePair("subCategoryCode", "GYMFIT_PERSONAL_TRAINING"));
                    if (patientId != null) {
                        nameValuePairs.add(new BasicNameValuePair("patientId", patientId.toString()));
                    }
                    if (doctorId != null) {
                        nameValuePairs.add(new BasicNameValuePair("doctorId", doctorId));
                    }
                    if (centerId != null) {
                        nameValuePairs.add(new BasicNameValuePair("centerId", centerId.toString()));
                    }
                    url += ActionUtil.formatQueryParamWithQuerySeparator(nameValuePairs);
                    Action action = new Action(url, "VIEW", ActionType.NAVIGATION);

                    PackItem ptPackItem = new PackItem(
                            item.getProductType(),
                            ptPackCostPrice,
                            String.valueOf(offerPrice),
                            perMonthPrice,
                            perMonthPriceWithoutDuration,
                            currencyUnit,
                            textGradientVariant,
                            null,
                            null,
                            action,
                            durationString,
                            durationUnit,
                            false,
                            false,
                            null,
                            "normal",
                            null,
                            null,
                            false,
                            null,
                            null,
                            null,
                            durationString,
                            null,
                            null,
                            null,
                            null,
                            null
                    );
                    validity.put(ptPackItem, pack.getDurationInDays());

                    packs.add(ptPackItem);
                    packCount += 1;

                }
                if (CollectionUtils.isEmpty(packs)) {
                    continue;
                }

                packs = this.removeCommonOffersAndAddOffers(userContext, packs, packOfferDetails, null, "GYM_PT");

                for (Map.Entry<PackItem, Integer> entry : validity.entrySet()) {
                    PackItem currentPack = entry.getKey();
                    Long validityDuration = Long.valueOf(entry.getValue());
                    String unit = validityDuration < 30 ? " Days" : (Math.round(Math.floor(validityDuration / 30)) > 1 ? " Months" : " Month");
                    validityDuration = validityDuration < 30 ? validityDuration : Math.round(Math.floor(validityDuration / 30));

                    List<PackOffer> offers = new ArrayList<>();
                    offers.add(new PackOffer("Valid for " + validityDuration + unit, null, null));
                    offers.addAll(currentPack.getOffers());
                    currentPack.setOffers(offers);
                }

                FitnessPackHeader header = item.getHeader();
                Boolean hideHeaderFadeOffset = false;
                Double headerImageAspectRatio = 4.78;

                if (expandedMembershipPack == null) {
                    this.expandedMembershipPack = new MembershipPackWidgetData(
                            packs,
                            header.getHeaderTitle(),
                            header.getHeaderSubTitle(),
                            header.getHeaderOffers(),
                            header.getHeaderImage(),
                            headerImageAspectRatio,
                            hideHeaderFadeOffset,
                            null
                    );
                } else {
                    this.collapsedMembershipPack = new MembershipPackWidgetData(
                            packs,
                            header.getHeaderTitle(),
                            header.getHeaderSubTitle(),
                            header.getHeaderOffers(),
                            header.getHeaderImage(),
                            headerImageAspectRatio,
                            hideHeaderFadeOffset,
                            null
                    );
                }

                item.setPacks(packs);
            }
        } catch (BaseException err) {
            log.info(" Fetch-details failed for center");
        }
        if (this.items != null) {
            this.isV2 = true;
            return Collections.singletonList(this);
        }

        return null;
    }

    private List<BaseWidget> buildLuxPackBrowseWidget(ServiceInterfaces interfaces, UserContext userContext, Map<String, String> queryParams, boolean isPartOfGstSplitSegment) throws Exception {
        UserProfile userProfile = userContext.getUserProfile();
        String userId = userProfile.getUserId();
        String centerId = queryParams.getOrDefault("gymCenterId", null);
        String centerServiceIdParam = queryParams.getOrDefault("centerServiceId", null);
        String cityId = userProfile.getCity().getCityId();
        Integer cultCityId = userProfile.getCity().getCultCityId();
        String centerServiceId = null;

        // Priority one: if already have centerServiceId then use it
        if (Objects.nonNull(centerServiceIdParam)) {
            CenterEntry center = interfaces.centerService.getCenterDetails(Long.parseLong(centerServiceIdParam), false, null, null).get();
            CenterEntry linkedCenter = null;
            if (CultUtil.isHybridCultCenter(center)) {
                Long linkedCenterServiceId = center.getLinkedCenterId();
                linkedCenter = interfaces.centerService.getCenterDetails(linkedCenterServiceId, false, null, null).get();
            } else linkedCenter = center;

            if (Objects.nonNull(linkedCenter)) {
                centerId = linkedCenter.getMeta().containsKey(GymUtil.GYMFIT_CENTER_ID_KEY) ? linkedCenter.getMeta().get(GymUtil.GYMFIT_CENTER_ID_KEY).toString() : null;
                centerServiceId = String.valueOf(linkedCenter.getId());
                String centerCityId = linkedCenter.getCity();
                if (Objects.nonNull(centerCityId)) {
                    City centerCity = interfaces.cityCache.getCityById(centerCityId);
                    if (Objects.nonNull(centerCity.getCityId()) && Objects.nonNull(centerCity.getCultCityId())) {
                        cityId = centerCity.getCityId();
                        cultCityId = centerCity.getCultCityId();
                    }
                }
            }
        }

        if (centerId == null) {
            return Collections.singletonList(this);
        }

        // Priority two: if no centerServiceId is there we'll try to find center details from gymfitCenterId
        CenterByExternalIdResponse centerResponse = centerServiceId != null ? null : interfaces.centerService.getCentersByExternalIds(List.of(Long.valueOf(centerId)), null, null, null).get();
        if (centerResponse != null && centerResponse.getGyms() != null && !centerResponse.getGyms().isEmpty()) {
            CenterWithExternalId externalId = null;
            Optional<CenterWithExternalId> externalOptionalId = centerResponse.getGyms().stream().filter(item -> item.getVertical().equals(CenterVertical.GYMFIT)).findFirst();
            if (externalOptionalId.isPresent()) {
                externalId = externalOptionalId.get();
                centerServiceId = externalId.getId().toString();
                String centerCityId = externalId.getCity();
                if (Objects.nonNull(centerCityId)) {
                    City centerCity = interfaces.cityCache.getCityById(centerCityId);
                    if (Objects.nonNull(centerCity.getCityId()) && Objects.nonNull(centerCity.getCultCityId())) {
                        cityId = centerCity.getCityId();
                        cultCityId = centerCity.getCultCityId();
                    }
                }
            }
        }

        // Priority three: for fail same if centerservice fails then call gymfit to get gymfit center then call centerService again to get center details
        if (Objects.isNull(centerServiceId)) {
            Collection<Center> gymfitCenterList = interfaces.gymfitClient.centerService().getCenters(List.of(centerId), false);
            Center gymfitCenter = (Center) gymfitCenterList.toArray()[0];
            centerServiceId = String.valueOf(gymfitCenter.getCenterServiceId());
            CenterEntry center = interfaces.centerService.getCenterDetails(gymfitCenter.getCenterServiceId(), false, null, null).get();
            String centerCityId = center.getCity();
            if (Objects.nonNull(centerCityId)) {
                City centerCity = interfaces.cityCache.getCityById(centerCityId);
                if (Objects.nonNull(centerCity.getCityId()) && Objects.nonNull(centerCity.getCultCityId())) {
                    cityId = centerCity.getCityId();
                    cultCityId = centerCity.getCultCityId();
                }
            }
        }

        for (FitnessPackCategoryItem item : this.items) {
            List<OfflineFitnessPack> luxPacks = userContext.getRequestCache().getNonAugmentedPackList(
                ProductType.LUX_FITNESS_PRODUCT, null, false,
                userContext, Integer.valueOf(centerServiceId)
            );
            log.info("LuxLog: buildLuxPackBrowseWidget luxPacks {}", luxPacks);
            if (CollectionUtils.isEmpty(luxPacks)) {
                continue;
            }

            List<String> productIds = luxPacks.stream().map(OfflineFitnessPack::getId).collect(Collectors.toList());
            log.info("LuxLog: buildLuxPackBrowseWidget productIds {}", productIds);
            UserEntry user = userContext.getUserEntryCompletableFuture().get();
            UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
            GymFitProductPricesResponse gymFitProductPricesResponse = (GymFitProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                ProductType.GYMFIT_FITNESS_PRODUCT, userContext, productIds, cityId, centerServiceId
            ).get();
            Map<String, ProductTaxBreakup> taxBreakupMap = null;
            if (isPartOfGstSplitSegment) {
                taxBreakupMap = interfaces.offerService.getTaxBreakUpForGymProducts(gymFitProductPricesResponse);
            }
            List<PackItem> packs = new ArrayList<PackItem>();

            List<PackOfferDetails> packOfferDetails = new ArrayList<PackOfferDetails>();
            int packCount = 0;

            // sort packs in reverse order
            luxPacks.sort(Collections.reverseOrder(Comparator.comparing(pack -> pack.getProduct().getDurationInDays())));

            // TODO: remove this hack once sorting logic is supported from gymfit portal
            //  hack for showing 24 or greater months pack at last (730 days = 24 months)
            int distance = (int) luxPacks.stream().filter(pack -> pack.getProduct().getDurationInDays() >= 730).count();
            if (distance > 0) { // if 24 months pack exists in list
                Collections.rotate(luxPacks, distance * -1); // to rotate in reverse manner
            }
            List<String> conditionalOffers = new ArrayList<>();
            for (OfflineFitnessPack pack : luxPacks) {
                log.info("LuxLog: buildLuxPackBrowseWidget pack {}", pack);
                PackOfferDetails offerDetails = this.getOfferDetails(pack, gymFitProductPricesResponse);
                List<OfferMini> offerMinis = offerDetails.getOffers();

                Double duration = pack.getProduct().getDurationInDays();
                Double packDuration = duration;

                // Remove No cost emi offers.
                offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

                // Remove fit-club from offers as there is a separate widget
                List<OfferMini> filterOfferMinis = new ArrayList<OfferMini>();
                for (OfferMini offer : offerMinis) {
                    if (offer.getAddons() != null) {
                        filterOfferMinis.add(offer);
                        for (Addon addon : offer.getAddons()) {
                            if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.GYMFIT_EXTENSION
                                    && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                                packDuration = packDuration + addon.getConfig().getCount();
                                break;
                            }
                        }
                    } else if (offer.getDescription() != null) {
                        filterOfferMinis.add(offer);
                    }
                }

                offerDetails.setOffers(filterOfferMinis);
                packOfferDetails.add(offerDetails);

                Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                        ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();


                ProductTaxBreakup priceBreakup = null;
                if (isPartOfGstSplitSegment) {
                    priceBreakup = taxBreakupMap.get(pack.getId());
                    offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
                }


                Double totalDuration = duration;

                String currency = offerDetails.getPrice().getCurrency();
                String cultPackCostPrice = offerDetails.getPrice().getMrp().compareTo(offerDetails.getPrice().getListingPrice()) != 0
                        ? String.valueOf(offerDetails.getPrice().getMrp().longValue()) : null;
                double numberOfMonths = Math.floor((packDuration / 30) * 2) / 2.0;
                String perMonthPriceWithoutDuration = "" + Math.round((Math.ceil(offerPrice.doubleValue()) / numberOfMonths));
                String perMonthPrice = perMonthPriceWithoutDuration + "/mo"; // TODO - revert

                String currencyUnit = currency.equals("INR") ? OrderUtil.RUPEE_SYMBOL : currency;

                String[] PackTextGradientArray = Arrays.stream(PackTextGradient.class.getEnumConstants()).map(Enum::name).toArray(String[]::new);
                String textGradientVariant = PackTextGradientArray[packCount % PackTextGradientArray.length];

                long totalMonthsRounded = Math.round(Math.floor(totalDuration / 30));

                // hack for showing duration of 6+6 months pack as 6+6 .
                // TODO: remove this once proper solution is integrated
                String extendedDurationString = null;
                String baseDurationString = null;
                String packName = pack.getProduct().getTitle();
                if (packName.contains("+")) {
                    String[] splittedPackNames = packName.split("\\+");
                    baseDurationString = splittedPackNames[0].trim();
                    extendedDurationString = splittedPackNames[1].trim().split(" ")[0].trim();
                }
                String durationString = extendedDurationString != null ? baseDurationString + "+" + extendedDurationString
                        : "" + totalMonthsRounded;
                Boolean isDoubleDigit = extendedDurationString != null;
                String durationUnit = duration > 59 ? "MONTHS" : "MONTH";

                String actionUrl = "curefit://lux_checkout?productId=" + pack.getId();
                log.info("LuxLog: buildLuxPackBrowseWidget actionUrl {}", actionUrl);
                Action action = new Action(actionUrl, "VIEW", ActionType.NAVIGATION);

                // lux_gx benefit
                Optional<BenefitEntry> luxGxBenefitOptional = pack.getProduct().getBenefits().stream().filter(benefitEntry -> benefitEntry.getName().equalsIgnoreCase("lux_gx")).findFirst();
                BenefitEntry luxGxBenefit = null;
                if (luxGxBenefitOptional.isPresent()) luxGxBenefit = luxGxBenefitOptional.get();
                if (Objects.nonNull(luxGxBenefit)) {
                    String validityString = "Valid for " + durationString + " " + durationUnit;
                    if (packDuration < 180) {
                        validityString = "Valid for " + packDuration.intValue() + " " + (packDuration.intValue() > 1 ? "days" : "day");
                    }
                    conditionalOffers.add(validityString);
                    Integer totalTickets = luxGxBenefit.getTickets();
                    Boolean isUnlimitedBenefit = (totalTickets > 100 || (totalTickets > 30 && luxGxBenefit.getType().equals(BenefitType.MONTHLY)));
                    durationUnit = totalTickets == 1 ? "SESSION" : "SESSIONS";
                    if (!isUnlimitedBenefit) {
                        durationString = String.valueOf(totalTickets);
                        perMonthPriceWithoutDuration = "" + Math.round((Math.ceil(offerPrice.doubleValue()) / totalTickets));
                        perMonthPrice = perMonthPriceWithoutDuration + "/session";
                    } else {
                        durationString = "∞";
                        perMonthPrice = null;
                        perMonthPriceWithoutDuration = null;
                    }
                } else {
                    // cult benefit -> lux + Elite
                    Optional<BenefitEntry> cultBenefitOptional = pack.getProduct().getBenefits().stream().filter(benefitEntry -> benefitEntry.getName().equalsIgnoreCase("cult")).findFirst();
                    BenefitEntry cultBenefit = null;
                    if (cultBenefitOptional.isPresent()) cultBenefit = cultBenefitOptional.get();
                    if (Objects.isNull(cultBenefit)) {
                        conditionalOffers.add("Get Unlimited access to this center");
                    } else {
                        conditionalOffers.add(null);
                    }
                }


                PackItem cultPackItem = new PackItem(
                        item.getProductType(),
                        cultPackCostPrice,
                        String.valueOf(offerPrice),
                        perMonthPrice,
                        perMonthPriceWithoutDuration,
                        currencyUnit,
                        textGradientVariant,
                        null,
                        null,
                        action,
                        durationString,
                        durationUnit,
                        isDoubleDigit,
                        false,
                        null,
                        "normal",
                        null,
                        null,
                        false,
                        priceBreakup,
                        null,
                        null,
                        String.valueOf(numberOfMonths),
                        null,
                        packDuration.intValue(),
                        null,
                        null,
                        null
                );

                packs.add(cultPackItem);
                packCount += 1;

            }

            if (CollectionUtils.isEmpty(packs)) {
                continue;
            }

            List<PackItem> finalPacks = this.removeCommonOffersAndAddOffers(userContext, packs, packOfferDetails, conditionalOffers, "LUX");

            FitnessPackHeader header = item.getHeader();
            Boolean hideHeaderFadeOffset = header.getImageSize() == HeaderImageSize.LARGE;
            Double headerImageAspectRatio = header.getImageSize() == HeaderImageSize.LARGE ? 1.98 : 4.78;
            item.setSimplifyHeader(true);

            if (expandedMembershipPack == null) {
                this.expandedMembershipPack = new MembershipPackWidgetData(
                        finalPacks,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            } else {
                this.collapsedMembershipPack = new MembershipPackWidgetData(
                        finalPacks,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            }

            item.setPacks(packs);
        }

        if (this.items != null) {
            this.isV2 = true;
            return Collections.singletonList(this);
        }

        return null;
    }


    private List<BaseWidget> buildGymFitnessPackBrowseWidget(ServiceInterfaces interfaces, UserContext userContext, Map<String, String> queryParams, boolean isPartOfGstSplitSegment, ProductSubType productSubType) throws Exception {
        UserProfile userProfile = userContext.getUserProfile();
        String userId = userProfile.getUserId();
        boolean isWeb = AppUtil.isWeb(userContext);

        String cityId = userProfile.getCity().getCityId();
        Integer cultCityId = userProfile.getCity().getCultCityId();
        CenterEntry center;
        if (queryParams.containsKey("centerId") && queryParams.get("centerId") != null) {
            Long id = Long.valueOf(queryParams.get("centerId"));
            center = interfaces.centerService.getCenterDetails(id, false, null, null).get();
            String centerCityId = center.getCity();
            if (Objects.nonNull(centerCityId)) {
                City centerCity = interfaces.cityCache.getCityById(centerCityId);
                if (Objects.nonNull(centerCity.getCityId()) && Objects.nonNull(centerCity.getCultCityId())) {
                    cityId = centerCity.getCityId();
                    cultCityId = centerCity.getCultCityId();
                }
            }
        } else {
            center = null;
        }
        for (FitnessPackCategoryItem item : this.items) {
            List<AugmentedOfflineFitnessPack> gymfitPacks = new ArrayList<>();
            gymfitPacks = userContext.getRequestCache().getAugmentedPackList(ProductType.GYMFIT_FITNESS_PRODUCT, productSubType, false, userContext, null);

            gymfitPacks.sort(Collections.reverseOrder(Comparator.comparing(pack -> pack.getProduct().getDurationInDays())));
            if (CollectionUtils.isEmpty(gymfitPacks)) {
                continue;
            }
            String centerServiceId = (Objects.nonNull(center)) ? center.getId().toString() : null;

            List<String> productIds = gymfitPacks.stream().map(pack -> pack.getId()).collect(Collectors.toList());
            UserEntry user = userContext.getUserEntryCompletableFuture().get();
            UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);

            GymFitProductPricesResponse gymFitProductPricesResponse = (GymFitProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                ProductType.GYMFIT_FITNESS_PRODUCT, userContext, productIds, cityId, centerServiceId
            ).get();
            Map<String, ProductTaxBreakup> taxBreakupMap = null;
            if (isPartOfGstSplitSegment) {
                taxBreakupMap  = interfaces.offerService.getTaxBreakUpForGymProducts(gymFitProductPricesResponse);
            }
            List<PackItem> packs = new ArrayList<PackItem>();

            List<PackOfferDetails> packOfferDetails = new ArrayList<PackOfferDetails>();
            int packCount = 0;

            // sort packs in reverse order
            gymfitPacks.sort(Collections.reverseOrder(Comparator.comparing(pack -> pack.getProduct().getDurationInDays())));

            // TODO: remove this hack once sorting logic is supported from gymfit portal
            //  hack for showing 24 or greater months pack at last (730 days = 24 months)
            int distance = (int) gymfitPacks.stream().filter(pack -> pack.getProduct().getDurationInDays() >= 730).count();
            if (distance > 0) { // if 24 months pack exists in list
                Collections.rotate(gymfitPacks, distance * -1); // to rotate in reverse manner
            }
            boolean isNewActionUrlForPackPageSupported = AppUtil.newActionUrlForPackPageSupported(userContext);
            for (AugmentedOfflineFitnessPack pack : gymfitPacks) {
                PackOfferDetails offerDetails = this.getOfferDetails(pack, gymFitProductPricesResponse);
                List<OfferMini> offerMinis = offerDetails.getOffers();

                Integer duration = pack.getProduct().getDurationInDays().intValue();
                Integer packDuration = duration;

                // Remove No cost emi offers.
                offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

                // Remove fit-club from offers as there is a separate widget
                List<OfferMini> filterOfferMinis = new ArrayList<OfferMini>();
                for (OfferMini offer : offerMinis) {
                    if (offer.getAddons() != null) {
                        filterOfferMinis.add(offer);
                        for (Addon addon : offer.getAddons()) {
                            if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.GYMFIT_EXTENSION
                                    && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                                packDuration = packDuration + addon.getConfig().getCount();
                                break;
                            }
                        }
                    } else if (offer.getDescription() != null) {
                        filterOfferMinis.add(offer);
                    }
                }

                offerDetails.setOffers(filterOfferMinis);
                packOfferDetails.add(offerDetails);

                long platformFeeValue = getPlatformFeeValue(pack.getAugments().getExtraCharges());

                Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                        ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();
                offerPrice += (!isPartOfGstSplitSegment ? platformFeeValue : 0);


                ProductTaxBreakup priceBreakup = null;
                if (isPartOfGstSplitSegment) {
                    priceBreakup = taxBreakupMap.get(pack.getId());
                    offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
                }


                Integer totalDuration = duration;

                String currency = offerDetails.getPrice().getCurrency();
                String cultPackCostPrice = offerDetails.getPrice().getMrp().compareTo(offerDetails.getPrice().getListingPrice()) != 0
                        ? String.valueOf(offerDetails.getPrice().getMrp().longValue() + (!isPartOfGstSplitSegment ? platformFeeValue : 0)) : null;
                double numberOfMonths = Math.floor((packDuration.doubleValue() / 30) * 2) / 2.0;
                String perMonthPriceWithoutDuration = "" + Math.round((Math.ceil(offerPrice.doubleValue()) / numberOfMonths));
                String perMonthPrice = perMonthPriceWithoutDuration + "/mo*";

                String currencyUnit = currency.equals("INR") ? OrderUtil.RUPEE_SYMBOL : currency;

                String[] PackTextGradientArray = Arrays.stream(PackTextGradient.class.getEnumConstants()).map(Enum::name).toArray(String[]::new);
                String textGradientVariant = PackTextGradientArray[packCount % PackTextGradientArray.length];

                long totalMonthsRounded = Math.round(Math.floor(totalDuration / 30));

                // hack for showing duration of 6+6 months pack as 6+6 .
                // TODO: remove this once proper solution is integrated
                String extendedDurationString = null;
                String baseDurationString = null;
                String packName = pack.getTitle();
                if (packName.contains("+")) {
                    String[] splittedPackNames = packName.split("\\+");
                    baseDurationString = splittedPackNames[0].trim();
                    extendedDurationString = splittedPackNames[1].trim().split(" ")[0].trim();
                }
                String durationString = extendedDurationString != null ? baseDurationString + "+" + extendedDurationString
                        : "" + totalMonthsRounded;
                Boolean isDoubleDigit = extendedDurationString != null;
                String durationUnit = duration > 59 ? "MONTHS" : "MONTH";

                String actionUrl = null;
                if (isNewActionUrlForPackPageSupported) {
                    actionUrl = getPacksPageAction(pack.getId());
                } else {
                    actionUrl = ActionUtil.getPackDetailAction(pack.getProductType(), pack.getId(), null, null, null, userContext.getSessionInfo().getUserAgent());
                }
                if (Objects.nonNull(center) && !isWeb) {
                    Instant instant = Instant.ofEpochMilli(center.getLaunchDate().getTime());
                    LocalDateTime launchDate = LocalDateTime.ofInstant(instant, ZoneId.of(userContext.getUserProfile().getTimezone()));
                    if (center.getLaunchDate() != null && launchDate.isBefore(TimeUtil.getDateNow(DEFAULT_ZONE_ID))) {
                        actionUrl += "&centerId=" + queryParams.get("centerId");
                    }
                }
                Action action = new Action(actionUrl, "VIEW", ActionType.NAVIGATION);
                HashMap<String, String> meta = new HashMap<>();
                meta.put("selectedSku", "PRO");
                action.setMeta(meta);
                List<BenefitEntry> centerBenefit = pack.getProduct().getBenefits().stream()
                        .filter(benefit -> benefit.getName().equals(ACCESS_CREDITS))
                        .toList();

                int credits = 0;
                PackActionOffer packActionOffer = null;
                if(!centerBenefit.isEmpty()) {
                    credits = centerBenefit.get(0).getTickets();
                    HashMap<String, String> analyticsData = new HashMap<>();
                    analyticsData.put("packCredit", String.valueOf(credits));
                    analyticsData.put("pageId", "fl_pack_comparison");
                    Action offerAction =  SelectPacksUtils.getShowCreditInfoModalAction(analyticsData, true);
                    offerAction.setTitle(" " + credits + " Credit" + ((Objects.equals(credits, 1)) ? "" : "s")  + "  ");
                    packActionOffer = new PackActionOffer(
                            offerAction, null, "to access other Elite centers (up to " + credits + " sessions)", null);
                    action.setAnalyticsData(analyticsData);
                }

                CFTextData additionalPrice = getAdditionalPricetext(pack.getAugments().getExtraCharges(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);
                String additionalPriceWeb = getAdditionalPriceAmount(pack.getAugments().getExtraCharges(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);

                PackItem cultPackItem = new PackItem(
                        item.getProductType(),
                        cultPackCostPrice,
                        String.valueOf(offerPrice),
                        perMonthPrice,
                        perMonthPriceWithoutDuration,
                        currencyUnit,
                        textGradientVariant,
                        null,
                        (packActionOffer != null && productSubType.equals(ProductSubType.GENERAL)) ? Collections.singletonList(packActionOffer) : null,
                        action,
                        durationString,
                        durationUnit,
                        isDoubleDigit,
                        false,
                        null,
                        "normal",
                        null,
                        null,
                        false,
                        priceBreakup,
                        null,
                        null,
                        String.valueOf(numberOfMonths),
                        credits!=0 ? credits : null,
                        packDuration,
                        isPartOfGstSplitSegment ? additionalPrice : null,
                        additionalPriceWeb,
                        pack
                );

                packs.add(cultPackItem);
                packCount += 1;

            }

            if (CollectionUtils.isEmpty(packs)) {
                continue;
            }

            List<PackItem> finalPacks = this.removeCommonOffersAndAddOffers(userContext, packs, packOfferDetails, null, productSubType.equals(ProductSubType.PLUS) ? "PRO_PLUS" : "PRO");

            FitnessPackHeader header = item.getHeader() != null ? item.getHeader() : new FitnessPackHeader();
            Boolean hideHeaderFadeOffset = header.getImageSize() == HeaderImageSize.LARGE;
            Double headerImageAspectRatio = header.getImageSize() == HeaderImageSize.LARGE ? 1.98 : 4.78;

            if (queryParams.containsKey("packsTopPadding"))
                item.setTopSpacing(queryParams.get("packsTopPadding"));

            if (expandedMembershipPack == null) {
                this.expandedMembershipPack = new MembershipPackWidgetData(
                        finalPacks,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            } else {
                this.collapsedMembershipPack = new MembershipPackWidgetData(
                        finalPacks,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            }

            item.setPacks(packs);
        }

        if (this.items != null) {
            this.isV2 = true;
            return Collections.singletonList(this);
        }

        return null;
    }

    private List<BaseWidget> buildCultFitnessPackBrowseWidget(ServiceInterfaces interfaces, UserContext userContext, Map<String, String> queryParams, boolean isPartOfGstSplitSegment, ProductSubType productSubType) throws Exception {
        UserProfile userProfile = userContext.getUserProfile();
        SessionInfo sessionInfo = userContext.getSessionInfo();
        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
        String callSource = AppUtil.callSource(sessionInfo.getApiKey(), interfaces.apiKeyService);
        String cityId = userProfile.getCity().getCityId();
        Integer cultCityId = userProfile.getCity().getCultCityId();
        CenterEntry center;
        String centerServiceId = null;
        if (queryParams.containsKey("centerId") && queryParams.get("centerId") != null) {
            centerServiceId = queryParams.get("centerId");
            Long id = Long.valueOf(queryParams.get("centerId"));
            center = interfaces.centerService.getCenterDetails(id, false, null, null).get();
            String centerCityId = center.getCity();
            if (Objects.nonNull(centerCityId)) {
                City centerCity = interfaces.cityCache.getCityById(centerCityId);
                if (Objects.nonNull(centerCity.getCityId()) && Objects.nonNull(centerCity.getCultCityId())) {
                    cityId = centerCity.getCityId();
                    cultCityId = centerCity.getCultCityId();
                }
            }
        } else {
            center = null;
        }

        // fetch and filter fitness packs here
        List<AugmentedOfflineFitnessPack> fitnessPacks = this.getFitnessPackPromise( userContext, interfaces,
                (Objects.nonNull(center) && Objects.nonNull(center.getCity())) ? center.getCity() : userContext.getUserProfile().getCity().getCityId(), productSubType);
        if (CollectionUtils.isEmpty(fitnessPacks)) {
            return null;
        }

        List<String> productIds = fitnessPacks.stream().map(OfflineFitnessPack::getId).toList();

        // get offers and prices
        CultProductPricesResponse cultPackProductPricesResponse = (CultProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                ProductType.FITNESS, userContext, productIds, cityId, centerServiceId
        ).get();
        List<CompletableFuture<FitnessPackCategoryItem>> fitnessCategoryItemPromises = new ArrayList<>();
        Map<String, ProductTaxBreakup> taxBreakupMap;
        if (isPartOfGstSplitSegment) {
            taxBreakupMap = interfaces.offerService.getTaxBreakUpForCultProducts(cultPackProductPricesResponse);
        } else {
            taxBreakupMap = null;
        }

        for (FitnessPackCategoryItem item : this.items) {
            String finalCityId = cityId;
            if (item.getProductType() == CultPackBrowseProductType.CULT_SELECT) {
                continue;
            }
            fitnessCategoryItemPromises.add(CompletableFuture.supplyAsync(() -> {
                try {
                    return this.buildCultFitnessPackCategoryItem(
                        item, interfaces, userContext, cultPackProductPricesResponse, queryParams,
                        taxBreakupMap, isPartOfGstSplitSegment, center, finalCityId, fitnessPacks, productSubType
                    );
                } catch (Exception e) {
                    interfaces.exceptionReportingService.reportException("Error while building cult pack widget item ", e);
                    return null;
                }
            }, interfaces.getTaskExecutor()));
        }
        CompletableFuture<List<FitnessPackCategoryItem>> fitnessPackCategoryItemsPromise = FutureUtil.allOf(fitnessCategoryItemPromises);
        this.items = fitnessPackCategoryItemsPromise.get().stream().filter(Objects::nonNull).collect(Collectors.toList());

        if (this.items != null) {
            this.isV2 = true;
            return Collections.singletonList(this);
        }
        return null;
    }

    private FitnessPackCategoryItem buildCultFitnessPackCategoryItem(
        FitnessPackCategoryItem item, ServiceInterfaces interfaces, UserContext userContext,
        CultProductPricesResponse cultPackProductPricesResponse, Map<String, String> queryParams,
        Map<String, ProductTaxBreakup> taxBreakupMap, boolean isPartOfGstSplitSegment, CenterEntry center,
        String cityId, List<AugmentedOfflineFitnessPack> packProducts, ProductSubType productSubType
    ) throws Exception {
        CultPackBrowseProductType productType = item.getProductType();

        List<PackItem> packs = new ArrayList<PackItem>();
        List<String> conditionalOffers = new ArrayList<>();

        List<PackOfferDetails> packOfferDetails = new ArrayList<PackOfferDetails>();
        int packCount = 0;
        Boolean isCenterLevelprisingWeb = AppUtil.isCenterLevelPricingSupportedWeb(interfaces, interfaces.getEnvironmentService(), userContext);
        Boolean isWeb = AppUtil.isWeb(userContext);
        Boolean isNewActionUrlForPackPageSupported = AppUtil.newActionUrlForPackPageSupported(userContext);

        for (AugmentedOfflineFitnessPack pack : packProducts) {
            PackOfferDetails offerDetails = this.getOfferDetails(pack, cultPackProductPricesResponse);
            List<OfferMini> offerMinis = offerDetails.getOffers();
            Integer packDuration = pack.getProduct().getDurationInDays().intValue();
            // Remove No cost emi offers.
            offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

            // Remove fit-club from offers as there is a separate widget
            List<OfferMini> filterOfferMinis = new ArrayList<OfferMini>();
            for (OfferMini offer : offerMinis) {
                if (offer.getAddons() != null) {
                    filterOfferMinis.add(offer);
                    for (Addon addon : offer.getAddons()) {
                        if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.CULT_EXTENSION
                                && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                            packDuration = packDuration + addon.getConfig().getCount();
                            break;
                        }
                    }
                } else if (offer.getDescription() != null) {
                    filterOfferMinis.add(offer);
                }
            }

            offerDetails.setOffers(filterOfferMinis);
            packOfferDetails.add(offerDetails);

            long platformFeeValue = getPlatformFeeValue(pack.getAugments().getExtraCharges());

            Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                    ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();
            offerPrice += (!isPartOfGstSplitSegment ? platformFeeValue : 0);

            ProductTaxBreakup priceBreakup = null;
            if (isPartOfGstSplitSegment) {
                priceBreakup = taxBreakupMap.get(pack.getProductId());
                offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
            }

            Integer totalDuration = pack.getProduct().getDurationInDays().intValue();

            String currency = offerDetails.getPrice().getCurrency();

            // Data members of PackItem;
            String cultPackCostPrice = offerDetails.getPrice().getMrp().compareTo(offerDetails.getPrice().getListingPrice()) != 0
                    ? String.valueOf(offerDetails.getPrice().getMrp().longValue() + (!isPartOfGstSplitSegment ? platformFeeValue : 0)) : null;
            double numberOfMonths = Math.floor((packDuration.doubleValue() / 30) * 2) / 2.0;
            String perMonthPriceWithoutDuration = "" + Math.round((Math.ceil(offerPrice.doubleValue()) / numberOfMonths));
            String perMonthPrice = perMonthPriceWithoutDuration + "/mo*";

            String currencyUnit = currency.equals("INR") ? OrderUtil.RUPEE_SYMBOL : currency;

            String[] PackTextGradientArray = Arrays.stream(PackTextGradient.class.getEnumConstants()).map(Enum::name).toArray(String[]::new);
            String textGradientVariant = PackTextGradientArray[packCount % PackTextGradientArray.length];

            long totalMonthsRounded = Math.round(Math.floor(totalDuration / 30));
            String durationString =  "" + totalMonthsRounded;
            String durationUnit = packDuration > 59 ? "MONTHS" : "MONTH";
            String actionUrl = null;
            if (isNewActionUrlForPackPageSupported && productType.equals(CultPackBrowseProductType.CULT_UNLIMITED) && !isWeb) {
                actionUrl = getPacksPageAction(pack.getProductId());
                if (Objects.nonNull(center) && !isWeb) {
                    Instant instant = Instant.ofEpochMilli(center.getLaunchDate().getTime());
                    LocalDateTime launchDate = LocalDateTime.ofInstant(instant, ZoneId.of(userContext.getUserProfile().getTimezone()));
                    if (center.getLaunchDate() != null && launchDate.isBefore(TimeUtil.getDateNow(DEFAULT_ZONE_ID))) {
                        if (!isWeb) {
                            actionUrl += "&centerId=" + queryParams.get("centerId");
                        }
                        if(Objects.nonNull(center) && isWeb){
                            Object cultCenter = center.getMeta().get(FitnessCenterUtil.CULT_CENTER_ID_KEY);
                            String cultCenterId = "";
                            if(cultCenter != null ) {
                                cultCenterId = cultCenter.toString();
                            }
                            actionUrl += "&centerId=" + cultCenterId ;
                        }
                    }
                }
            } else {
                actionUrl = AppUtil.getNewPackPageActionUrl(ActionUtil.getPackDetailAction(pack.getProductType(), pack.getId(), null, null, null, userContext.getSessionInfo().getUserAgent()));
                if(Objects.nonNull(center) && isWeb){
                    Object cultCenter = center.getMeta().get(FitnessCenterUtil.CULT_CENTER_ID_KEY);
                    String cultCenterId = "";
                    if(cultCenter != null ) {
                        cultCenterId = cultCenter.toString();
                    }
                    actionUrl += "&centerId=" + cultCenterId ;
                }
            }

            Boolean isAllIndiaPack = SkuPackUtil.isAllIndiaPack(pack.getProduct().getBenefits());
            if (isAllIndiaPack) {
                durationUnit = "Months all India";
            }

            Action action = new Action(actionUrl, "VIEW", ActionType.NAVIGATION);
            HashMap<String, String> meta = new HashMap<>();
            meta.put("selectedSku", "ELITE");
            action.setMeta(meta);


            CFTextData additionalPrice = getAdditionalPricetext(pack.getAugments().getExtraCharges(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);
            String additionalPriceWeb = getAdditionalPriceAmount(pack.getAugments().getExtraCharges(), (Objects.nonNull(priceBreakup)) ? priceBreakup.getTaxAmount() : null);
            PackItem cultPackItem = new PackItem(
                    productType,
                    cultPackCostPrice,
                    String.valueOf(offerPrice),
                    perMonthPrice,
                    perMonthPriceWithoutDuration,
                    currencyUnit,
                    textGradientVariant,
                    null,
                    null,
                    action,
                    durationString,
                    durationUnit,
                    false,
                    false,
                    null,
                    "normal",
                    null,
                    null,
                    false,
                    priceBreakup,
                    null,
                    null,
                    String.valueOf(numberOfMonths),
                    null,
                    packDuration,
                    isPartOfGstSplitSegment ? additionalPrice : null,
                    additionalPriceWeb,
                    pack
            );

            if (AppUtil.isAppClpV2Supported(userContext, interfaces)) {
                if (isAllIndiaPack) {
                    conditionalOffers.add("Unlimited access to all centers across cities");
                } else {
                    conditionalOffers.add("Unlimited access to all cult gyms and centers");
                }
            } else if (isAllIndiaPack) {
                conditionalOffers.add("Unlimited access to all centers across cities");
            }

            packs.add(cultPackItem);
            packCount += 1;

        }

        if (CollectionUtils.isEmpty(packs)) {
            return null;
        }

        List<PackItem> finalPacks = this.removeCommonOffersAndAddOffers(userContext, packs, packOfferDetails, conditionalOffers, productSubType.equals(ProductSubType.PLUS) ? "ELITE_PLUS" : "ELITE");

        FitnessPackHeader header = item.getHeader() != null ? item.getHeader() : new FitnessPackHeader();
        Boolean hideHeaderFadeOffset = header.getImageSize() == HeaderImageSize.LARGE;
        Double headerImageAspectRatio = header.getImageSize() == HeaderImageSize.LARGE ? 1.98 : 4.78;
        if (queryParams.containsKey("packsTopPadding"))
            item.setTopSpacing(queryParams.get("packsTopPadding"));

        if (expandedMembershipPack == null) {
            this.expandedMembershipPack = new MembershipPackWidgetData(
                    finalPacks,
                    header.getHeaderTitle(),
                    header.getHeaderSubTitle(),
                    header.getHeaderOffers(),
                    header.getHeaderImage(),
                    headerImageAspectRatio,
                    hideHeaderFadeOffset,
                    null
            );
        } else {
            this.collapsedMembershipPack = new MembershipPackWidgetData(
                    finalPacks,
                    header.getHeaderTitle(),
                    header.getHeaderSubTitle(),
                    header.getHeaderOffers(),
                    header.getHeaderImage(),
                    headerImageAspectRatio,
                    hideHeaderFadeOffset,
                    null
            );
        }

        if (header.getHeaderImage() == null || header.getHeaderImage().isEmpty()) {
            item.setSimplifyHeader(true);
            header.setHeaderImage(null);
        }

        item.setPacks(packs);
        return item;
    }

    private PackOfferDetails getOfferDetails(OfflineFitnessPack packProduct, CultProductPricesResponse packOffersV3) {
        if (packOffersV3.getOfferMap() == null || packOffersV3.getPriceMap() == null)
            return null;

        ProductPrice price = packProduct.getPrice();
        List<OfferMini> offers = new ArrayList<OfferMini>();

        Map<String, ProductPriceResponse> priceMap = packOffersV3.getPriceMap();
        if (priceMap != null) {
            ProductPriceResponse productOffer = priceMap.get(packProduct.getId());
            if (productOffer != null) {
                // Update Price.
                price = new ProductPrice();
                price.setMrp(BigDecimal.valueOf(productOffer.getProduct().getPrice().getMrp()));
                price.setListingPrice(BigDecimal.valueOf(productOffer.getProduct().getPrice().getSellingPrice()));
                price.setCurrency("INR");

                // Update Offers.
                List<String> productOfferOfferIds = productOffer.getOfferIds();
                for (String productOfferId : productOfferOfferIds) {
                    offers.add(packOffersV3.getOfferMap().get(productOfferId));
                }
            }
        }

        return new PackOfferDetails(price, offers);
    }

    private PackOfferDetails getPlayOfferDetails(FitnessPack packProduct, PlayProductPricesResponse packOffersV3) {
        if (packOffersV3.getOfferMap() == null || packOffersV3.getPriceMap() == null)
            return null;

        ProductPrice price = packProduct.getPrice();
        List<OfferMini> offers = new ArrayList<>();

        Map<String, ProductPriceResponse> priceMap = packOffersV3.getPriceMap();
        if (priceMap != null) {
            ProductPriceResponse productOffer = priceMap.get(packProduct.getProductId());
            if (productOffer != null) {
                // Update Price.
                price = new ProductPrice();
                price.setMrp(BigDecimal.valueOf(productOffer.getProduct().getPrice().getMrp()));
                price.setListingPrice(BigDecimal.valueOf(productOffer.getProduct().getPrice().getSellingPrice()));
                price.setCurrency("INR");

                // Update Offers.
                List<String> productOfferOfferIds = productOffer.getOfferIds();
                for (String productOfferId : productOfferOfferIds) {
                    offers.add(packOffersV3.getOfferMap().get(productOfferId));
                }
            }
        }

        return new PackOfferDetails(price, offers);
    }

//    private PackOfferDetails getOfferDetails(GymfitFitnessProduct packProduct, GymFitProductPricesResponse packOffersV3) {
//        ProductPrice price = packProduct.getPrice();
//        List<OfferMini> offers = new ArrayList<OfferMini>();
//
//        Map<String, ProductPriceResponse> priceMap = packOffersV3.getPriceMap();
//        if (priceMap != null) {
//            ProductPriceResponse productOffer = priceMap.get(packProduct.getProductId());
//            if (productOffer != null) {
//                // Update Price.
//                price = new ProductPrice();
//                price.setMrp(BigDecimal.valueOf(productOffer.getProduct().getPrice().getMrp()));
//                price.setListingPrice(BigDecimal.valueOf(productOffer.getProduct().getPrice().getSellingPrice()));
//                price.setCurrency("INR");
//
//                // Update Offers.
//                List<String> productOfferOfferIds = productOffer.getOfferIds();
//                for (String productOfferId : productOfferOfferIds) {
//                    offers.add(packOffersV3.getOfferMap().get(productOfferId));
//                }
//            }
//        }
//
//        return new PackOfferDetails(price, offers);
//    }

    private PackOfferDetails getOfferDetails(com.curefit.pms.pojo.customPacks.OfflineFitnessPack pack, GymFitProductPricesResponse packOffersV3) {
        ProductPrice price = pack.getPrice();
        List<OfferMini> offers = new ArrayList<OfferMini>();

        Map<String, ProductPriceResponse> priceMap = packOffersV3.getPriceMap();
        if (priceMap != null) {
            ProductPriceResponse productOffer = priceMap.get(pack.getId());
            if (productOffer != null) {
                // Update Price.
                price = new ProductPrice();
                price.setMrp(BigDecimal.valueOf(productOffer.getProduct().getPrice().getMrp()));
                price.setListingPrice(BigDecimal.valueOf(productOffer.getProduct().getPrice().getSellingPrice()));
                price.setCurrency("INR");

                // Update Offers.
                List<String> productOfferOfferIds = productOffer.getOfferIds();
                for (String productOfferId : productOfferOfferIds) {
                    offers.add(packOffersV3.getOfferMap().get(productOfferId));
                }
            }
        }

        return new PackOfferDetails(price, offers);
    }

    private PackOfferDetails getOfferDetails(GymfitPtProduct packProduct, GymFitProductPricesResponse packOffersV3) {
        ProductPrice price = packProduct.getPrice();
        List<OfferMini> offers = new ArrayList<OfferMini>();

        Map<String, ProductPriceResponse> priceMap = packOffersV3.getPriceMap();
        if (priceMap != null) {
            ProductPriceResponse productOffer = priceMap.get(packProduct.getProductId());
            if (productOffer != null) {
                // Update Price.
                price = new ProductPrice();
                price.setMrp(BigDecimal.valueOf(productOffer.getProduct().getPrice().getMrp()));
                price.setListingPrice(BigDecimal.valueOf(productOffer.getProduct().getPrice().getSellingPrice() != null ? productOffer.getProduct().getPrice().getSellingPrice() : productOffer.getProduct().getPrice().getListingPrice()));
                price.setCurrency("INR");

                // Update Offers.
                List<String> productOfferOfferIds = productOffer.getOfferIds();
                for (String productOfferId : productOfferOfferIds) {
                    offers.add(packOffersV3.getOfferMap().get(productOfferId));
                }
            }
        }

        return new PackOfferDetails(price, offers);
    }

    private String getPacksPageAction(String productId) {
        String url = "curefit://checkout_v2?productId=" + productId;
        return url;
    }

    private List<FitnessPack> getPlayPacksBySKU(
            CultPackBrowseProductType productType,
            Map<String, String> queryParams,
            UserContext userContext,
            ServiceInterfaces interfaces
    ) throws Exception {
        String workoutId = queryParams != null ? queryParams.getOrDefault("workoutId", null) : null;
        List<FitnessPack> allPacks = null;
        switch (productType) {
            case PLAY:
                allPacks = interfaces.catalogueService.getNewPlayPacksByCity(userContext.getUserProfile().getCity().getCityId(), userContext.getUserProfile().getUserId());
                break;
            case PLAY_SELECT:
                if (queryParams != null && queryParams.get("centerId") != null) {
                    allPacks = interfaces.catalogueService.getNewPlaySelectPackByCenterId(queryParams.get("centerId"), true, userContext.getUserProfile().getUserId());
                } else {
                    allPacks = interfaces.catalogueService.getNewPlaySelectPacksByCity(userContext.getUserProfile().getCity().getCityId(), true, userContext.getUserProfile().getUserId());
                }
                break;
            case PLAY_SPORT:
                String centerId = queryParams != null ? queryParams.getOrDefault("centerId", null) : null;
                Map<String, List<FitnessPack>> packs = interfaces.catalogueService.getPlaySportsPacksByCenterIdAndWorkoutId(centerId, workoutId, userContext.getUserProfile().getUserId());
                if (packs != null && !packs.isEmpty()){
                    if (workoutId != null)
                        allPacks = packs.get(workoutId);
                    else
                        allPacks = packs.get(packs.keySet().stream().toList().getFirst());
                } else
                    allPacks = null;
                break;

            case PLAY_CITY_SPORT:
                List<FitnessPack> slpPacks = interfaces.catalogueService.getPlayCityActivityWiseProducts(userContext.getUserProfile().getCity().getCityId(),
                        workoutId,
                        true,
                        userContext.getUserProfile().getUserId()
                );
                if (slpPacks != null && !slpPacks.isEmpty()){
                    allPacks = slpPacks;
                }
                break;
            case PLAY_BADMINTON:
                List<FitnessPack> baddyPacks = interfaces.catalogueService.getPlayCityActivityWiseProducts(userContext.getUserProfile().getCity().getCityId(),
                        PlayUtil.BADMINTON_WORKOUT_ID,
                        true,
                        userContext.getUserProfile().getUserId()
                );
                if (baddyPacks != null && !baddyPacks.isEmpty()){
                    allPacks = baddyPacks;
                }
                break;
            case PLAY_SWIM:
                List<FitnessPack> swimPacks = interfaces.catalogueService.getPlayCityActivityWiseProducts(userContext.getUserProfile().getCity().getCityId(),
                        PlayUtil.SWIMMING,
                        true,
                        userContext.getUserProfile().getUserId()
                );
                if (swimPacks != null && !swimPacks.isEmpty()){
                    allPacks = swimPacks;
                }
                break;
            case PLAY_SQUASH:
                List<FitnessPack> squashPacks = interfaces.catalogueService.getPlayCityActivityWiseProducts(userContext.getUserProfile().getCity().getCityId(),
                        PlayUtil.SQUASH_WORKOUT_ID,
                        true,
                        userContext.getUserProfile().getUserId()
                );
                if (squashPacks != null && !squashPacks.isEmpty()){
                    allPacks = squashPacks;
                }
                break;
            case PLAY_TENNIS:
                List<FitnessPack> tennisPacks = interfaces.catalogueService.getPlayCityActivityWiseProducts(userContext.getUserProfile().getCity().getCityId(),
                        PlayUtil.TENNIS_WORKOUT_ID,
                        true,
                        userContext.getUserProfile().getUserId()
                );
                if (tennisPacks != null && !tennisPacks.isEmpty()){
                    allPacks = tennisPacks;
                }
                break;
            case PLAY_TT:
                List<FitnessPack> ttPacks = interfaces.catalogueService.getPlayCityActivityWiseProducts(userContext.getUserProfile().getCity().getCityId(),
                        PlayUtil.TT_WORKOUT_ID,
                        true,
                        userContext.getUserProfile().getUserId()
                );
                if (ttPacks != null && !ttPacks.isEmpty()){
                    allPacks = ttPacks;
                }
        }

        if (allPacks != null)
            allPacks = allPacks.stream().filter(pack -> pack.getVisibility() != null && pack.getVisibility().contains("app")).collect(Collectors.toList());

        return allPacks;
    }

    private Action getPlaypackPageAction(
            CultPackBrowseProductType productType,
            FitnessPack pack,
            Map<String, String> queryParams,
            UserContext userContext,
            boolean isWeb,
            boolean isPlayPackPageV2
    ) {
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        Boolean isLoggedIn = userContext.getSessionInfo().getIsUserLoggedIn();
        switch (productType) {
            case PLAY:
                if (isPlayPackPageV2 && !isWeb) {
                    nameValuePairs.add(new BasicNameValuePair("productId", pack.getProductId()));
                    nameValuePairs.add(new BasicNameValuePair("productType", "PLAY"));
                    HashMap<String, String> meta = new HashMap<>();
                    meta.put("selectedSku", "PLAY");
                    return PlayUtil.getPlayPackPageAction(nameValuePairs, "Explore", meta);
                } else {
                    if (isWeb) {
                        String url = ActionUtil.playPackWebAction(pack.getProductId(), null, true, "packbrowse", pack.getSubscriptionPack() != null, queryParams.get("centerId"), null);
                        url += "&productType=PLAY";
                        Action action = new Action(url, "VIEW", ActionType.NAVIGATION);
                        HashMap<String, String> meta = new HashMap<>();
                        meta.put("selectedSku", "PLAY");
                        action.setMeta(meta);

                        return action;
                    } else {
                        HashMap<String, String> meta = new HashMap<>();
                        meta.put("selectedSku", "PLAY");

                        nameValuePairs.add(new BasicNameValuePair("productId", pack.getProductId()));
                        nameValuePairs.add(new BasicNameValuePair("productCode", pack.getProductId()));
                        return PlayUtil.getOldPlayPackPageAction(nameValuePairs, "VIEW", meta);
                    }
                }
            case PLAY_SELECT:
                if (!isLoggedIn) {
                    return ActionUtil.getLoginAction("VIEW");
                }
                if (queryParams.get("centerId") != null) {
                    nameValuePairs.add(new BasicNameValuePair("productId", pack.getProductId()));
                    nameValuePairs.add(new BasicNameValuePair("centerId", queryParams.get("centerId")));
                    nameValuePairs.add(new BasicNameValuePair("productType", "PLAY_SELECT"));

                    HashMap<String, String> selectMeta = new HashMap<>();
                    selectMeta.put("selectedSku", "PLAY SELECT");
                    return PlayUtil.getPlayPackPageAction(nameValuePairs, "Explore", selectMeta);
                } else {
                    Action selectAction = new Action(ALL_CENTERS_DEEPLINK, "VIEW", ActionType.NAVIGATION);
                    HashMap<String, String> selectMeta = new HashMap<>();
                    selectMeta.put("selectedSku", "PLAY SELECT");
                    selectAction.setMeta(selectMeta);
                    return selectAction;
                }
            case PLAY_SPORT:
                if (queryParams.get("centerId") != null && queryParams.get("workoutId") != null) {
                    nameValuePairs.add(new BasicNameValuePair("productId", pack.getProductId()));
                    nameValuePairs.add(new BasicNameValuePair("centerId", queryParams.get("centerId")));
                    nameValuePairs.add(new BasicNameValuePair("workoutId", queryParams.get("workoutId")));
                    nameValuePairs.add(new BasicNameValuePair("productType", "PLAY_SPORT"));

                    HashMap<String, String> playSportMeta = new HashMap<>();
                    playSportMeta.put("selectedSku", "PLAY SPORT");
                    return PlayUtil.getPlayPackPageAction(nameValuePairs, "Explore", playSportMeta);
                }
            case PLAY_CITY_SPORT:
                nameValuePairs.add(new BasicNameValuePair("productId", pack.getProductId()));
//                if (userContext.getSessionInfo().getAppVersion() >= 10.94f)
//                    nameValuePairs.add(new BasicNameValuePair("productType", "PLAY_CITY_SPORT"));
//                else
                nameValuePairs.add(new BasicNameValuePair("productType", "PLAY_SPORT"));
                if (queryParams.get("centerId") != null)
                    nameValuePairs.add(new BasicNameValuePair("centerId", queryParams.get("centerId")));
                if (queryParams.get("workoutId") != null)
                    nameValuePairs.add(new BasicNameValuePair("workoutId", queryParams.get("workoutId")));

                HashMap<String, String> playSportMeta = new HashMap<>();
                playSportMeta.put("selectedSku", "PLAY CITY SPORT");
                return PlayUtil.getPlayPackPageAction(nameValuePairs, "Explore", playSportMeta);
        }

        return new Action(ActionType.EMPTY_ACTION);
    }


    private List<AugmentedOfflineFitnessPack> getFitnessPackPromise(UserContext userContext, ServiceInterfaces interfaces, String centerCityId, ProductSubType productSubType) throws Exception {
            UserProfile userProfile = userContext.getUserProfile();
            String userId = userProfile.getUserId();
            List<AugmentedOfflineFitnessPack> fitnessPacks = userContext.getRequestCache().getAugmentedPackList(ProductType.FITNESS, productSubType, false, userContext, null);
            fitnessPacks.sort(Collections.reverseOrder(Comparator.comparing(pack -> pack.getProduct().getDurationInDays())));
            // Experiment for all India Pack, if the ALL india pack is present put it in the end of the list
            int ind = -1;
            for (int i = 0; i < fitnessPacks.size(); i++) {
                if (SkuPackUtil.isAllIndiaPack(fitnessPacks.get(i).getProduct().getBenefits())) {
                    ind = i;
                    break;
                }
            }
            if (ind != -1) {
                AugmentedOfflineFitnessPack pack = fitnessPacks.remove(ind);
                fitnessPacks.add(pack);
            }
            int distance = (int) fitnessPacks.stream().filter(pack -> pack.getProduct().getDurationInDays() >= 730).count();
            if (distance > 0) { // if 24 months pack exists in list
                Collections.rotate(fitnessPacks, distance * -1); // to rotate in reverse manner
            }
            return fitnessPacks;
    }

    private List<String> getOfferTextForPack(List<OfferMini> offers) {
        List<String> offerTexts = new ArrayList<String>();

        for (OfferMini offer : offers) {
            String offerText = null;

            UILabels offerUiLabel = offer.getUiLabels();

            if (offerUiLabel != null && offerUiLabel.getDescription() != null && !offerUiLabel.getDescription().isEmpty()) {
                offerText = offerUiLabel.getDescription();
            } else {
                List<Addon> offerAddons = offer.getAddons();
                for (Addon offerAddon : offerAddons) {
                    UILabels addonUiLabel = offerAddon.getUiLabels();
                    if (offerAddon != null
                            && addonUiLabel != null
                            && addonUiLabel.getDescription() != null
                            && !addonUiLabel.getDescription().isEmpty()
                    ) {
                        offerText = addonUiLabel.getDescription();
                    }
                }
            }

            if (offerText != null && !offerText.isEmpty()) {
                offerTexts.add(offerText);
            }
        }
        return offerTexts;
    }

    private List<OfferItem> getOffersToShow(UserContext userContext, PackOfferDetails offerDetails, String conditionalOffer, int offer_to_be_shown_upfront, String sku, PackItem pack) {
        List<String> benefitsList = this.getBenefitsList(sku, pack);
        int benefitsToBeShownUpfront = AppUtil.isWeb(userContext) ? 6 : 3;
        if (!benefitsList.isEmpty()) conditionalOffer = null;
        String iconType = userContext.getSessionInfo().getAppVersion() >= 8.48 ? "/image/icons/cult/offer_black.png"
                : "/image/icons/cult/tick.png";

        List<OfferItem> offerItems = new ArrayList<OfferItem>();

        List<String> offerTexts = this.getOfferTextForPack(offerDetails.getOffers());

        OfferItemTitleStyle titleStyle = new OfferItemTitleStyle("#33363F");
        OfferItemIconStyle iconStyle = new OfferItemIconStyle(12, 12);

        if (Objects.nonNull(conditionalOffer)) {
            offerItems.add(new OfferItem(conditionalOffer, iconType, null, titleStyle, 12, iconStyle));
        }

        for (String offerText : offerTexts) {
            offerItems.add(new OfferItem(offerText, iconType, null, titleStyle, 12, iconStyle));
        }

        List<OfferItem> offersToShow = new ArrayList<>(offerItems.subList(0, Math.min(offer_to_be_shown_upfront, offerItems.size())));

        if (!benefitsList.isEmpty()) {
            for (int i=0; i < Math.min(benefitsToBeShownUpfront, benefitsList.size()); i++) {
                offersToShow.add(new OfferItem(benefitsList.get(i), iconType, null, titleStyle, 12, iconStyle));
            }
        }

        int additionalOffers = Math.max((benefitsList.size() - benefitsToBeShownUpfront), 0) + Math.max((offerItems.size() - offer_to_be_shown_upfront), 0);

        if (!offersToShow.isEmpty() && additionalOffers > 0) {
            // show "+1 Offer" as the last offer
            String moreOffersTitle = additionalOffers + " more" + pluralizeStringIfRequired(" Offer", additionalOffers, "");
            offersToShow.add(new OfferItem(moreOffersTitle, iconType, null, titleStyle, 12, iconStyle));
        }
        return offersToShow;
    }

    private List<String> getBenefitsList(String sku, PackItem pack) {
        List<String> benefitsList = new ArrayList<>();
        if (sku != null && (sku.equals("ELITE_PLUS") || sku.equals("PRO_PLUS"))) {
            if (pack.getPack() != null && pack.getPack().getAugments().getExhaustiveBenefitList() != null) {
                List<BenefitEntry> exhaustivePackBenefitList = pack.getPack().getAugments().getExhaustiveBenefitList();
                for (BenefitEntry benefit : exhaustivePackBenefitList) {
                    if (benefit.getDisplayTitle() != null && !benefit.getDisplayTitle().isEmpty()) {
                        benefitsList.add(benefit.getDisplayTitle());
                    }
                }
            }
        }
        else if (sku !=null && (sku.equals("ELITE") || sku.equals("PRO") || sku.equals("PRO_SELECT") || sku.equals("ELITE_SELECT"))) {
            if (pack.getPack() != null) {
                AugmentedOfflineFitnessPack augmentedOfflineFitnessPack = pack.getPack();
                if (augmentedOfflineFitnessPack.getAugments() != null && augmentedOfflineFitnessPack.getAugments().getExhaustiveBenefitList() != null) {
                    List<BenefitEntry> exhaustivePackBenefitList = augmentedOfflineFitnessPack.getAugments().getExhaustiveBenefitList();
                    for (BenefitEntry benefit: exhaustivePackBenefitList) {
                        if (benefit.getName() != null && benefit.getName().equals(ACCESS_CREDITS)) {
                            continue;
                        }
                        else if (benefit.getDisplayTitle() != null && !benefit.getDisplayTitle().isEmpty()) {
                            benefitsList.add(benefit.getDisplayTitle());
                        }
                    }
                }
            }
        }
        return benefitsList;
    }

    private List<PackItem> removeCommonOffersAndAddOffers(UserContext userContext, List<PackItem> packs, List<PackOfferDetails> packOfferDetails, List<String> conditionalOffers, String sku) {
        List<String> commonOfferIds = new ArrayList<String>();

        Boolean isFirstOfferDetail = true;

        // Find all common offerIds.
//        for (PackOfferDetails packOfferDetail: packOfferDetails) {
//            List<OfferMini> offers = packOfferDetail.getOffers();
//
//            // If it's first Offer detail.
//            if (isFirstOfferDetail) {
//                for (OfferMini offer: offers) {
//                    commonOfferIds.add(offer.getOfferId());
//                }
//                isFirstOfferDetail = false;
//            } else {
//                List<String> newCommonOfferIds = new ArrayList<String>();
//
//                for (OfferMini offer: offers) {
//                    if (commonOfferIds.contains(offer.getOfferId())) {
//                        newCommonOfferIds.add(offer.getOfferId());
//                    };
//                }
//                commonOfferIds = newCommonOfferIds;
//            }
//        }

        List<PackItem> filteredPacks = new ArrayList<PackItem>();

        int index = 0;
        for (PackItem pack : packs) {
            PackOfferDetails currentPackOffer = packOfferDetails.get(index);
            int offer_to_be_shown_upfront = OFFERS_TO_BE_SHOWN_UPFRONT;

            if (Objects.nonNull(pack.getActionOffers()) && Objects.equals(sku, "ELITE_SELECT")) {
                offer_to_be_shown_upfront -= 1;
            }

            if (currentPackOffer != null) {
                // Remove common offer from offers of current pack.
                if (commonOfferIds.size() != 0) {
                    List<OfferMini> packOffers = currentPackOffer.getOffers();
                    Iterator<OfferMini> it = packOffers.iterator();
                    while (it.hasNext()) {
                        OfferMini packOffer = it.next();
                        if (commonOfferIds.contains(packOffer.getOfferId())) {
                            it.remove();
                        }
                    }
                }

                String conditionalOffer = null;
                if (CollectionUtils.isNotEmpty(conditionalOffers) && conditionalOffers.size() >= index+1) {
                    conditionalOffer = conditionalOffers.get(index);
                }

                List<OfferItem> offersToShow = this.getOffersToShow(userContext, currentPackOffer, conditionalOffer, offer_to_be_shown_upfront, sku, pack);
                index = index + 1;

                List<PackOffer> finalOffers = new ArrayList<PackOffer>();
                for (OfferItem offerToShow : offersToShow) {
                    String offerTitle = offerToShow.getTitle();
                    Action seeMoreAction = offerToShow.getSeeMore();
                    PackOffer offer = new PackOffer(offerTitle, null, seeMoreAction);
                    finalOffers.add(offer);
                }

                pack.setOffers(finalOffers);
                filteredPacks.add(pack);
            } else {
                filteredPacks.add(pack);
            }
        }
        return filteredPacks;
    }

    public List<BaseWidget> buildCoachFitnessPackBrowseWidget(ServiceInterfaces interfaces, UserContext userContext, Map<String, String> queryParams) throws Exception {
        UserProfile userProfile = userContext.getUserProfile();
        SessionInfo sessionInfo = userContext.getSessionInfo();
        SegmentSet<String> userPlatformSegmentSet = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();
        int daysRemaining = TransformUtil.getRemainingDaysForRenewal(interfaces, userContext);
        //For active complimentary packs we don't show renewal widget if remaining days of membership are greater than 4
        GenericBundleOrderResponse latestMembership = TransformUtil.getLatestMembership(interfaces, userContext);
        if (StringUtils.isEmpty(queryParams.get("getAllPacks"))) {
            if (userPlatformSegmentSet.contains(COACH_MEMBERS_PLATFORM_SEGMENT_ID) && daysRemaining > 4 && TransformUtil.isComplimentaryMembership(latestMembership)) {
                return null;
            }

            // for active memberships which are not in renewal we will not show pack browse widget
//            if (userPlatformSegments.contains(COACH_MEMBERS_PLATFORM_SEGMENT_ID) && daysRemaining > 30) {
//                return null;
//            }
        }

        for (FitnessPackCategoryItem item : this.items) {
            List<BundleSellableProduct> bundleItems;
            if (!StringUtils.isEmpty(queryParams.get("isTransformPlusProduct"))) {
                bundleItems = interfaces.getTransformClient().bundleProductSearch(TransformUtil.plusSubCategoryCode, true, false, userContext.getUserProfile().getUserId());
            } else if (!StringUtils.isEmpty(queryParams.get("subCategoryCode"))) {
                bundleItems = interfaces.getTransformClient().bundleProductSearch(queryParams.get("subCategoryCode"), true, false, userContext.getUserProfile().getUserId());
            } else if (StringUtils.isEmpty(subCategoryCode)) {
                bundleItems = interfaces.getTransformClient().bundleProductSearch(TransformUtil.transformSubCategoryCode, true, false, userContext.getUserProfile().getUserId());
            } else {
                bundleItems = interfaces.getTransformClient().bundleProductSearch(subCategoryCode, true, false, userContext.getUserProfile().getUserId());
            }
            final String pcosProductField = "isPcosProduct";
            List<BundleSellableProduct> pcosBundleItems = bundleItems.stream()
                    .filter(bundle -> bundle.getInfoSection().hasNonNull(pcosProductField) && bundle.getInfoSection().get(pcosProductField).booleanValue())
                    .collect(Collectors.toList());

            if (StringUtils.isEmpty(queryParams.get("getAllPacks"))) {
                if (isPCOSPack) {
                    bundleItems = pcosBundleItems;
                } else {
                    bundleItems.removeAll(pcosBundleItems);
                }
            }
            if (filterId != null && !filterId.isEmpty()) {
                List<BundleSellableProduct> filterItems = bundleItems.stream()
                        .filter(bundle -> filterId.equalsIgnoreCase(bundle.getProductCode()))
                        .collect(Collectors.toList());
                if (!filterItems.isEmpty()) {
                    bundleItems = filterItems;
                }
            }

            List<String> bundleProductIds = new ArrayList<String>();
            for (BundleSellableProduct bundleItem : bundleItems) {
                bundleProductIds.add(bundleItem.getProductCode());
            }

            UserEntry user = userContext.getUserEntryCompletableFuture().get();
            UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);

            String callSource = AppUtil.callSource(sessionInfo.getApiKey(), interfaces.apiKeyService);

            Set<String> relevantEntries = interfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.OFFER_SERVICE);
            Set<String> userPlatformSegments = userPlatformSegmentSet.getRelevantEntries(relevantEntries);
            CareProductPricesResponse pricesResponse = interfaces.getOfferService().getCarePackPrices(new CareProductDiscountRequest(
                    bundleProductIds,
                    userInfo,
                    userProfile.getCity().getCityId(),
                    OrderSource.valueOf(callSource),
                    userPlatformSegments)
            );

            List<PackItem> packs = new ArrayList<PackItem>();

            int packCount = 0;
            boolean isWeb = AppUtil.isWeb(userContext);

            for (BundleSellableProduct bundleProduct : bundleItems) {

                PackOfferDetails offerDetails = this.getOfferDetails(interfaces.getOfferService(), bundleProduct, pricesResponse);

                BigInteger offerPrice = offerDetails.getPrice().getListingPrice() != null
                        ? offerDetails.getPrice().getListingPrice().toBigInteger() : offerDetails.getPrice().getMrp().toBigInteger();

                int duration = Math.toIntExact(bundleProduct.getDuration());
                int totalDuration = Math.toIntExact(bundleProduct.getDuration());

                List<OfferMini> offerMinis = offerDetails.getOffers();
                for (OfferMini offer : offerMinis) {
                    if (offer.getAddons() != null) {
                        for (Addon addon : offer.getAddons()) {
                            if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.CULT_TRANSFORM_EXTENSION
                                    && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                                duration = duration + addon.getConfig().getCount();
                                break;
                            }
                        }
                    }
                }

                Integer extendedDuration = null;

                if (extendedDuration != null) {
                    totalDuration = duration - extendedDuration;
                }

                String currency = offerDetails.getPrice().getCurrency();

                // Data members of PackItem;
                String cultPackCostPrice = offerDetails.getPrice().getMrp().compareTo(offerDetails.getPrice().getListingPrice()) != 0
                        ? offerDetails.getPrice().getMrp().toBigInteger().toString() : null;
                String perMonthPriceWithoutDuration = "" + Math.round(Math.ceil(offerPrice.doubleValue() / Math.floor(duration / 30)));
                String perMonthPrice = perMonthPriceWithoutDuration + "/mo*";

                Boolean isDoubleDigit = extendedDuration != null;

                String[] TransformTextGradientArray = Arrays.stream(TransformTextGradient.class.getEnumConstants()).map(Enum::name).toArray(String[]::new);
                String textGradientVariant = TransformTextGradientArray[packCount % TransformTextGradientArray.length];

                long totalMonthsRounded = Math.round(Math.floor(totalDuration / 30));
                String durationString = extendedDuration != null ? "" + totalMonthsRounded + "+" + Math.round(Math.floor(extendedDuration / 30))
                        : "" + totalMonthsRounded;
                String durationUnit = duration > 59 ? "MONTHS" : "MONTH";

                String actionUrl = "curefit://tf_checkout?productId=" + bundleProduct.getProductCode();
                String productId = bundleProduct.getProductCode();
                if (AppUtil.isTransformStartDateChangesSupported(userContext)) {
                    actionUrl = "curefit://tf_new_checkout?productId=" + bundleProduct.getProductCode();
                }
                ActionType actionType = ActionType.NAVIGATION;
                if (isWeb) {
                    actionUrl = String.format("/newcheckout?productId=%s&subCategoryCode=TRANSFORM&pageType=carecartcheckout", bundleProduct.getProductCode());
                } else if (!userContext.getSessionInfo().getIsUserLoggedIn()) {
                    actionUrl = "curefit://loginmodal";
                    actionType = ActionType.SHOW_LOGIN_MODAL;
                }
                Action action = new Action(actionUrl, "VIEW", actionType);
                PackItem cultPackItem = new PackItem(
                        item.getProductType(),
                        cultPackCostPrice,
                        offerPrice.toString(),
                        perMonthPrice,
                        perMonthPriceWithoutDuration,
                        currency,
                        textGradientVariant,
                        null,
                        null,
                        action,
                        durationString,
                        durationUnit,
                        isDoubleDigit,
                        false,
                        "",
                        "normal",
                        null,
                        null,
                        false,
                        null,
                        null,
                        productId,
                        durationString,
                        null,
                        totalDuration,
                        null,
                        null,
                        null
                );

                List<PackOffer> offers = new ArrayList<>();
                String iconType = "image/transform/offer_icon.png";
                for (int i = 0; i < offerDetails.getOffers().size(); i++) {
                    OfferMini offerMini = offerDetails.getOffers().get(i);
                    if (offerMini.getDescription() != null && !Objects.equals(offerMini.getDescription(), "")) {
                        offers.add(new PackOffer(offerMini.getDescription(), iconType, null));
                    }
                }
                cultPackItem.setOffers(offers);
                packs.add(cultPackItem);
                packCount += 1;
            }

            if (packs.size() == 0) {
                continue;
            }
            FitnessPackHeader header = item.getHeader();
            Boolean hideHeaderFadeOffset = header.getImageSize() == HeaderImageSize.LARGE;
            Double headerImageAspectRatio = header.getImageSize() == HeaderImageSize.LARGE ? 1.98 : 4.78;
            if (expandedMembershipPack == null) {
                this.expandedMembershipPack = new MembershipPackWidgetData(
                        packs,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            } else {
                this.collapsedMembershipPack = new MembershipPackWidgetData(
                        packs,
                        header.getHeaderTitle(),
                        header.getHeaderSubTitle(),
                        header.getHeaderOffers(),
                        header.getHeaderImage(),
                        headerImageAspectRatio,
                        hideHeaderFadeOffset,
                        null
                );
            }
            item.setPacks(packs);
        }

        if (this.items != null) {
            return Collections.singletonList(this);
        }

        return null;
    }

    private PackOfferDetails getOfferDetails(OfferService offerService, BundleSellableProduct packProduct, CareProductPricesResponse packOffersV3) throws Exception {
        ProductPrice price = new ProductPrice();
        price.setListingPrice(BigDecimal.valueOf(packProduct.getListingPrice()));
        price.setMrp(BigDecimal.valueOf(packProduct.getMrp()));
        price.setCurrency("INR");

        List<OfferMini> offers = new ArrayList<OfferMini>();

        Map<String, Prices> priceMap = packOffersV3.getPriceMap();
        if (priceMap != null) {
            Prices prices = priceMap.get(packProduct.getProductCode());
            if (prices != null) {
                // Update Price.
                price = new ProductPrice();
                price.setMrp(BigDecimal.valueOf(prices.getMrp()));
                price.setListingPrice(BigDecimal.valueOf(prices.getSellingPrice()));
                price.setCurrency("INR");

                // Update Offers.
                List<String> productOfferOfferIds = prices.getOfferIds();
                GetOffersResponse offersResponse = offerService.getOfferByIds(productOfferOfferIds).get();
                for (String productOfferId : productOfferOfferIds) {
                    Offer offer = offersResponse.getData().get(productOfferId);
                    BigInteger maxConsCount = (offer.getMaxConsumptionCount() == null) ? null
                            : BigInteger.valueOf(offer.getMaxConsumptionCount());
                    Boolean isGroupOffer = offer.getConstraints() != null && offer.getConstraints().getIsGroupOffer() != null && offer.getConstraints().getIsGroupOffer();
                    OfferMini offerMini = new OfferMini(offer.getOfferId(), offer.getTitle(), offer.getDescription(),
                            offer.getUiLabels(), offer.getDisplayContexts(), offer.getTnc(), offer.getTncUrl(),
                            offer.getOfferType(), offer.getAddons(), maxConsCount, offer.getEndDate(), isGroupOffer);
                    offers.add(offerMini);
                }
            }
        }

        return new PackOfferDetails(price, offers);
    }

    public List<BaseWidget> buildLivePackBrowseWidget(FitnessPackCategoryItem item, ServiceInterfaces interfaces, UserContext userContext, Map<String, String> queryParams, boolean isPartOfGstSplitSegment) throws Exception {
        Tenant tenant = AppUtil.getTenantFromUserContext(userContext);
        String userId = userContext.getUserProfile().getUserId();
        RequestSource requestSource = AppUtil.getRequestSource(userContext, interfaces.getApiKeyService());
        String source = requestSource.getSource();
        String deviceId = userContext.getSessionInfo().getDeviceId();

        // get from api
        List<CFLiveProduct> cfLiveProducts = interfaces.getDiyfsService().getCFLiveProductsV2(userId, requestSource, tenant, deviceId);
        if (CollectionUtils.isEmpty(cfLiveProducts)) {
            log.error(" no live_pack_browse cflive products found for source:" + requestSource.getSource() + ", osName:" + requestSource.getOsName() + ", tenant:" + tenant.toString() + ", deviceId:" + deviceId);
            if (this.isSourceCombinationNotSupported(requestSource, tenant)) {
                return null;
            } else {
                throw new Error("No live packs found");
            }
        }
//        String bucketId = AppUtil.getBucketIdForOnboardingExperiment(interfaces.getEnvironmentService(), userContext, interfaces.getHamletHelper());
        List<String> cfProductIds = cfLiveProducts.stream().map(cfLiveProduct -> cfLiveProduct.getProductId()).collect(Collectors.toList());
        // offer service code
        UserInfo userInfo = UserInfo.builder()
                .userId(userId)
                .appTenant(tenant)
                .build();
        LiveProductDiscountRequest request = LiveProductDiscountRequest.builder()
                .productIds(cfProductIds)
                .orderSource(OrderSource.valueOf(source))
                .userInfo(userInfo)
                .build();

        LiveProductPricesResponse liveProductPricesResponse = interfaces.getOfferService().getLivePackPrices(request);
        Map<String, ProductTaxBreakup> taxBreakupMap;
        if (isPartOfGstSplitSegment) {
            taxBreakupMap = interfaces.offerService.getTaxBreakUpForLiveProducts(liveProductPricesResponse);
        } else {
            taxBreakupMap = null;
        }

        List<String> offerIds = new ArrayList<>();
        cfProductIds.forEach(productId -> {
            Prices prices = liveProductPricesResponse.getPriceMap().get(productId);
            if (prices != null && !CollectionUtils.isEmpty(prices.getBreakup())) {
                List<String> breakupOfferIds = prices.getBreakup().stream().map((priceBreakup) -> priceBreakup.getOfferId()).collect(Collectors.toList());
                offerIds.addAll(breakupOfferIds);
            }
        });

        Map<String, Offer> offerMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(offerIds)) {
            GetOffersResponse offersResponse = interfaces.getOfferService().getOfferByIds(offerIds).get();
            offerMap = offersResponse.getData();
        }
        boolean isIOS = "ios".equalsIgnoreCase(userContext.getSessionInfo().getOsName());
        Map<String, Offer> finalOfferMap = offerMap;
        AtomicInteger index = new AtomicInteger();
        index.set(0);
        List<PackItem> packItems = LivePackUtil.filterPacks(userContext, cfLiveProducts, true).stream().map(cfLiveProduct -> {
            String durationUnit = cfLiveProduct.getSubscriptionOptions().getDuration().getUnit().name();
            Integer durationValue = cfLiveProduct.getSubscriptionOptions().getDuration().getValue();
            LiveDurationStringResponse liveDurationStringResponse = this.getLiveDurationString(cfLiveProduct);
            String durationString = liveDurationStringResponse.getLiveDurationString();
            boolean isDoubleDigit = liveDurationStringResponse.isDoubleDigit();
            String durationUnitString = durationValue <= 1 ? durationUnit : durationUnit + "S";

            String[] TransformTextGradientArray = Arrays.stream(TransformTextGradient.class.getEnumConstants()).map(Enum::name).toArray(String[]::new);
            String textGradientVariant = TransformTextGradientArray[index.get() % TransformTextGradientArray.length];
            index.getAndIncrement();

            ProductTaxBreakup priceBreakup = null;
            if (isPartOfGstSplitSegment) {
                priceBreakup = taxBreakupMap.get(cfLiveProduct.getProductId());
            }

            if (cfLiveProduct.getMembershipType() == CFLiveMembershipType.TRIAL) {
                PackItem trialPackItem = new PackItem(item.getProductType(), null,
                        "Free Trial",
                        "",
                        "",
                        "",
                        textGradientVariant,
                        new ArrayList<>(),
                        null,
                        getLivePackAction(userContext, cfLiveProduct, null),
                        isIOS ? durationValue + "-" + durationUnitString : "" + durationValue,
                        isIOS ? "" : durationUnitString,
                        false,
                        false,
                        "",
                        isIOS ? "ios" : "normal",
                        isIOS ? 24 : null, null,
                        true,
                        null,
                        null,
                        null,
                        isIOS ? durationValue + "-" + durationUnitString : "" + durationValue,
                        null,
                        null,
                        null,
                        null,
                        null
                );
                return trialPackItem;
            }
            Prices prices = liveProductPricesResponse.getPriceMap().get(cfLiveProduct.getProductId());
            if (isPartOfGstSplitSegment) {
                prices.setSellingPrice(priceBreakup.getBasePrice());
            }
            List<String> offers = new ArrayList<>();
            ProductPrice cfLiveProductPrice = cfLiveProduct.getPrice();
            AtomicBoolean foundCorporateOffer = new AtomicBoolean(false);

            if (prices != null) {
                cfLiveProductPrice.setMrp(BigDecimal.valueOf(prices.getMrp()));
                cfLiveProductPrice.setListingPrice(BigDecimal.valueOf(prices.getSellingPrice()));
                cfLiveProductPrice.setShowPriceCut(true);
                cfLiveProductPrice.setCurrency(cfLiveProduct.getPrice().getCurrency());
                cfLiveProductPrice.setDiscountText("" + prices.getDiscount());

                if (!CollectionUtils.isEmpty(prices.getBreakup())) {
                    prices.getBreakup().forEach(discountBreakup -> {
                        Offer offer = finalOfferMap.get(discountBreakup.getOfferId());

                        if (offer != null && !CollectionUtils.isEmpty(offer.getAddons())) {
                            List<String> offerDescriptions = offer.getAddons().stream().map(addon -> {
                                if (addon != null && !TextUtils.isEmpty(addon.getDescription())) {
                                    return addon.getDescription();
                                }
                                return null;
                            }).filter(Objects::nonNull).collect(Collectors.toList());
                            offers.addAll(offerDescriptions);
                        } else if (offer != null && !TextUtils.isEmpty(offer.getDescription())) {
                            offers.add(offer.getDescription());
                        }
                    });
                }
            }

            String currencyUnit = cfLiveProductPrice.getCurrency().equals("INR") ? OrderUtil.RUPEE_SYMBOL : cfLiveProductPrice.getCurrency();
            boolean showCostPrice = !(isIOS || cfLiveProductPrice.getMrp().equals(cfLiveProductPrice.getListingPrice()));
            PackItem packItem = new PackItem(item.getProductType(), showCostPrice ? "" + cfLiveProductPrice.getMrp().intValue() : null,
                    "" + cfLiveProductPrice.getListingPrice().intValue(),
                    getLivePerMonthPrice(cfLiveProduct, prices),
                    getLivePerMonthPriceWithoutDuration(cfLiveProduct, prices),
                    currencyUnit,
                    textGradientVariant,
                    getPackOffersFromOffers(offers),
                    null,
                    getLivePackAction(userContext, cfLiveProduct, null),
                    isIOS ? cfLiveProduct.getTitle() : durationString,
                    isIOS ? "" : durationUnitString,
                    isDoubleDigit,
                    false,
                    "",
                    isIOS ? "ios" : "normal",
                    isIOS ? 24 : null, null,
                    false,
                    priceBreakup,
                    null,
                    null,
                    isIOS ? cfLiveProduct.getTitle() : durationString,
                    null,
                    null,
                    null,
                    null,
                    null
            );
            return packItem;
        }).collect(Collectors.toList());
        this.isV2 = true;
        if (CollectionUtils.isEmpty(packItems)) {
            log.error("live_pack_browse pack items empty found");
            return null;
        }
        Action restorePurchaseAction = getLiveRestorePurchaseAction(isIOS);

        FitnessPackHeader header = item.getHeader();
        Boolean hideHeaderFadeOffset = header.getImageSize() == HeaderImageSize.LARGE;
        Double headerImageAspectRatio = header.getImageSize() == HeaderImageSize.LARGE ? 1.98 : 4.78;

        if (expandedMembershipPack == null) {
            this.expandedMembershipPack = new MembershipPackWidgetData(
                    packItems,
                    header.getHeaderTitle(),
                    header.getHeaderSubTitle(),
                    header.getHeaderOffers(),
                    header.getHeaderImage(),
                    headerImageAspectRatio,
                    hideHeaderFadeOffset,
                    restorePurchaseAction
            );
        } else {
            this.collapsedMembershipPack = new MembershipPackWidgetData(
                    packItems,
                    header.getHeaderTitle(),
                    header.getHeaderSubTitle(),
                    header.getHeaderOffers(),
                    header.getHeaderImage(),
                    headerImageAspectRatio,
                    hideHeaderFadeOffset,
                    restorePurchaseAction
            );
        }
        item.setPacks(packItems);
        return Collections.singletonList(this);
    }

    private String getLivePerMonthPriceWithoutDuration(CFLiveProduct pack, Prices productPrice) {
        SubscriptionOptions subscriptionOptions = pack.getSubscriptionOptions();
        DurationUnit durationUnit = subscriptionOptions.getDuration().getUnit();
        long price = -1;
        if (durationUnit == DurationUnit.MONTH) {
            price = Math.round(productPrice.getSellingPrice() / subscriptionOptions.getDuration().getValue());
        }
        if (durationUnit == DurationUnit.YEAR) {
            price = Math.round(productPrice.getSellingPrice() / (12 * subscriptionOptions.getDuration().getValue()));
        }
        if (price > 0) {
            return "" + price;
        }
        return "";
    }

    private String getLivePerMonthPrice(CFLiveProduct pack, Prices productPrice) {
        String perMonthPriceWithoutDuration = this.getLivePerMonthPriceWithoutDuration(pack, productPrice);

        if (perMonthPriceWithoutDuration == "") {
            return "";
        }

        return perMonthPriceWithoutDuration + "/month*";
    }

    private List<PackOffer> getPackOffersFromOffers(List<String> offers) {
        if (CollectionUtils.isEmpty(offers)) {
            return new ArrayList<>();
        }
        return offers.stream().map(offer -> {
            if (TextUtils.isEmpty(offer.trim()))
                return null;
            return new PackOffer(offer, null, null);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Action getLivePackAction(UserContext userContext, CFLiveProduct cfLiveProduct, String bucketId) {
        Boolean isLoggedIn = userContext.getSessionInfo().getIsUserLoggedIn();

        OrderProduct orderProduct = new OrderProduct();
        orderProduct.setProductId(cfLiveProduct.getProductId());
        orderProduct.setProductType(ProductType.CF_LIVE);
        orderProduct.setQuantity(1);

        OrderProductOption orderProductOption = new OrderProductOption();
        orderProductOption.setIsPack(true);
        orderProduct.setOption(orderProductOption);

        orderProduct.setAppleIAPProductDetails(cfLiveProduct.getAppleIAPProductDetails());
        orderProduct.setAndroidIAPProductDetails(cfLiveProduct.getAndroidIAPProductDetails());

        Action action = new Action();

        if (!isLoggedIn) {
            action = ActionUtil.getLoginAction("");

            return action;
        }
        boolean isAppOnly = !AppUtil.isWeb(userContext) && !AppUtil.isTVApp(userContext.getSessionInfo().getOrderSource()) && !AppUtil.isInternationalApp(userContext);

        Float appVersion = userContext.getSessionInfo().getAppVersion();
        boolean versionCheck = appVersion >= 9.63f;

        if (cfLiveProduct.getMembershipType() == CFLiveMembershipType.TRIAL) {
            action.setActionType(ActionType.NAVIGATION);
            action.setUrl("curefit://fl_form?formId=Home_Guidance_Onboarding_v2&fillPreviousResponse=true&prefillFormId=Home_Guidance_Onboarding&activateTrial=true");
            action.setTitle("");
            return action;
        }


        action.setActionType(ActionType.GET_LIVE_PACK);
        action.setTitle("");
        action.setUrl("curefit://payment");

        LiveOrderPayload liveOrderPayload = new LiveOrderPayload();
        liveOrderPayload.setOrderProducts(Collections.singletonList(orderProduct));
        liveOrderPayload.setUseFitcash(!("ios".equalsIgnoreCase(userContext.getSessionInfo().getOsName())));
        liveOrderPayload.setOffersVersion(3);

        action.setPayload(liveOrderPayload);

        return action;
    }

    private Action getLiveRestorePurchaseAction(Boolean isIOS) {
        if (!isIOS) {
            return null;
        }
        Action action = new Action();
        action.setActionType(ActionType.RESTORE_PURCHASE);
        action.setTitle("RESTORE PURCHASES");

        AnalyticsData analyticsData = new AnalyticsData();
        analyticsData.setEventKey("applerestoreclicked");

        action.setAnalyticsData(analyticsData);
        return action;
    }

    private LiveDurationStringResponse getLiveDurationString(CFLiveProduct cfLiveProduct) {
        if (cfLiveProduct.getTitle().contains("+")) {
            String[] splittedTitle = cfLiveProduct.getTitle().split("\\+");
            String[] laterParts = splittedTitle[1].split(" ");
            String durationString = splittedTitle[0].trim() + "+" + laterParts[0].trim();
            return new LiveDurationStringResponse(durationString, true);
        }
        return new LiveDurationStringResponse(cfLiveProduct.getSubscriptionOptions().getDuration().getValue().toString(), false);
    }

    private boolean isSourceCombinationNotSupported(RequestSource requestSource, Tenant tenant) {
        return Objects.equals(requestSource.getSource(), "CUREFIT_NEW_WEBSITE") && Objects.equals(requestSource.getOsName(), "ios") && tenant == Tenant.CUREFIT_APP;
    }

    public String getLuxGymPriceForCenter(CenterEntry center, UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        Integer centerServiceId = Math.toIntExact((CultUtil.isHybridCultCenter(center) ? center.getLinkedCenterId() : center.getId()));
        List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                ProductType.LUX_FITNESS_PRODUCT, null, false,
                userContext, centerServiceId
        );
        Integer minPerMonthPrice = null;
        for (AugmentedOfflineFitnessPack pack: packs) {
            HashMap<String, Object> pricingResponse = this.getPackDetailsMap(userContext, interfaces, pack, null, null);
            int perMonthPrice = (int) Math.round(Double.parseDouble((String) pricingResponse.get("perMonthPriceWithoutDuration")));
            minPerMonthPrice = Objects.isNull(minPerMonthPrice) ? perMonthPrice : Math.min(minPerMonthPrice, perMonthPrice);
        }
        if (minPerMonthPrice != null) {
            return "₹" + minPerMonthPrice;
        }
        return null;
    }

    public HashMap<String, String> getFinalPricingFromProductId(UserContext userContext, ProductType productType, String productId, Boolean isSelect, ServiceInterfaces interfaces, Long centerServiceId) throws Exception {
        HashMap<String, String> packData = new HashMap<>();
        if (isSelect && Objects.isNull(centerServiceId)) return packData;
        CenterEntry center = null;
        if (isSelect) {
            center = interfaces.centerService.getCenterDetails(centerServiceId, false, null, null).get();
            if (Objects.isNull(center)) return packData;
            return SelectPacksUtils.getCenterPackDetails(center, Collections.singletonList(interfaces.catalogueServicePMS.getOfflineFitnessPackById(productId)), userContext, interfaces, false);
        } else if (!isSelect) {
            List<String> productIds = Collections.singletonList(productId);
            UserEntry user = userContext.getUserEntryCompletableFuture().get();
            String callSource = AppUtil.callSource(userContext.getSessionInfo().getApiKey(), interfaces.apiKeyService);

            City city = userContext.getUserProfile().getCity();

            CultProductPricesResponse cultPackProductPricesResponse = null;
            GymFitProductPricesResponse gymFitProductPricesResponse = null;
            PackOfferDetails offerDetails = null;
            UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);

            Integer packDuration = 1;
            if (Objects.equals(productType, ProductType.FITNESS)) {
                OfflineFitnessPack pack = interfaces.catalogueServicePMS.getOfflineFitnessPackById(productId);
                cultPackProductPricesResponse = (CultProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                        productType, userContext, productIds, pack.getClientMetadata().getCityId(), null
                ).get();
                offerDetails = this.getOfferDetails(pack, cultPackProductPricesResponse);
                packDuration = pack.getProduct().getDurationInDays().intValue();
            } else {
                OfflineFitnessPack pack = interfaces.catalogueServicePMS.getOfflineFitnessPackById(productId);
                packDuration = pack.getProduct().getDurationInDays().intValue();
                gymFitProductPricesResponse = (GymFitProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                        productType, userContext, productIds, pack.getClientMetadata().getCityId(), null
                ).get();
                offerDetails = this.getOfferDetails(pack, gymFitProductPricesResponse);
            }

            Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                    ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();

            packData.put("offerPrice", String.valueOf((int) Math.ceil(offerPrice)));
            Integer productDurationInMonths = packDuration / 30;
            packData.put("productDurationInMonths", String.valueOf(productDurationInMonths));
        }

        return packData;
    }

    public HashMap<String, Object> getPackDetailsMap(UserContext userContext,ServiceInterfaces interfaces, AugmentedOfflineFitnessPack pack, String SemanticId, WidgetContext widgetContext) throws Exception {
        HashMap<String, Object> packData = new HashMap<>();
        if (Objects.isNull(pack)) return packData;
        List<String> productIds = Collections.singletonList(pack.getId());
        UserEntry user = userContext.getUserEntryCompletableFuture().get();

        Integer centerServiceId = (Objects.nonNull(pack.getRestrictions()) && CollectionUtils.isNotEmpty(pack.getRestrictions().getCenters()))
                ? pack.getRestrictions().getCenters().getFirst() : null;

        CultProductPricesResponse cultPackProductPricesResponse = null;
        GymFitProductPricesResponse gymFitProductPricesResponse = null;
        PlayProductPricesResponse playProductPricesResponse = null;
        PackOfferDetails offerDetails = null;
        boolean isPartOfGstSplitSegment = AppUtil.doesUserBelongToGstSplitSegment(interfaces, interfaces.getEnvironmentService(), userContext);
        Map<String, ProductTaxBreakup> taxBreakupMap = null;

        if (Objects.equals(pack.getProduct().getProductType(), ProductType.FITNESS)) {
            cultPackProductPricesResponse = (CultProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.FITNESS, userContext, productIds, pack.getClientMetadata().getCityId(), null
            ).get();
            if (isPartOfGstSplitSegment) {
                taxBreakupMap = interfaces.offerService.getTaxBreakUpForCultProducts(cultPackProductPricesResponse);
            }
            offerDetails = this.getOfferDetails(pack, cultPackProductPricesResponse);
        }
        else if (
            Objects.equals(pack.getProduct().getProductType(), ProductType.GYMFIT_FITNESS_PRODUCT) ||
            Objects.equals(pack.getProduct().getProductType(), ProductType.LUX_FITNESS_PRODUCT)
        ) {
            gymFitProductPricesResponse = (GymFitProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.GYMFIT_FITNESS_PRODUCT, userContext, productIds, pack.getClientMetadata().getCityId(), centerServiceId != null ? String.valueOf(centerServiceId) : null
            ).get();
            if (isPartOfGstSplitSegment) {
                taxBreakupMap = interfaces.offerService.getTaxBreakUpForGymProducts(gymFitProductPricesResponse);
            }
            offerDetails = this.getOfferDetails(pack, gymFitProductPricesResponse);
        } else if (Objects.equals(pack.getProduct().getProductType(), ProductType.PLAY)) {
            playProductPricesResponse = (PlayProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.PLAY, userContext, productIds, pack.getClientMetadata().getCityId(), null
            ).get();

            if (isPartOfGstSplitSegment) {
                taxBreakupMap = interfaces.offerService.getTaxBreakUpForPlayProducts(playProductPricesResponse);
            }
        }

        if (Objects.nonNull(offerDetails)) {
            List<OfferMini> offerMinis = offerDetails.getOffers();

            Integer duration = pack.getProduct().getDurationInDays().intValue();
            Integer packDuration = duration;

            // Remove No cost emi offers.
            offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

            // Remove fit-club from offers as there is a separate widget
            List<OfferMini> filterOfferMinis = new ArrayList<OfferMini>();
            for (OfferMini offer : offerMinis) {
                if (offer.getAddons() != null) {
                    filterOfferMinis.add(offer);
                    for (Addon addon : offer.getAddons()) {
                        if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.GYMFIT_EXTENSION
                                && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                            packDuration = packDuration + addon.getConfig().getCount();
                            break;
                        } else if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.CULT_EXTENSION
                                && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                            packDuration = packDuration + addon.getConfig().getCount();
                            break;
                        } else if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.LUX_EXTENSION
                                && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                            packDuration = packDuration + addon.getConfig().getCount();
                            break;
                        }
                    }
                } else if (offer.getDescription() != null) {
                    filterOfferMinis.add(offer);
                }
            }

            offerDetails.setOffers(filterOfferMinis);

            long platformFeeValue = getPlatformFeeValue(pack.getAugments().getExtraCharges());

            Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                    ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();
            offerPrice += (!isPartOfGstSplitSegment ? platformFeeValue : 0);

            Long mrp = offerDetails.getPrice().getMrp().longValue() + (!isPartOfGstSplitSegment ? platformFeeValue : 0);

            ProductTaxBreakup priceBreakup = null;
            if (isPartOfGstSplitSegment) {
                priceBreakup = taxBreakupMap.get(pack.getId());
                offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
            }

            String actionUrl = getPacksPageAction(pack.getId());
            Action action = new Action(actionUrl, "BUY", ActionType.NAVIGATION);
            HashMap<String, String> meta = new HashMap<String, String>();
            meta.put("semanticId", SemanticId);
            if (SemanticId == "GYM_PACK") {
                meta.put("selectedSku", "PRO");
            } else if (SemanticId == "CULT_UNLIMITED") {
                meta.put("selectedSku", "ELITE");
            } else {
                meta.put("selectedSku", SemanticId);
            }
            action.setMeta(meta);

            Integer totalDuration = duration;
            double numberOfMonths = Math.floor((packDuration.doubleValue() / 30) * 2) / 2.0;
            Integer totalTickets = null;
            if (Objects.equals(pack.getProduct().getProductType(), ProductType.LUX_FITNESS_PRODUCT)) {
                Optional<BenefitEntry> luxGxBenefitOptional = pack.getProduct().getBenefits().stream().filter(benefitEntry -> benefitEntry.getName().equalsIgnoreCase("lux_gx")).findFirst();
                BenefitEntry luxGxBenefit = null;
                if (luxGxBenefitOptional.isPresent()) {
                    luxGxBenefit = luxGxBenefitOptional.get();
                    totalTickets = luxGxBenefit.getTickets();
                    numberOfMonths = totalTickets;
                }
            }
            String perMonthPriceWithoutDuration = (numberOfMonths < 1)
                    ? "" + Math.ceil(offerPrice.doubleValue())
                    : "" + Math.round((Math.ceil(offerPrice.doubleValue()) / numberOfMonths));
            long productDurationInMonths =  Objects.nonNull(totalTickets) ? totalTickets.longValue() : Math.round(Math.floor(totalDuration / 30));

            HashMap<String, Object> analyticsData = new HashMap<>();
            analyticsData.put("sku", SemanticId);
            analyticsData.put("month", String.valueOf(productDurationInMonths));
            analyticsData.put("widgetContext", widgetContext);
            action.setAnalyticsData(analyticsData);

            packData.put("perMonthPriceWithoutDuration", perMonthPriceWithoutDuration);
            packData.put("totalPackPrice", String.valueOf(offerPrice));
            packData.put("mrp", mrp);
            packData.put("packDuration", String.valueOf(productDurationInMonths));
            packData.put("offers", offerDetails.getOffers());
            packData.put("action", action);
            packData.put("benefits", pack.getAugments().getExhaustiveBenefitList());
            if (priceBreakup != null) {
                long tax = (long) Math.ceil(priceBreakup.getTaxAmount());
                packData.put("taxes_fees", String.valueOf(tax + platformFeeValue));
            }
        }

        log.info("Pack Data :" + packData.toString());
        return packData;
    }

    public HashMap<String, Object> getPlayPackDetailsMap(UserContext userContext,ServiceInterfaces interfaces, FitnessPack pack, String semanticId, WidgetContext widgetContext) throws Exception {
        HashMap<String, Object> packData = new HashMap<>();
        if (Objects.isNull(pack)) return packData;
        List<String> productIds = Collections.singletonList(pack.getProductId());

        PlayProductPricesResponse playProductPricesResponse = null;
        PackOfferDetails offerDetails = null;
        boolean isPartOfGstSplitSegment = AppUtil.doesUserBelongToGstSplitSegment(interfaces, interfaces.getEnvironmentService(), userContext);
        Map<String, ProductTaxBreakup> taxBreakupMap = null;

        if (Objects.equals(pack.getProductType(), ProductType.PLAY)) {
            playProductPricesResponse = (PlayProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.PLAY, userContext, productIds, String.valueOf(pack.getCityId()), null
            ).get();

            if (isPartOfGstSplitSegment) {
                taxBreakupMap = interfaces.offerService.getTaxBreakUpForPlayProducts(playProductPricesResponse);
            }

            offerDetails = this.getPlayOfferDetails(pack, playProductPricesResponse);
        }

        if (Objects.nonNull(offerDetails)) {
            List<OfferMini> offerMinis = offerDetails.getOffers();

            Integer duration = pack.getDuration();
            Integer packDuration = duration;

            // Remove No cost emi offers.
            offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

            // Remove fit-club from offers as there is a separate widget
            List<OfferMini> filterOfferMinis = new ArrayList<OfferMini>();
            for (OfferMini offer : offerMinis) {
                if (offer.getAddons() != null) {
                    filterOfferMinis.add(offer);
                    for (Addon addon : offer.getAddons()) {
                        if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.CULT_PLAY_EXTENSION
                                && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                            packDuration = packDuration + addon.getConfig().getCount();
                            break;
                        }
                    }
                } else if (offer.getDescription() != null) {
                    filterOfferMinis.add(offer);
                }
            }

            offerDetails.setOffers(filterOfferMinis);

            long platformFeeValue = getPlatformFeeValue(pack.getExtraCharges());

            Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                    ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();
            offerPrice += (!isPartOfGstSplitSegment ? platformFeeValue : 0);

            Long mrp = offerDetails.getPrice().getMrp().longValue() + (!isPartOfGstSplitSegment ? platformFeeValue : 0);

            ProductTaxBreakup priceBreakup = null;
            if (isPartOfGstSplitSegment) {
                ProductTaxBreakup priceBreakups = taxBreakupMap.get(pack.getProductId());
                priceBreakup = new ProductTaxBreakup(priceBreakups.getBasePrice(), priceBreakups.getTaxAmount(), priceBreakups.getTaxPercent());
                offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
            }

            String actionUrl = getPacksPageAction(pack.getProductId());
            Action action = new Action(actionUrl, "BUY", ActionType.NAVIGATION);
            HashMap<String, String> meta = new HashMap<String, String>();
            meta.put("semanticId", semanticId);
            meta.put("selectedSku", "PLAY");
            action.setMeta(meta);

            double numberOfMonths = Math.floor((packDuration.doubleValue() / 30) * 2) / 2.0;
            String perMonthPriceWithoutDuration = (numberOfMonths < 1)
                    ? "" + Math.ceil(offerPrice.doubleValue())
                    : "" + Math.round((Math.ceil(offerPrice.doubleValue()) / numberOfMonths));
            long productDurationInMonths = Math.round(Math.floor((double) duration / 30));

            HashMap<String, Object> analyticsData = new HashMap<>();
            analyticsData.put("sku", semanticId);
            analyticsData.put("month", String.valueOf(productDurationInMonths));
            analyticsData.put("widgetContext", widgetContext);
            action.setAnalyticsData(analyticsData);

            packData.put("perMonthPriceWithoutDuration", perMonthPriceWithoutDuration);
            packData.put("totalPackPrice", String.valueOf(offerPrice));
            packData.put("mrp", mrp);
            packData.put("packDuration", String.valueOf(productDurationInMonths));
            packData.put("offers", offerDetails.getOffers());
            packData.put("action", action);
            packData.put("benefits", pack.getExhaustivePackBenefitList());
            if (priceBreakup != null) {
                long tax = (long) Math.ceil(priceBreakup.getTaxAmount());
                packData.put("taxes_fees", String.valueOf(tax + platformFeeValue));
            }
        }

        return packData;
    }

    public HashMap<String, Object> getElitePackItemDetails(
            UserContext userContext, ServiceInterfaces interfaces, String centerCityId, Integer centerCultCityId
    ) throws Exception {

        boolean isPartOfGstSplitSegment = AppUtil.doesUserBelongToGstSplitSegment(interfaces, interfaces.getEnvironmentService(), userContext);

        UserProfile userProfile = userContext.getUserProfile();
        SessionInfo sessionInfo = userContext.getSessionInfo();

        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
        String callSource = AppUtil.callSource(sessionInfo.getApiKey(), interfaces.apiKeyService);
        List<AugmentedOfflineFitnessPack> packProducts = this.getFitnessPackPromise(userContext, interfaces, centerCityId, ProductSubType.GENERAL);

        if (CollectionUtils.isEmpty(packProducts)) {
            return null;
        }

        List<String> productIds = packProducts.stream().map(pack -> pack.getProductId()).toList();

        CultProductPricesResponse cultPackProductPricesResponse = (CultProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                ProductType.FITNESS, userContext, productIds, centerCityId, null
        ).get();

        Map<String, ProductTaxBreakup> taxBreakupMap = null;
        if (isPartOfGstSplitSegment) {
           taxBreakupMap = interfaces.offerService.getTaxBreakUpForCultProducts(cultPackProductPricesResponse);
        }

        CultService cultService = interfaces.getCultService();
        String minPriceString = null;
        Integer minPrice = Integer.MAX_VALUE;
        OfflineFitnessPack topPack = null;
        List<PackOfferDetails> packOfferDetails = new ArrayList<PackOfferDetails>();
        for (AugmentedOfflineFitnessPack packProduct : packProducts) {
            PackOfferDetails offerDetails = this.getOfferDetails(packProduct, cultPackProductPricesResponse);
            List<OfferMini> offerMinis = offerDetails.getOffers();
            Integer duration = packProduct.getProduct().getDurationInDays().intValue();
            Integer packDuration = duration;
            if (packDuration.equals(Integer.valueOf(365)) && topPack == null) {
                topPack = packProduct;
            }

            // Remove No cost emi offers.
            offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

            // Remove fit-club from offers as there is a separate widget
            List<OfferMini> filterOfferMinis = new ArrayList<OfferMini>();
            for (OfferMini offer : offerMinis) {
                if (offer.getAddons() != null) {
                    filterOfferMinis.add(offer);
                    for (Addon addon : offer.getAddons()) {
                        if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.CULT_EXTENSION
                                && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                            packDuration = packDuration + addon.getConfig().getCount();
                            break;
                        }
                    }
                } else if (offer.getDescription() != null) {
                    filterOfferMinis.add(offer);
                }
            }

            offerDetails.setOffers(filterOfferMinis);
            packOfferDetails.add(offerDetails);

            String currency = offerDetails.getPrice().getCurrency();
            String currencyUnit = currency.equals("INR") ? OrderUtil.RUPEE_SYMBOL : currency;

            Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                    ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();

            ProductTaxBreakup priceBreakup = null;
            if (isPartOfGstSplitSegment) {
                priceBreakup = taxBreakupMap.get(packProduct.getProductId());
                offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
            }
            double numberOfMonths = Math.floor((packDuration.doubleValue() / 30) * 2) / 2.0;
            String perMonthPriceWithoutDuration = "" + Math.round((Math.ceil(offerPrice.doubleValue()) / numberOfMonths));
            minPrice = Math.min(Integer.parseInt(perMonthPriceWithoutDuration), minPrice);
            minPriceString = currencyUnit + minPrice + "/month*";
        }
        if (topPack == null) topPack = packProducts.get(0);
        List<BenefitEntry> packBenefits =  topPack.getProduct().getBenefits();
        String awayTickets = null;
        if (!CollectionUtils.isEmpty(packBenefits)) {
            for (BenefitEntry benefit : packBenefits) {
                if (benefit.getName().equals("CULT_AWAY")) {
                    awayTickets = benefit.getTickets().toString();
                }
            }
        }
        String pauseDays = null;
        if (topPack.getProduct().getPauseDays() != null) {
            pauseDays = String.valueOf(topPack.getProduct().getPauseDays().intValue());
        }
        HashMap<String, Object> packData = new HashMap<>();
        packData.put("minPrice", minPriceString);
        packData.put("minMonthlyPrice", String.valueOf(minPrice));
        packData.put("pauseDays", pauseDays);
        packData.put("sameCitySessions", "Unlimited");
        packData.put("awayCitySessions", awayTickets);
        packData.put("fitnessPacksList", packProducts);
        packData.put("topPackDuration", String.valueOf(topPack.getProduct().getDurationInDays().intValue() / 30));
        return packData;
    }

    public HashMap<String, Object> getProPackItemDetails(UserContext userContext, ServiceInterfaces interfaces, String cityId) throws Exception {
        boolean isPartOfGstSplitSegment = AppUtil.doesUserBelongToGstSplitSegment(interfaces, interfaces.getEnvironmentService(), userContext);
        List<OfflineFitnessPack> gymfitPacks = userContext.getRequestCache().getNonAugmentedPackList(
                ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.GENERAL, false,
                userContext, null
        );
        gymfitPacks.sort(Collections.reverseOrder(Comparator.comparing(pack -> pack.getProduct().getDurationInDays())));
        if (CollectionUtils.isEmpty(gymfitPacks)) {
            return null;
        }

        List<String> productIds = gymfitPacks.stream().map(pack -> pack.getId()).collect(Collectors.toList());
        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
        GymFitProductPricesResponse gymFitProductPricesResponse = (GymFitProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                ProductType.GYMFIT_FITNESS_PRODUCT, userContext, productIds, cityId, null
        ).get();
        Map<String, ProductTaxBreakup> taxBreakupMap = null;
        if (isPartOfGstSplitSegment) {
            taxBreakupMap = interfaces.offerService.getTaxBreakUpForGymProducts(gymFitProductPricesResponse);
        }

        List<PackOfferDetails> packOfferDetails = new ArrayList<PackOfferDetails>();
        // sort packs in reverse order
        gymfitPacks.sort(Collections.reverseOrder(Comparator.comparing(pack -> pack.getProduct().getDurationInDays())));

        int distance = (int) gymfitPacks.stream().filter(pack -> pack.getProduct().getDurationInDays() >= 730).count();
        if (distance > 0) { // if 24 months pack exists in list
            Collections.rotate(gymfitPacks, distance * -1); // to rotate in reverse manner
        }

        List<OfflineFitnessPack> fitnessPacksList = new ArrayList<>(gymfitPacks);

        String minPriceString = null;
        Integer minPrice = Integer.MAX_VALUE;
        OfflineFitnessPack topPack = null;
        for (OfflineFitnessPack pack : gymfitPacks) {
            PackOfferDetails offerDetails = this.getOfferDetails(pack, gymFitProductPricesResponse);
            List<OfferMini> offerMinis = offerDetails.getOffers();
            if(Objects.equals(pack.getProduct().getDurationInDays().intValue(), Integer.valueOf(365)) && topPack == null){
                topPack = pack;
            }

            Integer duration = pack.getProduct().getDurationInDays().intValue();
            Integer packDuration = duration;

            // Remove No cost emi offers.
            offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

            // Remove fit-club from offers as there is a separate widget
            List<OfferMini> filterOfferMinis = new ArrayList<OfferMini>();
            for (OfferMini offer : offerMinis) {
                if (offer.getAddons() != null) {
                    filterOfferMinis.add(offer);
                    for (Addon addon : offer.getAddons()) {
                        if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.GYMFIT_EXTENSION
                                && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                            packDuration = packDuration + addon.getConfig().getCount();
                            break;
                        }
                    }
                } else if (offer.getDescription() != null) {
                    filterOfferMinis.add(offer);
                }
            }

            offerDetails.setOffers(filterOfferMinis);
            packOfferDetails.add(offerDetails);

            Long offerPrice = offerDetails.getPrice().getListingPrice() != null
                    ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();


            ProductTaxBreakup priceBreakup = null;
            if (isPartOfGstSplitSegment) {
                priceBreakup = taxBreakupMap.get(pack.getId());
                offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
            }

            String currency = offerDetails.getPrice().getCurrency();
            double numberOfMonths = Math.floor((packDuration.doubleValue() / 30) * 2) / 2.0;
            String perMonthPriceWithoutDuration = "" + Math.round((Math.ceil(offerPrice.doubleValue()) / numberOfMonths));
            String currencyUnit = currency.equals("INR") ? OrderUtil.RUPEE_SYMBOL : currency;
            minPrice = Math.min(Integer.parseInt(perMonthPriceWithoutDuration), minPrice);
            minPriceString = currencyUnit + minPrice + "/month*";
        }

        HashMap<String, Object> packData = new HashMap<>();
        packData.put("minPrice", minPriceString);
        packData.put("minMonthlyPrice", String.valueOf(minPrice));
        packData.put("fitnessPacksList", fitnessPacksList);
        if (Objects.isNull(topPack)) {
            topPack = gymfitPacks.get(0);
        }
        if (Objects.nonNull(topPack)) {
            String pauseDays = null;
            if (topPack.getProduct().getPauseDays() != null) {
                pauseDays = String.valueOf(topPack.getProduct().getPauseDays().intValue());
            }
            packData.put("pauseDays", pauseDays);
            packData.put("topPackDuration", String.valueOf(topPack.getProduct().getDurationInDays().intValue() / 30));
        }
        return packData;
    }

    public HashMap<String, String> getPlayPackItemDetails(
        UserContext userContext,
        ServiceInterfaces interfaces,
        Map<String, String> queryParams
    ) throws Exception {

        boolean isPartOfGstSplitSegment = AppUtil.doesUserBelongToGstSplitSegment(interfaces, interfaces.getEnvironmentService(), userContext);
        Long centerId = queryParams.get("centerId") != null ? Long.parseLong(queryParams.get("centerId")) : null;

        CenterEntry selectedCenter = null;
        if (centerId != null) {
            selectedCenter = interfaces.centerService.getCenterDetails(centerId, false, null, null).get();
        }

        UserProfile userProfile = userContext.getUserProfile();
        SessionInfo sessionInfo = userContext.getSessionInfo();

        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
        String callSource = AppUtil.callSource(sessionInfo.getApiKey(), interfaces.apiKeyService);
        CultPackBrowseProductType productType = CultPackBrowseProductType.PLAY;
        List<FitnessPack> playPacks = this.getPlayPacksBySKU(productType, queryParams, userContext, interfaces);
        if (playPacks == null || CollectionUtils.isEmpty(playPacks)) {
            return null;
        }
        playPacks.sort(Collections.reverseOrder(Comparator.comparing(FitnessPack::getDuration)));
        List<String> productIds = playPacks.stream().map(pack -> pack.getProductId()).collect(Collectors.toList());

        PlayProductPricesResponse playProductPricesResponse = (PlayProductPricesResponse) userContext.getRequestCache().getOfferResponse(
            ProductType.PLAY, userContext, productIds, userProfile.getCity().getCityId(), null
        ).get();

        Map<String, ProductTaxBreakup> taxBreakupMap = null;
        if (isPartOfGstSplitSegment) {
            taxBreakupMap = interfaces.offerService.getTaxBreakUpForPlayProducts(playProductPricesResponse);
        }

        String minPriceString = null;
        Integer minPrice = Integer.MAX_VALUE;
        FitnessPack topPack = null;
        for (FitnessPack packProduct : playPacks) {
            PackOfferDetails offerDetails = this.getPlayOfferDetails(packProduct, playProductPricesResponse);
            List<OfferMini> offerMinis = offerDetails.getOffers();
            Integer packDuration = packProduct.getDuration();
            if (packDuration.equals(365) && topPack == null) {
                topPack = packProduct;
            }

            // Remove No cost emi offers.
            offerMinis = OfferUtil.segregateNoCostEMIOffers(offerMinis).get("OTHER_OFFERS");

            List<OfferMini> filterOfferMinis = new ArrayList<>();
            for (OfferMini offer : offerMinis) {
                if (offer.getAddons() != null) {
                    filterOfferMinis.add(offer);
                    for (Addon addon : offer.getAddons()) {
                        if (addon != null && addon.getAddonType() != null && addon.getAddonType() == AddonType.CULT_PLAY_EXTENSION
                                && addon.getConfig() != null && addon.getConfig().getCount() != null) {
                            packDuration = packDuration + addon.getConfig().getCount();
                            break;
                        }
                    }
                } else if (offer.getDescription() != null) {
                    filterOfferMinis.add(offer);
                }
            }

            if (isPackNotSupportedDueToCenterTermination(packDuration, selectedCenter)) {
                continue;
            }

            offerDetails.setOffers(filterOfferMinis);

            String currency = offerDetails.getPrice().getCurrency();
            String currencyUnit = currency.equals("INR") ? OrderUtil.RUPEE_SYMBOL : currency;

            long offerPrice = offerDetails.getPrice().getListingPrice() != null
                    ? offerDetails.getPrice().getListingPrice().longValue() : offerDetails.getPrice().getMrp().longValue();

            ProductTaxBreakup priceBreakup = null;
            if (isPartOfGstSplitSegment) {
                priceBreakup = taxBreakupMap.get(packProduct.getProductId());
                offerPrice = (long) Math.ceil(priceBreakup.getBasePrice());
            }
            double numberOfMonths = Math.round(packDuration.doubleValue() * 0.0328767);
            String perMonthPriceWithoutDuration = "" + Math.round((Math.ceil((double) offerPrice) / numberOfMonths));
            minPrice = Math.min(Integer.parseInt(perMonthPriceWithoutDuration), minPrice);
            minPriceString = currencyUnit + minPrice + "/month*";
        }
        if (topPack == null) topPack = playPacks.get(0);
        List<PackBenefit> packBenefits = topPack.getPackBenefits();
        String sameCitySession = null;
        if (!CollectionUtils.isEmpty(packBenefits)) {
            for (PackBenefit benefit : packBenefits) {
                // TODO: 20/11/23 Add logic of benefits here
                if (benefit.getBenefit().equals("PLAY")) {
                    if (benefit.getTickets() < 1000)
                        sameCitySession = benefit.getTickets().toString();
                    else
                        sameCitySession = "Unlimited";
                }
            }
        }

        String pauseDays = null;
        if (topPack.getPauseMaxDays() != null) {
            pauseDays = topPack.getPauseMaxDays().toString();
        }
        HashMap<String, String> packData = new HashMap<>();
        packData.put("minPrice", minPriceString);
        packData.put("minMonthlyPrice", String.valueOf(minPrice));
        packData.put("pauseDays", pauseDays);
        packData.put("sameCitySessions", sameCitySession);
        packData.put("topPackDuration", String.valueOf(topPack.getDuration() / 30));
        return packData;
    }

    private CFTextData getAdditionalPricetext(List<ExtraChargesResponse> extraChargesResponses, Double gstAmount) {

        CFTextData additionalPrice = null;

        // currently we have only platform fee so, filtering out that only. later this logic can be changed here based on different extra charges
        ExtraChargesResponse platformFeeCharges = extraChargesResponses.stream()
                .filter(extraCharge -> Objects.equals(extraCharge.getExtraChargeType(), ExtraChargeType.PLATFORM_FEE.toString()))
                .findFirst().orElse(null);

        if (Objects.isNull(gstAmount)) gstAmount = 0.0;

        if (Objects.nonNull(platformFeeCharges) && Objects.nonNull(platformFeeCharges.getExtraChargeFee()) && Objects.nonNull(platformFeeCharges.getExtraChargeFee().getTotalFee())) {
            int totalExtraChargesIncludingGST = (int) Math.ceil(gstAmount + platformFeeCharges.getExtraChargeFee().getTotalFee());
            additionalPrice = new CFTextData();
            additionalPrice.setText("[P5, #F0F0F0, + ₹" + totalExtraChargesIncludingGST +  " taxes & fees]");
            additionalPrice.setOpacity(0.6);
        } else if (gstAmount > 0.0) {
            int totalGSTAmount = (int) Math.ceil(gstAmount);
            additionalPrice = new CFTextData();
            additionalPrice.setText("[P5, #F0F0F0, + ₹" + totalGSTAmount +  " GST]");
            additionalPrice.setOpacity(0.6);
        }

        return additionalPrice;
    }

    private long getPlatformFeeValue (List<ExtraChargesResponse> extraChargesResponses) {
        if (extraChargesResponses == null || extraChargesResponses.isEmpty()) return 0;
        ExtraChargesResponse platformFeeCharges = extraChargesResponses.stream()
                .filter(extraCharge -> Objects.equals(extraCharge.getExtraChargeType(), ExtraChargeType.PLATFORM_FEE.toString()))
                .findFirst().orElse(null);
        if (Objects.nonNull(platformFeeCharges) && Objects.nonNull(platformFeeCharges.getExtraChargeFee()) && Objects.nonNull(platformFeeCharges.getExtraChargeFee().getTotalFee())) {
           return (long) Math.ceil(platformFeeCharges.getExtraChargeFee().getTotalFee());
        }
        return 0;
    }

    private String getAdditionalPriceAmount(List<ExtraChargesResponse> extraChargesResponses, Double gstAmount) {

        String additionalPrice = "";

        // currently we have only platform fee so, filtering out that only. later this logic can be changed here based on different extra charges
        ExtraChargesResponse platformFeeCharges = extraChargesResponses.stream()
                .filter(extraCharge -> Objects.equals(extraCharge.getExtraChargeType(), ExtraChargeType.PLATFORM_FEE.toString()))
                .findFirst().orElse(null);

        if (Objects.isNull(gstAmount)) gstAmount = 0.0;

        if (Objects.nonNull(platformFeeCharges) && Objects.nonNull(platformFeeCharges.getExtraChargeFee()) && Objects.nonNull(platformFeeCharges.getExtraChargeFee().getTotalFee())) {
            int totalExtraChargesIncludingGST = (int) Math.ceil(gstAmount + platformFeeCharges.getExtraChargeFee().getTotalFee());

            additionalPrice = " + ₹" + totalExtraChargesIncludingGST +  " Taxes & Fees";

        } else if (gstAmount > 0.0) {
            int totalGSTAmount = (int) Math.ceil(gstAmount);

            additionalPrice = "+ ₹" + totalGSTAmount +  " GST";

        }

        return additionalPrice;
    }



    boolean isPackNotSupportedDueToCenterTermination(Integer packDuration, CenterEntry selectedCenter) {
        // Filter packs based on termination date of the center
        if (selectedCenter != null && selectedCenter.getTerminationDate() != null) {
            Long terminationDate = selectedCenter.getTerminationDate().getTime();
            Instant currentTimeInstant = Instant.now();
            LocalDate localDate = LocalDate.ofInstant(currentTimeInstant, ZoneId.systemDefault());
            Instant startOfDay = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
            Long currentTimeMillis = startOfDay.toEpochMilli();

            Long durationInMs = packDuration * TimeUtil.oneDayInMillis;
            return currentTimeMillis + durationInMs >= terminationDate;
        } else {
            return false;
        }
    }
}