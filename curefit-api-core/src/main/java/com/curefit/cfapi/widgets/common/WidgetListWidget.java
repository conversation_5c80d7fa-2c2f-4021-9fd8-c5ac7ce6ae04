package com.curefit.cfapi.widgets.common;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.Debugger;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@ToString
public class WidgetListWidget extends BaseWidget {

    List<BaseWidget> widgets;
    List<String> widgetList;
    Debugger debugger;

    public WidgetListWidget() {
        this.widgetType = WidgetType.WIDGET_LIST_WIDGET;
    }

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {

        widgets = new ArrayList<>();
        debugger = Debugger.getDebuggerFromUserContext(userContext);
        debugger.msg("widgetList", widgetList);

        if (CollectionUtils.isNotEmpty(widgetList)) {
            List<CompletableFuture<BaseWidget>> futureWidgets = widgetList.parallelStream()
                .map(item -> AppUtil.widgetCompletableFuture(interfaces, userContext, item))
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .toList();

            widgets = futureWidgets.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        }

        debugger.msg("widgets", widgets);
        widgetList = null;
        debugger = null;
        return widgets;

    }

}
