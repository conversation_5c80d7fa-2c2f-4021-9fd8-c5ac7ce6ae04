package com.curefit.cfapi.widgets.fitness;

import com.curefit.cfapi.builder.vm.BuildWidgetResponse;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.widgets.ProductPriceView;
import com.curefit.cfapi.pojo.widgets.SKUType;
import com.curefit.cfapi.pojo.widgets.SkuPack;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.curefit.cfapi.constants.StringConstants.FS_CULT_PASS_TRIAL_USERS;
import static com.curefit.cfapi.constants.StringConstants.FS_GYM_PASS_TRIAL_USERS;
import static com.curefit.cfapi.constants.StringConstants.FS_LIVE_PASS_TRIAL_USERS;
import static com.curefit.cfapi.constants.StringConstants.PAGE_THEME_PARAM;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class SKUComparisonWidget extends BaseWidget {

    List<String> widgetIds;
    Action cultPassBlackAction;
    Action cultPassGoldAction;
    Action cultPassLiveAction;
    Action cultPassPlayAction;
    Action cultPassSelectAction;

    List<SkuPack> packs;
    SKUType recommendedPack;
    String recommendationText;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        this.packs = new ArrayList<>();
        Map<String, String> queryParams = widgetContext.getQueryParams();
        String pageTheme = queryParams != null ? queryParams.get(PAGE_THEME_PARAM) : null;

        CompletableFuture<BuildWidgetResponse> blackWidgetPromise = interfaces.getWidgetBuilder().buildWidgets(Collections.singletonList(this.widgetIds.get(0)), userContext, widgetContext);
        CompletableFuture<BuildWidgetResponse> goldWidgetPromise = interfaces.getWidgetBuilder().buildWidgets(Collections.singletonList(this.widgetIds.get(1)), userContext, widgetContext);
        CompletableFuture<BuildWidgetResponse> liveWidgetPromise = interfaces.getWidgetBuilder().buildWidgets(Collections.singletonList(this.widgetIds.get(2)), userContext, widgetContext);

        List<CompletableFuture<SkuPack>> skuPackItemPromises = new ArrayList<>();
        skuPackItemPromises.add(this.buildCultPassBlackSKUCard(blackWidgetPromise, userContext, queryParams, pageTheme));
        skuPackItemPromises.add(this.buildCultPassGoldSKUCard(goldWidgetPromise, userContext, queryParams, pageTheme));

        CompletableFuture<Boolean> isCultPassPlaySupportedFuture = CompletableFuture.completedFuture(AppUtil.isCultPassPlaySupported(userContext, interfaces.environmentService, interfaces.segmentEvaluatorService));
        CompletableFuture<Boolean> isCenterLevelPricingSupportedFuture = CompletableFuture.completedFuture(AppUtil.isCenterLevelPricingSupported(userContext, interfaces, interfaces.getEnvironmentService()));

        List<Boolean> segmentChecks = FutureUtil.allOf(List.of(isCultPassPlaySupportedFuture, isCenterLevelPricingSupportedFuture)).get().parallelStream().toList();
        Boolean isCultPassPlaySupported = segmentChecks.get(0);
        Boolean isCenterLevelPricingSupported = segmentChecks.get(1);

        if (this.widgetIds.size() >= 4) {
            if (isCultPassPlaySupported) {
                CompletableFuture<BuildWidgetResponse> playWidgetPromise = interfaces.getWidgetBuilder().buildWidgets(Collections.singletonList(this.widgetIds.get(3)), userContext, widgetContext);
                skuPackItemPromises.add(this.buildCultPassPlaySKUCard(playWidgetPromise, userContext, queryParams, pageTheme, interfaces, true));
            }
            if (isCenterLevelPricingSupported) {
                skuPackItemPromises.add(this.buildCultPassSelectSKUCard(true));
            }
        }

        skuPackItemPromises.add(this.buildCultPassLiveSKUCard(liveWidgetPromise, userContext, queryParams, pageTheme));

        try {
            SegmentSet<String> userPlatformSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                    RequestType.PLATFORM_SEGMENTS,
                    userContext).get();
            if (userPlatformSegments.contains(FS_CULT_PASS_TRIAL_USERS)) {
                this.recommendedPack = SKUType.BLACK;
                this.recommendationText = "interest in group classes & ELITE gyms";
            } else if (userPlatformSegments.contains(FS_GYM_PASS_TRIAL_USERS)) {
                this.recommendedPack = SKUType.GOLD;
                this.recommendationText = "interest in PRO gyms";
            } else if (userPlatformSegments.contains(FS_LIVE_PASS_TRIAL_USERS)) {
                this.recommendedPack = SKUType.LIVE;
                this.recommendationText = "interest in live workouts";
            }
        } catch (Exception e) {}

        CompletableFuture<List<SkuPack>> skuPackItemsPromise = FutureUtil.allOf(skuPackItemPromises);
        this.packs = skuPackItemsPromise.get().parallelStream().filter(Objects::nonNull).collect(Collectors.toList());


        this.widgetIds = null;
        this.cultPassBlackAction = null;
        this.cultPassGoldAction = null;
        this.cultPassLiveAction = null;
        this.cultPassSelectAction = null;
        return Collections.singletonList(this);
    }

    private CompletableFuture<SkuPack> buildCultPassBlackSKUCard(CompletableFuture<BuildWidgetResponse> widgetPromise, UserContext userContext, Map<String, String> queryParams, String pageTheme) {
        return widgetPromise.thenApply(response -> {
            SkuPack pack = null;
            try { pack = SkuPackUtil.buildSkuPack(response, SKUType.BLACK, pageTheme); }
            catch (Exception e) { throw new RuntimeException(e); }
            pack.setCardAction(cultPassBlackAction);
            return pack;
        }).exceptionally(e -> null);
    }

    private CompletableFuture<SkuPack> buildCultPassGoldSKUCard(CompletableFuture<BuildWidgetResponse> widgetPromise, UserContext userContext, Map<String, String> queryParams, String pageTheme) {
        return widgetPromise.thenApply(response -> {
            SkuPack pack = null;
            try { pack = SkuPackUtil.buildSkuPack(response, SKUType.GOLD, pageTheme); }
            catch (Exception e) { throw new RuntimeException(e); }
            pack.setCardAction(cultPassGoldAction);
            return pack;
        }).exceptionally(e -> null);
    }

    private CompletableFuture<SkuPack> buildCultPassLiveSKUCard(CompletableFuture<BuildWidgetResponse> widgetPromise, UserContext userContext, Map<String, String> queryParams, String pageTheme) {
        return widgetPromise.thenApply(response -> {
            SkuPack pack = null;
            try { pack = SkuPackUtil.buildSkuPack(response, SKUType.LIVE, pageTheme); }
            catch (Exception e) { throw new RuntimeException(e); }
            pack.setCardAction(cultPassLiveAction);
            return pack;
        }).exceptionally(e -> null);
    }

    private CompletableFuture<SkuPack> buildCultPassPlaySKUCard(
        CompletableFuture<BuildWidgetResponse> widgetPromise,
        UserContext userContext,
        Map<String, String> queryParams,
        String pageTheme,
        ServiceInterfaces serviceInterfaces,
        boolean isCultPassPlaySupported
    ) {
        if (!isCultPassPlaySupported) {
            return null;
        }

        return widgetPromise.thenApply(response -> {
            SkuPack pack = null;
            try { pack = SkuPackUtil.buildSkuPack(response, SKUType.PLAY, pageTheme); }
            catch (Exception e) { throw new RuntimeException(e); }
            pack.setPackImage(PlayUtil.PLAY_COMPARISION_V1_IMAGE_URL);
            pack.setCardAction(Objects.requireNonNullElseGet(cultPassPlayAction, () -> PlayUtil.getSkuPurchasePageAction("VIEW", serviceInterfaces)));
            return pack;
        }).exceptionally(e -> null);
    }

    private CompletableFuture<SkuPack> buildCultPassSelectSKUCard(boolean isCenterLevelPricingSupported) {
        if (!isCenterLevelPricingSupported) {
            return null;
        }
        SkuPack cultPassSelect = new SkuPack();
        try {
            cultPassSelect.setType(SKUType.ELITE_SELECT);
            if (cultPassSelectAction != null) {
                cultPassSelect.setCardAction(cultPassSelectAction);
            } else {
                Action action = new Action("curefit://allgyms?includeCultCenters=true", "VIEW", ActionType.NAVIGATION);
                cultPassSelect.setCardAction(action);
            }
            ProductPriceView productPriceView = new ProductPriceView();
            productPriceView.setListingPrice("Select a");
            productPriceView.setSuffix("Center");
            cultPassSelect.setPackImage(SelectPacksUtils.SELECT_SKU_TAG_IMAGE_URL);
            cultPassSelect.setProductPrice(productPriceView);
            return CompletableFuture.completedFuture(cultPassSelect);
        } catch (Exception e) { return null; }
    }
}
