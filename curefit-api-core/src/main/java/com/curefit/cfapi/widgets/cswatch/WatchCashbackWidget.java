package com.curefit.cfapi.widgets.cswatch;


import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.meta.AnalyticsData;
import com.curefit.cfapi.model.internal.meta.EventData;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.cswatch.QueryParamType;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.pojo.vm.widget.WidgetMetric;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.SegmentEvaluatorService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.cswatch.CSWatchUtil;
import com.curefit.cfapi.view.viewbuilders.cswatch.ReviewGuidePageViewBuilder;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.common.banner.ContentMetric;
import com.curefit.cfapi.widgets.common.banner.MerchantryWidget;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.smartdevice.pojo.ReviewRegisteredResponse;
import com.curefit.smartdevice.pojo.UserDeviceInfoEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class WatchCashbackWidget extends BaseWidget {

    String aspectRatio = "345:148";
    String userId = "";
    String watchId = "";
    String deviceName = "";

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        userId = userContext.getUserProfile().getUserId();
        watchId = CSWatchUtil.getMacAddress(interfaces.httpServletRequest);

        if(watchId != null && !watchId.isEmpty()) {
            try {
                UserDeviceInfoEntry smartDeviceClientUserInfo = interfaces.smartDeviceClient.getUserInfo(userContext.getUserProfile().getUserId(), watchId);
                deviceName = smartDeviceClientUserInfo.getDeviceName();
            } catch (Exception e) {
                interfaces.exceptionReportingService.reportException(e);
            }
        }

        BaseWidget bannerWidget = buildBannerWidget(interfaces,userContext);
        return bannerWidget != null ? Collections.singletonList(buildBannerWidget(interfaces,userContext)) : null;
    }

    BaseWidget buildBannerWidget(ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        MerchantryWidget merchantryWidget = new MerchantryWidget();
        merchantryWidget.setWidgetType(WidgetType.WATCH_MERCHANTRY_WIDGET);

        List<BannerItem> items = new ArrayList<>();
//        items.add(getTrackAppBanner(userContext));
//        items.add(getReviewRewardBanner(interfaces, userContext));
        items.add(getSmartScaleBanner());
        items.add(getGpsHowToVideoBanner());
//        items.add(getRangerXRUltraPromoBanner());
//        items.add(getActiveTRPromoBanner());
//        items.add(getReviewRewardBanner(interfaces, userContext));
        items = items.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if(items.isEmpty()){
            return null;
        }
        merchantryWidget.setData(items);

        WidgetMetric widgetMetric = new WidgetMetric();
        widgetMetric.setWidgetName("Watch Cashback Widget");
        widgetMetric.setWidgetType(WidgetType.WATCH_CASHBACK_WIDGET.toString());
        merchantryWidget.setWidgetMetric(widgetMetric);
        merchantryWidget.setDisableGlassMorphism(true);

        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("type", "CAROUSEL");
        Map<String, Object> data = new HashMap<>();
        data.put("edgeToEdge", false);
        data.put("showPagination", false);
        data.put("aspectRatio", aspectRatio);
        layoutProps.put("data", data);
        layoutProps.put("spacing", Spacing.builder().top("0").bottom("20").build());
        merchantryWidget.setLayoutProps(layoutProps);

        return merchantryWidget;
    }


    BannerItem getSmartScaleBanner() {
        BannerItem item1 = new BannerItem();
        ContentMetric contentMetric = new ContentMetric();
        contentMetric.setContentUrl("https://cultsport.com/weighing-scale");
        contentMetric.setContentId("SmartScale_AppPromo");
        item1.setContentMetric(contentMetric);
        item1.setImage("image/watch/creatives/smart_scale_in_app_banner_2x.png");

        Action action = Action.builder()
                .actionType(ActionType.OPEN_WEBPAGE)
                .url("https://cultsport.com/weighing-scale")
                .analyticsData(new AnalyticsData(new AnalyticsData("button_click_event",
                        EventData.builder().actionType(String.valueOf(ActionType.OPEN_WEBPAGE)).contentId("SmartScale_AppPromo").build())))
                .build();


        item1.setAction(action);
        return item1;
    }

    BannerItem getRangerXRUltraPromoBanner() {
        if(deviceName != null && deviceName.equalsIgnoreCase("Ranger XR Ultra")) {
            return null;
        }
        // Banner 1
        BannerItem item1 = new BannerItem();
        ContentMetric contentMetric = new ContentMetric();
        contentMetric.setContentUrl("https://cultsport.com/smart-watch");
        contentMetric.setContentId("RangerXRUltra_AppPromo");
        item1.setContentMetric(contentMetric);
        item1.setImage("image/watch/creatives/Ranger XR Ultra Banner.png");

        Action action = Action.builder()
                .actionType(ActionType.OPEN_WEBPAGE)
                .url("https://cultsport.com/smart-watch")
                .analyticsData(new AnalyticsData(new AnalyticsData("button_click_event",
                        EventData.builder().actionType(String.valueOf(ActionType.OPEN_WEBPAGE)).contentId("RangerXRUltra_AppPromo").build())))
                .build();


        item1.setAction(action);
        return item1;
    }

    BannerItem getActiveTRPromoBanner() {
        if(deviceName != null && deviceName.equalsIgnoreCase("Active TR")) {
            return null;
        }

        // Banner 1
        BannerItem item1 = new BannerItem();
        ContentMetric contentMetric = new ContentMetric();
        contentMetric.setContentUrl("https://cultsport.com/smart-watch");
        contentMetric.setContentId("ActiveTR_AppPromo");
        item1.setContentMetric(contentMetric);
        item1.setImage("image/watch/creatives/Active TR(4).png");

        Action action = Action.builder()
                .actionType(ActionType.OPEN_WEBPAGE)
                .url("https://cultsport.com/smart-watch")
                .analyticsData(new AnalyticsData(new AnalyticsData("button_click_event",
                        EventData.builder().actionType(String.valueOf(ActionType.OPEN_WEBPAGE)).contentId("ActiveTR_AppPromo").build())))
                .build();


        item1.setAction(action);

        return item1;
    }

    BannerItem getGpsHowToVideoBanner() {
        if(deviceName == null || !(deviceName.equalsIgnoreCase("Velocity") || deviceName.equalsIgnoreCase("Sprint"))) {
            return null;
        }

        // Banner 1
        BannerItem item1 = new BannerItem();
        ContentMetric contentMetric = new ContentMetric();
        contentMetric.setContentUrl("https://youtu.be/lvwvhq-ZFDI");
        contentMetric.setContentId("HowToVideo_gps");
        item1.setContentMetric(contentMetric);
        item1.setImage("image/watch/creatives/gps_how_to_banner.png");

        Action action = Action.builder()
                .actionType(ActionType.OPEN_WEBPAGE)
                .url("https://youtu.be/lvwvhq-ZFDI")
                .analyticsData(new AnalyticsData(new AnalyticsData("button_click_event",
                        EventData.builder().actionType(String.valueOf(ActionType.OPEN_WEBPAGE)).contentId("HowToVideo_gps").build())))
                .build();


        item1.setAction(action);

        return item1;
    }

    BannerItem getTrackAppBanner(UserContext userContext) {
        String osName = userContext.getSessionInfo().getOsName();
        boolean isAndroid = StringUtils.isNotEmpty(osName) && osName.equalsIgnoreCase("android");

        if(!isAndroid) {
            return null;
        }

        if(userContext.getSessionInfo().getAppVersion() >= 4.92f) {
            return null;
        }

        // if watch exists, return null
        if(watchId != null && !watchId.isEmpty()) {
            return null;
        }

        // Banner 1
        BannerItem item1 = new BannerItem();
        ContentMetric contentMetric = new ContentMetric();
        contentMetric.setContentUrl("https://drive.google.com/drive/folders/1X_RBwiirNyiwLFhovAMMAPwHzYIixMBI?usp=sharing");
        contentMetric.setContentId("TrackApp_Apk");
        item1.setContentMetric(contentMetric);
        item1.setImage("image/watch/creatives/track banner(1).png");

        Action action = Action.builder()
                .actionType(ActionType.OPEN_WEBPAGE)
                .url("https://drive.google.com/drive/folders/1X_RBwiirNyiwLFhovAMMAPwHzYIixMBI?usp=sharing")
                .analyticsData(new AnalyticsData(new AnalyticsData("button_click_event",
                        EventData.builder().actionType(String.valueOf(ActionType.OPEN_WEBPAGE)).contentId("TrackApp_Apk").build())))
                .build();


        item1.setAction(action);

        return item1;
    }

    BannerItem getReviewRewardBanner(ServiceInterfaces interfaces, UserContext userContext) throws Exception {

        Float appVersion = userContext.getSessionInfo().getAppVersion();
        if(appVersion < 4.22f){
            return null;
        }

        if(hasUserOrderedFrom1P(userContext)){
            return null;
        }

        log.info("getting mac address as for userId: " + userId + " is " + watchId);
        if (watchId == null || deviceName == null) {
            return null;
        }

        if(!ReviewRatingsConstants.SUPPORTED_DEVICES_FOR_REWARDS.contains(deviceName)) {
            return null;
        }

        ReviewRegisteredResponse isReviewRegistered = null;
        try {
            isReviewRegistered = interfaces.smartDeviceClient.isReviewRegistered(userId, watchId);
        } catch (HttpException e){
            if(e.getStatusCode().getValue() != 404) {
                throw new Exception("Watch Cashback Widget failing for userId: " + userId + ", message: " + e.getMessage());
            }
        }
        if(isReviewRegistered == null){
            return null;
        }
        log.info("getting Cashback registered as for userId: " + userId + " is " + isReviewRegistered);
        if (isReviewRegistered.getIsReviewRegistered()) {
            return null;
        }


        ReviewRatingsConstants.ReviewReward deviceReviewReward = ReviewRatingsConstants.getDeviceBasedReward(deviceName);


        // Banner 1
        BannerItem item1 = new BannerItem();
        ContentMetric contentMetric = new ContentMetric();
        contentMetric.setContentUrl(deviceReviewReward.bannerImgUrl);
        contentMetric.setContentId(deviceReviewReward.reward);
        item1.setContentMetric(contentMetric);
        item1.setImage(deviceReviewReward.bannerImgUrl);

        String url = getUrl(interfaces.environmentService, watchId, deviceReviewReward.reward);

        item1.setAction(getBannerClickAction(userContext, url, deviceReviewReward, interfaces.segmentEvaluatorService, interfaces.environmentService));
        return item1;
    }

    Boolean hasUserOrderedFrom1P(UserContext userContext) throws ExecutionException, InterruptedException {
        SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();
        return userSegments.contains(CSWatchUtil.USERS_ORDERED_WATCH_1P_SEGMENT);
    }

    String getUrl(EnvironmentService environmentService, String watchId, String reward) {
        return getDomainName(environmentService) + "/watch/benefits/register/v2?macAddress=" + watchId + "&reward=" + reward;
    }

    String getDomainName(EnvironmentService environmentService) {
        if(environmentService.isStage()) {
            return "https://stage.cultsport.com";
        }
        if(environmentService.isAlpha()) {
            return "https://alpha.cultsport.com";
        }
        return "https://www.cultsport.com";
    }


    Action getBannerClickAction(UserContext userContext, String url, ReviewRatingsConstants.ReviewReward reviewReward, SegmentEvaluatorService segmentEvaluatorService, EnvironmentService environmentService) {

        if(CSWatchUtil.isReviewGuideFlowSupported(userContext, segmentEvaluatorService, environmentService)) {
            Action action =  ReviewGuidePageViewBuilder.getEntryBannerAction(userContext, reviewReward);
            action.setAnalyticsData(new AnalyticsData("button_click_event", EventData.builder().actionType("review-app-flow").build()));
            return action;

        } else {

            Map<String, Object> meta = new HashMap<>();
            meta.put("queryParams", Collections.singletonList(QueryParamType.sso));
            meta.put("refreshHomepage", true);

            AnalyticsData analyticsData = new AnalyticsData("button_click_event", EventData.builder().actionType("review-web-flow").build());

            return Action.builder()
                    .actionType(ActionType.CS_OPEN_WEB_VIEW)
                    .url(url)
                    .title("")
                    .meta(meta)
                    .analyticsData(analyticsData)
                    .build();
        }
    }

}
