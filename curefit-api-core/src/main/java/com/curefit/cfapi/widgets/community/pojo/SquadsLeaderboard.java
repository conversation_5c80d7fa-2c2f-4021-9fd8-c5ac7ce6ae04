package com.curefit.cfapi.widgets.community.pojo;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.widget.BaseWidget;
import com.curefit.cfapi.widgets.community.SquadGoalProgress;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SquadsLeaderboard extends BaseWidget {
    Long communityId;
    String title;
    List<LeaderboardItem> widgets;
    int indexThreshold;
    String userName;
    int sessionCount;
    String profileImageUrl;
    int weekOffset;
    List<ProgressIndicatorTile> progressIndicatorTiles;
    String expandedTile;
    boolean showProgressBar;
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class LeaderboardItem {
        Long communityId;
        String userId;
        String title;
        String subtitle;
        String avatarUrl;
        String rank;
        String rankImageUrl;
        boolean selfRankItem;
        Action profileClickAction;
        boolean showFitnessConnectionModal;
        boolean enableShare;
        WeeklyActivityData weeklyActivityData;
        FeedInteractionItem feedInteractionData;
        Boolean isSquadGoalOngoing;
        SquadGoalProgress userScoringDetails;
    }
}
