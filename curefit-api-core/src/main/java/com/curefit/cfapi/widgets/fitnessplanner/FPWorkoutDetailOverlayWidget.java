package com.curefit.cfapi.widgets.fitnessplanner;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.guidedWorkoutsPlayback.ExerciseStopwatchWidget;
import com.curefit.cfapi.widgets.guidedWorkoutsPlayback.ExerciseTimeCountWidget;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.List;

import static com.curefit.cfapi.pojo.vm.widget.WidgetType.WORKOUT_DETAIL_OVERLAY_WIDGET;
@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FPWorkoutDetailOverlayWidget extends BaseWidgetNonVM {

    List<Action> rightActions;
    ExerciseStopwatchWidget exerciseStopWatch;
    ExerciseExecutionWidget exerciseExecutionWidget;
    ExerciseTimeCountWidget exerciseTimeCountWidget;
    TextualAction textualAction;
    String[] linearGradient;
    Boolean disableTimer;

    public FPWorkoutDetailOverlayWidget() {
        super(WORKOUT_DETAIL_OVERLAY_WIDGET);
    }
}
