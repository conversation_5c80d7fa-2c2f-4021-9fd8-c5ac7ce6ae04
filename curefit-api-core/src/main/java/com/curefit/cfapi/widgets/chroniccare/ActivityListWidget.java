package com.curefit.cfapi.widgets.chroniccare;

import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.sfalbus.response.CGMStat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

import static com.curefit.cfapi.pojo.vm.widget.WidgetType.CHRONIC_CARE_ACTIVITY_LIST_WIDGET;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActivityListWidget extends BaseWidgetNonVM {

    List<DailyActivityLogs> activityLogs;
    SugarScoreLogicModalWidget sugarScoreLogicModalWidget;
    boolean isActivityEditable = false;
    public ActivityListWidget() {
        super(CHRONIC_CARE_ACTIVITY_LIST_WIDGET);
    }

    @Getter
    @Setter
    @RequiredArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class DailyActivityLogs {
        String startDate = "";
        String endDate = "";
        String message = "";
        List<CGMStat.PatientActivityImpact> activityList = new ArrayList<>();
    }
}
