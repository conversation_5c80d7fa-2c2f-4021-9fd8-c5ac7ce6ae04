package com.curefit.cfapi.widgets.enterprise;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.EnterpriseUtils;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.appConfig.EnterpriseConfig;
import com.curefit.cfapi.service.appConfig.EnterpriseConfig.BenefitDetails;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.banner.Header;
import com.curefit.product.enums.ProductType;
import com.fasterxml.jackson.annotation.JsonInclude;
import fit.cult.enterprise.dto.corporate.CorporateBenefits;
import fit.cult.enterprise.dto.corporate.CorporateDetails;
import fit.cult.enterprise.dto.program.ProgramType;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SKUCardV2Widget extends BaseWidget {

    private Header header;
    private List<SKUCardV2WidgetItem> data;
    private Boolean isHorizontal;
    private String productType;
    private Boolean isDisplayOnlyHeader;

    public SKUCardV2Widget() {
        super(WidgetType.SKU_CARD_WIDGET_V2);
    }

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        if (isDisplayOnlyHeader != null && isDisplayOnlyHeader) {
            String userId = userContext.getUserProfile()
                    .getUserId();
            CorporateDetails corporateDetails = interfaces.getEnterpriseClient()
                    .getCorporateWithBenefits(userId);
            if(corporateDetails == null) {
                return null;
            }
            boolean haveBenefitForDisplay = false;
            for(CorporateBenefits benefits: corporateDetails.getBenefits()) {
                if(benefits.getProgramType() == ProgramType.THERAPY ||
                        benefits.getProgramType() == ProgramType.ONLINE_DIETICIAN){
                    haveBenefitForDisplay = true;
                    break;
                }
            }
            if(haveBenefitForDisplay) {
                return super.buildView(interfaces, userContext, widgetContext);
            }
            return null;
        }
        data = new ArrayList<>();
        addDataItems(interfaces, userContext);
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return super.buildView(interfaces, userContext, widgetContext);
    }

    private void addDataItems(ServiceInterfaces interfaces, UserContext userContext) throws Exception {
        if (productType == null) {
            return;
        }
        String userId = userContext.getUserProfile()
                                   .getUserId();
        CorporateDetails corporateDetails = interfaces.getEnterpriseClient()
                                                      .getCorporateWithBenefits(userId);
        if (corporateDetails == null) {
            log.info("No corporate found for userId {}", userId);
            return;
        }

        if(corporateDetails.getBenefits().size() == 0) {
            return;
        }

        Map<ProgramType, List<CorporateBenefits>> benefitsByTypes = corporateDetails.getBenefits().stream().collect(groupingBy(CorporateBenefits::getProgramType));

        for(ProgramType programType : benefitsByTypes.keySet()) {
            if (Objects.equals(productType, ProductType.THERAPY.toString())) {
                if (programType != ProgramType.THERAPY) {
                    continue;
                }
            } else if (Objects.equals(productType, ProductType.CONSULTATION.toString())) {
                if (programType != ProgramType.DOCTOR_CONSULTATION) {
                    continue;
                }
            } else if (Objects.equals(productType, ProductType.NUTRITIONIST_CONSULTATION.toString())) {
                if (programType != ProgramType.ONLINE_DIETICIAN) {
                    continue;
                }
            }

            if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_ZOOM_ELITE_PRO_METRIC_WIDGET_ROLL_OUT_APP_VERSION)) {
                if(Objects.equals(productType, ProgramType.DISCOUNTED_CULT_PRO.name())) {
                    if (programType != ProgramType.DISCOUNTED_CULT_PRO) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.DISCOUNTED_CULT_LIVE.name())) {
                    if (programType != ProgramType.DISCOUNTED_CULT_LIVE) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.DISCOUNTED_CULT_ELITE.name())) {
                    if (programType != ProgramType.DISCOUNTED_CULT_ELITE) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.CULT_LIVE.name())) {
                    if (programType != ProgramType.CULT_LIVE) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.COUPON_BASED_CULT_LIVE.name())) {
                    if (programType != ProgramType.COUPON_BASED_CULT_LIVE) {
                        continue;
                    }
                }
            }

            if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_PT_PROGRAM__ROLL_OUT_APP_VERSION)) {
                if(Objects.equals(productType, ProgramType.ONLINE_PT.name())) {
                    if (programType != ProgramType.ONLINE_PT) {
                        continue;
                    }
                }
            }

            if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_DIRECT_ELITE_MEMBERSHIP_PROGRAM__ROLL_OUT_APP_VERSION)) {
                if(Objects.equals(productType, ProgramType.ELITE_FIX_DATES.name())) {
                    if (programType != ProgramType.ELITE_FIX_DATES) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.ELITE_FIX_DURATION.name())) {
                    if (programType != ProgramType.ELITE_FIX_DURATION) {
                        continue;
                    }
                }
            }

            if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_DIRECT_ELITE_MEMBERSHIP_PROGRAM__ROLL_OUT_APP_VERSION)) {
                if(Objects.equals(productType, ProgramType.PLAY_LIMITED_FIX_DATES.name())) {
                    if (programType != ProgramType.PLAY_LIMITED_FIX_DATES) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.PLAY_LIMITED_FIX_DURATION.name())) {
                    if (programType != ProgramType.PLAY_LIMITED_FIX_DURATION) {
                        continue;
                    }
                }
            }


            if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_DIRECT_ELITE_MEMBERSHIP_PROGRAM__ROLL_OUT_APP_VERSION)) {
                if (Objects.equals(productType, ProgramType.PLAY_BADMINTON_LIMITED_FIX_DATES.name())) {
                    if (programType != ProgramType.PLAY_BADMINTON_LIMITED_FIX_DATES) {
                        continue;
                    }
                } else if (Objects.equals(productType, ProgramType.PLAY_BADMINTON_LIMITED_FIX_DURATION.name())) {
                    if (programType != ProgramType.PLAY_BADMINTON_LIMITED_FIX_DURATION) {
                        continue;
                    }
                }
            }

            if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_DIRECT_ELITE_MEMBERSHIP_PROGRAM__ROLL_OUT_APP_VERSION)) {
                if (Objects.equals(productType, ProgramType.PLAY_SWIM_LIMITED_FIX_DATES.name())) {
                    if (programType != ProgramType.PLAY_SWIM_LIMITED_FIX_DATES) {
                        continue;
                    }
                } else if (Objects.equals(productType, ProgramType.PLAY_SWIM_LIMITED_FIX_DURATION.name())) {
                    if (programType != ProgramType.PLAY_SWIM_LIMITED_FIX_DURATION) {
                        continue;
                    }
                }
            }

            if (AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_DIRECT_PRO_MEMBERSHIP_PROGRAM__ROLL_OUT_APP_VERSION)) {
                if(Objects.equals(productType, ProgramType.PRO_FIX_DATES.name())) {
                    if (programType != ProgramType.PRO_FIX_DATES) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.PRO_FIX_DURATION.name())) {
                    if (programType != ProgramType.PRO_FIX_DURATION) {
                        continue;
                    }
                }
            }

            if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_DISCOUNTED_PLAY_MEMBERSHIP_PROGRAM__ROLL_OUT_APP_VERSION)) {
                if(Objects.equals(productType, ProgramType.DISCOUNTED_CULT_PLAY.name())) {
                    if (programType != ProgramType.DISCOUNTED_CULT_PLAY) {
                        continue;
                    }
                }
            }

            if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_DIRECT_ONEPASS_MEMBERSHIP_PROGRAM__ROLL_OUT_APP_VERSION)) {
                if (Objects.equals(productType, ProgramType.OP_PREMIUM_FIX_DATE.name())) {
                    if (programType != ProgramType.OP_PREMIUM_FIX_DATE) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.OP_PREMIUM_FIX_DURATION.name())) {
                    if (programType != ProgramType.OP_PREMIUM_FIX_DURATION) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.OP_STANDARD_FIX_DURATION.name())) {
                    if (programType != ProgramType.OP_STANDARD_FIX_DURATION) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.OP_STANDARD_FIX_DATE.name())) {
                    if (programType != ProgramType.OP_STANDARD_FIX_DATE) {
                        continue;
                    }
                }
            }

            if(AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_DIRECT_ONEPASS_MEMBERSHIP_PROGRAM__ROLL_OUT_APP_VERSION)) {
                if(Objects.equals(productType, ProgramType.ELITE_FIX_DATES_WITHOUT_LIVE.name())) {
                    if (programType != ProgramType.ELITE_FIX_DATES_WITHOUT_LIVE) {
                        continue;
                    }
                } else if(Objects.equals(productType, ProgramType.ELITE_FIX_DURATION_WITHOUT_LIVE.name())) {
                    if (programType != ProgramType.ELITE_FIX_DURATION_WITHOUT_LIVE) {
                        continue;
                    }
                }
            }

            CorporateBenefits benefit = EnterpriseUtils.getActiveBenefitForDisplay(interfaces, userContext, programType, benefitsByTypes.get(programType));
            if (benefit == null) {
                continue;
            }

            BenefitDetails cardConfig = EnterpriseConfig.PROGRAM_BENEFIT_DETAILS.get(benefit.getProgramType());
            if (cardConfig != null) {
                SKUCardV2WidgetItem item = EnterpriseUtils.getSKUCardFromOfferId(interfaces, userContext, benefit, productType, corporateDetails.getCorporate().getCode());
                if (item != null) {
                    item.setIsNewCard(EnterpriseConfig.skuWidgetNewUiSupported.contains(benefit.getProgramType()));
                    item.setTitle(cardConfig.getTitle())
                            .setSubTitle(cardConfig.getSubTitle())
                            .setBgImage(cardConfig.getBgImage())
                            .setSubTitleColor(cardConfig.getSubTitleColor());
                    cardConfig.getDetails()
                            .forEach(item::addBenefit);
                    if(item.getAction() == null) {
                        item.setAction(cardConfig.getAction());
                    }
                    if(item.getBottomAction() == null) {
                        item.setBottomAction(cardConfig.getBottomAction());
                    }
                    if (benefit.getProgramType().equals(ProgramType.OP_PREMIUM_FIX_DATE) || benefit.getProgramType().equals(ProgramType.OP_PREMIUM_FIX_DURATION) || benefit.getProgramType().equals(ProgramType.OP_STANDARD_FIX_DURATION) || benefit.getProgramType().equals(ProgramType.OP_STANDARD_FIX_DATE)) {
                        item.setAction(EnterpriseUtils.getOnePassMemberUrlAction(userContext, EnterpriseConfig.BUTTON_TITLE_BOOK_ONEPASS_CLASS));
                        item.setBottomAction(EnterpriseUtils.getOnePassMemberUrlAction(userContext, EnterpriseConfig.BUTTON_TITLE_BOOK_ONEPASS_CLASS));
                    }
                    data.add(item);
                }
            }
        }
    }
}
