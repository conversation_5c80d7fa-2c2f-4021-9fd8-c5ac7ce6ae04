package com.curefit.cfapi.widgets.chroniccare;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sugarfit.chs.pojo.DataPoint;
import com.sugarfit.chs.pojo.glucometerReadings.GlucoseTrendGroupedData;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SLGlucoseTrendWidget extends BaseWidgetNonVM {
    String title;
    GlucoseTrendGroupedData data;
    String slot;
    long startTime;
    long endTime;
    Map<String, List<DataPoint>> sugarReadingMap;
    NoDataCard noDataCard;
    Action citationAction = Action.builder().actionType(ActionType.OPEN_WEBPAGE).url("https://www.sugarfit.com/blog/normal-blood-sugar-levels?plainWebPage=true").title("Know about healthy ranges").build();


    public SLGlucoseTrendWidget() {
        super(WidgetType.SF_LITE_GLUCOSE_TREND_WIDGET);
    }

    @Getter
    @Setter
    @RequiredArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class NoDataCard {
        String topTitle = "You don’t have any sugar readings for this slot this month.";
        String bottomTitle = "Get insights about your glucose levels and take a step towards better health monitoring!";
        Action action = Action.builder().url("sfcommunity://sugarlogging").actionType(ActionType.NAVIGATION).title("LOG SUGAR LEVEL").build();
        String imageUrl = "image/chroniccare/SugarLogNoDataImage.png";
    }
}