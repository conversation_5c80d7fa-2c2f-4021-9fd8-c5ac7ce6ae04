package com.curefit.cfapi.widgets.trainerled;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.util.ActionUtil;
import com.curefit.diyfs.pojo.intl.FormatDetail;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.diyfs.pojo.intl.SocialMediaProfile;
import com.curefit.diyfs.pojo.intl.Trainer;
import static com.curefit.cfapi.pojo.vm.widget.WidgetType.TL_TRAINER_PROFILE_WIDGET;


@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TrainerProfileWidget extends BaseWidgetNonVM {
    String name;
    String image;
    Boolean isBookmarked;
    Action bookmarkAction;
    List<SocialMediaProfile> socialProfiles;
    List<String> formats;

    public TrainerProfileWidget(Trainer trainer, Boolean isBookmarked, Boolean isGuest) {
        super(TL_TRAINER_PROFILE_WIDGET);
        var id = trainer.getInstructorId();
        name = trainer.getName();
        image = trainer.getProfileImageUrl();
        this.isBookmarked = isBookmarked;
        socialProfiles = trainer.getOtherDetails().getSocialMediaProfiles();
        formats = trainer
                .getOtherDetails()
                .getResponseMeta()
                .getFormatsDetails()
                .stream()
                .map(FormatDetail::getTitle)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        bookmarkAction = ActionUtil.getTrainerBookmarkAction(id, name, isBookmarked, isGuest);
    }
}
