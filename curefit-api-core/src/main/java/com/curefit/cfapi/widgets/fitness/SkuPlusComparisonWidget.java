package com.curefit.cfapi.widgets.fitness;

import com.curefit.base.models.DateTimezone;
import com.curefit.catalogv1.services.augment.PMSAugments;
import com.curefit.catalogv1.services.filter.ElitePackFilter;
import com.curefit.catalogv1.services.filter.ProPackFilter;
import com.curefit.cfapi.model.Analytics.OfferTimerWidgetV2Event;
import com.curefit.cfapi.model.internal.cult.CultPackBrowseProductType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.userinfo.UserProfile;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.Debugger;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.pojo.vm.atom.CFGradientData;
import com.curefit.cfapi.pojo.vm.atom.GradientDirection;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.timer.CenterOfferTimerWidget;
import com.curefit.gymfit.models.Listing;
import com.curefit.gymfit.utils.Enums;
import com.curefit.offers.dtos.OfferMini;
import com.curefit.pms.entries.BaseMongoEntry;
import com.curefit.pms.entries.BenefitEntry;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.pms.pojo.ExhaustivePackBenefit;
import com.curefit.pms.pojo.customPacks.augments.AugmentedOfflineFitnessPack;
import com.curefit.product.enums.ProductType;
import com.curefit.product.models.cult.FitnessPack;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class SkuPlusComparisonWidget extends BaseWidget {

    List<HashMap<String, CultPackBrowseProductType>> items;
    List<String> packOfferingOrderList;
    DateTimezone timerEndTimeWithTz;
    String packOfferingOrder;

    boolean headerStickyV2;
    boolean disableStickyHeader;
    boolean disableStickyFooter;
    List<String> tabOrder;
    SkuPlusTabHeader header;
    HashMap<String,List<BaseWidget>> body;
    HashMap<String, List<SkuPlusFooter>> footer;
    CFTextData headerTitle;
    Props props;
    Debugger debugger;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        try {
            this.debugger = Debugger.getDebuggerFromUserContext(userContext);
            this.header = new SkuPlusTabHeader();
            this.body = new HashMap<>();
            this.footer = new HashMap<>();
            this.props = new Props();
            this.tabOrder = List.of(packOfferingOrder.split("_"));

            debugger.msg(this);

            if (this.items == null || this.items.size() < 2 || this.tabOrder == null || this.tabOrder.isEmpty()) {
                debugger.msg("PreCondition 1 failed");
                return null;
            }

            CultPackBrowseProductType sku1ProductType = this.items.get(0).get("productType");
            CultPackBrowseProductType sku2ProductType = this.items.get(1).get("productType");
            debugger.msg(sku1ProductType);
            debugger.msg(sku2ProductType);

            List<AugmentedOfflineFitnessPack> sku1PacksList = getPacksBySkuAndtabOrder(userContext, interfaces, sku1ProductType);
            List<AugmentedOfflineFitnessPack> sku2PacksList = getPacksBySkuAndtabOrder(userContext, interfaces, sku2ProductType);

            if (Objects.isNull(sku1PacksList) || Objects.isNull(sku2PacksList)) {
                debugger.msg("PreCondition 2 failed");
                return null;
            }

            List<SkuPlusTabHeader.TabItem> tabItemList = new ArrayList<>();
            for (int pack1Index = 0;pack1Index < sku1PacksList.size();pack1Index++) {
                AugmentedOfflineFitnessPack pack1 = sku1PacksList.get(pack1Index);
                long pack1Duration = Math.round(Math.floor((double) pack1.getProduct().getDurationInDays().intValue() / 30));
                Optional<AugmentedOfflineFitnessPack> optionalPack = sku2PacksList.stream()
                        .filter(item -> {
                            long totalMonthsRounded = Math.round(Math.floor(item.getProduct().getDurationInDays().intValue() / 30.0));
                            return totalMonthsRounded == pack1Duration;
                        })
                        .findFirst();

                AugmentedOfflineFitnessPack pack2 = optionalPack.orElse(null);
                debugger.msg(pack1);
                debugger.msg(pack2);

                if (Objects.isNull(pack2)) {
                    debugger.msg("Continue because no maching pack found");
                    continue;
                }

                HashMap<String,Object> pack1Map = new FitnessPackBrowseWidget().getPackDetailsMap(userContext, interfaces, pack1, String.valueOf(sku1ProductType), widgetContext);
                HashMap<String,Object> pack2Map = new FitnessPackBrowseWidget().getPackDetailsMap(userContext, interfaces, pack2, String.valueOf(sku2ProductType), widgetContext);
                debugger.msg(pack1Map);
                debugger.msg(pack2Map);

                Action pack1Action = (Action) pack1Map.getOrDefault("action",null);
                Action pack2Action = (Action) pack2Map.getOrDefault("action",null);
                debugger.msg(pack1Action);
                debugger.msg(pack2Action);

                // TAB LOGIC (HEADER)
                String pack2Duration = pack2Map.containsKey("packDuration") ? pack2Map.get("packDuration").toString() : null;
                String tabTitle = pack2Duration!=null ? pack2Duration.equals("1") ? STR."\{pack2Duration} MONTH" : STR."\{pack2Duration} MONTHS" : null;
                String tabSubTitle = pack2Map.containsKey("perMonthPriceWithoutDuration") ? STR."₹\{pack2Map.get("perMonthPriceWithoutDuration").toString()}/MO*" : null;
                String tabKey = pack1.getId();
                SkuPlusTabHeader.TabItem  tabItem = new SkuPlusTabHeader.TabItem(tabTitle, tabSubTitle, pack1Index == 0, tabKey, sku1ProductType.toString());
                tabItemList.add(tabItem);
                debugger.msg(tabItemList);

                // BODY LOGIC
                List<BaseWidget> widgetList = new ArrayList<>();

                // OFFER TIMER WIDGET LOGIC
                if (this.timerEndTimeWithTz != null) {
                    BaseWidget offerTimerWidget = this.getOfferTimerWidget(userContext, pack1Map, sku1ProductType, sku2ProductType, interfaces, pack1.getId());
                    debugger.msg(offerTimerWidget);
                    if (Objects.nonNull(offerTimerWidget)) widgetList.add(offerTimerWidget);
                }

                // SKU PLUS COMPARISON GRID WIDGET LOGIC  // TODO::///
                List<BenefitEntry> pack1Benefits = (List<BenefitEntry>) pack1Map.get("benefits");
                List<BenefitEntry> pack2Benefits = (List<BenefitEntry>) pack2Map.get("benefits");
                debugger.msg(pack1Benefits);
                debugger.msg(pack2Benefits);
                if (Objects.nonNull(pack1Benefits) && Objects.nonNull(pack2Benefits) && !pack1Benefits.isEmpty() && !pack2Benefits.isEmpty()) {
                    SkuPlusComparisonGridWidget skuPlusComparisonGridWidget = new SkuPlusComparisonGridWidget();
                    skuPlusComparisonGridWidget.getSkuPlusComparisonGridWidget(
                            pack1Benefits, pack2Benefits, sku1ProductType, sku2ProductType,
                            pack1Map.containsKey("perMonthPriceWithoutDuration") ? "₹" + pack1Map.get("perMonthPriceWithoutDuration").toString() + "/MO*" : null,
                            pack2Map.containsKey("perMonthPriceWithoutDuration") ? "₹" + pack2Map.get("perMonthPriceWithoutDuration").toString() + "/MO*" : null
                    );
                    widgetList.add(skuPlusComparisonGridWidget);
                }
                this.body.put(tabKey, widgetList);

                // FOOTER LOGIC
                this.footer.put(tabKey, this.getFooterList(sku1ProductType, pack1Map, pack2Map, pack1Action, pack2Action));
            }
            if (tabItemList.isEmpty()) {
                debugger.msg("TabList is empty returning null");
                return null;
            }
            this.header.setTabItemList(tabItemList);

            // HEADER TITLE
            this.getHeaderTitle(sku1ProductType, sku2ProductType);
            this.getProps(sku1ProductType, sku2ProductType);
            debugger.msg(this);
            this.debugger = null;
            return Collections.singletonList(this);
        } catch (Exception e) {
            debugger.err("Error building Sku plus comparison widget");
            interfaces.exceptionReportingService.reportException("Error in buiding SKU_PLUS_COMPARISON_WIDGET", e);
            return null;
        }
    }

    private Void getProps(CultPackBrowseProductType type1, CultPackBrowseProductType type2) {
        if (type1 == CultPackBrowseProductType.ELITE_PLUS || type2 == CultPackBrowseProductType.ELITE_PLUS) {
            this.props.setPrimaryColor("#AA7620");
            this.props.setSelectedGradientData(new CFGradientData(new ArrayList<>(){{add("#FFC748"); add("#FFD579"); add("#FFD783"); add("#AA7620");}}, GradientDirection.DIAGONAL));
            this.props.setBackgroundImage("/image/sku_plus/elite_plus_background.png");
        } else {
            this.props.setPrimaryColor("#BEBEC2");
            this.props.setSelectedGradientData(new CFGradientData(new ArrayList<>(){{add("#2D2C45"); add("#FFFFFF"); add("#BEBEC2"); add("#2D2E3A");}}, GradientDirection.DIAGONAL));
            this.props.setBackgroundImage("/image/sku_plus/default_background.png");
        }
        return null;
    }

    private List<SkuPlusFooter> getFooterList(CultPackBrowseProductType sku1ProductType, HashMap<String,Object> pack1Map, HashMap<String,Object> pack2Map, Action pack1Action, Action pack2Action) {
        List<SkuPlusFooter> skuPlusFooterList = new ArrayList<>();
        skuPlusFooterList.add(new SkuPlusFooter("Total\nPayable",  pack1Map.containsKey("taxes_fees") ? "+ Taxes & Fees" : null ,null, "start",null));
        skuPlusFooterList.add(new SkuPlusFooter("₹%s".formatted(pack1Map.getOrDefault("totalPackPrice", null)), pack1Map.containsKey("taxes_fees") ? "+₹%s".formatted(pack1Map.get("taxes_fees").toString()) : null, pack1Action,null,sku1ProductType == CultPackBrowseProductType.ELITE_PLUS ? "/image/sku_plus/sku1_frame.png" : "/image/sku_plus/pro_bottom_frame.png"));
        skuPlusFooterList.add(new SkuPlusFooter("₹%s".formatted(pack2Map.getOrDefault("totalPackPrice", null)), pack2Map.containsKey("taxes_fees") ? "+₹%s".formatted(pack2Map.get("taxes_fees").toString()) : null, pack2Action, null,null));
        return skuPlusFooterList;
    }

    private BaseWidget getOfferTimerWidget(UserContext userContext, HashMap<String,Object> pack1Map, CultPackBrowseProductType type1, CultPackBrowseProductType type2, ServiceInterfaces interfaces, String productId1) {
        String userTimeZone = userContext.getUserProfile().getTimezone();
        long offerEndTime = TimeUtil.getMillisFromDateTimezone(
                this.timerEndTimeWithTz.getDate(),
                userTimeZone
        );
        if (offerEndTime > TimeUtil.now(userTimeZone)) {
            List<OfferMini> pack1Offers = (List<OfferMini>) pack1Map.get("offers");
            debugger.msg(pack1Offers);
            if (pack1Offers != null && !pack1Offers.isEmpty()) {
                try {
                    interfaces.cfAnalytics.sendEvent(
                            OfferTimerWidgetV2Event.builder()
                                    .offers(pack1Offers)
                                    .skuType(String.valueOf(type1))
                                    .productId(productId1)
                                    .offerEndTime(offerEndTime)
                                    .month((String) pack1Map.get("packDuration"))
                                    .build(),
                            userContext, true, true, false, false
                    );
                } catch (Exception ignored) {}
                CenterOfferTimerWidget offerTimerWidget = new CenterOfferTimerWidget();
                offerTimerWidget.getCenterOfferTimerV2Widget("Offers for today", offerEndTime, pack1Offers, type1, type2, debugger);
                return offerTimerWidget;
            }
        }
        return null;
    }

    private Void getHeaderTitle(CultPackBrowseProductType type1, CultPackBrowseProductType type2) {
        this.headerTitle = new CFTextData("BUY NOW, START ANYTIME", UiUtils.UIColors.COLOR_WHITE, "1", UiUtils.TextAlignment.CENTER, UiUtils.TextTypeScales.P2);
        ArrayList<String> colors = new ArrayList<>();
        if (type1 == CultPackBrowseProductType.ELITE_PLUS || type2 == CultPackBrowseProductType.ELITE_PLUS) {
            colors.add("#FFC748");
            colors.add("#FFD579");
            colors.add("#FFD783");
            colors.add("#AA7620");
        } else {
            colors.add("#2D2C45");
            colors.add("#FFFFFF");
            colors.add("#BEBEC2");
            colors.add("#2D2E3A");
        }
        this.headerTitle.setGradient(new CFGradientData(colors, GradientDirection.DIAGONAL));
        this.headerTitle.setLetterSpacing(5);
        return null;
    }

    private List<AugmentedOfflineFitnessPack> getPacksBySkuAndtabOrder(UserContext userContext, ServiceInterfaces interfaces,CultPackBrowseProductType productType) throws Exception {
        List<AugmentedOfflineFitnessPack> fitnessPackList = new ArrayList<>();
        switch (productType) {
            case CULT_UNLIMITED -> fitnessPackList = userContext.getRequestCache().getAugmentedPackList(
                ProductType.FITNESS, ProductSubType.GENERAL, true,
                userContext, null
            ).stream().filter(pack -> !SkuPackUtil.isAllIndiaPack(pack.getProduct().getBenefits())).collect(Collectors.toList());
            case GYM_PACK -> fitnessPackList = userContext.getRequestCache().getAugmentedPackList(
                ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.GENERAL, true,
                userContext, null
            );
            case ELITE_PLUS -> fitnessPackList = userContext.getRequestCache().getAugmentedPackList(
                ProductType.FITNESS, ProductSubType.PLUS, true,
                userContext, null
            );
            case PRO_PLUS -> fitnessPackList = userContext.getRequestCache().getAugmentedPackList(
                ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.PLUS, true,
                userContext, null
            );
        }
        debugger.msg(fitnessPackList);
        if (Objects.isNull(fitnessPackList) || fitnessPackList.isEmpty()) {
            debugger.msg("fitnessPackList is empty");
            return null;
        }
        return sortPacksBytabOrder(fitnessPackList);
    }

    private List<AugmentedOfflineFitnessPack> sortPacksBytabOrder(List<AugmentedOfflineFitnessPack> packList) {
        HashMap<Long, AugmentedOfflineFitnessPack> durationPackMap = new HashMap<>();
        for (AugmentedOfflineFitnessPack pack : packList) {
            Integer totalDuration = pack.getProduct().getDurationInDays().intValue();
            long totalMonthsRounded = Math.round(Math.floor(totalDuration / 30));
            durationPackMap.put(totalMonthsRounded, pack);
        }
        List<AugmentedOfflineFitnessPack> finalSortedPacksList = new ArrayList<>();
        for (int i = 0; i < this.tabOrder.size(); i++) {
            Long key = Long.parseLong(this.tabOrder.get(i));
            if (durationPackMap.containsKey(key)) {
                finalSortedPacksList.add(durationPackMap.get(key));
            }
        }
        return finalSortedPacksList;
    }

    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    static
    class Props {
        String primaryColor;
        CFGradientData selectedGradientData;
        String backgroundImage;
    }
}
