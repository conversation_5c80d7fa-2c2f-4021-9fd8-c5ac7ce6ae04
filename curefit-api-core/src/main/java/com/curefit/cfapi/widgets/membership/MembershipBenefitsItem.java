package com.curefit.cfapi.widgets.membership;

import com.curefit.membership.pojo.entry.Benefit;
import com.curefit.membership.types.BenefitType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class MembershipBenefitsItem {

    public static final Integer UNLIMITED_STATIC_ACCESS_TICKETS = 100;
    public static final Integer UNLIMITED_MONTHLY_ACCESS_TICKETS = 30;

    BenefitItem cultAway;
    BenefitItem cult;
    BenefitItem centerAway;
    BenefitItem pause;
    BenefitItem noShow;
    BenefitItem credits;
    BenefitItem play;
    BenefitItem live;
    BenefitItem personalTraining;
    BenefitItem onePass;
    BenefitItem playLimited;
    BenefitItem playCorpLimited;
    BenefitItem playAway;
    BenefitItem playOtherSports;
    BenefitItem gymfitGA;
    BenefitItem gymfitGX;
    BenefitItem luxGx;
    BenefitItem cultGym;

    @Getter
    @Setter
    @ToString
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BenefitItem {
        Integer totalTickets;
        Integer usedTickets;
        Integer remainingTickets;
        BenefitType benefitType;
        Boolean isUnlimitedBenefit;
        Integer inProcessTickets; // tickets which is not totally used up but in the process of consumption

        public BenefitItem(Benefit benefit) {
            totalTickets = benefit.getMaxTickets();
            usedTickets = benefit.getTicketsUsed();
            remainingTickets = benefit.getMaxTickets() - benefit.getTicketsUsed();
            benefitType = benefit.getType();
            if (totalTickets >= UNLIMITED_STATIC_ACCESS_TICKETS) isUnlimitedBenefit = true;
            else if (benefitType.equals(BenefitType.MONTHLY) && totalTickets >= UNLIMITED_MONTHLY_ACCESS_TICKETS) isUnlimitedBenefit = true;
            else isUnlimitedBenefit = false;
            if (benefit.getName().equalsIgnoreCase("ACCESS_CREDITS")) isUnlimitedBenefit = false;
        }

        public BenefitItem(Benefit benefit, Integer inProcess) {
            totalTickets = benefit.getMaxTickets();
            usedTickets = benefit.getTicketsUsed();
            remainingTickets = benefit.getMaxTickets() - benefit.getTicketsUsed();
            benefitType = benefit.getType();
            inProcessTickets = inProcess;
        }

    }
}
