package com.curefit.cfapi.widgets.digital;

import com.curefit.base.enums.Tenant;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.diyfs.pojo.DIYUserAttribute;
import com.curefit.diyfs.pojo.FitnessProgramResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class CtaVerticalListWidgetView extends BaseWidget {
  List<Action> actions;
  Boolean v2Widget;
  @Override
  public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
    String userId = userContext.getUserProfile().getUserId();


    this.actions = actions.stream().map(action -> {
      if (action.getUrl().equals("programpageurl")) {
        DIYUserAttribute userAttribute = interfaces.diyfsService.getUserAttribute(userId, "HOME_GUIDANCE_ATTRIBUTE", Tenant.CUREFIT_APP);
        FitnessProgramResponse response = interfaces.diyfsService.getDIYCoachUserPlan(userId, Tenant.CUREFIT_APP);
        if (response == null || userAttribute == null || userAttribute.getMeta() == null || userAttribute.getMeta().getCyclopsPageId() == null) {
          return null;
        }
        String programPageUrl = "curefit://listpage?pageId=" + userAttribute.getMeta().getCyclopsPageId() + "&pageHook=coachprogram";
        action.setUrl(programPageUrl);
      }
      return action;
    })
      .filter(Objects::nonNull)
      .toList();

    return java.util.Collections.singletonList(this);
  }
}
