package com.curefit.cfapi.widgets;

import com.curefit.cfapi.widgets.base.BaseWidget;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class CultWorkoutBenefitsWidget extends BaseWidget {
    String title;
    String benefits;
}
