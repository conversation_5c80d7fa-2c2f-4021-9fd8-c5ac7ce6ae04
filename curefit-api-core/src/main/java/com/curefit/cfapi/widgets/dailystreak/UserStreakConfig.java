package com.curefit.cfapi.widgets.dailystreak;

import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.util.StreakStateType;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class UserStreakConfig {
    CFTextData bannerHeaderText;
    String bannerImageUrl;
    List<String> bannerGradientColors;
    CFTextData textWidgetHeading;
    CFTextData textWidgetSubHeading;
    List<String> calendarGradientColors;
    List<String> weeklyProgressGradientColors;
    StreakStateType streakStateType;
    boolean isBlackTheme;
}

