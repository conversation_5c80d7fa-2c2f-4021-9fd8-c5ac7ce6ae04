package com.curefit.cfapi.widgets.homescreenwidgets;

    import com.curefit.cfapi.constants.AppDeeplink;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGame;
import com.curefit.cfapi.service.HomePageWidget.HomeScreenWidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.widgets.base.HomeScreenBaseWidget;
import com.curefit.cfapi.widgets.community.CenterLevelChallengeWidget;
import com.curefit.cfapi.widgets.community.pojo.HabitBuildingData;
import com.curefit.cfapi.widgets.fitness.HabitGameProgressWidget;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.*;

import static com.curefit.cfapi.widgets.fitness.HabitGameProgressWidget.getIsSlipping;
import static com.curefit.cfapi.widgets.fitness.UserWeeklyActivity.fetchDaysSinceLastSunday;


@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HSNinjaWidget extends HomeScreenBaseWidget {
    String title;
    String subTitle;
    String dayCountTitle;
    Long weekActiveDays;
    String deeplink;

    @Override
    public List<HomeScreenBaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        String userId = userContext.getUserProfile().getUserId();
        setHomeScreenWidgetType(HomeScreenWidgetType.CULT_NINJA_WIDGET);
        try {
            Map<String, String> queryParams = widgetContext.getQueryParams() != null ? widgetContext.getQueryParams() : new HashMap<>();
            String isMultiplayerGame = queryParams.get("isMultiplayerGame") != null ? queryParams.get("isMultiplayerGame") : null;

            HabitBuildingData habitBuildingData;
            if (Objects.equals(isMultiplayerGame, "true")){
                habitBuildingData = CenterLevelChallengeWidget.getUserAttributesForHabitBuilding(Long.parseLong(userContext.getUserProfile().getUserId()),
                        interfaces, Constants.MULTIPLAYER_GAME_COMPLETION_TIME, Constants.HABIT_BUILDING_WEEK_ATTRIBUTE);
            } else {
                habitBuildingData = CenterLevelChallengeWidget.getUserAttributesForHabitBuilding(Long.parseLong(userId), interfaces);
            }

            int daysSinceLastSunday = fetchDaysSinceLastSunday();
            long habitWeek = habitBuildingData.getHabitWeek();
            long weekActiveDays = Math.min(3, habitBuildingData.getWeekActiveDays());
            boolean isSlipping = getIsSlipping(daysSinceLastSunday, habitBuildingData.getWeekActiveDays(), habitBuildingData.getHabitWeek());


            String title = String.format("%s/4 %s done", weekActiveDays == 3 ? habitWeek : habitWeek - 1, (habitWeek <= 1 ? " week " : " weeks "));


            if(!(habitWeek == 4 && weekActiveDays == 3)){
                setDayCountTitle("Active days");
                setWeekActiveDays(weekActiveDays);
                setTitle(title);
            }

            setWidgetConfig(habitWeek, weekActiveDays, isSlipping);
            setDeeplink(AppDeeplink.HOME_TAB.getDeeplinkString());

        } catch (Exception e){
            String message = String.format("<Homepage Widget> - <CULT_NINJA_WIDGET> Failed to build widget for user : %s : %s", userId, e.getMessage());
            interfaces.exceptionReportingService.reportException(message, e);
            return null;
        }
        return Collections.singletonList(this);
    }

    private void setWidgetConfig(long habitWeek, long weekActiveDays, boolean isSlipping){
        String levelImage = "";
        String bannerImage = "";
        String subTitle = "";


        if (habitWeek == 1) {
            if (weekActiveDays == 0) {
                levelImage = "Level4.png";
                bannerImage = "Banner1.png";
                subTitle = "Time to active?";
            } else if (weekActiveDays == 1) {
                levelImage = "Level4.png";
                bannerImage = "Banner2.png";
                subTitle = "Keep it up!";
            } else if (weekActiveDays == 2) {
                levelImage = "Level4.png";
                bannerImage = "Banner3.png";
                subTitle = "You're on fire!";
            } else if (weekActiveDays == 3) {
                levelImage = "Level4.png";
                bannerImage = "Banner4.png";
                subTitle = "crushed this week! 🌟";
            }
        } else if (habitWeek == 2) {
            if (weekActiveDays == 0) {
                levelImage = "Level3.png";
                bannerImage = "Banner5.png";
                subTitle = "Let's ace it together!";
            } else if (weekActiveDays == 1) {
                levelImage = "Level3.png";
                bannerImage = "Banner6.png";
                subTitle = "Keep the momentum!";
            } else if (weekActiveDays == 2) {
                levelImage = "Level3.png";
                bannerImage = "Banner7.png";
                subTitle = "You're unstoppable!";
            } else if (weekActiveDays == 3) {
                levelImage = "Level3.png";
                bannerImage = "Banner8.png";
                subTitle = "crushed this week! 🌟";
            }
        } else if (habitWeek == 3) {
            if (weekActiveDays == 0) {
                levelImage = "Level2.png";
                bannerImage = "Banner9.png";
                subTitle = "Let's hit 3 again";
            } else if (weekActiveDays == 1) {
                levelImage = "Level2.png";
                bannerImage = "Banner10.png";
                subTitle = "You're doing great!";
            } else if (weekActiveDays == 2) {
                levelImage = "Level2.png";
                bannerImage = "Banner11.png";
                subTitle = "Keep Moving!";
            } else if (weekActiveDays == 3) {
                levelImage = "Level2.png";
                bannerImage = "Banner12.png";
                subTitle = "Another week done🏅";
            }
        } else if (habitWeek == 4) {
            if (weekActiveDays == 0) {
                levelImage = "Level1.png";
                bannerImage = "Banner13.png";
                subTitle = "Final Week, Ninja!";
            } else if (weekActiveDays == 1) {
                levelImage = "Level1.png";
                bannerImage = "Banner14.png";
                subTitle = "💪 Just a bit more!";
            } else if (weekActiveDays == 2) {
                levelImage = "Level1.png";
                bannerImage = "Banner15.png";
                subTitle = "Finish strong!! 🎉";
            } else if (weekActiveDays == 3) {
                levelImage = "Ninja_Celebration.png";
                bannerImage = "Ninja_Celebration.png";
                subTitle = "";
            }
        }
        if(isSlipping){
            levelImage = "Negative.png";
        }

        levelImage = String.format("/image/fitninja/ninja_images/%s",  levelImage);
        bannerImage = String.format("/image/fitninja/ninja_images/%s", bannerImage);

        setSubTitle(subTitle);
        setAssets(
                Map.of("levelImage", levelImage, "bannerImage", bannerImage)
        );
    }
}
