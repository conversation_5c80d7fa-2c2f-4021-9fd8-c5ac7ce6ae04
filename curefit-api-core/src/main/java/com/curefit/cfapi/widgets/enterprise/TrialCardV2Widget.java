package com.curefit.cfapi.widgets.enterprise;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.EnterpriseUtils;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.appConfig.EnterpriseConfig;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.banner.Header;
import com.fasterxml.jackson.annotation.JsonInclude;
import fit.cult.enterprise.dto.corporate.CorporateBenefits;
import fit.cult.enterprise.dto.corporate.CorporateDetails;
import fit.cult.enterprise.dto.program.ProgramType;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TrialCardV2Widget extends BaseWidget {

    private Header header;
    private List<TrialCardV2Item> data;
    private String cardType;
    private String programType;

    public TrialCardV2Widget() {
        super(WidgetType.TRIAL_CARD_WIDGET_V2);
    }

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        if(!AppUtil.isEnterpriseCorpSupported(userContext)) {
            return null;
        }
        if (cardType == null) {
            cardType = "TRIAL_CARD";
        }
        data = new ArrayList<>();
        addDataItems(interfaces, userContext);
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        List<TrialCardV2Item> freeProgram = data.stream().filter(item -> item.getOfferPrice().intValue() == 0).sorted(Comparator.comparing(TrialCardV2Item::getPosition)).toList();
        List<TrialCardV2Item> paidProgram = data.stream().filter(item -> item.getOfferPrice().intValue() > 0).sorted(Comparator.comparing(TrialCardV2Item::getPosition)).toList();
        data = new ArrayList<>();
        data.addAll(freeProgram);
        data.addAll(paidProgram);
        return super.buildView(interfaces, userContext, widgetContext);
    }

    private void addDataItems(ServiceInterfaces interfaces, UserContext userContext) throws Exception {
        String userId = userContext.getUserProfile()
                                   .getUserId();
        CorporateDetails corporateDetails = interfaces.getEnterpriseClient()
                                                      .getCorporateWithBenefits(userId);
        if (corporateDetails == null) {
            log.info("No corporate found for userId {}", userId);
            return;
        }

        if(corporateDetails.getBenefits().size() == 0) {
            return;
        }

        log.info("Corporate found for userId {}", corporateDetails.toString());
        Map<ProgramType, List<CorporateBenefits>> benefitsByTypes = corporateDetails.getBenefits().stream().collect(groupingBy(CorporateBenefits::getProgramType));
        for(ProgramType programType : benefitsByTypes.keySet()) {

            TrialCardV2Item item = null;
            if (!shouldIncludeProgram(programType)) {
                continue;
            }
            CorporateBenefits benefit = EnterpriseUtils.getActiveBenefitForDisplay(interfaces, userContext, programType, benefitsByTypes.get(programType));
            if (benefit == null) {
                continue;
            }

            log.debug("Corporate program type: " + programType.toString());
            switch (programType) {
                case CULT_LIVE, COUPON_BASED_CULT_LIVE -> item = new TrialCardV2Item(benefit.getStartDate(), benefit.getEndDate(), BigDecimal.ZERO, BigDecimal.ZERO, true, ProgramType.CULT_LIVE);
                case ELITE_FIX_DATES, ELITE_FIX_DATES_WITHOUT_LIVE -> item = new TrialCardV2Item(benefit.getStartDate(), benefit.getEndDate(), BigDecimal.ZERO, BigDecimal.ZERO, true, ProgramType.ELITE_FIX_DATES);
                case PRO_FIX_DATES -> item = new TrialCardV2Item(benefit.getStartDate(), benefit.getEndDate(), BigDecimal.ZERO, BigDecimal.ZERO, true, ProgramType.PRO_FIX_DATES);
                case ELITE_FIX_DURATION, PRO_FIX_DURATION, ELITE_FIX_DURATION_WITHOUT_LIVE -> item = EnterpriseUtils.buildEliteDirectMembershipCardWidget(interfaces, userContext, benefit);
                case THERAPY, ONLINE_DIETICIAN, DOCTOR_CONSULTATION -> item =
                        EnterpriseUtils.getTrialCardFromOfferId(interfaces, userContext, benefit, cardType, true, corporateDetails.getCorporate().getCode());

                case DISCOUNTED_CULT_LIVE -> item = EnterpriseUtils.getTrialCardFromOfferId(interfaces,
                        userContext, benefit, cardType, false, corporateDetails.getCorporate().getCode());
                case ONLINE_PT -> item = EnterpriseUtils.buildOnlinePTTrialCardWidget(interfaces,userContext, benefit, cardType);
                case DISCOUNTED_CULT_PRO -> item = EnterpriseUtils.buildCultProProgramTrialCardWidget(interfaces, userContext, benefit);
                case DISCOUNTED_CULT_ELITE -> item = EnterpriseUtils.buildCultEliteProgramTrialCardWidget(interfaces, userContext, benefit);
                case DISCOUNTED_CULT_PLAY -> item = EnterpriseUtils.buildCultPlayProgramTrialCardWidget(interfaces, userContext, benefit);
                case PLAY_LIMITED_FIX_DATES, PLAY_LIMITED_FIX_DURATION -> item = EnterpriseUtils.buildCultPlayLimitedProgramTrialCardWidget(interfaces, userContext, benefit);
                case PLAY_SWIM_LIMITED_FIX_DATES, PLAY_SWIM_LIMITED_FIX_DURATION -> item = EnterpriseUtils.buildCultPlayLimitedProgramTrialCardWidget(interfaces, userContext, benefit);
                case PLAY_BADMINTON_LIMITED_FIX_DATES, PLAY_BADMINTON_LIMITED_FIX_DURATION -> item = EnterpriseUtils.buildCultPlayLimitedProgramTrialCardWidget(interfaces, userContext, benefit);
                case COACH -> item = EnterpriseUtils.buildTransformProgramTrialCardWidget(interfaces, userContext, benefit);
                case CULT_LIVE_FIXED_DURATION -> item = EnterpriseUtils.buildLiveDirectMembershipCardWidget(interfaces, userContext, benefit);
                case OP_PREMIUM_FIX_DATE, OP_STANDARD_FIX_DATE -> item = new TrialCardV2Item(benefit.getStartDate(), benefit.getEndDate(), BigDecimal.ZERO, BigDecimal.ZERO, true, ProgramType.OP_PREMIUM_FIX_DATE);
                case OP_PREMIUM_FIX_DURATION, OP_STANDARD_FIX_DURATION -> item = EnterpriseUtils.buildOnePassDirectMembershipCardWidget(interfaces, userContext, benefit);
                case ELITE_LIMITED_FIX_DATES, ELITE_LIMITED_FIX_DURATION, CULTPASSX_FIX_DATES, CULTPASSX_FIX_DURATION -> item = EnterpriseUtils.buildLimitedEliteTrialCardWidget(interfaces, userContext, corporateDetails.getCorporate().getCode());
            }
            if (item != null) {
                BenefitInfo cardConfig = EnterpriseConfig.PROGRAM_BENEFITS.get(benefit.getProgramType());
                Action action = EnterpriseConfig.TRIAL_CARD_ACTIONS.get(benefit.getProgramType());
                if (item.getAction() == null && action != null) {
                    item.setAction(action);
                }
                if (programType.equals(ProgramType.OP_PREMIUM_FIX_DURATION) || programType.equals(ProgramType.OP_PREMIUM_FIX_DATE) || programType.equals(ProgramType.OP_STANDARD_FIX_DATE) || programType.equals(ProgramType.OP_STANDARD_FIX_DURATION)) {
                    item.setAction(EnterpriseUtils.getOnePassMemberUrlAction(userContext, null));
                }
                if (cardConfig != null) {
                    item.setTitle(cardConfig.getTitle());
                    if ("CULT_CORP_PASS".equals(cardType)) {
                        item.setCompanyLogoUrl(corporateDetails.getCorporate()
                                .getLogo());
                        item.setCompanyName(corporateDetails.getCorporate().getName());
                        item.setSponsoredByCaption("sponsored by");

                        if ("THERAPY".equals(programType.toString())) {
                            item.setAction(new Action(EnterpriseConfig.THERAPY_BOOKING_URL, ActionType.NAVIGATION));
                        } else if("PT".equals(programType.toString())) {
                            item.setTitle("cultpass CORP");
                        } else {
                            item.setAction(null);
                        }
                    } else {
                        item.setSubtitle(cardConfig.getDesc());
                        item.setImage(cardConfig.getIconUrl());
                    }
                    item.setPosition(cardConfig.getPosition());
                    data.add(item);
                }
            }
        }
    }

    private boolean shouldIncludeProgram(ProgramType type) {
        if (programType == null) {
            return true;
        }
        return switch (programType) {
            case "THERAPY" -> type == ProgramType.THERAPY;
            case "DIETICIAN" -> type == ProgramType.ONLINE_DIETICIAN;
            case "PT" -> type == ProgramType.ONLINE_PT;
            case "TELECONSULTATION" -> type == ProgramType.DOCTOR_CONSULTATION;
            case "CULT_LIVE" -> type == ProgramType.CULT_LIVE || type == ProgramType.DISCOUNTED_CULT_LIVE;
            default -> false;
        };
    }
}
