package com.curefit.cfapi.widgets.fitness;

import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGame;
import com.curefit.cfapi.pojo.vm.atom.*;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.autoLaunchService.autoLaunchTriggers.NewBadgeAutoLaunch;
import com.curefit.cfapi.service.community.CommunityService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.community.pojo.HabitBuildingData;
import com.curefit.cfapi.widgets.community.pojo.ProgressIndicatorTile;
import com.curefit.common.data.exception.BaseException;
import com.curefit.cult.models.KickstarterPlanDetailsResponse;
import com.curefit.gymfit.models.Pitch;
import com.curefit.gymfit.models.PitchStatus;
import com.curefit.maestro.pojo.RewardPointsSummary;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.types.Status;
import com.curefit.quest.enums.Vertical;
import com.curefit.quest.pojo.*;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.socialservice.client.impl.SocialServiceClientImpl;
import com.curefit.uas.pojo.fitnessReport.AttendedClassHistory;
import com.curefit.uas.responses.FitnessReportResponse;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.curefit.cfapi.pojo.vm.widget.WidgetType.USER_WEEKLY_ACTIVITY_WIDGET;
import static com.curefit.cfapi.service.community.CommunityService.getUserEntryMap;
import static com.curefit.cfapi.util.CultUtil.MILLIS_TO_DAYS_DENOMINATOR;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections.CollectionUtils.isEmpty;


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
class OverrideAssetConfig {
    @JsonProperty("enableForProd")
    Boolean enableForProd;
    @JsonProperty("segmentId")
    String segmentId;
    @JsonProperty("fileType")
    String fileType;
    @JsonProperty("bgLottieAssetUrl")
    String bgLottieAssetUrl;
    @JsonProperty("bgImgAssetUrl")
    String bgImgAssetUrl;
    @JsonProperty("bgGIFAssetUrl")
    String bgGIFAssetUrl;
    @JsonProperty("title")
    String title;
    @JsonProperty("subtitle")
    String subtitle;
    @JsonProperty("pledgeAssetFileType")
    String pledgeAssetFileType;
    @JsonProperty("pledgeMeterLottieUrl")
    String pledgeMeterLottieUrl;
    @JsonProperty("pledgeMeterImgUrl")
    String pledgeMeterImgUrl;
    @JsonProperty("pledgeMeterGIFUrl")
    String pledgeMeterGIFUrl;
    @JsonProperty("hidePledgeMeterLabel")
    Boolean hidePledgeMeterLabel;
    @JsonProperty("hidePledgeMeterPointers")
    Boolean hidePledgeMeterPointers;
    @JsonProperty("hidePledgeMeterGrades")
    Boolean hidePledgeMeterGrades;
    @JsonProperty("hidePledgeMeterMessageGradient")
    Boolean hidePledgeMeterMessageGradient;
}

@Getter
@Setter
@Slf4j
@AllArgsConstructor
public class UserWeeklyActivity extends BaseWidget {

    private static final String USER_WEEKLY_ACTIVITY_WIDGET_ID = "";

    OverrideAssetConfig overrideAssetConfig;

    public UserWeeklyActivity() { super(USER_WEEKLY_ACTIVITY_WIDGET); }

    SocialServiceClientImpl socialServiceClient;


    @Override
    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        //String queryDate = queryParams.get("startDate") != null? queryParams.get("startDate"): null;

        ZoneId istZone = ZoneId.of("Asia/Kolkata");
        LocalDateTime now = LocalDateTime.now(Clock.system(istZone));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter sdfformatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String currentDate = now.format(sdfformatter);
        String userId = userContext.getUserProfile().getUserId();
        String userFirstName = userContext.getUserEntryCompletableFuture().get().getFirstName();
        List<Membership> memberships = MembershipUtil.activeMembershipPresentForBenefits(interfaces, userContext, List.of("GYMFIT_GA", "GYMFIT_GX", "CULT")); // "PLAY","LUX"

        if (CollectionUtils.isEmpty(memberships)) {
            Collection<Pitch> pitches = interfaces.gymfitClient.pitchService().getScheduledPitchDataForUser(userId, List.of(PitchStatus.SCHEDULED));
            if (Objects.nonNull(pitches) && !pitches.isEmpty()) {
                StringListWidget stringListWidget = new StringListWidget();
                stringListWidget.setCardBackground(CFImageData.builder().url("/image/mem-exp/xp/daily1x.png").width(350.0).height(500.0).boxFit(BoxFit.FILL).build());
                stringListWidget.setHorizontalPadding(20.0);
                stringListWidget.setCrossAlignment(Alignment.CENTER);
                stringListWidget.setPaddingInBetween(List.of(150, 10, 10, 100));
                List<CFTextData> textDataList = new ArrayList<>();
                textDataList.add(CFTextData.builder().text(STR."Hello \{userFirstName}!").typeScale(UiUtils.TextTypeScales.Inter16F700W).build());
                textDataList.add(CFTextData.builder().text("Congratulations on taking the first step").typeScale(UiUtils.TextTypeScales.H11).alignment(Alignment.CENTER.toString()).maxLine("3").build());
                textDataList.add(CFTextData.builder().text("Simply show up and we’ll handle the rest!").typeScale(UiUtils.TextTypeScales.P2).build());
                stringListWidget.setStringList(textDataList);
                return List.of(stringListWidget);
            }
        }

        KickstarterPlanDetailsResponse userFitnessDetails = interfaces.cultService.getUserKickstarterPlanData(userId, currentDate).get();
        String profilePictureUrl = userContext.getUserEntryCompletableFuture().get().getProfilePictureUrl();
        Boolean isCommunityUser = AppUtil.isCommunityTabSupported(userContext, interfaces.segmentEvaluatorService, interfaces.environmentService);

        if (userFitnessDetails == null) return null;

//        List<String> ACTIVE_MONDAY_MOTIVATION = Arrays.asList(
//                "Monday, the perfect day to crush your doubts and your workout.",
//                "Monday's calling for a workout warrior like you. Seize the day.",
//                "Monday: Unleash your fitness beast!",
//                "Sweat now, slay later. It's Monday!",
//                "Monday + Workout = Unstoppable.");
//
//        List<String> ACTIVE_TUESDAY_MOTIVATION = Arrays.asList(
//                "Sweat is fat crying.",
//                "The pain you feel today will be the strength you feel tomorrow.",
//                "Strive for progress, not perfection.",
//                "The hardest lift of all is lifting your butt off the couch.",
//                "Excuses don't burn calories.");
//
//        List<String> ACTIVE_MON_TUE_PLEDGE_OVERACHIEVED = Arrays.asList(
//                "Pace yourself; fitness is a journey, not a sprint.",
//                "Balance is essential; all work and no rest leads to burnout.",
//                "Rest isn't weakness; it's an integral part of growth.",
//                "Muscles grow during rest periods, not during the workout.",
//                "Rest days are as important as workout days."
//        );
//
//        List<String> ACTIVE_ON_TRACK_PLEDGE_ACHIEVABLE = Arrays.asList(
//                "Regular exercise can boost your immune system, making you more resistant to common illnesses.",
//                "Keep up the good work and continue pushing yourself towards your fitness goals.",
//                "Consistent exercise helps regulate blood sugar, aiding in diabetes prevention.",
//                "Routine physical activity improves mood, helping to mitigate depression.",
//                "Regular workouts enhance cognitive function, supporting brain health."
//        );
//
//        List<String> ACTIVE_PLEDGE_ACHIEVED = Arrays.asList(
//                "Goal accomplished! You're on fire!",
//                "Mission complete! You're a goal-crushing machine!",
//                "You did the thing! Keep up the fantastic work!",
//                "Goal demolished! You're leveling up in fitness!",
//                "Week's goal: crushed! Keep the momentum going!");
//
//        List<String> ACTIVE_PLEDGE_EXCEEDED = Arrays.asList(
//                "Boom! You soared past your weekly goal. Superstar status achieved!",
//                "Goal surpassed! You're leveling up like a pro. Keep pushing those limits!",
//                "Overachievement unlocked! You're on a roll!",
//                "Goal domination achieved! You're unstoppable!"
//        );
//
//        List<String> CLASS_CANCELLED = Arrays.asList(
//                "Even minor progress counts. Rest if necessary, resume stronger tomorrow.",
//                "All progress matters. Break if required, bounce back mightier tomorrow.",
//                "Tiny strides still advance. Pause when needed, rebound tougher tomorrow.",
//                "Progress, however small, is valid. Rest if needed, restart more powerful tomorrow."
//        );

        List <String> INACTIVE_ZERO_CLASSES_DONE = Arrays.asList(
                "Regular exercise can boost your immune system, making you more resistant to common illnesses",
                "Don't let a setback derail your journey. Take a deep breath, reset, and get back on track!",
                "Setbacks aren't Road Ends: Exhale, Regroup, and Keep Moving Forward"
        );

        List <String> INACTIVE_MORE_THAN_ONE_CLASS_DONE = Arrays.asList(
                "Regular exercise can boost your immune system, making you more resistant to common illnesses",
                "Keep up the good work and continue pushing yourself towards your fitness goals."
        );

        List<String> ACTIVE_NOT_ON_TRACK_PLEDGE_ACHIEVABLE = Arrays.asList(
                "You can do it, take it one step at a time and start small",
                "Dream, believe, achieve; mindset matters"
        );

        String ACTIVE_PLEDGE_UNACHIEVABLE_OR_STREAK_IS_ZERO = "You can still workout and come close to your target!";
        String ACTIVE_FRI_SAT_NO_CLASSES_DONE_AND_NON_ZERO_STREAK = "You are going to lose your streak if don’t do any activity by Sunday";
        String ACTIVE_SUN_NO_CLASSES_DONE_AND_NON_ZERO_STREAK = "Lets do a workout today and keep your " + userFitnessDetails.getWeeklyStreaks() + " week streak going";
        String INACTIVE_ONE_CLASS_DONE = "Welcome back, fitness lover! Let's pick up where we left off and keep crushing those goals!";

        UserWeeklyActivityResponse UserFitnessStatesWidgetResponse = new UserWeeklyActivityResponse();
        UserWeeklyActivityResponse.EditPreference editPreference = null;
        UserWeeklyActivityResponse.FitnessMetrices fitnessMetric = null;
        UserWeeklyActivityResponse.BadgeAllocation badgeAllocation = null;

        UserFitnessStatesWidgetResponse.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "0"));

        String bgIllustation = "/image/mem-exp/xp/daily1x.png";
        String bgIllustationLottie = null;
        String overlayLottie = null;
        String motivationText = "";
        String centerImgBarContent = null;
        String centerLottieBarContent = null;
        String centerTextBarContent = null;
        String labelTextBarContent = "THIS WEEK ACTIVITY";
        boolean displayWeeklyNavigator = false;
        boolean playLottieOnce = false;
        boolean darkShadeBg = false;

        Integer maxPledgeThreshold = 7;
        Integer minPledgeThreshold = 1;
        Integer pledgeIfNotSet = 3;
        String birdsLottie = "/image/mem-exp/lottie/bird.json";
        String celebrationLottie = "/image/mem-exp/lottie/celebration.json";
        String dailyLottie = "assets/daily.json";
        String pauseLottie = "assets/pause.json";
        String positiveLottie = "assets/positive.json";
        String milestoneLottie = "assets/milestone.json";
        String negativeLottie = "assets/negative.json";
        String brokenLottie = "assets/broken.json";
        overlayLottie = birdsLottie;
        bgIllustationLottie = dailyLottie;

        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        Integer weekNumber = calendar.get(Calendar.WEEK_OF_YEAR);
        int dow = calendar.get(Calendar.DAY_OF_WEEK);
        long baseSeed = 12345L;
        long seed = baseSeed + weekNumber * 1000 + dow;
        Random r = new Random(seed);

        Badge badge = null;
        String lastAttendedBookingIdOrCheckIn = getLastCheckinId(userFitnessDetails);
        if (lastAttendedBookingIdOrCheckIn != null) {
            badge = findBadge(userId, lastAttendedBookingIdOrCheckIn, interfaces);
        }

        //UserWeeklyActivityPageConfig weeklyActivityConfig = interfaces.userWeeklyActivityRepository.findByPageId("UserWeeklyActivity").get();
        //List<UserWeeklyActivityMedia> weeklyActivityContent = weeklyActivityConfig.getData();


        if (badge != null) {
            try {
                String badgeId = badge.getBadgeId();
                String badgeKey = "state_of_" + badgeId;
                String badgeShowedState = interfaces.featureStateCache.get(userId,
                        badgeKey).get();
                Boolean isExpanded = true;
                Boolean isClosed = false;
                if (badgeShowedState != null) {
                    if (badgeShowedState.equals("shown")) {
                        isExpanded = false;
                        isClosed = false;
                    } else if (badgeShowedState.equals("closed")) {
                        isExpanded = false;
                        isClosed = true;
                    }
                } else {
                    isClosed = false;
                    isExpanded = true;
                }
                if (!isClosed) {
                    String badgeName = badgeId.equals("HABIT_GAME_COMPLETED") ? "Woohoo!" : badge.getName();
                    badgeAllocation = new UserWeeklyActivityResponse.BadgeAllocation(badgeId, badge.getImageUrl(), badgeName, badge.getBadgeCategory(),
                            isCommunityUser, userContext.getUserEntryCompletableFuture().get().getFirstName(), "NEW ACHIEVEMENT UNLOCKED", badge.getName(), isExpanded);
                    interfaces.featureStateCache.set(userId,
                            badgeKey, "shown").get();
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        UserAttributesResponse userAttributesResponse = null;
        Integer userNewAttrPledge = 0;
        try {
            userAttributesResponse = interfaces.userAttributesCacheClient.getAttributes(Long.valueOf(userId),
                    Arrays.asList(Constants.RASHI_ATTRIBUTE_FOR_USER_PLEDGE_NEW), AppTenant.CUREFIT);
        } catch (BaseException e) {
            throw new RuntimeException(e);
        }

        Integer actualPledgedDays = pledgeIfNotSet;

        if (userAttributesResponse != null && userAttributesResponse.getAttributes() != null) {
            if (userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_PLEDGE_NEW) != null)
                userNewAttrPledge = Integer.parseInt(userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_PLEDGE_NEW).toString());
        }

        if(userNewAttrPledge != null && userNewAttrPledge > 0) {
            actualPledgedDays = userNewAttrPledge;
        }

        String FIRST_TIME_USER = "Hello " + userFirstName + "!\nDo atleast " + actualPledgedDays + " activity a week to achieve your fitness goals";

        Integer actualActiveDays = userFitnessDetails.getActiveDays() != null ? (Integer) userFitnessDetails.getActiveDays().getActive() : 0;
        Integer reformedPledgedDays = Math.max(actualPledgedDays, minPledgeThreshold);
        Integer reformedActiveDays = actualActiveDays;
        if (actualActiveDays > actualPledgedDays) {
            reformedPledgedDays = Math.max(Math.min(actualActiveDays, maxPledgeThreshold), minPledgeThreshold);
            reformedActiveDays = Math.min(actualActiveDays, maxPledgeThreshold);
        }

        String today = now.format(formatter);

        LocalDateTime previousDay = now.minusDays(1);
        String previousDayString = previousDay.format(formatter);

        LocalDate monday = now.toLocalDate().with(DayOfWeek.MONDAY);
        LocalDateTime mondayDateTime = monday.atStartOfDay();
        ZonedDateTime mondayZonedDateTime = mondayDateTime.atZone(istZone);
        String currentMonday = mondayZonedDateTime.format(formatter);

        LocalDate sunday = now.toLocalDate().with(DayOfWeek.SUNDAY);
        LocalDateTime sundayDateTime = sunday.atStartOfDay();
        ZonedDateTime sundayZonedDateTime = sundayDateTime.atZone(istZone);
        String currentSunday = sundayZonedDateTime.format(formatter);

        LocalDateTime previousMonday = now.minusWeeks(1).with(DayOfWeek.MONDAY);
        ZonedDateTime previousMondayZonedDateTime = previousMonday.atZone(istZone);
        String previousMondayString = previousMondayZonedDateTime.format(formatter);

        LocalDateTime previousSunday = now.minusWeeks(1).with(DayOfWeek.SUNDAY);
        ZonedDateTime previousSundayZonedDateTime = previousMonday.atZone(istZone);
        String previousSundayString = previousSundayZonedDateTime.format(sdfformatter);

        LocalDate nextMonday = now.toLocalDate().with(DayOfWeek.MONDAY).plusWeeks(1);
        LocalDateTime nextMondayDateTime = nextMonday.atStartOfDay();
        ZonedDateTime nextMondayZonedDateTime = nextMondayDateTime.atZone(istZone);
        String nextMondayString = nextMondayZonedDateTime.format(formatter);

        /*FitnessReportResponse reportData = (FitnessReportResponse) userContext.getRequestCache().getRequestFuture(RequestType.GET_WEEKLY_FITNESS_REPORT,
                userId, currentDate).get();*/
        FitnessReportResponse reportData = interfaces.uasFitnessReportClient.getWeeklyAugmentedResponseForUserAndWeekDate(Long.valueOf(userId), currentDate).get();
        
        List<AttendedClassHistory> classesAttendedHistory = reportData.getClassesAttendedHistory();
        int classesDoneLastWeek = getClassValueAtIndex(classesAttendedHistory, -2);
        int classesDoneLastToLastWeek = getClassValueAtIndex(classesAttendedHistory, -3);

        DayOfWeek dayOfWeek = now.getDayOfWeek();

        boolean isActiveUser = classesDoneLastWeek > 0 || classesDoneLastToLastWeek > 0;
        boolean pledgeAchievable = ((dayOfWeek != DayOfWeek.SUNDAY? (7 - dayOfWeek.getValue() + 1): 0) >= (actualPledgedDays - actualActiveDays));

        boolean showCoachMark = false;
        String coachMarkState = interfaces.featureStateCache.get(userId,
                AppUtil.USER_ACTIVITY_COACHMARK_CACHE_KEY).get();
        coachMarkState = coachMarkState != null && coachMarkState.isEmpty() ? null: coachMarkState;
        String newCoachMarkState = null;

        if(coachMarkState != null  && coachMarkState.equalsIgnoreCase("viewed")) {
            newCoachMarkState = "viewed_" + weekNumber;
        }
        else if (coachMarkState != null && !coachMarkState.equalsIgnoreCase("not_viewed")
                && !coachMarkState.split("_")[1].equalsIgnoreCase(weekNumber.toString())) {
            newCoachMarkState = "not_viewed";
            showCoachMark = true;
        } else if (coachMarkState == null || coachMarkState.equalsIgnoreCase("not_viewed")) {
            newCoachMarkState = "viewed_" + weekNumber;
            showCoachMark = false;
        }

        try {
            interfaces.featureStateCache.set(userId,
                    AppUtil.USER_ACTIVITY_COACHMARK_CACHE_KEY, newCoachMarkState);
        } catch (Exception e) {
            String message = String.format(
                    "Unable to reset featureCache State for user activity coachmark, error :: %s", e.getMessage());
            log.error(message, e);
        }


        boolean playAnimations = false;
        String dayOfWeekVal = String.valueOf(dayOfWeek.getValue());
        String animateOnceState = interfaces.featureStateCache.get(userId,
                AppUtil.USER_ACTIVITY_ANIMATE_KEY).get();
        animateOnceState = animateOnceState != null && animateOnceState.isEmpty() ? null: animateOnceState;
        String newAnimateOnceState = null;

        if (animateOnceState != null && !animateOnceState.contains("not_animate")
                && animateOnceState.split("_")[1].equalsIgnoreCase(dayOfWeekVal)) {
            newAnimateOnceState = "not_animate_" + dayOfWeekVal;
            playAnimations = false;
        } else if (animateOnceState == null || (animateOnceState.contains("not_animate")
                && !animateOnceState.split("_")[2].equalsIgnoreCase(dayOfWeekVal))
                || (!animateOnceState.contains("not_animate")
                && !animateOnceState.split("_")[1].equalsIgnoreCase(dayOfWeekVal))) {
            newAnimateOnceState = "animate_" + dayOfWeekVal;
            playAnimations = true;
        }

        try {
            interfaces.featureStateCache.set(userId,
                    AppUtil.USER_ACTIVITY_ANIMATE_KEY, newAnimateOnceState);
        } catch (Exception e) {
            String message = String.format(
                    "Unable to reset featureCache State for user activity animations, error :: %s", e.getMessage());
            log.error(message, e);
        }

        boolean isWeeklyStreakAboutToBreak = false;

        Action pledgeAction = new Action();
        pledgeAction.setActionType(ActionType.NAVIGATION);
        pledgeAction.setUrl("curefit://pledgescreen");

        boolean isMembershipPaused = false;
        boolean hasMembershipStartedThisWeek = false;
        Membership pausedMembership = null;
        if (!isEmpty(memberships)) {
            for (Membership membership : memberships) {
                if (membership.getStatus().equals(Status.PAUSED)) {
                    isMembershipPaused = true;
                    pausedMembership = membership;
                }
                String membershipStartDate = TimeUtil.getTimeInFormatFromMillis(membership.getStart(), "yyyy-MM-dd HH:mm:ss", "Asia/Kolkata");
                if (membershipStartDate.compareToIgnoreCase(currentMonday) >= 0 && membershipStartDate.compareToIgnoreCase(currentSunday) <= 0) {
                    hasMembershipStartedThisWeek = true;
                }
            }
        }
        centerTextBarContent = actualActiveDays + "/" + actualPledgedDays;

        if ((userFitnessDetails.getWasActiveInLastClass() != "first" && classesDoneLastWeek == 0
                && classesDoneLastToLastWeek == 0 && !hasMembershipStartedThisWeek)
        ) {
            editPreference = new UserWeeklyActivityResponse.EditPreference("No activity in last 2 weeks",
                    "You can edit your preferences if you have over committed on your goal.", pledgeAction);
        }
        else if((userFitnessDetails.getWasActiveInLastClass() != "first" && classesDoneLastWeek > actualPledgedDays
                && classesDoneLastToLastWeek > actualPledgedDays && !hasMembershipStartedThisWeek)
        ) {
            editPreference = new UserWeeklyActivityResponse.EditPreference("Overachieving your goals?",
                    "Time to raise the bar and update your preferences", pledgeAction);
        }

        if (isMembershipPaused) {
            motivationText = "Wishing you a quick recovery and sending lots of love your way.";
            if (pausedMembership != null && pausedMembership.getActivePause() != null && pausedMembership.getActivePause().getReason() != null
                    && !pausedMembership.getActivePause().getReason().isEmpty()) {
                motivationText = getPauseMotivationalText(pausedMembership.getActivePause().getReason(), (int) (pausedMembership.getRemainingPauseDuration() / MILLIS_TO_DAYS_DENOMINATOR));
            }
            bgIllustation = "/image/mem-exp/xp/pause1x.png";
            bgIllustationLottie = pauseLottie;
            displayWeeklyNavigator = false;
            badgeAllocation = null;
            editPreference = null;
            centerTextBarContent = null;
        } else if (userFitnessDetails.getWasActiveInLastClass() == "first" && hasMembershipStartedThisWeek) {
            motivationText = FIRST_TIME_USER;
            bgIllustation = "/image/mem-exp/xp/daily1x.png";
            bgIllustationLottie = dailyLottie;
            if (userFitnessDetails.getActiveDays() == null)
                editPreference = new UserWeeklyActivityResponse.EditPreference("First time with no pledge", "You can edit your preferences here.", pledgeAction);
        } else if (actualActiveDays == Math.max(actualPledgedDays, minPledgeThreshold)) {
            motivationText = null;
            bgIllustation = "/image/mem-exp/xp/completed1x.png";
            bgIllustationLottie = positiveLottie;
            centerLottieBarContent = "/image/mem-exp/lottie/happy.json";
            overlayLottie = celebrationLottie;
            playLottieOnce = true;
            darkShadeBg = true;
        } else if (actualActiveDays > Math.max(actualPledgedDays, minPledgeThreshold)) {
            motivationText = null;
            bgIllustation = "/image/mem-exp/xp/celebration1x.png";
            bgIllustationLottie = milestoneLottie;
            centerLottieBarContent = "/image/mem-exp/lottie/extra-happy.json";
            overlayLottie = celebrationLottie;
            playLottieOnce = true;
            darkShadeBg = true;
        } else if (!isActiveUser) {
            if (actualActiveDays == 0) {
                motivationText = INACTIVE_ZERO_CLASSES_DONE.get(r.nextInt(INACTIVE_ZERO_CLASSES_DONE.size()));
            } else if (actualActiveDays == 1) {
                motivationText = INACTIVE_ONE_CLASS_DONE;
            } else if (actualActiveDays > 1 || pledgeAchievable) {
                motivationText = INACTIVE_MORE_THAN_ONE_CLASS_DONE.get(r.nextInt(INACTIVE_MORE_THAN_ONE_CLASS_DONE.size()));
            }
            bgIllustation = "/image/mem-exp/xp/daily1x.png";
            bgIllustationLottie = dailyLottie;
        } else if (isActiveUser) {
            switch (dayOfWeek) {
                case MONDAY:
                    if(classesDoneLastWeek > 6) {
                        motivationText = null;
                        bgIllustation = "/image/mem-exp/xp/daily1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    else if (actualActiveDays == 0) {
                        motivationText = null;//ACTIVE_MONDAY_MOTIVATION.get(r.nextInt(ACTIVE_MONDAY_MOTIVATION.size()));
                        bgIllustation = "/image/mem-exp/xp/daily1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    else if (userFitnessDetails.getBookingCancelledToday()) {
                        motivationText = null;
                        bgIllustation = "/image/mem-exp/xp/negative1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    else {
                        motivationText = null;//ACTIVE_ON_TRACK_PLEDGE_ACHIEVABLE.get(r.nextInt(ACTIVE_ON_TRACK_PLEDGE_ACHIEVABLE.size()));
                        bgIllustation = "/image/mem-exp/xp/daily1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    break;
                case TUESDAY:
                    if(classesDoneLastWeek > 6) {
                        motivationText = null;
                        bgIllustation = "/image/mem-exp/xp/daily1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    else if (actualActiveDays == 0) {
                        motivationText = null;//ACTIVE_TUESDAY_MOTIVATION.get(r.nextInt(ACTIVE_TUESDAY_MOTIVATION.size()));
                        bgIllustation = "/image/mem-exp/xp/daily1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    else if (userFitnessDetails.getBookingCancelledToday()) {
                        motivationText = null;
                        bgIllustation = "/image/mem-exp/xp/negative1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    else {
                        motivationText = null;//ACTIVE_ON_TRACK_PLEDGE_ACHIEVABLE.get(r.nextInt(ACTIVE_ON_TRACK_PLEDGE_ACHIEVABLE.size()));
                        bgIllustation = "/image/mem-exp/xp/daily1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    break;
                case WEDNESDAY:
                case THURSDAY:
                    if (userFitnessDetails.getBookingCancelledToday()) {
                        motivationText = null;
                        bgIllustation = "/image/mem-exp/xp/negative1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    else if (pledgeAchievable) {
                        motivationText = ACTIVE_NOT_ON_TRACK_PLEDGE_ACHIEVABLE.get(r.nextInt(ACTIVE_NOT_ON_TRACK_PLEDGE_ACHIEVABLE.size()));
                        bgIllustation = "/image/mem-exp/xp/daily1x.png";
                        bgIllustationLottie = dailyLottie;
                    } else {
                        motivationText = null;//ACTIVE_PLEDGE_UNACHIEVABLE_OR_STREAK_IS_ZERO;
                        bgIllustation = "/image/mem-exp/xp/negative1x.png";
                        bgIllustationLottie = negativeLottie;
                    }
                    break;
                case FRIDAY:
                case SATURDAY:
                case SUNDAY:
                    if ((dayOfWeek == DayOfWeek.FRIDAY || dayOfWeek == DayOfWeek.SATURDAY) && actualActiveDays == 0 && ((Integer) userFitnessDetails.getWeeklyStreaks()) > 0) {
                        motivationText = ACTIVE_FRI_SAT_NO_CLASSES_DONE_AND_NON_ZERO_STREAK;
                        bgIllustation = "/image/mem-exp/xp/more-negative1x.png";
                        bgIllustationLottie = negativeLottie;
                        centerLottieBarContent = "/image/mem-exp/lottie/sad.json";
                        isWeeklyStreakAboutToBreak = true;
                        overlayLottie = null;
                    } else if ((dayOfWeek == DayOfWeek.SUNDAY) && actualActiveDays == 0 && ((Integer) userFitnessDetails.getWeeklyStreaks()) > 0) {
                        motivationText = ACTIVE_SUN_NO_CLASSES_DONE_AND_NON_ZERO_STREAK;
                        bgIllustation = "/image/mem-exp/xp/negative1x.png";
                        bgIllustationLottie = brokenLottie;
                        centerLottieBarContent = "/image/mem-exp/lottie/sad.json";
                        isWeeklyStreakAboutToBreak = true;
                        overlayLottie = null;
                    } else if (actualActiveDays < actualPledgedDays && dayOfWeek == DayOfWeek.SUNDAY) {
                        motivationText = INACTIVE_ZERO_CLASSES_DONE.get(r.nextInt(INACTIVE_ZERO_CLASSES_DONE.size()));
                        bgIllustation = "/image/mem-exp/xp/negative1x.png";
                        bgIllustationLottie = negativeLottie;
                        centerLottieBarContent = "/image/mem-exp/lottie/sad.json";
                        isWeeklyStreakAboutToBreak = false;
                        overlayLottie = null;
                    }
                    else if (userFitnessDetails.getBookingCancelledToday() && dayOfWeek != DayOfWeek.SUNDAY) {
                        motivationText = null;
                        bgIllustation = "/image/mem-exp/xp/negative1x.png";
                        bgIllustationLottie = dailyLottie;
                    }
                    else {
                        if (pledgeAchievable) {
                            motivationText = null;//ACTIVE_NOT_ON_TRACK_PLEDGE_ACHIEVABLE.get(r.nextInt(ACTIVE_NOT_ON_TRACK_PLEDGE_ACHIEVABLE.size()));
                            bgIllustation = "/image/mem-exp/xp/daily1x.png";
                            bgIllustationLottie = dailyLottie;
                        } else {
                            motivationText = null;//ACTIVE_PLEDGE_UNACHIEVABLE_OR_STREAK_IS_ZERO;
                            bgIllustation = "/image/mem-exp/xp/more-negative1x.png";
                            bgIllustationLottie = negativeLottie;
                        }
                    }
                    break;
                default:
                    return null;
            }

        }

        if(!showCoachMark) {
            editPreference = null;
        }

        if(!playAnimations) {
            overlayLottie = null;
            centerLottieBarContent = null;
            // Dont remove badge based on animations, it will be removed automatically if user does next class.
            //  badgeAllocation = null;
        }

        boolean allowOverrideContent = overrideAssetConfig != null ? (interfaces.getEnvironmentService().isAlpha() ? true: overrideAssetConfig.getEnableForProd()): false;
        List<String> userIdsList = getUserIds(interfaces, userId);
        userIdsList.add(userId);
        UserWeeklyActivityResponse.PledgeMeter pledgeMeter = isMembershipPaused ? null : (!isEmpty(userIdsList) && userIdsList.size()>1 && AppUtil.version75Check(userContext))  ?
                renderSquadWidget(interfaces, userContext, Math.min(actualActiveDays, maxPledgeThreshold - 1), actualPledgedDays, profilePictureUrl, userIdsList):
                new UserWeeklyActivityResponse.PledgeMeter(Math.min(actualActiveDays, maxPledgeThreshold - 1), actualPledgedDays, "most cult athletes", profilePictureUrl, "WORKOUTS",  "How consistent are you?",
                "On average, 78% of 'Cult' users engage in 3-5 activities per week.",
                "Exercise can help alleviate symptoms of depression, anxiety, and stress. It aids the release of endorphins, also known as \"feel-good\" hormones, which can improve mood and promote feelings of well-being. Regular exercise can also improve sleep, which in turn can positively affect your mental health.",
                "Results may vary based on factors such as the intensity and type of exercise, diet, starting fitness level, and more.",
                "/image/mem-exp/xp/meter.png",
                (allowOverrideContent && overrideAssetConfig.getPledgeAssetFileType() != null
                        && overrideAssetConfig.getPledgeAssetFileType().equalsIgnoreCase("GIF")
                        && overrideAssetConfig.getPledgeMeterGIFUrl() != null) ? overrideAssetConfig.getPledgeMeterGIFUrl() : ((overrideAssetConfig.getPledgeAssetFileType() != null
                        && overrideAssetConfig.getPledgeAssetFileType().equalsIgnoreCase("IMAGE")
                        && overrideAssetConfig.getPledgeMeterImgUrl() != null) ? overrideAssetConfig.getPledgeMeterImgUrl() : null),

                (allowOverrideContent && overrideAssetConfig.getPledgeAssetFileType() != null
                        && overrideAssetConfig.getPledgeAssetFileType().equalsIgnoreCase("LOTTIE")
                        && overrideAssetConfig.getPledgeMeterLottieUrl() != null) ? overrideAssetConfig.getPledgeMeterLottieUrl(): null,

                allowOverrideContent && overrideAssetConfig.getHidePledgeMeterLabel() != null ? !overrideAssetConfig.getHidePledgeMeterLabel() : true,

                allowOverrideContent && overrideAssetConfig.getHidePledgeMeterMessageGradient() != null ? !overrideAssetConfig.getHidePledgeMeterMessageGradient() : true,

                allowOverrideContent && overrideAssetConfig.getHidePledgeMeterPointers() != null ? !overrideAssetConfig.getHidePledgeMeterPointers() : true,

                allowOverrideContent && overrideAssetConfig.getHidePledgeMeterGrades() != null ? !overrideAssetConfig.getHidePledgeMeterGrades() : true, true, null, "");

        List<UserWeeklyActivityResponse.StreakData> streakData = null;
        if(userFitnessDetails.getTotalClassesAttended() > 0){
            UserWeeklyActivityResponse.StreakData classesAttended = new UserWeeklyActivityResponse.StreakData(userFitnessDetails.getTotalClassesAttended().toString(), "Workouts",
                 "/image/mem-exp/xp/workouts.png", false, null, null, null, null, null, null, null, darkShadeBg, null);

            UserWeeklyActivityResponse.StreakData weeklyStreak = new UserWeeklyActivityResponse.StreakData(
                    (actualActiveDays==0 && (int) userFitnessDetails.getWeeklyStreaks()==0? userFitnessDetails.getBestWeeklyStreak().toString() : userFitnessDetails.getWeeklyStreaks().toString()),
                    ((actualActiveDays==0 && (int) userFitnessDetails.getWeeklyStreaks()==0 ? "Best Weeks": (userFitnessDetails.getWeeklyStreaks().longValue() > 1 ? "Weeks Active" : "Week Active"))),
                 "/image/mem-exp/xp/streak.png", isWeeklyStreakAboutToBreak && (Integer.parseInt(userFitnessDetails.getWeeklyStreaks().toString()) > 0),
                    "Weeks Active", "Do at least one workout to stay consistent this week.", userFitnessDetails.getBestWeeklyStreak().toString(),
                    userFitnessDetails.getWeeklyStreaks().toString(), "Current", "Longest", "/image/mem-exp/xp/Streak-White.png", darkShadeBg, null);

            RewardPointsSummary rewardPointsSummary = interfaces.kudosService.getTotalPointsCount(userId);
            UserWeeklyActivityResponse.StreakData karmaPoints = new UserWeeklyActivityResponse.StreakData(rewardPointsSummary.getTotalPoints().toString(), "Karma pts",
                    "/image/mem-exp/xp/Karma_Selected.png", false, null, null, null, null, null, null, null, darkShadeBg, Action.builder().actionType(ActionType.NAVIGATION).url("curefit://karma_points").build());

            streakData = new ArrayList<>(Arrays.asList(classesAttended, weeklyStreak, karmaPoints));

        }
        UserWeeklyActivityResponse.SemiArcProgress semiArcProgress = isMembershipPaused ? null: new UserWeeklyActivityResponse.SemiArcProgress(reformedPledgedDays,
                reformedActiveDays, centerImgBarContent, centerLottieBarContent, centerTextBarContent, labelTextBarContent);

        String bgIllustationNetworkLottie = null;
        if(allowOverrideContent
                && ((overrideAssetConfig.getFileType().equalsIgnoreCase("LOTTIE") && overrideAssetConfig.getBgLottieAssetUrl() != null && !overrideAssetConfig.getBgLottieAssetUrl().isEmpty())
                    || (overrideAssetConfig.getFileType().equalsIgnoreCase("IMAGE") && overrideAssetConfig.getBgImgAssetUrl() != null && !overrideAssetConfig.getBgImgAssetUrl().isEmpty())
                    || (overrideAssetConfig.getFileType().equalsIgnoreCase("IMAGE") && overrideAssetConfig.getBgGIFAssetUrl() != null && !overrideAssetConfig.getBgGIFAssetUrl().isEmpty()))) {
            bgIllustationLottie = null;
            overlayLottie = null;
            bgIllustation =  (overrideAssetConfig.getFileType() != null
                    && overrideAssetConfig.getFileType().equalsIgnoreCase("IMAGE") && overrideAssetConfig.getBgImgAssetUrl() != null) ?
                    overrideAssetConfig.getBgImgAssetUrl() :
                    ((overrideAssetConfig.getFileType().equalsIgnoreCase("GIF") && overrideAssetConfig.getBgGIFAssetUrl() != null) ?
                        overrideAssetConfig.getBgGIFAssetUrl() : "");
            bgIllustationNetworkLottie = overrideAssetConfig.getFileType() != null
                    && overrideAssetConfig.getFileType().equalsIgnoreCase("LOTTIE") && overrideAssetConfig.getBgLottieAssetUrl() != null ?
                    overrideAssetConfig.getBgLottieAssetUrl() : "";
            motivationText = overrideAssetConfig.getTitle() != null && !overrideAssetConfig.getTitle().isEmpty() ? overrideAssetConfig.getTitle() : "";
        }

        if((currentDate.contains("2024-02-12") || currentDate.contains("2024-02-13") || currentDate.contains("2024-02-14"))) {
            overlayLottie = null;
            bgIllustation = "";
            bgIllustationLottie = null;
            if(reformedActiveDays == 0) {
                bgIllustationNetworkLottie = "/image/mem-exp/xp/Activity_1st.json";
                motivationText = "Start the week by setting your heart racing!";
            }
            else if(reformedActiveDays == 1) {
                bgIllustationNetworkLottie = "/image/mem-exp/xp/Activity_2nd.json";
                motivationText = "Lovely start! Each rep brings you closer to your goals!";
            }
            else if(reformedActiveDays == 2) {
                bgIllustationNetworkLottie = "/image/mem-exp/xp/Activity_3rd.json";
                motivationText = "Love is in the air! And you’re halfway there.";
            }
        }

        UserWeeklyActivityResponse.HabitBuildingGame habitBuildingGame = null;
        UserWeeklyActivityResponse.IntroDataHabitBuilding introDataHabitBuilding = null;
        try {
            EnergyStreakGame userGame = interfaces.userService.getUsersGame(userContext, null, interfaces);
            if (userGame != null) {
                // Habit Building game
                HabitGameProgressWidget habitGameProgressWidget = new HabitGameProgressWidget();
                WidgetContext newWidgetContext = new WidgetContext();
                Map<String,String> queryParams = new HashMap<>();
                if (userGame.getIsMultiPlayer() != null) queryParams.put("isMultiplayerGame",userGame.getIsMultiPlayer().toString());
                newWidgetContext.setQueryParams(queryParams);
                habitGameProgressWidget.buildView(interfaces, userContext, newWidgetContext);
                HabitBuildingData habitBuildingData;
                habitBuildingGame = habitGameProgressWidget.getHabitBuildingGame();
                habitBuildingData = habitGameProgressWidget.getHabitBuildingData();
                if(habitBuildingData != null && habitBuildingGame != null) {

                    int daysSinceLastSunday = fetchDaysSinceLastSunday();
                    Long workoutsRemaining = 3 - habitBuildingData.getWeekActiveDays() > 0 ? 3 - habitBuildingData.getWeekActiveDays() : 0;
                    Long daysLeft = (long) (7 - daysSinceLastSunday + 1);
                    Boolean isUnachievable = daysLeft < workoutsRemaining;

                    // change Bg Network lottie as per current status
                    if (habitGameProgressWidget.getDidWorkoutToday()) {
                        bgIllustationNetworkLottie = "/image/mem-exp/habit_building/bglotties/Active.json";
                    } else if (isUnachievable) {
                        bgIllustationNetworkLottie = "/image/mem-exp/habit_building/bglotties/Slipping.json";
                    } else if (habitBuildingGame.isSlipping) {
                        bgIllustationNetworkLottie = "/image/mem-exp/habit_building/bglotties/Slipping_Lottie.json";
                    } else  {
                        bgIllustationNetworkLottie = "/image/mem-exp/habit_building/bglotties/Normal.json";
                    }

                    // make plegdeMeter data as null for game to show up.
                    pledgeMeter = null;
                    motivationText = "";
                    if(!isMembershipPaused) {
                        bgIllustationLottie = null;
                    }
                    try {
                        introDataHabitBuilding = getIntroDataAfterChecking(interfaces, userContext);
                    } catch (Exception e) {
                        interfaces.exceptionReportingService.reportException(e);
                    }
                }
            }

            if(CultUtil.isPilatesOnlyMembership(interfaces.membershipService, userContext)) {
                bgIllustation = "/image/mem-exp/xp/daily2x.png";
                bgIllustationLottie = null;
                bgIllustationNetworkLottie = null;
            }

        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
        }

        UserWeeklyActivityResponse.UserActivityData userActivityData =
                new UserWeeklyActivityResponse.UserActivityData(
                        bgIllustation,
                        bgIllustationLottie,
                        overlayLottie,
                        playLottieOnce,
                        motivationText,
                        displayWeeklyNavigator,
                        pledgeMeter,
                        editPreference,
                        semiArcProgress,
                        fitnessMetric,
                        streakData,
                        badgeAllocation,
                        bgIllustationNetworkLottie, habitBuildingGame, introDataHabitBuilding,false, false,
                        !isEmpty(userIdsList) ? Action.builder().actionType(ActionType.NAVIGATION).url("curefit://squads_feed").build() : null
                );

        UserFitnessStatesWidgetResponse.setData(userActivityData);

        return Collections.singletonList(UserFitnessStatesWidgetResponse);
    }

    private List<String> getUserIds(ServiceInterfaces interfaces, String userId) {
        return interfaces.socialService.getAllUsersAcrossCommunitiesCached(userId);
    }

    private UserWeeklyActivityResponse.PledgeMeter renderSquadWidget(
            ServiceInterfaces serviceInterfaces, UserContext userContext, Integer activityIndex, Integer pledge, String profilePictureUrl, List<String> userIds
    ) {
        boolean allowOverrideContent = overrideAssetConfig != null ? (serviceInterfaces.getEnvironmentService().isAlpha() ? true: overrideAssetConfig.getEnableForProd()): false;
        //        int selfSessionCount = 0;
        try {
        Map<String, com.curefit.uas.responses.FitnessReportResponse> fitnessReportResponseMap = serviceInterfaces.uasFitnessReportClient.getBulkResponseForUserIdsAndWeekDate(CommunityUtil.getLastMonday(0), userIds).get();
        Map<String, UserEntry> userEntryMap = getUserEntryMap(userIds);
        List<ProgressIndicatorTile> progressIndicatorTiles = CommunityService.getSquadProgressTilesFromFitnessResponse(userContext, serviceInterfaces, userEntryMap, fitnessReportResponseMap, userIds);

            return new UserWeeklyActivityResponse.PledgeMeter(
                activityIndex, pledge, "", profilePictureUrl, "SQUAD LEADERBOARD",  "How consistent are you?",
                "On average, 78% of 'Cult' users engage in 3-5 activities per week.",
                "Exercise can help alleviate symptoms of depression, anxiety, and stress. It aids the release of endorphins, also known as \"feel-good\" hormones, which can improve mood and promote feelings of well-being. Regular exercise can also improve sleep, which in turn can positively affect your mental health.",
                "Results may vary based on factors such as the intensity and type of exercise, diet, starting fitness level, and more.",
                "/image/mem-exp/xp/meter.png",
                (allowOverrideContent && overrideAssetConfig.getPledgeAssetFileType() != null
                        && overrideAssetConfig.getPledgeAssetFileType().equalsIgnoreCase("GIF")
                        && overrideAssetConfig.getPledgeMeterGIFUrl() != null) ? overrideAssetConfig.getPledgeMeterGIFUrl() : (
                                (overrideAssetConfig.getPledgeAssetFileType() != null
                        && overrideAssetConfig.getPledgeAssetFileType().equalsIgnoreCase("IMAGE")
                        && overrideAssetConfig.getPledgeMeterImgUrl() != null) ? overrideAssetConfig.getPledgeMeterImgUrl() : null),
                (allowOverrideContent && overrideAssetConfig.getPledgeAssetFileType() != null
                        && overrideAssetConfig.getPledgeAssetFileType().equalsIgnoreCase("LOTTIE")
                        && overrideAssetConfig.getPledgeMeterLottieUrl() != null) ? overrideAssetConfig.getPledgeMeterLottieUrl(): null,
                allowOverrideContent && overrideAssetConfig.getHidePledgeMeterLabel() != null ? !overrideAssetConfig.getHidePledgeMeterLabel() : true,
                    false,
                allowOverrideContent && overrideAssetConfig.getHidePledgeMeterPointers() != null ? !overrideAssetConfig.getHidePledgeMeterPointers() : true,
                allowOverrideContent && overrideAssetConfig.getHidePledgeMeterGrades() != null ? !overrideAssetConfig.getHidePledgeMeterGrades() : true, false, progressIndicatorTiles, "YOU");
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException("Error in fetching bulk fitness report for users: ", e);
        }
        return null;
    }

    public static UserWeeklyActivityResponse.IntroDataHabitBuilding getIntroDataAfterChecking(ServiceInterfaces interfaces, UserContext userContext) throws ExecutionException, InterruptedException {
        String introKey = "intro_habit_building_state";
        String userId = userContext.getUserProfile().getUserId();
        String introKeyState = interfaces.featureStateCache.get(userId,
                introKey).get();
        if(introKeyState == null) {
            // set current time
            interfaces.featureStateCache.set(userId,
                    introKey, String.valueOf(System.currentTimeMillis())).get();
            return getIntroDataHabitBuilding(userContext);
        } else {
            long introKeyStateTimestamp = Long.parseLong(introKeyState);
            long currentTime = System.currentTimeMillis();
            long twentyFourHoursInMillis = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

            if (currentTime - introKeyStateTimestamp < twentyFourHoursInMillis) {
                // If 24 hours have not passed since the last check, show intro
                return getIntroDataHabitBuilding(userContext);
            } else {
                // Otherwise, don't show intro
                return null;
            }
        }

    }

    public static UserWeeklyActivityResponse.IntroDataHabitBuilding getIntroDataHabitBuilding(UserContext userContext) {
        UserWeeklyActivityResponse.IntroDataHabitBuilding introDataHabitBuilding = new UserWeeklyActivityResponse.IntroDataHabitBuilding();
        String imageUrl = userContext.getSessionInfo().getAppVersion() >= 10.52f ? "/image/mem-exp/habit_building/game_intro_2.png" : "image/mem-exp/habit_building/game_intro_heavy_padding.png";
        introDataHabitBuilding.setImageUrl(imageUrl);
        introDataHabitBuilding.setEntityId("intro_habit_building_state");

        long currentTime = System.currentTimeMillis();
        // Subtract 36 hours from the current time
        long thirtySixHoursAgo = currentTime - (36 * 60 * 60 * 1000);
        introDataHabitBuilding.setValueOfEntityToBeSet(String.valueOf(thirtySixHoursAgo));

        return introDataHabitBuilding;
    }

    public static Boolean checkNinjaInLastNDays(Long completionTimeMillis, int n){
        long currentTimeMillis = System.currentTimeMillis();
        long nDaysAgoMillis = currentTimeMillis - TimeUnit.DAYS.toMillis(n);
        return completionTimeMillis >= nDaysAgoMillis;
    }

    private static LocalDateTime getNextMonday() {
        LocalDateTime now = LocalDateTime.now();
        int daysUntilNextMonday = DayOfWeek.MONDAY.getValue() - now.getDayOfWeek().getValue();
        if (daysUntilNextMonday <= 0) {
            daysUntilNextMonday += 7; // If today is Monday, add 7 days to get next Monday
        }
        return now.plusDays(daysUntilNextMonday).withHour(23).withMinute(59).withSecond(59);
    }

    public String getLastCheckinId(KickstarterPlanDetailsResponse userFitnessDetails) {
        String lastAttendedClassStartDateTimeUTC = userFitnessDetails.getLastAttendedClassStartDateTimeUTC();
        String lastGymCheckinDateTimeUTC = userFitnessDetails.getLastGymCheckinDateTimeUTC();
        if (lastAttendedClassStartDateTimeUTC == null && lastGymCheckinDateTimeUTC == null) {
            return null;
        }

        if (lastAttendedClassStartDateTimeUTC != null && lastGymCheckinDateTimeUTC != null) {
            LocalDateTime lastAttendedDateTime = LocalDateTime.parse(lastAttendedClassStartDateTimeUTC, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime lastGymCheckinDateTime = LocalDateTime.parse(lastGymCheckinDateTimeUTC, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // Compare timestamps and return the appropriate ID
            if (lastAttendedDateTime.isAfter(lastGymCheckinDateTime)) {
                return userFitnessDetails.getLastAttendedBookingId().toString();
            } else {
                return userFitnessDetails.getLastGymCheckinId();
            }
        } else if (lastAttendedClassStartDateTimeUTC != null) {
            return userFitnessDetails.getLastAttendedBookingId().toString();
        } else {
            return userFitnessDetails.getLastGymCheckinId();
        }
    }

    public Long getCompletedDaysforHabitGame(Long completedTime) {
        Long epochTime = completedTime; // Example epoch time in milliseconds

        // Get current epoch time
        Long currentTimeMillis = System.currentTimeMillis();

        Instant epochInstant = Instant.ofEpochMilli(epochTime);
        Instant currentInstant = Instant.ofEpochMilli(currentTimeMillis);

        // Calculate difference in days
        Long daysDifference = Duration.between(epochInstant, currentInstant).toDays();

        return daysDifference;

    }

    public static  int fetchDaysSinceLastSunday() {

        LocalDate today = LocalDate.now();
        DayOfWeek currentDayOfWeek = today.getDayOfWeek();

        // Calculate days since last Sunday
        int daysSinceLastSunday = currentDayOfWeek.getValue() - DayOfWeek.SUNDAY.getValue();

        if (daysSinceLastSunday <= 0) {
            // If it's Sunday, then 0 days have passed since last Sunday
            daysSinceLastSunday += 7;
        }
        return daysSinceLastSunday;
    }


    public static CompletableFuture<List<Object>> futureAllOf(CompletableFuture<?>... futures) {
        return CompletableFuture.allOf(futures)
                .thenApply(x -> Arrays.stream(futures)
                        .map(f -> (Object) f.join())
                        .collect(toList())
                );
    }

    public Map<String, Object> getLatestBadge(UserContext userContext, ServiceInterfaces interfaces, Integer daysThreshold) throws ExecutionException, InterruptedException {
        String userId = userContext.getUserProfile().getUserId();
        Debugger debugger = Debugger.getDebuggerFromUserContext(userContext);

        List<QuestBadge> questBadges = interfaces.questClient.getBadgesForUser(userId).get();
        debugger.msg("questBadges", questBadges);

        List<Badge> cultBadges = questBadges.stream()
            .filter(item -> item.getVertical().equals(Vertical.CULT))
            .flatMap(qb -> qb.getBadges().stream())
            .map(BadgeWrapper::getBadge).toList();
        debugger.msg("cultBadges", cultBadges);

        List<UserBadge> badges = Optional.ofNullable(
                interfaces.questClient.getUserActivitySummary(userId).get()
        ).map(UserActivitySummary::getBadges).orElse(Collections.emptyList());
        debugger.msg("badges", badges);

        if (badges.isEmpty()) return null;

        Set<String> cultBadgeIds = cultBadges.stream().map(Badge::getBadgeId).collect(Collectors.toSet());
        debugger.msg("cultBadgeIds", cultBadgeIds);

        badges = new ArrayList<>(badges);
        badges.removeIf(item -> !cultBadgeIds.contains(item.getBadgeId()));

        if (badges.isEmpty()) return null;

        badges.sort(Comparator.comparing(UserBadge::getAwardedAt, Comparator.nullsLast(Date::compareTo)).reversed());

        debugger.msg("sortedBadges", badges);
        UserBadge latestBadge = badges.getFirst();
        debugger.msg("latestBadge", latestBadge);

        if (daysThreshold != null && Math.abs(TimeUtil.getDaysInBetween(latestBadge.getAwardedAt(), new Date(), userContext.getUserProfile().getTimezone())) > daysThreshold) {
            debugger.msg("Threshold reached");
            return null;
        }

        Badge badge = cultBadges.stream()
            .filter(b -> b.getBadgeId().equals(latestBadge.getBadgeId()))
            .findFirst()
            .orElse(null);

        Map<String, Object> mp = new HashMap<>();
        mp.put("badge", badge);
        mp.put("bookingId", latestBadge.getLastActivityId());
        return mp;
    }

    public Badge findBadge(String cultUserId, String bookingId, ServiceInterfaces interfaces) {
        List<QuestBadge> questBadges = null;
        
        CompletableFuture<UserActivitySummary> userActivitySummaryCompletableFuture = interfaces.questClient.getUserActivitySummary(cultUserId);

        CompletableFuture<List<QuestBadge>> questBadgesCompletableFuture = interfaces.questClient.getBadgesForUser(cultUserId);

        try {
            questBadges = questBadgesCompletableFuture.get();
        } catch (ExecutionException | InterruptedException e) {
            log.error("QUEST SERVICE::Error in getting user badges for userId: {} is {}", cultUserId, e.getMessage());
            return null;
        }
        
        UserActivitySummary userActivitySummary = null;
        try {
            userActivitySummary = userActivitySummaryCompletableFuture.get();
        } catch(InterruptedException | ExecutionException e) {
            log.error("QUEST SERVICE::Error in getting user activity summary for userId: {} is {}", cultUserId, e.getMessage());
            return null;
        }
        
        if (userActivitySummary == null || questBadges == null || questBadges.isEmpty()) return null;

        List<UserBadge> badges = userActivitySummary.getBadges();
        for(UserBadge badge: badges) {
            if ( badge.getLastActivityId() != null && badge.getLastActivityId().equals(bookingId)) {
                for (QuestBadge questBadge: questBadges) {
                    if (questBadge.getVertical().equals(Vertical.CULT)) {
                        for (BadgeWrapper badgeWrapper: questBadge.getBadges()) {
                            if (badgeWrapper.getBadge().getBadgeId().equals(badge.getBadgeId())) {
                                return badgeWrapper.getBadge();
                            }
                        }
                    }

                }
            }
        }
        return null;
    }

    public static String getPauseMotivationalText(String pauseReason, Integer pauseDaysLeft) {
        String mtcText = "";
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        int weekNumber = calendar.get(Calendar.WEEK_OF_YEAR);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        long baseSeed = 12345L;
        long seed = baseSeed + weekNumber * 1000 + dayOfWeek;
        Random r = new Random(seed);
        Map<String, List<String>> reasonMotivationA = new HashMap<>() {{
            this.put("I am travelling out of town", Arrays.asList("Enjoy your travel, don't forget to take care of your health"));
            this.put("I am injured", Arrays.asList("Wishing you a quick recovery and sending lots of love your way"));
            this.put("I am unwell", Arrays.asList("Take care of yourself first, your health is more important than any workout"));
            this.put("Work is keeping me busy", Arrays.asList("Remember, your health is your wealth. Don't let work steal your workout",
                    "Don't let work win. Push through the busy days and make time for your workout. Your body will thank you."));
            this.put("I have personal commitments", Arrays.asList("Prioritize health by sticking to workout routine, even if personal commitments arise"));
            this.put("Others", Arrays.asList("Remember, even small progress is still progress. Take a break if you need it, and come back stronger tomorrow"));
        }};
        Map<String, List<String>> reasonMotivationB = new HashMap<>() {{
            this.put("I am travelling out of town", Arrays.asList("Welcome back, fitness lover! Let's pick up where we left off and keep crushing those goals!"));
            this.put("I am injured", Arrays.asList("Bounce back from your injury and conquer your fitness journey with renewed strength.",
                    "Begin anew with a joyful recovery and take steady steps towards your fitness goals."));
            this.put("I am unwell", Arrays.asList("We hope you recovered well, its time to conquer your fitness journey with renewed strength"));
            this.put("Work is keeping me busy", Arrays.asList("Hey workaholic, it's time to give your wrokout some attention!",
                    "Remember, your health is your wealth. Don't let work steal your workout"));
            this.put("I have personal commitments", Arrays.asList("Personal commitment conquered! Now, redirect that focus to your fitness journey and unleash your full potential."));
            this.put("Others", Arrays.asList("Break time's over! Time to refocus on your fitness regimen and get back on track towards your goals."));
        }};

        if(pauseDaysLeft > 2) {
            List<String> reasons = reasonMotivationA.get(pauseReason);
            if(reasons != null)
                mtcText = reasons.get(r.nextInt(reasons.size()));
        }
        else {
            List<String> reasons = reasonMotivationB.get(pauseReason);
            if(reasons != null)
                mtcText = reasons.get(r.nextInt(reasons.size()));
        }
        return mtcText;
    }

    public static String getClassCancelMotivationalText(String cancelReason) {
        String mtcText = "";
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        int weekNumber = calendar.get(Calendar.WEEK_OF_YEAR);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        long baseSeed = 12345L;
        long seed = baseSeed + weekNumber * 1000 + dayOfWeek;
        Random r = new Random(seed);
        Map<String, List<String>> reasonMotivation = new HashMap<>() {{
            this.put("Busy with work", Arrays.asList("Don't let work win. Push through the busy days and make time for your workout. Your body will thank you."));
            this.put("Feeling unwell", Arrays.asList("Take care of yourself first, your health is more important than any workout"));
            this.put("Not recovered from last workout", Arrays.asList("If you feel overwhelmed or exhausted, it's okay to take a break and recharge."));
            this.put("Personal commitments", Arrays.asList("Remember, even small progress is still progress. Take a break if you need it, and come back stronger tomorrow"));
            this.put("Others", Arrays.asList("Remember, even small progress is still progress. Take a break if you need it, and come back stronger tomorrow"));
        }};
        List<String> reasons = reasonMotivation.get(cancelReason);
        if(reasons != null)
            mtcText = reasons.get(r.nextInt(reasons.size()));

        return mtcText;
    }

    public enum CustomDayOfWeek {
        MONDAY(1),
        TUESDAY(2),
        WEDNESDAY(3),
        THURSDAY(4),
        FRIDAY(5),
        SATURDAY(6),
        SUNDAY(7);

        private final int value;

        private CustomDayOfWeek(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public static String convertUtcToIst(String utcDateTimeString) {
        SimpleDateFormat utcFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        utcFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

        SimpleDateFormat istFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        istFormat.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"));

        try {
            Date utcDateTime = utcFormat.parse(utcDateTimeString);
            return istFormat.format(utcDateTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return null;
    }
    /**
     * Gets the class value at a specific relative index from the attended class history list.
     *
     * @param history List of attended class history
     * @param relativeIndex Relative index (-2 for second from last, -3 for third from last, etc.)
     * @return The class value at the specified index, or 0 if not available
     */
    public static Integer getClassValueAtIndex(List<AttendedClassHistory> history, int relativeIndex) {
        if (history == null || history.isEmpty()) return 0;

        int actualIndex = history.size() + relativeIndex;
        if (actualIndex < 0 || actualIndex >= history.size()) return 0;

        AttendedClassHistory entry = history.get(actualIndex);
        return entry != null ? entry.getValue().intValue() : 0;
    }
}