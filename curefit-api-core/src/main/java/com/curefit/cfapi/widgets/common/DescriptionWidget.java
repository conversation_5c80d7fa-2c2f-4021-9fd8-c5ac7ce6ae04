package com.curefit.cfapi.widgets.common;

import com.curefit.cfapi.model.internal.common.Description;
import com.curefit.cfapi.pojo.view.ContainerStyle;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

import static com.curefit.cfapi.pojo.vm.widget.WidgetType.DESCRIPTION_WIDGET;

/**
 * There is similar a DESCRIPTION_WIDGET in node variant of CF-API
 * Please extend this widget's functionality as per usage and keep it consistent with node variant
 * This widget can be used to render a list of text to provide textual information of something
 */

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DescriptionWidget extends BaseWidgetNonVM {

    String title;
    Boolean hasDividerBelow;
    List<String> items;
    List<Description> descriptions;
    ContainerStyle rowStyle;
    ContainerStyle containerStyle;

    public DescriptionWidget() {
        super(DESCRIPTION_WIDGET);
        descriptions = new ArrayList<>();
    }

    public void addItem(Description description) {
        descriptions.add(description);
    }
}
