package com.curefit.cfapi.widgets.chroniccare;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SLHomeSugarLoggingWidget extends BaseWidgetNonVM {
    String title;
    String description;
    Double sugarValue;
    String unit = "mg/dL";
    Action action;

    public SLHomeSugarLoggingWidget() {
        super(WidgetType.SF_LITE_HOME_SUGAR_LOG_WIDGET);
    }
}
