package com.curefit.cfapi.widgets.fitness;
import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.vm.atom.Alignment;
import com.curefit.cfapi.pojo.vm.atom.CFContainerData;
import com.curefit.cfapi.pojo.vm.atom.CFImageData;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.Debugger;
import com.curefit.cfapi.util.StringUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.curefit.cfapi.service.UserStreakService.RASHI_KEY_FOR_USER_STREAK_ONBOARDING;

@Getter
@Setter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class StringListWidget extends BaseWidget {
    List<CFTextData> stringList;
    List<Integer> paddingInBetween; // size should be stringList.length + 1, else default it takes 0 padding
    Alignment crossAlignment;
    Alignment mainAlignment;
    Double horizontalPadding;
    Double widthRatio;
    CFImageData cardBackground;
    Debugger debugger;
    CFContainerData containerData;
    CFImageData prefixImage;
    Alignment graphicsAlignment;
    CFImageData suffixImage;

    public StringListWidget() {
        this.widgetType = WidgetType.STRING_LIST_WIDGET;
    }

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {

        debugger = Debugger.getDebuggerFromUserContext(userContext);
        debugger.msg(this);

        if (Objects.nonNull(stringList)) {
            stringList = stringList.stream().filter(item -> Objects.nonNull(item.getText())).collect(Collectors.toList());
            stringList.forEach(item -> {
                if (Objects.nonNull(item.getGradient()) && CollectionUtils.isEmpty(item.getGradient().getColors())) {
                    item.setGradient(null);
                }
                if (Objects.nonNull(item.getPrefixIcon()) && (Objects.isNull(item.getPrefixIcon().getCode()) || item.getPrefixIcon().getCode().length() < 2)) {
                    item.setPrefixIcon(null);
                }
                if (Objects.nonNull(item.getSuffixIcon()) && (Objects.isNull(item.getSuffixIcon().getCode()) || item.getSuffixIcon().getCode().length() < 2)) {
                    item.setSuffixIcon(null);
                }
                if (Objects.nonNull(item.getPrefixImage()) && (Objects.isNull(item.getPrefixImage().getUrl()) || item.getPrefixImage().getUrl().length() < 2)) {
                    item.setPrefixImage(null);
                }
                if (Objects.nonNull(item.getSuffixImage()) && (Objects.isNull(item.getSuffixImage().getUrl()) || item.getSuffixImage().getUrl().length() < 2)) {
                    item.setSuffixImage(null);
                }
                if(Objects.nonNull(item.getText())){
                    item.setText(StringUtil.replaceTemplateVariables(item.getText(), userContext, interfaces));
                }

                if (Objects.nonNull(cardBackground) && Objects.isNull(cardBackground.getUrl())) {
                    cardBackground = null;
                }
            });
        } else return new ArrayList<>();

        if (Objects.isNull(paddingInBetween)) {
            paddingInBetween = new ArrayList<>();
        }

        while (paddingInBetween.size() <= stringList.size()) paddingInBetween.add(0);
        while (paddingInBetween.size() > stringList.size() + 1) paddingInBetween.removeLast();
        setLayoutProps(null);
        debugger.msg(this);

        return Collections.singletonList(this);
    }

}
