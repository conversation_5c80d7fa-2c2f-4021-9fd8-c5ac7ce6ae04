package com.curefit.cfapi.widgets.fitness;

import com.curefit.catalogv1.pms.PMSDataMapper;
import com.curefit.catalogv1.services.augment.PMSAugments;
import com.curefit.center.dtos.CenterEntry;
import com.curefit.cfapi.model.internal.cult.CultPackBrowseProductType;
import com.curefit.cfapi.model.internal.cult.PackInfoCategoryItem;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.vm.atom.CFImageData;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.pojo.widgets.SkuPack;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.Debugger;
import com.curefit.cfapi.util.GymUtil;
import com.curefit.cfapi.util.PackInfoListUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.pms.pojo.customPacks.augments.AugmentedOfflineFitnessPack;
import com.curefit.product.enums.ProductType;
import com.curefit.product.models.cult.FitnessPack;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;


import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.curefit.cfapi.util.PackInfoListUtil.*;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@ToString
public class PackInfoListWidget extends BaseWidget {

    public PackInfoListWidget() {
        this.widgetType = WidgetType.PACK_INFO_CARD_LIST_WIDGET;
    }

    List<PackInfoCategoryItem> packInfoCategoryItems;
    Boolean showSkuCard;
    CultPackBrowseProductType selectedKey;
    List<SkuPack> skuPackTabList;
    Map<String, List<com.curefit.cfapi.pojo.vm.widget.BaseWidget>> packInfoCardList;
    List<com.curefit.cfapi.pojo.vm.widget.BaseWidget> topWidget;
    List<com.curefit.cfapi.pojo.vm.widget.BaseWidget> bottomWidget;
    List<String> topWidgetList;
    List<String> bottomWidgetList;
    Boolean showLocation;
    Boolean showHeaderSingle;
    CFTextData location;
    CFImageData tabCardBackground;
    Debugger debugger;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {

        debugger = Debugger.getDebuggerFromUserContext(userContext);

        PackInfoListUtil.widgetMapping = new HashMap<>();
        skuPackTabList = new ArrayList<>();
        packInfoCardList = new HashMap<>();

        debugger.msg("this", this);
        debugger.msg("topWidgetList", topWidgetList);
        debugger.msg("bottomWidgetList", bottomWidgetList);

        String centerName = null;
        String cityName = userContext.getUserProfile().getCity().getName();
        com.curefit.cfapi.pojo.vm.widget.BaseWidget headerWidget = null;

        topWidget = PackInfoListUtil.getWidgetList(interfaces, userContext, topWidgetList);
        bottomWidget = PackInfoListUtil.getWidgetList(interfaces, userContext, bottomWidgetList);

        for (PackInfoCategoryItem item: packInfoCategoryItems) {
            PackInfoListUtil.PackInfoData data = null;
            PackInfoListUtil.PackInfoData data2 = null;
            PackInfoListUtil.PackInfoData data3 = null;
            debugger.msg("item", item);
            debugger.msg("getProductType", item.getProductType());
            switch (item.getProductType()) {
                case CULT_UNLIMITED -> {
                    headerWidget = PackInfoListUtil.getHeaderWidget(null, userContext.getUserProfile().getCity().getName(), item.getProductType());
                    debugger.msg("headerWidget", headerWidget);
                    List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                        ProductType.FITNESS, ProductSubType.GENERAL, !Objects.nonNull(item.getShowPrivatePacks()) || !item.getShowPrivatePacks(),
                        userContext, null
                    );
                    debugger.msg("packs", packs);
                    data = getPackInfoData(item, userContext, interfaces, packs, null);
                }
                case GYM_PACK -> {
                    headerWidget = PackInfoListUtil.getHeaderWidget(null, userContext.getUserProfile().getCity().getName(), item.getProductType());
                    debugger.msg("headerWidget", headerWidget);
                    List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                        ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.GENERAL, !Objects.nonNull(item.getShowPrivatePacks()) || !item.getShowPrivatePacks(),
                        userContext, null
                    );
                    debugger.msg("packs", packs);
                    data = getPackInfoData(item, userContext, interfaces, packs, null);
                }
                case PRO_PLUS -> {
                    List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                        ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.PLUS, !Objects.nonNull(item.getShowPrivatePacks()) || !item.getShowPrivatePacks(),
                        userContext, null
                    );
                    debugger.msg("packs", packs);
                    data = getPackInfoData(item, userContext, interfaces, packs, null);
                }
                case ELITE_PLUS -> {
                    List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                        ProductType.FITNESS, ProductSubType.PLUS, !Objects.nonNull(item.getShowPrivatePacks()) || !item.getShowPrivatePacks(),
                        userContext, null
                    );
                    debugger.msg("packs", packs);
                    data = getPackInfoData(item, userContext, interfaces, packs, null);
                }
                case ELITE_LITE -> {
                    List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                        ProductType.FITNESS, ProductSubType.LITE, !Objects.nonNull(item.getShowPrivatePacks()) || !item.getShowPrivatePacks(),
                        userContext, null
                    );
                    debugger.msg("packs", packs);
                    data = getPackInfoData(item, userContext, interfaces, packs, null);
                }
                case ELITE_SELECT -> {
                    Integer centerServiceId = getCenterServiceIdFromWidgetContext(widgetContext);
                    debugger.msg("centerServiceId", centerServiceId);
                    if (centerServiceId != null) {
                        CenterEntry center = interfaces.centerService.getCenterDetails(Long.valueOf(centerServiceId), true, null, null).get();
                        centerName = center.getName();
                        headerWidget = PackInfoListUtil.getHeaderWidget(center, userContext.getUserProfile().getCity().getName(), item.getProductType());
                        debugger.msg("headerWidget", headerWidget);
                        List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                            ProductType.FITNESS, ProductSubType.GENERAL, !Objects.nonNull(item.getShowPrivatePacks()) || !item.getShowPrivatePacks(),
                            userContext, centerServiceId
                        );
                        debugger.msg("packs", packs);
                        data = getPackInfoData(item, userContext, interfaces, packs, String.valueOf(centerServiceId));
                    }
                }
                case PRO_SELECT -> {
                    Integer centerServiceId = getCenterServiceIdFromWidgetContext(widgetContext);
                    debugger.msg("centerServiceId", centerServiceId);
                    if (centerServiceId != null) {
                        CenterEntry center = interfaces.centerService.getCenterDetails(Long.valueOf(centerServiceId), true, null, null).get();
                        centerName = center.getName();
                        headerWidget = PackInfoListUtil.getHeaderWidget(center, userContext.getUserProfile().getCity().getName(), item.getProductType());
                        debugger.msg("headerWidget", headerWidget);
                        List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                            ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.GENERAL, !Objects.nonNull(item.getShowPrivatePacks()) || !item.getShowPrivatePacks(),
                            userContext, centerServiceId
                        );
                        debugger.msg(packs);
                        data = getPackInfoData(item, userContext, interfaces, packs, String.valueOf(centerServiceId));
                    }
                }
                case SELECT_LITE -> {
                    Integer centerServiceId = getCenterServiceIdFromWidgetContext(widgetContext);
                    debugger.msg("centerServiceId", centerServiceId);
                    if (centerServiceId != null) {
                        CenterEntry center = interfaces.centerService.getCenterDetails(Long.valueOf(centerServiceId), true, null, null).get();
                        centerName = center.getName();
                        headerWidget = PackInfoListUtil.getHeaderWidget(center, userContext.getUserProfile().getCity().getName(), item.getProductType());
                        debugger.msg("headerWidget", headerWidget);
                        ProductType productType = ProductType.FITNESS;
                        if (GymUtil.isProGym(center)) productType = ProductType.GYMFIT_FITNESS_PRODUCT;
                        debugger.msg("isProGym", GymUtil.isProGym(center));
                        List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                            productType, ProductSubType.LITE, !Objects.nonNull(item.getShowPrivatePacks()) || !item.getShowPrivatePacks(),
                            userContext, centerServiceId
                        );
                        debugger.msg("packs", packs);
                        data = getPackInfoData(item, userContext, interfaces, packs, String.valueOf(centerServiceId));
                    }
                }
                case LUX_PACK -> {
                    Integer centerServiceId = getCenterServiceIdFromWidgetContext(widgetContext);
                    String dividePack = getParamsFromWidgetContext(widgetContext, "dividePack");
                    debugger.msg("centerServiceId", centerServiceId);
                    if (centerServiceId != null) {
                        CenterEntry center = interfaces.centerService.getCenterDetails(Long.valueOf(centerServiceId), true, null, null).get();
                        debugger.msg("isLuxGym", GymUtil.isLuxGym(center));
                        if (GymUtil.isLuxGym(center)) {
                            centerName = center.getName();
                            List<AugmentedOfflineFitnessPack> packs = userContext.getRequestCache().getAugmentedPackList(
                                ProductType.LUX_FITNESS_PRODUCT, null, !Objects.nonNull(item.getShowPrivatePacks()) || !item.getShowPrivatePacks(),
                                userContext, centerServiceId
                            );
                            debugger.msg("packs", packs);
                            if (Objects.nonNull(dividePack) && dividePack.equals("true")) {
                                List<AugmentedOfflineFitnessPack> normalLuxPacks = packs.stream().filter(pack -> (!SkuPackUtil.isLuxElitePack(pack) && !SkuPackUtil.isLuxPilatesPack(pack))).toList();
                                if (!normalLuxPacks.isEmpty()) {
                                    data = getPackInfoData(item, userContext, interfaces, normalLuxPacks, String.valueOf(centerServiceId));
                                }
                            } else {
                                data = getPackInfoData(item, userContext, interfaces, packs, String.valueOf(centerServiceId));
                            }
                            if (Objects.nonNull(dividePack) && dividePack.equals("true")) {
                                List<AugmentedOfflineFitnessPack> lux_elite_packs = packs.stream().filter(SkuPackUtil::isLuxElitePack).toList();
                                List<AugmentedOfflineFitnessPack> lux_pilates_packs = packs.stream().filter(SkuPackUtil::isLuxPilatesPack).toList();
                                if (!lux_elite_packs.isEmpty()) {
                                    item.setProductType(CultPackBrowseProductType.LUX_ELITE);
                                    data2 = getPackInfoData(item, userContext, interfaces, lux_elite_packs, String.valueOf(centerServiceId));
                                }
                                if (!lux_pilates_packs.isEmpty()) {
                                    item.setProductType(CultPackBrowseProductType.LUX_PILATES);
                                    data3 = getPackInfoData(item, userContext, interfaces, lux_pilates_packs, String.valueOf(centerServiceId));
                                }
                            }
                        }
                    }
                }
                case PLAY -> {
                    headerWidget = PackInfoListUtil.getHeaderWidget(null, userContext.getUserProfile().getCity().getName(), item.getProductType());
                    debugger.msg("headerWidget", headerWidget);
                    List<FitnessPack> packList = interfaces.catalogueService.getNewPlayPacksByCity(userContext.getUserProfile().getCity().getCityId(), userContext.getUserProfile().getUserId());
                    packList = packList.stream().filter(pack -> pack.getVisibility() != null && pack.getVisibility().contains("app")).collect(Collectors.toList());
                    data = getPlayPackInfoData(item, userContext, interfaces, packList, null);
                }
            }
            setPackInfoListWidgetData(data, skuPackTabList, packInfoCardList, item);
            setPackInfoListWidgetData(data2, skuPackTabList, packInfoCardList, item);
            setPackInfoListWidgetData(data3, skuPackTabList, packInfoCardList, item);
            debugger.msg(data);
        }

        boolean showingHeader = false;
        if (
            Boolean.TRUE.equals(showHeaderSingle) &&
            Objects.nonNull(headerWidget) &&
            Objects.nonNull(packInfoCardList) &&
            packInfoCardList.size() == 1
        ) {
            debugger.msg("condition satisfied for header widget");
            topWidget.add(headerWidget);
            showingHeader = true;
        } else {
            String hw = (Objects.nonNull(headerWidget)) ? headerWidget.toString() : "null";
            String picw = (Objects.nonNull(packInfoCardList)) ? packInfoCardList.toString() : "null";
            String picwLen = (Objects.nonNull(packInfoCardList)) ? String.valueOf(packInfoCardList.size()) : "null";
            debugger.msg("condition failed for header widget", "header Widget: " + hw + ", picw size: " + picwLen + ", packInfoCardWidget: " + picw);
        }

        debugger.msg(showLocation);
        debugger.msg(centerName);
        if (!showingHeader && Boolean.TRUE.equals(showLocation)) {
            if (Objects.nonNull(centerName)) {
                location = PackInfoListUtil.getLocationWidget(centerName);
            } else {
                location = PackInfoListUtil.getLocationWidget("Across " + cityName);
            }
        }

        if (
            Boolean.FALSE.equals(showSkuCard) &&
            CollectionUtils.isNotEmpty(skuPackTabList) &&
            skuPackTabList.size() == 1
        ) {
            skuPackTabList = null;
        } else if (Boolean.FALSE.equals(showSkuCard)) {
            debugger.msg("showing the sku tab list as skuPackTabList: " + skuPackTabList);
        }

        this.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("45", "0"));

        if (Objects.nonNull(tabCardBackground) && Objects.isNull(tabCardBackground.getUrl())) {
            tabCardBackground = null;
        }

        // extra info for response
        packInfoCategoryItems = null;
        showSkuCard = null;
        showLocation = null;
        debugger = null;
        PackInfoListUtil.debugger = null;
        topWidgetList = null;
        bottomWidgetList = null;
        showHeaderSingle = null;

        return Collections.singletonList(this);

    }

}
