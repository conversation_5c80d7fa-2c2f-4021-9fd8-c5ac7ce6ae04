package com.curefit.cfapi.widgets.chroniccare.sfliteapp;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;

import static com.curefit.cfapi.pojo.vm.widget.WidgetType.WEBINAR_CARD_WIDGET;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WebinarCardWidget  extends BaseWidgetNonVM {
    String title;
    String boldTitle;
    WebinarCard webinarCard;
    public WebinarCardWidget() {
        super(WEBINAR_CARD_WIDGET);
    }

    @Getter
    @Setter
    @RequiredArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class WebinarCard {
        boolean isWebinarBooked;
        Date webinarStartTime;
        Integer webinarDurationInMins;
        String webinarTitle;
        String image;
        String hostName;
        Action primaryAction;
        String webinarId;
        Long webinarSessionId;
    }
}
