package com.curefit.cfapi.widgets.chroniccare;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.sugarfit.chs.pojo.glucometerReadings.GlucometerReadingEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SLSugarLogListWidget extends BaseWidgetNonVM {
    String description;
    long startTime;
    long endTime;
    String selectedSlot;
    String selectedType;
    List<GlucometerReadingEntry> data;
    Action editAction;
    String title;
    NoDataCard noDataCard;
    Action citationAction = Action.builder().actionType(ActionType.OPEN_WEBPAGE).url("https://www.sugarfit.com/blog/normal-blood-sugar-levels?plainWebPage=true").title("Know about healthy ranges").build();

    public SLSugarLogListWidget() {
        super(WidgetType.SF_LITE_SUGAR_LOG_LIST_WIDGET);
    }
    @Getter
    @Setter
    @RequiredArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class NoDataCard {
        String topTitle = "You have not tracked any glucose data for the month. Log your sugar levels and get insights about your glucose!";
        Action action = Action.builder().url("sfcommunity://sugarlogging").actionType(ActionType.NAVIGATION).title("LOG SUGAR LEVEL").build();
    }
}
