package com.curefit.cfapi.widgets.common;

import com.curefit.cfapi.model.internal.cult.CultPackBrowseProductType;
import com.curefit.cfapi.model.internal.cult.SkuMediumThumbnailCardItem;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.PlayUtil;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.fitness.FitnessPackBrowseWidget;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkuMediumThumbnailCardWidget extends BaseWidget {

    Header header;
    BannerDetail backgroundBanner;
    List<SkuMediumThumbnailCardItem> items;

    @Override
    public List<BaseWidget> buildView(
        ServiceInterfaces interfaces,
        UserContext userContext,
        WidgetContext widgetContext
    ) throws Exception {

        for (SkuMediumThumbnailCardItem item : this.items) {

            if (item.getTitle() != null && !item.getTitle().isEmpty()) {
                String title = item.getTitle();
                if (PlayUtil.isUserBelongToRenewPlayMembership(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext))
                    title = "Renew CultPass Play";
                item.setCfTitle(new CFTextData(title,
                        UiUtils.UIColors.COLOR_WHITE,
                        "2",
                        UiUtils.TextAlignment.LEFT,
                        UiUtils.TextTypeScales.P8
                ));
            }

            if (item.getSubTitle() != null && !item.getSubTitle().isEmpty())
                item.setCfSubTitle(new CFTextData(item.getSubTitle(),
                        UiUtils.UIColors.COLOR_WHITE,
                        "2",
                        UiUtils.TextAlignment.LEFT,
                        UiUtils.TextTypeScales.H1
                ));

            SkuMediumThumbnailCardItem.Footer footer = item.getFooter();

            if (footer.getTitle() != null)
                footer.setCfTitle(new CFTextData(footer.getTitle(),
                        UiUtils.UIColors.COLOR_WHITE,
                        "2",
                        UiUtils.TextAlignment.LEFT,
                        UiUtils.TextTypeScales.P3
                ));

            if (footer.getSubTitle() != null)
                footer.setCfSubTitle(new CFTextData(footer.getSubTitle(),
                        UiUtils.UIColors.COLOR_WHITE,
                        "2",
                        UiUtils.TextAlignment.LEFT,
                        UiUtils.TextTypeScales.P3
                ));


            if (
                item.getProductType() == CultPackBrowseProductType.GYM_PACK ||
                item.getProductType() == CultPackBrowseProductType.CULT_UNLIMITED
            ){
                boolean isUserBelongShowEliteSkuMinPriceSegment = AppUtil.doesUserBelongShowEliteSkuMinPriceSegment(
                        interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
                FitnessPackBrowseWidget packs = new FitnessPackBrowseWidget();
                HashMap<String, Object> minPrice = null;
                if (item.getProductType() == CultPackBrowseProductType.CULT_UNLIMITED || isUserBelongShowEliteSkuMinPriceSegment) {
                    minPrice = packs.getElitePackItemDetails(userContext, interfaces, userContext.getUserProfile().getCity().getCityId(), userContext.getUserProfile().getCity().getCultCityId());
                } else if (item.getProductType() == CultPackBrowseProductType.GYM_PACK) {
                    minPrice = packs.getProPackItemDetails(userContext, interfaces, userContext.getUserProfile().getCity().getCityId());
                }
                if (Objects.nonNull(minPrice)) {
                    String proPackMinPrice = minPrice.get("minPrice").toString();
                    footer.setCfTitle(new CFTextData(proPackMinPrice,
                            UiUtils.UIColors.COLOR_WHITE,
                            "2",
                            UiUtils.TextAlignment.LEFT,
                            UiUtils.TextTypeScales.P3
                    ));
                }
            } else if (item.getProductType() == CultPackBrowseProductType.PLAY) {
                FitnessPackBrowseWidget packs = new FitnessPackBrowseWidget();
                Map<String, String> queryParams = new HashMap<>();
                HashMap<String, String> minPack = packs.getPlayPackItemDetails(userContext, interfaces, queryParams);
                String packMinPrice = minPack != null ? minPack.getOrDefault("minPrice", "999") : "999";
                footer.setCfTitle(new CFTextData(packMinPrice,
                        UiUtils.UIColors.COLOR_WHITE,
                        "2",
                        UiUtils.TextAlignment.LEFT,
                        UiUtils.TextTypeScales.P3
                ));
            }
            item.setFooter(footer);

        }
        return Collections.singletonList(this);
    }

}

@Getter
@Setter
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class BannerDetail {
    String imageUrl;
    String aspectRatio;
}
