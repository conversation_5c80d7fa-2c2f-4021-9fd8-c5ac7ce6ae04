package com.curefit.cfapi.widgets.fitness;

import com.curefit.cfapi.pojo.widgets.SkuPack;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SKUTab {
    String id;
    String title;
    List<BaseWidget> widgets;
    SkuPack pack;
    BannerImage backgroundBanner;

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class BannerImage {
        String imageUrl;
        int height;
        int width; // leave it null for width = ScreenWidth
    }
}
