package com.curefit.cfapi.widgets.dailystreak;

import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.constants.AppDeeplink;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.HomePageWidgetConfigurator;
import com.curefit.cfapi.util.UserStreakUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.uas.responses.StreakResponse;
import lombok.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.curefit.cfapi.service.UserStreakService.*;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DSHomePageWidget extends BaseWidget {
    int currentStreakCount;
    CFTextData streakCountText;
    String imageUrl;
    List<String> gradientColors;
    Action action;
    Integer bestStreakCount;
    String icon;
    boolean showUserStreakFlow;
    String boxShadowColor;
    List<String> borderGradient;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext,
                                      WidgetContext widgetContext) throws Exception {
        setWidgetType(WidgetType.DS_HOME_PAGE_WIDGET);
        try {
            Object response = userContext.getRequestCache().getMexRequestFuture(RequestType.USER_STREAK_DETAILS, userContext).get();
            StreakResponse userStreakData = null;
            if(response instanceof StreakResponse) userStreakData = (StreakResponse) response;
            if(UserStreakUtil.showUserStreakSummaryWidget(interfaces, userContext, userStreakData) != Boolean.FALSE){
                return null;
            }

            Long streakRepairExpiryEpoch = UserStreakUtil.streakRepairExpiredEpoch(userContext, interfaces);
            Long streakRepairConsumedEpoch = UserStreakUtil.streakRepairConsumedEpoch(userContext, interfaces);
            DSHomePageWidgetConfig dsHomePageWidgetConfig = HomePageWidgetConfigurator.createHomePageWidgetConfig(userStreakData, streakRepairExpiryEpoch, streakRepairConsumedEpoch);

            String imageUrl = dsHomePageWidgetConfig.getImageUrl();
            List<String> gradientColors = dsHomePageWidgetConfig.getGradientColors();

            Action action = Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .url(AppDeeplink.USER_STREAK_MAIN_PAGE.getDeeplinkString())
                    .build();

            setStreakCountText(CFTextData.builder()
                    .text(" [P2,#FFFFFF,day] [P2,#FFFFFF,streak]")
                    .color("#FFFFFF")
                    .build());
            setImageUrl(imageUrl);
            setCurrentStreakCount(userStreakData.getCurrentStreakCount());
            setGradientColors(gradientColors);
            setShowUserStreakFlow(true);
            setBoxShadowColor(dsHomePageWidgetConfig.getBoxShadowColor());
            setBorderGradient(dsHomePageWidgetConfig.getBorderGradient());
            setAction(action);
            setIcon(userContext.getSessionInfo().getAppVersion() >= DS_DYNAMIC_ASSETS_APP_VERSION ?
                    (userStreakData.isStreakPaused() || userStreakData.getCurrentStreakCount() == 0) ? "image/daily-streak/fire_gray.png" : "image/daily-streak/fire.png"
                    : (userStreakData.isStreakPaused() || userStreakData.getCurrentStreakCount() == 0) ? "assets/fire_gray.png" : "assets/fire.png");
            setBestStreakCount(userStreakData.getBestStreakCount());

            return Collections.singletonList(this);
        } catch (Exception e){
            interfaces.exceptionReportingService.reportException(e);
            return null;
        }
    }
}
