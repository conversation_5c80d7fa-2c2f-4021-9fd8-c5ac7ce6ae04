package com.curefit.cfapi.widgets.community;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.userservice.pojo.entry.UserEntry;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@Builder
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SquadGoalsBgImageWidget extends BaseWidget {
    String image;
    String gradientImage;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext,
                                      WidgetContext widgetContext) throws Exception {
        Map<String, String> queryParams = widgetContext.getQueryParams();

        if(queryParams.get("image") != null){
            setImage(queryParams.get("image"));
        }
        return Collections.singletonList(this);
    }
}
