package com.curefit.cfapi.widgets.guidedWorkoutsPlayback;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.view.ContainerStyle;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.view.viewmodels.fitnessplanner.MediaData;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FPAutoplayVideoWidget extends BaseWidgetNonVM {
    List<Action> leftActions;
    List<Action> rightActions;
    MediaData media;
    int itemIndex;
    Boolean isRestType;
    ContainerStyle buttonContainerStyle;
    Boolean shouldFlipVideo;


    public FPAutoplayVideoWidget() {this(WidgetType.FP_AUTOPLAY_VIDEO);}

    protected FPAutoplayVideoWidget(WidgetType fpAutoplayVideo) {
        super(fpAutoplayVideo);
    }
}
