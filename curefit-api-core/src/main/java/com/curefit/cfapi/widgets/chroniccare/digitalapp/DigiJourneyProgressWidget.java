package com.curefit.cfapi.widgets.chroniccare.digitalapp;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DigiJourneyProgressWidget extends BaseWidgetNonVM {
    DonutProgress fitScore;
    DonutProgress sessionProgress;
    DonutProgress lessonProgress;
    DonutProgress tasksProgress;
    DayProgress dayProgress;
    Action action =  Action.builder().actionType(ActionType.NAVIGATION).url("curefit://digiprogramjourney").build();
    Action scoreAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://digiscorepage").build();

    public DigiJourneyProgressWidget(){
        super(WidgetType.DIGI_JOURNEY_PROGRESS_WIDGET);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DonutProgress {
        Long score;
        Long total;
        String labelName;
        String progressColor;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DayProgress {
        int currentDay;
        int totalDays;
    }
}
