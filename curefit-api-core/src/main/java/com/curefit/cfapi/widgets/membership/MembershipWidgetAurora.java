package com.curefit.cfapi.widgets.membership;

import com.curefit.center.dtos.CenterEntry;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.atom.CFGradientData;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.atom.GradientDirection;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.SfRenewalConfig;
import com.curefit.cfapi.view.viewmodels.enterprise.CorpMembershipCardView;
import com.curefit.cfapi.view.viewmodels.enterprise.PilatesMembershipCardView;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.fitness.FitnessWidgetUtil;
import com.curefit.cfapi.widgets.fitness.SkuPackUtil;
import com.curefit.common.data.exception.BaseException;
import com.curefit.membership.pojo.entry.Attribute;
import com.curefit.membership.pojo.entry.Benefit;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.types.AttributeKeyType;
import com.curefit.membership.types.BenefitType;
import com.curefit.membership.types.MembershipType;
import com.curefit.membership.types.Status;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.product.enums.ProductType;
import com.curefit.product.models.diy.CFLiveProduct;
import com.fasterxml.jackson.annotation.JsonInclude;
import fit.cult.enterprise.dto.corporate.CorporateDetails;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.scheduling.annotation.Async;
import reactor.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.curefit.cfapi.util.MembershipUtil.*;
import static com.curefit.cfapi.util.PlayUtil.*;
import static com.curefit.membership.types.Status.PURCHASED;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class MembershipWidgetAurora extends BaseWidget {

    String header;
    String backgroundImage;
    List<MembershipCardItem> data;
    Boolean canShowBenefitCards = false;
    Boolean cardWithActions;

    static Comparator<Membership> membershipEndDateComparator = (o1, o2) -> o1.getEnd() > o2.getEnd() ? 1 : -1;

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        CompletableFuture<List<Membership>> membershipsFuture = getMemberships(interfaces, userContext);
        CompletableFuture<CorporateDetails> corporateDetailsFuture = CompletableFuture.completedFuture(null);
        boolean isEnterpriseUser = AppUtil.isEnterpriseFeatureSupported(userContext, 9.75f) && AppUtil.doesUserBelongToEnterpriseUserSegment(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
        if (isEnterpriseUser) {
            corporateDetailsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return interfaces.enterpriseClient.getCorporateForProfileCard(userContext.getUserProfile().getUserId());
                } catch (Exception e) {
                    log.error("MembershipWidgetAurora::buildView Error while build Corp Membership card  for user id : {}", userContext.getUserProfile().getUserId(), e);
                    return null;
                }
            }, interfaces.getTaskExecutor());
        }
        List<Membership> memberships = membershipsFuture.get();
        List<MembershipCardItem> membershipCardItems = new ArrayList<>();
        final Boolean hasUpcomingMemberships = hasUpcomingMemberships(memberships, userContext);

        if (!CollectionUtils.isEmpty(memberships)) {
            boolean isMembershipDetailV2Supported = AppUtil.isMembershipDetailV2PageSupported(userContext);

            CorporateDetails corporateDetails = corporateDetailsFuture.get();

            List<CompletableFuture<List<MembershipCardItem>>> membershipCardItemFutures = memberships.stream().map(membership -> CompletableFuture.supplyAsync(() -> {
                MembershipCardItem benefitMembershipCard = null;
                MembershipCardItem membershipCard = new MembershipCardItem();
                final ProductType productType = setProductTypeFromProductId(membership);
                if (productType == null) {
                    return null;
                }
                MembershipState membershipState = getMembershipState(membership, userContext);
                String cultSelectCenterName = "";
                try {
                    cultSelectCenterName = CultUtil.getSelectCenterNameFromMembership(membership, interfaces);
                } catch (BaseException e) {
                    throw new RuntimeException(e);
                }
                try {
                    String title = getMembershipTitle(productType, membership, interfaces, !cultSelectCenterName.isEmpty(), userContext);
                    membershipCard.setTitle(title);
                    if (productType == ProductType.FITNESS || productType == ProductType.GYMFIT_FITNESS_PRODUCT) {
                        if (SkuPackUtil.isPlusMembership(membership)) {
                            String[] splitTitle = title.split(" ");
                            membershipCard.setTitle(splitTitle[0]);
                            final String finalSplitTitle = splitTitle[1];
                            membershipCard.setSubtitle2(new CFTextData() {
                                {
                                    if (productType == ProductType.FITNESS)
                                        setText("[H9,#CB8EFF,%s][P11,#CB8EFF, PLUS,italic]".formatted(finalSplitTitle));
                                    else
                                        setText("[H9,#FF9F9E,%s][P11,#FF9F9E, PLUS,italic]".formatted(finalSplitTitle));
                                    setAlignment("start");
                                    setIsRichText(true);
                                    setTypeScale(UiUtils.TextTypeScales.H9);
                                }
                            });
                        } else if (membership.getMetadata() != null && membership.getMetadata().containsKey("isAddonApplied")
                                && membership.getMetadata().containsKey("addonType")
                                && membership.getMetadata().get("addonType").toString().equals(ProductSubType.EXTERNAL_ADDON.toString())) {
                            // pack is booster pack
                            membershipCard.setTitle(title + " + ");
                            membershipCard.setSubtitle2(new CFTextData() {
                                {
                                    setText("Booster");
                                    setAlignment("start");
                                    setTypeScale(UiUtils.TextTypeScales.H9);
                                    setGradient(new CFGradientData(new ArrayList<String>() {
                                        {
                                            if (productType == ProductType.FITNESS) {
                                                add("#9648FF");
                                                add("#EED3FF");
                                                add("#6A4693");
                                            } else if (productType == ProductType.GYMFIT_FITNESS_PACK) {
                                                add("#FF9F9E");
                                                add("#FFDADD");
                                                add("#FFBDBC");
                                                add("#FB857F");
                                            } else add("#FFFFFF");
                                        }
                                    }, GradientDirection.DIAGONAL));
                                }
                            });
                        }
                    }
                } catch (BaseException ignored) {
                    membershipCard.setTitle(membership.getName());
                }
                membershipCard.setMembershipState(membershipState.toString());
                membershipCard.setMembershipStateTextColor(getPrimaryColorForMembershipState(membershipState));
                if (productType.equals(ProductType.GYM_PT_PRODUCT)) {
                    membershipCard.setProgressBar(null);
                } else {
                    membershipCard.setProgressBar(getProgressBarDateForMembership(membership, membershipState, userContext, false));
                }

                membershipCard.setSubtitle(cultSelectCenterName);
                membershipCard.setProductType(productType);

                if (CultUtil.isCultpassXMembership(membership) || CultUtil.isLimitedSessionEliteMembership(membership)) {
                    membershipCard.setSubtitle(CultUtil.getLimitedEliteSessionsLeftText(membership, userContext, false));
                }

                if (corporateDetails != null) {
                    membershipCard.setSponsoredByCaption("sponsored by");
                    membershipCard.setCompanyLogoUrl(corporateDetails.getCorporate().getLogo());
                    membershipCard.setCompanyName(corporateDetails.getCorporate().getName());
                }
                String skuType = AppUtil.isMembershipDetailPageMigratedForMembership(membership, userContext);
                try {
                    if (productType.equals(ProductType.FITNESS)) {
                        long membershipId = membership.getId();
                        boolean canShowNewMembershipDetailPage = isMembershipDetailV2Supported && !CultUtil.isJPMCMembership(membership);
                        membershipCard.setCtaAction(getCtaAction(membership, membershipState, interfaces, userContext, hasUpcomingMemberships, membershipId, canShowNewMembershipDetailPage, productType));
                        if (canShowNewMembershipDetailPage) {
                            membershipCard.setCardAction(ActionUtil.getMembershipDetailPageActionV2(String.valueOf(membershipId), false, skuType));
                        } else {
                            membershipCard.setCardAction(CultUtil.fitnessMembershipAction(String.valueOf(membershipId), false, userContext.getSessionInfo().getUserAgent(), false));
                        }
                    } else if (productType.equals(ProductType.CF_LIVE) || productType.equals(ProductType.LIVE)) {
                        CompletableFuture<CFLiveProduct> cfLiveProductFuture = CompletableFuture.supplyAsync(() -> interfaces.diyfsService.getCFLiveProduct(membership.getProductId()), interfaces.getTaskExecutor());

                        MembershipCardItem finalMembershipCard = membershipCard;
                        cfLiveProductFuture.thenAccept(cfLiveProduct -> {
                            finalMembershipCard.setCtaAction(FitnessWidgetUtil.getMembershipStateAction(userContext, membershipState, membership, cfLiveProduct, interfaces.apiKeyService));
                        });

                        if (isMembershipDetailV2Supported) {
                            membershipCard.setCardAction(ActionUtil.getMembershipDetailPageActionV2(String.valueOf(membership.getId()), false, skuType));
                        } else {
                            membershipCard.setCardAction(FitnessWidgetUtil.getLiveMembershipCardAction(userContext, membership, membershipState));
                        }
                    } else if (productType.equals(ProductType.BUNDLE)) {
                        membershipCard.setCtaAction(null);
                        membershipCard.setCardAction(TransformUtil.getPostPurchaseClpActionBasedOnSubCategoryCode(TransformUtil.getTransformSubCategoryCode(membership), null));
                    } else if (productType.equals(ProductType.GYM_PT_PRODUCT)) {
                        ProgressBar ptMembershipProgressBar = GymPtUtil.getProgressBarForGymPtProduct(membership, membershipState, userContext, GymPtUtil.GYM_PT_MEMBERSHIP_BENEFIT_NAME);
                        ptMembershipProgressBar.setProgressBarColor(getPrimaryColorForProgressBar(membershipState));
                        ptMembershipProgressBar.setProgressBarBackgroundColor(getBackgroundColorForMembershipState(membershipState));
                        membershipCard.setProgressBar(ptMembershipProgressBar);
                        if (GymPtUtil.hasActivePTBenefits(membership)) {
                            membershipCard.setCtaAction(GymPtUtil.getPtAction(userContext, membership, membershipState, interfaces));
                        }
                        membershipCard.setCardAction(GymPtUtil.getPackNavigationAction(membership.getId(), AppUtil.isWeb(userContext)));
                    } else if (productType.equals(ProductType.PLAY)) {
                        processPlayMembership(membership, userContext, membershipCard, cultSelectCenterName, isMembershipDetailV2Supported, skuType);
                    } else if (productType.equals(ProductType.LUX_FITNESS_PRODUCT)) {
                        if (isMembershipDetailV2Supported && Objects.nonNull(skuType) && (skuType.toUpperCase().contains("LUX") || skuType.toUpperCase().contains("LUX_GX") || skuType.toUpperCase().contains("LUX_GROUP_CLASSES"))) {
                            membershipCard.setCardAction(ActionUtil.getMembershipDetailPageActionV2(String.valueOf(membership.getId()), false, skuType));
                        } else {
                            membershipCard.setCardAction(GymUtil.getMembershipDetailAction(membership.getId(), AppUtil.isWeb(userContext)));
                        }
                        membershipCard.setCtaAction(null);
                        membershipCard = copyMembershipCardItem(membershipCard);
                    } else {
                        long membershipId = membership.getId();
                        membershipCard.setCtaAction(GymUtil.getMembershipStateAction(userContext, membership, productType, membershipId));
                        if (isMembershipDetailV2Supported && Objects.nonNull(skuType)) {
                            membershipCard.setCardAction(ActionUtil.getMembershipDetailPageActionV2(String.valueOf(membershipId), false, skuType));
                        } else {
                            membershipCard.setCardAction(GymUtil.getMembershipDetailAction(membership.getId(), AppUtil.isWeb(userContext)));
                        }
                    }

                    if (canShowBenefitCards
                            && !Objects.equals(membershipState, MembershipState.EXPIRED)
                            && !(productType.equals(ProductType.CF_LIVE) || productType.equals(ProductType.LIVE))
                            && membership.getBenefits().stream().anyMatch(item -> (item.getName().startsWith("CFLIVE") || item.getName().startsWith("CF_LIVE")))
                    ) {
                        MembershipState benefitMembershipState = membershipState;
                        if (Objects.equals(benefitMembershipState, MembershipState.PAUSED)) {
                            benefitMembershipState = MembershipState.ACTIVE;
                        }
                        final MembershipCardItem finalBenefitMembershipCard = new MembershipCardItem();
                        if (corporateDetails != null) {
                            finalBenefitMembershipCard.setSponsoredByCaption("sponsored by");
                            finalBenefitMembershipCard.setCompanyLogoUrl(corporateDetails.getCorporate().getLogo());
                            finalBenefitMembershipCard.setCompanyName(corporateDetails.getCorporate().getName());
                        }
                        finalBenefitMembershipCard.setProgressBar(getProgressBarDateForMembership(membership, benefitMembershipState, userContext, false));
                        finalBenefitMembershipCard.setMembershipState(benefitMembershipState.toString());
                        finalBenefitMembershipCard.setMembershipStateTextColor(getPrimaryColorForMembershipState(benefitMembershipState));
                        finalBenefitMembershipCard.setTitle("cultpass HOME");
                        final MembershipState finalBenefitMembershipState = benefitMembershipState;
                        CompletableFuture<CFLiveProduct> cfLiveProductFutureForBenefit = CompletableFuture.supplyAsync(() -> interfaces.diyfsService.getCFLiveProduct(membership.getProductId()), interfaces.getTaskExecutor());
                        cfLiveProductFutureForBenefit.thenAccept(cfLiveProduct -> {
                            if (isMembershipDetailV2Supported) {
                                finalBenefitMembershipCard.setCardAction(ActionUtil.getMembershipDetailPageActionV2(String.valueOf(membership.getId()), true, "HOME"));
                            } else {
                                finalBenefitMembershipCard.setCardAction(FitnessWidgetUtil.getLiveMembershipCardAction(userContext, membership, finalBenefitMembershipState));
                            }
                            if (Objects.nonNull(cfLiveProduct)) {
                                finalBenefitMembershipCard.setCtaAction(FitnessWidgetUtil.getMembershipStateAction(userContext, finalBenefitMembershipState, membership, cfLiveProduct, interfaces.apiKeyService));
                            }
                        });
                        benefitMembershipCard = finalBenefitMembershipCard;
                    }

                } catch (Exception e) {
                    log.error("error by creating membership widget: ", e);
                    membershipCard.setCtaAction(null);
                }
                if (Objects.nonNull(benefitMembershipCard)) {
                    return List.of(membershipCard, benefitMembershipCard);
                }
                return List.of(membershipCard);
            }, interfaces.getTaskExecutor())).toList();

            CompletableFuture.allOf(membershipCardItemFutures.toArray(new CompletableFuture[0])).join();
            for (CompletableFuture<List<MembershipCardItem>> future : membershipCardItemFutures) {
                if (Objects.nonNull(future.get())) {
                    membershipCardItems.addAll(future.get());
                }
            }

            if (corporateDetails != null) {
                int benefitCount = corporateDetails.getBenefitCount();
                String corporateName = corporateDetails.getCorporate().getName();
                CorpMembershipCardView corpMembershipCardView = new CorpMembershipCardView();
                corpMembershipCardView.setSubtitle(corporateName + " • " + benefitCount + ((benefitCount <= 1) ? " Benefit" : " Benefits"));
                boolean isOptumUser = AppUtil.doesUserBelongToOptumCorpSegment(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
                if (isOptumUser) {
                    membershipCardItems.addFirst(corpMembershipCardView);
                } else {
                    membershipCardItems.add(corpMembershipCardView);
                }
            }
        } else {
            return null;
        }
        if (CollectionUtils.isEmpty(membershipCardItems)) {
            return null;
        }
        setData(membershipCardItems);
        return Collections.singletonList(this);
    }

    public void processPlayMembership(
            Membership membership,
            UserContext userContext,
            MembershipCardItem membershipCard,
            String cultSelectCenterName,
            boolean isMembershipDetailV2Supported,
            String skuType
    ) {
        boolean isSportLevelPlayMembership = isPlaySportsLevelMembership(membership);
        boolean isSelectPlayMembership = isPlayLiteMembership(membership);
        boolean isLimitedSessionMembership = PlayUtil.isEnterpriseLimitedSessionPlayMembership(membership);

        if (isMembershipDetailV2Supported) {
            membershipCard.setCardAction(ActionUtil.getMembershipDetailPageActionV2(String.valueOf(membership.getId()), false, skuType));
        } else {
            membershipCard.setCardAction(PlayUtil.getPackNavigationAction(membership.getId(), AppUtil.isWeb(userContext)));
        }
        membershipCard.setCtaAction(PlayUtil.getMembershipStateAction(userContext, membership, ProductType.PLAY, membership.getId()));
        if (userContext.getSessionInfo().getAppVersion() >= 10.89)
            membershipCard.setCtaActionV2(true);
        if (isSportLevelPlayMembership) {
            membershipCard.setSubtitle(cultSelectCenterName);
        } else if (isSelectPlayMembership) {
            membershipCard.setSubtitle("L  I  T  E");
        } else if (isLimitedSessionMembership) {
            Benefit playBenefit = membership.getBenefits().stream().filter(
                    benefit -> benefit.getName().equals(PlayUtil.PLAY_PACK_BENEFIT_NAME)
            ).findFirst().get();

            Integer maxSessions = playBenefit.getMaxTickets();
            String suffix = playBenefit.getType() == BenefitType.STATIC ? " Sessions" : " Sessions/month";
            membershipCard.setSubtitle(maxSessions + suffix);
        }
    }

    @Async("cfApiTaskExecutor")
    public static CompletableFuture<List<Membership>> getMemberships(ServiceInterfaces serviceInterfaces, UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        return serviceInterfaces.membershipService.getCachedMembershipsForUser(userId, "curefit", null).thenApply(memberships -> {
            if (CollectionUtils.isEmpty(memberships)) {
                return Collections.<Membership>emptyList();
            }
            memberships.sort(membershipEndDateComparator);
            List<Membership> validMemberships = memberships.stream().filter(membership -> membership.getEnd() >= TimeUtil.now(userContext.getUserProfile().getTimezone()) && (membership.getStatus().equals(PURCHASED) || membership.getStatus().equals(Status.PAUSED))).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(validMemberships)) {
                memberships = validMemberships;
            } else {
                memberships = memberships.stream().filter(membership -> membership.getEnd() < TimeUtil.now(userContext.getUserProfile().getTimezone()) && membership.getStatus().equals(PURCHASED)).collect(Collectors.toList());
            }
            return memberships;
        }).exceptionally(ex -> {
            log.error("Error fetching memberships for user id : {} {}", userId, ex.getMessage(), ex);
            return Collections.<Membership>emptyList();
        });
    }

    public MembershipWidgetAurora setCanShowBenefitCards(Boolean canShow) {
        this.canShowBenefitCards = canShow;
        return this;
    }

    public static Boolean hasUpcomingMemberships(List<Membership> memberships, UserContext userContext) {
        if (CollectionUtils.isEmpty(memberships)) {
            return false;
        }
        for (Membership membership : memberships) {
            LocalDate today = TimeUtil.getDateNow(userContext.getUserProfile().getTimezone()).toLocalDate();
            LocalDate startDate = TimeUtil.getDateFromTime(membership.getStart(), userContext.getUserProfile().getTimezone()).toLocalDate();
            if (startDate.isAfter(today)) {
                return true;
            }
        }
        return false;
    }

    public static String getMembershipTitle(ProductType productType, Membership membership, ServiceInterfaces interfaces, Boolean isCultSelect, UserContext userContext) throws BaseException {
        Boolean isLimitedMembership = CultUtil.isLimitedSessionEliteMembership(membership);
        Boolean isCultpassXMembership = CultUtil.isCultpassXMembership(membership);

        if (productType == ProductType.PLAY) {
            if (PlayUtil.isEnterpriseLimitedSessionPlayMembership(membership) && PlayUtil.isPlaySportsCityLevelMembership(membership)) {
                Attribute accessSportWorkoutAttribute = getMembershipAttributeByKey(AttributeKeyType.ACCESS_SPORT_WORKOUT, membership);
                String workoutId = accessSportWorkoutAttribute.getAttrValue();
                return "cultpass %s (Limited Pack)".formatted(getSportNameByWorkoutId(workoutId));
            }else if (isPlaySportsLevelMembership(membership)) {
                Attribute accessSportWorkoutAttribute = getMembershipAttributeByKey(AttributeKeyType.ACCESS_SPORT_WORKOUT, membership);
                String workoutId = accessSportWorkoutAttribute.getAttrValue();
                return "cultpass %s".formatted(getSportNameByWorkoutId(workoutId));
            } else if (PlayUtil.isPlaySportsCityLevelMembership(membership)) {
                Attribute accessSportWorkoutAttribute = getMembershipAttributeByKey(AttributeKeyType.ACCESS_SPORT_WORKOUT, membership);
                String workoutId = accessSportWorkoutAttribute.getAttrValue();
                return "cultpass %s".formatted(getSportNameByWorkoutId(workoutId));
            } else if (PlayUtil.isEnterpriseLimitedSessionPlayMembership(membership)) {
                return "cultpass PLAY (Limited Pack)";
            }
            return "cultpass PLAY";
        } else if (isLimitedMembership) {
            return "cultpass ELITE (Limited Pack)";
        } else if (isCultpassXMembership) {
            return "ELITE + ONEPASS";
        } else if (isCultSelect) {
            return "cultpass SELECT";
        } else if (productType == ProductType.FITNESS) {
            return "cultpass ELITE";
        } else if (productType == ProductType.GYMFIT_FITNESS_PRODUCT) {
            return "cultpass PRO";
        } else if (productType == ProductType.GYM_PT_PRODUCT) {
            return "Gym Personal Training";
        } else if (productType == ProductType.LUX_FITNESS_PRODUCT) {
            Benefit pilatesBenefit = membership.getBenefits().stream().filter(benefit -> benefit.getName().equals("LUX_GX")).findFirst().orElse(null);
            if (pilatesBenefit != null) {
                return "Pilates";
            }
            Benefit luxGroupClassesBenefit = membership.getBenefits().stream().filter(benefit -> benefit.getName().equals("LUX_GROUP_CLASSES")).findFirst().orElse(null);
            if (luxGroupClassesBenefit != null){
                return "LUX GROUP CLASSES";
            }
            if (membership.getMetadata().containsKey("centerServiceCenterId")) {
                String centerServiceId = membership.getMetadata().get("centerServiceCenterId").toString();
                List<CenterEntry> centerEntries = interfaces.centerService.getCachedCentersById(Collections.singletonList(centerServiceId), false, null, null);
                if (Objects.nonNull(centerEntries) && !centerEntries.isEmpty()) {
                    CenterEntry centerEntry = centerEntries.get(0);
                    return centerEntry.getName();
                }
            }
            return membership.getName();
        } else if (productType == ProductType.ONEPASS_PRODUCT) {
            return "OnePass";
        } else if (productType == ProductType.BUNDLE) {
            String subCategoryCode = TransformUtil.getTransformSubCategoryCode(membership);
            if (membership.getProductId().contains("TRANSFORM_NC")) {
                return "cult Nutrition Coach";
            }
            return switch (subCategoryCode) {
                case "BOOTCAMP" -> "cult Bootcamp";
                case "TRANSFORM_PLUS" -> "cult Transform Plus";
                case "LIFT" -> "cult Lift";
                case "TRANSFORM_PT" -> "cult Online Personal Trainer";
                case "TRANSFORM_NC" -> "cult Nutrition Coach";
                case "TRANSFORM_FC" -> "cult Fitness Coach";
                default -> "cult Transform";
            };
        }
        return "cultpass HOME";
    }

    private Action getCtaAction(Membership membership, MembershipState membershipState, ServiceInterfaces interfaces, UserContext userContext, Boolean hasUpcomingMemberships, long membershipId, Boolean canShowNewMembershipDetailPage, ProductType productType) throws BaseException {
        Action ctaAction = null;
        try {
            if (membership.getType() == MembershipType.COMPLIMENTARY) {
                NameValuePair nameValuePair = new BasicNameValuePair("pageFrom", "fitnesshubclp");
                List<NameValuePair> nameValuePairs = new ArrayList<>();
                nameValuePairs.add(nameValuePair);
                ctaAction = ActionUtil.getClassBookingAction(userContext, ProductType.FITNESS, nameValuePairs);
                ctaAction.setTitle("BOOK A CLASS");
                return ctaAction;
            }
            switch (membershipState) {
                case ACTIVE, EXPIRING:
                    if (membership.getActivePause() != null) {
                        ctaAction = CultUtil.getMembershipUnpauseAction(membership, productType, userContext);
                    } else {
                        PackPauseResumeDetails pausePackData = CultUtil.getPackPauseResumeDetails(userContext, membership, productType);
                        if (pausePackData != null && pausePackData.getIsPauseAllowed()) {
                            ctaAction = CultUtil.getPackPauseResumeAction(pausePackData, membership, userContext);
                        }
                    }
                    break;
                case PAUSED:
                    PackPauseResumeDetails pausePackData = CultUtil.getPackPauseResumeDetails(userContext, membership, productType);
                    if (pausePackData != null && pausePackData.getIsPauseAllowed()) {
                        ctaAction = CultUtil.getPackPauseResumeAction(pausePackData, membership, userContext);
                    }
                    break;
                case UPCOMING:
                    ctaAction = CultUtil.getChangeStartDateAction(membership, productType);
                    break;
                case EXPIRED:
                    if (CultUtil.getIsSelectRenewal(userContext, Arrays.asList(membership))) {
                        Action _ctaAction = CultUtil.getSelectRenewalAction(userContext, membership, interfaces);
                        ctaAction = _ctaAction;
                    } else if (AppUtil.isRenewalForCenterLevelPricingSupported(userContext, interfaces, interfaces.getEnvironmentService())) {
                        Action _ctaAction = new Action();
                        _ctaAction.setUrl("curefit://fl_listpage?pageId=select_renewal&hideTitle=true");
                        _ctaAction.setTitle("RENEW");
                        ctaAction = _ctaAction;
                    } else if (!hasUpcomingMemberships) {
                        String skuType = AppUtil.isMembershipDetailPageMigratedForMembership(membership, userContext);
                        if (canShowNewMembershipDetailPage && Objects.nonNull(skuType)) {
                            ctaAction = ActionUtil.getMembershipDetailPageActionV2(String.valueOf(membershipId), false, skuType);
                        } else {
                            ctaAction = CultUtil.fitnessMembershipAction(
                                    String.valueOf(membershipId), false, userContext.getSessionInfo().getUserAgent(), true);
                        }
                    }
                    break;
            }
        } catch (Exception e) {
            return null;
        }
        return ctaAction;
    }

}
