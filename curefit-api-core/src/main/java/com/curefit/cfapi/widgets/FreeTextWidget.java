package com.curefit.cfapi.widgets;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.Debugger;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.Collections;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class FreeTextWidget extends BaseWidget {

    String text;
    JsonNode jsonObject;
    public FreeTextWidget() {
        this.widgetType = WidgetType.FREE_TEXT_WIDGET;
    }

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext,
                                      WidgetContext widgetContext) throws Exception {

        Debugger debugger = Debugger.getDebuggerFromUserContext(userContext);

        try {
            debugger.msg(text);
            ObjectMapper mapper = new ObjectMapper();
            jsonObject = mapper.readTree(text);
            debugger.msg(jsonObject);
        } catch (Exception ignored) {}
        return Collections.singletonList(this);
    }

}
