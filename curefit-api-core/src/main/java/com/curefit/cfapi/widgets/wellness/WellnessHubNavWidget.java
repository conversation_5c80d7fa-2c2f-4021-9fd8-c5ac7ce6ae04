package com.curefit.cfapi.widgets.wellness;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.Status;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.model.internal.wellness.WellnessHubNavItem;
import com.curefit.cfapi.model.internal.wellness.WellnessHubNavVariantType;
import com.curefit.cfapi.model.internal.wellness.WellnessHubProductType;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.pojo.user.QuickActionScore;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.ActionUtil;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.curefit.cfapi.util.UserAttributeUtil.WELLNESS_HUB_PRODUCT_TYPE_ACTIVE_PACK_SEGMENT_KEY;
import static com.curefit.cfapi.util.UserAttributeUtil.WELLNESS_PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Slf4j
public class WellnessHubNavWidget extends BaseWidget {

    WellnessHubNavVariantType variant;
    List<WellnessHubNavItem> navItems;
    Integer maxItemsToShowUpfront;
    Boolean skipAffinityOrdering;
    public WellnessHubNavWidget(WidgetType widgetType) {
        super(widgetType);
    }

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws ExecutionException, InterruptedException {

        if (!AppUtil.isWellnessHubNavWidgetSupported(userContext)) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(this.navItems) && this.variant != null) {
            // Filtering out invalid values from config
            this.navItems = this.navItems.stream().filter(item -> {
                return item.getProductType() != null &&
                        item.getActionV2() != null &&
                        item.getImageUrl() != null &&
                        item.getStatus().equals(Status.LIVE) &&
                        StringUtils.isNotEmpty(item.getText()) &&
                        item.getProductAffinityScore() != null &&
                        (this.variant.equals(WellnessHubNavVariantType.VARIANT_A) ? item.getLargeImageUrl() != null : true);
            }).collect(Collectors.toList());

            // Getting Product Types Ordering based on affinity
            CompletableFuture<List<WellnessHubProductType>> orderedProductTypesPromise = CompletableFuture.supplyAsync(() -> {
                return this.sortOfferings(
                        userContext,
                        this.navItems.stream().map(item -> item.getProductType()).distinct().collect(Collectors.toList()),
                        interfaces
                );
            }, interfaces.getTaskExecutor());

            // Getting Segment Validation
            CompletableFuture<Map<String, Segment>> segmentPromise = CompletableFuture.supplyAsync(() -> {
                return this.getSegmentMapFromItems(interfaces, userContext);
            }, interfaces.getTaskExecutor());

            //Resolving Items as per segment Promise
            this.navItems = this.getValidSegmentedList(segmentPromise.get());

            if (CollectionUtils.isNotEmpty(this.navItems)) {
               // Resolving Ordering of Items based Product Affinity Based
               this.navItems = this.getAffinityBasedSortedNavItem(orderedProductTypesPromise.get());

               //Resolving action from actionv2 for items
               this.navItems = this.getListWithUpdatedAction(interfaces, userContext);

               // Building View as per variant
               if (variant.equals(WellnessHubNavVariantType.VARIANT_A)) {
                   this.navItems = this.getVariantAView();
               } else if (variant.equals(WellnessHubNavVariantType.VARIANT_B)) {
                   this.navItems = this.getVariantBView();
               } else {
                   this.navItems = this.getVariantCView();
               }

               return Collections.singletonList(this);
            }
        }

        return null;
    }

    private List<WellnessHubNavItem> getVariantAView() {
        AtomicInteger i = new AtomicInteger();
        Integer MAX_LARGE_ITEM_TO_SHOW_UPFRONT = 4;
        Integer MAX_ITEM_TO_SHOW_UPFRONT = 4;
        Integer size = this.navItems.size();

        if (size > MAX_ITEM_TO_SHOW_UPFRONT) {
            this.maxItemsToShowUpfront = MAX_ITEM_TO_SHOW_UPFRONT;
        } else {
            this.maxItemsToShowUpfront = size;
        }

        return this.navItems.stream().map(item -> {
            if (i.get() < MAX_LARGE_ITEM_TO_SHOW_UPFRONT) {
                i.getAndIncrement();
                item.setUseLargeImage(true);
            } else {
                item.setUseLargeImage(false);
            }
            item.setSegmentIds(null);
            item.setProductAffinityScore(null);
            return item;
        }).collect(Collectors.toList());

    }

    private List<WellnessHubNavItem> getVariantBView() {
        Integer size = this.navItems.size();
        Integer MAX_ITEM_TO_SHOW_UPFRONT = 6;

        if (size > MAX_ITEM_TO_SHOW_UPFRONT) {
            this.maxItemsToShowUpfront = MAX_ITEM_TO_SHOW_UPFRONT;
        } else {
            this.maxItemsToShowUpfront = size;
        }

        return this.navItems.stream().map(item -> {
           item.setSegmentIds(null);
            item.setProductAffinityScore(null);
            return item;
        }).collect(Collectors.toList());

    }

    private List<WellnessHubNavItem> getVariantCView() {
        Integer size = this.navItems.size();

        if (size > 6) {
            this.maxItemsToShowUpfront = 5;
        } else {
            this.maxItemsToShowUpfront = size;
        }

        return this.navItems.stream().map(item -> {
            item.setSegmentIds(null);
            item.setProductAffinityScore(null);
            return item;
        }).collect(Collectors.toList());
    }

    private List<WellnessHubNavItem> getValidSegmentedList(Map<String, Segment> segmentMap) {
        if (segmentMap == null) {
            return this.navItems;
        }
        return this.navItems.stream().map(item -> {
            if (CollectionUtils.isNotEmpty(item.getSegmentIds())) {
                AtomicBoolean isValidSegmentPresent = new AtomicBoolean(false);
                item.getSegmentIds().stream().forEach(segmentId -> {
                    if (!isValidSegmentPresent.get()) {
                        isValidSegmentPresent.set(segmentMap.get(segmentId) != null);
                    }
                });
                if (isValidSegmentPresent.get()) {
                    return item;
                }
            } else {
                return item;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Map<WellnessHubProductType, Double> getProductAffinityScoreMap() {
        Map<WellnessHubProductType, Double> map = new HashMap<WellnessHubProductType, Double>();

        this.navItems.stream().forEach(item -> {
            map.put(item.getProductType(), Double.valueOf(item.getProductAffinityScore()));
        });
        return map;
    }

    private Map<String, Segment> getSegmentMapFromItems(ServiceInterfaces interfaces,  UserContext userContext) {
        List<String> segmentIds = new ArrayList<>();

        this.navItems.stream().forEach(item -> {
            if (CollectionUtils.isNotEmpty(item.getSegmentIds())) {
                item.getSegmentIds().stream().forEach(segmentId -> {
                    segmentIds.add(segmentId);
                });
            }
        });

        try {
            return interfaces.getSegmentEvaluatorService().getSegmentConditionMap(segmentIds.stream().distinct().collect(Collectors.toList()), userContext);
        } catch (Exception ex) {
            interfaces.exceptionReportingService.reportException("Error while getting segment in wellness hub nav widget", ex);
        }

        return null;
    }

    private List<WellnessHubNavItem> getListWithUpdatedAction(ServiceInterfaces interfaces,  UserContext userContext) {
        return this.navItems.stream().map(item -> {
            if (item.getAction() == null) {
                try {
                    item.setAction(ActionUtil.getVMActionFromActionV2(item.getActionV2(), userContext, interfaces));
                    item.setActionV2(null);
                } catch (Exception ex) {
                    interfaces.exceptionReportingService.reportException("Error while mapping action v2 in wellness hub nav widget", ex);
                }
            }
            return item;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<WellnessHubProductType> sortOfferings(UserContext userContext, List<WellnessHubProductType> offerings, ServiceInterfaces interfaces) {
        try {
            var quickActionScoreResponseMap = this.computeScore(userContext, interfaces);
            var finalOfferings = offerings.stream().filter(a -> quickActionScoreResponseMap.containsKey(a)).collect(Collectors.toList());
            finalOfferings.sort((a, b) -> {
                var scoreA = quickActionScoreResponseMap.get(a);
                var scoreB = quickActionScoreResponseMap.get(b);
                return scoreA.getFinalScore() > scoreB.getFinalScore() ? -1 : 1;
            });
            return finalOfferings;
        } catch (Exception e) {
            log.error("Error while sorting quick action ", e);
            return offerings;
        }
    }

    private Map<WellnessHubProductType, QuickActionScore> computeScore(UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        Double totalUsageFrequency = 0.0;
        Double maxUsageFrequency = 0.0;
        Double maxConversionPercentage = 0.0;

        var attributesToFetch = new ArrayList(WELLNESS_PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY.values());
        attributesToFetch.addAll(WELLNESS_HUB_PRODUCT_TYPE_ACTIVE_PACK_SEGMENT_KEY.values());
        var userAttributesResponse = interfaces.userAttributesCacheClient.getAttributes(
                Long.valueOf(userContext.getUserProfile().getUserId()),
                attributesToFetch, AppUtil.getAppTenantFromUserContext(userContext));

        var attributes = userAttributesResponse.getAttributes();
        var productTypeToScoreMap = this.getProductAffinityScoreMap();
        var productTypes = productTypeToScoreMap.keySet();

        SegmentSet<String> userPlatformSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();

        for (WellnessHubProductType productType : productTypes) {
            var score = attributes.get(WELLNESS_PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY.get(productType));
            if (score != null) {
                var scoreValue = Math.max(Double.valueOf((String) attributes.get(WELLNESS_PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY.get(productType))), 0.0D);
                totalUsageFrequency = totalUsageFrequency + scoreValue;
                maxUsageFrequency = scoreValue > maxUsageFrequency ? scoreValue : maxUsageFrequency;
            }
        }
        if (totalUsageFrequency > 0.0) {
            maxConversionPercentage = maxUsageFrequency / totalUsageFrequency;
        }

        Map<WellnessHubProductType, QuickActionScore> scoreMap = new HashMap<WellnessHubProductType, QuickActionScore>();
        for (WellnessHubProductType productType : productTypes
        ) {
            var quickActionScore = new QuickActionScore();
            quickActionScore.setBusinessScore(productTypeToScoreMap.get(productType));
            if (WELLNESS_HUB_PRODUCT_TYPE_ACTIVE_PACK_SEGMENT_KEY.containsKey(productType) &&
                    userPlatformSegments.contains(WELLNESS_HUB_PRODUCT_TYPE_ACTIVE_PACK_SEGMENT_KEY.get(productType))) {
                quickActionScore.setIsPackUser(true);
            } else {
                quickActionScore.setIsPackUser(false);
            }
            var score = attributes.get(WELLNESS_PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY.get(productType));
            if (score != null) {
                var scoreValue = Math.max(Double.valueOf((String) score), 0.0D);
                var conversionPercentage = totalUsageFrequency  > 0 ? scoreValue / totalUsageFrequency : 0;
                var conversionScore = maxConversionPercentage > 0 ? conversionPercentage / maxConversionPercentage : 0;
                quickActionScore.setConversionScore(conversionScore);
            } else {
                quickActionScore.setConversionScore(0.0);
            }
            quickActionScore.setFinalScore(0.75 * quickActionScore.getConversionScore()
                    + 0.15 * quickActionScore.getBusinessScore()
                    + 0.10 * (quickActionScore.getIsPackUser() ? 1 : 0));
            scoreMap.put(productType, quickActionScore);
        }

        return scoreMap;
    }

    private List<WellnessHubNavItem> getAffinityBasedSortedNavItem(List<WellnessHubProductType> orderedProductTypes) {
        List<WellnessHubNavItem> items = new ArrayList<>();
        for (WellnessHubProductType wellnessHubProductType : orderedProductTypes) {
            items.add(this.navItems.stream().filter(navItem -> {
                return navItem.getProductType().equals(wellnessHubProductType);
            }).findFirst().orElse(null));
        }

        if (BooleanUtils.isTrue(this.skipAffinityOrdering)) {
            items = items.stream().filter(Objects::nonNull).collect(Collectors.toList());
            WellnessHubNavItem foodItem = items.stream().filter(item -> item.getProductType().equals(WellnessHubProductType.FOOD)).findFirst().orElse(null);
            WellnessHubNavItem gearItem = items.stream().filter(item -> item.getProductType().equals(WellnessHubProductType.GEAR)).findFirst().orElse(null);
            List<WellnessHubNavItem> otherItems = items.stream().filter(item -> {
                return !item.getProductType().equals(WellnessHubProductType.GEAR) && !item.getProductType().equals(WellnessHubProductType.FOOD);
            }).collect(Collectors.toList());
            items = new ArrayList<>(){{
                add(gearItem);
                add(foodItem);
                addAll(otherItems);
            }};
        }

        return items.stream().filter(Objects::nonNull).collect(Collectors.toList());

    }
}