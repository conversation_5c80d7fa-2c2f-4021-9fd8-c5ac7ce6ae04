package com.curefit.cfapi.widgets.fitness;

import com.curefit.cfapi.widgets.base.BaseWidget;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;


@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CenterWarningTextWidget extends BaseWidget {
    List<String> warning;

    public CenterWarningTextWidget(){
        this.warning = new ArrayList<>();
    }

    public void setWarning(String warning) {
        this.warning.add(warning);
    }
}