package com.curefit.cfapi.widgets.chroniccare;

import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SfQuoteWidget extends BaseWidgetNonVM {
    String quote;

    public SfQuoteWidget() {
        super(WidgetType.SF_QUOTE_WIDGET);
    }
}
