package com.curefit.cfapi.widgets.fitedit;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.Status;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.culticon.CultIconPost;
import com.curefit.cfapi.pojo.util.ActionUtil;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.culticon.CfApiCultIconPostRequest;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.common.banner.ContentMetric;
import com.curefit.cfapi.widgets.common.banner.MerchantryWidget;
import com.curefit.diyfs.pojo.culticon.FiteditContentCategoryType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FitEditRecipeWidget extends MerchantryWidget {

    @Override
    public List<BaseWidget> buildView(ServiceInterfaces interfaces, UserContext userContext, WidgetContext widgetContext) throws Exception {
        this.setWidgetType(WidgetType.MERCHANTRY_WIDGET);
        this.setData(this.fetchBannerItems(interfaces, userContext));
        return super.buildView(interfaces, userContext, widgetContext);
    }

    private List<BannerItem> fetchBannerItems(ServiceInterfaces interfaces, UserContext userContext) throws Exception {
        CfApiCultIconPostRequest cultIconPostRequest = new CfApiCultIconPostRequest();
        cultIconPostRequest.setLimit(10);
        cultIconPostRequest.setCategoryType(FiteditContentCategoryType.FITEDIT_RECIPE);
        cultIconPostRequest.setTemplateId(this.getTemplateId());
        cultIconPostRequest.setUserId(userContext.getUserProfile().getUserId());
        cultIconPostRequest.setUsePosterAsThumbnail(false);
        var cultIconResponse=  interfaces.cultIconService.getCultIconPosts(cultIconPostRequest).get(800, TimeUnit.MILLISECONDS);
        var posts = cultIconResponse.getPosts();
        Collections.shuffle(posts);

        return cultIconResponse.getPosts().stream().map(this::mapPostToBannerItem).collect(Collectors.toList());
    }

    private BannerItem mapPostToBannerItem(CultIconPost cultIconPost) {
        BannerItem ret = new BannerItem();
        ret.setImage(cultIconPost.getImageUrl());
        ContentMetric contentMetric = new ContentMetric();
        contentMetric.setContentId(cultIconPost.getTitle());
        ret.setContentMetric(contentMetric);
        ret.setLoopVideo(false);
        ret.setStatus(Status.LIVE);
        ret.setBannerIdentifier(cultIconPost.getTitle());
        if (cultIconPost.getDescription() != null) {
            ret.setDescription(cultIconPost.getDescription());
        }
        ret.setDedupeId(cultIconPost.getTitle());
        if (cultIconPost.getDeepLinkUrl() != null && !cultIconPost.getDeepLinkUrl().isEmpty()) {
            Action action = ActionUtil.baseNavigationAction();
            action.setUrl(cultIconPost.getDeepLinkUrl());
            ret.setAction(action);
        } else {
            ret.setAction(ActionUtil.iconFeedWithSelectedPost(cultIconPost));
        }
        return ret;
    }
}
