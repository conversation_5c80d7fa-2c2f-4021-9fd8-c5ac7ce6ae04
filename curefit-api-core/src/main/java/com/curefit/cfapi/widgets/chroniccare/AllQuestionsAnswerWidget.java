package com.curefit.cfapi.widgets.chroniccare;

import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sugarfit.nest.pojo.PostEntry;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.List;

import static com.curefit.cfapi.pojo.vm.widget.WidgetType.ALL_QUESTIONS_ANSWER_WIDGET;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AllQuestionsAnswerWidget extends BaseWidgetNonVM {
    String title = "All Questions";
    List<PostEntry> data;
    String  pageType;
    boolean showTitle;
    long buildTime;

    public AllQuestionsAnswerWidget() {
        super(ALL_QUESTIONS_ANSWER_WIDGET);
    }
}
