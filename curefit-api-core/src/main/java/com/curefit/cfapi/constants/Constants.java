package com.curefit.cfapi.constants;
import com.curefit.cfapi.pojo.user.UserFormAutoLaunchConfig;
import com.google.common.collect.ImmutableMap;

import java.util.*;

public class Constants {
    private Constants() {}

    public static final int TL_RECOMMENDATIONS_COUNT = 4;
    public static final String CLOUDINARY_BASE_URL = "https://cdn-images.cure.fit/www-curefit-com/image/upload/";

    public static final List<String> TEST_USER_IDS = Arrays.asList("4078239", "84648087", "85621698");
    public static final String superscriptSt = "\u02e2\u1d57";
    public static final String superscriptNd = "\u207f\u1d48";
    public static final String superscriptRd = "\u02b3\u1d48";
    public static final String superscriptTh = "\u1d57\u02b0";
    public static final String ENTERPRISE_WEBHOOK_API_KEYS = "ENTERPRISE_WEBHOOK_API_KEYS";
    public static final String CRM_BANNER_REGEX_ATTRIBUTE_NAME_TAG = "attributeName";
    public static final String CRM_BANNER_REGEX_EVENT_PROPERTY_NAME_TAG = "propertyName";
    public static final String CRM_BANNER_REGEX_COMPUTED_VARIABLE_TAG = "variableName";
    public static final String CRM_BANNER_REGEX_FUNCTIONAL_TAGS_TAG = "functionDefinition";
    public static final String CRM_BANNER_REGEX_FOR_RASHI_ATTRIBUTE = String.format("\\{\\{\\{___(?<%s>.*?)___}}}", CRM_BANNER_REGEX_ATTRIBUTE_NAME_TAG); // Regex: {{{___<attributeName>___}}}
    public static final String CRM_BANNER_REGEX_FOR_EVENT_PROPERTY = String.format("\\{\\{\\{_\\$_(?<%s>.*?)_\\$_}}}", CRM_BANNER_REGEX_EVENT_PROPERTY_NAME_TAG); // Regex: {{{_$_<propertyName>_$_}}}
    public static final String CRM_BANNER_REGEX_FOR_FUNCTIONAL_TAGS = String.format("\\$\\$\\$(?<%s>.*?)\\$\\$\\$", CRM_BANNER_REGEX_FUNCTIONAL_TAGS_TAG); // Regex: $$$<functionName>.*$$$
    public static final String CRM_BANNER_REGEX_FOR_COMPUTED_VARIABLE = String.format("\\#\\#\\#(?<%s>.*?)\\#\\#\\#", CRM_BANNER_REGEX_COMPUTED_VARIABLE_TAG); // Regex: ###<propertyName>###
    public static final String CRM_BANNER_COMPUTED_SKU_PER_MONTH_PRICE = "perMonthPrice";
    public static final String CRM_BANNER_FUNCTION_TIME_DIFFERENCE = "GETTIMEDIFF";
    public static final String CRM_BANNER_FUNCTION_INDEX_VALUE = "GETINDEX";
    public static final String CRM_BANNER_FUNCTION_EPOCH_TO_DATE = "EPOCHTODATE";
    public static final String CRM_BANNER_FUNCTION_SUM = "SUM";
    public static final String CRM_BANNER_FUNCTION_MINUS = "MINUS";
    public static final String CRM_BANNER_FUNCTION_PRODUCT = "PRODUCT";
    public static final String CRM_BANNER_FUNCTION_DIVIDE = "DIVIDE";
    public static final List<String> CRM_BANNER_FUNCTIONAL_TAGS_FUNCTIONS = Arrays.asList(CRM_BANNER_FUNCTION_TIME_DIFFERENCE, CRM_BANNER_FUNCTION_INDEX_VALUE, CRM_BANNER_FUNCTION_EPOCH_TO_DATE, CRM_BANNER_FUNCTION_SUM, CRM_BANNER_FUNCTION_MINUS, CRM_BANNER_FUNCTION_PRODUCT, CRM_BANNER_FUNCTION_DIVIDE);

    public static final String JOURNEY_EVENT_ID = "journeyEventId";

    public static final String WIDGET_ID = "widgetId";
    public static final String TRANSFORM_SPONSOR_WIDGET_ID = "270ea8da-de4c-4517-9391-ade31865e4bd-testing";

    public static final String CENTRUM_HIGHLIGHTS = "68a00b33-1624-4629-9bb7-db5dae448b72";

    public static final String DATALAKE_ANALYTICS_KEY_EXPERIMENT = "experiment";

    public static final String DATALAKE_ANALYTICS_KEY_SEGMENTS = "segments";

    public static final String RASHI_ATTRIBUTE_FOR_ENERGY_STREAK = "energy_streak";
    public static final String RASHI_ATTRIBUTE_FOR_ENERGY_STREAK_COMPLETION = "energy_streak_completed";
    public static final String RASHI_ATTRIBUTE_FOR_CULTPACK_START_DATE = "cultpackstartdate";
    public static final String ENERGY_STREAK_TSHIRT_SEGMENT = "M1 Game Pilot Tshirt Users Only";
    public static final int SECONDS_IN_ONE_DAY = 24 * 60 * 60;

    public static final String RASHI_ATTRIBUTE_FOR_ENERGY_LEVEL_V2 = "energy_streak_v2_level";
    public static final Long HABIT_GAME_LAST_WEEK = 4L;
    public static final Long HIGHEST_GAME_LEVEL = 12L;
    public static final Integer HABIT_GAME_ACTIVE_DAY_THRESHOLD = 3;
    public static final String RASHI_ATTRIBUTE_FOR_USER_GAME_ID = "energy_streak_game_id";
    public static final String RASHI_ATTRIBUTE_FOR_ENERGY_STREAK_COMPLETION_V2 = "energy_streak_completed_v2";
    public static final String FEATURE_STATE_CACHE_GYM_PROGRESSION_EXP_AUTO_LAUNCH_BASE = "gym_progression_exp_launch_";

    public static final String RASHI_ATTRIBUTE_FOR_ENERGY_STREAK_COMPLETION_BASE = "energy_streak_completed_v";

    public static final String FEATURE_STATE_CACHE_ENERGY_STREAK_ONBOARDING_BASE = "energy_streak_onboarding_v";
    public static final String FEATURE_STATE_CACHE_ENERGY_STREAK_SLIPPED_BASE = "energy_streak_slipped_v";
    public static final String FEATURE_STATE_CACHE_ENERGY_STREAK_SLIPPING_BASE = "energy_streak_slipping_v";


    public static final String RASHI_ATTRIBUTE_FOR_WORKOUTS_DONE_THIS_WEEK = "workouts done this week";

    public static final String RASHI_ATTRIBUTE_FOR_USER_PLEDGE = "USER_PLEDGE_SIGNUP_2023";

    public static final String RASHI_ATTRIBUTE_FOR_USER_PLEDGE_NEW = "pledge";

    public static final String RASHI_ATTRIBUTE_FOR_PERSONAL_BEST_WEIGHT_LOGGED = "personal_best_weight_logged";

    public static final String RASHI_ATTRIBUTE_FOR_GYM_WEIGHT_LOGGED = "gym_weight_logged";

    public static final String RASHI_ATTRIBUTE_FOR_USER_WEEKLY_ACTIVE_DAYS = "weekly_active_days";

    public static final String RASHI_ATTRIBUTE_FOR_USER_WEEKLY_ACTIVE_DAYS_UPDATED_DATE = "weekly_active_days_updated_date";

    public static final String RASHI_ATTRIBUTE_FOR_FAV_FORMAT_2023 = "yearly_favourite_format_2023";
    public static final String RASHI_ATTRIBUTE_FOR_GYM_GOAL_2023 = "user_gym_fitness_goal_2023";

    public static final String YER_2023_GAME_ID = "4";

    public static final String ENERGY_STREAK_KEYS_DEPENDENT_ON_GAME_ID_CUTOFF = "4";

    public static final String ENERGY_STREAK_M1_HABIT_GAME = "energy_streak_m1_habit_game";
    public static final String KARMA_KUDOS_ONBOARDING_KEY = "karma_kudos_onboarding_key";

    public static final String HABIT_BUILDING_WEEK_ATTRIBUTE = "habit_building_week";
    public static final String HABIT_BUILDING_WEEK_ATTRIBUTE_V2 = "habit_building_week_v2";
    public static final String MULTIPLAYER_GAME_COMPLETION_TIME = "multiplayer_game_completion_time";
    public static final String RASHI_ATTRIBUTE_FOR_MULTIPLAYER_COMPLETION = "multiplayer_game_completed";

    public static final String HABIT_BUILDING_COMPLETION_TIME = "habit_building_completion_time";
    public static final String HABIT_BUILDING_LAST_OPERATION = "habit_building_last_operation";
    public static final String CURRENT_WEEK_ACTIVE_DAYS_ATTRIBUTES = "current_week_active_days";
    public static final String LAST_ACTIVITY_DATE_ATTRIBUTE = "last_activity_date";
    public static final String HABIT_IS_START_WEEK = "is_habit_start_week";
    public static final String HABIT_BUILDING_OPERATION = "habit_building_operation";
    public static final String GLUCO_RX_CGM_SELF_INSTALLATION_VIDEO_URL = "https://cdn-videos.cure.fit/www-curefit-com/video/upload/image/chroniccare/gluco-rx-installation.mp4";
    public static final String GET_TRANSITION_DETAILS_PAGE_VIEW = "curefit://get_transition_detail?membershipServiceId=";

    public static final String ELITE_PACK_BANNER = "/image/banners/cult/upgrade_bk_elite.png";
    public static final String ELITE_SELECT_BANNER = "/image/banners/cult/elite_select_bg.png";
    public static final String ELITE_PLUS_PACK_BANNER = "image/banners/cult/elite_plus_bg.png";
    public static final String PRO_PACK_BANNER = "/image/banners/cult/upgrade_bk_pro.png";
    public static final String PRO_SELECT_BANNER = "/image/banners/cult/pro_select_bg.png";
    public static final String PRO_PLUS_PACK_BANNER = "image/banners/cult/upgrade_bk_select.png";

    public static final String INFO_ICON = "/image/membership/info.png";
    public static final String CLASS_BOOKING_PAGE = "curefit://classbookingv2";
    public static final String CONFIRMATION_PAGE_V1 = "curefit://orderconfirmationv1";
    public static final String HOMETAB = "curefit://hometab";
    public static final String NEW_BADGE_PAGE = "curefit://new_badge_award";
    public final static String FLUTTER_REFERRAL_PAGE_DEEPLINK = "curefit://fl_referral_page";

    // @TODO: to move to cult-api and expose as client method and API
    public static final List<Long> CULT_PILATES_WORKOUT_IDS = List.of(483L, 484L, 485L, 489L, 490L, 494L, 495L);

    public static final List<String> RASHI_ATTRIBUTES_FOR_HABIT_BUILDING = Arrays.asList(
            HABIT_BUILDING_WEEK_ATTRIBUTE, CURRENT_WEEK_ACTIVE_DAYS_ATTRIBUTES, LAST_ACTIVITY_DATE_ATTRIBUTE, HABIT_IS_START_WEEK, HABIT_BUILDING_OPERATION, HABIT_BUILDING_COMPLETION_TIME, HABIT_BUILDING_LAST_OPERATION
    );
    public static final String CHECKED_IN_USER_SEGMENT_NAME = "Checked In Users";

    public static final List<String> FRONT_BODY_PARTS = Arrays.asList("ABS", "CHEST", "CORE", "FULL_BODY", "BICEPS", "FOREARMS");

    public static final int CULT_PACK_DEFAULT_IMAGE_VERSION = 80;

    public static final List<String> BACK_BODY_PARTS = Arrays.asList("BACK", "GLUTES", "LEGS", "SHOULDERS", "TRICEPS");

    public static final Long USER_PREFERENCE_ID_MY_EXERCISES = 39L;
    public static final Long TENANT_ID_MY_EXERCISES = 11L;

    public static final Integer MAX_TRIAL_COUNT_EXPERIMENT = 2;

    public static final Integer MAX_TRIAL_COUNT = 2;

    public static final Integer GX_SCHEDULE_BUFFER = 4;
    public static final String ORDER_CONFIRMATION_PAGE = "curefit://fl_orderconfirmation";
    public static final String CLASSBOOKING_AUDIO = "image/mem-exp/class_success.mp3";

    public static final String TEST_USERS_SEGMENT_FOR_TEST_CENTERS = "test_user_segment";

    public static final String REFERRAL_CODE_USER_ATTRIBUTE_KEY = "referral_link_code";

    public static final String[] WHITELISTED_BODY_PART_IDS_NEW = {
            "5b07954842e13c066120aedc",
            "5da57b4090480a000a5d64e6",
            "5b0794ad42e13c066120aed7",
            "5b07943ec3947d289fc4ab41",
            "5b176ec2aa146d3b909460ed"
    };

    public static final String[] WHITELISTED_BODY_PART_IDS = {
            "5b07954842e13c066120aedc",
            "5b06a7fec3947d289fc4ab40",
            "5da57b4090480a000a5d64e6",
            "5b0794ad42e13c066120aed7",
            "5b06a80642e13c066120aed5",
            "5b079498c3947d289fc4ab42",
            "5b06b48d42e13c066120aed6",
            "5b163f2cc92b8c5468c0e9aa",
            "5b07943ec3947d289fc4ab41",
            "5b164a28aa146d3b909460c4",
            "5b0794c642e13c066120aed8",
            "5b176ec2aa146d3b909460ed"
    };

    public static String OLD_TEMPLATE_ID_PARAM_NAME = "templatePackId";
    public static String PMS_TEMPLATE_ID_PARAM_NAME = "pmsTemplateId";

    public static final String[] FOUNDATIONAL_EXERCISE_IDS = {
            "60af65d9804e1900082e7dc2",
            "60af65d5804e1900082e7852",
            "60af65d4804e1900082e7743",
            "627a1d1fb14d894a97dfd479",
            "5b0e6f7842e13c066120b0cc"
    };

    public static final ImmutableMap<String, String> MUSCLE_GROUP_ID_MAPPING = ImmutableMap.<String, String>builder()
            .put("5b07943ec3947d289fc4ab41", "BACK")
            .put("5b0794ad42e13c066120aed7", "CHEST")
            .put("5da57b4090480a000a5d64e6", "LEGS")
            .put("5b176ec2aa146d3b909460ed", "ARMS")
            .put("5b07954842e13c066120aedc", "SHOULDERS")
            .build();
    public static String[] CHEST = {
            "60af65d9804e1900082e7dc2",
            "60af65d6804e1900082e7a0a",
            "60af65d6804e1900082e7a5c"
    };

    public static String[] BACK = {
            "5ad435c28f97dc5a55523f24",
            "60af65d5804e1900082e7852",
            "5b0cf50542e13c066120af72"
    };

    public static String[] SHOULDERS = {
            "5ae97b4c8f97dc5a555245ac",
            "60af667f804e1900082e7f79",
            "60af65d8804e1900082e7bd8"
    };

    public static String[] LEGS = {
            "60af65d4804e1900082e7743",
            "60af65d4804e1900082e775c",
            "60af667d804e1900082e7eaa"
    };

    public static String[] ARMS = {
            "60af667e804e1900082e7f5b",
            "60af65d8804e1900082e7cae",
            "60af65d7804e1900082e7af4"
    };

    public static final Map<String, Map<String, Double>> MUSCLE_QUADRANT_VECTOR_MAP = Map.of(
            "TOP_LEFT", Map.of(
                    "x", 0.0,
                    "y", 0.0
            ),
            "TOP_RIGHT", Map.of(
                    "x", -74.0,
                    "y", 0.0
            ),
            "BOTTOM_LEFT", Map.of(
                    "x", 0.0,
                    "y", -70.0
            ),
            "BOTTOM_RIGHT", Map.of(
                "x", -74.0,
                "y", -60.0
            )
    );

    public static final ImmutableMap<String, String> MUSCLE_ANIMATION_QUADRANT_MAP = ImmutableMap.<String, String>builder()
            .put("BACK", "TOP_RIGHT")
            .put("BICEPS", "")
            .put("CHEST", "TOP_LEFT")
            .put("LOWER_BACK", "TOP_RIGHT")
            .put("CORE", "")
            .put("FOREARMS", "")
            .put("GLUTES", "BOTTOM_RIGHT")
            .put("LEGS", "")
            .put("QUADS", "BOTTOM_LEFT")
            .put("SHOULDERS", "")
            .put("TRICEPS", "TOP_RIGHT")
            .put("HAMSTRINGS", "BOTTOM_RIGHT")
            .put("TRAPS", "TOP_RIGHT")
            .build();
    
    public static final Map<String, Integer> CULT_UNBOUND_PAGE_ID_TO_WORKOUT_ID_MAP = Map.ofEntries(
            Map.entry("unbound_men_open", 460),
            Map.entry("unbound_men_pro", 461),
            Map.entry("unbound_women_pro", 463),
            Map.entry("unbound_women_open", 462),
            Map.entry("unbound_champ_l2_men_single", 477),
            Map.entry("unbound_champ_l2_women_single", 479),
            Map.entry("unbound_champ_l2_men_double", 481),
            Map.entry("unbound_champ_l2_women_double", 482),
            Map.entry("unbound_champ_l2_mixed_double", 488),
            Map.entry("unbound_champ_l2_men_single_lite", 486),
            Map.entry("unbound_champ_l2_women_single_lite", 487)
    );

    public static final List<UserFormAutoLaunchConfig> userFormAutoLaunchConfigList = Arrays.asList(
            UserFormAutoLaunchConfig.builder().segmentIdIn("nps journey elite pro d30").segmentIdNotIn("Cultpass continuous survey submitted").formId("NPS_V3_GX_GYM").deepLink("curefit://nps_bottom_tray?isBottomTray=true&source=POPUP&formId=NPS_V3_GX_GYM").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("CSI Auto trigger Play").segmentIdNotIn("Cultpass continuous survey submitted").formId("NPS_V3_PLAY").deepLink("curefit://nps_bottom_tray?isBottomTray=true&source=POPUP&formId=NPS_V3_PLAY").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("CSI Auto trigger PT").segmentIdNotIn("Cultpass continuous survey submitted").formId("NPS_V3_GYM_PT").deepLink("curefit://nps_bottom_tray?isBottomTray=true&source=POPUP&formId=NPS_V3_GYM_PT").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("CSI Auto trigger Live").segmentIdNotIn("Cultpass continuous survey submitted").formId("NPS_V3_LIVE").deepLink("curefit://nps_bottom_tray?isBottomTray=true&source=POPUP&formId=NPS_V3_LIVE").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("CSI Auto trigger Bootcamp").segmentIdNotIn("Cultpass continuous survey submitted").formId("NPS_V3_BOOTCAMP").deepLink("curefit://nps_bottom_tray?isBottomTray=true&source=POPUP&formId=NPS_V3_BOOTCAMP").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("CSI Auto trigger TRANSFORM Plus").segmentIdNotIn("Cultpass continuous survey submitted").formId("NPS_V3_TransformPlus").deepLink("curefit://nps_bottom_tray?isBottomTray=true&source=POPUP&formId=NPS_V3_TransformPlus").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("CSI Auto trigger TRANSFORM").segmentIdNotIn("Cultpass continuous survey submitted").formId("NPS_V3_TRANSFORM").deepLink("curefit://nps_bottom_tray?isBottomTray=true&source=POPUP&formId=NPS_V3_TRANSFORM").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("CSI Auto trigger GX").segmentIdNotIn("Cultpass continuous survey submitted").formId("d15-gx-survey").deepLink("curefit://fl_form?formId=d15-gx-survey").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("CSI Auto trigger gym/hybrid").segmentIdNotIn("Cultpass continuous survey submitted").formId("d15-gym-survey").deepLink("curefit://fl_form?formId=d15-gym-survey").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("GX 1st class survey for new users").formId("gx_1st_class_survey").deepLink("curefit://fl_form?formId=gx_1st_class_survey").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("Gym Onboarding Both Session Feedback Through CFS Dec 2024").formId("gym_onboarding_cfs_feedback_form").deepLink("curefit://fl_form?formId=gym_onboarding_cfs_feedback_form").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("Pilates_NPS Entry Segment").formId("NPS_V3_PILATES").deepLink("curefit://fl_form?formId=NPS_V3_PILATES").build(),
            UserFormAutoLaunchConfig.builder().segmentIdIn("Pilates_PT users_NPS_Entry segment").formId("NPS_V3_PILATES_PT").deepLink("curefit://fl_form?formId=NPS_V3_PILATES_PT").build()
    );
}
