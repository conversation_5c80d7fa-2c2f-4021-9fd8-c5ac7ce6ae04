package com.curefit.cfapi.repository;
import com.curefit.cfapi.model.mongo.CultUnboundLocalityConfig;
import com.curefit.commons.sf.repository.BaseMongoRepository;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import java.util.concurrent.CompletableFuture;

@Repository
public interface CultUnboundLocalityConfigRepository extends BaseMongoRepository<CultUnboundLocalityConfig> {

    @Async
    CompletableFuture<CultUnboundLocalityConfig> findByPageId(String pageId);
}
