package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.ConditionalField;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.sugarfit.challenges.pojo.CoachLeaderboard;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PlatformSegmentEvaluator extends BaseEvaluatorImpl {

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.PLATFORM_SEGMENTS;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        List<String> platformSegments = segment.getPlatformSegments();
        return platformSegments != null && !platformSegments.isEmpty();
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(segment.getPlatformSegments())) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.BELONGS_TO_ANY);
            evaluatorRequest.setValues((List<String>) segment.getPlatformSegments());
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).toList();
            for (ConditionalField conditionalField : conditionalFields) {
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), (List<String>) conditionalField.getValue(), userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    // TODO: rewrite
    @Override
    List<String> getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        SegmentSet<String> userPlatformSegments = userContext.getRequestCache() == null
                ? null
                : (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
        Set<String> eligibleValues = userPlatformSegments != null ? userPlatformSegments.getRelevantEntries(new HashSet<>(values)) : Collections.emptySet();
        return CollectionUtils.isNotEmpty(eligibleValues) ? new ArrayList<>(eligibleValues) :
                Collections.emptyList();
    }

    @Override
    Collection getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }

}
