package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.ConditionalField;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.service.FoodMarketplace;
import com.curefit.product.enums.ProductType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ProductTypeEvaluator extends BaseEvaluatorImpl {

    final private FoodMarketplace foodMarketplace;

    @Autowired
    ProductTypeEvaluator(
           FoodMarketplace foodMarketplace
    ) {
        this.foodMarketplace = foodMarketplace;
    }

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.PRODUCT_TYPE;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return CollectionUtils.isNotEmpty(segment.getProductType());
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(segment.getProductType())) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.BELONGS_TO_ANY);
            evaluatorRequest.setValues((List<ProductType>) segment.getProductType());
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).collect(Collectors.toList());
            for (ConditionalField conditionalField : conditionalFields) {
                List<String> values = (List<String>) conditionalField.getValue();
                List<ProductType> eligibleProductTypes =  values.stream().map(value -> ProductType.valueOf(value)).collect(Collectors.toList());
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), eligibleProductTypes, userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    List<ProductType> getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        return userContext.getUserProfile().getCity().getAvailableOfferings().stream().filter(productType -> {
                    return this.checkProductTypeEligibilityRequirements(productType, userContext);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    List<ProductType> getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }

    public Boolean checkProductTypeEligibilityRequirements(ProductType productType, UserContext userContext) {
        switch (productType) {
            case FOOD_MARKETPLACE:
                return foodMarketplace.getFoodMarketplacePresence(userContext);
            default:
                return true;
        }
    }
}
