package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.ConditionalField;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.service.ApiKeyService;
import com.curefit.cfapi.util.AppUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SourceEvaluator extends BaseEvaluatorImpl {

    private ApiKeyService apiKeyService;

    @Autowired
    public SourceEvaluator(ApiKeyService apiKeyService) {
        this.apiKeyService = apiKeyService;
    }

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.SOURCES;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return CollectionUtils.isNotEmpty(segment.getSources());
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {

        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(segment.getSources())) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.BELONGS_TO_ANY);
            evaluatorRequest.setValues((List<String>) segment.getSources());
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).collect(Collectors.toList());
            for (ConditionalField conditionalField : conditionalFields) {
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), (List<String>) conditionalField.getValue(), userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    List<String> getUserEligibleValues(UserContext userContext, Collection values) {
        return Collections.singletonList(AppUtil.callSource(userContext.getSessionInfo().getApiKey(), apiKeyService));
    }

    @Override
    List<String> getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
