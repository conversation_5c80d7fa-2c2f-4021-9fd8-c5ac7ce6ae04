package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.util.MathUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Service
public class MaxAppVersionEvaluator extends BaseEvaluatorImpl {

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.SUPPORTED_MAX_APP_VERSION;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return StringUtils.isNotEmpty(segment.getMaxSupportedAppVersion());
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
        evaluatorRequest.setType(ConditionType.MAXIMUM);
        evaluatorRequest.setValues(Collections.singletonList(Float.valueOf(segment.getMaxSupportedAppVersion())));
        evaluatorRequest.setUserContext(userContext);
        return Collections.singletonList(evaluatorRequest);
    }

    @Override
    List<Float> getUserEligibleValues(UserContext userContext, Collection values) {
        return Collections.singletonList(MathUtil.round(userContext.getSessionInfo().getClientVersion(), 2));
    }

    @Override
    List<Float> getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
