package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.model.internal.segment.*;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.commons.util.Serializer;
import com.curefit.hamlet.models.pojo.UserAssignment;
import com.curefit.hamlet.models.response.UserAllocation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;

@Service
@Slf4j
public class HamletEvaluator extends BaseEvaluatorImpl {

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.HAMLET;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return segment.getHamlet() != null;
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (segment.getHamlet() != null) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.BELONGS_TO_ANY);
            evaluatorRequest.setValues(Collections.singleton(segment.getHamlet()));
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(f -> f.getField().equals(getType().toString())).toList();
            for (ConditionalField conditionalField : conditionalFields) {
                try {
                    conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), Collections.singletonList(Serializer.deserialize(Serializer.serialize(conditionalField.getValue()), Hamlet.class)), userContext));
                } catch (Exception e) {
                    log.error("error in serializing hamlet object in conditional fields for value: " + conditionalField.getValue());
                }
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    List<Hamlet> getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        Future<UserAllocation> hamletExperimentPromise = userContext.getUserProfile().getHamletUserExperimentPromise();
        UserAllocation userAllocation =  hamletExperimentPromise != null ? hamletExperimentPromise.get(): null;
        Map<String, UserAssignment> assignmentsMap = userAllocation == null ? Collections.emptyMap() : userAllocation.getAssignmentsMap();
        List<Hamlet> experiments = new ArrayList<>();
        for (Map.Entry<String, UserAssignment> assignmentEntry : assignmentsMap.entrySet()) {
            experiments.add(new Hamlet(assignmentEntry.getKey(), assignmentEntry.getValue().getBucket().getBucketId()));
        }
        return experiments;
    }

    @Override
    List<Hamlet> getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
