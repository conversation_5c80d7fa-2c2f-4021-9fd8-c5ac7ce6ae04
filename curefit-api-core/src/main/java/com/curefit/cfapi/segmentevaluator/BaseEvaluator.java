package com.curefit.cfapi.segmentevaluator;


import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.model.internal.userinfo.UserContext;


import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface BaseEvaluator {
    public CompletableFuture<Boolean> checkCondition(ConditionEvaluatorRequest evaluatorRequest, Segment segment) throws Exception;
    public EvaluatorType getType();
    public boolean isEligibleForSegment(Segment segment);
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext);
}
