package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.builder.vm.RequestCache;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.commons.util.Serializer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

@Slf4j
@AllArgsConstructor
@Service
public abstract class BaseEvaluatorImpl implements BaseEvaluator {

    public abstract EvaluatorType getType();

    public abstract boolean isEligibleForSegment(Segment segment);

    abstract Collection getUserEligibleValues(UserContext userContext, Collection values) throws Exception;

    abstract Collection getOverriddenValue(UserContext userContext, Collection values) throws Exception;

    public CompletableFuture<Boolean> checkCondition(ConditionEvaluatorRequest evaluatorRequest, Segment segment) throws Exception {
        if (CollectionUtils.isEmpty(evaluatorRequest.getValues())) {
            return CompletableFuture.completedFuture(true);
        }
        Collection userEligibleValues = this.getUserEligibleValues(evaluatorRequest.getUserContext(), evaluatorRequest.getValues());

        // TODO: Add hashcode and equals for all values
        if (userEligibleValues == null) {
            userEligibleValues = CollectionUtils.EMPTY_COLLECTION;
        }
        Collection intersectionValues = CollectionUtils.intersection(evaluatorRequest.getValues(), userEligibleValues);
        boolean result = false;
        switch (evaluatorRequest.getType()) {
            case MINIMUM:
                if (CollectionUtils.isNotEmpty(userEligibleValues) && CollectionUtils.isNotEmpty(evaluatorRequest.getValues())) {
                    Float userValue = (Float) userEligibleValues.toArray()[0];
                    Float segmentValue = (Float) evaluatorRequest.getValues().toArray()[0];
                    result = userValue.compareTo(segmentValue) >= 0;
                }
                break;
            case MAXIMUM:
                if (CollectionUtils.isNotEmpty(userEligibleValues) && CollectionUtils.isNotEmpty(evaluatorRequest.getValues())) {
                    Float userValue = (Float) userEligibleValues.toArray()[0];
                    Float segmentValue = (Float) evaluatorRequest.getValues().toArray()[0];
                    result = userValue.compareTo(segmentValue) <= 0;
                }
                break;
            case BELONGS_TO_ALL:
                result = intersectionValues.size() == evaluatorRequest.getValues().size();
                break;
            case BELONGS_TO_ANY:
                result = intersectionValues.size() > 0;
                break;
            case NOT_BELONGS_TO_ALL:
                result = intersectionValues.size() == 0;
                break;
            case NOT_BELONGS_TO_ANY:
                result = intersectionValues.size() != evaluatorRequest.getValues().size();
                break;
        }
        return CompletableFuture.completedFuture(result);

    }
}
