
package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.cache.CSProductCatalogueCacheService;
import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.ConditionalField;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.mongo.Segment;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CultsportCollectionSlugEvaluator extends BaseEvaluatorImpl {
    @Autowired
    @Lazy
    CSProductCatalogueCacheService csProductCatalogueCacheService;

    @Autowired
    CultsportCollectionSlugEvaluator(
    ) {}

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.CULTSPORT_COLLECTION_SLUG_SEGMENTS;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return CollectionUtils.isNotEmpty(segment.getCultsportCollectionSlugSegments());
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(segment.getCultsportCollectionSlugSegments())) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.BELONGS_TO_ANY);
            evaluatorRequest.setValues((List<String>) segment.getCultsportCollectionSlugSegments());
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).collect(Collectors.toList());
            for (ConditionalField conditionalField : conditionalFields) {
                List<String> values = (List<String>)conditionalField.getValue();
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), values, userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    List<String> getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        if (userContext.getRequestQueryParams() != null && StringUtils.isNotEmpty(userContext.getRequestQueryParams().get("collection-slug"))) {
            return List.of(userContext.getRequestQueryParams().get("collection-slug"));
        }
        return Collections.emptyList();
    }

    @Override
    List<String> getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
