package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.ConditionalField;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CodePushVersionEvaluator extends BaseEvaluatorImpl {

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.SUPPORTED_CODEPUSH_VERSION;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return StringUtils.isNotEmpty(segment.getMinCodepushVersion());
    }


    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (StringUtils.isNotEmpty(segment.getMinCodepushVersion())) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.MINIMUM);
            evaluatorRequest.setValues(Collections.singletonList(Float.valueOf(segment.getMinCodepushVersion())));
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).collect(Collectors.toList());
            for (ConditionalField conditionalField : conditionalFields) {
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), Collections.singletonList(Float.valueOf((String) conditionalField.getValue())), userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    List<Float> getUserEligibleValues(UserContext userContext, Collection values) {
        if (userContext.getSessionInfo().getCpVersion() != null) {
            return Collections.singletonList(userContext.getSessionInfo().getCpVersion());
        } else {
            return Collections.emptyList();
        }
    }

    @Override
    List<Float> getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
