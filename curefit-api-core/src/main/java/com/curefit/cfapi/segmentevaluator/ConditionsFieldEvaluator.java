package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ConditionsFieldEvaluator extends BaseEvaluatorImpl {

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.CONDITIONAL_FIELDS;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        if (CollectionUtils.isEmpty(segment.getConditionalFields())) return false;
        return CollectionUtils.isNotEmpty(segment.getConditionalFields().stream().filter(conditionalField -> conditionalField.getValue() != null).collect(Collectors.toList()));
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        return null;
    }

    @Override
    Collection getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        return null;
    }

    @Override
    Collection getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
