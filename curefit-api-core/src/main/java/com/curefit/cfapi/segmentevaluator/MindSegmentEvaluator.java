package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.segment.*;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.pojo.base.Vertical;
import com.curefit.cfapi.util.CultUtil;
import com.curefit.cult.models.CultSummary;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MindSegmentEvaluator extends BaseEvaluatorImpl {
    @Override
    public EvaluatorType getType() {
        return EvaluatorType.MIND_SEGMENTS;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return CollectionUtils.isNotEmpty(segment.getMindUserTypes());
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(segment.getMindUserTypes())) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.BELONGS_TO_ANY);
            evaluatorRequest.setValues(segment.getMindUserTypes());
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).collect(Collectors.toList());
            for (ConditionalField conditionalField : conditionalFields) {
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), (List<String>) conditionalField.getValue(), userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    Collection getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        CultSummary summary = (CultSummary) userContext.getRequestCache().getRequestFuture(RequestType.CULT_MIND_SUMMARY, userContext).get();
        if (summary == null) {
            return Collections.emptyList();
        }
        List<CultUserType> cultUserTypes = CultUtil.getCultUserTypes(userContext, summary, Vertical.MIND_FIT);
        return cultUserTypes.stream().map(CultUserType::toString).collect(Collectors.toList());
    }

    @Override
    Collection getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
