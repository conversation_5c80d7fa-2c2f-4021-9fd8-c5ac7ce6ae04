package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.ConditionalField;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.model.mongo.csBlog.CSBlogArticle;
import com.curefit.cfapi.service.cultsport.csBlog.CSBlogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.utils.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CSBlogCategorySegmentEvaluator extends BaseEvaluatorImpl {
    @Autowired
    CSBlogService csBlogService;

    @Autowired
    CSBlogCategorySegmentEvaluator(
            CSBlogService csBlogService
    ) {
    }

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.CULTSPORT_BLOG_CATEGORY_SEGMENTS;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return CollectionUtils.isNotEmpty(segment.getCultsportBlogCategorySegments());
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(segment.getCultsportBlogCategorySegments())) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.BELONGS_TO_ANY);
            evaluatorRequest.setValues((List<String>) segment.getCultsportBlogCategorySegments());
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).collect(Collectors.toList());
            for (ConditionalField conditionalField : conditionalFields) {
                List<String> values = (List<String>)conditionalField.getValue();
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), values, userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    List<String> getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        if (userContext.getRequestQueryParams() != null && StringUtils.isNotBlank(userContext.getRequestQueryParams().get("articleSlug"))) {

            CSBlogArticle article = (CSBlogArticle) userContext.getRequestCache().getRequestFuture(RequestType.GET_CS_BLOG_ARTICLE, userContext, userContext.getRequestQueryParams().get("articleSlug")).get();
            if ( article != null && StringUtils.isNotBlank(article.getCategory())) {
                return List.of(article.getCategory());
            }
        }
        return Collections.emptyList();
    }

    @Override
    List<String> getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}

