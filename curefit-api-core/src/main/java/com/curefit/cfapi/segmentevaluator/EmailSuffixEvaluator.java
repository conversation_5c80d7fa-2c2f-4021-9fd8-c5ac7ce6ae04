package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.ConditionalField;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.userservice.pojo.entry.UserEntry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EmailSuffixEvaluator extends BaseEvaluatorImpl {

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.EMAIL_SUFFIX;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return CollectionUtils.isNotEmpty(segment.getEmailSuffixes()) || CollectionUtils.isNotEmpty(segment.getExcludeEmailSuffixes());
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(segment.getEmailSuffixes())) {
            conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(ConditionType.BELONGS_TO_ANY, segment.getEmailSuffixes(), userContext));
        }
        if (CollectionUtils.isNotEmpty(segment.getExcludeEmailSuffixes())) {
            conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(ConditionType.NOT_BELONGS_TO_ALL, segment.getExcludeEmailSuffixes(), userContext));
        }

        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).collect(Collectors.toList());
            for (ConditionalField conditionalField : conditionalFields) {
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), (List<String>) conditionalField.getValue(), userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    List<String> getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        List<String> emailSuffixes = new ArrayList<>();
        if (StringUtils.isNotEmpty(user.getEmail())) {
            emailSuffixes.add(user.getEmail().substring(user.getEmail().indexOf("@") + 1));
        }
        if (StringUtils.isNotEmpty(user.getWorkEmail())) {
            emailSuffixes.add(user.getWorkEmail().substring(user.getWorkEmail().indexOf("@") + 1));
        }
        return emailSuffixes;
    }

    @Override
    List<String> getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
