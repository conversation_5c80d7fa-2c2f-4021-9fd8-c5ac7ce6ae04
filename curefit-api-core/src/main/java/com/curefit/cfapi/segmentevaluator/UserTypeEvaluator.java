package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.model.internal.segment.*;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.userservice.pojo.entry.UserEntry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;

import static com.curefit.cfapi.model.internal.segment.EvaluatorType.USER_TYPE;

@Slf4j
@Service
public class UserTypeEvaluator extends BaseEvaluatorImpl {
    @Override
    public EvaluatorType getType() {
        return USER_TYPE;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return CollectionUtils.isNotEmpty(segment.getUserType());
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(segment.getUserType())) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.BELONGS_TO_ANY);
            evaluatorRequest.setValues((List<String>) segment.getUserType());
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).collect(Collectors.toList());
            for (ConditionalField conditionalField : conditionalFields) {
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), (List<String>) conditionalField.getValue(), userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    Collection getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        List<String> userTypes = new ArrayList<>();
        try {
            if (BooleanUtils.isFalse(userContext.getSessionInfo().getIsUserLoggedIn())) {
                userTypes.add(UserType.NOT_LOGGED_IN.toString());
                return userTypes;
            }
            UserEntry user = userContext.getUserEntryCompletableFuture().get();
            LocalDateTime localDateTime = TimeUtil.getLocalDateTimeFromDate(user.getCreatedOn(), TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()));
            LocalDateTime oneDayBefore = LocalDateTime.now().atZone(ZoneId.of(userContext.getUserProfile().getTimezone())).minus(1, ChronoUnit.DAYS).toLocalDateTime();
            if (localDateTime.isAfter(oneDayBefore) && BooleanUtils.isTrue(userContext.getSessionInfo().getIsUserLoggedIn())) {
                userTypes.add(UserType.REGISTERED_IN_24_HOURS.toString());
                return userTypes;
            }
        } catch (Exception e) {
            log.error("Error in calculating userType segment", e);
        }
        return userTypes;
    }

    @Override
    Collection getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
