package com.curefit.cfapi.segmentevaluator;

import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.ConditionalField;
import com.curefit.cfapi.model.internal.segment.EvaluatorType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.SegmentTestOverride;
import com.curefit.cfapi.model.mongo.Segment;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CitiesEvaluator extends BaseEvaluatorImpl {

    @Override
    public EvaluatorType getType() {
        return EvaluatorType.CITIES;
    }

    @Override
    public boolean isEligibleForSegment(Segment segment) {
        return CollectionUtils.isNotEmpty(segment.getCityIds());
    }

    @Override
    public List<ConditionEvaluatorRequest> getEvaluatorRequestsForUser(Segment segment, UserContext userContext) {
        List<ConditionEvaluatorRequest> conditionEvaluatorRequests = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(segment.getCityIds())) {
            ConditionEvaluatorRequest evaluatorRequest = new ConditionEvaluatorRequest();
            evaluatorRequest.setType(ConditionType.BELONGS_TO_ANY);
            evaluatorRequest.setValues((List<String>) segment.getCityIds());
            evaluatorRequest.setUserContext(userContext);
            conditionEvaluatorRequests.add(evaluatorRequest);
        }
        if (CollectionUtils.isNotEmpty(segment.getValidConditionalFields())) {
            List<ConditionalField> validConditionalFields = segment.getValidConditionalFields();
            List<ConditionalField> conditionalFields = validConditionalFields.stream().filter(conditionalField -> conditionalField.getField().equals(getType().toString())).collect(Collectors.toList());
            for (ConditionalField conditionalField : conditionalFields) {
                conditionEvaluatorRequests.add(new ConditionEvaluatorRequest(conditionalField.getCondition(), (List<String>) conditionalField.getValue(), userContext));
            }
        }
        return conditionEvaluatorRequests;
    }

    @Override
    List<String> getUserEligibleValues(UserContext userContext, Collection values) throws Exception {
        return Collections.singletonList(userContext.getUserProfile().getCity().getCityId());
    }

    @Override
    List<String> getOverriddenValue(UserContext userContext, Collection values) throws Exception {
        return this.getUserEligibleValues(userContext, values);
    }
}
