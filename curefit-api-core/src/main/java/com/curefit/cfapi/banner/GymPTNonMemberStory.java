package com.curefit.cfapi.banner;

import com.curefit.center.dtos.CenterEntry;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.banner.BannerType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.FitnessPlannerUtil;
import com.curefit.cfapi.util.GymPtUtil;
import com.curefit.cfapi.util.GymUtil;
import com.curefit.cfapi.view.viewmodels.UpcomingBannerItem;
import com.curefit.cfapi.view.viewmodels.transform.StoryItem;
import com.curefit.common.data.exception.RuntimeBaseException;
import com.curefit.identity.model.IdentityResponse;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

import static com.curefit.cfapi.constants.FitnessPlannerConstants.CENTER_RESPONSE;
import static com.curefit.cfapi.constants.FitnessPlannerConstants.IDENTITY_RESPONSE;

@Slf4j
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class GymPTNonMemberStory extends BaseBanner {

    public GymPTNonMemberStory(BannerType bannerType) {
        this.bannerType = bannerType;
    }

    @Override
    public BaseBanner buildView(
            ServiceInterfaces interfaces,
            UserContext userContext,
            WidgetContext widgetContext
    ) throws RuntimeBaseException {
        UpcomingBannerItem bannerItem = this.buildBanner(interfaces, userContext);
        if (bannerItem != null) {
            this.setStoryTextItems(bannerItem.getItems());
            this.setAction(bannerItem.getAction());
            this.setImage(bannerItem.getImageUrl());
            this.setVideoUrl(bannerItem.getVideoUrl());
            this.setPauseAutoScroll(false);
            this.setBlurItemData(bannerItem.getBlurItemData());
            return this;
        }
        return null;
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    private UpcomingBannerItem buildBanner(ServiceInterfaces interfaces, UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();

        Map<String, List<Long>> gymPtConfig = GymPtUtil.gymPtConfig;
        try {
            gymPtConfig = interfaces.appConfigCache.getConfig(GymPtUtil.GYM_PT_CENTERS_CONFIG, new TypeReference<>() {
            }, GymPtUtil.gymPtConfig);
        } catch (IOException ignored) {
        }

        if (userSegments.contains(GymPtUtil.GYM_PT_EXPIRED_D30_MEMBERS_SEGMENT)) {
            Membership ptMemberShip = GymPtUtil.getLatestPTMembership(interfaces, userContext);
            if (ptMemberShip != null) {
                if (Instant.ofEpochMilli(ptMemberShip.getEnd()).compareTo(Instant.now()) < 0) {
                    //Expired PT pack case
                    Long trainerId = Long.valueOf(ptMemberShip.getMetadata().get("preferredTrainerId").toString());
                    Long centerId = Long.valueOf(ptMemberShip.getMetadata().get("preferredCenterId").toString());
                    return expiredPTPackStory(interfaces, trainerId, centerId, gymPtConfig);
                }
            }
        } else if (userSegments.contains(GymPtUtil.GYM_PT_TRIAL_BOOKED_D30_MEMBERS_SEGMENT)) {
            // Trial Booked but cancelled case
            return null;
//            return trialCancelledStory(interfaces,userContext)
        } else {
            // Member has checked in into our gym, nudge to purchase PT
            return nudgePTPurchaseStory(interfaces, userContext, gymPtConfig);

        }
        return null;
    }

    private UpcomingBannerItem nudgePTPurchaseStory(ServiceInterfaces interfaces, UserContext userContext, Map<String, List<Long>> gymPtConfig) {
        try {
            List<CenterEntry> ptEnabledCenters = GymUtil.getAllPTEnabledCentersFromCenterService(interfaces, userContext);
            if (!ptEnabledCenters.isEmpty()) {
                CenterEntry center = GymPtUtil.getMostVisitedPTGym(userContext, ptEnabledCenters, interfaces);
                if (center != null) {
                    UpcomingBannerItem bannerItem = new UpcomingBannerItem();
                    List<StoryItem> storyItems = new ArrayList<>();

                    String centerName = center.getName();
                    StoryItem locationItem = new StoryItem();
                    locationItem.setTitle(centerName);
                    locationItem.setFontSize("P5");
                    locationItem.setColor("#FFFFFF");
                    locationItem.setIcon("location");
                    locationItem.setPaddingTop(10);
                    locationItem.setPaddingBottom(15);
                    storyItems.add(locationItem);

                    Action explorePt = new Action("curefit://pt_alltrainers?centerId=" + center.getId(), ActionType.NAVIGATION);

                    List<Long> ptPilotCenterServiceIdList = gymPtConfig.get(GymPtUtil.GYM_PT_NUTRITION_PILOT_GYM_CENTER_SERVICE_ID_LIST);

                    if (ptPilotCenterServiceIdList == null) {
                        ptPilotCenterServiceIdList = List.of(492L, 1071L, 916L);
                    }

                    if (ptPilotCenterServiceIdList.contains(center.getId())) {
                        return null;
                    } else {
                        bannerItem.setImageUrl(GymPtUtil.EXPLORE_PT_STORY_IMAGE_URL);
                        explorePt.setTitle("EXPLORE PT");
                    }
                    bannerItem.setItems(storyItems);
                    bannerItem.setAction(explorePt);
                    return bannerItem;
                }
            }
            return null;
        } catch (Exception e) {
            log.error("PT Story Error in fetching all PT gyms", e);
            return null;
        }
    }

    private UpcomingBannerItem expiredPTPackStory(ServiceInterfaces interfaces, Long trainerId, Long centerId, Map<String, List<Long>> gymPtConfig) {
        UpcomingBannerItem bannerItem = new UpcomingBannerItem();
        List<StoryItem> storyItems = new ArrayList<>();

        Map<String, Object> trainerAndCenterDetails = FitnessPlannerUtil.getTrainerNameAndCenterAsync(interfaces, trainerId, centerId);
        if (trainerAndCenterDetails == null) return null;
        IdentityResponse identityResponse = (IdentityResponse) trainerAndCenterDetails.get(IDENTITY_RESPONSE);
        CenterEntry center = (CenterEntry) trainerAndCenterDetails.get(CENTER_RESPONSE);
        String centerName = center.getName();
        String trainerName = identityResponse.getName();

        StoryItem trainerNameAndCenterItem = new StoryItem();
        trainerNameAndCenterItem.setTitle(trainerName + " • " + centerName);
        trainerNameAndCenterItem.setFontSize("P5");
        trainerNameAndCenterItem.setColor("#FFFFFF");
        trainerNameAndCenterItem.setPaddingTop(10);
        trainerNameAndCenterItem.setPaddingBottom(15);
        storyItems.add(trainerNameAndCenterItem);

        Action renewPackAction = GymPtUtil.getPickAPackAction();
        renewPackAction.setTitle("RENEW PT");

        boolean isPtPlusCenter = gymPtConfig.get(GymPtUtil.GYM_PT_NUTRITION_PILOT_GYM_CENTER_SERVICE_ID_LIST).contains(centerId);

        if (isPtPlusCenter) {
            bannerItem.setVideoUrl(GymPtUtil.PT_PLUS_EXPIRED_STORY_VIDEO_URL);
        } else {
            bannerItem.setVideoUrl(GymPtUtil.PT_EXPIRED_STORY_VIDEO_URL);
        }
        bannerItem.setItems(storyItems);
        bannerItem.setAction(renewPackAction);
        return bannerItem;
    }
}
