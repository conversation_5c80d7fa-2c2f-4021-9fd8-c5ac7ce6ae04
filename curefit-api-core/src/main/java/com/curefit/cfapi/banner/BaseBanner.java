package com.curefit.cfapi.banner;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.vm.banner.BannerType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.view.viewbuilders.*;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.common.data.exception.RuntimeBaseException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        visible = true,
        defaultImpl = BaseBanner.class,
        property = "bannerType")
@JsonSubTypes({
        @JsonSubTypes.Type(value = TransformBanner.class, name = "TRANSFORM"),
        @JsonSubTypes.Type(value = CultHabitBanner.class, name = "CULT_HABIT_BANNER"),
        @JsonSubTypes.Type(value = OfferTimerBanner.class, name = "OFFER_TIMER"),
        @JsonSubTypes.Type(value = ActiveWeekFitnessReportBanner.class, name = "ACTIVE_WEEK_FITNESS_REPORT"),
        @JsonSubTypes.Type(value = ActiveLastWeekFitnessReportBanner.class, name = "ACTIVE_LAST_WEEK_FITNESS_REPORT"),
        @JsonSubTypes.Type(value = InactiveDaysFitnessReportBanner.class, name = "INACTIVE_DAYS_FITNESS_REPORT"),
        @JsonSubTypes.Type(value = LastActiveWeekFitnessReportBanner.class, name = "LAST_ACTIVE_WEEK_FITNESS_REPORT"),
        @JsonSubTypes.Type(value = LastActiveWeekFitnessReportBanner.class, name = "LAST_ACTIVE_WEEK_FITNESS_REPORT"),
        @JsonSubTypes.Type(value = UpcomingBannerStory.class, name = "UPCOMING_BANNER"),
        @JsonSubTypes.Type(value = PostClassCelebrationBanner.class, name = "POST_CLASS_CELEBRATION"),
        @JsonSubTypes.Type(value = SquadInviteBanner.class, name = "SQUAD_INVITE_BANNER"),
        @JsonSubTypes.Type(value = UpcomingQRBannerStory.class, name = "UPCOMING_QR_BANNER"),
        @JsonSubTypes.Type(value = SmartWorkoutPlanOnboardBanner.class, name = "SMART_WORKOUT_PLAN_ONBOARD"),
        @JsonSubTypes.Type(value = SmartWorkoutPlanTodayWod.class, name = "SMART_WORKOUT_PLAN_TODAY_WOD"),
        @JsonSubTypes.Type(value = SmartWorkoutPlanResumeWod.class, name = "SMART_WORKOUT_PLAN_RESUME_WOD"),
        @JsonSubTypes.Type(value = AtHomeGuidanceJourneyBanner.class, name = "AT_HOME_GUIDANCE_JOURNEY"),
        @JsonSubTypes.Type(value = UpcomingLiveBanner.class, name = "UPCOMING_LIVE_BANNER"),
        @JsonSubTypes.Type(value = GymClosedBanner.class, name = "GYM_CLOSED_BANNER"),
        @JsonSubTypes.Type(value = CrmAssetBanner.class, name = "CRM_ASSET"),
        @JsonSubTypes.Type(value = BootcampWhatsappBanner.class, name = "BOOTCAMP_WHATSAPP_BANNER"),
        @JsonSubTypes.Type(value = LiftWhatsappBanner.class, name = "LIFT_WHATSAPP_BANNER"),
        @JsonSubTypes.Type(value = LiftClpBanner.class, name = "LIFT_CLP_BANNER"),
        @JsonSubTypes.Type(value = BootcampHPBanner.class, name = "BOOTCAMP_HP_BANNER"),
        @JsonSubTypes.Type(value = GymPTSessionStory.class, name = "GYM_PT_STORY_WIDGETS"),
        @JsonSubTypes.Type(value = GymPTNonMemberStory.class, name = "GYM_PT_NON_MEMBER_STORY_WIDGETS"),
        @JsonSubTypes.Type(value = TransformPlusHPBanner.class, name = "TRANSFORM_PLUS_HP_BANNER"),
        @JsonSubTypes.Type(value = CenterScheduleBanner.class, name = "CENTER_SCHEDULE_BANNER"),
})
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class BaseBanner extends BannerItem {

    public BaseBanner(BannerType bannerType) {
        this.bannerType = bannerType;
    }

    public BaseBanner buildView(
            ServiceInterfaces interfaces,
            UserContext userContext,
            WidgetContext widgetContext
    ) throws RuntimeBaseException {
        return this;
    }
}
