package com.curefit.cfapi.model.internal.userinfo;

import com.curefit.hamlet.models.response.UserAllocation;
import com.curefit.location.models.City;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

import java.util.concurrent.CompletableFuture;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserProfile {
    String userId;
    String subUserId;
    String areaId;
    String cultCenterId;
    String mindCenterId;
    String timezone;
    String[] customerOfferIds;
    TestSegmentOverride testSegmentOverride;
    City city;
    CompletableFuture<UserAllocation> hamletUserExperimentPromise;
    CompletableFuture<PreferredLocation> preferredLocationPromise;
}


@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
class TestSegmentOverride {
    Boolean dataExist;
    String[] segmentIds;
}
