package com.curefit.cfapi.model.internal.cult;


import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.AnnouncementService;
import com.curefit.cfapi.view.viewmodels.cult.ClassListView;
import com.curefit.cult.models.responses.CenterNotificationResponse;
import com.curefit.cult.services.spi.CultService;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldDefaults;


@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class CultSchedulePageNotificationParams {

    CultService cultService;
    AnnouncementService announcementService;
    UserContext userContext;
    ClassListView.ShutdownScheduleDetails shutdownNotificationData;
    Object fitClubNotificationData;
    CenterNotificationResponse centerNotificationResponse;

}