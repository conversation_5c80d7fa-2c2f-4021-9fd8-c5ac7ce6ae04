package com.curefit.cfapi.model.mongo;

import com.curefit.cfapi.pojo.autoLaunch.AutoLaunchConfig;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGame;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGameConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "pageconfigs")
@Getter
@Setter
@ToString
public class AutoLaunchPageConfig extends PageConfig {
    AutoLaunchConfig data;
}
