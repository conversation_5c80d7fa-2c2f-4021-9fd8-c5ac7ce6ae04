package com.curefit.cfapi.builder.vm;

import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.cache.CoachMarkConfigService;
import com.curefit.cfapi.cache.PageCache;
import com.curefit.cfapi.cache.VMCache;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.userinfo.UserProfile;
import com.curefit.cfapi.model.internal.vm.MdcTag;
import com.curefit.cfapi.model.internal.vm.Tab;
import com.curefit.cfapi.model.internal.vm.page.PageContext;
import com.curefit.cfapi.model.internal.vm.page.PageElement;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.model.mongo.Page;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.pagehook.PageHookService;
import com.curefit.cfapi.pojo.announcement.AnnouncementView;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.vm.coachmark.CoachMark;
import com.curefit.cfapi.pojo.vm.page.*;
import com.curefit.cfapi.service.AnnouncementService;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.SegmentEvaluatorService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.FutureUtil;
import com.curefit.cfapi.util.PromUtil;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.common.data.exception.RuntimeBaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.location.models.City;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.util.TextUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PageBuilder {
    private final WidgetBuilder widgetBuilder;
    private final VMCache vmCache;
    private final PageCache pageCache;
    private final SegmentEvaluatorService segmentEvaluatorService;
    private final AsyncListenableTaskExecutor taskExecutor;
    private final AnnouncementService announcementService;
    private final CoachMarkConfigService coachMarkConfigService;
    private final ExceptionReportingService exceptionReportingService;
    private final PageHookService pageHookService;
    private final ServiceInterfaces serviceInterfaces;
    private final PromUtil promUtil;

    @Autowired
    public PageBuilder(
            VMCache vmCache,
            PageCache pageCache,
            WidgetBuilder widgetBuilder,
            SegmentEvaluatorService segmentEvaluatorService,
            ServiceInterfaces serviceInterfaces,
            AnnouncementService announcementService,
            CoachMarkConfigService coachMarkConfigService,
            PromUtil promUtil,
            ExceptionReportingService exceptionReportingService,
            @Qualifier("cfApiTaskExecutor") AsyncListenableTaskExecutor taskExecutor,
            PageHookService pageHookService) {
        this.widgetBuilder = widgetBuilder;
        this.vmCache = vmCache;
        this.pageCache = pageCache;
        this.serviceInterfaces = serviceInterfaces;
        this.segmentEvaluatorService = segmentEvaluatorService;
        this.taskExecutor = taskExecutor;
        this.announcementService = announcementService;
        this.coachMarkConfigService = coachMarkConfigService;
        this.promUtil = promUtil;
        this.exceptionReportingService = exceptionReportingService;
        this.pageHookService = pageHookService;
    }


    public PageView getPage(String pageId, UserContext userContext, PageContext pageContext) throws Exception {
        Map<String, String> queryParams = pageContext.getQueryParams();
        Page page = this.pageCache.getPage(pageId, AppUtil.getAppTenantFromUserContext(userContext), AppUtil.bypassCache(userContext)).get();
        if(page == null) {
            RuntimeBaseException baseEx = new RuntimeBaseException("Page not found", new ResourceNotFoundException("Page not found"), AppStatus.BAD_REQUEST);
            baseEx.setLogType(LogType.WARNING);
            baseEx.setFingerprint(List.of(String.format("pageId:%s", pageId)));
            throw baseEx;
        }
        if (page.getSla() != null && page.getSla().getOwner() != null) {
            MDC.put(MdcTag.SLA_OWNER.toString(), page.getSla().getOwner().toString());
        }
        HashMap<String, Object> pageData = this.pageHookService.getPageData(pageId, userContext, pageContext);

        if (pageData != null) {
            page.setPageData(pageData);
        }
        Page preparedPage = this.pageHookService.preparePage(pageId, userContext, pageContext);
        if (preparedPage != null) {
            page = preparedPage;
        }

        queryParams.put("isLivePage", page.getIsLivePage() != null &&  page.getIsLivePage() ? "TRUE" : "FALSE");
        queryParams.put("pageId", pageId);
        PageView builtPage = null;
        if (page.getPageType() == PageType.LIST_PAGE) {
            builtPage = this.buildListPage(page, userContext, pageContext).get();
            PageView postHookPage = pageHookService.applyPageHook(builtPage, userContext, pageContext);
            return postHookPage;
        } else if (page.getPageType() == PageType.TAB_PAGE) {
            builtPage =  this.buildTabPage(page, userContext, pageContext).get();
            PageView postHookPage = pageHookService.applyPageHook(builtPage, userContext, pageContext);
            return postHookPage;
        }
        return null;
    }

    @Async
    public CompletableFuture<List<Section>> getTabPageSection(UserContext userContext, Page page) {

        /* check if page requested for is a tab page, if not no section will be available */
        if (!PageType.TAB_PAGE.equals(page.getPageType())) {
            return CompletableFuture.completedFuture(new ArrayList<Section>());
        }
        List<Tab> tabs =  this.getTabsForPage(userContext, page);
        List<Section> sections = tabs.stream()
                .map(tab -> {
                    {
                        Section section = new Section();
                        section.setName(tab.getName());
                        section.setId(tab.getSubPageId());
                        Page subPage = this.pageCache.getPageSync(tab.getSubPageId(), AppUtil.getAppTenantFromUserContext(userContext), AppUtil.bypassCache(userContext));
                        section.setPriority(tab.getPriority());
                        PageMetric pageMetric = new PageMetric();
                        pageMetric.setPageId(subPage.getPageId());
                        pageMetric.setPageName(subPage.getName());
                        section.setPageMetric(pageMetric);
                        return section;
                    }
                })
                .sorted(new Comparator<Section>() {
                    @Override
                    public int compare(Section o1, Section o2) {
                        return o1.getPriority() < o2.getPriority() ? -1 : 1;
                    }
                })
                .collect(Collectors.toList());
        return CompletableFuture.completedFuture(sections);
    }

    @Async
    public CompletableFuture<List<BaseWidget>> getWidgetLayout(UserContext userContext, Page page, WidgetContext widgetContext) throws ExecutionException, InterruptedException {

        PageElement pageToRender = this.getPageElement(page, userContext);
        // const positonOverrideMap = _.mapKeys(pageToRender.widgetPositionOverrides, (override) => { return override.widgetId })
        CompletableFuture<BuildWidgetResponse> headerResponseFuture = CollectionUtils.isNotEmpty(pageToRender.getHeader()) ?
                this.widgetBuilder.buildWidgets(pageToRender.getHeader(), userContext, widgetContext)
                : null;

        List<BaseWidget> widgets =  CollectionUtils.isNotEmpty(pageToRender.getHeader()) ? headerResponseFuture.get().widgets : null;
        return CompletableFuture.completedFuture(widgets);
    }

    private List<Tab> getTabsForPage(UserContext userContext, Page page) {
        List<CompletableFuture<Tab>> tabsFutures = page.getTabs().stream()
                .map(tab -> {
                    try {
                        return this.checkTabToBeShown(tab, userContext);
                    } catch (ExecutionException | InterruptedException e) {
                        e.printStackTrace();
                        return  null;
                    }
                })
                .collect(Collectors.toList());

        List<Tab> tabs = tabsFutures
                .stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return tabs;
    }


    @Async
    public CompletableFuture<ListPageView> buildListPage(Page page, UserContext userContext, PageContext pageContext) throws Exception {
        boolean isDebugEnabled = AppUtil.isDebugEnabled(pageContext.getQueryParams());
        PageElement pageElement = this.getPageElement(page, userContext);

        boolean isPartialCall = Boolean
                .parseBoolean(ObjectUtils.defaultIfNull(pageContext.getQueryParams().get("isPartialCall"), "false"));
        List<String> bodyToConstruct = pageElement.getBody();
        if (isPartialCall && pageElement.getBody() != null && pageElement.getBody().size() > 0) {
            int widgetsToFetch = Math.min(pageElement.getBody().size(), Integer
                    .parseInt(ObjectUtils.defaultIfNull(pageContext.getQueryParams().get("widgetsToFetch"), "3")));
            bodyToConstruct = pageElement.getBody().subList(0, widgetsToFetch);
        }
        long renderStartTime = System.nanoTime();

        // Checks if it's a conditional page
        if (CollectionUtils.isNotEmpty(page.getSegmentIds())) {
            Segment segment = this.segmentEvaluatorService.checkCondition(page.getSegmentIds(), userContext, isDebugEnabled).get();
            // Throw unauthorized if the user does not belong to any of the tagged segments
            if (isDebugEnabled) {
                log.info("PageBuilder:buildListPage:: segmentIds:{} evaluated to {} to userId:{}", page.getSegmentIds(),
                        segment != null, userContext.getUserProfile().getUserId());
            }
            if (segment == null) {
                throw new Exception("Unauthorized Access!");
            }
        }

        WidgetContext widgetContext = new WidgetContext(pageContext);
        AnnouncementView announcement = null;
        CoachMark coachMark = null;
        CompletableFuture<CoachMark> coachMarkFuture = this.coachMarkConfigService.getCoachMark(userContext, page.getPageId(), Collections.emptyList());
        CompletableFuture<AnnouncementView> announcementFuture = CollectionUtils.isNotEmpty(page.getAnnouncementIds())  ? announcementService.getSingleActiveAnnouncement(userContext, false, page.getAnnouncementIds(), false) : null;
        CompletableFuture<BuildWidgetResponse> headerResponseFuture = CollectionUtils.isNotEmpty(pageElement.getHeader()) ? this.widgetBuilder.buildWidgets(pageElement.getHeader(), userContext, widgetContext) : null;
        CompletableFuture<BuildWidgetResponse> footerResponseFuture = CollectionUtils.isNotEmpty(pageElement.getFooter()) ? this.widgetBuilder.buildWidgets(pageElement.getFooter(), userContext, widgetContext) : null;
        CompletableFuture<BuildWidgetResponse> bodyResponseFuture = this.widgetBuilder.buildWidgets(bodyToConstruct, userContext, widgetContext);
        List<com.curefit.cfapi.pojo.vm.widget.BaseWidget> mergedWidgetList = new ArrayList<>();
        List<com.curefit.cfapi.pojo.vm.widget.BaseWidget> footerWidgetList = new ArrayList<>();
        if (headerResponseFuture != null) {
            mergedWidgetList.addAll(headerResponseFuture.get().widgets);
        }
        if (footerResponseFuture != null) {
            footerWidgetList.addAll(footerResponseFuture.get().widgets);
        }
        mergedWidgetList.addAll(bodyResponseFuture.get().widgets);
        if (announcementFuture != null){
            announcement = announcementFuture.get();
        }
        if(coachMarkFuture != null){
            coachMark = coachMarkFuture.get();
        }

        PageMetric pageMetric = new PageMetric();
        pageMetric.setPageId(page.getPageId());
        pageMetric.setPageName(page.getName());
        CompletableFuture<PageSelector> pageSelectorPromise = this.buildPageSelector(userContext, page);
        ListPageView listPageView = new ListPageView();
        listPageView.setPageType(page.getPageType());
        listPageView.setName(page.getName());
        listPageView.setPageId(page.getPageId());
        listPageView.setActions(this.buildPageLoadActions(page.getPageActions(), userContext));
        listPageView.setPageLoadActions(page.getPageLoadActions());
        listPageView.setIcon(page.getIcon());
        listPageView.setPageMetric(pageMetric);
        listPageView.setBody(mergedWidgetList);
        listPageView.setAuroraTheme(page.getAuroraTheme());
        listPageView.setFooter(footerWidgetList);
        if (page.getPageData() != null) {
            listPageView.setPageData(page.getPageData());
        }
        listPageView.setPageSelector(pageSelectorPromise.get());
        listPageView.setTheme(getPageTheme(page, serviceInterfaces, serviceInterfaces.environmentService, userContext));
        listPageView.setBackgroundAnimation(page.getBackgroundAnimation());
        if (announcement != null){
            listPageView.setAnnouncementData(announcement);
        }
        if (coachMark != null){
            listPageView.setCoachMark(coachMark);
        }
        listPageView.setRightBarButton(page.getRightBarButton());
        listPageView.setSearchBarButton(page.getSearchBarButton());

        if(pageElement.getFloatingButtonAction() != null) {
            listPageView.setFloatingButtonAction(pageElement.getFloatingButtonAction());
        }

        if (page.getInitAction() != null) {
            listPageView.setInitAction(page.getInitAction());
        }

        if (page.getCustomSheetProps() != null) {
            listPageView.setCustomSheetProps(page.getCustomSheetProps());
        }

        if(page.getWebTheme() != null){
            listPageView.setWebTheme(page.getWebTheme());
        }

        long timeElapsed = System.nanoTime() - renderStartTime;
//        this.promUtil.reportPageRenderTimeToProm(page.getPageId(), timeElapsed);

        return CompletableFuture.completedFuture(listPageView);
    }

    private String getPageTheme(Page page, ServiceInterfaces serviceInterfaces, EnvironmentService environmentService, UserContext userContext) {
        if (!TextUtils.isEmpty(page.getPageId()) && page.getPageId().equals("hometab") && AppUtil.doesUserBelongToNewHomeAuroraCLP(serviceInterfaces, environmentService, userContext)) {
            return "DARK";
        }
        return page.getTheme();
    }

    @Async
    public CompletableFuture<TabPageView> buildTabPage(Page page, UserContext userContext, PageContext pageContext) throws Exception {

        boolean showSearchBarButton = false;
        if (page.getSearchBarButton()  != null) {
            showSearchBarButton = page.getSearchBarButton().getSegmentIds() == null || page.getSearchBarButton().getSegmentIds().isEmpty() || this.segmentEvaluatorService.checkCondition(page.getSearchBarButton().getSegmentIds(), userContext).get() != null;
        }

        AnnouncementView announcement = null;
        CoachMark coachMark = null;
        CompletableFuture<AnnouncementView> announcementFuture = CollectionUtils.isNotEmpty(page.getAnnouncementIds())  ? announcementService.getSingleActiveAnnouncement(userContext, false, page.getAnnouncementIds(), false) : null;
        if(announcementFuture != null){
            announcement = announcementFuture.get();
        }
        PageMetric pageMetric = new PageMetric();
        pageMetric.setPageId(page.getPageId());
        pageMetric.setPageName(page.getName());

        PageElement pageElement = this.getPageElement(page, userContext);
        page.setTabs(pageElement.getTabs());

        CompletableFuture<PageSelector> pageSelectorPromise = this.buildPageSelector(userContext, page);

        List<Tab> tabs = this.getTabsForPage(userContext, page);
        String selectedTabQueryParam = pageContext.getQueryParams().get("selectedTab");
        String selectedTab = null;
        for (Tab tab: tabs) {
            if (Objects.equals(tab.getSubPageId(), selectedTabQueryParam)) {
                selectedTab = tab.getSubPageId();
            }
        }
        if (selectedTab == null && tabs.size() > 0) {
            selectedTab = tabs.stream().min(Comparator.comparingInt(Tab::getPriority)).get().getSubPageId();
            log.debug("selectedTab set from newTabs: " + selectedTab);
        }
        TabPageView tabPageView = new TabPageView();
        tabPageView.setLists(new HashMap<>());
        tabPageView.setSections(new ArrayList<>());

        String finalSelectedTab = selectedTab;
        Map<String, CompletableFuture<ListPageView>> sectionPromiseMap = new HashMap<>();
        for (Tab tab: tabs) {
            if (!AppUtil.isVMPageOptimizationSupported(userContext) || (Objects.equals(tab.getSubPageId(), finalSelectedTab))) {
                try {
                    sectionPromiseMap.put(tab.getSubPageId(), this.buildTabListPageView(tab, userContext, pageContext));
                } catch (Exception err) {
                    log.error("error while building list page: " + tab.getSubPageId(), err);
                }
            }
        }

        for (Tab tab: tabs) {
            CompletableFuture<ListPageView> sectionPromise = sectionPromiseMap.get(tab.getSubPageId());

            Section section = new Section();
            section.setName(tab.getName());
            section.setPriority(tab.getPriority());
            section.setId(tab.getSubPageId());

            if (sectionPromise != null) {
                ListPageView listPageView = sectionPromise.get();
                section.setPageMetric(listPageView.getPageMetric());
                tabPageView.getLists().put(tab.getSubPageId(), listPageView);
            }
            tabPageView.getSections().add(section);
        }

        List<String> subPageIds = tabPageView.getSections().stream().map(section -> section.getId()).collect(Collectors.toList());
        CompletableFuture<CoachMark> coachMarkFuture = this.coachMarkConfigService.getCoachMark(userContext, page.getPageId(), subPageIds);
        if(coachMarkFuture != null){
            coachMark = coachMarkFuture.get();
        }

        tabPageView.setPageType(page.getPageType());
        tabPageView.setPageId(page.getPageId());
        tabPageView.setName(page.getName());
        tabPageView.setActions(this.buildPageLoadActions(page.getPageActions(), userContext));
        tabPageView.setIcon(page.getIcon());
        tabPageView.setPageMetric(pageMetric);
        if (page.getPageData() != null) {
            tabPageView.setPageData(page.getPageData());
        }
        tabPageView.setAuroraTheme(page.getAuroraTheme());
        tabPageView.setPageSelector(pageSelectorPromise.get());
        tabPageView.setTheme(getPageTheme(page, serviceInterfaces, serviceInterfaces.environmentService, userContext));
        tabPageView.setBackgroundAnimation(page.getBackgroundAnimation());
        if(announcement != null){
            tabPageView.setAnnouncementData(announcement);
        }
        if (coachMark != null){
            tabPageView.setCoachMark(coachMark);
        }
        tabPageView.setRightBarButton(page.getRightBarButton());
        tabPageView.setSearchBarButton(showSearchBarButton ? page.getSearchBarButton() : null);
        if(pageElement.getFloatingButtonAction() != null) {
            tabPageView.setFloatingButtonAction(pageElement.getFloatingButtonAction());
        }
        tabPageView.setSelectedTabIndex(0);

        tabPageView.setSections(tabPageView.getSections().stream().sorted(Comparator.comparingInt(Section::getPriority)).collect(Collectors.toList()));

        for (int i = 0; i < tabPageView.getSections().size(); i++) {
            if (Objects.equals(tabPageView.getSections().get(i).getId(), page.getPulsatingDotTab())) {
                tabPageView.setPulsatingDotIndex(i);
            }
        }
        tabPageView.setPulsatingDotVersion(page.getPulsatingDotVersion());
        tabPageView.setSelectedTabIndex(0);
        for (int i = 0; i < tabPageView.getSections().size(); i++) {
            if (Objects.equals(tabPageView.getSections().get(i).getId(), selectedTab)) {
                tabPageView.setSelectedTabIndex(i);
            }
        }
        return CompletableFuture.completedFuture(tabPageView);
    }

    @Async
    public CompletableFuture<ListPageView> buildTabListPageView(Tab tab, UserContext userContext, PageContext pageContext) throws Exception {
        Page subPage = this.pageCache.getPage(tab.getSubPageId(), AppUtil.getAppTenantFromUserContext(userContext), AppUtil.bypassCache(userContext)).get();
        return this.buildListPage(subPage, userContext, pageContext);
    }

    private CompletableFuture<Tab> checkTabToBeShown(Tab tab, UserContext userContext) throws ExecutionException, InterruptedException {
        /*        const deliveryArea = await userProfile.deliveryAreaPromise
        const preferredLocation = await userProfile.preferredLocationPromise*/


        CompletableFuture<Segment> doesUserBelongToTabSegmentFuture = null;
        CompletableFuture<Segment> doesUserBelongToPageSegmentFuture = null;

        if (CollectionUtils.isNotEmpty(tab.getSegmentIds())) {
            doesUserBelongToTabSegmentFuture = this.segmentEvaluatorService.checkCondition(tab.getSegmentIds(), userContext);
        }

        Page subPage = this.pageCache.getPage(tab.getSubPageId(), AppUtil.getAppTenantFromUserContext(userContext), AppUtil.bypassCache(userContext)).get();

        if (subPage == null) {
            return CompletableFuture.completedFuture(null);
        }

        if (CollectionUtils.isNotEmpty(subPage.getSegmentIds())) {
            doesUserBelongToPageSegmentFuture = this.segmentEvaluatorService.checkCondition(subPage.getSegmentIds(), userContext);
        }

        // Checking for tab segment pass
        if (doesUserBelongToTabSegmentFuture != null) {
            Segment doesUserBelongToTabSegment =  doesUserBelongToTabSegmentFuture.get();
            // Skip the tab, if the user does not belong to tab segments
            if (doesUserBelongToTabSegment == null) {
                return CompletableFuture.completedFuture(null);
            }
        }

        // Checking for page segment pass
        if (doesUserBelongToPageSegmentFuture != null) {
            Segment doesUserBelongToPageSegment = doesUserBelongToPageSegmentFuture.get();
            // Skip the tab if the user does not belong to the tagged segment
            if (doesUserBelongToPageSegment == null) {
                return CompletableFuture.completedFuture(null);
            }
        }

        return CompletableFuture.completedFuture(tab);
    }
    public PageElement getPageElement(Page page, UserContext userContext) {
        PageElement pageOverride = this.checkIfPageOverrideExists(page, userContext);
        if (pageOverride != null) {
            return pageOverride;
        }

        PageElement pageElement = new PageElement();
        pageElement.setBody(page.getBody());
        pageElement.setHeader(page.getHeader());
        pageElement.setFooter(page.getFooter());
        pageElement.setPageActions(page.getPageActions());
        pageElement.setTabs(page.getTabs());
        pageElement.setWidgetPositionOverrides(page.getWidgetPositionOverrides());
        pageElement.setFloatingButtonAction(page.getFloatingButtonAction());

        return pageElement;
    }

    private PageElement checkIfPageOverrideExists(Page page, UserContext userContext) {
        if (CollectionUtils.isEmpty(page.getPageOverrides())) {
            return null;
        }
        List<CompletableFuture<PageElement>> pageOverridePromises = new ArrayList<>();
        for (PageElement pageOverride : page.getPageOverrides()) {
            pageOverridePromises.add(CompletableFuture.supplyAsync(() -> {
                Segment segment = null;
                try {
                    segment = this.segmentEvaluatorService.checkCondition(Collections.singletonList(pageOverride.getSegmentId()), userContext).get();
                } catch (Exception e) {
                    this.exceptionReportingService.reportException("Error in evaluating segmentId: " + pageOverride.getSegmentId(), e);
                }
                return segment != null ? pageOverride : null;
            }, taskExecutor));
        }
        CompletableFuture<List<PageElement>> pageOverridePromise = FutureUtil.allOf(pageOverridePromises);
        try {
            return pageOverridePromise.get().stream().filter(Objects::nonNull).findFirst().orElse(null);
        } catch (Exception e) {
            this.exceptionReportingService.reportException("Error in evaluating page override", e);
            return null;
        }
    }


    public CompletableFuture<PageSelector> buildPageSelector(UserContext userContext, Page page) {

        UserProfile userProfile = userContext.getUserProfile();
        if (AppUtil.isInternationalApp(userContext)
                || page.getSelector() != PageSelectorType.CITY_SELECTOR) {
            return CompletableFuture.completedFuture(null);
        }
        City city = this.serviceInterfaces.cityCache.getCityById(userProfile.getCity().getCityId());
        if (city == null) {
            return CompletableFuture.completedFuture(null);
        }
        CityPageSelector pageSelector = new CityPageSelector();
        pageSelector.setCityName(city.getName());
        pageSelector.setSelector(PageSelectorType.CITY_SELECTOR);
        return CompletableFuture.completedFuture(pageSelector);
    }

    public List<Action> buildPageLoadActions(List<Action> actions, UserContext userContext) {
        if (CollectionUtils.isEmpty(actions)) return null;
        List<CompletableFuture<Action>> pageActionsPromises = new ArrayList<>();
        for (Action action: actions) {
            pageActionsPromises.add(CompletableFuture.supplyAsync(() -> {
                if (CollectionUtils.isEmpty(action.getSegmentIds())) return action;
                Segment segment = null;
                try {
                    segment = this.segmentEvaluatorService.checkCondition(action.getSegmentIds(), userContext).get();
                } catch (Exception e) {
                    this.exceptionReportingService.reportException("Error in build page actions", e);
                }
                return segment != null ? action : null;
            }, taskExecutor));
        }
        CompletableFuture<List<Action>> pageActionsPromise = FutureUtil.allOf(pageActionsPromises);
        try {
            return pageActionsPromise.get().stream().filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e) {
            this.exceptionReportingService.reportException("Error in build page actions", e);
            return null;
        }
    }
}
