package com.curefit.cfapi.builder.vm;

import com.curefit.albus.agent.AgentAssignmentRequest;
import com.curefit.albus.booking.ActiveConsultationResponse;
import com.curefit.albus.common.BundleSubCategoryCode;
import com.curefit.albus.common.ConsultationInfo;
import com.curefit.albus.common.agent.UserActiveAgentResponse;
import com.curefit.albus.common.states.AlbusBookingState;
import com.curefit.albus.request.BundleSearchRequestParam;
import com.curefit.albus.response.*;
import com.curefit.albus.response.bootcamp.UserProgramCenterResponse;
import com.curefit.albus.response.bundle.GenericBundleOrderResponse;
import com.curefit.base.enums.Tenant;
import com.curefit.cfapi.builder.vm.request.FitcashRequest;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.mapper.CultsportMapper;
import com.curefit.cfapi.model.internal.tread.TreadUserProfile;
import com.curefit.cfapi.model.internal.userinfo.SessionInfo;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.userinfo.UserProfile;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.model.mongo.csBlog.CSBlogArticle;
import com.curefit.cfapi.model.mongo.csBlog.CSBlogCategory;
import com.curefit.cfapi.model.mongo.csBlog.CSBlogTag;
import com.curefit.cfapi.pojo.cultsport.CouponInfo;
import com.curefit.cfapi.service.SegmentEvaluatorService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.pagecache.ApiResponseInfo;
import com.curefit.cfapi.service.pagecache.ApiResponseKey;
import com.curefit.cfapi.service.pagecache.PageKey;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.widgets.cultsport.common.CultsportCustomerPhotos;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.RuntimeBaseException;
import com.curefit.cult.models.BulkBookingResponse;
import com.curefit.cult.models.CultSummary;
import com.curefit.cult.models.UserTrialEligibilityResponse;
import com.curefit.cultsport.feedback.common.enums.FeedbackSort;
import com.curefit.cultsport.feedback.common.models.ProductFeedback;
import com.curefit.cultsport.feedback.common.pojos.response.FeedbackListResponse;
import com.curefit.cultsport.user.common.dto.WishlistEntry;
import com.curefit.fitcash.model.WalletBalance;
import com.curefit.gearvault.models.CatalogueProductV2;
import com.curefit.gearvault.models.PriorityDeliveryDetailsForCity;
import com.curefit.gymfit.models.TrialUsage;
import com.curefit.magneto.common.dto.responses.SortRankingTemplateResponse;
import com.curefit.offers.dtos.BestPrice;
import com.curefit.offers.dtos.BestPriceRequest;
import com.curefit.offers.dtos.GetOffersResponse;
import com.curefit.offers.dtos.cult.CultProductPricesResponse;
import com.curefit.offers.dtos.gear.GearProductDto;
import com.curefit.offers.dtos.gymfit.GymFitProductPricesResponse;
import com.curefit.offers.dtos.play.PlayProductPricesResponse;
import com.curefit.offers.enums.OrderSource;
import com.curefit.offers.types.UserInfo;
import com.curefit.pms.enums.Namespace;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.pms.enums.Visibility;
import com.curefit.pms.pojo.Restriction;
import com.curefit.pms.pojo.customPacks.OfflineFitnessPack;
import com.curefit.pms.pojo.customPacks.augments.AugmentedOfflineFitnessPack;
import com.curefit.pms.requests.augments.AugmentContext;
import com.curefit.pms.requests.augments.AugmentedPackSearchRequest;
import com.curefit.pms.requests.augments.PackAugmentRequest;
import com.curefit.product.enums.ProductType;
import com.curefit.product.enums.Status;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.shifu.enums.LifestyleScoreType;
import com.curefit.shifu.enums.UserPictureTag;
import com.curefit.shifu.pojo.LabTestInfo;
import com.curefit.shifu.pojo.UserActionDetails;
import com.curefit.shifu.pojo.UserPictureEntry;
import com.curefit.shifu.pojo.challenges.ChallengeLeaderBoardResponse;
import com.curefit.shifu.pojo.lifestyle.LifestyleScoreEntry;
import com.curefit.sportsapi.pojo.FTSTrialSport;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.tread.POJOS.request.PreSessionValidationRequest;
import com.curefit.tread.POJOS.response.PreSessionValidationResponse;
import com.curefit.uas.responses.FitnessReportResponse;
import com.curefit.uas.responses.StreakResponse;
import com.curefit.ufs.pojo.UserWodEntry;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.curefit.cfapi.config.CircuitBreakerConfiguration.CircuitBreakerName.*;
import static com.curefit.cfapi.constants.FitnessPlannerConstants.TENANT_AI_TRAINER;
import static com.curefit.cfapi.util.TransformUtil.*;

@Slf4j
public class RequestCache {
    private Map<RequestType, Future<Object>> requestMap;
    private Map<String, List<OfflineFitnessPack>> packCacheMap;
    private Map<String, Future<Object>> dynamicKeyCacheMap;
    private Map<String, Future<Object>> csRequestMap;
    private Map<RequestType, Future<Object>> mexRequestMap;
    private ServiceInterfaces interfaces;
    @Autowired
    private CultsportMapper cultsportMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String SEGMENT_OVERRIDE_REDIS_KEY = "cfapi:segmentOverride";

    private static final String IMPRESSION_CAP_REDIS_KEY = "AggImpCap:%s";

    public RequestCache(
            ServiceInterfaces interfaces) {
        this.interfaces = interfaces;
        this.requestMap = new HashMap<>();
        this.packCacheMap = new HashMap<>();
        this.csRequestMap = new HashMap<>();
        this.mexRequestMap = new HashMap<>();
        this.dynamicKeyCacheMap = new HashMap<>();
    }

    public synchronized Future<Object> getRequestFuture(RequestType requestType, UserContext userContext) {
        if (requestMap.containsKey(requestType)) {
            return requestMap.get(requestType);
        }
        String userId = userContext.getUserProfile().getUserId();
        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            // do api call
            switch (requestType) {
                case CS_SHOPIFY_WEBVIEW_ELIGIBILITY:
                    return this.isUserEligibleForCsShopifyWebviewInApp(userContext);
                case GET_CS_WISHLIST:
                    return this.getUserWishlist(userContext);
                case GET_CULT_UPCOMING_BOOKINGS:
                    return this.getCultUpcomingBookings(userContext);
                case HOMETAB_CACHE_DATA:
                    return this.getHometabCacheData(userId);
                case CULT_MIND_SUMMARY:
                    return this.getCultSummaryForUser(userContext);
                case PLATFORM_SEGMENTS:
                    return this.getUserSegments(userContext);
                case LIVE_ELIGIBLE_FOR_TRIAL:
                    return this.isEligibleForLiveTrial(userId, userContext);
                case SEGMENT_OVERRIDE:
                    return this.getSegmentOverride(userId);
                case GET_ALL_PATIENTS:
                    return this.getAllPatients(userId);
                case GET_TRANSFORM_ACTIVE_PACKS:
                    return this.getTransformActiveBundlePacksForUser(userId);
                case GET_TRANSFORM_ACTIVE_CONSULTATIONS:
                    return this.getTransformActiveConsultationsForUser(userId);
                case GET_TRANSFORM_WAITLIST_STATUS:
                    return this.getTransformWaitlistStatus(userId);
                case GET_TRANSFORM_MEMBERSHIPS:
                    return this.getTransformMembershipsForUser(userId);
                case GET_BOOTCAMP_MEMBERSHIPS:
                    return this.getBootcampMembershipsForUser(userId);
                case GET_LIFESTYLE_SCORE:
                    return this.getLifestyleScoreUser(userId);
                case CULT_ROW_CLASS_PARAMS:
                    return this.getCultROWSessionValidationParams(userContext);
                case TREAD_USER_PROFILE:
                    return this.getTreadUserProfile(userContext);
                case GET_USER_ACTIVE_ACTIONS:
                    return this.getUserActionDetails(userId);
                case GET_CHALLENGES_DATA:
                    return this.getChallengesData(userId);
                case WIDGET_IMPRESSION_CAP_DATA:
                    return this.getImpressionCappedContentIdsForUser(userId);
                case GET_TRANSFORM_LAB_BOOKING_DATA:
                    return this.getTransformLabBookingData(userId, userContext);
                case GET_LIFESTYLE_COACH_ACTIVE_CONSULTATIONS:
                    return this.getTransformLifestyleCoachActiveConsultations(userId);
                case GET_PLUS_LIFESTYLE_COACH_ACTIVE_CONSULTATIONS:
                    return this.getTransformPlusLifestyleCoachActiveConsultations(userId);
                case GET_NUTRITIONIST_ACTIVE_CONSULTATIONS:
                    return this.getTransformNutritionistActiveConsultations(userId);
                case GET_FITNESS_COACH_ACTIVE_CONSULTATIONS:
                    return this.getTransformFitnessCoachActiveConsultations(userId);
                case GET_TRANSFORM_PT_ACTIVE_CONSULTATION:
                    return this.getTransformPTCoachActiveConsultations(userId);
                case GET_TRANSFORM_USER_IMAGE:
                    return this.getTransformUserImage(userId);
                case GET_BOOTCAMP_START_DATE:
                    return this.getTransformBootcampStartDate(userId);
                case GET_LIFT_START_DATE:
                    return this.getLiftStartDate(userId);
                case GET_TRANSFORM_NC_LAB_BOOKING_DATA:
                    return this.getTransformNCLabBookingData(userId, userContext);
                case GET_CURRENT_WEEK_FITNESS_REPORT:
                    return this.getCurrentWeekFitnessData(userId);
                case GET_TRANSFORM_PT_PACKS:
                    return this.getTransformPTActiveBundlePacksForUser(userId);
                case GET_SMART_WORKOUT_PLAN_CURRENT_DAY_WOD:
                    return this.getCurrentDaySmartWorkoutPlanWod(userId);
                case GET_CULTSPORT_SNAPMINT_EXPERIMENT_ELIGIBILITY:
                    return this.getCSUserSnapmintEligibility(userContext);
                case CULT_UNBOUND_WORKOUT_IDS:
                    return interfaces.cultService.getCultUnboundWorkoutIds(false);
                case CULT_UNBOUND_CHAMPIONSHIP_WORKOUT_IDS:
                    return interfaces.cultService.getCultUnboundWorkoutIds(true);
                case GET_GX_TRIALS:
                    return this.getGXTrials(userContext, interfaces);
                case GET_GYM_TRIALS:
                    return this.getGymTrials(userContext, interfaces);
                case GET_PLAY_TRIALS:
                    return this.getPlayTrials(userContext, interfaces);
                case GET_LUX_TRAILS:
                    return this.getLuxTrials(userContext, interfaces);
            }
            return null;
        });
        requestMap.put(requestType, result);
        return result;
    }

    public synchronized Future<Object> getMexRequestFuture(RequestType requestType, UserContext userContext) {
        if (mexRequestMap.containsKey(requestType)) {
            return mexRequestMap.get(requestType);
        }

        if(requestType == RequestType.USER_STREAK_DETAILS && mexRequestMap.containsKey(RequestType.USER_STREAK_DETAILS_WITH_ACTIVITY_MAP)) return mexRequestMap.get(RequestType.USER_STREAK_DETAILS_WITH_ACTIVITY_MAP);

        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            return switch (requestType) {
                case USER_STREAK_DETAILS -> this.getUserStreakDetails(userContext, interfaces, false);
                case USER_STREAK_DETAILS_WITH_ACTIVITY_MAP -> this.getUserStreakDetails(userContext, interfaces, true);
                default -> null;
            };
        });
        mexRequestMap.put(requestType, result);
        return result;
    }

    private StreakResponse getUserStreakDetails(UserContext userContext, ServiceInterfaces interfaces, boolean fetchDateToActivityDetailsMapForEndDatesWeek){
        if(!isUserInStreakSegment(userContext, interfaces.segmentEvaluatorService)) return null;

        String userId = userContext.getUserProfile().getUserId();
        StreakResponse streakResponse = interfaces.userActivityService.getUserStreakDetails(userId, LocalDate.now().toString(), fetchDateToActivityDetailsMapForEndDatesWeek, true);

        if(Objects.isNull(streakResponse) || Objects.isNull(streakResponse.getBestStreakCount()) || streakResponse.getBestStreakCount() == 0) return null;
        return streakResponse;
    }

    private static boolean isUserInStreakSegment(UserContext userContext, SegmentEvaluatorService segmentEvaluatorService) {
        boolean isUserPartOfStreak = false;
        try {
            Segment segment = segmentEvaluatorService.checkCondition(List.of(AppUtil.USER_STREAK_SEGMENT_EXPERIMENT), userContext).join();
            Segment excludedUsers = segmentEvaluatorService.checkCondition(List.of(AppUtil.USER_STREAK_SEGMENT_EXCLUDED), userContext).join();
            isUserPartOfStreak = segment != null && excludedUsers == null;
        } catch (Exception e) {
            log.error("Error while fetching showUserStreakFlow", e);
        }
        boolean userStreakVersionCheck = userContext.getSessionInfo().getAppVersion() >= AppUtil.MIN_USER_STREAK_VERSION;
        return isUserPartOfStreak && userStreakVersionCheck;
    }

    private UserTrialEligibilityResponse getGXTrials(UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        return this.interfaces.circuitBreakerConfiguration.get(CULT_SERVICE).executeCallable(
            () -> interfaces.cultService.getUserTrialEligibility(userContext.getUserProfile().getUserId(), "")
        );
    }

    private TrialUsage getGymTrials(UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        return interfaces.circuitBreakerConfiguration.get(GYMFIT_SERVICE).executeCallable(
            () -> interfaces.trialUsageService.getTrialUsageByUserId(
                userContext.getUserProfile().getUserId(),
                userContext.getSessionInfo().getDeviceId()
            )
        );
    }

    private TrialUsage getLuxTrials(UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        return interfaces.circuitBreakerConfiguration.get(GYMFIT_SERVICE).executeCallable(
            () -> interfaces.trialUsageService.getTrialUsageByUserIdAndTrialType(
                userContext.getUserProfile().getUserId(),
                userContext.getSessionInfo().getDeviceId(),
                "LUX"
            )
        );
    }

    private List<FTSTrialSport> getPlayTrials(UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        return interfaces.circuitBreakerConfiguration.get(SPORTS_API_SERVICE).executeCallable(
            () -> interfaces.sportsApiService.getTrialSportDetails(
                userContext.getUserProfile().getUserId(),
                userContext.getUserProfile().getCity().getCityId(),
                PlayUtil.isUserPhoneNumberPresent(userContext)
            )
        );
    }


    private boolean isUserEligibleForCsShopifyWebviewInApp(UserContext userContext) {
        return AppUtil.isUserEligibleForCsShopifyWebviewInApp(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
    }
    public synchronized Future<Object> getRequestFuture(RequestType requestType, FitcashRequest fitcashRequest) {
        if (requestMap.containsKey(requestType)) {
            return requestMap.get(requestType);
        }
        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            // do api call
            return this.getFitCashBalance(fitcashRequest);

        });
        requestMap.put(requestType, result);
        return result;
    }

    public synchronized Future<Object> getRequestFuture(RequestType requestType, BestPriceRequest bestPricingRequest) {
        if (requestMap.containsKey(requestType)) {
            return requestMap.get(requestType);
        }
        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            switch (requestType) {
                case GET_BEST_PRICES:
                    return this.getBestPrices(bestPricingRequest);
            }
            return null;
        });
        requestMap.put(requestType, result);
        return result;
    }

    public synchronized Future<Object> getOffersFromOfferIds(RequestType requestType, List<String> offerIds) {
        String requestCacheKey = getCSRequestCacheKey(requestType, offerIds);
        if (csRequestMap.containsKey(requestCacheKey)) {
            return csRequestMap.get(requestCacheKey);
        }
        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            switch (requestType) {
                case GET_OFFERS_FROM_OFFERID:
                    return this.getOffersFromOfferIds(offerIds);
            }
            return null;
        });
        csRequestMap.put(requestCacheKey, result);
        return result;
    }

    private String getCSRequestCacheKey(RequestType requestType, List<String> values) {
        List<String> substrings = new ArrayList<>(List.of(String.valueOf(requestType)));
        substrings.addAll(values);
        return String.join("::", substrings);
    }

    private boolean getCSUserSnapmintEligibility(UserContext userContext) {
        return AppUtil.isSnapmintEnabledForUser(interfaces.getSegmentEvaluatorService(), interfaces.getEnvironmentService(), userContext);
    }

    public synchronized Future<Object> getRequestFuture(RequestType requestType, UserContext userContext,
                                                        String productId) {
        String requestCacheKey = getCSRequestCacheKey(requestType, Collections.singletonList(productId != null ? productId.strip() : ""));
        if (csRequestMap.containsKey(requestCacheKey)) {
            log.debug("Working fine for " + requestType.toString());
            return csRequestMap.get(requestCacheKey);
        }
        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            switch (requestType) {
                case GET_CULTSPORT_APPAREL_PDP_EXP:
                    return AppUtil.getNewApparelPdpEnabled(interfaces.segmentEvaluatorService,
                            interfaces.environmentService, userContext);
                case GET_CULTSPORT_PRODUCT:
                    return this.getGearProduct(productId, userContext);
                case GET_CULTSPORT_PRODUCT_V3:
                    return this.getCatalogueProductV2(productId);
                case GET_CULTSPORT_PRODUCT_OFFER:
                    return this.getGearProductOffer(productId, userContext);
                case GET_GEAR_BULK_OFFER:
                    return this.getGearBulkOffer(userContext, productId);
                case GET_CULTSPORT_PRODUCT_RATING:
                    return this.getGearProductRating(userContext, productId);
                case GET_CULTSPORT_PRODUCT_REVIEW:
                    return this.getGearProductReview(userContext, productId);
                case GET_CULTSPORT_PRODUCT_IMAGE_REVIEW:
                    return this.getGearProductImageReview(userContext, productId);
                case GET_FOOTWEAR_PDP_EXP:
                    return AppUtil.shouldUseNewFootwearPDPUI(interfaces.segmentEvaluatorService,
                            interfaces.environmentService, userContext);
                case GET_CULTSPORT_PLP_PAGE_EXP:
                    return AppUtil.shouldUseNewPLPPage(interfaces.segmentEvaluatorService,
                            interfaces.environmentService, userContext);
                case GET_TRAINER_ATTIRE_EXP:
                    return AppUtil.doesUserBelongsToTrainerAttireExperiment(interfaces.segmentEvaluatorService,
                            interfaces.environmentService, userContext);
                case GET_CS_BLOG_ARTICLE:
                    return this.getCSBlogArticleForSlug(productId);
                case GET_CS_BLOG_CATEGORIES:
                    return this.getAllCSBlogCategories();
                case GET_CS_BLOG_TAGS:
                    return this.getAllCSBlogTags();
                case GET_ELIGIBLE_COUPONS_FOR_USER:
                    return this.getCouponsForUsers(userContext);
            }
            return null;
        });
        csRequestMap.put(requestCacheKey, result);
        return result;
    }

    public synchronized Future<Object> getCSPlpRequestFuture(RequestType requestType, String collectionSlug) {
        if (requestMap.containsKey(requestType)) {
            return requestMap.get(requestType);
        }
        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            switch (requestType) {
                case GET_CULTSPORT_PLP_SORT_FILTERS:
                    return this.getAllActiveSortForCollection(collectionSlug);
            }
            return null;
        });
        requestMap.put(requestType, result);
        return result;
    }

    private List<SortRankingTemplateResponse> getAllActiveSortForCollection(String collectionSlug) throws Exception {
        try {
            return this.interfaces.cultsportPLPService.getAllActiveSortForCollection(collectionSlug);
        } catch (Exception ex) {
            if (!(ex.toString() != null && ex.toString().contains("The resource you were looking for could not be found"))) {
                this.interfaces.exceptionReportingService.reportException("Error while fetching getAllActiveSortForCollection cultsport plp for collectionSlug = " + collectionSlug, ex);
            }
        }
        return null;
    }


    private CSBlogArticle getCSBlogArticleForSlug(String articleSlug) {
        try {
            return interfaces.getCsBlogService().getBlogArticleForSlug(articleSlug);
        } catch (Exception ex) {
            interfaces.exceptionReportingService.reportException(ex);
        }
        return null;
    }

    private List<CSBlogCategory> getAllCSBlogCategories() {
        try {
            return this.interfaces.csBlogService.getAllCSBlogCategories();
        } catch (Exception ex) {
            interfaces.exceptionReportingService.reportException(ex);
        }
        return new ArrayList<>();
    }

    private List<CSBlogTag> getAllCSBlogTags() {
        try {
            return this.interfaces.csBlogService.getAllCSBlogTags();
        } catch (Exception ex) {
            interfaces.exceptionReportingService.reportException(ex);
        }
        return new ArrayList<>();
    }

    public synchronized Future<Object> getRequestFuture(RequestType requestType, String subCategoryCode,
                                                        UserContext userContext, Boolean clearCache) {
        if (requestMap.containsKey(requestType)) {
            if (clearCache) {
                requestMap.remove(requestType);
            } else {
                return requestMap.get(requestType);
            }
        }
        String userId = userContext.getUserProfile().getUserId();
        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            // do api call
            return switch (requestType) {
                case GET_TRANSFORM_CONSULTATION_PRODUCT_CODES, GET_TRANSFORM_NC_CONSULTATION_PRODUCT_CODES, GET_TRANSFORM_FC_CONSULTATION_PRODUCT_CODES, GET_TRANSFORM_PT_CONSULTATION_PRODUCT_CODES, GET_TRANSFORM_PLUS_CONSULTATION_PRODUCT_CODES -> this.getTransformConsultationProductCodes(userId, subCategoryCode, userContext);
                case GET_TRANSFORM_BOOTCAMP_CONSULTATION_PRODUCT_CODES -> this.getTransformBootcampConsultationProductCodes(userId);
                case GET_TRANSFORM_COACHES, GET_TRANSFORM_PT_COACHES, GET_TRANSFORM_NC_COACHES, GET_TRANSFORM_FC_COACHES, GET_TRANSFORM_PLUS_COACHES, GET_TRANSFORM_BOOTCAMP_COACHES -> this.getTransformAssignedCoaches(userId, subCategoryCode, userContext);
                default -> null;
            };
        });
        requestMap.put(requestType, result);
        return result;
    }

    public synchronized Future<Object> getTransformAgentTypeRequestFuture(RequestType requestType, String agentType, UserContext userContext, Boolean clearCache) {
        if (requestMap.containsKey(requestType)) {
            if (clearCache) {
                requestMap.remove(requestType);
            } else {
                return requestMap.get(requestType);
            }
        }
        String userId = userContext.getUserProfile().getUserId();
        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            // do api call
            return switch (requestType) {
                case GET_TRANSFORM_ACTIVE_CONSULTATIONS_V2 -> this.getTransformProductActiveConsultations(userId, agentType);
                default -> null;
            };
        });
        requestMap.put(requestType, result);
        return result;
    }

    public synchronized Future<Object> getRequestFutureForCSPriorityDelivery(RequestType requestType,
                                                                             UserContext userContext, String reqCityId, String reqPincode, String collectionSlug) {
        String cityId = StringUtils.isNotBlank(reqCityId) ? reqCityId : "";
        String pincode = StringUtils.isNotBlank(reqPincode) ? reqPincode : "";
        String requestCacheKey = getCSRequestCacheKey(requestType, List.of(cityId, pincode, collectionSlug));
        if (csRequestMap.containsKey(requestCacheKey)) {
            log.debug("Working fine for " + requestType.toString());
            return csRequestMap.get(requestCacheKey);
        }
        Future<Object> result = interfaces.getTaskExecutor().submit(() -> {
            return switch (requestType) {
                case GET_CULTSPORT_PRIORITY_DELIVERY_CITIES -> this.getPriorityDeliveryEligibleCitiesForCollection(userContext, collectionSlug);
                case GET_CULTSPORT_CITY_FOR_PINCODE -> this.getCachedCityForPincode(pincode);
                case GET_CULTSPORT_CITY_FROM_REQUEST_BODY -> this.getCityIdFromWidgetRequestBody(userContext, collectionSlug);
                case GET_CULTSPORT_PLP_SELECTED_CITY -> this.getSelectedCityId(cityId, userContext, collectionSlug);
                case GET_CULTSPORT_PRIORITY_DETAILS_FOR_CITY -> this.getCachedPriorityDeliveryDetailsForCity(cityId);
                default -> null;
            };
        });
        csRequestMap.put(requestCacheKey, result);
        return result;
    }

    private String getSelectedCityId(String cityId, UserContext userContext, String collectionSlug) {
        try {
            return this.interfaces.cultsportPLPService.getSelectedCityId(cityId, userContext, collectionSlug);
        } catch (Exception e) {
            this.interfaces.exceptionReportingService
                    .reportException("Error while fetching getSelectedCityId for cityId:" + cityId, e);
        }
        return null;
    }

    private String getCityIdFromWidgetRequestBody(UserContext userContext, String collectionSlug) {
        try {
            return this.interfaces.cultsportPLPService.getCityIdFromWidgetRequestBody(userContext, collectionSlug);
        } catch (Exception e) {
            this.interfaces.exceptionReportingService
                    .reportException("Parsing failed for CultsportPLPRequestBody from request body", e);
        }
        return null;
    }

    private PriorityDeliveryDetailsForCity getCachedPriorityDeliveryDetailsForCity(String cityId) {
        try {
            return this.interfaces.cultsportPLPService.getCachedPriorityDeliveryDetailsForCity(cityId);
        } catch (Exception e) {
            this.interfaces.exceptionReportingService.reportException(
                    "Fetch failed for Same Day Delivery Details for cityId: %s" + cityId + e.toString(), e);
        }
        return null;
    }

    private String getCachedCityForPincode(String pincode) {
        try {
            return this.interfaces.cultsportPLPService.getCachedCityForPincode(pincode);
        } catch (Exception e) {
            this.interfaces.exceptionReportingService
                    .reportException("Failed to fetch city for pincode: %s" + pincode + e.toString(), e);
        }
        return null;
    }

    private List<String> getPriorityDeliveryEligibleCitiesForCollection(UserContext userContext,
                                                                        String collectionSlug) {
        try {
            return this.interfaces.cultsportPLPService.getPriorityDeliveryEligibleCitiesForCollection(userContext,
                    collectionSlug);
        } catch (Exception ex) {
            this.interfaces.exceptionReportingService
                    .reportException("Fetch failed for Priority Delivery Eligible Cities:", ex);
        }
        return null;
    }

    private List<UserPictureEntry> getTransformUserImage(String userId) {
        return interfaces.shifuClient.searchUserPictures(Long.parseLong(userId), UserPictureTag.FIRST);
    }

    private BestPrice getBestPrices(BestPriceRequest bestPricingRequest)
            throws Exception {
        return this.interfaces.offerService.getBestPrices(bestPricingRequest).get();
    }

    private Map<String, CouponInfo> getCouponsForUsers(UserContext userContext)
            throws Exception {
        return interfaces.constelloService.getAllEligibleVouchersForUserFromCache(userContext, "EXPLICITLY_APPLIED_OFFER");
    }

    private UserWodEntry getCurrentDaySmartWorkoutPlanWod(String userId) {
        try {
            return interfaces.circuitBreakerConfiguration.get(USER_FITNESS_SERVICE).executeCallable(() -> interfaces.ufsService.fitnessPlan().getCurrentDayWod(userId, TENANT_AI_TRAINER).get(900, TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
        }
        return null;
    }

    private GetOffersResponse getOffersFromOfferIds(List<String> offerIds)
            throws Exception {
        return this.interfaces.offerService.getOfferByIds(offerIds).get();
    }

    private WalletBalance getFitCashBalance(FitcashRequest fitcashRequest) {
        return this.interfaces.fitcashService.getBalance(fitcashRequest.getUserId(), fitcashRequest.getCurrency());
    }

    private Map<ApiResponseKey, ApiResponseInfo> getHometabCacheData(String userId) throws IOException {
        return this.interfaces.apiResponseCacheService.getPageCacheData(userId, PageKey.HOMETAB);
    }

    private CultSummary getCultSummaryForUser(UserContext userContext) throws Exception {
        return this.interfaces.cultService.getCultSummary(AppUtil.getUserIdOrMockedUserId(userContext));
    }

    private WishlistEntry getUserWishlist(UserContext userContext) throws BaseException {
        return this.interfaces.cultsportUserClient.getWishlistProducts(userContext.getUserProfile().getUserId());
    }

    private Map<String, BulkBookingResponse> getCultUpcomingBookings(UserContext userContext) {

        String userId = userContext.getUserProfile().getUserId();
        ApiResponseKey apiResponseKey = ApiResponseKey.CULT_UPCOMING;
        try {
            Map<ApiResponseKey, ApiResponseInfo> apiResponseCacheDataMap = (Map<ApiResponseKey, ApiResponseInfo>) getRequestFuture(
                    RequestType.HOMETAB_CACHE_DATA, userContext).get();
            if (!apiResponseCacheDataMap.containsKey(apiResponseKey)) {
                return null;
            }

            LocalDate startDate = LocalDate.now(ZoneId.of(userContext.getUserProfile().getTimezone()));
            LocalDate endDate = LocalDate.now(ZoneId.of(userContext.getUserProfile().getTimezone())).plusDays(7);

            Map<String, BulkBookingResponse> bookingResponseMap = this.interfaces.cultService.getAllBooking(Collections.singletonList(userContext.getUserProfile().getUserId()), startDate.toString(), endDate.toString(), true, userContext.getSessionInfo().getDeviceId()).get(1200, TimeUnit.MILLISECONDS);

            if ((MapUtils.isEmpty(bookingResponseMap) || !bookingResponseMap.containsKey(userId)
                    || bookingResponseMap.get(userId) == null ||
                    (CollectionUtils.isEmpty(bookingResponseMap.get(userId).getBookings())
                            && CollectionUtils.isEmpty(bookingResponseMap.get(userId).getWaitlists())))) {
                interfaces.getApiResponseCacheService().removeUpdatedTimeForApiResponseKey(userId, apiResponseKey);
                return null;
            }
            return bookingResponseMap;

        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
            return null;
        }
    }

    private UserProgramCenterResponse getTransformBootcampStartDate(String userId) {
        return interfaces.transformClient.getUserProgramCenter(userId, BundleSubCategoryCode.BOOTCAMP);
    }

    private UserProgramCenterResponse getLiftStartDate(String userId) {
        return interfaces.transformClient.getUserProgramCenter(userId, BundleSubCategoryCode.LIFT);
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    private FitnessReportResponse getCurrentWeekFitnessData(String userId) {
        String startDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        return interfaces.uasFitnessReportClient.getWeeklyAugmentedResponseForUserAndWeekDate(Long.valueOf(userId), startDate).get();
    }

    private SegmentSet<String> getUserSegments(UserContext userContext) throws Exception {
        try {
            return interfaces.segmentationCacheClient.getUserSegments(AppUtil.getUserIdOrMockedUserId(userContext));
        } catch (Exception e) {
            log.error("Failed to evaluate segments for userId:{}, mockUserId:{}, headers:{}, segmentOverride:{}",
                    userContext.getUserProfile().getUserId(), userContext.getMockSegmentUserId(), userContext.getApiHeaders(),
                    userContext.getRequestCache() == null ? "<notEvaluated>" : userContext.getRequestCache().getSegmentOverride(userContext.getUserProfile().getUserId()), e);
            throw e;
        }
    }

    private List<PatientDetail> getAllPatients(String userId) throws Exception {
        return interfaces.albusClient.getAllPatients(userId);
    }

    private List<ActivePackResponse> getTransformActiveBundlePacksForUser(String userId) throws Exception {
        return interfaces.transformClient.getActiveBundlePacksForUser(userId, null,
                CategoryCode.BUNDLE, BundleSubCategoryCode.TRANSFORM);
    }

    private List<ActivePackResponse> getTransformPTActiveBundlePacksForUser(String userId) throws Exception {
        return interfaces.transformClient.getActiveBundlePacksForUser(userId, null,
                CategoryCode.BUNDLE, BundleSubCategoryCode.TRANSFORM_PT);
    }

    private List<ActiveConsultationResponse> getTransformActiveConsultationsForUser(String userId) throws Exception {
        return interfaces.transformClient.getActiveConsultations(userId, null);
    }

    private List<GenericBundleOrderResponse> getTransformMembershipsForUser(String userId) throws Exception {
        BundleSearchRequestParam bundleSearchRequestParam = new BundleSearchRequestParam();
        bundleSearchRequestParam.setSubCategoryCode(BundleSubCategoryCode.TRANSFORM.name());
        bundleSearchRequestParam.setStatus(AlbusBookingState.CONFIRMED.name());
        bundleSearchRequestParam.setCustomerId(userId);
        return interfaces.transformClient.searchBundleOrders(bundleSearchRequestParam);
    }

    private List<GenericBundleOrderResponse> getBootcampMembershipsForUser(String userId) throws Exception {
        BundleSearchRequestParam bundleSearchRequestParam = new BundleSearchRequestParam();
        bundleSearchRequestParam.setSubCategoryCode(BundleSubCategoryCode.BOOTCAMP.name());
        bundleSearchRequestParam.setStatus(AlbusBookingState.CONFIRMED.name());
        bundleSearchRequestParam.setCustomerId(userId);
        return interfaces.transformClient.searchBundleOrders(bundleSearchRequestParam);
    }

    private WaitlistStatus getTransformWaitlistStatus(String userId) throws Exception {
        return interfaces.transformClient.getWaitlistStatusForUser(userId);
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    private Boolean isEligibleForLiveTrial(String userId, UserContext userContext) {
        Tenant tenant = AppUtil.getTenantFromUserContext(userContext);
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) return false;
        if (tenant == Tenant.CUREFIT_APP && !BooleanUtils.isTrue(userContext.getSessionInfo().getIsUserLoggedIn())) {
            return Boolean.TRUE;
        }
        return interfaces.getDiyfsService().isEligibleForTrial(userId,
                Tenant.fromString(AppUtil.getTenantFromUserContext(userContext).toString())).get();
    }

    private String getSegmentOverride(String userId) {
        return interfaces.defaultRedisKeyValueStore.hget(SEGMENT_OVERRIDE_REDIS_KEY, userId);
    }

    private LifestyleScoreEntry getLifestyleScoreUser(String userId) {
        return interfaces.shifuClient.getLatestLifestyleScoreForUser(Long.valueOf(userId), LifestyleScoreType.TRANSFORM);
    }

    private PreSessionValidationResponse getCultROWSessionValidationParams(UserContext userContext) throws Exception {
        String treadUserId = CultROWUtil.getTreadUserProfile(userContext, interfaces).getTread_user_id();
        return interfaces.treadClient
                .getPreSessionValidationData(PreSessionValidationRequest.builder().user_id(treadUserId).build(), null);
    }

    private TreadUserProfile getTreadUserProfile(UserContext userContext) throws Exception {
        return CultROWUtil.getTreadUserProfile(userContext, interfaces);
    }

    private CatalogueProductV2 getGearProduct(String productId, UserContext userContext) {
        try {
            return (CatalogueProductV2) userContext.getRequestCache()
                    .getRequestFuture(RequestType.GET_CULTSPORT_PRODUCT_V3, userContext, productId).get();
        } catch (Exception ex) {
            if (!(ex != null && ex.toString() != null
                    && ex.toString().indexOf("The resource you were looking for could not be found") >= 0)) {
                this.interfaces.exceptionReportingService
                        .reportException("Error while fetching product in cultsport pdp", ex);
            }
        }
        return null;
    }

    private CatalogueProductV2 getCatalogueProductV2(String productId) {
        try {
            return this.interfaces.cultsportPDPService.getCatalogueProductV2(productId, false);
        } catch (Exception ex) {
            if (!(ex.toString() != null
                    && ex.toString().contains("The resource you were looking for could not be found"))) {
                this.interfaces.exceptionReportingService
                        .reportException("Error while fetching product in cultsport pdp with new experiment", ex);
            }
        }
        return null;
    }

    private Map<String, GearProductDto> getGearProductOffer(String productId, UserContext userContext)
            throws Exception {
        return this.interfaces.gearvaultService.getGearProductOffer(userContext,
                userContext.getUserEntryCompletableFuture().get(), List.of(productId));
    }

    private GetOffersResponse getGearBulkOffer(UserContext userContext, String productId) throws Exception {
        return this.interfaces.crossSellRecoBuilderService.getGearBulkOfferIds(userContext,
                this.interfaces.offerService);
    }

    private ProductFeedback getGearProductRating(UserContext userContext, String productId) throws Exception {
        try {
            return this.interfaces.getCultsportFeedbackServiceClient().getAggregateProductFeedback(productId);
        } catch (Exception ex) {
            this.interfaces.exceptionReportingService
                    .reportException("Error while fetching product rating in cultsport pdp", ex);
        }
        return null;
    }

    private FeedbackListResponse getGearProductReview(UserContext userContext, String productId) throws Exception {
        try {
            return this.interfaces.getCultsportFeedbackServiceClient().listProductFeedbacks(productId, 0, 5,
                    FeedbackSort.MOST_POSITIVE, false);
        } catch (Exception ex) {
            this.interfaces.exceptionReportingService
                    .reportException("Error while fetching product review in cultsport pdp", ex);
        }
        return null;
    }

    private CultsportCustomerPhotos getGearProductImageReview(UserContext userContext, String productId)
            throws Exception {
        try {
            return this.interfaces.cultsportPDPService.getGearProductImageReview(userContext, productId, 0, 5, false);
        } catch (Exception ex) {
            this.interfaces.exceptionReportingService
                    .reportException("Error while fetching product review in cultsport pdp", ex);
        }
        return null;
    }

    private UserActionDetails getUserActionDetails(String userId) {
        return this.interfaces.shifuClient.getUserActionDetails(Long.valueOf(userId), TransformUtil.tenant);
    }

    private ChallengeLeaderBoardResponse getChallengesData(String userId) {
        return this.interfaces.shifuClient.getChallengeLeaderBoard(Long.parseLong(userId));
    }

    private Set<String> getImpressionCappedContentIdsForUser(String userId) {
        Map<String, String> contentIdsMap = this.interfaces.cfApiRedisKeyValueStore
                .hgetAll(String.format(IMPRESSION_CAP_REDIS_KEY, userId));
        if (contentIdsMap.containsKey("contentIds") && !StringUtils.isBlank(contentIdsMap.get("contentIds"))) {
            try {
                return objectMapper.readValue(contentIdsMap.get("contentIds"), new TypeReference<Set<String>>() {
                });
            } catch (Exception e) {
                log.error("Error while reading Impression capping data for user {}", userId, e);
            }
        }
        return new HashSet<>();
    }

    private List<UserActiveAgentResponse> getTransformAssignedCoaches(String userId, String subCategoryCode, UserContext userContext) {
        AgentAssignmentRequest agentAssignmentRequest = new AgentAssignmentRequest();
        agentAssignmentRequest.setUserId(userId);
        List<UserActiveAgentResponse> agentsAssignedToUserResponse = new ArrayList<>();
        try {
            if (org.springframework.util.StringUtils.hasLength(subCategoryCode)) {
                agentAssignmentRequest.setSubCategoryCode(subCategoryCode);
                agentsAssignedToUserResponse = interfaces.transformClient.getOrAssignAllRequiredAgentsToUser(agentAssignmentRequest);
            }
        } catch (Exception e) {
            // do nothing
        }

        try {
            if (!TransformUtil.transformNcSubCategoryCode.equals(subCategoryCode)) {
                if (AppUtil.doesUserBelongsToTransformPlusNCPostPurchaseSegment(userContext)) {
                    agentAssignmentRequest.setSubCategoryCode(transformNcSubCategoryCode);
                    List<UserActiveAgentResponse> ncCoaches = interfaces.transformClient.getOrAssignAllRequiredAgentsToUser(agentAssignmentRequest);
                    if (!org.springframework.util.CollectionUtils.isEmpty(ncCoaches)) {
                        agentsAssignedToUserResponse.addAll(ncCoaches);
                    }
                }
            }
        } catch (Exception e) {
            // do nothing
        }

        try {
            if (!TransformUtil.transformFcSubCategoryCode.equals(subCategoryCode)) {
                if (AppUtil.doesUserBelongsToTransformFCPostPurchaseSegment(userContext)) {
                    agentAssignmentRequest.setSubCategoryCode(transformFcSubCategoryCode);
                    List<UserActiveAgentResponse> fcCoaches = interfaces.transformClient.getOrAssignAllRequiredAgentsToUser(agentAssignmentRequest);
                    if (!org.springframework.util.CollectionUtils.isEmpty(fcCoaches)) {
                        agentsAssignedToUserResponse.addAll(fcCoaches);
                    }
                }
            }
        } catch (Exception e) {
            // do nothing
        }


        return new ArrayList<>(agentsAssignedToUserResponse.stream().collect(Collectors.toMap(UserActiveAgentResponse::getAgentType, Function.identity(), (a, b) -> b)).values());
    }

    private List<ConsultationInfo> getTransformConsultationProductCodes(String userId, String subCategoryCode, UserContext userContext) {
        String scc = !StringUtils.isEmpty(subCategoryCode) ? subCategoryCode : TransformUtil.transformSubCategoryCode;
        List<ConsultationInfo> result = new ArrayList<>();
        try {
            result = interfaces.transformClient.getConsultationInfo(userId, scc);
        } catch (Exception e) {
            // do nothing
        }

        try {
            if (!TransformUtil.transformNcSubCategoryCode.equals(subCategoryCode)) {
                if (AppUtil.doesUserBelongsToTransformPlusNCPostPurchaseSegment(userContext)) {
                    List<ConsultationInfo> ncCoaches = interfaces.transformClient.getConsultationInfo(userId, transformNcSubCategoryCode);
                    if (!org.springframework.util.CollectionUtils.isEmpty(ncCoaches)) {
                        result.addAll(ncCoaches);
                    }
                }
            }
        } catch (Exception e) {
            // do nothing
        }

        try {
            if (!TransformUtil.transformFcSubCategoryCode.equals(subCategoryCode)) {
                if (AppUtil.doesUserBelongsToTransformFCPostPurchaseSegment(userContext)) {
                    List<ConsultationInfo> fcCoaches = interfaces.transformClient.getConsultationInfo(userId, transformFcSubCategoryCode);
                    if (!org.springframework.util.CollectionUtils.isEmpty(fcCoaches)) {
                        result.addAll(fcCoaches);
                    }
                }
            }
        } catch (Exception e) {
            // do nothing
        }

        return result;
    }

    private List<ConsultationInfo> getTransformBootcampConsultationProductCodes(String userId) {
        return interfaces.transformClient.getConsultationInfo(userId, TransformUtil.bootcampSubCategoryCode);
    }

    private BookingInfo<DiagnosticStateInfo> getTransformLabBookingData(String userId, UserContext userContext)
            throws ExecutionException, InterruptedException {
        List<PatientDetail> patientDetails = interfaces.transformClient.getAllPatients(userId);
        PatientDetail selfPatient = patientDetails.stream().filter(patient -> patient.getRelationship().equals("Self"))
                .findFirst().orElse(null);
        if (selfPatient != null) {
            LabTestInfo labTestInfo = interfaces.shifuClient.getLabTestDetailsForUser(Long.parseLong(userId), plusSubCategoryCode);
            String labTestProductId = labTestInfo.getProductCode();
            List<BookingInfo<DiagnosticStateInfo>> bookingInfos = interfaces.albusClient
                    .getActiveDiagnosticBookings(userId, selfPatient.getId(), null);
            Date startingDate = new Date();
            startingDate.setTime(1667199960000L); // Milliseconds for 31st Oct 2022
            return bookingInfos.stream()
                    .filter(bookingData -> bookingData.getDiagnosticsTestOrderResponse().get(0).getProductCodes()
                            .contains(labTestProductId) && bookingData.getBooking().getCreatedAt().after(startingDate))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    private BookingInfo<DiagnosticStateInfo> getTransformNCLabBookingData(String userId, UserContext userContext)
            throws ExecutionException, InterruptedException {
        List<PatientDetail> patientDetails = interfaces.transformClient.getAllPatients(userId);
        PatientDetail selfPatient = patientDetails.stream().filter(patient -> patient.getRelationship().equals("Self"))
                .findFirst().orElse(null);
        if (selfPatient != null) {
            LabTestInfo labTestInfo = interfaces.shifuClient.getLabTestDetailsForUser(Long.parseLong(userId), transformNcSubCategoryCode);
            if (labTestInfo != null) {
                String labTestProductId = labTestInfo.getProductCode();
                if (labTestProductId != null) {
                    List<BookingInfo<DiagnosticStateInfo>> bookingInfos = interfaces.albusClient
                            .getActiveDiagnosticBookings(userId, selfPatient.getId(), null);
                    Date startingDate = new Date();
                    startingDate.setTime(1707436800000L); // Milliseconds for 9th Feb 2023, aka supposed launch of pilot NC product
                    return bookingInfos.stream()
                            .filter(bookingData -> bookingData.getDiagnosticsTestOrderResponse().get(0).getProductCodes()
                                    .contains(labTestProductId) && bookingData.getBooking().getCreatedAt().after(startingDate))
                            .findFirst()
                            .orElse(null);
                }
            }
        }
        return null;
    }

    private List<ActiveConsultationResponse> getTransformNutritionistActiveConsultations(String userId) {
        return interfaces.transformClient.getActiveConsultations(userId, List.of(TransformUtil.nutritionistAgentType));
    }

    private List<ActiveConsultationResponse> getTransformLifestyleCoachActiveConsultations(String userId) {
        return interfaces.transformClient.getActiveConsultations(userId, List.of(TransformUtil.coachAgentType));
    }

    private List<ActiveConsultationResponse> getTransformFitnessCoachActiveConsultations(String userId) {
        return interfaces.transformClient.getActiveConsultations(userId, List.of(TransformUtil.fitnessCoachAgentType));
    }

    private List<ActiveConsultationResponse> getTransformPlusLifestyleCoachActiveConsultations(String userId) {
        return interfaces.transformClient.getActiveConsultations(userId, List.of(TransformUtil.plusLifestyleAgentType));
    }

    private List<ActiveConsultationResponse> getTransformPTCoachActiveConsultations(String userId) {
        return interfaces.transformClient.getActiveConsultations(userId, List.of(TransformUtil.ptAgentType));
    }

    private List<ActiveConsultationResponse> getTransformProductActiveConsultations(String userId, String agentType) {
        return interfaces.transformClient.getActiveConsultations(userId, List.of(agentType));
    }

    public List<OfflineFitnessPack> getNonAugmentedPackList(ProductType productType, ProductSubType productSubType, Boolean publicOnly, UserContext userContext, Integer centerServiceId) throws Exception {
        String key = getPackKey(productType, productSubType, publicOnly, userContext, centerServiceId);
        if (packCacheMap.containsKey(key)) {
            return packCacheMap.get(key);
        }
        key = key + "_NOAUGMENTS";
        if (packCacheMap.containsKey(key)) {
            return packCacheMap.get(key);
        }

        List<OfflineFitnessPack> packList = getPackList(key, productType, productSubType, publicOnly, userContext, centerServiceId, false);
        packCacheMap.put(key, packList);
        return packList;
    }

    public List<AugmentedOfflineFitnessPack> getAugmentedPackList(ProductType productType, ProductSubType productSubType, Boolean publicOnly, UserContext userContext, Integer centerServiceId) throws  Exception {
        String key = getPackKey(productType, productSubType, publicOnly, userContext, centerServiceId);
        return getPackList(key, productType, productSubType, publicOnly, userContext, centerServiceId, true).stream().map(pack -> (AugmentedOfflineFitnessPack) pack).collect(Collectors.toList());
    }

    private String getPackKey(ProductType productType, ProductSubType productSubType, Boolean publicOnly, UserContext userContext, Integer centerServiceId) {
        boolean onlyPublicPacks = AuthUtil.isGuestUser(userContext);
        if (!publicOnly && onlyPublicPacks) publicOnly = true;

        String packType = productType.toString() + (Objects.nonNull(productSubType) ? "_" + productSubType.getValue() : "") + "_" + (publicOnly ? "PUBLIC" : "ALL");
        if (Objects.nonNull(centerServiceId)) {
            packType = packType + "_" + centerServiceId;
        }
        return packType;
    }

    private List<OfflineFitnessPack> getPackList(String key, ProductType productType, ProductSubType productSubType, Boolean publicOnly, UserContext userContext, Integer centerServiceId, Boolean withAugments) throws Exception {
        if (packCacheMap.containsKey(key)) return packCacheMap.get(key);
        List<AugmentedOfflineFitnessPack> result = getPackList(userContext, publicOnly, productType, productSubType, centerServiceId, withAugments);
        List<OfflineFitnessPack> converterResult = new ArrayList<>() {{ addAll(result); }};
        packCacheMap.put(key, converterResult);
        return converterResult;
    }

    private List<AugmentedOfflineFitnessPack> getPackList(
        UserContext userContext, Boolean publicOnly, ProductType productType,
        ProductSubType productSubType, Integer centerServiceId, Boolean withAugments
    ) throws Exception {
        UserProfile userProfile = userContext.getUserProfile();
        String userId = userProfile.getUserId();
        String cityId = userProfile.getCity().getCityId();
        Restriction restriction = Restriction.builder().cities(Collections.singletonList(cityId)).build();

        if (Objects.nonNull(centerServiceId)) {
            restriction = Restriction.builder().centers(Collections.singletonList(centerServiceId)).build();
        }

        SegmentSet<String> userPlatformSegmentsSet = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();

        Set<String> pmsRelevantEntries = interfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.PMS);
        Set<String> userPlatformSegments = userPlatformSegmentsSet.getRelevantEntries(pmsRelevantEntries);

        Visibility visibility = AppUtil.isWeb(userContext) ? Visibility.WEBSITE : Visibility.APP;

        if (AuthUtil.isGuestUser(userContext)) publicOnly = true;

        return interfaces.offlineFitnessPackService.searchCachedPacksWithAugments(
            AugmentedPackSearchRequest.builder()
                .namespace(Namespace.OFFLINE_FITNESS)
                .productTypes(Collections.singletonList(productType))
                .productSubType(productSubType)
                .restrictions(restriction)
                .status(Status.ACTIVE)
                .saleEnabled(true)
                .visibility(visibility)
                .userId(publicOnly ? null : userId)
                .isPrivate(publicOnly ? false : null)
                .userSegmentIds(new ArrayList<>(userPlatformSegments))
                .augments(PackAugmentRequest.builder()
                    .context(AugmentContext.builder().userId(userContext.getUserProfile().getUserId()).build())
                    .includeExtraCharges(withAugments)
                    .includeExhaustiveBenefits(withAugments)
                    .build())
                .build()
        ).get();
    }

    public Future<Object> getOfferResponse(ProductType productType, UserContext userContext, List<String> productIds, String cityId, String centerServiceId) {
        String key = productType.toString() + "_" + productIds.stream().sorted().toList() + "_" + cityId + "_" + ((Objects.nonNull(centerServiceId)) ? centerServiceId : "");
        if (dynamicKeyCacheMap.containsKey(key)) {
            return dynamicKeyCacheMap.get(key);
        }

        Future<Object> result = interfaces.getTaskExecutor().submit(() ->
            switch (productType) {
                case FITNESS -> getCultProductPricesResponse(userContext, productIds, cityId, centerServiceId);
                case PLAY -> getPlayProductPricesResponse(userContext, productIds, cityId, centerServiceId);
                case GYMFIT_FITNESS_PRODUCT, LUX_FITNESS_PRODUCT -> getGymfitProductPricesResponse(userContext, productIds, cityId, centerServiceId);
                default -> throw new RuntimeBaseException("ProductType not defined for offers in request cache", AppStatus.SERVER_ERROR);
            }
        );

        dynamicKeyCacheMap.put(key, result);
        return result;
    }

    private CultProductPricesResponse getCultProductPricesResponse(
        UserContext userContext, List<String> productIds, String cityId, String centerServiceId
    ) throws Exception {

        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
        SessionInfo sessionInfo = userContext.getSessionInfo();
        String callSource = AppUtil.callSource(sessionInfo.getApiKey(), interfaces.apiKeyService);

        SegmentSet<String> userPlatformSegmentSet = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();

        Set<String> relevantEntries = interfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.OFFER_SERVICE);
        Set<String> userPlatformSegments = userPlatformSegmentSet.getRelevantEntries(relevantEntries);

        return interfaces.offerService.getCultPackPrices(
            productIds,
            userInfo,
            OrderSource.valueOf(callSource),
            cityId,
            userPlatformSegments,
            null,
            null,
            centerServiceId
        ).get();

    }

    private GymFitProductPricesResponse getGymfitProductPricesResponse(
        UserContext userContext, List<String> productIds, String cityId, String centerServiceId
    ) throws Exception {

        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
        SessionInfo sessionInfo = userContext.getSessionInfo();
        String callSource = AppUtil.callSource(sessionInfo.getApiKey(), interfaces.apiKeyService);

        SegmentSet<String> userPlatformSegmentSet = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();

        Set<String> relevantEntries = interfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.OFFER_SERVICE);
        Set<String> userPlatformSegments = userPlatformSegmentSet.getRelevantEntries(relevantEntries);

        return interfaces.offerService.getGymfitProductPrices(
            productIds,
            userInfo,
            OrderSource.valueOf(callSource),
            cityId,
            userPlatformSegments,
            null,
            centerServiceId
        ).get();

    }

    private PlayProductPricesResponse getPlayProductPricesResponse(
        UserContext userContext, List<String> productIds, String cityId, String centerServiceId
    ) throws Exception {

        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
        SessionInfo sessionInfo = userContext.getSessionInfo();
        String callSource = AppUtil.callSource(sessionInfo.getApiKey(), interfaces.apiKeyService);

        SegmentSet<String> userPlatformSegmentSet = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();

        Set<String> relevantEntries = interfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.OFFER_SERVICE);
        Set<String> userPlatformSegments = userPlatformSegmentSet.getRelevantEntries(relevantEntries);

        return interfaces.offerService.getPlayPackPrices(
            productIds,
            userInfo,
            OrderSource.valueOf(callSource),
            cityId,
            userPlatformSegments,
            null,
            null,
            centerServiceId
        ).get();

    }

}