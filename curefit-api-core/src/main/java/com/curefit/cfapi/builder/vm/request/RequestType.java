package com.curefit.cfapi.builder.vm.request;

public enum RequestType {
    FITCASH,
    HOMETAB_CACHE_DATA,
    CULT_MIND_SUMMARY,
    HAMLET_USER_EXPIREMENTS,
    PLATFORM_SEGMENTS,
    LIVE_ELIGIBLE_FOR_TRIAL,
    SEGMENT_OVERRIDE,
    GET_ALL_PATIENTS,
    GET_TRANSFORM_ACTIVE_PACKS,
    GET_TRANSFORM_ACTIVE_CONSULTATIONS,
    GET_TRANSFORM_WAITLIST_STATUS,
    GET_TRANSFORM_MEMBERSHIPS,
    GET_BOOTCAMP_MEMBERSHIPS,
    GET_LIFESTYLE_SCORE,
    CULT_ROW_CLASS_PARAMS,
    TREAD_USER_PROFILE,
    GET_CULTSPORT_PRIORITY_DELIVERY_CITIES,
    GET_CULTSPORT_PLP_SELECTED_CITY,
    GET_CULTSPORT_CITY_FOR_PINCODE,
    GET_CULTSPORT_PRIORITY_DETAILS_FOR_CITY,
    GET_CULTSPORT_CITY_FROM_REQUEST_BODY,
    GET_CULTSPORT_PLP_SORT_FILTERS,
    GET_CULTSPORT_PRODUCT,
    GET_CULTSPORT_PRODUCT_V3,
    GET_CULTSPORT_APPAREL_PDP_EXP,
    GET_CULTSPORT_PRODUCT_OFFER,
    GET_CULTSPORT_SNAPMINT_EXPERIMENT_ELIGIBILITY,
    GET_GEAR_BULK_OFFER,
    GET_CULTSPORT_PRODUCT_RATING,
    GET_CULTSPORT_PRODUCT_REVIEW,
    GET_CULTSPORT_PRODUCT_IMAGE_REVIEW,
    GET_USER_ACTIVE_ACTIONS,
    GET_CULT_UPCOMING_BOOKINGS,
    GET_CHALLENGES_DATA,
    WIDGET_IMPRESSION_CAP_DATA,
    GET_TRANSFORM_LAB_BOOKING_DATA,
    GET_LIFESTYLE_COACH_ACTIVE_CONSULTATIONS,
    GET_PLUS_LIFESTYLE_COACH_ACTIVE_CONSULTATIONS,
    GET_NUTRITIONIST_ACTIVE_CONSULTATIONS,
    GET_FITNESS_COACH_ACTIVE_CONSULTATIONS,
    GET_TRANSFORM_PT_ACTIVE_CONSULTATION,
    GET_TRANSFORM_ACTIVE_CONSULTATIONS_V2,
    GET_TRANSFORM_COACHES,
    GET_TRANSFORM_PT_COACHES,
    GET_TRANSFORM_NC_COACHES,
    GET_TRANSFORM_FC_COACHES,
    GET_TRANSFORM_PLUS_COACHES,
    GET_TRANSFORM_BOOTCAMP_COACHES,
    GET_TRANSFORM_CONSULTATION_PRODUCT_CODES,
    GET_TRANSFORM_NC_CONSULTATION_PRODUCT_CODES,
    GET_TRANSFORM_FC_CONSULTATION_PRODUCT_CODES,
    GET_TRANSFORM_PT_CONSULTATION_PRODUCT_CODES,
    GET_TRANSFORM_PLUS_CONSULTATION_PRODUCT_CODES,
    GET_TRANSFORM_BOOTCAMP_CONSULTATION_PRODUCT_CODES,

    GET_TRANSFORM_USER_IMAGE,
    GET_BOOTCAMP_START_DATE,
    GET_LIFT_START_DATE,
    GET_FOOTWEAR_PDP_EXP,
    GET_CURRENT_WEEK_FITNESS_REPORT,
    GET_CULTSPORT_PLP_PAGE_EXP,
    GET_CS_BLOG_ARTICLE,
    GET_CS_BLOG_CATEGORIES,
    GET_CS_BLOG_TAGS,
    GET_PERF_PDP_EXP,
    GET_PERF_NAVIGATION_EXP,
    GET_BEST_PRICES,
    GET_ELIGIBLE_COUPONS_FOR_USER,
    GET_TRAINER_ATTIRE_EXP,
    GET_OFFERS_FROM_OFFERID,
    GET_TRANSFORM_NC_LAB_BOOKING_DATA,
    GET_TRANSFORM_PT_PACKS,
    GET_SMART_WORKOUT_PLAN_CURRENT_DAY_WOD,
    GET_CS_WISHLIST,
    CS_SHOPIFY_WEBVIEW_ELIGIBILITY,
    CULT_UNBOUND_WORKOUT_IDS,
    CULT_UNBOUND_CHAMPIONSHIP_WORKOUT_IDS,
    GET_GX_TRIALS,
    GET_GYM_TRIALS,
    GET_PLAY_TRIALS,
    GET_LUX_TRAILS,
    USER_STREAK_DETAILS,
    USER_STREAK_DETAILS_WITH_ACTIVITY_MAP
}