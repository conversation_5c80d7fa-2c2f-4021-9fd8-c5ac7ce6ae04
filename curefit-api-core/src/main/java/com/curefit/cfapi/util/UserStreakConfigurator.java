package com.curefit.cfapi.util;

import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.widgets.dailystreak.UserStreakConfig;
import com.curefit.uas.responses.StreakResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * Service class for configuring user streak widgets.
 * Creates and configures UserStreakConfig objects based on user streak data.
 */
@Slf4j
public final class UserStreakConfigurator {
    
    private UserStreakConfigurator() {
        // Prevent instantiation
    }
    
    /**
     * Creates a UserStreakConfig based on the user's streak data.
     */
    public static UserStreakConfig createUserStreakConfig(StreakResponse userStreakData, Long streakRepairExpiryEpoch, Long streakRepairConsumedEpoch, String streakRepairExpiryMonth) {
        if (userStreakData == null) {
            return createDefaultUserStreakConfig();
        }

        UserStreakConfig config = new UserStreakConfig();
        StreakStateType stateType = StreakStateDeterminer.determineStreakStateType(userStreakData, streakRepairExpiryEpoch, streakRepairConsumedEpoch);
        CuroState curoState = CuroStateFactory.getCuroStateForType(stateType);

        config.setStreakStateType(stateType);
        setDefaultGradients(config);
        configureByStateType(config, stateType, userStreakData);
        setCuroStateData(config, curoState);
        handleRepairAwardedBannerText(config, stateType, streakRepairExpiryMonth);

        return config;
    }

    private static void handleRepairAwardedBannerText(UserStreakConfig config, StreakStateType stateType, String streakRepairExpiryMonth){
        if(stateType.equals(StreakStateType.REPAIR_AWARDED) && streakRepairExpiryMonth != null){
            String bannerText = config.getBannerHeaderText().getText();
            String newBannerText = bannerText.replace("$repair_date", streakRepairExpiryMonth);
            config.getBannerHeaderText().setText(newBannerText);
        }
    }

    private static void setDefaultGradients(UserStreakConfig config) {
        config.setCalendarGradientColors(StreakGradients.DEFAULT_BACKGROUND);
        config.setWeeklyProgressGradientColors(StreakGradients.DEFAULT_BACKGROUND);
    }
    
    private static void configureByStateType(UserStreakConfig config, StreakStateType stateType, StreakResponse userStreakData) {
        switch (stateType) {
            case PAUSED -> configurePausedState(config);
            case ACTIVE_ONBOARDING, ACTIVE, REPAIR_CONSUMED -> configureActiveState(config, userStreakData);
            case GOAL_REACHED, INACTIVE, INACTIVE_ONE_REST_DAY, INACTIVE_ZERO_REST_DAYS, STREAK_LOST, REPAIR_AWARDED ->
                configureInactiveState(config, stateType);
        }
    }
    
    private static void configurePausedState(UserStreakConfig config) {
        config.setCalendarGradientColors(StreakGradients.PAUSED_CALENDAR);
        config.setBannerGradientColors(StreakGradients.PAUSED_BANNER);
        config.setBlackTheme(true);
    }
    
    private static void configureActiveState(UserStreakConfig config, StreakResponse userStreakData) {
        int numberOfActivities = StreakActivityChecker.getNumberOfActivitiesDoneToday(userStreakData);
        
        config.setTextWidgetHeading(CFTextData.builder()
            .text("Congratulations!")
            .color("#FFFFFF")
            .typeScale("P5")
            .build());
            
        config.setTextWidgetSubHeading(CFTextData.builder()
            .text("You have completed\n" + numberOfActivities + " activity" + (numberOfActivities != 1 ? "ies" : "") + " today")
            .typeScale("H1")
            .alignment("center")
            .build());
            
        config.setBannerGradientColors(StreakGradients.COMPLETION_SUCCESS);
    }
    
    private static void configureInactiveState(UserStreakConfig config, StreakStateType stateType) {
        log.info("<DS> todayStreakDayType: {}", stateType);
        
        config.setTextWidgetHeading(CFTextData.builder()
            .text("To keep your streak going")
            .color("#FFFFFF")
            .typeScale("P5")
            .build());
            
        config.setTextWidgetSubHeading(CFTextData.builder()
            .text("Do any of the following\nactivities today")
            .typeScale("H1")
            .alignment("center")
            .build());

        setInactiveStateGradient(config, stateType);
    }
    
    private static void setInactiveStateGradient(UserStreakConfig config, StreakStateType stateType) {
        var gradient = switch (stateType) {
            case INACTIVE_ZERO_REST_DAYS, INACTIVE_ONE_REST_DAY -> StreakGradients.NO_REST_AVAILABLE;
            case STREAK_LOST -> StreakGradients.STREAK_LOST;
            default -> StreakGradients.NEUTRAL;
        };
        config.setBannerGradientColors(gradient);
    }
    
    private static void setCuroStateData(UserStreakConfig config, CuroState curoState) {
        if (curoState != null) {
            config.setBannerImageUrl(curoState.getImageUrl());
            config.setBannerHeaderText(CFTextData.builder()
                .text(curoState.getMessage())
                .color("#FFFFFF")
                .typeScale("P5")
                .alignment("center")
                .maxLine("4")
                .build());
        }
    }
    
    private static UserStreakConfig createDefaultUserStreakConfig() {
        UserStreakConfig config = new UserStreakConfig();
        config.setCalendarGradientColors(StreakGradients.DEFAULT_BACKGROUND);
        config.setWeeklyProgressGradientColors(StreakGradients.DEFAULT_BACKGROUND);
        config.setBannerGradientColors(StreakGradients.COMPLETION_SUCCESS);
        config.setBannerImageUrl("image/daily-streak/mock_daily_streak.png");
        config.setTextWidgetHeading(CFTextData.builder()
            .text("Ready to start")
            .color("#FFFFFF")
            .typeScale("P5")
            .build());
        config.setTextWidgetSubHeading(CFTextData.builder()
            .text("Begin your fitness journey today")
            .build());
        config.setBannerHeaderText(CFTextData.builder()
            .text("Hey there! Ready to start your fitness journey? Let's build your first streak together!")
            .color("#FFFFFF")
            .typeScale("P5")
            .alignment("center")
            .build());
        return config;
    }
} 