package com.curefit.cfapi.util;

import com.curefit.alfred.models.order.OfferInfo;
import com.curefit.alfred.models.order.Order;
import com.curefit.alfred.models.order.ProductSnapshot;
import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.common.DiscountBreakUpInfo;
import com.curefit.cfapi.model.internal.common.PriceComponent;
import com.curefit.cfapi.model.internal.fitstore.*;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionMeta;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.fitstore.FitInfo;
import com.curefit.cfapi.pojo.vm.widget.Text.TextItem;
import com.curefit.cfapi.pojo.vm.widget.Text.TextItemParent;
import com.curefit.cfapi.pojo.vm.widget.Text.TextStyle;
import com.curefit.cfapi.util.cultsport.CultsportUtil;
import com.curefit.cfapi.widgets.cultsport.common.CultsportPLPBulletInfoConfig;
import com.curefit.common.data.exception.BaseException;
import com.curefit.cultsport.user.client.CultsportUserClient;
import com.curefit.cultsport.user.common.dto.WishlistEntry;
import com.curefit.location.models.UserDeliveryAddress;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.common.BillingWidget;
import com.curefit.cfapi.widgets.common.GearOptionSelector;
import com.curefit.cfapi.widgets.cultsport.common.CultsportProductHightlightInfoItem;
import com.curefit.cfapi.widgets.cultsport.common.ModifyOrderType;
import com.curefit.cfapi.widgets.cultsport.order.CultsportModifyAddressWidget;
import com.curefit.cfapi.widgets.cultsport.order.CultsportModifyOrderReasonWidget;
import com.curefit.cfapi.widgets.cultsport.order.CultsportOrderProductCardWidget;
import com.curefit.cfapi.widgets.cultsport.pdp.CultsportProductAddOnWidget.CultsportProductAddOnItem;
import com.curefit.cfapi.widgets.fitstore.PriceWithDiscount;
import com.curefit.cfapi.widgets.fitstore.ProductBrowseAnnotation;
import com.curefit.cfapi.widgets.fitstore.ProductBrowseItem;
import com.curefit.cfapi.widgets.fitstore.ProductInfoItem;
import com.curefit.falcon.Location;
import com.curefit.gearvault.models.*;
import com.curefit.product.ProductPrice;
import com.curefit.product.models.OrderProductOptions;
import com.curefit.product.models.ProductOffersInfo;
import com.google.gson.Gson;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
public class FitstoreUtil {

    public static final String CUSTOMER_CARE_EMAIL = "<EMAIL>";
    private static final String GEAR_PRODUCT_ID_PREFIX = "GEAR-";

    public static final String REFUND_INFO_TEXT = "The refund amount will return to original mode of payment in 5-7 business days. Details will be sent to registered mobile number and mail ID";
    public static final String RETURN_INFO_TEXT = "Please note we accept returns if the product is unused and is in its original condition";
    public static final String EXCHANGE_INFO_TEXT = "Please note we accept exchange if the product is unused and is in its original condition";

    public static final String CANCEL_REASON_TITLE = "Reason for Cancellation";
    public static final String RETURN_REASON_TITLE = "Reason for Return";
    public static final String REPLACE_REASON_TITLE = "Reason for Exchange";
    public static final String REASON_SUBTITLE = "Let us know what went wrong. This will help us improve future services";

    public static final String FITSTORE_PRODUCT_NON_RETURNABLE_INFO = "Please note that some of the products in your cart are non-returnable";

    public static final String FITSTORE_PRODUCT_NON_DELIVERY_INFO = "We are unable to deliver at your selected address at the moment. Please select another address.";

    public static final String FITSTORE_PRODUCT_UNAVAILABLE_INFO = "We are unable to deliver some products at this address. Please select another address.";

    public static final String FITSTORE_COVID_DELIVERY_DELAY_INFO = "Due to COVID-19 restrictions in certain areas, delivery might be delayed. We deeply regret the inconvenience caused";

    public static final String ITEM_UNAVAILABLE_INFO = "SELECTED QUANTITY NOT AVAILABLE";

    public static final String ITEM_OOS_INFO = "OUT OF STOCK";

    public static final String ITEM_NON_RETURNABLE_INFO = "NON-RETURNABLE ITEM";

    public static final String ADDRESS_NOT_SERVICEABLE = "ADDRESS IS NOT SERVICEABLE";

    public static final String FITSTORE_CART_PAGE_NAME = "fitstorecart";

    public static final String PAYMENT_VERIFICATION_ERROR = "Could not verify payment! Any amount deducted will be refunded within 7 days.";

    public static final String EMPTY_CATALOGUE_ERROR = "We seem to be all sold out. Please check some of our other categories";

    public static final String INVALID_SEARCH_QUERY_ERROR = "Uh-oh! Unable to find what you searched for. Please try again, or go back.";

    public static final String INVALID_FILTERS_ERROR = "We couldn't find any products for your selection. Try removing some filters.";

    public static final String SUNSET_MODE_ERROR = "Please visit cure.fit app to complete your transaction.";

    public static final String UNSUPPORTED_FEATURE_ERROR = "This feature is no longer supported.";

    public static final String FITSTORE_MAX_CART_QUANTITY_ERROR = "Your cart has crossed the maximum limit which is 25 items";

    public static final String FITSTORE_MAX_ITEM_QUANTITY_ERROR = "Maximum quantity allowed for this item is 10";

    public static final String FITSTORE_MAX_ITEM_QUANTITY_TOP_ERROR = "Your cart has crossed the maximum individual item limit which is 10 items";

    public static final String TRAINER_OFFER_CONSTRAINT_STRING = "Please add 3 T-shirts, 1 bottom and 1 shorts. Items outside these limits will be chargeable.";

    public static final Integer MAX_CART_QUANTITY = 25;

    public static final Integer MAX_ITEM_QUANITY = 10;

    public static final String STORE_FLUTTER_EXPERIENCE = "18704a0f-0f50-484d-afef-44ce39abaf92"; // for plp, pdp, cart
                                                                                                  // and address screen

    public static final String TREASURE_ELIGIBLE_USER_SEGMENT = "9d4cdb00-292a-4108-a75e-b089b52244a1"; // for treasure
                                                                                                        // hunt
                                                                                                        // evaluation

    public static final String FITSTORE_COLLECTION_SLUGS_FILTER_KEY = "attributes.collection_slugs";

    public static final String FITSTORE_DYNAMIC_COLLECTION_SLUG = "dynamic_collection";

    public static final String FITSTORE_RANDOM_DYNAMIC_COLLECTION_SLUG = "dynamic_collection_random";

    public static final String FITSTORE_DYNAMIC_COLLECTION_PRODUCT_ID_FILTER_KEY = "id";

    public static final String FITSTORE_HIGHLIGHTED_FILTER_KEY = "attributes.articleType";

    public static final String FITSTORE_HIGHLIGHTED_PRICE_FILTER_KEY = "attributes.priceBucket";

    public static final String FITSTORE_HIGHLIGHTED_WEIGHT_FILTER_KEY = "attributes.maxWeightSupportGroup";

    public static final List<String> FITSTORE_MULTI_SELECT_NON_ORDERING_FILTER_KEYS = List.of("attributes.priceBucket");

    public static final List<String> FITSTORE_FORBIDDEN_FILTERS = Arrays.asList(new String[] { "attributes.category" });

    private static final HashMap<String, FitInfo> FIT_INFO_MAP;

    public static final String CS_PRODUCT_DESCRIPTION_BULLET_SEPARATOR = "||";
    public static final String CS_PRODUCT_DESCRIPTION_BULLET_SEPARATOR_REGEX = "\\|\\|";

    public static final String CS_PRODUCT_DESCRIPTION_PARA_SEPARATOR = "|||";
    public static final String CS_PRODUCT_DESCRIPTION_PARA_SEPARATOR_REGEX = "\\|\\|\\|";

    public static List<String> FITSTORE_HEAVY_EQUIPMENT_CATEGORIES = new ArrayList<>() {
        {
            add("LARGE EQUIPMENT");
        }
    };

    public static String CS_TIMELINE_ACTIVE_COLOR = "#188A5A";
    public static String CS_TIMELINE_INACTIVE_COLOR = "#EFEFEF";

    public static String CS_ACTIVE_LINK_COLOR = "#2588E3";
    public static String CS_STATUS_POSITIVE_COLOR = "#188A5A";
    public static String CS_STATUS_NEGATIVE_COLOR = "#FF3A6A";
    public static String CS_STATUS_NEUTRAL_COLOR = "#454748";

    public static String CS_ORDER_CANCELED_STATE = "canceled";
    public static String CS_ORDER_PAYMENT_PENDING_STATE = "payment_pending";
    public static String CS_ORDER_PAYMENT_UNDER_REVIEW_STATE = "payment_under_review";
    public static String CS_ORDER_PAYMENT_FAILED = "payment_failed";

    public static List<String> FITSTORE_NO_INSTALLATION_SLA_ARTICLE_TYPES = new ArrayList<>() {
        {
            add("Single Speed Cycle");
            add("Kids Cycle");
        }
    };
    static {
        FIT_INFO_MAP = new HashMap<>();
        FitInfo slimInfo = new FitInfo();
        slimInfo.setTitle("This is a slim fit product");
        slimInfo.setDescription("It sits close to your body. Choose a larger size if looking for a comfortable fit.");
        slimInfo.setIcon("/cultgear-content/assets/fit-slim.png");
        FitInfo relaxedInfo = new FitInfo();
        relaxedInfo.setTitle("This is a relaxed fit product");
        relaxedInfo.setDescription("It sits loosely on your body. Choose a smaller size if looking for a slimmer fit.");
        relaxedInfo.setIcon("/cultgear-content/assets/fit-relaxed.png");
        FIT_INFO_MAP.put("slim", slimInfo);
        FIT_INFO_MAP.put("relaxed", relaxedInfo);
    }

    private FitstoreUtil() {
    }

    private static String getGearMicroAppPrefixedUrl(String url) {
        return "gear-micro-" + url;
    }

    public static List<String> getAddOnCollections(EnvironmentService environmentService) {
        List<String> collection = new ArrayList<>();
        if (environmentService.isProduction() || environmentService.isAlpha()) {
            collection.add("addons");
        } else {
            collection.add("street-men");
        }
        return collection;
    }

    public static String gearPrefixedProductId(CartItem item) {
        return GEAR_PRODUCT_ID_PREFIX + item.getSku();
    }

    public static String gearPrefixedProductId(CatalogueVariantV2 item) {
        return GEAR_PRODUCT_ID_PREFIX + item.getSku();
    }

    public static String getNonGearPrefixedProductId(String gearPrefixedSku) {
        return gearPrefixedSku.replace(GEAR_PRODUCT_ID_PREFIX, "");
    }

    public static FitInfo getFitInfo(String type) {
        return FIT_INFO_MAP.get(type);
    }

    public static Action getFitStoreProductPageAction(String productId) {
        return new Action("curefit://" + FitstoreUtil.getGearMicroAppPrefixedUrl("products") + "?id=" + productId,
                ActionType.NAVIGATION);
    }

    public static Action getFitStoreFlutterProductPageAction(String productId) {
        return new Action("curefit://fl_storepdp" + "?id=" + productId, ActionType.NAVIGATION);
    }

    public static Action getFitStoreFlutterTreasureHuntAction(String productId) {
        return new Action("curefit://treasurehunt?productId=" + productId, ActionType.SSOWEB);
    }

    public static Action getFitstoreWebTreasureHuntAction(String productId) {
        return new Action("curefit://treasurehunt?productId=" + productId, ActionType.NAVIGATION);
    }

    public static Action getFitStoreFlutterCartAction(String title, Integer cartItemCount) {
        Action action = new Action("curefit://fl_storecart?addons=true&listingBrand=GEAR_FIT", title,
                ActionType.NAVIGATION);
        if (cartItemCount != null) {
            action.setMeta(new HashMap<>() {
                {
                    put("cartItemCount", cartItemCount);
                }
            });
        }
        return action;
    }

    public static Action getFitStoreFlutterSearchAction() {
        return new Action("curefit://wellnesssearchpage?pageId=CultGear&subVertical=FITSTORE&placeHolderText=Search",
                ActionType.NAVIGATION);
    }

    public static Action getFitStoreCartAction(String title) {
        return new Action("curefit://" + FITSTORE_CART_PAGE_NAME + "?addons=true&listingBrand=GEAR_FIT", title,
                ActionType.NAVIGATION);
    }

    public static Action getFitStoreCollectionListingAction(String collectionId, String title) {
        return new Action("curefit://gearlist?id=" + collectionId + "&pageName=" + collectionId, title,
                ActionType.NAVIGATION);
    }

    public static Action getFitStoreAddressSelectionAction(UserContext userContext, String title) {
        String url = AppUtil.isFromFlutterAppFlow(userContext) ? "curefit://fl_selectaddress"
                : "curefit://selectaddress";
        return new Action(url + "?listingBrand=GEAR_FIT&sourcePageType=" + FITSTORE_CART_PAGE_NAME, title,
                ActionType.NAVIGATION);
    }

    public static Action getFitStoreCartPayFreeAction(String title) {
        return new Action("", title, ActionType.GEAR_PAY_FREE);
    }

    public static Action getFitStoreCartPayAction(String title, String pageFrom) {
        return new Action("curefit://payment?pageFrom=" + pageFrom, title, ActionType.GEAR_CART_PAY_NOW);
    }

    public static List<CartItem> getUnifiedCartItems(List<CartItem> cartResponse) {
        Map<String, Integer> productQuantityMap = new HashMap<String, Integer>();
        Map<String, CartItem> productItemMap = new HashMap<String, CartItem>();
        cartResponse.stream().forEach(item -> {
            if (productQuantityMap.containsKey(item.getSku())) {
                productQuantityMap.put(item.getSku(),
                        Integer.sum(item.getQuantity(), productQuantityMap.get(item.getSku())));
            } else {
                productItemMap.put(item.getSku(), item);
                productQuantityMap.put(item.getSku(), item.getQuantity());
            }
        });
        List<CartItem> distinctItems = productItemMap.values().stream().collect(Collectors.toList());
        return distinctItems.stream().map(item -> {
            if (productQuantityMap.containsKey(item.getSku())) {
                item.setQuantity(productQuantityMap.get(item.getSku()));
            }
            return item;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static List<GearOptionSelector> buildSizeSelector(CatalogueProductV2 product, FitstorePDPRequest request,
            boolean isCart) {
        // this is only being used by Cart screen and PDP
        return product.getVariants().stream().map(variant -> {
            // hardcoded logic that only gets the size option of every variant
            CatalogueVariantOption sizeOption = getSizeOptionForVariant(variant);
            String title = sizeOption != null
                    ? (sizeOption.getPresentation() != null ? sizeOption.getPresentation() : sizeOption.getName())
                    : null;
            if (title == null) {
                // not showing a variant if it does not have a defined name/presentation
                return null;
            }
            return GearOptionSelector.builder()
                    // ToDo::Dheeraj::check with is_orderable usage
                    .outOfStock(!variant.isOrderable() || variant.getTotal_on_hand() <= 0)
                    .isSelected(Objects.equals(variant.getSku(), request.getSku()))
                    .title(title)
                    .sku(variant.getSku())
                    .action(
                            Action.builder()
                                    .actionType(isCart ? ActionType.REFRESH_GEAR_CART_SCREEN : ActionType.REFRESH_PAGE)
                                    .meta(new HashMap<>() {
                                        {
                                            put("sku", variant.getSku());
                                            put("quantity", 1);
                                            put("id", String.valueOf(product.getId()));
                                        }
                                    })
                                    .build())
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static CatalogueVariantOption getSizeOptionForVariant(@NonNull CatalogueVariantV2 variant) {
        List<CatalogueVariantOption> options = Arrays.stream(variant.getOption_values())
                .filter(Objects::nonNull)
                .filter(opt -> GearMicroappUtil.VariantPermittedOptionPattern.matcher(opt.getOption_type_presentation())
                        .find())
                .collect(Collectors.toList());
        if (options.size() > 0) {
            return options.get(0); // expecting only one size attribute per variant
        }
        return null;
    }

    public static Location getDefaultLocationForSearch() {
        return new Location("IN", "Bangalore");
    }

    public static String getProductSlugFallback(String slug) {
        if (StringUtils.isEmpty(slug)) {
            return "v2";
        }
        return slug;
    }

    public static Action getFistoreWebPdpAction(String productId, String productSlug, String itemListName,
            String itemListId, Integer index, String locationId) {
        String url = "curefit://detailspage";
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        nameValuePairs.add(new BasicNameValuePair("productId", productId));

        if (StringUtils.isNotBlank(productSlug)) {
            nameValuePairs.add(new BasicNameValuePair("productSlug", productSlug));
        }

        if (index != null && index >= 0) {
            nameValuePairs.add(new BasicNameValuePair("index", index.toString()));
        }

        if (StringUtils.isNotBlank(locationId)) {
            nameValuePairs.add(new BasicNameValuePair("locationId", locationId));
        }

        if (StringUtils.isNotBlank(itemListName)) {
            nameValuePairs.add(new BasicNameValuePair("itemListName", itemListName));
        }

        if (StringUtils.isNotBlank(itemListId)) {
            nameValuePairs.add(new BasicNameValuePair("itemListId", itemListId));
        }

        url += ActionUtil.formatQueryParamWithQuerySeparator(nameValuePairs);

        return new Action(url, "EXPLORE", ActionType.NAVIGATION);
    }

    public static String getImageUrlFromGearCatalogueProduct(CatalogueProductV2 product) {
        String imageUrl = null;
        if (product.getImage() != null) {
            imageUrl = product.getImage();
        } else {
            GearImage image = Arrays.stream(product.getMaster().getImages()).findFirst().orElse(null);
            imageUrl = image != null ? image.getProduct_url() : null;
        }
        return imageUrl;
    }

    public static String getImageUrlFromFitstorePLPProduct(FitstorePLPProduct product) {
        String imageUrl = null;
        if (product.getAttributes().getImageUrl() != null && product.getAttributes().getImageUrl().get(0) != null) {
            imageUrl = product.getAttributes().getImageKey().get(0);
        }
        return imageUrl;
    }

    public static String generateCatalogProductHash(@NonNull String id) {
        return GEAR_PRODUCT_ID_PREFIX + "PRODUCT-" + id;
    }

    public static List<ProductInfoItem> getPLPProductInfoItems(FitstorePLPProductAttributesResponse attributes,
            Map<String, List<CultsportPLPBulletInfoConfig>> plpBulletInfoConfigMap) {
        if (attributes != null && StringUtils.isNotEmpty(attributes.getArticleType())
                && plpBulletInfoConfigMap.get(attributes.getArticleType()) != null
                && attributes.getProductSpecifications() != null
                && CollectionUtils.isNotEmpty(attributes.getProductSpecifications())) {
            Map<String, String> productSpecs = new HashMap<>();
            attributes.getProductSpecifications().forEach(x -> productSpecs.put(x.getKey(), x.getValue()));
            return plpBulletInfoConfigMap.get(attributes.getArticleType()).stream().map(config -> {
                if (config != null && StringUtils.isNotEmpty(productSpecs.get(config.getFieldKey()))) {
                    return ProductInfoItem.builder().title(config.getFieldKey())
                            .value(productSpecs.get(config.getFieldKey())).imageUrl(config.getImageUrl()).build();
                }
                return null;
            }).filter(Objects::nonNull).toList();
        }
        return new ArrayList<>();

    }

    public static ProductBrowseItem getProductBrowseItem(UserContext userContext, FitstorePLPProduct product,
            ServiceInterfaces interfaces) {
        String imageUrl = CollectionUtils.isNotEmpty(product.getAttributes().getImageKey())
                ? product.getAttributes().getImageKey().get(0)
                : product.getAttributes().getImageUrl().get(0);
        if (StringUtils.isEmpty(imageUrl)) {
            CatalogueProductV2 p = null;
            try {
                // p = interfaces.gearvaultService.getCatalogueProduct(product.getId(),
                // userContext.getSessionInfo().getOrderSource());
                p = (CatalogueProductV2) userContext.getRequestCache()
                        .getRequestFuture(RequestType.GET_CULTSPORT_PRODUCT, userContext, product.getId()).get();
            } catch (Exception e) {
                e.printStackTrace();
            }
            imageUrl = FitstoreUtil.getImageUrlFromGearCatalogueProduct(p);
        }

        List<ProductBrowseAnnotation> annotations = product.getAttributes().getAnnotations() != null
                && CollectionUtils.isNotEmpty(Arrays.asList(product.getAttributes().getAnnotations()))
                        ? Arrays.stream(product.getAttributes().getAnnotations().toArray()).map(annotation -> {
                            return ProductBrowseAnnotation.builder()
                                    .color("#736CF0")
                                    .borderColor("#E3E2FC")
                                    .backgroundColor("#E3E2FC")
                                    .text((String) annotation)
                                    .build();
                        }).collect(Collectors.toList())
                        : new ArrayList<>();

        BigDecimal mrp = product.getAttributes().getPrice().getMrp() > 0
                ? BigDecimal.valueOf(product.getAttributes().getPrice().getMrp()).setScale(0, RoundingMode.HALF_UP)
                : BigDecimal.valueOf(0.0);
        BigDecimal listingPrice = BigDecimal.valueOf(product.getAttributes().getPrice().getListingPrice()).setScale(0,
                RoundingMode.HALF_UP);
        int discount = mrp.doubleValue() > 0
                ? (int) Math.round(((mrp.doubleValue() - listingPrice.doubleValue()) * 100) / mrp.doubleValue())
                : 0;

        PriceWithDiscount priceWithDiscount = new PriceWithDiscount();
        priceWithDiscount.setListingPrice(listingPrice);
        priceWithDiscount.setMrp(mrp);
        priceWithDiscount.setPrimaryPrice(OrderUtil.RUPEE_SYMBOL + " " + listingPrice);
        priceWithDiscount.setSecondaryPrice(discount > 0 ? (OrderUtil.RUPEE_SYMBOL + " " + mrp) : null);
        priceWithDiscount.setCurrency("INR");
        if (discount > 0) {
            priceWithDiscount.setDiscountPercent(discount);
        }

        // return null if don't want to show wishlist
        Boolean isProductInWishlist = null;

        if (AppUtil.shouldEnableWishlist(interfaces.segmentEvaluatorService, interfaces.environmentService,
                userContext)) {
            Set<Integer> userWishlist = new HashSet<>();

            try {
                userWishlist = ((WishlistEntry) userContext.getRequestCache().getRequestFuture(
                        RequestType.GET_CS_WISHLIST,
                        userContext).get()).getProductIds();
            } catch (Exception e) {
                // if there is an exception, we can show wislhist status as false
                interfaces.exceptionReportingService.reportException(e);
            }

            isProductInWishlist = userWishlist.contains(Integer.valueOf(product.getId()));

        }

        ProductBrowseItem productBrowseItem = ProductBrowseItem
                .builder()
                .id(product.getId())
                .brand(product.getAttributes().getBrand())
                .title(product.getTitle())
                .imageUrl(imageUrl)
                .imageUrls(product.getAttributes().getImageKey())
                .annotations(annotations)
                .price(priceWithDiscount)
                .action(product.getAction())
                .tag(product.getAttributes().getTag())
                .rating(product.getAttributes().getRating())
                .ratingCount(product.getAttributes().getRatingCount())
                .articleType(product.getAttributes().getArticleType())
                .category(product.getAttributes().getCategory())
                .productInfoItems(product.getAttributes().getProductInfoItems())
                .build();

        if (isProductInWishlist != null) {
            productBrowseItem.setIsInWishlist(isProductInWishlist);
        }

        return productBrowseItem;

    }

    public static FitstoreCartListItem convertFitstorePLPProductToFitstoreCartListItem(
            AtomicReference<Boolean> isFlutterStorePDPSupported,
            FitstorePLPProduct fitstorePLPProduct) {
        FitstoreCartListItem fitstoreCartListItem = new FitstoreCartListItem();
        fitstoreCartListItem.setTitle(fitstorePLPProduct.getTitle());
        fitstoreCartListItem.setImage(FitstoreUtil.getImageUrlFromFitstorePLPProduct(fitstorePLPProduct));

        String discountPercent = fitstorePLPProduct.getAttributes().getPrice().getDiscountText();

        BigDecimal mrp = fitstorePLPProduct.getAttributes().getPrice().getMrp() > 0 ? BigDecimal
                .valueOf(fitstorePLPProduct.getAttributes().getPrice().getMrp()).setScale(0, RoundingMode.HALF_UP)
                : BigDecimal.valueOf(0.0);
        BigDecimal listingPrice = BigDecimal.valueOf(fitstorePLPProduct.getAttributes().getPrice().getListingPrice())
                .setScale(0, RoundingMode.HALF_UP);

        ProductPrice price = new ProductPrice();
        price.setCurrency("INR");
        price.setMrp(mrp);
        price.setListingPrice(listingPrice);
        price.setDiscountText(discountPercent);
        fitstoreCartListItem.setPrice(price);
        fitstoreCartListItem.setProductId(String.valueOf(fitstorePLPProduct.getId()));
        fitstoreCartListItem.setBrand(fitstorePLPProduct.getAttributes().getBrand());
        fitstoreCartListItem.setAction(isFlutterStorePDPSupported.get()
                ? FitstoreUtil.getFitStoreFlutterProductPageAction(String.valueOf(fitstorePLPProduct.getId()))
                : FitstoreUtil.getFitStoreProductPageAction(String.valueOf(fitstorePLPProduct.getId())));
        if (fitstorePLPProduct.getAttributes().getTag() != null) {
            FitStoreTagInfo fitStoreTagInfo = new FitStoreTagInfo();
            fitStoreTagInfo.setText(fitstorePLPProduct.getAttributes().getTag());
            fitStoreTagInfo.setTextColor("#4AB74A");
            fitstoreCartListItem.setTagInfo(fitStoreTagInfo);
        }

        if (fitstorePLPProduct.getAttributes() != null && fitstorePLPProduct.getAttributes().getAnnotations() != null
                && !fitstorePLPProduct.getAttributes().getAnnotations().isEmpty()) {
            fitstoreCartListItem.setAnnotations(fitstorePLPProduct.getAttributes().getAnnotations());
        }

        return fitstoreCartListItem;
    }

    public static Action getCancelReturnReplaceInventoryAction(@NonNull String title, @NonNull ActionType actionType,
            String addressId, @NonNull List<String> inventoryIds, @NonNull String orderId, String contextMessage,
            String totalPrice, String paymentMode, String shipmentId) {
        return Action
                .builder()
                .actionType(actionType)
                .title(title)
                .payload(new HashMap<>() {
                    {
                        put("inventoryUnitId", inventoryIds.get(0));
                        put("addressId", addressId);
                        put("orderId", orderId);
                        put("contextMessage", contextMessage);
                        put("price", totalPrice);
                        put("paymentMode", paymentMode);
                        put("shipmentId", shipmentId);
                    }
                })
                .build();
    }

    public static Action getCancelReplaceReturnInventoryNavigationAction(
            @NonNull String title,
            ModifyOrderType modifyOrderType,
            @NonNull List<String> inventoryIds,
            @NonNull String orderId,
            String iconUrl,
            String discountAmount,
            String shipmentType,
            Boolean isDisabled) {
        if (ModifyOrderType.CANCEL.equals(modifyOrderType)) {
            Boolean isReturnShipmentCancel = !"forward".equalsIgnoreCase(shipmentType);
            return Action
                    .builder()
                    .actionType(ActionType.SHOW_CONFIRMATION_MODAL)
                    .title(title)
                    .iconUrl(iconUrl)
                    .meta(new HashMap<>() {
                        {
                            put("modalTitle", isReturnShipmentCancel
                                    ? ("Are you sure you want to cancel " + shipmentType.toLowerCase() + " request")
                                    : "Are you sure you want to cancel this order.");
                            put("modalText",
                                    isReturnShipmentCancel ? " "
                                            : ("We hate to see you go!.\nYou will loose out on "
                                                    + OrderUtil.RUPEE_SYMBOL + discountAmount + " special discount"));
                            put("modalYesButton", "YES, CANCEL");
                            put("modalCancelButton", "GO BACK");
                            put("imageUrl", isReturnShipmentCancel ? null : "image/cultsport/my_order/sad_face.png");
                            put("action", Action
                                    .builder()
                                    .actionType(ActionType.NAVIGATION)
                                    .url("/modifyorder/" + orderId + "/" + String.valueOf(inventoryIds.get(0)) + "/"
                                            + modifyOrderType.toString())
                                    .title(title)
                                    .iconUrl(iconUrl)
                                    .build());
                        }
                    })
                    .build();
        }

        if (isDisabled) {
            return Action
                    .builder()
                    .title(title)
                    .iconUrl(iconUrl)
                    .disabled(isDisabled)
                    .build();
        }

        return Action
                .builder()
                .actionType(ActionType.NAVIGATION)
                .url("/modifyorder/" + orderId + "/" + String.valueOf(inventoryIds.get(0)) + "/"
                        + modifyOrderType.toString())
                .title(title)
                .iconUrl(iconUrl)
                .build();
    }

    public static Action getCultsportHomePageAction(String title) {
        return Action.builder()
                .url("/")
                .title(title != null ? title : "GO TO HOME PAGE")
                .actionType(ActionType.NAVIGATION)
                .payload(new HashMap<>() {
                    {
                        put("replace", true);
                    }
                })
                .build();
    }

    public static Action getCultsportMyOrdersAction() {
        return Action.builder()
                .url("/orders")
                .title("GO TO MY ORDERS")
                .actionType(ActionType.NAVIGATION)
                .payload(new HashMap<>() {
                    {
                        put("replace", true);
                    }
                })
                .build();
    }

    public static CompletableFuture<BaseWidgetNonVM> getCultsportModifyOrderReasonWidget(List<GearReason> reasons,
            String title, String subTitle) {
        Collections.shuffle(reasons);

        CultsportModifyOrderReasonWidget widget = new CultsportModifyOrderReasonWidget();
        widget.setReasons(reasons);
        widget.setTitle(title);
        widget.setSubTitle(subTitle);
        return CompletableFuture.completedFuture(widget);
    }

    public static CompletableFuture<BaseWidgetNonVM> getCultsportOrderProductInfoWidget(
            GearInventoryUnit inventoryUnit, String orderType) {
        String size = "";
        String image = "";
        if (inventoryUnit.getVariant().getSize() != null) {
            size = inventoryUnit.getVariant().getSize();
        } else if (inventoryUnit.getProduct().getSize() != null) {
            size = inventoryUnit.getProduct().getSize();
        }

        if (inventoryUnit.getVariant().getImage() != null) {
            image = inventoryUnit.getVariant().getImage();
        } else if (inventoryUnit.getProduct().getImage() != null) {
            image = inventoryUnit.getProduct().getImage();
        }

        CultsportOrderProductCardWidget widget = new CultsportOrderProductCardWidget();
        widget.setTitle(inventoryUnit.getProduct().getName());
        widget.setBrand(inventoryUnit.getProduct().getBrand());
        widget.setAction(FitstoreUtil.getFistoreWebPdpAction(inventoryUnit.getProduct().getId().toString(),
                inventoryUnit.getProduct().getSlug(), null, null, null, null));

        if (ModifyOrderType.REPLACE.toString().equalsIgnoreCase(orderType) && inventoryUnit.isReplaceable() && inventoryUnit.getReplaceable_by() != null) {
            String replaceAvailableDate = parseDateIntoMonthDateFormat(inventoryUnit.getReplaceable_by());
            if (replaceAvailableDate != null) {
                widget.setReturnExchangeInfo(CultsportProductHightlightInfoItem
                        .builder()
                        .title("Exchange available till " + replaceAvailableDate)
                        .titleColor("#00BA78")
                        .build());
            }
        } else if (ModifyOrderType.RETURN.toString().equalsIgnoreCase(orderType) && inventoryUnit.isReturnable() && inventoryUnit.getReturnable_by() != null){
            String returnAvailableDate = parseDateIntoMonthDateFormat(inventoryUnit.getReturnable_by());

            if (returnAvailableDate != null) {
                widget.setReturnExchangeInfo(CultsportProductHightlightInfoItem
                        .builder()
                        .title("Return available till " + returnAvailableDate)
                        .titleColor("#00BA78")
                        .build());
            }
        }

        if (StringUtils.isNotEmpty(size)) {
            widget.setSubtitle("Size: " + size);
        }
        if (StringUtils.isNotEmpty(image)) {
            widget.setImageUrl(image);
        }
        return CompletableFuture.completedFuture(widget);
    }

    public static String parseDateIntoMonthDateFormat(String date){
        try {
            String pattern = "MMMMM dd";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
            Date formattedDate = new SimpleDateFormat("yyyy-MM-dd").parse(date);
            return simpleDateFormat.format(formattedDate);
        } catch (Exception ex) {
            return null;
        }

    }

    public static CompletableFuture<BaseWidgetNonVM> getInventoryUnitBillingWidget(
            UserContext userContext,
            List<PriceBreakUpItem> priceBreakUpItemList,
            Boolean isReturnFlow,
            Boolean isCancelFlow,
            String paymentMode,
            String fitCashAmount,
            String netAmount,
            Boolean isFitCashUsed,
            DetailedInventoryUnitItem inventoryUnit) {
        BillingWidget widget = new BillingWidget();
        List<PriceComponent> priceComponents = new ArrayList<>();
        Boolean isCod = FitstoreUtil.isCodPaymentMethod(paymentMode);
        Boolean isFitCashAmountPresent = isFitCashUsed && StringUtils.isNotBlank(fitCashAmount);
        if (CollectionUtils.isNotEmpty(priceBreakUpItemList)) {
            widget.setTitle("Order breakdown");

            priceBreakUpItemList.stream().forEach(priceBreakUpItem -> {
                PriceComponent comp = PriceComponent.builder()
                        .title(priceBreakUpItem.getKey())
                        .value((BooleanUtils.isTrue(priceBreakUpItem.getIs_discount()) ? "-" : "")
                                + OrderUtil.RUPEE_SYMBOL + priceBreakUpItem.getValue())
                        .build();
                priceComponents.add(comp);
            });

            // Setting last item in list a bold
            priceComponents.get(priceComponents.size() - 1).setIsBold(true);

            if (isCancelFlow) {
                if (isCod) {
                    if (isFitCashAmountPresent) {
                        widget.setTitle("Refund details");
                        widget.setSubTitle("Refund will be initiated back to the source account used to pay");
                        PriceComponent comp = PriceComponent.builder()
                                .title("Refund Fitcash")
                                .value(OrderUtil.RUPEE_SYMBOL + fitCashAmount)
                                .isBold(true)
                                .build();
                        priceComponents.add(comp);
                    } else {
                        return CompletableFuture.completedFuture(null);
                    }
                } else if (StringUtils.isNotBlank(netAmount) || isFitCashAmountPresent) {
                    widget.setTitle("Refund details");
                    widget.setSubTitle("Refund will be initiated back to the source account used to pay");
                    if (isFitCashAmountPresent) {
                        PriceComponent comp = PriceComponent.builder()
                                .title("Refund Fitcash")
                                .value(OrderUtil.RUPEE_SYMBOL + fitCashAmount)
                                .isBold(true)
                                .build();
                        priceComponents.add(comp);
                    }
                    if (StringUtils.isNotBlank(netAmount)) {
                        PriceComponent comp = PriceComponent.builder()
                                .title("Refund Amount")
                                .value(OrderUtil.RUPEE_SYMBOL + netAmount)
                                .isBold(true)
                                .build();
                        priceComponents.add(comp);
                    }
                } else {
                    return CompletableFuture.completedFuture(null);
                }

            } else if (isReturnFlow) {
                if (isFitCashAmountPresent) {
                    PriceComponent comp = PriceComponent.builder()
                            .title("Refund Fitcash")
                            .value(OrderUtil.RUPEE_SYMBOL + fitCashAmount)
                            .isBold(true)
                            .build();
                    priceComponents.add(comp);
                }
                if (StringUtils.isNotBlank(netAmount)) {
                    PriceComponent comp = PriceComponent.builder()
                            .title("Refund Amount")
                            .value(OrderUtil.RUPEE_SYMBOL + netAmount)
                            .isBold(true)
                            .build();
                    priceComponents.add(comp);
                }
            }
            widget.setPriceDetails(priceComponents);
            // if (StringUtils.isNotBlank(paymentMode)) {
            // String finalAmount = StringUtils.isNotEmpty(totalFinalAmount) ?
            // totalFinalAmount :
            // priceBreakUpItemList.get(priceBreakUpItemList.size()-1).getValue();
            // widget.setBulletInfos(List.of(paymentMode + ": " + OrderUtil.RUPEE_SYMBOL +
            // finalAmount));
            // }
            widget.setCurrency(userContext.getUserProfile().getCity().getCountry().getCurrencyCode());
            if(inventoryUnit !=null && isConfirmedCultsportOrder(inventoryUnit)) {
                widget.setInvoiceAction(buildInvoiceDownloadAction(inventoryUnit.getInventory_unit_id()));
            }
            return CompletableFuture.completedFuture(widget);
        }
        return CompletableFuture.completedFuture(null);
    }

    public static boolean isConfirmedCultsportOrder(DetailedInventoryUnitItem inventoryUnitItem) {
        return !(new HashSet<>(List.of(CS_ORDER_CANCELED_STATE, CS_ORDER_PAYMENT_PENDING_STATE, CS_ORDER_PAYMENT_UNDER_REVIEW_STATE,CS_ORDER_PAYMENT_FAILED)).contains(inventoryUnitItem.getStatus().getState()));
    }

    private static Action buildInvoiceDownloadAction(String inventoryUnitId) {
        String url = "/v2/cultsport-order/invoice/invoiceUrl/" + inventoryUnitId;
        ActionMeta actionMeta =  new ActionMeta();
        actionMeta.setMethod("GET");
        actionMeta.setUrl(url);

        Action invoiceDownloadAction = Action.builder()
                .actionType(ActionType.REST_API)
                .url(url)
                .meta(actionMeta).build();
        return invoiceDownloadAction;
    }


    public static Boolean isCodPaymentMethod(String paymentMethod) {
        return "cod".equalsIgnoreCase(paymentMethod);
    }

    public static List<GearReason> getReplacementReasonsWithSubReasons(GearInventoryUnit inventoryUnit) {
        List<GearAvailableSize> availableSizes = new ArrayList<GearAvailableSize>();
        if (CollectionUtils.isNotEmpty(inventoryUnit.getVariant_availability())) {
            availableSizes.addAll(inventoryUnit.getVariant_availability().stream().map(variant -> {
                return GearAvailableSize.builder()
                        .title(variant.getSize())
                        .enabled(variant.getAvailability())
                        .build();
            }).collect(Collectors.toList()));
        }

        List<GearReason> reasons = inventoryUnit.getReplace_reasons();
        return reasons.stream().map(reason -> {
            if (reason.getCode() != null && reason.getCode().toLowerCase().contains("size")) {
                reason.setEnable_size_selector(true);
                reason.setSize_selector_collapsable(true);
                reason.setAvailableSizes(availableSizes);
                reason.setSizeGuide(GearSizeGuide.builder()
                        .illustration(inventoryUnit.getSize_chart_table())
                        .chart(inventoryUnit.getMeasurement_illustration())
                        .build());
            }
            return reason;
        }).collect(Collectors.toList());

    }

    public static CompletableFuture<BaseWidgetNonVM> getModifyAddressWidget(UserDeliveryAddress address,
            Boolean isReplaceOrReturnFlow, String title) {
        if (address == null) {
            return CompletableFuture.completedFuture(null);
        }
        CultsportModifyAddressWidget widget = new CultsportModifyAddressWidget();
        widget.setTitle(StringUtils.isNotBlank(title) ? title
                : (isReplaceOrReturnFlow ? "Pick Up Address" : "Delivery Address"));
        widget.setAddressHeader(
                address.getName() + (address.getPhoneNumber() != null ? " " + address.getPhoneNumber() : ""));
        String addressString = "";
        if (address.getGearDeliveryInfo() != null && address.getGearDeliveryInfo().getFloorNumber() != null && StringUtils.isNotBlank(address.getGearDeliveryInfo().getFloorNumber())) {
            addressString += "Floor No: " + address.getGearDeliveryInfo().getFloorNumber() + ", ";
        }
        addressString += address.getAddressLine1() + ", " + address.getAddressLine2();
        widget.setAddress(addressString);
        if (isReplaceOrReturnFlow) {
            widget.setActions(List.of(new Action(ActionType.MODIFY_GEAR_PICKUP_ADDRESS, "CHANGE PICKUP ADDRESS")));
        }
        widget.setAddressType(address.getAddressType());

        return CompletableFuture.completedFuture(widget);
    }

    public static Action getFitstoreHelpAction(String orderId, String inventoryUnitId) {
        return Action.builder()
                .actionType(ActionType.EXTERNAL_DEEP_LINK)
                .title("Need Help?")
                .url("mailto:" + FitstoreUtil.CUSTOMER_CARE_EMAIL + "?subject=[Gear] Need help with [Order " + orderId
                        + "]"
                        + (StringUtils.isNotBlank(inventoryUnitId) ? " [Inventory Unit " + inventoryUnitId + "]" : ""))
                .build();
    }

    public static Action getFeedbackPageAction(String feedbackId) {
        return Action.builder().actionType(ActionType.NAVIGATION).url("/feedback/" + feedbackId).build();
    }

    public static Action createAddToCartActionForPdp(CatalogueProductV2 product, UserContext userContext) {
        Action action;
        List<CatalogueVariantV2> orderableVariants = product.getVariants().stream()
                .filter(CatalogueVariantV2::isOrderable).toList();
        if (orderableVariants.size() <= 0) {
            action = Action.builder().title("OUT OF STOCK").disabled(true).build();
        } else {
            if (product.is_reward()) {
                Boolean isUserEligible = Integer.parseInt(product.getPrice()) > 0 && product.getAppliedOffers().stream()
                        .anyMatch(offer -> GearMicroappUtil.rewardOfferIdsSet.contains(offer.getOfferId()));
                log.info(String.format("Reward product info: id: %s, price: %s, isUserEligible: %s, appliedOffers: %s",
                        product.getId(), product.getPrice(), isUserEligible,
                        new Gson().toJson(product.getAppliedOffers())));
                action = !userContext.getSessionInfo().getIsUserLoggedIn()
                        ? CultsportUtil.getOpenLoginModalAction("LOGIN", true)
                        : Action.builder().actionType(ActionType.ADD_TO_CART)
                                .title(isUserEligible ? "GET FOR FREE" : "INELIGIBLE FOR REWARD").isPrimary(false)
                                .disabled(!isUserEligible).build();
            } else {
                CatalogueVariantV2 variant = product.getVariants().stream().collect(Collectors.toList()).size() == 1
                        ? orderableVariants.get(0)
                        : null;
                Object meta = variant != null ? GearMicroappUtil.getGearAddToCartActionMeta(product, variant, true)
                        : null;
                action = Action.builder().title("BUY NOW").disabled(false).actionType(ActionType.ADD_TO_CART).meta(meta)
                        .build();
            }
        }
        return action;
    }

    public static Action createAddToCartActionFromFitstorePLPProduct(FitstorePLPProduct plpProduct,
            UserContext userContext) {
        FitstorePLPProductAttributesResponse fitstorePLPProductAttributesResponse = plpProduct.getAttributes();
        Action action;
        List<FitstorePLPProductVariant> orderableVariants = fitstorePLPProductAttributesResponse.getVariants().stream()
                .filter(variant -> variant.getIsOrderable()).toList();
        if (orderableVariants.size() <= 0) {
            action = Action.builder().title("OUT OF STOCK").disabled(true).build();
        } else {

            FitstorePLPProductVariant variant = fitstorePLPProductAttributesResponse.getVariants().stream()
                    .collect(Collectors.toList()).size() == 1 ? orderableVariants.get(0) : null;
            Object meta = variant != null
                    ? GearMicroappUtil.getGearAddToCartActionMetaFromFitstorePLPProduct(plpProduct, variant, false)
                    : null;
            action = Action.builder().title("ADD TO CART").disabled(false).actionType(ActionType.ADD_TO_CART).meta(meta)
                    .build();
        }
        return action;
    }

    public static CultsportProductAddOnItem generateCultsportProductAddOnItemFromFitstorePLPProduct(
            UserContext userContext,
            FitstorePLPProduct product, ServiceInterfaces interfaces) {
        String imageUrl = CollectionUtils.isNotEmpty(product.getAttributes().getImageKey())
                ? product.getAttributes().getImageKey().get(0)
                : product.getAttributes().getImageUrl().get(0);

        BigDecimal mrp = product.getAttributes().getPrice().getMrp() > 0
                ? BigDecimal.valueOf(product.getAttributes().getPrice().getMrp()).setScale(0, RoundingMode.HALF_UP)
                : BigDecimal.valueOf(0.0);
        BigDecimal listingPrice = BigDecimal.valueOf(product.getAttributes().getPrice().getListingPrice()).setScale(0,
                RoundingMode.HALF_UP);
        int discount = mrp.intValue() > 0
                ? Math.round(((mrp.intValue() - listingPrice.intValue()) * 100)) / mrp.intValue()
                : 0;

        Action addToCartAction = FitstoreUtil.createAddToCartActionFromFitstorePLPProduct(product, userContext);

        List<ProductBrowseAnnotation> annotations = product.getAttributes().getAnnotations() != null
                && CollectionUtils.isNotEmpty(Arrays.asList(product.getAttributes().getAnnotations()))
                        ? Arrays.stream(product.getAttributes().getAnnotations().toArray()).map(annotation -> {
                            return ProductBrowseAnnotation.builder()
                                    .color("#736CF0")
                                    .borderColor("#E3E2FC")
                                    .backgroundColor("#E3E2FC")
                                    .text((String) annotation)
                                    .build();
                        }).collect(Collectors.toList())
                        : new ArrayList<>();

        return CultsportProductAddOnItem
                .builder()
                .brand(product.getAttributes().getBrand())
                .productName(product.getTitle())
                .price(PriceWithDiscount
                        .builder()
                        .mrp(mrp)
                        .listingPrice(listingPrice)
                        .primaryPrice(OrderUtil.RUPEE_SYMBOL + listingPrice)
                        .secondaryPrice(discount > 0 ? (OrderUtil.RUPEE_SYMBOL + mrp) : null)
                        .discountPercent(discount)
                        .currency("INR")
                        .build())
                .imageUrl(imageUrl)
                .imageUrls(product.getAttributes().getImageKey())
                .annotations(annotations)
                .action(product.getAction())
                .addToCartAction(addToCartAction)
                .build();
    }

    public static Action getCSOrderDetailsAction(String userId, boolean isAppWebview, String orderId, String inventoryUnitId, String title, String channel, String channelDetailsPageUrl) {
        
        if (channel != null && channel.equals("SHOPIFY") && StringUtils.isNotEmpty(channelDetailsPageUrl)) {
            ActionMeta meta = new ActionMeta();
            meta.setUrl("/user/getShopifyMultipassAction/" + userId);
            Map<String, Object> body = new HashMap<String, Object>() {{ put("shopifyRedirectUrl", channelDetailsPageUrl); }};
            body.put("isAppWebview", isAppWebview);
            meta.setBody(body);
            meta.setMethod("post");
            return Action
                    .builder()
                    .actionType(ActionType.REST_API)
                    .meta(meta)
                    .title(title)
                    .build();
        }
        return Action
                .builder()
                .actionType(ActionType.NAVIGATION)
                .url("/order/" + orderId + "/" + inventoryUnitId)
                .title(title)
                .build();
    }

    public static CultsportProductHightlightInfoItem getHighlightInfoItemFromInventoryUnitItem(
            InventoryUnitStatusInfo statusInfo) {
        if (statusInfo != null && StringUtils.isNotBlank(statusInfo.getState())) {
            String subTitle = statusInfo.getTitle();
            if (CS_ORDER_CANCELED_STATE.equalsIgnoreCase(statusInfo.getState())) {
                subTitle = "Order Cancelled";
            }
            String color = getStateColor(statusInfo.getState().toLowerCase());
            String imageUrl = getStateImage(statusInfo.getState().toLowerCase());
            String title = statusInfo.getEddInfo();
            return CultsportProductHightlightInfoItem
                    .builder()
                    .title(StringUtils.isNotBlank(title)? title: subTitle)
                    .subTitle(subTitle)
                    .titleColor(color)
                    .imageUrl(imageUrl)
                    .build();
        }
        return null;
    }

    public static CultsportProductHightlightInfoItem getReturnExchangeInfoFromInventoryUnitItem(
            InventoryUnitStatusInfo statusInfo) {
        if (statusInfo != null && statusInfo.getReturnExchangeInfo() != null) {
            String returnExchangeInfo = statusInfo.getReturnExchangeInfo();
            if(StringUtils.isNotBlank(returnExchangeInfo)) {
                return CultsportProductHightlightInfoItem
                        .builder()
                        .title(returnExchangeInfo)
                        .titleColor("#00BA78")
                        .build();
            }
        }
        return null;
    }

    public static CultsportProductHightlightInfoItem getRefundCollectionInfoFromInventoryUnit(
            List<RefundDetails> refund_details) {
        String refundLink = refund_details.isEmpty() ? null : refund_details.get(0).getRefund_link();

        if(CollectionUtils.isEmpty(refund_details) || refundLink == null){
            return null;
        }

        RefundDetails refundDetails = refund_details.get(0);
        String title = null;
        String imageUrl = null;
        String titleColor = null;
        String backgroundColor = null;
        String actionTitle = null;

        switch (refundDetails.getStatus()) {
            case "pending" -> {
                title = "Collect your refund!";
                imageUrl = "image/cultsport/my_order/cash.svg";
                titleColor = "#FFFFFF";
                backgroundColor = "#FF3278";
                actionTitle = "Collect";
            }
            case "failure" -> {
                title = "Refund Failed!";
                imageUrl = "image/cultsport/order_status/failed_delivered.png";
                titleColor = "#1A1A1E";
                backgroundColor = "#FFFFFF";
                actionTitle = "View refund details";
            }
            case "success" -> {
                title = "Refund Collected  ";
                imageUrl = "image/cultsport/my_order/green_white_tick.svg";
                titleColor = "#1A1A1E";
                backgroundColor = "#FFFFFF";
                actionTitle = "View refund details";
            }
            default -> {
            }
        }
         
         if(title == null) {
             return null;
         }

        return CultsportProductHightlightInfoItem
                .builder()
                .imageUrl(imageUrl)
                .title(title)
                .titleColor(titleColor)
                .backgroundColor(backgroundColor)
                .action(new Action(refundLink, actionTitle, ActionType.EXTERNAL_DEEP_LINK))
                .build();
    }

    public static String getStateColor(String state) {
        String color = FitstoreUtil.CS_STATUS_NEUTRAL_COLOR;
        switch (state) {
            case "backordered":
            case "onhand":
            case "on_hand":
            case "packed":
            case "shipped":
            case "out_for_delivery":
            case "out_for_pickup":
            case "delivered":
            case "ready_to_ship": {
                return FitstoreUtil.CS_STATUS_POSITIVE_COLOR;
            }
            case "failed_delivery":
            case "failed_pickup":
            case "pickup_canceled":
            case "act_as_delivered":
            case "qc_rejected":
            case "canceled":
            case "rto_initiated": {
                return FitstoreUtil.CS_STATUS_NEGATIVE_COLOR;
            }
            case "return_initiated":
            case "exchange_initiated":
            case "qc_approved":
            case "received":
            case "picked_up":
            case "exchange_picked_up":
            case "return_approved": {
                return FitstoreUtil.CS_STATUS_NEUTRAL_COLOR;
            }
            // Payment states (Delayed payments)
            case "payment_pending":
            case "payment_under_review":
            case "payment_failed":
                return FitstoreUtil.CS_STATUS_NEGATIVE_COLOR;
            default: {
                return color;
            }
        }
    }

    public static String getStateImage(String state) {
        switch (state) {
            case "backordered":
            case "onhand":
            case "on_hand":
            case "packed":
            case "ready_to_ship": {
                return "image/cultsport/order_status/packed.png";
            }
            case "shipped":
            case "out_for_delivery":
            case "out_for_pickup": {
                return "image/cultsport/order_status/shipped.png";
            }
            case "delivered": {
                return "image/cultsport/order_status/completed.png";
            }
            case "return_initiated":
            case "exchange_initiated": {
                return "image/cultsport/order_status/return_initiated.png";
            }
            case "received":
            case "qc_approved":
            case "return_approved": {
                return "image/cultsport/order_status/refunded.png";
            }
            case "picked_up":
            case "exchange_picked_up": {
                return "image/cultsport/order_status/pickup.png";
            }
            case "failed_pickup": {
                return "image/cultsport/order_status/pick_failed.png";
            }
            case "failed_delivery":
            case "rto_initiated": {
                return "image/cultsport/order_status/failed_delivered.png";
            }
            case "pickup_canceled":
            case "qc_rejected":
            case "canceled":
            case "act_as_delivered": {
                return "image/cultsport/order_status/failed.png";
            }
            // Payment states (Delayed payments)
            case "payment_pending":
            case "payment_under_review":
                return "image/cultsport/order_status/order_pending.png";
            case "payment_failed":
                return "image/cultsport/order_status/failed_delivered.png";
            default: {
                return "image/cultsport/order_status/completed.png";
            }
        }
    }

    public static List<DiscountBreakUpInfo> buildOfferDiscountBreakup(Order order) {
        List<DiscountBreakUpInfo> items = new ArrayList<>();
        Map<String, Double> offerIdToDiscountMap = new HashMap<>();
        Map<String, OfferInfo> offerMap = new HashMap<>();
        if (order.getOffersInfo() != null) {
            order.getOffersInfo().forEach(offer -> offerMap.put(offer.getOfferId(), offer));
            for (ProductSnapshot productSnapshot : order.getProductSnapshots()) {
                if (productSnapshot != null && productSnapshot.getOption() != null) {
                    OrderProductOptions option = productSnapshot.getOption();
                    if (option != null) {
                        List<ProductOffersInfo> offers = option.getOffersInfo();
                        if (offers != null && offers.size() > 0) {
                            for (ProductOffersInfo offer : offers) {
                                if (offer != null && offer.getOfferId() != null) {
                                    String offerId = offer.getOfferId();
                                    Double discount = Double.parseDouble(offer.getDiscount().toString());
                                    if (offerIdToDiscountMap.containsKey(offerId)) {
                                        offerIdToDiscountMap.put(offerId,
                                                (offerIdToDiscountMap.get(offerId) > 0
                                                        ? offerIdToDiscountMap.get(offerId)
                                                        : 0) + discount * productSnapshot.getQuantity());
                                    } else {
                                        offerIdToDiscountMap.put(offerId, discount * productSnapshot.getQuantity());
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (!offerIdToDiscountMap.isEmpty()) {
                for (String offerId : offerIdToDiscountMap.keySet()) {
                    if (offerIdToDiscountMap.get(offerId) > 0) {
                        DiscountBreakUpInfo item = new DiscountBreakUpInfo();
                        item.setOfferId(offerId);
                        item.setDiscount(offerIdToDiscountMap.get(offerId));
                        items.add(item);
                    }
                }
            }

            if (items.size() > 0) {
                items.sort((offer1, offer2) -> (int) Math.round(offer2.getDiscount())
                        - (int) Math.round(offer1.getDiscount()));
                items = items.stream().map(item -> {
                    if (offerMap.get(item.getOfferId()) != null) {
                        item.setTitle(offerMap.get(item.getOfferId()).getOfferTitle());
                        item.setValue("-" + OrderUtil.getCurrencySymbol("INR")
                                + OrderUtil.toFixedValue(item.getDiscount(), 2));
                        return item;
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.toList());
            }

            return items;
        }

        return null;
    }

    public static List<TextItemParent> getBestPriceInfo(Double bestPrice, Boolean isBoldText) {
        return List.of(TextItemParent.builder().items(Arrays.asList(
                TextItem.builder().textStyle(isBoldText ? TextStyle.BOLD : TextStyle.REGULAR)
                        .text("Get for " + OrderUtil.RUPEE_SYMBOL + bestPrice.intValue())
                        .textColor("#00BA78")
                        .build(),
                TextItem.builder().textStyle(TextStyle.REGULAR)
                        .text(" with coupon")
                        .build()))
                .build());
    }

    public static boolean isProductInWishlist(CultsportUserClient cultsportUserClient, String userId, Integer productId)
            throws BaseException {
        return cultsportUserClient.isInWishlist(userId, productId).isInWishlist();
    }

}