package com.curefit.cfapi.util;

import com.curefit.cfapi.widgets.dailystreak.DSHomePageWidgetConfig;
import com.curefit.uas.responses.StreakResponse;

/**
 * Service class for configuring home page widgets.
 * Creates and configures DSHomePageWidgetConfig objects based on user streak data.
 */
public final class HomePageWidgetConfigurator {
    
    private HomePageWidgetConfigurator() {
        // Prevent instantiation
    }
    
    /**
     * Creates a DSHomePageWidgetConfig based on the user's streak data.
     */
    public static DSHomePageWidgetConfig createHomePageWidgetConfig(StreakResponse userStreakData, Long streakRepairExpiryEpoch, Long streakRepairConsumedEpoch) {
        if (userStreakData == null) {
            return createDefaultHomePageWidgetConfig();
        }

        DSHomePageWidgetConfig config = new DSHomePageWidgetConfig();
        StreakStateType stateType = StreakStateDeterminer.determineStreakStateType(userStreakData, streakRepairExpiryEpoch, streakRepairConsumedEpoch);
        CuroState curoState = CuroStateFactory.getCuroStateForType(stateType);

        setDefaultHomePageConfig(config);
        configureHomePageByStateType(config, stateType);
        setHomePageCuroStateData(config, curoState);

        return config;
    }
    
    private static void setDefaultHomePageConfig(DSHomePageWidgetConfig config) {
        config.setBoxShadowColor(StreakGradients.HOME_PAGE_NEUTRAL_BOX_SHADOW);
        config.setBorderGradient(StreakGradients.NEUTRAL_BORDER);
    }
    
    private static void configureHomePageByStateType(DSHomePageWidgetConfig config, StreakStateType stateType) {
        switch (stateType) {
            case PAUSED, STREAK_LOST -> configureInactiveHomePageState(config, stateType);
            case ACTIVE_ONBOARDING, ACTIVE, REPAIR_CONSUMED -> config.setGradientColors(StreakGradients.HOME_PAGE_SUCCESS);
            case INACTIVE_ZERO_REST_DAYS, INACTIVE_ONE_REST_DAY -> configureWarningHomePageState(config);
            case GOAL_REACHED, INACTIVE, REPAIR_AWARDED -> config.setGradientColors(StreakGradients.HOME_PAGE_NEUTRAL);
        }
    }

    private static void configureInactiveHomePageState(DSHomePageWidgetConfig config, StreakStateType stateType) {
        config.setGradientColors(StreakGradients.HOME_PAGE_INACTIVE);
        if (stateType == StreakStateType.STREAK_LOST) {
            config.setBoxShadowColor(StreakGradients.HOME_PAGE_WARNING_BOX_SHADOW);
            config.setBorderGradient(StreakGradients.WARNING_BORDER);
        }
    }
    
    private static void configureWarningHomePageState(DSHomePageWidgetConfig config) {
        config.setGradientColors(StreakGradients.HOME_PAGE_NO_REST);
        config.setBoxShadowColor(StreakGradients.HOME_PAGE_WARNING_BOX_SHADOW);
        config.setBorderGradient(StreakGradients.WARNING_BORDER);
    }
    
    private static void setHomePageCuroStateData(DSHomePageWidgetConfig config, CuroState curoState) {
        if (curoState != null) {
            config.setImageUrl(curoState.getHomeImageUrl());
        }
    }
    
    private static DSHomePageWidgetConfig createDefaultHomePageWidgetConfig() {
        DSHomePageWidgetConfig config = new DSHomePageWidgetConfig();
        config.setImageUrl("image/daily-streak/ds_home_page_mock.png");
        config.setGradientColors(StreakGradients.HOME_PAGE_SUCCESS);
        return config;
    }
} 