package com.curefit.cfapi.pagehook;


import com.curefit.base.enums.AppTenant;
import com.curefit.base.service.EnvironmentService;
import com.curefit.center.client.CenterService;
import com.curefit.center.dtos.CenterEntry;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.FeatureStateCache;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.Analytics.ContinuePurchaseOptions;
import com.curefit.cfapi.model.internal.LevelStatusEnum;
import com.curefit.cfapi.model.internal.cult.HomeFloatingAction;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.PageContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.model.mongo.Page;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.vm.items.UpcomingItem;
import com.curefit.cfapi.pojo.vm.items.UpcomingItemMetaInfo;
import com.curefit.cfapi.pojo.vm.kickStarterContent.WorkoutCategoryPersonalizedContent;
import com.curefit.cfapi.pojo.vm.page.PageView;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.repository.UserKickstartPlanPersonalizedData;
import com.curefit.cfapi.service.SegmentEvaluatorService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.fitness.BestPackService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewmodels.fitness.AutomatedBookingInfo;
import com.curefit.cfapi.view.viewmodels.fitness.RenewMembershipInfo;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.CardListWidgetView;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.common.banner.Header;
import com.curefit.cfapi.widgets.common.banner.MerchantryWidget;
import com.curefit.cfapi.widgets.digital.LevelSectionWidget;
import com.curefit.cfapi.widgets.fitness.BookingCardWidget;
import com.curefit.cfapi.widgets.fitness.CultClassUserAttributes;
import com.curefit.cfapi.widgets.fitness.FitnessPackBrowseWidget;
import com.curefit.cfapi.widgets.hometab.TrayWidget;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.cult.models.CultDocument;
import com.curefit.cult.models.KickstarterPlanDetailsResponse;
import com.curefit.cult.models.Workout;
import com.curefit.gymfit.models.Pitch;
import com.curefit.gymfit.models.PitchStatus;
import com.curefit.logging.models.ActivityTypeDS;
import com.curefit.math.enums.DataType;
import com.curefit.membership.client.MembershipFilter;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.types.AttributeKeyType;
import com.curefit.membership.types.Status;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.product.enums.ProductType;
import com.curefit.rashi.pojo.UserAttributeEntry;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.ufs.pojo.UserWodEntry;
import com.curefit.ufs.pojo.enums.UserWodStartType;
import com.curefit.ufs.pojo.enums.UserWodStatus;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.AccessLevel;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.json.JSONObject;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Clock;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.curefit.cfapi.banner.SmartWorkoutPlanResumeWod.CUTOFF_TIME_RESUME_STORY;
import static com.curefit.cfapi.service.fitness.PastActivityService.getCenterTourChapter;
import static com.curefit.cfapi.util.CultUtil.MILLIS_TO_DAYS_DENOMINATOR;
import static com.curefit.cfapi.util.FitnessCenterUtil.CULT_CENTER_ID_KEY;
import static com.curefit.cfapi.util.FitnessCenterUtil.getUserMembershipType;


@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class HomePageHook implements PageHook {

    FeatureStateCache featureStateCache;
    private UserKickstartPlanPersonalizedData pageConfigRepository;
    private SegmentEvaluatorService segmentEvaluatorService;
    private EnvironmentService environmentService;
    private BestPackService bestPackService;
    private static final String PERSONALISED_CONTENT_PAGE_ID = "UserKickstarterPersonalizedContentV2";
    private static final String SEGEMNT_ID_FOR_AUTOMATED_BOOKING = "07336338-30b4-4bb3-a712-fece33510f2";
    public static final String CULT_WHATSAPP_NO = "918792514523";
    public static final List<Integer> WEEKENDS_AT_CULT_FORMAT = Arrays.asList(368, 183, 364, 398);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public HomePageHook(
            FeatureStateCache featureStateCache,
            UserKickstartPlanPersonalizedData pageConfigRepository,
            SegmentEvaluatorService segmentEvaluatorService,
            EnvironmentService environmentService,
            BestPackService bestPackService
    ) {
        this.featureStateCache = featureStateCache;
        this.pageConfigRepository = pageConfigRepository;
        this.segmentEvaluatorService = segmentEvaluatorService;
        this.environmentService = environmentService;
        this.bestPackService = bestPackService;
    }

    private static String getImageUrlForVerticals(UpcomingItemMetaInfo upcomingItemMetaInfo) {
        if(upcomingItemMetaInfo != null && upcomingItemMetaInfo.getTenant() != null) {
            if (upcomingItemMetaInfo.getTenant().equals("fitso")) {
                return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/play2.png";
            } else if (upcomingItemMetaInfo.getTenant().equals("gymfit")) {
                return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/gym.png";
            } else if (upcomingItemMetaInfo.getTenant().equals("live")) {
                return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/live.png";
            } else if (upcomingItemMetaInfo.getTenant().equals("transform")) {
                return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/transform.png";
            } else if (upcomingItemMetaInfo.getTenant().equals("gymPt")) {
                return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/gymPt.png";
            }
        }
        return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/generic.png";
    }

    public static String getImageUrlForPastActivityVerticals(ActivityTypeDS vertical) {
        if(vertical != null) {
            switch (vertical) {
                case CULT_CLASS : return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/generic.png";
                case GYMFIT_CHECKIN: return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/gym.png";
                case LIVE_SESSION: return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/live.png";
                case PLAY_CLASS: return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/play2.png";
            }
        }
        return "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/generic.png";
    }

    public static LevelSectionWidget.ChapterItem getOnboardingClassInfo(UpcomingItem classInfo , UserContext userContext) {
        LevelSectionWidget.ChapterItem chapter = new LevelSectionWidget.ChapterItem();

        String superScript = "";

        String tz = userContext.getUserProfile().getTimezone();

        Instant instant = Instant.ofEpochMilli(classInfo.getTimestamp());
        Instant newInstant = instant.minus(30, ChronoUnit.MINUTES);
        Long newTimestamp = newInstant.toEpochMilli();

        Integer diffIndays = TimeUtil.getDaysInBetween(LocalDate.now(ZoneId.of(tz)), Instant.ofEpochMilli(newTimestamp).atZone(ZoneId.of(tz)).toLocalDate());

        if (classInfo.getHeader() != null && Objects.equals(classInfo.getHeader().getTitle(), "Today")) {
            superScript = classInfo.getHeader().getTitle().toUpperCase() + TimeUtil.getTimeInFormatFromMillis(newTimestamp, " • hh:mm aa", tz);
        } else if (diffIndays == 1) {
            superScript = "TOMORROW" + TimeUtil.getTimeInFormatFromMillis(newTimestamp, " • hh:mm aa", tz);
        } else {
            superScript = TimeUtil.getTimeInFormatFromMillis(newTimestamp, "d MMM, EEE • hh:mm aa", tz);
        }

        chapter.setSuperScript(superScript);
        chapter.setStatus(LevelStatusEnum.ACTIVE);
        BookingCardWidget booking = new BookingCardWidget();
        booking.setTitle("Onboarding session with fitness consultant");
        String subTitle = classInfo.getUpcomingItemMetaInfo() != null && classInfo.getUpcomingItemMetaInfo().getCenterName() != null ?
                classInfo.getUpcomingItemMetaInfo().getCenterName() : "";
        booking.setSubTitle(subTitle);
        booking.setSecondaryText("30 mins");
        booking.setImageUrl(getImageUrlForVerticals(classInfo.getUpcomingItemMetaInfo()));
        booking.setClassTime(classInfo.getDate());
        chapter.setWidgets(List.of(booking));
        log.debug("AFM Onboarding flow {}",chapter);
        return  chapter;
    }

    public static boolean ifIsClassBetweenTimings(UpcomingItem classInfo, String tz) {
        LocalDateTime classDate = Instant.ofEpochMilli(classInfo.getTimestamp()).atZone(ZoneId.of(tz)).toLocalDateTime();
        LocalDateTime startTime = classDate.withHour(7).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endTime = classDate.withHour(20).withMinute(31).withSecond(0).withNano(0);
        log.debug("afm slot booking timings {} {} {}",classDate,startTime,endTime);
        DayOfWeek activityWeek = classDate.getDayOfWeek();
        boolean inBetweenTimings = classDate.isAfter(startTime) && classDate.isBefore(endTime);
        boolean isSunday = activityWeek != DayOfWeek.SUNDAY;

        LocalDateTime morningTimingsStart = classDate.withHour(6).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime morningTimingsEnd = classDate.withHour(11).withMinute(0).withSecond(0).withNano(0);

        boolean isFridayMorning = activityWeek == DayOfWeek.FRIDAY && classDate.isAfter(morningTimingsStart) && classDate.isBefore(morningTimingsEnd);
        boolean isSaturdayMorning = activityWeek == DayOfWeek.SATURDAY && classDate.isAfter(morningTimingsStart) && classDate.isBefore(morningTimingsEnd);

        log.debug("afm slot booking timings other checks {} {} {} {} {} {} ",inBetweenTimings,isSunday,morningTimingsStart, morningTimingsEnd, isFridayMorning, isSaturdayMorning);

        return inBetweenTimings && isSunday && !isSaturdayMorning && !isFridayMorning;
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    public static LevelSectionWidget setLevelSectionWidget(ServiceInterfaces interfaces, UserContext userContext, List<UpcomingItem> finalUpcomingClasses, boolean fromFlutterBottomSheet, boolean isNonMember) throws ParseException, BaseException, IOException {
        LevelSectionWidget levelSectionWidget = new LevelSectionWidget();
        levelSectionWidget.setHasDivideBelow(false);
        List<LevelSectionWidget.ChapterItem> chapterItemList = new ArrayList<>();

        List<Workout> workoutList = new ArrayList<>();
        if(fromFlutterBottomSheet || AppUtil.doesUserBelongToGymPTSegment(interfaces, userContext)) {
            workoutList = interfaces.cultService.browseFitnessWorkout(true, "ADULT", Long.valueOf(userContext.getUserProfile().getCity().getCultCityId())).get();
        }
        List<String> cultUnboundWorkoutIds = interfaces.cultService.getCultUnboundWorkoutIds(false);
        Map<String, Workout> workoutMap = new HashMap<>();
        workoutList.forEach(cultWorkout -> {
            workoutMap.put(cultWorkout.getId().toString(), cultWorkout);
        });

        boolean onboardingClassShown = AppUtil.isUserPartOfAFMSlotBookingFeature(userContext,interfaces);
        for (int i = 0; i < finalUpcomingClasses.size(); i++) {
            UpcomingItem classInfo = finalUpcomingClasses.get(i);

            String tz = userContext.getUserProfile().getTimezone();

            long currentEpoch = System.currentTimeMillis();
            long diffInMins = (classInfo.getTimestamp() - currentEpoch) / (1000*60);
            log.debug("afm slot booking 0 {}", classInfo);
            // log.debug("afm slot booking {} {} {} {} {}",diffInMins,classInfo.getTimestamp(),currentEpoch,onboardingClassShown,classInfo.getUpcomingItemMetaInfo().getTenant().equals("cult"));
            if(onboardingClassShown && classInfo.getUpcomingItemMetaInfo() != null && classInfo.getUpcomingItemMetaInfo().getTenant() != null
                    && classInfo.getUpcomingItemMetaInfo().getTenant().equals("cult") && diffInMins > 30
                    && ifIsClassBetweenTimings(classInfo,tz)) {
                LevelSectionWidget.ChapterItem chapter = getOnboardingClassInfo(classInfo,userContext);
                log.debug("afm slot booking chapter {}",chapter);
                onboardingClassShown = false;
                chapterItemList.add(chapter);
            } else if(onboardingClassShown && classInfo.getUpcomingItemMetaInfo() != null && classInfo.getUpcomingItemMetaInfo().getTenant() != null
                    && classInfo.getUpcomingItemMetaInfo().getTenant().equals("cult")) {
                onboardingClassShown = false;
            }

            LevelSectionWidget.ChapterItem chapter = new LevelSectionWidget.ChapterItem();
            chapter.setIconSize(18.0);
            chapter.setSuperScript(classInfo.getHeader() != null ? classInfo.getHeader().getTitle().toUpperCase() : "");
            String superScript = "";
            Integer diffIndays = TimeUtil.getDaysInBetween(LocalDate.now(ZoneId.of(tz)), Instant.ofEpochMilli(classInfo.getTimestamp()).atZone(ZoneId.of(tz)).toLocalDate());
            if (classInfo.getHeader() != null && classInfo.getHeader().getTitle() == "Today") {
                superScript = classInfo.getHeader().getTitle().toUpperCase() + TimeUtil.getTimeInFormatFromMillis(classInfo.getTimestamp(), " • hh:mm aa", tz);
            } else if (diffIndays == 1) {
                superScript = "TOMORROW" + TimeUtil.getTimeInFormatFromMillis(classInfo.getTimestamp(), " • hh:mm aa", tz);
            } else {
                superScript = TimeUtil.getTimeInFormatFromMillis(classInfo.getTimestamp(), "d MMM, EEE • hh:mm aa", tz);
            }

            log.debug("automated booking get super script {}", superScript);

            chapter.setSuperScript(superScript);
            chapter.setIsLocked(true);

            if (i == 0) {
                chapter.setStatus(LevelStatusEnum.ACTIVE);
            } else {
                chapter.setStatus(LevelStatusEnum.LOCKED);
            }
            BookingCardWidget booking = new BookingCardWidget();
            String title = (classInfo.getUpcomingItemMetaInfo() != null && classInfo.getUpcomingItemMetaInfo().getTitle() != null) ?
                    classInfo.getUpcomingItemMetaInfo().getTitle() : "";
            booking.setTitle(title);
            String subTitle = classInfo.getUpcomingItemMetaInfo() != null && classInfo.getUpcomingItemMetaInfo().getCenterName() != null ?
                    classInfo.getUpcomingItemMetaInfo().getCenterName() : "";
            booking.setSubTitle(subTitle);
            if (classInfo.getUpcomingItemMetaInfo() != null && (classInfo.getUpcomingItemMetaInfo().getTenant().equals("cult")
                    || classInfo.getUpcomingItemMetaInfo().getTenant().equals("fitso"))) {

                SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
                Date date1 = format.parse(classInfo.getUpcomingItemMetaInfo().getStartTime());
                Date date2 = format. parse(classInfo.getUpcomingItemMetaInfo().getEndTime());
                long difference = date2.getTime() - date1.getTime();
                String minutes = TimeUtil.millisToMins(difference);
                booking.setSecondaryText(minutes);
            } else if (classInfo.getUpcomingItemMetaInfo() != null && classInfo.getUpcomingItemMetaInfo().getTenant().equals("gymPt") && classInfo.getUpcomingItemMetaInfo().getTrainerName() != null) {
                booking.setSecondaryText(classInfo.getUpcomingItemMetaInfo().getTrainerName());
            }

            if (classInfo.getAdditionalTitle() != null) {
                booking.setAdditionalInfo((AppUtil.isBookingV2Supported(userContext) ? "  •  " : "") + classInfo.getAdditionalTitle());
                if (classInfo.getAdditionalTitleColor() != null) {
                    booking.setAdditionalInfoColor(classInfo.getAdditionalTitleColor());
                }
            }

            booking.setAction(classInfo.getAction());
            booking.setClassTime(classInfo.getDate());
            booking.setLeftAction(classInfo.getPrimaryAction());


            /*Action rightAction = classInfo.getSecondaryAction();
            // if (rightAction.getTitle().toUpperCase().contains("WAITLIST")) {

            if (rightAction != null) {
                try {
                    CultManageOptionsMeta rightActionMeta = (CultManageOptionsMeta) rightAction.getMeta();
                    rightActionMeta.setFromFlutterBottomSheet(fromFlutterBottomSheet);
                    rightAction.setMeta(rightActionMeta);
                } catch (Exception e) {
                    RescheduleClassModalV2 rightActionMeta = (RescheduleClassModalV2) rightAction.getMeta();
                    rightActionMeta.setFromFlutterBottomSheet(fromFlutterBottomSheet);
                    CultManageOptionsMeta cultManageOptionsMeta = rightActionMeta.getCancelActionMeta();
                    cultManageOptionsMeta.setFromFlutterBottomSheet(fromFlutterBottomSheet);
                    rightActionMeta.setCancelActionMeta(cultManageOptionsMeta);
                    rightAction.setMeta(rightActionMeta);
                }
            }*/
            if(classInfo.getWorkoutID() == null || !cultUnboundWorkoutIds.contains(classInfo.getWorkoutID().toString())) {
                booking.setRightAction(classInfo.getSecondaryAction());
            }

            booking.setMoreActions(classInfo.getMoreActions());

            if (classInfo.getWorkoutID() != null && !workoutMap.isEmpty() && workoutMap.get(classInfo.getWorkoutID().toString()) != null) {
                List<CultDocument> cultDocuments = workoutMap.get(classInfo.getWorkoutID().toString())
                        .getDocuments().stream().filter(cultDocument -> cultDocument.getTagID() == 11).collect(Collectors.toList());

                if (cultDocuments != null && !cultDocuments.isEmpty()) {
                    booking.setImageUrl(UrlPathBuilder.prefixSlash(cultDocuments.get(0).getUrl() != null ? cultDocuments.get(0).getUrl() : ""));
                }
            }

            if (classInfo.getUpcomingItemMetaInfo() != null && classInfo.getUpcomingItemMetaInfo().getTenant() == "fitso") {
                booking.setImageUrl(UrlPathBuilder.prefixSlash(classInfo.getUpcomingItemMetaInfo().getImage()));
            }

            if (classInfo.getUpcomingItemMetaInfo() != null && booking.getImageUrl() == null) {
                booking.setImageUrl(getImageUrlForVerticals(classInfo.getUpcomingItemMetaInfo()));
            }

            if (classInfo.getUpcomingItemMetaInfo() != null) {
                chapter.setWidgets(List.of(booking));
                chapterItemList.add(chapter);
            } else {
                log.error("Kickstarter plan classes ignored userID : {} classInfo : {}", userContext.getUserProfile().getUserId(), classInfo);
            }
        }

        if (chapterItemList.isEmpty() || isNonMember) {
            LevelSectionWidget.ChapterItem pastActivityChapter = getCenterTourChapter(userContext, interfaces, List.of(PitchStatus.SCHEDULED));
            if (pastActivityChapter != null)
                chapterItemList.add(pastActivityChapter);
        }

        if (AppUtil.isPastActivitySegment(userContext, interfaces)) {
            LevelSectionWidget.ChapterItem pastActivityChapter = getLatestPastActivity(userContext, interfaces);
            if (pastActivityChapter != null)
                chapterItemList.add(pastActivityChapter);
        }

        levelSectionWidget.setChapters(chapterItemList);
        levelSectionWidget.setWidgetType(WidgetType.LEVEL_SECTION_WIDGET_V2);
        return levelSectionWidget;
    }

    private static Map<String, Object> getUserAttributesObj(UserContext userContext, ServiceInterfaces interfaces) throws BaseException {
        List<String> attributes = new ArrayList<>();
        attributes.add("last_cult_class_details");
        attributes.add("last_gym_session_details");
        attributes.add("last_live_class_details");
        attributes.add("last_play_class_details_clone");
        String userId = userContext.getUserProfile().getUserId();
        UserAttributesResponse response = interfaces.userAttributesCacheClient.getAttributes(Long.valueOf(userId),
                attributes,
                AppUtil.getAppTenantFromUserContext(userContext));
        if (response == null || response.getAttributes() == null) return null;
        log.debug("user attributes cult : {}", response.getAttributes().toString());
        return response.getAttributes();
    }

    public static CultClassUserAttributes parseString(String input) {

        input = input.substring(1, input.length() - 1);
        String[] pairs = input.split(", ");
        Map<String, String> map = new HashMap<>();

        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            map.put(keyValue[0].trim(), keyValue[1].trim());
        }

        CultClassUserAttributes attributes = new CultClassUserAttributes();

        // Assign values from the map to the object fields
        attributes.setBookingtype(map.get("bookingtype"));
        attributes.setCenterid(Long.parseLong(map.get("centerid")));
        attributes.setClassid(Long.parseLong(map.get("classid")));
        attributes.setCaloriesburned(Long.parseLong(map.get("caloriesburned")));
        attributes.setOccured_at(Long.parseLong(map.get("occured_at")));
        attributes.setWorkoutname(map.get("workoutname"));
        attributes.setWorkoutid(Long.parseLong(map.get("workoutid")));
        attributes.setDurationminutes(Long.parseLong(map.get("durationminutes")));
        attributes.setBookingid(Long.parseLong(map.get("bookingid")));
        attributes.setCentername(map.get("centername"));

        return attributes;
    }

    private static LevelSectionWidget.ChapterItem getLatestPastActivity(UserContext userContext, ServiceInterfaces interfaces) throws BaseException {
        Gson gson = new Gson();
        LevelSectionWidget.ChapterItem pastActivityChapter = new LevelSectionWidget.ChapterItem();
        BookingCardWidget pastBooking = new BookingCardWidget();
        pastBooking.setTitle("Past activities");
        pastBooking.setImageUrl("https://cdn-images.cure.fit/www-curefit-com/image/upload/image/pastSession.png");
        pastBooking.setSubTitle("VIEW ALL");
        Action pastActivityNavAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .url("curefit://past_activities")
                .build();
        pastBooking.setAction(pastActivityNavAction);
        pastBooking.setShowCardShadow(true);
        pastBooking.setImageSize(60);
        pastActivityChapter.setWidgets(List.of(pastBooking));
        pastActivityChapter.setStatus(LevelStatusEnum.ACTIVE);
        return pastActivityChapter;
    }



    private MerchantryWidget setPersonalizedDataContent(ServiceInterfaces interfaces, UserContext userContext, List<WorkoutCategoryPersonalizedContent> personalizedContent) {

        List<BannerItem> banners = new ArrayList<>();
        MerchantryWidget merchantryWidget = new MerchantryWidget();
        merchantryWidget.setWidgetType(WidgetType.MERCHANTRY_WIDGET);
        merchantryWidget.setHasDivideBelow(false);
        Object layout = null;
        merchantryWidget.setLayoutProps(this.getLayoutData());
        merchantryWidget.setTemplateId("2 GRID (162:215)");
        merchantryWidget.setMaxNumBanners(4);
        merchantryWidget.setMinNumBanners(0);
        Header header = new Header();
        header.setTitle("Stay Right");
        header.setShowChevron(false);
        header.setShowReverse(false);
        merchantryWidget.setHeader(header);
        for (int i = 0; i < personalizedContent.size(); i++) {
            WorkoutCategoryPersonalizedContent bannerContent = personalizedContent.get(i);
            BannerItem banner = new BannerItem();
            banner.setImage(bannerContent.getImageUrl());
            Action action = new Action();
            action.setActionType(ActionType.NAVIGATION);
            action.setUrl(bannerContent.getContentUrl());
            banner.setAction(action);
            banners.add(banner);
        }
        merchantryWidget.setData(banners);
        return merchantryWidget;
    }

    @Override
    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    public HashMap<String, Object> getPageData(ServiceInterfaces interfaces, UserContext userContext, PageContext pageContext) throws ExecutionException, InterruptedException {
        HashMap<String, Object> pageData = new HashMap<>();
        if (AppUtil.isCultIconCoachmarkSupported(userContext)) {
            HashMap<String, Object> cultIconData = new HashMap<>();
            cultIconData.put("cultIcon", true);
            if (!this.featureStateCache.match(userContext.getUserProfile().getUserId(), "cult-icon-coachmark", "sent").get()) {
                cultIconData.put("coachmark", new HashMap<>() {
                    {
                        put("title", "You did it!\n");
                        put("subtitle", "You're officially are a cult icon! You made it to the top and there's no stopping you.\n" +
                                "Please reach out to the center manager to collect your goodie bag.\n" +
                                "Team cult.fit");
                    }
                });
                this.featureStateCache.set(userContext.getUserProfile().getUserId(), "cult-icon-coachmark", "sent");
            }
            pageData.put("cultIconData", cultIconData);
        }

        try{
            if(AppUtil.isChampionUser(userContext)){
                pageData.put("coverLottieUrl", "/image/community/highlights/champion_lottie.json");
            }
        }catch (Exception e){
            log.error("Error in getting profile ring for userId: {}, error msg: {}", userContext.getUserProfile().getUserId(), e.getMessage());
            interfaces.exceptionReportingService.reportException(e);
        }


        try {
            //interfaces.cultService

//            boolean doesUserBelongToGoalSetting = AppUtil.doesUserBelongToGoalSetting(userContext,interfaces);
            boolean isUserPartOfExperiment = AppUtil.isUserEligibleForAutomatedBooking(userContext, interfaces);
            boolean isPlayVisibilityExperiment = PlayUtil.isPlayHomePageExperimentSupported(userContext, environmentService, segmentEvaluatorService);
            boolean isUserWithZeroBooking = PlayUtil.isUserNonMemberWithZeroBooking(userContext, environmentService, segmentEvaluatorService);
//                    doesUserBelongToGoalSetting ||
//                    AppUtil.isUserAnInternalUserForAutomatedBooking(userContext,interfaces);

            pageData.put("newFooter", isUserPartOfExperiment);
            pageData.put("slideNewFooter", false);
            pageData.put("blurEnabled", true);

            WidgetContext widgetContext = new WidgetContext(pageContext);
            List<BaseWidget> classesWidgets = new TrayWidget().buildView(interfaces, userContext, widgetContext);
            CardListWidgetView<UpcomingItem> cardListWidgetView = null;
            List<UpcomingItem> upcomingItems = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(classesWidgets)) {
                cardListWidgetView = (CardListWidgetView<UpcomingItem>) classesWidgets.get(0);
                upcomingItems = cardListWidgetView.getData();
            }

            List<UpcomingItem> finalUpcomingClasses = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(upcomingItems)) {
                List<UpcomingItem> sortedItems = TimelineHelper.groupUpcomingOrCompletedItems(upcomingItems, userContext.getUserProfile().getTimezone(), false, null, WidgetType.TRAY_WIDGET);
                UpcomingItem firstCard = sortedItems.get(0);
                String[] subTitleSplit = firstCard.getSubTitle().split(" • ");
                firstCard.setCollapsedSubTitle(firstCard.getSubTitle());
                if (subTitleSplit.length > 1) {
                    String finalSubTitle = subTitleSplit[0] + ", " + firstCard.getHeader().getTitle() + LocalDate.parse(firstCard.getDate()).format(DateTimeFormatter.ofPattern(" d MMM, ")) + subTitleSplit[1];
                    firstCard.setCollapsedSubTitle(finalSubTitle);
                }
                finalUpcomingClasses = sortedItems;
            }

            pageData.put("numClasses", finalUpcomingClasses.size());
            List<Object> widgets = new ArrayList<>();

            if (AppUtil.isMemberJourneyExperienceSupported(interfaces, environmentService, userContext)) {
                pageData.put("hideProfileIcon", true);
            }

            // for non member users
            if (AppUtil.doesUserBelongsToContinuePurchaseJourney(interfaces.segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = getContinuePurchaseData(interfaces, userContext);
                if (renewMembershipInfo != null) {
                    pageData.put("renewMembershipInfo", renewMembershipInfo);
                }
            }

            // for member users
            if (AppUtil.doesUserBelongToProBirthdayOfferSegment(segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = getBirthdayAnniversaryOfferData(true, true);
                pageData.put("renewMembershipInfo", renewMembershipInfo);
            } else if (AppUtil.doesUserBelongToEliteBirthdayOfferSegment(segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = getBirthdayAnniversaryOfferData(false, true);
                pageData.put("renewMembershipInfo", renewMembershipInfo);
            } else if (AppUtil.doesUserBelongToProAnniversaryOfferSegment(segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = getBirthdayAnniversaryOfferData(true, false);
                pageData.put("renewMembershipInfo", renewMembershipInfo);
            } else if (AppUtil.doesUserBelongToEliteAnniversaryOfferSegment(segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = getBirthdayAnniversaryOfferData(false, false);
                pageData.put("renewMembershipInfo", renewMembershipInfo);
            } else if (PlayUtil.doesUserBelongToPlayDeadBaseFreeTrialSegment(segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = PlayUtil.getDeadBaseOfferData(true);
                pageData.put("renewMembershipInfo", renewMembershipInfo);
            } else if (PlayUtil.doesUserBelongToPlayDeadBaseRenewalSegment(segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = PlayUtil.getDeadBaseOfferData(false);
                pageData.put("renewMembershipInfo", renewMembershipInfo);
            } else if (PlayUtil.doesUserBelongToPlayAnniversaryOfferSegment(segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = PlayUtil.getBirthdayAnniversaryOfferData(false);
                pageData.put("renewMembershipInfo", renewMembershipInfo);
            } else if (PlayUtil.doesUserBelongToPlayBirthdayOfferSegment(segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = PlayUtil.getBirthdayAnniversaryOfferData(true);
                pageData.put("renewMembershipInfo", renewMembershipInfo);
            }

            RenewMembershipInfo userRenewalInfo = getUserRenewalInfo(userContext, interfaces, bestPackService);
            if (Objects.nonNull(userRenewalInfo)) {
                pageData.put("renewMembershipInfo", userRenewalInfo);
            } else if (PlayUtil.doesUserBelongToPlayUFRExperimentSegment(segmentEvaluatorService, environmentService, userContext)) {
                RenewMembershipInfo renewMembershipInfo = PlayUtil.getUserRenewalData(userContext, interfaces);
                if (renewMembershipInfo != null) {
                    pageData.put("renewMembershipInfo", renewMembershipInfo);
                }
            }

            KickstarterPlanDetailsResponse response = new KickstarterPlanDetailsResponse();
            log.debug("Kickstarter plan in home page hook {} {} {}", isUserPartOfExperiment, finalUpcomingClasses.size() > 0
                    , AppUtil.doesUserBelongToActivityFirstHPSegment(interfaces, userContext));

            // ACTIVITY FIRST HP
            boolean playVisibilityWithZeroBooking = isPlayVisibilityExperiment && (finalUpcomingClasses == null || finalUpcomingClasses.size() == 0) && isUserWithZeroBooking;
            if (AppUtil.doesUserBelongToActivityFirstHPSegment(interfaces, userContext) && !playVisibilityWithZeroBooking) {
                List<Membership> memberships = MembershipUtil.activeMembershipPresentForBenefits(interfaces, userContext, List.of("GYMFIT_GA", "GYMFIT_GX", "CULT", "PLAY"));
                AutomatedBookingInfo collapsedBottomSheetState = getCollapsedBottomSheetState(userContext, interfaces, finalUpcomingClasses, memberships);
                widgets.add(collapsedBottomSheetState);
                pageData.put("enableActivityFirstHP", true);
                if (finalUpcomingClasses != null && finalUpcomingClasses.size() > 0) {
                    LevelSectionWidget levelSectionWidget = this.setLevelSectionWidget(interfaces, userContext, finalUpcomingClasses, false, CollectionUtils.isEmpty(memberships));
                    pageData.put("numClasses", levelSectionWidget.getChapters().size());
                    widgets.add(levelSectionWidget);
                }
            }

            if (widgets != null && !widgets.isEmpty()) {
                pageData.put("automatedBookingFlow", widgets);
            }

            if (AppUtil.showWhatsappIconOnHome(interfaces, userContext)) {
                Action action = new Action();
                action.setActionType(ActionType.SEND_WHATSAPP_MESSAGE);
                Map<String, Object> meta = new HashMap<>();
                meta.put("number", CULT_WHATSAPP_NO);
                meta.put("message", "Hi");
                action.setMeta(meta);

                HashMap<String, Object> analyticsData = new HashMap<>();
                analyticsData.put("widget_name", "CIRCULAR_FLOATING_ACTION_BUTTON");
                analyticsData.put("pageId", "hometab");
                action.setAnalyticsData(analyticsData);

                HomeFloatingAction homeFloatingAction = new HomeFloatingAction();
                homeFloatingAction.setImageUrl("image/icons/cult/whatsapp-icon.json");
                homeFloatingAction.setAction(action);

                pageData.put("floatingAction", homeFloatingAction);
            }
        } catch (Exception error) {
            pageData.put("newFooter", false);
            log.error("Kickstarter plan error in home page hook {}", error);
            interfaces.exceptionReportingService.reportException(error);
        }

        return pageData;
    }

    public static RenewMembershipInfo getDeadBaseRenewalData(UserContext userContext, ServiceInterfaces interfaces) throws BaseException {
        RenewMembershipInfo renewMembershipInfo = new RenewMembershipInfo();
        renewMembershipInfo.setSubtextStyle("P5");
        renewMembershipInfo.setTitleStyle("P3");
        renewMembershipInfo.setCTAText("CLAIM");
        if(AppUtil.doesUserBelongToDeadBaseBestOfferEliteSegment(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext)
            || AppUtil.doesUserBelongToDeadBaseBestOfferProSegment(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext)) {
            renewMembershipInfo.setTitle("Guaranteed best price enabled");
            renewMembershipInfo.setSubtext("ONLY FOR YOU");
            renewMembershipInfo.setTitleColor("#0FE498");
            renewMembershipInfo.setBackgroundColorStart("#6F42A8");
            renewMembershipInfo.setBackgroundColorEnd("#2B0061");
            Action action = new Action("", ActionType.GET_BEST_ACTIVE_PACK);
            Map<String, String> lastMembershipDetails = AppUtil.getLastMembershipDetailsForBestPrice(userContext, interfaces);
            if(lastMembershipDetails != null) {
                boolean isProUser = AppUtil.doesUserBelongToDeadBaseBestOfferProSegment(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
                boolean isEliteUser = AppUtil.doesUserBelongToDeadBaseBestOfferEliteSegment(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext);
                action.setMeta(new HashMap<String, String>() {{
                    put("apiUrl", isProUser ? "/v2/gymfit/bestPack" : (isEliteUser ? "/v2/cult/bestPack" : null));
                    put("startDate", lastMembershipDetails.get("startDate"));
                    if (!isProUser) put("centerId", lastMembershipDetails.get("centerId"));
                }});
                renewMembershipInfo.setAction(action);
            }
        }
        else if (AppUtil.doesUserBelongToDeadBaseAlumniPassElitePlaySegment(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext)
                || AppUtil.doesUserBelongToDeadBaseAlumniPassEliteNonPlaySegment(interfaces.segmentEvaluatorService, interfaces.environmentService, userContext)) {
            renewMembershipInfo.setAction(new Action("curefit://fl_listpage?pageId=Dead_Base&hideTitle=true", ActionType.NAVIGATION));
            renewMembershipInfo.setTitle("ALUMNI PASS");
            renewMembershipInfo.setSubtext("Get 14 days free access with");
            renewMembershipInfo.setTitleColor("#FFDB69");
            renewMembershipInfo.setBackgroundColorStart("#6F42A8");
            renewMembershipInfo.setBackgroundColorEnd("#2B0061");
        }
        return renewMembershipInfo;
    }

    public static Membership processMemberships(List<Membership> memberships, UserContext userContext) {
        if (memberships == null || memberships.isEmpty()) {
            return null;
        }

        // Filter memberships
        List<Membership> filteredMemberships = new ArrayList<>(memberships.stream()
                // Remove memberships with start date after today
                .filter(m -> m.getStart() <= Instant.now().toEpochMilli())
                // Remove memberships with end date less than 60 days ago
                .filter(m -> m.getEnd() >= Instant.now().minusSeconds(60L * 24 * 60 * 60).toEpochMilli())
                .toList());

        filteredMemberships.sort(Comparator.comparingLong(Membership::getEnd));

        return filteredMemberships.isEmpty() ? null : filteredMemberships.getLast();
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    public static RenewMembershipInfo getUserRenewalInfo(UserContext userContext, ServiceInterfaces interfaces, BestPackService bestPackService) throws Exception {

        Boolean isUserInBestPriceSegment = AppUtil.doesUserBelongToMasterBestPriceSegment(interfaces, userContext);
        Boolean isUserInRenewSegment = AppUtil.doesUserBelongToMasterRenewSegment(interfaces, userContext) || AppUtil.isRenewalForCenterLevelPricingSupported(userContext, interfaces, interfaces.environmentService);
        List<Membership> memberships = new ArrayList<>();
        if (isUserInBestPriceSegment || isUserInRenewSegment) {
            memberships = interfaces.membershipService.getCachedMembershipsForUser(
                userContext.getUserProfile().getUserId(),
                "curefit",
                MembershipFilter.builder()
                    .benefits(List.of("CULT", "GYMFIT_GA", "GYMFIT_GX"))
                    .status(Status.CANCELLED)
                    .status(Status.PAUSED)
                    .status(Status.PURCHASED)
                    .build()
            ).get().stream().filter(membership -> !membership.getBenefitNames(true).contains("PLAY")).toList();
        } else return null;

        Map<String, Object> analyticsDataOnClick = new HashMap<>();

        Membership renewMembership = processMemberships(memberships, userContext);
        Action bestPack = null;

        String title = "";
        String subTitle = "";
        if (Objects.isNull(renewMembership)) {
            analyticsDataOnClick.put("renewMembershipId", "NOT_FOUND");
            title = "Buy Now";
            subTitle = "Your Cultpass is Expired";
            bestPack = bestPackService.findBestCultPack(null, userContext, ProductSubType.GENERAL);
            analyticsDataOnClick.put("renewSku", "ELITE");
            analyticsDataOnClick.put("option", 14);
            analyticsDataOnClick.put("three", "yes");
        } else {
            analyticsDataOnClick.put("renewMembershipId", renewMembership.getId());
            LocalDate today = TimeUtil.getDateNow(userContext.getUserProfile().getTimezone()).toLocalDate();
            LocalDate endDate = TimeUtil.getDateFromTime(renewMembership.getEnd(), userContext.getUserProfile().getTimezone()).toLocalDate();
            int daysLeft = Math.abs(TimeUtil.getDaysInBetween(endDate, today));
            analyticsDataOnClick.put("daysLeft", daysLeft);
            boolean isExpired = true;
            if (renewMembership.getEnd() >= Instant.now().toEpochMilli()) {
                isExpired = false;
                title = daysLeft + (daysLeft > 1 ? " days" : " day");
            } else title = daysLeft + (daysLeft > 1 ? " days ago" : " day ago");
            analyticsDataOnClick.put("isExpired", isExpired);
            FitnessCenterUtil.UserMembershipType userMembershipType = getUserMembershipType(renewMembership, false, userContext, true);
            boolean renewExperiment = AppUtil.doesUserBelongToRenewExperiment(interfaces, userContext);
            switch (userMembershipType) {
                case ELITE -> {
                    analyticsDataOnClick.put("skuType", "ELITE");
                    if (isExpired) subTitle = "Cultpass ELITE expired";
                    else subTitle = "Cultpass Elite expires in";
//                    if (isUserInBestPriceSegment) {
                    if (!isUserInBestPriceSegment || renewExperiment) {
                        bestPack = bestPackService.findBestCultPack(null, userContext, ProductSubType.PLUS);
                        analyticsDataOnClick.put("option", 2);
                        analyticsDataOnClick.put("renewSku", "ELITE_PLUS");
                    } else {
                        bestPack = bestPackService.findBestCultPack(null, userContext, ProductSubType.GENERAL);
                        analyticsDataOnClick.put("renewSku", "ELITE");
                        analyticsDataOnClick.put("option", 3);
                    }
//                    } else {
//                        String url = "curefit://fl_listpage?pageId=elite_plus_vs_elite";
//                        if (!AppUtil.isSKuPlusFlow(interfaces, interfaces.environmentService, userContext)) {
//                            url = "curefit://fl_listpage?pageId=CultPassBlackSKU";
//                        }
//                        bestPack = Action.builder().actionType(ActionType.NAVIGATION).url(url).build();
//                        analyticsDataOnClick.put("option", 1);
//                    }
                }
                case ELITE_PLUS -> {
                    analyticsDataOnClick.put("skuType", "ELITE_PLUS");
                    if (isExpired) subTitle = "Cultpass ELITE PLUS expired";
                    else subTitle = "Cultpass Elite PLUS expires in";
//                    String url = "curefit://fl_listpage?pageId=elite_plus_vs_elite";
//                    if (!AppUtil.isSKuPlusFlow(interfaces, interfaces.environmentService, userContext)) {
//                        url = "curefit://fl_listpage?pageId=CultPassBlackSKU";
//                    }
//                    bestPack = Action.builder().actionType(ActionType.NAVIGATION).url(url).build();
                    bestPack = bestPackService.findBestCultPack(null, userContext, ProductSubType.PLUS);
                    analyticsDataOnClick.put("option", 4);
                    analyticsDataOnClick.put("renewSku", "ELITE_PLUS");
                }
                case PRO -> {
                    analyticsDataOnClick.put("skuType", "PRO");
                    if (isExpired) subTitle = "Cultpass PRO expired";
                    else subTitle = "Cultpass PRO expires in";
//                    if (isUserInBestPriceSegment) {
                    if (renewExperiment || !isUserInBestPriceSegment) {
                        bestPack = bestPackService.getBestGymfitPack(userContext, ProductSubType.PLUS, null);
                        analyticsDataOnClick.put("option", 6);
                        analyticsDataOnClick.put("renewSku", "PRO_PLUS");
                    } else {
                        bestPack = bestPackService.getBestGymfitPack(userContext, ProductSubType.GENERAL, null);
                        analyticsDataOnClick.put("renewSku", "PRO");
                        analyticsDataOnClick.put("option", 7);
                    }
//                    } else {
//                        String url = "curefit://fl_listpage?pageId=pro_plus_vs_pro";
//                        if (!AppUtil.isSKuPlusFlow(interfaces, interfaces.environmentService, userContext)) {
//                            url = "curefit://fl_listpage?pageId=CultPassGoldSKU";
//                        }
//                        bestPack = Action.builder().actionType(ActionType.NAVIGATION).url(url).build();
//                        analyticsDataOnClick.put("option", 5);
//                    }
                }
                case PRO_PLUS -> {
                    analyticsDataOnClick.put("skuType", "PRO_PLUS");
                    if (isExpired) subTitle = "Cultpass PRO PLUS expired";
                    else subTitle = "Cultpass PRO PLUS expires in";
//                    String url = "curefit://fl_listpage?pageId=pro_plus_vs_pro";
//                    if (!AppUtil.isSKuPlusFlow(interfaces, interfaces.environmentService, userContext)) {
//                        url = "curefit://fl_listpage?pageId=CultPassGoldSKU";
//                    }
//                    bestPack = Action.builder().actionType(ActionType.NAVIGATION).url(url).build();
                    bestPack = bestPackService.getBestGymfitPack(userContext, ProductSubType.PLUS, null);
                    analyticsDataOnClick.put("option", 8);
                    analyticsDataOnClick.put("renewSku", "PRO_PLUS");
                }
                case ELITE_SELECT -> {
                    String skuType = "ELITE_SELECT";
                    analyticsDataOnClick.put("skuType", skuType);
                    if (isExpired) subTitle = "Cultpass SELECT expired";
                    else subTitle = "Cultpass SELECT expires in";
                    String selectMembershipCenterId = MembershipUtil.getAccessCenterIdForMembership(renewMembership);
                    analyticsDataOnClick.put("selectMembershipCenterId", selectMembershipCenterId);
                    if (renewExperiment) {
                        bestPack = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://fl_listpage?pageId=CultPassBlackSKU").build();
                        analyticsDataOnClick.put("renewSku", "CultPassBlackSKU");
                        analyticsDataOnClick.put("option", 9);
                    } else if (Objects.nonNull(selectMembershipCenterId)) {
                        bestPack = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://fl_pack_comparison?centerId=" + selectMembershipCenterId + "&selectedSku=center_select&centerType=ELITE").build();
                        analyticsDataOnClick.put("renewSku", "comparisongrid");
                        analyticsDataOnClick.put("option", 10);
                    }
                }
                case PRO_SELECT -> {
                    String skuType = "PRO_SELECT";
                    analyticsDataOnClick.put("skuType", skuType);
                    if (isExpired) subTitle = "Cultpass SELECT expired";
                    else subTitle = "Cultpass SELECT expires in";
                    String selectMembershipCenterId = MembershipUtil.getAccessCenterIdForMembership(renewMembership);
                    analyticsDataOnClick.put("selectMembershipCenterId", selectMembershipCenterId);
                    if (renewExperiment) {
                        bestPack = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://fl_listpage?pageId=CultPassGoldSKU").build();
                        analyticsDataOnClick.put("renewSku", "CultPassBlackSKU");
                        analyticsDataOnClick.put("option", 11);
                    } else if (Objects.nonNull(selectMembershipCenterId)) {
                        bestPack = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://fl_pack_comparison?centerId=" + selectMembershipCenterId + "&selectedSku=center_select&centerType=PRO").build();
                        analyticsDataOnClick.put("renewSku", "comparisongrid");
                        analyticsDataOnClick.put("option", 12);
                    }
                }
            }
            analyticsDataOnClick.put("one", isUserInBestPriceSegment);
            analyticsDataOnClick.put("two", renewExperiment);
        }
        if (Objects.isNull(bestPack) || Objects.isNull(bestPack.getUrl())) {
            bestPack = bestPackService.findBestCultPack(null, userContext, ProductSubType.GENERAL);
            analyticsDataOnClick.put("renewSku", "ELITE");
            analyticsDataOnClick.put("option", 13);
        }

        analyticsDataOnClick.put("from", "HOME_PAGE_RENEW_STRIP");

        bestPack.setAnalyticsData(analyticsDataOnClick);

        RenewMembershipInfo renewMembershipInfo = new RenewMembershipInfo();
        if (isUserInBestPriceSegment) {
            renewMembershipInfo.setTitle("Guaranteed best price enabled");
            renewMembershipInfo.setSubtext("ONLY FOR YOU");
            renewMembershipInfo.setTitleColor("#0FE498");
            renewMembershipInfo.setBackgroundColorStart("#6F42A8");
            renewMembershipInfo.setBackgroundColorEnd("#2B0061");
            renewMembershipInfo.setCTAText("CLAIM");
        } else {
            renewMembershipInfo.setTitle(title);
            renewMembershipInfo.setSubtext(subTitle);
            renewMembershipInfo.setSubtextStyle("P5");
            renewMembershipInfo.setTitleStyle("P3");
            renewMembershipInfo.setTitleColor("#FF5942");
            renewMembershipInfo.setBackgroundColorStart("#580909");
            renewMembershipInfo.setBackgroundColorEnd("#000000");
            renewMembershipInfo.setCTAText("RENEW");
        }
        renewMembershipInfo.setAction(bestPack);

        return renewMembershipInfo;
    }


    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    public static RenewMembershipInfo getUserRenewalData(UserContext userContext, ServiceInterfaces interfaces, Boolean isPartOfCenterSelectionSegment, Boolean isProUser) throws BaseException {
        // added filter to remove play membership as it is not supported in renewal and has cult as a benefit
        List<Membership> memberships = MembershipUtil.activeMembershipPresentForBenefits(interfaces, userContext, List.of("CULT", "GYMFIT_GA", "GYMFIT_GX")).stream().filter(membership -> !membership.getBenefitNames(true).contains("PLAY")).collect(Collectors.toList());
        LocalDate today = TimeUtil.getDateNow(userContext.getUserProfile().getTimezone()).toLocalDate();
        Membership renewalMembership = null;
        Membership lastExpiredMembership = null;
        Integer daysLeft = null;
        String startDate = null;
        if (!CollectionUtils.isEmpty(memberships)) {
            for (Membership membership : memberships) {
                if (membership.getStatus().equals(Status.PURCHASED)) {
                    LocalDate endDate = TimeUtil.getDateFromTime(membership.getEnd(), userContext.getUserProfile().getTimezone()).toLocalDate();
                    int numDaysToEndFromToday = TimeUtil.getDaysInBetween(today, endDate);
                    startDate = endDate.plusDays(1).toString();
                    if (numDaysToEndFromToday <= 30) {
                        renewalMembership = membership;
                        daysLeft = numDaysToEndFromToday;
                        break;
                    }
                }
            }
        } else {
            String userId = userContext.getUserProfile().getUserId();
            List<String> benefits = List.of("CULT");
            // added filter to remove play membership as it is not supported in renewal and has cult as a benefit
            List<Membership> membershipDetails = interfaces.membershipService.getCachedMembershipsForUser(userId, "curefit", MembershipFilter.builder().benefits(benefits).status(Status.PURCHASED).status(Status.CANCELLED).build()).get().stream().filter(membership -> !membership.getBenefitNames(true).contains("PLAY")).collect(Collectors.toList());
            long currentTimeMillis = System.currentTimeMillis();
            membershipDetails = membershipDetails.stream().filter(membership -> membership.getEnd() < currentTimeMillis).collect(Collectors.toList());
            long maxEndTime = 0;
            for (Membership membership : membershipDetails) {
                if (membership.getStatus().equals(Status.CANCELLED) || membership.getEnd() < currentTimeMillis) {
                    if (membership.getEnd() > maxEndTime) {
                        LocalDate endDate = TimeUtil.getDateFromTime(membership.getEnd(), userContext.getUserProfile().getTimezone()).toLocalDate();
                        daysLeft = Math.abs(TimeUtil.getDaysInBetween(endDate, today));
                        maxEndTime = membership.getEnd();
                        lastExpiredMembership = membership;
                    }
                }
            }
            if (lastExpiredMembership != null && daysLeft != null && daysLeft <= 60) {
                RenewMembershipInfo renewMembershipInfo = new RenewMembershipInfo();
                renewMembershipInfo.setTitle(daysLeft + (daysLeft > 1 ? " days ago" : " day ago"));
                renewMembershipInfo.setSubtext("Cultpass " + (isProUser ? "PRO" : "ELITE") + " expired");
                renewMembershipInfo.setSubtextStyle("P5");
                renewMembershipInfo.setTitleStyle("P3");
                renewMembershipInfo.setTitleColor("#FF5942");
                renewMembershipInfo.setBackgroundColorStart("#580909");
                renewMembershipInfo.setBackgroundColorEnd("#000000");
                renewMembershipInfo.setCTAText("RENEW");
                Boolean isSelectRenewal = (lastExpiredMembership.getBenefitNames(true).contains("GYMFIT_GA") || lastExpiredMembership.getBenefitNames(true).contains("GYMFIT_GX") || lastExpiredMembership.getBenefitNames(true).contains("CULT") || lastExpiredMembership.getBenefitNames(true).contains("CULT_GYM")) && lastExpiredMembership.getAttributes().stream().anyMatch(p -> p.getAttrKey().equals(AttributeKeyType.ACCESS_CENTER.toString()));
                if (isSelectRenewal) {
                    renewMembershipInfo.setSubtext("Cultpass SELECT expired");
                    Action _ctaAction = CultUtil.getSelectRenewalAction(userContext, lastExpiredMembership, interfaces);
                    renewMembershipInfo.setAction(_ctaAction);
                } else if (isPartOfCenterSelectionSegment) {
                    renewMembershipInfo.setAction(new Action("curefit://fl_listpage?pageId=select_renewal&hideTitle=true", ActionType.NAVIGATION));
                } else {
                    Action action = new Action("", ActionType.GET_BEST_ACTIVE_PACK);
                    String finalStartDate = today.toString();
                    String centerId = null;
                    String membershipCityId = lastExpiredMembership.getMetadata().get("cityId") != null ? lastExpiredMembership.getMetadata().get("cityId").toString() : null;
                    if (membershipCityId != null && userContext.getUserProfile().getCity().getName().equals(membershipCityId)) {
                        centerId = getCenterFromMembership(lastExpiredMembership, interfaces.centerService);
                    }
                    String finalCenterId = centerId;
                    action.setMeta(new HashMap<String, String>() {{
                        put("apiUrl", isProUser ? "/v2/gymfit/bestPack" : "/v2/cult/bestPack");
                        put("startDate", finalStartDate);
                        if (!isProUser) put("centerId", finalCenterId);
                    }});
                    renewMembershipInfo.setAction(action);
                }
                return renewMembershipInfo;
            }
        }
        if (renewalMembership == null || daysLeft == null) {
            return null;
        }
        RenewMembershipInfo renewMembershipInfo = new RenewMembershipInfo();
        renewMembershipInfo.setTitle(daysLeft + (daysLeft > 1 ? " days" : " day"));
        renewMembershipInfo.setSubtext("Cultpass " + (isProUser ? "PRO" : "ELITE") + " expires in");
        renewMembershipInfo.setSubtextStyle("P5");
        renewMembershipInfo.setTitleStyle("P3");
        if (daysLeft > 15) {
            renewMembershipInfo.setTitleColor("#F7C744");
            renewMembershipInfo.setBackgroundColorStart("#3D3D3D");
            renewMembershipInfo.setBackgroundColorEnd("#000000");
        } else {
            renewMembershipInfo.setTitleColor("#FF5942");
            renewMembershipInfo.setBackgroundColorStart("#580909");
            renewMembershipInfo.setBackgroundColorEnd("#000000");
        }
        renewMembershipInfo.setCTAText("RENEW");
        Boolean isSelectRenewal = (renewalMembership.getBenefitNames(true).contains("GYMFIT_GA") || renewalMembership.getBenefitNames(true).contains("GYMFIT_GX") || renewalMembership.getBenefitNames(true).contains("CULT") || renewalMembership.getBenefitNames(true).contains("CULT_GYM")) && renewalMembership.getAttributes().stream().anyMatch(p -> p.getAttrKey().equals(AttributeKeyType.ACCESS_CENTER.toString()));
        if (isSelectRenewal) {
            renewMembershipInfo.setSubtext("Cultpass SELECT expires in");
            Action _ctaAction = CultUtil.getSelectRenewalAction(userContext, renewalMembership, interfaces);
            renewMembershipInfo.setAction(_ctaAction);
        } else if (isPartOfCenterSelectionSegment) {
            renewMembershipInfo.setAction(new Action("curefit://fl_listpage?pageId=select_renewal&hideTitle=true", ActionType.NAVIGATION));
        } else {
            Action action = new Action("", ActionType.GET_BEST_ACTIVE_PACK);
            String finalStartDate = startDate;
            String centerId = null;
            String membershipCityId = renewalMembership.getMetadata().get("cityId") != null ? renewalMembership.getMetadata().get("cityId").toString() : null;
            if (membershipCityId != null && userContext.getUserProfile().getCity().getName().equals(membershipCityId)) {
                centerId = getCenterFromMembership(renewalMembership, interfaces.centerService);
            }
            String finalCenterId = centerId;
            action.setMeta(new HashMap<String, String>() {{
                put("apiUrl", isProUser ? "/v2/gymfit/bestPack" : "/v2/cult/bestPack");
                put("startDate", finalStartDate);
                if (!isProUser) put("centerId", finalCenterId);
            }});
            renewMembershipInfo.setAction(action);
        }
        return renewMembershipInfo;
    }

    public static String getCenterFromMembership(Membership membership, CenterService centerService) {
        Object centerId = membership.getMetadata().get("centerId");
        if (centerId == null) {
            centerId = membership.getMetadata().get("centerServiceCenterId");
        }
        String centerServiceId = null;
        if (centerId != null) {
            centerServiceId = objectMapper.convertValue(centerId, new TypeReference<>() {
            });
        }
        String cultCenterId = null;
        if(centerServiceId != null) {
            try {
                List<CenterEntry> centerEntries = centerService.getCachedCentersById(List.of(centerServiceId), false, null, null);
                if(centerEntries != null && !centerEntries.isEmpty()){
                    if(centerEntries.stream().findFirst().get().getMeta().containsKey(CULT_CENTER_ID_KEY)) {
                        cultCenterId = centerEntries.stream().findFirst().get().getMeta().get(CULT_CENTER_ID_KEY).toString();
                    }
                }
            } catch (HttpException e) {
                e.printStackTrace();
            }
        }

        return cultCenterId;
    }

    public static RenewMembershipInfo getBirthdayAnniversaryOfferData(boolean proMember, boolean birthdayOffer) {
        RenewMembershipInfo renewMembershipInfo = new RenewMembershipInfo();
        renewMembershipInfo.setSubtext(birthdayOffer ? "Your birthday gift \uD83C\uDF82" : "Cult Anniversary gift \uD83C\uDF89");
        renewMembershipInfo.setTitle("Additional 10% off");
        renewMembershipInfo.setSubtextStyle("P5");
        renewMembershipInfo.setTitleStyle("P3");
        renewMembershipInfo.setTitleColor("#0FE498");
        renewMembershipInfo.setBackgroundColorStart("#3D3D3D");
        renewMembershipInfo.setBackgroundColorEnd("#000000");
        renewMembershipInfo.setCTAText("CLAIM");
        Action action = new Action(
                proMember ? "curefit://listpage?pageId=SKUPurchasePage&selectedTab=gold&scrollToWidgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play&widgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play" :
                        "curefit://listpage?pageId=SKUPurchasePage&selectedTab=black&scrollToWidgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play&widgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play",
                ActionType.NAVIGATION);
        renewMembershipInfo.setAction(action);
        return renewMembershipInfo;
    }

    private static String getUrlForContinuePurchase(String sku, Boolean isSelect, Integer centerServiceId, String productId) {
        if (Objects.equals(sku, "GYMFIT_FITNESS_PRODUCT")) {
            if (isSelect) {
                return "curefit://fl_pack_comparison?centerId=" + centerServiceId + "&selectedSku=center_select&centerType=PRO";
            } else {
                return "curefit://checkout_v2?productId=" + productId;
            }
        } else if (Objects.equals(sku, "FITNESS")) {
            if (isSelect) {
                return "curefit://fl_pack_comparison?centerId=" + centerServiceId + "&selectedSku=center_select&centerType=ELITE";
            } else {
                return "curefit://checkout_v2?productId=" + productId;
            }
        }
        return "curefit://checkout_v2?productId=" + productId;
    }

    public static RenewMembershipInfo getContinuePurchaseData(ServiceInterfaces interfaces, UserContext userContext) throws BaseException {
        try {
            UserAttributesResponse attributesResponse = interfaces.userAttributesClient.getAttributes(
                    Long.valueOf(userContext.getUserProfile().getUserId()), "continue_purchase_option",
                    AppUtil.getAppTenantFromUserContext(userContext), null
            );

            if (Objects.isNull(attributesResponse) || Objects.isNull(attributesResponse.getAttributes()) || attributesResponse.getAttributes().isEmpty()) {
                return null;
            }

            String Options = (String) attributesResponse.getAttributes().getOrDefault("continue_purchase_option", "");
            if (Objects.isNull(Options) || Options.isEmpty()) {
                return null;
            }
            JSONObject jsonObject = new JSONObject(Options);
            String productId = jsonObject.has("productid") ? jsonObject.getString("productid") : null;
            if (Objects.equals(productId, "")) return null;
            String centerName = jsonObject.has("centername") ? jsonObject.getString("centername") : null;
            Integer centerServiceId = jsonObject.has("centerserviceid") ? jsonObject.getInt("centerserviceid") : null;
            String sku = jsonObject.has("sku") ? jsonObject.getString("sku") : null;
            Boolean isSelect = jsonObject.has("isselect") && jsonObject.getBoolean("isselect");
            FitnessPackBrowseWidget browseWidget = new FitnessPackBrowseWidget();
            HashMap<String, String> getFinalPricingResponse = browseWidget.getFinalPricingFromProductId(userContext, ProductType.valueOf(sku),
                    productId, isSelect, interfaces, (Objects.nonNull(centerServiceId)) ? Long.valueOf(centerServiceId) : null);
            String sellingPrice = getFinalPricingResponse.get("offerPrice");
            String duration = getFinalPricingResponse.get("productDurationInMonths");

            RenewMembershipInfo renewMembershipInfo = new RenewMembershipInfo();
            renewMembershipInfo.setSubtext(isSelect ? centerName : (Objects.equals(sku, "GYMFIT_FITNESS_PRODUCT") ? "Cultpass PRO" : "Cultpass ELITE"));
            renewMembershipInfo.setTitle("₹" + sellingPrice + " • " + duration + " Month" + ((Objects.equals(duration, "1")) ? "" : "s"));
            renewMembershipInfo.setSubtextStyle("P1");
            renewMembershipInfo.setTitleStyle("P7");
            renewMembershipInfo.setCtaTextStyle("P6");
            renewMembershipInfo.setTitleColor("#FFD783");
            renewMembershipInfo.setBackgroundColorStart("#3D3D3D");
            renewMembershipInfo.setBackgroundColorEnd("#000000");
            renewMembershipInfo.setCTAText("CONTINUE\nPURCHASE");
            Action action = new Action(getUrlForContinuePurchase(sku, isSelect, centerServiceId, productId), ActionType.NAVIGATION);
            renewMembershipInfo.setAction(action);
            interfaces.cfAnalytics.sendEvent(
                    ContinuePurchaseOptions.builder().centerName(centerName).centerServiceId(String.valueOf(centerServiceId)).isSelect(isSelect)
                            .price(sellingPrice).productId(productId).showingFullPrice(true).sku(sku).duration(duration).build(),
                    userContext, true, true, false, false
            );
            return renewMembershipInfo;
        } catch (Exception e) {
            interfaces.exceptionReportingService.reportException(e);
        }
        return null;
    }

    public static void resetFitnessReportRashiAttributes(UserContext userContext, ServiceInterfaces interfaces, String attribute, Object attributeValue) throws BaseException {
        UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
        userAttributeEntry.setUserId(Long.parseLong(userContext.getUserProfile().getUserId()));
        userAttributeEntry.setAppTenant(AppUtil.getAppTenantFromUserContext(userContext));
        userAttributeEntry.setAttribute(attribute);
        userAttributeEntry.setAttrValue(attributeValue);
        userAttributeEntry.setDataType(DataType.OBJECT);
        userAttributeEntry.setDescription("fitness report");
        userAttributeEntry.setNamespace("QUEST");
        interfaces.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, AppUtil.getAppTenantFromUserContext(userContext));
    }

    public static Long getWeeklyActiveDays(UserContext userContext, ServiceInterfaces interfaces) throws BaseException {
        UserAttributesResponse userAttributesResponse;
        Long weeklyActiveDays = null;
        String weeklyActiveDaysUpdatedDated = null;
        ZoneId istZone = ZoneId.of("Asia/Kolkata");
        LocalDateTime now = LocalDateTime.now(Clock.system(istZone));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate monday = now.toLocalDate().with(DayOfWeek.MONDAY);
        LocalDateTime mondayDateTime = monday.atStartOfDay();
        ZonedDateTime mondayZonedDateTime = mondayDateTime.atZone(istZone);
        String currentMondayDate = mondayZonedDateTime.format(formatter);


        userAttributesResponse = interfaces.userAttributesCacheClient.getAttributes(Long.valueOf(userContext.getUserProfile().getUserId()),
                Arrays.asList(Constants.RASHI_ATTRIBUTE_FOR_USER_WEEKLY_ACTIVE_DAYS, Constants.RASHI_ATTRIBUTE_FOR_USER_WEEKLY_ACTIVE_DAYS_UPDATED_DATE), AppTenant.CUREFIT);

        if (userAttributesResponse != null && userAttributesResponse.getAttributes() != null) {
            if (userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_WEEKLY_ACTIVE_DAYS) != null) {
                weeklyActiveDays = Long.parseLong(userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_WEEKLY_ACTIVE_DAYS).toString());
            }
            if (userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_WEEKLY_ACTIVE_DAYS_UPDATED_DATE) != null) {
                weeklyActiveDaysUpdatedDated = userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_WEEKLY_ACTIVE_DAYS_UPDATED_DATE).toString();
            }
        }
        if(weeklyActiveDays == null || weeklyActiveDaysUpdatedDated == null || weeklyActiveDaysUpdatedDated.compareToIgnoreCase(currentMondayDate) < 0) {
            weeklyActiveDays = 0L;
        }
        return weeklyActiveDays;
    }

    public static AutomatedBookingInfo getCollapsedBottomSheetState(UserContext userContext, ServiceInterfaces interfaces,
                                                                    List<UpcomingItem> finalUpcomingClasses, List<Membership> memberships) throws ExecutionException, InterruptedException, BaseException {
        boolean isMembershipPaused = false;
        long remainingPauseDays = 0;
        Membership pausedMembership = null;
        boolean hasActiveProMembership = false;
        if(memberships != null && !memberships.isEmpty()) {
            List<Membership> proMemberships = GymUtil.getProMemberships(memberships);
            if(proMemberships!=null && !proMemberships.isEmpty()) {
                hasActiveProMembership = true;
            }
        }
        if (!CollectionUtils.isEmpty(memberships)) {
            for (Membership membership : memberships) {
                if (membership.getStatus().equals(Status.PAUSED)) {
                    pausedMembership = membership;
                    isMembershipPaused = true;
                    remainingPauseDays = membership.getRemainingPauseDuration() / MILLIS_TO_DAYS_DENOMINATOR;
                }
            }
        }

        AutomatedBookingInfo collapsedBottomSheetState = new AutomatedBookingInfo();


        UserWodEntry userWodEntry = null;
        if (AppUtil.doesUserHasPlan(userContext)) {
            try {
                userWodEntry = (UserWodEntry) userContext.getRequestCache().getRequestFuture(
                        RequestType.GET_SMART_WORKOUT_PLAN_CURRENT_DAY_WOD,
                        userContext).get();
            } catch (Exception e) {
                interfaces.exceptionReportingService.reportException(e);
            }
        }
        boolean isResumeWorkoutCase = userWodEntry != null && userWodEntry.getStatus() == UserWodStatus.STARTED && userWodEntry.getStartType() == UserWodStartType.NORMAL
                && Duration.between(userWodEntry.getStartTime(), Instant.now()).toMillis() < CUTOFF_TIME_RESUME_STORY;

        if (isResumeWorkoutCase) {
            Action resumeWorkoutAction = new Action();
            resumeWorkoutAction.setTitle("RESUME");
            resumeWorkoutAction.setActionType(ActionType.NAVIGATION);
            resumeWorkoutAction.setIconUrl("image/vm/18ee80e4-871d-40f8-945f-e7534b47ab44.png");
            collapsedBottomSheetState.setCollapsedTitle(userWodEntry.getWorkoutName());
            int cycleDay = userWodEntry.getDayOfCycle();
            Instant startTime = userWodEntry.getStartTime();
            long timeSpent = Duration.between(startTime, Instant.now()).toMinutes();

            collapsedBottomSheetState.setCollapsedSubTitle(STR."\{timeSpent}" + " mins done • Smart Workout Plan");
            resumeWorkoutAction.setUrl(STR."curefit://fitnessplanpage?tenantId=1&fullScreenMode=true&cycleDay=\{cycleDay}&wodStartTime=\{startTime.toEpochMilli()}&wodId=\{userWodEntry.getId()}");

            HashMap<String, Object> meta = new HashMap<>();
            meta.put("showCTA", true);
            meta.put("CTAText", "RESUME WORKOUT");
            List<Action> metaAction = List.of(new Action(STR."curefit://fitnessplanpage?tenantId=1&fullScreenMode=true&cycleDay=\{cycleDay}&wodStartTime=\{startTime.toEpochMilli()}&wodId=\{userWodEntry.getId()}", "RESUME", ActionType.NAVIGATION));
            meta.put("actions", metaAction);
            resumeWorkoutAction.setMeta(meta);
            collapsedBottomSheetState.setAction(resumeWorkoutAction);
        } else if (!finalUpcomingClasses.isEmpty()) {
            String collapsedTitle = finalUpcomingClasses.getFirst().getCollapsedTitle() != null ?
                    finalUpcomingClasses.getFirst().getCollapsedTitle() :
                    finalUpcomingClasses.getFirst().getTitle();
            collapsedBottomSheetState.setCollapsedTitle(collapsedTitle);

            String collapsedSubTitle = finalUpcomingClasses.get(0).getCollapsedSubTitle() != null ?
                    finalUpcomingClasses.get(0).getCollapsedSubTitle() :
                    finalUpcomingClasses.get(0).getSubTitle();
            collapsedBottomSheetState.setCollapsedSubTitle(collapsedSubTitle);
            Action action = finalUpcomingClasses.get(0).getAction();
            action.setMeta(new HashMap<>() {
                {
                    put("showCTA", false);
                }
            });
            collapsedBottomSheetState.setAction(action);
        } else if (isMembershipPaused) {
            Action action = new Action();
            if (CultUtil.isActiveCultMembership(pausedMembership)) {
                boolean isMembershipDetailV2Supported = AppUtil.isMembershipDetailV2PageSupported(userContext);
                action = CultUtil.getUnPauseCultClassAction(pausedMembership, isMembershipDetailV2Supported, userContext);
            } else if (GymUtil.isActiveProMembership(pausedMembership)) {
                action = GymUtil.getUnpauseGymMembershipAction(pausedMembership, userContext);
            } else if (PlayUtil.isActivePlayMembership(pausedMembership)) {
                action = PlayUtil.getMembershipUnpauseAction(pausedMembership, ProductType.PLAY, userContext,
                        PlayUtil.getPlayMemberships(Collections.singletonList(pausedMembership), userContext).get(0).getId());
            }
            action.setTitle("UNPAUSE");
            action.setActionType(ActionType.NAVIGATION);
            collapsedBottomSheetState.setCollapsedTitle("Membership Paused");
            // collapsedBottomSheetState.setCollapsedSubTitle("Pause ends in " + remainingPauseDays + (remainingPauseDays > 1 ? " days" : "day"));
            action.setIconUrl("/image/upcoming-notification/Membership.png");
            HashMap<String, Object> meta = (HashMap<String, Object>) action.getMeta();
            meta.put("showCTA", true);
            meta.put("CTAText", "UNPAUSE MEMBERSHIP");
            action.setMeta(meta);
            List<Action> metaAction = (List<Action>) meta.get("actions");
            String actionUrl = metaAction != null && !metaAction.isEmpty() ? metaAction.get(0).getUrl() : "";
            action.setUrl(actionUrl);
            collapsedBottomSheetState.setAction(action);
        } else if (CollectionUtils.isEmpty(memberships)) {
            Collection<Pitch> pitches = interfaces.gymfitClient.pitchService().getScheduledPitchDataForUser(userContext.getUserProfile().getUserId(), List.of(PitchStatus.SCHEDULED));
            if (Objects.nonNull(pitches) && !pitches.isEmpty()) {
                Pitch pitch = pitches.stream().findFirst().get();
                CenterEntry center = interfaces.centerService.getCachedCentersById(List.of(pitch.getCenterServiceCenterId()), false, null, null).getFirst();
                if (center != null) {
                    collapsedBottomSheetState.setCollapsedTitle(STR."Center Tour, \{center.getName()}");
                    String tz = userContext.getUserProfile().getTimezone();
                    Date lastDate = TimeUtil.addDays(Date.from(TimeUtil.getDateFromTime(pitch.getCreatedAt().getTime(), tz).atZone(ZoneId.of(tz)).toInstant()), tz, 2);
                    collapsedBottomSheetState.setCollapsedSubTitle(STR."Valid till \{TimeUtil.getFormattedDate(lastDate.toInstant().atZone(ZoneId.of(tz)))}");
                    Action action = new Action();
                    action.setMeta(new HashMap<>() {{ put("showCTA", false); }});
                    action.setIconUrl("/image/upcoming-notification/schedule_confirm.png");
                    collapsedBottomSheetState.setAction(action);
                }
            }
        }
        if (Objects.isNull(collapsedBottomSheetState.getCollapsedTitle())) {

            Action action = new Action();
            action.setTitle("BOOK");
            action.setUrl("curefit://fl_classbookingv2");
            action.setActionType(ActionType.NAVIGATION);
            if(hasActiveProMembership) {
                action.setUrl("curefit://allgyms?centerType=GYM");
                action.setActionType(ActionType.NAVIGATION);
                action.setTitle("CHECKIN");
            }
            else {
                action.setTitle("BOOK");
                action.setUrl("curefit://fl_classbookingv2");
                action.setActionType(ActionType.NAVIGATION);
            }

            UserAttributesResponse userAttributesResponse = null;
            Long workoutsThisWeek = 0L;
            Long userPledge = 3L;
            ZoneId istZone = ZoneId.of("Asia/Kolkata");
            LocalDateTime now = LocalDateTime.now(Clock.system(istZone));
            DateTimeFormatter sdfformatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String currentDate = now.format(sdfformatter);
            try {
                userAttributesResponse = interfaces.userAttributesCacheClient.getAttributes(Long.valueOf(userContext.getUserProfile().getUserId()),
                        Arrays.asList(Constants.RASHI_ATTRIBUTE_FOR_USER_PLEDGE_NEW), AppTenant.CUREFIT);
                workoutsThisWeek = getWeeklyActiveDays(userContext, interfaces);
            } catch (BaseException e) {
                throw new RuntimeException(e);
            }
            if (userAttributesResponse != null && userAttributesResponse.getAttributes() != null) {
                if (userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_PLEDGE_NEW) != null) {
                    userPledge = Long.parseLong(userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_PLEDGE_NEW).toString());
                }
            }

            /*if(response.getWasActiveInLastClass() == "first") {
                collapsedBottomSheetState.setCollapsedTitle("No class scheduled");
                collapsedBottomSheetState.setCollapsedSubTitle("");
                action.setIconUrl("/image/upcoming-notification/filled_time.png");
            }*/

            String collapsedTitle = workoutsThisWeek + "/" + Math.max(userPledge, 1);
            String collapsedSubTitle = "This Week Activity";
            collapsedBottomSheetState.setCollapsedTitle(collapsedTitle);
            collapsedBottomSheetState.setCollapsedSubTitle(collapsedSubTitle);
            action.setIconUrl("/image/upcoming-notification/Energy.png");

            action.setMeta(new HashMap<>() {
                {
                    put("showCTA", false);
                }
            });
            collapsedBottomSheetState.setAction(action);
        }

        return collapsedBottomSheetState;
    }

    public Object getLayoutData() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("type", "GRID");
        Map<String, Object> layoutData = new HashMap<>();
        layoutData.put("isExpandable", false);
        layoutData.put("aspectRatio", "162:215");
        layoutData.put("variant", "2");
        layoutData.put("edgeToEdge", false);
        layoutData.put("showPagination", false);
        layoutProps.put("data", layoutData);
        layoutProps.put("spacing", Spacing.builder().top("80").bottom("40").build());
        return layoutProps;
    }

    @Override
    public Page preparePage(String pageId, ServiceInterfaces interfaces, UserContext userContext, PageContext pageContext) throws Exception {
        return null;
    }

    @Override
    public PageView applyPageHook(PageView pageView, ServiceInterfaces interfaces, UserContext userContext, PageContext pageContext) throws Exception {

        return pageView;
    }
}