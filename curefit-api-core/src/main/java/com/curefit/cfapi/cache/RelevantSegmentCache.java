package com.curefit.cfapi.cache;

import com.curefit.offers.client.OfferService;
import com.curefit.pms.services.impl.OfflineFitnessPackService;
import com.sugarfit.challenges.client.ChallengesClient;
import com.sugarfit.poll.client.PollSupportClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.*;

@Slf4j
@Component
public class RelevantSegmentCache {

    private static final int PMS_CACHE_REFRESH_TIME  = 10 * 60 * 1000;
    private static final int OFFER_CACHE_REFRESH_TIME  = 2 * 60 * 1000;
    private static final int SUGARFIT_CACHE_REFRESH_TIME  = 2 * 60 * 1000;
    private final Map<RelevantSegmentType, Set<String>> relevantSegments = new HashMap<>();

    @Autowired
    private OfflineFitnessPackService packService;

    @Autowired
    private OfferService offerService;

    @Autowired
    private ChallengesClient challengesClient;

    @Autowired
    private PollSupportClient pollSupportClient;

    @Scheduled(fixedDelay = PMS_CACHE_REFRESH_TIME, initialDelay = 200)
    public void refreshPMSSegmentCache() {
        try {
            List<String> pmsRelevantSegments = packService.getRelevantSegments();
            log.info("PMS relevant segments: {}", pmsRelevantSegments);
            relevantSegments.put(RelevantSegmentType.PMS, new HashSet<>(pmsRelevantSegments));
        } catch (Exception e) {
            log.error("Error fetching PMS relevant segments: {}", e.getMessage());
        }
    }

    @Scheduled(fixedDelay = OFFER_CACHE_REFRESH_TIME, initialDelay = 200)
    public void refreshOfferSegmentCache() {
        try {
            Set<String> offerRelevantSegments = offerService.getRelevantSegments().getData();
            log.info("Offer relevant segments: {}", offerRelevantSegments);
            relevantSegments.put(RelevantSegmentType.OFFER_SERVICE, offerRelevantSegments);
        } catch (Exception e) {
            log.error("Error fetching offer relevant segments: {}", e.getMessage());
        }

    }

    @Scheduled(fixedDelay = SUGARFIT_CACHE_REFRESH_TIME, initialDelay = 200)
    public void refreshSugarfitChallengesSegmentCache() {
        try {
            Set<String> challengesClientRelevantSegments = new HashSet<>(challengesClient.getRelevantSegments());
            log.info("Sugarfit Challenges relevant segments: {}", challengesClientRelevantSegments);
            relevantSegments.put(RelevantSegmentType.SF_CHALLENGES_CLIENT, challengesClientRelevantSegments);
        } catch (Exception e) {
            log.error("Error fetching Sugarfit Challenges relevant segments: {}", e.getMessage());
        }

    }

    @Scheduled(fixedDelay = SUGARFIT_CACHE_REFRESH_TIME, initialDelay = 200)
    public void refreshSugarfitPollsSegmentCache() {
        try {
            Set<String> pollSupportClientRelevantSegments = new HashSet<>(pollSupportClient.getRelevantSegments());
            log.info("Sugarfit Polls relevant segments: {}", pollSupportClientRelevantSegments);
            relevantSegments.put(RelevantSegmentType.SF_POLL_CLIENT, pollSupportClientRelevantSegments);
        } catch (Exception e) {
            log.error("Error fetching Sugarfit Polls relevant segments: {}", e.getMessage());
        }

    }

    public Set<String> getRelevantSegments(RelevantSegmentType key) {
        if (!relevantSegments.containsKey(key)) {
            refreshCache(key);
        }
        return this.relevantSegments.get(key);
    }

    private void refreshCache(RelevantSegmentType key) {
        switch (key) {
            case PMS -> refreshPMSSegmentCache();
            case OFFER_SERVICE -> refreshOfferSegmentCache();
            case SF_POLL_CLIENT -> refreshSugarfitPollsSegmentCache();
            case SF_CHALLENGES_CLIENT -> refreshSugarfitChallengesSegmentCache();
        }
    }

    public enum RelevantSegmentType {
       OFFER_SERVICE,
       PMS,
        SF_POLL_CLIENT,
        SF_CHALLENGES_CLIENT,
    }
}
