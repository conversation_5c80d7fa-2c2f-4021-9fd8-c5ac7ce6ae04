package com.curefit.cfapi.cache;


import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.store.KeyValueStore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SegmentCache {
    private static final String SEGMENT_KEY_PREFIX = "vm:segment:";
    private final KeyValueStore redisKeyValueStore;
    EnvironmentService environmentService;
    ExceptionReportingService exceptionReportingService;
    private final String cachePreloadKey = "cfapi:segmentCachedIds";
    private final LoadingCache<String, String> segmentLRUCache;
    private final ObjectReader segmentObjectReader;

    @Autowired
    public SegmentCache(
            @Qualifier("defaultRedisKeyValueStore") KeyValueStore defaultRedisKeyValueStore,
            @Qualifier("cfApiRedisKeyValueStore") KeyValueStore cfApiRedisKeyValueStore,
            EnvironmentService environmentService,
            ExceptionReportingService exceptionReportingService
    ) throws Exception {
        this.environmentService = environmentService;
        this.exceptionReportingService = exceptionReportingService;

        if (environmentService.isProduction()) {
            this.redisKeyValueStore = cfApiRedisKeyValueStore;
        } else {
            this.redisKeyValueStore = defaultRedisKeyValueStore;
        }

        this.segmentLRUCache = CacheBuilder.newBuilder()
                .maximumSize(2000)
                .concurrencyLevel(32)
                .expireAfterWrite(600, TimeUnit.SECONDS)
                .build(new CacheLoader<>() {
                    @Override
                    public String load(String cacheKey) throws Exception {
                        return redisKeyValueStore.get(cacheKey, SEGMENT_KEY_PREFIX);
                    }
                });

        this.segmentObjectReader = new ObjectMapper()
                .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setSerializationInclusion(JsonInclude.Include.NON_ABSENT)
                .readerFor(Segment.class);
    }


    public CompletableFuture<Segment> getSegment(String id, boolean bypassCache) {
        Segment ret = null;
        try {
            var segmentJsonStr = this.getStringifiedSegment(id, bypassCache);
            if (segmentJsonStr == null) {
                return null;
            }
            ret = this.segmentObjectReader.readValue(segmentJsonStr);
            if (ret == null || ret.getArchived()) {
                return null;
            }
        } catch (Exception e) {
            log.error("invalid segmentId: " + id, e);
            if (environmentService.isProduction()) {
                BaseException baseException = new BaseException(e);
                baseException.setContext(new HashMap<>() {{ put("segmentId", id); }});
                this.exceptionReportingService.reportWarning(baseException);
            }
        }
        return CompletableFuture.completedFuture(ret);
    }

    private String getStringifiedSegment(String id, boolean bypassCache) throws ExecutionException {
        if (bypassCache) {
            return this.redisKeyValueStore.get(id, SEGMENT_KEY_PREFIX);
        } else {
            return this.segmentLRUCache.get(id);
        }
    }

    @PreDestroy
    public void preDestroy() {
        try {
            Long cacheSize = this.redisKeyValueStore.ssize(this.cachePreloadKey);
            Set<String> segmentIdsInCache = new HashSet<>(this.segmentLRUCache.asMap().keySet());
            if (cacheSize == null || segmentIdsInCache.isEmpty()) {
                return;
            }
            if (cacheSize > segmentIdsInCache.size()) {
                this.redisKeyValueStore.expire(this.cachePreloadKey, 50000);
                return;
            }
            this.redisKeyValueStore.sadd(this.cachePreloadKey, segmentIdsInCache);
            this.redisKeyValueStore.expire(this.cachePreloadKey, 50000);
        } catch (Exception ex) {
            this.exceptionReportingService.reportWarning(ex);
        }
    }

    @PostConstruct
    public void postConstruct() {
        CompletableFuture.runAsync(this::preloadCache);
    }

    public void preloadCache() {
        try {
            Long cacheSize = this.redisKeyValueStore.ssize(this.cachePreloadKey);
            if (cacheSize == null || cacheSize == 0) {
                return;
            }
            Set<String> segmentIds = this.redisKeyValueStore.smembers(this.cachePreloadKey);
            if (segmentIds == null || segmentIds.isEmpty()) {
                return;
            }
            this.segmentLRUCache.getAll(segmentIds);
        } catch (Exception ex) {
            this.exceptionReportingService.reportWarning(ex);
        }
    }
}
