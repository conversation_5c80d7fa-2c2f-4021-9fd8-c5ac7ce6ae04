package com.curefit.cfapi.service;

import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.user.QuickActionScore;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.common.data.exception.BaseException;
import com.curefit.product.enums.ProductType;
import com.curefit.rashi.client.UserAttributesCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import response.QuickActionScoreResponse;

import java.util.*;
import java.util.concurrent.ExecutionException;

import static com.curefit.cfapi.util.UserAttributeUtil.PRODUCT_TYPE_TO_USAGE_FREQUENCY_KEY;

@Service
@Slf4j
public class QuickActionService {

    public static final List<String> CULT_RELAUNCHED_CITY_LIST = new ArrayList<String>(){
        {
            add("Indore");
            add("Chandigarh");
            add("Gurgaon");
            add("Mumbai");
            add("Pune");
            add("Surat");
            add("Bangalore");
            add("Hyderabad");
        }
    };

    private UserAttributesCacheClient userAttributesCacheClient;
    private EnvironmentService environmentService;

    Map<ProductType, Double> productTypeToBusinessScore = new HashMap<ProductType, Double>();
    Map<ProductType, Double> productTypeToBusinessScoreCultShutdownPref = new HashMap<ProductType, Double>();
    Map<ProductType, String> productTypeToActivePackSegmentKey = new HashMap<ProductType, String>();

    @Autowired
    public QuickActionService(UserAttributesCacheClient userAttributesCacheClient,
                              EnvironmentService environmentService) {
        this.userAttributesCacheClient = userAttributesCacheClient;
        this.environmentService = environmentService;

        productTypeToBusinessScore.put(ProductType.CULT_ROW, 1.000);
        productTypeToBusinessScore.put(ProductType.FITNESS, 1.000);
        productTypeToBusinessScore.put(ProductType.GYMFIT_FITNESS_PRODUCT, 0.931);
        productTypeToBusinessScore.put(ProductType.GYM_PT_PRODUCT, 0.92);
        productTypeToBusinessScore.put(ProductType.PLAY, 0.9);
        productTypeToBusinessScore.put(ProductType.LIFT, 0.895);
        productTypeToBusinessScore.put(ProductType.CULT_BOOTCAMP, 0.89);
        productTypeToBusinessScore.put(ProductType.COACH, 0.862);
        productTypeToBusinessScore.put(ProductType.LIVE, 0.79);
        productTypeToBusinessScore.put(ProductType.CULTSPORT_HOME_GYM, 0.72);
        productTypeToBusinessScore.put(ProductType.GEAR, 0.68);
        productTypeToBusinessScore.put(ProductType.CULTSPORT_CYCLE, 0.61);
        productTypeToBusinessScore.put(ProductType.ULTRAFIT, 0.54);
        productTypeToBusinessScore.put(ProductType.NUTRITIONIST_CONSULTATION, 0.48);
        productTypeToBusinessScore.put(ProductType.THERAPY, 0.45);
        productTypeToBusinessScore.put(ProductType.BUNDLE, 0.4); // sugar.fit
        productTypeToBusinessScore.put(ProductType.DIAGNOSTICS, 0.3);
        productTypeToBusinessScore.put(ProductType.LIVE_PERSONAL_TRAINING, 0.22);
        productTypeToBusinessScore.put(ProductType.DIY_MEDITATION, 0.169);
        productTypeToBusinessScore.put(ProductType.RECIPE, 0.100);
        productTypeToBusinessScore.put(ProductType.CONSULTATION, 0.09);
        productTypeToBusinessScore.put(ProductType.LIVE_SGT, 0.09);
        productTypeToBusinessScore.put(ProductType.WHOLE_FIT, 0.09);
        productTypeToBusinessScore.put(ProductType.FOOD_MARKETPLACE, 0.09);
        productTypeToBusinessScore.put(ProductType.FOOD, 0.09);
        productTypeToBusinessScore.put(ProductType.SKIN_HAIR, 0.09);
        productTypeToBusinessScore.put(ProductType.COSMETICS, 0.09);

        productTypeToBusinessScoreCultShutdownPref.put(ProductType.CULT_ROW, 1.000);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.LIVE, 1.000);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.LIFT, 1.000);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.CULT_BOOTCAMP, 1.000);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.COACH, 0.95);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.FOOD, 0.936);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.FOOD_MARKETPLACE, 0.936);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.CONSULTATION, 0.871);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.GEAR, 0.807);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.LIVE_PERSONAL_TRAINING, 0.743);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.FITNESS, 0.679);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.GYMFIT_FITNESS_PRODUCT, 0.614);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.LIVE_SGT, 0.550);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.NUTRITIONIST_CONSULTATION, 0.486);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.THERAPY, 0.421);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.DIAGNOSTICS, 0.357);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.BUNDLE, 0.357); // sugar.fit
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.WHOLE_FIT, 0.293);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.DIY_MEDITATION, 0.229);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.RECIPE, 0.164);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.SKIN_HAIR, 0.100);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.COSMETICS, 0.100);
        productTypeToBusinessScoreCultShutdownPref.put(ProductType.PLAY, 0.100);

        productTypeToActivePackSegmentKey.put(ProductType.LIVE_SGT, "ALL_CULT_MEMBERS_SGT");
        productTypeToActivePackSegmentKey.put(ProductType.LIVE_PERSONAL_TRAINING, "livept_members");
        productTypeToActivePackSegmentKey.put(ProductType.LIVE, "cultlive_members");
        productTypeToActivePackSegmentKey.put(ProductType.COACH, "transform_active_users");
        productTypeToActivePackSegmentKey.put(ProductType.LIFT, "cult_lift_active_users");
        productTypeToActivePackSegmentKey.put(ProductType.CULT_BOOTCAMP, "transform_bootcamp_users");
        productTypeToActivePackSegmentKey.put(ProductType.DIY_MEDITATION, "cultlive_members");
        productTypeToActivePackSegmentKey.put(ProductType.FITNESS, "cult_members");
        productTypeToActivePackSegmentKey.put(ProductType.GEAR, "cultpass Members (All members who have an active membership+");
        productTypeToActivePackSegmentKey.put(ProductType.FOOD, "eat_subscription_users");
        productTypeToActivePackSegmentKey.put(ProductType.NUTRITIONIST_CONSULTATION, "nutrition_pack_users");
        productTypeToActivePackSegmentKey.put(ProductType.THERAPY, "Therapy active users");
        productTypeToActivePackSegmentKey.put(ProductType.CONSULTATION, "health_pack_users");
        productTypeToActivePackSegmentKey.put(ProductType.GYMFIT_FITNESS_PRODUCT, "Cult Pass Combined Segment");
        productTypeToActivePackSegmentKey.put(ProductType.PLAY, "Play benefit active users");
        productTypeToActivePackSegmentKey.put(ProductType.GYM_PT_PRODUCT, "Gym PT Quick Action Segment");
    }

    public QuickActionScoreResponse computeScore(UserContext userContext, String userId) throws BaseException, ExecutionException, InterruptedException {
        SegmentSet<String> userPlatformSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                RequestType.PLATFORM_SEGMENTS,
                userContext).get();
        var attributesToFetch = new ArrayList(PRODUCT_TYPE_TO_USAGE_FREQUENCY_KEY.values());
        attributesToFetch.addAll(productTypeToActivePackSegmentKey.values());
        var userAttributesResponse = this.userAttributesCacheClient.getAttributes(
                Long.valueOf(userId),
                attributesToFetch, AppUtil.getAppTenantFromUserContext(userContext));
        var attributes = userAttributesResponse.getAttributes();
        Double totalUsageFrequency = 0.0;
        Double maxUsageFrequency = 0.0;
        Double maxConversionPercentage = 0.0;
        var productTypeToScoreMap = productTypeToBusinessScore;
        var productTypes = productTypeToScoreMap.keySet();
        for (ProductType productType : productTypes) {
            var score = attributes.get(PRODUCT_TYPE_TO_USAGE_FREQUENCY_KEY.get(productType));
            if (score != null) {
                var scoreValue = Math.max(Double.valueOf((String) attributes.get(PRODUCT_TYPE_TO_USAGE_FREQUENCY_KEY.get(productType))), 0.0D);
                totalUsageFrequency = totalUsageFrequency + scoreValue;
                maxUsageFrequency = scoreValue > maxUsageFrequency ? scoreValue : maxUsageFrequency;
            }
        }
        if (totalUsageFrequency > 0.0) {
            maxConversionPercentage = maxUsageFrequency / totalUsageFrequency;
        }

        Map<ProductType, QuickActionScore> scoreMap = new HashMap<ProductType, QuickActionScore>();
        for (ProductType productType : productTypes
        ) {
            var quickActionScore = new QuickActionScore();
            quickActionScore.setBusinessScore(productTypeToScoreMap.get(productType));
            if (productTypeToActivePackSegmentKey.containsKey(productType) &&
                    userPlatformSegments.contains(productTypeToActivePackSegmentKey.get(productType))) {
                quickActionScore.setIsPackUser(true);
            } else {
                quickActionScore.setIsPackUser(false);
            }
            var score = attributes.get(PRODUCT_TYPE_TO_USAGE_FREQUENCY_KEY.get(productType));
            if (score != null) {
                var scoreValue = Math.max(Double.valueOf((String) score), 0.0D);
                var conversionPercentage = totalUsageFrequency  > 0 ? scoreValue / totalUsageFrequency : 0;
                var conversionScore = maxConversionPercentage > 0 ? conversionPercentage / maxConversionPercentage : 0;
                quickActionScore.setConversionScore(conversionScore);
            } else {
                quickActionScore.setConversionScore(0.0);
            }

            quickActionScore.setFinalScore(0.60 * quickActionScore.getConversionScore()
                    + 0.25 * quickActionScore.getBusinessScore()
                    + 0.15 * (quickActionScore.getIsPackUser() ? 1 : 0));

            scoreMap.put(productType, quickActionScore);
        }
        var quickActionScoreResponse = new QuickActionScoreResponse();
        quickActionScoreResponse.setScoreMap(scoreMap);
        return quickActionScoreResponse;
    }
}
