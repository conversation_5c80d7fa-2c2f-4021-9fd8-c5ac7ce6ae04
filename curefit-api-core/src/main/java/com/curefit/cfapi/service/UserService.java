package com.curefit.cfapi.service;

import com.amazonaws.services.sns.model.PublishResult;
import com.amazonaws.util.CollectionUtils;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.enums.Tenant;
import com.curefit.base.enums.UserAgent;
import com.curefit.base.enums.VerticalType;
import com.curefit.base.service.EnvironmentService;
import com.curefit.catalogv1.pms.ICatalogueServicePMS;
import com.curefit.cfapi.builder.vm.RequestCache;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.FeatureStateCache;
import com.curefit.cfapi.cache.SegmentTagCache;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.Analytics.UserCitySetReasonAnalyticsEvent;
import com.curefit.cfapi.model.internal.cult.LocationPreferenceType;
import com.curefit.cfapi.model.internal.eat.DeliverySubArea;
import com.curefit.cfapi.model.internal.userinfo.*;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.model.mongo.*;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.base.LatLong;
import com.curefit.cfapi.pojo.base.VideoStatus;
import com.curefit.cfapi.pojo.device.DeviceInfo;
import com.curefit.cfapi.pojo.location.UserDeliveryAddress;
import com.curefit.cfapi.pojo.status.*;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGame;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGameConfig;
import com.curefit.cfapi.repository.CharacterRevealPageConfigData;
import com.curefit.cfapi.repository.EnergyStreakGamesData;
import com.curefit.cfapi.repository.TVVideoStatusRepository;
import com.curefit.cfapi.repository.UserDeliveryAddressesRepository;
import com.curefit.cfapi.service.CFAnalytics.CFAnalytics;
import com.curefit.cfapi.service.Games.MultiPlayerGameService;
import com.curefit.cfapi.service.slack.SlackAuthService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewbuilders.ActivePackViewBuilderV1;
import com.curefit.cfapi.view.viewmodels.fitness.EnergyActivityStreak;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.community.CenterLevelChallengeWidget;
import com.curefit.cfapi.widgets.community.pojo.HabitBuildingData;
import com.curefit.cfapi.widgets.dailystreak.DSHomePageWidget;
import com.curefit.cfapi.widgets.dailystreak.DSSummaryWidget;
import com.curefit.cfapi.widgets.fitness.HabitTickerWidget;
import com.curefit.cfapi.widgets.fitness.UserWeeklyActivityResponse;
import com.curefit.cfapi.widgets.hometab.SplashScreenData;
import com.curefit.cfapi.widgets.transform.FitnessDeviceModalData;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.cult.models.CultSummary;
import com.curefit.cult.models.CultUserPreference;
import com.curefit.cult.models.MembershipDetailsGrouped;
import com.curefit.cult.models.PreferenceSetting;
import com.curefit.cult.services.spi.CultService;
import com.curefit.diyfs.client.spi.DiyfsService;
import com.curefit.diyfs.pojo.DIYPackFulfilment;
import com.curefit.diyfs.pojo.LiveClass;
import com.curefit.diyfs.pojo.enums.EngagementFeatureType;
import com.curefit.hamlet.models.pojo.UserAssignment;
import com.curefit.hamlet.models.response.UserAllocation;
import com.curefit.iris.models.SendCampaignNotificationsRequest;
import com.curefit.iris.models.SendCampaignNotificationsResponse;
import com.curefit.iris.models.SendCampaignNotificationsResponseStatus;
import com.curefit.iris.services.spi.CampaignService;
import com.curefit.location.models.City;
import com.curefit.location.models.DetectedCityResponseByIp;
import com.curefit.location.service.CityCache;
import com.curefit.math.enums.DataType;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.pms.pojo.customPacks.OfflineFitnessPack;
import com.curefit.product.models.diy.DIYProduct;
import com.curefit.product.models.eat.ListingBrandIdType;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.client.UserAttributesCacheClient;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.client.UserEventClient;
import com.curefit.rashi.enums.UserEventType;
import com.curefit.rashi.pojo.EventEntry;
import com.curefit.rashi.pojo.UserAttributeEntry;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.rashi.pojo.UserEventEntry;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.socialservice.client.impl.NotificationCenterServiceClientImpl;
import com.curefit.socialservice.enums.NotificationCenterEntryType;
import com.curefit.uas.responses.StreakResponse;
import com.curefit.userservice.client.UserServiceClient;
import com.curefit.userservice.exception.UserDeviceTokenException;
import com.curefit.userservice.pojo.entry.DeviceDetailEntry;
import com.curefit.userservice.pojo.entry.UserDeviceTokenEntry;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.curefit.userservice.pojo.response.UsersResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Sets;
import lombok.AccessLevel;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.DateTime;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.curefit.cfapi.service.Games.MultiPlayerGameService.getGameCurrentLevel;
import static com.curefit.cfapi.widgets.fitness.HabitGameProgressWidget.getIfGameIsCompleted;
import static com.curefit.cfapi.widgets.fitness.HabitGameProgressWidget.getIsSlipping;
import static com.curefit.cfapi.widgets.fitness.UserWeeklyActivity.fetchDaysSinceLastSunday;
import static com.curefit.cfapi.widgets.transform.FitnessDeviceModalData.getListOfMetrics;

@Slf4j
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserService {

	private static final String PUSH_NOTIFICATION_CURRENTLY_PLAYING_ON_TV = "PUSH_NOTIFICATION_CURRENTLY_PLAYING_ON_TV";
	private static final String LIVE_CURRENTLY_PLAYING_ON_TV = "LIVE_CURRENTLY_PLAYING_ON_TV";
	private static final int RETRY_LIMIT_MILLISECONDS = 91000;
	private static final int  CACHE_REFRESH_TIME = 29 * 60 * 1000;
	private static final String USER_PENDING_INVITES_SET_PREFIX = "pendingInvitesSet_";
	private static final String GYM_LOGGING_TRIGGER_PREFIX = "GYM_LOGGING_TRIGGER_";
	EnergyStreakGamePageConfig gameConfig;

	CityCache cityCache;
	HttpServletRequest request;
	HttpServletResponse response;

	UserServiceClient userServiceClient;
	NotificationCenterServiceClientImpl notificationCenterServiceClient;

	DiyfsService diyfsService;

	ActivePackViewBuilderV1 activePackViewBuilderV1;

	ApiKeyService apiKeyService;

	DeviceService deviceService;

	SessionService sessionService;

	NavBarService navBarService;

	UserAgentService userAgentService;

	EnvironmentService envBusiness;

	TVVideoStatusRepository tvRepository;

	CampaignService campaignService;

	ICatalogueServicePMS catalogueServicePMS;

	SlackAuthService slackAuthService;

	SegmentEvaluatorService segmentEvaluatorService;

    EnvironmentService environmentService;

	RashiClient rashiClient;

    SegmentTagCache segmentTagCache;

    UserDeliveryAddressesRepository userDeliveryAddressesRepository;

    UserEventClient userEventClient;

	UserAttributesCacheClient userAttributesCacheClient;

	FeatureStateCache featureStateCache;

	UserAttributesClient userAttributesClient;

	EnergyStreakGamesData energyStreakGamesData;
	CFAnalytics cfAnalytics;

	CharacterRevealPageConfigData pageConfigRepository;

	KeyValueStore socialServiceRedisKeyValueStore;
	KeyValueStore defaultRedisKeyValueStore;
	ExceptionReportingService exceptionReportingService;
	RollbarService rollbarService;
	MultiPlayerGameService multiPlayerGameService;
	ServiceInterfaces serviceInterfaces;

	@Autowired
	public UserService(ExceptionReportingService exceptionReportingService, ApiKeyService apiKeyService,
			DeviceService deviceService, SessionService sessionService,
			NavBarService navBarService, UserAgentService userAgentService, HttpServletRequest request,
			HttpServletResponse response, CityCache cityCache, UserServiceClient userServiceClient,
					   NotificationCenterServiceClientImpl notificationCenterServiceClient,
			DiyfsService diyfsService, ActivePackViewBuilderV1 activePackViewBuilderV1, EnvironmentService envBusiness,
			TVVideoStatusRepository tvRepository, CampaignService campaignService, ICatalogueServicePMS catalogueServicePMS,
			SlackAuthService slackAuthService, SegmentEvaluatorService segmentEvaluatorService,
			EnvironmentService environmentService,
			RashiClient rashiClient, SegmentTagCache segmentTagCache,
			UserEventClient userEventClient, UserAttributesCacheClient userAttributesCacheClient,
			FeatureStateCache featureStateCache, UserDeliveryAddressesRepository userDeliveryAddressesRepository,
			UserAttributesClient userAttributesClient, EnergyStreakGamesData energyStreakGamesData,
			CFAnalytics cfAnalytics, CharacterRevealPageConfigData pageConfigRepository, MultiPlayerGameService multiPlayerGameService,
			@Qualifier("socialServiceRedisKeyValueStore") KeyValueStore socialServiceRedisKeyValueStore,
		    @Qualifier("defaultRedisKeyValueStore") KeyValueStore defaultRedisKeyValueStore) {
		this.apiKeyService = apiKeyService;
		this.exceptionReportingService = exceptionReportingService;
		this.deviceService = deviceService;
		this.sessionService = sessionService;
		this.navBarService = navBarService;
		this.userAgentService = userAgentService;
		this.request = request;
		this.response = response;
		this.cityCache = cityCache;
		this.userServiceClient = userServiceClient;
		this.notificationCenterServiceClient = notificationCenterServiceClient;
		this.diyfsService = diyfsService;
		this.activePackViewBuilderV1 = activePackViewBuilderV1;
		this.envBusiness = envBusiness;
		this.tvRepository = tvRepository;
		this.campaignService = campaignService;
		this.catalogueServicePMS = catalogueServicePMS;
		this.slackAuthService = slackAuthService;
		this.segmentEvaluatorService = segmentEvaluatorService;
		this.rashiClient = rashiClient;
        this.environmentService = environmentService;
        this.segmentTagCache = segmentTagCache;
        this.userEventClient = userEventClient;
        this.userAttributesCacheClient = userAttributesCacheClient;
        this.featureStateCache = featureStateCache;
		this.userDeliveryAddressesRepository = userDeliveryAddressesRepository;
		this.userAttributesClient = userAttributesClient;
		this.energyStreakGamesData = energyStreakGamesData;
		this.cfAnalytics = cfAnalytics;
		this.pageConfigRepository = pageConfigRepository;
		this.multiPlayerGameService = multiPlayerGameService;
		this.socialServiceRedisKeyValueStore = socialServiceRedisKeyValueStore;
		this.defaultRedisKeyValueStore = defaultRedisKeyValueStore;
	}

	public LoginResponseAndSession registerDeviceAndCreateSession(UserEntry user, String deviceId,
			String apikeyQueryParam, String appVersion, UserStatusRequestBody requestBody, Boolean isNotLoggedInFlow, DeviceInfo userDeviceInfo)
			throws ExecutionException, InterruptedException, IOException, BaseException {
		UserAgent userAgent = AuthUtil.getUserAgent(request, userAgentService, apiKeyService);
		RequestBodyDeviceInfo requestDeviceInfo = requestBody != null ? requestBody.getRequestBodyDeviceInfo() : null;
		AppTenant tenant = AppUtil.getTenantFromReq(request, apiKeyService);

		if ((requestDeviceInfo == null && userDeviceInfo == null)) {
			if ((userAgent == UserAgent.DESKTOP || userAgent == UserAgent.MBROWSER)) {
				requestDeviceInfo = new RequestBodyDeviceInfo();
				requestDeviceInfo.setAppId("web");
				requestDeviceInfo.setBrand("browser");
				requestDeviceInfo.setModel("browser");
				requestDeviceInfo.setOsName("browser");
				requestDeviceInfo.setOsVersion("5.1");
				requestDeviceInfo.setAppVersion(8.0f);
			} else {
				throw new Error("Invalid request params.");
			}
		}

		UserCity userCity = LocationUtil.getUserCityFromRequest(request, request.getHeader("cityid"), Tenant.fromString(tenant.toString()), cityCache, this.userAttributesClient);
		requestDeviceInfo.setCityId(userCity.getCity().getCityId());
		City city = userCity.getCity();

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setUserId(user.getId().toString());
		deviceInfo.setRegisterDate(new Date());
		deviceInfo.setAppVersion(Float.parseFloat(appVersion));
		deviceInfo.setTenant(Tenant.fromString(tenant.toString()));

		if (requestDeviceInfo != null) {
			deviceInfo.setAppId(requestDeviceInfo.getAppId());
			deviceInfo.setBrand(requestDeviceInfo.getBrand());
			deviceInfo.setDeviceModel(requestDeviceInfo.getModel());
			deviceInfo.setOsName(requestDeviceInfo.getOsName());
			deviceInfo.setOsVersion(requestDeviceInfo.getOsVersion());
			deviceInfo.setPushNotificationToken(requestDeviceInfo.getPushNotificationToken());
		}

		if (userDeviceInfo != null) {
			deviceInfo.setAppId(userDeviceInfo.getAppId());
			deviceInfo.setBrand(userDeviceInfo.getBrand());
			deviceInfo.setDeviceModel(userDeviceInfo.getDeviceModel());
			deviceInfo.setOsName(userDeviceInfo.getOsName());
			deviceInfo.setOsVersion(userDeviceInfo.getOsVersion());
			deviceInfo.setPushNotificationToken(userDeviceInfo.getPushNotificationToken());
		}

		DeviceDetailEntry devicePlatform = this.userServiceClient.getDeviceByIdAndTenant(deviceId, tenant.toString());
		if (devicePlatform != null) {
			this.deviceService.updateDeviceAndDeviceInfoPlatform(devicePlatform, deviceInfo);
		}

		SessionData sessionData = this.getSessionDataFromDevice(deviceInfo, requestDeviceInfo != null ? requestDeviceInfo.getCityId() : null);
//		log.info("cityId " + requestDeviceInfo!=null ? requestDeviceInfo.getCityId() : null);
		if (requestDeviceInfo !=null && requestDeviceInfo.getCityId() != null) {
			sessionData.setCityId(requestDeviceInfo.getCityId());
			sessionData.setIsCityManuallySelected(userCity.getIsCityManuallySelected());
			sessionData.setCitySelectedTimeInMillis(new Date().getTime());
		}
		sessionData.setTlaSemusnoc(UUID.randomUUID().toString());
		if (requestDeviceInfo != null && Objects.equals(requestDeviceInfo.getAttributionSource(), "FITTERNITY")) {
			sessionData.setAttributionSource("FITTERNITY");
		}
		Session session = this.sessionService
				.createSession(deviceId, deviceInfo.getUserId(), isNotLoggedInFlow, sessionData).get();
		session.setUserAgent(userAgent);
		AuthUtil.setCookies(exceptionReportingService, userAgentService, apiKeyService, session.getSt(),
				session.getAt(), deviceId,
				apikeyQueryParam, request, response);
		request.setAttribute("session", session);
		UserContext userContext = this.getUserContextFromReq(request).get();
		Float lat = NumberUtils.isParsable(request.getHeader("lat")) ? Float.parseFloat(request.getHeader("lat"))
				: null;
		Float lon = NumberUtils.isParsable(request.getHeader("lon")) ? Float.parseFloat(request.getHeader("lon"))
				: null;
		this.cfAnalytics.sendEvent(
			UserCitySetReasonAnalyticsEvent.builder()
				.cityId(userCity.getCity().getCityId())
				.cityName(userCity.getCity().getName())
				.isCitySelectedManually(userCity.getIsCityManuallySelected())
				.reason(userCity.getCitySelectionReason())
				.userIp(AppUtil.getClientIpAddr(request))
				.latitude(lat)
				.longitude(lon)
				.from("userService.registerDeviceAndCreateSession")
				.isReasonSession(Objects.equals(userCity.getCitySelectionReason(), "SESSION"))
				.build(),
			userContext, false, true, false, false
		);
		request.setAttribute("userContext", userContext);
		List<VerticalType> supportedVerticals = this.navBarService
				.getSupportedVerticalsForNavBar((UserContext) request.getAttribute("userContext"), city).get();

		if (!AuthUtil.isFixedGuestUser(userContext)) { // For guest users, skip adding device entries
			if (devicePlatform != null) {
				devicePlatform.setAt(session.getAt());
				devicePlatform.setWebClientId(requestBody.getWebClientId());
				devicePlatform.setAppInstanceId(requestBody.getFirebaseInstanceId());
				this.userServiceClient.upsertDevice(devicePlatform);
			} else {
				this.deviceService.createNewDevicePlatform(deviceId, deviceInfo, session.getAt(),);
			}
		}

		if (!(userAgent == UserAgent.DESKTOP || userAgent == UserAgent.MBROWSER)) {
			log.info("supported verticals " + supportedVerticals);
			supportedVerticals = !CollectionUtils.isNullOrEmpty(supportedVerticals) && supportedVerticals.size() > 4
					? supportedVerticals.subList(0, 4)
					: supportedVerticals;// appTabs can only handle 5 tabs for mobile
		}
		CompletableFuture<UserAllocation> userExperiments = CompletableFuture.completedFuture(null);
		VerticalsWithHighlightInformation verticalInfo = this.navBarService
				.getUserAndSupportedVerticalsInfo((UserContext) request.getAttribute("userContext"), city,
						user.getId().toString(), userExperiments, segmentEvaluatorService)
				.get();
		LoginResponse loginResponse = new LoginResponse();
		loginResponse.user = new UserView(user, session.getIsNotLoggedIn(), null, null, null, null);
		loginResponse.session = new SessionView(session, null);
		loginResponse.setCityName(city.getName());
		loginResponse.setCityId(city.getCityId());

		loginResponse.redirectUri = AuthUtil.getRedirectUrlIfNeeded(userContext, slackAuthService, requestBody, request, session.getAt(),
				session.getIsNotLoggedIn());

		List<TabType> appTabs = new ArrayList<>();
		appTabs.add(TabType.PLAN);
		appTabs.addAll(supportedVerticals.stream().map(verticalType -> TabType.valueOf(verticalType.toString()))
				.collect(Collectors.toList()));
		loginResponse.setAppTabs(appTabs); // appTabs can only handle 5 tabs

		loginResponse.setVerticals(verticalInfo.getVerticals());
		loginResponse.setIsMoreToBeHighlighted(verticalInfo.getIsMoreToBeHighlighted());
		LoginResponseAndSession ret = new LoginResponseAndSession();
		ret.setSession(session);
		ret.setLoginResponse(loginResponse);
		return ret;
	}

	public SessionData getSessionDataFromDevice(DeviceInfo deviceInfo, String cityId) {
		SessionData sessionData = new SessionData();
		if (deviceInfo.getGateId() != null) {
			sessionData.setGateId(deviceInfo.getGateId());
			sessionData.setLocationName(deviceInfo.getLocationName());
			sessionData.setLocationAddress(deviceInfo.getLocationAddress());
		}
		if (deviceInfo.getCultCenterId() != null) {
			sessionData.setCultCenterId(deviceInfo.getCultCenterId());
			sessionData.setMindCenterId(deviceInfo.getMindCenterId());
		}
		// Temp hack to avoid crash after closing cult hsr 27th main
		if (Objects.equals(sessionData.getCultCenterId(), "8")) {
			sessionData.setCultCenterId("3");
		}

		if (cityId != null) {
			sessionData.setCityId(cityId);
		}

		return sessionData;
	}

	public Session updateUserLocation(UserLocationUpdateRequest userLocationUpdateRequest, Session session, SessionBusiness sessionBusiness) throws IOException, ExecutionException, InterruptedException {

		SessionData sessionData = session.getSessionData();
		if (sessionData.getGearLocationPreference() == null) {
			sessionData.setGearLocationPreference(new LocationPreferenceData());
		}

		if (StringUtils.isNotBlank(userLocationUpdateRequest.getPincode())) {
			sessionData.getGearLocationPreference().setPincode(userLocationUpdateRequest.getPincode());
		} else if (StringUtils.isNotBlank(userLocationUpdateRequest.getAddressId())) {
			com.curefit.cfapi.model.mongo.UserDeliveryAddress userDeliveryAddress = userDeliveryAddressesRepository.findUserDeliveryAddressByAddressId(userLocationUpdateRequest.getAddressId());
			if (userDeliveryAddress == null) return session;

			LocationPreferenceData updatedGearLocationPreference = new LocationPreferenceData();
			updatedGearLocationPreference.setAddressId(userDeliveryAddress.getAddressId());
			if (StringUtils.isNotBlank(userDeliveryAddress.getAddressType())) {
				updatedGearLocationPreference.setPlaceName(userDeliveryAddress.getAddressType());
			}
			updatedGearLocationPreference.setPlaceAddress(userDeliveryAddress.getAddressLine1() != null ? (userDeliveryAddress.getAddressLine1() + ", " + (userDeliveryAddress.getAddressLine2() != null ? userDeliveryAddress.getAddressLine2() : "")) : "");

			if (userDeliveryAddress.getStructuredAddress() != null && StringUtils.isNotBlank(userDeliveryAddress.getStructuredAddress().getPincode())) {
				updatedGearLocationPreference.setPincode(userDeliveryAddress.getStructuredAddress().getPincode());
			}
			sessionData.setGearLocationPreference(updatedGearLocationPreference);
		}
		return sessionBusiness.updateSessionData(session.getAt(), sessionData, session.getIsNotLoggedIn(), false).get();
	}

	public Session createDeviceLoginIfNoSession(UserStatusRequestBody requestBody, String deviceIdCookie,
			String apikeyQueryParam) throws BaseException, ExecutionException, InterruptedException, IOException {
		// This is not needed post we migrate to Janus
		// This is needed to handle the case for the first time web load on a browser
		// where cookie won;t be present and web
		// won't be passing st / at as well
		// session object => this.request.getAttribute("session")

		Session session = (Session) this.request.getAttribute("session");
		if (session.getUserId() != null) {
			return session;
		}
		// app sends deviceId in headers and cookies both, but correct value is only in
		// headers. So consider deviceId from headers above cookies
		String deviceId = request.getHeader("deviceid") != null ? request.getHeader("deviceid") : deviceIdCookie;
		deviceId = deviceId != null ? deviceId : UUID.randomUUID().toString();
		log.debug("Device Id for requestId = " + request.getHeader("x-request-id") + " from reqHeaders = "
				+ request.getHeader("deviceid") + "from cookie = " + deviceIdCookie + " and real = " + deviceId);
		String appVersion = request.getHeader("appversion");
		UserEntry user = this.getAnonymousUser();
		LoginResponseAndSession loginResponseAndSession = this.registerDeviceAndCreateSession(user, deviceId,
				apikeyQueryParam, appVersion, requestBody, true, null);
		return loginResponseAndSession.getSession();
	}

	private UserEntry getAnonymousUser() {
		UserEntry userEntry = new UserEntry();
		userEntry.setId(0L);
		userEntry.setIsInternalUser(false);
		return userEntry;
	}

	public Map<String, String> addAnalyticsData(UserContext userContext, AppTenant tenant, CompletableFuture<UserAllocation> hamletUserExperimentPromise)
			throws ExecutionException, InterruptedException {
		String userId = userContext.getUserProfile().getUserId();
		Map<String, String> analyticsData = new HashMap<>();
		UserAllocation userAllocation = hamletUserExperimentPromise.get();
        List<String> userExperimentList = getExperimentDataForAnalytics(analyticsData, userAllocation);
        if (userExperimentList != null) {
            analyticsData.put(Constants.DATALAKE_ANALYTICS_KEY_EXPERIMENT, String.join(",", userExperimentList));
        }
        Set<String> userTrackedSegments = getSessionTrackedSegmentsForUser(userContext, tenant, serviceInterfaces);
        if (userTrackedSegments != null) {
            analyticsData.put(Constants.DATALAKE_ANALYTICS_KEY_SEGMENTS, String.join(",", userTrackedSegments));
        }
        log.debug("analytics data for userid:{}, tenant:{}, data:{}", userId, tenant, analyticsData.entrySet().stream()
                .map(entry -> entry.getKey() + ":" + entry.getValue()).collect(Collectors.joining(",")));
        return analyticsData;
    }

	@SneakyThrows({InterruptedException.class, ExecutionException.class})
    private Set<String> getSessionTrackedSegmentsForUser(UserContext userContext, AppTenant tenant, ServiceInterfaces serviceInterfaces) {
        Set<String> trackedSegments = segmentTagCache.getSessionTrackedSegments(tenant);
        if (!CollectionUtils.isNullOrEmpty(trackedSegments)) {
			if (userContext.getRequestCache() == null) {
				userContext.setRequestCache(new RequestCache(serviceInterfaces));
			}
			SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
			return userSegments.getRelevantEntries(trackedSegments);
        } else {
            return null;
        }
    }

    private List<String> getExperimentDataForAnalytics(Map<String, String> analyticsData, UserAllocation userAllocation) {
        if (userAllocation != null && userAllocation.getAssignmentsMap() != null) {
            List<String> userExperimentList = userAllocation.getAssignmentsMap().keySet().stream().map(experimentId -> {
                UserAssignment userAssignment = userAllocation.getAssignmentsMap().get(experimentId);
                return String.format("E:%s_%s_%s", userAssignment.getExperimentId(),
                        userAssignment.getBucket() != null ? userAssignment.getBucket().getBucketId() : "undefined",
                        userAssignment.getVersionIdentifier());
            }).collect(Collectors.toList());
            return userExperimentList;
        } else {
            log.error("error at User Hamlet Experiment Mapping api ");
            return null;
        }
    }

    public FindPreferredCityResponse findPreferredCity(AppTenant tenant, Float latitude, Float longitude, String ip,
	   Session session, String passedCityId, UserContext userContext, CFAnalytics cfAnalytics) throws BaseException, JsonProcessingException {
		DetectedCityResponseByIp detectedCityResponseByIp = this.cityCache.getCityAndCountryByIp(Tenant.fromString(tenant.toString()), ip);
		boolean showCitySelection = false;
		/*
		 * Set default City for country "OTHER" and location as servicable for
		 * International B2B Demo App Users or Mindfit App
		 */
		if (AppUtil.isMindFitApp(userContext) || (tenant == AppTenant.LIVEFIT
				&& AppUtil.doesUserBelongToLiveB2BDemoExperiment(this.envBusiness, userContext))) {
			FindPreferredCityResponse ret = new FindPreferredCityResponse();
			City selectedCity = this.cityCache.getDefaultCityForCountry("OTHER");
			ret.setShowCitySelection(showCitySelection);
			ret.setCity(selectedCity);
			ret.setDetectedCityName(detectedCityResponseByIp.getDetectedCityName());
			ret.setDetectedCountryCode(detectedCityResponseByIp.getDetectedCountryCode());
			ret.setIsCityChangeToBeNotified(false);
			ret.setDetectedLocation(detectedCityResponseByIp.getDetectedLocation());
			session.getSessionData().setIsLocationServiceable(true);
			return ret;
		}

		FindPreferredCityResponse ret = new FindPreferredCityResponse();
		UserCity userCity = LocationUtil.getUserCityFromRequest(request, passedCityId, Tenant.fromString(tenant.toString()), cityCache, this.userAttributesClient);
		if (!Objects.isNull(cfAnalytics)) {
            cfAnalytics.sendEvent(
                UserCitySetReasonAnalyticsEvent.builder()
                    .cityId(userCity.getCity().getCityId())
					.cityName(userCity.getCity().getName())
                    .isCitySelectedManually(userCity.getIsCityManuallySelected())
                    .reason(userCity.getCitySelectionReason())
                    .userIp(AppUtil.getClientIpAddr(request))
                    .latitude(latitude)
                    .longitude(longitude)
                    .from("user/status")
					.isReasonSession(Objects.equals(userCity.getCitySelectionReason(), "SESSION"))
                    .build(),
                userContext, false, true, false, false
            );
        }
		ret.setCity(userCity.getCity());
		ret.setIsCityManuallySelected(userCity.getIsCityManuallySelected());
		ret.setDetectedCityName(detectedCityResponseByIp.getDetectedCityName());
		ret.setDetectedCountryCode(detectedCityResponseByIp.getDetectedCountryCode());
		ret.setDetectedLocation(detectedCityResponseByIp.getDetectedLocation());
		ret.setShowCitySelection(false);
		ret.setIsCityChangeToBeNotified(false);
		Boolean isInternationalApp = tenant == AppTenant.LIVEFIT;
		if (userCity.getIsCityManuallySelected()) {
			session.getSessionData().setCitySelectedTimeInMillis(new Date().getTime());
		} else if (
			!isInternationalApp &&
			!Objects.isNull(session) &&
			!Objects.isNull(session.getSessionData()) &&
			!Objects.isNull(session.getSessionData().getCitySelectedTimeInMillis())
		) {
			long timeDiff = new Date().getTime() - session.getSessionData().getCitySelectedTimeInMillis();
			if (timeDiff > 5 * 24 * 60 * 60 * 1000) {
				ret.setShowCitySelection(true);
			}
		}
		return ret;
	}

	public CompletableFuture<UserContext> getUserContextFromReq(HttpServletRequest request) throws BaseException, JsonProcessingException {
		Session session = (Session) request.getAttribute("session");
		AppTenant tenant = AppUtil.getTenantFromReq(request, apiKeyService);
		String cityId = (Objects.isNull(session) ||Objects.isNull(session.getSessionData()) || Objects.isNull(session.getSessionData().getCityId())) &&
				AppTenant.ONYX.equals(tenant) ? "Other_USA" : null;
		UserCity userCity = LocationUtil.getUserCityFromRequest(request, cityId, Tenant.fromString(tenant.toString()), cityCache, this.userAttributesClient);
		City city = userCity.getCity();
		CompletableFuture<UserEntry> userPromise = this.getUser(session.getUserId());
		// If subuserid present in header validate the user is authorized to book for
		// the sub user
		String subUserId = request.getHeader("subuserid");
		// TODO: fixme
//        if (subUserId != null) {
//            UserEntry user = userPromise.get();
//            const subUserRelation = _.find(user.subUserRelations, relation => {
//            return relation.subUserId === subUserId
//            })
//            if (!subUserRelation) {
//                throw new UnauthorizedError("Not authorized to operate on this sub user")
//            }
//
//        }
		UserProfile userProfile = new UserProfile();
		userProfile.setSubUserId(subUserId);
		userProfile.setUserId(session.getUserId());
		userProfile.setCultCenterId(session.getSessionData().getCultCenterId());
		userProfile.setMindCenterId(session.getSessionData().getMindCenterId());
		userProfile.setAreaId(session.getSessionData().getLocationPreferenceData() != null
				&& session.getSessionData().getLocationPreferenceData().getAreaId() != null
						? session.getSessionData().getLocationPreferenceData().getAreaId()
						: "1");
		userProfile.setCity(city);
		userProfile.setTimezone(city.getTimezone());

		UserContext ret = new UserContext();
		ret.setSessionInfo(this.getSessionInfoInUserContext(request));
		ret.setUserProfile(userProfile);
		ret.setUserEntryCompletableFuture(userPromise);
		return CompletableFuture.completedFuture(ret);
	}

	public SessionInfo getSessionInfoInUserContext(HttpServletRequest request) {
		SessionInfo ret = new SessionInfo();
		Boolean bypassAuth = (Boolean) request.getAttribute("bypassAuth");
		Session session = (Session) request.getAttribute("session");
		if (BooleanUtils.isNotTrue(bypassAuth) || session != null) {
			log.debug("Not bypassing auth session");
			UserAgent userAgent = session.getUserAgent();
			ret.setUserAgent(userAgent);
			ret.setDeviceId(session.getDeviceId());
			ret.setIsUserLoggedIn(session.getIsNotLoggedIn() == null || !session.getIsNotLoggedIn());
			ret.setSessionData(session.getSessionData());
			ret.setAt(session.getAt());
		}
		String osName = request.getHeader("osname");
		Float appVersion = NumberUtils.isParsable(request.getHeader("appversion"))
				? Float.parseFloat(request.getHeader("appversion"))
				: null;
		Float clientVersion = NumberUtils.isParsable(request.getHeader("clientversion"))
				? Float.parseFloat(request.getHeader("clientversion"))
				: null;
		Float codePushVersion = NumberUtils.isParsable(request.getHeader("codepushversion"))
				? Float.parseFloat(request.getHeader("codepushversion"))
				: 0f;
		Float lat = NumberUtils.isParsable(request.getHeader("lat")) ? Float.parseFloat(request.getHeader("lat"))
				: null;
		Float lon = NumberUtils.isParsable(request.getHeader("lon")) ? Float.parseFloat(request.getHeader("lon"))
				: null;

		String apiKey = request.getHeader("api-key");
		if (apiKey == null) {
			apiKey = request.getHeader("apikey");
		}
		if (apiKey == null && request.getHeader("authorization") != null) {
			apiKey = request.getHeader("authorization").split(" ")[1];
		}
		String orderSource = AppUtil.callSource(apiKey, apiKeyService);

		String appSource = request.getHeader("appsource") != null
				? request.getHeader("appsource")
				: null;

		ret.setOsName(osName);
		ret.setAppVersion(appVersion);
		ret.setClientVersion(clientVersion);
		ret.setCpVersion(codePushVersion);
		ret.setLat(lat);
		ret.setLon(lon);
		ret.setApiKey(apiKey);
		ret.setOrderSource(orderSource);

		if (appSource != null) {
			ret.setAppsource(appSource);
		}

		return ret;
	}

	@Async
	public CompletableFuture<List<ActiveDIYPackViewV1>> getDIYActivePacks(UserContext userContext) {
		List<DIYPackFulfilment> diyServicefulfilments = new ArrayList<>();
		try {
			diyServicefulfilments = this.diyfsService
					.getDIYPackFulfilmentsForUserForProductType(userContext.getUserProfile().getUserId(),null,null).get(300, TimeUnit.MILLISECONDS);
		} catch (Exception e) {
			log.error("could not get diy packs", e);
		}
		List<CompletableFuture<ActiveDIYPackViewV1>> activePacksPromise = new ArrayList<>();
		if (diyServicefulfilments != null && diyServicefulfilments.size() > 0) {
			if (diyServicefulfilments.size() > 3) {
				diyServicefulfilments = diyServicefulfilments.subList(0, 3);
			}
			diyServicefulfilments.forEach(diyServicefulfilment -> {
				try {
					CompletableFuture<ActiveDIYPackViewV1> packView = this.activePackViewBuilderV1.buildDIYPackView(userContext, diyServicefulfilment);
					if(packView != null) {
						activePacksPromise.add(packView);
					}
				} catch (Exception e) {
					log.error("error in buildDIYPackView", e);
				}
			});
		}
		return FutureUtil.allOf(activePacksPromise);
	}

//	private FindPreferredCityResponse detectPreferredCity(AppTenant tenant, Float latitude, Float longitude, String ip,
//														  Session session, DetectedCityResponseByIp detectedCityResponseByIp, UserContext userContext) {
//		Boolean isInternationalApp = tenant == AppTenant.LIVEFIT;
//		Boolean showCitySelection = false;
//		CityAndCountry cityAndCountry = null;
//		Boolean throughCoordinates = true;
//		if (!isInternationalApp && latitude != null && longitude != null) {
//			cityAndCountry = this.cityCache.getCityAndCountry(Tenant.fromString(tenant.toString()), latitude, longitude);
//		}
//		if (cityAndCountry == null) {
//			throughCoordinates = false;
//			cityAndCountry = new CityAndCountry();
//			cityAndCountry.setCity(detectedCityResponseByIp.getCity());
//			cityAndCountry.setCountry(detectedCityResponseByIp.getCountry());
//		}
//
//		String sessionCityId = session.getSessionData().getCityId();
//
//		// Notify if there is any city change marked in less than a min
//		Boolean isCityChangeToBeNotified = false;
//		if (session.getSessionData().getCitySelectedTimeInMillis() != null) {
//			isCityChangeToBeNotified = (new Date().getTime()
//					- session.getSessionData().getCitySelectedTimeInMillis()) < 60000;
//		}
//
//		/*
//		 * check if session has a city id selected and the city is among the cities
//		 * supported by tenant then set as default session city
//		 */
//		City cityBasedOnSession = null;
//		if (AppUtil.isTataNeuWebApp(userContext, apiKeyService) && latitude != null && longitude != null) {
//			cityBasedOnSession = cityAndCountry.getCity();
//		}
//		else if (sessionCityId != null) {
//			City cityInSession = this.cityCache.getCityById(sessionCityId);
//			if (cityInSession.getTenant().contains(tenant.toString())) {
//				cityBasedOnSession = cityInSession;
//			}
//		}
//
//		City cityBasedOnIp = detectedCityResponseByIp.getCity();
//
//		/*
//		 * for international app :: do not check for city in session for india app :: If
//		 * city stored in session is different from detected city and city selection has
//		 * been made 5 days before, show the city selection popup
//		 */
//		if (cityBasedOnSession != null && !isInternationalApp) {
//			if (cityAndCountry.getCity() != null) {
//				if (cityAndCountry.getCity().getCityId().equals(sessionCityId))
//					showCitySelection = false;
//				else if (session.getSessionData().getCitySelectedTimeInMillis() != null) {
//					long timeDiff = new Date().getTime() - session.getSessionData().getCitySelectedTimeInMillis();
//					if (timeDiff > 5 * 24 * 60 * 60 * 1000) {
//						showCitySelection = true;
//					}
//				}
//			}
//		} else {
//			// If we are able to detect the city then don't show the pop up,
//			// else show the city / country selection popup
//			isCityChangeToBeNotified = true;
//			if (cityAndCountry.getCity() != null) {
//				session.getSessionData().setCityId(cityAndCountry.getCity().getCityId());
//				session.getSessionData().setIsCityManuallySelected(true);
//				session.getSessionData().setCitySelectedTimeInMillis(new Date().getTime());
//				showCitySelection = false;
//			} else {
//				// set as default if ip level detection is not available
//				cityAndCountry.setCity(
//						cityBasedOnIp != null ? cityBasedOnIp : this.cityCache.getDefaultCityForTenant(Tenant.fromString(tenant.toString())));
//				showCitySelection = true;
//			}
//		}
//
//		/*
//		 * international app :: cityAndCountry -> IP based city or Default india app ::
//		 * cityAndCountry -> session based, IP based or Default city modal -> disabled
//		 * on international app
//		 */
//		City finalCity = cityBasedOnSession;
//		if (finalCity == null || isInternationalApp) {
//			finalCity = cityAndCountry.getCity();
//		}
//
//		if (isInternationalApp) {
//			showCitySelection = false;
//		}
//
//		Boolean isDetectedCityServiceable = AppUtil.isDigitalServiceableAtLocation(tenant, finalCity.getCityId(),
//				this.cityCache);
//		session.getSessionData().setIsLocationServiceable(isDetectedCityServiceable);
//
//		FindPreferredCityResponse ret = new FindPreferredCityResponse();
//		ret.setShowCitySelection(showCitySelection);
//		ret.setCity(finalCity);
//		ret.setDetectedCityName(detectedCityResponseByIp.detectedCityName);
//		ret.setDetectedCountryCode(detectedCityResponseByIp.detectedCountryCode);
//		ret.setIsCityChangeToBeNotified(isCityChangeToBeNotified);
//		ret.setDetectedLocation(detectedCityResponseByIp.getDetectedLocation());
//
//		return ret;
//	}

	public UsersResponse getUserByEmail(String email) throws BaseException {
		return this.userServiceClient.getByEmail(email, AppTenant.CUREFIT);
	}

	@Async
	public CompletableFuture<UserEntry> getUser(String userId) throws BaseException {
		return this.userServiceClient.getUser(userId);
	}

	@Async
	public CompletableFuture<UserEntry> deviceLogin(String deviceId) throws BaseException {
		UserEntry user = this.userServiceClient.deviceLogin(deviceId);
		return CompletableFuture.completedFuture(user);
	}

	public CompletableFuture<PreferredLocation> getPreferredLocation(UserContext userContext, String userId,
			SessionData sessionData, Float longitude, Float latitude, String mealSlot, Boolean ignoreServiceableTimings,
			ListingBrandIdType listingBrand, UserAgent userAgentType) {
		if (listingBrand == null) {
			listingBrand = ListingBrandIdType.EAT_FIT;
		}
		if (userAgentType == null) {
			userAgentType = UserAgent.APP;
		}

		// this.logger.info(`getPreferredLocation: mealSlot: ${mealSlot}
		// ignoreServiceableTimings: ${ignoreServiceableTimings}`)
		LocationPreferenceData locationPreferenceData = sessionData.getLocationPreferenceData();
		Boolean isWithinServicableCity = EatUtil.isEatFitAvailable(sessionData.getCityId());
		String tz = userContext.getUserProfile().getCity().getTimezone();
		String cityId = sessionData.getCityId();
		UserDeliveryAddress address;
		DeliverySubArea area;
		DeliverySubArea defaultArea;
		Boolean isInServicableArea;
		Boolean isUserPreferenceSet;
		LatLong latLong;

		return CompletableFuture.completedFuture(null);
	}

	public Boolean loginIntoTV(String userId, String token)
			throws MalformedURLException, BaseException, URISyntaxException {
		UserDeviceTokenEntry tokenEntry = userServiceClient.deviceTokenLogin(userId, token);
		if (userId.equals(String.valueOf(tokenEntry.getUserId()))) {
			return true;
		}
		return false;
	}

	public UserDeviceTokenEntry generateToken(String deviceId)
			throws MalformedURLException, UserDeviceTokenException, URISyntaxException {
		return userServiceClient.generateToken(deviceId);
	}

	public UserDeviceTokenEntry getTokenStatus(String deviceId)
			throws UserDeviceTokenException, MalformedURLException, URISyntaxException, InterruptedException {
		UserDeviceTokenEntry entry = userServiceClient.getTokenStatus(deviceId);
		int i = 0;
		// retry for 5mins until user logsin / new token is generated
		while (i < RETRY_LIMIT_MILLISECONDS && (entry.getUserId() == null && entry.getCode() == null)) {
			Thread.sleep(1000);
			i += 1000;
			entry = userServiceClient.getTokenStatus(deviceId);
		}
		return entry;
	}

	public TVVideoInfo updateTVVideoStatus(TVVideoInfo videoStatusRequest, UserContext userContext) {
		TVVideoInfo status = videoStatusRequest;
		String userId = userContext.getUserProfile().getUserId();
		status.setUserId(userId);
		Optional<TVVideoInfo> dbEntry = tvRepository.findByUserId(userId);
		if (dbEntry.isPresent()) {
			TVVideoInfo entry = dbEntry.get();
			entry.setDeviceId(videoStatusRequest.getDeviceId());
			entry.setStatus(videoStatusRequest.getStatus());
			entry.setClassId(videoStatusRequest.getClassId());
			entry.setLiveClass(videoStatusRequest.isLiveClass());
			entry.setUserId(userId);
			status = entry;
		}
		Long lastPNTriggerTime = status.getLastPNTriggerTime();
		if (VideoStatus.STARTED.equals(status.getStatus()) && (null == lastPNTriggerTime
				|| (null != lastPNTriggerTime && (Instant.now().toEpochMilli() - lastPNTriggerTime > 1800000)))) {
			String location = userContext.getSessionInfo().getSessionData().getLocationName();
			Tenant tenant = Tenant.fromString(AppUtil.getTenantFromUserContext(userContext).toString());
			try {
				boolean shouldSendPN = false;
				if (status.isLiveClass()) {
					LiveClass liveClass = diyfsService.getLiveClassById(videoStatusRequest.getClassId(), userId,
							location, tenant);
					if (null != liveClass && !CollectionUtils.isNullOrEmpty(liveClass.getFeatures())
							&& liveClass.getFeatures().contains(EngagementFeatureType.ENERGY)) {
						shouldSendPN = true;
					}
				} else {
					List<DIYProduct> diyProducts = diyfsService.getDIYFitnessProductsByProductIds(userId,
							Collections.singletonList(videoStatusRequest.getClassId()), tenant);
					if (!CollectionUtils.isNullOrEmpty(diyProducts)) {
						DIYProduct diyClass = diyProducts.get(0);
						if (null != diyClass && !CollectionUtils.isNullOrEmpty(diyClass.getFeatures())
								&& diyClass.getFeatures().contains(EngagementFeatureType.ENERGY.toString())) {
							shouldSendPN = true;
						}
					}
				}
				if (shouldSendPN) {
					AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
					SendCampaignNotificationsResponse response = triggerPN(userId, appTenant);
					Map<String, List<SendCampaignNotificationsResponseStatus>> campaignNotificationResponse = response
							.getResponse();
					if (null != campaignNotificationResponse && campaignNotificationResponse.containsKey(userId)) {
						log.info("**** campaign response " + campaignNotificationResponse);
						List<SendCampaignNotificationsResponseStatus> statusList = campaignNotificationResponse
								.get(userId);
						if (!CollectionUtils.isNullOrEmpty(statusList) && null != statusList.get(0)) {
							SendCampaignNotificationsResponseStatus statusResponse = statusList.get(0);
							if (statusResponse.getSent() == true) {
								status.setLastPNTriggerTime(Instant.now().toEpochMilli());
							}
						}
					}
				}
			} catch (Exception e) {
				log.error("Error while triggering push notification for user id " + userId, e);
			}
		}
		status = tvRepository.save(status);
		return status;
	}

	@SneakyThrows({InterruptedException.class, ExecutionException.class})
	private SendCampaignNotificationsResponse triggerPN(String userId, AppTenant appTenant) throws BaseException {
		SendCampaignNotificationsRequest notificationRequest = new SendCampaignNotificationsRequest();
		UserEntry user = userServiceClient.getUser(userId).get();
		com.curefit.iris.models.UserContext context = new com.curefit.iris.models.UserContext();
		context.setUserId(userId);
		context.setPhone(user.getPhone());
		context.setEmailId(user.getEmail());
		notificationRequest.setUserContexts(Collections.singletonList(context));
		notificationRequest.setCampaignId(LIVE_CURRENTLY_PLAYING_ON_TV);
		notificationRequest.setCreativeIds(Collections.singletonList(PUSH_NOTIFICATION_CURRENTLY_PLAYING_ON_TV));
		return campaignService.sendCampaignMessages(notificationRequest, appTenant);
	}

	public TVVideoInfo getCurrentlyPlayingVideoInfo(String userId) {
		Optional<TVVideoInfo> dbEntry = tvRepository.findByUserIdAndStatus(userId, VideoStatus.STARTED);
		return dbEntry.orElse(null);
	}

	public MembershipDetailsGrouped getActiveMembershipDetails(UserContext userContext, String userId) {
		RequestCache requestCache = userContext.getRequestCache();
		MembershipDetailsGrouped currentDetails = null;
		try {
			CultSummary summary = (CultSummary) requestCache.getRequestFuture(RequestType.CULT_MIND_SUMMARY, userContext).get();
			if (null != summary && null != summary.getMembershipSummary()) {
				currentDetails = summary.getMembershipSummary().getCurrent();
				if (null != currentDetails && null != currentDetails.getCult()) {
					OfflineFitnessPack product = catalogueServicePMS.getOfflineFitnessPackById(currentDetails.getCult().getProductID());
					if (null != product) {
						currentDetails.getCult().setPackName(product.getDisplayName()); // TODO: Fetch title from membership instead
					}
				}
			}
		} catch (Exception e) {
			log.error("Failure while fetching membership pack details for user " + userId , e);
		}
		return currentDetails;
	}

	public UserEntry updateByUserId(final String userId, final UserEntry userEntry) {
		if(userId == null || userEntry == null) {
			log.info("Invalid update user Request, userId or userEntry is null");
			return null;
		}
		return userServiceClient.updateByUserId(userId, userEntry);
	}

	public void sendUserActivityRashiEvent(String userId, String eventName, JSONObject body) throws BaseException, JsonProcessingException {
		rashiClient.publishUserEvent(UserEventType.USER_ACTIVITY_EVENT, eventName, new Date(), userId , body, AppTenant.CUREFIT, UUID.randomUUID().toString());
	}

	public static boolean isCultEmployee(SegmentEvaluatorService segmentEvaluatorService,
			EnvironmentService environmentService, UserContext userContext) {
		if (environmentService.isProduction()) {
			String segmentId = "be27f500-9524-4f2f-947e-0b9747a2620c";
			Segment segment = segmentEvaluatorService
					.checkCondition(Collections.singletonList(segmentId), userContext).join();
			return segment != null;
		}
		return true;
	}

	public void sendUserProfileRashiEvent(String userId, String eventName, JSONObject body)
			throws BaseException, JsonProcessingException {
		rashiClient.publishUserEvent(UserEventType.USER_PROFILE_EVENT, eventName, new Date(), userId, body,
				AppTenant.CUREFIT, UUID.randomUUID().toString());
	}

	public EnergyActivityStreak getUserEnergyStreak(UserContext userContext)
			throws ParseException, BaseException, ExecutionException, InterruptedException {
		log.debug("UserService::getUserEnergyStreak received request for" + userContext.toString());
		String userId = userContext.getUserProfile().getUserId();

		// Loading all events from Rashi post launch date
		String startDate = "17/03/2023";
		List<EventEntry> events = this.getRashiEventsForEnergyStreak(userId, startDate);

		// Calculate Level Based on the Events from Rashi
		EnergyActivityStreak energyActivityStreak = this.calculateUsersEnergyLevel(events, userId);
		log.debug("UserService::getUserEnergyStreak level for userId: " + userId + energyActivityStreak);

		// Publish Profile Event to Rashi
		this.publishRashiAttributes(energyActivityStreak, userId, userContext);

		//Cache flag to show onboarding page to users
		try {
			energyActivityStreak.setShowOnboarding(!this.featureStateCache.match(userId,
					"energy_streak_onboarding","viewed").get());

			if(energyActivityStreak.getShowOnboarding()) {
				this.featureStateCache.set(userId,"energy_streak_onboarding", "viewed");
			}
		} catch (Exception e) {
			log.error("UserService::getUserEnergyStreak Exception while fetching cache data flag" + e.toString());
		}

		//Cache flag to reset assets
		try {
			energyActivityStreak.setResetActivityStreakAssets(!this.featureStateCache.match(userId,
					"energy_assets_deleted","viewed").get());
		} catch (Exception e) {
			log.error("UserService::getUserEnergyStreak Exception while fetching cache data flag" + e.toString());
		}

		try{
			this.featureStateCache.set(userId,"energy_streak_level",
					energyActivityStreak.getCurrentLevel().toString());
		} catch (Exception e) {
			log.error("UserService::getUserEnergyStreak Exception while setting cache data flag" + e.toString());
		}

		energyActivityStreak.setUserName(userContext.getUserEntryCompletableFuture().get().getFirstName() +
				"'s Fitness Journey");
		energyActivityStreak.setProfilePictureUrl(userContext.getUserEntryCompletableFuture().get().getProfilePictureUrl());


		return this.postProcessResponse(userContext, energyActivityStreak);
	}

	private EnergyActivityStreak calculateUserEnergyLevelAsPerHabitGameAttributes(UserContext userContext, HabitBuildingData habitBuildingData, EnergyStreakGame game) throws BaseException, ExecutionException, InterruptedException {

		EnergyActivityStreak energyActivityStreak = new EnergyActivityStreak(0,0, false, game, userContext);
		String userId = userContext.getUserProfile().getUserId();
		UserWeeklyActivityResponse.HabitBuildingGame habitBuildingGame;
		Boolean isGameCompleted= getIfGameIsCompleted(habitBuildingData.getCompletionTime());
		if(!isGameCompleted) {
			habitBuildingGame = new UserWeeklyActivityResponse.HabitBuildingGame();
			Long currLevel = Long.valueOf(getGameCurrentLevel(habitBuildingData.getHabitWeek().intValue(),
					habitBuildingData.getWeekActiveDays().intValue()));
			int daysSinceLastSunday = fetchDaysSinceLastSunday();
			Boolean isSlipping = getIsSlipping(daysSinceLastSunday, habitBuildingData.getWeekActiveDays(), habitBuildingData.getHabitWeek());
			habitBuildingGame.setIsSlipping(isSlipping);

			// handle classes 0 case. This is to ensure user is on current week platform.
			// Keep users on base in multi-player mode
			if (habitBuildingData.getWeekActiveDays() == 0 && (energyActivityStreak.getGame().getIsMultiPlayer() == null ||
					!energyActivityStreak.getGame().getIsMultiPlayer())) {
				currLevel = currLevel + 1;
				energyActivityStreak.setIsActiveOnCurrentLevel(false);
			}
			// Check to ensure user doesnt go beyong current weeks level.
			if (habitBuildingData.getHabitWeek() * Constants.HABIT_GAME_ACTIVE_DAY_THRESHOLD <= currLevel) {
				currLevel = habitBuildingData.getHabitWeek() * Constants.HABIT_GAME_ACTIVE_DAY_THRESHOLD;
			}
			// Check for overflowing level.
			if(currLevel > Constants.HIGHEST_GAME_LEVEL) {
				currLevel = Constants.HIGHEST_GAME_LEVEL;
			}

			habitBuildingGame.setCurrLevel(currLevel);
			energyActivityStreak.setCurrentLevel(Math.toIntExact(habitBuildingGame.getCurrLevel()));
			energyActivityStreak.setSlippingUser(habitBuildingGame.getIsSlipping());

			String energy_streak_level = null;
			try {
				energy_streak_level = this.featureStateCache.get(userId,Constants.RASHI_ATTRIBUTE_FOR_ENERGY_LEVEL_V2).get();
				log.debug("UserService::getUserEnergyStreak fetched feature cache attributes " + energy_streak_level + " for userId " + userId);

			} catch(Exception e) {
				log.error("UserService::calculateUsersEnergyLevel Exception while fetching energy_streak from rashi" + e.toString());
			}

			if (energy_streak_level != null) {
				Integer lastValueShownToUser = Integer.parseInt(energy_streak_level);

				if(lastValueShownToUser > energyActivityStreak.getCurrentLevel()) {
					energyActivityStreak.setPreviousLevel(Math.min(Constants.HIGHEST_GAME_LEVEL.intValue(), energyActivityStreak.getCurrentLevel() + 1));
				} else if(lastValueShownToUser < energyActivityStreak.getCurrentLevel()){
					energyActivityStreak.setPreviousLevel(Math.max(0,energyActivityStreak.getCurrentLevel() - 1));
				} else {
					energyActivityStreak.setPreviousLevel(energyActivityStreak.getCurrentLevel());
				}

			} else {
				energyActivityStreak.setPreviousLevel(Math.max(0, energyActivityStreak.getCurrentLevel() - 1));
			}

			Long workoutsRemaining = 3 - habitBuildingData.getWeekActiveDays() > 0 ? 3 - habitBuildingData.getWeekActiveDays() : 0;
			Long daysLeft = (long) (7 - daysSinceLastSunday + 1);
			Boolean isUnachievable = daysLeft < workoutsRemaining;

			if(isSlipping){
				Map<String, Object> copySlippingUser = new HashMap<>();
				copySlippingUser.put("title", STR."You will move back if you don't complete \{workoutsRemaining} workout\{workoutsRemaining >= 2 ? "s " : " "}this week.");
				copySlippingUser.put("subTitle", "Keep working out to stay on track.");

				Map<String, Object> metaCopies = (Map<String, Object>) game.getMetaCopies();
				metaCopies.put("slippingCopy", copySlippingUser);
				game.setMetaCopies(metaCopies);
			}

//			if(habitBuildingData.getLastOperation() == 0 && !isSlipping){
//				Map<String, Object> copySlippingUser = new HashMap<>();
//				copySlippingUser.put("title","You did not progress last week. Be sure to complete 3 workouts this week.");
//				copySlippingUser.put("subTitle", "Keep working out to stay on track.");
//
//				Map<String, Object> metaCopies = (Map<String, Object>) game.getMetaCopies();
//				metaCopies.put("slippingCopy", copySlippingUser);
//				game.setMetaCopies(metaCopies);
//				energyActivityStreak.setSlippingUser(true);
//			}

			if(isUnachievable){
				Map<String, Object> copySlippingUser = new HashMap<>();
				copySlippingUser.put("title", "It'll be a fresh start next week, be active as much as possible this week to bounce back!");
				copySlippingUser.put("subTitle", "Keep working out to stay on track.");

				Map<String, Object> metaCopies = (Map<String, Object>) game.getMetaCopies();
				metaCopies.put("slippingCopy", copySlippingUser);
				game.setMetaCopies(metaCopies);
				energyActivityStreak.setSlippingUser(true);
			}
		}

		else {
			energyActivityStreak.setCurrentLevel(Math.toIntExact(Constants.HIGHEST_GAME_LEVEL));
			energyActivityStreak.setPreviousLevel(Math.toIntExact(Constants.HIGHEST_GAME_LEVEL));
			energyActivityStreak.setSlippingUser(false);
		}
		energyActivityStreak.setPlayerWeek(Math.toIntExact(habitBuildingData.getHabitWeek()));

        return energyActivityStreak;
    }
	private EnergyActivityStreak calculateUsersEnergyLevel(List<EventEntry> events, String userId) throws BaseException {
		EnergyActivityStreak energyActivityStreak = new EnergyActivityStreak(0,0, false);
		Date date,datePlus3days;
		Calendar c = Calendar.getInstance();

		// For every workout within 3 days of the previous workout, increase level by 1
		for(int i=0; i < events.size(); i++) {

			date = events.get(i).getOccuredAt();

			// Here we set the time to check if the user has not worked out 3 days in a row.
			c.setTime(date);
			c.set(Calendar.HOUR_OF_DAY, 23);
			c.set(Calendar.MINUTE, 59);
			c.add(Calendar.DATE, 3); // Adding 3 days
			datePlus3days = c.getTime();

			if (i == 0){
				energyActivityStreak.setCurrentLevel(1);
			}

			if (i+1 < events.size()) {
				if(events.get(i+1).getOccuredAt().before(datePlus3days)) {
					energyActivityStreak.setCurrentLevel(Math.min(energyActivityStreak.getCurrentLevel() + 1, 12));
				} else {
					energyActivityStreak.setCurrentLevel(Math.max(energyActivityStreak.getCurrentLevel() - 1, 0));
				}
			} else {
				c.setTime(new Date());

				Calendar lastWorkoutTime = Calendar.getInstance();
				lastWorkoutTime.setTime(events.get(i).getOccuredAt());
				lastWorkoutTime.set(Calendar.HOUR_OF_DAY, 23);
				lastWorkoutTime.set(Calendar.MINUTE, 59);


				// Decrease one level for every 3 days not worked out since last workout
				Long daysSinceLastWorkout = ((c.getTime().getTime() - lastWorkoutTime.getTime().getTime()))/ (1000 * Constants.SECONDS_IN_ONE_DAY);
				energyActivityStreak.setDaysSinceLastWorkout(daysSinceLastWorkout);
				energyActivityStreak.setCurrentLevel(Math.max(0, energyActivityStreak.getCurrentLevel() - (daysSinceLastWorkout.intValue()/3)));

				c.add(Calendar.DATE, -2);
				// If last activity was 2 days ago, user is slipping
				if (daysSinceLastWorkout % 3 != 0) {
					energyActivityStreak.setSlippingUser(true);
					if(daysSinceLastWorkout % 3 ==2) {
						Map<String, Object> copySlippingUser = new HashMap<>();
						copySlippingUser.put("title", "You will move a step back if you don't workout by " + (energyActivityStreak.getDaysSinceLastWorkout() % 3 == 2 ? "today.": "tomorrow."));
						copySlippingUser.put("subTitle", "Keep working out to stay on track.");

						Map<String, Object> updatedMeta = (Map<String, Object>) energyActivityStreak.getMeta();
						updatedMeta.put("copySlippingUser",copySlippingUser);
						energyActivityStreak.setMeta(updatedMeta);
					}
				}
			}

			if (energyActivityStreak.getCurrentLevel() == 12) {
				energyActivityStreak.setPreviousLevel(12);
				break;
			}

		}

		// Set previousLevel through feature cache if value is found, else previousLevel = currentLevel - 1
		String energy_streak_level = null;
		try {
			energy_streak_level = this.featureStateCache.get(userId,"energy_streak_level").get();
			log.debug("UserService::getUserEnergyStreak fetched feature cache attributes " + energy_streak_level + " for userId " + userId);

		} catch(Exception e) {
			log.error("UserService::calculateUsersEnergyLevel Exception while fetching energy_streak from rashi" + e.toString());
		}

		if (energy_streak_level != null) {
			Integer lastValueShownToUser = Integer.parseInt(energy_streak_level);

			if(lastValueShownToUser > energyActivityStreak.getCurrentLevel()) {
				energyActivityStreak.setPreviousLevel(Math.min(12, energyActivityStreak.getCurrentLevel() + 1));
			} else if(lastValueShownToUser < energyActivityStreak.getCurrentLevel()){
				energyActivityStreak.setPreviousLevel(Math.max(0,energyActivityStreak.getCurrentLevel() - 1));
			} else {
				energyActivityStreak.setPreviousLevel(energyActivityStreak.getCurrentLevel());
			}

		} else {
			energyActivityStreak.setPreviousLevel(Math.max(0, energyActivityStreak.getCurrentLevel() - 1));
		}

		if(energyActivityStreak.getCurrentLevel() < energyActivityStreak.getPreviousLevel()) {
			Map<String, Object> copySlippingUser = new HashMap<>();
			copySlippingUser.put("title", "You moved back because you haven't worked out in 3 days.");
			copySlippingUser.put("subTitle", "Keep working out to stay on track.");

			Map<String, Object> updatedMeta = (Map<String, Object>) energyActivityStreak.getMeta();
			updatedMeta.put("copySlippingUser",copySlippingUser);
			energyActivityStreak.setMeta(updatedMeta);
		}

		return energyActivityStreak;
	}

	public boolean resetUserEnergyStreak(UserContext userContext) throws ExecutionException, InterruptedException {
		String userId = userContext.getUserProfile().getUserId();
		// Publish Profile Event to Rashi
		try {
			JSONObject rashiEventBody = new JSONObject();
			rashiEventBody.put(Constants.RASHI_ATTRIBUTE_FOR_ENERGY_STREAK, -1);
			UserEventEntry rashiAttributeEntry = new UserEventEntry(Long.parseLong(userId), UserEventType.USER_PROFILE_EVENT, null, new Date(), rashiEventBody, AppUtil.getAppTenantFromUserContext(userContext));
			String requestId = request.getHeader("x-request-id");

			PublishResult result = this.rashiClient.publishUserEvent(rashiAttributeEntry, AppUtil.getAppTenantFromUserContext(userContext), requestId);
			log.debug("UserService::resetUserEnergyStreak successfully published Rashi Event " + result.toString());
		} catch (Exception e) {
			log.error("UserService::resetUserEnergyStreak Exception while publishing energy_streak to rashi" + e.toString());
			return false;
		}

		try {
			this.featureStateCache.unset(userId, "energy_streak_slipped").get();
			this.featureStateCache.unset(userId, "energy_streak_slipping").get();
			this.featureStateCache.unset(userId, "energy_streak_level").get();
		} catch (Exception e) {
			log.error("UserService::resetUserEnergyStreak Exception while unsetting cache data" + e.toString());
			return false;
		}

		return true;
	}

	public boolean resetOnboarding(UserContext userContext, String gameId) {
		String userId = userContext.getUserProfile().getUserId();
		try {
			if(gameId != null) {
				this.featureStateCache.unset(userId, Constants.FEATURE_STATE_CACHE_ENERGY_STREAK_ONBOARDING_BASE + gameId).get();
			}
			this.featureStateCache.unset(userId, "energy_streak_onboarding").get();
			this.featureStateCache.unset(userId, "energy_streak_onboarding_v2").get();
		} catch (Exception e) {
			log.error("UserService::resetUserEnergyStreak Exception while unsetting cache data" + e.toString());
			return false;
		}
		return true;
	}

	public List<EventEntry> getRashiEventsForEnergyStreak(String userId, String startDate) throws BaseException {
		List<String> energyEvents = new ArrayList<String>();
		energyEvents.add("GYM_SESSION_ATTENDED");
		energyEvents.add("cult_class_attended");
		List<EventEntry> events = this.userEventClient.getEventsAfterDate(Long.valueOf(userId) , energyEvents,AppTenant.CUREFIT, startDate);
		events.sort(Comparator.comparing(EventEntry::getOccuredAt));
		log.info("UserService::getUserEnergyStreak got "+ events.size() + " events for userId:" + userId);

		return events;
	}

	public void publishRashiAttributes(EnergyActivityStreak energyActivityStreak, String userId, UserContext userContext) {
		try {
			JSONObject rashiEventBody = new JSONObject();
			rashiEventBody.put(Constants.RASHI_ATTRIBUTE_FOR_ENERGY_STREAK, energyActivityStreak.getCurrentLevel());
			UserEventEntry rashiAttributeEntry = new UserEventEntry(Long.parseLong(userId),
					UserEventType.USER_PROFILE_EVENT, null, new Date(), rashiEventBody,
					AppUtil.getAppTenantFromUserContext(userContext));
			String requestId = request.getHeader("x-request-id");

			PublishResult result = this.rashiClient.publishUserEvent(rashiAttributeEntry,
					AppUtil.getAppTenantFromUserContext(userContext), requestId);
			log.info("UserService::getUserEnergyStreak successfully published Rashi Event " + result.toString());
		} catch (Exception e) {
			log.error("UserService::getUserEnergyStreak Exception while publishing energy_streak to rashi"
					+ e.toString());
		}

		if(energyActivityStreak.getCurrentLevel() == 12) {
			try {
				JSONObject rashiEventBody = new JSONObject();
				rashiEventBody.put(Constants.RASHI_ATTRIBUTE_FOR_ENERGY_STREAK_COMPLETION, true);
				UserEventEntry rashiAttributeEntry = new UserEventEntry(Long.parseLong(userId),
						UserEventType.USER_PROFILE_EVENT, null, new Date(), rashiEventBody,
						AppUtil.getAppTenantFromUserContext(userContext));
				String requestId = request.getHeader("x-request-id");

				PublishResult result = this.rashiClient.publishUserEvent(rashiAttributeEntry,
						AppUtil.getAppTenantFromUserContext(userContext), requestId);
				log.debug("UserService::getUserEnergyStreak successfully published Rashi Event " + result.toString());
			} catch (Exception e) {
				log.error("UserService::getUserEnergyStreak Exception while publishing energy_streak_completion to rashi"
						+ e.toString());
			}
		}
	}

	@SneakyThrows({InterruptedException.class, ExecutionException.class})
	public EnergyActivityStreak postProcessResponse(UserContext userContext, EnergyActivityStreak energyActivityStreak) throws BaseException {

		try {
			UserAttributesResponse userAttributesResponse = this.userAttributesCacheClient.getAttributes(Long.valueOf(userContext.getUserProfile().getUserId()), Collections.singletonList(Constants.RASHI_ATTRIBUTE_FOR_CULTPACK_START_DATE), AppTenant.CUREFIT);
			if(userAttributesResponse != null && userAttributesResponse.getAttributes() != null && userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_CULTPACK_START_DATE) != null) {
				Date date = new Date(Long.parseLong(userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_CULTPACK_START_DATE).toString()));
				log.info("UserService::getXWorkoutsToBeCompletedInYDays got rashi attribute cultpackstartdate for user" + userContext.getUserProfile().getUserId() + " as "+ date.toString());

				Calendar culpackstartdate = Calendar.getInstance();
				culpackstartdate.setTime(date);
				culpackstartdate.add(Calendar.DATE, 21);

				Calendar today = Calendar.getInstance();
				today.setTime(new Date());

				if(culpackstartdate.after(today)) {
					Long daysToCompleteWorkout = ((culpackstartdate.getTime().getTime() - today.getTime().getTime()))/ (1000 * Constants.SECONDS_IN_ONE_DAY);
					Long remainingWorkouts = Long.valueOf(12 - energyActivityStreak.getCurrentLevel());

					if(2 * daysToCompleteWorkout >= remainingWorkouts && energyActivityStreak.getCurrentLevel() < 12) {
						Map<String, Object> copySlippingUser = new HashMap<>();
						copySlippingUser.put("title", energyActivityStreak.getSlippingUser() ? "You will move a step back if you don't workout by " + (energyActivityStreak.getDaysSinceLastWorkout() % 3 == 2 ? "today.": "tomorrow.") : "You moved back because you haven't worked out in 3 days.");
						copySlippingUser.put("subTitle", "Complete " + remainingWorkouts + " workouts in the next " + daysToCompleteWorkout + " days to win the game");
						energyActivityStreak.setWorkoutsRemaining("Complete " + remainingWorkouts + " workouts in the next " + daysToCompleteWorkout + " days to win the game");

						Map<String, Object> updatedMeta = (Map<String, Object>) energyActivityStreak.getMeta();
						updatedMeta.put("copySlippingUser",copySlippingUser);
						energyActivityStreak.setMeta(updatedMeta);
					}
				} else {
					energyActivityStreak.setIsBadgeSegment(true);
					Map<String, Object> updatedMeta = (Map<String, Object>) energyActivityStreak.getMeta();
					updatedMeta.put("finalVideoUrl",EnergyActivityStreak.FINAL_VIDEO_URL_BADGE);
					energyActivityStreak.setMeta(updatedMeta);

					List<Object> lockedHeadersList = (List<Object>) updatedMeta.get("lockedHeadersList");
					Map<String, Object> locked_level_4 = (Map<String, Object>) lockedHeadersList.get(4);
					locked_level_4.put("title", "Become Fitness Superhero");
					locked_level_4.put("subTitle", "By finishing 12 classes in 21 days you will enter the league of top 10% of fitness enthusiast.");
					lockedHeadersList.add(4,locked_level_4);
					updatedMeta.put("lockedHeadersList", lockedHeadersList);

					if (!this.featureStateCache.match(userContext.getUserProfile().getUserId(), "energy_level_transition","viewed").get()){
						energyActivityStreak.setPreviousLevel(energyActivityStreak.getCurrentLevel());
						energyActivityStreak.setShowTransition(true);
					}
				}

			}
		} catch (Exception e) {
			log.error("UserService::postProcessResponse Exception while postprocessing energy level"
					+ e.toString());
		}

		SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
		if(!userSegments.contains(Constants.ENERGY_STREAK_TSHIRT_SEGMENT)) {
			energyActivityStreak.setIsBadgeSegment(true);
			Map<String, Object> updatedMeta = (Map<String, Object>) energyActivityStreak.getMeta();
			updatedMeta.put("onBoardingVideoUrl",EnergyActivityStreak.ONBOARDING_VIDEO_URL_BADGE);
			updatedMeta.put("finalVideoUrl",EnergyActivityStreak.FINAL_VIDEO_URL_BADGE);

			List<Object> lockedHeadersList = (List<Object>) updatedMeta.get("lockedHeadersList");
			Map<String, Object> locked_level_4 = (Map<String, Object>) lockedHeadersList.get(4);
			locked_level_4.put("title", "Become Fitness Superhero");
			locked_level_4.put("subTitle", "By finishing 12 classes in 21 days you will enter the league of top 10% of fitness enthusiast.");
			lockedHeadersList.add(locked_level_4);
			updatedMeta.put("lockedHeadersList", lockedHeadersList);

			energyActivityStreak.setMeta(updatedMeta);

			energyActivityStreak.setShowTransition(false);
		}

		return energyActivityStreak;
	}

	public LocationUtil.CityResponse updateCity(
		UserContext userContext,
		Session session,
		String selectedCityId,
		ServiceInterfaces serviceInterfaces
	) throws BaseException, IOException, ExecutionException, InterruptedException {
		session.getSessionData().setCityId(selectedCityId);
		session.getSessionData().setIsCityManuallySelected(true);
		session.getSessionData().setCitySelectedTimeInMillis(new Date().getTime());
		session.getSessionData().setCultCenterId(null);
		session.getSessionData().setMindCenterId(null);

		City city = this.cityCache.getCityById(selectedCityId);
		userContext.getUserProfile().setCity(city);
		userContext.getSessionInfo().getSessionData().setCityId(city.getCityId());

		AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
		serviceInterfaces.cfAnalytics.sendEvent(
			UserCitySetReasonAnalyticsEvent.builder()
				.cityId(city.getCityId())
				.cityName(city.getName())
				.isCitySelectedManually(true)
				.reason("MANUALLY")
				.userIp(AppUtil.getClientIpAddr(request))
				.latitude(userContext.getSessionInfo().getLat())
				.longitude(userContext.getSessionInfo().getLon())
				.build(),
			userContext, false, true, false, false
		);

		Map<String, Object> eventData = new HashMap<>();
		eventData.put("TimeZone", city.getTimezone());
		eventData.put("cityId", city.getCityId());

		if (AppUtil.isMobileApp(userContext)) {
			AppUtil.updateValueInUserAttribute(userAttributesClient, session.getUserId(), "user_city_set_reason", "MANUALLY", userContext, "setting the Home Page preference");
		}

		if (!Objects.isNull(userContext) && AppUtil.isMobileApp(userContext)) {
			Map<String, Object> eventDatax = new HashMap<>();
			eventDatax.put("user_city_set_reason", "Manually");
			eventData.put("latitude", userContext.getSessionInfo().getLat());
			eventData.put("longitude", userContext.getSessionInfo().getLon());
			eventData.put("ip", "null");
			eventDatax.put("at", session.getAt());
			eventDatax.put("cityId", selectedCityId);
			JSONObject eventDataJson = new JSONObject(eventDatax);
			rashiClient.publishUserEvent(UserEventType.USER_ACTIVITY_EVENT, "user_city_Set_reason", new Date(), session.getUserId(), eventDataJson, "Curefit", AppUtil.getAppTenantFromUserContext(userContext), UUID.randomUUID().toString());
		}
		JSONObject eventDataJson = new JSONObject(eventData);

		this.rashiClient.publishUserEvent(UserEventType.USER_PROFILE_UPDATE, "default", new Date(), userContext.getUserProfile().getUserId(), eventDataJson, null, appTenant, UUID.randomUUID().toString());

		this.sessionService.updateSessionData(session.getAt(), session.getSessionData(), !userContext.getSessionInfo().getIsUserLoggedIn());

		this.deviceService.updateSessionInformationPlatform(
			session.getDeviceId(), session.getSessionData(),
			userContext.getSessionInfo().getAppVersion(), null, userContext.getSessionInfo().getLat(),
			userContext.getSessionInfo().getLon(), null, appTenant,
			userContext.getUserProfile().getTimezone(),
			userContext, null, userContext.getSessionInfo().getSessionData().getDetectedCity()
		);
		CompletableFuture<UserAllocation> userExperiments = CompletableFuture.completedFuture(null);

		VerticalsWithHighlightInformation verticalInfo =
			this.navBarService
			.getUserAndSupportedVerticalsInfo(
				userContext, city, session.getUserId(),
				userExperiments, segmentEvaluatorService
			).get();

		List<VerticalType> supportedVerticals =
			verticalInfo.getVerticals().stream()
			.map(VerticalInfo::getVerticalType).toList();

		List<TabType> appTabs = new ArrayList<>(supportedVerticals.size() + 1);
		appTabs.add(TabType.PLAN);
		appTabs.addAll(supportedVerticals.stream().map(supportedVertical -> TabType.valueOf(supportedVertical.toString())).toList());

		LocationUtil.CityResponse cityResponse =
			LocationUtil.CityResponse.builder().appTabs(appTabs).verticals(verticalInfo.getVerticals())
			.isMoreToBeHighlighted(verticalInfo.getIsMoreToBeHighlighted()).supportedVerticals(supportedVerticals).build();

		return cityResponse;

	}

	private static String getPreferenceKey(String cityId, String key) {
		return "fitness_preference_" + cityId.toLowerCase() + "_" + key;
	}

	public void updateLocationData(
		UserContext userContext, String userId, String cityId,
		String key, String value, SessionData.Preference userPreference
	) throws BaseException {
		String prefKey = getPreferenceKey(cityId, key);
		if(!userContext.getSessionInfo().getIsUserLoggedIn()) {
			userPreference.addCustomSettings(prefKey, value);
		}
		else {
			userAttributesClient.createOrUpdateAttribute(
				new UserAttributeEntry(
					Long.valueOf(userId), "GLOBAL",
					DataType.STRING, prefKey, value,
					"preferred " + key,
					new Date(), new Date(),
					AppUtil.getAppTenantFromUserContext(userContext),
					null
				),
				AppUtil.getAppTenantFromUserContext(userContext)
			);
		}
	}

	public void saveUserCultPreference(String key, Object value, CultService cultService, UserContext userContext) {
		CultUserPreference cultUserPreference = new CultUserPreference();
		PreferenceSetting preferenceSetting = new PreferenceSetting();
		preferenceSetting.setKey(key);
		preferenceSetting.setValue(value);
		cultUserPreference.setSettings(Collections.singletonList(preferenceSetting));

		Integer currentCultCityId = userContext.getUserProfile().getCity().getCultCityId();
		String userId = userContext.getUserProfile().getUserId();

		cultService.savePreferences(
			Long.valueOf(currentCultCityId),
			userId, cultUserPreference, "CUREFIT_API",
			userContext.getUserProfile().getSubUserId()
		);
	}

	public LocationUtil.LocationStatusResponse setLocationPreference(
		UserContext userContext, String userId, String cityId,
		LocationUtil.LocationPreferenceRequestEntity preference,
		ServiceInterfaces serviceInterfaces,
		SessionBusiness sessionBusiness
	) throws Exception {

		try {

			if (Objects.isNull(preference) || Objects.isNull(preference.getPrefLocationType()))
				return LocationUtil.LocationStatusResponse.getAndSetData(false, "Value for prefLocationType is not provided");

			if (
				preference.getPrefLocationType() == LocationPreferenceType.current_location &&
				(Objects.isNull(userContext.getSessionInfo().getLat()) ||
				Objects.isNull(userContext.getSessionInfo().getLon()) ||
				userContext.getSessionInfo().getLat() == 0 ||
				userContext.getSessionInfo().getLon() == 0)
			) return LocationUtil.LocationStatusResponse.getAndSetData(false, "Please provide location permission");

			if (
				preference.getPrefLocationType() == LocationPreferenceType.coordinates &&
				(Objects.isNull(preference.getCoordinates()) ||
				Objects.isNull(preference.getCoordinatesName()) ||
				Objects.isNull(preference.getCoordinates().getLatitude()) ||
				Objects.isNull(preference.getCoordinates().getLongitude()))
			) return LocationUtil.LocationStatusResponse.getAndSetData(false, "Map Location not found");

			if (
				preference.getPrefLocationType() == LocationPreferenceType.coordinates ||
				preference.getPrefLocationType() == LocationPreferenceType.current_location
			) {
				LocationUtil.DetectedCityStatusResponse checkValue = LocationUtil.isCoordinateInCity(
					userContext,
					cityId,
					(preference.getPrefLocationType() == LocationPreferenceType.current_location)
						? userContext.getSessionInfo().getLat()
						: preference.getCoordinates().getLatitude(),
					(preference.getPrefLocationType() == LocationPreferenceType.current_location)
						? userContext.getSessionInfo().getLon()
						: preference.getCoordinates().getLongitude(),
					serviceInterfaces
				);
				if (!checkValue.getOk()) {
					return LocationUtil.LocationStatusResponse.getAndSetData(
						false,
						"Selected City is " + checkValue.getCurrentCityName() + " but detected city is " + checkValue.getDetectedCity().getName(),
						checkValue.getCurrentCityName(),
						checkValue.getDetectedCity()
					);
				}
			}

			if(preference.getPrefLocationType() == LocationPreferenceType.locality) {

				if(Objects.isNull(preference.getLocality()) || preference.getLocality().isBlank() || preference.getLocality().equals("undefined")) {
					exceptionReportingService.reportErrorMessage("setLocationPreference:: the value for locality is " + preference.getLocality() + ", userId: " + userId);
					return LocationUtil.LocationStatusResponse.getAndSetData(false, "Locality not found");
				}
			}

			SessionData.Preference userPreference = new SessionData.Preference();

			String at = userContext.getSessionInfo().getAt();
			SessionData sessionData = userContext.getSessionInfo().getSessionData();

			if(Objects.equals(preference.getPageFrom(), "CLASS_BOOKING")) {
				if (AuthUtil.isGuestUser(userContext)) {
					SessionData.Preference cultUserPreference = new SessionData.Preference();
					cultUserPreference.addCustomSettings("USER_BOOKING_V2_ACTIVE_SETTING", null);

					switch (preference.getPrefLocationType()) {
						case coordinates -> {
							cultUserPreference.getSettings().get(0).setValue("USER_BOOKING_V2_LAT_LONG");
							cultUserPreference.addCustomSettings(
								"USER_BOOKING_V2_LAT_LONG",
								Map.of(
									"latitude", preference.getCoordinates().getLatitude(),
									"longitude", preference.getCoordinates().getLongitude()
								)
							);
						}
						case locality -> {
							cultUserPreference.getSettings().get(0).setValue("USER_BOOKING_V2_LOCALITY_V3");
							cultUserPreference.addCustomSettings(
								"USER_BOOKING_V2_LOCALITY_V3",
								preference.getLocality()
							);
						}
						case current_location -> {
							cultUserPreference.getSettings().get(0).setValue("USER_BOOKING_V2_LAT_LONG");
							cultUserPreference.addCustomSettings(
								"USER_BOOKING_V2_LAT_LONG",
								Map.of(
									"latitude", userContext.getSessionInfo().getLat(),
									"longitude", userContext.getSessionInfo().getLon()
								)
							);
						}
					}

					sessionData.setCultPreference(cultUserPreference);
					sessionBusiness.updateSessionData(at, sessionData, !userContext.getSessionInfo().getIsUserLoggedIn(), true);
				} else {
					CultUserPreference cultUserPreference = new CultUserPreference();
					PreferenceSetting preferenceSetting = new PreferenceSetting();
					preferenceSetting.setKey("USER_BOOKING_V2_ACTIVE_SETTING");
					preferenceSetting.setValue(null);
					cultUserPreference.setSettings(Collections.singletonList(preferenceSetting));
					Integer currentCultCityId = userContext.getUserProfile().getCity().getCultCityId();

					CultUserPreference cultPreferences =
							serviceInterfaces.cultService.getPreferenceByKey(userId, Long.valueOf(currentCultCityId), "USER_BOOKING_V2_ACTIVE_SETTING");

					if (
						!Objects.isNull(cultPreferences) &&
						!Objects.isNull(cultPreferences.getSettings()) &&
						!cultPreferences.getSettings().isEmpty() &&
						Objects.equals(cultPreferences.getSettings().get(0).getValue(), "USER_BOOKING_V2_FAVOURITE_CENTER")
					) {
						serviceInterfaces.cultService.savePreferences(
							Long.valueOf(currentCultCityId),
							userId, cultUserPreference, "CUREFIT_API",
							userContext.getUserProfile().getSubUserId()
						);
					}
				}
			}

			updateLocationData(
				userContext, userId, cityId,
				LocationPreferenceType.loc_pref_type.toString(),
				preference.getPrefLocationType().toString(), userPreference
			);

			String value = null;
			switch (preference.getPrefLocationType()) {
				case coordinates -> {
					value = preference.getCoordinates().getLatitude() + "," + preference.getCoordinates().getLongitude();
					updateLocationData(
						userContext, userId, cityId,
						LocationPreferenceType.coordinates_name.toString(),
						preference.getCoordinatesName(), userPreference
					);
				}
				case locality -> {
					if (!Objects.isNull(preference.getLocality()))
						value = preference.getLocality();
				}
				case current_location -> value = userContext.getSessionInfo().getLat() + "," + userContext.getSessionInfo().getLon();
			}

			if (!Objects.isNull(value)) {
				updateLocationData(userContext, userId, cityId, preference.getPrefLocationType().toString(), value, userPreference);
				if (!userContext.getSessionInfo().getIsUserLoggedIn()) {
					sessionData.setFitnessLocationPreference(userPreference);
					sessionBusiness.updateSessionData(at, sessionData, !userContext.getSessionInfo().getIsUserLoggedIn(), true);
				}

				return LocationUtil.LocationStatusResponse.getAndSetData(true, null);
			}

			return LocationUtil.LocationStatusResponse.getAndSetData(
				false,
				"Value for preferred location type " + preference.getPrefLocationType() + " is not provided"
			);
		} catch (Exception e) {
			serviceInterfaces.exceptionReportingService.reportException("Unable to set location preference for userId - " + userId, e);
			return LocationUtil.LocationStatusResponse.getAndSetData(false, "Something went wrong. Please try again later");
		}

	}

	/*
	Energy Streak V2 APIs
	 */
	@Scheduled(fixedDelay = CACHE_REFRESH_TIME, initialDelay = 300)
	public void refreshCache() throws ExecutionException, InterruptedException {
		log.info("UserService:: refreshCache executing");
		EnergyStreakGamePageConfig gameConfigData = this.energyStreakGamesData.findByPageId("EnergyStreaksConfig").get();
		if(gameConfigData != null) {
			this.gameConfig = gameConfigData;
		} else {
			this.gameConfig = (EnergyStreakGamePageConfig) Collections.emptyList();
		}
	}

	public EnergyStreakGamePageConfig getGameConfig() throws ExecutionException, InterruptedException {
		if(gameConfig == null) {
			this.refreshCache();
		}

		return this.gameConfig;
	}

	public EnergyActivityStreak getUserEnergyStreakV2(UserContext userContext, ServiceInterfaces serviceInterfaces) throws BaseException, ExecutionException, InterruptedException {
		log.info("UserService::getUserEnergyStreakV2 received request for" + userContext.toString());
		String userId = userContext.getUserProfile().getUserId();


		EnergyStreakGame userGame = this.getUsersGame(userContext, null, serviceInterfaces);
		if(userGame == null) {
			throw new BaseException("User is not part of any game!");
		}

		HabitBuildingData habitBuildingData = null;
		if (userGame.getIsMultiPlayer() != null && userGame.getIsMultiPlayer()) {
			habitBuildingData = CenterLevelChallengeWidget.getUserAttributesForHabitBuilding(Long.parseLong(userContext.getUserProfile().getUserId()),
					serviceInterfaces, Constants.MULTIPLAYER_GAME_COMPLETION_TIME, Constants.HABIT_BUILDING_WEEK_ATTRIBUTE);
		} else {
			habitBuildingData = CenterLevelChallengeWidget.getUserAttributesForHabitBuilding(Long.parseLong(userContext.getUserProfile().getUserId()), serviceInterfaces);
		}
		EnergyActivityStreak energyActivityStreak = this.calculateUserEnergyLevelAsPerHabitGameAttributes(userContext,habitBuildingData, userGame);

		if (userGame.getIsMultiPlayer() != null && userGame.getIsMultiPlayer()) {
			energyActivityStreak = this.multiPlayerGameService.setMultiPlayerData(energyActivityStreak, userContext, serviceInterfaces);
		}
		return this.postProcessResponseV2(userContext, energyActivityStreak, userGame);
	}

	private EnergyActivityStreak calculateUsersEnergyLevelV2(UserContext userContext, ServiceInterfaces serviceInterfaces) throws BaseException, ExecutionException, InterruptedException {

		String userId = userContext.getUserProfile().getUserId();
		List<String> attributes = Arrays.asList(Constants.RASHI_ATTRIBUTE_FOR_ENERGY_LEVEL_V2, Constants.RASHI_ATTRIBUTE_FOR_USER_GAME_ID);
		UserAttributesResponse userAttributesResponse = this.userAttributesCacheClient.getAttributes(Long.valueOf(userId),attributes, AppTenant.CUREFIT);

		// Get User's Game
		EnergyStreakGame userGame = getUsersGame(userContext, userAttributesResponse, serviceInterfaces);
		if(userGame == null) {
			throw new BaseException("User is not part of any game!");
		}

		// Get Users Level
		Integer currentLevel = 0;
		Integer previousLevel = 0;
		if(userAttributesResponse != null && userAttributesResponse.getAttributes() != null &&
				userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_ENERGY_LEVEL_V2) != null) {

			String currentLevelString = (String) userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_ENERGY_LEVEL_V2);
			String previousLevelString = this.featureStateCache.get(userId, Constants.RASHI_ATTRIBUTE_FOR_ENERGY_LEVEL_V2).get();
			currentLevel = currentLevelString == null ? 0 : Integer.parseInt(currentLevelString);
			previousLevel = previousLevelString == null ? currentLevel: Integer.parseInt(previousLevelString);
		}

		Boolean isUserSlipping = AppUtil.isUserSlippingLevelV2(userGame, serviceInterfaces, userContext);

		// Initialise Response
		EnergyActivityStreak energyActivityStreak = new EnergyActivityStreak(currentLevel,previousLevel, isUserSlipping, userGame, userContext);

		if(energyActivityStreak.getCurrentLevel() < energyActivityStreak.getPreviousLevel() && userGame != null) {
			Map<String, Object> gameCopy = (Map<String, Object>) userGame.getMetaCopies();
			Map<String, Object> updatedMeta = (Map<String, Object>) energyActivityStreak.getMeta();

			updatedMeta.put("copySlippingUser",gameCopy.get("slippedCopy"));
			energyActivityStreak.setMeta(updatedMeta);
		}

		return energyActivityStreak;
	}

	// Returns which active game is the user part of
	public EnergyStreakGame getUsersGame(UserContext userContext, UserAttributesResponse userAttributesResponse, ServiceInterfaces serviceInterfaces) {
		EnergyStreakGame userGame = null;
		try {
			EnergyStreakGamePageConfig gameConfig = this.getGameConfig();

			if(userAttributesResponse != null && userAttributesResponse.getAttributes() != null &&
					userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_GAME_ID) != null) {
				String gameId = userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_USER_GAME_ID).toString();
				userGame = gameConfig.getData().getGames().stream().filter(game -> game.getIsActive()
						&& gameId.equals(game.getGameId())
						&& AppUtil.doesUserBelongToGameSegmentV2(game, serviceInterfaces, userContext)).findAny().orElse(null);
			}
			if(userGame == null) {
				EnergyStreakGameConfig gameConfigData = gameConfig.getData();

				if(gameConfigData!=null && gameConfigData.getIsActive()) {
					for (int i = 0; i < gameConfigData.getGames().size(); i++) {
						if (gameConfigData.getGames().get(i).getIsActive() && AppUtil.doesUserBelongToGameSegmentV2(gameConfigData.getGames().get(i), serviceInterfaces , userContext)) {
							userGame = gameConfigData.getGames().get(i);
							break;
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("UserService:getUsersGame Error in fetching userGame for user " + userContext.getUserProfile().getUserId() + " due to " + e.getMessage());
			return null;
		}

		return userGame;

	}

	public void publishRashiAttributesV2(String gameCompleteKey, String userId, UserContext userContext) throws ExecutionException, InterruptedException, BaseException {

			try {

				try{
					this.featureStateCache.set(userId, gameCompleteKey, "viewed");
				} catch (Exception e) {
					log.error("UserService::getUserEnergyStreak Exception while setting cache data flag" + e.toString());
				}

				JSONObject rashiEventBody = new JSONObject();
				rashiEventBody.put(gameCompleteKey, true);
				UserEventEntry rashiAttributeEntry = new UserEventEntry(Long.parseLong(userId),
						UserEventType.USER_PROFILE_EVENT, null, new Date(), rashiEventBody,
						AppUtil.getAppTenantFromUserContext(userContext));
				String requestId = request.getHeader("x-request-id");

				PublishResult result = this.rashiClient.publishUserEvent(rashiAttributeEntry,
						AppUtil.getAppTenantFromUserContext(userContext), requestId);
				log.info("UserService::publishRashiAttributesV2 successfully published Rashi Event " + result.toString());
			} catch (Exception e) {
				log.error("UserService::publishRashiAttributesV2 Exception while publishing energy_streak_completion to rashi"
						+ e.toString());
			}
	}

	public EnergyActivityStreak postProcessResponseV2(UserContext userContext, EnergyActivityStreak energyActivityStreak, EnergyStreakGame userGame) throws ExecutionException, InterruptedException, BaseException {

		String userId = userContext.getUserProfile().getUserId();
		String showOnboardingKey;
		if(userGame != null && userGame.getGameId() != null && userGame.getGameId().equals("habit_game")){
			showOnboardingKey = Constants.ENERGY_STREAK_M1_HABIT_GAME;
		}
		else {
			showOnboardingKey = userGame != null && userGame.getGameId() != null ?
					Constants.FEATURE_STATE_CACHE_ENERGY_STREAK_ONBOARDING_BASE + userGame.getGameId(): "energy_streak_onboarding_v2";
		}

		// Publish Profile Event to Rashi
		log.info("UserService::getUserEnergyStreakV2 publishing rashi attributes for userId: " + userId);

		String gameCompleteKey = MultiPlayerGameService.getGameCompletionKey(userGame);
		if(userGame != null && energyActivityStreak.getCurrentLevel() == userGame.getLevelData().size()) {
			this.publishRashiAttributesV2(gameCompleteKey, userId, userContext);
		}


		//Cache flag to show onboarding page to users
		try {
			energyActivityStreak.setShowOnboarding(!this.featureStateCache.match(userId,
					showOnboardingKey,"viewed").get());

			if(energyActivityStreak.getShowOnboarding()) {
				this.featureStateCache.set(userId,showOnboardingKey, "viewed");
			}
		} catch (Exception e) {
			log.error("UserService::getUserEnergyStreak Exception while fetching cache data flag" + e.toString());
		}

		try{
			if(energyActivityStreak.getCurrentLevel() == 1 && energyActivityStreak.getIsActiveOnCurrentLevel() != null && !energyActivityStreak.getIsActiveOnCurrentLevel()){
				this.featureStateCache.set(userId, Constants.RASHI_ATTRIBUTE_FOR_ENERGY_LEVEL_V2,
						"0");
			} else {
				this.featureStateCache.set(userId, Constants.RASHI_ATTRIBUTE_FOR_ENERGY_LEVEL_V2,
						energyActivityStreak.getCurrentLevel().toString());
			}
		} catch (Exception e) {
			log.error("UserService::getUserEnergyStreak Exception while setting cache data flag" + e.toString());
		}

        if (userGame != null && userGame.getGameId().equalsIgnoreCase(Constants.YER_2023_GAME_ID)) {
			energyActivityStreak = this.updateCharacterBasedAssetForYER(userContext, energyActivityStreak);
		}

		return energyActivityStreak;
	}

	public EnergyActivityStreak updateCharacterBasedAssetForYER(UserContext userContext, EnergyActivityStreak energyActivityStreak) throws ExecutionException, InterruptedException, BaseException {
		CharacterRevealPageConfig pageConfig = this.pageConfigRepository.findByPageId("CharacterRevealPageConfig").get();
		List<String> attributes = Arrays.asList(Constants.RASHI_ATTRIBUTE_FOR_FAV_FORMAT_2023, Constants.RASHI_ATTRIBUTE_FOR_GYM_GOAL_2023);
		UserAttributesResponse userAttributesResponse = this.userAttributesCacheClient.getAttributes(Long.valueOf(userContext.getUserProfile().getUserId()), attributes, AppTenant.CUREFIT);

		// profileTags, assetsConfig, assetUrls -> finalUrl, lv 10-12 title

		if(userAttributesResponse != null && userAttributesResponse.getAttributes() != null &&
				userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_FAV_FORMAT_2023) != null) {
			String fav_format_attribute = (String) userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_FAV_FORMAT_2023);
			String defaultGymFavFormat = "HRX";
			if(fav_format_attribute != null && !fav_format_attribute.isEmpty()) {
				log.info("UserService::getUserEnergyStreak check pageConfig: {} {} {} {}", fav_format_attribute, pageConfig, pageConfig.getData(), pageConfig.getData().getFavFormatMap(),
						pageConfig.getData().getFavFormatMap().size());
				String favFormat = pageConfig.getData().getFavFormatMap().containsKey(fav_format_attribute.toUpperCase()) ?
						pageConfig.getData().getFavFormatMap().get(fav_format_attribute.toUpperCase()) : "HYBRID";
				if(favFormat.equalsIgnoreCase("GYM")) {
					if(userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_GYM_GOAL_2023) != null) {
						String gymGoalAnswerId = (String) userAttributesResponse.getAttributes().get(Constants.RASHI_ATTRIBUTE_FOR_GYM_GOAL_2023);
						log.info("UserService::getUserEnergyStreak check questionConfig: {} {} {} {}", gymGoalAnswerId, pageConfig.getData().getQuestionOptionsConfig(),
								pageConfig.getData().getQuestionOptionsConfig().size(), pageConfig.getData().getQuestionOptionsConfig().get(0).getQuestionId());
						CharacterQuestionConfig questionConfig = pageConfig.getData().getQuestionOptionsConfig().stream()
								.filter(obj -> gymGoalAnswerId.equalsIgnoreCase(obj.getQuestionId()) && obj.getQuestionId() != null)
								.findFirst().orElse(null);
						if(questionConfig != null) {
							favFormat = questionConfig.getMapToFormat();
						}
						else {
							favFormat = defaultGymFavFormat;
						}
					}
					else {
						favFormat = defaultGymFavFormat;
					}
				}
				if(favFormat != null && !favFormat.isEmpty()) {
					Map<String, CharacterConfigSection> characterConfigs = pageConfig.getData().getCharacterConfig();
					if(characterConfigs != null && characterConfigs.containsKey(favFormat)) {
						CharacterConfigSection characterConfig = characterConfigs.get(favFormat);
						log.info("UserService::getUserEnergyStreak fav format: {} {}", favFormat, userContext.getUserProfile().getUserId());
						// Adding fav format
						energyActivityStreak = energyActivityStreak.clone();
						String finalAssetUrl = energyActivityStreak.getGame().getAssetUrls().getFinalUrl();
						log.info("UserService::getUserEnergyStreak finalAssetUrl: {}", finalAssetUrl);
						String prefixFinalAssetUrl = finalAssetUrl.substring(0, finalAssetUrl.lastIndexOf('/') + 1);
						energyActivityStreak.getGame().getAssetUrls().setFinalUrl(prefixFinalAssetUrl + favFormat.toUpperCase() + ".mp4");

						String metaAssetConfig = "{\"unreachedLevels\":{\"milestone_upcoming\":\"year_end_reveal_game/B_M_Upcoming.json\",\"normal_upcoming\":\"year_end_reveal_game/B_Upcoming.json\",\"milestone_idle\":\"year_end_reveal_game/B_M_Idle.json\",\"normal_idle\":\"year_end_reveal_game/B_Idle.json\"},\"currentLevels\":{\"level_1_to_3\":\"year_end_reveal_game/B_01_Active.json\",\"level_3\":\"year_end_reveal_game/B_1M_Active.json\",\"level_3_to_6\":\"year_end_reveal_game/B_02_Active.json\",\"level_6\":\"year_end_reveal_game/B_2M_Active.json\",\"level_6_to_9\":\"year_end_reveal_game/B_03_Active.json\",\"level_9\":\"year_end_reveal_game/B_3M_Active.json\",\"level_9_to_12\":\"year_end_reveal_game/B_04_Active.json\"},\"slippingLevels\":{\"level_1_to_3\":\"year_end_reveal_game/B_01_Slipping.json\",\"level_3\":\"year_end_reveal_game/B_1M_Slipping.json\",\"level_3_to_6\":\"year_end_reveal_game/B_02_Slipping.json\",\"level_6\":\"year_end_reveal_game/B_2M_Slipping.json\",\"level_6_to_9\":\"year_end_reveal_game/B_03_Slipping.json\",\"level_9\":\"year_end_reveal_game/B_3M_Slipping.json\",\"level_9_to_12\":\"year_end_reveal_game/B_04_Slipping.json\"},\"reachedLevels\":{\"level_1_to_3\":\"year_end_reveal_game/B_01_Slipping.json\",\"level_3\":\"year_end_reveal_game/B_1M_Slipping.json\",\"level_3_to_6\":\"year_end_reveal_game/B_02_Slipping.json\",\"level_6\":\"year_end_reveal_game/B_2M_Slipping.json\",\"level_6_to_9\":\"year_end_reveal_game/B_03_Slipping.json\",\"level_9\":\"year_end_reveal_game/B_3M_Slipping.json\",\"level_9_to_12\":\"year_end_reveal_game/B_04_Slipping.json\"},\"jumping_vman\":{\"reverse_jump\":\"year_end_reveal_game/Reverse_jump.json\",\"level_1_to_3_jump\":\"year_end_reveal_game/0_JUMP.json\",\"level_3_jump\":\"year_end_reveal_game/0-1_JUMP.json\",\"level_3_to_6_jump\":\"year_end_reveal_game/01_JUMP.json\",\"level_6_jump\":\"year_end_reveal_game/1-2_JUMP.json\",\"level_6_to_9_jump\":\"year_end_reveal_game/02_JUMP.json\",\"level_9_jump\":\"year_end_reveal_game/2-3_JUMP.json\",\"level_9_to_12_jump\":\"year_end_reveal_game/03_jump.json\"},\"still_vman\":{\"initial_jump\":\"year_end_reveal_game/0_JUMP.json\",\"level_1_to_3\":\"year_end_reveal_game/01_Still.json\",\"level_3_to_6\":\"year_end_reveal_game/01_Still.json\",\"level_6_to_9\":\"year_end_reveal_game/02_Still.json\",\"level_9_to_12\":\"year_end_reveal_game/03_Still.json\"},\"crystals\":{\"level_3\":\"year_end_reveal_game/C1.png\",\"level_6\":\"year_end_reveal_game/C2.png\",\"level_9\":\"year_end_reveal_game/C3.png\"},\"levelGlow\":{\"level_3\":\"year_end_reveal_game/L1_bg.png\",\"level_6\":\"year_end_reveal_game/L2_bg.png\",\"level_9\":\"year_end_reveal_game/L3_bg.png\",\"level_12\":\"year_end_reveal_game/L4_bg.png\"},\"finalBlock\":{\"level_1_to_3\":\"year_end_reveal_game/B_01_Badge.json\",\"level_3_to_6\":\"year_end_reveal_game/B_02_Badge.json\",\"level_6_to_9\":\"year_end_reveal_game/B_03_Badge.json\",\"level_9_to_12\":\"year_end_reveal_game/B_03_Badge_static.json\"},\"mileStoneBadges\":{\"level3_done\":\"year_end_reveal_game/level3_done.png\",\"level6_done\":\"year_end_reveal_game/level6_done.png\",\"level9_done\":\"year_end_reveal_game/level9_done.png\",\"level12_done\":\"year_end_reveal_game/level12_done.png\"},\"entryBlock\":\"year_end_reveal_game/entry_block.png\",\"background\":\"year_end_reveal_game/BG.json\",\"textGlow\":\"year_end_reveal_game/Text_glow.png\",\"vmanSide\":\"year_end_reveal_game/V_man_side.svg\",\"badgeLastFrame\":\"year_end_reveal_game/FAV_FORMAT_Badge_Last_Frame.jpg\"}"; //(String) energyActivityStreak.getGame().getMeta().get("assetsConfig");
						log.info("UserService::getUserEnergyStreak metaAssetConfig: {}", metaAssetConfig);
						String newMetaAssetConfig = metaAssetConfig.replace("FAV_FORMAT", favFormat.toUpperCase());
						energyActivityStreak.getGame().getMeta().put("assetsConfig", newMetaAssetConfig);

						String profileTags = "[\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\",\"AKA USER_NAME\"]"; // (String) energyActivityStreak.getGame().getMeta().get("profileTags");
						log.info("UserService::getUserEnergyStreak profileTags: {}", profileTags);
						String newProfileTags = profileTags.replace("USER_NAME", userContext.getUserEntryCompletableFuture().get().getFirstName());
						energyActivityStreak.getGame().getMeta().put("profileTags", newProfileTags);

						// Adding character name
						for(int i = 0; i < energyActivityStreak.getGame().getLevelData().size(); i++) {
							if(i == 9 || i == 10 || i == 11) {
								if (energyActivityStreak.getGame().getLevelData().get(i) != null) {
									if (energyActivityStreak.getGame().getLevelData().get(i).getLevelCopy() != null) {
										if (energyActivityStreak.getGame().getLevelData().get(i).getLevelCopy().getTitle() != null) {
											energyActivityStreak.getGame().getLevelData().get(i).getLevelCopy().setTitle(
													"Ultimate" + " " + characterConfig.getCharacterName());
										}
									}
								}
							}
						}
						energyActivityStreak.setUserName(characterConfig.getCharacterName() + "'s Journey");
						energyActivityStreak.setWorkoutsRemaining("Collect all superpowers by 31 Jan");

						Map<String, Object> overrideGameMeta = new HashMap<>();
						// Reward action override
						Map<String, Object> rewardCTA = new HashMap<>();
						rewardCTA.put("title", "Close");
						rewardCTA.put("url", "curefit://hometab");
						rewardCTA.put("actionType", "NAVIGATION");
						rewardCTA.put("subTitle","Your reward will reach you soon");
						Map<String,Object> rewardCTAMeta = new HashMap<>();
						rewardCTAMeta.put("viaDeepLink", true);
						rewardCTA.put("meta", rewardCTAMeta);
						overrideGameMeta.put("rewardCTA", rewardCTA);

						// YER action override
						Map<String, Object> classBookingCta = new HashMap<>();
						if (AppUtil.hasUserSeenTheYER2023(userContext)) {
							classBookingCta.put("title", "Book your next class");
							classBookingCta.put("url", "curefit://classbookingv2");
							classBookingCta.put("actionType", "NAVIGATION");
							Map<String,Object> classBookingCtaMeta = new HashMap<>();
							classBookingCtaMeta.put("viaDeepLink", true);
							classBookingCta.put("meta", classBookingCtaMeta);
						}
						else {
							classBookingCta.put("title", "VIEW YEAR END REPORT");
							classBookingCta.put("url", "curefit://yearendreveal");
							classBookingCta.put("actionType", "NAVIGATION");
							Map<String, Object> classBookingCtaMeta = new HashMap<>();
							classBookingCtaMeta.put("viaDeepLink", true);
							classBookingCta.put("meta", classBookingCtaMeta);
						}
						overrideGameMeta.put("classBookingCta", classBookingCta);

						energyActivityStreak.setMeta(overrideGameMeta);

					}
				}
			}

		}

		return energyActivityStreak;
	}

	private String getPendingInvitesSetKey(String userId) {
		return USER_PENDING_INVITES_SET_PREFIX + userId;
	}

	public BackgroundDetails getBackgroundDetails(UserContext userContext, ServiceInterfaces interfaces, List<NotificationCenterEntryType> notifTypes) {
		String userId = userContext.getUserProfile().getUserId();
		Boolean showNotifIcon = AppUtil.isNewSquadUsersSegment(userContext, interfaces);
		Long pendingNotificationsCount = 0L;


		CompletableFuture<DSHomePageWidget> futureDSWidgetResponse = CompletableFuture.supplyAsync(() -> {
			try {
				WidgetContext widgetContext = new WidgetContext();
				List<BaseWidget> dsHomePageWidgetResponse = new DSHomePageWidget().buildView(interfaces, userContext, widgetContext);
				if(dsHomePageWidgetResponse != null && !dsHomePageWidgetResponse.isEmpty() && dsHomePageWidgetResponse.getFirst() != null){
					return (DSHomePageWidget) dsHomePageWidgetResponse.getFirst();
				}
			} catch (Exception e) {
				interfaces.exceptionReportingService.reportException(e);
			}
			return null;
        });

		CompletableFuture<DSSummaryWidget> futureDSSummaryWidgetResponse = CompletableFuture.supplyAsync(() -> {
			try {
				WidgetContext widgetContext = new WidgetContext();
				List<BaseWidget> dsSummaryWidgetResponse = new DSSummaryWidget().buildView(interfaces, userContext, widgetContext);
				if(dsSummaryWidgetResponse != null && !dsSummaryWidgetResponse.isEmpty() && dsSummaryWidgetResponse.getFirst() != null){
					return (DSSummaryWidget) dsSummaryWidgetResponse.getFirst();
				}
			} catch (Exception e) {
				interfaces.exceptionReportingService.reportException(e);
			}
			return null;
		});


		try {
			if (showNotifIcon) {
				if (AppUtil.isEnableSquadSegment(userContext, interfaces)) {
					if (notifTypes != null && !notifTypes.isEmpty()) {
						pendingNotificationsCount = notificationCenterServiceClient.getUnreadNotificationCount(userId, notifTypes);
					} else {
						pendingNotificationsCount = notificationCenterServiceClient.getUnreadNotificationCount(userId);
					}
				} else {
					pendingNotificationsCount = socialServiceRedisKeyValueStore.ssize(getPendingInvitesSetKey(userId));
				}
			}
		} catch (Exception e) {
			log.error("Unread notification fetch error : {}", e);
		}
		Boolean pilatesUser = false;
		try {
			pilatesUser = CultUtil.isPilatesOnlyMembership(interfaces.membershipService, userContext);

		} catch (Exception e) {
			log.error("BACKGROUND_DETAILS_FLOW: Error fetching user membership: {}", e);
			interfaces.exceptionReportingService.reportException("BACKGROUND_DETAILS_FLOW: Error fetching user membership", e);
		}
		List<String> cacheImmuneWidgetIds = new ArrayList<>();
		cacheImmuneWidgetIds.add("4ff0e091-1abd-4135-9f88-87cd60c8ec8e");

		Action syncAction = new Action(ActionType.POST_FITNESS_DEVICE_METRICS, "SYNC");
		Map<String, Object> syncMeta = new HashMap<>();
		syncMeta.put("metrics", getListOfMetrics(userContext));
		syncMeta.put("metricPermissions",getListOfMetrics(userContext));
		syncAction.setMeta(syncMeta);

		boolean enableSyncAction = false;

		try {
			String dataSyncCache = interfaces.featureStateCache.get(userId, AppUtil.FD_DATA_SYNC_KEY).get();
			if (dataSyncCache == null) {
				enableSyncAction = true;
				DateTime now = DateTime.now();
				Long remainingSeconds = 900L;
				interfaces.featureStateCache.set(userContext.getUserProfile().getUserId(), AppUtil.FD_DATA_SYNC_KEY, now.toString(), remainingSeconds);
			}
		} catch (Exception e) {
			log.error("Error while fetchng data sync cache data : {}", e);
			interfaces.exceptionReportingService.reportException("Error while fetchng data sync cache data : {}", e);
		}

		boolean showSosButton = false;
		boolean enableSOSButton = false;
		boolean showUserStreakFlow = false;

		try {
			showUserStreakFlow = userContext.getRequestCache().getMexRequestFuture(RequestType.USER_STREAK_DETAILS, userContext).get() != null;
		} catch (Exception e){
			log.error("Error while fetching streak details", e);
		}

		try {
			showSosButton = shouldShowSOSButton(interfaces, userContext);
			enableSOSButton = shouldEnableSOSButton(interfaces, userContext);
		} catch (Exception e) {
			log.error("Error while fetching showSosButton", e);
		}

		List<BaseWidget> widgets = new ArrayList<>();

		DSHomePageWidget dsHomePageWidget = new DSHomePageWidget();
		try {
			dsHomePageWidget = futureDSWidgetResponse.get();
			if(dsHomePageWidget != null){
				widgets.add(dsHomePageWidget);
			}
		} catch (Exception e){
			log.error("Error while fetching dsHomePageWidget", e);
		}


		try {
			DSSummaryWidget dsSummaryWidget;
			dsSummaryWidget = futureDSSummaryWidgetResponse.get();
			if(dsSummaryWidget != null){
				widgets.add(dsSummaryWidget);
			}
		} catch (Exception e){
			log.error("Error while fetching dsHomePageWidget", e);
		}

		return BackgroundDetails.builder()
				.showNotifIcon(showNotifIcon)
				.pendingNotificationsCount(pendingNotificationsCount)
				.notifIconAction(Action.builder()
						.url("curefit://notification_center")
						.actionType(ActionType.NAVIGATION)
						.build())
				.initialDelayInSeconds("3600")
				.frequencyInSeconds("3600")
				.noOfTries("4")
				.metrics(getListOfMetrics(userContext))
				.metricsPermission(FitnessDeviceModalData.getListOfMetricPermission(userContext))
				.enableBackgroundTaskAndroid(false)
				.enableBackgroundTaskAndroidV2(true)
				.enableBackgroundTaskIos(true)
				.bottomTrayCachingInfo(BottomTrayCachingInfo.builder()
						.bottomTrayCachingDurationInMs(120000)
						.enableBottomTrayCaching(AppUtil.isBottomTrayCachingSupported(userContext))
						.cacheImmuneWidgets(cacheImmuneWidgetIds).build())
				.syncDeviceData(enableSyncAction)
				.syncAction(syncAction)
				.showSOSButton(showSosButton)
				.enableSendMessageToWatchApp(true)
				.enableSOSButton(enableSOSButton)
				.pilatesUser(pilatesUser)
				.showDailyStreakFlow(showUserStreakFlow)
				.dsHomePageWidget(dsHomePageWidget)
				.widgets(widgets)
				.build();
	}

	public boolean shouldShowSOSButton(ServiceInterfaces interfaces, UserContext userContext) {
		try {
			String userId = userContext.getUserProfile().getUserId();
			Segment segment = interfaces.segmentEvaluatorService
						.checkCondition(Collections.singletonList("eb9153b2-45cb-4c46-b0be-b58db68ba43a"), userContext).join();
			boolean segmentCheck = segment != null;
			if (!segmentCheck)
				return false;
			String visibilityFlag = interfaces.sosVisibilityCache.getSOSButtonVisibility(userId);
			log.info("SOS Button visibility for user {}: {}", userId, visibilityFlag);
			return "true".equalsIgnoreCase(visibilityFlag) || interfaces.sosVisibilityCache.shouldSOSBeVisible(userId);
		} catch (Exception e) {
			log.error("Error fetching SOS button visibility from Redis: ", e);
			return false;
		}
	}

	public static boolean isUserBelongToLoginFlowSegment(ServiceInterfaces interfaces, UserContext userContext) {
		try {
			if ((interfaces.environmentService.isProduction() || interfaces.environmentService.isAlpha()) && userContext.getSessionInfo().getAppVersion() > 11.03) {
				Segment segment = interfaces.segmentEvaluatorService
						.checkCondition(Collections.singletonList("c5038980-5c99-4527-97e1-1cc88d949b66"), userContext).join();
				return segment != null;
			}else
				return false;
		} catch (Exception e) {
			return false;
		}
	}

	public boolean shouldEnableSOSButton(ServiceInterfaces interfaces, UserContext userContext) {
		try {
			Segment segment = interfaces.segmentEvaluatorService
					.checkCondition(Collections.singletonList("eb9153b2-45cb-4c46-b0be-b58db68ba43a"), userContext).join();
            return segment != null;
		} catch (Exception e) {
			log.error("Error showing sos button :  ", e);
			return false;
		}
	}


	public void clearNotificationsCounter(String userId) {
		socialServiceRedisKeyValueStore.del(getPendingInvitesSetKey(userId));
	}

	public ClearNotifResponse clearNotificationsCounterV2(String userId, List<NotificationCenterEntryType> notifTypes) {

		try {
			if (notifTypes == null || notifTypes.isEmpty()) {
				this.notificationCenterServiceClient.markNotificationAsRead(userId);
			} else {
				this.notificationCenterServiceClient.markNotificationAsRead(userId, notifTypes);
			}
		} catch (Exception e) {
			log.error("Clear cult notifications error : ", e);
		}
		try {
			socialServiceRedisKeyValueStore.del(getPendingInvitesSetKey(userId));
		} catch (Exception e) {
			log.error("Clear cult notifications redis error : ", e);
		}
		ClearNotifResponse clearNotifResponse = new ClearNotifResponse();
		clearNotifResponse.setSuccess(true);
		return clearNotifResponse;
	}

	public String getAndPurgeGymLoggingTrigger(String userId) {
		String gymLoggingTriggerKey = this.getGymLoggingTriggerKey(userId);
		String gymLoggingTrigger = this.defaultRedisKeyValueStore.get(gymLoggingTriggerKey);
		CompletableFuture.runAsync(() -> {
			this.defaultRedisKeyValueStore.del(gymLoggingTriggerKey);
		});
		return gymLoggingTrigger;
	}

	private String getGymLoggingTriggerKey(String userId) {
		return GYM_LOGGING_TRIGGER_PREFIX + userId;
	}

	/*
	End of Energy Streak V2 APIs
	 */

     public SplashScreenData getSplashScreenData(UserContext userContext, ServiceInterfaces serviceInterfaces) {
		try {
			/// Disabling habit ticker until count is not near 1 million
			if(true || !userContext.getSessionInfo().getIsUserLoggedIn()
					|| !AppUtil.isActiveEliteOrProUser(userContext, serviceInterfaces)) return SplashScreenData.builder().build();

			List<BaseWidget> widgets = new HabitTickerWidget().buildView(serviceInterfaces, userContext, new WidgetContext());
			if(Objects.nonNull(widgets) && !widgets.isEmpty() && widgets.getFirst() instanceof HabitTickerWidget) {
                return SplashScreenData
						.builder()
						.widget(widgets.getFirst())
						.timeoutInMilliseconds(3000)
						.build();
			}
		} catch (Exception e){
			serviceInterfaces.exceptionReportingService.reportException("Error while fetching splash screen data", e);
		}
		return SplashScreenData.builder().build();
	 }
}