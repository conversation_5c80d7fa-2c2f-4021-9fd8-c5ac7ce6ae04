package com.curefit.cfapi.service.gymfit;

import com.curefit.center.dtos.CenterEntry;
import com.curefit.center.dtos.CenterSearchFilters;
import com.curefit.center.dtos.CenterSearchRequest;
import com.curefit.center.dtos.LocalityEntry;
import com.curefit.center.enums.CenterStatus;
import com.curefit.center.enums.CenterVertical;
import com.curefit.center.enums.SkuName;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.GymOnboardingCentersConfigService;
import com.curefit.cfapi.model.internal.cult.LocationPreference;
import com.curefit.cfapi.model.internal.cult.LocationPreferenceType;
import com.curefit.cfapi.model.internal.userinfo.SessionInfo;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.gym.TrainerDetails;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.location.GoogleAPIKeyOwner;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewbuilders.gymfit.CentersPageViewBuilder;
import com.curefit.cfapi.view.viewmodels.gymfit.CentersPageView;
import com.curefit.cfapi.view.viewmodels.gymfit.NearbyCenterCreditsView;
import com.curefit.common.data.exception.BaseException;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.location.models.LatLong;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.cfapi.util.FitnessCenterUtil.*;
import static com.curefit.cfapi.view.viewmodels.gymfit.CentersPageView.*;

@Slf4j
@Service
public class GymfitService {

    private final ServiceInterfaces serviceInterfaces;
    private final CentersPageViewBuilder centersPageViewBuilder;

    private final AppConfigCache appConfigCache;
    private final GymOnboardingCentersConfigService gymOnboardingCentersConfigService;

    @Autowired
    public GymfitService(ServiceInterfaces serviceInterfaces, CentersPageViewBuilder centersPageViewBuilder,
                         AppConfigCache appConfigCache, GymOnboardingCentersConfigService gymOnboardingCentersConfigService) {
        this.serviceInterfaces = serviceInterfaces;
        this.centersPageViewBuilder = centersPageViewBuilder;
        this.appConfigCache =  appConfigCache;
        this.gymOnboardingCentersConfigService = gymOnboardingCentersConfigService;
    }

    public List<TrainerDetails> getTrainerDetails(List<Long> ids, Long centerId) {
        return GymUtil.getTrainerDetails(this.serviceInterfaces, ids, centerId);
    }

    public CentersPageView getCentersPageView(UserContext userContext, Map<String, String> queryParams, String[] centerTypes) throws Exception {
        try {
            Boolean isWeb = AppUtil.isWeb(userContext);

            boolean showPlayLiteOnly = Boolean.parseBoolean(queryParams.getOrDefault("showPlayLite", "false"));
            String searchText = queryParams.getOrDefault("searchText", "");
            CenterSearchFilters.SortBy sortBy = queryParams.containsKey("sortBy") ? CenterSearchFilters.SortBy.valueOf((queryParams.get("sortBy"))) : CenterSearchFilters.SortBy.DISTANCE;
            Integer pageNumber = queryParams.containsKey("pageNumber") ? Integer.parseInt(queryParams.get("pageNumber")) : 0;
            Integer pageSize = queryParams.containsKey("pageSize") ? Integer.parseInt(queryParams.get("pageSize")) : 5;
            boolean isSearchScreen = queryParams.containsKey("isSearchScreen") && Boolean.parseBoolean(queryParams.get("isSearchScreen"));
            boolean isOnboardingFlow = queryParams.containsKey("isOnboardingFlow") && Boolean.parseBoolean(queryParams.get("isOnboardingFlow"));
            if(isOnboardingFlow){
                pageSize = 50;
            }
            boolean isFlutterAllCentersPageSupported = AppUtil.isFlutterAllCentersPageEnabled(userContext);
            Long scrollToCenterId = queryParams.containsKey("scrollToCenterId") && queryParams.get("scrollToCenterId").length() > 0
                    ? Long.parseLong(queryParams.get("scrollToCenterId"))
                    : null;
            List<Integer> activities = queryParams.containsKey("formats") ? Arrays.stream(queryParams.get("formats").split(","))
                    .map(Integer::parseInt).toList() : null;
            List<Long> localityIdList = queryParams.containsKey("localityId") ? Arrays.stream(queryParams.get("localityId").split(","))
                    .map(Long::parseLong).toList() : null;
            boolean hasLocalityList = localityIdList != null && !localityIdList.isEmpty();
            Double radius = (queryParams.containsKey("radius") && !queryParams.get("radius").isEmpty()) ? Math.floor(Double.parseDouble(queryParams.get("radius"))) : null;
            String userPreferenceCityId = userContext.getUserProfile().getCity() != null ? userContext.getUserProfile().getCity().getCityId() : "";
            String cityId = queryParams.getOrDefault("cityId", userPreferenceCityId);
            boolean isCultPassPlaySupported = AppUtil.isCultPassPlaySupported(userContext, serviceInterfaces.environmentService, serviceInterfaces.segmentEvaluatorService);
            boolean isPlaySLPSupported = PlayUtil.isSportLevelPricingSupported(serviceInterfaces.segmentEvaluatorService, serviceInterfaces.environmentService, userContext);
            boolean isOnlyOnePass = false;
            if(centerTypes!= null && Arrays.asList(centerTypes).stream().anyMatch(s -> s.equals("ONEPASS"))) {
                isOnlyOnePass = true;
            }

            List<String> centerTypesList = centerTypes != null ? new LinkedList<>(Arrays.asList(centerTypes)) : new ArrayList<>();
            if (!isCultPassPlaySupported) {
                centerTypesList.remove(CenterTypeValue.SPORT.name());
            }

            boolean isInternalUser = AppUtil.isInternalUser(userContext);

            String userId = userContext.getUserProfile().getUserId();
            SessionInfo sessionInfo = userContext.getSessionInfo();

            float queryParamLatitude = Float.parseFloat(queryParams.getOrDefault("lat", "0"));
            float queryParamLongitude = Float.parseFloat(queryParams.getOrDefault("lon", "0"));
            boolean hasQueryParamCoordinates = queryParamLatitude != 0 && queryParamLongitude != 0;

            Float latitude = sessionInfo.getLat();
            Float longitude = sessionInfo.getLon();
            String localityName = null;

            boolean isLuxGymsSupported = AppUtil.doesUserBelongToLuxGymSegment(serviceInterfaces,serviceInterfaces.getEnvironmentService(),userContext);
            List<SkuName> skuNames = getSkuNames(isCultPassPlaySupported,isLuxGymsSupported, isOnlyOnePass);

            boolean hasNextPage = false;
            List<CenterEntry> unfilteredCenters;
            List<CenterStatus> statuses = new ArrayList<>();
            statuses.add(CenterStatus.ACTIVE);

            Map<CentersPageView.CenterTypeValue, Boolean> centerTypeMap = getCenterTypeFilterMap(centerTypesList, isCultPassPlaySupported);

            CenterSearchRequest centerSearchRequest = new CenterSearchRequest();
            centerSearchRequest.setSearchStr(searchText.trim().length() > 0 ? searchText.trim() : null);
            centerSearchRequest.setOffset(pageNumber * pageSize);
            centerSearchRequest.setLimit(pageSize);
            centerSearchRequest.setStatuses(statuses);
            centerSearchRequest.setEnrichWithMapDistance(true);
            centerSearchRequest.setSortBy(sortBy);
            if (!isOnlyOnePass || (activities == null || activities.isEmpty())) {
                centerSearchRequest.setTypes(getCenterTypes(centerTypeMap));
            }
            //centerSearchRequest.setType(centerTypeFilter);
            centerSearchRequest.setActivities(activities);
            centerSearchRequest.setRadius(radius);

            if (isWeb && hasLocalityList) {
                centerSearchRequest.setIncludeDetails(true);
                LocalityEntry localityEntry = this.serviceInterfaces.localityService.getLocalityById(localityIdList.get(0), Collections.emptyMap(), null);
                cityId = localityEntry.getCityId();
            }

            if (!isWeb || (!hasLocalityList && !hasQueryParamCoordinates)) {
                centerSearchRequest.setCity(cityId);
            }

            if (localityIdList != null && !localityIdList.isEmpty()) {
                centerSearchRequest.setLocalityIds(localityIdList);
            }

            centerSearchRequest.setSkuNames(skuNames);
            centerSearchRequest.setIncludeDetails(true);

            LocationPreference locationPreference = serviceInterfaces.localityProviderService.getUserLocationPreference(serviceInterfaces, userContext, cityId, userId);
            boolean isLocalityView = locationPreference == null || locationPreference.getLocality() != null;

            if (locationPreference != null) {
                LatLong latlong = serviceInterfaces.localityProviderService.getUserPreferenceLatLon(sessionInfo, locationPreference);
                if (latlong != null && latlong.getLatitude() != null && latlong.getLongitude() != null) {
                    latitude = latlong.getLatitude();
                    longitude = latlong.getLongitude();
                }
            }

            if (isWeb) {
                if (hasQueryParamCoordinates) {
                    centerSearchRequest.setLatitude((double) queryParamLatitude);
                    centerSearchRequest.setLongitude((double) queryParamLongitude);
                } else if (!hasLocalityList && cityId.isEmpty()) {
                    centerSearchRequest.setLatitude(Double.valueOf(latitude));
                    centerSearchRequest.setLongitude(Double.valueOf(longitude));
                }
            } else {
                if (latitude != null && longitude != null) {
                    centerSearchRequest.setLatitude(Double.valueOf(latitude));
                    centerSearchRequest.setLongitude(Double.valueOf(longitude));
                }
            }

            if (isSearchScreen) {
                if (searchText.length() != 0) {
                    unfilteredCenters = serviceInterfaces.centerService.searchCenters(centerSearchRequest, Collections.emptyMap(), null).get();
                    hasNextPage = unfilteredCenters != null && unfilteredCenters.size() == pageSize;
                } else {
                    unfilteredCenters = new ArrayList<>();
                }
            } else if (isWeb) {
                unfilteredCenters = serviceInterfaces.centerService.searchCenters(centerSearchRequest, Collections.emptyMap(), null).get();
                hasNextPage = unfilteredCenters != null && unfilteredCenters.size() == pageSize;

                if (hasQueryParamCoordinates) {
                    localityName = serviceInterfaces.localityProviderService.getLocationName(latitude, longitude, serviceInterfaces, GoogleAPIKeyOwner.OTHERS);
                } else if (!cityId.isEmpty()) {
                    localityName = serviceInterfaces.localityProviderService.getDeFaultLocality(cityId);
                }
            } else {
                if (locationPreference != null) {
                    if (locationPreference.getPreferredLocationType() == LocationPreferenceType.current_location && latitude != null && longitude != null) {
                        if (locationPreference.isUserPreference() && serviceInterfaces.localityProviderService.isCoordinateInCity(userContext, cityId, latitude, longitude, serviceInterfaces)) {
                            // Don't fetch location name for Members. (cost reduction measure)
                            if (AppUtil.isUserActiveCultpassMember(userContext, serviceInterfaces.environmentService)) {
                             localityName = "Around your location";
                            } else {
                               localityName = serviceInterfaces.localityProviderService.getLocationName(latitude, longitude, serviceInterfaces, GoogleAPIKeyOwner.GYM_CENTERS_PAGE);
                            }
                        } else {
                            localityName = serviceInterfaces.localityProviderService.getDeFaultLocality(cityId);
                        }
                    } else {
                        localityName = locationPreference.getLocationDisplayName();
                    }

                    if (locationPreference.getPreferredLocationType() == LocationPreferenceType.current_location || locationPreference.getPreferredLocationType() == LocationPreferenceType.coordinates) {
                        unfilteredCenters = serviceInterfaces.centerService.searchCenters(centerSearchRequest, Collections.emptyMap(), null).get();
                        hasNextPage = unfilteredCenters != null && unfilteredCenters.size() == pageSize;
                    } else if (isFlutterAllCentersPageSupported && locationPreference.getLocalityEntry() != null && locationPreference.getCoordinates() != null) {
                        unfilteredCenters = serviceInterfaces.centerService.searchCenters(centerSearchRequest, Collections.emptyMap(), null).get();
                        hasNextPage = unfilteredCenters != null && unfilteredCenters.size() == pageSize;
                    } else {
                        unfilteredCenters = getCentersList(serviceInterfaces, locationPreference, skuNames, userContext, isLuxGymsSupported);
                    }
                } else {
                    String defaultLocality = serviceInterfaces.localityProviderService.getDeFaultLocality(cityId);
                    LocalityEntry localityEntry = serviceInterfaces.localityProviderService.getLocalityFromName(cityId, defaultLocality);
                    if (isFlutterAllCentersPageSupported && localityEntry != null && localityEntry.getLatitude() != null && localityEntry.getLongitude() != null) {
                        centerSearchRequest.setLatitude(localityEntry.getLatitude());
                        centerSearchRequest.setLongitude(localityEntry.getLongitude());
                        centerSearchRequest.setSortBy(CenterSearchFilters.SortBy.DISTANCE);
                        unfilteredCenters = serviceInterfaces.centerService.searchCenters(centerSearchRequest, Collections.emptyMap(), null).get();
                        hasNextPage = unfilteredCenters != null && unfilteredCenters.size() == pageSize;
                    } else {
                        unfilteredCenters = getCentersByLocality(localityEntry, serviceInterfaces, skuNames);
                    }
                    localityName = defaultLocality;
                }
            }

            List<CenterEntry> centers = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(unfilteredCenters)) {
                centers = unfilteredCenters.stream().filter(center -> !center.getMeta().containsKey("isVirtualCenter") || !(Boolean) center.getMeta().get("isVirtualCenter")).collect(Collectors.toList());
            }

            if (!isCultPassPlaySupported) {
                centers = centers.stream().filter((center) -> !center.getVertical().equals(CenterVertical.PLAY)).collect(Collectors.toList());
            }

            if (isOnlyOnePass) {
                boolean isUserBelongToCenterLevelOnepassSegment = AppUtil.doesUserBelongToCenterLevelOnepassSegment(serviceInterfaces,serviceInterfaces.getEnvironmentService(),userContext);
                if (isUserBelongToCenterLevelOnepassSegment) {
                    centers = centers.stream().filter((center) -> 2594 == center.getId()).collect(Collectors.toList());
                    hasNextPage = centers != null && centers.size() == pageSize;
                } else {
                    centers = centers.stream().filter((center) -> 2594 != center.getId()).collect(Collectors.toList());
                }
            }

            if(isOnboardingFlow){
                List<Integer> gymOnboardingCentersList = gymOnboardingCentersConfigService.getPageConfigData();
                centers = centers.stream().filter((center) -> gymOnboardingCentersList.contains(center.getId().intValue())).collect(Collectors.toList());
            }

            boolean isTestUser = AppUtil.isUserPartOfTestSegment(RequestType.PLATFORM_SEGMENTS, userContext);
            if (!(isInternalUser || isTestUser)) {
                // whitelisting centers for internal users and test user segment
                if (centers != null && !centers.isEmpty()) {
                    centers = centers.stream()
                            .filter(center -> !isCenterEnabledForOnlyInternalUsers(center.getId()))
                            .collect(Collectors.toList());
                }
            }


            if (showPlayLiteOnly)
                hasNextPage= false;

            CentersPageView.Meta meta = new CentersPageView.Meta(hasNextPage, cityId);

            if (locationPreference != null && locationPreference.getPreferredLocationType() != null) {
                meta.setLocationPreference(locationPreference.getPreferredLocationType().name());
            }

            if (isWeb) {
                return centersPageViewBuilder.buildWebsiteView(serviceInterfaces, userContext, centers, meta, isSearchScreen, searchText, cityId, latitude, longitude, queryParamLatitude, queryParamLongitude, radius != null ? radius.intValue() : null, centerTypeMap, centerTypesList, activities, localityIdList, isCultPassPlaySupported);
            }

            return centersPageViewBuilder.buildView(
                serviceInterfaces,
                filterHybridGymCenter(centers, centerTypesList, true),
                meta,
                centerTypeMap,
                centerTypesList,
                isSearchScreen,
                isOnboardingFlow,
                latitude,
                longitude,
                localityName,
                isLocalityView,
                cityId,
                skuNames,
                pageNumber,
                scrollToCenterId,
                userContext,
                queryParams,
                isCultPassPlaySupported,
                isOnlyOnePass,
                isPlaySLPSupported
            );
        } catch (BaseException | IOException e) {
            log.error(String.format("Error in center service searchCenters call :: params : %s, error : %s", queryParams, e.getMessage()), e);
        }
        return new CentersPageView();
    }

    public NearbyCenterCreditsView getNearbyCreditsPageView(UserContext userContext, Map<String, String> queryParams, String[] centerTypes) throws Exception {
        try {
            String searchText = queryParams.getOrDefault("queryString", "");
            CenterSearchFilters.SortBy sortBy = queryParams.containsKey("sortBy") ? CenterSearchFilters.SortBy.valueOf((queryParams.get("sortBy"))) : CenterSearchFilters.SortBy.DISTANCE;
            Integer pageNumber = queryParams.containsKey("pageNumber") ? Integer.parseInt(queryParams.get("pageNumber")) : 0;
            Integer pageSize = queryParams.containsKey("pageSize") ? Integer.parseInt(queryParams.get("pageSize")) : 5;
            List<String> centerTypesList = centerTypes != null ? new LinkedList<>(Arrays.asList(centerTypes)) : new ArrayList<>();

            String userId = userContext.getUserProfile().getUserId();
            String cityId = userContext.getSessionInfo().getSessionData().getCityId();
            SessionInfo sessionInfo = userContext.getSessionInfo();

            Float latitude = sessionInfo.getLat();
            Float longitude = sessionInfo.getLon();
            String localityName = null;

            List<SkuName> skuNames = getSkuNames(false,false, false);

            boolean hasNextPage = false;
            List<CenterEntry> unfilteredCenters = new ArrayList<>();
            List<CenterStatus> statuses = new ArrayList<>();
            statuses.add(CenterStatus.ACTIVE);

            Map<CentersPageView.CenterTypeValue, Boolean> centerTypeMap = getCenterTypeFilterMap(centerTypesList, false);

            CenterSearchRequest centerSearchRequest = new CenterSearchRequest();
            centerSearchRequest.setSearchStr(searchText.trim().length() > 0 ? searchText.trim() : null);
            centerSearchRequest.setOffset(pageNumber * pageSize);
            centerSearchRequest.setLimit(pageSize);
            centerSearchRequest.setStatuses(statuses);
            centerSearchRequest.setEnrichWithMapDistance(true);
            centerSearchRequest.setSortBy(sortBy);
            if(centerTypesList.contains("HYBRID") && centerTypesList.size() == 1){
                centerSearchRequest.setCategories(List.of("HYBRID"));
            } else {
                centerSearchRequest.setTypes(getCenterTypes(centerTypeMap));
                centerSearchRequest.setSkuNames(getSkuNames(false, false, false));
            }
            centerSearchRequest.setIncludeDetails(true);
            centerSearchRequest.setCity(cityId);

            LocationPreference locationPreference = serviceInterfaces.localityProviderService.getUserLocationPreference(serviceInterfaces, userContext, cityId, userId);

            if (locationPreference != null) {
                LatLong latlong = serviceInterfaces.localityProviderService.getUserPreferenceLatLon(sessionInfo, locationPreference);
                if (latlong != null && latlong.getLatitude() != null && latlong.getLongitude() != null) {
                    latitude = latlong.getLatitude();
                    longitude = latlong.getLongitude();
                }
            }

            if (latitude != null && longitude != null) {
                centerSearchRequest.setLatitude(Double.valueOf(latitude));
                centerSearchRequest.setLongitude(Double.valueOf(longitude));
            }

            if (locationPreference != null) {
                if (locationPreference.getPreferredLocationType() == LocationPreferenceType.current_location && latitude != null && longitude != null) {
                    if (locationPreference.isUserPreference() && serviceInterfaces.localityProviderService.isCoordinateInCity(userContext, cityId, latitude, longitude, serviceInterfaces)) {
                        localityName = serviceInterfaces.localityProviderService.getLocationName(latitude, longitude, serviceInterfaces, GoogleAPIKeyOwner.NEARBY_CREDITS_PAGE);
                    } else {
                        localityName = serviceInterfaces.localityProviderService.getDeFaultLocality(cityId);
                    }
                } else {
                    localityName = locationPreference.getLocationDisplayName();
                }

                if (locationPreference.getPreferredLocationType() == LocationPreferenceType.current_location || locationPreference.getPreferredLocationType() == LocationPreferenceType.coordinates) {
                    unfilteredCenters = serviceInterfaces.centerService.searchCenters(centerSearchRequest, Collections.emptyMap(), null).get();
                } else if (locationPreference.getLocalityEntry() != null && locationPreference.getCoordinates() != null) {
                    unfilteredCenters = serviceInterfaces.centerService.searchCenters(centerSearchRequest, Collections.emptyMap(), null).get();
                }
            } else {
                localityName = serviceInterfaces.localityProviderService.getDeFaultLocality(cityId);
            }

            if (Objects.isNull(unfilteredCenters)) {
                unfilteredCenters = getCentersList(serviceInterfaces, locationPreference, skuNames, userContext, false);
            }

            hasNextPage = unfilteredCenters != null && unfilteredCenters.size() == pageSize;

            return centersPageViewBuilder.buildNearbyCreditsView(
                    serviceInterfaces,
                    filterCenters(unfilteredCenters),
                    centerTypeMap,
                    centerTypesList,
                    latitude,
                    longitude,
                    localityName,
                    userContext,
                    queryParams,
                    hasNextPage
            );
        } catch (BaseException | IOException e) {
            log.error(String.format("Error in center service searchCenters call :: params : %s, error : %s", queryParams, e.getMessage()), e);
        }
        return new NearbyCenterCreditsView();
    }

    public static Map<CentersPageView.CenterTypeValue, Boolean> getCenterTypeFilterMap(List<String> centerTypes, boolean isCultPassPlaySupported) {
        Map<CentersPageView.CenterTypeValue, Boolean> centerTypeFilterMap = new HashMap<>();
        boolean isCenterTypeSelected = centerTypes != null && !centerTypes.isEmpty();

        centerTypeFilterMap.put(CentersPageView.CenterTypeValue.GROUP_CLASS, !isCenterTypeSelected);
        centerTypeFilterMap.put(CentersPageView.CenterTypeValue.GYM, !isCenterTypeSelected);
        if (isCultPassPlaySupported) {
            centerTypeFilterMap.put(CenterTypeValue.SPORT, !isCenterTypeSelected);
        }

        if (centerTypes != null) {
            for (String centerType : centerTypes) {
                CenterTypeValue centerTypeValue = CenterTypeValue.valueOf(centerType);
                switch (centerTypeValue) {
                    case GROUP_CLASS -> centerTypeFilterMap.put(CenterTypeValue.GROUP_CLASS, true);
                    case GYM -> centerTypeFilterMap.put(CenterTypeValue.GYM, true);
                    case SPORT -> centerTypeFilterMap.put(CenterTypeValue.SPORT, true);
                    case ONEPASS -> centerTypeFilterMap.put(CenterTypeValue.ONEPASS, true);
                    case HYBRID -> centerTypeFilterMap.put(CenterTypeValue.HYBRID, true);
                }
            }
        }

        return centerTypeFilterMap;
    }


    private CenterSearchFilters.CenterType getCenterType(Map<CentersPageView.CenterTypeValue, Boolean> centerTypesMap) {
        CenterSearchFilters.CenterType centerTypeFilter = null;

        if (centerTypesMap.get(CenterTypeValue.GROUP_CLASS) && centerTypesMap.get(CenterTypeValue.GYM) && centerTypesMap.get(CenterTypeValue.SPORT) != null) {
        } else if (centerTypesMap.get(CenterTypeValue.GROUP_CLASS) && centerTypesMap.get(CenterTypeValue.GYM)) {
        } else if (centerTypesMap.get(CenterTypeValue.GROUP_CLASS)) {
            centerTypeFilter = CenterSearchFilters.CenterType.CULT_CENTER;
        } else if (centerTypesMap.get(CenterTypeValue.GYM)) {
            centerTypeFilter = CenterSearchFilters.CenterType.GYM;
        } else if (centerTypesMap.get(CenterTypeValue.SPORT)) {
            centerTypeFilter = CenterSearchFilters.CenterType.PLAY;
        }
        // TODO: 28/07/22 Add SPORT Filter here

        return centerTypeFilter;
    }

    public static List<CenterSearchFilters.CenterType> getCenterTypes(Map<CentersPageView.CenterTypeValue, Boolean> centerTypesMap) {
        List<CenterSearchFilters.CenterType> centerTypeFilterList = new ArrayList<>();

        for (Map.Entry<CenterTypeValue, Boolean> centerTypeValueBooleanEntry : centerTypesMap.entrySet()) {
            if (centerTypeValueBooleanEntry.getValue()){
                switch (centerTypeValueBooleanEntry.getKey()){
                    case GROUP_CLASS -> centerTypeFilterList.add(CenterSearchFilters.CenterType.CULT_CENTER);
                    case GYM -> centerTypeFilterList.add(CenterSearchFilters.CenterType.GYM);
                    case SPORT -> centerTypeFilterList.add(CenterSearchFilters.CenterType.PLAY);
                    case ONEPASS -> centerTypeFilterList.add(CenterSearchFilters.CenterType.ONEPASS);
                }
            }
        }

        return centerTypeFilterList;
    }

    private List<SkuName> getSkuNames(boolean isCultPassPlaySupported, boolean isLuxGymsSupported, boolean isOnlyOnePass) {
        List<SkuName> skuNames = new ArrayList<>();
        if (isOnlyOnePass) {
            skuNames.add(SkuName.ONEPASS);
            return skuNames;
        }
        skuNames.add(SkuName.GOLD);
        skuNames.add(SkuName.BLACK);
        if (isLuxGymsSupported) {
            skuNames.add(SkuName.LUX);
        }
        if (isCultPassPlaySupported) {
            skuNames.add(SkuName.PLAY);
        }
        return skuNames;
    }

    private static List<CenterEntry> filterCenters (List<CenterEntry> centers) {
        List<CenterEntry> filterHybridGymCenters = centers.stream().filter(item -> !CultUtil.isHybridGymCenter(item)).toList();
        List<CenterEntry> filterVirtualCenters = filterHybridGymCenters.stream().filter(center -> !center.getMeta().containsKey("isVirtualCenter") || !(Boolean) center.getMeta().get("isVirtualCenter")).collect(Collectors.toList());
        List<CenterEntry> filterNonActiveCenters = filterVirtualCenters.stream().filter(FitnessCenterUtil::isCenterActive).toList();
        return new ArrayList<>(filterNonActiveCenters);
    }


    public boolean isCenterEnabledForOnlyInternalUsers(Long centerId) {
        try {
            List<Long> centerWhitelistedForInternalUsers = AppUtil.testCenterIdsList(serviceInterfaces);
            if (!org.springframework.util.CollectionUtils.isEmpty(centerWhitelistedForInternalUsers)) {
                return centerWhitelistedForInternalUsers.contains(centerId);
            }
        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
        }
        return false;
    }

    public Action getGymOnboardingAction(UserContext userContext, Map<String,String> queryParams) {
        try{
            Integer centerId = null;
            CenterEntry centerEntry;
            centerEntry = GymPtUtil.getUserPTCenter(userContext, serviceInterfaces);
            if(centerEntry != null){
                centerId = centerEntry.getId().intValue();
            }
            List<Integer> gymOnboardingCentersList = null;

            try {
                gymOnboardingCentersList = gymOnboardingCentersConfigService.getPageConfigData();
            } catch (Exception e) {
                log.error("Error in fetching gymOnboardingCenters from pageConfigs: {}", e.getMessage());
                return new Action(ActionType.EMPTY_ACTION);
            }

            if(gymOnboardingCentersList == null || gymOnboardingCentersList.isEmpty()) {
                log.error("Error for userId: {} in api checkOnboardingCenter. gymOnboardingCenters list is null or empty", userContext.getUserProfile().getUserId());
                return new Action(ActionType.EMPTY_ACTION);
            }
            Action action = new Action(ActionType.NAVIGATION);
            if (centerId != null && gymOnboardingCentersList.contains(centerId)) {
                action.setUrl("curefit://pt_slot_selection?productId=pt_free_trial_product");
                action.setTitle("BOOK YOUR FIRST SESSION NOW");
            } else {
                action.setUrl("curefit://allgyms?centerType=GYM&isOnboardingFlow=true");
                action.setTitle("Select Center for Onboarding");
            }
            return action;
        } catch (Exception e) {
            log.error("Error for userId: {} in api checkOnboardingCenter: {}", userContext.getUserProfile().getUserId(), e.getMessage());
            return new Action(ActionType.EMPTY_ACTION);
        }
    }

}
