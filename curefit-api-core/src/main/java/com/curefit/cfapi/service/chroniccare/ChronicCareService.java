package com.curefit.cfapi.service.chroniccare;

import com.amazonaws.services.sns.AmazonSNS;
import com.amazonaws.services.sns.model.MessageAttributeValue;
import com.amazonaws.services.sns.model.PublishRequest;
import com.amazonaws.services.sns.model.PublishResult;
import com.curefit.albus.booking.ActiveConsultationResponse;
import com.curefit.albus.common.BundleProduct;
import com.curefit.albus.common.BundleSellableProduct;
import com.curefit.albus.common.BundleSubCategoryCode;
import com.curefit.albus.common.PatientPreferredAgentResponse;
import com.curefit.albus.request.AddPreferredDoctorRequest;
import com.curefit.albus.request.MembershipUpdateRequest;
import com.curefit.albus.request.UserCallbackRequest;
import com.curefit.albus.request.devicePhleboTask.InputFieldsConfig;
import com.curefit.albus.request.devicePhleboTask.PhleboMarkActionRequest;
import com.curefit.albus.request.devicePhleboTask.PhleboTaskSearchRequest;
import com.curefit.albus.request.inventory.ConsultationInventoryAvailabilityRequestParams;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.*;
import com.curefit.albus.response.actions.UserPreferencePojo;
import com.curefit.albus.response.chronic.UserOnboardingActionWithContext;
import com.curefit.albus.response.device.PhleboActions;
import com.curefit.albus.response.device.PhleboTask;
import com.curefit.albus.response.device.PhlebotomistUser;
import com.curefit.albus.service.AlbusClient;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.service.EnvironmentService;
import com.curefit.cf.commons.pojo.PagedResultEntry;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.dto.*;
import com.curefit.cfapi.dto.sugarfit.*;
import com.curefit.cfapi.dto.sugarfit.SfGenericSuccessModal.SfInstruction;
import com.curefit.cfapi.dto.sugarfit.thingstodo.ThingsToDoCard;
import com.curefit.cfapi.model.internal.chroniccare.*;
import com.curefit.cfapi.model.internal.chroniccare.sfbadges.SfBadgeEntry;
import com.curefit.cfapi.model.internal.exception.BadRequestException;
import com.curefit.cfapi.model.internal.userinfo.SugarfitAppLanguagePreference;
import com.curefit.cfapi.model.internal.userinfo.TabType;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.userinfo.UserProfile;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionMeta;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.chroniccare.*;
import com.curefit.cfapi.pojo.chroniccare.UserGoalBody.UserGoalData;
import com.curefit.cfapi.pojo.chroniccare.actvitylog.*;
import com.curefit.cfapi.pojo.chroniccare.challenges.SfChallengeEntrySubmissionData;
import com.curefit.cfapi.pojo.chroniccare.challenges.SfChallengeUserEntry;
import com.curefit.cfapi.pojo.chroniccare.daNux.DANuxDataUpdatePayload;
import com.curefit.cfapi.pojo.chroniccare.diagnosticteststore.SfDiagnosticStoreTest;
import com.curefit.cfapi.pojo.chroniccare.experiencecenter.SfExperienceCenterKSSaveRequest;
import com.curefit.cfapi.pojo.chroniccare.freemium.FreemiumItemsRequest;
import com.curefit.cfapi.pojo.chroniccare.support.SfPollAnswer;
import com.curefit.cfapi.pojo.chroniccare.support.SfSupportTicket;
import com.curefit.cfapi.pojo.chroniccare.support.SfSupportTicketReply;
import com.curefit.cfapi.pojo.chroniccare.support.SfSupportTicketRequest;
import com.curefit.cfapi.pojo.chroniccare.support.automation.CSChat;
import com.curefit.cfapi.pojo.chroniccare.support.automation.CSConversation;
import com.curefit.cfapi.pojo.chroniccare.support.automation.TicketReplyRequest;
import com.curefit.cfapi.pojo.vm.items.InstructionItem;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.activitylog.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.activitylog.meallogging.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.blucon.SfBluconDetailsPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.challenges.SfChallengeDetailsPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.challenges.SfChallengeLeaderboardPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.challenges.SfChallengesListPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.diagnosticstoretests.SfDiagnosticStoreTestPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.diagnosticstoretests.SfDiagnosticTDPBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.digitalapp.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.ecommerce.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.experiencecenter.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.experiencecentre.FBVExperienceHistoryPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.medicalRecords.ConsultationDetailPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.medicalRecords.ConsultationsPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.medicalRecords.ReportsPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.nux.NuxPreferencesPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.nux.PackStartDateSelectionPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.nux.SetupJourneyPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.polls.SfPollsPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.SfCoachCelebrationPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.SfRenewalUserReportPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.renewal.SfUserReportCongratsPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.sales.SfMegaSalesCLPBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.smartglucometer.SfSmartGlucometerDashboardPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.smartglucometer.SfSmartGlucometerOnboardingPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.smartglucometer.SfSmartGlucometerSettingsPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.smartglucometer.SfSmartGlucometerTimelinePageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.smartscale.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.support.SfSupportFAQsPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.support.SfSupportPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.support.SfSupportTicketsListPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.support.SfTicketsDetailsPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.support.automation.SfCSConversationPageBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.thingstodo.ThingsToDoCardsBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.vitals.SfVitalMetricDetailsPageV2Builder;
import com.curefit.cfapi.view.viewbuilders.digitalApp.DAFreemiumNuxPageViewBuilder;
import com.curefit.cfapi.view.viewbuilders.fbv.FBVOnboardingPageViewBuilder;
import com.curefit.cfapi.view.viewmodels.SupportPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.ActivityCustomDishPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.ActivityMealFavouritesPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.ActivityMealLogPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.ActivitySearchPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.activitylog.meallogging.MealActivitySearchPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.blucon.SfBluconDetailsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.challenges.SfChallengeDetailsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.challenges.SfChallengeLeaderboardPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.challenges.SfChallengesListPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.diagnosticstoretest.SfDiagnosticTDPView;
import com.curefit.cfapi.view.viewmodels.chroniccare.diagnosticstoretest.SfDiagnosticTestInstructionView;
import com.curefit.cfapi.view.viewmodels.chroniccare.diagnosticstoretest.SfDiagnosticTestSuccessPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.diagnosticstoretest.SfDiagnosticTestSuccessPageViewBuilder;
import com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.ecommerce.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.experiencecenter.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.experiencecentre.FBVExperienceHistoryPage;
import com.curefit.cfapi.view.viewmodels.chroniccare.medicalRecords.SfConsultationDetailPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.medicalRecords.SfConsultationsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.medicalRecords.SfReportsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.nux.NuxPreferencesPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.nux.PackStartDateSelection;
import com.curefit.cfapi.view.viewmodels.chroniccare.nux.SetupJourneyPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.phleboApp.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.polls.SfPollsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.renewal.SfCoachCelebrationPage;
import com.curefit.cfapi.view.viewmodels.chroniccare.renewal.SfRenewalUserReportPage;
import com.curefit.cfapi.view.viewmodels.chroniccare.renewal.SfUserReportCongratsPage;
import com.curefit.cfapi.view.viewmodels.chroniccare.sales.SfMegaSalesCLP;
import com.curefit.cfapi.view.viewmodels.chroniccare.sfliteapp.SfLiteAppHomePageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.smartglucometer.SmartGlucometerDashboardPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.smartglucometer.SmartGlucometerOnboardingPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.smartglucometer.SmartGlucometerTimelinePageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.smartscale.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.support.SfSupportPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.support.SfSupportTicketsListPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.support.SfTicketDetailsPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.support.automation.SfCSConversationPageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.vitals.SfVitalMetricDetailsPageViewV2;
import com.curefit.cfapi.view.viewmodels.digitalApp.DAFreemiumNuxPageView;
import com.curefit.cfapi.view.viewmodels.fbv.FBVOnboardingRequest;
import com.curefit.cfapi.view.viewmodels.fbv.FBVOnboardingResponse;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.*;
import com.curefit.cfapi.widgets.chroniccare.activitylog.SfActivityDishItemWidget;
import com.curefit.cfapi.widgets.chroniccare.challenges.SfChallengeCardWidget;
import com.curefit.cfapi.widgets.chroniccare.ecommerce.SfEComThingsToBuyListWidget;
import com.curefit.cfapi.widgets.chroniccare.freemium.FreemiumHomeTrendingEventWidget;
import com.curefit.cfapi.widgets.chroniccare.freemium.FreemiumSearchHistoryItemWidget;
import com.curefit.cfapi.widgets.chroniccare.freemium.FreemiumSearchTabWidget;
import com.curefit.cfapi.widgets.chroniccare.freemiumdiscover.SfDiscoverBlogCardWidget;
import com.curefit.cfapi.widgets.chroniccare.sfexperience.SfExperienceFBVWidget;
import com.curefit.cfapi.widgets.chroniccare.support.SfSupportRecentTicketsWidget;
import com.curefit.cfapi.widgets.chroniccare.wellness.SfWellnessLiveSessionsWidgetView;
import com.curefit.cfapi.widgets.common.NowLiveWidgetView;
import com.curefit.cfapi.widgets.digital.NowLiveSessionWidget;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.common.data.model.entity.Gender;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.client.pojo.HttpRequestDetail;
import com.curefit.commons.client.restTemplate.RestTemplateClient;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.commons.util.Serializer;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.converse.VideoRoomClient;
import com.curefit.converse.pojo.RoomMetricPojos.RoomMetricsRequest;
import com.curefit.diyfs.pojo.DIYFilterRequestV2;
import com.curefit.diyfs.pojo.LiveClass;
import com.curefit.diyfs.pojo.enums.PreferredStreamType;
import com.curefit.diyfs.pojo.enums.SubscriptionStatus;
import com.curefit.ehr.client.EHRClient;
import com.curefit.ehr.enums.PatientActivityType;
import com.curefit.ehr.exception.EHRClientException;
import com.curefit.iris.models.NotificationMeta;
import com.curefit.math.enums.DataType;
import com.curefit.metricservice.enums.MeasurementType;
import com.curefit.metricservice.exceptions.MetricClientException;
import com.curefit.metricservice.models.UserMetricValue;
import com.curefit.metricservice.pojo.UserMetricValueWithRangeLabel;
import com.curefit.ollivander.client.agent.OllivanderAgentClient;
import com.curefit.ollivander.client.center.OllivanderCenterClient;
import com.curefit.ollivander.common.exception.OllivanderClientException;
import com.curefit.ollivander.common.pojo.request.center.CenterSearchRequestParam;
import com.curefit.ollivander.common.pojo.response.agent.AgentResponse;
import com.curefit.ollivander.common.pojo.response.center.CenterResponseV2;
import com.curefit.plan.models.PatientInterventionResponse;
import com.curefit.plan.models.ScheduledInterventionSearchRequest;
import com.curefit.plan.models.ScheduledPlanItem;
import com.curefit.pojo.DateRange;
import com.curefit.pojo.PatientActivityLoggingRequest;
import com.curefit.pojo.PatientActivityLoggingResponse;
import com.curefit.pojo.PatientActivitySearchRequest;
import com.curefit.polaris.PolarisServiceClient;
import com.curefit.polaris.exception.PolarisClientException;
import com.curefit.product.enums.ProductType;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.enums.UserEventType;
import com.curefit.rashi.pojo.UserAttributeEntry;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.rashi.pojo.UserEventEntry;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.sfalbus.enums.PhleboTaskCategory;
import com.curefit.sfalbus.request.AgentSearchDetails;
import com.curefit.sfalbus.request.PatientActivityUpdateRequest;
import com.curefit.sfalbus.request.phlebo_task.PhleboLocation;
import com.curefit.sfalbus.request.phlebo_task.UserPhleboTaskBookingRequest;
import com.curefit.sfalbus.request.phlebo_task.UserPhleboTaskCancelRequest;
import com.curefit.sfalbus.request.phlebo_task.UserPhleboTaskRescheduleRequest;
import com.curefit.sfalbus.response.CGMStat;
import com.curefit.shifu.pojo.UserActivityEntry;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.userservice.client.UserServiceClient;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.curefit.userservice.pojo.response.UsersResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.sugarfit.ambrosia.ActivityComparisonRequest;
import com.sugarfit.ambrosia.client.AmbrosiaClient;
import com.sugarfit.ambrosia.enums.DishLogType;
import com.sugarfit.ambrosia.pojo.*;
import com.sugarfit.ambrosia.pojo.external.PatientActivityLoggingCompareEntry;
import com.sugarfit.ambrosia.pojo.external.UserActivityBulkFilterRequest;
import com.sugarfit.ambrosia.pojo.masterData.FoodgroupEntry;
import com.sugarfit.ambrosia.pojo.masterData.MeasurementUnitEntry;
import com.sugarfit.ambrosia.pojo.pdf.UserMealPlanPdfResponse;
import com.sugarfit.catalog.client.CatalogClient;
import com.sugarfit.catalog.pojo.BlogDetailedResponse;
import com.sugarfit.catalog.pojo.Entry.UserInteractionEntry;
import com.sugarfit.catalog.pojo.GenericVideoData;
import com.sugarfit.catalog.pojo.request.InfoBitesFilterRequest;
import com.sugarfit.catalog.pojo.request.UserChapterTrackingRequest;
import com.sugarfit.catalog.pojo.response.ExperiencePageCGMDataResponse;
import com.sugarfit.catalog.pojo.response.InfoBitesResponse;
import com.sugarfit.challenges.client.ChallengesClient;
import com.sugarfit.challenges.enums.ChallengeAudienceType;
import com.sugarfit.challenges.enums.ChallengeType;
import com.sugarfit.challenges.enums.RankEligibilityType;
import com.sugarfit.challenges.pojo.*;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.enums.*;
import com.sugarfit.chs.pojo.BaseResponse;
import com.sugarfit.chs.pojo.DefaultSuccessResponse;
import com.sugarfit.chs.pojo.*;
import com.sugarfit.chs.pojo.badge.UserBadgeProgressEntry;
import com.sugarfit.chs.pojo.blucon.BluconEventEntry;
import com.sugarfit.chs.pojo.careplix.CareplixFaceBasedVitalsRequest;
import com.sugarfit.chs.pojo.careplix.CareplixScanStatusUpdateRequest;
import com.sugarfit.chs.pojo.cgminsights.BgInsightDetailedResponse;
import com.sugarfit.chs.pojo.cgminsights.BgInsightDetailsFilterRequest;
import com.sugarfit.chs.pojo.cgminsights.BgInsightsSummary;
import com.sugarfit.chs.pojo.cgminsights.BgInsightsSummaryRequest;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalScansForDayResponse;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalUploadRequest;
import com.sugarfit.chs.pojo.faceBasedVitals.FaceBasedVitalsRequest;
import com.sugarfit.chs.pojo.fitnessData.CalorieData;
import com.sugarfit.chs.pojo.fitnessData.HeartRateData;
import com.sugarfit.chs.pojo.fitnessData.RawHealthDataEntry;
import com.sugarfit.chs.pojo.fitnessData.StepsData;
import com.sugarfit.chs.pojo.glucometerReadings.GlucometerReadingEntry;
import com.sugarfit.chs.pojo.glucometerReadings.GlucometerReadingFilterRequest;
import com.sugarfit.chs.pojo.mbscore.MetabolicScoreResponse;
import com.sugarfit.chs.pojo.smartScale.SmartScaleDataUploadRequest;
import com.sugarfit.chs.pojo.smartScale.SmartScaleDeleteLogRequest;
import com.sugarfit.chs.pojo.userProfile.KickstarterAssessment;
import com.sugarfit.chs.pojo.userProfile.UserProfileEntry;
import com.sugarfit.chs.pojo.userProfile.UserProfileRequest;
import com.sugarfit.chs.pojo.userProfile.UserProfileResponse;
import com.sugarfit.customersupport.client.CustomerSupportClient;
import com.sugarfit.customersupport.pojo.SessionReplyRequest;
import com.sugarfit.customersupport.pojo.SupportChatEntry;
import com.sugarfit.customersupport.pojo.SupportConversationEntry;
import com.sugarfit.experiment.client.ExperimentClient;
import com.sugarfit.experiment.pojo.*;
import com.sugarfit.experiment.pojo.result.UserExperimentResult;
import com.sugarfit.fitness.client.PlanClient;
import com.sugarfit.fitness.client.SFFitnessClient;
import com.sugarfit.fitness.enums.DistanceUnit;
import com.sugarfit.fitness.enums.Level;
import com.sugarfit.fitness.pojo.CultClassAccessMapEntry;
import com.sugarfit.fitness.pojo.UserSessionLogEntry;
import com.sugarfit.fitness.pojo.UserSessionLogRequest;
import com.sugarfit.freshdesk.pojo.Attachments;
import com.sugarfit.freshdesk.pojo.*;
import com.sugarfit.indus.IndusClient;
import com.sugarfit.indus.pojo.OrderCart;
import com.sugarfit.indus.reponse.OfferResponse;
import com.sugarfit.indus.reponse.OrderDetailedResponse;
import com.sugarfit.indus.reponse.ProductResponse;
import com.sugarfit.indus.reponse.ServiceabilityCheckResponse;
import com.sugarfit.indus.request.ProductSearchRequest;
import com.sugarfit.indus.request.ReviewRequest;
import com.sugarfit.lexicon.client.LexiconClient;
import com.sugarfit.lexicon.request.*;
import com.sugarfit.lexicon.response.*;
import com.sugarfit.lms.dtos.*;
import com.sugarfit.lms.entry.LMSClient;
import com.sugarfit.lms.entry.LeadEntry;
import com.sugarfit.lms.entry.ReferralClient;
import com.sugarfit.lms.entry.WebinarSessionEventsEntry;
import com.sugarfit.lms.enums.WebinarSessionEventType;
import com.sugarfit.lms.referral.entry.*;
import com.sugarfit.lms.referral.pojo.ReferralSummary;
import com.sugarfit.lms.request.CustomRenewalPackRequest;
import com.sugarfit.lms.request.RPOrderCreateRequest;
import com.sugarfit.lms.request.RPOrderStatusUpdateRequest;
import com.sugarfit.lms.response.RPOrderCreateResponse;
import com.sugarfit.logging.client.LoggingClient;
import com.sugarfit.logging.enums.*;
import com.sugarfit.logging.pojo.BaseActivityLogEntry;
import com.sugarfit.logging.pojo.MedicineActivityLogEntry;
import com.sugarfit.logging.pojo.request.*;
import com.sugarfit.logging.pojo.response.*;
import com.sugarfit.medicine.client.SFMedicineClient;
import com.sugarfit.medicine.pojo.MedicineEntry;
import com.sugarfit.medicine.pojo.StaticData;
import com.sugarfit.medicine.pojo.prescription.Duration;
import com.sugarfit.medicine.pojo.prescription.*;
import com.sugarfit.nest.client.MasterClassClient;
import com.sugarfit.nest.client.NestClient;
import com.sugarfit.nest.client.SFChatClient;
import com.sugarfit.nest.enums.ContentType;
import com.sugarfit.nest.enums.UserType;
import com.sugarfit.nest.pojo.*;
import com.sugarfit.nest.request.SmartBotPostEntry;
import com.sugarfit.poll.client.PollSupportClient;
import com.sugarfit.poll.enums.DistressType;
import com.sugarfit.poll.pojo.CompletePollEntry;
import com.sugarfit.poll.pojo.PollEntry;
import com.sugarfit.poll.pojo.QuestionEntry;
import com.sugarfit.poll.pojo.VoteEntry;
import com.sugarfit.sleep.client.SleepClient;
import com.sugarfit.sms.client.SMSClient;
import com.sugarfit.sms.common.GenericListNuxDataEntry;
import com.sugarfit.sms.common.GenericNuxDataEntry;
import com.sugarfit.sms.entry.UserTaskResponseEntry;
import com.sugarfit.sms.enums.NUXProfile;
import com.sugarfit.sms.enums.RenewalActionType;
import com.sugarfit.sms.enums.UserTodoTaskType;
import com.sugarfit.sms.pojo.renewal_journey.RenewalActionPayload;
import com.sugarfit.sms.pojo.renewal_journey.ReportCardPDFResponse;
import com.sugarfit.sms.request.NUXProfileUpdateRequest;
import com.sugarfit.sms.response.NUXStatusResponse;
import com.sugarfit.sms.response.UserTodoDaySummary;
import com.sugarfit.sms.response.UserTodoResponse;
import com.sugarfit.talktube.TalktubeClient;
import com.sugarfit.talktube.enums.ParticipantType;
import com.sugarfit.talktube.pojo.*;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.logging.log4j.util.Strings;
import org.eclipse.jetty.util.ajax.JSON;
import org.joda.time.DateTime;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import request.VitalAddData;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.curefit.cfapi.pojo.chroniccare.SfOpsRequestType.REQUEST_DIAGNOSTICS;
import static com.curefit.cfapi.util.AppUtil.getAppTenantFromUserContext;
import static com.curefit.cfapi.util.AppUtil.isUltraFitApp;
import static com.curefit.cfapi.util.ChronicCareAppUtil.*;
import static com.curefit.cfapi.util.DigitalAppUtil.DIGITAL_FTUE_SEEN_ATTRIBUTE;
import static com.curefit.cfapi.util.DigitalAppUtil.*;
import static com.curefit.cfapi.util.SfBadgeUtils.getLogActionFromBadgeCategory;
import static com.curefit.cfapi.util.TimeUtil.DEFAULT_ZONE_ID;
import static com.curefit.cfapi.util.TimeUtil.IST_TIMEZONE;
import static com.curefit.cfapi.view.viewbuilders.chroniccare.HealthPageViewBuilder.CHS_GLUCOSE_METRIC_NAME;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ChronicCareService {
    public static final String MEMBERSHIP_UPDATE_REASON = "customer-update-through-app";
    private static final String VOICE_BASED_MEAL_LOGGING_ENABLED_USERS = "voice_based_meal_logging_enabled_users";
    static final int PAGE_SIZE = 10;
    static final Double defaultConsultPrice = 600.0;
    static final String SOURCE_REF_ID = "SUGAR_FIT_APP";
    static final String TRIAL_PRODUCTCODE = "SUGARFIT_TRIAL";
    static final String LSQ_LEAD_CAPTURE_API = "/v2/LeadManagement.svc/Lead.Capture";
    static final String DEFAULT_TIME = "00:00";
    private final int SIX_HOURS_IN_SECONDS = 60 * 60 * 6;
    private static int TIMEZONE_OFFSET = 330 * 60 * 1000; // 5 hour 30min
    static final List<MeasurementUnit> DEFAULT_DIET_UNITS = Collections.unmodifiableList(Stream.of("g", "serving", "ml",
                    "piece", "units", "teaspoon", "cup", "glass", "katori", "bowl", "teabag", "slice")
            .map(x -> {
                MeasurementUnit result = new MeasurementUnit();
                result.setUnit(x);
                return result;
            }).collect(Collectors.toList()));
    private static final long MILLIS_IN_A_DAY = 24 * 60 * 60 * 1000;
    private static final String TENANT = "SUGARFIT";
    private static final long WEIGHT_METRIC_ID = 3L;
    final ServiceInterfaces serviceInterfaces;
    final CHSClient chsClient;

    final PlanClient planClient;
    final AlbusClient albusClient;
    final EHRClient ehrClient;
    final PolarisServiceClient polarisServiceClient;
    final ReferralClient referralClient;
    final LMSClient lmsClient;
    final AmbrosiaClient ambrosiaClient;
    final SFFitnessClient sfFitnessClient;
    final SFMedicineClient sfMedicineClient;
    final SleepClient sfSleepClient;
    final LoggingClient sfLoggingClient;
    final ExperimentClient experimentClient;
    final CatalogClient catalogClient;
    final RashiClient rashiClient;
    final AmazonSNS sns;
    final SMSClient smsClient;
    final SFChatClient chatClient;
    final NestClient nestClient;
    final TalktubeClient talktubeClient;
    final MasterClassClient masterClassClient;
    final OllivanderAgentClient ollivanderAgentClient;
    final OllivanderCenterClient ollivanderCenterClient;
    final UserServiceClient userServiceClient;
    final RestTemplateClient restTemplateClient;
    final CustomerSupportClient customerSupportClient;
    final PollSupportClient pollSupportClient;
    final ChallengesClient challengesClient;
    final VideoRoomClient videoRoomClient;
    final PhoneUtil phoneUtil;
    final ChronicCarePatientService chronicCarePatientService;
    final EnvironmentService environmentService;
    final ChronicCarePurchasePageViewBuilder chronicCarePurchasePageViewBuilder;
    final SugarJournalReadingsPageViewBuilder sugarJournalReadingsPageViewBuilder;
    final SugarJournalInsightsPageViewBuilder sugarJournalInsightsPageViewBuilder;
    final ChronicCareHomePageViewBuilder chronicCareHomePageViewBuilder;
    final SfHomePageViewBuilder homePageViewBuilder;
    final DigiHomePageBuilder digiHomePageBuilder;
    final SfHomePageViewBuilderV2 homePageViewBuilderV2;
    final SfLiteAppHomePageBuilder sfLiteAppHomePageBuilder;
    final PheloHomepageViewBuilder pheloHomepageViewBuilder;
    final AgmInsightPageViewBuilder agmInsightPageViewBuilder;
    final AgmInsightPageViewBuilderV2 agmInsightPageViewBuilderV2;
    final HamburgerMenuBuilder hamburgerMenuBuilder;
    final ServiceabilityFormBuilder serviceabilityFormBuilder;
    final PreferenceFormBuilder preferenceFormBuilder;
    final PreferenceFormBuilderV2 preferenceFormBuilderv2;
    final SfCGMInstallationBookingPageViewBuilder cgmInstallationBookingPageViewBuilder;
    final CongratsPageViewBuilder congratsPageViewBuilder;
    final SfDiagnosticTestSuccessPageViewBuilder sfDiagnosticTestSuccessPageViewBuilder;
    final ProductDescriptionPageViewBuilder productDescriptionPageViewBuilder;
    final UserOnboardingService userOnboardingService;
    final PrePurchasePromisePageViewBuilder prePurchasePromisePageViewBuilder;
    final DiabetesTypeSelectionPageViewBuilder diabetesTypeSelectionPageViewBuilder;
    final FoodGroupImageConfig foodGroupImageConfig;
    final GoalsHomePageViewBuilder goalsHomePageViewBuilder;
    final GoalsUpdatePageViewBuilder goalsUpdatePageViewBuilder;
    final GoalsHabitPageViewBuilder goalsHabitPageViewBuilder;
    final ExceptionReportingService exceptionReportingService;
    final ChronicCareServiceHelper chronicCareServiceHelper;
    final ActivityLogsPageViewBuilder activityLogsPageViewBuilder;
    final ActivityLogsPageViewBuilderV2 activityLogsPageViewBuilderV2;
    final ActivityLogsPageViewBuilderV3 activityLogsPageViewBuilderV3;
    final UserAttributesClient userAttributesClient;
    final SubscriptionPageViewBuilder subscriptionPageViewBuilder;
    final RenewSubscriptionPageViewBuilder renewSubscriptionPageViewBuilder;
    final RenewalJourneyPacksPageViewBuilder renewalJourneyPacksPageViewBuilder;
    final RenewSubscriptionSuccessPageViewBuilder renewSubscriptionSuccessPageViewBuilder;
    final SuccessPageViewBuilder successPageViewBuilder;
    final ActivitySearchPageViewBuilder activitySearchPageViewBuilder;
    final SfMealActivitySearchPageViewBuilder mealActivitySearchPageViewBuilder;
    final ActivityMealLogPageViewBuilder activityMealLogPageViewBuilder;
    final ActivityCustomDishPageViewBuilder activityCustomDishPageViewBuilder;
    final ActivityMealFavouritesPageBuilder activityMealFavouritesPageBuilder;
    final ActivityFitnessLogPageViewBuilder activityFitnessLogPageViewBuilder;
    final ActivityMedicineLogPageViewBuilder activityMedicineLogPageViewBuilder;
    final VitalPageViewBuilder vitalPageViewBuilder;
    final HealthPageViewBuilder healthPageViewBuilder;
    final ImpactPageViewBuilder impactPageViewBuilder;
    final ImpactCompareResultPageViewBuilder impactCompareResultPageViewBuilder;
    final CGMTimelinePageViewBuilder cgmTimelinePageViewBuilder;
    final ExperimentCollectionPageViewBuilder experimentCollectionPageViewBuilder;
    final MyExperimentsPageViewBuilder myExperimentsPageViewBuilder;
    final OngoingExperimentsViewBuilder ongoingExperimentsViewBuilder;

    final CGMScoredMealsPageViewBuilder cgmScoredMealsPageViewBuilder;
    final ChronicCareNutritionPageViewBuilder chronicCareNutritionPageViewBuilder;
    final ExperimentResultPageViewBuilder experimentResultPageViewBuilder;
    final FitnessDevicesPageViewBuilder fitnessDevicesPageViewBuilder;
    final SupportPageViewBuilder supportPageViewBuilder;
    final SugarLoggingPageViewBuilder sugarLoggingPageViewBuilder;
    final SugarJournalV2PageViewBuilder sugarJournalV2PageViewBuilder;

    final CgmStorePageViewBuilder cgmStorePageViewBuilder;
    final ReferralInterstitialConfigBuilder referralInterstitialConfigBuilder;
    final ConsultationsPageViewBuilder consultationsPageViewBuilder;
    final ConsultationDetailPageViewBuilder consultationDetailPageViewBuilder;
    final ReportsPageViewBuilder reportsPageViewBuilder;
    final OrdersPageViewBuilder ordersPageViewBuilder;
    final NutrientContributorPageViewBuilder nutrientContributorPageViewBuilder;
    final NutritionLogsPageViewBuilder nutritionLogsPageViewBuilder;
    final FBVScanPageViewBuilder fbvScanPageViewBuilder;
    final FaceBasedVitalLogsPageViewBuilder faceBasedVitalLogsPageViewBuilder;
    final FBVExperienceHistoryPageViewBuilder fbvExperienceHistoryPageViewBuilder;
    final FaceBasedVitalTrendsPageViewBuilder faceBasedVitalTrendsPageViewBuilder;
    final FitnessJournalV2PageViewBuilder fitnessJournalV2PageViewBuilder;
    final FreemiumNuxPageViewBuilder freemiumNuxPageViewBuilder;
    final DAFreemiumNuxPageViewBuilder daFreemiumNuxPageViewBuilder;
    final FreemiumOnboardingPageViewBuilder freemiumOnboardingPageViewBuilder;
    final SfFreemiumHomePageViewBuilder freemiumHomePageViewBuilder;
    final SfAccreditationsPageViewBuilder accreditationsPageViewBuilder;
    final SfTestimonialsPageViewBuilder testimonialsPageViewBuilder;
    final FreemiumPackListPageViewBuilder freemiumPackListPageViewBuilder;
    final SfExperiencePageViewBuilder sfExperiencePageViewBuilder;
    final CommunityPageViewBuilder communityPageViewBuilder;
    final FreemiumDiscoverPageViewBuilder freemiumDiscoverPageViewBuilder;
    final SfFreemiumBlogDetailsPageViewBuilder freemiumBlogDetailsPageViewBuilder;
    final SfFreemiumBlogListPageViewBuilder freemiumBlogListPageViewBuilder;
    final SugarFitBlogWidgetBuilder sugarFitBlogWidgetBuilder;
    final MyQuestionsPageViewBuilder myQuestionsPageViewBuilder;
    final AnswersPageViewBuilder answersPageViewBuilder;
    final FreemiumSearchPageViewBuilder freemiumSearchPageViewBuilder;
    final BadgesPageViewBuilder badgesPageViewBuilder;
    final SfSupportTicketsListPageBuilder sfSupportTicketsListPageBuilder;
    final SfTicketsDetailsPageBuilder sfTicketsDetailsPageBuilder;
    final SfSupportPageBuilder sfSupportPageBuilder;
    final SfPollsPageBuilder sfPollsPageBuilder;
    final SfSlotBookingPageViewBuilder sfSlotBookingPageViewBuilder;
    final SfConsultationSlotBookingPageViewBuilder sfConsultationSlotBookingPageViewBuilder;
    final SfChallengesListPageBuilder sfChallengesListPageBuilder;
    final SfPollSurveyBuilder sfPollSurveyBuilder;
    final SfRecommendedFitnessPlanWidgetBuilder sfRecommendedFitnessPlanWidgetBuilder;
    final SfRecommendedFitnessItemWidgetBuilder sfRecommendedFitnessItemWidgetBuilder;
    final SfChallengeDetailsPageBuilder sfChallengeDetailsPageBuilder;
    final SfChallengeLeaderboardPageBuilder sfChallengeLeaderboardPageBuilder;
    final PackStartDateSelectionPageBuilder packStartDateSelectionPageBuilder;
    final NuxPreferencesPageViewBuilder nuxPreferencesPageViewBuilder;
    final SetupJourneyPageViewBuilder setupJourneyPageViewBuilder;
    final SfNoShowPenaltyBuilder sfNoShowPenaltyBuilder;
    final SfCSConversationPageBuilder csConversationPageBuilder;
    final SfBluconDetailsPageBuilder sfBluconDetailsPageBuilder;
    final SfDiagnosticStoreTestPageBuilder sfDiagnosticStoreTestPageBuilder;
    final SfMealActivityDetailPageViewBuilder mealActivityDetailPageViewBuilder;
    final SfMealActivitySlotsListWidgetBuilder mealActivitySlotsListWidgetBuilder;
    final SfECommercePLPBuilder sfEcommercePLPBuilder;
    final SfECommercePDPBuilder sfECommercePDPBuilder;
    final SfECommercePDPV2Builder sfECommercePDPV2Builder;
    final SfDiagnosticTDPBuilder sfDiagnosticTDPBuilder;
    final SfECommerceWebPDPBuilder sfECommerceWebPDPBuilder;
    final SfECommerceProductGroupBuilder sfECommerceProductGroupBuilder;
    final SfECommerceOrderDetailsBuilder sfECommerceOrderDetailsBuilder;
    final SfECommerceOrdersPageBuilder sfEcommerceOrdersPageBuilder;
    final SfECommerceOrderSuccessPageBuilder sfECommerceOrderSuccessPageBuilder;
    final SfECommerceThingsToBuyWidgetBuilder sfECommerceThingsToBuyWidgetBuilder;
    final SfExperienceCenterCLPBuilder sfExperienceCenterCLPBuilder;
    final SfExperienceCenterSelectPageBuilder sfExperienceCenterSelectPageBuilder;
    final SfCoachCelebrationPageBuilder coachCelebrationPageBuilder;
    final SfRenewalUserReportPageBuilder renewalUserReportPageBuilder;
    final SfUserReportCongratsPageBuilder userReportCongratsPageBuilder;
    final SfFlashbackReportPageBuilder flashbackReportPageBuilder;
    final SfFlashbackCongratsPageBuilder flashbackCongratsPageBuilder;
    final SfWellnessAtCenterCLPBuilder wellnessAtCenterCLPBuilder;
    final SfWellnessAtCenterPDPBuilder wellnessAtCenterPDPBuilder;
    final SfWellnessCenterBookingsPageBuilder wellnessCenterBookingsPageBuilder;
    final SfWellnessAtCenterPrePurchasePageBuilder wellnessAtCenterPrePurchasePageBuilder;
    final SfMegaSalesCLPBuilder sfMegaSalesCLPBuilder;
    final PhleboAppHomePageViewBuilder phleboAppHomePageViewBuilder;
    final PhleboAppCaptureTaskPageViewBuilder phleboAppCaptureTaskPageViewBuilder;
    final PhleboAppInventoryPageViewBuilder phleboAppInventoryPageViewBuilder;
    final SfLiveClassListPageBuilder sfLiveClassListPageBuilder;
    final CGMConfigurationBuilder cgmConfigurationBuilder;
    final SfSmartScalePLPBuilder sfSmartScalePLPBuilder;
    final SmartScaleVitalsListPageBuilder smartScaleVitalsListPageBuilder;
    final SmartScaleVitalsDetailsPageBuilder smartScaleVitalsDetailsPageBuilder;
    final SmartScaleVitalsTimelinePageBuilder smartScaleVitalsTimelinePageBuilder;
    final SmartScaleOnboardingPageBuilder smartScaleOnboardingPageBuilder;
    final FBVOnboardingPageViewBuilder fbvOnboardingPageBuilder;
    final SmartScaleVitalsDetailsTabPageBuilder smartScaleVitalsDetailsTabPageBuilder;
    final SmartScaleSettingsPageBuilder smartScaleSettingsPageBuilder;
    final SfSupportFAQsPageBuilder sfSupportFAQsPageBuilder;
    final DigiProgramLearnPageBuilder digiProgramLearnPageBuilder;
    final SfOnboardingWalkthroughPageBuilder sfOnboardingWalkthroughPageBuilder;
    final WalkthroughScreenBuilder walkthroughScreenBuilder;
    final ThingsToDoCardsBuilder thingsToDoCardsBuilder;
    final SfEcommerceProductReviewsPageBuilder ecommerceProductReviewsPageBuilder;
    final SfStoreLPBuilder sfStoreLPBuilder;
    final SfStoreCategoryPageBuilder sfStoreCategoryPageBuilder;
    final SfStoreLifestylePageBuilder sfStoreLifestylePageBuilder;
    final SfStoreSearchPageBuilder sfStoreSearchPageBuilder;
    final SfSmartGlucometerDashboardPageBuilder smartGlucometerDashboardPageBuilder;
    final SfSmartGlucometerSettingsPageBuilder smartGlucometerSettingsPageBuilder;
    final SfSmartGlucometerOnboardingPageBuilder smartGlucometerOnboardingPageBuilder;
    final SfSmartGlucometerTimelinePageBuilder smartGlucometerTimelinePageBuilder;
    final SfStoreProductAddReviewPageBuilder sfStoreProductAddReviewPageBuilder;
    final SfDynamicDeeplinkPageBuilder sfDynamicDeeplinkPageBuilder;

    final DigiTasksToDoPageBuilder digiTasksToDoPageBuilder;
    final DATasksJourneyPageViewBuilder daTasksJourneyPageViewBuilder;
    final DigiBookPageBuilder digiBookPageBuilder;
    final DAProgressPageViewBuilder daProgressPageViewBuilder;
    final DigiPrePurchasePageBuilder digiPrePurchasePageBuilder;
    final DigiScorePageBuilder digiScorePageBuilder;
    final SfVitalMetricDetailsPageV2Builder vitalMetricDetailsPageV2Builder;
    final DALessonsJourneyPageViewBuilder daLessonsJourneyPageViewBuilder;
    final DADietPlanJourneyPageViewBuilder daDietPlanJourneyPageViewBuilder;

    final IndusClient indusClient;
    final ObjectMapper objectMapper;
    final AppConfigCache appConfigCache;
    final HttpServletRequest servletRequest;

    final LexiconClient lexiconClient;


    @Qualifier("cfApiRedisKeyValueStore")
    @Autowired
    KeyValueStore cfApiRedisKeyValueStore;

    @Value("${external.lsq.baseUrl}")
    private String lsqBaseUrl;

    @Value("${external.oms.baseUrl}")
    private String omsBaseUrl;

    @Value("${external.lsq.accessKey}")
    private String lsqAccessKey;

    @Value("${external.lsq.secretKey}")
    private String lsqSecretKey;

    public ChronicCarePurchasePageView getPurchasePage(UserContext userContext, String productCode, String typeOfDiabetes) throws BaseException {
        List<BundleSellableProduct> products = null;
        UserProfile userProfile = userContext.getUserProfile();
        String userId = userProfile.getUserId();
        products = this.serviceInterfaces.getSfAlbusClient().bundleProductSearch(BundleSubCategoryCode.CHRONIC_CARE_SUBSCRIPTION.name(), true, false, userId);
        return this.chronicCarePurchasePageViewBuilder.buildView(products, typeOfDiabetes, productCode, userContext);
    }

    public CongratsPageView getCongratsPage(UserContext userContext, String type, String consultationType, String productId, String bookingId) throws Exception {
        return this.congratsPageViewBuilder.buildView(userContext, type, consultationType, productId, bookingId);
    }

    public SfDiagnosticTestSuccessPageView getDiagnosticTestSuccessPage(String orderId) throws Exception {
        try {
            return this.sfDiagnosticTestSuccessPageViewBuilder.buildView(orderId);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public ChronicCareServiceHelper getChronicCareServiceHelper() {
        return chronicCareServiceHelper;
    }

    public List<TabType> getBottomTabs(UserContext userContext) throws HttpException {
        List<TabType> appTabs = new ArrayList<>();
        String userId = userContext.getUserProfile().getUserId();
        Boolean sfPackPurchased = this.userOnboardingService.getIfSugarFitPackActive(userId);
        boolean isFreemium = this.serviceInterfaces.chronicCareServiceHelper.isSfFreemiumUser(userContext);
        BundleProduct activeBundle = null;
        Optional<ActivePackResponse> activePackResponseOptional = this.userOnboardingService.getSugarFitActivePack(userId);
        if (activePackResponseOptional.isPresent()) {
            activeBundle = activePackResponseOptional.get().getBundleProduct();
        }
        if (DigitalAppUtil.isDigitalAppUser(activeBundle, isFreemium)){
            appTabs.add(TabType.SF_DIGI_HOME);
            appTabs.add(TabType.SF_DIGI_PROGRESS);
            appTabs.add(TabType.SF_LOGGING);
            appTabs.add(TabType.SF_FITNESS);
            if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)) {
                appTabs.add(TabType.SF_STORE);
            } else {
                appTabs.add(TabType.SF_COMMUNITY);
            }
        } else if (sfPackPurchased) {
            appTabs.add(TabType.SF_HOME);
            if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)) {
                appTabs.add(TabType.SF_STORE);
            } else {
                appTabs.add(TabType.SF_NUTRITION);
            }
            appTabs.add(TabType.SF_LOGGING);
            appTabs.add(TabType.SF_FITNESS);
            if (isInAppChatEnabledForUser(userContext, activeBundle, this.serviceInterfaces.chronicCareServiceHelper)) {
                appTabs.add(TabType.SF_CHAT);
            } if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)
                    && chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(activeBundle)) {
                appTabs.add(TabType.SF_NUTRITION);
            } else {
                appTabs.add(TabType.SF_GOALS);
            }
        } else {
            appTabs.add(TabType.SF_HOME);
            Boolean sfPackExpired = this.userOnboardingService.getIfSugarFitPackExpired(userId);
            if (sfPackExpired) {
                appTabs.add(TabType.SF_GOALS);
            }
        }
        return appTabs;
    }

    public HashMap getBottomTabsV2(UserContext userContext) throws HttpException {
        List<TabType> appTabs = new ArrayList<>();
        List<LoggingTabMenu> sfLoggingTabMenu = new ArrayList<>();
        String userId = userContext.getUserProfile().getUserId();
        Boolean sfPackPurchased = this.userOnboardingService.getIfSugarFitPackActive(userId);
        boolean isFreemium = this.serviceInterfaces.chronicCareServiceHelper.isSfFreemiumUser(userContext);
        BundleProduct activeBundle = null;
        Optional<ActivePackResponse> activePackResponseOptional = this.userOnboardingService.getSugarFitActivePack(userId);
        if (activePackResponseOptional.isPresent()) {
            activeBundle = activePackResponseOptional.get().getBundleProduct();
        }
        if (DigitalAppUtil.isDigitalAppUser(activeBundle, isFreemium)){
             sfLoggingTabMenu = getSfLoggingTabMenu(userContext, chronicCareServiceHelper, this.serviceInterfaces);
             appTabs.add(TabType.SF_DIGI_HOME);
             appTabs.add(TabType.SF_DIGI_PROGRESS);
             appTabs.add(TabType.SF_LOGGING);
             appTabs.add(TabType.SF_FITNESS);
            if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)) {
                appTabs.add(TabType.SF_STORE);
            } else {
                appTabs.add(TabType.SF_COMMUNITY);
            }
        } else if (sfPackPurchased) {
            if (serviceInterfaces.getChronicCareServiceHelper().isSpecialSugarControlPlanPack(activeBundle)) {
                appTabs.add(TabType.SF_HOME);
                appTabs.add(TabType.SF_LOGGING);
                appTabs.add(TabType.SF_STORE);
            } else {
                appTabs.add(TabType.SF_HOME);
                if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)) {
                    appTabs.add(TabType.SF_STORE);
                } else {
                    appTabs.add(TabType.SF_NUTRITION);
                }
                appTabs.add(TabType.SF_LOGGING);
                sfLoggingTabMenu = getSfLoggingTabMenu(userContext, chronicCareServiceHelper, this.serviceInterfaces);
                appTabs.add(TabType.SF_FITNESS);
                if (isInAppChatEnabledForUser(userContext, activeBundle, this.serviceInterfaces.chronicCareServiceHelper)) {
                    appTabs.add(TabType.SF_CHAT);
                } else if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)
                        && chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(activeBundle)) {
                    appTabs.add(TabType.SF_NUTRITION);
                } else {
                    appTabs.add(TabType.SF_GOALS);
                }
            }
        } else {
            appTabs.add(TabType.SF_HOME);
            BundleProduct expiredBundle = null;
            Optional<ActivePackResponse> sugarFitExpiredPack = this.userOnboardingService.getSugarFitExpiredPack(userContext.getUserProfile().getUserId());
            if (sugarFitExpiredPack.isPresent()) {
                expiredBundle = sugarFitExpiredPack.get().getBundleProduct();
            }
            Boolean sfPackExpired = sugarFitExpiredPack.isPresent() && sugarFitExpiredPack.get().getBundleProduct() != null && (!sugarFitExpiredPack.get().getBundleProduct().getIsTrialProduct());
            if (sfPackExpired) {
                if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)) {
                    appTabs.add(TabType.SF_STORE);
                } else {
                    appTabs.add(TabType.SF_NUTRITION);
                }
                sfLoggingTabMenu = getSfLoggingTabMenu(userContext, chronicCareServiceHelper, this.serviceInterfaces);
                appTabs.add(TabType.SF_LOGGING);
                appTabs.add(TabType.SF_FITNESS);
                if (isInAppChatEnabledForUser(userContext, expiredBundle, this.serviceInterfaces.chronicCareServiceHelper)) {
                    appTabs.add(TabType.SF_CHAT);
                } else if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)
                        && chronicCareServiceHelper.isSpecialSugarControl7DayPlanPack(expiredBundle)) {
                    appTabs.add(TabType.SF_NUTRITION);
                } else {
                    appTabs.add(TabType.SF_GOALS);
                }
            }
        }

        FitnessDeviceSyncMeta fitnessDeviceSyncMeta = chronicCareServiceHelper.getFitnessDeviceSyncMeta(userContext);

        HashMap map = new HashMap();
        map.put("tabs", appTabs);
        map.put("sfLoggingTabMenu", sfLoggingTabMenu);
        map.put("activeDeviceId", fitnessDeviceSyncMeta.getActiveDeviceId());
        map.put("lastSyncedTime", fitnessDeviceSyncMeta.getLastSyncedTime());
        return map;
    }

    public String getWhatsAppNumber(UserContext userContext) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            return this.serviceInterfaces.getSfAlbusClient().getUserInfo(userId).getWhatsAppNumber();
        } catch (Exception e) {
            return "";
        }
    }

    public String getCallingNumber(UserContext userContext) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            return this.serviceInterfaces.getSfAlbusClient().getUserInfo(userId).getCallingNumber();
        } catch (Exception e) {
            return "";
        }
    }


    public List<SugarfitAppLanguagePreference> getAppLanguagePreferences(UserContext userContext) {
        List<SugarfitAppLanguagePreference> preferences = new ArrayList<>();

        preferences.add(new SugarfitAppLanguagePreference("English", "en", ""));
        preferences.add(new SugarfitAppLanguagePreference("Hindi", "hi", "हिंदी"));
        preferences.add(new SugarfitAppLanguagePreference("Kannada", "kn", "ಕನ್ನಡ"));
        preferences.add(new SugarfitAppLanguagePreference("Telugu", "te", "తెలుగు"));
//        preferences.add(new LanguagePreference("Tamil", "ta", "தமிழ்"));

        return preferences;
    }

    public CGMPhotoReadingModalDetails getCgmPhotoReadingModalDetails() {
        CGMPhotoReadingModalDetails details = new CGMPhotoReadingModalDetails();
        var action = new Action("curefit://sfreadingsuploadpage","Upload Reading",ActionType.NAVIGATION);
        details.setTitle("Unlock Coach Advice");
        details.setSubTitle("You can now get insights about your sugar value by uploading your reader photo with the coach.");
        details.setAction(action);
        return details;
    }

    public boolean isDeleteAccountEnabled(UserContext userContext) {
        try {
            return isDeleteAccountEnabledUser(userContext);
        } catch (Exception e) {
            return false;
        }
    }

    public String updateWhatsAppNumber(UserContext userContext, String whatsAppNumber) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return this.serviceInterfaces.getSfAlbusClient().updateUserInfo(userId, whatsAppNumber);
    }


    public String getSugarfitAppLanguage(UserContext userContext) {
        try {
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppLanguagePreferenceResponse appLanguagePreference = chsClient.fetchAppLanguagePreference(userId,appTenant);
            if (Objects.nonNull(appLanguagePreference)) {
                return appLanguagePreference.getLanguagePreference();
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public Boolean updateAppLanguagePreference(UserContext userContext, String language) throws HttpException {
        try {
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            AppLanguagePreferenceRequest appLanguagePreferenceRequest = new AppLanguagePreferenceRequest();
            appLanguagePreferenceRequest.setLanguagePreference(language);
            appLanguagePreferenceRequest.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            appLanguagePreferenceRequest.setSource("NUX");
            chsClient.updateAppLanguagePreference(appLanguagePreferenceRequest, appTenant);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public Boolean updateTranslations(TranslationDataEntry translationDataEntry) throws HttpException {
        try {
            chsClient.createTranslationData(translationDataEntry);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public TranslationDataResponse getTranslations(String gender) {
        try {
            return chsClient.getAllTranslationData(gender);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public PatientDetail updateCallingNumber(UserContext userContext, String callingNumber) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return this.serviceInterfaces.getSfAlbusClient().updateContactNumber(userId, callingNumber);
    }

    public ProductDescriptionPageView getProductDescriptionPage(UserContext userContext, String type) {
        return this.productDescriptionPageViewBuilder.buildView(userContext, type);
    }

    public Object getPreferenceForm(UserContext userContext, UserEntry user, Number pageNumber) throws ResourceNotFoundException {
        return isPreferenceFormV2Supported(userContext) ? this.preferenceFormBuilderv2.buildView(userContext, user, pageNumber) : this.preferenceFormBuilder.buildView(userContext, user, pageNumber);
    }

    public PatientDetail getOrCreateChronicCarePatientForUserId(UserContext userContext) throws BaseException, ExecutionException, InterruptedException {
        try {
            return chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        } catch (ResourceNotFoundException e) {
            String userId = userContext.getUserProfile().getUserId();
            UserEntry userEntry = userServiceClient.getUser(userId).get();
            String name = userEntry.getFirstName() + (Objects.nonNull(userEntry.getLastName()) ? " " + userEntry.getLastName() : "");
            return this.createOrEditPatient(userContext, name);
        }
    }

    public SugarJournalReadingsPageView getSugarJournalReadingsPageView(UserContext userContext) throws ResourceNotFoundException {

        long currentEpoch = Instant.now().toEpochMilli();
        Calendar c = Calendar.getInstance(getUserTimezone(userContext));
        c.add(Calendar.DAY_OF_MONTH, -7);
        long fromEpoch = c.getTime().toInstant().toEpochMilli();
        Long self = chronicCarePatientService.getChronicCarePatientForUserId(userContext).getId();
        try {
            List<UserMetricValueWithRangeLabel> response = this.getMetricServicePojoFromCHSSugarEntry(userContext,fromEpoch,currentEpoch);

            return sugarJournalReadingsPageViewBuilder.buildView(userContext, response);
        } catch (Exception e) {
            String msg = String.format("Failed to get sugar journal pageview for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
        }
        return new SugarJournalReadingsPageView();
    }

    private List<UserMetricValueWithRangeLabel> getMetricServicePojoFromCHSSugarEntry(UserContext userContext, Long fromEpoch, Long toEpoch) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = getUserTimezone(userContext);
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        GlucometerReadingFilterRequest filterRequest = new GlucometerReadingFilterRequest();
        filterRequest.setUserId(userId);
        filterRequest.setStartTime(fromEpoch);
        filterRequest.setEndTime(toEpoch);
        filterRequest.setSlot(null);
        filterRequest.setTargetRangeTypes(null);
        List<GlucometerReadingEntry> chsEntries = chsClient.filterGlucometerReadings(filterRequest, appTenant, timeZone);
        if(CollectionUtils.isEmpty(chsEntries)){
            return new ArrayList<>();
        }
        List<UserMetricValueWithRangeLabel> response = chsEntries.stream().map(item -> {
            String rangeLabel;

            if(item.getType().equals(GlucoseTargetRangeType.HYPO)){
                rangeLabel = "LOW";
            } else if(item.getType().equals(GlucoseTargetRangeType.HYPER)){
                rangeLabel = "HIGH";
            } else rangeLabel = "MODERATE";

            //{"tags": "", "readingType": "Pre Lunch", "readingComments": ""}

            Map<String,Object> metadata = new HashMap<>();
            metadata.put("readingType",item.getSlot());
            metadata.put("readingComments", Strings.isNotEmpty(item.getComments()) ? item.getComments() : "");
            metadata.put("tags",Strings.isNotEmpty(item.getTags()) ? item.getTags() : "");

            UserMetricValueWithRangeLabel userMetricValueWithRangeLabel = new UserMetricValueWithRangeLabel();

            userMetricValueWithRangeLabel.setUserMetricValue(
                    UserMetricValue.builder().id(item.getId()).metricDate(item.getReadingTime()).value(item.getValue()).sourceMetadata(objectMapper.valueToTree(metadata)).timeZone("Asia/Kolkata").build());

            userMetricValueWithRangeLabel.setRangeLabel(rangeLabel);
            log.info("userMetricEntry with range label {} :: {} :: {}", rangeLabel,userMetricValueWithRangeLabel.getRangeLabel(),userMetricValueWithRangeLabel);
            return userMetricValueWithRangeLabel;
        }).collect(Collectors.toList());

        return response;
    }

    public SugarJournalInsightsPageView getSugarJournalInsightsPageView(UserContext userContext, Map<String, String> queryParams) throws ResourceNotFoundException, HttpException {

        List<Long> metricIds = new ArrayList<>(1);
        MetricEntry metricEntry = chsClient.getActiveMetricByName(CHS_GLUCOSE_METRIC_NAME);
        metricIds.add(Objects.nonNull(metricEntry) ? metricEntry.getId() : 680);

        Calendar c = Calendar.getInstance(getUserTimezone(userContext));
        int year = Integer.parseInt(queryParams.containsKey("year") ? queryParams.get("year") : String.valueOf(c.get(Calendar.YEAR)));
        int month = Integer.parseInt(queryParams.containsKey("month") ? queryParams.get("month") : String.valueOf(c.get(Calendar.MONTH) + 1));

        Calendar mycal = new GregorianCalendar(year, month - 1, 1);
        long start = mycal.getTimeInMillis();
        int lastDate = mycal.getActualMaximum(Calendar.DAY_OF_MONTH);
        mycal = new GregorianCalendar(year, month - 1, lastDate + 1);
        long end = TimeUtil.getEndOfDay(new Date(mycal.getTimeInMillis()), userContext.getUserProfile().getTimezone()).getTime();

        Long self = chronicCarePatientService.getChronicCarePatientForUserId(userContext).getId();

        try {
            log.info("Searching sugar readings for userId :: {}, metric :: {}, patient :: {}, start :: {}, end :: {}, source :: {}",
                    userContext.getUserProfile().getUserId(), metricIds, self, start, end, SOURCE_REF_ID);

            List<UserMetricValueWithRangeLabel> response = this.getMetricServicePojoFromCHSSugarEntry(userContext,start,end);
            log.info("Total entries in metric response :: {}", response.size());
            return sugarJournalInsightsPageViewBuilder.buildView(userContext, response, queryParams);
        } catch (Exception e) {
            String msg = String.format("Failed to get sugar journal values for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
        }

        return new SugarJournalInsightsPageView();
    }

    public ChronicCareHomePageView getChronicCareHomePageView(UserContext userContext, String cgmDeviceId) throws Exception {
        return chronicCareHomePageViewBuilder.buildView(userContext, serviceInterfaces, cgmDeviceId);
    }

    public SfHomePageView getSugarFitHomePageView(UserContext userContext, Map<String, String> queryParams) throws Exception {
        String cgmDeviceId = queryParams.get("cgmDeviceId");
        String sessionId = queryParams.getOrDefault("sessionId", "");
        Integer cgmConfigVersion = Integer.valueOf(queryParams.getOrDefault("cgmConfigVersion", "0"));
        return homePageViewBuilder.buildView(userContext, cgmDeviceId, sessionId, cgmConfigVersion);
    }

    public SfHomePageView getSugarFitHomePageViewV2(UserContext userContext, Map<String, String> queryParams) throws Exception {
        String cgmDeviceId = queryParams.get("cgmDeviceId");
        String sessionId = queryParams.getOrDefault("sessionId", "");
        Integer cgmConfigVersion = Integer.valueOf(queryParams.getOrDefault("cgmConfigVersion", "0"));
        if (isHomePageRevampEnabled(userContext)) {
            return homePageViewBuilderV2.buildView(userContext, cgmDeviceId, sessionId, cgmConfigVersion);
        }
        return homePageViewBuilder.buildView(userContext, cgmDeviceId, sessionId, cgmConfigVersion);
    }

    public SfDigiHomePageView getDigitalAppHomePage(UserContext userContext, Map<String, String> queryParams) throws Exception {
        String cgmDeviceId = queryParams.get("cgmDeviceId");
        String sessionId = queryParams.getOrDefault("sessionId", "");
        Integer cgmConfigVersion = Integer.valueOf(queryParams.getOrDefault("cgmConfigVersion", "0"));
        return digiHomePageBuilder.buildView(userContext, cgmDeviceId, sessionId, cgmConfigVersion);
    }

    public SfLiteAppHomePageView sfLiteAppHomePage(UserContext userContext, Map<String, String> queryParams) throws Exception {
        return sfLiteAppHomePageBuilder.buildView(userContext);
    }

    public boolean sfLiteMarkWebinarJoin(UserContext userContext, Map<String, String> queryParams) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            Date joinTime = Calendar.getInstance(getUserTimezone(userContext)).getTime();
            String webinarId = queryParams.getOrDefault("webinarId", "");
            Long webinarSessionId = Long.valueOf(queryParams.getOrDefault("webinarSessionId", ""));

            WebinarSessionEventsEntry joinSessionEntry = new WebinarSessionEventsEntry();
            joinSessionEntry.setWebinarId(webinarId);
            joinSessionEntry.setWebinarSessionId(webinarSessionId);
            joinSessionEntry.setEventTime(joinTime);
            joinSessionEntry.setUserId(userId);
            joinSessionEntry.setEventType(WebinarSessionEventType.JOIN);
            lmsClient.addWebinarSessionEvent(joinSessionEntry);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void updateKickStartOath(UserContext userContext) {
         Date completionDate = Calendar.getInstance(getUserTimezone(userContext)).getTime();
         Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
         serviceInterfaces.getSfAlbusClient().updateKSOath(userId, completionDate);
    }

    public PrePurchasePromisePage getChronicCarePrePurchasePromisePage(UserContext userContext, String packType) throws BaseException, ExecutionException, InterruptedException {
        CompletableFuture<UserEntry> userEntryCompletableFuture = serviceInterfaces.userService.getUser(userContext.getUserProfile().getUserId());
        return prePurchasePromisePageViewBuilder.buildView(userContext, packType, userEntryCompletableFuture.get());
    }

    public DiabetesTypeSelectionPage getDiabetesTypeSelectionPage(UserContext userContext) throws ResourceNotFoundException {
        return diabetesTypeSelectionPageViewBuilder.buildView(userContext);
    }

    public AgmInsightPageView getAgmInsightPageView(UserContext userContext, String cgmDeviceId) throws ResourceNotFoundException, HttpException {
        if (isAgmInsightsV2SupportedApp(userContext)) {
            return agmInsightPageViewBuilderV2.buildView(userContext, cgmDeviceId);
        }
        return agmInsightPageViewBuilder.buildView(userContext, cgmDeviceId);
    }

    public HamburgerMenu getHamburgerMenuPageView(Map<String, String> queryParams, UserContext userContext) throws ResourceNotFoundException {
        String userId = userContext.getUserProfile().getUserId();
        boolean salesApp = Objects.nonNull(queryParams) && queryParams.containsKey("salesApp") && Boolean.parseBoolean(queryParams.getOrDefault("salesApp", "false"));
        return hamburgerMenuBuilder.buildView(userContext, salesApp);
    }

    public ChronicCareNutritionPageView getPatientDietPlanV2(UserContext userContext, String date) throws BaseException {
        return chronicCareNutritionPageViewBuilder.buildView(userContext, date);
    }

    public String getMealPlanPdf(UserContext userContext) throws ResourceNotFoundException, HttpException {
        AppTenant tenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = getUserTimezone(userContext);
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);

        long epochTime = Calendar.getInstance(timeZone).getTimeInMillis();
        DailyUserMealPlanResponse mealPlanResponse = this.ambrosiaClient.fetchDailyUserMealPlan(patientDetail.getId(), epochTime, tenant, timeZone);
        if (mealPlanResponse != null && mealPlanResponse.getDailyUserMealPlanEntry() != null) {
            UserMealPlanPdfResponse pdfResponse = this.ambrosiaClient.fetchDailyUserMealPlanPdf(patientDetail.getId(), null, tenant, timeZone);
            return pdfResponse.getUrl();
        }
        return null;
    }

    public String getMealPlanPdfV2(UserContext userContext) throws ResourceNotFoundException, HttpException {
        try {
            AppTenant tenant = getAppTenantFromUserContext(userContext);
            TimeZone timeZone = getUserTimezone(userContext);
            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            UserMealPlanPdfResponse pdfResponse = this.ambrosiaClient.fetchDailyUserMealPlanPdf(patientDetail.getId(), null, tenant, timeZone);
            if (Objects.nonNull(pdfResponse)) {
                return pdfResponse.getUrl();
            }
        } catch (Exception e) {
            String msg = String.format("Failed to get meal plan pdf for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
        }
        return null;
    }

    public UserMealPlanPdfResponse getLatestMealPlanPdf(UserContext userContext, Long epochTime) throws ResourceNotFoundException, HttpException {
        AppTenant tenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = getUserTimezone(userContext);
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);

        return this.ambrosiaClient.fetchLatestUserMealPlanPdf(patientDetail.getId(), epochTime, tenant, timeZone);
    }

    public UserMealPlanState getUserMealPlanState(UserContext userContext) throws ResourceNotFoundException, HttpException {
        AppTenant tenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = getUserTimezone(userContext);

        return this.ambrosiaClient.fetchUserMealPlanState(Long.parseLong(userContext.getUserProfile().getUserId()), tenant, timeZone);
    }

    public List<FoodgroupEntry> getFoodGroupsData() throws HttpException {
        return ambrosiaClient.getFoodgroupsMasterData();
    }

    public UserMealPlanFeedback logDietPlanFeedback(UserContext userContext, UserMealPlanFeedback userMealPlanFeedback) throws EHRClientException, PolarisClientException, ResourceNotFoundException, HttpException {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        userMealPlanFeedback.setPatientId(patientDetail.getId().toString());
        return this.ambrosiaClient.saveUserMealPlanFeedback(userMealPlanFeedback);
    }

    public List<PatientActivityLoggingResponse> logPatientActivity(@RequestBody List<PatientActivityLoggingRequest> patientLoggingRequests, UserContext userContext) throws EHRClientException, ResourceNotFoundException {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        patientLoggingRequests.forEach(patientActivityLoggingRequest -> patientActivityLoggingRequest.setPatientId(patientDetail.getId()));
        return this.ehrClient.logPatientActivity(patientLoggingRequests, "SUGARFIT");
    }

    public List<CGMStat.PatientActivityImpact> logPatientActivityV2(@RequestBody List<PatientActivityLoggingRequest> patientActivityLoggingRequests, UserContext userContext) throws ResourceNotFoundException {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        String userId = userContext.getUserProfile().getUserId();
        List<PatientActivityUpdateRequest> patientActivityUpdateRequestList = new ArrayList<>();
        for (PatientActivityLoggingRequest patientActivityLoggingRequest : patientActivityLoggingRequests) {
            patientActivityLoggingRequest.setPatientId(patientDetail.getId());
            PatientActivityUpdateRequest patientActivityUpdateRequest = new PatientActivityUpdateRequest();
            patientActivityUpdateRequest.setUserId(userId);
            patientActivityUpdateRequest.setPatientActivityLoggingRequest(patientActivityLoggingRequest);
            patientActivityUpdateRequestList.add(patientActivityUpdateRequest);
        }
        return this.serviceInterfaces.getSfAlbusClient().logPatientActivity(patientActivityUpdateRequestList, "SUGARFIT");
    }

    public Boolean logPatientActivityV3(SfActivityLogPayload sfActivityLogPayload, UserContext userContext) throws HttpException, ResourceNotFoundException {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        String userId = userContext.getUserProfile().getUserId();
        String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        String appVersion = String.valueOf(userContext.getSessionInfo().getAppVersion());
        Map<String, String> metaData = new HashMap<>();
        metaData.put("appVersion", appVersion);
        switch (sfActivityLogPayload.getActivityType()) {
            case NUTRITION -> {
                MealActivityLogRequest mealActivityLogRequest=new MealActivityLogRequest();
                List<DishDetails> dishDetails = new ArrayList<>();
                sfActivityLogPayload.getMealDetails().getDishList().forEach(dish -> {
                    DishEntry dishEntry = new DishEntry();
                    dishEntry.setDishId(dish.getDishId());
                    dishDetails.add(new DishDetails(dishEntry, dish.getUnit(), dish.getQuantity()));
                });
                mealActivityLogRequest.setUserId(Long.parseLong(userId));
                mealActivityLogRequest.setLoggingTime(sfActivityLogPayload.getLoggingTime());
                mealActivityLogRequest.setActivityStartTime(sfActivityLogPayload.getActivityTime());
                if(sfActivityLogPayload.getMealDetails().getMealId() != null) {
                    mealActivityLogRequest.setId(Long.valueOf(sfActivityLogPayload.getMealDetails().getMealId()));
                }
                mealActivityLogRequest.setFavoriteMealId(sfActivityLogPayload.getMealDetails().getFavoriteMealId());
                mealActivityLogRequest.setIsMarkedFavorite(sfActivityLogPayload.getMealDetails().getIsMarkedFavorite());
                mealActivityLogRequest.setDishDetails(dishDetails);
                mealActivityLogRequest.setMetadata(metaData);
                this.sfLoggingClient.logMeal(mealActivityLogRequest, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
            }
            case FITNESS -> {
                FitnessActivityLogRequest entry = new FitnessActivityLogRequest();
                if (sfActivityLogPayload.getActivityId() != null) {
                    entry.setId(Long.valueOf(sfActivityLogPayload.getActivityId()));
                }
                entry.setUserId(Long.parseLong(userId));
                entry.setMetadata(metaData);
                entry.setNotes(sfActivityLogPayload.getNotes());
                entry.setLoggingTime(sfActivityLogPayload.getLoggingTime());
                entry.setActivityStartTime(sfActivityLogPayload.getActivityTime());
                entry.setActivityEndTime(SfDateUtils.addMins(sfActivityLogPayload.getActivityTime().getTime(), sfActivityLogPayload.getWorkoutDetails().getDurationInMins()));
                entry.setCalories(sfActivityLogPayload.getWorkoutDetails().getCalories());
                entry.setWorkoutId(sfActivityLogPayload.getWorkoutDetails().getWorkoutId());
                double distance = sfActivityLogPayload.getWorkoutDetails().getDistance();
                if (sfActivityLogPayload.getWorkoutDetails().getDistanceUnit() == DistanceUnit.KM) {
                    distance = distance * 1000;
                }
                entry.setDistance(distance);

                if (sfActivityLogPayload.getWorkoutDetails().getFatigue() != null) {
                    entry.setFatigue(Fatigue.valueOf(sfActivityLogPayload.getWorkoutDetails().getFatigue().name()));
                }
                this.sfLoggingClient.logWorkout(entry, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
            }
            case MEDICATION -> {
//                BulkMedicineLoggingPayload bulkMedicineLoggingPayload = new BulkMedicineLoggingPayload();
                MedicineActivityLogRequest bulkMedicineLoggingPayload= new MedicineActivityLogRequest();
                List<MedicineActivityLogEntry> medicineLoggingInfos = new ArrayList<>();
                sfActivityLogPayload.getMedicationDetails().getMedications().forEach(medication -> {
                    MedicineActivityLogEntry medicineEntry = new MedicineActivityLogEntry();
                    medicineEntry.setMedicineCode(medication.getPrescription().getMedicine().getUcode());
                    if(medication.getLoggedId()!=null) {
                        medicineEntry.setId(Long.valueOf(medication.getLoggedId()));
                    }
                    medicineEntry.setDeletedOn(!BooleanUtils.isTrue(medication.getIsConsumed()) ? sfActivityLogPayload.getLoggingTime() : null);
                    medicineEntry.setActivityStartTime(new Date(medication.getActivityTime()));
                    medicineEntry.setQuantity(medication.getIntakeDetails().getQuantity());
                    medicineEntry.setUnit(medication.getIntakeDetails().getUnit());
                    medicineEntry.setSlot(medication.getIntakeDetails().getIntakeSlot().getSlot());
                    medicineEntry.setUserId(Long.parseLong(userId));
                    medicineEntry.setLoggingTime(sfActivityLogPayload.getLoggingTime());
                    medicineEntry.setMetadata(metaData);
                    medicineLoggingInfos.add(medicineEntry);
                });
                bulkMedicineLoggingPayload.setEntries(medicineLoggingInfos);
                this.sfLoggingClient.logMedicines(bulkMedicineLoggingPayload, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
            }
            case SLEEP -> {
                SleepActivityLogRequest sleepLoggingPayload = new SleepActivityLogRequest();
                if (sfActivityLogPayload.getActivityId() != null) {
                    sleepLoggingPayload.setId(Long.valueOf(sfActivityLogPayload.getActivityId()));
                }
                sleepLoggingPayload.setLoggingTime(sfActivityLogPayload.getLoggingTime());
                sleepLoggingPayload.setActivityStartTime(sfActivityLogPayload.getActivityTime());
                sleepLoggingPayload.setActivityEndTime(getDateFromWakeUpTime(sfActivityLogPayload.getSleepDetails().getWakeUpTime(), userContext));
                sleepLoggingPayload.setUserId(Long.parseLong(userId));
                sleepLoggingPayload.setMetadata(metaData);
                sleepLoggingPayload.setNotes(sfActivityLogPayload.getSleepDetails().getNotes());
                if(sfActivityLogPayload.getSleepDetails().getWakeUpFeeling()!=null) {
                    sleepLoggingPayload.setWakeUpFeeling(WakeUpFeeling.valueOf(sfActivityLogPayload.getSleepDetails().getWakeUpFeeling().name()));
                }
                this.sfLoggingClient.logSleep(sleepLoggingPayload, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
            }
            default -> {
            }
        }
        return true;
    }

    public MealLogResponse logPatientMealActivity(SfActivityLogPayload sfActivityLogPayload, UserContext userContext) throws HttpException, ResourceNotFoundException {
        String userId = userContext.getUserProfile().getUserId();
        String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        String appVersion = String.valueOf(userContext.getSessionInfo().getAppVersion());
        Map<String, String> metaData = new HashMap<>();
        metaData.put("appVersion", appVersion);
        MealActivityLogRequest mealActivityLogRequest = new MealActivityLogRequest();
        List<DishDetails> dishDetails = new ArrayList<>();
        if (sfActivityLogPayload.getMealDetails().getDishLogType() == DishLogType.DISH_IMAGE) {
            sfActivityLogPayload.getMealDetails().getDishList().forEach(dish -> {
                if(dish != null && dish.getDishImageKey() != null && !dish.getDishImageKey().isEmpty()) {
                    DishDetails dd = new DishDetails();
                    dd.setDishLogType(dish.getDishLogType());
                    dd.setDishImageKey(dish.getDishImageKey());
                    dd.setDishImageSource(dish.getDishImageSource());
                    dishDetails.add(dd);
                }
            });
        } else {
            sfActivityLogPayload.getMealDetails().getDishList().forEach(dish -> {
                if(dish != null && dish.getQuantity() != null && dish.getUnit() != null) {
                    DishEntry dishEntry = new DishEntry();
                    dishEntry.setDishId(dish.getDishId());
                    DishDetails dd = new DishDetails(dishEntry, dish.getUnit(), dish.getQuantity());
                    dd.setDishLogType(dish.getDishLogType());
                    dishDetails.add(dd);
                }
            });
        }

        mealActivityLogRequest.setSlot(sfActivityLogPayload.getMealDetails().getMealSlot());
        mealActivityLogRequest.setUserId(Long.parseLong(userId));
        mealActivityLogRequest.setLoggingTime(sfActivityLogPayload.getLoggingTime());
        mealActivityLogRequest.setActivityStartTime(sfActivityLogPayload.getActivityTime());
        if (sfActivityLogPayload.getMealDetails().getMealId() != null) {
            mealActivityLogRequest.setId(Long.valueOf(sfActivityLogPayload.getMealDetails().getMealId()));
        }
        if (sfActivityLogPayload.getMealDetails().getNotes() != null) {
            mealActivityLogRequest.setNotes(sfActivityLogPayload.getMealDetails().getNotes());
        }
        mealActivityLogRequest.setFavoriteMealId(sfActivityLogPayload.getMealDetails().getFavoriteMealId());
        mealActivityLogRequest.setIsMarkedFavorite(sfActivityLogPayload.getMealDetails().getIsMarkedFavorite());
        mealActivityLogRequest.setDishDetails(dishDetails);
        mealActivityLogRequest.setMetadata(metaData);
        mealActivityLogRequest.setDishLogType(sfActivityLogPayload.getMealDetails().getDishLogType());
        if(sfActivityLogPayload.getCommunityId() != null) {
            mealActivityLogRequest.setCommunityId(sfActivityLogPayload.getCommunityId());
        }
        MealActivityLogResponse res = this.sfLoggingClient.logMeal(mealActivityLogRequest, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));

        MealLogResponse mealLogResponse = new MealLogResponse() ;
        mealLogResponse.setResponse(res);
        if(res.getMealLogInsight()!=null){
            String nutritionType = res.getMealLogInsight().getNutritionType().name().equals("Carbs") ? "Carbs" : (res.getMealLogInsight().getNutritionType().name() + "s");
            String text = "";
            if (res.getMealLogInsight().getNutritionLevel().equals(NutritionLevel.IN_RANGE)) {
                text = "Awesome! Your " + nutritionType.toLowerCase() + " for " + res.getSlot().name().toLowerCase() + " are <within the target>";
            } else if (res.getMealLogInsight().getNutritionLevel().equals(NutritionLevel.LOW)) {
                text = "Your " + nutritionType.toLowerCase() + " intake has been on the <lower side>.";
            } else if (res.getMealLogInsight().getNutritionLevel().equals(NutritionLevel.HIGH)) {
                text = "Your " + nutritionType.toLowerCase() + " intake has been on the <higher side>.";
                mealLogResponse.setThingsToDoTitle("Things to do when " + nutritionType.toLowerCase() + " levels are high:");
                boolean isFreemiumUser = serviceInterfaces.getChronicCareServiceHelper().isSfFreemiumUser(userContext);
                if (isFreemiumUser) {
                    mealLogResponse.setThingsToDo(new String[]{"Go for a 15 min walk post 30 min after having meal.",
                            "Drink ~300ml of water after 1 hour of meal to flush out excess sugars."});
                } else {
                    mealLogResponse.setThingsToDo(new String[]{"Go for a 15 min walk post 30 min after having meal.",
                            "Drink ~300ml of water after 1 hour of meal to flush out excess sugars.",
                            "Follow the advice recommended by your coach."});
                }
            }
            mealLogResponse.setInsightText(text);
        }
        return mealLogResponse;
    }


    public Object getPatientActivity(Map<String, String> queryParams, UserContext userContext) throws
            EHRClientException, HttpException, ResourceNotFoundException {
        String activityId = queryParams.getOrDefault("activityId", "");
        String activityType = queryParams.getOrDefault("activityType", "");
        return switch (activityType) {
            case "FITNESS" -> this.activityFitnessLogPageViewBuilder.buildView(queryParams);
            case "MEDICATION" -> this.activityMedicineLogPageViewBuilder.buildView(queryParams, userContext);
            case "SLEEP" -> this.sfLoggingClient.fetchLoggedSleep(Long.valueOf(activityId));
            default -> ehrClient.getPatientActivity(activityId, "SUGARFIT");
        };
    }

    public Boolean createFavMeal(SfCreateFavMealPayload sfCreateFavMealPayload, UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        UserFavouriteMealEntry requestPayload = new UserFavouriteMealEntry();
        requestPayload.setLastLoggedAt(sfCreateFavMealPayload.getLoggingTime());
        requestPayload.setPreferredSlotTime(sfCreateFavMealPayload.getPreferredTime());
        List<DishDetails> dishDetails = new ArrayList<>();
        sfCreateFavMealPayload.getDishList().forEach(dish -> {
            DishEntry dishEntry = new DishEntry();
            dishEntry.setDishId(dish.getDishId());
            dishDetails.add(new DishDetails(dishEntry, dish.getUnit(), dish.getQuantity()));
        });
        requestPayload.setDishDetails(dishDetails);
        requestPayload.setUserId(Long.parseLong(userId));
        this.ambrosiaClient.addFavoriteMeal(requestPayload, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
        return true;
    }

    public Boolean deleteFavMeal(Map<String, String> queryParams, UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        String mealId = queryParams.getOrDefault("mealId", "");
        this.ambrosiaClient.deleteFavoriteMeal(mealId, Long.parseLong(userId));
        return true;
    }

    public ActivityCustomDishPageView getCustomDishPage(Map<String, String> queryParams) throws HttpException {
        return activityCustomDishPageViewBuilder.buildView(queryParams);
    }

    public Boolean suggestIngredient(UserDishSuggestionEntry userDishSuggestionEntry, UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        userDishSuggestionEntry.setUserId(Long.valueOf(userId));
        this.ambrosiaClient.addDishSuggestion(userDishSuggestionEntry);
        return true;
    }

    public SfActivityDishItemWidget createCustomDish(SfCreateDishPayload sfCreateDishPayload, UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        DishEntry requestPayload = new DishEntry();
        requestPayload.setUserId(Long.parseLong(userId));
        requestPayload.setDishId(sfCreateDishPayload.getDishId());
        requestPayload.setName(sfCreateDishPayload.getDishName());
        requestPayload.setPrimaryUnit(sfCreateDishPayload.getPrimaryUnit());
        if (sfCreateDishPayload.getChildDishes() != null && !sfCreateDishPayload.getChildDishes().isEmpty()) {
            List<DishDetails> dishDetails = new ArrayList<>();
            sfCreateDishPayload.getChildDishes().forEach(dish -> {
                DishEntry dishEntry = new DishEntry();
                dishEntry.setDishId(dish.getDishId());
                dishDetails.add(new DishDetails(dishEntry, dish.getUnit(), dish.getQuantity()));
            });
            requestPayload.setChildDishDetails(dishDetails);
        }
        DishEntry dishEntry = this.ambrosiaClient.addOrUpdateCustomDish(requestPayload);
        if (dishEntry != null) {
            List<SfDishUnit> sfDishUnits = new ArrayList<>();
            List<SfNutrition> nutritions = new ArrayList<>();
            NutritionProfile nutritionProfile = dishEntry.getNutritionInfo();
            if (nutritionProfile != null) {
                nutritions.add(new SfNutrition("Protein", SfNutritionType.PROTEIN, nutritionProfile.getProtein(), "g", "#1AA9A4"));
                nutritions.add(new SfNutrition("Fat", SfNutritionType.FAT, nutritionProfile.getFat(), "g", "#7A6CBB"));
                nutritions.add(new SfNutrition("Carbs", SfNutritionType.CARBS, nutritionProfile.getCarbohydrates(), "g", "#FF808A"));
                nutritions.add(new SfNutrition("Fiber", SfNutritionType.FIBER, nutritionProfile.getFibre(), "g", "#5080AE"));
            }

            SfNutritionInfo nutritionInfo = new SfNutritionInfo(dishEntry.getNutritionInfo().getCalories(), nutritions);
            dishEntry.getUnits().forEach(u -> {
                sfDishUnits.add(new SfDishUnit(u.getUnit(), u.getConversionToGrams()));
            });
            SfDish sfDish = new SfDish(dishEntry.getDishId(),
                    dishEntry.getName(),
                    1.0,
                    dishEntry.getPrimaryUnit(),
                    sfDishUnits,
                    nutritionInfo,
                    dishEntry.getIsCustom()
            );
            return new SfActivityDishItemWidget(sfCreateDishPayload.getLogType(), sfDish, dishEntry.getIsCustom());
        }
        return null;
    }

    public Boolean deleteCustomDish(Map<String, String> queryParams, UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        String dishId = queryParams.getOrDefault("dishId", "");
        this.ambrosiaClient.deleteCustomDish(dishId, Long.parseLong(userId));
        return true;
    }

    public SfMedication getEmptyMedication(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        String medicineName = queryParams.getOrDefault("medicineName", "");
        StaticData staticData = this.sfMedicineClient.fetchStaticData();
        return ChronicCareAppUtil.getEmptyMedication(userContext, staticData, medicineName, null);
    }

    public Boolean addMedicineToPrescription(SfMedication medication, UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        Quantity quantity = new Quantity();
        quantity.setValue(medication.getPrescription().getIntakeDetails().getQuantity());
        quantity.setUnit(medication.getPrescription().getIntakeDetails().getUnit());
        MedicineEntry medicineEntry = new MedicineEntry();
        medicineEntry.setUCode(medication.getPrescription().getMedicine().getUcode());
        Duration duration = new Duration();
        duration.setValue(medication.getPrescription().getDurationOfMedication().getDuration());
        duration.setUnit(medication.getPrescription().getDurationOfMedication().getUnit());
        FrequencySchedule frequencySchedule = new FrequencySchedule();
        frequencySchedule.setValue(medication.getPrescription().getFrequencyOfMedication().getFrequency());
        frequencySchedule.setUnit(medication.getPrescription().getFrequencyOfMedication().getFrequencyType());
        switch (medication.getPrescription().getFrequencyOfMedication().getFrequencyType()) {
            case DAY -> {
                List<Schedule> schedules = new ArrayList<>();
                medication.getPrescription().getFrequencyOfMedication().getDailyDosage().forEach(dailyDosage -> {
                    Schedule schedule = new Schedule();
                    schedule.setSlot(dailyDosage.getIntakeSlot().getSlot());
                    schedule.setIntakeTime(dailyDosage.getTime());
                    schedules.add(schedule);
                });
                frequencySchedule.setSchedules(schedules);
            }
            case WEEK -> {
                List<Schedule> schedules = new ArrayList<>();
                medication.getPrescription().getFrequencyOfMedication().getWeeklyDosage().getDaysOfWeek().forEach(dayOfWeek -> {
                    Schedule schedule = new Schedule();
                    schedule.setDayOfWeek(dayOfWeek);
                    schedule.setSlot(medication.getPrescription().getFrequencyOfMedication().getWeeklyDosage().getIntakeSlot().getSlot());
                    schedule.setIntakeTime(medication.getPrescription().getFrequencyOfMedication().getWeeklyDosage().getTime());
                    schedules.add(schedule);
                });
                frequencySchedule.setSchedules(schedules);
            }
            case MONTH -> {
                List<Schedule> schedules = new ArrayList<>();
                medication.getPrescription().getFrequencyOfMedication().getMonthlyDosage().getDatesOfMonth().forEach(date -> {
                    Schedule schedule = new Schedule();
                    schedule.setDateOfMonth(date);
                    schedule.setSlot(medication.getPrescription().getFrequencyOfMedication().getWeeklyDosage().getIntakeSlot().getSlot());
                    schedule.setIntakeTime(medication.getPrescription().getFrequencyOfMedication().getWeeklyDosage().getTime());
                    schedules.add(schedule);
                });
                frequencySchedule.setSchedules(schedules);
            }
            case SOS -> {
                List<Schedule> schedules = new ArrayList<>();
                Schedule schedule = new Schedule();
                schedule.setSlot(medication.getPrescription().getFrequencyOfMedication().getSosDosage().getIntakeSlot().getSlot());
                schedules.add(schedule);
                frequencySchedule.setSchedules(schedules);
            }
            default -> {
            }
        }

        UserCustomPrescriptionEntry prescriptionEntry = new UserCustomPrescriptionEntry();
        prescriptionEntry.setStartDate(Objects.nonNull(medication.getActivityTime()) ? new Date(medication.getActivityTime()) : new Date());
        prescriptionEntry.setUserId(Long.parseLong(userId));
        prescriptionEntry.setQuantity(quantity);
        prescriptionEntry.setMedicineEntry(medicineEntry);
        prescriptionEntry.setDuration(duration);
        prescriptionEntry.setFrequencySchedule(frequencySchedule);
        this.sfMedicineClient.addCustomPrescription(prescriptionEntry);
        return true;
    }

    public SfMedication addCustomMedicine(SfMedication medication, UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        MedicineEntry medicineEntry = new MedicineEntry();
        medicineEntry.setProductName(medication.getPrescription().getMedicine().getName() + " - " +
                medication.getPrescription().getMedicine().getQuantity() + medication.getPrescription().getMedicine().getUnit());
        medicineEntry.setStrength(medication.getPrescription().getMedicine().getQuantity() + "" +
                medication.getPrescription().getMedicine().getUnit());
        medicineEntry.setUserId(Long.parseLong(userId));
        medicineEntry.setIsCustom(true);
        MedicineEntry newMedEntry = this.sfMedicineClient.addCustomMedicine(medicineEntry);
        SfMedication.MedicinePrescription medicinePrescription = medication.getPrescription();
        medicinePrescription.setMedicine(new SfMedicine(newMedEntry.getUCode(), newMedEntry.getProductName(), newMedEntry.getIsCustom()));
        medication.setPrescription(medicinePrescription);
        this.addMedicineToPrescription(medication, userContext);
        return medication;
    }

    public ScheduledPlanItem logScheduledIntervention(PatientInterventionLoggingRequest patientInterventionLoggingRequest, UserContext userContext) throws EHRClientException, ResourceNotFoundException {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        PatientInterventionResponse patientInterventionResponse = new PatientInterventionResponse();
        patientInterventionResponse.setScheduledPlanItemId(patientInterventionLoggingRequest.getScheduledInterventionId());
        patientInterventionResponse.setStatus(patientInterventionLoggingRequest.getStatus());
        return this.ehrClient.updateInterventionStatus(patientDetail.getId(), patientInterventionResponse, "SUGARFIT");
    }

    public UserNFCReadingResponse recordInAppCGMReading(InAppCGMReading inAppCGMReading, UserContext userContext) throws HttpException {
        String timezone = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        UserNFCReadingRequest userNFCReadingRequest = new UserNFCReadingRequest();
        userNFCReadingRequest.setUserId(inAppCGMReading.getPatientUserId() != null ? inAppCGMReading.getPatientUserId() : Long.valueOf(userContext.getUserProfile().getUserId()));
        userNFCReadingRequest.setHexReading(inAppCGMReading.getHexReading());
        userNFCReadingRequest.setReadingTime(inAppCGMReading.getReadingTime());
        userNFCReadingRequest.setHardwareId(getModifiedHardwareIdToSendToBackend(inAppCGMReading.getHardwareId(), inAppCGMReading.getDeviceModel()));
        userNFCReadingRequest.setDeviceModel(inAppCGMReading.getDeviceModel());
        userNFCReadingRequest.setMetadata(inAppCGMReading.getMetadata());
        userNFCReadingRequest.setTrendSupported(true);
        userNFCReadingRequest.setReadingMode(inAppCGMReading.getReadingMode() != null ? inAppCGMReading.getReadingMode() : ReadingMode.APP);
        return chsClient.uploadCgmNFCReadings(userNFCReadingRequest, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezone));
    }

    public GlucoRxReadingResponse recordInAppCGMBleReading(InAppCGMBleReading inAppCGMReading, UserContext userContext) throws HttpException {
        try {
            String timezone = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
            GlucoRxReadingRequest glucoRxReadingRequest = new GlucoRxReadingRequest();
            glucoRxReadingRequest.setReadings(inAppCGMReading.getReadings());
            glucoRxReadingRequest.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            glucoRxReadingRequest.setHardwareId(inAppCGMReading.getSerialNumber());
            if(ChronicCareAppUtil.isCGMRemainingHoursSupported(userContext)){
                glucoRxReadingRequest.setRemainingHours(inAppCGMReading.getRemainingHours());
            }
            return chsClient.uploadCgmGlucoRxReadings(glucoRxReadingRequest, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezone));
        } catch (Exception e) {
            String msg = String.format("Error in uploading GlucoRx reading for user :: %s", userContext.getUserProfile().getUserId());
            log.error(msg, e);
            return null;
        }
    }

    public BluconEventEntry createGlucoRxEvent(BluconEventEntry entry, UserContext userContext) throws HttpException {
        entry.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        return chsClient.createCgmEvent(entry, getAppTenantFromUserContext(userContext));
    }

    public ActivateDeviceResponse libreSensorInstall(UserContext userContext, ActivateDeviceRequest activateDeviceRequest) throws HttpException {
        activateDeviceRequest.setUserId(activateDeviceRequest.getUserId() != null ? activateDeviceRequest.getUserId() : Long.valueOf(userContext.getUserProfile().getUserId()));
        if (activateDeviceRequest.getDeviceModel() == null) {
            activateDeviceRequest.setDeviceModel(DeviceModel.ABBOTT_LIBRE);
        }
        if (activateDeviceRequest.getActivateTime() == null) {
            activateDeviceRequest.setActivateTime(new Date());
        }
        activateDeviceRequest.setHardwareId(getModifiedHardwareIdToSendToBackend(activateDeviceRequest.getHardwareId(), activateDeviceRequest.getDeviceModel()));
        return chsClient.activateCgmDevice(activateDeviceRequest, getAppTenantFromUserContext(userContext), "APP");
    }

    public CGMRequestResponse requestSensor(UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        DefaultSuccessResponse defaultSuccessResponse = chsClient.requestCgm(Long.valueOf(userId), getAppTenantFromUserContext(userContext));
        CGMRequestResponse cgmRequestResponse = new CGMRequestResponse();
        if (defaultSuccessResponse.isSuccess()) {
            cgmRequestResponse.setHeader("CGM Request Successful");
            cgmRequestResponse.setTitle("Congratulations!");
            cgmRequestResponse.setSubtitle("Your request for an additional sensor has been logged. Our team will reach out to you with next steps.");
            cgmRequestResponse.setActionText("DONE");
        } else {
            cgmRequestResponse.setHeader("CGM Request failed");
            cgmRequestResponse.setActionText("Retry");
        }
        return cgmRequestResponse;
    }

    public SfTimezoneUpdateResponse updateTimezone(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        String timezoneId = String.valueOf(queryParams.getOrDefault("timezone", "Asia/Kolkata"));
        TimezoneResponse timezoneResponse = chsClient.updateIfDifferentTimezone(Long.valueOf(userContext.getUserProfile().getUserId()),
                TimeZone.getTimeZone(timezoneId), getAppTenantFromUserContext(userContext));
        SfTimezoneUpdateResponse sfTimezoneUpdateResponse = new SfTimezoneUpdateResponse();
        sfTimezoneUpdateResponse.setTimezoneChanged(timezoneResponse.isTimezoneChanged());
        sfTimezoneUpdateResponse.setTimeZone(timezoneResponse.getTimeZone());
        return sfTimezoneUpdateResponse;
    }

    public OpsRequestResponse opsRequest(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        AppTenant tenant = getAppTenantFromUserContext(userContext);
        try {
            SfOpsRequestType requestType = SfOpsRequestType.valueOf(queryParams.getOrDefault("requestType", ""));
            DefaultSuccessResponse defaultSuccessResponse = null;
            switch (requestType) {
                case REQUEST_NEW_CGM -> defaultSuccessResponse = chsClient.requestCgm(Long.valueOf(userId), tenant);
                case REQUEST_CGM_INSTALLATION -> defaultSuccessResponse = chsClient.requestCgmInstallation(Long.valueOf(userId), tenant);
                case REQUEST_DIAGNOSTICS -> {
                    UserEventEntry userEventEntry = new UserEventEntry(Long.parseLong(userId),
                            UserEventType.USER_ACTIVITY_EVENT, REQUEST_DIAGNOSTICS.name(), new Date(), null, tenant);
                    this.rashiClient.publishUserEvent(userEventEntry, tenant, userContext.getApiHeaders().get("x-request-id"));
                    defaultSuccessResponse = new DefaultSuccessResponse();
                    defaultSuccessResponse.setSuccess(true);
                }
                default -> {
                }
            }
            OpsRequestResponse opsRequestResponse = new OpsRequestResponse();
            if (defaultSuccessResponse != null && defaultSuccessResponse.isSuccess()) {
                opsRequestResponse.setHeader("Request Successful");
                opsRequestResponse.setTitle("Congratulations!");
                opsRequestResponse.setSubtitle("Your request has been logged. Our team will reach out to you with next steps.");
                opsRequestResponse.setActionText("DONE");
            } else {
                opsRequestResponse.setHeader("Request failed");
                opsRequestResponse.setActionText("Retry");
            }
            return opsRequestResponse;
        } catch (Exception e) {
            String msg = String.format("Error in making ops request :: %s", userId);
            log.error(msg, e);
            exceptionReportingService.reportException(msg, e);
            return null;
        }
    }

    public CGMRequestResponse requestInstallation(UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        DefaultSuccessResponse defaultSuccessResponse = chsClient.requestCgmInstallation(Long.valueOf(userId), getAppTenantFromUserContext(userContext));
        CGMRequestResponse cgmRequestResponse = new CGMRequestResponse();
        if (defaultSuccessResponse.isSuccess()) {
            cgmRequestResponse.setHeader("CGM installation request successful");
            cgmRequestResponse.setTitle("Congratulations!");
            cgmRequestResponse.setSubtitle("Your request for sensor installation has been logged. Our team will reach out to you with next steps.");
            cgmRequestResponse.setActionText("DONE");
        } else {
            cgmRequestResponse.setHeader("CGM installation request failed");
            // cgmRequestResponse.setTitle("Congratulations!");
            // cgmRequestResponse.setSubtitle("Your request for an additional sensor has been logged. Our team will reach out to you with next steps.");
            cgmRequestResponse.setActionText("Retry");
        }
        return cgmRequestResponse;
    }

    public ReadingModeResponse updatePatientCGMReadingMode(PatientCGMReadingMode patientCGMReadingMode, UserContext userContext) throws HttpException {
        return chsClient.updateCgmReadingMode(Long.valueOf(userContext.getUserProfile().getUserId()), patientCGMReadingMode.getMode(), getAppTenantFromUserContext(userContext));
    }

    public Object addSugarReading(UserContext userContext, SugarReadingBody sugarReading) throws ResourceNotFoundException {
        log.info("addSugarReading : " + sugarReading.toString());
        log.info("addSugarReading : " + (userContext.getUserProfile().getUserId()));
        Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
        String[] date = sugarReading.getDate().split("-");
        String[] time = sugarReading.getTime().split(":");
        calendar.set(Integer.parseInt(date[0]), Integer.parseInt(date[1]) - 1, Integer.parseInt(date[2]), Integer.parseInt(time[0]), Integer.parseInt(time[1]));

        Map<String,Object> metadata = objectMapper.convertValue(sugarReading.getSourceMeta(), new TypeReference<>() {
        });

        GlucometerReadingEntry glucometerReadingInsertRequest = new GlucometerReadingEntry();
        glucometerReadingInsertRequest.setId(sugarReading.getId());
        glucometerReadingInsertRequest.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        glucometerReadingInsertRequest.setValue(sugarReading.getValue());
        glucometerReadingInsertRequest.setSource(SOURCE_REF_ID);
        glucometerReadingInsertRequest.setSlot(
                (metadata != null && metadata.containsKey("readingType"))
                        ? String.valueOf(metadata.get("readingType")) : null);
        glucometerReadingInsertRequest.setComments(
                (metadata != null && metadata.containsKey("readingComments"))
                        ? String.valueOf(metadata.get("readingComments")) : "");
        glucometerReadingInsertRequest.setTags(
                (metadata != null && metadata.containsKey("tags"))
                        ? String.valueOf(metadata.get("tags")) : "");
        glucometerReadingInsertRequest.setReadingTime(calendar.getTime());

        try{
            chsClient.saveGlucometerReadings(glucometerReadingInsertRequest, getUserTimezone(userContext));
        } catch (Exception e) {
            String msg = String.format("Unable to add UserMetricValue for user :: %s", userContext.getUserProfile().getUserId());
            log.error(msg, e);
            exceptionReportingService.reportException(msg, e);
            return false;
        }
        return true;
    }

    public UserMetricEntry getUserMetricCHS(UserContext userContext, Long metricId, Double value, String source, Long id, JsonNode sourceMetaData, Date metricDate) {
        log.info("addSugarReading : " + (userContext.getUserProfile().getUserId()));
        UserMetricEntry userMetricEntry = new UserMetricEntry();
        userMetricEntry.setMetricId(metricId);
        userMetricEntry.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        userMetricEntry.setValue(String.valueOf(value));
        userMetricEntry.setSource(source);
        userMetricEntry.setDataSource(MeasurementType.MANUAL.name());
        userMetricEntry.setMetadata(objectMapper.convertValue(sourceMetaData, new TypeReference<Map<String, Object>>() {}));
        userMetricEntry.setStartTime(metricDate);
        userMetricEntry.setEndTime(metricDate);
        return userMetricEntry;
    }

    public SFNuxStatusResponse getSugarfitNuxStatus(UserContext userContext ) throws HttpException {
        SFNuxStatusResponse nuxStatusResponse = new SFNuxStatusResponse();
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        String source = null;
        try {
            AppLanguagePreferenceResponse appLanguagePreference = chsClient.fetchAppLanguagePreference(userId, appTenant);
            if (Objects.nonNull(appLanguagePreference)) {
                source = appLanguagePreference.getSource();
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        NUXStatusResponse response = serviceInterfaces.getSmsClient().getNUXStatus(userId, true, timeZone);
        boolean nuxPreferencesPending = serviceInterfaces.getChronicCareServiceHelper().sugarfitPaidNuxPreferencesPending(response);
        boolean addressPending = serviceInterfaces.getChronicCareServiceHelper().sugarfitPaidNuxAddressPending(response);
        boolean hasActivePack = response.getActivePack() != null;
        nuxStatusResponse.setSugarFitDigitalUser(DigitalAppUtil.isDigitalAppUser(hasActivePack ? response.getActivePack().getProductCode(): null, response.getFreemium()));
        nuxStatusResponse.setShowNuxAppLanguagePreferences(!Objects.equals(source, "NUX"));
        nuxStatusResponse.setSugarFitFreemiumUser(response.getFreemium());
        nuxStatusResponse.setSugarfitNuxCompleted(response.getNuxCompleted());
        nuxStatusResponse.setSugarfitNuxPreferencesPending(hasActivePack && nuxPreferencesPending);
        nuxStatusResponse.setSugarfitNuxAddressPending(hasActivePack && addressPending);
        nuxStatusResponse.setShowPackStartDateSelection(!response.getNuxCompleted()
                && nuxPreferencesPending
                && hasActivePack);

        return nuxStatusResponse;
    }

    public PackStartDateSelection getPackStartDateSelectionPage(UserContext userContext) {
        return packStartDateSelectionPageBuilder.buildView(userContext);
    }

    public NuxPreferencesPageView getNuxPreferencesPage(UserContext userContext) {
        return nuxPreferencesPageViewBuilder.buildView(userContext);
    }

    public SetupJourneyPageView getSetupJourneyPage(UserContext userContext) {
        return setupJourneyPageViewBuilder.buildView(userContext);
    }

    public Object addUserPreference(UserContext userContext, UserPreference userPreference) {
        try {
            NUXProfileUpdateRequest request = new NUXProfileUpdateRequest();
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            if (userPreference.getAttribute().equals("language")) {
                request.setProfiles(new ArrayList<>() {{
                    add(NUXProfile.LANGUAGE);
                }});
                request.setLanguages(userPreference.getValues());
                serviceInterfaces.getSmsClient().updateNUXData(Long.valueOf(userContext.getUserProfile().getUserId()), request, timeZone);
            } else if (userPreference.getAttribute().equals("cuisines")) {
                request.setProfiles(new ArrayList<>() {{
                    add(NUXProfile.CUISINE);
                }});
                request.setCuisines(userPreference.getValues());
                serviceInterfaces.getSmsClient().updateNUXData(Long.valueOf(userContext.getUserProfile().getUserId()), request, timeZone);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public ChronicCareSaveUserAddressResponse addUserAddressPreference(UserContext userContext, UserPreference userPreference) {
        ChronicCareSaveUserAddressResponse response = new ChronicCareSaveUserAddressResponse();
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        try {
            NUXProfileUpdateRequest request = new NUXProfileUpdateRequest();
            request.setProfiles(new ArrayList<>() {{
                add(NUXProfile.ADDRESS);
            }});
            request.setAddressId(userPreference.getValues().get(0));
            serviceInterfaces.getSmsClient().updateNUXData(Long.valueOf(userContext.getUserProfile().getUserId()), request, timeZone);
        } catch (Exception e) {
            e.printStackTrace();
            response.setErrorMessage(e.getMessage());
            response.setServiceable(false);
            return response;
        }
        response.setServiceable(true);
        return response;
    }

    public ServiceabilityFormResponse getServiceablityForm(UserContext userContext, String productCode, UserEntry user) {
        List<PatientDetail> patientsList = new ArrayList<>();
        UserProfile userProfile = userContext.getUserProfile();
        String userId = userProfile.getUserId();
        patientsList = this.serviceInterfaces.getSfAlbusClient().getAllPatients(userId);
        return this.serviceabilityFormBuilder.buildView(userContext, patientsList, productCode, user);
    }

    public ServiceabilityAndPatientResponse serviceabilityAndCreatePatient(UserContext userContext, String pinCode, String name, String productCode) {
        ServiceabilityAndPatientResponse response = new ServiceabilityAndPatientResponse();
        Boolean serviceability = verifyServiceability(userContext, pinCode, productCode);
        response.setServiceability(serviceability);
        if (serviceability) {
            response.setPatient(createOrEditPatient(userContext, name));
        }
        return response;
    }

    ;

    public ServiceabilityAndPatientResponse serviceabilityAndCreatePatientV2(UserContext userContext, String addressId, String name, String productCode) {
        ServiceabilityAndPatientResponse response = new ServiceabilityAndPatientResponse();
        Boolean serviceability = verifyServiceabilityV2(addressId, productCode, userContext);
        response.setServiceability(serviceability);
        if (serviceability) {
            response.setPatient(createOrEditPatient(userContext, name));
        }
        return response;
    }

    ;

    public Boolean verifyServiceability(UserContext userContext, String pinCode, String productCode) {
        if (productCode.equals(TRIAL_PRODUCTCODE)) {
            return true;
        }
        String tenant = getTenantFromUserContextWithFallback(userContext);
        Boolean serviceability = serviceInterfaces.getSfAlbusClient().getServiceability(userContext.getUserProfile().getUserId(), pinCode, productCode, tenant);
        return serviceability;
    }

    public Boolean verifyServiceabilityV2(String addressId, String productCode, UserContext userContext) {
        if (productCode.equals(TRIAL_PRODUCTCODE)) {
            return true;
        }
        String tenant = getTenantFromUserContextWithFallback(userContext);
        Boolean serviceability = serviceInterfaces.getSfAlbusClient().checkSFServiceabilityV2(addressId, productCode, tenant);
        return serviceability;
    }

    private String getTenantFromUserContextWithFallback(UserContext userContext) {
        // adding additional checks because of recent order source to api key mapping changes. which were matching SF with curefit.
        return com.curefit.base.enums.Tenant.ULTRAFIT_APP.equals(AppUtil.getTenantFromUserContext(userContext)) ?
                com.curefit.base.enums.Tenant.ULTRAFIT_APP.toString() : com.curefit.base.enums.Tenant.SUGARFIT_APP.toString();
    }

    public PatientDetail createOrEditPatient(UserContext userContext, String patientName) {
        String userId = userContext.getUserProfile().getUserId();
        String editedPatientName = patientName.replaceAll("[^a-zA-Z ]", "").trim();
        try {
            PatientDetail selfPatient = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            if (selfPatient.getName().equals(editedPatientName)) {
                return selfPatient;
            }
            selfPatient.setName(editedPatientName);
            selfPatient.setFirstName(null);
            selfPatient.setLastName(null);
            selfPatient.setMiddleName(null);
            return serviceInterfaces.getSfAlbusClient().updatePatient(userId, selfPatient.getId().toString(), selfPatient);
        } catch (ResourceNotFoundException e) {
            PatientDetail patientDetail = new PatientDetail();
            patientDetail.setName(editedPatientName);
            patientDetail.setRelationship("Self");
            patientDetail.setCurefitUserId(userId);
            return serviceInterfaces.getSfAlbusClient().createPatient(userId, patientDetail);
        }
    }

    public PatientDetail editGenderDobPatient(UserContext userContext, Gender gender, Date dob) throws Exception {
        String userId = userContext.getUserProfile().getUserId();
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        if (dob != null) {
            LocalDateTime today = TimeUtil.getDateNow(DEFAULT_ZONE_ID);
            LocalDateTime dobDateTime = TimeUtil.getLocalDateTimeFromDate(dob, TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()));
            if (today.minusYears(18).isBefore(dobDateTime)) {
                log.error("UserId {} is below 18, rejecting", userId);
                throw new BaseException("sugar.fit subscription is valid for users above 18 years of age. Please check the date of birth entered.");
            }
        }
        NUXProfileUpdateRequest request = new NUXProfileUpdateRequest();
        List<NUXProfile> profiles = new ArrayList<>();
        if (dob != null) {
            profiles.add(NUXProfile.DATE_OF_BIRTH);
        }

        if (gender != null) {
            profiles.add(NUXProfile.GENDER);
        }

        request.setProfiles(profiles);
        request.setGender(String.valueOf(gender));
        request.setDateOfBirth(dob);
        serviceInterfaces.getSmsClient().updateNUXData(Long.valueOf(userContext.getUserProfile().getUserId()), request, timeZone);
        return new PatientDetail();
    }

    public CGMScoredMealsPage getCGMScoredMealsPage(UserContext userContext, Map<String, String> queryParams) throws ResourceNotFoundException, HttpException {
        String cgmDeviceId = queryParams.getOrDefault("cgmDeviceId", "");
        String topScored = queryParams.getOrDefault("topScored", "");
        if (isAgmInsightsV2SupportedApp(userContext)) {
            return cgmScoredMealsPageViewBuilder.buildViewV2(userContext, cgmDeviceId, Boolean.valueOf(topScored));
        }
        return cgmScoredMealsPageViewBuilder.buildView(userContext, cgmDeviceId, Boolean.valueOf(topScored));
    }

    public boolean updateOnboardingStatus(UserContext userContext, String status) throws BaseException {
        String userId = userContext.getUserProfile().getUserId();
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        smsClient.updateNUXData(Long.valueOf(userId), NUXProfileUpdateRequest.builder().profiles(Collections.singletonList(NUXProfile.EMAIL)).build(), timeZone);
        return true;
    }

    public Object assignDoctor(UserContext userContext, DoctorAssignRequest doctorAssignRequest) {
        try {
            serviceInterfaces.getSfAlbusClient().assignDoctor(
                    new AddPreferredDoctorRequest(doctorAssignRequest.getDoctorId(), null,
                            doctorAssignRequest.getDoctorType(),
                            com.curefit.base.enums.Tenant.SUGARFIT_APP.toString(), userContext.getUserProfile().getUserId()));
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public List<ScheduledPlanItem> getPatientScheduledInterventions(UserContext userContext, Long startTime, Long endTime) throws EHRClientException, ResourceNotFoundException {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        ScheduledInterventionSearchRequest searchRequest = new ScheduledInterventionSearchRequest();
        searchRequest.setPatientId(patientDetail.getId().intValue());
        searchRequest.setDateRange(new DateRange(startTime, endTime));
        searchRequest.setStatusIn(Lists.newArrayList(ScheduledPlanItem.Status.DONE, ScheduledPlanItem.Status.MISSED, ScheduledPlanItem.Status.SCHEDULED));
        return ehrClient.getInterventionsForPatient(searchRequest, "SUGARFIT");
    }

    public void saveUserCallbackRequest(CallbackRequest callbackRequest, UserContext userContext) {
        Date callbackTime;
        HashMap<String, Object> meta = new HashMap<>();
        String userId = null;
        try {
            callbackTime = new Date(callbackRequest.getDate());
            userId = userContext.getUserProfile().getUserId();
            Optional.ofNullable(userId).ifPresent(id -> meta.put("userId", id));
        } catch (Exception ignored) {
            callbackTime = new Date();
        }
        UserCallbackRequest userCallbackRequest = UserCallbackRequest.builder()
                .callbackTime(callbackTime)
                .contactNumber(callbackRequest.getPhNumber())
                .metadata(meta).build();
        serviceInterfaces.getSfAlbusClient().saveUserCallBackRequest(userId, userCallbackRequest);
    }

    private List<ChronicCareDietPlanDetail.FoodGroupUIInfo> getFoodGroupUIInfoList(List<CalorieBreakUp> calorieBreakUps) {
        if (CollectionUtils.isEmpty(calorieBreakUps)) {
            return Lists.newArrayList();
        }
        return calorieBreakUps.stream().map(this::getFoodGroupUIInfo).collect(Collectors.toList());
    }

    private ChronicCareDietPlanDetail.FoodGroupUIInfo getFoodGroupUIInfo(CalorieBreakUp calorieBreakUp) {
        if (null == calorieBreakUp || CollectionUtils.isEmpty(calorieBreakUp.getFoodgroups())) {
            return null;
        }

        String key = calorieBreakUp.getFoodgroups().stream().sorted().collect(Collectors.joining("#")) + "_" + (int) calorieBreakUp.getContribution();
        ChronicCareDietPlanDetail.FoodGroupUIInfo foodGroupUIInfo = new ChronicCareDietPlanDetail.FoodGroupUIInfo();
        foodGroupUIInfo.setKey(key);
        foodGroupUIInfo.setS3Url(foodGroupImageConfig.getS3URL(calorieBreakUp));

        FoodGroupImageConfig.LegendInfo legendInfo = foodGroupImageConfig.getLegendInfo(calorieBreakUp);
        foodGroupUIInfo.setLegendColor(legendInfo.getLegendColor());
        foodGroupUIInfo.setLegendText(legendInfo.getLegendText());
        return foodGroupUIInfo;
    }

    /**
     * @param month needs to be in range 0-11
     * @return
     */
    public GoalsHabitPageView getGoalsHabitPageView(UserContext userContext, int year, int month) {
        return this.goalsHabitPageViewBuilder.buildView(userContext, year, month);
    }

    public GoalsHomePageView getGoalsHomePageView(UserContext userContext, Map<String, String> queryParams) {
        return this.goalsHomePageViewBuilder.buildView(userContext, queryParams);

    }

    public GoalsUpdatePageView getGoalsUpdatePageView(UserContext userContext, Map<String, String> queryParams)
            throws Exception {
        return this.goalsUpdatePageViewBuilder.buildView(userContext, queryParams);

    }

    public Object updateUserGoal(UserContext userContext, UserGoalBody userGoalBody) throws ResourceNotFoundException, HttpException {
        log.info("update user goal : " + userGoalBody.toString());
        log.info("update user goal : " + (userContext.getUserProfile().getUserId()));
        //Note: For the time being it is set, please remove below line once time functionality implemented for glucose metric.
        userGoalBody.setTime(DEFAULT_TIME);
        List<UserMetricEntry> userMetricEntries = new ArrayList<>();
        for (UserGoalData userGoalData : userGoalBody.getUserGoalData()) {
            if (userGoalData.getValue() == null) {
                continue;
            }
            Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
            calendar.set(Calendar.MILLISECOND, 0);
            String[] date = userGoalBody.getDate().split("-");
            String[] time = userGoalBody.getTime().split(":");
            ObjectNode sourceMeta = getSourceMetaWithAllFields(userGoalData);
            calendar.set(Integer.parseInt(date[0]), Integer.parseInt(date[1]) - 1, Integer.parseInt(date[2]),
                    Integer.parseInt(time[0]), Integer.parseInt(time[1]), 0);
            MetricEntry metricEntry = chsClient.getActiveMetricByName(CHS_GLUCOSE_METRIC_NAME);
            if(!userGoalData.getMetricId().equals(Objects.nonNull(metricEntry) ? metricEntry.getId() : 680)){
                log.info("metricId for goal update :: {}",userGoalData.getMetricId());
                try{
                    UserMetricEntry userMetricEntry = this.getUserMetricCHS(userContext, userGoalData.getMetricId(), userGoalData.getValue(),
                            SOURCE_REF_ID, null, sourceMeta, calendar.getTime());

                    userMetricEntries.add(userMetricEntry);
                } catch (Exception e) {
                    String message = "error while fetching metric for id ::" + userGoalData.getMetricId();
                    log.error(message,e);
                    exceptionReportingService.reportWarning(message, e);
                }
            }
            else {
//                userGoalBody.setTime("00:00:00");
                log.info("metricId for sugar goal update :: {}",userGoalData.getMetricId());
                this.addSugarReading(userContext,new SugarReadingBody(userGoalData.getValue(),null,userGoalData.getSourceMeta(),userGoalBody.getDate(),userGoalBody.getTime()));
            }
        }

        try {
            if(!CollectionUtils.isEmpty(userMetricEntries)){
                chsClient.insertUserMetricGeneric(userMetricEntries);
            }
        } catch (HttpException e) {
            String message = String.format("Error in upserting metric in CHS, user id :: %s, error :: %s", userContext.getUserProfile().getUserId(),
                    e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
            return false;
        }
        return true;
    }

    private ObjectNode getSourceMetaWithAllFields(UserGoalData userGoalData) {
        if (!(userGoalData.getSourceMeta() == null || userGoalData.getSourceMeta().isNull())) {
            ObjectNode sourceMeta = (ObjectNode) userGoalData.getSourceMeta();
            if (!sourceMeta.has("tags")) {
                sourceMeta.put("tags", "");
            }
            if (!sourceMeta.has("readingComments")) {
                sourceMeta.put("readingComments", "");
            }
            return sourceMeta;
        } else {
            return null;
        }
    }

    public List<MealsSearchResponse> getMealList(UserContext userContext, String searchString) throws BaseException {
        String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        List<MealsSearchResponse> response = new ArrayList<>();
        List<DishEntry> searchList = this.ambrosiaClient.searchDishes(searchString, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
        if (CollectionUtils.isEmpty(searchList)) {
            searchList = new ArrayList<>();
        }
        addDefaultResponse(searchList, searchString);
        MealsSearchResponse searchResponse = new MealsSearchResponse();
        searchResponse.setTitle("Search results");
        searchResponse.setMealsList(searchList);
        response.add(searchResponse);
        return response;
    }

    public FreemiumSearchPageView freemiumSearch(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        String query = queryParams.getOrDefault("query", "");
        FreemiumSearchTabWidget.FreemiumSearchTabId selectedTabId = FreemiumSearchTabWidget.FreemiumSearchTabId.valueOf(
                queryParams.getOrDefault("filterType", String.valueOf(FreemiumSearchTabWidget.FreemiumSearchTabId.ALL)));
        int pageNumber = Integer.parseInt(queryParams.getOrDefault("pageNumber", String.valueOf(0)));
        int pageSize = Integer.parseInt(queryParams.getOrDefault("pageSize", String.valueOf(10)));
        String sessionId = queryParams.getOrDefault("sessionId", "");
        return freemiumSearchPageViewBuilder.buildView(userContext, query, selectedTabId, pageNumber, pageSize, sessionId);
    }

    public List<FreemiumSearchHistoryItemWidget> getFreemiumItemById(UserContext userContext, FreemiumItemsRequest freemiumItemsRequest) throws BaseException {
        List<FreemiumItemsRequest.FreemiumItem> items = freemiumItemsRequest.getItems();
        if (!CollectionUtils.isEmpty(items)) {
            return freemiumSearchPageViewBuilder.buildHistoryItems(userContext, items);
        }
        return new ArrayList<>();
    }

    public ActivitySearchPageView getActivitySearchPageView(UserContext userContext, Map<String, String> queryParams) throws Exception {
        return activitySearchPageViewBuilder.buildView(userContext, queryParams);
    }

    public MealActivitySearchPageView getMealActivitySearchPageView(UserContext userContext, Map<String, String> queryParams) throws Exception {
        return mealActivitySearchPageViewBuilder.buildView(userContext, queryParams);
    }

    public ActivityMealLogPageView getMealLoggingPage(Map<String, String> queryParams) throws HttpException {
        return activityMealLogPageViewBuilder.buildView(queryParams);
    }

    public ActivityMealFavouritesPageView getMealFavouritesPage(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        return activityMealFavouritesPageBuilder.buildView(userContext, queryParams);
    }

    public List<MeasurementUnitEntry> getDishMeasurementUnits() throws HttpException {
        return this.ambrosiaClient.filterMeasurementUnits();
    }

    public MealTimeSlotTransformResponse getMealTimeSlots(UserContext userContext,Map<String, String> queryParams) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        AppTenant tenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = TimeZone.getTimeZone(userContext.getUserProfile().getTimezone());
        long activityTime = TimeUtil.now(userContext.getUserProfile().getTimezone());
        if(queryParams.containsKey("activityTime")&& queryParams.get("activityTime")!=null && !queryParams.get("activityTime").equals(""))
            activityTime = Long.parseLong(queryParams.get("activityTime"));
        MealSlotFilterResponse data = this.sfLoggingClient.filterMealSlots(userId,activityTime,tenant,timeZone);
        List<List<MealSlotResponse>> options = new ArrayList<>();
        List<MealSlotResponse> slots = data.getMealSlotResponses();
        for (int i = 0; i < slots.size(); i += 6) {
            int end = Math.min(i + 6, slots.size());
            options.add(slots.subList(i, end));
        }
        int defaultCarouselIndex = 0;
        MealSlot defaultSelectedTimeSlot = data.getDefaultSlot();
        for (int i = 0; i < options.size(); i++) {
            List<MealSlotResponse> childSlots = options.get(i);
            for (MealSlotResponse slot : childSlots) {
                if (slot.getMealSlot().name().equals(defaultSelectedTimeSlot.name())) {
                    defaultCarouselIndex = i;
                    defaultSelectedTimeSlot = slot.getMealSlot();
                    break;
                }
            }
        }
        MealTimeSlotTransformResponse res = new MealTimeSlotTransformResponse();
        res.setData(options);
        res.setSelectedSlot(defaultSelectedTimeSlot);
        res.setDefaultCarouselIndex(defaultCarouselIndex);
        return  res;
    }



    private void addDefaultResponse(List<DishEntry> resultList, String searchQuery) {
        if (!isSearchQueryInResult(resultList, searchQuery)) {
            // no match found, create a new catalog entry
            DishEntry newDish = new DishEntry();
            String formattedName = getFormattedDishName(searchQuery);
            newDish.setName(formattedName);
            newDish.setDataSource("USER_APP");
            try {
                String generatedId = String.format("%s-%s", "MANUAL", CryptoUtil.sha1(formattedName, ""));
                newDish.setId(generatedId);
                log.info("Generated id : {} for manual entry : {}", generatedId, formattedName);
            } catch (Exception e) {
                String message = String.format("Error in creating id for meal logging item :: %s, error :: %s", formattedName, e.getMessage());
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }
            newDish.setUnits(DEFAULT_DIET_UNITS);
            log.info(String.format("for search query :: %s, created new dish with name :: %s", searchQuery, formattedName));
            resultList.add(0, newDish);
        }
    }

    private String getFormattedDishName(String searchQuery) {
        try {
            String cleanedSearchQuery = StringUtils.trimLeadingWhitespace(StringUtils.trimTrailingWhitespace(searchQuery));
            List<String> dishWords = Stream.of(cleanedSearchQuery.split("\\s")).map(String::toLowerCase).collect(Collectors.toList());
            StringBuilder sb = new StringBuilder();
            for (String dishWord : dishWords) {
                String trimmedWord = StringUtils.trimLeadingWhitespace(StringUtils.trimTrailingWhitespace(dishWord));
                if (StringUtils.hasText(trimmedWord)) {
                    if (sb.length() != 0) {
                        sb.append(' ');
                    }
                    sb.append(trimmedWord.substring(0, 1).toUpperCase());
                    sb.append(trimmedWord.substring(1));
                }
            }
            return sb.toString();
        } catch (Exception e) {
            log.error(String.format("Error in formatting dishname :: %s, error : %s", searchQuery, e.getMessage()), e);
            return searchQuery;
        }
    }

    private boolean isSearchQueryInResult(List<DishEntry> resultList, String searchQuery) {
        if (!StringUtils.hasText(searchQuery)) {
            log.info(String.format("search query :: %s, is empty", searchQuery));
            return true;
        }
        String cleanedQuery = StringUtils.trimTrailingWhitespace(StringUtils.trimLeadingWhitespace(searchQuery));
        List<String> allNames = new ArrayList<>();
        for (DishEntry result : resultList) {
            allNames.add(result.getName());
            if (result.getAlternateNames() != null) {
                allNames.addAll(result.getAlternateNames());
            }
        }
        for (String name : allNames) {
            if (name.equalsIgnoreCase(cleanedQuery)) {
                log.info(String.format("search query :: %s, present in results :: %s", cleanedQuery, allNames));
                return true;
            }
        }
        log.info(String.format("search query :: %s, not present in results :: %s", cleanedQuery, allNames));
        return false;
    }

    public UserActivityEntry updateInterventionActivity(UserContext userContext, InterventionsStatusUpdateRequest req) throws Exception {
        return this.chronicCareServiceHelper.updateInterventionActivity(userContext, req);
    }


    public ActivityLogsPageView getActivityLogsPageView(UserContext userContext, Map<String, String> queryParams) throws ParseException {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()));
        LocalDate currentDate = LocalDate.now();
        Date endDate = queryParams.containsKey("endDate")
                ? simpleDateFormat.parse(queryParams.get("endDate"))
                : Date.from(currentDate.atStartOfDay().atZone(ZoneId.of(userContext.getUserProfile().getTimezone())).toInstant());
        Date fromDate = queryParams.containsKey("startDate")
                ? simpleDateFormat.parse(queryParams.get("startDate"))
                : new DateTime(endDate).minusDays(6).toDate();
        Date toDate = new Date(endDate.getTime() + (1000 * 60 * 60 * 24));

        long fromDateEpoch = fromDate.getTime();
        long toDateEpoch = toDate.getTime();

        try {
            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            List<CGMStat.PatientActivityImpact> activityLogs = searchActivities(patientDetail, null,
                    fromDateEpoch, toDateEpoch);
            return activityLogsPageViewBuilder.buildView(userContext, queryParams, activityLogs);
        } catch (ResourceNotFoundException | EHRClientException e) {
            log.error(String.format("Error in searchActivities call :: params : %s, error : %s", queryParams, e.getMessage()), e);
        }

        return new ActivityLogsPageView();
    }

    public ActivityLogsPageView getActivityLogsPageViewV2(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()));
            LocalDate currentDate = LocalDate.now();
            Date endDate = queryParams.containsKey("endDate")
                    ? simpleDateFormat.parse(queryParams.get("endDate"))
                    : Date.from(currentDate.atStartOfDay().atZone(ZoneId.of(userContext.getUserProfile().getTimezone())).toInstant());
            Date fromDate = queryParams.containsKey("startDate")
                    ? simpleDateFormat.parse(queryParams.get("startDate"))
                    : new DateTime(endDate).minusDays(6).toDate();
            Date toDate = new Date(endDate.getTime() + (1000 * 60 * 60 * 24));

            long fromDateEpoch = fromDate.getTime();
            long toDateEpoch = toDate.getTime();
            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            List<PatientActivityImpact> activityLogs = searchActivitiesV2(userContext, patientDetail, null,
                    fromDateEpoch, toDateEpoch);
            CompletableFuture<Optional<ActivePackResponse>> activePackResponseFuture = userOnboardingService.getSugarFitActivePackFuture(serviceInterfaces, userContext.getUserProfile().getUserId());
            Optional<ActivePackResponse> activePackResponse = activePackResponseFuture.get();
            return activityLogsPageViewBuilderV2.buildView(userContext, queryParams, activityLogs, activePackResponse);
        } catch (ResourceNotFoundException | ParseException | InterruptedException | ExecutionException e) {
            log.error(String.format("Error in searchActivities call :: params : %s, error : %s", queryParams, e.getMessage()), e);
        }

        return new ActivityLogsPageView();
    }

    public ActivityLogsPageView getActivityLogsPageViewV3(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()));
            LocalDate currentDate = LocalDate.now();

            com.sugarfit.ambrosia.enums.PatientActivityType patientActivityType = null ;

            String selectedFilters = queryParams.getOrDefault("selectedFilters", null);
            if (selectedFilters != null && !selectedFilters.equals("")) {
                String[] selectedFiltersArray = selectedFilters.split(" ");
                if (selectedFiltersArray.length > 0) {
                    patientActivityType = com.sugarfit.ambrosia.enums.PatientActivityType.valueOf(selectedFiltersArray[0]);
                }
            }

            Date endDate = null;
            if (queryParams.containsKey("endDate")) {
                endDate = simpleDateFormat.parse(queryParams.get("endDate"));
            } else if (patientActivityType == com.sugarfit.ambrosia.enums.PatientActivityType.SLEEP) {
                Date tempD = Date.from(currentDate.atStartOfDay().atZone(ZoneId.of(userContext.getUserProfile().getTimezone())).toInstant());
                Calendar c = Calendar.getInstance();
                c.setTime(tempD);
                c.add(Calendar.DATE, -1);
                endDate = c.getTime();
                queryParams.put("endDate", simpleDateFormat.format(endDate.getTime()));
            } else {
                endDate = Date.from(currentDate.atStartOfDay().atZone(ZoneId.of(userContext.getUserProfile().getTimezone())).toInstant());
            }

            Date fromDate = queryParams.containsKey("startDate")
                    ? simpleDateFormat.parse(queryParams.get("startDate"))
                    : new DateTime(endDate).minusDays(6).toDate();
            Date toDate = new Date(endDate.getTime() + (1000 * 60 * 60 * 24));

            long fromDateEpoch = fromDate.getTime();
            long toDateEpoch = toDate.getTime();
            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            List<PatientActivityImpact> activityLogs = searchActivitiesV2(userContext, patientDetail, patientActivityType,
                    fromDateEpoch, toDateEpoch);
            CompletableFuture<Optional<ActivePackResponse>> activePackResponseFuture = userOnboardingService.getSugarFitActivePackFuture(serviceInterfaces, userContext.getUserProfile().getUserId());
            Optional<ActivePackResponse> activePackResponse = activePackResponseFuture.get();
            return activityLogsPageViewBuilderV3.buildView(userContext, queryParams, activityLogs, activePackResponse);
        } catch (ResourceNotFoundException | ParseException | ExecutionException | InterruptedException e) {
            log.error(String.format("Error in searchActivities call :: params : %s, error : %s", queryParams, e.getMessage()), e);
        }

        return new ActivityLogsPageView();
    }

    private List<CGMStat.PatientActivityImpact> searchActivities(PatientDetail patientDetail,
                                                                 PatientActivityType patientActivityType,
                                                                 long fromDate,
                                                                 long toDate) throws EHRClientException {
        PatientActivitySearchRequest patientActivitySearchRequest = new PatientActivitySearchRequest();
        patientActivitySearchRequest.setPatientId(patientDetail.getId());
        patientActivitySearchRequest.setPatientActivityType(patientActivityType);
        patientActivitySearchRequest.setFromDateEpoch(fromDate);
        patientActivitySearchRequest.setToDateEpoch(toDate);
        return this.serviceInterfaces.getSfAlbusClient().searchPatientActivities(patientActivitySearchRequest, TENANT);
    }

    private List<PatientActivityImpact> searchActivitiesV2(UserContext userContext, PatientDetail patientDetail,
                                                           com.sugarfit.ambrosia.enums.PatientActivityType patientActivityType,
                                                           long fromDate,
                                                           long toDate) throws HttpException {
        com.sugarfit.ambrosia.pojo.external.PatientActivitySearchRequest patientActivitySearchRequest = new
                com.sugarfit.ambrosia.pojo.external.PatientActivitySearchRequest();
        patientActivitySearchRequest.setPatientId(patientDetail.getId());
        patientActivitySearchRequest.setPatientActivityType(patientActivityType);
        patientActivitySearchRequest.setFromDateEpoch(fromDate);
        patientActivitySearchRequest.setToDateEpoch(toDate);
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = TimeZone.getTimeZone(userContext.getUserProfile().getTimezone());

        return isUltraFitApp(userContext) ?
                this.sfLoggingClient.filterPatientActivitiesWithImpactfulActivities(patientActivitySearchRequest, appTenant, timeZone) :
                this.sfLoggingClient.filterPatientLoggedActivities(patientActivitySearchRequest, appTenant, timeZone);

    }

    public Boolean updateDisclaimer(UserContext userContext, HomepageDisclaimer disclaimer) throws HttpException {
        DefaultSuccessResponse defaultSuccessResponse = chsClient.updateCgmDisclaimerStatus(Long.valueOf(userContext.getUserProfile().getUserId()), disclaimer.getAcceptDisclaimer(), getAppTenantFromUserContext(userContext));
        return defaultSuccessResponse.isSuccess();
    }

    public SubscriptionPageView getSubscriptionPage(UserContext userContext) throws ResourceNotFoundException, HttpException {
        return this.subscriptionPageViewBuilder.buildView(userContext);
    }

    public RenewSubscriptionPageView getRenewSubscriptionPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return this.renewSubscriptionPageViewBuilder.buildView(userContext);
    }

    public RenewalJourneyPacksPageView getRenewalJourneyPacksPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return this.renewalJourneyPacksPageViewBuilder.buildView(userContext);
    }

    public RenewSubscriptionSuccessPageView getRenewSubscriptionSuccessPage(UserContext userContext) throws BaseException {
        return this.renewSubscriptionSuccessPageViewBuilder.buildView(userContext);
    }

    public Boolean addCustomRenewalPack(UserContext userContext, CustomRenewalPackRequest customRenewalPackRequest) throws BaseException {
        try{
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            customRenewalPackRequest.setUserId(userId);
            lmsClient.addCustomRenewalPack(customRenewalPackRequest);
            return true;
        } catch (Exception exec) {
            exceptionReportingService.reportException(exec);
        }
        return false;
    }

    public SubscriptionPageView getSuccessPage(UserContext userContext, Map<String, String> queryParams) throws ResourceNotFoundException, HttpException {
        return this.successPageViewBuilder.buildView(userContext, queryParams);
    }

    public Boolean updateRagusMigrationDisclaimer(UserContext userContext) throws BaseException {

        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
        userAttributeEntry.setUserId(Long.parseLong(userContext.getUserProfile().getUserId()));
        userAttributeEntry.setAppTenant(getAppTenantFromUserContext(userContext));
        userAttributeEntry.setAttribute("sf-ragus-migration-disclaimer");
        userAttributeEntry.setAttrValue("ACCEPTED");
        userAttributeEntry.setDataType(DataType.STRING);
        this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, getAppTenantFromUserContext(userContext));
        return true;
    }

    public Boolean saveContactDetailsOnServer(UserContext userContext, SFContactDetails contactDetails) throws BaseException, JsonProcessingException {
        String userId = userContext.getUserProfile().getUserId();
        try {
            UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
            userAttributeEntry.setUserId(Long.parseLong(userId));
            userAttributeEntry.setAppTenant(AppTenant.SUGARFIT);
            userAttributeEntry.setAttribute("sf-saved-coach-contact-details");
            userAttributeEntry.setAttrValue(objectMapper.writeValueAsString(contactDetails));
            this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, AppTenant.SUGARFIT);
        } catch (Exception exec) {
            log.info("saveContactDetailsOnServer failed for USER-ID: " + userId + " EXCEPTION: "+exec.getLocalizedMessage());
            exceptionReportingService.reportException(exec);
            return false;
        }
        return true;
    }

    public void updateFuturePackStartDate(UserContext userContext, Long startDateInMillis) throws Exception {
        String userId = userContext.getUserProfile().getUserId();
        String timezone = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        Optional<ActivePackResponse> activePack = this.userOnboardingService.getSugarFitActivePackForHome(userId);
        Optional<ActivePackResponse> upcomingPack = this.userOnboardingService.getSugarFitUpcomingPack(userId);
            if (upcomingPack.isPresent() || activePack.isPresent()) {
                ActivePackResponse currentPack = upcomingPack.orElseGet(activePack::get);
                Date now = new Date();
                if (startDateInMillis > now.getTime()) {
                    long maxStartDateDelay = chronicCareServiceHelper.getMaxStartDateDelay(currentPack.getBundleProduct());
                    long maxStartDate = currentPack.getPurchaseDate() + (maxStartDateDelay * MILLIS_IN_A_DAY);
                    if (startDateInMillis > maxStartDate) {
                        throw new BaseException(String.format("Date cannot be extended beyond :: %s", new Date(maxStartDate)));
                    }
                }
                Date startDate = TimeUtil.getStartOfDay(new Date(startDateInMillis), timezone);
                MembershipUpdateRequest membershipUpdateRequest = new MembershipUpdateRequest(startDate, null, null, MEMBERSHIP_UPDATE_REASON, RandomStringUtils.random(10, true, false));
                this.serviceInterfaces.getSfAlbusClient().updateUserMembership(null, currentPack.getBookingId(), membershipUpdateRequest);
            } else {
                throw new BaseException(String.format("No upcoming or active subscription for user :: %s", userId));
            }
    }

    public List<ReferralBenefitEntry> fetchReferralBenefits(UserContext userContext) throws BaseException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        return referralClient.getReferralBenefits(appTenant);
    }

    public UserReferralEntry createUserReferral(UserReferralEntry userReferralEntry, UserContext userContext) throws BaseException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        userReferralEntry.setUserId(userId);
        userReferralEntry.setTenant(appTenant);

        UserReferralEntry referralEntry = referralClient.addReferral(userReferralEntry);
        if (isReferralChallengeSupportedApp(userContext)) {
            return referralEntry;
        } else {
            if (referralEntry.getIsNewReferral()) {
                return referralEntry;
            } else {
                String errorMessage = "Someone has already referred " +
                        referralEntry.getName() + ", " + referralEntry.getPhone() +
                        ". You can check out the “My Referrals” section below, in case this referral was shared by you.";
                throw new BaseException(errorMessage, LogType.INFO, ErrorUtil.CFAPI_NAMESPACE,
                        AppStatus.RESOURCE_EXISTS, errorMessage, errorMessage);
            }
        }
    }

    public List<UserReferralEntry> createBulkReferral(List<UserReferralEntry> userReferralEntries, UserContext userContext) throws BaseException {
        try {
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            for(UserReferralEntry entry : userReferralEntries){
                entry.setUserId(userId);
                entry.setTenant(appTenant);
            }

            return referralClient.addReferralBulk(userReferralEntries);
        }
        catch (Exception e) {
            exceptionReportingService.reportException("error in creating bulk referral", e);
            log.info("Error creating bulk referrals, userId: {}, error: {}", userContext.getUserProfile().getUserId(), e.getMessage());
            log.info("Error stacktrace: {}", Arrays.toString(e.getStackTrace()));
        }
        return new ArrayList<>();
    }

    public UserReferralEntry createUserReferralUsingCode(UserReferralEntry userReferralEntry, UserContext userContext) throws BaseException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        userReferralEntry.setUserId(userId);
        userReferralEntry.setTenant(appTenant);
        return referralClient.addReferralUsingCode(userReferralEntry);
    }

    public UserReferralCodeMapEntry fetchUserReferralCode(UserContext userContext) throws BaseException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return referralClient.getUserReferralCode(userId, appTenant);
    }

    public UserReferralCodeMapEntry validateUserReferralCode(String code) throws BaseException {
        return referralClient.validateUserReferralCode(code);
    }

    public List<ReferralRewardEntry> fetchReferralRewards(UserContext userContext) throws BaseException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        return referralClient.getReferralRewards(appTenant);
    }

    public SfReferralPageView fetchReferralPage(UserContext userContext) throws BaseException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);

        ReferralConfigEntry referralConfig = referralClient.getReferralConfig(appTenant);

        SfReferralPageView sfReferralPageView = new SfReferralPageView();

        if(!ChronicCareAppUtil.isSfReferralPageV2SupportedAppVersion(userContext)){
            sfReferralPageView.setTitle(referralConfig.getDetailPageTitle());
            sfReferralPageView.setSubTitle(referralConfig.getDetailPageSubtitle());
            sfReferralPageView.setHasWhatsApp(false);
            sfReferralPageView.setHomepageImageUrl(referralConfig.getHomepageImageUrl());
            sfReferralPageView.setDetailPageImageUrl(referralConfig.getDetailPageImageUrl());
            sfReferralPageView.setInterstitialImageUrl(referralConfig.getInterstitialImageUrl());
            return sfReferralPageView;
        }

        sfReferralPageView.setTitle("Help India be Diabetes Free");
        sfReferralPageView.setSubTitle("Earn ₹4000* on each referral");

        sfReferralPageView.setGoalBannerImageUrl("image/chroniccare/referral/goalBannerImage_v2.png");
        sfReferralPageView.setGoalAchievedNumber(ChronicCareAppUtil.getReferralGoalAchievedValue());
        sfReferralPageView.setGoalAchievedTitle("People Referred");
        sfReferralPageView.setGoalTargetNumber(ChronicCareAppUtil.getReferralGoalTargetValue());
        sfReferralPageView.setGoalTargetTitle("Monthly Sugar.fit Goal");

        sfReferralPageView.setEarningsSectionTitle("Your Earnings");
        sfReferralPageView.setEarningBtnSubText("People earned upto ₹30k");

        sfReferralPageView.setInputSectionTitle("Refer your Friend");
        sfReferralPageView.setInputSubmitBtnTitle("Refer your Friend");
        sfReferralPageView.setHasWhatsApp(false);

        sfReferralPageView.setInstructionSectionTitle("How to refer");
        sfReferralPageView.addInstructionPoint(new SfReferralPageView.InstructionPoint("Share your referral link and have your friend purchase using it.", "image/chroniccare/referral/icn_human.png"));
        sfReferralPageView.addInstructionPoint(new SfReferralPageView.InstructionPoint("Earn @{PRICE} for purchases above @{PRICE} earn @{PRICE} for purchases between @{PRICE} and @{PRICE}","image/chroniccare/referral/icn_wallet.png", new ArrayList<>(){{
            add("₹4,000");
            add("₹19,900;");
            add("₹2,000");
            add("₹11,990");
            add("₹19,900");
        }}));
        sfReferralPageView.addInstructionPoint(new SfReferralPageView.InstructionPoint("Referral rewards are valid if the purchase is not canceled within 15 days.", "image/chroniccare/referral/icn_offer.png"));
        sfReferralPageView.addInstructionPoint(new SfReferralPageView.InstructionPoint("Benefits are valid for 30 days; Sugar.fit can change or cancel the program anytime.", "image/chroniccare/referral/icn_finger.png"));

        String userId = userContext.getUserProfile().getUserId();
        boolean isFreemium = this.serviceInterfaces.chronicCareServiceHelper.isSfFreemiumUser(userContext);
        BundleProduct activeBundle = null;
        Optional<ActivePackResponse> activePackResponseOptional = this.userOnboardingService.getSugarFitActivePack(userId);
        if (activePackResponseOptional.isPresent()) {
            activeBundle = activePackResponseOptional.get().getBundleProduct();
        }
        if (DigitalAppUtil.isDigitalAppUser(activeBundle, isFreemium)){
            sfReferralPageView.setTitle("Be the friend who saves life");
            sfReferralPageView.setSubTitle(null);
            sfReferralPageView.setGoalBannerImageUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/referral_banner_digi-2025-03-17-11:58.png");
        }

        return sfReferralPageView;
    }

    public ReferralSummary getReferralSummary(UserContext userContext) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            return referralClient.getReferralSummary(userId);
        } catch (Exception e) {
            exceptionReportingService.reportException("error in fetching user referrals", e);
            log.info("Error fetching user referrals: {}", e.getMessage());
            log.info("Error stack: {}", Arrays.toString(e.getStackTrace()));
        }
        return null;
    }

    public List<UserReferralEntry> fetchUserReferrals(UserContext userContext) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return referralClient.fetchUserReferrals(userId);
    }

    public UserReferralRewardEntry addUserReferralReward(UserReferralRewardEntry userRefReward, UserContext userContext) throws BaseException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        userRefReward.setUserId(userId);
        userRefReward.setTenant(appTenant);
        return referralClient.addUserReferralReward(userRefReward);
    }

    public BaseResponse addRawHealthData(@RequestBody RawHealthDataEntry rawHealthDataEntry) throws BaseException {
        return chsClient.addRawHealthData(rawHealthDataEntry);
    }

    public VitalPageView getVitalPageView(UserContext userContext, Map<String, String> queryParams) throws ResourceNotFoundException {
        return this.vitalPageViewBuilder.buildView(userContext, queryParams);
    }

    public HealthPageView getHealthPageView(UserContext userContext) throws ResourceNotFoundException, HttpException {
        return this.healthPageViewBuilder.buildView(userContext);
    }

    public ActiveDeviceEntry updateActiveDevice(UserContext userContext, Map<String, String> queryParams) throws ResourceNotFoundException, HttpException {
        ActiveDeviceEntry activeDeviceEntry = new ActiveDeviceEntry();
        activeDeviceEntry.setUserId(Long.parseLong(userContext.getUserProfile().getUserId()));
        if (!queryParams.containsKey("deviceId")) {
            throw new ResourceNotFoundException("deviceId is missing");
        }
        activeDeviceEntry.setDeviceId(queryParams.get("deviceId"));
        AppTenant tenant = getAppTenantFromUserContext(userContext);
        return this.chsClient.updateActiveDevice(activeDeviceEntry, tenant);
    }

    public Boolean addFitnessData(UserContext userContext, VitalAddData data) {

        Long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        String deviceId = userContext.getSessionInfo().getDeviceId();
        try {
            ActiveDeviceEntry activeDeviceEntry = this.chsClient.fetchActiveDevice(userId);
            if (activeDeviceEntry.getDeviceId() != null)
                deviceId = activeDeviceEntry.getDeviceId();
        } catch (Throwable e) {
            exceptionReportingService.reportException("failed fetching active device:", e);
        }
        String pageId = data.getPageId();

        try {
            if (pageId.equals("heartRate")) {
                HeartRateData req = new HeartRateData();
                req.setValue(data.getValue());
//                req.setUserId(4080998L);
//                req.setDeviceId("C3D7C3A7-BAC0-44F5-ADC1-51625ECF56E9");
                req.setDeviceId(deviceId);
                req.setUserId(userId);
                req.setStartDate(data.getStartTime());
                req.setEndDate(data.getEndTime());
                this.chsClient.addHeartRateData(req);
                return true;
            } else if (pageId.equals("steps")) {
                StepsData req = new StepsData();
                req.setValue(data.getValue());
                req.setDeviceId(deviceId);
                req.setUserId(userId);
                req.setStartDate(data.getStartTime());
                req.setEndDate(data.getEndTime());
                this.chsClient.addStepsData(req);
                return true;

            } else if (pageId.equals("calorie")) {
                CalorieData req = new CalorieData();
                req.setValue(data.getValue());
                req.setStartDate(data.getStartTime());
                req.setEndDate(data.getEndTime());
                req.setType("TOTAL");
                req.setDeviceId(deviceId);
                req.setUserId(userId);
                this.chsClient.addCalorieData(req);
                return true;
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("error in adding vital add  from add page:" + "pageId", e);
            return false;
        }
        return false;

    }

    public FitnessDeviceSyncMeta fitnessDeviceConfig(UserContext userContext) {
        return chronicCareServiceHelper.getFitnessDeviceSyncMeta(userContext);
    }

    public ImpactPageView getImpactPage(UserContext userContext, Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        return this.impactPageViewBuilder.buildView(userContext, queryParams);
    }
    public ImpactPageView getImpactPageV2(UserContext userContext, Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        return this.impactPageViewBuilder.buildViewV2(userContext, queryParams);
    }

    public List<PatientActivityLoggingCompareEntry> getMoreAcitivtyItems(UserContext userContext, Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        if (queryParams.containsKey("type") && queryParams.containsKey("pageNumber")) {
            String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
            com.sugarfit.ambrosia.enums.PatientActivityType type = com.sugarfit.ambrosia.enums.PatientActivityType.valueOf(queryParams.get("type"));
            int pageNumber = Integer.parseInt(queryParams.get("pageNumber"));
//            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
//            Long patientId = patientDetail.getId();
            UserActivityBulkFilterRequest request = new UserActivityBulkFilterRequest();
            request.setPageNumber(pageNumber);
            request.setPageSize(PAGE_SIZE);
            request.setUserId(userContext.getUserProfile().getUserId());
            request.setPatientActivityTypes(List.of(type));
            return this.sfLoggingClient.fetchAllPatientActivitiesForComparison(request, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
        }
        return new ArrayList<>();
    }

    public List<ComparableActivityLogResponse<BaseActivityLogEntry>> getMoreAcitivtyItemsV2(UserContext userContext, Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        List<ComparableActivityLogResponse<BaseActivityLogEntry>> resultList = new ArrayList<>();
        if (queryParams.containsKey("type") && queryParams.containsKey("pageNumber")) {
            String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
            ActivityType type = ActivityType.valueOf(queryParams.get("type"));
            int pageNumber = Integer.parseInt(queryParams.get("pageNumber"));
//            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
//            Long patientId = patientDetail.getId();
            ComparableActivitiesRequest request = new ComparableActivitiesRequest();
            request.setPageNumber(pageNumber);
            request.setPageSize(PAGE_SIZE);
            request.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            request.setActivityType(type);
            ComparableActivitiesResponse res = this.sfLoggingClient.filterComparableActivities(request,
                    getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
            if(res!=null && res.getComparableActivities()!=null){
                resultList = res.getComparableActivities();
            }
        }
        return resultList;
    }

    public ImpactCompareResultPageView getImpactCompareResultPage(UserContext userContext, ActivityComparisonRequest activityComparisonRequest) throws HttpException, ResourceNotFoundException {
        return this.impactCompareResultPageViewBuilder.buildView(userContext, activityComparisonRequest);
    }

    public ImpactCompareResultPageView getImpactCompareResultPageV2(UserContext userContext, ActivityLogComparisonRequest activityComparisonRequest) throws HttpException, ResourceNotFoundException {
        return this.impactCompareResultPageViewBuilder.buildViewV2(userContext, activityComparisonRequest);
    }

    public CGMTimelinePageView getCGMTimelinePage(UserContext userContext, Map<String, String> queryParams) throws ResourceNotFoundException, HttpException, ParseException {
        return this.cgmTimelinePageViewBuilder.buildView(userContext, queryParams);
    }

    public MetabolicScoreResponse fetchLatestMetabolicScore(UserContext userContext) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezone = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        AppTenant tenant = getAppTenantFromUserContext(userContext);
        return this.chsClient.fetchLatestMetabolicScore(userId, tenant, TimeZone.getTimeZone(timezone));
    }

    public BgInsightsSummary fetchLatestInsightsSummary(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezone = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        AppTenant tenant = getAppTenantFromUserContext(userContext);
        if (queryParams.containsKey("date")) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sdf.setTimeZone(TimeZone.getTimeZone(timezone));
            Date date = new Date(sdf.parse(queryParams.get("date")).getTime());
            Float appVersion = userContext.getSessionInfo().getAppVersion();
            BgInsightsSummaryRequest req = new BgInsightsSummaryRequest();
            req.setUserId(userId);
            req.setAppVersion(appVersion);
            req.setDate(date);
            return this.chsClient.fetchCgmInsightsSummary(req, tenant, TimeZone.getTimeZone(timezone));
        }
        BgInsightsSummary failedState = new BgInsightsSummary();
        failedState.setIsLatest(false);
        return failedState;
    }

    public BgInsightDetailedResponse fetchCgmInsightDetails(UserContext userContext, @RequestParam Map<String, String> queryParams) throws HttpException, ParseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezone = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        AppTenant tenant = getAppTenantFromUserContext(userContext);
        String activityId = queryParams.getOrDefault("activityId", null);
        if (queryParams.containsKey("date") && queryParams.containsKey("eventType")) {
            BgInsightDetailsFilterRequest req = new BgInsightDetailsFilterRequest();
            req.setUserId(userId);
            log.info(queryParams.get("date"));
            Long date = Long.valueOf(queryParams.get("date"));
            req.setDate(new Date(date));
            req.setEventType(InsightEventType.valueOf(queryParams.get("eventType")));
            if (activityId != null) {
                req.setActivityId(activityId);
            }
            req.setNocturnalAsPrevDay(true);
            return this.chsClient.fetchCgmInsightDetails(req, tenant, TimeZone.getTimeZone(timezone));
        }
        return null;

    }

    public com.sugarfit.experiment.pojo.StaticData fetchExperimentStaticData(UserContext userContext) throws HttpException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        return experimentClient.fetchStaticData(appTenant);
    }

    public ExperimentCollectionPageView getExperimentCollection(UserContext userContext, UserExperimentCollectionFilterRequest experimentFilterRequest) {
        return experimentCollectionPageViewBuilder.buildView(userContext, experimentFilterRequest);
    }

    public MyExperimentsPageView getMyExperimentsPage(UserContext userContext, Date date) {
        return myExperimentsPageViewBuilder.buildView(userContext, date);
    }

    public List<UfExperimentCardWidget> getOngoingExperiments(UserContext userContext, Date date) {
        return ongoingExperimentsViewBuilder.buildView(userContext, date);
    }

    public Experiment fetchExperiment(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        String experimentId = queryParams.get("experimentId");
        return experimentClient.fetchExperiment(experimentId, getAppTenantFromUserContext(userContext));
    }

    public UserExperiment joinExperiment(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        Long experimentId = Long.valueOf(queryParams.get("experimentId"));
        JoinExperimentRequest request = new JoinExperimentRequest();
        request.setExperimentId(experimentId);
        request.setUserId(userId);
        return experimentClient.joinExperiment(request, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
    }

    public UserExperiment unjoinExperiment(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        Long userExperimentId = Long.valueOf(queryParams.get("userExperimentId"));
        String unjoinReason = queryParams.get("reason");
        UnjoinExperimentRequest request = new UnjoinExperimentRequest();
        request.setUserExperimentId(userExperimentId);
        request.setUserId(userId);
        request.setReason(unjoinReason);
        return experimentClient.unjoinExperiment(request, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
    }

    public UserExperimentActionResponse markExperimentActivity(UserContext userContext, UserExperimentAction userExperimentAction) throws HttpException {
        String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        userExperimentAction.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        return experimentClient.markExperimentAction(userExperimentAction, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
    }

    public UserExperimentActionResponse undoExperiment(UserContext userContext, Map<String, String> queryParams, Long experimentTime) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        Long userExperimentId = Long.valueOf(queryParams.get("userExperimentId"));
        Date experimentDay = new Date(experimentTime);
        UserExperimentActionUndoRequest request = new UserExperimentActionUndoRequest();
        request.setUserExperimentId(userExperimentId);
        request.setUserId(userId);
        request.setExperimentDay(experimentDay);
        return experimentClient.undoExperimentAction(request, getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(timezoneId));
    }

    public ExperimentResultPageView getExperimentResultPage(UserContext userContext, Map<String, String> queryParams) throws HttpException, ResourceNotFoundException {
        return this.experimentResultPageViewBuilder.buildView(userContext, queryParams);
    }

    public UserExperimentResult fetchRawExperimentResult(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezoneId = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        Long userExperimentId = Long.valueOf(queryParams.get("userExperimentId"));
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        return this.experimentClient.fetchExperimentResult(userId, userExperimentId, appTenant, TimeZone.getTimeZone(timezoneId));
    }

    public boolean allowCultFitness(UserContext userContext) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        CultClassAccessMapEntry result = this.sfFitnessClient.grantCultClassAccessPermission(userId);
        return result.getIsPermitted();
    }

    public boolean hideCultFitness(UserContext userContext) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        CultClassAccessMapEntry result = this.sfFitnessClient.revokeCultClassAccessPermission(userId);
        return !result.getIsPermitted();
    }

    public FitnessDevicesPageView fetchFitnessAppsAndDevicesPage(UserContext userContext) {
        return this.fitnessDevicesPageViewBuilder.buildView(userContext);
    }

    public boolean updateResearchAccess(UserContext userContext, Boolean accessGranted) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        AppTenant appTenant = getAppTenantFromUserContext(userContext);

        UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
        userAttributeEntry.setUserId(userId);
        userAttributeEntry.setAppTenant(appTenant);
        userAttributeEntry.setAttribute("research-studies-access-granted");
        userAttributeEntry.setAttrValue(accessGranted);
        userAttributeEntry.setDataType(DataType.BOOLEAN);
        this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
        return true;
    }

    public SfWellnessAtGlanceWidget fetchWellnessAtGlanceWidget(UserContext userContext) {
        return this.chronicCareHomePageViewBuilder.getWellnessAtGlanceWidget(userContext);
    }

    public String mailGlucoseReadings(UserContext userContext) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String timezone = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        return chsClient.mailGlucoseReadings(userId, appTenant, TimeZone.getTimeZone(timezone));
    }

    public SupportPageView getHelpAndSupportPage(UserContext userContext) {
        return this.supportPageViewBuilder.buildView(userContext);
    }

    public SugarLoggingPageView getSugarLoggingPage(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        return this.sugarLoggingPageViewBuilder.buildView(userContext,queryParams);
    }

    public GlucometerReadingEntry saveGlucometerReadings(UserContext userContext, GlucometerReadingEntry request) throws HttpException {
        String timezone = userContext.getUserProfile().getTimezone() != null ? userContext.getUserProfile().getTimezone() : "Asia/Kolkata";
        request.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        return this.chsClient.saveGlucometerReadings(request, TimeZone.getTimeZone(timezone));
    }

    public DefaultSuccessResponse deleteGlucometerReadings(Map<String, String> queryParams) throws HttpException {
        String id = queryParams.getOrDefault("id", "");
        if (id == null || id.equals(""))
            return new DefaultSuccessResponse();
        return this.chsClient.deleteGlucometerReadings(Long.valueOf(id));
    }

    public SugarJournalV2PageView getSugarJournalV2page(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        return this.sugarJournalV2PageViewBuilder.buildView(userContext,queryParams);
    }

    public SugarLogListWidget getSugarLogWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        return this.sugarJournalV2PageViewBuilder.getSugarLogWidgetData(userContext,queryParams);
    }

    public SLSugarLogListWidget getSLSugarLogWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        return this.sugarJournalV2PageViewBuilder.getSLSugarLogWidgetData(userContext,queryParams);
    }
    public CgmStorePageView getCgmStorePage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return this.cgmStorePageViewBuilder.buildView(userContext, queryParams);
    }

    public GlucoseTrendDeviationWidget getTrendDeviationWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        return this.sugarJournalV2PageViewBuilder.getTrendDeviationWidgetData(userContext,queryParams);
    }

    public GlucoseTrendWidget getTrendWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        return this.sugarJournalV2PageViewBuilder.getTrendWidgetData(userContext,queryParams);
    }

    public SLGlucoseTrendWidget getSLTrendWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        return this.sugarJournalV2PageViewBuilder.getSLTrendWidgetData(userContext,queryParams);
    }

    public GlucoseAverageReadingWidget getMonthAverageReadingWidgetData(UserContext userContext, Map<String, String> queryParams) throws HttpException, ParseException {
        return this.sugarJournalV2PageViewBuilder.getMonthAverageReadingWidgetData(userContext,queryParams);
    }

    public ReferralInterstitialConfig getReferralInterstitials(UserContext userContext) {
        return this.referralInterstitialConfigBuilder.buildConfig(userContext);
    }

    public Object getVoiceLoggingConfig(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        Map<String, Object> voiceLoggingConfig = new HashMap<>();
        ArrayList<com.sugarfit.ambrosia.enums.PatientActivityType> voiceLoggingEnabledActivities = new ArrayList<>();

        boolean isVoiceMealLoggingEnabled = !isUltraFitApp(userContext) && isVoiceMealLoggingEnabledForUser(userContext);
        if (isVoiceMealLoggingEnabled) {
            voiceLoggingEnabledActivities.add(com.sugarfit.ambrosia.enums.PatientActivityType.NUTRITION);
        }

        voiceLoggingConfig.put("voiceLoggingEnabledActivities", voiceLoggingEnabledActivities);
        return voiceLoggingConfig;
    }

    boolean isVoiceMealLoggingEnabledForUser(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            return userSegments.contains(VOICE_BASED_MEAL_LOGGING_ENABLED_USERS);
        } catch (Exception e) {
            String message = String.format("voice meal logging segment error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    public SfConsultationsPageView getConsultationsPage(UserContext userContext, Map<String, String> queryParams) {
        return this.consultationsPageViewBuilder.buildView(userContext, queryParams);
    }

    public SfConsultationDetailPageView getConsultationDetailPage(UserContext userContext, Long bookingId) {
        return this.consultationDetailPageViewBuilder.buildView(userContext, bookingId);
    }

    public SfReportsPageView getReportsPage(UserContext userContext) {
        return this.reportsPageViewBuilder.buildView(userContext);
    }

    public SfOrdersPageView getOrdersPage(UserContext userContext) {
        return this.ordersPageViewBuilder.buildView(userContext);
    }

    public Object getOrderInvoice(UserContext userContext, @RequestParam Map<String, String> queryParams) throws HttpException {
        String cfOrderId = queryParams.getOrDefault("cfOrderId", "");
        Map<String, String> headers = new HashMap();
        headers.put("Content-type", "application/json");
        headers.put("pragma", "no-cache");
        String url = omsBaseUrl + "/invoice/" + cfOrderId + "/documents";
        HttpRequestDetail<String, Object> httpRequestDetail = new HttpRequestDetail(url, null, null, headers, null, new TypeReference<>() {
        });
        Object response = restTemplateClient.post(httpRequestDetail).getBody();
        if (ChronicCareAppUtil.isSfInvoiceUrlChangeUnsupportedVersion(userContext)) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                OrderInvoiceResponse invoiceResponse = mapper.convertValue(response, OrderInvoiceResponse.class);
                if (invoiceResponse != null && invoiceResponse.getPaymentInvoicesResponse() != null) {
                    List<String> receiptUrls = invoiceResponse.getPaymentInvoicesResponse().getRECEIPT();
                    if (!CollectionUtils.isEmpty(receiptUrls)) {
                        invoiceResponse.getPaymentInvoicesResponse().setINVOICE(new ArrayList<>(){{addAll(receiptUrls);}});
                    }
                    return invoiceResponse;
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
        }
        return response;
    }

    public boolean emailDocumentByType(UserContext userContext, @RequestParam Map<String, String> queryParams, String userEmailId) throws ResourceNotFoundException, HttpException {
        String documentType = queryParams.getOrDefault("documentType", "");
        String userId = userContext.getUserProfile().getUserId();

        switch(documentType) {
            case "PRESCRIPTION":
                Long bookingId = Long.valueOf(queryParams.getOrDefault("bookingId", null));
                Long consultationId = Long.valueOf(queryParams.getOrDefault("consultationId", null));
                this.serviceInterfaces.getSfAlbusClient().sendPrescriptionEmail(consultationId, bookingId, userId);
                break;
            case "DIAGNOSTIC":
                String diagnosticBookingId = queryParams.getOrDefault("carefitOrderId", "");
                this.chsClient.emailDiagnosticReportToUser(Long.valueOf(userId), Long.valueOf(diagnosticBookingId), userEmailId);
                break;
            case "DIET_PLAN":
                PatientDetail patientDetail = this.chronicCarePatientService.getChronicCarePatientForUserId(userContext);
                AppTenant appTenant = getAppTenantFromUserContext(userContext);
                String emailId = queryParams.getOrDefault("emailId", "");
                this.ambrosiaClient.sendDietPlanPdfOnEmail(patientDetail.getId(), emailId, appTenant);
                break;
        }
        return true;
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    public boolean getEmailDocument(UserContext userContext, @RequestParam Map<String, String> queryParams) throws BaseException {
        String emailId = queryParams.getOrDefault("emailId", "");
        UserEntry userEntry = userServiceClient.getUser(userContext.getUserProfile().getUserId()).get();
        String userId = userContext.getUserProfile().getUserId();
        boolean emailExists = userEntry.getEmail() == null || userEntry.getEmail().isEmpty();

        if (emailExists) {
            NUXProfileUpdateRequest nuxProfileUpdateRequest = new NUXProfileUpdateRequest();
            nuxProfileUpdateRequest.setEmail(emailId);
            nuxProfileUpdateRequest.setProfiles(Collections.singletonList(NUXProfile.EMAIL));
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            this.smsClient.updateNUXData(Long.valueOf(userId), nuxProfileUpdateRequest, timeZone);
        }

        return emailDocumentByType(userContext, queryParams, emailExists ? userEntry.getEmail() : emailId );
    }

    public AssetResponse getUrlToUploadReport(UserContext userContext, String category) throws ResourceNotFoundException {
        PatientDetail patientDetail = this.chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        return this.serviceInterfaces.getSfAlbusClient().getUrlToUploadReports(patientDetail.getId(), category, userContext.getUserProfile().getUserId());
    }

    public void confirmReportUpload(UserContext userContext, @RequestParam Map<String, String> queryParams) throws ResourceNotFoundException {
        String s3path = queryParams.getOrDefault("s3path", null);
        String category = queryParams.getOrDefault("category", null);
        String filename = queryParams.getOrDefault("filename", null);
        PatientDetail patientDetail = this.chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        this.serviceInterfaces.getSfAlbusClient().confirmReportUpload(s3path, patientDetail.getId(), category, filename);
    }

    public void emailDiagnosticReport(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        String bookingId = queryParams.getOrDefault("carefitOrderId", "");
        String emailId = queryParams.getOrDefault("emailId", "");
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        this.chsClient.emailDiagnosticReportToUser(userId, Long.valueOf(bookingId), emailId);
    }

    public void emailDoctorPrescription(UserContext userContext, Map<String, String> queryParams) {
        Long bookingId = Long.valueOf(queryParams.getOrDefault("bookingId", null));
        Long consultationId = Long.valueOf(queryParams.getOrDefault("consultationId", null));
        String emailId = queryParams.getOrDefault("emailId", "");
        String userId = userContext.getUserProfile().getUserId();
        this.serviceInterfaces.getSfAlbusClient().sendPrescriptionToUser(userId, emailId, bookingId, consultationId);
    }

    public String downloadReport(String s3Prefix) {
        EncodedStreamResponse encodedAsset = this.serviceInterfaces.getSfAlbusClient().getEncodedStream(s3Prefix);
        return encodedAsset.getEncodedString();
    }

    public boolean deleteUserReport(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        String documentId = queryParams.getOrDefault("documentId", null);
        boolean selfUploaded = Boolean.parseBoolean(queryParams.getOrDefault("selfUploaded", "false"));
        if (documentId != null && selfUploaded) {
            this.serviceInterfaces.getSfAlbusClient().deletePatientDocument(documentId, userId);
            return true;
        }
        return false;
    }

    public NutrientContributorPageView getNutrientsContributorPage(UserContext userContext,Map<String, String> queryParams) throws HttpException {
        return this.nutrientContributorPageViewBuilder.buildView(userContext,queryParams);
    }

    public NutritionLogsPageView getNutritionPage(UserContext userContext,Map<String, String> queryParams) throws HttpException {
        return this.nutritionLogsPageViewBuilder.buildView(userContext,queryParams);
    }

    public NutritionIntakeDetailWidget getNutritionIntakeWidgetData(UserContext userContext,Map<String, String> queryParams) throws HttpException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = TimeZone.getTimeZone(userContext.getUserProfile().getTimezone());
        return this.nutritionLogsPageViewBuilder.getNutritionIntakeDetailWidget(userId, queryParams, appTenant, timeZone, userContext);
    }

    public NutritionInsightsWidget getNutritionInsightsWidget(UserContext userContext,Map<String, String> queryParams) throws HttpException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = TimeZone.getTimeZone(userContext.getUserProfile().getTimezone());
        return this.nutritionLogsPageViewBuilder.getNutritionInsightsWidget(userId, queryParams, appTenant, timeZone, MealSlot.BREAKFAST, userContext);
    }

    public NutritionLogListWidget getNutritionLogListWidget(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = TimeZone.getTimeZone(userContext.getUserProfile().getTimezone());
        return this.nutritionLogsPageViewBuilder.getNutritionLogListWidget(userId, queryParams, appTenant, timeZone, userContext);
    }


    public Boolean deleteMealLog(Map<String, String> queryParams, UserContext userContext) throws HttpException {
        String mealId = queryParams.getOrDefault("mealId", "");
        this.sfLoggingClient.deleteLoggedMeal(Long.parseLong(mealId),Long.valueOf(userContext.getUserProfile().getUserId()));
        return true;
    }


    public FBVScanPageView getFaceBasedVitalsScanPage(UserContext userContext) {
        return this.fbvScanPageViewBuilder.buildView(userContext);
    }

    public FBVScanPageView getFaceBasedVitalsScanPageV2(UserContext userContext) {
        return this.fbvScanPageViewBuilder.buildViewV2(userContext);
    }

    public void updateFaceScanStatus(UserContext userContext, CareplixScanStatusUpdateRequest request) throws HttpException {
        request.setEmployeeId(userContext.getUserProfile().getUserId());
        try {
            this.chsClient.updateCareplixScanStatus(request);
        } catch (HttpException e) {
            log.info("Error while sending face scan logs for userId::{} payload::{} err::{}", userContext.getUserProfile().getUserId(), request, e.getMessage());
        }
    }

    public List<UserMetricEntry> addFaceBasedVitalsData(UserContext userContext, FaceBasedVitalUploadRequest uploadRequest) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        uploadRequest.setUserId(userId);
        return this.chsClient.addFaceBasedVitalsData(uploadRequest);
    }

    public CareplixFaceBasedVitalSaveResponse addFaceBasedVitalsDataV2(UserContext userContext, CareplixFaceBasedVitalsRequest uploadRequest) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        uploadRequest.setUserId(userId);
        try {
            this.chsClient.addCareplixFaceBasedVitalsScanData(uploadRequest);
            return CareplixFaceBasedVitalSaveResponse.builder().success(true).build();
        } catch (BaseException e) {
            String msg = e.getMessage();
            if(msg != null) {
                int idx = msg.indexOf("message: ");
                int endIdx = msg.indexOf("code: ");
                if(idx > -1 && ((idx + 9) < msg.length()) && endIdx > 0) {
                    return CareplixFaceBasedVitalSaveResponse.builder().success(false).message(msg.substring(idx + 9, endIdx)).build();
                }
            }
            return CareplixFaceBasedVitalSaveResponse.builder().success(false).build();
        }
    }

    public FaceBasedVitalLogsPage getFaceBasedVitalLogsPage(UserContext userContext, FaceBasedVitalsRequest request) {
        return this.faceBasedVitalLogsPageViewBuilder.buildView(userContext, request);
    }

    public FBVExperienceHistoryPage getFBVExperienceLogsPage(UserContext userContext) {
        return this.fbvExperienceHistoryPageViewBuilder.buildView(userContext);
    }

    public FaceBasedVitalTrendsPage getFaceBasedVitalTrendsPage(UserContext userContext, FaceBasedVitalsRequest request) {
        return this.faceBasedVitalTrendsPageViewBuilder.buildView(userContext, request);
    }

    public FitnessJournalV2PageView getFitnessJournalV2PageView(UserContext userContext) throws BaseException {
        return this.fitnessJournalV2PageViewBuilder.buildView(userContext);
    }

    public FreemiumNuxPageView getFreemiumNuxPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return this.freemiumNuxPageViewBuilder.buildView(userContext, queryParams);
    }

    public DAFreemiumNuxPageView getDAFreemiumNuxPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        String formType = Objects.nonNull(queryParams) ? queryParams.getOrDefault("formType", null) : null;
        boolean salesApp = Objects.nonNull(queryParams) && queryParams.containsKey("salesApp") && Boolean.parseBoolean(queryParams.getOrDefault("salesApp", "false"));
        return this.daFreemiumNuxPageViewBuilder.buildView(userContext, formType, salesApp);
    }

    public boolean updateDANUXData(UserContext userContext, List<DANuxDataUpdatePayload> payload) throws HttpException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            NUXProfileUpdateRequest request = new NUXProfileUpdateRequest();
            List<NUXProfile> profiles = new ArrayList<>();
            payload.forEach(nuxUpdateData -> {
                switch (nuxUpdateData.getProfile()) {
                    case NUXProfile.NAME -> {
                        profiles.add(NUXProfile.NAME);
                        request.setName(nuxUpdateData.getValues().getFirst());
                    }
                    case NUXProfile.GENDER -> {
                        profiles.add(NUXProfile.GENDER);
                        request.setGender(nuxUpdateData.getValues().getFirst());
                    }
                    case NUXProfile.AGE -> {
                        profiles.add(NUXProfile.AGE);
                        LocalDate epochDate = Instant.ofEpochMilli(Long.parseLong(nuxUpdateData.getValues().getFirst()))
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();

                        LocalDate currentDate = LocalDate.now();
                        Period period = Period.between(epochDate, currentDate);
                        request.setAge(period.getYears());
                    }
                    case LANGUAGE -> {
                        profiles.add(NUXProfile.LANGUAGE);
                        request.setLanguages(nuxUpdateData.getCodes());
                    }
                    case HEIGHT -> {
                        profiles.add(NUXProfile.HEIGHT);
                        GenericNuxDataEntry<Double> heightEntry = new GenericNuxDataEntry<>();
                        heightEntry.setValue(Double.valueOf(nuxUpdateData.getValues().getFirst()));
//                    heightEntry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setHeight(heightEntry);
                    }
                    case WEIGHT -> {
                        profiles.add(NUXProfile.WEIGHT);
                        GenericNuxDataEntry<Double> weightEntry = new GenericNuxDataEntry<>();
                        weightEntry.setValue(Double.valueOf(nuxUpdateData.getValues().getFirst()));
//                    weightEntry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setWeight(weightEntry);
                    }
                    case DIABETES_TYPE_V1 -> {
                        profiles.add(NUXProfile.DIABETES_TYPE_V1);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setDiabetesTypeV1(entry);
                    }
                    case HBA1C_LEVEL -> {
                        profiles.add(NUXProfile.HBA1C_LEVEL);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setHba1cLevel(entry);
                    }
                    case BIGGEST_CHALLENGE_IN_MANAGING_DIABETES -> {
                        profiles.add(NUXProfile.BIGGEST_CHALLENGE_IN_MANAGING_DIABETES);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setChallengeInManagingDiabetes(entry);
                    }
                    case CURRENT_MEDICATION -> {
                        profiles.add(NUXProfile.CURRENT_MEDICATION);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setCurrentMedication(entry);
                    }
                    case DIET -> {
                        profiles.add(NUXProfile.DIET);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setDiet(entry);
                    }
                    case FOOD_HABIT_STRUGGLE -> {
                        profiles.add(NUXProfile.FOOD_HABIT_STRUGGLE);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setFoodHabitStruggle(entry);
                    }
                    case PHYSICAL_ACTIVE -> {
                        profiles.add(NUXProfile.PHYSICAL_ACTIVE);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setPhysicalActive(entry);
                    }
                    case SLEEP_LEVEL -> {
                        profiles.add(NUXProfile.SLEEP_LEVEL);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setSleepLevel(entry);
                    }
                    case MAIN_GOAL_FOR_APP_USAGE -> {
                        profiles.add(NUXProfile.MAIN_GOAL_FOR_APP_USAGE);
                        GenericListNuxDataEntry<String> entry = new GenericListNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues());
                        entry.setOptions(
                                nuxUpdateData.getCodes()
                                        .stream()
                                        .map(Long::valueOf)
                                        .collect(Collectors.toList())
                        );
                        request.setMainGoalForAppUsageV2(entry);
                    }
                    case STRESS_RELATED_CHALLENGE -> {
                        profiles.add(NUXProfile.STRESS_RELATED_CHALLENGE);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setStressRelatedChallenge(entry);
                    }
                    case BLOOD_SUGAR_TRACKING -> {
                        profiles.add(NUXProfile.BLOOD_SUGAR_TRACKING);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setBloodSugarTracking(entry);
                    }
                    case GUIDANCE_LOOKING_FOR -> {
                        profiles.add(NUXProfile.GUIDANCE_LOOKING_FOR);
                        GenericListNuxDataEntry<String> entry = new GenericListNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues());
                        entry.setOptions(
                                nuxUpdateData.getCodes()
                                        .stream()
                                        .map(Long::valueOf)
                                        .collect(Collectors.toList())
                        );
                        request.setGuidanceLookingForV2(entry);
                    }
                    case DEMOGRAPHIC_INFO -> {
                        profiles.add(NUXProfile.DEMOGRAPHIC_INFO);
                        GenericNuxDataEntry<Boolean> entry = new GenericNuxDataEntry<>();
                        entry.setValue(true);
                        request.setDemoInfo(entry);
                    }
                    case DIABETES_PROFILE_INFO -> {
                        profiles.add(NUXProfile.DIABETES_PROFILE_INFO);
                        GenericNuxDataEntry<Boolean> entry = new GenericNuxDataEntry<>();
                        entry.setValue(true);
                        request.setDiabetesProfileInfo(entry);
                    }
                    case BLOOD_SUGAR_TRACKING_INFO -> {
                        profiles.add(NUXProfile.BLOOD_SUGAR_TRACKING_INFO);
                        GenericNuxDataEntry<Boolean> entry = new GenericNuxDataEntry<>();
                        entry.setValue(true);
                        request.setBloodSugarTrackingInfo(entry);
                    }
                    case LIFESTYLE_INFO -> {
                        profiles.add(NUXProfile.LIFESTYLE_INFO);
                        GenericNuxDataEntry<Boolean> entry = new GenericNuxDataEntry<>();
                        entry.setValue(true);
                        request.setLifestyleInfo(entry);
                    }
                    case SLEEP_QUALITY_INFO -> {
                        profiles.add(NUXProfile.SLEEP_QUALITY_INFO);
                        GenericNuxDataEntry<Boolean> entry = new GenericNuxDataEntry<>();
                        entry.setValue(true);
                        request.setSleepQualityInfo(entry);
                    }
                    case REDUCTION_IN_MEDICATION_INFO -> {
                        profiles.add(NUXProfile.REDUCTION_IN_MEDICATION_INFO);
                        GenericNuxDataEntry<Boolean> entry = new GenericNuxDataEntry<>();
                        entry.setValue(true);
                        request.setReductionInMedicationInfo(entry);
                    }
                    case REMINDER_PREFERENCE -> {
                        profiles.add(NUXProfile.REMINDER_PREFERENCE);
                        GenericNuxDataEntry<String> entry = new GenericNuxDataEntry<>();
                        entry.setValue(nuxUpdateData.getValues().getFirst());
                        entry.setOption(Long.valueOf(nuxUpdateData.getCodes().getFirst()));
                        request.setReminderPreference(entry);
                    }
                    case CITY -> {
                        profiles.add(NUXProfile.CITY);
                        request.setCity(nuxUpdateData.getValues().getFirst());
                    }
                }
            });
            request.setProfiles(profiles);
            this.smsClient.updateNUXData(userId, request, timeZone);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException("error in updating DA NUX data", e);
            return false;
        }
    }

    public void updateNUXData(UserContext userContext,  Map<String, String> queryParams) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        if (queryParams.containsKey("type")) {
            NUXProfileUpdateRequest request = new NUXProfileUpdateRequest();
            String type = queryParams.get("type");
            String value = queryParams.get("value");
            String diabetesTypeComment = queryParams.getOrDefault("diabetesTypeComment", "");

            switch (type) {
                case "NAME" -> {
                    request.setProfiles(new ArrayList<>() {{
                        add(NUXProfile.NAME);
                    }});
                    request.setName(value);
                }
                case "GENDER" -> {
                    request.setProfiles(new ArrayList<>() {{
                        add(NUXProfile.GENDER);
                    }});
                    request.setGender(value);
                }
                case "AGE" -> {
                    request.setProfiles(new ArrayList<>() {{
                        add(NUXProfile.AGE);
                    }});
                    request.setAge(Integer.valueOf(value));
                }
                case "DIABETES_TYPE" -> {
                    request.setProfiles(new ArrayList<>() {{
                        add(NUXProfile.DIABETES_TYPE);
                    }});
                    request.setDiabetesType(value);
                    request.setDiabetesTypeComment(diabetesTypeComment);
                }
                case "FASTING_GLUCOSE" -> {
                    request.setProfiles(new ArrayList<>() {{
                        add(NUXProfile.FASTING_GLUCOSE);
                    }});
                    request.setFastingGlucoseComment(value);
                }
                case "WEBINAR_INTEREST" -> {
                    request.setProfiles(new ArrayList<>() {{
                        add(NUXProfile.WEBINAR_INTEREST);
                    }});
                    request.setWebinarInterested(value);
                }
                case "USER_GOAL" -> {
                    request.setProfiles(new ArrayList<>() {{
                        add(NUXProfile.USER_GOAL);
                    }});
                    request.setUserGoal(value);
                }
                case "HBA1C" -> {
                    request.setProfiles(new ArrayList<>() {{
                        add(NUXProfile.HBA1C);
                    }});
                    request.setHba1c(value);
                }
            }
            this.smsClient.updateNUXData(userId, request, timeZone);
        }
    }

    public boolean updateNUXEmail(UserContext userContext,  Map<String, String> queryParams) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        if (queryParams.containsKey("emailId")) {
            NUXProfileUpdateRequest request = new NUXProfileUpdateRequest();
            request.setProfiles(new ArrayList<>() {{
                add(NUXProfile.EMAIL);
            }});
            request.setEmail(queryParams.get("emailId"));
            this.smsClient.updateNUXData(userId, request, timeZone);
            NUXStatusResponse response = this.smsClient.getNUXStatus(userId, false, timeZone);
            if(response!=null && response.getNuxCompleted())
                return true;
        }
        return false;
    }


    public FreemiumOnboardingPageView getFreemiumOnboardingPage () throws HttpException, IOException {
        return this.freemiumOnboardingPageViewBuilder.buildView();
    }

    public SfFreemiumHomePageView getFreemiumHomePage(UserContext userContext) {
        return this.freemiumHomePageViewBuilder.buildView(userContext);
    }

    public List<BaseWidgetNonVM> getFreemiumHomeDiscoverWidgets(UserContext userContext, Map<String, String> queryParams) {
        String sessionId = queryParams.getOrDefault("sessionId", "");
        return this.sugarFitBlogWidgetBuilder.getHomepageBlogWidgets(userContext, sessionId);
    }

    public SfAccreditationsPageView getAccreditationsPage(UserContext userContext) {
        return this.accreditationsPageViewBuilder.buildView(userContext);
    }

    public FreemiumPackListPageView getFreemiumPacksPage(UserContext userContext) {
        return this.freemiumPackListPageViewBuilder.buildView(userContext);
    }

    public SfTestimonialsPageView getTestimonialsPage(UserContext userContext) {
        return this.testimonialsPageViewBuilder.buildView(userContext);
    }

    public void markInfoBiteViewed(UserContext userContext, String infoBiteId) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        this.catalogClient.markInfoBitesViewed(userId, infoBiteId);
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    public boolean createFreemiumLead(UserContext userContext, LeadEntry leadEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        UserEntry userEntry = this.userServiceClient.getUser(userContext.getUserProfile().getUserId()).get();
        if (userEntry != null) {
            leadEntry.setPhone(userEntry.getPhone());
            leadEntry.setFirstName(userEntry.getFirstName());
//            leadEntry.setUserId(userId);
            this.lmsClient.addLead(leadEntry);
        }
        return true;
    }

    public boolean scheduleFreeCoachCall(UserContext userContext) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            this.lmsClient.scheduleFreemiumCoachCall(userId);
        } catch (Exception e) {
            exceptionReportingService.reportException("error in scheduling freemium coach call", e);
            return false;
        }
        return true;
    }

    public SfExperiencePageView getExperiencePage(UserContext userContext) {
        return this.sfExperiencePageViewBuilder.buildView(userContext);
    }

    public ExperiencePageCGMDataResponse getExperienceCgmGraph() throws HttpException {
        return this.catalogClient.getExperiencePageCGMData();
    }

    public SfExperienceFBVWidget getExperienceFBVWidgetData(UserContext userContext) {
        return this.sfExperiencePageViewBuilder.getExperienceFBVWidgetData(userContext);
    }

    public CommunityPageView getCommunityPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return this.communityPageViewBuilder.buildView(userContext, queryParams);
    }

    public PostEntry savePost(UserContext userContext, PostEntry postEntry) throws BaseException {
        Long communityId = getOpenCommunityIdForUser(userContext, this.serviceInterfaces.environmentService);
        postEntry.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        postEntry.setUserType(UserType.USER);
        postEntry.setCommunityId(communityId);
        return postEntry.getId() != null ?  nestClient.editPost(postEntry): nestClient.createPost(postEntry);
    }

    public PostEntry getImageUploadPath(UserContext userContext, PostEntry postEntry) throws BaseException {
        postEntry.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        postEntry.setUserType(UserType.USER);
        return nestClient.createPost(postEntry);
    }

    public Map<String, String> fetchPostsFileUploadPath(Map<String, String> queryParams) throws BaseException {
        int count = Integer.parseInt(queryParams.getOrDefault("count","1"));
        return nestClient.fetchPostsFileUploadPath(count);
    }
    public Map<String, String> fetchCommentsFileUploadPath(Map<String, String> queryParams) throws BaseException {
        return nestClient.fetchCommentsFileUploadPath();
    }

    public Map<String, String> fetchPostFiles(Map<String, String> queryParams) throws BaseException {
        if (!queryParams.containsKey("postId"))
            return null;
        long postId = Long.parseLong(queryParams.get("postId"));
        return nestClient.fetchFiles(postId, null);
    }

    public Map<String, String> fetchCommentFiles(Map<String, String> queryParams) throws BaseException {
        if (!queryParams.containsKey("commentId"))
            return null;
        long commentId = Long.parseLong(queryParams.get("commentId"));
        return nestClient.fetchCommentFiles(commentId, null);
    }

    public PostEntry getPostbyId(Map<String, String> queryParams) throws BaseException {
        if (!queryParams.containsKey("postId"))
            return null;
        long postId = Long.parseLong(queryParams.get("postId"));
        return nestClient.fetchPost(postId);
    }

    public List <PostEntry> fetchTrendingPosts(UserContext userContext) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long communityId = getOpenCommunityIdForUser(userContext, this.serviceInterfaces.environmentService);
        return nestClient.fetchTrendingPosts(0, 10, "createdOn", "DESC", userId, communityId).getElements();
    }

    public PostEntry deletePost(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("postId"))
            return null;
        long postId = Long.parseLong(queryParams.get("postId"));
        return nestClient.deletePost(postId, userId);
    }

    public PagedResultEntry<Long, PostEntry> fetchAllPosts(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long communityId = getOpenCommunityIdForUser(userContext, this.serviceInterfaces.environmentService);
        int offset = Integer.parseInt(queryParams.getOrDefault("pageNumber", "0"));
        return nestClient.fetchAllPosts(offset, 10, "createdOn", "DESC", userId, communityId);
    }

    public PostEntry likePost(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("postId"))
            return null;
        long postId = Long.parseLong(queryParams.get("postId"));
        try {
            return nestClient.likePost(postId, userId);
        }
        catch (Exception e ){
            return null;
        }
    }

    public PostEntry unlikePost(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("postId"))
            return null;
        long postId = Long.parseLong(queryParams.get("postId"));
        try {
            return nestClient.unlikePost(postId, userId);
        }
        catch (Exception e ){
            return null;
        }
    }


    public List<CommentEntry> fetchComments(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("postId"))
            return null;
        long postId = Long.parseLong(queryParams.get("postId"));
        Integer limit = Integer.parseInt(queryParams.getOrDefault("limit","10"));
        Integer pageNumber=Integer.parseInt(queryParams.getOrDefault("pageNumber","0"));
        return nestClient.fetchCommentsPaginated(pageNumber*limit,limit,"createdOn","ASC",userId,postId).getElements();
    }

    public CommentEntry saveComments(UserContext userContext, CommentEntry commentEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        commentEntry.setUserId(userId);
        commentEntry.setUserType(UserType.USER);
        return commentEntry.getId() != null ? nestClient.editComment(commentEntry) : nestClient.createComment(commentEntry);
    }

    public CommentEntry deleteComment(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("commentId"))
            return null;
        long commentId = Long.parseLong(queryParams.get("commentId"));
        return nestClient.deleteComment(commentId, userId);
    }

    public CommentEntry likeComment(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("commentId"))
            return null;
        long commentId = Long.parseLong(queryParams.get("commentId"));
        try {
            return nestClient.likeComment(commentId, userId);
        }
        catch (Exception e ){
            return null;
        }
    }

    public CommentEntry unlikeComment(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("commentId"))
            return null;
        long commentId = Long.parseLong(queryParams.get("commentId"));
        try {
            return nestClient.unlikeComment(commentId, userId);
        }
        catch (Exception e){
            return null;
        }
    }

    public PostEntry markSpamPost(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        long postId = Long.parseLong(queryParams.get("postId"));
        SpamReportEntry spamReportEntry = new SpamReportEntry();
        spamReportEntry.setContentId(postId);
        spamReportEntry.setContentType(ContentType.POST);
        spamReportEntry.setCategory(queryParams.get("reasons"));
        spamReportEntry.setUserId(userId);
        return nestClient.markSpamPost(spamReportEntry);
    }

    public PostEntry unspamPost(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("postId"))
            return null;
        long postId = Long.parseLong(queryParams.get("postId"));
        return nestClient.unspamPost(postId, userId);
    }

    public PostEntry markSpamComment(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("commentId"))
            return null;
        long commentId = Long.parseLong(queryParams.get("commentId"));
        SpamReportEntry spamReportEntry = new SpamReportEntry();
        spamReportEntry.setContentId(commentId);
        spamReportEntry.setContentType(ContentType.COMMENT);
        spamReportEntry.setCategory(queryParams.get("reasons"));
        spamReportEntry.setUserId(userId);
        return nestClient.markSpamComment(spamReportEntry);
    }

    public PostEntry unspamComment(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("commentId"))
            return null;
        long commentId = Long.parseLong(queryParams.get("commentId"));
        return nestClient.unspamComment(commentId, userId);
    }

    public List<PostEntry> fetchExpertPosts(UserContext userContext) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long communityId = getOpenCommunityIdForUser(userContext, this.serviceInterfaces.environmentService);
        return nestClient.fetchExpertPosts(0, 5, "createdOn", "DESC", userId, communityId).getElements();
    }

    public List<PostEntry> fetchAllPostsWithExpertPosts(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        int limitForExpertPost = 3;
        int limitForAllPosts = 7;
        int pageNumber = Integer.parseInt(queryParams.getOrDefault("pageNumber", "0"));
        String pageType = "";
        if (queryParams.containsKey("pageType")) {
            pageType = queryParams.get("pageType");
        }
        Long communityId = getOpenCommunityIdForUser(userContext, this.serviceInterfaces.environmentService);
        List<PostEntry> expertPosts = new ArrayList<>();
        List<PostEntry> allPosts = new ArrayList<>();
        if (pageType.equals("allQuestions")) {
            try {
                expertPosts = nestClient.fetchExpertPosts(pageNumber * limitForExpertPost, limitForExpertPost, "createdOn", "DESC", userId, communityId).getElements();
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            try {
                allPosts = nestClient.fetchAllPosts(pageNumber * limitForAllPosts, limitForAllPosts, "createdOn", "DESC", userId, communityId).getElements();
            } catch (Exception e) {
                log.info(e.getMessage());
            }
            List<PostEntry> merge = new ArrayList<PostEntry>();
            int i = 0,j = 0, k = 0;
            while(true) {
                for(int start=j;start<Math.min(allPosts.size(),j+2);start++){
                    PostEntry post = allPosts.get(start);
                    if(post!=null){
                        merge.add(post);
                    }
                }
                j+=2;
                if(k<expertPosts.size()) {
                    PostEntry post = expertPosts.get(k);
                    if (post != null) {
                        merge.add(post);
                    }
                    k++;
                }
                if(j>=allPosts.size() && k>=expertPosts.size()){
                    break;
                }
            }
            return merge;
        }
        if (pageType.equals("myQuestions"))
            return nestClient.fetchMyPosts(pageNumber * limitForAllPosts, limitForAllPosts, "createdOn", "DESC", userId, communityId).getElements();
        if (pageType.equals("followedQuestions"))
            return nestClient.fetchMyFollowedPosts(pageNumber * limitForAllPosts, limitForAllPosts, "createdOn", "DESC", userId, communityId).getElements();
        if (pageType.equals("trendingQuestions"))
            return nestClient.fetchTrendingPosts(pageNumber * limitForAllPosts, limitForAllPosts, "createdOn", "DESC", userId, communityId).getElements();
        return new ArrayList<>();
    }

    public SfFreemiumDiscoverPageView getFreemiumDiscoverPage(UserContext userContext, Map<String, String> queryParams) {
        return this.freemiumDiscoverPageViewBuilder.buildView(userContext, queryParams);
    }

    public SfFreemiumBlogDetailsPageView getFreemiumBlogDetailsPage(UserContext userContext, Map<String, String> queryParams) {
        return this.freemiumBlogDetailsPageViewBuilder.buildView(userContext, queryParams);
    }

    public SfFreemiumBlogListPageView getFreemiumBlogListPage(UserContext userContext, Map<String, String> queryParams) {
        return this.freemiumBlogListPageViewBuilder.buildView(userContext, queryParams);
    }

    public void saveBlogForUser(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String blogId = queryParams.getOrDefault("blogId", "");
        catalogClient.saveBlogForUser(userId, blogId);
    }

    public void unsaveBlogForUser(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String blogId = queryParams.getOrDefault("blogId", "");
        catalogClient.unsaveBlogForUser(userId, blogId);
    }

    public CommentEntry addCommentOnBLog(UserContext userContext, CommentEntry commentEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        commentEntry.setUserId(userId);
        commentEntry.setUserType(UserType.USER);
        return commentEntry.getId() == null ? nestClient.createComment(commentEntry) : nestClient.editComment(commentEntry);
    }

    public MyQuestionsPageView getMyQuestionsPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return this.myQuestionsPageViewBuilder.buildView(userContext, queryParams);
    }

    public AnswersPageView getAnswersPageView(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return this.answersPageViewBuilder.buildView(userContext, queryParams);
    }

    public CommunityUserInfo fetchUserInfo(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        CommunityUserInfo data = new CommunityUserInfo();
        if (!queryParams.containsKey("userType")) return data;
        if (!queryParams.containsKey("userId")) return data;
        Long userId = Long.valueOf(queryParams.get("userId"));
        data.setUserType(queryParams.get("userType"));
        try {
            if (queryParams.get("userType").equals("USER")) {

                UserEntry userEntry = userServiceClient.getUser(queryParams.get("userId")).get();
                if (userEntry != null) {
                    data.setName((userEntry.getFirstName() != null ? userEntry.getFirstName() : "") + " " + (userEntry.getLastName() != null ? userEntry.getLastName() : ""));
                    data.setImageUrl(userEntry.getProfilePictureUrl());
                    data.setGender(userEntry.getGender());
                }
            } else if (queryParams.get("userType").equals("COACH")) {
                AgentResponse agentResponse = ollivanderAgentClient.getAgent(userId);

                if (agentResponse != null) {
                    data.setName(agentResponse.getName());
                    data.setImageUrl(agentResponse.getDisplayImage());
                    data.setGender(String.valueOf(agentResponse.getGender()));
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return data;
        }
        return data;
    }


    public PostEntry markLastestComment(UserContext userContext, Map<String, String> queryParams) throws BaseException {

        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        if (!queryParams.containsKey("postId"))
            return null;
        return nestClient.markRead(Long.valueOf(queryParams.get("postId")),userId);
    }

    public SfHomeEventsTalkWidget getEventsTalkWidget(UserContext userContext, String type) {
        boolean isWebinar = type.equals("WEBINAR");
        SfHomeEventsTalkWidget widget = new SfHomeEventsTalkWidget();
        widget.setTitle(isWebinar ? "Exclusively for you" : "Events and Talks");
        HashMap<String,String> liveUserCount = new HashMap<>();
        try {
            NowLiveWidgetView widgetView = new NowLiveWidgetView();
            widgetView.setProductType(ProductType.LIVE_VIDEO_CALL);
            widgetView.setTitle("Events and Talk");
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("isFreemiumEventWidget", "true");
            queryParams.put("pageId", "LiveWorkout");
            if (isWebinar) {
                queryParams.put("isWebinar", "true");
            }
            WidgetContext widgetContext = new WidgetContext();
            widgetContext.setQueryParams(queryParams);
            List<BaseWidget> widgets = widgetView.buildView(serviceInterfaces, userContext, widgetContext);
            if (widgets!=null && widgets.size() > 0) {
                NowLiveWidgetView nowLiveWidget = (NowLiveWidgetView) widgets.get(0);
                widget.setWidgets(nowLiveWidget.getWidgets());
                nowLiveWidget.getWidgets().forEach(i->{
                    liveUserCount.put(i.getContentId(),chronicCareServiceHelper.getSfEventsTalkUserCount(i.getContentId()));
                });
            }
        } catch (Exception e) {
            exceptionReportingService.reportErrorMessage("Exception in event&talk widget" + e.getMessage());
            return null;
        }
        widget.setLiveUserCount(liveUserCount);
        return widget;
    }

    public FreemiumHomeTrendingEventWidget getWebinarWidget(UserContext userContext) {
        return this.freemiumHomePageViewBuilder.getTrendingEventWidget(userContext);
    }

    public SfCoachCommunitySessionWidget getCoachCommunitySessionWidget(UserContext userContext, WidgetContext widgetContext) {
        return this.chronicCareHomePageViewBuilder.getUpcomingCoachCommunitySessionWidget(userContext, widgetContext);
    }

    public SfHomeJournalWidget getHomeJournalWidgetData(UserContext userContext) {

        return this.freemiumHomePageViewBuilder.getHomeJournalWidgetData(userContext);
    }

    public boolean updateWeight(UserContext userContext, Map<String, String> queryParams) throws BaseException, MetricClientException {
        if (!queryParams.containsKey("value"))
            return false;

        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        Calendar calendar = Calendar.getInstance(timeZone);
        List<UserMetricEntry> userMetricEntries = new ArrayList<>();
        UserMetricEntry userMetricEntry = this.getUserMetricCHS(
                userContext,
                environmentService.isProduction() || environmentService.isAlpha() ? ChronicCareAppUtil.WEIGHT_METRIC_ID_PROD : ChronicCareAppUtil.WEIGHT_METRIC_ID_STAGE,
                Double.valueOf(queryParams.get("value")),
                AppUtil.getAppTenantFromUserContext(userContext).toString(), null, null, calendar.getTime());
        userMetricEntries.add(userMetricEntry);
        chsClient.insertUserMetricGeneric(userMetricEntries);
        return true;
    }

    public String getDeepLinkForSharing(Map<String, String> queryParams){
        if (!queryParams.containsKey("pageType"))
            return "";
        String pageType = queryParams.get("pageType");
        if(pageType.equals("community")) {
            if (!queryParams.containsKey("id"))
                return "";
            long postId = Long.parseLong(queryParams.get("id"));
            return this.serviceInterfaces.getDeeplinkCacheService().getSfCommunityPostDeepLink(postId);
        } else if (pageType.equals("blogdetails")) {
            if (!queryParams.containsKey("id"))
                return "";
            String blogId = queryParams.get("id");
            return this.serviceInterfaces.getDeeplinkCacheService().getSfBlogPostDeepLink(blogId);
        } else if (pageType.equals("videoplayer")) {
            if (!queryParams.containsKey("videoUrl"))
                return "";
            String videoUrl = queryParams.getOrDefault("videoUrl", "");
            String title = queryParams.getOrDefault("title", "");
            String thumbnailUrl = queryParams.getOrDefault("thumbnailUrl", "");
            return this.serviceInterfaces.getDeeplinkCacheService().getSfVideoDeepLink(videoUrl, title, thumbnailUrl);
        } else if (pageType.equals("challenge")) {
            if (!queryParams.containsKey("id"))
                return "";
            long postId = Long.parseLong(queryParams.get("id"));
            return this.serviceInterfaces.getDeeplinkCacheService().getSfCommunityPostDeepLink(postId);
        }
        return "";
    }

    private String getSfEventsTalkUserCountKey(String id) {
        return "SFAPP:" + "EVENTSTALK:" + "EVENTID:" + id;
    }

    public String getSfEventsTalkUserCount(String id) {
        var redisKey = this.getSfEventsTalkUserCountKey(id);
        String longValue = null;
        try {
            longValue = this.cfApiRedisKeyValueStore.get(redisKey);
        } catch (Exception e) {
            log.warn("count  cache miss", e);
        }
        if (longValue != null) {
            return longValue;
        }
        try {
            HashMap<String, String> config = appConfigCache.getConfig("SF_EVENTS_TALK_CONFIG", new TypeReference<>() {
            }, new HashMap<>());
            int min = config.get("min") == null ? 30 : Integer.parseInt(config.get("min"));
            int max = config.get("max") == null ? 50 : Integer.parseInt(config.get("max"));
            longValue = String.valueOf(Math.floor((Math.random() * (max - min)) + min));

        } catch (Exception e) {
            log.error(" app config failed" + e.getMessage());
            return null;
        }
        this.cfApiRedisKeyValueStore.set(redisKey, longValue, SIX_HOURS_IN_SECONDS);
        return longValue;
    }

    public Object lsqLeadPushApi(List<LeadSquaredPlainRequest> requestBody, HttpServletRequest request) throws BaseException {
        String userIpAddress = AppUtil.extractClientIpPreferIPv6(request);
        LeadSquaredPlainRequest LeadObj = new LeadSquaredPlainRequest();
        LeadObj.setAttribute("mx_ip");
        LeadObj.setValue(userIpAddress);
        requestBody.add(LeadObj);
        lmsClient.addToLSQ(requestBody);
        Map<String, Boolean> obj = new HashMap<>();
        obj.put("success", true);
        return obj;
    }

    public Object partnerLeadPushApi(Map<String, String> requestBody) throws BaseException {
        try {
            List<LeadSquaredPlainRequest> lsqRequest = new ArrayList<>();

            for(Map.Entry<String,String> entry : requestBody.entrySet()){
                LeadSquaredPlainRequest LeadObj = new LeadSquaredPlainRequest();
                LeadObj.setAttribute(entry.getKey());
                LeadObj.setValue(entry.getValue());
                lsqRequest.add(LeadObj);
            }

            lmsClient.addToLSQ(lsqRequest);
            Map<String, Boolean> obj = new HashMap<>();
            obj.put("success", true);
            return obj;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            throw e;
        }
    }

    public Boolean pushSnsEvent(SNSEventBody snsEventBody) {
        if (snsEventBody.getEventType() != null) {
            String topicArn = "";
            if (snsEventBody.getEventType().equals("WEBINAR_ATTENDANCE")) {
                topicArn = "arn:aws:sns:ap-south-1:035243212545:" + (this.environmentService.isProduction() ? "production-sugarfit-webinar-attendance" : "stage-sugarfit-webinar-attendance");
            }
            Map<String, String> attributes = snsEventBody.getAttributes();
            attributes.put("REQUEST_ID", UUID.randomUUID().toString());
            Map<String, MessageAttributeValue> messageAttributes = attributes.entrySet().stream().collect(
                    Collectors.toMap(Map.Entry::getKey, (entry) -> (new MessageAttributeValue()).withDataType("String").withStringValue((String)entry.getValue())));

            PublishRequest sendMessageRequest = new PublishRequest(topicArn, "{}");
            sendMessageRequest.withMessageAttributes(messageAttributes);
            this.sns.publish(sendMessageRequest);
            return true;
        }
        return false;
    }

    public PhleboHomepageView getPhleboHomepageView(UserContext userContext, Map<String, String> queryParams) throws Exception {
        return pheloHomepageViewBuilder.buildView(userContext, queryParams);
    }

    public UsersResponse getUserInfoByPhone(Map<String, String> queryParams) throws Exception {
        String phone = queryParams.get("phone");
        String countryCallingCode = queryParams.get("callingCode");
        return userServiceClient.getByPhone(phone,countryCallingCode);
    }

    public String getServiceableAddressId(UserContext userContext) throws HttpException {
        String userId = userContext.getUserProfile().getUserId();
        try{
            UserPreferencePojo userPreferencePojo = serviceInterfaces.getSfAlbusClient().getUserPreference(userId,UserPreferencePojo.PreferenceType.META,"addressId");
            if(userPreferencePojo != null && userPreferencePojo.getPreferenceTypeValues()!=null && userPreferencePojo.getPreferenceTypeValues().size()>0){
                return userPreferencePojo.getPreferenceTypeValues().get(0);
            }
            else {
                return null;
            }
        }
        catch (Exception e){
            String msg = String.format("Failed to getServiceableAddressId for user :: %s",userId);
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
            return null;
        }
    }
    public FitnessWeeklyStatWidget getFitnessWeeklyStatWidgetData(UserContext userContext, Map<String, String> queryParams){
        return this.fitnessJournalV2PageViewBuilder.getFitnessWeeklyStatWidget(serviceInterfaces, userContext,queryParams);
    }

    public FitnessWalkInsightsWidget getFitnessWalkInsightsWidget(UserContext userContext, Map<String, String> queryParams){
        return this.fitnessJournalV2PageViewBuilder.getFitnessWalkInsightsWidget(userContext,queryParams);
    }
    public FitnessLiveClassWidget getFitnessLiveClassWidget(UserContext userContext, Map<String, String> queryParams){
        return this.fitnessJournalV2PageViewBuilder.getFitnessLiveClassWidget(userContext,queryParams);
    }
    public FitnessLogListWidget getFitnessLogListWidget(UserContext userContext, Map<String, String> queryParams){
        return this.fitnessJournalV2PageViewBuilder.getFitnessLogListWidget(userContext,queryParams);
    }

    public Boolean deleteWorkout(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        String id = queryParams.getOrDefault("id", "");
        if (id == null || id.equals(""))
            return false;
         this.sfLoggingClient.deleteLoggedWorkout(Long.valueOf(id),Long.valueOf(userContext.getUserProfile().getUserId()));
        return true;
    }
    public BadgesPageView getBadgesPageView(UserContext userContext) throws BaseException {
        return this.badgesPageViewBuilder.buildView(userContext);
    }

    public SfBadgeEntry fetchAchievedBadges(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        if (!isBadgesEnabled(userContext, chronicCareServiceHelper)) {
            return null;
        }

        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = getUserTimezone(userContext);
        if(!queryParams.containsKey("pollAfter"))
            return null;
        Long pollAfter = Long.valueOf(queryParams.get("pollAfter"));
        List<UserBadgeProgressEntry> response = chsClient.fetchAchievedBadges(userId, pollAfter, timeZone, appTenant);
        return response != null && response.size() > 0 ? this.badgesPageViewBuilder.getBadgeEntry(response.get(0)) : null;
    }

    public List<SfBadgeEntry> fetchBrokenStreaks(UserContext userContext) throws BaseException {
        if (!isBadgesEnabled(userContext, chronicCareServiceHelper)) {
            return null;
        }

        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = getUserTimezone(userContext);
        List<UserBadgeProgressEntry> response = chsClient.updateAndFetchBrokenStreakBadges(userId, timeZone, appTenant);

        if (CollectionUtils.isEmpty(response)) {
            return null;
        }

        return response.stream().map(badge -> {
            SfBadgeEntry badgeEntry = this.badgesPageViewBuilder.getBadgeEntry(badge);
            Action logAction = getLogActionFromBadgeCategory(badge.getBadgeEntry().getCategory(), userContext, serviceInterfaces);
            badgeEntry.setLogAction(logAction);
            return badgeEntry;
        }).toList();
    }

    public UserBadgeProgressEntry markBrokenStreaksRead(UserContext userContext, Map<String, String> body) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = getUserTimezone(userContext);
        Long userBadgeId = Long.valueOf(body.get("userBadgeId"));
        return chsClient.markBadgeProgressRead(userBadgeId, userId, timeZone, appTenant);
    }

    public SfSupportRecentTicketsWidget getRecentTickets(UserContext userContext, Map<String, String> queryParams) {
        return sfSupportPageBuilder.buildRecentCardWidget(userContext);
    }

    public SfSupportTicketsListPageView getAllSupportTickets(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        String pageNumber = queryParams.getOrDefault("pageNumber", "0");
        return sfSupportTicketsListPageBuilder.buildView(userContext, Integer.parseInt(pageNumber));
    }

    public SfTicketDetailsPageView getTicketDetailsPage(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        String ticketId = queryParams.getOrDefault("ticketId", "0");
        return sfTicketsDetailsPageBuilder.buildView(userContext, Long.parseLong(ticketId));
    }

    public SfWebinarDetailsPageView getWebinarDetailsPage(UserContext userContext) {
        SfHomeEventsTalkWidget sfHomeEventsTalkWidget =  getEventsTalkWidget(userContext, "WEBINAR");
        SfWebinarDetailsPageView pageView = new SfWebinarDetailsPageView();
        if (!CollectionUtils.isEmpty(sfHomeEventsTalkWidget.getWidgets())) {
            NowLiveSessionWidget webinarWidget = sfHomeEventsTalkWidget.getWidgets().get(0);
            if (webinarWidget.getAction() != null) {
                pageView.setAction(webinarWidget.getAction());
            }
        }
        return pageView;
    }

    public SfSupportTicket createSupportTicket(UserContext userContext, SfSupportTicketRequest supportTicket) throws ResourceNotFoundException {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);

        FreshdeskTicketRequest freshdeskTicketRequest = new FreshdeskTicketRequest();
        freshdeskTicketRequest.setName(patientDetail.getName());
        freshdeskTicketRequest.setEmail(patientDetail.getEmailId());
        freshdeskTicketRequest.setUserId(userContext.getUserProfile().getUserId());
        freshdeskTicketRequest.setSubject(supportTicket.getTicket().getSubject());
        freshdeskTicketRequest.setDescription(supportTicket.getTicket().getDescription());
        freshdeskTicketRequest.setPriority(1); // Default Low priority
        freshdeskTicketRequest.setStatus(2); // Status Open

        CustomFields customFields = new CustomFields();
        customFields.setCustomerCallRequested(supportTicket.getNeedCallback());
        freshdeskTicketRequest.setCustomFields(customFields);

        if (!CollectionUtils.isEmpty(supportTicket.getTicket().getAttachments())) {
            List<AttachmentsRequest> attachmentsRequests = new ArrayList<>();
            supportTicket.getTicket().getAttachments().forEach(att -> {
                AttachmentsRequest attReq = new AttachmentsRequest();
                attReq.setAttachmentUrl(att.getFilePath());
                attReq.setFileExtension(att.getFileExtension());
                attachmentsRequests.add(attReq);
            });
            freshdeskTicketRequest.setAttachments(attachmentsRequests);
        }

        FreshdeskTicket freshdeskTicket = customerSupportClient.createTicket(freshdeskTicketRequest);
        return freshDeskToSupportTicket(freshdeskTicket);
    }

    public SfTicketDetailsPageView.Chat replyToSupportTicket(UserContext userContext, SfSupportTicketReply reply) throws ResourceNotFoundException {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);

        ReplyRequest replyRequest = new ReplyRequest();
        replyRequest.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        replyRequest.setBody(reply.getMessage());
        replyRequest.setFrom_email(patientDetail.getEmailId());

        if (!CollectionUtils.isEmpty(reply.getAttachments())) {
            List<AttachmentsRequest> attachmentsRequests = new ArrayList<>();
            reply.getAttachments().forEach(att -> {
                AttachmentsRequest attReq = new AttachmentsRequest();
                attReq.setAttachmentUrl(att.getFilePath());
                attReq.setFileExtension(att.getFileExtension());
                attReq.setContentType(att.getMimeType());
                attachmentsRequests.add(attReq);
            });
            replyRequest.setAttachments(attachmentsRequests);
        }

        Reply submittedReply = customerSupportClient.replyOnTicket(reply.getTicketId(), replyRequest);
        SfTicketDetailsPageView.Chat chatResponse = new SfTicketDetailsPageView.Chat();
        chatResponse.setMessage(submittedReply.getBodyText());
        chatResponse.setIsUserReply(true);
        chatResponse.setTime(submittedReply.getCreatedAt());
        List<Attachments> replyAttachments = submittedReply.getAttachments();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(replyAttachments)) {
            replyAttachments.forEach(att -> {
                chatResponse.addAttachment(att.getAttachmentURL(), att.getContentType());
            });
        }
        return chatResponse;
    }

    public boolean updateSupportTicketStatus(Long ticketId, int status) throws ResourceNotFoundException {
        customerSupportClient.updateTicketStatus(ticketId, status);
        return true;
    }

    public Object getSignedUrlForSupportTicket(int fileCount) {
        return this.customerSupportClient.fetchFileUploadPath(fileCount);
    }

    public SfCSConversationPageView getCSTicketConversation(UserContext userContext, String category, Long conversationId) {
        return csConversationPageBuilder.buildView(userContext, category, conversationId);
    }

    public CSConversation.Status getCSTicketStatus(UserContext userContext, Map<String, String> queryParams) {
        try {
            String conversationId = queryParams.get("conversationId");
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            if (Objects.nonNull(conversationId) && !conversationId.isEmpty()) {
                SupportConversationEntry supportSession = customerSupportClient.fetchSessionDetail(userId, Long.valueOf(conversationId));
                if (Objects.nonNull(supportSession)) {
                    return CSConversation.getTicketStatus(supportSession.getStatus(), supportSession.getTicketId());
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public SfGenericPageView getGenericPageData(UserContext userContext, Map<String, String> queryParams) {
        String pageType = queryParams.getOrDefault("pageId", "");
        switch (pageType) {
            case "sfsupportfaqs": return sfSupportFAQsPageBuilder.buildView(userContext);
            case "digiprogramlearn": return digiProgramLearnPageBuilder.buildView(userContext);
            default: return null;
        }
    }

    public SfOnboardingWalkthroughView getOnboardingWalkthroughSteps(UserContext userContext) {
        return sfOnboardingWalkthroughPageBuilder.buildView(userContext);
    }

    public WalkthroughScreenView getWalkthroughScreens(UserContext userContext) {
        return walkthroughScreenBuilder.buildView(userContext);
    }

    public List<CSChat> replyToCSConversation(UserContext userContext, TicketReplyRequest replyRequest) {
        SessionReplyRequest sessionReplyRequest = new SessionReplyRequest();
        sessionReplyRequest.setResponse(replyRequest.getResponseMessage());
        sessionReplyRequest.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        if (!CollectionUtils.isEmpty(replyRequest.getAttachments())) {
            List<AttachmentsRequest> attachmentsRequests = replyRequest.getAttachments().stream().map(att -> {
                AttachmentsRequest attReq = new AttachmentsRequest();
                attReq.setAttachmentUrl(att.getFilePath());
                attReq.setFileExtension(att.getFileExtension());
                attReq.setContentType(att.getMimeType());
                return attReq;
            }).toList();
            sessionReplyRequest.setAttachments(attachmentsRequests);
        }
        List<SupportChatEntry> supportChatEntries = customerSupportClient.replyOnSession(Long.valueOf(userContext.getUserProfile().getUserId()), replyRequest.getConversationId(), sessionReplyRequest);
        Collections.reverse(supportChatEntries);
        if (!CollectionUtils.isEmpty(supportChatEntries)) {
            return supportChatEntries.stream().map(entry -> ChronicCareAppUtil.convertSupportChatToCsChat(entry, userContext)).toList();
        }
        return new ArrayList<>();
    }

    public SfSupportPageView getSupportPage(UserContext userContext) {
        return this.sfSupportPageBuilder.buildView(userContext);
    }

    public List<String> getGenericSupportTopics() {
        return this.customerSupportClient.fetchAllCategories();
    }

    public ConsultationSummaryResponse getConsultationSummary(Long consultationId, UserContext userContext) throws BaseException {
        String userId = userContext.getUserProfile().getUserId();
        ConsultationSummaryResponse consultationSummaryResponse = this.serviceInterfaces.getSfAlbusClient().getConsultationSummary(consultationId);
        if (!Objects.equals(consultationSummaryResponse.getPatientDetail().getCurefitUserId(), userId)) {
            String message = "Invalid Access to get consultation summary";
            throw new BaseException(message);
        }
        return consultationSummaryResponse;
    }

    public CommunityFilterResponse filterCommunities(UserContext userContext, CommunityFilterRequest communityFilterRequest) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        communityFilterRequest.setUserId(userId);
        communityFilterRequest.setUserType(UserType.USER);
        return chatClient.filterCommunities(communityFilterRequest);
    }

    public CommunityFilterResponse filterUserCommunities(UserContext userContext, CommunityFilterRequest communityFilterRequest) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        communityFilterRequest.setUserId(userId);
        communityFilterRequest.setUserType(UserType.USER);
        return chatClient.filterUserCommunities(communityFilterRequest);
    }

    public boolean markSeenCommunityWelcomeModal(UserContext userContext) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
            userAttributeEntry.setUserId(userId);
            userAttributeEntry.setAppTenant(appTenant);
            userAttributeEntry.setAttribute("sf-seen-community-welcome-modal");
            userAttributeEntry.setAttrValue(true);
            userAttributeEntry.setDataType(DataType.BOOLEAN);
            this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public boolean markSeenUnlockFbvExpModal(UserContext userContext) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
            userAttributeEntry.setUserId(userId);
            userAttributeEntry.setAppTenant(appTenant);
            userAttributeEntry.setAttribute(UNLOCK_FBV_EXP_USER_ATTRIBUTE_FLAG);
            userAttributeEntry.setAttrValue(true);
            userAttributeEntry.setDataType(DataType.BOOLEAN);
            this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public boolean markSeenCommunitySendIntroModal(UserContext userContext) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
            userAttributeEntry.setUserId(userId);
            userAttributeEntry.setAppTenant(appTenant);
            userAttributeEntry.setAttribute("sf-seen-community-send-intro-modal");
            userAttributeEntry.setAttrValue(true);
            userAttributeEntry.setDataType(DataType.BOOLEAN);
            this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public CommunityIntroTemplatesData fetchUserIntroductionTemplates(UserContext userContext) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());

            CommunityIntroTemplatesData result = new CommunityIntroTemplatesData();
            result.setHasSeenCommunityWelcomeModal(chronicCareServiceHelper.isCommunityWelcomeModalShown(userContext));
            result.setHasSeenChatSendIntroModal(chronicCareServiceHelper.isCommunitySendIntroModalShown(userContext));
            result.setIntroTemplatesData(chatClient.filterUserIntroductionTemplates(userId));

            return result;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public MessageTemplateFilterResponse filterUserIntroductionReplyTemplates(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long communityId = Long.valueOf(queryParams.getOrDefault("communityId", null));
        Long messageId = Long.valueOf(queryParams.getOrDefault("messageId", null));
        return chatClient.filterUserIntroductionReplyTemplates(communityId, userId, messageId);
    }

    public NudgesResponse fetchChatNudges(UserContext userContext) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return chatClient.fetchChatNudges(userId);
    }

    public Map<Long, UnreadMessageInfoEntry> fetchUnreadMessageInfo(UserContext userContext, UnreadMessageInfoFilterRequest filterRequest) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        filterRequest.setUserId(userId);
        filterRequest.setUserType(UserType.USER);
        return chatClient.fetchUnreadMessageInfo(filterRequest);
    }

    public MessageFilterResponse filterChatMessages(UserContext userContext, MessageFilterRequest messageFilterRequest) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        messageFilterRequest.setRequesterId(userId);
        messageFilterRequest.setRequesterIdType(UserType.USER);
        messageFilterRequest.setRequestSource("APP");
        return chatClient.filterMessages(messageFilterRequest);
    }

    public MessageNotificationPreferenceEntry fetchCommunityNotificationPreference(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long communityId = Long.valueOf(queryParams.getOrDefault("communityId", null));
        return chatClient.fetchNotificationPreference(userId, UserType.USER, communityId);
    }

    public MessageNotificationPreferenceEntry saveCommunityNotificationPreference(UserContext userContext, MessageNotificationPreferenceEntry notificationPreferenceEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        notificationPreferenceEntry.setUserId(userId);
        notificationPreferenceEntry.setUserType(UserType.USER);
        return chatClient.saveNotificationPreference(notificationPreferenceEntry);
    }

    public Map<String, String> fetchChatMediaUploadPath(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long communityId = Long.valueOf(queryParams.get("communityId"));
        return chatClient.fetchMediaUploadPath(communityId);
    }

    public Map<String, String> fetchChatMediaDownloadPath(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long messageId = Long.valueOf(queryParams.get("messageId"));
        return chatClient.fetchMediaDownloadPath(messageId);
    }

    public Action fetchSlotBookingAction(UserContext userContext) throws Exception {
        Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());
        CompletableFuture<UserOnboardingActionWithContext> onboardingActionFuture = getUserOnboardingActionsFuture(
                serviceInterfaces,
                userContext,
                activePackResponse,
                userOnboardingService,
                exceptionReportingService
        );
        UserOnboardingActionWithContext onboardingActions = onboardingActionFuture.get();

        if (Objects.nonNull(onboardingActions) && activePackResponse.isPresent()
                && Objects.nonNull(onboardingActions.getCoachCardActionWithContext())
                && Objects.nonNull(onboardingActions.getCoachCardActionWithContext().getContext())
                && !CollectionUtils.isEmpty(onboardingActions.getCoachCardActionWithContext().getContext().getProductCodes())) {
            String coachConsultationProduct = onboardingActions.getCoachCardActionWithContext().getContext().getProductCodes().stream().findFirst().get();
            PatientDetail patient = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            ChronicCareTeam assignedCareTeam = chronicCareServiceHelper.getAssignedCareTeam(userContext, patient.getId(), activePackResponse.get().getBundleProduct());
            PatientPreferredAgentResponse coach = assignedCareTeam.getCoach();
            Long coachCenterId = coach.getAgentResponse().getAgentCenterMapping().stream().findFirst().get().getCenterId();
            Date upcomingCoachConsultationDate = chronicCareServiceHelper.getUpcomingConsultationDateForDisablingMultipleBookings(userContext, userContext.getUserProfile().getUserId(), coachConsultationProduct);
            if (upcomingCoachConsultationDate != null) {
                return getDisabledCoachOrDoctorBookingAction(userContext, upcomingCoachConsultationDate, "coach");
            }
            if (chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.get().getBundleProduct())) {
                if (!masterClassClient.fetchCoachConsultationStatus(Long.valueOf(userContext.getUserProfile().getUserId()))) {
                    return getDisabledCoachOrDoctorBookingActionForSpecialPackDelayed();
                }
            }

            return Action.builder()
                    .isEnabled(onboardingActions.getCoachCardActionWithContext().getAction().isActionPermitted())
                    .title("Book Slot")
                    .actionType(ActionType.NAVIGATION)
                    .url(SfHomePageUtil.getAgentDatePickerUrl(coachConsultationProduct, coachCenterId, coach.getAgentResponse().getId(), activePackResponse.get()))
                    .build();
        }

        return null;
    }

    public CoachAvailabilityStatus fetchCoachAvailabilityStatus(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        String coachId = queryParams.getOrDefault("coachId", null);

        if (coachId != null) {
            return chatClient.fetchCoachAvailabilityStatus(Long.valueOf(coachId));
        }
        return null;
    }

    public SfSlotBookingPageView getSlotBookingDatePickerV2(UserContext userContext, Map<String, String> queryParams) {
        return sfSlotBookingPageViewBuilder.buildView(userContext, queryParams);
    }

    public MessageEntry sendChatMessage(UserContext userContext, SendMessageRequest sendMessageRequest) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        sendMessageRequest.setUserId(userId);
        sendMessageRequest.setUserType(UserType.USER);
        return chatClient.sendMessage(sendMessageRequest);
    }

    public MessageEntry deleteChatMessage(UserContext userContext, DeleteMessageRequest deleteMessageRequest) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        deleteMessageRequest.setUserId(userId);
        deleteMessageRequest.setUserType(UserType.USER);
        return chatClient.deleteMessage(deleteMessageRequest);
    }

    public StarredMessageEntry starChatMessage(UserContext userContext, StarredMessageEntry starredMessageEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        starredMessageEntry.setUserId(userId);
        starredMessageEntry.setUserType(UserType.USER);
        return chatClient.starredMessage(starredMessageEntry);
    }

    public boolean unStarChatMessage(UserContext userContext, StarredMessageEntry starredMessageEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        starredMessageEntry.setUserId(userId);
        starredMessageEntry.setUserType(UserType.USER);
        return chatClient.unStarredMessage(starredMessageEntry);
    }

    public List<StarredMessageEntry> filterStarredMessages(UserContext userContext) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return chatClient.filterStarredMessage(userId, UserType.USER);
    }

    public MessageEntry addChatReaction(UserContext userContext, MessageReactionEntry messageReactionEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        messageReactionEntry.setUserId(userId);
        messageReactionEntry.setUserType(UserType.USER);
        return chatClient.addMessageReaction(messageReactionEntry);
    }

    public MessageEntry deleteChatReaction(UserContext userContext, MessageReactionEntry messageReactionEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        messageReactionEntry.setUserId(userId);
        messageReactionEntry.setUserType(UserType.USER);
        return chatClient.deleteMessageReaction(messageReactionEntry);
    }

    public void updateLastReadMessage(UserContext userContext, UnreadMessageInfoEntry messageInfoEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        messageInfoEntry.setUserId(userId);
        messageInfoEntry.setUserType(UserType.USER);
        chatClient.updateLastReadMessage(messageInfoEntry);
    }

    public SfPollsPageView getAllPolls(UserContext userContext, Map<String, String> queryParams) {
        String pollType = queryParams.getOrDefault("pollType", "activePolls");
        Integer pageNumber = Integer.valueOf(queryParams.getOrDefault("pageNumber", "0"));
        return this.sfPollsPageBuilder.buildView(userContext, pollType, pageNumber);
    }

    public PollEntry getPollById(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long pollId = Long.valueOf(queryParams.getOrDefault("pollId", "0"));
        return this.pollSupportClient.fetchPoll(userId, pollId);
    }

    public PollResultsData fetchCompletedPoll(UserContext userContext, Map<String, String> queryParams) throws Exception {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long pollId = Long.valueOf(queryParams.getOrDefault("pollId", "0"));
        CompletePollEntry res = this.pollSupportClient.fetchCompletedPoll(userId, pollId);
        PollResultsData pollResultData = objectMapper.convertValue(res, PollResultsData.class);
        if(res != null && res.getResults() != null && res.getResults().getMentalHealthResult() != null) {
            pollResultData.setMentalHealthResult(res.getResults().getMentalHealthResult());
            if (DistressType.HIGH_DISTRESS.equals(res.getResults().getMentalHealthResult().getDDSDistress()) || DistressType.MODERATE_DISTRESS.equals(res.getResults().getMentalHealthResult().getDDSDistress())) {
                Action action = chronicCareServiceHelper.getPyschologyConsultBookingAction(userContext);
                if (Objects.nonNull(action)) {
                    pollResultData.setPrimaryAction(action);
                }
            } else if (DistressType.LOW_DISTRESS.equals(res.getResults().getMentalHealthResult().getDDSDistress())) {
                pollResultData.setPrimaryAction(chronicCareServiceHelper.getCoachConsultBookingAction(userContext, ""));
            }
        }
        return pollResultData;
    }

    public QuestionEntry answerOnPoll(UserContext userContext, SfPollAnswer sfPollAnswer) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return this.pollSupportClient.voteOnQuestion(userId, sfPollAnswer.getPollId(), sfPollAnswer.getQuestionId(), sfPollAnswer.getAnswerIds());
    }

    public CompletePollEntry completePoll(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long pollId = Long.valueOf(queryParams.getOrDefault("pollId", "0"));
        return this.pollSupportClient.completePoll(userId, pollId);
    }

    public List<BaseWidgetNonVM> getPollSurveyGroupWidget(UserContext userContext) throws BaseException {
        return sfPollSurveyBuilder.buildView(userContext);
    }

    public List<BaseWidgetNonVM> getPollGroupWidget(UserContext userContext) throws Exception {
        return homePageViewBuilder.getPollCardWidgets(userContext);
    }

    public SfDiscoverBlogCardWidget getBlogWidget(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String blogId = queryParams.getOrDefault("blogId", "0");
        try {
            BlogDetailedResponse blogDetailedResponse = this.catalogClient.getBlogDetails(userId, blogId);
            if (blogDetailedResponse != null) {
                SfDiscoverBlogCardWidget blogCardWidget = new SfDiscoverBlogCardWidget();
                blogCardWidget.setBlogData(blogDetailedResponse.getBlogData());
                blogCardWidget.setFromPage("sfpolldetailspage");
                blogCardWidget.setReadBlog(blogDetailedResponse.getBlogData().isRead());
                blogCardWidget.setHasShadow(false);
                return blogCardWidget;
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("BlogWidget API failure", e);
            return null;
        }
        return null;
    }

    public SfChallengesListPageView getChallengesListPage(UserContext userContext, Map<String, String> queryParams) {
        SfChallengesListPageView.ChallengeTabs type = SfChallengesListPageView.ChallengeTabs.valueOf(queryParams.getOrDefault("type", "ACTIVE_CHALLENGES"));
        Integer pageNumber = Integer.valueOf(queryParams.getOrDefault("pageNumber", "0"));
        return this.sfChallengesListPageBuilder.buildView(userContext, type, pageNumber);
    }

    public SfChallengeDetailsPageView getChallengeDetailsPage(UserContext userContext, Map<String, String> queryParams) {
        Long challengeId = Long.valueOf(queryParams.getOrDefault("challengeId", "0"));
        return this.sfChallengeDetailsPageBuilder.buildView(userContext, challengeId);
    }

    public boolean challengeHasTodaysEntry(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long challengeId = Long.valueOf(queryParams.getOrDefault("challengeId", "0"));
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        List<ChallengesUserResponseEntry> userEntries = challengesClient.fetchUserResponsesEntries(userId, challengeId);
        if (!CollectionUtils.isEmpty(userEntries)) {
            AtomicBoolean hasTodaysEntry = new AtomicBoolean(false);
            Calendar todayCalendar = Calendar.getInstance(getUserTimezone(userContext));
            Date today = todayCalendar.getTime();
            userEntries.forEach(e -> {
                if (SfDateUtils.isSameDay(today, e.getActivityTime())) {
                    hasTodaysEntry.set(true);
                }
            });
            return hasTodaysEntry.get();
        }
        return false;
    }

    public List<SfChallengeUserEntry> getRejectedChallengeDetails(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long challengeId = Long.valueOf(queryParams.getOrDefault("challengeId", "0"));
        List<ChallengesUserResponseEntry> challengesUserResponseEntries = challengesClient.fetchRejectedEntries(userId, challengeId);
        if (!CollectionUtils.isEmpty(challengesUserResponseEntries)) {
            List<SfChallengeUserEntry> challengeUserEntries = challengesUserResponseEntries.stream().map(userEntry -> {
                SfChallengeUserEntry sfChallengeUserEntry = new SfChallengeUserEntry();
                sfChallengeUserEntry.setApprovalStatus(userEntry.getApprovalStatus());
                sfChallengeUserEntry.setChallengeType(userEntry.getChallengeType());
                sfChallengeUserEntry.setActivityTime(userEntry.getActivityTime());

                SfChallengeUserEntry.UserValue userValue = new SfChallengeUserEntry.UserValue();
                switch (userEntry.getChallengeType()) {
                    case STEPS_CHALLENGE -> userValue.setSteps(userEntry.getUserResponseValue().getUserSteps().getStepsCount());
                    case SUGAR_LOG_CHALLENGE -> userValue.setSugarValue(userEntry.getUserResponseValue().getGlucometerReadingEntry().getValue());
                    default -> {
                        userValue.setCaption(userEntry.getUserResponseValue().getCaption());
                        if (userEntry.getUserResponseValue().getAttachments() != null) {
                            List<SfAttachment> attachments = new ArrayList<>();
                            List<com.sugarfit.challenges.pojo.Attachments> attachmentsFromBE = userEntry.getUserResponseValue().getAttachments().values().stream().toList();
                            attachmentsFromBE.forEach(a -> {
                                if (a != null) {
                                    SfAttachment sfAttachment = new SfAttachment();
                                    sfAttachment.setFileUrl(a.getAttachmentUrl());
                                    sfAttachment.setMimeType(a.getMimeType());
                                    attachments.add(sfAttachment);
                                }
                            });
                            userValue.setAttachments(attachments);
                        }
                    }
                };

                sfChallengeUserEntry.setUserValue(userValue);
                return sfChallengeUserEntry;
            }).toList();

            return challengeUserEntries;
        }
        return null;
    }

    public boolean uploadChallengeEntry(UserContext userContext, SfChallengeUserEntry challengeEntry) throws BaseException, ParseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        ChallengesUserResponseEntry challengesUserResponseEntry = new ChallengesUserResponseEntry();
        challengesUserResponseEntry.setUserId(userId);
        challengesUserResponseEntry.setChallengeId(challengeEntry.getChallengeId());
        challengesUserResponseEntry.setChallengeType(challengeEntry.getChallengeType());
        challengesUserResponseEntry.setActivityTime(challengeEntry.getActivityTime());
        challengesUserResponseEntry.setTimeZone(getUserTimezone(userContext));

        UserResponseValue userResponseValue = new UserResponseValue();
        if (challengeEntry.getUserValue().getAttachments() != null) {
            Map<String, com.sugarfit.challenges.pojo.Attachments> attachmentsForBE = new HashMap<>();
            challengeEntry.getUserValue().getAttachments().forEach(a -> {
                com.sugarfit.challenges.pojo.Attachments serverAttachment = new com.sugarfit.challenges.pojo.Attachments();
                serverAttachment.setAttachmentUrl(a.getFileUrl());
                serverAttachment.setMimeType(a.getMimeType());
                attachmentsForBE.put(a.getFilePath(), serverAttachment);
            });
            userResponseValue.setAttachments(attachmentsForBE);
        }
        challengesUserResponseEntry.setUserResponseValue(userResponseValue);
        challengesClient.uploadEntry(challengesUserResponseEntry);
        return true;
    }

    public Map<String, String> getSignedUrlForChallengeEntry(int fileCount) throws BaseException {
        return this.challengesClient.fetchSignedUploadUrl(fileCount);
    }

    public ChallengesEntry joinChallenge(UserContext userContext, Long challengeId) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return this.challengesClient.joinChallenge(userId, challengeId);
    }

    public SfChallengeCardWidget getChatWidgetForPendingChallenge(UserContext userContext) throws BaseException, ExecutionException, InterruptedException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache()
                .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
        Set<String> relevantSegments = serviceInterfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.SF_CHALLENGES_CLIENT);
        List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(relevantSegments));
        List<ChallengesEntry> activeChallenges = challengesClient.fetchActiveChallengesForUser(userId, 0, 20, "createdOn", "DESC", userSegments).getElements();

        if (!CollectionUtils.isEmpty(activeChallenges)) {
            activeChallenges = activeChallenges.stream().filter(c -> c.getChallengeType() != ChallengeType.REFERRAL_CHALLENGE).toList();
        }

        if (!CollectionUtils.isEmpty(activeChallenges)) {
            Optional<ChallengesEntry> unuploadedChallengeOpt = activeChallenges.stream().filter(challengesEntry -> {
                if (challengesEntry.getUserJoinedDate() != null && !SfDateUtils.isFutureDate(userContext, challengesEntry.getStartDate())) {
                    AtomicBoolean hasUploadedTodaysData = new AtomicBoolean(false);

                    try {
                        List<ChallengesUserResponseEntry> challengesUserResponseEntries = challengesClient.fetchUserResponsesEntries(userId, challengesEntry.getId());
                        if (!CollectionUtils.isEmpty(challengesUserResponseEntries)) {
                            Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
                            Date today = calendar.getTime();
                            challengesUserResponseEntries.forEach(userEntry -> {
                                if (userEntry != null && SfDateUtils.isSameDay(today, userEntry.getActivityTime())) {
                                    hasUploadedTodaysData.set(true);
                                }
                            });
                        }
                    } catch (Exception e) {
                        // Ignore
                    }
                    return !hasUploadedTodaysData.get();
                }
                return false;
            }).findFirst();
            if (unuploadedChallengeOpt.isPresent()) {
                ChallengesEntry challengesEntry = unuploadedChallengeOpt.get();
                SfChallengeCardWidget challengeCardWidget = new SfChallengeCardWidget();
                boolean isIndividual = challengesEntry.getChallengeAudience().equals(ChallengeAudienceType.INDIVIDUAL_CHALLENGE);
                challengeCardWidget.setTitle(challengesEntry.getTitle());
                challengeCardWidget.setBadgeTitle(isIndividual ? "INDIVIDUAL CHALLENGE" : "BATTLE OF COACHES");
                challengeCardWidget.setDuration(challengesEntry.getDuration());
                challengeCardWidget.setStartTime(challengesEntry.getStartDate());
                challengeCardWidget.setEndTime(challengesEntry.getEndDate());
                challengeCardWidget.setUserJoinedDate(challengesEntry.getUserJoinedDate());
                challengeCardWidget.setChallengeAudienceType(challengesEntry.getChallengeAudience());

                List<LeaderboardEntry> leaderBoardEntries = challengesClient.fetchLeaderboard(userId, challengesEntry.getId(), 0, 1, "createdOn", "DESC").getElements();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(leaderBoardEntries)) {
                    boolean notEligible = false;
                    int minimumParticipationLimit = 0;
                    LeaderboardEntry leaderboardEntry = leaderBoardEntries.get(0);
                    if(leaderboardEntry.getRankEligibility() != null) {
                        notEligible = leaderboardEntry.getRankEligibility() == RankEligibilityType.NOT_ELIGIBLE;
                    }
                    if(leaderboardEntry.getMinimumParticipationLimit() != null) {
                        minimumParticipationLimit = leaderboardEntry.getMinimumParticipationLimit();
                    }
                    if(!isIndividual && notEligible && minimumParticipationLimit >= 0) {
                        challengeCardWidget.setErrorMsg((minimumParticipationLimit - leaderboardEntry.getUserLeaderBoardEntries().size()) + " more participants required to be eligible");
                    }
                }

                List<String> gradientColors = new ArrayList<>();
                if (isIndividual) {
                    gradientColors.add("#8791EF");
                    gradientColors.add("#4461C8");
                } else {
                    gradientColors.add("#816a9f");
                    gradientColors.add("#3B225D");
                }
                challengeCardWidget.setGradientColors(gradientColors);
                Action primaryAction = new Action();
                primaryAction.setTitle("Join Challenge");
                primaryAction.setUrl("curefit://sfchallengedetailspage?challengeId=" + challengesEntry.getId());
                primaryAction.setActionType(ActionType.NAVIGATION);
                challengeCardWidget.setPrimaryAction(primaryAction);
                return challengeCardWidget;
            }
        }
        return null;
    }

    public SfChallengeLeaderboardPageView getLeaderboardPage(UserContext userContext, Long challengeId,  Long coachId, Integer pageNumber) throws BaseException {
        return this.sfChallengeLeaderboardPageBuilder.buildView(userContext, challengeId, coachId, pageNumber);
    }

    public SfChallengeEntrySubmissionData getChallengeSubmissionData(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        Long challengeId = Long.parseLong(queryParams.getOrDefault("challengeId", null));
        long submissionTime = Long.parseLong(queryParams.getOrDefault("submissionTime", null));
        UserPerformanceResponse userPerformanceResponse = challengesClient.fetchUserPerformanceResponse(userId, challengeId, new Date(submissionTime), true);
        if (userPerformanceResponse != null) {
            SfChallengeEntrySubmissionData sfChallengeEntrySubmissionData = new SfChallengeEntrySubmissionData();
            if (userPerformanceResponse.getIsUpdatedData()) {
                if (userPerformanceResponse.getCoachLeaderboard() != null) {
                    sfChallengeEntrySubmissionData.setCoachLeaderboardItem(getChallengeLeaderboardItem(userPerformanceResponse.getCoachLeaderboard(), true, chronicCareServiceHelper));
                }
                if (userPerformanceResponse.getUserLeaderboard() != null) {
                    sfChallengeEntrySubmissionData.setUserLeaderboardItem(getChallengeLeaderboardItem(userPerformanceResponse.getUserLeaderboard(), true, chronicCareServiceHelper));
                }
                if (userPerformanceResponse.getPercentile() != null) {
                    String winnerMessage = "Good going. You are an amazing player";
                    String winnerBadge = "image/chroniccare/challenges/challenge_team_player_v2.png";

                    if (userPerformanceResponse.getPercentile() >= 80 && userPerformanceResponse.getPercentile() < 100) {
                        winnerMessage = "You are doing better than " + Math.round(userPerformanceResponse.getPercentile()) + "% of the other participants";
                        winnerBadge = "image/chroniccare/challenges/challenge_best_player_v2.png";
                    }

                    if (userPerformanceResponse.getPercentile() == 100) {
                        winnerMessage = "Congratulations on being the best player! Keep up the great work.";
                        winnerBadge = "image/chroniccare/challenges/challenge_best_player_v2.png";
                    }

                    sfChallengeEntrySubmissionData.setWinnerBadge(winnerBadge);
                    sfChallengeEntrySubmissionData.setPerformanceMessage(winnerMessage);
                }
            }
            sfChallengeEntrySubmissionData.setIsUpdatedData(userPerformanceResponse.getIsUpdatedData());
            return sfChallengeEntrySubmissionData;
        }
        return null;
    }

    public SfRecommendedFitnessPlanWidget getRecommendedFitnessPlanWidget(UserContext userContext) throws BaseException {
        if (isRecommendedFitnessPlanEnabled(userContext)) {
            return this.sfRecommendedFitnessPlanWidgetBuilder.buildView(userContext);
        }
        return null;
    }

    public SfRecommendedFitnessPlanPageView getRecommendedFitnessPlanDetails(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return this.sfRecommendedFitnessItemWidgetBuilder.buildView(userContext, queryParams);
    }

    public SfNoShowPenaltyData checkForCoachBookingPenaltyNotification(UserContext userContext) {
        try {
            List<NotificationMeta> notificationMetaList = serviceInterfaces.inAppNotificationsService.getActiveUserAndAppId(userContext.getUserProfile().getUserId(),
                    "SUGARFIT");
            Optional<NotificationMeta> noShowNotificationOpt = notificationMetaList.stream().filter(notification ->
                    !Objects.isNull(notification.getTaskId()) && notification.getTaskId().contains("CONS-MISSED-PENALTY")).findFirst();
            if (noShowNotificationOpt.isPresent()) {
                SfNoShowPenaltyData noShowPenaltyData = sfNoShowPenaltyBuilder.buildView(userContext);
                if (Objects.nonNull(noShowPenaltyData)) {
                    Action viewedAction = new Action();
                    viewedAction.setActionType(ActionType.REST_API);
                    viewedAction.setTitle("I understand");
                    ActionMeta closeActionMeta = new ActionMeta();
                    closeActionMeta.setBody(new SfNoShowPenaltyBuilder.CloseActionState());
                    closeActionMeta.setMethod("POST");
                    closeActionMeta.setUrl("/user/inAppNotification/" + noShowNotificationOpt.get().getNotificationId());
                    viewedAction.setMeta(closeActionMeta);
                    noShowPenaltyData.setViewedAction(viewedAction);
                    return noShowPenaltyData;
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }


    public SFCGMDeliveryData checkForCGMDeliveryNotification(UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            NUXStatusResponse nuxStatus = smsClient.getNUXStatus(userId, true, timeZone);
            CompletableFuture<UserOnboardingActionWithContext> onboardingActionsFuture = null;
            if (nuxStatus != null && nuxStatus.getActivePack() != null) {
                if (!nuxStatus.getFreemium() && !nuxStatus.getNuxCompleted()) {
                    return null;
                } else {
                    Long activePackBookingId = nuxStatus.getActivePack().getSubscriptionId();
                    onboardingActionsFuture = userOnboardingService.getUserOnboardingActionsFuture(userContext, activePackBookingId);
                }
            }
            if (onboardingActionsFuture != null) {
                UserOnboardingActionWithContext onboardingActions = onboardingActionsFuture.get();
                List<NotificationMeta> notificationMetaList = serviceInterfaces.inAppNotificationsService.getActiveUserAndAppId(userContext.getUserProfile().getUserId(),
                        "SUGARFIT");
                Optional<NotificationMeta> noShowNotificationOpt = notificationMetaList.stream().filter(notification ->
                        !Objects.isNull(notification.getTaskId()) && notification.getTaskId().contains("CGM-DELIVERED")).findFirst();
                if (noShowNotificationOpt.isPresent()) {
                    boolean hasGlucoRxCgm = false;
                    CgmOnboardingStatusResponse cgmOnboardingStatus = chsClient.fetchOnboardingStatus(userId, null, getAppTenantFromUserContext(userContext));
                    if (Objects.nonNull(cgmOnboardingStatus)) {
                        hasGlucoRxCgm = cgmOnboardingStatus.getPendingDevices()
                                .stream()
                                .anyMatch(device -> device == DeviceModel.GLUCO_RX);
                    }

                    SFCGMDeliveryData cgmMetaData = new SFCGMDeliveryData();
                    cgmMetaData.setTitle("CGM Delivery Done");
                    cgmMetaData.setInstructionTitle("WHAT CAN YOU DO NEXT");
                    List<InstructionItem> instructions = new ArrayList<>();
                    InstructionItem instructionItem = new InstructionItem();
                    instructionItem.setIconType("image/chroniccare/instructions/calendar.png");
                    instructionItem.setText("Book video installation with our expert to get your CGM installed");
                    instructions.add(instructionItem);
                    Action primaryAction = null;
                    if (Objects.nonNull(cgmOnboardingStatus)) {
                        List<DeviceModel> availableCgmDevices = cgmOnboardingStatus.getPendingDevices();
                        primaryAction = chronicCareServiceHelper.getCgmWatchVideoAction(availableCgmDevices);
                    }
                    if (hasGlucoRxCgm) {
                        Map<String, Object> cgmActionMeta = new HashMap<>();
                        cgmActionMeta.put("cgms", cgmOnboardingStatus.getPendingDevices());
                        primaryAction = Action.builder().actionType(ActionType.SHOW_CGM_TYPE_SELECTION_MODAL).title("ACTIVATE NOW").meta(cgmActionMeta).build();
                    } else if (ChronicCareAppUtil.isUserExperiencePartTwoReleased(userContext)) {
                        UserPreferencePojo userPreferencePojo = serviceInterfaces.getSfAlbusClient().getUserPreference(userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "cgmServiceAvailable");
                        if (Objects.nonNull(userPreferencePojo) && org.apache.commons.collections.CollectionUtils.isNotEmpty(userPreferencePojo.getPreferenceTypeValues())) {
                            boolean isTierOneUser = Boolean.parseBoolean(userPreferencePojo.getPreferenceTypeValues().get(0));
                            if (!isTierOneUser) {
                                boolean hasNfc = chronicCareServiceHelper.getUserNfcStatus(userContext);
                                if (!hasNfc) {
                                    // For a Non-NFC and Tier-2 user give option to mark CGM as started
                                    primaryAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfcgmreaderinstallpage").title("Activate Now").build();
                                }
                            }
                        }
                    }

                     try {
                         String phleboConsultProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
                         Action secondaryAction = chronicCareServiceHelper.getBookCGMInstallationAction(userContext, phleboConsultProductId);
                         cgmMetaData.setSecondaryAction(secondaryAction);
                     } catch (Exception e) {
                         exceptionReportingService.reportException(e);
                     }
                    Action viewedAction = new Action();
                    viewedAction.setActionType(ActionType.REST_API);
                    ActionMeta closeActionMeta = new ActionMeta();
                    closeActionMeta.setBody(new SfNoShowPenaltyBuilder.CloseActionState());
                    closeActionMeta.setMethod("POST");
                    closeActionMeta.setUrl("/user/inAppNotification/" + noShowNotificationOpt.get().getNotificationId());
                    viewedAction.setMeta(closeActionMeta);
                    cgmMetaData.setPrimaryAction(primaryAction);
                    cgmMetaData.setViewedAction(viewedAction);
                    cgmMetaData.setInstructionItems(instructions);
                    return cgmMetaData;
                }
            }

        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public SfNoShowPenaltyData getCoachBookingPenaltyData(UserContext userContext) {
        return sfNoShowPenaltyBuilder.buildView(userContext);
    }

    public List<BaseWidgetNonVM> getChallengeGroupWidget(UserContext userContext) throws BaseException, ExecutionException, InterruptedException {
        return homePageViewBuilder.getChallengeWidgets(userContext);
    }

    public SfBluconDetailsPageView getBluconDetailsPage(UserContext userContext) {
        return sfBluconDetailsPageBuilder.buildView(userContext);
    }

    public SfDiagnosticStoreTest getDiagnosticStoreTests(UserContext userContext,Map<String, String> queryParams) throws BaseException {
        String addressId = queryParams.getOrDefault("addressId", null);
        return sfDiagnosticStoreTestPageBuilder.buildView(userContext,addressId);
    }


    public SfECommercePLPView getECommercePLP(UserContext userContext) {
        return sfEcommercePLPBuilder.buildView(userContext);
    }

    public SfECommercePDPView getECommercePDP(UserContext userContext, Map<String, String> queryParams) {
        String productId = queryParams.getOrDefault("productId", null);
        return sfECommercePDPBuilder.buildView(userContext, productId);
    }

    public SfECommercePDPView getECommercePDPV2(UserContext userContext, Map<String, String> queryParams) {
        return sfECommercePDPV2Builder.buildView(userContext, queryParams);
    }


    public SfDiagnosticTDPView getDiagnosticTDP(UserContext userContext,Map<String, String> queryParams) throws Exception {
        Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());
        return sfDiagnosticTDPBuilder.buildView(userContext,activePackResponse,queryParams);
    }

    public SfDiagnosticTestInstructionView getDiagnosticTestInstructions(List<String> productCodes) {
        return chronicCareServiceHelper.getDiagnosticsTestsInstructions(productCodes);
    }

    public SfEcommerceWebPDPView getECommerceWebPDP(UserContext userContext, Map<String, String> queryParams) {
        String subCategory = queryParams.getOrDefault("subCategory", null);
        return sfECommerceWebPDPBuilder.buildView(userContext, subCategory);
    }

    public Object getECommerceProductGroups(UserContext userContext, Map<String, String> queryParams) {
        return sfECommerceProductGroupBuilder.buildView(userContext);
    }

    public Object getECommerceConfigs() {
        try {
            return appConfigCache.<HashMap<String, String>>getConfig("SF_ECOMMERCE_CONFIG", new TypeReference<>() {
            }, new HashMap<>());
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public SfECommerceOrdersPageView getECommerceOrdersPage(UserContext userContext, Map<String, String> queryParams) {
        String pageNumberStr = queryParams.getOrDefault("pageNumber", "");
        long pageNumber = 0L;
        if (!pageNumberStr.isEmpty()) {
            pageNumber = Long.parseLong(pageNumberStr);
        }
        return sfEcommerceOrdersPageBuilder.buildView(userContext, pageNumber);
    }

    public SfECommerceOrderDetailsView getECommerceOrderDetails(UserContext userContext, Map<String, String> queryParams) {
        String orderIdStr = queryParams.getOrDefault("orderId", "");
        long orderId = 0L;
        if (!orderIdStr.isEmpty()) {
            orderId = Long.parseLong(orderIdStr);
        }
        return sfECommerceOrderDetailsBuilder.buildView(userContext, orderId);
    }

    public SfECommerceOrderSuccessPageView getECommerceOrderSuccessPage(UserContext userContext, Map<String, String> queryParams) {
        String orderIdStr = queryParams.getOrDefault("orderId", "");
        long orderId = 0L;
        if (!orderIdStr.isEmpty()) {
            orderId = Long.parseLong(orderIdStr);
        }
        return sfECommerceOrderSuccessPageBuilder.buildView(userContext, orderId);
    }

    public SfEComThingsToBuyListWidget getECommerceThingsToBuy(UserContext userContext, Map<String, String> queryParams) {
        return sfECommerceThingsToBuyWidgetBuilder.buildView(userContext);
    }

    public SfExperienceCenterCLPView getExperienceCenterCLP(UserContext userContext) {
        return sfExperienceCenterCLPBuilder.buildView(userContext);
    }

    public SfExperienceCenterSelectPageView getExperienceCenterList(UserContext userContext) {
        return sfExperienceCenterSelectPageBuilder.buildView(userContext);
    }

    public ServiceabilityCheckResponse ecommerceServiceabilityCheck(UserContext userContext, Long pincode) throws HttpException {
        return indusClient.serviceabilityCheck(pincode);
    }

    public OrderDetailedResponse ecommerceServiceabilityCheck(Long cfOrderId) throws HttpException {
        OrderDetailedResponse response = indusClient.getOrderDetails(null, cfOrderId);
        response.setDeliveryAddress(null);
        return response;
    }


    public boolean uploadRoomMetrics(UserContext userContext, RoomMetricsRequest roomMetricsRequest) throws BaseException {
        videoRoomClient.postRoomMetrics(roomMetricsRequest);
        return true;
    }

    public void updateUserDeviceNfcStatus(UserContext userContext, UserNfcStatusRequest nfcStatusRequest) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        nfcStatusRequest.setUserId(userId);
        chsClient.updateUserNfcStatus(nfcStatusRequest, appTenant);
    }

    public QuestionEntry answerPollSurvey(UserContext userContext, VoteEntry voteEntry) throws BaseException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return this.pollSupportClient.voteOnQuestionV2(userId, voteEntry.getPollId(), voteEntry.getQuestionId(), voteEntry);
    }

    public Object getActivityDetails(UserContext userContext, Long activityId, ActivityType activityType) throws HttpException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        TimeZone timeZone = getUserTimezone(userContext);
        return this.sfLoggingClient.fetchActivityDetail(activityId, activityType, appTenant, timeZone);
    }

    public List<FormQuestionEntry> getExperienceCenterKickStarterConfig() throws HttpException {
        return chsClient.fetchForm(String.valueOf(UserProfileCategory.KICKSTARTER_ASSESSMENT));
    }

    public KickstarterAssessment getExperienceCenterKickStarterForm(String phoneNumber, String countryCode) throws BaseException {
        if (!Objects.isNull(phoneNumber)) {
            UsersResponse userResponse = userServiceClient.getByPhone(phoneNumber, countryCode);
            if (!Objects.isNull(userResponse)) {
                String completePhoneNum = Objects.isNull(countryCode) ?  phoneUtil.getCompletePhoneNumber(phoneNumber) : (countryCode + "-" + phoneNumber);
                UserEntry userEntry = userResponse.getUsers().get(completePhoneNum);
                if (!Objects.isNull(userEntry)) {
                    UserProfileResponse userProfileResponse = chsClient.getLifestyleRecall(userEntry.getId(), List.of(UserProfileCategory.KICKSTARTER_ASSESSMENT));
                    if (!Objects.isNull(userProfileResponse)) {
                        return userProfileResponse.getKickstarterAssessment();
                    }
                }
            }
        }
        return null;
    }

    public List<UserProfileEntry> saveExperienceCenterKickStarterForm(SfExperienceCenterKSSaveRequest ksSaveRequest) throws BaseException {
        if (!Objects.isNull(ksSaveRequest.getPhoneNumber())) {
            UsersResponse userResponse = userServiceClient.getByPhone(ksSaveRequest.getPhoneNumber(), ksSaveRequest.getCountryCode());
            if (!Objects.isNull(userResponse)) {
                String completePhoneNum = Objects.isNull(ksSaveRequest.getCountryCode()) ?  phoneUtil.getCompletePhoneNumber(ksSaveRequest.getPhoneNumber()) : (ksSaveRequest.getCountryCode() + "-" + ksSaveRequest.getPhoneNumber());
                UserEntry userEntry = userResponse.getUsers().get(completePhoneNum);
                if (!Objects.isNull(userEntry)) {
                    UserProfileRequest userProfileRequest = new UserProfileRequest();
                    userProfileRequest.setUserId(userEntry.getId());
                    userProfileRequest.setKickstarterAssessment(ksSaveRequest.getKickstarterAssessment());
                    return chsClient.saveLifestyleRecall(userProfileRequest);
                }
            }
        }
        return null;
    }

    public Object getCGMInstallationVideosData(UserContext userContext) throws Exception {
        List<GenericVideoData> cgmVideos = catalogClient.getCGMInstallationVideo();
        HashMap<String, Object> meta = new HashMap<>();
        meta.put("allVideos", cgmVideos);

        Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());
        CompletableFuture<UserOnboardingActionWithContext> onboardingActionFuture = getUserOnboardingActionsFuture(
                serviceInterfaces,
                userContext,
                activePackResponse,
                userOnboardingService,
                exceptionReportingService
        );

        UserOnboardingActionWithContext onboardingActions = onboardingActionFuture.get(5, TimeUnit.SECONDS);
        boolean allowCGMActivation = false;
        if (onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse() != null
                && onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions() != null) {
            allowCGMActivation = onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse().getSelfInstallationActions().equals(SelfInstallationActions.ALLOW_SELF_ACTIVATION);
        }

        if(allowCGMActivation) {
//          Ask user if facing any difficulty while self installation
            Action primaryAction = chronicCareServiceHelper.getWatchCGMInstallationVideoAction("Restart the Tutorial", true, false);
            Action secondaryAction = null;
            try {
               String phleboConsultProductId = ChronicCareServiceHelper.getProductIdForPhleboConsult(onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse());
               secondaryAction = chronicCareServiceHelper.getBookCGMInstallationAction(userContext,phleboConsultProductId);
            } catch (Exception e) {
               exceptionReportingService.reportException(e);
            }
            Date upcomingConsultationDate = chronicCareServiceHelper.getUpcomingConsultationDateForDisablingMultipleBookings(
                    userContext,
                    userContext.getUserProfile().getUserId(), ChronicCareServiceHelper.getProductIdForPhleboConsult(
                            onboardingActions.getAGMDataActionWithContext().getContext().getSelfInstallationStatusResponse()));
            if(upcomingConsultationDate != null) {
//                already booked consultation
                meta.put("hideFacingDifficulty", true);
            } else {
                meta.put("hideFacingDifficulty", false);
                meta.put("primaryAction", primaryAction);
                if (secondaryAction != null) {
                    meta.put("secondaryAction", secondaryAction);
                }
            }
        } else {
            meta.put("hideFacingDifficulty", true);
        }

        return meta;
    }

    public Map<String, JsonNode> getFoodCompareData(List<String> keys) throws HttpException {
        return this.catalogClient.getBulkStaticData(keys);
    }

    public boolean isDiabetesType2Selected(UserContext userContext) throws BaseException {
        Long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        String attributeName = "nux_diabetes_type";
        UserAttributesResponse userAttributesResponse = userAttributesClient.getAttributes(userId, attributeName, getAppTenantFromUserContext(userContext), null);
        if (null != userAttributesResponse && null != userAttributesResponse.getAttributes() && userAttributesResponse.getAttributes().containsKey(attributeName) && userAttributesResponse.getAttributes().get(attributeName) != null) {
            String response = String.valueOf(userAttributesResponse.getAttributes().get(attributeName));
            Map<String, String> diabetesTypeResponse = (Map<String, String>) JSON.parse(response);
            return diabetesTypeResponse.get("diabetes_type") != null && diabetesTypeResponse.get("diabetes_type").equals("TYPE_2_DIABETES");
        }
        return false;
    }

    public Action getContactDetailsAction(UserContext userContext) throws BaseException {
        Map<String, Object> meta = new HashMap<>();
        String attributeName = "sf-saved-coach-contact-details";
        Long userId = Long.parseLong(userContext.getUserProfile().getUserId());
//        coach-contact-details attribute having the contact details saved in user's device
         UserAttributesResponse userAttributesResponse = userAttributesClient.getAttributes(userId,
                 attributeName, getAppTenantFromUserContext(userContext), null);
//         doorman config for getting latest coach contact details
        SFContactDetails config = chronicCareServiceHelper.getCoachContactDetails();
        String newGivenName = config.getGivenName();
        String newFamilyName = config.getFamilyName();
        List<SFContactDetails.PhoneNumber> newPhoneNumbers = config.getPhoneNumbers();
        boolean syncContactDetails = false;
        boolean userAttributeExists = userAttributesResponse != null && userAttributesResponse.getAttributes() != null && userAttributesResponse.getAttributes().containsKey(attributeName);
        SFContactDetails savedContactDetails = null;
        if(config != null && newGivenName != null && newPhoneNumbers != null && !CollectionUtils.isEmpty(newPhoneNumbers)) {
            try {
                savedContactDetails = Serializer.deserialize((String) userAttributesResponse.getAttributes().get(attributeName), SFContactDetails.class);
            } catch (Exception e) {
                log.error("Exception in fetching user attribute value", e);
            }
            if(userAttributeExists && savedContactDetails != null && savedContactDetails.getGivenName() != null && savedContactDetails.getPhoneNumbers() != null) {
                    List<SFContactDetails.PhoneNumber> phoneNumbers = savedContactDetails.getPhoneNumbers();
//                    Checking if firstname, lastname or phone number varies
                    if(!savedContactDetails.getGivenName().equals(newGivenName) || !savedContactDetails.getFamilyName().equals(newFamilyName) || phoneNumbers.size() != newPhoneNumbers.size()) {
                        syncContactDetails = true;
                    }

//                    Checking if phone number change detected
                    List<String> savedNumbers = savedContactDetails.getPhoneNumbers().stream().map(SFContactDetails.PhoneNumber::getNumber).toList();
                    List<String> newNumbers = savedContactDetails.getPhoneNumbers().stream().map(SFContactDetails.PhoneNumber::getNumber).toList();
                    AtomicInteger counter = new AtomicInteger();
                    newNumbers.forEach(el -> {
                        if(savedNumbers.contains(el)) {
                            counter.getAndIncrement();
                        }
                    });

//                    phone number change detected
                    if(counter.get() != newNumbers.size()) {
                        syncContactDetails = true;
                    }
                } else {
                syncContactDetails = true;
            }
        }

        if(syncContactDetails) {
            meta.put("givenName", newGivenName);
            meta.put("familyName", newFamilyName);
            meta.put("phoneNumbers", newPhoneNumbers);
            return Action.builder().title("CONTACT DETAILS").actionType(ActionType.SF_SAVE_CONTACT_TO_PHONE).meta(meta).build();
        }

        return null;
    }

    public PollEntry getFeedbackForm(Long pollId) throws BaseException {
        return this.pollSupportClient.fetchPoll(pollId);
    }

    public List<QuestionEntry> saveFeedbackResponse(Long pollId, Long userId, List<VoteEntry> voteEntries) throws BaseException {
        List<QuestionEntry> res =  new ArrayList<>();
        for (VoteEntry voteEntry: voteEntries) {
            res.add(this.pollSupportClient.voteOnQuestionV2(userId, pollId, voteEntry.getQuestionId(), voteEntry));
        }
        return res;
    }

    public boolean isPlainTextInput(String phoneNumber) {
        try {
            List<String> usersList = appConfigCache.getConfig("SF_OTP_PLAIN_TEXT_INPUT_USERS", new TypeReference<List<String>>() {
            }, new ArrayList<>());
            if (!CollectionUtils.isEmpty(usersList)) {
                return usersList.contains(phoneNumber);
            }
        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return false;
    }

    public SfCGMInstallationBookingPage getCGMInstallationSlots(UserContext userContext, Map<String, Object> queryParams) {
        return this.cgmInstallationBookingPageViewBuilder.buildView(userContext, queryParams);
    }

    public SfRecommendedSlotPage getRecommendedCGMInstallationSlots(UserContext userContext, Map<String, Object> queryParams) {
        String title = "Recommended Slots for \n"+"CGM Installation";
        return chronicCareServiceHelper.getRecommendedSlotPageData(userContext, title, queryParams);
    }

    public boolean bookCgmInstallation(UserContext userContext, UserPhleboTaskBookingRequest requestPayloadReceived) {
        try {
            UserPhleboTaskBookingRequest requestPayload = new UserPhleboTaskBookingRequest();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            requestPayload.setUserId(userId);
            requestPayload.setPhleboId(requestPayloadReceived.getPhleboId());
            requestPayload.setSlotStartTime(requestPayloadReceived.getSlotStartTime());
            requestPayload.setSlotEndTime(requestPayloadReceived.getSlotEndTime());
            requestPayload.setTaskCategory(PhleboTaskCategory.DELIVERYANDINSTALLATION);
            serviceInterfaces.sfAlbusClient.bookPhleboTasks(requestPayload);
            return true;
        } catch (Exception e) {
            String message = "Failed to book CGM Installation, Exception: ";
            log.error(message+e.getMessage());
            exceptionReportingService.reportException(message, e);
        }
        return false;
    }

    public boolean rescheduleCgmInstallation(UserContext userContext, UserPhleboTaskRescheduleRequest requestPayloadReceived) {
        try {
            UserPhleboTaskRescheduleRequest requestPayload = new UserPhleboTaskRescheduleRequest();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            requestPayload.setUserId(userId);
            requestPayload.setPhleboId(requestPayloadReceived.getPhleboId());
            requestPayload.setSlotStartTime(requestPayloadReceived.getSlotStartTime());
            requestPayload.setSlotEndTime(requestPayloadReceived.getSlotEndTime());
            requestPayload.setTaskCategory(requestPayloadReceived.getTaskCategory());
            requestPayload.setPhleboTaskIds(requestPayloadReceived.getPhleboTaskIds());
            serviceInterfaces.sfAlbusClient.reschedulePhleboTasks(requestPayload);
            return true;
        } catch (Exception e) {
            String message = "Failed to reschedule CGM Installation, Exception: ";
            log.error(message+e.getMessage());
            exceptionReportingService.reportException(message, e);        }
        return false;
    }

    public boolean cancelCgmInstallation(UserContext userContext, UserPhleboTaskCancelRequest requestPayloadReceived) {
        try {
            UserPhleboTaskCancelRequest requestPayload = new UserPhleboTaskCancelRequest();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            requestPayload.setUserId(userId);
            requestPayload.setPhleboTaskIds(requestPayloadReceived.getPhleboTaskIds());
            serviceInterfaces.sfAlbusClient.cancelPhleboTasks(requestPayload);
            return true;
        } catch (Exception e) {
            String message = "Failed to cancel CGM Installation, Exception: ";
            log.error(message+e.getMessage());
            exceptionReportingService.reportException(message, e);
        }
        return false;
    }

    public SFConsultMissedModalData checkForWelcomeCallMissedNotification(UserContext userContext) {
//        Welcome Call Only
        try {
            List<NotificationMeta> notificationMetaList = serviceInterfaces.inAppNotificationsService.getActiveUserAndAppId(userContext.getUserProfile().getUserId(),
                    "SUGARFIT");
            Optional<NotificationMeta> noShowNotificationOpt = notificationMetaList.stream().filter(notification ->
                    !Objects.isNull(notification.getTaskId()) && notification.getTaskId().contains("CONS-MISSED-CGM-DELIVERY")).findFirst();

            if (noShowNotificationOpt.isPresent()) {
                SfInAppNotification.InAppNotificationData inAppNotificationData = null;
                try {
                    inAppNotificationData = Serializer.deserialize(noShowNotificationOpt.get().getDataBlob(), SfInAppNotification.InAppNotificationData.class);

                } catch (Exception exec) {
                    log.error("Exception | Missed In App Notification: "+ exec.getMessage());
                    exceptionReportingService.reportException("Exception | Missed In App Notification: ", exec);
                }
                SFConsultMissedModalData metaData = new SFConsultMissedModalData();
                Action primaryAction = chronicCareServiceHelper.getCoachConsultBookingAction(userContext, "");
                Action viewedAction = new Action();
                viewedAction.setActionType(ActionType.REST_API);
                ActionMeta closeActionMeta = new ActionMeta();
                closeActionMeta.setBody(new SfNoShowPenaltyBuilder.CloseActionState());
                closeActionMeta.setMethod("POST");
                closeActionMeta.setUrl("/user/inAppNotification/" + noShowNotificationOpt.get().getNotificationId());
                viewedAction.setMeta(closeActionMeta);
                metaData.setTitle("YOU HAVE MISSED YOUR COACH CONSULTATION");
                metaData.setType("COACH");
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                String attributeName = "latest_missed_coach_consult_info";
                UserAttributesResponse userAttributesResponse = serviceInterfaces.userAttributesClient
                        .getAttributes(userId, attributeName, getAppTenantFromUserContext(userContext), null);
                SfNoShowPenaltyBuilder.CoachConsultationMissedAttribute consultationMissedAttribute = Serializer.deserialize((String) userAttributesResponse.getAttributes().get(attributeName), SfNoShowPenaltyBuilder.CoachConsultationMissedAttribute.class);
                AgentResponse coach = serviceInterfaces.ollivanderAgentClient.getAgent(consultationMissedAttribute.getCoachId());
                metaData.setImageURL(coach.getDisplayImage());
                metaData.setCardMessage("Coach consultation is the first most important step of your journey.");
                metaData.setCardLabel("Please note");
                metaData.setCardTitle(inAppNotificationData != null && inAppNotificationData.getTitle() != null ? inAppNotificationData.getTitle() : "Your CGM Delivery is in 2 days.");
                metaData.setCardSubtitle("It is advised to connect with your coach before CGM installation");
                metaData.setPrimaryAction(primaryAction);
                metaData.setViewedAction(viewedAction);
                return metaData;
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public SfConsultationDeeplinkPage getConsultationJoinDeeplinkPage(UserContext userContext) throws ExecutionException, InterruptedException {
        SfConsultationDeeplinkPage pageView = new SfConsultationDeeplinkPage();
        String userIdString = userContext.getUserProfile().getUserId();
        CompletableFuture<ActivePackResponse> activePackResponseFuture = userOnboardingService.getSugarFitActivePackResponseFuture(userIdString);
        CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture = chronicCareServiceHelper.getConsultationBookingsFuture(userContext);
        if (Objects.nonNull(activePackResponseFuture.get()) && Objects.nonNull(consultationBookingsFuture.get())) {
            CompletableFuture<List<ActiveCard>> consultationActiveCards = chronicCareServiceHelper.getConsultationActiveCardsFuture(userContext, activePackResponseFuture, consultationBookingsFuture, null, null, null);
            List<ActiveCard> activeCards = consultationActiveCards.get();
            if (Objects.nonNull(activeCards) && !CollectionUtils.isEmpty(activeCards)) {
                Optional<ActiveCard> liveVideoCallCard = activeCards.stream()
                        .filter(c -> Objects.nonNull(c)
                        && Objects.nonNull(c.getPrimaryAction())
                        && Objects.nonNull(c.getPrimaryAction().getUrl())
                        && c.getPrimaryAction().getIsEnabled()
                        && c.getPrimaryAction().getUrl().contains("curefit://videochat")).findFirst();
                liveVideoCallCard.ifPresent(activeCard -> pageView.setAction(activeCard.getPrimaryAction()));
            }
        }

        return pageView;
    }

    public SfCoachCelebrationPage getCoachCelebrationPage(UserContext userContext, Map<String, String> queryParams) {
        return coachCelebrationPageBuilder.buildView(userContext);
    }

    public CoachAppreciationSuccessModalData submitCoachAppreciation(UserContext userContext, RenewalActionPayload renewalActionPayload) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = getUserTimezone(userContext);
        renewalActionPayload.setUserId(userId);
        renewalActionPayload.setActionType(RenewalActionType.COACH_CELEBRATION_COMPLETION);
        renewalActionPayload.setCoachCelebrationCompletionDate(Calendar.getInstance(timeZone).getTime());

        smsClient.markRenewalJourneyAction(renewalActionPayload);

        CoachAppreciationSuccessModalData modalData = new CoachAppreciationSuccessModalData();

        RenewalOfferCardData renewalOfferCardData = new RenewalOfferCardData();
        renewalOfferCardData.setSmallTitle("Your coach is");
        renewalOfferCardData.setLargeTitle("TOP RATED");
        renewalOfferCardData.setSubTitle("Extend your program now and lock your coach for another year");
        renewalOfferCardData.setOfferText(String.format("ONLY %s SEATS LEFT", new Random().nextInt(5) + 5));
        renewalOfferCardData.setAction(Action.builder().url("curefit://renewsubscription").title("BOOK FOR NEXT YEAR NOW").actionType(ActionType.NAVIGATION).build());

        modalData.setRenewalCard(renewalOfferCardData);
        return modalData;
    }

    public SfRenewalUserReportPage getRenewalUserReportPage(UserContext userContext) {
        return renewalUserReportPageBuilder.buildView(userContext);
    }

    public SfUserReportCongratsPage getUserReportCongratsPage(UserContext userContext) {
        return userReportCongratsPageBuilder.buildView(userContext);
    }

    public void updateRenewalJourney(UserContext userContext, RenewalActionPayload renewalActionPayload) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        renewalActionPayload.setUserId(userId);
        smsClient.markRenewalJourneyAction(renewalActionPayload);
    }

    public ReportCardPDFResponse getUserReportPdf(Map<String, String> queryParams) throws HttpException {
        String reportCardId = queryParams.get("reportCardId");
        if (reportCardId == null) {
            return null;
        }
        return smsClient.getRenewalReportCardPublicURL(Long.valueOf(reportCardId));
    }

    public boolean applyRenewalOffer(UserContext userContext) {
        try {
            this.smsClient.applyRenewalOffer(Long.valueOf(userContext.getUserProfile().getUserId()));
            return true;
        } catch (HttpException e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public SfGenericSuccessModal requestCallback(UserContext userContext, Map<String, Object> requestPayload) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String requestType = String.valueOf(requestPayload.getOrDefault("requestType", ""));
        if (requestType.equals("SF_RENEWAL_REQUEST_CALLBACK")) {
            smsClient.registerUserRenewalCallbackRequest(userId);

            SfGenericSuccessModal modal = new SfGenericSuccessModal();
            modal.setTitle("Request raised");
            modal.setSubTitle("Our team will reach out to you soon");
            modal.setInstructionTitle("WHAT TO EXPECT");

            List<SfInstruction> instructions = new ArrayList<>();
            instructions.add(new SfInstruction("image/chroniccare/instructions/call_icon.png", "Our Health Counsellor will explain you the plan, pricing and benefits."));

            modal.setInstructions(instructions);
            return modal;
        }
        return null;
    }

    public SfFlashbackReportPage getFlashbackReportPage(UserContext userContext) {
        return flashbackReportPageBuilder.buildView(userContext);
    }

    public SfFlashbackReportCongratsPage getFlashbackReportCongratsPage(UserContext userContext) {
        return flashbackCongratsPageBuilder.buildView(userContext);
    }

    public ReportCardPDFResponse getFlashbackReportPdf(Map<String, String> queryParams) throws HttpException {
        String reportCardId = queryParams.get("reportCardId");
        if (reportCardId == null) {
            return null;
        }
        return smsClient.getFlashBackCardPublicURL(Long.valueOf(reportCardId));
    }

    public SfMealActivityDetailPageView getMealActivityDetailsPage(UserContext userContext, Map<String, String> queryParams) throws Exception {
        return this.mealActivityDetailPageViewBuilder.buildView(userContext, queryParams);
    }

    public Map<String, String> getMealActivitySignedUploadURL(Map<String, String> queryParams) throws BaseException {
        String count = queryParams.getOrDefault("count", "1");
        return this.ambrosiaClient.fetchDishSignedUploadUrl(Integer.parseInt(count));
    }

    public Map<String, String> fetchCgmReaderPhotoDownloadURL(Map<String, String> queryParams) throws BaseException {
        String count = queryParams.getOrDefault("count", "1");
        return this.chsClient.fetchUserDocumentsSignedUploadURL(Integer.parseInt(count));
    }

    public List<UserReadingsDocumentsEntry> publishCgmReaderPhotos(List<UserReadingsDocumentsEntry> userReadingsDocumentsEntries) throws BaseException {
        return this.chsClient.publishDocuments(userReadingsDocumentsEntries);
    }

    public Map<String, String> fetchReadingsImgDownloadURL(Map<String, String> queryParams) {
        String filename = queryParams.getOrDefault("filename", "");
        try {
            return this.chsClient.fetchUserDocumentsSignedDownloadURL(filename);
        } catch (Exception exception) {
            exceptionReportingService.reportException(exception);
            log.error("fetchReadingsImgDownloadURL | Exception: "+exception.getMessage());
        }
        return null;
    }

    public Map<String, String> fetchMealActivityDownloadURL(Map<String, String> queryParams) {
        String filename = queryParams.getOrDefault("filename", "");
        try {
            return this.ambrosiaClient.fetchDishSignedGetUrl(filename);
        } catch (Exception exception) {
            exceptionReportingService.reportException(exception);
            log.error("fetchMealActivityDownloadURL | Exception: "+exception.getMessage());
        }
        return null;
    }

    public boolean addMealActivityToFavourite(Map<String, String> queryParams) {
        try {
            String mealLogId = queryParams.getOrDefault("mealLogId", "");
            this.sfLoggingClient.addFavouriteMeal(Long.valueOf(mealLogId));
            return true;
        } catch (Exception exception) {
            exceptionReportingService.reportException(exception);
            log.error("Meal Activity | Add to fav | Exception: "+exception.getMessage());
        }
        return false;
    }

    public boolean removeMealActivityFromFavourite(Map<String, String> queryParams) {
        try {
            String mealLogId = queryParams.getOrDefault("mealLogId", "");
            this.sfLoggingClient.unmarkFavouriteMeal(Long.valueOf(mealLogId));
            return true;
        } catch (Exception exception) {
            exceptionReportingService.reportException(exception);
            log.error("Meal Activity | Remove from fav | Exception: "+exception.getMessage());
        }
        return false;
    }

    public DishEntry getDetailedDishEntry(Map<String, String> queryParams) {
        try {
            String dishId = queryParams.getOrDefault("dishId", "");
            if(!dishId.equals("")) {
                return ambrosiaClient.fetchDish(dishId);
            }
            return null;
        } catch (Exception exception) {
            exceptionReportingService.reportException(exception);
            log.error("Meal Activity | Dish Entry Fetch | Exception: "+exception.getMessage());
        }
        return null;
    }

    public SfMealSlotResponse getMealActivitySlots(UserContext userContext, Map<String, String> queryParams) throws Exception {
        return this.mealActivitySlotsListWidgetBuilder.buildSlots(userContext, queryParams);
    }

    public boolean isImageMealLoggingDisabled(UserContext userContext) throws Exception {
        return ChronicCareAppUtil.isImageMealLoggingDisabled(userContext, chsClient);
    }

    public SfWellnessAtCenterPrePurchaseView getWellnessAtCenterPrePurchaseView(UserContext userContext) {
        return wellnessAtCenterPrePurchasePageBuilder.buildView(userContext);
    }

    public SfWellnessAtCenterCLPView getWellnessAtCenterCLP(UserContext userContext, Map<String, String> queryParams) {
        return wellnessAtCenterCLPBuilder.buildView(userContext);
    }

    public SfWellnessAtCenterPDPView getWellnessAtCenterPDP(UserContext userContext, Map<String, String> queryParams) {
        String productCode = queryParams.getOrDefault("productCode", null);
        return wellnessAtCenterPDPBuilder.buildView(userContext, productCode);
    }

    public SfWellnessCenterBookingPageView getWellnessCenterBookings(UserContext userContext, Map<String, String> queryParams) {
        int pageNumber = Integer.parseInt(queryParams.getOrDefault("pageNumber", String.valueOf(0)));
        String wellnessType = queryParams.getOrDefault("wellnessType", null);
        return wellnessCenterBookingsPageBuilder.buildView(userContext, wellnessType, pageNumber);
    }

    public WebinarJoinResponse handleWebinarJoinRequest(WebinarJoinRequest request, String userAgent) throws BaseException {
        if (Objects.isNull(request.getMeta())) {
            WebinarJoinRequestMeta webinarJoinRequestMeta = new WebinarJoinRequestMeta();
            request.setMeta(webinarJoinRequestMeta);
        }
        request.getMeta().setUserAgent(userAgent);
        return lmsClient.handleWebinarJoinRequest(request);
    }

    public Map<String, String> fetchWebinarPackPurchaseVoice(String customerName) throws BaseException {
        Map<String, String> response = new HashMap<>();
        response.put("preSignedUrl", lmsClient.fetchWebinarPackPurchaseVoice(customerName));
        return response;
    }

    public EverWebinarJoinStatusResponse fetchEverWebinarJoinStatus(String phoneNumber, String webinarCode) throws BaseException {
        return lmsClient.fetchEverWebinarJoinStatus(phoneNumber, webinarCode);
    }

    public SfMegaSalesCLP getMegaSalesCLP(UserContext userContext, Map<String, String> queryParams) {
        return sfMegaSalesCLPBuilder.buildView(userContext);
    }

    public boolean updateCGMStatusToBackend(UserContext userContext, CgmStatusUpdateRequest cgmStatusUpdateRequest) {
        try {
            CgmAction status = null;
            switch (cgmStatusUpdateRequest.getStatus()) {
                case CRASHED -> status = CgmAction.CRASHED;
                case STARTING, NOT_STARTED -> status = CgmAction.DELIVERED;
                case SHUT_DOWN -> status = CgmAction.ENDED;
            }
            CgmActionEvent cgmActionEvent = new CgmActionEvent();
            Long userId = cgmStatusUpdateRequest.getUserId() != null
                    ? Long.valueOf(cgmStatusUpdateRequest.getUserId())
                    : Long.valueOf(userContext.getUserProfile().getUserId());
            cgmActionEvent.setUserId(userId);
            cgmActionEvent.setDeviceId(cgmStatusUpdateRequest.getCgmDeviceId());
            if (status != null && cgmStatusUpdateRequest.getCgmDeviceId() != null) {
                chsClient.executeCgmDeviceAction(status, cgmActionEvent, getAppTenantFromUserContext(userContext), "APP");
                return true;
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public void processRazorpayPayoutEvents(JsonNode request, String webhookSignature) throws HttpException {
        lmsClient.handleRazorpayPayoutWebhook(request);
        if (request != null
                && request.hasNonNull("entity") && request.get("entity").asText().equals("event")
                && request.hasNonNull("event") && request.get("event").asText().equals("order.paid")) {
            processRazorpaySubscriptionEvents(request, webhookSignature);
        }
    }

    public void processRazorpaySubscriptionEvents(JsonNode requestJson, String webhookSignature) throws HttpException {
        lmsClient.handleRazorpaySubscriptionWebhook(requestJson, webhookSignature);
    }

    public void processRazorpayEvents(JsonNode requestJson, String webhookSignature) throws HttpException {
        lmsClient.handleRazorpayEvents(requestJson, webhookSignature);
    }

    public void processShopifyEvents(JsonNode requestJson, String shopifyTopic) throws HttpException {
        lmsClient.handleShopifyEvents(requestJson, shopifyTopic);
    }

    public PhlebotomistUser getPhlebotomistInfo(UserContext userContext) {
        Long userID = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            return this.serviceInterfaces.sfAlbusClient.getPhlebotomistAppUser(userID);
        } catch (Exception exec) {
            String msg = String.format("Failed to get Phlebotomist Details :: %s", userID);
            exceptionReportingService.reportException(msg, exec);
            log.error(msg, exec);
        }
        return null;
    }

    public SfPhelboAppUserInfo getPhleboAppUserInfo(UserContext userContext, Map<String, String> queryParams) {
        String phleboUserID = userContext.getUserProfile().getUserId();
        try {
            SfPhelboAppUserInfo userData = new SfPhelboAppUserInfo();
            String userID = queryParams.getOrDefault("userID", "");
            UserEntry userApiRes = this.userServiceClient.getUser(userID).get();
            UserNfcStatusResponse nfcStatusApiRes = this.serviceInterfaces.chsClient.getUserNfcStatus(Long.valueOf(userID));
            if(userApiRes != null) {
                userData.setUser(userApiRes);
            }
            userData.setUserNFCStatus(nfcStatusApiRes != null && nfcStatusApiRes.getUserNfcStatus() != null && nfcStatusApiRes.getUserNfcStatus());
            return userData;
        } catch (Exception exec) {
            String msg = String.format("Failed to get User Details for Phlebotomist User:: %s", phleboUserID);
            exceptionReportingService.reportException(msg, exec);
            log.error(msg, exec);
        }
        return null;
    }

    public boolean markPhleboAppPhleboAction(UserContext userContext, PhleboMarkActionRequest actionRequest) {
        Long userID = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            this.serviceInterfaces.sfAlbusClient.markPhleboActions(actionRequest);
            return true;
        } catch (Exception exec) {
            String msg = String.format("Failed to Update Phlebo Action :: %s", userID);
            exceptionReportingService.reportException(msg, exec);
            log.error(msg, exec);
        }
        return false;
    }

    public boolean capturePhleboAppTasksData(UserContext userContext, List<InputFieldsConfig> captureDataRequest, Map<String, String> queryParams) {
        Long userID = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            Long phleboTaskId = Long.valueOf(queryParams.getOrDefault("phleboTaskId", ""));
            this.serviceInterfaces.sfAlbusClient.captureData(captureDataRequest, phleboTaskId);
            return true;
        } catch (Exception exec) {
            String msg = String.format("Failed to Update Phlebo Tasks Captured :: %s", userID);
            exceptionReportingService.reportException(msg, exec);
            log.error(msg, exec);
        }
        return false;
    }

    public boolean updatePhleboAppInventory(UserContext userContext, List<InputFieldsConfig> inventoryRequest, Map<String, String> queryParams) {
        Long userID = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            Long phleboId = Long.valueOf(queryParams.getOrDefault("phleboId", ""));
            Boolean isStartOfDayInventoryUpdate = Boolean.valueOf(queryParams.getOrDefault("isStartTime", null));

            this.serviceInterfaces.sfAlbusClient.savePhleboInventory(inventoryRequest, phleboId, !isStartOfDayInventoryUpdate);
            return true;
        } catch (Exception exec) {
            String msg = String.format("Failed to Update Phlebo Inventory :: %s", userID);
            exceptionReportingService.reportException(msg, exec);
            log.error(msg, exec);
        }
        return false;
    }

    public boolean triggerPhleboAppPhoneCall(UserContext userContext, Map<String, String> queryParams) {
        Long phleboUserID = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            Long phleboTaskId = Long.valueOf(queryParams.getOrDefault("phleboTaskId", ""));
            String phone = queryParams.getOrDefault("phone", "");
            this.serviceInterfaces.sfAlbusClient.triggerPhleboCall(phleboTaskId, phone);
            return true;
        } catch (Exception exec) {
            String msg = String.format("Failed to Trigger Phlebo Phone Call :: %s", phleboUserID);
            exceptionReportingService.reportException(msg, exec);
            log.error(msg, exec);
        }
        return false;
    }

    public boolean sendPhleboAppPhleboLocation(UserContext userContext, PhleboLocation phleboLocation) {
        Long phleboUserID = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            this.serviceInterfaces.sfAlbusClient.addPhleboLocation(phleboLocation);
            return true;
        } catch (Exception exec) {
            String msg = String.format("Failed to Send Phlebo Location :: %s", phleboUserID);
            exceptionReportingService.reportException(msg, exec);
            log.error(msg, exec);
        }
        return false;
    }

    public PhleboTask reschedulePhleboAppTrip(UserContext userContext, Map<String, String> queryParams) {
        Long phleboUserID = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            Long phleboTaskId = Long.valueOf(queryParams.getOrDefault("phleboTaskId", ""));
            String reason = queryParams.getOrDefault("reason", "");
            return this.serviceInterfaces.sfAlbusClient.requestReschedule(phleboTaskId, reason);
        } catch (Exception exec) {
            String msg = String.format("Failed to reschedule Phlebo Trip :: %s", phleboUserID);
            exceptionReportingService.reportException(msg, exec);
            log.error(msg, exec);
        }
        return null;
    }

    public List<PhleboActions> getPhleboAppAllowedActions(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long phleboUserID = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            long phleboTaskId = Long.parseLong(queryParams.getOrDefault("phleboTaskId", "0"));
            if (phleboTaskId != 0L) {
                return this.serviceInterfaces.sfAlbusClient.getPhleboTaskAllowedActions(phleboTaskId);
            }
        } catch (Exception exec) {
            String msg = String.format("Failed to get Phlebo Allowed Actions :: %s", phleboUserID);
            exceptionReportingService.reportException(msg, exec);
            log.error(msg, exec);
        }
        return null;
    }

    public PhleboAppHomePageView getPhleboAppHomePageData(UserContext userContext, PhleboTaskSearchRequest requestPayload) throws BaseException {
        return phleboAppHomePageViewBuilder.buildView(userContext, requestPayload);
    }

    public PhleboAppInventoryPageView getPhleboAppInventoryPageData(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return phleboAppInventoryPageViewBuilder.buildView(userContext, queryParams);
    }

    public PhleboAppCaptureTaskPageView getPhleboAppCaptureTasksPageData(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return phleboAppCaptureTaskPageViewBuilder.buildView(userContext, queryParams);
    }

    public PhleboLocation getPhleboLiveLocation(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Long phleboTaskId = Long.valueOf(queryParams.getOrDefault("phleboTaskId", ""));
        return this.serviceInterfaces.sfAlbusClient.getPhleboLocation(phleboTaskId);
    }

    public UserEntry getPhleboProfile(UserContext userContext,  Map<String, String> queryParams) throws BaseException {
        try {
            Long phleboId = Long.valueOf(queryParams.getOrDefault("phleboId", ""));
            AgentResponse agentData = ollivanderAgentClient.getAgent(phleboId);
            if(agentData != null) {
                UserEntry userEntry = new UserEntry();
                userEntry.setFirstName(agentData.getName());
                userEntry.setPhone(agentData.getPhoneNumber());
                return userEntry;
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public Map<String, String> getSignedUrlPathForPhleboAppAttachment(int fileCount) {
        return this.serviceInterfaces.sfAlbusClient.fetchFileUploadPath(fileCount);
    }

    public Map<String, String> getSignedUrlValueForPhleboAppAttachment(String fileName) {
        return this.serviceInterfaces.sfAlbusClient.fetchSignedUploadURL(fileName);
    }

    public SfCgmDeviceStatus getActiveCGMDetails(UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            CgmOnboardingStatusResponse onboardingStatusResponse = chsClient.fetchOnboardingStatus(userId, null, getAppTenantFromUserContext(userContext));
            if (Objects.nonNull(onboardingStatusResponse)) {
                return chronicCareServiceHelper.getCgmDeviceStatus(userContext, onboardingStatusResponse, null);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return new SfCgmDeviceStatus();
    }

    public DefaultSuccessResponse uploadCgmReadingFile(UserContext userContext, MultipartFile file, String deviceId) throws HttpException, IOException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return chsClient.uploadAbbotReadingsSync(file, userId, deviceId, false, getAppTenantFromUserContext(userContext));
    }

    public DefaultSuccessResponse activateSugarControlPlan(UserContext userContext, String activationCode, UserEntry userEntry) throws BadRequestException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            userServiceClient.updateByUserId(userId.toString(), userEntry);
            serviceInterfaces.sfAlbusClient.redeemActivationCode(userId, activationCode);
            return new DefaultSuccessResponse(true);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
            throw new BadRequestException(String.format("Error Activating Sugar Control Plan, message :: %s", e.getMessage()));
        }
    }

    @Async
    public void saveDietPlanParameters(UserContext userContext, UserMealPlanPreference dietPlanParameters) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        this.lexiconClient.generateMealPlan(dietPlanParameters, userId);
    }

    public ResponseAsync saveDietPlanParametersV2(UserContext userContext, UserMealPlanPreference dietPlanParameters) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        return this.lexiconClient.generateMealPlanV2(dietPlanParameters, userId, userId);
    }

    public SfCgmWebinarListPage getCgmWebinarListPage(UserContext userContext) {
        SfCgmWebinarListPage result = new SfCgmWebinarListPage();
        try {
            String userId = userContext.getUserProfile().getUserId();
            com.curefit.base.enums.Tenant tenant = com.curefit.base.enums.Tenant.fromString(AppUtil.getTenantFromUserContext(userContext).toString());
            String countryId = userContext.getUserProfile().getCity().getCountryId();
            CgmOnboardingStatusResponse cgmOnboardingStatus = serviceInterfaces.getChsClient().fetchOnboardingStatus(Long.valueOf(userId), null, getAppTenantFromUserContext(userContext));
            List<DeviceModel> pendingDevices = cgmOnboardingStatus.getPendingDevices();
            boolean isTrackyCgm = Objects.nonNull(pendingDevices) && !CollectionUtils.isEmpty(pendingDevices) && pendingDevices.getFirst().equals(DeviceModel.TRACKY);
            boolean isGlucoRxCgm = Objects.nonNull(pendingDevices) && !CollectionUtils.isEmpty(pendingDevices) && pendingDevices.getFirst().equals(DeviceModel.GLUCO_RX);
            boolean isLibreCGM = Objects.nonNull(pendingDevices) && !CollectionUtils.isEmpty(pendingDevices) && pendingDevices.getFirst().equals(DeviceModel.ABBOTT_LIBRE);
            boolean isNfcUser = chronicCareServiceHelper.getUserNfcStatus(userContext);
            boolean userHasTeluguLanguagePreference = chronicCareServiceHelper.userHasTeluguLanguagePreference(userContext);
            DIYFilterRequestV2 diyFilterRequestV2 = DIYFilterRequestV2.builder().preferredStreamType(PreferredStreamType.VIDEO_CALL).build();
            List<LiveClass> liveClasses = serviceInterfaces.getDiyfsService().getUpcomingClassesByFilters(50, 0, tenant, diyFilterRequestV2, userId, countryId).get();

            List<LiveClass> cgmLiveWebinars = new ArrayList<>(liveClasses.parallelStream().filter(webinar -> {
                if (ChronicCareAppUtil.isCgmInstallationWebinar(webinar) && !ChronicCareAppUtil.isCgmInstallationTeluguWebinar(webinar)) {
                    if (ChronicCareAppUtil.isLiveClassFull(webinar, serviceInterfaces)) {
                        return false;
                    }
                    if (isTrackyCgm) {
                        return ChronicCareAppUtil.isCgmInstallationTrackyWebinar(webinar);
                    }
                    if (isGlucoRxCgm) {
                        return ChronicCareAppUtil.isCgmInstallationGlucoRxWebinar(webinar);
                    } else if (isLibreCGM) {
                        return ChronicCareAppUtil.isCgmInstallationLibreReaderWebinar(webinar);
                    }
                    return !ChronicCareAppUtil.isCgmInstallationLibreReaderWebinar(webinar);
                }
                return false;
            }).toList());

            List<LiveClass> cgmTeluguLiveWebinars = new ArrayList<>();
            if (userHasTeluguLanguagePreference) {
                cgmTeluguLiveWebinars = new ArrayList<>(liveClasses.parallelStream().filter(webinar -> {
                    if (ChronicCareAppUtil.isCgmInstallationWebinar(webinar)) {
                        if (ChronicCareAppUtil.isLiveClassFull(webinar, serviceInterfaces)) {
                            return false;
                        }
                        if (ChronicCareAppUtil.isCgmInstallationTeluguWebinar(webinar)) {
                            if (isTrackyCgm) {
                                return ChronicCareAppUtil.isCgmInstallationTrackyWebinar(webinar);
                            }
                            if (isGlucoRxCgm) {
                                return ChronicCareAppUtil.isCgmInstallationGlucoRxWebinar(webinar);
                            } else if (isLibreCGM) {
                                return ChronicCareAppUtil.isCgmInstallationLibreReaderWebinar(webinar);
                            }
                            return !ChronicCareAppUtil.isCgmInstallationLibreReaderWebinar(webinar);
                        }
                    }
                    return false;
                }).toList());
            }

            if (userHasTeluguLanguagePreference && !cgmTeluguLiveWebinars.isEmpty()) {
                cgmLiveWebinars = cgmTeluguLiveWebinars;
            }

            HashMap<String, Map<String, String>> config = appConfigCache.getConfig("SF_UPCOMING_WEBINAR_CONFIG", new TypeReference<>() {}, new HashMap<>());
            int liveWindowInMinute = Integer.parseInt(resolveNullable(() -> config.get("cgm_installation").get("live_window_in_minute")).orElse("60"));
            int joinWindowFromEnd = Integer.parseInt(resolveNullable(() -> config.get("cgm_installation").get("join_window_from_end")).orElse("30"));
            long liveWindowInMillis = (long) liveWindowInMinute * 60 * 1000;
            long joinWindowInMillis = (long) joinWindowFromEnd * 60 * 1000;

            if (!CollectionUtils.isEmpty(cgmLiveWebinars)) {
                cgmLiveWebinars.sort(Comparator.comparingLong(LiveClass::getScheduledTimeEpoch));

                cgmLiveWebinars.forEach(session -> {
                    SfCgmInstallationWebinarWidget widget = new SfCgmInstallationWebinarWidget();

                    long currentTime = Calendar.getInstance(getUserTimezone(userContext)).getTimeInMillis();
                    boolean isLive = session.getScheduledTimeEpoch() < currentTime + liveWindowInMillis;
                    boolean isBooked = session.getSlots().getFirst().getSubscriptionStatus() == SubscriptionStatus.SUBSCRIBED;

                    String actionTitle = isBooked ? "View Details" : "Book Now";
                    Action sessionAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(session.getLiveClassId(), session.getSlots().get(0).getClassId(), "now_live_widget", actionTitle, userContext, session.getContentCategory());
                    Action joinAction = LiveUtil.getLiveInteractiveSessionRestApiAction(userContext, session.getLiveClassId());
                    joinAction.setIsEnabled(true);
                    joinAction.setTitle("Join Session");

                    widget.setAction(isLive ? joinAction : sessionAction);
                    widget.setScheduleEpochTime(session.getScheduledTimeEpoch());
                    widget.setDurationEpoch(session.getDuration());
                    widget.setTitle(session.getTitle());
                    widget.setImage(LiveUtil.getImage(session.getBannerImages(), userContext.getSessionInfo(), 1L));
                    widget.setLive(isLive);
                    widget.setShowImage(isLive);
                    widget.setBooked(isBooked);
                    widget.setPageFrom("sfcgmwebinarlistpage");

                    // Card visible till few minutes before end time
                    if (currentTime <= (session.getScheduledTimeEpoch() + session.getDuration() - joinWindowInMillis)) {
                        result.addWidget(widget);
                    }
                });
            }
        } catch (Exception e) {
            String message = String.format("Error in fetching cgm installation webinars, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        return result;
    }

    public SfLiveClassListPageView getLiveClassesPage(UserContext userContext) {
        return sfLiveClassListPageBuilder.buildView(userContext);
    }

    public SfUserCGMConfig getUserCGMConfiguration(UserContext userContext, Map<String, String> queryParams) throws ExecutionException, InterruptedException, ResourceNotFoundException {
        return cgmConfigurationBuilder.buildConfig(userContext, queryParams);
    }

    public VoipInstanceStatusEntry updateVoipCallStatus(UserContext userContext, VoipInstanceStatusEntry voipStatus) throws BaseException {
        voipStatus.setParticipantType(ParticipantType.RECEIVER);
        return talktubeClient.updateStatus(voipStatus);
    }

    public VoipJoinTokenResponse getVoipJoinToken(Long voipInstanceId, ParticipantType participantType) throws BaseException {
        return talktubeClient.fetchJoinToken(voipInstanceId, participantType);
    }

    public VoipInstanceEntry endVoip(VoipEndRequest voipEndRequest) throws BaseException {
        voipEndRequest.setEndedBy(UserType.USER);
        return talktubeClient.endVoip(voipEndRequest);
    }

    public UserDeviceVoipAttributeEntry upsertUserDeviceVoipAttribute(UserDeviceVoipAttributeEntry userDeviceVoipAttributeEntry) throws BaseException {
        return talktubeClient.upsertUserDeviceVoipAttribute(userDeviceVoipAttributeEntry);
    }

    public SfECommercePLPView getSmartScalePLP(UserContext userContext) {
        return sfSmartScalePLPBuilder.buildView(userContext);
    }

    public SmartScaleOnboardingResponse getSmartScaleOnboardingData(UserContext userContext) throws BaseException {
        return smartScaleOnboardingPageBuilder.buildView(userContext);
    }

    public FBVOnboardingResponse getFbvOnboardingData(UserContext userContext) throws BaseException {
        return fbvOnboardingPageBuilder.buildView(userContext);
    }

    public SmartScaleOnboardingResponse updateSmartScaleOnboardingData(UserContext userContext,
                                                                       SmartScaleOnboardingRequest smartScaleOnboardingRequest) throws BaseException {
        String userId = userContext.getUserProfile().getUserId();
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        if (smartScaleOnboardingRequest.getValue() != null) {
            if (smartScaleOnboardingRequest.getStepType() == SmartScaleOnboardingResponse.OnboardingStepType.DOB) {
                long dobEpoch = Long.parseLong(String.valueOf(smartScaleOnboardingRequest.getValue()));
                LocalDateTime today = TimeUtil.getDateNow(DEFAULT_ZONE_ID);
                LocalDateTime dobDateTime = TimeUtil.getLocalDateTimeFromDate(new Date(dobEpoch), TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()));
                if (today.minusYears(18).isBefore(dobDateTime)) {
                    log.error("UserId {} is below 18, rejecting", userId);
                    throw new BaseException("sugar.fit subscription is valid for users above 18 years of age. Please check the date of birth entered.");
                }
            }

            NUXProfileUpdateRequest request = new NUXProfileUpdateRequest();
            List<NUXProfile> profiles = new ArrayList<>();

            if (smartScaleOnboardingRequest.getStepType() == SmartScaleOnboardingResponse.OnboardingStepType.HEIGHT) {
                Calendar calendar = Calendar.getInstance(timeZone);
                UserVitalEntry userVitalEntry = new UserVitalEntry();
                userVitalEntry.setUserId(Long.valueOf(userId));
                userVitalEntry.setStartTime(calendar.getTime());
                userVitalEntry.setValue(String.valueOf(smartScaleOnboardingRequest.getValue()));
                userVitalEntry.setSource(AppUtil.getAppTenantFromUserContext(userContext).toString());
                chsClient.saveUserHeight(userVitalEntry, timeZone);
            } else {
                if (smartScaleOnboardingRequest.getStepType() == SmartScaleOnboardingResponse.OnboardingStepType.GENDER) {
                    profiles.add(NUXProfile.GENDER);
                    request.setGender(String.valueOf(smartScaleOnboardingRequest.getValue()));
                } else if (smartScaleOnboardingRequest.getStepType() == SmartScaleOnboardingResponse.OnboardingStepType.DOB) {
                    NUXStatusResponse nuxStatusResponse = smsClient.getNUXStatus(Long.valueOf(userId), true, timeZone);
                    if (nuxStatusResponse != null && nuxStatusResponse.getFreemium()) {
                        long dobEpoch = (long) smartScaleOnboardingRequest.getValue();
                        profiles.add(NUXProfile.AGE);
                        LocalDate ld = new Date(dobEpoch).toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                        Period period = Period.between(ld, LocalDate.now());
                        request.setAge(period.getYears());
                    } else {
                        long dobEpoch = (long) smartScaleOnboardingRequest.getValue();
                        profiles.add(NUXProfile.DATE_OF_BIRTH);
                        request.setDateOfBirth(new Date(dobEpoch));
                    }
                }

                request.setProfiles(profiles);
                serviceInterfaces.getSmsClient().updateNUXData(Long.valueOf(userContext.getUserProfile().getUserId()), request, timeZone);
            }
        }

        return smartScaleOnboardingPageBuilder.buildView(userContext);
    }

    public FBVOnboardingResponse updateFbvOnboardingData(UserContext userContext,
                                                         FBVOnboardingRequest fbvOnboardingRequest) throws BaseException {
        String userId = userContext.getUserProfile().getUserId();
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        if (fbvOnboardingRequest.getValue() != null) {
            if (fbvOnboardingRequest.getStepType() == FBVOnboardingResponse.OnboardingStepType.DOB) {
                long dobEpoch = Long.parseLong(String.valueOf(fbvOnboardingRequest.getValue()));
                LocalDateTime today = TimeUtil.getDateNow(DEFAULT_ZONE_ID);
                LocalDateTime dobDateTime = TimeUtil.getLocalDateTimeFromDate(new Date(dobEpoch), TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()));
                if (today.minusYears(18).isBefore(dobDateTime)) {
                    log.error("UserId {} is below 18, rejecting", userId);
                    throw new BaseException("sugar.fit subscription is valid for users above 18 years of age. Please check the date of birth entered.");
                }
            }

            NUXProfileUpdateRequest request = new NUXProfileUpdateRequest();
            List<NUXProfile> profiles = new ArrayList<>();
            if (fbvOnboardingRequest.getStepType() == FBVOnboardingResponse.OnboardingStepType.WEIGHT) {
                Calendar calendar = Calendar.getInstance(timeZone);
                List<UserMetricEntry> userMetricEntries = new ArrayList<>();
                UserMetricEntry userMetricEntry = this.getUserMetricCHS(
                        userContext,
                        environmentService.isProduction() || environmentService.isAlpha() ? ChronicCareAppUtil.WEIGHT_METRIC_ID_PROD : ChronicCareAppUtil.WEIGHT_METRIC_ID_STAGE,
                        Double.parseDouble(String.valueOf(fbvOnboardingRequest.getValue())),
                        AppUtil.getAppTenantFromUserContext(userContext).toString(), null, null, calendar.getTime());
                userMetricEntries.add(userMetricEntry);
                chsClient.insertUserMetricGeneric(userMetricEntries);
            } else if (fbvOnboardingRequest.getStepType() == FBVOnboardingResponse.OnboardingStepType.HEIGHT) {
                Calendar calendar = Calendar.getInstance(timeZone);
                UserVitalEntry userVitalEntry = new UserVitalEntry();
                userVitalEntry.setUserId(Long.valueOf(userId));
                userVitalEntry.setStartTime(calendar.getTime());
                userVitalEntry.setValue(String.valueOf(fbvOnboardingRequest.getValue()));
                userVitalEntry.setSource(AppUtil.getAppTenantFromUserContext(userContext).toString());
                chsClient.saveUserHeight(userVitalEntry, timeZone);
            } else {
                if (fbvOnboardingRequest.getStepType() == FBVOnboardingResponse.OnboardingStepType.GENDER) {
                    profiles.add(NUXProfile.GENDER);
                    request.setGender(String.valueOf(fbvOnboardingRequest.getValue()));
                } else if (fbvOnboardingRequest.getStepType() == FBVOnboardingResponse.OnboardingStepType.DOB) {
                    NUXStatusResponse nuxStatusResponse = smsClient.getNUXStatus(Long.valueOf(userId), true, timeZone);
                    if (nuxStatusResponse != null && nuxStatusResponse.getFreemium()) {
                        long dobEpoch = (long) fbvOnboardingRequest.getValue();
                        profiles.add(NUXProfile.AGE);
                        LocalDate ld = new Date(dobEpoch).toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                        Period period = Period.between(ld, LocalDate.now());
                        request.setAge(period.getYears());
                    } else {
                        long dobEpoch = (long) fbvOnboardingRequest.getValue();
                        profiles.add(NUXProfile.DATE_OF_BIRTH);
                        request.setDateOfBirth(new Date(dobEpoch));
                    }
                }

                request.setProfiles(profiles);
                serviceInterfaces.getSmsClient().updateNUXData(Long.valueOf(userContext.getUserProfile().getUserId()), request, timeZone);
            }
        }

        return fbvOnboardingPageBuilder.buildView(userContext);
    }

    public SmartScaleVitalsListPageView getSmartScaleVitalsListPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return smartScaleVitalsListPageBuilder.buildView(userContext, queryParams);
    }

    public SmartScaleVitalsDetailsPageView getSmartScaleVitalDetailsPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return smartScaleVitalsDetailsPageBuilder.buildView(userContext, queryParams);
    }

    public SfVitalMetricDetailsPageViewV2 getVitalMetricDetailsPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return vitalMetricDetailsPageV2Builder.buildView(userContext, queryParams);
    }

    public SmartScaleVitalsDetailsTabView getSmartScaleVitalDetailTabs(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return smartScaleVitalsDetailsTabPageBuilder.buildView(userContext);
    }

    public SmartScaleVitalsTimelinePageView getSmartScaleVitalsTimelinePage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return smartScaleVitalsTimelinePageBuilder.buildView(userContext, queryParams);
    }

    public SmartScaleSettingsPageView getSmartScaleSettingsPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return smartScaleSettingsPageBuilder.buildView(userContext, queryParams);
    }
    public boolean deleteSmartScaleReading(UserContext userContext, Map<String, String> queryParams) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        long timeStamp = Long.parseLong(queryParams.getOrDefault("timeStamp", "0"));
        if (timeStamp > 0) {
            SmartScaleDeleteLogRequest request = new SmartScaleDeleteLogRequest();
            request.setUserId(userId);
            request.setTimeStamp(new Date(timeStamp));
            chsClient.deleteSmartScaleLog(request, ChronicCareAppUtil.getUserTimezone(userContext));
            return true;
        }
        return false;
    }

    public boolean pushSmartScaleDataToServer(UserContext userContext, SmartScaleDataUploadRequest request) throws HttpException {
        request.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        chsClient.uploadSmartScaleData(request);

        uploadSmartScaleDataToBackend(userContext, request);
        return true;
    }

    private void uploadSmartScaleDataToBackend(UserContext userContext, SmartScaleDataUploadRequest request) {
        try {
            if (Objects.nonNull(request.getWeight())) {
                TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
                Calendar calendar = Calendar.getInstance(timeZone);
                List<UserMetricEntry> userMetricEntries = new ArrayList<>();
                UserMetricEntry userMetricEntry = this.getUserMetricCHS(
                        userContext,
                        environmentService.isProduction() || environmentService.isAlpha() ? ChronicCareAppUtil.WEIGHT_METRIC_ID_PROD : ChronicCareAppUtil.WEIGHT_METRIC_ID_STAGE,
                        Double.parseDouble(String.valueOf(request.getWeight())),
                        AppUtil.getAppTenantFromUserContext(userContext).toString(), null, null, calendar.getTime());
                userMetricEntries.add(userMetricEntry);
                chsClient.insertUserMetricGeneric(userMetricEntries);
            }
        } catch (Exception e) {
            String message = String.format("Error uploading data to metric service :: %s", e.getMessage());
            exceptionReportingService.reportException(message, e);
        }
    }

    public PostEntry sendSFGPTPost(UserContext userContext, SmartBotPostEntry postEntry) throws BaseException {
        postEntry.setCommunityId(getGPTCommunityId());
        postEntry.setUserType(UserType.USER);
        if (Objects.nonNull(userContext.getUserProfile())) {
            postEntry.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        } else {
            postEntry.setUserId(getSfGPTDefaultUserId());
        }
        return nestClient.createSmartChatBotPost(postEntry);
    }

    public PostEntry getSFGPTResponse(UserContext userContext, Long postId) throws BaseException {
        Long userId = getSfGPTDefaultUserId();
        if (Objects.nonNull(userContext.getUserProfile())) {
            userId = Long.valueOf(userContext.getUserProfile().getUserId());
        }
        return nestClient.fetchPostDetailed(userId, postId);
    }

    public Boolean uploadGPTGlucoseReadings(UserContext userContext, CGMBGReadingsEvent cgmbgReadingsEvent) throws BaseException {
        cgmbgReadingsEvent.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
        chsClient.uploadGlucoseReadingsForGPTInsights(cgmbgReadingsEvent);
        return true;
    }

    // get insights
    public GPTGlucoseInsights fetchGPTInsights(UserContext userContext, String deviceId) throws BaseException {
        return chsClient.fetchGPTInsights(Long.valueOf(userContext.getUserProfile().getUserId()), deviceId, IST_TIMEZONE);
    }

    public SfThingsToDoPageView getThingsToDoPage(UserContext userContext, Map<String, String> queryParams) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            SfThingsToDoPageView result = new SfThingsToDoPageView();
            Long currentTime = Calendar.getInstance(getUserTimezone(userContext)).getTimeInMillis();
            Long queryDateEpoch = Long.valueOf(queryParams.getOrDefault("dateEpoch", String.valueOf(currentTime)));

            ActivePackResponse activePackResponse = userOnboardingService.getSugarFitActivePackForHome(String.valueOf(userId)).orElse(null);
            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            ChronicCareTeam assignedCareTeam = chronicCareServiceHelper.getAssignedCareTeam(userContext, patientDetail.getId(), Objects.nonNull(activePackResponse) ? activePackResponse.getBundleProduct() : null);
            UserTodoDaySummary userTodoDaySummary = smsClient.getUserTodoDaySummary(userId, queryDateEpoch);
            CgmOnboardingStatusResponse cgmOnboardingStatusResponse = chsClient.fetchOnboardingStatus(userId, null, getAppTenantFromUserContext(userContext));
            FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse = chsClient.getScansForDay(userId, getUserTimezone(userContext));

            List<UserTodoResponse> allTasks = new ArrayList<>();
            if (!CollectionUtils.isEmpty(userTodoDaySummary.getPendingTodoList())) {
                allTasks.addAll(userTodoDaySummary.getPendingTodoList());
            }
            if (!CollectionUtils.isEmpty(userTodoDaySummary.getCompletedTodoList())) {
                allTasks.addAll(userTodoDaySummary.getCompletedTodoList());
            }

            if (!CollectionUtils.isEmpty(userTodoDaySummary.getLoggingTodoList())) {
                result.addCard(thingsToDoCardsBuilder.getLoggingItemCard(userTodoDaySummary.getLoggingTodoList(), userContext, userTodoDaySummary.getIsLoggingCompleted()));
            }

            allTasks.forEach(pendingTask -> {
                switch (pendingTask.getUserTodoTaskType()) {
                    case BOOK_COACH_CONSULT -> result.addCard(thingsToDoCardsBuilder.getBookCoachConsultCard(pendingTask, assignedCareTeam, activePackResponse));
                    case BOOK_DOCTOR_CONSULT -> result.addCard(thingsToDoCardsBuilder.getBookDoctorConsultCard(pendingTask, userContext, assignedCareTeam, activePackResponse));
                    case PSYCHOLOGIST_CONSULT -> result.addCard(thingsToDoCardsBuilder.getBookPsychologistConsultCard(pendingTask, activePackResponse));
                    case BOOK_DIAGNOSTICS -> result.addCard(thingsToDoCardsBuilder.getBookDiagnosticsCard(userContext, pendingTask, activePackResponse, patientDetail));
                    case FBV_SCAN -> result.addCard(thingsToDoCardsBuilder.getFaceScanCard(pendingTask, userContext, cgmOnboardingStatusResponse, activePackResponse, faceBasedVitalScansForDayResponse));
                    case CELEBRATE_YOUR_COACH -> result.addCard(thingsToDoCardsBuilder.getCoachCelebrationCard(pendingTask, assignedCareTeam));
                    case JOIN_CHALLENGE -> result.addCard(thingsToDoCardsBuilder.getJoinChallengeCard(userContext, pendingTask));
                    case VIEW_DIAGNOSTIC_REPORT -> result.addCard(thingsToDoCardsBuilder.getDiagnosticReportCard(pendingTask));
                    case FITNESS_DEVICE_SYNC -> result.addCard(thingsToDoCardsBuilder.getFitnessSyncCard(userContext, pendingTask));
                    case OFFLINE_ACCESS -> result.addCard(thingsToDoCardsBuilder.getOfflineAccessCard(pendingTask));
                    case SAVE_COACH_NUMBER -> result.addCard(thingsToDoCardsBuilder.getSaveCoachContactCard(userContext, pendingTask));
                    case INTERVENTIONS -> result.addCard(thingsToDoCardsBuilder.getInterventionCard(userContext, pendingTask, false));
                    case LIVE_CLASS -> result.addCard(thingsToDoCardsBuilder.getInterventionCard(userContext, pendingTask, true));
                }
            });

            List<ThingsToDoCard> allCards = result.getCards();
            boolean isToday = isToday(queryDateEpoch, userContext.getUserProfile().getTimezone());
            if (!isToday && !CollectionUtils.isEmpty(allCards)) {
                allCards.forEach(card -> {
                    if (!card.getUserTodoTaskType().equals(UserTodoTaskType.INTERVENTIONS)
                            && !card.getUserTodoTaskType().equals(UserTodoTaskType.FOOD_LOGGING)) {
                        card.setAction(null);
                    }
                });
            }

            return result;
        } catch (Exception e) {
            String errorMessage = String.format("Error in fetching things to do page for user :: %s", userId);
            log.error(errorMessage, e);
            exceptionReportingService.reportException(errorMessage, e);
            return null;
        }
    }
    public boolean submitNPS(UserContext userContext, Map<String, String> queryParams) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            int rating = Integer.parseInt(queryParams.getOrDefault("rating", "-1"));
            String npsSubmitDate = queryParams.getOrDefault("npsSubmitDate", null);
            if (rating > -1) {
                VoteEntry voteEntry = new VoteEntry();
                voteEntry.setUserId(userId);
                voteEntry.setRating(rating);
                this.pollSupportClient.voteOnQuestionV2(userId, NPS_POLL_ID, NPS_POLL_QUESTION_ID, voteEntry);
                this.pollSupportClient.completePoll(userId, NPS_POLL_ID);
            }
            if(npsSubmitDate!=null){
                AppTenant appTenant = getAppTenantFromUserContext(userContext);
                UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
                userAttributeEntry.setUserId(userId);
                userAttributeEntry.setAppTenant(appTenant);
                userAttributeEntry.setAttribute("nps_submit_date");
                userAttributeEntry.setAttrValue(npsSubmitDate);
                userAttributeEntry.setDataType(DataType.STRING);
                this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
            }
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }
    public boolean dismissNPS(UserContext userContext, String npsDismissDate) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
            userAttributeEntry.setUserId(userId);
            userAttributeEntry.setAppTenant(appTenant);
            userAttributeEntry.setAttribute("nps_dismiss_date");
            userAttributeEntry.setAttrValue(npsDismissDate);
            userAttributeEntry.setDataType(DataType.STRING);
            this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }
    public SfEcommerceProductReviewsPage getProductReviewsPage(UserContext userContext, Map<String, String> queryParams) {
        String productId = queryParams.getOrDefault("productId", null);
        int pageNumber = Integer.parseInt(queryParams.getOrDefault("pageNumber", String.valueOf(0)));
        return ecommerceProductReviewsPageBuilder.buildView(userContext, productId, pageNumber);
    }
    public SfStoreLPView getStoreLandingPage(UserContext userContext) {
        return sfStoreLPBuilder.buildView(userContext);
    }
    public SfStoreCategoryPageView getStoreCategoriesPage(UserContext userContext,Map<String, String> queryParams) {
        String categoryId = queryParams.getOrDefault("categoryId", "");
        return sfStoreCategoryPageBuilder.buildView(userContext, categoryId);
    }
    public SfStoreLifestylePageView getStoreLifestylesPage(UserContext userContext,Map<String, String> queryParams) {
        String lifestyleId = queryParams.getOrDefault("lifestyleId", "");
        return sfStoreLifestylePageBuilder.buildView(userContext, lifestyleId);
    }

    public SfStoreLifestylePageView getStoreLifestylesPageV2(UserContext userContext, ProductSearchRequest productSearchRequest) {
        return sfStoreLifestylePageBuilder.buildView(userContext, productSearchRequest);
    }
    public boolean updateStoreOrderAddress(UserContext userContext, Map<String, String> requestBody ) {
        try {
            Long orderId = Long.valueOf(requestBody.getOrDefault("orderId", "0"));
            String addressId = requestBody.getOrDefault("addressId", "");
            indusClient.updateOrderAddress(orderId, addressId);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }
    public boolean cancelStoreOrder(UserContext userContext,Map<String, String> queryParams) {
        try {
            Long orderId = Long.valueOf(queryParams.getOrDefault("orderId", "0"));
            indusClient.cancelUserOrder(orderId);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }
    public SfStoreSearchPageView getStoreSearchPage(UserContext userContext,Map<String, String> queryParams) {
        String query = queryParams.getOrDefault("query", "");
        return sfStoreSearchPageBuilder.buildView(userContext, query);
    }
    public List<OfferResponse> getUserCoupons(UserContext userContext, OrderCart orderCart) {
        try {
            return indusClient.getOffers(Long.valueOf(userContext.getUserProfile().getUserId()), orderCart);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }
    public boolean setSearchOpened(UserContext userContext, Map<String, String> queryParams) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            String productCode = queryParams.getOrDefault("productCode", "");
            indusClient.addProductSearch(userId, productCode);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public ActivePackResponse getActivePackOnUser(UserContext userContext) {
        try {
            Optional<ActivePackResponse> activePackResponseOptional = this.userOnboardingService.getSugarFitActivePack(userContext.getUserProfile().getUserId());
            if (activePackResponseOptional.isPresent()) {
                ActivePackResponse actualPack = activePackResponseOptional.get();
                ActivePackResponse limitedActivePack = new ActivePackResponse();
                limitedActivePack.setUserId(actualPack.getUserId());
                limitedActivePack.setProductCode(actualPack.getProductCode());
                if (Objects.nonNull(actualPack.getBundleProduct())) {
                    BundleProduct bundleProduct = new BundleProduct();
                    bundleProduct.setIsTrialProduct(actualPack.getBundleProduct().getIsTrialProduct());
                    limitedActivePack.setBundleProduct(bundleProduct);
                }
                return limitedActivePack;
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        ActivePackResponse defaultResponse = new ActivePackResponse();
        defaultResponse.setProductCode("");
        return defaultResponse;
    }
    public RPOrderCreateResponse createSubscriptionOrder(RPOrderCreateRequest rpOrderCreateRequest) throws HttpException {
        return this.lmsClient.createRazorPaySubscriptionOrder(rpOrderCreateRequest);
    }
    public Map<String, Boolean> confirmSubscriptionOrder(RPOrderStatusUpdateRequest rpOrderStatusUpdateRequest) throws HttpException {
        this.lmsClient.updateOrderStatus(rpOrderStatusUpdateRequest);
        Map<String, Boolean> obj = new HashMap<>();
        obj.put("success", true);
        return obj;
    }
    public List<GlucometerReadingEntry> bulkSaveGlucometerReadings(UserContext userContext, List<GlucometerReadingEntry> requests) throws HttpException {
        requests.forEach(request -> request.setUserId(Long.valueOf(userContext.getUserProfile().getUserId())));
        return this.chsClient.saveBulkGlucometerReadings(requests, ChronicCareAppUtil.getUserTimezone(userContext));
    }

    public List<GlucometerReadingEntry> bulkSaveRawGlucometerReadings(UserContext userContext, GlucometerRawDataRequest request) throws HttpException {
        List<GlucometerReadingEntry> insertRequest = new ArrayList<>();

        long userId = Long.parseLong(userContext.getUserProfile().getUserId());
        GlucometerReadingFilterRequest glucometerReadingFilterRequest = new GlucometerReadingFilterRequest();

        Long startEpoch = 0L;
        glucometerReadingFilterRequest.setUserId(userId);
        glucometerReadingFilterRequest.setStartTime(startEpoch);
        glucometerReadingFilterRequest.setEndTime(System.currentTimeMillis());
        glucometerReadingFilterRequest.setSortOrder(Sort.Direction.DESC);
        glucometerReadingFilterRequest.setSlot(null);
        glucometerReadingFilterRequest.setTargetRangeTypes(null);

        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
        List<GlucometerReadingEntry> entries = chsClient.filterGlucometerReadings(glucometerReadingFilterRequest, appTenant, timeZone);

        long lastSyncedTimeEpoch;
        if(entries != null && !entries.isEmpty()) {
            GlucometerReadingEntry lastGlucometerEntry = entries.stream().filter(entry -> entry.getDeviceId() != null).findFirst().orElse(null);
            if(lastGlucometerEntry != null) {
                lastSyncedTimeEpoch = lastGlucometerEntry.getReadingTime().getTime();
            } else {
                lastSyncedTimeEpoch = 0L;
            }
        } else {
            lastSyncedTimeEpoch = 0L;
        }

        request.getRawData().forEach(data -> {
            // Create ZonedDateTime in UTC
            ZonedDateTime gmtDateTime = ZonedDateTime.of(
                    data.get(3) + 1792, data.get(5), data.get(6), data.get(7), data.get(8), data.get(9), 0,
                    ZoneOffset.UTC
            );

            // Convert to GMT+5:30
            ZonedDateTime gmt530DateTime = gmtDateTime.minusHours(5).minusMinutes(30);

            if (gmt530DateTime.toInstant().toEpochMilli() > lastSyncedTimeEpoch && gmt530DateTime.toInstant().toEpochMilli() <= Instant.now().toEpochMilli()) {
                GlucometerReadingEntry entry = new GlucometerReadingEntry();
                entry.setComments("");
                entry.setUserId(userId);
                entry.setReadingTime(Date.from(gmt530DateTime.toInstant()));
                entry.setSlot("");
                entry.setSource(request.getSource());
                entry.setValue(data.get(12).doubleValue());
                entry.setIsUnSlotted(true);
                entry.setDeviceId(request.getDeviceId());

                insertRequest.add(entry);
            }
        });


        return this.chsClient.saveBulkGlucometerReadings(insertRequest, ChronicCareAppUtil.getUserTimezone(userContext));
    }

    public SmartGlucometerDashboardPageView getSmartGlucometerDashboardPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return smartGlucometerDashboardPageBuilder.buildView(userContext, queryParams);
    }

    public SmartScaleSettingsPageView getSmartGlucometerSettingsPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return smartGlucometerSettingsPageBuilder.buildView(userContext, queryParams);
    }

    public SmartGlucometerOnboardingPageView getSmartGlucometerOnboardingPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return smartGlucometerOnboardingPageBuilder.buildView(userContext, queryParams);
    }

    public SmartGlucometerTimelinePageView getSmartGlucometerTimelinePage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return smartGlucometerTimelinePageBuilder.buildView(userContext, queryParams);
    }

    public SfStoreProductAddReviewPage getStoreProductAddReviewPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        String productId = queryParams.getOrDefault("productId", null);
        String orderIdStr = queryParams.getOrDefault("orderId", "");
        long orderId = 0L;
        if (!orderIdStr.isEmpty()) {
            orderId = Long.parseLong(orderIdStr);
        }
        return sfStoreProductAddReviewPageBuilder.buildView(userContext, productId, orderId);
    }

    public boolean addStoreProductReview(UserContext userContext, ReviewRequest reviewRequest) {
        try {
            reviewRequest.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            indusClient.postProductReview(reviewRequest);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public Object getSignedUrlForStoreProductReview(int fileCount) throws BaseException {
        return indusClient.fetchSignedUploadUrl(fileCount);
    }


    public Boolean markCgmDelivery(UserContext userContext, Map<String, String> requestBody) {
        try {
            Long orderId = Long.valueOf(requestBody.getOrDefault("orderId", "0"));
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            this.serviceInterfaces.getSfAlbusClient().markDeviceDelivered(userId, orderId, calendar.getTime());
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public Boolean markCgmInstallationViaReader(UserContext userContext) throws HttpException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            chsClient.activateCgmDeviceNonNfcUser(userId, appTenant, String.valueOf(userId));
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public boolean saveUserRenewalCancelReason(UserContext userContext, String cancellationReason) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
            userAttributeEntry.setUserId(userId);
            userAttributeEntry.setAppTenant(appTenant);
            userAttributeEntry.setAttribute("renewal_cancellation_reason");
            userAttributeEntry.setAttrValue(cancellationReason);
            userAttributeEntry.setDataType(DataType.STRING);
            this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public boolean saveDietPlanShowTime(UserContext userContext, Long time) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
            userAttributeEntry.setUserId(userId);
            userAttributeEntry.setAppTenant(appTenant);
            userAttributeEntry.setAttribute(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE);
            userAttributeEntry.setAttrValue(time);
            userAttributeEntry.setDataType(DataType.NUMBER);
            this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public boolean saveDietPlanSeen(UserContext userContext) throws BaseException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
            userAttributeEntry.setUserId(userId);
            userAttributeEntry.setAppTenant(appTenant);
            userAttributeEntry.setAttribute(DIGITAL_DIET_PLAN_SEEN_ATTRIBUTE);
            userAttributeEntry.setAttrValue(true);
            userAttributeEntry.setDataType(DataType.BOOLEAN);
            this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public Boolean addCGMToAccount(UserContext userContext) throws HttpException {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            serviceInterfaces.getSfAlbusClient().createDeviceOrder(userId);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public SfWellnessLiveSessionsWidgetView updateFitnessLevelForLiveClasses(UserContext userContext, Map<String, String> requestBody) {
        try {
            Level fitnessLevel = Level.valueOf(requestBody.getOrDefault("fitnessLevel", "NO_LEVEL"));
            Long startOfWeek = Long.valueOf(requestBody.getOrDefault("startOfWeek", "0"));
            Long endOfWeek = Long.valueOf(requestBody.getOrDefault("endOfWeek", "0"));

            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            this.planClient.updateFitnessLevel(userId, fitnessLevel);
            SfWellnessLiveSessionsWidgetView sfWellnessLiveSessionsWidgetView = new SfWellnessLiveSessionsWidgetView();
            sfWellnessLiveSessionsWidgetView.buildView(serviceInterfaces, userContext, startOfWeek, endOfWeek);
            return sfWellnessLiveSessionsWidgetView;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public SfWellnessLiveSessionsWidgetView getWellnessLiveSessionsWidget(UserContext userContext, Map<String, String> requestBody) {
        try {
            Long startOfWeek = Long.valueOf(requestBody.getOrDefault("startOfWeek", "0"));
            Long endOfWeek = Long.valueOf(requestBody.getOrDefault("endOfWeek", "0"));

            SfWellnessLiveSessionsWidgetView sfWellnessLiveSessionsWidgetView = new SfWellnessLiveSessionsWidgetView();
            sfWellnessLiveSessionsWidgetView.buildView(serviceInterfaces, userContext, startOfWeek, endOfWeek);
            return sfWellnessLiveSessionsWidgetView;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public boolean pushRashiEvent(UserContext userContext, SfRashiEventBody eventBody) {
        try {
            JSONObject body = new JSONObject(eventBody.getAttributes());
            UserEventEntry userEventEntry = new UserEventEntry(Long.parseLong(userContext.getUserProfile().getUserId()), UserEventType.USER_ACTIVITY_EVENT, eventBody.getEventType(), new Date(), body, AppUtil.getAppTenantFromUserContext(userContext));
            String requestId = servletRequest.getHeader("x-request-id");
            PublishResult result = this.rashiClient.publishUserEvent(userEventEntry, AppUtil.getAppTenantFromUserContext(userContext), requestId);
            log.debug("pushRashiEvent " + result.toString());
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public List<InfoBitesResponse> getChatStories(UserContext userContext) throws HttpException {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            if(isSfChatStoriesEnabledUser(userContext)){
                ArrayList<String> categoryIds = appConfigCache.getConfig("SF_CHAT_STORIES_CATEGORY_IDS", new TypeReference<>() {
                }, new ArrayList<>());
                InfoBitesFilterRequest req = new InfoBitesFilterRequest();
                req.setCategoryIds(categoryIds);
                return catalogClient.filterInfoBites(userId, req);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error while fetching chat stories", e);
        }
        return null;
    }
    
    public SfDatesAvailableWidget getAgentSlots(Long agentId, String productCode, Long centerId) {
        ConsultationInventoryAvailabilityRequestParams inventoryRequest = new ConsultationInventoryAvailabilityRequestParams();
        if (Objects.nonNull(centerId)) {
            inventoryRequest.setCenterId(centerId);
        }
        inventoryRequest.setDoctorId(agentId);
        inventoryRequest.setBookingStartTimeEpoch((new Date()).toInstant().toEpochMilli());
        inventoryRequest.setDaysToGo(6L);
        inventoryRequest.setParentBookingId(-1L);
        inventoryRequest.setProductCode(productCode);
        inventoryRequest.setExcludePreferredDoctor(false);
        inventoryRequest.setStrictGenderCheck(false);
        inventoryRequest.setPatientId(null);
        ConsultationInventoryInfo inventoryInfo = this.serviceInterfaces.sfAlbusClient.getConsultationAvailableSlots(inventoryRequest, 94059277L);

        return sfConsultationSlotBookingPageViewBuilder.buildView(inventoryInfo.getFormattedAvailableSlotResponseList());
    }

    public SfExpCenterAreaResponse getSfExpCenterAreaResponse(String city) throws OllivanderClientException {
        CenterSearchRequestParam searchRequest = new CenterSearchRequestParam();
        searchRequest.setSubServiceTypeCode("SUGARFIT_EXPERIENCE_CENTER");
        if (city != null) {
            searchRequest.setCityCode(city);
        }
        List<CenterResponseV2> centerList = ollivanderCenterClient.searchCentersV3(searchRequest);
        List<Long> centerIds = centerList.stream().map(CenterResponseV2::getId).toList();
        Map<String, List<Long>> areaCenterMap = centerList.stream().collect(
                Collectors.groupingBy(
                        center -> center.getAddress().getArea(),
                        Collectors.mapping(CenterResponseV2::getId, Collectors.toList())
                ));
        return new SfExpCenterAreaResponse(centerIds, areaCenterMap);
    }

    public SfExpCenterCityResponse getSfExpCenterCityResponse() throws OllivanderClientException {
        CenterSearchRequestParam searchRequest = new CenterSearchRequestParam();
        searchRequest.setSubServiceTypeCode("SUGARFIT_EXPERIENCE_CENTER");
        List<CenterResponseV2> centerList = ollivanderCenterClient.searchCentersV3(searchRequest);
        List<String> areaList = areaList = centerList.stream().map(center -> center.getAddress().getArea()).collect(Collectors.toList());
        Map<String, List<String>> cityAreaMap = centerList.stream().collect(
                Collectors.groupingBy(
                        center -> center.getAddress().getCityCode(),
                        Collectors.mapping(center -> center.getAddress().getArea(), Collectors.toList())
                ));
        return new SfExpCenterCityResponse(areaList, cityAreaMap, "Bangalore");
    }

    public Optional<ConsultationProductListingResponse> getAgentConsultPriceResponse(Long agentId, String productCode, Long centerId) {
        try {
            List<ConsultationProductListingResponse> listings = this.serviceInterfaces.getSfAlbusClient().getConsultationListings(agentId);
            return listings.stream().filter(consultationListing -> Objects.equals(consultationListing.getProductCode(), productCode) && Objects.equals(consultationListing.getCenterId(), centerId)).findFirst();
        } catch(Exception e) {
            String msg = String.format("Failed to get agent price, agent :; %s, productCode: %s center :: %s", agentId, productCode, centerId);
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
        }
        return Optional.empty();
    }

    public List<PublicAgentResponse> agentPublicResponseByCenter(Long centerId) {
        try {
            AgentSearchDetails agentSearchDetails = new AgentSearchDetails();
            agentSearchDetails.setDoctorTypeCodesCsv("GP");
            agentSearchDetails.setCenterId(centerId);

            List<BasicDoctorResponse> doctorResponse = this.serviceInterfaces.getSfAlbusClient().getAgentDetailsWithAvailability(agentSearchDetails);
            List<PublicAgentResponse> publicAgentResponses = new ArrayList<>();
            List<CenterResponseV2> center = List.of(ollivanderCenterClient.getCenterV2(centerId));
            for (BasicDoctorResponse basicDoctorResponse: doctorResponse) {
                PublicAgentResponse publicAgentResponse = objectMapper.convertValue(basicDoctorResponse, PublicAgentResponse.class);
                Optional<ConsultationProductListingResponse> listingResponse = getAgentConsultPriceResponse(publicAgentResponse.getId(), publicAgentResponse.getProductCode(), centerId);
                if (listingResponse.isPresent()) {
                    publicAgentResponse.setPrice(listingResponse.get().getListingPrice());
                    publicAgentResponse.setListingCode(listingResponse.get().getListingCode());
                }
                else {
                    publicAgentResponse.setPrice(defaultConsultPrice);
                }
                publicAgentResponse.setCenters(center);
                publicAgentResponses.add(publicAgentResponse);
            }
            return publicAgentResponses;
        } catch (Exception e) {
            String msg = String.format("Failed to get agent public response for center :: %s", centerId);
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
        }
        return new ArrayList<>();
    }

    public List<PublicAgentResponse> agentPublicResponse(List<Long> centerIds, Map<String, String> queryParams) throws ExecutionException, InterruptedException, OllivanderClientException {
        Double latitude = null;
        Double longitude  = null;
        try {
            longitude = queryParams.containsKey("longitude") ? Double.parseDouble(queryParams.get("longitude")) : null;
            latitude = queryParams.containsKey("latitude") ? Double.parseDouble(queryParams.get("latitude")) : null;
        } catch (Exception e) {
            log.error("Cannot parse received latitude and longitude, queryParams: {}, ignoring lat-long for inputs", queryParams);
        }

        List<PublicAgentResponse> publicAgentResponses = new ArrayList<>();

        if (CollectionUtils.isEmpty(centerIds)) {
            centerIds = getSfExpCenterAreaResponse(null).getCenterIds();
        }
        Map<Long, CompletableFuture<List<PublicAgentResponse>>> centerToAgentsMap = centerIds.stream().collect(
                Collectors.toMap(Function.identity(), centerId -> CompletableFuture.supplyAsync(() -> agentPublicResponseByCenter(centerId))));

        for (Long centerId: centerIds) {
            publicAgentResponses.addAll(centerToAgentsMap.get(centerId).get());
        }

        Double finalLatitude = latitude;
        Double finalLongitude = longitude;
        publicAgentResponses.sort((agent1, agent2) -> {
            if (finalLatitude != null && finalLongitude != null) {
                Double d1 = ChronicCareAppUtil.calculateDistanceInMiles(finalLatitude, finalLongitude, agent1.getCenters().get(0).getAddress().getLatitude().doubleValue(), agent1.getCenters().get(0).getAddress().getLongitude().doubleValue());
                Double d2 = ChronicCareAppUtil.calculateDistanceInMiles(finalLatitude, finalLongitude, agent2.getCenters().get(0).getAddress().getLatitude().doubleValue(), agent2.getCenters().get(0).getAddress().getLongitude().doubleValue());
                int response = d1.compareTo(d2);
                if (response != 0) {
                    return response;
                }
            }
            if (agent1.getEarliestAvailability() != null && agent2.getEarliestAvailability() != null
                    && agent1.getEarliestAvailability().getStartTime() != null && agent2.getEarliestAvailability().getStartTime() != null) {
                return agent1.getEarliestAvailability().getStartTime().compareTo(agent2.getEarliestAvailability().getStartTime());
            }
            if (agent1.getEarliestAvailability() != null && agent1.getEarliestAvailability().getStartTime() != null) {
                return -1;
            }
            if (agent2.getEarliestAvailability() != null && agent2.getEarliestAvailability().getStartTime() != null) {
                return 1;
            }
            return agent1.getId().compareTo(agent2.getId());
        });

        return publicAgentResponses;
    }

    public Map<String, String> askGPTV2(SugarGptDTO sugarGptDTO) throws HttpException {
        return this.lexiconClient.chatV3(sugarGptDTO);
    }

    public SugarGPTResponse getGPTV2(Long userId, Long sessionId, String prevRunId) throws HttpException {
        return this.lexiconClient.getLatestChatForSession(userId, sessionId, prevRunId);
    }

    public ChatResponse askGPT(ChatRequest request) throws HttpException {
        return this.lexiconClient.askGPT(request);
    }


    public List<ChatResponse> getAllChats(String sessionId) throws HttpException {
        return this.lexiconClient.getAllChats(sessionId, null);
    }

    public SessionChatResponse getAllChatsV2(UserContext userContext, String sessionId, String page ) throws HttpException {
        long userId  = Long.parseLong(userContext.getUserProfile().getUserId());
        return this.lexiconClient.getSessionChats(userId, Long.parseLong(sessionId), Long.parseLong(page), 30L );
    }

    public boolean saveRagusChatFeedback(UserContext userContext, ChatFeedbackDTO chatFeedback) throws HttpException {
        chatFeedback.setReviewerId(userContext.getUserProfile().getUserId());
        this.lexiconClient.saveChatFeedbackV2(chatFeedback);
        return true;
    }

    public ChatResponse extractCGM(CGMRequest request) throws HttpException {
        return this.lexiconClient.extractCGMReport(request);
    }

    public String extractCGMInsightsV2(CGMRequest request) throws HttpException {
        return this.lexiconClient.extractCGMInsightsV2(request);
    }

    public ResponseAsync extractGPTCGMInsightsV3(CGMRequest request) throws HttpException {
        return this.lexiconClient.agentCGMReportAsync(request);
    }

    public ChatResponse extractDiagnostic(DiagnosticsReportRequest request) throws HttpException {
        return this.lexiconClient.extractDiagnosticReport(request);
    }

    public ChatResponse extractDiagnosticV2(DiagnosticsReportRequest request) throws HttpException {
        String response = this.lexiconClient.extractDiagnosticReportV2(request);
        ChatResponse chatResponse = new ChatResponse();
        chatResponse.setId(response);
        return chatResponse;
    }

    public ResponseAsync extractGPTDiagnosticV3(DiagnosticsReportRequest request) throws HttpException {
        return this.lexiconClient.agentDiagnosticReportAsync(request);
    }

    public ChatResponse getMessage(String id, UserContext userContext) throws HttpException {
        try {
            ChatResponse chatResponse = this.lexiconClient.getMessage(id, userContext.getUserProfile().getUserId());
            if (chatResponse == null) {
                chatResponse = new ChatResponse();
            }
            chatResponse.setBody(chatResponse.getResponse());
            return chatResponse;
        } catch (Exception e) {
            return new ChatResponse();
        }
    }

    public DiagnosticReportResponse getGPTDiagnosticReport(UserContext userContext, String id, String fileUrl) throws HttpException {
        try {
            Long sessionId = Long.valueOf(userContext.getUserProfile().getUserId());
            return this.lexiconClient.diagnosticPolling(Long.valueOf(userContext.getUserProfile().getUserId()), sessionId, id, fileUrl);
        } catch (Exception e) {
            return new DiagnosticReportResponse();
        }
    }

    public CGMReportResponse getGPTCGMReport(UserContext userContext, String id, String fileUrl) throws HttpException {
        try {
            Long sessionId = Long.valueOf(userContext.getUserProfile().getUserId());
            return this.lexiconClient.cgmReportPolling(Long.valueOf(userContext.getUserProfile().getUserId()), sessionId, id, fileUrl);
        } catch (Exception e) {
            return new CGMReportResponse();
        }
    }

    public SugarGPTResponse getGPTMealPlanResponse(UserContext userContext, String id) throws HttpException {
        try {
            Long sessionId = Long.valueOf(userContext.getUserProfile().getUserId());
            return this.lexiconClient.getMealPlanResponse(Long.valueOf(userContext.getUserProfile().getUserId()), sessionId, id);
        } catch (Exception e) {
            return null;
        }
    }

    public SignedURLResponse getGPTSignedURL(SignedGetURLRequest request) throws HttpException {
        return this.lexiconClient.getSignedGetURL(request);
    }

    public SignedPostURLResponse getGPTSignedPostURL(SignedPostURLRequest request) throws HttpException {
        return this.lexiconClient.getSignedPostURL(request);
    }

    public DigiTasksToDoPageView getDigiTasksToDoPage(UserContext userContext, Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        Long currentTime = Calendar.getInstance(getUserTimezone(userContext)).getTimeInMillis();
        Long queryDateEpoch = Long.valueOf(queryParams.getOrDefault("dateEpoch", String.valueOf(currentTime)));
        CompletableFuture<ActivePackResponse> activePackResponseFuture = userOnboardingService.getSugarFitActivePackResponseFuture(userContext.getUserProfile().getUserId());
        ActivePackResponse activePackResponse = activePackResponseFuture.get();
        boolean isTrialPacExpired = DigitalAppUtil.isTrialPackExpired(userContext, activePackResponse);
        return digiTasksToDoPageBuilder.buildView(userContext, queryDateEpoch, isTrialPacExpired);
    }

    public DATasksJourneyPageView getDATaskJourneyPage(UserContext userContext, Map<String, String> queryParams) throws BaseException, ExecutionException, InterruptedException {
        Long currentTime = Calendar.getInstance(getUserTimezone(userContext)).getTimeInMillis();
        Long queryDateEpoch = Long.valueOf(queryParams.getOrDefault("dateEpoch", String.valueOf(currentTime)));
        CompletableFuture<ActivePackResponse> activePackResponseFuture = userOnboardingService.getSugarFitActivePackResponseFuture(userContext.getUserProfile().getUserId());
        ActivePackResponse activePackResponse = activePackResponseFuture.get();
        boolean isTrialPacExpired = DigitalAppUtil.isTrialPackExpired(userContext, activePackResponse);
        return daTasksJourneyPageViewBuilder.buildView(userContext, queryDateEpoch, isTrialPacExpired);
    }

    public DALessonsJourneyPageView getDALessonsJourneyPage(UserContext userContext) throws BaseException {
        return daLessonsJourneyPageViewBuilder.buildView(userContext);
    }

    public DADietPlanJourneyPageView getDADietPlanJourneyPage(UserContext userContext) throws BaseException {
            return daDietPlanJourneyPageViewBuilder.buildView(userContext);
    }

    public UserTaskResponseEntry updateDigiTasksToDoActivity(UserContext userContext, UserTaskResponseEntry req) throws Exception {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        req.setUserId(userId);
        return serviceInterfaces.getSmsClient().logUserTaskResponse(req);
    }

    public UserSessionLogEntry logLiveSessionJoin(UserContext userContext, String sessionId) throws Exception {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        UserSessionLogRequest sessionLogRequest = new UserSessionLogRequest();
        sessionLogRequest.setUserId(userId);
        sessionLogRequest.setSessionId(Long.valueOf(sessionId));
        sessionLogRequest.setEventTime(Calendar.getInstance(getUserTimezone(userContext)).getTime());
        sessionLogRequest.setEventType(com.sugarfit.fitness.enums.UserEventType.JOINED_SESSION);
        return sfFitnessClient.userSessions(sessionLogRequest);
    }

    public DigiBookPageView getDigiBookPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        Integer bookId = null;
        if (queryParams.containsKey("bookId")) {
            bookId = Integer.valueOf(queryParams.get("bookId"));
        }
        return digiBookPageBuilder.buildView(userContext, bookId);
    }

    public boolean markDigiChapterCompleted(UserContext userContext, UserChapterTrackingRequest requestBody) throws BaseException {
        try {
            requestBody.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            catalogClient.markChapterCompleted(requestBody, appTenant, timeZone);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public boolean markDigiBookCompleted(UserContext userContext, UserChapterTrackingRequest requestBody) throws BaseException {
        try {
            requestBody.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            requestBody.setIsVideoWatched(true);
            requestBody.setPageId(1);
            requestBody.setChapterId(1);
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            catalogClient.markChapterCompleted(requestBody, appTenant, timeZone);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public boolean markDigiPageViewed(UserContext userContext, UserChapterTrackingRequest requestBody) throws BaseException {
        try {
            requestBody.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            catalogClient.updateViewedPage(requestBody, appTenant, timeZone);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public boolean saveUserInteractionOnDigiPage(UserContext userContext, UserInteractionEntry requestBody) throws BaseException {
        try {
            requestBody.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            catalogClient.createUserInteraction(requestBody, appTenant, timeZone);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public UserInteractionEntry getUserInteractionOnDigiPage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        try {
            Integer bookId = null;
            Integer chapterId = null;
            Integer pageId = null;
            if (queryParams.containsKey("bookId")) {
                bookId = Integer.valueOf(queryParams.get("bookId"));
            }
            if (queryParams.containsKey("chapterId")) {
                chapterId = Integer.valueOf(queryParams.get("chapterId"));
            }
            if (queryParams.containsKey("pageId")) {
                pageId = Integer.valueOf(queryParams.get("pageId"));
            }
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            return catalogClient.fetchUserInteractionForUserIdAndBookIdAndChapterIdAndPageId(userId, bookId, chapterId, pageId, appTenant, timeZone);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public DAProgressPageView getDAProgressPageView(UserContext userContext) throws BaseException {
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        return daProgressPageViewBuilder.buildView(userContext, timeZone);
    }

    public DigiPrePurchasePageView getDigiPrePurchasePage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        boolean paymentDisabled = Boolean.parseBoolean(queryParams.getOrDefault("paymentDisabled", "false"));
        return digiPrePurchasePageBuilder.buildView(userContext, paymentDisabled);
    }

    public DigiScorePageView getDigiScorePage(UserContext userContext, Map<String, String> queryParams) throws BaseException {
        return digiScorePageBuilder.buildView(userContext);
    }

    public RagusAiConfig getRagusAiConfig(UserContext userContext) throws BaseException {
        try {
            RagusAiConfig ragusAiConfig = new RagusAiConfig();
            if (ChronicCareAppUtil.isInternationalSugarfitUser(userContext)) {
                ragusAiConfig.setDisableQuickLinks(true);
            }
            return ragusAiConfig;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public boolean digitalFTUESeenByUser(UserContext userContext) throws BaseException {
        try {
            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            AppTenant appTenant = getAppTenantFromUserContext(userContext);
            UserAttributeEntry userAttributeEntry = new UserAttributeEntry();
            userAttributeEntry.setUserId(userId);
            userAttributeEntry.setAppTenant(appTenant);
            userAttributeEntry.setAttribute(DIGITAL_FTUE_SEEN_ATTRIBUTE);
            userAttributeEntry.setAttrValue(calendar.getTime().getTime());
            userAttributeEntry.setDataType(DataType.NUMBER);
            this.userAttributesClient.createOrUpdateAttribute(userAttributeEntry, appTenant);
            return true;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public SfUserDietPreferenceMetrics getUserDietPreferenceMetrics(UserContext userContext) {
        try {
            SfUserDietPreferenceMetrics dietPreferenceMetrics = new SfUserDietPreferenceMetrics();
            UserEntry user = userContext.getUserEntryCompletableFuture().join();
            if (Objects.nonNull(user) && Objects.nonNull(user.getBirthday())) {
                LocalDate birthLocalDate = user.getBirthday().toInstant().atZone(ChronicCareAppUtil.getUserTimezone(userContext).toZoneId()).toLocalDate();
                LocalDate today = LocalDate.now();
                int age = Period.between(birthLocalDate, today).getYears();
                dietPreferenceMetrics.setAge(age);
            }
            if (Objects.nonNull(user) && Objects.nonNull(user.getGender())) {
                dietPreferenceMetrics.setGender(user.getGender());
            }
            Double latestHeight = chronicCareServiceHelper.getLatestMetricValue(userContext.getUserProfile().getUserId(), (environmentService.isProduction() || environmentService.isAlpha() ? ChronicCareAppUtil.HEIGHT_METRIC_ID_PROD : ChronicCareAppUtil.HEIGHT_METRIC_ID_STAGE));
            if (Objects.nonNull(latestHeight)) {
                dietPreferenceMetrics.setHeightInCms(latestHeight);
            }
            Double latestWeight = chronicCareServiceHelper.getLatestMetricValue(userContext.getUserProfile().getUserId(), (environmentService.isProduction() || environmentService.isAlpha() ? ChronicCareAppUtil.WEIGHT_METRIC_ID_PROD : ChronicCareAppUtil.WEIGHT_METRIC_ID_STAGE));
            if (Objects.nonNull(latestWeight)) {
                dietPreferenceMetrics.setWeightInKgs(latestWeight);
            }

            SfUserDietPreferenceMetrics.PreferenceFormSection cuisineTypeSection = new SfUserDietPreferenceMetrics.PreferenceFormSection();
            cuisineTypeSection.setType("CUISINE_TYPE");
            cuisineTypeSection.setTitle("What cuisines do you prefer?");
            cuisineTypeSection.setMeta(chronicCareServiceHelper.getStaticCuisineTypes());
            try {
                UserPreferencePojo cuisines = serviceInterfaces.getSfAlbusClient().getUserPreference(
                        userContext.getUserProfile().getUserId(),
                        UserPreferencePojo.PreferenceType.META,
                        "cuisines");
                if (cuisines != null && cuisines.getPreferenceTypeValues().size() > 0) {
                    cuisineTypeSection.setDefaultValue(cuisines.getPreferenceTypeValues());
                }
            } catch (Exception ignoreException) {
            }
            dietPreferenceMetrics.setCuisinePreferenceSection(cuisineTypeSection);
            return dietPreferenceMetrics;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public List<SfUserDietPlanPreferenceSteps> getUserDietPreferenceSteps(UserContext userContext) {
        try {
            List<SfUserDietPlanPreferenceSteps> steps = new ArrayList<>();

            UserEntry user = userContext.getUserEntryCompletableFuture().join();

            // PROFILE STEP
            SfUserDietPlanPreferenceSteps profileStep = new SfUserDietPlanPreferenceSteps();
            profileStep.setStepName("PROFILE");

            List<SfUserDietPlanPreferenceSteps.Question> profileQuestions = new ArrayList<>();

            // AGE
            Integer age = null;
            if (user != null && user.getBirthday() != null) {
                LocalDate birthLocalDate = user.getBirthday().toInstant().atZone(ChronicCareAppUtil.getUserTimezone(userContext).toZoneId()).toLocalDate();
                LocalDate today = LocalDate.now();
                age = Period.between(birthLocalDate, today).getYears();
            }
            profileQuestions.add(new SfUserDietPlanPreferenceSteps.Question("age", "Age", "input", "years", age, null, null));

            // HEIGHT
            Double latestHeight = chronicCareServiceHelper.getLatestMetricValue(
                    userContext.getUserProfile().getUserId(),
                    (environmentService.isProduction() || environmentService.isAlpha()
                            ? ChronicCareAppUtil.HEIGHT_METRIC_ID_PROD
                            : ChronicCareAppUtil.HEIGHT_METRIC_ID_STAGE)
            );
            profileQuestions.add(new SfUserDietPlanPreferenceSteps.Question("height", "Height", "input", "cms", latestHeight, null, null));

            // WEIGHT
            Double latestWeight = chronicCareServiceHelper.getLatestMetricValue(
                    userContext.getUserProfile().getUserId(),
                    (environmentService.isProduction() || environmentService.isAlpha()
                            ? ChronicCareAppUtil.WEIGHT_METRIC_ID_PROD
                            : ChronicCareAppUtil.WEIGHT_METRIC_ID_STAGE)
            );
            profileQuestions.add(new SfUserDietPlanPreferenceSteps.Question("weight", "Weight", "input", "kgs", latestWeight, null, null));

            profileStep.setQuestions(profileQuestions);
            steps.add(profileStep);

            // PREFERENCES STEP
            SfUserDietPlanPreferenceSteps preferenceStep = new SfUserDietPlanPreferenceSteps();
            preferenceStep.setStepName("PREFERENCES");

            List<SfUserDietPlanPreferenceSteps.Question> preferenceQuestions = new ArrayList<>();

            List<NUXCuisine> cuisineMeta = chronicCareServiceHelper.getDynamicCuisineTypes();

            List<String> defaultCuisines = new ArrayList<>();
            try {
                UserPreferencePojo cuisines = serviceInterfaces.getSfAlbusClient().getUserPreference(
                        userContext.getUserProfile().getUserId(),
                        UserPreferencePojo.PreferenceType.META,
                        "cuisines"
                );
                if (cuisines != null && !cuisines.getPreferenceTypeValues().isEmpty()) {
                    defaultCuisines = cuisines.getPreferenceTypeValues();
                }
            } catch (Exception ignore) {}

            preferenceQuestions.add(new SfUserDietPlanPreferenceSteps.Question("cuisine", "What cuisines do you like?", "multiple_choice", null, defaultCuisines, null, cuisineMeta));
            preferenceQuestions.add(new SfUserDietPlanPreferenceSteps.Question("diet", "What type of diet your are looking for?", "multiple_choice", null, null, Arrays.asList("Veg", "Non-Veg", "Egg"), null));

            preferenceStep.setQuestions(preferenceQuestions);
            steps.add(preferenceStep);

            // DISLIKE / ALLERGENS STEP
            SfUserDietPlanPreferenceSteps dislikeAllergenStep = new SfUserDietPlanPreferenceSteps();
            dislikeAllergenStep.setStepName("DISLIKE & ALLERGENS");

            List<SfUserDietPlanPreferenceSteps.Question> dislikeAllergenQuestions = new ArrayList<>();
            dislikeAllergenQuestions.add(new SfUserDietPlanPreferenceSteps.Question("dislikes", "Select items you DISLIKE 👎🏼", "multiple_choice", null, null,
                    Arrays.asList("Bitter gourd", "Brinjal", "Fish", "Milk", "Mushrooms", "Prawns"), null));
            dislikeAllergenQuestions.add(new SfUserDietPlanPreferenceSteps.Question("allergens", "Select your known ALLERGIES 👎🏼", "multiple_choice", null, null,
                    Arrays.asList("Dairy", "Gluten", "Peanuts", "Red Meat"), null));

            dislikeAllergenStep.setQuestions(dislikeAllergenQuestions);
            steps.add(dislikeAllergenStep);

            return steps;

        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }

        return Collections.emptyList();
    }


    public List<ProductResponse> getCoachSellableCGMs() {
        try {
            ProductSearchRequest productSearchRequest = new ProductSearchRequest();
            productSearchRequest.setSearchTags(List.of("COACH_SELLABLE_CGMS"));
            productSearchRequest.setRequireOnlyUserFacing(false);
            return indusClient.filterProductsResponses(productSearchRequest);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return Collections.emptyList();
    }

    public SfDynamicDeeplinkPage getDynamicDeeplinkAction(UserContext userContext, Map<String, String> queryParams) {
        try {
            SfDynamicDeeplinkPage.DeeplinkType deeplinkType = queryParams.containsKey("type") ?
                    SfDynamicDeeplinkPage.DeeplinkType.valueOf(queryParams.getOrDefault("type", "")): null;
            return sfDynamicDeeplinkPageBuilder.buildView(userContext, deeplinkType);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }
}
