package com.curefit.cfapi.service.autoLaunchService.autoLaunchTriggers;

import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.FeatureStateCache;
import com.curefit.cfapi.constants.AppDeeplink;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.autoLaunch.AutoLaunchAction;
import com.curefit.cfapi.pojo.feedback.Feedback;
import com.curefit.cfapi.repository.AutoLaunchPageConfigRepository;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.autoLaunchService.AutoLaunchBaseService;
import com.curefit.cfapi.service.autoLaunchService.AutoLaunchEvaluator;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.UserStreakUtil;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.uas.responses.StreakResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

import static com.curefit.cfapi.service.UserStreakService.RASHI_KEY_FOR_USER_STREAK_ONBOARDING;

@Service
@Slf4j
public class StreakPageAutoLaunch extends AutoLaunchBaseService implements AutoLaunchEvaluator {
    ServiceInterfaces serviceInterfaces;

    @Autowired
    public StreakPageAutoLaunch(AutoLaunchPageConfigRepository autoLaunchPageConfigRepository, ServiceInterfaces serviceInterfaces, FeatureStateCache featureStateCache) throws ExecutionException, InterruptedException {
        super("UserStreakAutoLaunch", autoLaunchPageConfigRepository, serviceInterfaces, featureStateCache);
        this.serviceInterfaces = serviceInterfaces;
    }

    @Override
    public Optional<AutoLaunchAction> evaluate(UserContext userContext, Feedback feedback) throws ExecutionException, InterruptedException {
        super.evaluate(userContext, feedback);
        if (!super.getAutoLaunchConfig().isEnabled()) {
            return Optional.empty();
        }
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            
            UserAttributesResponse userAttributesResponse = serviceInterfaces.userAttributesCacheClient.getAttributes(userId,
                    List.of(UserStreakUtil.USER_STREAK_RASHI_FIRST_ACTIVITY_DATE_KEY),
                    AppTenant.CUREFIT);
            
            
            Object firstActivityDateObj = userAttributesResponse.getAttributes().get(UserStreakUtil.USER_STREAK_RASHI_FIRST_ACTIVITY_DATE_KEY);
            if (firstActivityDateObj != null) {
                String firstActivityDate = firstActivityDateObj.toString();
                String res = this.serviceInterfaces.featureStateCache.get(userContext.getUserProfile().getUserId(), AppUtil.USER_STREAK_AUTOLAUNCH).get();
                if (res == null || !res.equals(firstActivityDate)) {
                    StreakResponse streakResponse = (StreakResponse) userContext.getRequestCache().getMexRequestFuture(RequestType.USER_STREAK_DETAILS, userContext).get();
                    if (streakResponse != null) {
                        Integer streakCount = streakResponse.getCurrentStreakCount();
                        AutoLaunchResult autoLaunchResult = getAutoLaunchResult(userContext);
                        log.info("StreakPageAutoLaunch has been triggered for User {} for first activity date {} streakCount={} with deepLink={}", userId, firstActivityDate, streakCount, autoLaunchResult.getFlutterAction().getUrl());
                        return Optional.of(autoLaunchResult);
                    }
                    return Optional.empty();
                }
            }
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService
                    .reportException("Unable to trigger user streak auto launch", e);
        }
        return Optional.empty();
    }
    
    private @NotNull AutoLaunchResult getAutoLaunchResult(UserContext userContext) {
        String deepLink = AppDeeplink.USER_STREAK_MAIN_PAGE.getDeeplinkString();

        Action action = new Action(deepLink, "",ActionType.NAVIGATION);
        return new AutoLaunchResult(action, super.getPriority(), userContext, super.getPageId());
    }
    
    
    class AutoLaunchResult extends AutoLaunchAction {

        UserContext userContext;
        public AutoLaunchResult(Action action, Integer priority, UserContext userContext, String pageId) {
            super(action, priority, pageId);
            this.userContext = userContext;
        }
        
        @Override
        public void onExecute() {
            try {
                Long userId = Long.valueOf(this.userContext.getUserProfile().getUserId());
                
                UserAttributesResponse userAttributesResponse = serviceInterfaces.userAttributesCacheClient.getAttributes(userId,
                        List.of(UserStreakUtil.USER_STREAK_RASHI_FIRST_ACTIVITY_DATE_KEY),
                        AppTenant.CUREFIT);
                
                Object firstActivityDateObj = userAttributesResponse.getAttributes().get(UserStreakUtil.USER_STREAK_RASHI_FIRST_ACTIVITY_DATE_KEY);
                
                if (firstActivityDateObj != null) {
                    String firstActivityDate = firstActivityDateObj.toString();
                    log.info("StreakPageAutoLaunch has been executed for User {} for first activity date {}", userId, firstActivityDate);
                    
                    serviceInterfaces.featureStateCache.set(userContext.getUserProfile().getUserId(), AppUtil.USER_STREAK_AUTOLAUNCH, firstActivityDate);
                }
                UserStreakUtil.setUserOnboarded(userContext, serviceInterfaces);
            } catch (Exception e) {
                serviceInterfaces.exceptionReportingService
                        .reportException("Unable to execute user streak auto launch", e);
            }
        }
    }
}
