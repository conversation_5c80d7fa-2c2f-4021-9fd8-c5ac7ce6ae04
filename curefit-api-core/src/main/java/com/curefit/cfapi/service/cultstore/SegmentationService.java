package com.curefit.cfapi.service.cultstore;

import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.pojo.cultstore.CultstoreRashiEventBody;
import com.curefit.cfapi.pojo.cultstore.UserSegmentResponse;
import com.curefit.cfapi.pojo.cultstore.UserSegmentsRequest;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.enums.UserEventType;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.segmentation.pojo.SegmentUserListEntry;
import com.curefit.userservice.client.UserServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class SegmentationService {
    private final String LOG_TAG = SegmentationService.class.getName();
    private final UserSegmentClient userSegmentClient;
    private final SegmentationCacheClient segmentationCacheClient;
    private final UserServiceClient userServiceClient;
    private final RollbarService rollbarService;
    private final ISegmentNameIdMap segmentNameIdMap;
    private final RashiClient rashiClient;

    public SegmentationService(UserSegmentClient userSegmentClient, SegmentationCacheClient segmentationCacheClient, UserServiceClient userServiceClient,
                               RollbarService rollbarService, AppConfigSegmentNameIdMap appConfigSegmentNameIdMap, RashiClient rashiClient) {
        this.userSegmentClient = userSegmentClient;
        this.segmentationCacheClient = segmentationCacheClient;
        this.userServiceClient = userServiceClient;
        this.rollbarService = rollbarService;
        this.segmentNameIdMap = appConfigSegmentNameIdMap;
        this.rashiClient = rashiClient;
    }

    public UserSegmentResponse getMatchingSegmentsForUser(UserSegmentsRequest userSegmentsRequest) {
        Long userId = userSegmentsRequest.getUserId();
        List<Long> segmentIds = userSegmentsRequest.getSegmentIds();
        UserSegmentResponse userSegmentResponse = new UserSegmentResponse();
        checkIfCultUserId(userId);

        try {
            SegmentSet<String> segmentNames = segmentationCacheClient.getUserSegments(userId.toString());
            List<Long> activeSegmentIds = segmentNameIdMap.getActiveSegmentIDs(segmentNames, segmentIds);
            userSegmentResponse.setUserId(userId);
            userSegmentResponse.setActiveSegmentIds(activeSegmentIds);
            return userSegmentResponse;
        } catch (Exception e) {
            String errorMessage = String.format("%s Error retrieving matching segments for user %d: %s", LOG_TAG, userId, e.getMessage());
            log.error(errorMessage);
            rollbarService.error(e, errorMessage);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error retrieving matching segments for user: " + userId);
        }
    }

    public void addUserToSegment(Long userId, Long segmentId, String appTenant) {
        try {
            SegmentUserListEntry segmentUserListEntry = getSegmentUserListEntry(userId, segmentId);
            userSegmentClient.addUsersToSegment(segmentUserListEntry, AppTenant.fromString(appTenant));
        } catch (Exception e) {
            String errorMessage = String.format("%s Error adding user %d: from segment %d ::%s", LOG_TAG, userId, segmentId, e.getMessage());
            log.error(errorMessage);
            rollbarService.error(e, errorMessage);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to add user to segment");
        }
    }

    public void removeUserFromSegment(Long userId, Long segmentId, String appTenant) {
        try {
            SegmentUserListEntry segmentUserListEntry = getSegmentUserListEntry(userId, segmentId);
            userSegmentClient.removeUsersFromSegment(segmentUserListEntry,AppTenant.fromString(appTenant));
        } catch (Exception e) {
            String errorMessage = String.format("%s Error removing user %d from segment %d: %s", LOG_TAG, userId, segmentId, e.getMessage());
            log.error(errorMessage);
            rollbarService.error(e, errorMessage);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to remove user from segment");
        }
    }

    private void checkIfCultUserId(Long userId){
        // Check if the user is a Cult user
        try{
            this.userServiceClient.getUser(userId.toString()).get();
        } catch (Exception e) {
            log.error("Error checking if user {} is a Cult user, Check client log if user is present", userId, e);
            rollbarService.error(e, "Error checking if user is a Cult user: " + userId + "Check client log if user is present");
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error checking if user is a Cult user" + "Check client log if user is present");
        }
    }

    private SegmentUserListEntry getSegmentUserListEntry(Long userId, Long segmentId) {
        SegmentUserListEntry segmentUserListEntry = new SegmentUserListEntry();
        segmentUserListEntry.setUserId(userId);
        segmentUserListEntry.setSegmentId(segmentId);
        return segmentUserListEntry;
    }

    public void sendRashiEventForSegment(@NotNull Long userId, CultstoreRashiEventBody cultstoreRashiEventBody) {
        checkIfCultUserId(userId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("category", cultstoreRashiEventBody.getCategory());

        try {
            log.info("{}, Sending Rashi event for userId {}. Event: {}", LOG_TAG, userId, jsonObject);
            this.rashiClient.publishUserEvent(UserEventType.USER_ACTIVITY_EVENT, cultstoreRashiEventBody.getEventName(), new Date(),
                    userId.toString(), jsonObject, AppTenant.fromString(cultstoreRashiEventBody.getAppTenant()), UUID.randomUUID().toString());
            log.info("{}, Rashi event sent for userId {}", LOG_TAG, userId);
        } catch (Exception e) {
            String errorMessage = String.format("%s Error sending Rashi event for user %d: %s", LOG_TAG, userId, e.getMessage());
            log.error(errorMessage);
            rollbarService.error(e, errorMessage);
        }
    }
}