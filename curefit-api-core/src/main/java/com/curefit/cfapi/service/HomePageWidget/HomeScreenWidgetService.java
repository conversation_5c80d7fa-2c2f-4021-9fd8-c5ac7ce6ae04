package com.curefit.cfapi.service.HomePageWidget;


import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.Analytics.HomeScreenWidgetEvent;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGame;
import com.curefit.cfapi.service.CFAnalytics.CFAnalytics;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.widgets.base.HomeScreenBaseWidget;
import com.curefit.cfapi.widgets.fitness.HabitGameProgressWidget;
import com.curefit.cfapi.widgets.homescreenwidgets.HSDailyStreakWidget;
import com.curefit.cfapi.widgets.homescreenwidgets.HSLogoutView;
import com.curefit.cfapi.widgets.homescreenwidgets.HSNinjaWidget;
import com.curefit.cfapi.widgets.homescreenwidgets.HSUserActivityWidget;
import com.curefit.rashi.enums.UserEventType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class HomeScreenWidgetService {
    ServiceInterfaces serviceInterfaces;
    private static final String HOME_SCREEN_WIDGET_ADDED_EPOCH = "home_screen_widget_added_epoch";
    private static final String HOME_SCREEN_WIDGET_REMOVED_EPOCH = "home_screen_widget_removed_epoch";

    public HomeScreenBaseWidget getHomeScreenWidget(UserContext userContext){
        try {
            WidgetContext widgetContext = new WidgetContext();
            List<HomeScreenBaseWidget> widgetResponse = null;
            HomeScreenBaseWidget widget = null;

            if(!userContext.getSessionInfo().getIsUserLoggedIn()){
                widgetResponse = new HSLogoutView().buildView(serviceInterfaces, userContext, widgetContext);
                if(widgetResponse != null && widgetResponse.getFirst() != null){
                    widget = widgetResponse.getFirst();
                }
                return widget;
            }

            if (userContext.getRequestCache().getMexRequestFuture(RequestType.USER_STREAK_DETAILS, userContext).get() != null){
                widgetResponse = new HSDailyStreakWidget().buildView(serviceInterfaces, userContext, widgetContext);
                if(Objects.isNull(widgetResponse)) return null;
            } else {
                if (userContext.getSessionInfo().getAppVersion() >= 10.98f) {
                    EnergyStreakGame userGame = serviceInterfaces.userService.getUsersGame(userContext, null, serviceInterfaces);
                    if (userGame != null) {
                        HSNinjaWidget hsNinjaWidget = new HSNinjaWidget();
                        Map<String,String> queryParams = new HashMap<>();
                        if (userGame.getIsMultiPlayer() != null) queryParams.put("isMultiplayerGame",userGame.getIsMultiPlayer().toString());
                        widgetContext.setQueryParams(queryParams);
                        widgetResponse = hsNinjaWidget.buildView(serviceInterfaces, userContext, widgetContext);
                    }
                }
            }

            if(widgetResponse == null || widgetResponse.getFirst() == null){
                widgetResponse = new HSUserActivityWidget().buildView(serviceInterfaces, userContext, widgetContext);
            }

            if(widgetResponse != null && widgetResponse.getFirst() != null){
                widget = widgetResponse.getFirst();
                serviceInterfaces.cfAnalytics.sendEvent(
                        HomeScreenWidgetEvent.builder()
                                .type(widget.getHomeScreenWidgetType())
                                .platform(userContext.getSessionInfo().getOsName())
                                .build(),
                        userContext, true, true, false, false);
            }
            return widget;
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
            return null;
        }
    }

    public Boolean updateHomeWidgetStatus(UserContext userContext, Boolean status) {
        try {
            JSONObject body = new JSONObject();
            if (Boolean.TRUE.equals(status)) {
                body.put(HOME_SCREEN_WIDGET_ADDED_EPOCH, System.currentTimeMillis());
            } else {
                body.put(HOME_SCREEN_WIDGET_REMOVED_EPOCH, System.currentTimeMillis());
            }

            serviceInterfaces.rashiClient
                    .publishUserEvent(
                            UserEventType.USER_PROFILE_EVENT,
                            "USER_HOME_WIDGET_STATUS",
                            new Date(), userContext.getUserProfile().getUserId(),
                            body,
                            AppTenant.CUREFIT,
                            UUID.randomUUID().toString()
                    );

            return status;
        } catch (Exception e) {
            log.error("Error in sending home screen widget event", e);
            serviceInterfaces.exceptionReportingService.reportException(e);
            return null;
        }
    }


}
