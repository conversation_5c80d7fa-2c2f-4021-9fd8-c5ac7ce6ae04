package com.curefit.cfapi.service;

import com.amazonaws.services.sns.model.PublishResult;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.enums.Tenant;
import com.curefit.cfapi.model.internal.exception.UnauthorizedException;
import com.curefit.cfapi.model.internal.userinfo.SessionData;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.mongo.Device;
import com.curefit.cfapi.pojo.device.ActiveDevice;
import com.curefit.cfapi.pojo.device.DeviceInfo;
import com.curefit.cfapi.pojo.status.DetectedCity;
import com.curefit.cfapi.repository.DeviceRepository;
import com.curefit.cfapi.repository.MongoDbDeviceRepositoryOperations;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.DeviceUtil;
import com.curefit.common.data.exception.BaseException;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.enums.UserEventType;
import com.curefit.rashi.pojo.UserEventEntry;
import com.curefit.userservice.client.UserServiceClient;
import com.curefit.userservice.pojo.entry.DeviceDetailEntry;
import com.curefit.userservice.pojo.misc.LatLong;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class DeviceService {

    private final AsyncListenableTaskExecutor taskExecutor;
    private DeviceRepository deviceRepository;
    private MongoDbDeviceRepositoryOperations deviceRepositoryOperations;
    private RashiClient rashiClient;
    private UserServiceClient userServiceClient;
    private final ExceptionReportingService exceptionReportingService;
    private final HttpServletRequest request;
    private SessionBusiness sessionBusiness;

    @Autowired
    public DeviceService(
        MongoDbDeviceRepositoryOperations deviceRepositoryOperations, DeviceRepository deviceRepository,
        RashiClient rashiClient, @Qualifier("cfApiTaskExecutor") final AsyncListenableTaskExecutor taskExecutor,
        UserServiceClient userServiceClient, ExceptionReportingService exceptionReportingService,
        HttpServletRequest request,
        SessionBusiness sessionBusiness
        ) {
        this.deviceRepository = deviceRepository;
        this.deviceRepositoryOperations = deviceRepositoryOperations;
        this.rashiClient = rashiClient;
        this.taskExecutor = taskExecutor;
        this.userServiceClient = userServiceClient;
        this.exceptionReportingService = exceptionReportingService;
        this.request = request;
        this.sessionBusiness = sessionBusiness;
    }

    @Async
    public CompletableFuture<DeviceDetailEntry> getDeviceByDeviceId(String deviceId, AppTenant tenant) {
        try {
            DeviceDetailEntry deviceDetailEntry = this.userServiceClient.getDeviceByIdAndTenant(deviceId, tenant.toString());
            if(deviceDetailEntry == null) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.completedFuture(deviceDetailEntry);
        } catch (Exception e) {
            log.error("Failed to get Device by DeviceId and Tenant");
            this.exceptionReportingService.reportException("Failed to get Device by DeviceId and Tenant ",e);
            return null;
        }
    }

    @Async
    public CompletableFuture<Device> getRecentAppOrBrowserLoggedInDevice(String userId) {
        try {
            DeviceDetailEntry deviceDetailEntry = this.userServiceClient.getRecentAppOrBrowserLoggedInDevice(userId);
            if(deviceDetailEntry == null) {
                return CompletableFuture.completedFuture(null);
            }
            Device device = DeviceUtil.constructFromDeviceDetailEntry(deviceDetailEntry);
            return CompletableFuture.completedFuture(device);
        } catch (Exception e) {
            log.error("Failed to getRecentAppOrBrowserLoggedInDevice by userId");
            this.exceptionReportingService.reportException("Failed to get getRecentAppOrBrowserLoggedInDevice by userId",e);
            return null;
        }
    }

    @Async
    public CompletableFuture<Device> getRecentAppOrBrowserLoggedInDevice(String userId, Tenant tenant) {
        try {
            DeviceDetailEntry deviceDetailEntry = this.userServiceClient.getRecentAppOrBrowserLoggedInDevice(userId, tenant.toString());
            if(deviceDetailEntry == null) {
                return CompletableFuture.completedFuture(null);
            }
            Device device = DeviceUtil.constructFromDeviceDetailEntry(deviceDetailEntry);
            return CompletableFuture.completedFuture(device);
        } catch (Exception e) {
            log.error("Failed to getRecentAppOrBrowserLoggedInDevice by userId and tenant");
            this.exceptionReportingService.reportException("Failed to get getRecentAppOrBrowserLoggedInDevice by userId and tenant",e);
            return null;
        }
    }

    @Async
    public CompletableFuture<Device> createNewDeviceCfApi(String deviceId, DeviceInfo deviceInfo, String at) {
        Device device = new Device();
        device.setDeviceId(deviceId);
        device.setUserId(deviceInfo.getUserId());
        device.setActiveDevice(deviceInfo);
        device.setIsLoggedIn(true);
        device.setAt(at);
        return CompletableFuture.completedFuture(this.deviceRepository.insert(device));
    }

    @Async
    public CompletableFuture<DeviceDetailEntry> createNewDevicePlatform(String deviceId, DeviceInfo deviceInfo, String at) {
        DeviceDetailEntry device = new DeviceDetailEntry();
        device.setDeviceId(deviceId);
        device.setUserId(deviceInfo.getUserId());
        device.setIsLoggedIn(true);
        device.setAt(at);
        
        DeviceUtil.populateFromDeviceInfo(deviceInfo, device);
        return CompletableFuture.completedFuture(this.userServiceClient.upsertDevice(device));
    }

    @Async
    public CompletableFuture<Device> updateDevice(Device existingDevice) {
        try {
            Device device = this.deviceRepository.findFirstByDeviceIdAndTenant(existingDevice.getDeviceId(), existingDevice.getActiveDevice().getTenant()).get();
            device = DeviceUtil.copyValuesFrom(existingDevice, device);
            return CompletableFuture.completedFuture(this.deviceRepository.save(device));
        } catch (Exception e) {
            log.error("Failed to get update Device");
            this.exceptionReportingService.reportException("Failed to update Device ",e);
            return null;
        }
    }

    public Device updateDeviceAndDeviceInfoCfApi(Device device, DeviceInfo deviceInfo) {
        Device deviceRead = new Device();
        try {
            deviceRead = this.deviceRepository.findFirstByDeviceIdAndTenant(device.getDeviceId(), device.getActiveDevice().getTenant()).get();
        } catch (Exception e) {
            log.info("Error reading device from mongodb",e);
        }
        device.setOldDevices(deviceRead != null && deviceRead.getOldDevices() != null ? deviceRead.getOldDevices() : new ArrayList<>());
        device.getOldDevices().add(device.getActiveDevice());
        if (Objects.equals(deviceInfo.getUserId(), device.getActiveDevice().getUserId())) {
            DeviceInfo activeDevice = device.getActiveDevice();
            deviceInfo.setGateId(activeDevice.getGateId());
            deviceInfo.setLocationName(activeDevice.getLocationName());
            deviceInfo.setLocationAddress(activeDevice.getLocationAddress());
            deviceInfo.setCultCenterId(activeDevice.getCultCenterId());
            deviceInfo.setMindCenterId(activeDevice.getMindCenterId());
        }
        device.setActiveDevice(deviceInfo);
        device.setUserId(deviceInfo.getUserId());
        device.setIsLoggedIn(true);
        return device;
    }

    public DeviceDetailEntry updateDeviceAndDeviceInfoPlatform(DeviceDetailEntry deviceDetailEntry, DeviceInfo deviceInfo) {
        if (Objects.equals(deviceInfo.getUserId(), deviceDetailEntry.getUserId())) {
            deviceInfo.setGateId(deviceDetailEntry.getGateId());
            deviceInfo.setLocationName(deviceDetailEntry.getLocationName());
            deviceInfo.setLocationAddress(deviceDetailEntry.getLocationAddress());
            deviceInfo.setCultCenterId(deviceDetailEntry.getCultCenterId());
            deviceInfo.setMindCenterId(deviceDetailEntry.getMindCenterId());
        }
        DeviceUtil.populateFromDeviceInfo(deviceInfo, deviceDetailEntry);
        return deviceDetailEntry;
    }

    @Async
    public CompletableFuture<Boolean> updateSessionInformationPlatform(String deviceId, SessionData sessionData, Float appVersion, String codePushVersion, Float latitude, Float longitude, String advertiserId, AppTenant tenant, String timezone, UserContext userContext, String ip, DetectedCity detectedCity) throws BaseException {
        DeviceDetailEntry device = this.userServiceClient.getDeviceByIdAndTenant(deviceId, tenant.toString());
        if (device == null || StringUtils.isBlank(device.getDeviceId())) {
            log.error("updateSessionInformationPlatform for the deviceId {} failed, device: {}, tenant: {}", deviceId, device, tenant);
            return CompletableFuture.completedFuture(false);
        }
        device.setGateId(sessionData.getGateId());
        device.setLocationName(sessionData.getLocationName());
        device.setLocationAddress(sessionData.getLocationAddress());
        device.setCultCenterId(sessionData.getCultCenterId());
        device.setMindCenterId(sessionData.getMindCenterId());
        device.setCityId(sessionData.getCityId());
        device.setTenant(tenant.toString());
        device.setIp(ip);
        if (appVersion != null)
            device.setAppVersion(String.format("%.2f", appVersion));
        if (codePushVersion != null) {
            device.setCodePushVersion(codePushVersion);
        }
        if(!AppUtil.isWeb(userContext)){
            // push device event to Rashi if not web
            this.pushDeviceUpdateToRashi(userContext, device);
        }
        if (sessionData.getLocationPreferenceData() != null && sessionData.getLocationPreferenceData().getLatLong() != null) {
            com.curefit.cfapi.pojo.base.LatLong latLong = sessionData.getLocationPreferenceData().getLatLong();
            device.setPreferredLatLong(new LatLong(latLong.getLatitude().doubleValue(),latLong.getLongitude().doubleValue()));
        } else {
            device.setPreferredLatLong(null);
        }
        if (latitude != null && longitude != null) {
            device.setCurrentLatLong(new LatLong(latitude.doubleValue(), longitude.doubleValue()));
        }
        device.setDetectedCity(new com.curefit.userservice.pojo.misc.DetectedCity(detectedCity.getCity(), detectedCity.getCountry(), null));
        if(detectedCity.getLocation() != null && detectedCity.getLocation().getLatitude() != null && detectedCity.getLocation().getLongitude() != null) {
            LatLong detectedCityLocation = new LatLong(detectedCity.getLocation().getLatitude().doubleValue(), detectedCity.getLocation().getLongitude().doubleValue());
            device.getDetectedCity().setLocation(detectedCityLocation);
        }
        if (advertiserId != null) {
            device.setAdvertiserId(advertiserId);
        }
        this.pushLocationAndTimezoneUpdateToRashi(userContext, device.getUserId(), latitude, longitude, timezone, sessionData.getCityId());
        try {
            this.userServiceClient.upsertDevice(device);
        } catch (Exception e) {
            this.exceptionReportingService.reportException("error while upserting device", e);
            return CompletableFuture.completedFuture(false);
        }
        return CompletableFuture.completedFuture(true);
    }

    private CompletableFuture<Boolean> pushLocationAndTimezoneUpdateToRashi(UserContext userContext, String userId, Float latitude, Float longitude, String timezone, String cityId) {
        //send Location to Rashi
        CompletableFuture<Boolean> locationAndTimezoneUpdateToRashiPromise = CompletableFuture
                .supplyAsync(() -> {
                    JSONObject rashiEventBody = new JSONObject();
                    if(latitude !=null && longitude!=null){
                        JSONObject location = new JSONObject();
                        location.put("latitude", latitude);
                        location.put("longitude", longitude);
                        rashiEventBody.put("location", location);
                    }
                    if(timezone!=null){
                        rashiEventBody.put("timezone", timezone);
                    } else {
                        log.debug("timezone is null for rashi event userId: "+userId);
                    }
                    if(cityId!=null){
                        rashiEventBody.put("cityId", cityId);
                    } else {
                        log.debug("cityId is null for rashi event userId: "+userId);
                    }
                    if(rashiEventBody.containsKey("location") || rashiEventBody.containsKey("timezone") || rashiEventBody.containsKey("cityId") ){
                        UserEventEntry rashiUserLocationEntry = new UserEventEntry(Long.parseLong(userId), UserEventType.USER_PROFILE_EVENT, null, new Date(), rashiEventBody, AppUtil.getAppTenantFromUserContext(userContext));
                        PublishResult locationPublishToRashiResult;
                        try {
                            String requestId = request.getHeader("x-request-id");
                            locationPublishToRashiResult = this.rashiClient.publishUserEvent(rashiUserLocationEntry, AppUtil.getAppTenantFromUserContext(userContext), requestId);
                            return true;
                        }catch (Exception e){
                            log.error("Exception while publishing location to rashi" + e.toString());
                            return false;
                        }
                    }
                    return false;
                },taskExecutor).exceptionally(error -> {
                    log.error("ERROR while publishing location to rashi" + error.toString());
                    this.exceptionReportingService.reportException("error in pushLocationAndTimezoneUpdateToRashi", error);
                    return false;
                });
        return locationAndTimezoneUpdateToRashiPromise;
    }

    private CompletableFuture<Boolean> pushDeviceUpdateToRashi(UserContext userContext, DeviceDetailEntry device) {
        CompletableFuture<Boolean> deviceUpdateToRashiPromise = CompletableFuture
                .supplyAsync(() -> {
                    String deviceId = device.getDeviceId();
                    if(deviceId == null || deviceId.isEmpty()) {
                        return false;
                    }
                    JSONObject rashiDeviceInfo = new JSONObject();
                    rashiDeviceInfo.put("deviceId", deviceId);
                    rashiDeviceInfo.put("isLoggedIn", device.getIsLoggedIn());
                    rashiDeviceInfo.put("appId", device.getAppId());
                    rashiDeviceInfo.put("brand", device.getBrand());
                    rashiDeviceInfo.put("deviceModel", device.getDeviceModel());
                    rashiDeviceInfo.put("osName", device.getOsName());
                    rashiDeviceInfo.put("osVersion", device.getOsVersion());
                    rashiDeviceInfo.put("pushNotificationToken", device.getPushNotificationToken());
                    rashiDeviceInfo.put("registerDate", device.getRegisterDate());
                    rashiDeviceInfo.put("appVersion", device.getAppVersion());
                    rashiDeviceInfo.put("tenant", device.getTenant());
                    rashiDeviceInfo.put("lastOpenDate", new Date().getTime());

                    JSONArray deviceArray = new JSONArray();
                    deviceArray.add(rashiDeviceInfo);
                    JSONObject rashiEventBody = new JSONObject();
                    rashiEventBody.put("devices", deviceArray);
                    UserEventEntry rashiUserDeviceEntry = new UserEventEntry(Long.parseLong(device.getUserId()), UserEventType.USER_PROFILE_EVENT, null, new Date(), rashiEventBody, AppUtil.getAppTenantFromUserContext(userContext));
                    PublishResult devicePublishToRashiResult;
                    try {
                        String requestId = request.getHeader("x-request-id");
                        devicePublishToRashiResult = this.rashiClient.publishUserEvent(rashiUserDeviceEntry, AppUtil.getAppTenantFromUserContext(userContext), requestId);
                        return true;
                    }catch (Exception e){
                        log.error("Exception while publishing device to rashi" + e.toString());
                        return false;
                    }
                }, taskExecutor).exceptionally(error -> {
                    log.error("ERROR while publishing device to rashi" + error.toString());
                    this.exceptionReportingService.reportException("error in pushDeviceUpdateToRashi", error);
                    return false;
                });
        return deviceUpdateToRashiPromise;
    }

    private CompletableFuture<List<DeviceDetailEntry>> getLoggedInDevicesForUser(String userId, AppTenant tenant) throws BaseException {
        return CompletableFuture.completedFuture(this.userServiceClient.getLoggedInDevicesForUser(userId, tenant.toString()));
    }

    private CompletableFuture<DeviceDetailEntry> getDeviceByIdAndTenant(String deviceId, AppTenant tenant) throws BaseException {
        return CompletableFuture.completedFuture(this.userServiceClient.getDeviceByIdAndTenant(deviceId, tenant.toString()));
    }

    public Boolean logoutDeviceAndExpireSession(DeviceDetailEntry device) throws Exception {
        this.sessionBusiness.expireSession(device.getAt());
        this.userServiceClient.logoutDevice(device.getDeviceId(), device.getTenant());
        return true;
    }

    public Boolean logoutDeviceAndExpireSession(String deviceId, String userId, AppTenant tenant) throws Exception {
        DeviceDetailEntry device = this.getDeviceByDeviceId(deviceId, tenant).get();
        if (!Objects.equals(userId, device.getUserId())) {
            throw new UnauthorizedException("Unauthorised operation");
        }
        this.sessionBusiness.expireSession(device.getAt());
        this.userServiceClient.logoutDevice(device.getDeviceId(), device.getTenant());
        return true;
    }

    public Boolean bulkLogoutDeviceAndExpireSessions(String userId, AppTenant tenant) throws Exception {
        try {
            List<DeviceDetailEntry> devices = this.getLoggedInDevicesForUser(userId, tenant).get();
            for (DeviceDetailEntry device: devices) {
                this.logoutDeviceAndExpireSession(device);
            }
            return true;
        } catch (Exception e) {
            log.error("bulkLogoutDevices for the user " + userId + " and tenant " + tenant + " failed", e);
            this.exceptionReportingService.reportException("bulkLogoutDevices for the user " + userId + " and tenant " + tenant + " failed", e);
            throw e;
        }
    }

    public List<ActiveDevice> getActiveSessionsForUser(String userId, AppTenant tenant) throws Exception{
        try {
            List<DeviceDetailEntry> devices = this.getLoggedInDevicesForUser(userId, tenant).get();
            List<ActiveDevice> activeDevices = new ArrayList<>();
            for (DeviceDetailEntry device: devices) {
                if (Objects.equals(device.getAppId(), "web")) {
                    continue;
                }
                if (Objects.equals(device.getDeviceModel(), "browser")) {
                    continue;
                }
                ActiveDevice activeDevice = new ActiveDevice();
                activeDevice.setLastLoggedInAt(device.getRegisterDate());
                activeDevice.setDeviceId(device.getDeviceId());
                activeDevice.setDeviceModel(device.getDeviceModel());
                activeDevices.add(activeDevice);
            }
            return activeDevices;
        } catch (Exception e) {
            log.error("Failed to get active devices for the user " + userId + " and tenant " + tenant, e);
            this.exceptionReportingService.reportException("Failed to get active devices for the user " + userId + " and tenant " + tenant, e);
            throw e;
        }
    }
}
