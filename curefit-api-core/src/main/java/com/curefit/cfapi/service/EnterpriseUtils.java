package com.curefit.cfapi.service;

import com.curefit.albus.response.BasicDoctorResponse;
import com.curefit.albus.response.DoctorCenterMappingResponse;
import com.curefit.albus.response.MyDoctorResponse;
import com.curefit.albus.response.bundle.GenericBundleOrderResponse;
import com.curefit.base.enums.Tenant;
import com.curefit.catalogv1.CatalogueServiceV2Utilities;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.model.internal.cult.PackOfferDetails;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.userinfo.UserProfile;
import com.curefit.cfapi.model.internal.vm.Status;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.appConfig.EnterpriseConfig;
import com.curefit.cfapi.util.ActionUtil;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.OfferUtil;
import com.curefit.cfapi.util.TimeUtil;
import com.curefit.cfapi.util.TransformUtil;
import com.curefit.cfapi.util.PlayUtil;
import com.curefit.cfapi.view.viewmodels.common.AccordionSectionItem;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.common.banner.ContentMetric;
import com.curefit.cfapi.widgets.enterprise.*;
import com.curefit.cfapi.widgets.fitness.SkuPackUtil;
import com.curefit.common.data.exception.BaseException;
import com.curefit.cult.models.CultSummary;
import com.curefit.cult.models.MembershipDetails;
import com.curefit.gymfit.models.Listing;
import com.curefit.gymfit.utils.Enums;
import com.curefit.membership.client.MembershipFilter;
import com.curefit.membership.pojo.entry.Benefit;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.offers.dtos.GetOffersResponse;
import com.curefit.offers.dtos.LiveProductDiscountRequest;
import com.curefit.offers.dtos.LiveProductPricesResponse;
import com.curefit.offers.dtos.OfferMini;
import com.curefit.offers.dtos.care.CareProductDiscountRequest;
import com.curefit.offers.dtos.care.CareProductPricesResponse;
import com.curefit.offers.dtos.cult.CultProductPricesResponse;
import com.curefit.offers.dtos.cult.ProductPriceResponse;
import com.curefit.offers.dtos.gymfit.GymFitProductPricesResponse;
import com.curefit.offers.dtos.play.PlayProductPricesResponse;
import com.curefit.offers.enums.OrderSource;
import com.curefit.offers.types.Offer;
import com.curefit.offers.types.OfferDiscountDetails;
import com.curefit.offers.types.UserConsumptionDetailsRequest;
import com.curefit.offers.types.UserConsumptionDetailsResponse;
import com.curefit.offers.types.UserInfo;
import com.curefit.pms.enums.Namespace;
import com.curefit.pms.enums.ProductSubType;
import com.curefit.pms.enums.Visibility;
import com.curefit.pms.pojo.Restriction;
import com.curefit.pms.pojo.customPacks.OfflineFitnessPack;
import com.curefit.pms.requests.PackSearchRequest;
import com.curefit.product.ProductPrice;
import com.curefit.product.enums.ProductType;
import com.curefit.product.models.Product;
import com.curefit.product.models.cult.FitnessPack;
import com.curefit.rashi.pojo.EventEntry;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.type.DayOfWeek;
import fit.cult.enterprise.dto.corporate.Corporate;
import fit.cult.enterprise.dto.corporate.CorporateBenefits;
import fit.cult.enterprise.dto.corporate.CorporateDetails;
import fit.cult.enterprise.dto.meeting.MeetingItem;
import fit.cult.enterprise.dto.meeting.MeetingStatus;
import fit.cult.enterprise.dto.meeting.Meetings;
import fit.cult.enterprise.dto.program.ProgramDetails;
import fit.cult.enterprise.dto.program.ProgramType;
import lombok.AccessLevel;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.curefit.cfapi.service.appConfig.EnterpriseConfig.*;

@Service
@Slf4j
public class EnterpriseUtils {

    public static Pair<BigDecimal, BigDecimal> getCultLivePrices(CorporateBenefits cultBenefit,
                                                                 ServiceInterfaces interfaces, UserContext userContext) throws Exception {
        if (cultBenefit == null) {
            return null;
        }
        ProgramType programType = cultBenefit.getProgramType();
        if (programType != ProgramType.DISCOUNTED_CULT_LIVE && programType != ProgramType.CULT_LIVE && programType != ProgramType.COUPON_BASED_CULT_LIVE) {
            return null;
        }
        if (programType == ProgramType.CULT_LIVE || programType == ProgramType.COUPON_BASED_CULT_LIVE) {
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }
        String offerId = cultBenefit.getDetails()
                .getOfferId();
        Pair<BigDecimal, BigDecimal> prices = EnterpriseUtils.getCultLiveOfferPrices(interfaces, userContext, offerId);
        if (prices == null) {
            return null;
        }
        BigDecimal offerPrice = prices.getRight();
        if (offerPrice == null) {
            offerPrice = prices.getLeft();
        }
        return Pair.of(prices.getLeft(), offerPrice);
    }

    public static TrialCardV2Item buildCultProProgramTrialCardWidget(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        OfflineFitnessPack fitnessProduct = getProProgramPackForDuration(interfaces, userContext, benefit);
        if(fitnessProduct == null) {
            log.info("TRIAL_CARD_WIDGET buildCultProProgramTrialCardWidget no product found for {}", benefit.getProgramType());
            return null;
        }
        log.info("EnterpriseUtils checking user consumptions for benefit type -> {} with offer id -> {}", benefit.getProgramType(), benefit.getDetails().getOfferId());
        Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, benefit.getDetails().getOfferId());
        boolean isMembershipActive = consumptions != null && consumptions.getRight() > 0;
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.GYMFIT_FITNESS_PRODUCT, benefit, isMembershipActive);
        return new TrialCardV2Item(programDates.getLeft(), programDates.getRight(), fitnessProduct.getPrice().getMrp(), fitnessProduct.getPrice().getListingPrice(), benefit.getProgramType(), isMembershipActive, benefit.getDurationDays(), fitnessProduct, null, userContext, null);
    }

    public static CorporateBenefits getProgramForBenefitList(List<CorporateBenefits> benefits) {
        if(benefits.size() == 1) {
            return benefits.get(0);
        }
        CorporateBenefits defaultBenefit = benefits.stream().filter(CorporateBenefits::isDefaultProgram).findFirst().orElse(null);
        return benefits.stream().filter(item -> !item.isDefaultProgram()).findFirst().orElse(defaultBenefit);
    }

    public static CorporateBenefits getActiveBenefitForDisplay(ServiceInterfaces interfaces, UserContext userContext, ProgramType programType, List<CorporateBenefits> benefits) throws Exception {
        if(benefits.size() == 1) {
            return benefits.get(0);
        }
        benefits.sort(Comparator.comparing(CorporateBenefits::isDefaultProgram));
        CorporateBenefits defaultBenefit = benefits.stream().filter(CorporateBenefits::isDefaultProgram).findFirst().orElse(null);
        CorporateBenefits offeredBenefit = benefits.stream().filter(item -> !item.isDefaultProgram()).findFirst().orElse(null);

        switch (programType) {
            case CULT_LIVE, COUPON_BASED_CULT_LIVE -> {
                return offeredBenefit != null ? offeredBenefit : defaultBenefit;
            }
            case THERAPY, ONLINE_DIETICIAN, DOCTOR_CONSULTATION, ONLINE_PT -> {
                if(offeredBenefit != null) {
                    Pair<Integer, Integer> offerConsumption = getUserConsumptions(userContext, interfaces, offeredBenefit.getDetails().getOfferId());
                    if(offerConsumption == null) {
                        return offeredBenefit;
                    } else if(!Objects.equals(offerConsumption.getLeft(), offerConsumption.getRight())) {
                        return offeredBenefit;
                    } else {
                        return defaultBenefit;
                    }
                }
            }
            case DISCOUNTED_CULT_LIVE -> {
                if(offeredBenefit != null) {
                    Pair<Integer, Integer> offerConsumption = getUserConsumptions(userContext, interfaces, offeredBenefit.getDetails().getOfferId());
                    boolean isMembershipActive = offerConsumption != null && offerConsumption.getRight() > 0;
                    if(isMembershipActive) {
                        Pair<Date, Date> programDates = getActiveCultLiveMembershipDates(userContext);
                        if(programDates != null && programDates.getRight().before(new Date())) {
                            return defaultBenefit;
                        } else {
                            return offeredBenefit;
                        }
                    }
                    return offeredBenefit;
                }
                return defaultBenefit;
            }
            case DISCOUNTED_CULT_ELITE, DISCOUNTED_CULT_PRO  -> {
                if(offeredBenefit != null) {
                    Pair<Integer, Integer> offerConsumption = getUserConsumptions(userContext, interfaces, offeredBenefit.getDetails().getOfferId());
                    boolean isMembershipActive = offerConsumption != null && offerConsumption.getRight() > 0;

                    if (isMembershipActive) {
                        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, programType.equals(ProgramType.DISCOUNTED_CULT_PRO) ? ProductType.GYMFIT_FITNESS_PRODUCT : ProductType.FITNESS, offeredBenefit, true);
                        if(programDates.getRight().before(new Date())) {
                            return defaultBenefit;
                        } else {
                            return offeredBenefit;
                        }
                    }
                    return offeredBenefit;
                }
                return defaultBenefit;
            }
        }
        return null;
    }

    private static Pair<Date, Date> getProgramStartEndDate(ServiceInterfaces interfaces, UserContext userContext, ProductType productType, CorporateBenefits benefit, boolean isOfferConsumed) throws BaseException {
        Date programStartDate = benefit.getStartDate();
        Date programEndDate = benefit.getEndDate();
        if (isOfferConsumed) {
            Membership activeMembership = getMembershipSummaryForProgram(interfaces, userContext, productType);
            if (activeMembership != null) {
                programStartDate =  new Date(new Timestamp(activeMembership.getStart()).getTime());
                programEndDate = new Date(new Timestamp(activeMembership.getEnd()).getTime());
            }
        }
        return Pair.of(programStartDate, programEndDate);
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    private static Membership getMembershipSummaryForProgram(ServiceInterfaces interfaces, UserContext userContext, ProductType productType) throws BaseException {
        List<String> benefits = List.of("CULT", "PLAY", "ONEPASS");
        List<Membership> membershipList = interfaces.membershipService.getCachedMembershipsForUser(userContext.getUserProfile().getUserId(), "curefit", MembershipFilter.builder().benefits(benefits).build()).get();

        if(membershipList == null || membershipList.size() == 0) {
            return null;
        }
        List<Membership> filteredOrder = new ArrayList<>();

        log.debug("Enterprise Membership list for user" + membershipList.toString());
        for(Membership membership: membershipList) {
            ProductType packProductType;
            if (CatalogueServiceV2Utilities.hasMigratedToPMS(membership.getProductId())) {
                OfflineFitnessPack pack = interfaces.catalogueServicePMS.getOfflineFitnessPackById(membership.getProductId());
                packProductType = pack.getProductType();

            } else {
                log.info("Product Id {} for product type :: {}", membership.getProductId(), productType);
                Product product = interfaces.catalogueService.getProduct(membership.getProductId());
                packProductType = product.getProductType();
            }
            if(membership.getStatus() != com.curefit.membership.types.Status.CANCELLED && packProductType.equals(productType)) {
                filteredOrder.add(membership);
            }

        }
        if (filteredOrder.size() == 0) {
            log.info("No membership found for product type :: {}", productType);
            return null;
        }
        filteredOrder.sort(Comparator.comparing(Membership::getEnd));
        return filteredOrder.get(filteredOrder.size() - 1);
    }

    public static TrialCardV2Item buildCultEliteProgramTrialCardWidget(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        OfflineFitnessPack cultPack = getEliteProgramPackForDuration(interfaces, userContext, benefit);
        if(cultPack == null) {
            log.info("TRIAL_CARD_WIDGET buildCultEliteProgramTrialCardWidget no product found for {}", benefit.getProgramType());
            return null;
        }
        log.info("EnterpriseUtils checking user consumptions for benefit type -> {} with offer id -> {}", benefit.getProgramType(), benefit.getDetails().getOfferId());
        Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, benefit.getDetails().getOfferId());
        boolean isMembershipActive = consumptions != null && consumptions.getRight() > 0;
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.FITNESS, benefit, isMembershipActive);
        return new TrialCardV2Item(programDates.getLeft(), programDates.getRight(), cultPack.getPrice().getMrp(), cultPack.getPrice().getListingPrice(), benefit.getProgramType(), isMembershipActive, benefit.getDurationDays() ,null, cultPack, userContext, null);
    }

    public static TrialCardV2Item buildCultPlayProgramTrialCardWidget(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        FitnessPack playPack = getPlayProgramPackForDuration(interfaces, userContext, benefit);
        if (playPack == null) {
            log.info("TRIAL_CARD_WIDGET buildCultPlayProgramTrialCardWidget no product found for {}", benefit.getProgramType());
            return null;
        }
        log.info("EnterpriseUtils checking user consumptions for benefit type -> {} with offer id -> {}", benefit.getProgramType(), benefit.getDetails().getOfferId());
        Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, benefit.getDetails().getOfferId());
        boolean isMembershipActive = consumptions != null && consumptions.getRight() > 0;
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.PLAY, benefit, isMembershipActive);
        return new TrialCardV2Item(programDates.getLeft(), programDates.getRight(), playPack.getPrice().getMrp(), playPack.getPrice().getListingPrice(), benefit.getProgramType(), isMembershipActive, benefit.getDurationDays() ,null, null, userContext, playPack);
    }

    public static TrialCardV2Item buildTransformProgramTrialCardWidget(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        GenericBundleOrderResponse latestTransformMembership = TransformUtil.getLatestMembership(interfaces, userContext);
        Date startDate = Date.from(Instant.ofEpochMilli(latestTransformMembership.getStartTimeEpoch()).atZone(ZoneId.of(TimeUtil.DEFAULT_ZONE_ID)).toInstant());
        Date endDate = Date.from(Instant.ofEpochMilli(latestTransformMembership.getExpiryTimeEpoch()).atZone(ZoneId.of(TimeUtil.DEFAULT_ZONE_ID)).toInstant());
        return new TrialCardV2Item(startDate, endDate, BigDecimal.ZERO, BigDecimal.ZERO, true, benefit.getProgramType());
    }

    public static TrialCardV2Item buildEliteDirectMembershipCardWidget(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.FITNESS, benefit, true);
        return new TrialCardV2Item(programDates.getLeft(), programDates.getRight(), BigDecimal.ZERO, BigDecimal.ZERO, true, benefit.getProgramType());
    }

    public static TrialCardV2Item buildLiveDirectMembershipCardWidget(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        Tenant tenant = AppUtil.getTenantFromUserContext(userContext);
        com.curefit.diyfs.pojo.Membership liveMembership = interfaces.diyfsService.getMembershipDetails(userContext.getUserProfile().getUserId(), tenant);
        Date startDate = Date.from(Instant.ofEpochMilli(liveMembership.getStart()).atZone(ZoneId.of(TimeUtil.DEFAULT_ZONE_ID)).toInstant());
        Date endDate = Date.from(Instant.ofEpochMilli(liveMembership.getEnd()).atZone(ZoneId.of(TimeUtil.DEFAULT_ZONE_ID)).toInstant());
        return new TrialCardV2Item(startDate, endDate, BigDecimal.ZERO, BigDecimal.ZERO, true, benefit.getProgramType());
    }

    public static TrialCardV2Item buildOnePassDirectMembershipCardWidget(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.ONEPASS_PRODUCT, benefit, true);
        return new TrialCardV2Item(programDates.getLeft(), programDates.getRight(), BigDecimal.ZERO, BigDecimal.ZERO, true, benefit.getProgramType());
    }

    public static TrialCardV2Item buildLimitedEliteTrialCardWidget(ServiceInterfaces interfaces, UserContext userContext, String corporateCode) throws Exception {
        Membership activeMembership = getMembershipSummaryForProgram(interfaces, userContext, ProductType.FITNESS);
        if (activeMembership == null) {
            return null;
        }
        boolean isMemCity = activeMembership.getMetadata().containsKey("cityId") && activeMembership.getMetadata().get("cityId").equals(userContext.getUserProfile().getCity().getCityId());
        Benefit membershipBenefit = activeMembership.getBenefits().stream().filter(benefit -> benefit.getName().equals(isMemCity ? "CULT" : "CULT_AWAY")).findFirst().orElse(null);

        if (membershipBenefit == null) {
            return null;
        }
        double progressVal = (double) membershipBenefit.getTicketsUsed() / (double) membershipBenefit.getMaxTickets();
        return new TrialCardV2Item(membershipBenefit.getMaxTickets(), membershipBenefit.getTicketsUsed(), getProgressColor(progressVal), membershipBenefit, !isMemCity);
    }

    public static TrialCardV2Item buildCultPlayLimitedProgramTrialCardWidget(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefits) throws Exception {
        Membership activeMembership = getMembershipSummaryForProgram(interfaces, userContext, ProductType.PLAY);
        log.debug("Corp Active Membership: " + activeMembership != null ? activeMembership.toString() : "NULL");
        if (activeMembership == null) {
            return null;
        }
        boolean isMemCity = activeMembership.getMetadata().containsKey("cityId") && activeMembership.getMetadata().get("cityId").equals(userContext.getUserProfile().getCity().getCityId());
        Benefit membershipBenefit = activeMembership.getBenefits().stream().filter(benefit -> benefit.getName().equals(isMemCity ? "PLAY" : "PLAY_AWAY")).findFirst().orElse(null);

        if (membershipBenefit == null) {
            return null;
        }
        double progressVal = (double) membershipBenefit.getTicketsUsed() / (double) membershipBenefit.getMaxTickets();
        return new TrialCardV2Item(membershipBenefit.getMaxTickets(), membershipBenefit.getTicketsUsed(), getProgressColor(progressVal), membershipBenefit, !isMemCity);
    }

    public static String getProgressColor(double progressVal) {
        if (progressVal < 0.5) {
            return "#5bdbb6";
        } else if (progressVal < 0.75) {
            return "#ffa300";
        } else {
            return "#b00020";
        }
    }

    public static Action getCultpassXTrialCardAction() {
        Action action = new Action();
        action.setActionType(ActionType.SHOW_CUSTOM_BOTTOM_SHEET);
        HashMap<String, Object> meta = new HashMap<>();
        List<BaseWidget> widgets = new ArrayList<>();

        TrialCardV2Widget bottomSheetWidget = new TrialCardV2Widget();
        List<TrialCardV2Item> data = new ArrayList<>();

        bottomSheetWidget.addSpacing("30", "30");

        TrialCardV2Item onepassItem = new TrialCardV2Item();
        onepassItem.setImage("/image/vm/5d10db55-c151-49d2-b1ec-501788201c65.png");
        onepassItem.setAction(new Action(ONEPASS_MEMBER_URL, ActionType.NAVIGATION));
        onepassItem.setTitle("View onepass gyms");
        data.add(onepassItem);

        TrialCardV2Item eliteItem = new TrialCardV2Item();
        eliteItem.setImage("/image/vm/a470bbc5-d5d6-49b0-b94d-af8ebad7c141.png");
        eliteItem.setAction(new Action(CULT_ELITE_CLP_MEMBER_URL, ActionType.NAVIGATION));
        eliteItem.setTitle("Book cult class");
        data.add(eliteItem);

        bottomSheetWidget.setData(data);
        widgets.add(bottomSheetWidget);

        meta.put("showTopNotch", true);
        meta.put("blurEnabled", false);
        meta.put("widgets", widgets);
        meta.put("pageId", "enterpriseclp");
        action.setMeta(meta);
        return action;
    }

    public static TrialCardV2Item buildOnlinePTTrialCardWidget(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit, String cardType) throws Exception {

        if(!AppUtil.isEnterpriseFeatureSupported(userContext, AppUtil.ENTERPRISE_PT_PROGRAM__ROLL_OUT_APP_VERSION)) {
            return null;
        }

        String offerId = benefit.getDetails()
                .getOfferId();
        Pair<String, ProductPrice> priceWithProductId = getCareProductIdWithMinOfferPrice(interfaces, offerId, benefit, userContext);
        if (priceWithProductId == null) {
            return null;
        }
        log.info("getCareOfferPrices benefit type -> {} :: offer Price Listing :: {} Mrp Price :: {} Product ID :: {}", benefit.getProgramType(), priceWithProductId.getRight().getListingPrice(), priceWithProductId.getRight().getMrp(), priceWithProductId.getLeft());
        Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, offerId);
        if (consumptions == null) {
            return new TrialCardV2Item(benefit.getStartDate(), benefit.getEndDate(), priceWithProductId.getRight().getMrp(), priceWithProductId.getRight().getListingPrice(), false, benefit.getProgramType());
        }
        return new TrialCardV2Item(consumptions.getLeft().longValue(), consumptions.getRight().longValue(), priceWithProductId.getRight().getMrp(), priceWithProductId.getRight().getListingPrice(), userContext, cardType, priceWithProductId.getLeft());
    }

    public static TrialCardV2Item getTrialCardFromOfferId(ServiceInterfaces interfaces, UserContext userContext,
                                                          CorporateBenefits benefit, String cardType,
                                                          boolean isSessionBased, String corporateCode) throws Exception {
        String offerId = benefit.getDetails()
                .getOfferId();

        Pair<BigDecimal, BigDecimal> prices = getOfferPriceForBenefit(interfaces, offerId, benefit, userContext);
        if (prices == null) {
            log.info("getTrialCardFromOfferId price null for {}", benefit.getProgramType());
            return null;
        }

        Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, offerId);
        if (!isSessionBased || consumptions == null) {
            boolean isActive =
                    benefit.getProgramType() == ProgramType.DISCOUNTED_CULT_LIVE &&
                            consumptions != null && consumptions.getRight() > 0;

            Date startDate = benefit.getStartDate();
            Date endDate = benefit.getEndDate();

            if (consumptions != null && benefit.getProgramType().equals(ProgramType.DISCOUNTED_CULT_LIVE)) {
                Pair<Date, Date> programDates = getActiveCultLiveMembershipDates(userContext);
                if(programDates != null) {
                    startDate = programDates.getLeft();
                    endDate = programDates.getRight();
                }
            }

            return new TrialCardV2Item(startDate, endDate, prices.getLeft(),
                    prices.getRight(), isActive, benefit.getProgramType());
        }
        if (benefit.getProgramType() != ProgramType.DOCTOR_CONSULTATION) {
            return new TrialCardV2Item(consumptions.getLeft(), consumptions.getRight(), prices.getLeft(),
                    prices.getRight(), false, userContext, corporateCode);
        }
        return new TrialCardV2Item(consumptions.getLeft(), consumptions.getRight(), prices.getLeft(), prices.getRight(), true, userContext, corporateCode);
    }

    public static SKUCardV2WidgetItem getSKUCardFromOfferId(ServiceInterfaces interfaces, UserContext userContext,
                                                            CorporateBenefits benefit, String productType, String corporateCode) throws Exception {

        SKUCardV2WidgetItem cardV2WidgetItem = null;
        switch (benefit.getProgramType()) {
            case DISCOUNTED_CULT_ELITE -> cardV2WidgetItem = buildCultEliteProgramSKUWidgetItem(interfaces, userContext, benefit, productType);
            case DISCOUNTED_CULT_PRO -> cardV2WidgetItem = buildCultProProgramSKUWidgetItem(interfaces, userContext, benefit, productType);
            case DISCOUNTED_CULT_PLAY -> cardV2WidgetItem = buildCultPlayProgramSKUWidgetItem(interfaces, userContext, benefit, productType);
            case ONLINE_PT -> cardV2WidgetItem = buildOnlinePTProgramSKUWidgetItem(interfaces, userContext, benefit, productType);
            case CULT_LIVE, COUPON_BASED_CULT_LIVE, ELITE_FIX_DATES, PRO_FIX_DATES, OP_PREMIUM_FIX_DATE, ELITE_FIX_DATES_WITHOUT_LIVE, OP_STANDARD_FIX_DATE,
                 PLAY_LIMITED_FIX_DATES -> cardV2WidgetItem = new SKUCardV2WidgetItem(benefit.getStartDate(), benefit.getEndDate(),BigDecimal.valueOf(0), BigDecimal.valueOf(0), productType, 1, null, corporateCode);
            case ELITE_FIX_DURATION, PRO_FIX_DURATION, ELITE_FIX_DURATION_WITHOUT_LIVE, PLAY_LIMITED_FIX_DURATION -> cardV2WidgetItem = buildEliteDirectMembershipSKUWidgetItem(interfaces, userContext, benefit, productType, corporateCode);
            case OP_PREMIUM_FIX_DURATION, OP_STANDARD_FIX_DURATION -> cardV2WidgetItem = buildOnePassMembershipSKUWidgetItem(interfaces, userContext, benefit, productType, corporateCode);
            case DISCOUNTED_CULT_LIVE -> {
                String offerId = benefit.getDetails()
                        .getOfferId();
                Pair<BigDecimal, BigDecimal> prices = getOfferPriceForBenefit(interfaces, offerId, benefit, userContext);
                if(prices != null) {
                    Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, offerId);
                    if(consumptions != null && consumptions.getRight() > 0) {
                        Pair<Date, Date> memberShipDates = getActiveCultLiveMembershipDates(userContext);
                        cardV2WidgetItem = new SKUCardV2WidgetItem(memberShipDates != null ? memberShipDates.getLeft() : benefit.getStartDate(), memberShipDates != null ? memberShipDates.getRight() : benefit.getEndDate(),BigDecimal.valueOf(0), BigDecimal.valueOf(0), productType, consumptions.getRight(), null, corporateCode);
                    } else {
                        String offerOffValue = EnterpriseUtils.getCultOfferAmountCaption(interfaces, offerId, benefit, prices.getLeft(), prices.getRight(), false, userContext);
                        cardV2WidgetItem = new SKUCardV2WidgetItem(benefit.getStartDate(), benefit.getEndDate(), prices.getLeft(), prices.getRight(), productType, 0, offerOffValue, corporateCode);
                    }
                }
            }
            default -> {
                String offerId = benefit.getDetails()
                        .getOfferId();
                Pair<BigDecimal, BigDecimal> prices = getOfferPriceForBenefit(interfaces, offerId, benefit, userContext);
                if (prices != null) {
                    Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, offerId);

                    if (consumptions == null) {
                        String offerOffValue = EnterpriseUtils.getCultOfferAmountCaption(interfaces, offerId, benefit, prices.getLeft(), prices.getRight(), false, userContext);
                        cardV2WidgetItem = new SKUCardV2WidgetItem(benefit.getStartDate(), benefit.getEndDate(), prices.getLeft(), prices.getRight(), productType, 0, offerOffValue, corporateCode);
                    } else {
                        cardV2WidgetItem = new SKUCardV2WidgetItem(consumptions.getLeft(), prices.getLeft(), prices.getRight(), consumptions.getRight(), productType, corporateCode);
                    }
                }
            }
        }
        return cardV2WidgetItem;
    }

    public static SKUCardV2WidgetItem buildCultProProgramSKUWidgetItem(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit, String productType) throws Exception {
        OfflineFitnessPack proPack = getProProgramPackForDuration(interfaces, userContext, benefit);
        if(proPack == null) {
            log.info("TRIAL_CARD_WIDGET buildCultProProgramSKUWidgetItem no product found for {}", benefit.getProgramType());
            return null;
        }
        Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, benefit.getDetails().getOfferId());
        boolean isMembershipActive = consumptions != null && consumptions.getRight() > 0;
        BigDecimal actualPrice = (proPack.getPrice() != null) ? proPack.getPrice().getMrp() : null;
        BigDecimal listingPrice = (proPack.getPrice() != null) ? proPack.getPrice().getListingPrice() : null;
        String offerOffValue = EnterpriseUtils.getCultOfferAmountCaption(interfaces, benefit.getDetails().getOfferId(), benefit, actualPrice, listingPrice, false, userContext);
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.GYMFIT_FITNESS_PRODUCT, benefit, isMembershipActive);
        return new SKUCardV2WidgetItem(programDates, actualPrice, listingPrice, consumptions != null ? consumptions.getRight() : 0, offerOffValue, productType, benefit.getDurationDays(), proPack, null, userContext, null);
    }

    public static SKUCardV2WidgetItem buildCultEliteProgramSKUWidgetItem(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit, String productType) throws Exception {
        OfflineFitnessPack fitnessPack = getEliteProgramPackForDuration(interfaces, userContext, benefit);
        if(fitnessPack == null) {
            log.info("SKU_CARD_WIDGET buildCultEliteProgramSKUWidgetItem no product found for {}", benefit.getProgramType());
            return null;
        }
        Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, benefit.getDetails().getOfferId());
        BigDecimal actualPrice = (fitnessPack.getPrice().getMrp() != null) ? fitnessPack.getPrice().getMrp() : null;
        BigDecimal listingPrice = (fitnessPack.getPrice().getListingPrice() != null) ? fitnessPack.getPrice().getListingPrice() : null;
        String offerOffValue = EnterpriseUtils.getCultOfferAmountCaption(interfaces, benefit.getDetails().getOfferId(), benefit, actualPrice, listingPrice, false, userContext);
        boolean isMembershipActive = consumptions != null && consumptions.getRight() > 0;
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.FITNESS, benefit, isMembershipActive);
        return new SKUCardV2WidgetItem(programDates,actualPrice, listingPrice, consumptions != null ? consumptions.getRight() : 0, offerOffValue, productType, benefit.getDurationDays(),null, fitnessPack, userContext, null);
    }

    public static SKUCardV2WidgetItem buildCultPlayProgramSKUWidgetItem(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit, String productType) throws Exception {
        FitnessPack playPack = getPlayProgramPackForDuration(interfaces, userContext, benefit);
        if (playPack == null) {
            log.info("SKU_CARD_WIDGET buildCultPlayProgramSKUWidgetItem no product found for {}", benefit.getProgramType());
            return null;
        }
        log.info("EnterpriseUtils checking user consumptions for benefit type -> {} with offer id -> {}", benefit.getProgramType(), benefit.getDetails().getOfferId());
        Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, benefit.getDetails().getOfferId());
        boolean isMembershipActive = consumptions != null && consumptions.getRight() > 0;
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.PLAY, benefit, isMembershipActive);
        BigDecimal actualPrice = (playPack.getPrice() != null) ? playPack.getPrice().getMrp() : null;
        BigDecimal listingPrice = (playPack.getPrice() != null) ? playPack.getPrice().getListingPrice() : null;
        String offerOffValue = EnterpriseUtils.getCultOfferAmountCaption(interfaces, benefit.getDetails().getOfferId(), benefit, actualPrice, listingPrice, true, userContext);
        return new SKUCardV2WidgetItem(programDates,actualPrice, listingPrice, consumptions != null ? consumptions.getRight() : 0, offerOffValue, productType, benefit.getDurationDays(),null, null, userContext, playPack);
    }

    public static SKUCardV2WidgetItem buildEliteDirectMembershipSKUWidgetItem(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit, String productType, String corporateCode) throws Exception {
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.FITNESS, benefit, true);
        return new SKUCardV2WidgetItem(programDates.getLeft(), programDates.getRight(),BigDecimal.valueOf(0), BigDecimal.valueOf(0), productType, 1, null, corporateCode);
    }

    public static SKUCardV2WidgetItem buildOnePassMembershipSKUWidgetItem(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit, String productType, String corporateCode) throws Exception {
        Pair<Date, Date> programDates = getProgramStartEndDate(interfaces, userContext, ProductType.ONEPASS_PRODUCT, benefit, true);
        return new SKUCardV2WidgetItem(programDates.getLeft(), programDates.getRight(),BigDecimal.valueOf(0), BigDecimal.valueOf(0), productType, 1, null, corporateCode);
    }

    public static SKUCardV2WidgetItem buildOnlinePTProgramSKUWidgetItem(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit, String productType) throws Exception {
        String offerId = benefit.getDetails()
                .getOfferId();
        Pair<BigDecimal, BigDecimal> prices = getOfferPriceForBenefit(interfaces, offerId, benefit, userContext);
        if(prices != null) {
            return new SKUCardV2WidgetItem(getUserConsumptions(userContext, interfaces, offerId), prices);
        }
        return null;
    }

    public static BenefitInfo buildActivationBenefitItem(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit){

        BenefitInfo benefitInfo = EnterpriseConfig.PROGRAM_BENEFITS.get(benefit.getProgramType());
        if (benefitInfo == null) {
            return null;
        }
        benefitInfo.setCurrencyUnit("₹");
        try {
            if(benefit.getProgramType() == ProgramType.DISCOUNTED_CULT_PRO || benefit.getProgramType() == ProgramType.DISCOUNTED_CULT_ELITE || benefit.getProgramType() == ProgramType.DISCOUNTED_CULT_PLAY) {
                switch (benefit.getProgramType()) {
                    case DISCOUNTED_CULT_ELITE -> {
                        OfflineFitnessPack fitnessPack = EnterpriseUtils.getEliteProgramPackForDuration(interfaces, userContext, benefit);
                        if(fitnessPack == null) {
                            return null;
                        }
                        benefitInfo.setCostPrice(String.valueOf(fitnessPack.getPrice().getMrp()));
                        benefitInfo.setOfferPrice(String.valueOf(fitnessPack.getPrice().getListingPrice()));
                        benefitInfo.setOfferCaption("for " + EnterpriseUtils.getDayDurationCaption(fitnessPack.getProduct().getDurationInDays().intValue()));
                        return benefitInfo;
                    }
                    case DISCOUNTED_CULT_PRO -> {
                        OfflineFitnessPack fitnessProduct = EnterpriseUtils.getProProgramPackForDuration(interfaces, userContext, benefit);
                        if (fitnessProduct == null) {
                            return null;
                        }
                        benefitInfo.setCostPrice(String.valueOf(fitnessProduct.getPrice().getMrp()));
                        benefitInfo.setOfferPrice(String.valueOf(fitnessProduct.getPrice().getListingPrice()));
                        benefitInfo.setOfferCaption("for " + EnterpriseUtils.getDayDurationCaption(fitnessProduct.getProduct().getDurationInDays().intValue()));
                        return benefitInfo;
                    }
                    case DISCOUNTED_CULT_PLAY -> {
                        FitnessPack fitnessPack = EnterpriseUtils.getPlayProgramPackForDuration(interfaces, userContext, benefit);
                        if (fitnessPack == null) {
                            return null;
                        }
                        benefitInfo.setCostPrice(String.valueOf(fitnessPack.getPrice().getMrp()));
                        benefitInfo.setOfferPrice(String.valueOf(fitnessPack.getPrice().getListingPrice()));
                        benefitInfo.setOfferCaption("for " + EnterpriseUtils.getDayDurationCaption(fitnessPack.getDuration()));
                        return benefitInfo;
                    }
                    default -> {
                        return null;
                    }
                }
            } else if(benefit.getProgramType() == ProgramType.CULT_LIVE || benefit.getProgramType() == ProgramType.COUPON_BASED_CULT_LIVE) {
                String durationCaption = "for " + EnterpriseUtils.getDateDurationCaption(benefit.getStartDate(), benefit.getEndDate());
                benefitInfo.setOfferCaption(durationCaption);
                benefitInfo.setOfferPrice("0");
                benefitInfo.setCostPrice("0");
                return benefitInfo;
            }
            else {
                String offerId = benefit.getDetails()
                        .getOfferId();
                Pair<BigDecimal, BigDecimal> prices = EnterpriseUtils.getOfferPriceForBenefit(interfaces, offerId, benefit, userContext);
                if (prices == null) {
                    return null;
                }
                Pair<Integer, Integer> consumptions = getUserConsumptions(userContext, interfaces, offerId);
                BigDecimal cp;
                BigDecimal op;
                if (prices.getRight() == null) {
                    cp = null;
                    op = prices.getLeft();
                } else {
                    cp = prices.getLeft();
                    op = prices.getRight();
                }

                if (cp != null) {
                    benefitInfo.setCostPrice(String.valueOf(cp.longValue()));
                }

                long totalCount = consumptions != null ? consumptions.getLeft() : 0;
                switch (benefit.getProgramType()) {
                    case ONLINE_PT -> {
                        if(cp != null) {
                            benefitInfo.setCostPrice(String.valueOf(cp.longValue()));
                        }
                        String upToSession = "(upto " + totalCount + (totalCount > 1 ? " sessions" : " session") + ")";
                        if(op.compareTo(BigDecimal.ZERO) == 0) {
                            benefitInfo.setOfferCaption("session " + upToSession);
                            benefitInfo.setOfferPrice(String.valueOf(op.longValue()));
                        }
                        else {
                            String caption = "/session";
                            benefitInfo.setOfferPrice(String.valueOf(op.longValue()));
                            benefitInfo.setOfferCaption(caption + " " + upToSession);
                        }
                    }
                    case THERAPY, ONLINE_DIETICIAN, DOCTOR_CONSULTATION -> {
                        Pair<BigDecimal, BigDecimal> perSessionPrices = Pair.of(cp, op);
                        op = perSessionPrices.getRight();
                        if (perSessionPrices.getLeft() != null) {
                            benefitInfo.setCostPrice(String.valueOf(perSessionPrices.getLeft().longValue()));
                        }

                        String upToSession = "(upto " + totalCount + (totalCount > 1 ? " sessions" : " session") + ")";
                        if(op.compareTo(BigDecimal.ZERO) == 0) {
                            benefitInfo.setOfferCaption("session " + upToSession);
                            benefitInfo.setOfferPrice(String.valueOf(op.longValue()));
                        }
                        else {
                            String caption = "/session";
                            if(benefit.getProgramType() == ProgramType.DOCTOR_CONSULTATION) {
                                caption += " onwards";
                            }
                            benefitInfo.setOfferPrice(String.valueOf(op.longValue()));
                            benefitInfo.setOfferCaption(caption + " " + upToSession);
                        }
                    }
                    case DISCOUNTED_CULT_LIVE -> {
                        String durationCaption = "for " + EnterpriseUtils.getDateDurationCaption(benefit.getStartDate(), benefit.getEndDate());
                        benefitInfo.setOfferCaption(durationCaption);
                        if(op.compareTo(BigDecimal.ZERO) == 0) {
                            benefitInfo.setOfferPrice("0");
                        }
                        else {
                            benefitInfo.setOfferPrice(String.valueOf(op.longValue()));
                        }
                    }
                    default -> {}
                }
                return benefitInfo;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Exception in buildActivationBenefitItem {} Error {}", benefit.getProgramType(), e.getMessage());
            return null;
        }
    }

    public static Pair<BigDecimal, BigDecimal> getOfferPriceForBenefit(ServiceInterfaces interfaces, String offerId, CorporateBenefits benefit, UserContext userContext) throws Exception {
        Pair<BigDecimal, BigDecimal> prices = null;
        switch (benefit.getProgramType()) {
            case DISCOUNTED_CULT_LIVE -> prices = getCultLiveOfferPrices(interfaces, userContext, offerId);
            case THERAPY, ONLINE_DIETICIAN, DOCTOR_CONSULTATION -> prices = getCareOfferPrices(interfaces, offerId, benefit, userContext);
            case ONLINE_PT -> {
                if(AppUtil.isEnterpriseFeatureSupported(userContext,AppUtil.ENTERPRISE_PT_PROGRAM__ROLL_OUT_APP_VERSION)) {
                    prices = getCareOfferPrices(interfaces, offerId, benefit, userContext);
                }
            }
        }
        if (prices == null) {
            log.info("getOfferPriceForBenefit :: Benefit Type -> {} :: Offer not found for offerId -> {}",benefit.getProgramType(), offerId);
        }
        return prices;
    }

    public static EnterpriseDoctorWidgetItem getDoctorWidgetItem(boolean isProdOrAlpha, MyDoctorResponse doctor,
                                                                 ProductType productType, UserContext userContext, ServiceInterfaces interfaces) {
        String userId = userContext.getUserProfile()
                .getUserId();
        CorporateDetails corporateDetails = interfaces.getEnterpriseClient()
                .getCorporateWithBenefits(userId);
        if (corporateDetails == null) {
            log.info("getDoctorWidgetItem no corporate found for userId {} productType {}", userId, productType);
            return null;
        }
        else if(corporateDetails.getBenefits().size() == 0) {
            log.info("getDoctorWidgetItem no benefit found for corporate id {} productType {}", corporateDetails.getCorporate().getCode(), productType);
            return null;
        }
        String doctorType = doctor.getMyDoctorType()
                .getCode();
        log.info("getDoctorWidgetItem doctorType {}, productType {}", doctorType, productType);
        if (EnterpriseConfig.THERAPY_DOCTOR_TYPE_CODES.contains(doctorType)) {
            if(productType == ProductType.THERAPY && !checkProgramExistBenefit(corporateDetails, ProgramType.THERAPY)) {
                return null;
            }
            if (productType != ProductType.THERAPY) {
                return null;
            }
        } else if (EnterpriseConfig.NUTRITIONIST_DOCTOR_TYPE_CODES.contains(doctorType)) {
            if(productType == ProductType.NUTRITIONIST_CONSULTATION && !checkProgramExistBenefit(corporateDetails, ProgramType.ONLINE_DIETICIAN)) {
                return null;
            }
            if (productType != ProductType.NUTRITIONIST_CONSULTATION) {
                return null;
            }
        } else if(productType == ProductType.CONSULTATION && !checkProgramExistBenefit(corporateDetails, ProgramType.DOCTOR_CONSULTATION)) {
            log.info("productType {}, not found in benefit {}", productType, ProgramType.DOCTOR_CONSULTATION);
            return null;
        }
        else if (productType != ProductType.CONSULTATION) {
            return null;
        }
        Pair<String, String> productCodeTypePair = EnterpriseUtils.getProductCodeTypePair(doctor, doctorType);
        if (productCodeTypePair == null) {
            return null;
        }
        EnterpriseDoctorWidgetItem item = new EnterpriseDoctorWidgetItem().setTitle(doctor.getName())
                .setImage(doctor.getDisplayImage())
                .setVideoUrl(doctor.getDisplayVideo());
        int sessionThreshold = isProdOrAlpha ? EnterpriseConfig.DOCTOR_SESSIONS_MIN_THRESHOLD :
                EnterpriseConfig.DOCTOR_SESSIONS_MIN_STAGE_THRESHOLD;
        if (doctor.getNoOfSessionsTaken() != null && doctor.getNoOfSessionsTaken() > sessionThreshold) {
            item.setHeading(doctor.getNoOfSessionsTaken() + " SESSIONS TAKEN");
        }
        EnterpriseUtils.setSubSpecialities(item, doctor, productType);

        String actionUrl = EnterpriseUtils.getDoctorActionUrl(productType, doctor, productCodeTypePair);
        item.setCardAction(new Action(actionUrl, ActionType.NAVIGATION))
                .setAction(new Action(actionUrl, "Know More", ActionType.NAVIGATION));
        return item;
    }

    public static Action getEnterpriseCLPAction() {
        return new Action("curefit://enterpriseclp", "", ActionType.NAVIGATION);
    }

    public static Action getOnePassMemberUrlAction(UserContext userContext, String title) {
        if (!AppUtil.isWeb(userContext) && userContext.getSessionInfo().getAppVersion() < AppUtil.ENTERPRISE_DIRECT_ONEPASS_MEMBERSHIP_PROGRAM__ROLL_OUT_APP_VERSION) {
            return getOnePassUnsupportedAction(title);
        }
        return new Action(EnterpriseConfig.ONEPASS_MEMBER_URL, title, ActionType.NAVIGATION);
    }


    public static Action getOnePassUnsupportedAction(String title) {
        Action unPauseAction = new Action(ActionType.SHOW_ALERT_MODAL, title);
        HashMap<String, Object> meta = new HashMap<>();
        meta.put("title", "Update the app");
        meta.put("subTitle", "This action is not supported in the current app version. Please update the app.");
        List<Action> metaAction = List.of(new Action(ActionType.EMPTY_ACTION, "OK"));
        meta.put("actions", metaAction);
        HashMap<String, Object> subInnerMeta = new HashMap();
        subInnerMeta.put("fontSize", 14);
        HashMap<String, Object> subOuterMeta = new HashMap();
        subOuterMeta.put("subTitleStyle", subInnerMeta);
        meta.put("meta", subOuterMeta);
        unPauseAction.setMeta(meta);
        return unPauseAction;
    }
    public static long dateDiffInDays(Date fromDate, Date toDate) {
        long difference = (toDate.getTime() - fromDate.getTime()) / 86400000;
        return Math.abs(difference);
    }

    public static Pair<BigDecimal, BigDecimal> getPerSessionPrices(Pair<BigDecimal, BigDecimal> prices,
                                                                   long totalSessions) {
        BigDecimal left = prices.getLeft();
        BigDecimal right = prices.getRight();
        if (left != null) {
            left = left.divide(BigDecimal.valueOf(totalSessions), RoundingMode.HALF_EVEN);
        }
        if (right != null) {
            right = right.divide(BigDecimal.valueOf(totalSessions), RoundingMode.HALF_EVEN);
        }
        return Pair.of(left, right);
    }

    public static boolean verifyUserCultLiveMembership(UserContext userContext, ServiceInterfaces interfaces) throws Exception {
        String userId = userContext.getUserProfile()
                .getUserId();
        CorporateDetails corporateDetails = interfaces.getEnterpriseClient()
                .getCorporateWithBenefits(userId);
        if (corporateDetails == null) {
            return false;
        }

        for (CorporateBenefits benefit : corporateDetails.getBenefits()) {
            if(benefit.getProgramType().equals(ProgramType.CULT_LIVE) || benefit.getProgramType().equals(ProgramType.COUPON_BASED_CULT_LIVE)) {
                return true;
            }
            if(EnterpriseConfig.offeredCultLiveProgramTypes.contains(benefit.getProgramType())) {
                Pair<Integer, Integer> programConsumption = getUserConsumptions(userContext, interfaces, benefit.getDetails().getOfferId());
                if (programConsumption != null && programConsumption.getRight() > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    public static CorporateBenefits getCultLiveBenefit(UserContext userContext, ServiceInterfaces interfaces) {
        String userId = userContext.getUserProfile()
                .getUserId();
        CorporateDetails corporateDetails = interfaces.getEnterpriseClient()
                .getCorporateWithBenefits(userId);
        if (corporateDetails == null) {
            log.info("No corporate found for userId {}", userId);
            return null;
        }
        for (CorporateBenefits benefit : corporateDetails.getBenefits()) {
            ProgramType programType = benefit.getProgramType();
            if (programType == ProgramType.CULT_LIVE || programType == ProgramType.DISCOUNTED_CULT_LIVE || programType == ProgramType.COUPON_BASED_CULT_LIVE) {
                return benefit;
            }
        }
        return null;
    }

    public static CorporateBenefits getWellnessBenefit(UserContext userContext, ServiceInterfaces interfaces) {
        String userId = userContext.getUserProfile()
                .getUserId();
        CorporateDetails corporateDetails = interfaces.getEnterpriseClient()
                .getCorporateWithBenefits(userId);
        if(corporateDetails == null) {
            return null;
        }
        for (CorporateBenefits benefit: corporateDetails.getBenefits()) {
            ProgramType programType = benefit.getProgramType();
            if(programType == ProgramType.ONLINE_DIETICIAN || programType == ProgramType.THERAPY || programType == ProgramType.DOCTOR_CONSULTATION) {
                return benefit;
            }
        }
        return null;
    }

    public static List<BannerItem> getOfferBanners(UserContext userContext, ServiceInterfaces interfaces) {
        List<BannerItem> items = new ArrayList<>();
        String userId = userContext.getUserProfile()
                .getUserId();
        CorporateDetails corporateDetails = interfaces.getEnterpriseClient()
                .getCorporateWithBenefits(userId);
        if (corporateDetails == null) {
            log.info("BannerCarouselWidget No corporate found for userId {}", userId);
            return items;
        }

        for (CorporateBenefits benefit : corporateDetails.getBenefits()) {
            if (benefit.getProgramType() != ProgramType.DISCOUNTED_MISC) {
                continue;
            }
            ProgramDetails detail = benefit.getDetails();
            for (String banner : detail.getBanners()) {
                ContentMetric contentMetric = new ContentMetric();
                contentMetric.setContentUrl(banner);
                BannerItem item = new BannerItem();
                item.setOfferId(detail.getOfferId());
                item.setImage(banner);
                item.setStatus(Status.LIVE);
                item.setContentMetric(contentMetric);
                item.setAction(new Action(detail.getActionUrl(), ActionType.NAVIGATION));
                items.add(item);
            }
        }
        return items;
    }

    public static Pair<Integer, Integer> getUserConsumptions(UserContext userContext, ServiceInterfaces interfaces,
                                                             String offerId) throws Exception {
        String userId = userContext.getUserProfile()
                .getUserId();
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(userId);
        userInfo.setAppTenant(Tenant.CUREFIT_APP);
        userInfo.setDeviceId(userContext.getSessionInfo()
                .getDeviceId());
        UserConsumptionDetailsRequest request = new UserConsumptionDetailsRequest();
        request.setUserInfo(userInfo);
        request.setOfferId(offerId);
        UserConsumptionDetailsResponse consumptionDetails = interfaces.getOfferService()
                .getUserConsumptionDetails(request);
        if (consumptionDetails == null) {
            log.info("EnterpriseUtils user consumption found null for offer id :: {}", offerId);
            return null;
        }
        log.info("EnterpriseUtils user consumption found for offer id :: {}, max possible consumption -> {}, consumed count -> {}", offerId, consumptionDetails.getMaxPossibleConsumptions(), consumptionDetails.getConsumedSoFar());
        return Pair.of(consumptionDetails.getMaxPossibleConsumptions(), consumptionDetails.getConsumedSoFar());
    }

    public static String getDateDurationCaption(Date startDate, Date endDate) {
        long duration = EnterpriseUtils.dateDiffInDays(startDate, endDate);
        String durationStr;
        if(duration <= 30) {
            durationStr = duration + (duration > 1 ? " days" : "day");
        }
        else {
            int months = (int) Math.floor(duration / 30.0);
            durationStr = months + (months > 1 ? " months" : " month");
        }
        return durationStr;
    }

    public static String getDayDurationCaption(Integer duration) {
        String durationStr;
        if(duration < 30) {
            durationStr = duration + (duration > 1 ? " days" : " day");
        }
        else {
            int months = (int) Math.floor(duration / 30.0);
            durationStr = months + (months > 1 ? " months" : " month");
        }
        return durationStr;
    }

    public static EnterpriseInteractiveClassItem getUserLatestZoomMeeting(UserContext userContext, ServiceInterfaces interfaces, boolean forUpcomingTray) {
        String userId = userContext.getUserProfile().getUserId();

        Meetings meetings = interfaces.getEnterpriseClient().getUpcomingUserMeeting(userId);
        if (meetings == null || meetings.getMeetings() == null || meetings.getMeetings().size() == 0) {
            log.info("EnterpriseInteractiveClass No corporate meeting found for user id {}", userId);
            return null;
        }

        List<MeetingItem> meetingItems = meetings.getMeetings();
        List<MeetingItem> onGoingMeeting = meetingItems.stream().filter(item -> item.getStatus().equals(MeetingStatus.ONGOING)).toList();
        List<MeetingItem> upcomingMeeting = meetingItems.stream().filter(item -> item.getStatus().equals(MeetingStatus.UPCOMING)).toList();

        long currentTimeStamp = System.currentTimeMillis() / 1000;
        Corporate corporate = interfaces.getEnterpriseClient().getCorporate(userId);

        if(onGoingMeeting.size() > 0) {
            long durationInMinute = onGoingMeeting.get(0).getDuration() / 60;
            long diff = currentTimeStamp - onGoingMeeting.get(0).getScheduledTimeEpoch();
            long diffInMinutes = diff / 60;
            if(diffInMinutes > durationInMinute) {
                log.info("EnterpriseInteractiveClass ongoing meeting time exceed end time {}", diffInMinutes);
                return null;
            }
            return new  EnterpriseInteractiveClassItem(onGoingMeeting.get(0), corporate != null ? corporate.getName() : null, userContext, forUpcomingTray);
        }
        else if(upcomingMeeting.size() > 0) {
            long durationInMinute = upcomingMeeting.get(0).getDuration() / 60;
            float diff = upcomingMeeting.get(0).getScheduledTimeEpoch() - currentTimeStamp;
            float diffInHours = diff / 3600;
            if (diffInHours > 0 && diffInHours <= EnterpriseConfig.ZOOM_CALL_WIDGET_SHOW_HOUR_THRESHOLD) {
                return new  EnterpriseInteractiveClassItem(upcomingMeeting.get(0), corporate != null ? corporate.getName() : null, userContext, forUpcomingTray);
            } else if (diffInHours < 0) {
                float diffForOngoing = currentTimeStamp - upcomingMeeting.get(0).getScheduledTimeEpoch();
                float diffInMinutes = diffForOngoing / 60;
                if(diffInMinutes < durationInMinute) {
                    return new  EnterpriseInteractiveClassItem(upcomingMeeting.get(0), corporate != null ? corporate.getName() : null, userContext, forUpcomingTray);
                }
            }
        }
        return null;
    }

    private static Boolean checkProgramExistBenefit(CorporateDetails corporateDetails, ProgramType programType) {
        boolean result = corporateDetails.getBenefits().stream().filter(corporateBenefits -> corporateBenefits.getProgramType() == programType).findFirst().orElse(null) != null;
        log.info("checkProgramExistBenefit Result programType {} Result {}", programType, result);
        return result;
    }

    public static Pair<BigDecimal, BigDecimal> getCultLiveOfferPrices(ServiceInterfaces interfaces, UserContext userContext, String offerId) throws Exception {
        GetOffersResponse offersResponse = interfaces.offerService.getOfferByIds(Collections.singletonList(offerId)).get();
        if (offersResponse == null || MapUtils.isEmpty(offersResponse.getData())) {
            log.info("getCultLiveOfferPrices offer detail found null");
            return null;
        }

        Offer offer = offersResponse.getData().get(offerId);
        UserEntry userEntry = userContext.getUserEntryCompletableFuture().get();
        LiveProductPricesResponse offerResponse = interfaces.offerService.getLivePackPrices(LiveProductDiscountRequest.builder()
                .productIds(offer.getProductIds())
                .orderSource(OrderSource.valueOf(userContext.getSessionInfo().getOrderSource()))
                .userInfo(UserInfo.builder()
                        .userId(userEntry.getId().toString())
                        .appTenant(AppUtil.getTenantFromUserContext(userContext))
                        .build())
                .build());

        if (offerResponse == null) {
            log.info("getCultLiveOfferPrices offer pack fetch request failed");
            return null;
        }

        List<String> productIds = offer.getProductIds();
        Map<String,ProductPrice> offerProductPriceMap = new HashMap<>();
        ProductPrice minProductPrice = null;
        if(CollectionUtils.isNotEmpty(productIds)) {
            List<Product> products = interfaces.getCatalogueService().getProducts(productIds);
            for(Product product: products) {
                ProductPrice price = OfferUtil.getCareProductPrice(product, offerResponse.getPriceMap());
                offerProductPriceMap.put(product.getProductId(), price);
                if(minProductPrice == null) {
                    minProductPrice = price;
                }
                else if(price.getListingPrice().compareTo(minProductPrice.getListingPrice()) < 0) {
                    minProductPrice = price;
                }
            }
        }
        log.info("getCultLiveOfferPrices benefit type -> DISCOUNTED_CULT_LIVE, offerProductPriceMap :: {}", offerProductPriceMap);
        if (minProductPrice != null) {
            return Pair.of(minProductPrice.getMrp(), minProductPrice.getListingPrice());
        }
        return null;
    }

    private static Pair<BigDecimal, BigDecimal> getCareOfferPrices(ServiceInterfaces interfaces, String offerId, CorporateBenefits benefit, UserContext userContext) throws Exception {
        GetOffersResponse offers = interfaces.offerService.getOfferByIds(Collections.singletonList(offerId)).get();
        if (offers == null || MapUtils.isEmpty(offers.getData())) {
            return null;
        }
        Offer offer = offers.getData()
                .get(offerId);
        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        CareProductPricesResponse offerResponse = interfaces.offerService.getCarePackPrices(CareProductDiscountRequest.builder()
                .productIds(offer.getProductIds())
                .cityId(userContext.getUserProfile().getCity().getCityId())
                .source(OrderSource.valueOf(userContext.getSessionInfo().getOrderSource()))
                .userInfo(UserInfo.builder()
                        .userId(user.getId().toString())
                        .phone(user.getPhone())
                        .email(user.getEmail())
                        .deviceId(userContext.getSessionInfo().getDeviceId())
                        .build()).build());
        if(offerResponse == null) {
            log.info("getCareOfferPrices benefit type -> {} :: carePackPrice fetch request failed", benefit.getProgramType());
            return null;
        }

        List<String> productIds = offer.getProductIds();
        if (CollectionUtils.isNotEmpty(productIds)) {
            List<Product> products = interfaces.getCatalogueService()
                    .getProducts(productIds);
            ProductPrice minProductPrice = null;
            Map<String,ProductPrice> offerProductPriceMap = new HashMap<>();
            for(Product product: products) {
                ProductPrice price = OfferUtil.getCareProductPrice(product, offerResponse.getPriceMap());
                offerProductPriceMap.put(product.getProductId(), price);
                if(minProductPrice == null) {
                    minProductPrice = price;
                }
                else if(price.getListingPrice().compareTo(minProductPrice.getListingPrice()) < 0) {
                    minProductPrice = price;
                }
            }
            if(minProductPrice != null) {
                log.info("TRIAL_CARD_WIDGET Benefit Type :: " + benefit.getProgramType() + " \n Offer ID :: " + offer.getOfferId() + " \n Actual Price :: " + minProductPrice.getMrp() + " \n Discounted Price :: " + minProductPrice.getListingPrice() + " \n Product Ids :: " + offer.getProductIds());
                return Pair.of(minProductPrice.getMrp(), minProductPrice.getListingPrice());
            }
            log.info("getCareOfferPrices benefit type -> {} :: offerService found min price null", benefit.getProgramType());
            return null;
        }
        else {
            log.error("getCareOfferPrices benefit type -> {} :: ProductIds not found for offerId {}",benefit.getProgramType(), offer.getOfferId());
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }
    }

    private static Pair<String, ProductPrice> getCareProductIdWithMinOfferPrice(ServiceInterfaces interfaces, String offerId, CorporateBenefits benefit, UserContext userContext) throws Exception {
        GetOffersResponse offers = interfaces.offerService.getOfferByIds(Collections.singletonList(offerId)).get();
        if (offers == null || MapUtils.isEmpty(offers.getData())) {
            return null;
        }
        Offer offer = offers.getData()
                .get(offerId);
        UserEntry user = userContext.getUserEntryCompletableFuture().get();
        CareProductPricesResponse offerResponse = interfaces.offerService.getCarePackPrices(CareProductDiscountRequest.builder()
                .productIds(offer.getProductIds())
                .cityId(userContext.getUserProfile().getCity().getCityId())
                .source(OrderSource.valueOf(userContext.getSessionInfo().getOrderSource()))
                .userInfo(UserInfo.builder()
                        .userId(user.getId().toString())
                        .phone(user.getPhone())
                        .email(user.getEmail())
                        .deviceId(userContext.getSessionInfo().getDeviceId())
                        .build()).build());
        if(offerResponse == null) {
            log.info("getCareOfferPrices benefit type -> {} :: carePackPrice fetch request failed", benefit.getProgramType());
            return null;
        }

        List<String> productIds = offer.getProductIds();
        String minPriceProductId = null;
        if (CollectionUtils.isNotEmpty(productIds)) {
            List<Product> products = interfaces.getCatalogueService()
                    .getProducts(productIds);
            ProductPrice minProductPrice = null;
            Map<String,ProductPrice> offerProductPriceMap = new HashMap<>();
            for(Product product: products) {
                ProductPrice price = OfferUtil.getCareProductPrice(product, offerResponse.getPriceMap());
                offerProductPriceMap.put(product.getProductId(), price);
                if(minProductPrice == null) {
                    minProductPrice = price;
                    minPriceProductId = product.getProductId();
                }
                else if(price.getListingPrice().compareTo(minProductPrice.getListingPrice()) < 0) {
                    minProductPrice = price;
                    minPriceProductId = product.getProductId();
                }
            }
            log.info("getCareOfferPrices benefit type -> {} :: offerService Price Map :: {}", benefit.getProgramType(), offerProductPriceMap);
            if(minProductPrice != null) {
                log.info("TRIAL_CARD_WIDGET Benefit Type :: " + benefit.getProgramType() + " \n Offer ID :: " + offer.getOfferId() + " \n Actual Price :: " + minProductPrice.getMrp() + " \n Discounted Price :: " + minProductPrice.getListingPrice() + " \n Product Ids :: " + offer.getProductIds());
                return Pair.of(minPriceProductId, minProductPrice);
            }
            log.info("getCareOfferPrices benefit type -> {} :: offerService found min price null", benefit.getProgramType());
            return null;
        }
        else {
            log.error("getCareOfferPrices benefit type -> {} :: ProductIds not found for offerId {}",benefit.getProgramType(), offer.getOfferId());
            return null;
        }
    }

    private static FitnessPack getPlayProgramPackForDuration(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        String offerId = benefit.getDetails().getOfferId();
        GetOffersResponse offers = interfaces.offerService.getOfferByIds(Collections.singletonList(offerId)).get();
        if (offers == null || MapUtils.isEmpty(offers.getData())) {
            log.info("getPlayProgramPackForDuration program offer found null");
            return null;
        }
        List<FitnessPack> playPackList = interfaces.catalogueService.getNewPlayPacksByCity(userContext.getUserProfile().getCity().getCityId(), userContext.getUserProfile().getUserId());
        List<FitnessPack> activePlayPackList = playPackList.stream().filter(pack -> pack.getIsActive()
        && (pack.getVisibility() != null && pack.getVisibility().contains("app"))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activePlayPackList)) {
            log.info("getPlayProgramPackForDuration play program not found any active packs for city: " + userContext.getUserProfile().getCity().getName() + ", id:  " + userContext.getUserProfile().getCity().getCityId());
            return null;
        }
        activePlayPackList.sort(Comparator.comparing(FitnessPack::getDuration));
        long benefitDuration;
        FitnessPack durationProduct = null;
        if (benefit.getDurationDays() == null || benefit.getDurationDays() == 0) {
            benefitDuration = 0;
        } else {
            benefitDuration = Math.round(Math.floor(benefit.getDurationDays().floatValue() / 30));
            durationProduct = activePlayPackList.stream().filter(product -> Math.floor(product.getDuration().floatValue() / 30) == benefitDuration).findFirst().orElse(null);
        }
        if (durationProduct == null) {
            long closestDeviation = Integer.MAX_VALUE;
            for(FitnessPack product: activePlayPackList) {
                long deviation = benefitDuration == 0 ? product.getDuration() : Math.abs(benefit.getDurationDays() - product.getDuration());
                if (deviation < closestDeviation) {
                    closestDeviation = deviation;
                    durationProduct = product;
                }
            }
        }
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(activePlayPackList)) {
            //logic for remove 18month pack from top
            int distance = (int) activePlayPackList.stream().filter(pack -> pack.getDuration() >= 545).count();
            if (distance > 0) { // if 18 months pack exists in list
                Collections.rotate(activePlayPackList, distance * -1); // to rotate in reverse manner
            }
        }

        if (durationProduct != null) {
            PlayProductPricesResponse packProductPricesResponse = (PlayProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.PLAY, userContext, Collections.singletonList(durationProduct.getProductId()), userContext.getUserProfile().getCity().getCityId(), null
            ).get();
            ProductPrice offerPrice = getPlayOfferDetails(durationProduct, packProductPricesResponse);
            durationProduct.setPrice(offerPrice);
            return durationProduct;
        }
        return null;
    }

    private static OfflineFitnessPack getEliteProgramPackForDuration(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        String offerId = benefit.getDetails().getOfferId();
        GetOffersResponse offers = interfaces.offerService.getOfferByIds(Collections.singletonList(offerId)).get();
        if (offers == null || MapUtils.isEmpty(offers.getData())) {
            log.info("getEliteProgramPackForDuration pro program offer found null");
            return null;
        }
        UserProfile userProfile = userContext.getUserProfile();
        List<OfflineFitnessPack> fitnessPacks = userContext.getRequestCache().getNonAugmentedPackList(
            ProductType.FITNESS, ProductSubType.GENERAL, false,
            userContext, null
        );

        if (CollectionUtils.isEmpty(fitnessPacks)) {
            log.info("getEliteProgramPackForDuration elite program not found any active packs");
            return null;
        }
        fitnessPacks.sort(Comparator.comparing(p -> p.getProduct().getDurationInDays()));
        long benefitDuration;
        OfflineFitnessPack durationPack = null;
        if (benefit.getDurationDays() == null || benefit.getDurationDays() == 0) {
            benefitDuration = 0;
        } else {
            benefitDuration = Math.round(Math.floor(benefit.getDurationDays().floatValue() / 30));
            durationPack = fitnessPacks.stream()
                    .filter(pack -> !SkuPackUtil.isAllIndiaPack(pack.getProduct().getBenefits()))
                    .filter(pack -> Math.floor(pack.getProduct().getDurationInDays().floatValue() / 30) == benefitDuration).findFirst().orElse(null);
        }
        if (durationPack == null) {
            double closestDeviation = Integer.MAX_VALUE;
            for(OfflineFitnessPack pack: fitnessPacks) {
                double deviation = benefitDuration == 0 ? pack.getProduct().getDurationInDays() : Math.abs(benefit.getDurationDays() - pack.getProduct().getDurationInDays());
                if (deviation < closestDeviation) {
                    closestDeviation = deviation;
                    durationPack = pack;
                }
            }
        }

        if (durationPack != null) {
            SegmentSet<String> userPlatformSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                    RequestType.PLATFORM_SEGMENTS,
                    userContext).get();
            UserEntry user = userContext.getUserEntryCompletableFuture().get();
            UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
            String callSource = AppUtil.callSource(userContext.getSessionInfo().getApiKey(), interfaces.apiKeyService);


            CultProductPricesResponse cultPackProductPricesResponse = (CultProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.FITNESS, userContext, Collections.singletonList(durationPack.getId()), userProfile.getCity().getCityId(), null
            ).get();
            ProductPrice offerPrice = getCultFitOfferDetails(durationPack, cultPackProductPricesResponse);
            durationPack.getPrice().setListingPrice(BigDecimal.valueOf(offerPrice.getListingPrice().longValue()));
            durationPack.getPrice().setMrp(BigDecimal.valueOf(offerPrice.getMrp().longValue()));
            return durationPack;
        }
        return null;
    }

    public static OfflineFitnessPack getProProgramPackForDuration(ServiceInterfaces interfaces, UserContext userContext, CorporateBenefits benefit) throws Exception {
        String offerId = benefit.getDetails().getOfferId();
        GetOffersResponse offers = interfaces.offerService.getOfferByIds(Collections.singletonList(offerId)).get();
        if (offers == null || MapUtils.isEmpty(offers.getData())) {
            log.info("getProProgramPackForDuration pro program offer found null");
            return null;
        }
        boolean isWeb = AppUtil.isWeb(userContext);
        UserProfile userProfile = userContext.getUserProfile();
        String userId = userProfile.getUserId();

        List<OfflineFitnessPack> gymfitPacks = userContext.getRequestCache().getNonAugmentedPackList(
                ProductType.GYMFIT_FITNESS_PRODUCT, ProductSubType.GENERAL, false,
                userContext, null
        );
        if (CollectionUtils.isEmpty(gymfitPacks)) {
            log.info("getProProgramPackForDuration pro program not found any active packs");
            return null;
        }
        gymfitPacks.sort(Comparator.comparing(p -> p.getProduct().getDurationInDays()));
        long benefitDuration;
        OfflineFitnessPack durationProduct = null;

        if(benefit.getDurationDays() == null || benefit.getDurationDays() == 0) {
            benefitDuration = 0;
        } else {
            benefitDuration = Math.round(Math.floor(benefit.getDurationDays().floatValue() / 30));
            durationProduct = gymfitPacks.stream().filter(pack -> Math.round(Math.floor(pack.getProduct().getDurationInDays() / 30)) == benefitDuration).findFirst().orElse(null);
        }

        if (durationProduct == null) {
            long closestDeviation = Integer.MAX_VALUE;
            for(OfflineFitnessPack product: gymfitPacks) {
                long deviation = (long) (benefitDuration == 0 ? product.getProduct().getDurationInDays() : Math.abs(benefit.getDurationDays() - product.getProduct().getDurationInDays()));
                if (deviation < closestDeviation) {
                    closestDeviation = deviation;
                    durationProduct = product;
                }
            }
        }

        if (durationProduct != null) {

            SegmentSet<String> userPlatformSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                    RequestType.PLATFORM_SEGMENTS,
                    userContext).get();

            UserEntry user = userContext.getUserEntryCompletableFuture().get();
            UserInfo userInfo = new UserInfo(userContext.getUserProfile().getUserId(), userContext.getSessionInfo().getDeviceId(), user.getPhone(), user.getEmail(), user.getWorkEmail(), Tenant.CUREFIT_APP);
            // TODO: pass specific centerID if required
            GymFitProductPricesResponse gymFitProductPricesResponse = (GymFitProductPricesResponse) userContext.getRequestCache().getOfferResponse(
                    ProductType.GYMFIT_FITNESS_PRODUCT, userContext, Collections.singletonList(durationProduct.getId()), userContext.getUserProfile().getCity().getCityId(), null
            ).get();
            PackOfferDetails offerDetails = getGymFitOfferDetails(durationProduct, gymFitProductPricesResponse);
            durationProduct.getPrice().setListingPrice(offerDetails.getPrice().getListingPrice());
            return durationProduct;
        }
        return null;
    }

    private static ProductPrice getCultFitOfferDetails(OfflineFitnessPack fitnessPack, CultProductPricesResponse cultProductPricesResponse) {
        ProductPrice productPrice = fitnessPack.getPrice();

        Map<String, ProductPriceResponse> priceResponseMap = cultProductPricesResponse.getPriceMap();
        if( priceResponseMap != null) {
            ProductPriceResponse productOffer = priceResponseMap.get(fitnessPack.getId());
            if (productOffer != null) {
                productPrice.setMrp(BigDecimal.valueOf(productOffer.getProduct().getPrice().getMrp()));
                productPrice.setListingPrice(BigDecimal.valueOf(productOffer.getProduct().getPrice().getSellingPrice()));
            }
        }
        return productPrice;
    }

    private static ProductPrice getPlayOfferDetails(FitnessPack fitnessPack, PlayProductPricesResponse cultProductPricesResponse) {
        ProductPrice productPrice = fitnessPack.getPrice();

        Map<String, ProductPriceResponse> priceResponseMap = cultProductPricesResponse.getPriceMap();
        if( priceResponseMap != null) {
            ProductPriceResponse productOffer = priceResponseMap.get(fitnessPack.getProductId());
            if (productOffer != null) {
                productPrice.setMrp(BigDecimal.valueOf(productOffer.getProduct().getPrice().getMrp()));
                productPrice.setListingPrice(BigDecimal.valueOf(productOffer.getProduct().getPrice().getSellingPrice()));
            }
        }
        return productPrice;
    }

    private static PackOfferDetails getGymFitOfferDetails(OfflineFitnessPack pack, GymFitProductPricesResponse packOffersV3) {
        ProductPrice price = pack.getPrice();
        List <OfferMini> offers = new ArrayList<>();

        Map<String, ProductPriceResponse> priceMap = packOffersV3.getPriceMap();
        if ( priceMap != null) {
            ProductPriceResponse productOffer = priceMap.get(pack.getId());
            if (productOffer != null) {
                // Update Price.
                price.setMrp(BigDecimal.valueOf(productOffer.getProduct().getPrice().getMrp()));
                price.setListingPrice(BigDecimal.valueOf(productOffer.getProduct().getPrice().getSellingPrice()));
                price.setCurrency("INR");

                // Update Offers.
                List<String> productOfferOfferIds = productOffer.getOfferIds();
                for (String productOfferId: productOfferOfferIds) {
                    offers.add(packOffersV3.getOfferMap().get(productOfferId));
                }
            }
        }

        return new PackOfferDetails(price, offers);
    }

    private static BigDecimal getDiscountedPriceFromOffer(OfferDiscountDetails offerDiscountDetails, BigDecimal actualPrice) {
        BigDecimal discounted = actualPrice;
        switch (offerDiscountDetails.getType()) {
            case ABSOLUTE -> discounted = BigDecimal.valueOf(offerDiscountDetails.getValue());

            case FLAT -> discounted = actualPrice.subtract(BigDecimal.valueOf(offerDiscountDetails.getValue()));

            case PERCENTAGE -> {
                BigDecimal discount = actualPrice.multiply(BigDecimal.valueOf(offerDiscountDetails.getValue() / 100D));
                if (offerDiscountDetails.getMaxDiscountAmount() != null) {
                    discount = discount.max(BigDecimal.valueOf(offerDiscountDetails.getMaxDiscountAmount()));
                }
                discounted = actualPrice.subtract(discount);
            }
        }
        return discounted;
    }

    public static String getCultOfferAmountCaption(ServiceInterfaces interfaces, String offerId, CorporateBenefits benefit, BigDecimal actualPrice, BigDecimal offerPrice, boolean isPlayProduct, UserContext userContext) throws Exception {
        GetOffersResponse offers = interfaces.offerService.getOfferByIds(Collections.singletonList(offerId)).get();
        if (offers == null || MapUtils.isEmpty(offers.getData())) {
            return null;
        }
        Offer offer = offers.getData()
                .get(offerId);
        BigDecimal actual = isPlayProduct ? getActivePlayPackPrice(interfaces, benefit, userContext): getActiveCultPackPrice(interfaces, benefit);
        String offerAmountCaption = "";
        OfferDiscountDetails priceDetails = offer.getPriceOfferDetails();
        BigDecimal offerDiscount = (actualPrice != null && offerPrice != null) ? actualPrice.subtract(offerPrice) : null;
        switch (priceDetails.getType()) {
            case ABSOLUTE -> {
                BigDecimal discount = (offerDiscount != null) ? offerDiscount : actual.subtract(BigDecimal.valueOf(priceDetails.getValue()));
                offerAmountCaption = "₹" + discount.toBigInteger();
            }
            case FLAT -> offerAmountCaption = "₹" + priceDetails.getValue().intValue();
            case PERCENTAGE -> offerAmountCaption = priceDetails.getValue().intValue() + "%";
        }
        return offerAmountCaption;
    }

    public static String getCorporateCodeFromRashiEvent(ServiceInterfaces interfaces, UserContext userContext) throws BaseException {
        ArrayList<String> eventNames = new ArrayList<>();
        eventNames.add(EMAIL_DEEP_LINK_CLICKED_EVENT_NAME);
        List<EventEntry> eventEntries = interfaces.userEventClient.getEvents(Long.valueOf(userContext.getUserProfile().getUserId()), eventNames, AppUtil.getAppTenantFromUserContext(userContext));
        log.info("CorpExploreBannerWidget All Event Entry Data :: {}", eventEntries);
        if (eventEntries == null) {
            log.info("CorpExploreBannerWidget found event entry null");
            return null;
        }
        EventEntry eventEntry = eventEntries.stream().filter(entry -> Objects.equals(entry.getName(), EMAIL_DEEP_LINK_CLICKED_EVENT_NAME)).findFirst().orElse(null);
        log.info("CorpExploreBannerWidget Found Event Entry Data :: {}", eventEntry);
        if (eventEntry != null && eventEntry.getBody() != null) {
            return (String) eventEntry.getBody().get(DEEP_LINK_CORP_CODE_PARAMETER_KEY);
        }
        return null;
    }

    public static Corporate getCorporateFromRashiEvent(ServiceInterfaces interfaces, UserContext userContext) throws BaseException {
        String corpCodeFromDeepLink = EnterpriseUtils.getCorporateCodeFromRashiEvent(interfaces, userContext);
        log.info("getCorporateOfUser corp code from DeepLink Event :: {}", corpCodeFromDeepLink);
        if (corpCodeFromDeepLink != null) {
            return interfaces.getEnterpriseClient().getCorpForCultPassCode(corpCodeFromDeepLink);
        }
        return null;
    }

    public static Action getPlayProgramBuyAction(UserContext userContext, FitnessPack fitnessProduct, String title) {
        Action buyAction = new Action(ActionUtil.playPack(fitnessProduct, "enterpriseclp"), ActionType.NAVIGATION);
        if(userContext.getUserProfile().getCity() != null && userContext.getUserProfile().getCity().getCityId() != null) {
            if(title != null) {
                buyAction.setTitle(title);
            }
            return buyAction;
        }

        Action action = new Action(null, ActionType.SHOW_CHANGE_CITY);
        action.setMeta(new ProEliteActionPurchaseMeta(false, true));
        action.setCompletionAction(buyAction);
        if(title != null) {
            action.setTitle(title);
        }
        return action;
    }

    public static Action getProProgramBuyAction(UserContext userContext, OfflineFitnessPack fitnessProduct, String title) {
        Action buyAction = new Action(
                ActionUtil.getPackDetailAction(fitnessProduct.getProductType(), fitnessProduct.getId(), null, null, null, userContext.getSessionInfo().getUserAgent()),
                ActionType.NAVIGATION);
        if(userContext.getUserProfile().getCity() != null && userContext.getUserProfile().getCity().getCityId() != null) {
            if(title != null) {
                buyAction.setTitle(title);
            }
            return buyAction;
        }

        Action action = new Action(null, ActionType.SHOW_CHANGE_CITY);
        action.setMeta(new ProEliteActionPurchaseMeta(false, true));
        action.setCompletionAction(buyAction);
        if(title != null) {
            action.setTitle(title);
        }
        return action;
    }

    public static Action getEliteProgramBuyAction(UserContext userContext, OfflineFitnessPack cultPack, String title) {
        String url = ActionUtil.getPackDetailAction(cultPack.getProductType(), cultPack.getId(), true, "enterpriseclp", null, userContext.getSessionInfo().getUserAgent());
        Action buyAction = new Action(url, ActionType.NAVIGATION);
        if(userContext.getUserProfile().getCity() != null && userContext.getUserProfile().getCity().getCityId() != null) {
            if(title != null) {
                buyAction.setTitle(title);
            }
            return buyAction;
        }
        Action action = new Action(null, ActionType.SHOW_CHANGE_CITY);
        action.setCompletionAction(buyAction);
        action.setMeta(new ProEliteActionPurchaseMeta(false, true));
        if(title != null) {
            action.setTitle(title);
        }
        return action;
    }

    private static BigDecimal getActiveCultPackPrice(ServiceInterfaces interfaces, CorporateBenefits benefit) throws BaseException {
        long diffInDays = EnterpriseUtils.dateDiffInDays(benefit.getStartDate(), benefit.getEndDate());
        List<OfflineFitnessPack> fitnessPacks = interfaces.getCatalogueServicePMS()
                .getCultPacks(true); // TODO: Fetch from PMS after moving enterprise packs
        if (CollectionUtils.isEmpty(fitnessPacks)) {
            log.error("No active cult pack found");
            return BigDecimal.ZERO;
        }
        BigDecimal price = BigDecimal.ZERO;
        long closestDeviation = Integer.MAX_VALUE;

        for (OfflineFitnessPack fitnessPack : fitnessPacks) {
            long deviation = getDeviationFromExpectedDuration(diffInDays, fitnessPack);
            if (deviation < closestDeviation) {
                closestDeviation = deviation;
                price = fitnessPack.getPrice()
                        .getListingPrice();
            }
        }
        return price;
    }

    private static BigDecimal getActivePlayPackPrice(ServiceInterfaces interfaces, CorporateBenefits benefit, UserContext userContext) throws BaseException {
        long diffInDays = EnterpriseUtils.dateDiffInDays(benefit.getStartDate(), benefit.getEndDate());
        List<FitnessPack> cultPacks = interfaces.getCatalogueService().getNewPlayPacksByCity(userContext.getUserProfile().getCity().getCityId(), userContext.getUserProfile().getUserId());
        cultPacks = cultPacks.stream().filter(pack -> pack.getVisibility() != null && pack.getVisibility().contains("app")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cultPacks)) {
            log.error("No active play pack found");
            return BigDecimal.ZERO;
        }
        BigDecimal price = BigDecimal.ZERO;
        long closestDeviation = Integer.MAX_VALUE;

        for (FitnessPack cultPack : cultPacks) {
            long deviation = getDeviationFromExpectedDuration(diffInDays, cultPack);
            if (deviation < closestDeviation) {
                closestDeviation = deviation;
                price = cultPack.getPrice()
                        .getListingPrice();
            }
        }
        return price;
    }

    private static long getDeviationFromExpectedDuration(long diffInDays, FitnessPack cultPack) {
        long cultDuration;
        if (cultPack.getDuration() != null) {
            cultDuration = cultPack.getDuration();
        } else if (cultPack.getNumDays() != null) {
            cultDuration = cultPack.getNumDays();
        } else {
            cultDuration = Integer.MAX_VALUE;
        }
        return Math.abs(diffInDays - cultDuration);
    }

    private static long getDeviationFromExpectedDuration(long diffInDays, OfflineFitnessPack fitnessPack) {
        int cultDuration;
        if (fitnessPack.getProduct().getDurationInDays() != null) {
            cultDuration = fitnessPack.getProduct().getDurationInDays().intValue();
        } else {
            cultDuration = Integer.MAX_VALUE;
        }
        return Math.abs(diffInDays - cultDuration);
    }

    private static String getDoctorActionUrl(ProductType productType, BasicDoctorResponse doctor, Pair<String,
            String> productCodeTypePair) {
        String baseUrl = (productType == ProductType.THERAPY) ? EnterpriseConfig.DOCTOR_FLUTTER_URL :
                EnterpriseConfig.DOCTOR_REACT_URL;
        String actionUrl = baseUrl + "&doctorId=" + doctor.getId() + "&productId=" + productCodeTypePair.getLeft() +
                "&doctorType=" + productCodeTypePair.getRight();
        Optional<DoctorCenterMappingResponse> centerOptional = doctor.getDoctorCenterMapping()
                .stream()
                .findFirst();
        if (centerOptional.isPresent()) {
            actionUrl += "&centerId=" + centerOptional.get()
                    .getCenterId();
        }
        if (doctor.getSlugValue() != null) {
            actionUrl += "&slugValue=" + doctor.getSlugValue();
        }
        return actionUrl;
    }

    private static Pair<String, String> getProductCodeTypePair(BasicDoctorResponse doctor, String doctorType) {
        return doctor.getConsultationProducts()
                .stream()
                .filter(product -> "ONLINE".equals(product.getConsultationMode()) && doctorType.equals(product.getSubServiceType()))
                .findFirst()
                .map(product -> Pair.of(product.getProductCode(), product.getSubServiceType()))
                .orElse(null);
    }

    private static void setSubSpecialities(EnterpriseDoctorWidgetItem item, BasicDoctorResponse doctor,
                                           ProductType productType) {
        Set<String> attributes;
        switch (productType) {
            case NUTRITIONIST_CONSULTATION -> attributes = doctor.getCuisines();
            case THERAPY -> attributes = doctor.getSubSpecialities();
            default -> attributes = doctor.getLanguagesSpoken();
        }
        if (CollectionUtils.isNotEmpty(attributes)) {
            attributes.forEach(item::addSubSpeciality);
        }
    }

    private static Pair<Date, Date> getActiveCultLiveMembershipDates(UserContext userContext) throws Exception {
        CultSummary summary = (CultSummary) userContext.getRequestCache().getRequestFuture(RequestType.CULT_MIND_SUMMARY, userContext).get();
        MembershipDetails membershipSummary = null;
        if (summary != null && summary.getMembershipSummary().getCurrent().getCult() != null) {
            membershipSummary = summary.getMembershipSummary().getCurrent().getCult();
        } else if (summary != null && summary.getMembershipSummary().getUpcoming().getCult() != null) {
            membershipSummary = summary.getMembershipSummary().getUpcoming().getCult();
        }
        log.info("DISCOUNTED_CULT_LIVE found membership :: {}", membershipSummary);
        if(membershipSummary != null) {
            try {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                return Pair.of(df.parse(membershipSummary.getStartDate()), df.parse(membershipSummary.getEndDate()));
            } catch (ParseException e) {
                e.printStackTrace();
                log.error("getActiveCultLiveMembershipDates Date parse get failed");
            }
        }
        return null;
    }

    public static TimeOfDayType getTimesOfDay(String timezone) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        calendar.setTime(new Date());
        int timeOfDay = calendar.get(Calendar.HOUR_OF_DAY);
        if (timeOfDay >= 5 && timeOfDay <= 12) {
            return TimeOfDayType.MORNING;
        }
        else if(timeOfDay >= 13 && timeOfDay <= 15) {
            return TimeOfDayType.AFTERNOON;
        }
        else if(timeOfDay >= 16 && timeOfDay <= 20) {
            return TimeOfDayType.EVENING;
        }
        return TimeOfDayType.NIGHT;
    }

    public static boolean isWeekendDay(String timezone) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        calendar.setTime(new Date());
        int day = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        return  day == DayOfWeek.SUNDAY_VALUE || day == DayOfWeek.SATURDAY_VALUE;
    }

    public static AccordionSectionItem getCultPassCorpAccordionSection() {
        return AccordionSectionItem.builder()
            .title("cultpass CORP").icon("CORP")
            .action(new Action("curefit://enterpriseclp", ActionType.NAVIGATION))
            .subtitle("View All Benefits")
            .build();
    }

}

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class ProEliteActionPurchaseMeta {
    boolean canSkip;
    boolean fromFlutter;

    public ProEliteActionPurchaseMeta(boolean canSkip, boolean fromFlutter) {
        this.canSkip = canSkip;
        this.fromFlutter = fromFlutter;
    }
}