package com.curefit.cfapi.service;

import com.curefit.catalogv1.services.spi.ICatalogueService;
import com.curefit.center.client.LocalityService;
import com.curefit.center.dtos.*;
import com.curefit.center.enums.CenterStatus;
import com.curefit.center.enums.SkuName;
import com.curefit.cfapi.cache.CultUnboundLocalityConfigService;
import com.curefit.cfapi.config.CircuitBreakerConfiguration;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.service.location.GoogleAPIKeyOwner;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.view.viewbuilders.fitness.LocalitySelectorPageViewBuilder;
import com.curefit.cfapi.view.viewmodels.fitness.LocalitySelectorPageView;
import com.curefit.cult.models.CultCenter;
import com.curefit.cult.models.CultUserPreference;
import com.curefit.cult.models.PreferenceSetting;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.location.models.City;
import com.curefit.membership.client.MembershipFilter;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.types.MembershipType;
import com.curefit.membership.types.Status;
import com.curefit.rashi.client.UserAttributesClient;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.curefit.cfapi.config.CircuitBreakerConfiguration.CircuitBreakerName.CENTER_SERVICE;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LocalitySelectorService {
    ServiceInterfaces serviceInterfaces;
    LocalitySelectorPageViewBuilder localitySelectorPageViewBuilder;
    CircuitBreakerConfiguration circuitBreakerConfiguration;
    LocalityService localityService;
    ICatalogueService catalogueService;
    UserAttributesClient userAttributesClient;
    AppConfigCache appConfigCache;
    CultUnboundLocalityConfigService cultUnboundLocalityConfigService;
    
    @Autowired
    public LocalitySelectorService(
            ServiceInterfaces serviceInterfaces,
            LocalitySelectorPageViewBuilder localitySelectorPageViewBuilder,
            CircuitBreakerConfiguration circuitBreakerConfiguration,
            LocalityService localityService,
            ICatalogueService catalogueService,
            UserAttributesClient userAttributesClient,
            AppConfigCache appConfigCache,
            CultUnboundLocalityConfigService cultUnboundLocalityConfigService
    ) {
        this.serviceInterfaces = serviceInterfaces;
        this.localitySelectorPageViewBuilder = localitySelectorPageViewBuilder;
        this.circuitBreakerConfiguration = circuitBreakerConfiguration;
        this.localityService = localityService;
        this.catalogueService = catalogueService;
        this.userAttributesClient = userAttributesClient;
        this.appConfigCache = appConfigCache;
        this.cultUnboundLocalityConfigService = cultUnboundLocalityConfigService;
    }

    public LocalitySelectorPageView getLocalitySelectorPageView(UserContext userContext, Map<String, String> queryParams) {
        try {
            List<SkuName> skuNames = new ArrayList<>();
            Map<String, String> customHeaders = new HashMap<>();
            List<CenterStatus> centerStatuses = new ArrayList<>();
            Boolean isWeb = AppUtil.isWeb(userContext);
            Boolean isBookingFlow = queryParams.containsKey("pageFrom") && Objects.equals(queryParams.get("pageFrom"), "CLASS_BOOKING");
            Boolean isLocationSelectionFlow = queryParams.containsKey("pageFrom") && Objects.equals(queryParams.get("pageFrom"), "LOCATION_ACCESS_SELECT");
            Boolean isOnlyOnePass = queryParams.containsKey("centerType") && Objects.equals(queryParams.get("centerType"), "ONEPASS");
            centerStatuses.add(CenterStatus.ACTIVE);
            String cityId = queryParams.getOrDefault("cityId", userContext.getUserProfile().getCity().getCityId());
            String filterWorkoutId = queryParams.getOrDefault("filterWorkoutId", null);
            String isCultUnbound = queryParams.getOrDefault("isCultUnbound", "false");
            CenterSearchFilters.CenterType centerTypeFilter = null;
            if (isBookingFlow) {
                centerTypeFilter = CenterSearchFilters.CenterType.CULT_CENTER;
            }
            if (isOnlyOnePass) {
                centerTypeFilter = CenterSearchFilters.CenterType.ONEPASS;
            }
            CenterSearchFilters.CenterType centerType = centerTypeFilter;
            List<LocalityCenterCountEntry> localityCenterCountEntries = circuitBreakerConfiguration.get(CENTER_SERVICE).executeCallable(() -> localityService.getAllLocalitiesWithCenterCounts(skuNames, cityId, centerStatuses, centerType, customHeaders, null));

            if ((userContext.getSessionInfo().getAppVersion() >= 10.87f && Objects.equals(isCultUnbound, "true")) || StringUtils.isNotBlank(filterWorkoutId)) {
                List<Integer> cultUnboundConfigData = cultUnboundLocalityConfigService.getPageConfigData();
                localityCenterCountEntries = localityCenterCountEntries.stream().filter(entry -> cultUnboundConfigData.contains(entry.getLocalityId().intValue())).toList();
            }

            String currentLocationName = null;
            City cityData;

            if(userContext.getSessionInfo().getLat() != null && userContext.getSessionInfo().getLon() != null) {
                // Don't fetch location name for Members. (Cost Reduction Measure)
                if (!AppUtil.isUserActiveCultpassMember(userContext, serviceInterfaces.environmentService)) {
                    currentLocationName = serviceInterfaces.localityProviderService.getLocationName(userContext.getSessionInfo().getLat(), userContext.getSessionInfo().getLon(), serviceInterfaces, GoogleAPIKeyOwner.LOCALITY_SELECTOR_PAGE);
                }
            }

            Boolean isUserMember = false;
            List<String> favoriteCenterNames = new ArrayList<>();
            List<Integer> favouriteCenterIds = new ArrayList<>();
            if (isBookingFlow) {
                String userId = userContext.getUserProfile().getUserId();
                List<String> benefits = List.of("CULT");
                List<Membership> membershipList = serviceInterfaces.membershipService.getCachedMembershipsForUser(userId, "curefit", MembershipFilter.builder().benefits(benefits).status(Status.PURCHASED).status(Status.PAUSED).build()).get();
                membershipList = membershipList.stream().filter(membership -> membership.getType() != MembershipType.COMPLIMENTARY).collect(Collectors.toList());
                if (!membershipList.isEmpty()) {
                    isUserMember = true;
                    Integer cultCityId = userContext.getUserProfile().getCity().getCultCityId();
                    CultUserPreference cultUserPreference = serviceInterfaces.cultService.getPreferenceByKey(userId, Long.valueOf(cultCityId), "USER_BOOKING_V2_FAVOURITE_CENTER");
                    List<PreferenceSetting> settings = cultUserPreference.getSettings();
                    if (!settings.isEmpty()) {
                        PreferenceSetting setting = settings.get(0);
                        if (setting != null) {
                            List<Integer> centerIds = (List<Integer>) setting.getValue();
                            List<String> favoriteCenterProductIds = this.getCultCenterProductIds(centerIds);
                            List<CultCenter> centers = this.catalogueService.getCultCenters(favoriteCenterProductIds);
                            boolean save = false;
                            if (centers != null && !centers.isEmpty()) {
                                for (CultCenter center: centers) {
                                    if (center != null && !Objects.equals(center.getState(), "INACTIVE")) {
                                        favoriteCenterNames.add(center.getName());
                                        favouriteCenterIds.add(center.getId().intValue());
                                    } else {
                                        save = true;
                                    }
                                }
                            }
                            if(save){
                                setting.setValue(favouriteCenterIds);
                                settings.set(0, setting);
                                cultUserPreference.setSettings(settings);
                                serviceInterfaces.cultService.savePreferences(
                                        Long.valueOf(cultCityId),
                                        userId, cultUserPreference, "CUREFIT_API",
                                        userContext.getUserProfile().getSubUserId()
                                );
                            }
                        }
                    }
                }
            }

            if (isWeb && !cityId.isEmpty()) {
                cityData = this.serviceInterfaces.cityCache.getCityById(cityId);
            } else {
                cityData = userContext.getUserProfile().getCity();
            }

            return localitySelectorPageViewBuilder.buildView(
                userContext, localityCenterCountEntries, currentLocationName, cityData, isBookingFlow,
                isUserMember, favoriteCenterNames, favouriteCenterIds, isLocationSelectionFlow,
                serviceInterfaces.environmentService, userAttributesClient, serviceInterfaces
            );
        } catch (Exception e){
            log.error(e.getMessage());
            e.printStackTrace();
        }

        return new LocalitySelectorPageView();
    }

    private List<String> getCultCenterProductIds(List<Integer> CultCenterIds) {
        List<String> centerIdsStr = CultCenterIds.stream().map(String::valueOf).collect(Collectors.toList());
        centerIdsStr.replaceAll(centerId -> "CULTCENTER"+centerId);
        return centerIdsStr;
    }

}
