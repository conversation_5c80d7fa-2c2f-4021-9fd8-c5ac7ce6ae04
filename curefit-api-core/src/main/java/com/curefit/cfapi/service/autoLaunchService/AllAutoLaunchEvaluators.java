package com.curefit.cfapi.service.autoLaunchService;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.autoLaunch.AutoLaunchAction;
import com.curefit.cfapi.pojo.feedback.Feedback;
import com.curefit.cfapi.service.autoLaunchService.autoLaunchTriggers.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Service
@Slf4j
public class AllAutoLaunchEvaluators {

    private final List<AutoLaunchBaseService> autoLaunchEvaluators;
    @Autowired
    public AllAutoLaunchEvaluators(
        CfsFormAutoLaunch cfsFormAutoLaunch,
        DeadBaseAutoLaunch deadBaseAutoLaunch,
        DeviceRegistrationAutoLaunch deviceRegistrationAutoLaunch,
        EnergyStreakGameV1AutoLaunch energyStreakGameV1AutoLaunch,
        EnergyStreakGameV2AutoLaunch energyStreakGameV2AutoLaunch,
        FeedbackAutoLaunch feedbackAutoLaunch,
        GoalSettingIntroAutoLaunch goalSettingIntroAutoLaunch,
        GymPtPlusAutoLaunch gymPtPlusAutoLaunch,
        GymExerciseLoggingScreenAutoLaunch gymExerciseLoggingScreenAutoLaunch,
        GymAssessmentAutoLaunch gymAssessmentAutoLaunch,
        NoShowAutoLaunch noShowAutoLaunch,
        NinjaCelebrationAutoLaunch ninjaCelebrationAutoLaunch,
        SquadGoalAutoLaunch squadGoalAutoLaunch,
        StreakPageAutoLaunch streakPageAutoLaunch,
        NewBadgeAutoLaunch newBadgeAutoLaunch,
        UserStreakRepairAutoLaunch userStreakRepairAutoLaunch
    ){
        autoLaunchEvaluators = List.of(
            cfsFormAutoLaunch,
            deadBaseAutoLaunch,
            deviceRegistrationAutoLaunch,
            feedbackAutoLaunch,
            noShowAutoLaunch,
            energyStreakGameV1AutoLaunch,
            energyStreakGameV2AutoLaunch,
            goalSettingIntroAutoLaunch,
            gymAssessmentAutoLaunch,
            gymPtPlusAutoLaunch,
            gymExerciseLoggingScreenAutoLaunch,
            ninjaCelebrationAutoLaunch,
            squadGoalAutoLaunch,
            streakPageAutoLaunch,
            newBadgeAutoLaunch,
            userStreakRepairAutoLaunch
        );
    }

    public List<AutoLaunchAction> getAllAutoLaunchActions(Feedback feedback, UserContext userContext) throws ExecutionException, InterruptedException {
        List<AutoLaunchAction> launchActions = new ArrayList<>();
        for(AutoLaunchBaseService autoLaunchEvaluator : autoLaunchEvaluators){
            log.debug("Auto launch testing: for auto launch event: {}",autoLaunchEvaluator);
            autoLaunchEvaluator.evaluate(userContext, feedback).ifPresent(launchActions::add);
        }
        return launchActions;
    }

}
