package com.curefit.cfapi.service.autoLaunchService.autoLaunchTriggers;

import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.CfsFormCache;
import com.curefit.cfapi.cache.FeatureStateCache;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.autoLaunch.AutoLaunchAction;
import com.curefit.cfapi.pojo.feedback.Feedback;
import com.curefit.cfapi.pojo.user.UserFormAutoLaunchConfig;
import com.curefit.cfapi.repository.AutoLaunchPageConfigRepository;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.autoLaunchService.AutoLaunchBaseService;
import com.curefit.cfapi.service.autoLaunchService.AutoLaunchEvaluator;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.segmentation.pojo.SegmentUserListEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import static com.curefit.cfapi.constants.Constants.userFormAutoLaunchConfigList;
@Service
@Slf4j
public class CfsFormAutoLaunch extends AutoLaunchBaseService implements AutoLaunchEvaluator {
    CfsFormCache cfsFormCache;
    ServiceInterfaces serviceInterfaces;
    FeatureStateCache featureStateCache;

    private static final String LAST_AUTO_LAUNCH_KEY = "CFS_FORM_LAST_AUTO_LAUNCH_DATE";
    private static final Long autoLaunchInterval = 10 * 60 * 1000L;

    @Autowired
    public CfsFormAutoLaunch(AutoLaunchPageConfigRepository autoLaunchPageConfigRepository, ServiceInterfaces serviceInterfaces, FeatureStateCache featureStateCache, CfsFormCache cfsFormCache) throws ExecutionException, InterruptedException {
        super("CfsFormAutoLaunch", autoLaunchPageConfigRepository, serviceInterfaces, featureStateCache);
        this.cfsFormCache = cfsFormCache;
        this.serviceInterfaces = serviceInterfaces;
        this.featureStateCache = featureStateCache;
    }

    @Override
    public Optional<AutoLaunchAction> evaluate(UserContext userContext, Feedback feedback) throws ExecutionException, InterruptedException {
        super.evaluate(userContext, feedback);
        if (!super.getAutoLaunchConfig().isEnabled()) {
            return Optional.empty();
        }
        if(!shouldAutoLaunch(userContext, LAST_AUTO_LAUNCH_KEY, autoLaunchInterval)){
            return Optional.empty();
        }
        setAutoLaunch(userContext, LAST_AUTO_LAUNCH_KEY);
        try {
            SegmentSet<String> userPlatformSegments = (SegmentSet<String>) userContext.getRequestCache().getRequestFuture(
                    RequestType.PLATFORM_SEGMENTS,
                    userContext).get();

            for (UserFormAutoLaunchConfig userFormAutoLaunchConfig : userFormAutoLaunchConfigList) {
                if (userPlatformSegments.contains(userFormAutoLaunchConfig.getSegmentIdIn()) && (userFormAutoLaunchConfig.getSegmentIdNotIn() == null || !userPlatformSegments.contains(userFormAutoLaunchConfig.getSegmentIdNotIn()))) {
                    Action action = new Action(userFormAutoLaunchConfig.getDeepLink(), ActionType.NAVIGATION);
                    AutoLaunchResult autoLaunchResult = new AutoLaunchResult(action, super.getPriority(), super.getPageId(),userContext, userFormAutoLaunchConfig.getFormId());
                    return Optional.of(autoLaunchResult);
                }
            }

        } catch (Exception e) {
            log.error("Unable trigger cfs form action due to" + e.getMessage());
            serviceInterfaces.exceptionReportingService
                    .reportException(new Exception("Unable trigger cfs form auto launch due to" + e.getMessage()));
        }
        return Optional.empty();
    }

    class AutoLaunchResult extends AutoLaunchAction {

        UserContext userContext;
        String formId;
        public AutoLaunchResult(Action action, Integer priority, String pageId, UserContext userContext, String formId) {
            super(action, priority, pageId);
            this.userContext = userContext;
            this.formId = formId;
        }

        @Override
        public void onExecute() {
            if (userContext.getSessionInfo().getAppVersion() <= 10.93f) {
                String userId = userContext.getUserProfile().getUserId();
                log.info("cfs_autolaunch_testing: Fetched the userId and formId");
                SegmentUserListEntry segmentUserListEntry = new SegmentUserListEntry();
                segmentUserListEntry.setUserId(Long.valueOf(userId));
                log.info("cfs_autolaunch_testing: Successfully set the userId");
                Optional<UserFormAutoLaunchConfig> matchingLaunchConfig = userFormAutoLaunchConfigList.stream()
                        .filter(userFormAutoLaunchConfig -> userFormAutoLaunchConfig.getFormId().equals(formId))
                        .findFirst();
                String segmentName = matchingLaunchConfig.map(UserFormAutoLaunchConfig::getSegmentIdIn).orElse(null);
                if (segmentName == null) {
                    return;
                }
                segmentUserListEntry.setSegmentName(segmentName);
                log.info("cfs_autolaunch_testing: Resetting CfsFormCache for userId:" + userId + ", formId:" + formId);
                //CompletableFuture<Boolean> promise = this.cfsFormCache.purgeActiveCfsFormActionForUser(userId, formId);
                cfsFormCache.purgeUserId(segmentUserListEntry);
            }
        }
    }
}
