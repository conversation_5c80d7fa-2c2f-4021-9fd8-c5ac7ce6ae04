package com.curefit.cfapi.service;

import com.curefit.albus.service.AlbusClient;
import com.curefit.alfred.services.spi.IOrderService;
import com.curefit.alfred.services.spi.IShipmentService;
import com.curefit.base.service.EnvironmentService;
import com.curefit.catalogv1.pms.ICatalogueServicePMS;
import com.curefit.catalogv1.services.spi.ICatalogueService;
import com.curefit.center.client.CenterService;
import com.curefit.center.client.LocalityService;
import com.curefit.center.client.ReviewService;
import com.curefit.center.client.ScheduleService;
import com.curefit.cfapi.builder.vm.BannerBuilder;
import com.curefit.cfapi.builder.vm.WidgetBuilder;
import com.curefit.cfapi.cache.*;
import com.curefit.cfapi.config.CircuitBreakerConfiguration;
import com.curefit.cfapi.config.ConstantsConfig;
import com.curefit.cfapi.config.SOSConfiguration;
import com.curefit.cfapi.mapper.CultsportMapper;
import com.curefit.cfapi.repository.UserWeeklyActivityRepository;
import com.curefit.cfapi.service.CFAnalytics.CFAnalytics;
import com.curefit.cfapi.service.CowinAlert.CowinAlertService;
import com.curefit.cfapi.service.HomePageWidget.HomeScreenWidgetService;
import com.curefit.cfapi.service.chroniccare.ChronicCareServiceHelper;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.service.community.RagnarCommunityService;
import com.curefit.cfapi.service.constello.ConstelloService;
import com.curefit.cfapi.service.culticon.CultIconService;
import com.curefit.cfapi.service.cultsport.*;
import com.curefit.cfapi.service.cultsport.analytics.MixpanelEventService;
import com.curefit.cfapi.service.cultsport.analytics.MoEngageService;
import com.curefit.cfapi.service.cultsport.csBlog.CSBlogService;
import com.curefit.cfapi.service.cultsport.payment.snapmint.CSSnapmintService;
import com.curefit.cfapi.service.fitness.FitnessReportService;
import com.curefit.cfapi.service.fitness.PastActivityService;
import com.curefit.cfapi.service.hamlet.HamletHelper;
import com.curefit.cfapi.service.kudosService.KudosService;
import com.curefit.cfapi.service.location.LocalityProviderService;
import com.curefit.cfapi.service.mealplanner.MealPlannerService;
import com.curefit.cfapi.service.pagecache.ApiResponseCacheService;
import com.curefit.cfapi.service.seo.SEOService;
import com.curefit.cfapi.service.trainerled.TrainerLedService;
import com.curefit.cfs.client.CFSClient;
import com.curefit.commons.sf.CommonConfiguration;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.cult.services.impl.CultServiceImpl;
import com.curefit.cult.services.spi.CultService;
import com.curefit.cult.services.spi.MindService;
import com.curefit.cultsport.feedback.client.FeedbackServiceClient;
import com.curefit.cultsport.user.client.CultsportUserClient;
import com.curefit.diyfs.client.spi.DiyfsService;
import com.curefit.eatapi.services.spl.FoodRecommendationServiceInterface;
import com.curefit.eatapi.services.spl.IEatRedisService;
import com.curefit.eatapi.services.spl.IKioskDemandService;
import com.curefit.ehr.client.EHRClient;
import com.curefit.fitcash.services.FitcashServiceImpl;
import com.curefit.foodway.client.FoodwayAppClient;
import com.curefit.fuse.client.FuseDiagnosticClientV2;
import com.curefit.gearvault.implementation.GearvaultClient;
import com.curefit.gymfit.client.GymfitClient;
import com.curefit.gymfit.client.spi.CenterMachineService;
import com.curefit.gymfit.client.spi.GymOvercrowdingService;
import com.curefit.gymfit.client.spi.TrialUsageService;
import com.curefit.hercules.client.HerculesService;
import com.curefit.iris.services.impl.InAppNotificationsServiceImpl;
import com.curefit.location.service.CityCache;
import com.curefit.location.service.MaxmindService;
import com.curefit.logging.service.service.ILoggingService;
import com.curefit.logging.services.spi.IActivityLoggingService;
import com.curefit.magneto.client.MagnetoCSProductClient;
import com.curefit.magneto.client.MagnetoClient;
import com.curefit.mealplanner.client.service.MealPlannerClient;
import com.curefit.membership.client.MembershipClientImpl;
import com.curefit.metricservice.client.MetricClient;
import com.curefit.microlearning.client.service.MicroLearningClient;
import com.curefit.odin.api.SprinklrTicketService;
import com.curefit.offers.client.OfferService;
import com.curefit.ollivander.client.agent.OllivanderAgentClient;
import com.curefit.ollivander.client.center.OllivanderCenterClient;
import com.curefit.personaltrainer.client.PersonalTrainerServiceClient;
import com.curefit.pms.services.impl.ExchangeService;
import com.curefit.pms.services.spi.IConfigService;
import com.curefit.pms.services.spi.IOfflineFitnessPackService;
import com.curefit.quest.client.spi.QuestClient;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.client.UserAttributesCacheClient;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.client.UserEventClient;
import com.curefit.reportissues.services.*;
import com.curefit.riddler.service.RiddlerCacheService;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.shifu.service.ShifuClient;
import com.curefit.smartdevice.client.services.SmartDeviceClient;
import com.curefit.socialservice.client.impl.SocialServiceClientImpl;
import com.curefit.sportsapi.client.SportsApiClientImpl;
import com.curefit.thirdPartyIntegrations.services.impl.Tata3pService;
import com.curefit.tread.client.TreadClientImpl;
import com.curefit.uas.impl.UASFitnessReportClient;
import com.curefit.uas.impl.UserActivityServiceClient;
import com.curefit.uas.inteface.IUserActivityServiceClient;
import com.curefit.ufs.services.UfsService;
import com.curefit.userservice.client.UserServiceClient;
import com.sugarfit.ambrosia.client.AmbrosiaClient;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.fitness.client.PlanClient;
import com.sugarfit.logging.client.LoggingClient;
import com.sugarfit.sms.client.SMSClient;
import fit.cult.enterprise.client.EnterpriseClient;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.stereotype.Service;
import com.curefit.pms.services.impl.PricingServiceImpl;

import javax.servlet.http.HttpServletRequest;

@Service
@Getter
@Setter
@FieldDefaults(level = AccessLevel.PUBLIC)
public class ServiceInterfaces {
    IEatRedisService eatRedisService;

    FitcashServiceImpl fitcashService;

    OfferService offerService;

    MealPlannerService mealPlannerService;

    ConstelloService constelloService;

    IShipmentService shipmentService;

    ICatalogueService catalogueService;

    ICatalogueServicePMS catalogueServicePMS;

    ILoggingService loggingService;

    CultsportLiveScoreService cultsportLiveScoreService;

    DiyfsService diyfsService;

    CFSClient cfsClient;

    MetricClient metricClient;

    ShifuClient shifuClient;

    IOfflineFitnessPackService offlineFitnessPackService;

    MicroLearningClient microLearningClient;
    MealPlannerClient mealPlannerClient;
    UserSegmentClient userSegmentClient;

    @Qualifier("albusClient")
    AlbusClient albusClient;

    @Qualifier("ptClient")
    AlbusClient ptClient;

    @Qualifier("sugarfitClient")
    AlbusClient sfAlbusClient;

    @Qualifier("transformClient")
    AlbusClient transformClient;

    SMSClient smsClient;

    GymfitClient gymfitClient;

    UfsService ufsService;

    InAppNotificationsServiceImpl inAppNotificationsService;

    UserServiceClient userServiceClient;

    ApiResponseCacheService apiResponseCacheService;

    RiddlerCacheService riddlerService;

    SegmentEvaluatorService segmentEvaluatorService;

    @Lazy
    @Autowired
    VMCache vmCache;

    @Lazy
    @Autowired
    PageCache pageCache;

    @Lazy
    @Autowired
    WidgetTemplateCache widgetTemplateCache;

    EnvironmentService environmentService;

    HamletHelper hamletHelper;

    ProductService productService;

    CircuitBreakerConfiguration circuitBreakerConfiguration;

    CityCache cityCache;

    FoodRecommendationServiceInterface foodRecommendationService;

    IKioskDemandService kioskDemandService;

    @Lazy
    @Autowired
    ServiceConfigCache serviceConfigCache;

    UserActionMappingService userActionMappingService;

    @Qualifier("cfApiTaskExecutor")
    AsyncListenableTaskExecutor taskExecutor;

    CultService cultService;

    MindService mindService;

    SegmentationCacheClient segmentationCacheClient;

    WidgetBuilder widgetBuilder;

    BannerBuilder bannerBuilder;

    KeyValueStore defaultRedisKeyValueStore;

    KeyValueStore cfApiRedisKeyValueStore;

    AnnouncementService announcementService;

    ExceptionReportingService exceptionReportingService;

    QuickActionService quickActionService;

    UserAttributesCacheClient userAttributesCacheClient;

    UserEventClient userEventClient;

    UserService userService;

    ApiKeyService apiKeyService;

    TrainerLedService trainerLedService;

    EHRClient ehrClient;

    CultIconService cultIconService;

    DeeplinkCacheService deeplinkCacheService;

    CowinAlertService cowinAlertService;

    FoodwayAppClient foodwayClient;

    GearvaultService gearvaultService;

    CultsportPDPService cultsportPDPService;

    CultsportPLPService cultsportPLPService;

    GearvaultClient gearvaultClient;

    IOrderService orderService;

    @Qualifier("OmsOrderService")
    com.curefit.oms.services.spi.IOrderService omsOrderService;

    MagnetoClient magnetoClient;
    MagnetoCSProductClient magnetoCSProductClient;
    CultsportMapper cultsportMapper;
    CultsportUserClient cultsportUserClient;

    CrossSellRecoBuilderService crossSellRecoBuilderService;

    FoodMarketplace foodMarketplace;

    TrialUsageService trialUsageService;

    CenterService centerService;

    LocalityService localityService;

    LocalityProviderService localityProviderService;

    MembershipClientImpl membershipService;

    CfApiFeedbackService feedbackService;

    CultsportFeedbackService cultsportFeedbackService;

    SupportArticleService supportArticleService;

    FuseDiagnosticClientV2 fuseService;

    SocialServiceClientImpl socialService;

    OllivanderCenterClient ollivanderCenterClient;

    OllivanderAgentClient ollivanderAgentClient;
    RagnarCommunityService ragnarCommunityService;

    EnterpriseClient enterpriseClient;

    UserAttributesClient userAttributesClient;

    Tata3pService tata3pService;

    Tata3pServiceHelper tata3pServiceHelper;

    PersonalTrainerServiceClient personalTrainerServiceClient;

    RashiClient rashiClient;

    ProductHubService productHubService;

    ProductFAQMapService productFAQMapService;

    MediaService mediaService;

    ProductVerticalService productVerticalService;

    CfApiNodeService cfApiNodeService;

    TreadClientImpl treadClient;

    FeedbackServiceClient cultsportFeedbackServiceClient;

    HerculesService herculesService;

    @Lazy
    @Autowired
    SimpleWodCache simpleWodCache;

    PostClassCelebrationConfigService postClassCelebrationConfigService;

    PrimaryQuickActionsConfigService primaryQuickActionsConfigService;
    PrimaryQuickActionNewLogicConfigService primaryQuickActionNewLogicConfigService;

    PilotCenterConfigService pilotCenterConfigService;

    FitstoreSearchService fitstoreSearchService;

    FitstorePLPHelper fitstorePLPHelper;

    SportsApiClientImpl sportsApiService;

    JourneyService journeyService;

    @Lazy
    @Autowired
    CaravanWorkflowCache caravanWorkflowCache;

    SEOService seoService;

    IdentityServiceClient identityServiceClient;

    @Lazy
    @Autowired
    CrmAssetCache crmAssetCache;

    QuestClient questClient;

    DistanceService distanceService;

    GymOvercrowdingService gymOvercrowdingService;

    ScheduleService scheduleService;

    CHSClient chsClient;

    PlanClient planClient;

    LoggingClient sfLoggingClient;

    AmbrosiaClient ambrosiaClient;

    @Lazy
    @Autowired
    AppConfigCache appConfigCache;
    MoEngageService moEngageService;

    @Lazy
    @Autowired
    UserOnboardingService sfUserOnboardingService;

    @Lazy
    @Autowired
    ChronicCareServiceHelper chronicCareServiceHelper;

    CSProductCatalogueCacheService csProductCatalogueCacheService;
    CenterMachineService centerMachineService;

    MixpanelEventService mixpanelEventService;

    ConstantsConfig constantsConfig;

    SmartDeviceClient smartDeviceClient;

    HttpServletRequest httpServletRequest;

    CSBlogService csBlogService;

    UserWeeklyActivityRepository userWeeklyActivityRepository;
    DatalakeService datalakeService;
    ReviewService reviewService;

	
    @Lazy
    @Autowired
    FeatureStateCache featureStateCache;

    CultServiceImpl cultServiceImpl;

    @Lazy
    @Autowired
    HerculesCache herculesCache;

    @Lazy
    @Autowired
    FitChampionsCache fitChampionsCache;

    CFAnalytics cfAnalytics;
    CultsportReviewService cultsportReviewService;
    CommonConfiguration commonConfiguration;

    IUserActivityServiceClient userActivityService;

    IActivityLoggingService activityLoggingService;
    IConfigService configService;
    CSSnapmintService csSnapmintService;
    ExchangeService exchangeService;
    MaxmindService maxmindService;
    @Lazy
    @Autowired
    KudosService kudosService;

    SprinklrTicketService sprinklrTicketService;
    FitnessReportService fitnessReportService;

    @Lazy
    @Autowired
    CustomerSupportService customerSupportService;

    @Lazy
    @Autowired
    HomeScreenWidgetService homeScreenWidgetService;

    @Autowired
    RelevantSegmentCache relevantSegmentCache;


  	@Lazy
    @Autowired
    BookingCache sosVisibilityCache;
      SOSConfiguration sosConfiguration;

    @Lazy
    @Autowired
    PastActivityService pastActivityService;

    @Lazy
    @Autowired
    UserActivityServiceClient userActivityServiceClient;
    
    @Lazy
    @Autowired
    UASFitnessReportClient uasFitnessReportClient;
    

    PricingServiceImpl pricingService;

    public ServiceInterfaces(
            IEatRedisService eatRedisService,
            FitcashServiceImpl fitcashService,
            OfferService offerService,
            MealPlannerService mealPlannerService,
            IShipmentService shipmentService,
            ICatalogueService catalogueService,
            ICatalogueServicePMS catalogueServicePMS,
            ILoggingService loggingService,
            CultsportLiveScoreService cultsportLiveScoreService,
            DiyfsService diyfsService,
            MetricClient metricClient,
            UserSegmentClient userSegmentClient,
            CFSClient cfsClient,
            @Qualifier("albusClient") AlbusClient albusClient,
            @Qualifier("ptClient") AlbusClient ptClient,
            @Qualifier("sugarfitClient") AlbusClient sfAlbusClient,
            @Qualifier("transformClient") AlbusClient transformClient,
            InAppNotificationsServiceImpl inAppNotificationsService,
            UserServiceClient userServiceClient,
            ApiResponseCacheService apiResponseCacheService,
            RiddlerCacheService riddlerService,
            SegmentEvaluatorService segmentEvaluatorService,
            @Lazy VMCache vmCache,
            @Lazy PageCache pageCache,
            WidgetTemplateCache widgetTemplateCache,
            EnvironmentService environmentService,
            HamletHelper hamletHelper,
            ProductService productService,
            CircuitBreakerConfiguration circuitBreakerConfiguration,
            CityCache cityCache,
            FoodRecommendationServiceInterface foodRecommendationService,
            ServiceConfigCache serviceConfigCache,
            @Qualifier("cfApiTaskExecutor") AsyncListenableTaskExecutor taskExecutor,
            CultService cultService,
            MindService mindService,
            UserActionMappingService userActionMappingService,
            SegmentationCacheClient segmentationCacheClient,
            @Lazy WidgetBuilder widgetBuilder,
            @Lazy BannerBuilder bannerBuilder,
            @Qualifier("defaultRedisKeyValueStore") KeyValueStore defaultRedisKeyValueStore,
            @Qualifier("cfApiRedisKeyValueStore") KeyValueStore cfApiRedisKeyValueStore,
            AnnouncementService announcementService,
            ExceptionReportingService exceptionReportingService,
            IKioskDemandService kioskDemandService,
            GymfitClient gymfitClient,
            UfsService ufsService,
            QuickActionService quickActionService,
            UserAttributesCacheClient userAttributesCacheClient,
            UserEventClient userEventClient,
            UserService userService,
            @Qualifier("mealPlannerClient") MealPlannerClient mealPlannerClient,
            ApiKeyService apiKeyService,
            EHRClient ehrClient,
            TrainerLedService trainerLedService,
            CultIconService cultIconService,
            DeeplinkCacheService deeplinkCacheService,
            CowinAlertService cowinAlertService,
            ShifuClient shifuClient,
            @Qualifier("microLearningClient") MicroLearningClient microLearningClient,
            FoodwayAppClient foodwayClient,
            GearvaultService gearvaultService,
            CultsportPDPService cultsportPDPService,
            CultsportPLPService cultsportPLPService,
            GearvaultClient gearvaultClient,
            IOrderService orderService,
            @Qualifier("OmsOrderService") com.curefit.oms.services.spi.IOrderService omsOrderService,
            MagnetoClient magnetoClient,
            MagnetoCSProductClient magnetoCSProductClient,
            CrossSellRecoBuilderService crossSellRecoBuilderService,
            FoodMarketplace foodMarketplace,
            TrialUsageService trialUsageService,
            CenterService centerService,
            LocalityService localityService,
            LocalityProviderService localityProviderService,
            MembershipClientImpl membershipService,
            CfApiFeedbackService feedbackService,
            CultsportFeedbackService cultsportFeedbackService,
            SupportArticleService supportArticleService,
            FuseDiagnosticClientV2 fuseService,
            SocialServiceClientImpl socialService,
            OllivanderCenterClient ollivanderCenterClient,
            OllivanderAgentClient ollivanderAgentClient,
            EnterpriseClient enterpriseClient,
            UserAttributesClient userAttributesClient,
            TreadClientImpl treadClient,
            Tata3pService tata3pService,
            Tata3pServiceHelper tata3pServiceHelper,
            PersonalTrainerServiceClient personalTrainerServiceClient,
            RashiClient rashiClient,
            ProductHubService productHubService,
            ProductFAQMapService productFAQMapService,
            MediaService mediaService,
            ProductVerticalService productVerticalService,
            CfApiNodeService cfApiNodeService,
            FeedbackServiceClient cultsportFeedbackServiceClient,
            HerculesService herculesService,
            @Lazy SimpleWodCache simpleWodCache,
            PostClassCelebrationConfigService postClassCelebrationConfigService,
            PrimaryQuickActionsConfigService primaryQuickActionsConfigService,
            PrimaryQuickActionNewLogicConfigService primaryQuickActionNewLogicConfigService,
            PilotCenterConfigService pilotCenterConfigService,
            FitstoreSearchService fitstoreSearchService,
            SportsApiClientImpl sportsApiService,
            FitstorePLPHelper fitstorePLPHelper,
            JourneyService journeyService,
            ConstelloService constelloService,
            CaravanWorkflowCache caravanWorkflowCache,
            SEOService seoService,
            IdentityServiceClient identityServiceClient,
            @Lazy CrmAssetCache crmAssetCache,
            QuestClient questClient,
            DistanceService distanceService,
            GymOvercrowdingService gymOvercrowdingService,
            ScheduleService scheduleService,
            SMSClient smsClient,
            CHSClient chsClient,
            PlanClient planClient,
            LoggingClient sfLoggingClient,
            AmbrosiaClient ambrosiaClient,
            @Lazy AppConfigCache appConfigCache,
            CSProductCatalogueCacheService csProductCatalogueCacheService,
            MixpanelEventService mixpanelEventService,
            CenterMachineService centerMachineService,
            ConstantsConfig constantsConfig,
            SmartDeviceClient smartDeviceClient,
            HttpServletRequest httpServletRequest,
            UserWeeklyActivityRepository userWeeklyActivityRepository,
            DatalakeService datalakeService,
            @Lazy FeatureStateCache featureStateCache,
            ReviewService reviewService,
            CSBlogService csBlogService,
            CultServiceImpl cultServiceImpl,
            @Lazy HerculesCache herculesCache,
            @Lazy FitChampionsCache fitChampionsCache,
            CFAnalytics cfAnalytics,
            CultsportReviewService cultsportReviewService,
            MoEngageService moEngageService,
            CultsportUserClient cultsportUserClient,
            CultsportMapper cultsportMapper,
            CommonConfiguration commonConfiguration,
            IUserActivityServiceClient userActivityService,
            IOfflineFitnessPackService offlineFitnessPackService,
            IActivityLoggingService activityLoggingService,
            IConfigService configService,
            CSSnapmintService csSnapmintService,
            ExchangeService exchangeService,
            MaxmindService maxmindService,
            @Lazy KudosService kudosService,
            SprinklrTicketService sprinklrTicketService,
            FitnessReportService fitnessReportService,
            @Lazy CustomerSupportService customerSupportService,
            @Lazy BookingCache sosVisibilityCache,
            SOSConfiguration sosConfiguration,
            @Lazy PastActivityService pastActivityService,
            RagnarCommunityService ragnarCommunityService,
            PricingServiceImpl pricingService,
            RelevantSegmentCache relevantSegmentCache
    ) {
        this.ragnarCommunityService = ragnarCommunityService;
        this.cultsportMapper = cultsportMapper;
        this.postClassCelebrationConfigService = postClassCelebrationConfigService;
        this.primaryQuickActionsConfigService = primaryQuickActionsConfigService;
        this.primaryQuickActionNewLogicConfigService = primaryQuickActionNewLogicConfigService;
        this.pilotCenterConfigService = pilotCenterConfigService;
        this.simpleWodCache = simpleWodCache;
        this.herculesService = herculesService;
        this.fitChampionsCache = fitChampionsCache;
        this.eatRedisService = eatRedisService;
        this.fitcashService = fitcashService;
        this.offerService = offerService;
        this.mealPlannerService = mealPlannerService;
        this.constelloService = constelloService;
        this.shipmentService = shipmentService;
        this.catalogueService = catalogueService;
        this.catalogueServicePMS = catalogueServicePMS;
        this.loggingService = loggingService;
        this.diyfsService = diyfsService;
        this.cfsClient = cfsClient;
        this.metricClient = metricClient;
        this.userSegmentClient = userSegmentClient;
        this.albusClient = albusClient;
        this.sfAlbusClient = sfAlbusClient;
        this.gymfitClient = gymfitClient;
        this.cultsportLiveScoreService = cultsportLiveScoreService;
        this.ufsService = ufsService;
        this.ptClient = ptClient;
        this.inAppNotificationsService = inAppNotificationsService;
        this.userServiceClient = userServiceClient;
        this.apiResponseCacheService = apiResponseCacheService;
        this.riddlerService = riddlerService;
        this.segmentEvaluatorService = segmentEvaluatorService;
        this.vmCache = vmCache;
        this.pageCache = pageCache;
        this.widgetTemplateCache = widgetTemplateCache;
        this.environmentService = environmentService;
        this.hamletHelper = hamletHelper;
        this.productService = productService;
        this.circuitBreakerConfiguration = circuitBreakerConfiguration;
        this.cityCache = cityCache;
        this.foodRecommendationService = foodRecommendationService;
        this.serviceConfigCache = serviceConfigCache;
        this.taskExecutor = taskExecutor;
        this.cultService = cultService;
        this.mindService = mindService;
        this.userActionMappingService = userActionMappingService;
        this.segmentationCacheClient = segmentationCacheClient;
        this.widgetBuilder = widgetBuilder;
        this.bannerBuilder = bannerBuilder;
        this.defaultRedisKeyValueStore = defaultRedisKeyValueStore;
        this.cfApiRedisKeyValueStore = cfApiRedisKeyValueStore;
        this.announcementService = announcementService;
        this.exceptionReportingService = exceptionReportingService;
        this.kioskDemandService = kioskDemandService;
        this.quickActionService = quickActionService;
        this.userAttributesCacheClient = userAttributesCacheClient;
        this.mealPlannerClient = mealPlannerClient;
        this.userEventClient = userEventClient;
        this.userService = userService;
        this.apiKeyService = apiKeyService;
        this.trainerLedService = trainerLedService;
        this.ehrClient = ehrClient;
        this.cultIconService = cultIconService;
        this.deeplinkCacheService = deeplinkCacheService;
        this.cowinAlertService = cowinAlertService;
        this.shifuClient = shifuClient;
        this.microLearningClient = microLearningClient;
        this.transformClient = transformClient;
        this.foodwayClient = foodwayClient;
        this.gearvaultService = gearvaultService;
        this.cultsportPDPService = cultsportPDPService;
        this.cultsportPLPService = cultsportPLPService;
        this.gearvaultClient = gearvaultClient;
        this.orderService = orderService;
        this.omsOrderService = omsOrderService;
        this.magnetoClient = magnetoClient;
        this.magnetoCSProductClient = magnetoCSProductClient;
        this.cultsportUserClient = cultsportUserClient;
        this.crossSellRecoBuilderService = crossSellRecoBuilderService;
        this.foodMarketplace = foodMarketplace;
        this.trialUsageService = trialUsageService;
        this.centerService = centerService;
        this.localityService = localityService;
        this.localityProviderService = localityProviderService;
        this.supportArticleService = supportArticleService;
        this.membershipService = membershipService;
        this.feedbackService = feedbackService;
        this.cultsportFeedbackService = cultsportFeedbackService;
        this.enterpriseClient = enterpriseClient;
        this.fuseService = fuseService;
        this.socialService = socialService;
        this.ollivanderCenterClient = ollivanderCenterClient;
        this.ollivanderAgentClient = ollivanderAgentClient;
        this.userAttributesClient = userAttributesClient;
        this.rashiClient = rashiClient;
        this.treadClient = treadClient;
        this.tata3pService = tata3pService;
        this.tata3pServiceHelper = tata3pServiceHelper;
        this.personalTrainerServiceClient = personalTrainerServiceClient;
        this.productHubService = productHubService;
        this.productFAQMapService = productFAQMapService;
        this.mediaService = mediaService;
        this.productVerticalService = productVerticalService;
        this.cfApiNodeService = cfApiNodeService;
        this.cultsportFeedbackServiceClient = cultsportFeedbackServiceClient;
        this.fitstoreSearchService = fitstoreSearchService;
        this.fitstorePLPHelper = fitstorePLPHelper;
        this.sportsApiService = sportsApiService;
        this.journeyService = journeyService;
        this.caravanWorkflowCache = caravanWorkflowCache;
        this.seoService = seoService;
        this.identityServiceClient = identityServiceClient;
        this.crmAssetCache = crmAssetCache;
        this.questClient = questClient;
        this.distanceService = distanceService;
        this.gymOvercrowdingService = gymOvercrowdingService;
        this.scheduleService = scheduleService;
        this.smsClient = smsClient;
        this.chsClient = chsClient;
        this.planClient = planClient;
        this.sfLoggingClient = sfLoggingClient;
        this.ambrosiaClient = ambrosiaClient;
        this.appConfigCache = appConfigCache;
        this.csProductCatalogueCacheService = csProductCatalogueCacheService;
        this.mixpanelEventService = mixpanelEventService;
        this.centerMachineService = centerMachineService;
        this.constantsConfig = constantsConfig;
        this.smartDeviceClient = smartDeviceClient;
        this.httpServletRequest = httpServletRequest;
        this.userWeeklyActivityRepository = userWeeklyActivityRepository;
        this.datalakeService = datalakeService;
        this.featureStateCache = featureStateCache;
        this.reviewService = reviewService;
        this.csBlogService = csBlogService;
        this.cultServiceImpl = cultServiceImpl;
        this.herculesCache = herculesCache;
        this.cfAnalytics = cfAnalytics;
        this.cultsportReviewService = cultsportReviewService;
        this.moEngageService = moEngageService;
        this.commonConfiguration = commonConfiguration;
        this.userActivityService = userActivityService;
        this.offlineFitnessPackService = offlineFitnessPackService;
        this.activityLoggingService = activityLoggingService;
        this.configService = configService;
        this.csSnapmintService = csSnapmintService;
        this.exchangeService = exchangeService;
        this.maxmindService = maxmindService;
        this.kudosService = kudosService;
        this.sprinklrTicketService = sprinklrTicketService;
        this.fitnessReportService = fitnessReportService;
        this.customerSupportService = customerSupportService;
        this.pastActivityService = pastActivityService;
        this.pricingService = pricingService;
      	this.sosVisibilityCache = sosVisibilityCache;
        this.sosConfiguration = sosConfiguration;
        this.relevantSegmentCache = relevantSegmentCache;
    }
}
