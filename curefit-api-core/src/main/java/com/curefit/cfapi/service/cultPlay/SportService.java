package com.curefit.cfapi.service.cultPlay;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionIcon;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.util.OrderUtil;
import com.curefit.cfapi.util.PlayUtil;
import com.curefit.cfapi.view.viewbuilders.fitsoSports.FTSSportTrialBottomSheetPageView;
import com.curefit.cfapi.view.viewmodels.fitso.packdetail.PackSportsListView;
import com.curefit.cfapi.view.viewmodels.fitso.packdetail.PlayPackCenterMeta;
import com.curefit.cfapi.view.viewmodels.fitso.packdetail.SportDetail;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.cultPlay.web.PlaySportSection;
import com.curefit.cfapi.widgets.fitness.CFImageTextWidgetType1;
import com.curefit.cfapi.widgets.fitness.CFImageTextWidgetType1Item;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.product.models.cult.FitnessPack;
import com.curefit.sportsapi.pojo.FTSSportsInfo;
import com.curefit.sportsapi.pojo.FTSTrialSport;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.curefit.cfapi.pojo.app.action.ActionType.*;
import static com.curefit.cfapi.util.ActionUtil.sportTrialCTAWithAnalytics;
import static com.curefit.cfapi.util.PlayUtil.SINGLE_SPORT_PAGE_DEEPLINK;


@Slf4j
@Service
public class SportService {

    private final ServiceInterfaces serviceInterfaces;

    @Autowired
    public SportService(ServiceInterfaces serviceInterfaces){
        this.serviceInterfaces = serviceInterfaces;
    }

    private List<FTSTrialSport> getSportsListDataForTrial(UserContext userContext) {
        List<FTSTrialSport> sportsList = new ArrayList<>();
        try {
            String cityId = userContext.getUserProfile().getCity() != null ? userContext.getUserProfile().getCity().getCityId() : "";
            Boolean isUserPhoneNumberPresent = PlayUtil.isUserPhoneNumberPresent(userContext);
            sportsList = serviceInterfaces.sportsApiService.getTrialSportDetails(userContext.getUserProfile().getUserId(),
                    cityId,
                    isUserPhoneNumberPresent
            );
        } catch (Exception e) {
            log.error("getSportsListDataForTrial play sports list for trial: " + userContext, e);
        }
        return sportsList;
    }

    public FTSSportTrialBottomSheetPageView getSportsListForTrialApp(UserContext userContext) {
        FTSSportTrialBottomSheetPageView pageView = new FTSSportTrialBottomSheetPageView();

        pageView.setTitle("Select a sport for a free session");
        pageView.setSubtitle("");

        List<FTSTrialSport> sportsList = this.getSportsListDataForTrial(userContext);

        CFImageTextWidgetType1 widget = new CFImageTextWidgetType1();
        List<CFImageTextWidgetType1Item> items = new ArrayList<>();
        List<BaseWidget> widgetList = new ArrayList<>();
        for (FTSTrialSport model : sportsList) {
            CFImageTextWidgetType1Item item = new CFImageTextWidgetType1Item();
            item.setImage(model.getIconUrl());
            item.setTitle(model.getName());
            //item.setSubtitle1(model.getCenterCount() + " centres available"); /* commenting for now*/
            item.setSubtitle3(model.getAvailableTrial() == 0 ? "Free session consumed" : model.getAvailableTrial() + " free trial available");
            item.addSpacing("0", "20");
            item.setAction(sportTrialCTAWithAnalytics(model));
            items.add(item);
        }
        widget.setItems(items);
        widget.addSpacing("0", "20");
        widgetList.add(widget);
        pageView.setWidgets(widgetList);
        return pageView;
    }

    public FTSSportTrialBottomSheetPageView getSportsListForTrialWeb(UserContext userContext) throws Exception {
        boolean isLoggedIn = Integer.parseInt(userContext.getUserProfile().getUserId()) > 0;
        boolean isPlayMember = false;
        if(isLoggedIn){
            List<Membership> memberships = PlayUtil.getCurrentPlayMembershipsInCity(userContext, this.serviceInterfaces);
            isPlayMember = memberships != null && !memberships.isEmpty();
        }
        FTSSportTrialBottomSheetPageView pageView = new FTSSportTrialBottomSheetPageView();
        pageView.setTitle("Select a sport");
        List<BaseWidget> widgetList = new ArrayList<>();
        if(!isLoggedIn || !isPlayMember){
            List<FTSTrialSport> sportsList = this.getSportsListDataForTrial(userContext);
            if(sportsList.size() == 0){
                pageView.setTitle("No Sports Found!");
                return pageView;
            }
            for (FTSTrialSport model : sportsList) {
                PlaySportSection widget  = new PlaySportSection(model);
                if(widget.getAction() != null && widget.getAction().getMeta() != null){
                    List<NameValuePair> nameValuePairs = new ArrayList<>();
                    nameValuePairs.add(new BasicNameValuePair("workoutId", String.valueOf(model.getWorkoutId())));
                    nameValuePairs.add(new BasicNameValuePair("productType", "PLAY"));
                    nameValuePairs.add(new BasicNameValuePair("pageFrom", "PLAY"));
                    Action action = PlayUtil.getPlayClassBookingWebAction(nameValuePairs, "SELECT");
                    action.setMeta(widget.getAction().getMeta());
                    widget.setAction(action);
                }
                widgetList.add(widget);
            }
            pageView.setSubtitle("for a free session");
        } else {
            // case: play member
            String cityId = userContext.getUserProfile().getCity() != null ? userContext.getUserProfile().getCity().getCityId() : "";
            Boolean isUserPhoneNumberPresent = PlayUtil.isUserPhoneNumberPresent(userContext);
            List<FTSSportsInfo> cityWiseSports = serviceInterfaces.sportsApiService.getCityWorkoutsInfo(cityId, isUserPhoneNumberPresent).get();
            if(cityWiseSports.size() == 0){
                pageView.setTitle("No Sports Found!");
                return pageView;
            }
            for (FTSSportsInfo model : cityWiseSports) {
                PlaySportSection widget  = new PlaySportSection(model);
                List<NameValuePair> nameValuePairs = new ArrayList<>();
                nameValuePairs.add(new BasicNameValuePair("workoutId", String.valueOf(model.getId())));
                nameValuePairs.add(new BasicNameValuePair("productType", "PLAY"));
                nameValuePairs.add(new BasicNameValuePair("pageFrom", "PLAY"));
                Action action = PlayUtil.getPlayClassBookingWebAction(nameValuePairs, "SELECT");
                if(widget.getAction() != null && widget.getAction().getMeta() != null){
                    action.setMeta(widget.getAction().getMeta());
                }
                widget.setAction(action);
                widgetList.add(widget);
            }
            pageView.setSubtitle("for a session");
        }
        pageView.setWidgets(widgetList);
        return pageView;
    }

    public PackSportsListView getPackSportsList(
            UserContext userContext,
            Map<String, String> queryParams
    ) throws Exception {
        String cityId = userContext.getUserProfile().getCity() != null ? userContext.getUserProfile().getCity().getCityId() : "";
        String flowType = queryParams.get("flowType");

        Boolean isUserPhoneNumberPresent = PlayUtil.isUserPhoneNumberPresent(userContext);
        List<FTSSportsInfo> cityWiseSports = serviceInterfaces.sportsApiService.getCityWorkoutsInfo(cityId, isUserPhoneNumberPresent).get();

        String title = "Select your preferred sport";
        String subtitle = "Preferred sport helps us to understand your preference and manage capacity. You can still book and play all sports at all cult centers.";

        PackSportsListView packSportsListView = new PackSportsListView();
        packSportsListView.setTitle(title);
        packSportsListView.setSubtitle(subtitle);

        List<SportDetail> sportDetails = new ArrayList<>();

        for(FTSSportsInfo cityWiseSport: cityWiseSports) {
            SportDetail sport = new SportDetail();
            sport.setName(cityWiseSport.getName());
            sport.setIconName(ActionIcon.CHEVRON_RIGHT);

            Action centerSelectAction =  new Action(
                    ActionType.SELECT_PLAY_CENTER,
                    null
            );
            PlayPackCenterMeta actionMeta = new PlayPackCenterMeta();
            actionMeta.setWorkoutId(cityWiseSport.getId());
            actionMeta.setWorkoutName(cityWiseSport.getName());
            actionMeta.setPageFrom("playpack");
            actionMeta.setProductId(queryParams.get("productId"));
            if (flowType != null && flowType.equals("PLAY_SELECT_UPGRADE")) {
                actionMeta.setIsPlaySelectUpgradeFlow(true);
                actionMeta.setIsPlayPage(false);
            }

            centerSelectAction.setMeta(actionMeta);
            sport.setAction(centerSelectAction);

            sport.setImage(cityWiseSport.getPackIcon());
            sport.setWorkoutId(cityWiseSport.getId());

            sportDetails.add(sport);
        }

        packSportsListView.setSports(sportDetails);

        return packSportsListView;
    }

    public FTSSportTrialBottomSheetPageView getPackSportsListV2(
        UserContext userContext,
        Map<String, String> queryParams
    ) throws Exception {
        String cityId = userContext.getUserProfile().getCity() != null ? userContext.getUserProfile().getCity().getCityId() : "";

        Boolean isUserPhoneNumberPresent = PlayUtil.isUserPhoneNumberPresent(userContext);
        List<FTSSportsInfo> cityWiseSports = serviceInterfaces.sportsApiService.getCityWorkoutsInfo(cityId, isUserPhoneNumberPresent).get();

        String title = "Select your preferred sport";
        String subtitle = "Preferred sport helps us to understand your preference and manage capacity. You can still book and play all sports at all cult centers.";

        FTSSportTrialBottomSheetPageView pageView = new FTSSportTrialBottomSheetPageView();

        pageView.setTitle(title);
        pageView.setSubtitle(subtitle);

        CFImageTextWidgetType1 widget = new CFImageTextWidgetType1();
        List<CFImageTextWidgetType1Item> items = new ArrayList<>();
        List<BaseWidget> widgetList = new ArrayList<>();

        for (FTSSportsInfo model : cityWiseSports) {
            List<FitnessPack> citySportsPack = serviceInterfaces.catalogueService.getPlayProductCenterActivityByCity(
                userContext.getUserProfile().getCity().getCityId(),
                model.getId().toString(),
                true,
                userContext.getUserProfile().getUserId()
            );
            if (citySportsPack.isEmpty())
                continue;

            List <NameValuePair> pageParams = new ArrayList<>();
            pageParams.add(new BasicNameValuePair("productId", queryParams.get("productId")));
            pageParams.add(new BasicNameValuePair("workoutId", model.getId().toString()));
            pageParams.add(new BasicNameValuePair("workoutName", model.getName()));

            Action action = PlayUtil.getPreferedCenterPageAction(pageParams, "", null);

            CFImageTextWidgetType1Item item = new CFImageTextWidgetType1Item();
            item.setImage(model.getIconUrl());
            item.setTitle(model.getName());
            // item.setSubtitle3("Starting at " + OrderUtil.RUPEE_SYMBOL + minPrice +"/mo");
            item.addSpacing("0", "20");
            item.setAction(action);
            items.add(item);
        }
        widget.setItems(items);
        widget.addSpacing("0", "20");
        widgetList.add(widget);
        pageView.setWidgets(widgetList);
        return pageView;
    }

    public FTSSportTrialBottomSheetPageView getPackSportsWebList(UserContext userContext, Map<String, String> queryParams) throws Exception {
        String cityId = userContext.getUserProfile().getCity() != null ? userContext.getUserProfile().getCity().getCityId() : "";
        Boolean isUserPhoneNumberPresent = PlayUtil.isUserPhoneNumberPresent(userContext);
        List<FTSSportsInfo> cityWiseSports = serviceInterfaces.sportsApiService.getCityWorkoutsInfo(cityId, isUserPhoneNumberPresent).get();

        FTSSportTrialBottomSheetPageView pageView = new FTSSportTrialBottomSheetPageView();
        if (cityWiseSports.isEmpty()) {
            pageView.setTitle("No Sports Found!");
            return pageView;
        }
        pageView.setTitle("Select your preferred sport");
        pageView.setSubtitle("Preferred sport helps us to understand your preference and manage capacity. You can still book and play all sports at all cult centers.");
        List<BaseWidget> widgetList = new ArrayList<>();
        for (FTSSportsInfo model : cityWiseSports) {
            PlaySportSection widget  = new PlaySportSection(model);
            widget.setBtnText("SELECT");
            widgetList.add(widget);
        }
        pageView.setWidgets(widgetList);
        return pageView;
    }

    public FTSSportTrialBottomSheetPageView getSportsListForPurchaseSportPack(UserContext userContext) throws Exception {
        List<FTSSportsInfo> citySports = serviceInterfaces.sportsApiService.getCityWorkoutsInfo(userContext.getUserProfile().getCity().getCityId(), true).get();

        FTSSportTrialBottomSheetPageView pageView = new FTSSportTrialBottomSheetPageView();

        pageView.setTitle("Select your sport of choice");

        CFImageTextWidgetType1 widget = new CFImageTextWidgetType1();
        List<CFImageTextWidgetType1Item> items = new ArrayList<>();
        List<BaseWidget> widgetList = new ArrayList<>();

        for (FTSSportsInfo model : citySports) {
            List<FitnessPack> citySportsPack = serviceInterfaces.catalogueService.getPlayProductCenterActivityByCity(
                userContext.getUserProfile().getCity().getCityId(),
                model.getId().toString(),
                true,
                userContext.getUserProfile().getUserId()
            );
            if (citySportsPack.isEmpty())
                continue;
            citySportsPack = citySportsPack.stream().filter(pack -> pack.getVisibility() != null && pack.getVisibility().contains("app")).collect(Collectors.toList());
            final List<FitnessPack> packList = citySportsPack;
            String minPrice = PlayUtil.getMinPriceOfSportLevelPack(null, packList, userContext, serviceInterfaces);
            String url = SINGLE_SPORT_PAGE_DEEPLINK + "sportId=" + model.getId().toString();
            Action action = new Action(url, NAVIGATION);

            CFImageTextWidgetType1Item item = new CFImageTextWidgetType1Item();
            item.setImage(model.getIconUrl());
            item.setTitle(model.getName());
            item.setSubtitle3("Starting at " + OrderUtil.RUPEE_SYMBOL + minPrice +"/mo*");
            item.addSpacing("0", "20");
            item.setAction(action);
            items.add(item);
        }
        widget.setItems(items);
        widget.addSpacing("0", "20");
        widgetList.add(widget);
        pageView.setWidgets(widgetList);
        return pageView;
    }
}
