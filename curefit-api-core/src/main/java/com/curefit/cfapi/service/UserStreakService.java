package com.curefit.cfapi.service;


import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.builder.vm.BuildWidgetResponse;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.constants.AppDeeplink;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.PageContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.autoLaunch.AutoLaunchAction;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.widget.BaseWidget;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.service.autoLaunchService.autoLaunchTriggers.UserStreakRepairAutoLaunch;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewmodels.fitness.DSPageData;
import com.curefit.cfapi.widgets.common.banner.BannerCarouselWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.dailystreak.*;
import com.curefit.cfapi.widgets.hometab.AddActivityCard;
import com.curefit.cfapi.widgets.hometab.AddActivityWidget;
import com.curefit.logging.models.ActivityTypeDS;
import com.curefit.uas.enums.streaks.StreakDayType;
import com.curefit.uas.impl.UserActivityServiceClient;
import com.curefit.uas.responses.StreakActivityResponse;
import com.curefit.uas.responses.StreakResponse;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserStreakService {
    ServiceInterfaces serviceInterfaces;
    UserActivityServiceClient userActivityServiceClient;
    UserStreakRepairAutoLaunch userStreakRepairAutoLaunch;

    public static String RASHI_KEY_FOR_USER_STREAK_ONBOARDING = "user_onboarding_epoch_app_streak";
    public static String USER_STREAK_FEEDBACK_SEGMENT = "a33e8e23-6f80-4d9a-9640-640df81448ab";
    public static final float DS_DYNAMIC_ASSETS_APP_VERSION = 10.98f;
    public static final float IOS_HOME_SCREEN_WIDGET_APP_VERSION = 10.95f;
    public static final float ANDROID_HOME_SCREEN_WIDGET_APP_VERSION = 11.07f;
    public static final float ANDROID_HOME_WIDGET_PINNING_SUPPORT_APP_VERSION = 11.18f;
    public static final String STREAK_YOU_WERE_ACTIVE_WIDGET = "52b03b5d-5d5b-4787-aace-6b26f6024a36";
    public static final String STREAK_QUICK_ACTION_WIDGET = "b507aee2-7300-41b0-a6c7-c41167ec7284-new-iconsDaily-Streak";
    public static final String STREAK_REPAIR_DATE_WIDGET = "8f814bd9-da2c-48c8-9bdf-1a69be379461";
    private final String HOME_SCREEN_ONBOARDING_WIDGET = "16e13574-1d77-487b-bb4d-0551e74fa6b2";
    private final String IOS = "ios";
    private final String ANDROID = "android";
    private static final String curefitYellow = "#FFDC18";
    private static final String curefitBlue = "#00BEFF";
    private static final String white10 = "1AFFFFFF";
    private static final String white = "FFFFFF";
    private static final String transparent = "#00FFFFFF";

    public DSPageData getDSPageData(UserContext userContext) throws Exception {
        try {
            List<BaseWidget> widgets = new ArrayList<>();


            // Build home screen onboarding widget for iOS users with app version >= 10.95
            CompletableFuture<BuildWidgetResponse> futureHomeScreenOnboardingWidget = null;
            String os = userContext.getSessionInfo().getOsName();
            float appVersion = userContext.getSessionInfo().getAppVersion();

            if((os.equalsIgnoreCase(IOS) && appVersion >= IOS_HOME_SCREEN_WIDGET_APP_VERSION) ||
                    (os.equalsIgnoreCase(ANDROID) && appVersion >= ANDROID_HOME_SCREEN_WIDGET_APP_VERSION)){
                List<String> widgetIds = new ArrayList<>();
                widgetIds.add(HOME_SCREEN_ONBOARDING_WIDGET);
                PageContext pageContext = new PageContext();
                WidgetContext widgetContext = new WidgetContext(pageContext);
                futureHomeScreenOnboardingWidget = this.serviceInterfaces.getWidgetBuilder().buildWidgets(widgetIds, userContext, widgetContext);
            }
            Object response = userContext.getRequestCache().getMexRequestFuture(RequestType.USER_STREAK_DETAILS_WITH_ACTIVITY_MAP, userContext).get();
            StreakResponse userStreakData = null;
            if(response instanceof StreakResponse) userStreakData = (StreakResponse) response;

            Long streakRepairExpiryEpoch = UserStreakUtil.streakRepairExpiredEpoch(userContext, serviceInterfaces);
            Long streakRepairConsumedEpoch = UserStreakUtil.streakRepairConsumedEpoch(userContext, serviceInterfaces);
            String streakRepairExpiryMonth = UserStreakUtil.getStreakRepairExpiryMonth(userContext, serviceInterfaces);
            UserStreakConfig userStreakConfig = UserStreakConfigurator.createUserStreakConfig(userStreakData, streakRepairExpiryEpoch, streakRepairConsumedEpoch, streakRepairExpiryMonth);

            DSBannerWidgetData dsBannerWidgetData = new DSBannerWidgetData();
            dsBannerWidgetData.setWidgetType(WidgetType.DAILY_STREAK_BANNER);
            dsBannerWidgetData.setStreakPaused(userStreakData.isStreakPaused());
            dsBannerWidgetData.setBannerImageUrl(userStreakConfig.getBannerImageUrl());
            dsBannerWidgetData.setCurrentStreakCount(userStreakData.getCurrentStreakCount());
            dsBannerWidgetData.setActiveDayIcon("image/daily-streak/fire.png");
            dsBannerWidgetData.setUnusedRestDayIcon("image/daily-streak/rest.png");
            dsBannerWidgetData.setUsedRestDayIcon("image/daily-streak/rest_blur.png");
            dsBannerWidgetData.setPauseDayImageUrl("image/daily-streak/fire_gray.png");
            dsBannerWidgetData.setSuffixLottieUrl("image/daily-streak/fire_lottie.json");

            Integer maximumRestDays = userStreakData.getMaximumRestDays();
            dsBannerWidgetData.setMaximumRestDays(maximumRestDays);

            Integer remainingRestDays = userStreakData.getRemainingRestDays();
            dsBannerWidgetData.setRemainingRestDays(remainingRestDays);

            dsBannerWidgetData.setHeaderText(userStreakConfig.getBannerHeaderText());
            dsBannerWidgetData.setMiddleText(CFTextData.builder().text(userStreakData.getCurrentStreakCount() <= 1 ? "day streak" : "days streak").color("#FFFFFF").typeScale("H1").build());

            String restDaysText = String.format((remainingRestDays != null ? remainingRestDays : 0) + " rest " + (remainingRestDays != null && remainingRestDays > 1 ? "days" : "day") + " left this week");
            String restDaysColor = (remainingRestDays != null && remainingRestDays == 0) ? "#EF3E3E" : "#00BEFF";

            if (!userStreakData.isStreakPaused() && userStreakData.getCurrentStreakCount() > 0) {
                dsBannerWidgetData.setBottomText(CFTextData.builder()
                        .text(restDaysText)
                        .color(restDaysColor)
                        .typeScale("P5")
                        .build());

            }

            dsBannerWidgetData.setDailyStreakCountText(CFTextData.builder()
                    .text(String.valueOf(userStreakData.getCurrentStreakCount()))
                    .color("#FFFFFF")
                    .typeScale("Inter60F900W")
                    .build());

            dsBannerWidgetData.setDailyStreakCountShadowText(CFTextData.builder()
                    .text(String.valueOf(userStreakData.getCurrentStreakCount()))
                    .color("#FFFFFF")
                    .typeScale("Inter60F900W")
                    .opacity(0.1)
                    .build());

            dsBannerWidgetData.setGradientColors(userStreakConfig.getBannerGradientColors());

            widgets.add(dsBannerWidgetData);

            // Add streak repair date widget if user_streak_repair_expiry_date_month attribute is set
            if (userStreakConfig.getStreakStateType().equals(StreakStateType.REPAIR_AWARDED) && streakRepairExpiryMonth != null) {
                try {
                    PageContext pageContext = new PageContext();
                    WidgetContext widgetContext = new WidgetContext(pageContext);
                    
                    BuildWidgetResponse streakRepairWidgetResponse = this.serviceInterfaces.getWidgetBuilder().buildWidgets(
                        List.of(STREAK_REPAIR_DATE_WIDGET), userContext, widgetContext).get();

                    if (streakRepairWidgetResponse != null && streakRepairWidgetResponse.getWidgets() != null
                        && !streakRepairWidgetResponse.getWidgets().isEmpty()) {
                        BaseWidget streakRepairWidget = streakRepairWidgetResponse.getWidgets().getFirst();
                        streakRepairWidget.addSpacing("0", "30");
                        widgets.add(streakRepairWidget);
                    }
                } catch (Exception e) {
                    log.error("Error adding streak repair date widget: ", e);
                    serviceInterfaces.exceptionReportingService.reportException("Error adding streak repair date widget", e);
                }
            }

            LocalDate today = LocalDate.now();
            LocalDate monday = today.with(DayOfWeek.MONDAY);


            Map<String, StreakDayType> dateToActivitiesMap =
                    userStreakData.getDateToDayTypeMap() != null ? userStreakData.getDateToDayTypeMap() : new HashMap<>();

            Map<String, StreakDayType> weeklyData = IntStream.range(0, 7)
                    .mapToObj(monday::plusDays)
                    .filter(date -> dateToActivitiesMap.get(date.toString()) != null)
                    .collect(Collectors.toMap(
                            date -> date.getDayOfWeek().name().toLowerCase(),
                            date -> dateToActivitiesMap.get(date.toString()),
                            (existing, replacement) -> existing,
                            LinkedHashMap::new
                    ));

            DSWeeklyProgressWidgetData dsWeeklyProgressWidgetData = new DSWeeklyProgressWidgetData();
            dsWeeklyProgressWidgetData.setWidgetType(WidgetType.DAILY_STREAK_WEEKLY_PROGRESS_WIDGET);
            dsWeeklyProgressWidgetData.setWeeklyData(weeklyData);
            dsWeeklyProgressWidgetData.setWeeklyDataV2(convertMonthlyDataToV2(weeklyData, true));
            dsWeeklyProgressWidgetData.setGradientColors(userStreakConfig.getWeeklyProgressGradientColors());
            dsWeeklyProgressWidgetData.setActiveDayIcon("image/daily-streak/fire.png");
            dsWeeklyProgressWidgetData.setRestDayIcon("image/daily-streak/rest.png");

            if (!userStreakData.isStreakPaused()) {
                widgets.add(dsWeeklyProgressWidgetData);
            }

            List<BaseWidget> userActionWidgets = getUserActionWidget(serviceInterfaces, userContext, userStreakData, userStreakConfig);
            widgets.addAll(userActionWidgets);

            DSCalendarWidgetData dsCalendarWidgetData = new DSCalendarWidgetData();
            dsCalendarWidgetData.setWidgetType(WidgetType.DAILY_STREAK_CALENDAR);
            dsCalendarWidgetData.setGradientColors(userStreakConfig.getCalendarGradientColors());
            dsCalendarWidgetData.setMonthlyDataV2(new HashMap<>());
            widgets.add(dsCalendarWidgetData);

            if(futureHomeScreenOnboardingWidget != null){
                BuildWidgetResponse homeScreenOnboardingWidgetResponse = futureHomeScreenOnboardingWidget.get();

                if(homeScreenOnboardingWidgetResponse != null && homeScreenOnboardingWidgetResponse.getWidgets() != null && !homeScreenOnboardingWidgetResponse.getWidgets().isEmpty()) {
                    BaseWidget homeScreenOnboardingWidget = homeScreenOnboardingWidgetResponse.getWidgets().getFirst();
                    if(appVersion >= ANDROID_HOME_WIDGET_PINNING_SUPPORT_APP_VERSION &&
                            userContext.getSessionInfo().getOsName().equals(ANDROID)
                            && homeScreenOnboardingWidget instanceof BannerCarouselWidget
                            && ((BannerCarouselWidget) homeScreenOnboardingWidget).getData() != null
                            && !((BannerCarouselWidget) homeScreenOnboardingWidget).getData().isEmpty()){

                        BannerItem bannerItems = ((BannerCarouselWidget) homeScreenOnboardingWidget).getData().getFirst();
                        bannerItems.setAction(Action.builder().actionType(ActionType.ADD_HOME_SCREEN_WIDGET).build());
                    }

                    widgets.add(homeScreenOnboardingWidget);
                }
            }

            return DSPageData.builder()
                    .widgets(widgets)
                    .isBlackTheme(userStreakConfig.isBlackTheme())
                    .currentStreakCount(userStreakData.getCurrentStreakCount())
                    .infoButtonAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://fl_custom_sheet?pageId=Streak_lp").build())
                    .ctaText(userStreakData.isStreakPaused() ? "UN-PAUSE PACK" : "BOOK CLASS")
                    .ctaAction(Action.builder().actionType(ActionType.NAVIGATION).url(userStreakData.isStreakPaused() ? "curefit://fl_account_view" : "curefit://fl_custom_sheet?pageId=Streak_lp_2").build())
                    .pageLandingAction(getPageLandingAutoLaunch(userContext, serviceInterfaces))
                    .build();
        } catch (Exception e){
            String message = String.format("Failed to get DSPageData for user: %s :: %s", userContext.getUserProfile().getUserId(), e.getMessage());
            serviceInterfaces.exceptionReportingService.reportException(message, e);
            throw e;
        }
    }

    private Action getPageLandingAutoLaunch(UserContext userContext, ServiceInterfaces serviceInterfaces){
        try {
            Optional<AutoLaunchAction> autoLaunchAction = userStreakRepairAutoLaunch.getAutoLaunchAction(userContext);
            if(autoLaunchAction.isPresent()) {
                userStreakRepairAutoLaunch.resetStreakRepairRashiAttributes(userContext);
                return autoLaunchAction.get().getFlutterAction();
            }

            if(!UserStreakUtil.isUserOnboarded(userContext, serviceInterfaces)){
                UserStreakUtil.setUserOnboarded(userContext, serviceInterfaces);
                return Action
                        .builder()
                        .actionType(ActionType.NAVIGATION)
                        .url(AppDeeplink.USER_STREAK_ONBOARDING_PAGE_EXISTING_STREAK.getDeeplinkString())
                        .build();
            }
        } catch (Exception e){
            serviceInterfaces.exceptionReportingService.reportException(e);
        }
        return null;
    }

    private List<BaseWidget> getUserActionWidget(ServiceInterfaces serviceInterfaces, UserContext userContext, StreakResponse userStreakData, UserStreakConfig userStreakConfig){
        List<BaseWidget> widgets = new ArrayList<>();
        if(userStreakData.isStreakPaused()) return widgets;
        try {
            boolean isActivityDoneOnEndDate = StreakActivityChecker.isActivityDoneToday(userStreakData);
            if(!isActivityDoneOnEndDate){
                DSTextWidgetData dsTextWidgetData = new DSTextWidgetData();
                dsTextWidgetData.setWidgetType(WidgetType.DAILY_STREAK_TEXT_WIDGET);
                dsTextWidgetData.setHeading(userStreakConfig.getTextWidgetHeading());
                dsTextWidgetData.setSubHeading(userStreakConfig.getTextWidgetSubHeading());

                PageContext pageContext = new PageContext();
                WidgetContext widgetContext = new WidgetContext(pageContext);
                widgetContext.getQueryParams().put("isUserStreakPage", "true");

                BuildWidgetResponse bodyResponse = this.serviceInterfaces.getWidgetBuilder().buildWidgets(List.of(STREAK_QUICK_ACTION_WIDGET), userContext, widgetContext).get();

                if (bodyResponse != null && bodyResponse.getWidgets() != null && !bodyResponse.getWidgets().isEmpty()) {
                    BaseWidget firstWidget = bodyResponse.getWidgets().getFirst();

                    if (firstWidget instanceof AddActivityWidget addActivityWidget) {
                        List<AddActivityCard> cards = new ArrayList<>();

                        List<StreakActivityResponse> eligibleActivities = userStreakData.getEligibleActivitiesWithEndDateScore();
                        if (eligibleActivities != null && addActivityWidget.getCards() != null) {
                            for (AddActivityCard activity : addActivityWidget.getCards()) {
                                activity.setIsActivityDoneForToday(false);

                                for (StreakActivityResponse streakActivity : eligibleActivities) {
                                    if (isActivityMatch(activity, streakActivity)) {
                                        if (isActivityThresholdMet(streakActivity)) {
                                            activity.setIsActivityDoneForToday(true);
                                        }
                                        cards.add(activity);
                                        break;
                                    }
                                }
                            }
                        }

                        addActivityWidget.setNumOfRows(1);
                        addActivityWidget.setCards(cards);
                        addActivityWidget.setNumOfCol(addActivityWidget.getCards().size());
                        addActivityWidget.getActivityCardProps().setCardHeight(120.0);


                        if (addActivityWidget.getCards().size() == 3) {
                            addActivityWidget.getActivityCardProps().setHorizontalPadding(50.0);
                        } else if (addActivityWidget.getCards().size() == 2) {
                            addActivityWidget.getActivityCardProps().setHorizontalPadding(100.0);
                        }

                        if (!cards.isEmpty()) {
                            widgets.add(dsTextWidgetData);
                            widgets.add(addActivityWidget);
                        }
                    }
                }
            } else {
                BuildWidgetResponse bodyResponse = this.serviceInterfaces.getWidgetBuilder().buildWidgets(List.of(STREAK_YOU_WERE_ACTIVE_WIDGET), userContext, new WidgetContext(new PageContext())).get();
                if (bodyResponse != null && bodyResponse.getWidgets() != null && !bodyResponse.getWidgets().isEmpty()) {
                    widgets.add(bodyResponse.getWidgets().getFirst());
                }
            }

        } catch (Exception e) {
            String message = String.format("<Daily Streak> - userId: %s : Failed to build widgets :: %s", userContext.getUserProfile().getUserId(), e.getMessage());
            serviceInterfaces.exceptionReportingService.reportException(message, e);
        }
        return widgets;
    }

    private boolean isActivityMatch(AddActivityCard activity, StreakActivityResponse streakActivity) {
        return streakActivity != null
                && streakActivity.getActivityType() != null
                && activity.getTitle() != null
                && getIconTitle(streakActivity.getActivityType()).contains(activity.getTitle());
    }

    private boolean isActivityThresholdMet(StreakActivityResponse streakActivity) {
        Long activityScore = streakActivity.getActivityScore();
        Long minThreshold = streakActivity.getActivityMinThresholdScore();
        return activityScore != null && minThreshold != null && activityScore >= minThreshold;
    }

    public DSCalendarWidgetData getAllUserActivity(UserContext userContext, String startDate, String endDate) {
        try {
            String userId = userContext.getUserProfile().getUserId();

            if (startDate == null || endDate == null) {
                return DSCalendarWidgetData.builder()
                        .monthlyDataV2(new HashMap<>())
                        .build();
            }

            Map<String, StreakDayType> dateToActivitiesDetailsMap = userActivityServiceClient.getStreakDetailsForTimePeriod(
                    userContext.getUserProfile().getUserId(), startDate, endDate);

            StreakResponse userStreakData = userActivityServiceClient.getUserStreakDetails(
                    userId,
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    false,
                    false);

            if (dateToActivitiesDetailsMap == null) {
                dateToActivitiesDetailsMap = new HashMap<>();
            }

            return DSCalendarWidgetData.builder()
                    .monthlyData(dateToActivitiesDetailsMap)
                    .monthlyDataV2(convertMonthlyDataToV2(dateToActivitiesDetailsMap, false))
                    .gradientColors(userStreakData.isStreakPaused() ? StreakGradients.PAUSED_CALENDAR : StreakGradients.DEFAULT_BACKGROUND)
                    .restDayIcon("image/daily-streak/rest.png")
                    .activeDayIcon("image/daily-streak/fire.png")
                    .build();
        } catch (Exception e) {
            String message = String.format("<Daily-streak> Failed to get all user activity for user: %s :: %s", userContext.getUserProfile().getUserId(), e.getMessage());
            serviceInterfaces.exceptionReportingService.reportException(message, e);
            return DSCalendarWidgetData.builder()
                    .monthlyDataV2(new HashMap<>())
                    .build();
        }
    }

    public DSHomePageWidget getDSHomePageWidget(UserContext userContext, @RequestParam Map<String, String> queryParams) {
        DSHomePageWidget dsHomePageWidget = new DSHomePageWidget();
        try {
            WidgetContext widgetContext = new WidgetContext();
            List<com.curefit.cfapi.widgets.base.BaseWidget> dsHomePageWidgetResponse = dsHomePageWidget.buildView(serviceInterfaces, userContext, widgetContext);
            if (dsHomePageWidgetResponse != null && !dsHomePageWidgetResponse.isEmpty() && dsHomePageWidgetResponse.getFirst() != null) {
                dsHomePageWidget = (DSHomePageWidget) dsHomePageWidgetResponse.getFirst();
            }
            return dsHomePageWidget;
        } catch (Exception e){
            serviceInterfaces.exceptionReportingService.reportException(String.format("Failed to build DSHomePageWidget :: userId : %s :: %s", userContext.getUserProfile().getUserId(), e.getMessage()), e);
            return dsHomePageWidget;
        }
    }

    public DSDayOneData getDSDayOneData(UserContext userContext) {
        Boolean isM1JourneyUser = AppUtil.doesUserBelongToM1RecommendationSegment(userContext,  serviceInterfaces.segmentEvaluatorService, serviceInterfaces.environmentService);
        UserStreakUtil.setUserOnboarded(userContext, serviceInterfaces);
        return DSDayOneData.builder()
                .lastLottieAction(Action.builder().actionType(ActionType.NAVIGATION).url(AppDeeplink.USER_STREAK_MAIN_PAGE.getDeeplinkString()).build())
                .animationDuration(Arrays.asList(2000, 2000, 10000))
                .lottieUrls(
                        isM1JourneyUser ? UserStreakLottieUrls.M1_JOURNEY : UserStreakLottieUrls.NON_M1_JOURNEY
                )
                .build();
    }

    public DSSummaryWidget getDSSummaryWidget(UserContext userContext){
        DSSummaryWidget dsSummaryWidget = new DSSummaryWidget();
        try {
            WidgetContext widgetContext = new WidgetContext();
            List<com.curefit.cfapi.widgets.base.BaseWidget> widgetResponse = dsSummaryWidget.buildView(serviceInterfaces, userContext, widgetContext);
            if(widgetResponse != null && !widgetResponse.isEmpty() && widgetResponse.getFirst() != null){
                dsSummaryWidget = (DSSummaryWidget) widgetResponse.getFirst();
            }
            return dsSummaryWidget;
        } catch (Exception e){
            String message = String.format("Failed to get DSSummaryWidget for user: %s :: %s", userContext.getUserProfile().getUserId(), e.getMessage());
            serviceInterfaces.exceptionReportingService.reportException(message, e);
            return dsSummaryWidget;
        }
    }


    private List<String> getIconTitle(ActivityTypeDS activityTypeDS) {
        if (activityTypeDS == null) {
            return null;
        }

        return switch (activityTypeDS) {
            case ActivityTypeDS.CULT_CLASS -> List.of("book a\ncult class");
            case ActivityTypeDS.PLAY_CLASS -> List.of("play\nsports");
            case ActivityTypeDS.GYMFIT_CHECKIN -> List.of("checkin\nat gym", "view\nQR code");
            case ActivityTypeDS.LIVE_SESSION -> List.of("workout\nat home");
            default -> null;
        };
    }

    /**
     * Converts StreakDayType to StreakIconData with appropriate visual properties.
     */
    public static StreakIconData convertStreakDayTypeToIconData(StreakDayType streakDayType, boolean weekView) {
        if (streakDayType == null) {
            return StreakIconData.builder()
                    .height(15.0)
                    .width(15.0)
                    .iconContainerHeight(32.0)
                    .iconContainerWidth(32.0)
                    .borderRadius(32.0)
                    .backgroundColor(weekView ? transparent : white10)
                    .pause(true)
                    .isConsideredInStreak(null)
                    .build();
        }

        return switch (streakDayType) {
            case ACTIVE -> StreakIconData.builder()
                    .imageUrl("image/daily-streak/fire.png")
                    .height(15.0)
                    .width(15.0)
                    .iconContainerHeight(32.0)
                    .iconContainerWidth(32.0)
                    .borderRadius(32.0)
                    .borderColor(curefitYellow)
                    .backgroundColor(weekView ? transparent : white10)
                    .pause(null)
                    .isConsideredInStreak(true)
                    .build();

            case REST -> StreakIconData.builder()
                    .imageUrl("image/daily-streak/rest.png")
                    .height(15.0)
                    .width(15.0)
                    .iconContainerHeight(32.0)
                    .iconContainerWidth(32.0)
                    .borderRadius(32.0)
                    .borderColor(curefitBlue)
                    .backgroundColor(weekView ? transparent : white10)
                    .pause(null)
                    .isConsideredInStreak(true)
                    .build();

            case PAUSED -> StreakIconData.builder()
                    .height(15.0)
                    .width(15.0)
                    .iconContainerHeight(32.0)
                    .iconContainerWidth(32.0)
                    .borderRadius(32.0)
                    .backgroundColor(white)
                    .pause(true)
                    .isConsideredInStreak(null)
                    .build();

            case REPAIR -> StreakIconData.builder()
                    .imageUrl("/image/daily-streak-v0/Union.png")
                    .height(13.0)
                    .width(13.0)
                    .iconContainerHeight(32.0)
                    .iconContainerWidth(32.0)
                    .borderRadius(32.0)
                    .borderColor(transparent)
                    .backgroundColor(white10)
                    .pause(false)
                    .isConsideredInStreak(true)
                    .build();

            default -> StreakIconData.builder()
                    .height(15.0)
                    .width(15.0)
                    .iconContainerHeight(32.0)
                    .iconContainerWidth(32.0)
                    .borderRadius(32.0)
                    .backgroundColor(weekView ? transparent : white10)
                    .pause(false)
                    .isConsideredInStreak(null)
                    .build();
        };
    }

    /**
     * Converts a Map of StreakDayType to a Map of StreakIconData.
     */
    public static Map<String, StreakIconData> convertMonthlyDataToV2(Map<String, StreakDayType> monthlyData, boolean weekView) {
        if (monthlyData == null) {
            return new HashMap<>();
        }

        return monthlyData.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> convertStreakDayTypeToIconData(entry.getValue(), weekView)
                ));
    }
}