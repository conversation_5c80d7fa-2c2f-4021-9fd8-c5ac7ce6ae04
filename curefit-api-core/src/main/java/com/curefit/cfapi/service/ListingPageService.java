package com.curefit.cfapi.service;

import com.curefit.base.enums.UserAgent;
import com.curefit.cfapi.model.mongo.ListingPage;
import com.curefit.cfapi.repository.ListingPageRepository;
import com.curefit.cfapi.view.viewbuilders.ListingPageViewBuilder;
import com.curefit.cfapi.view.viewmodels.listingpages.ListingPageView;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ListingPageService {
    ListingPageRepository pageRepository;
    ListingPageViewBuilder listingPageBuilder;

    @Autowired
    public ListingPageService(ListingPageRepository pageRepository, ListingPageViewBuilder listingPageBuilder) {
        this.pageRepository = pageRepository;
        this.listingPageBuilder = listingPageBuilder;
    }
    public ListingPageView getPage(String slug, UserAgent agent) throws BaseException {
        ListingPage page = this.pageRepository.findBySlug(slug);
        if (page == null || !page.getPublished()) {
            throw new ResourceNotFoundException("Page is not published");
        }
        return listingPageBuilder.buildView(
            page.getTitle(),
            page.getDescription(),
            page.getCategory(),
            page.getSubCategory(),
            page.getTags(),
            agent
        );
    }
}
