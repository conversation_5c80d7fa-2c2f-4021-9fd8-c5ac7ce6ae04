package com.curefit.cfapi.service.community;

import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.ragnar.RagnarConstants;
import com.curefit.cfapi.ragnar.enums.ExpertType;
import com.curefit.cfapi.ragnar.response.HighlightCardData;
import com.curefit.cfapi.ragnar.response.HighlightCardResponse;
import com.curefit.cfapi.ragnar.response.HighlightPeopleResponse;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.RuntimeBaseException;
import com.curefit.cult.models.CultAttendedBookingResponse;
import com.curefit.cult.models.CultClass;
import com.curefit.cult.models.CultEmployeeResponse;
import com.curefit.cult.models.TrainerDetailsResponse;
import com.curefit.cult.models.responses.CultClassResponse;
import com.curefit.cult.services.impl.CultServiceImpl;
import com.curefit.logging.models.ActivityDS;
import com.curefit.logging.models.ActivityTypeDS;
import com.curefit.logging.models.request.ActivityDSSearchRequest;
import com.curefit.logging.models.response.ActivityResponse;
import com.curefit.logging.service.impl.LoggingService;
import com.curefit.quest.client.spi.QuestClient;
import com.curefit.quest.enums.Vertical;
import com.curefit.quest.pojo.*;
import com.curefit.uas.impl.UASFitnessReportClient;
import com.curefit.uas.responses.FitnessReportResponse;
import com.curefit.userservice.client.UserServiceClient;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.curefit.userservice.pojo.request.UsersSearchRequest;
import com.curefit.userservice.pojo.response.UsersResponse;
import com.google.common.collect.Lists;
import io.github.resilience4j.bulkhead.annotation.Bulkhead;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;

import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class RagnarCommunityService {
    @Autowired
    UserServiceClient userServiceClient;

    @Autowired
    QuestClient questClient;

    @Autowired
    CultServiceImpl cultServiceImpl;
    @Autowired
    UASFitnessReportClient uasFitnessReportClient;

    @Autowired
    LoggingService loggingService;

    @Autowired
    ExceptionReportingService exceptionReportingService;


    @CircuitBreaker(name = "ragnarCommunityCircuitBreaker", fallbackMethod = "getHighlightCardFallback")
    @Bulkhead(name = "ragnarCommunityBulkhead", type = Bulkhead.Type.THREADPOOL)
    @TimeLimiter(name = "ragnarCommunityTimeout2000ms")
    @Retry(name = "ragnarCommunityMax2Attempts")
    public CompletableFuture<HighlightCardResponse> getHighlightCard(UserContext userContext, String classId, String bookingId) {
        return CompletableFuture.supplyAsync(() -> {
            String cultUserId = userContext.getUserProfile().getUserId();
            return getHighlightCard(cultUserId, classId, bookingId);
        });
    }

    @CircuitBreaker(name = "ragnarCommunityCircuitBreaker", fallbackMethod = "getHighlightPeopleFallback")
    @Bulkhead(name = "ragnarCommunityBulkhead", type = Bulkhead.Type.THREADPOOL)
    @TimeLimiter(name = "ragnarCommunityTimeout2000ms")
    @Retry(name = "ragnarCommunityMax2Attempts")
    public CompletableFuture<HighlightPeopleResponse> getHighlightPeople(UserContext userContext, String classId) {
       return CompletableFuture.supplyAsync(() -> {
           String cultUserId = userContext.getUserProfile().getUserId();
           return getHighlightPeople(cultUserId, classId);
       });
    }

    public CompletableFuture<HighlightCardResponse> getHighlightCardFallback(UserContext userContext, String classId, String bookingId, Throwable throwable) {
        log.error("Circuit breaker triggered for getHighlightCard. userId:{} classId: {}, bookingId: {}, error: {}", userContext.getUserProfile().getUserId(), classId, bookingId, throwable.getMessage());
        return CompletableFuture.completedFuture(null);
    }

    public CompletableFuture<HighlightPeopleResponse> getHighlightPeopleFallback(UserContext userContext, String classId, Throwable throwable) {
        log.error("Circuit breaker triggered for getHighlightPeople. userId:{} classId: {}, error: {}", userContext.getUserProfile().getUserId(), classId, throwable.getMessage());
        return CompletableFuture.completedFuture(null);
    }


    private HighlightCardResponse getHighlightCard(String cultUserId, String classId, String bookingId) {
        try {
            HighlightCardResponse response = new HighlightCardResponse();
            
            CompletableFuture<CultClassResponse> cultClassResponsePromise = cultServiceImpl.getCultClass(classId, true, false, false, true, true, false);
            CompletableFuture<List<String>> momentsResponsePromise = cultServiceImpl.getCultMomentsImagesForClass(cultUserId, classId);
            
            CompletableFuture<UserEntry> userEntryPromise = userServiceClient.getUser(cultUserId);
            CompletableFuture<UserActivitySummary> userActivitySummaryPromise = questClient.getUserActivitySummary(cultUserId);
            
            CompletableFuture<List<Object>> combinedFuture = futureAllOf(cultClassResponsePromise, momentsResponsePromise, userEntryPromise, userActivitySummaryPromise);
            List<Object> responses = combinedFuture.get();
            CultClassResponse cultClassResponse = (CultClassResponse) responses.get(0);
            List<String> moments = (List<String>) responses.get(1);
            UserEntry userEntry = (UserEntry) responses.get(2);
            UserActivitySummary userActivitySummary = (UserActivitySummary) responses.get(3);
            
            CultClass cultClass = cultClassResponse.getCultClass();
            if (cultClass == null) {
                return null;
            }
            
            // log.info("<Class Highlights> workout info: {}", cultClass.getWorkout());
            
            if (cultClass.getDate() != null) {
                String classDate = getFormattedDateForMoments(cultClass.getDate());
                response.setDate(classDate);
            }
            
            if (cultClass.getStartTime() != null) {
                String classTime = cultClass.getStartTime();
                
                Integer hour = Integer.parseInt(classTime.split(":")[0]);
                Integer minute = Integer.parseInt(classTime.split(":")[1]);
                String classTimings = "";
                if (hour == 12) {
                    classTimings = hour.toString();
                    if (minute != 0) {
                        classTimings += ":" + minute;
                    }
                    classTimings += " PM";
                } else if (hour > 12) {
                    Integer hourPM = hour - 12;
                    classTimings = hourPM.toString();
                    if (minute != 0) {
                        classTimings += ":" + minute;
                    }
                    classTimings += " PM";
                } else {
                    classTimings = hour.toString();
                    if (minute != 0) {
                        classTimings += ":" + minute;
                    }
                    classTimings += " AM";
                }
                
                response.setTime(classTimings);
            }
            
            response.setWodId(cultClass.getWodId());
            response.setTitle(cultClass.getWorkout() != null? cultClass.getWorkout().getName(): "");
            
            boolean foundMoment = false;
            if (moments != null && !moments.isEmpty()) {
                response.setHighlightImage(moments.get(0));
                foundMoment = true;
            }
            
            ActivityDS activity = findActivityForBookingId(bookingId, cultUserId, cultClass.getDate());
            
            log.info("Class Highlights user activity: {}, cultUserId: {}, bookingId: {}", activity, cultUserId, bookingId);
            
            Badge badge = findBadge(cultUserId, bookingId, userActivitySummary);
            if (badge != null) {
                response.setBadge(badge.getImageUrl());
            }
            
            if (activity != null) {
                log.info("<Class Highlights> in condition buildCardData: {}", activity);
                List<HighlightCardData> cardData = buildCardData(activity, cultUserId, userActivitySummary, badge);
                response.setCardData(cardData);
            }
            
            String avatarUrl = userEntry != null ? userEntry.getProfilePictureUrl() : "";
            response.setAvatarUrl(avatarUrl);
            
            response.setCardbgUrl(RagnarConstants.DEFAULT_CARD_BG_IMG_URL);
            response.setDefaultHighlightImage(RagnarConstants.DEFAULT_HIGHLIGHT_IMG_URL);
            
            if (!foundMoment && avatarUrl == null) {
                if (badge != null) {
                    response.setCardbgUrl(RagnarConstants.BADGE_BG_IMG_URL);
                }
            }
            return response;
        }
        catch (Exception e) {
            log.error("Failed to create highlight card: {} due to {}", cultUserId, e.getMessage());
            RuntimeBaseException ex = new RuntimeBaseException(e);
            ex.setContext(Map.of("cultUserId", cultUserId, "classId", classId));
//            ex.setFingerprint(List.of(String.format("pod:%s", "membership")));
            exceptionReportingService.reportException(ex.getMessage(), ex);
        }
        return null;
    }
    
    private HighlightPeopleResponse getHighlightPeople(String cultUserId, String classId) {
        try {
            HighlightPeopleResponse response = new HighlightPeopleResponse();
            List<HighlightPeopleResponse.PeopleResponse> responseList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.clear(Calendar.MINUTE);
            calendar.clear(Calendar.SECOND);
            calendar.clear(Calendar.MILLISECOND);
            
            CompletableFuture<CultClassResponse> cultClassResponsePromise = cultServiceImpl.getCultClass(classId, true, false, false, false, true, false);
            CompletableFuture<List<CultAttendedBookingResponse>> attendedUserIdsPromise = cultServiceImpl.getAttendedUserIdsForClassV2(classId);
            
            CultClassResponse cultClassResponse = cultClassResponsePromise.get();
            List<CultAttendedBookingResponse> attendanceResponse = attendedUserIdsPromise.get();
            log.info("getHighlightPeople HighlightPeopleResponse attendanceResponse {}",attendanceResponse);
            List<String> attendedUserIds = attendanceResponse.stream().map(CultAttendedBookingResponse::getId).collect(toList());
            log.info("getHighlightPeople HighlightPeopleResponse {}",attendedUserIds);
            Map<String, com.curefit.uas.responses.FitnessReportResponse> fitnessReportResponse = this.uasFitnessReportClient.getBulkResponseForUserIdsAndWeekDate(new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime()), attendedUserIds).get();
//            log.info("getfitnessReportResponse fitnessReportResponse {}",fitnessReportResponse);
            
            Map<String, CultAttendedBookingResponse> userToAttendanceMap = new HashMap<>();
            
            UsersSearchRequest usersSearchRequest = new UsersSearchRequest();
            usersSearchRequest.setUserIds(attendedUserIds);
            log.info("getHighlightPeople HighlightPeopleResponse UsersSearchRequest {}",usersSearchRequest);
            
            CompletableFuture<UsersResponse> userEntriesPromise = CompletableFuture.supplyAsync(() -> userServiceClient.getBulkUsers(usersSearchRequest));
            
            for (CultAttendedBookingResponse user : attendanceResponse) {
                userToAttendanceMap.put(user.getId(), user);
            }
            
            UsersResponse userEntries = userEntriesPromise.get();
            CultClass cultClass = cultClassResponse.getCultClass();
            
            if (cultClass != null && cultClass.getTrainerIDs() != null && cultClass.getTrainerIDs().size() > 0) {
                for (String trainerId : cultClass.getTrainerIDs()) {
                    log.info("ragnar-fix: trainerID {}", trainerId);
                    TrainerDetailsResponse trainerDetailsResponse = cultServiceImpl.getTrainerDetails(trainerId).get();
                    CultEmployeeResponse trainerData = cultServiceImpl.getCultEmployeesById(Long.parseLong(trainerId));
                    Long trainerIdentityId = trainerData.getEmployee().getIdentityID();
                    log.info("ragnar-fix: trainerdata {}", trainerData.getEmployee());
                    log.info("ragnar-fix: identityId {}", trainerIdentityId);
                    log.info("ragnar-fix: trainerDetailResponse {}", trainerDetailsResponse);
                    if (trainerDetailsResponse != null) {
                        HighlightPeopleResponse.PeopleResponse peopleResponse = new HighlightPeopleResponse.PeopleResponse();
                        peopleResponse.setDisplayName(trainerDetailsResponse.getName());
                        peopleResponse.setTrainerId(Long.toString(trainerIdentityId));
                        peopleResponse.setTag(ExpertType.TRAINER.toString());
                        peopleResponse.setAvatarUrl(trainerDetailsResponse.getImage());
                        // Todo::dheeraj::ragnar:: is this db being used and if yes do we need to migrate or can remove?
//                        CommunityUser communityUser = communityUserService.getCommunityUserByExpertId(trainerId);
//                        if (communityUser != null) {
//                            peopleResponse.setCultUserId(communityUser.getCultUserId());
//                            peopleResponse.setSocialUserId(communityUser.getSocialUserId());
//                        }
//
                        peopleResponse.setClassesAttended(0L);
                        peopleResponse.setSuperUser(false);
                        
                        peopleResponse.setSecondaryData(null);
                        responseList.add(peopleResponse);
                    }
                }
            }
            Map<String, Integer> totalClassesAttendedMap = fillInTotalClassesAttendedMapFromCultApiUAS(attendedUserIds, cultClass);
            
            for(String userId : attendedUserIds) {
                
                CultAttendedBookingResponse entry = userToAttendanceMap.get(userId);
//                Long classesAttended = entry.getClassesAttended();
                
                Integer totalClassesAttended = null;
                if(fitnessReportResponse.get(userId) != null) {
                    totalClassesAttended = fitnessReportResponse.get(userId).getFitnessReport().getTotalClassesAttended();
                }
                if (totalClassesAttended == null) {
                    try {
                        totalClassesAttended = totalClassesAttendedMap.get(userId);
                    } catch (Exception e) {
                        RuntimeBaseException ex = new RuntimeBaseException(e);
                        ex.setContext(Map.of("cultUserId", cultUserId, "attendedUserId", userId, "classId", classId));
//                        ex.setFingerprint(List.of(String.format("pod:%s", "membership")));
                        exceptionReportingService.reportException("Error in getLatestFitnessReportForUserIds for cultUserId = " + cultUserId + " and attendeeUserId = " + userId, ex);
                    }
                }
                
                UserEntry userEntry = userEntries.getUsers().get(userId);
                
                HighlightPeopleResponse.PeopleResponse peopleResponse = new HighlightPeopleResponse.PeopleResponse();
                peopleResponse.setCultUserId(userId);
                
                peopleResponse.setDisplayName(userEntry.getFirstName() +
                        ((userEntry.getLastName() != null && !"".equals(userEntry.getLastName()) && !userEntry.getLastName().equalsIgnoreCase("NULL")) ? " " + userEntry.getLastName() : ""));
                peopleResponse.setAvatarUrl(userEntry.getProfilePictureUrl());
                Long thresh = 1L;
                if (totalClassesAttended != null) {
                    String classCompletedText = totalClassesAttended > thresh ? " Classes Completed" : " Class Completed";
                    peopleResponse.setSecondaryData(totalClassesAttended + classCompletedText);
                    peopleResponse.setClassesAttended(totalClassesAttended.longValue());
                }
                try {
                    peopleResponse.setSuperUser(entry.getSuperUser());
                } catch(Exception e) {
                    log.error("CommunityUser total classes super user: {}", userId);
                    RuntimeBaseException ex = new RuntimeBaseException(e);
                    ex.setContext(Map.of("cultUserId", cultUserId, "attendedUserId", userId, "classId", classId));
//                    ex.setFingerprint(List.of(String.format("pod:%s", "membership")));
                    exceptionReportingService.reportException(ex.getMessage(), ex);
                }
                
                responseList.add(peopleResponse);
            }
            
            response.setData(responseList);
            return response;
        } catch (Exception e) {
            log.error("Failed to create highlight people list: {} due to {}", cultUserId, e.getMessage());
            RuntimeBaseException ex = new RuntimeBaseException(e);
            ex.setContext(Map.of("cultUserId", cultUserId, "classId", classId));
//                    ex.setFingerprint(List.of(String.format("pod:%s", "membership")));
            exceptionReportingService.reportException(ex.getMessage(), ex);        }
        return null;
    }
    
    private ActivityDS findActivityForBookingId(String bookingId, String cultUserId, String classDate) throws IOException {
        ActivityDSSearchRequest activitySearchRequest = new ActivityDSSearchRequest();
        List<String> userIds = new ArrayList<>();
        userIds.add(cultUserId);
        List<ActivityTypeDS> activityTypes = new ArrayList<>();
        activityTypes.add(ActivityTypeDS.CULT_CLASS);
        activitySearchRequest.setActivityType(activityTypes);
        activitySearchRequest.setUserId(userIds);
        List<String> dates = new ArrayList<>();
        dates.add(classDate);
        activitySearchRequest.setDate(dates);
        ActivityResponse activityResponse = loggingService.getActivities(activitySearchRequest);
        
        for(ActivityDS activity: activityResponse.getActivities()) {
            if (activity.getClientActivityId().equals(bookingId)) {
                return activity;
            }
        }
        return null;
    }
    
    private List<HighlightCardData> buildCardData(ActivityDS activity, String cultUserId, UserActivitySummary userActivitySummary, Badge badge) throws ExecutionException, InterruptedException {
        log.info("<Class Highlights> in function buildCardData: {}", activity);
        List<HighlightCardData> cardData = new ArrayList<>();
        
        if(badge != null) {
            HighlightCardData badgeData = new HighlightCardData();
            badgeData.setTitle("New Achievement");
            int splitIndex = badge.getName().indexOf(' ');
            badgeData.setValue(badge.getName().substring(0, splitIndex));
            badgeData.setUnit(badge.getName().substring(splitIndex));
            badgeData.setIcon("ACHIEVEMENT");
            cardData.add(badgeData);
        }
        
        HighlightCardData duration = new HighlightCardData();
        duration.setTitle("Workout Duration");
        duration.setUnit("Mins");
        if (activity.getMeta() != null && activity.getMeta().getDuration() != null && badge == null) {
            String durationStr = Long.toString(activity.getMeta().getDuration()/1000/60);
            duration.setValue(durationStr);
            duration.setIcon("CLOCK");
            cardData.add(duration);
        }
        
        HighlightCardData caloriesBurn = new HighlightCardData();
        caloriesBurn.setTitle("Calories Burnt");
        caloriesBurn.setUnit("Cals");
        if (activity.getMeta() != null && activity.getMeta().getCaloriesBurnt() != null) {
            caloriesBurn.setValue(Long.toString(activity.getMeta().getCaloriesBurnt()));
            caloriesBurn.setIcon("FIRE");
            cardData.add(caloriesBurn);
        }
        
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.clear(Calendar.MINUTE);
        c.clear(Calendar.SECOND);
        c.clear(Calendar.MILLISECOND);
        
        com.curefit.uas.responses.FitnessReportResponse fitnessReportResponse = this.uasFitnessReportClient.getWeeklyAugmentedResponseForUserAndWeekDate(Long.valueOf(cultUserId), new SimpleDateFormat("yyyy-MM-dd").format(c.getTime())).get();
        
        if(fitnessReportResponse != null && fitnessReportResponse.getFitnessReport() != null) {
            //Days Worked out `this week`
            HighlightCardData classes = new HighlightCardData();
            classes.setTitle("This week");
            Integer numClassCount = fitnessReportResponse.getFitnessReport().getClassesAttended();
            classes.setUnit(numClassCount > 1 ? "Classes" : "Class");
            classes.setValue(numClassCount.toString());
            classes.setIcon("FITNESS");
            cardData.add(classes);
            
            //Current Weekly Streak
            HighlightCardData streak = new HighlightCardData();
            streak.setTitle("Current Streak");
            int numWeekStreak = Math.max(fitnessReportResponse.getFitnessReport().getStreak(), 1);
            streak.setUnit(numWeekStreak > 1 ? "Weeks" : "Week");
            streak.setValue(Integer.toString(numWeekStreak));
            streak.setIcon("FLASH");
            cardData.add(streak);
        }



         /*
                Commenting this out since the the values here are corrupted.

        UserActivitySummary userActivitySummary = questClient.getUserActivitySummary(cultUserId);

        HighlightCardData classes = new HighlightCardData();
        classes.setTitle("Classes Done");
        Long numClassCount = userActivitySummary.getMeta().getCultClassCount();
        classes.setUnit(numClassCount > 1 ? "Classes" : "Class");
        if (userActivitySummary != null && userActivitySummary.getMeta() != null && userActivitySummary.getMeta().getCultClassCount() != null) {
            classes.setValue(numClassCount.toString());
            cardData.add(classes);
        }

        HighlightCardData streak = new HighlightCardData();
        if (userActivitySummary != null && userActivitySummary.getWeeklyStreaks() != null && userActivitySummary.getWeeklyStreaks().size() > 0) {
            UserWeeklyStreak weeklyStreak = userActivitySummary.getWeeklyStreaks().get(0);
            streak.setTitle("Current Streak");
            Long numWeekStreak = weeklyStreak.getCurrent();
            streak.setUnit(numWeekStreak > 1 ? "Weeks" : "Week");
            streak.setValue(numWeekStreak.toString());
            cardData.add(streak);
        }
          */
        
        return cardData;
    }
    
    private static CompletableFuture<List<Object>> futureAllOf(CompletableFuture<?>... futures) {
        return CompletableFuture.allOf(futures)
                .thenApply(x -> Arrays.stream(futures)
                        .map(f -> (Object) f.join())
                        .collect(toList())
                );
    }
    
    private Badge findBadge(String cultUserId, String bookingId, UserActivitySummary userActivitySummary) throws ExecutionException, InterruptedException {
        List<QuestBadge> questBadges = null;
        
        CompletableFuture<List<QuestBadge>> questBadgesCompletableFuture = questClient.getBadgesForUser(cultUserId);
        
        try {
            questBadges = questBadgesCompletableFuture.get();
        } catch (ExecutionException | InterruptedException ignored) {
            return null;
        }
        
        if (userActivitySummary == null || questBadges == null || questBadges.isEmpty()) return null;
        
        List<UserBadge> badges = userActivitySummary.getBadges();
        for(UserBadge badge: badges) {
            if ( badge.getLastActivityId() != null && badge.getLastActivityId().equals(bookingId)) {
                for (QuestBadge questBadge: questBadges) {
                    if (questBadge.getVertical().equals(Vertical.CULT)) {
                        for (BadgeWrapper badgeWrapper: questBadge.getBadges()) {
                            if (badgeWrapper.getBadge().getBadgeId().equals(badge.getBadgeId())) {
                                return badgeWrapper.getBadge();
                            }
                        }
                    }
                    
                }
            }
        }
        return null;
    }
    
    private Map<String, Integer> fillInTotalClassesAttendedMapFromCultApiUAS(List<String> attendedUserIds, CultClass cultClass) {
        Map<String, Integer> totalClassesAttendedMap = new ConcurrentHashMap<>();
        try {
            Map<String, FitnessReportResponse> fitnessReportResponseMap = uasFitnessReportClient.getBulkResponseForUserIdsAndWeekDate(LocalDate.now().toString(), attendedUserIds).get();
            List<String> missingUserDetails = new ArrayList<>();
            for (String attendedUserId: attendedUserIds) {
                if (fitnessReportResponseMap.get(attendedUserId) == null) {
                    missingUserDetails.add(attendedUserId);
                } else {
                    totalClassesAttendedMap.put(attendedUserId, fitnessReportResponseMap.get(attendedUserId).getFitnessReport().getTotalClassesAttended());
                }
            }
            if (!missingUserDetails.isEmpty()) {
                log.info("fillInTotalClassesAttendedMapFromCultApiUAS for cultClass={} has missingUserDetails for userIds={}", cultClass.getId(), missingUserDetails);
                List<List<String>> missingUserDetailsBatches = Lists.partition(missingUserDetails, 5);
                
                CompletableFuture.allOf(missingUserDetailsBatches.stream()
                        .map(missingUserList ->
                                uasFitnessReportClient.getTotalClassesAttendedForLatestFitnessReportForUserIds(3,
                                                missingUserList.stream().map(Long::valueOf).collect(toList()))
                                        .thenAccept(result -> totalClassesAttendedMap.putAll(result.getTotalClassesAttendedMap()))
                        ).toArray(CompletableFuture[]::new)).get();
            }
        } catch (Exception e) {
            RuntimeBaseException ex = new RuntimeBaseException(e);
//            ex.setFingerprint(List.of(String.format("pod:%s", "membership")));
            exceptionReportingService.reportException(ex.getMessage(), ex);
        }
        return totalClassesAttendedMap;
    }
    
    private static final Map<String,String> monthFormatMapping = new HashMap<String,String>() {{
        put("01","JAN");
        put("02","FEB");
        put("03","MAR");
        put("04","APR");
        put("05","MAY");
        put("06","JUN");
        put("07","JUL");
        put("08","AUG");
        put("09","SEP");
        put("10","OCT");
        put("11","NOV");
        put("12","DEC");
    }};
    
    private static String getFormattedDateForMoments(String classDate) {
        String month = classDate.split("-")[1];
        String formattedMonth = monthFormatMapping.get(month);
        String date = classDate.split("-")[2];
        
        return date + " " + formattedMonth;
    }
}
