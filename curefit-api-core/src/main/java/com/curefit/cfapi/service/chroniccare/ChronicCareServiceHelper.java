package com.curefit.cfapi.service.chroniccare;

import com.curefit.albus.booking.ActiveConsultationResponse;
import com.curefit.albus.common.PatientPreferredAgentResponse;
import com.curefit.albus.common.*;
import com.curefit.albus.common.pojo.DiagnosticJourneyResponse;
import com.curefit.albus.response.ActivePackResponse;
import com.curefit.albus.response.*;
import com.curefit.albus.response.actions.UserPreferencePojo;
import com.curefit.albus.response.actions.chronic.DiagnosticSellableAction;
import com.curefit.albus.response.chronic.UserDiagnosticActionWithContext;
import com.curefit.albus.response.chronic.UserOnboardingActionWithContext;
import com.curefit.albus.response.contexts.chronic.DiagnosticSellableContext;
import com.curefit.albus.response.group_class.ActiveGroupClassOrderResponse;
import com.curefit.albus.response.group_class.GroupClassOrderEntry;
import com.curefit.albus.service.AlbusClient;
import com.curefit.base.enums.AppTenant;
import com.curefit.base.enums.Tenant;
import com.curefit.cf.commons.pojo.SearchOperator;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.RelevantSegmentCache;
import com.curefit.cfapi.dto.ChronicCareTeam;
import com.curefit.cfapi.dto.sugarfit.DiabeticProfileData;
import com.curefit.cfapi.dto.sugarfit.MegaSaleData;
import com.curefit.cfapi.dto.sugarfit.SfCgmDeviceStatus;
import com.curefit.cfapi.dto.sugarfit.SubscriptionPageItem;
import com.curefit.cfapi.model.internal.chroniccare.*;
import com.curefit.cfapi.model.internal.meta.LiveCalenderEvenetCancelPayload;
import com.curefit.cfapi.model.internal.meta.TeleconsultationManageOptionsMeta;
import com.curefit.cfapi.model.internal.meta.VideoSubscribeUnSubscribeActionMeta;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.*;
import com.curefit.cfapi.pojo.chroniccare.InterventionsStatusUpdateRequest;
import com.curefit.cfapi.pojo.chroniccare.SFContactDetails;
import com.curefit.cfapi.pojo.chroniccare.SfRecommendedSlotPage;
import com.curefit.cfapi.pojo.chroniccare.actvitylog.SfAttachment;
import com.curefit.cfapi.pojo.chroniccare.challenges.SfChallengeLeaderboardItem;
import com.curefit.cfapi.pojo.chroniccare.challenges.SfChallengeUserEntry;
import com.curefit.cfapi.pojo.chroniccare.diagnosticteststore.SfDiagnosticTest;
import com.curefit.cfapi.pojo.chroniccare.ecommerce.SfDeliveryTrackingDetails;
import com.curefit.cfapi.pojo.chroniccare.ecommerce.SfECommerceDeliveryDetails;
import com.curefit.cfapi.pojo.chroniccare.ecommerce.SfECommerceOrderTrackingDetails;
import com.curefit.cfapi.pojo.chroniccare.ecommerce.SfECommerceProduct;
import com.curefit.cfapi.pojo.chroniccare.experiencecenter.SfWellnessTherapyProduct;
import com.curefit.cfapi.pojo.chroniccare.sfdigital.UpsellingData;
import com.curefit.cfapi.pojo.chroniccare.support.SfSupportTicket;
import com.curefit.cfapi.pojo.view.ContainerStyle;
import com.curefit.cfapi.pojo.view.TextStyle;
import com.curefit.cfapi.pojo.vm.header.Header;
import com.curefit.cfapi.service.DeviceService;
import com.curefit.cfapi.service.ExceptionReportingService;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.chroniccare.nux.UserOnboardingService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewbuilders.chroniccare.SfNoShowPenaltyBuilder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.ecommerce.SfECommercePDPV2Builder;
import com.curefit.cfapi.view.viewbuilders.chroniccare.smartscale.SmartScaleVitalsListPageBuilder;
import com.curefit.cfapi.view.viewmodels.chroniccare.*;
import com.curefit.cfapi.view.viewmodels.chroniccare.diagnosticstoretest.SfDiagnosticTestInstructionView;
import com.curefit.cfapi.view.viewmodels.chroniccare.digitalapp.DigiPrePurchasePageView;
import com.curefit.cfapi.view.viewmodels.chroniccare.ecommerce.SfECommercePDPView;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.chroniccare.*;
import com.curefit.cfapi.widgets.chroniccare.challenges.SfChallengeCardWidget;
import com.curefit.cfapi.widgets.chroniccare.challenges.SfChallengeCardWidgetV2;
import com.curefit.cfapi.widgets.chroniccare.diagnosticTestStore.SfDoctorRecommendedTestWidget;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DAProgramJourneyWidget;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DigiJourneyProgressWidget;
import com.curefit.cfapi.widgets.chroniccare.digitalapp.DigiLessonsCarouselWidget;
import com.curefit.cfapi.widgets.chroniccare.poll.SfPollCardWidget;
import com.curefit.cfapi.widgets.chroniccare.poll.SfPollCardWidgetV2;
import com.curefit.cfapi.widgets.chroniccare.poll.SfPollResultCardWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.common.data.model.entity.Gender;
import com.curefit.common.rest.client.CommonHttpHelper;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.commons.util.Serializer;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.diyfs.pojo.DIYFilterRequestV2;
import com.curefit.diyfs.pojo.LiveClass;
import com.curefit.diyfs.pojo.enums.PreferredStreamType;
import com.curefit.diyfs.pojo.enums.SubscriptionStatus;
import com.curefit.fuse.dto.ProductSellerResponse;
import com.curefit.iris.models.NotificationMeta;
import com.curefit.location.models.UserDeliveryAddress;
import com.curefit.ollivander.common.constant.AgentType;
import com.curefit.ollivander.common.exception.OllivanderClientException;
import com.curefit.ollivander.common.pojo.request.agent.AgentSearchRequestParam;
import com.curefit.ollivander.common.pojo.response.agent.AgentResponse;
import com.curefit.ollivander.common.pojo.response.center.CenterResponseV2;
import com.curefit.oms.services.impl.OrderService;
import com.curefit.product.models.Product;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.sfalbus.enums.DeviceType;
import com.curefit.sfalbus.enums.PhleboTaskCategory;
import com.curefit.sfalbus.enums.PhleboTaskUserAction;
import com.curefit.sfalbus.response.DeviceShipmentResponse;
import com.curefit.sfalbus.response.diagnostics.DiagnosticsOrderTrackingResponse;
import com.curefit.sfalbus.response.phlebo_task.PhleboSlot;
import com.curefit.sfalbus.response.phlebo_task.PhleboTaskState;
import com.curefit.sfalbus.response.phlebo_task.UpcomingPhleboTaskResponse;
import com.curefit.shifu.commons.ShifuClientConfiguration;
import com.curefit.shifu.pojo.UserActivityEntry;
import com.curefit.shifu.pojo.goal.UserGoalEntry;
import com.curefit.shifu.pojo.goal.summary.GoalWithData;
import com.curefit.shifu.pojo.goal.summary.UserGoalSummary;
import com.curefit.shifu.request.UserActivityStatusUpdateRequest;
import com.curefit.shifu.response.WeeklyProgressResponse;
import com.curefit.shifu.service.ShifuClient;
import com.curefit.subuser.common.pojo.PatientDetail;
import com.curefit.userservice.client.UserServiceClient;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.sugarfit.ambrosia.pojo.NUXCuisine;
import com.sugarfit.ambrosia.pojo.UserMealPlanEntry;
import com.sugarfit.ambrosia.pojo.pdf.UserMealPlanPdfResponse;
import com.sugarfit.catalog.client.CatalogClient;
import com.sugarfit.catalog.enums.ChapterType;
import com.sugarfit.catalog.pojo.BookComposition;
import com.sugarfit.catalog.pojo.ChapterComposition;
import com.sugarfit.catalog.pojo.Entry.BookEntry;
import com.sugarfit.catalog.pojo.PageTracking;
import com.sugarfit.challenges.enums.ChallengeAudienceType;
import com.sugarfit.challenges.enums.ChallengeType;
import com.sugarfit.challenges.enums.EligibilityType;
import com.sugarfit.challenges.enums.RankEligibilityType;
import com.sugarfit.challenges.pojo.*;
import com.sugarfit.chs.client.CHSClient;
import com.sugarfit.chs.enums.DeviceModel;
import com.sugarfit.chs.enums.FaceBasedVitalRangeCategory;
import com.sugarfit.chs.enums.SmartScaleMetricName;
import com.sugarfit.chs.pojo.*;
import com.sugarfit.chs.pojo.UserReport.UserReportEntry;
import com.sugarfit.chs.pojo.cgmstat.CgmStat;
import com.sugarfit.chs.pojo.cgmstat.CgmStatFilter;
import com.sugarfit.chs.pojo.faceBasedVitals.*;
import com.sugarfit.chs.pojo.smartScale.SmartScaleLogsRequest;
import com.sugarfit.chs.pojo.smartScale.SmartScaleLogsResponse;
import com.sugarfit.customersupport.client.CustomerSupportClient;
import com.sugarfit.customersupport.pojo.SupportConversationEntry;
import com.sugarfit.fitness.client.SFFitnessClient;
import com.sugarfit.fitness.enums.SessionType;
import com.sugarfit.fitness.enums.SortOrder;
import com.sugarfit.fitness.enums.UserEventType;
import com.sugarfit.fitness.pojo.SessionLogResponse;
import com.sugarfit.fitness.pojo.UserSessionLogFetchRequest;
import com.sugarfit.housemd.HousemdClient;
import com.sugarfit.housemd.enums.OrderAction;
import com.sugarfit.housemd.pojo.order.OrderFilterRequest;
import com.sugarfit.housemd.pojo.order.OrderFilterResponse;
import com.sugarfit.indus.IndusClient;
import com.sugarfit.indus.enums.ProductActions;
import com.sugarfit.indus.enums.ProductCategory;
import com.sugarfit.indus.enums.ProductSubCategory;
import com.sugarfit.indus.enums.ShipmentStatus;
import com.sugarfit.indus.reponse.*;
import com.sugarfit.nest.client.MasterClassClient;
import com.sugarfit.nest.client.SFChatClient;
import com.sugarfit.nest.enums.UserMasterClassStatus;
import com.sugarfit.nest.enums.UserType;
import com.sugarfit.nest.pojo.masterclass.MasterClassFilterRequest;
import com.sugarfit.nest.pojo.masterclass.UserMasterClassEntry;
import com.sugarfit.poll.client.PollSupportClient;
import com.sugarfit.poll.enums.PollStatus;
import com.sugarfit.poll.enums.PollType;
import com.sugarfit.poll.enums.Status;
import com.sugarfit.poll.pojo.PollEntry;
import com.sugarfit.poll.pojo.QuestionEntry;
import com.sugarfit.poll.pojo.QuestionOptionsEntry;
import com.sugarfit.sms.client.SMSClient;
import com.sugarfit.sms.entry.AppointmentAudioTaskMappingEntry;
import com.sugarfit.sms.entry.UserRecommendationLogEntry;
import com.sugarfit.sms.entry.UserTaskDaySummary;
import com.sugarfit.sms.enums.MetricThresholdLimit;
import com.sugarfit.sms.enums.NUXStepName;
import com.sugarfit.sms.pojo.renewal_journey.RenewalJourneyResponse;
import com.sugarfit.sms.response.AssignedAgentResponse;
import com.sugarfit.sms.response.AudioTask.UpcomingAudioTaskSlotResponse;
import com.sugarfit.sms.response.FreemiumPackResponse;
import com.sugarfit.sms.response.NUXStatusResponse;
import com.sugarfit.sms.response.UserTodoDaySummary;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.curefit.cfapi.constants.Constants.GLUCO_RX_CGM_SELF_INSTALLATION_VIDEO_URL;
import static com.curefit.cfapi.pojo.app.action.ActionType.NAVIGATION;
import static com.curefit.cfapi.pojo.app.action.ActionType.SHOW_DIAGNOSTIC_INSTRUCTIONS_MODAL;
import static com.curefit.cfapi.util.AppUtil.getAppTenantFromUserContext;
import static com.curefit.cfapi.util.AppUtil.isSugarFitApp;
import static com.curefit.cfapi.util.ChronicCareAppUtil.*;
import static com.curefit.cfapi.util.DigitalAppUtil.DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE;
import static com.curefit.cfapi.util.SfDateUtils.isFutureDate;
import static com.curefit.cfapi.util.SfHomePageUtil.*;
import static com.curefit.cfapi.view.viewbuilders.chroniccare.FaceBasedVitalTrendsPageViewBuilder.*;
import static com.curefit.cfapi.view.viewbuilders.chroniccare.RenewSubscriptionPageViewBuilder.getVedicNumberFormat;
import static com.curefit.cfapi.view.viewbuilders.chroniccare.ecommerce.SfECommercePDPV2Builder.RECOMMENDED_PRODUCTS;
import static com.curefit.cfapi.view.viewbuilders.chroniccare.ecommerce.SfECommercePLPBuilder.*;
import static com.curefit.cfapi.view.viewbuilders.chroniccare.ecommerce.SfSmartScalePLPBuilder.E_COM_SMART_SCALE_PRODUCT;
import static com.curefit.cfapi.view.viewmodels.chroniccare.ActiveCardStatus.StatusType.*;
import static java.util.concurrent.CompletableFuture.supplyAsync;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ChronicCareServiceHelper {
    public static final String SUGARFIT_SHORT_COACH_CONSULT = "SUGAR002";
    public static final String SUGARFIT_LONG_COACH_CONSULT = "SUGAR003";
    public static final String SUGARFIT_DOCTOR_CONSULT = "SUGAR001";
    public static final String SUGARFIT_PSYCHOLOGIST_CONSULT = "SFPD001";
    public static final Long SUGARFIT_PSYCHOLOGIST_AGENT_ID = 115083L;
    public static final Set<String> SUGARFIT_PHLEBO_CONSULT_PRODUCTS = Set.of(SF_PHLEBO_CGM_CONSULTATION_PRODUCT, SF_PHLEBO_BLUCON_CONSULTATION_PRODUCT);
    public final static Set<String> SUGARFIT_COACH_CONSULT_PRODUCTS = Set.of(SUGARFIT_LONG_COACH_CONSULT, SUGARFIT_SHORT_COACH_CONSULT);
    public final static Set<String> SUGARFIT_DOCTOR_CONSULT_PRODUCTS = Set.of(SUGARFIT_DOCTOR_CONSULT);
    public final static Set<String> SUGARFIT_PC = new HashSet<>() {{
        add("SUGAR001");
        add("SUGAR002");
        add("SUGAR003");
        add("SFAUDIO001");
        add("CF1068");
        add("CF1069");
        add("CF1070");
        add("CF1071");
        add("CF1072");
        add("CF01219273");
        add("ULTRA001");
        add("ULTRA002");
        add("ULTRA003");
        add("READING");
        add("INSTALLATION");
        add("MASTER-CLASS-NUTRITION");
        add("MASTER-CLASS-DOCTOR");
        add("MASTER-CLASS-FITNESS");
        add("MASTER-CLASS-GENERIC");
        add("MASTER-CLASS-JUICE");
        add("SF_GROUP_SNC");
        add("SF_GROUP_YOGA");
        add("SFMUSIC001");
        add("SFBODYTONING001");
        add("BCA001");
        add("SFVRCHROMO001");
        add("SFMASSAGE001");
        add("SFPD001");
        add("SF-FITNESS-CLASS");
    }};
    public static final String FREEMIUM_WELLNESS_ENABLED_USERS_SEGMENT = "freemium_wellness_enabled_users_segment";
    public static final String INFINITY_SYMBOL = "∞";
    final static String BUNDLE_SPECS_DOCTOR_TYPE_KEY = "doctorTypes";
    final static String BUNDLE_SPECS_COACH_TYPE_KEY = "coachTypes";
    static final String SHIFU_SUGARFIT_TENANT = "SUGARFIT";
    static final String SHIFU_ULTRAFIT_TENANT = "ULTRAFIT";
    static final String RENEWAL_FLAG_KEY = "isRenewal";
    static final String MAX_START_DATE_DELAY_DAYS_KEY = "maxDelayStartDays";
    public static final String MASTER_CLASS_TAG_PREFIX = "SF-MASTER-CLASS";
    public static final String MASTER_CLASS_FITNESS = "MASTER-CLASS-FITNESS";
    static final String COACH_AUDIO_CALL_ICON_URL = "image/chroniccare/phone_callback_icon.png";
    final ServiceInterfaces serviceInterfaces;
    final ShifuClientConfiguration shifuClientConfiguration;
    final CommonHttpHelper commonHttpHelper;
    final UserOnboardingService userOnboardingService;
    final ExceptionReportingService exceptionReportingService;
    final ChronicCarePatientService chronicCarePatientService;
    final DeviceService deviceService;
    final ObjectMapper objectMapper;
    final CHSClient chsClient;
    final PollSupportClient pollSupportClient;
    final MasterClassClient masterClassClient;
    final UserServiceClient userServiceClient;
    final SFChatClient sfChatClient;
    final CatalogClient catalogClient;

    final List<String> NOT_HIGHLIGHTED_GRADIENT_CSS = Arrays.asList(
            "#FFFFFF",
            "#F9CF87",
            "#FFFFFF",
            "#FCBA4A",
            "#FFFFFF",
            "#FFFFFF"
    );
    final SMSClient smsClient;
    final HousemdClient housemdClient;
    final AppConfigCache appConfigCache;
    final IndusClient indusClient;
    @Qualifier("cfApiRedisKeyValueStore")
    @Autowired
    KeyValueStore cfApiRedisKeyValueStore;
    @Getter
    ShifuClient sugarfitShifuClient;
    ShifuClient ultrafitShifuClient;
    final CustomerSupportClient customerSupportClient;
    final OrderService orderService;
    final SFFitnessClient sfFitnessClient;

    public static long dateDiffInDays(Long fromDate, Long toDate) {
        double difference = ((double) (toDate - fromDate)) / (86400000.0);
        return Math.round(difference);
    }

    public static long getStartOfCurrentMonth(String timezone) {
        Calendar cal = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime().getTime();
    }

    public static long getEndOfCurrentMonth(String timezone) {
        Calendar cal = Calendar.getInstance(TimeZone.getTimeZone(timezone));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTime().getTime();
    }

    public boolean isTierOneFreemiumUser(UserContext userContext) {
        try {
            String userCity = getUserCityId(userContext, serviceInterfaces);
            List<String> tierOneCityList = getFreemiumTierOneCityList();

            if (!CollectionUtils.isEmpty(tierOneCityList)) {
                return Objects.nonNull(tierOneCityList.stream().filter(city -> city.equalsIgnoreCase(userCity)).findFirst().orElse(null));
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public List<String> getFreemiumTierOneCityList() {
        try {
            return appConfigCache.getConfig("SF_FREEMIUM_TIER_ONE_CITY_LIST", new TypeReference<>() {
            }, new ArrayList<>());
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public static String getProductIdForPhleboConsult(SelfInstallationStatusResponse selfInstallationStatusResponse) {
        return selfInstallationStatusResponse.getConsultProductCode();
    }

    public static BaseWidgetNonVM buildMentalHealthPollWidget(UserContext userContext, PollEntry pollEntry) throws BaseException {
        try {
            if (pollEntry.getStatus() != null && pollEntry.getStatus().equals(Status.PUBLISHED)) {
                boolean isCompleted = pollEntry.getPollStatus() != null && pollEntry.getPollStatus().equals(PollStatus.COMPLETED);
                if (!isCompleted) {
                    String pageUrl = "curefit://sfpolldetailspage?pollId=" + pollEntry.getId();
                    String actionTitle = "Mental Health";

                    SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
                    List<BannerItem> bannerItemList = new ArrayList<>();

                    BannerItem bannerItem = new BannerItem();
                    bannerItem.setImage("image/chroniccare/marketing/banners/home/<USER>/mental_health_v1.png");
                    bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url(pageUrl).title(actionTitle).build());
                    bannerItemList.add(bannerItem);
                    sfBannerCarouselWidget.setData(bannerItemList);

                    Map<String, Object> layoutProps = new HashMap<>();
                    layoutProps.put("roundedCorners", false);
                    layoutProps.put("bannerHeight", 133);
                    layoutProps.put("bannerWidth", 335);
                    layoutProps.put("bannerOriginalHeight", 133);
                    layoutProps.put("bannerOriginalWidth", 335);
                    layoutProps.put("verticalPadding", 20);
                    if (isHomePageRevampEnabled(userContext)) {
                        layoutProps.put("backgroundColor", "#FFFFFF");
                    }
                    sfBannerCarouselWidget.setLayoutProps(layoutProps);
                    return sfBannerCarouselWidget;
                }
            }
        } catch (Exception e) {
            log.error("Mental Health Poll Widget | Exception:: " + e);
        }
        return null;
    }

    public SfDoctorRecommendedTestWidget buildDiagnosticsWidgetForPack(SfDoctorRecommendedTestWidget widget, String userId, Long bookingId, Boolean isPackExpired, String title)  {
        try {
            Header header = new Header();
            header.setTitle(title);
            widget.setHeader(header);

            UserDiagnosticActionWithContext userDiagnosticContext =
                    serviceInterfaces.getSfAlbusClient().getSugarFitUserDiagnosticSellableActionWithContext(userId, bookingId);

            DiagnosticSellableAction sellableAction = userDiagnosticContext.getDiagnosticSellableContextActionWithContext().getAction();

            if (sellableAction.getIsUserEligible()) {
                DiagnosticSellableContext sellableContext =
                        userDiagnosticContext.getDiagnosticSellableContextActionWithContext().getContext();

                List<String> sellableProducts = sellableContext.getDiagnosticSellableProducts();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(sellableProducts)) {
                    String[] sellableProductsArray = sellableProducts.toArray(new String[0]);
                    ProductSellerResponse[] productList = serviceInterfaces.getFuseService().getProductDetails(sellableProductsArray);
                    if (productList!=null) {
                        List<SfDoctorRecommendedTestWidget.TestDetail> testDetailsList = new ArrayList<>();
                        Arrays.stream(productList).forEach(p -> {
                            SfDoctorRecommendedTestWidget.TestDetail testDetail = new SfDoctorRecommendedTestWidget.TestDetail();
                            testDetail.setTestId(p.getProduct().getCode());
                            testDetail.setTestName(p.getProduct().getName());
                            testDetail.setTestIncluded(p.getProduct().getItemsCount());
                            testDetailsList.add(testDetail);
                        });
                        widget.setTestDetails(testDetailsList);
                        if(isPackExpired){
                            widget.setTopRibbonVisible(true);
                            widget.setDoctorSuggestion("Doctors suggests health checkup every 3 months");
                        }else{
                            // Set Doctor Data
                            setDoctorData(widget,sellableContext.getDoctorId());
                        }
                        widget.setAction(Action.builder().actionType(ActionType.NAVIGATION)
                                .title("BOOK").url("curefit://sfdiagnosticstestlistpage").build());
                        return widget;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error building widget for active pack", e);
            throw new RuntimeException("Failed to build widget for active pack", e);
        }

        return null;
    }

    private void setDoctorData(SfDoctorRecommendedTestWidget widget, Long doctorId) {
        if (doctorId != null) {
            try {
                AssignedAgentData doctorData = new AssignedAgentData();
                doctorData.setAgentName(serviceInterfaces.getOllivanderAgentClient().getAgent(doctorId).getName());
                doctorData.setAgentImageUrl(serviceInterfaces.getOllivanderAgentClient().getAgent(doctorId).getDisplayImage());
                widget.setDoctorData(doctorData);
            } catch (OllivanderClientException e) {
                log.error("Error fetching doctor details for doctorId: {}", doctorId, e);
                throw new RuntimeException("Failed to fetch doctor details", e);
            }
        }
    }

    public static BaseWidgetNonVM buildPollWidgets(PollSupportClient pollSupportClient, UserServiceClient userServiceClient, UserContext userContext, PollEntry pollEntry, ContainerStyle containerStyle) throws BaseException {
        if (pollEntry.getStatus() != null && pollEntry.getStatus().equals(Status.PUBLISHED)) {
            // fetch the poll again to get all the details about questions in the PollEntry object
            PollEntry detailedPollEntry = pollSupportClient.fetchPoll(Long.valueOf(userContext.getUserProfile().getUserId()), pollEntry.getId());

            Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
            Date currentTime = calendar.getTime();
            Date pollExpiryTime = detailedPollEntry.getExpiryTime();
            Date nextDayOfExpiry = DateUtils.addDays(pollExpiryTime, 1);
            boolean isCompleted = detailedPollEntry.getPollStatus() != null && detailedPollEntry.getPollStatus().equals(PollStatus.COMPLETED);
            boolean hasMultipleQuestions = detailedPollEntry.getQuestions() != null && detailedPollEntry.getQuestions().size() > 1;
            String pageUrl = "curefit://sfpolldetailspage?pollId=" + detailedPollEntry.getId();
            String actionTitle = detailedPollEntry.getPollType() == PollType.INFO ? "Play Quiz" : "Vote now";
            if (isCompleted && hasMultipleQuestions) {
                pageUrl = "curefit://sfpollresult?actionType=VIEW_QUESTIONS&pollId=" + detailedPollEntry.getId();
                actionTitle = "View results";
            }

            List<String> userImages = new ArrayList<>();
            if (detailedPollEntry.getPollCompletedUserIds() != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(detailedPollEntry.getPollCompletedUserIds())) {
                for (int i = 0; i < detailedPollEntry.getPollCompletedUserIds().size(); i++) {
                    String votedUserId = String.valueOf(detailedPollEntry.getPollCompletedUserIds().get(i));
                    try {
                        UserEntry userEntry = userServiceClient.getUser(votedUserId).get();
                        String userProfilePic = userEntry.getProfilePictureUrl();
                        if (userProfilePic != null && !userProfilePic.isEmpty()) {
                            // Only 2 pictures to display on UI
                            if (userImages.size() < 3) {
                                userImages.add(userProfilePic);
                                if (userImages.size() == 2) break;
                            }
                        }
                    } catch (Exception e) {
                        // Ignore
                    }
                }

            }

            if (currentTime.after(detailedPollEntry.getStartTime()) && currentTime.before(detailedPollEntry.getExpiryTime())) {
                if (!isCompleted) {
                    SfPollCardWidget pollCardWidget = new SfPollCardWidget();
                    pollCardWidget.setPollId(detailedPollEntry.getId());
                    pollCardWidget.setPollType(detailedPollEntry.getPollType());
                    pollCardWidget.setTitle(detailedPollEntry.getName());
                    pollCardWidget.setVotedUserImages(userImages);
                    pollCardWidget.setTime(detailedPollEntry.getStartTime());
                    pollCardWidget.setVotesCount(detailedPollEntry.getVoteCount());
                    pollCardWidget.setCardAction(Action.builder().actionType(ActionType.NAVIGATION).url(pageUrl).title(actionTitle).build());
                    return pollCardWidget;
                }
            } else if (currentTime.after(pollExpiryTime) && currentTime.before(nextDayOfExpiry)) {
                if (isCompleted && detailedPollEntry.getPollType() == PollType.POLL) {
                    AtomicReference<String> userSelectedOption = new AtomicReference<>("");
                    AtomicReference<Long> resultInPercent = new AtomicReference<>(0L);
                    AtomicReference<QuestionOptionsEntry> highestVotedOption = new AtomicReference<>();
                    try {
                        Long userVotedId = detailedPollEntry.getQuestions().get(0).getUserVote().getVoteOptions().get(0);
                        QuestionEntry questionEntry = detailedPollEntry.getQuestions().get(0);
                        questionEntry.getOptions().forEach(opt -> {
                            if (highestVotedOption.get() == null || highestVotedOption.get().getVoteCount().compareTo(opt.getVoteCount()) < 0) {
                                highestVotedOption.set(opt);
                            }
                            if (Objects.equals(opt.getId(), userVotedId)) {
                                userSelectedOption.set(opt.getOptionValue());
                                Long percent = (long) ((float) opt.getVoteCount() / detailedPollEntry.getVoteCount() * 100);
                                resultInPercent.set(percent);
                            }
                        });
                    } catch (Exception e) {
                        // Ignore
                    }

                    SfPollResultCardWidget pollResultCardWidget = new SfPollResultCardWidget();
                    pollResultCardWidget.setPollId(detailedPollEntry.getId());
                    pollResultCardWidget.setTitle(detailedPollEntry.getName());
                    pollResultCardWidget.setVotedUserImages(userImages);
                    pollResultCardWidget.setTime(detailedPollEntry.getStartTime());
                    pollResultCardWidget.setVotesCount(detailedPollEntry.getVoteCount());
                    pollResultCardWidget.setOptionsCount(detailedPollEntry.getQuestions().get(0).getOptions().size());
                    pollResultCardWidget.setSelectedOptionTitle(userSelectedOption.get());
                    pollResultCardWidget.setResultInPercent(resultInPercent.get());
                    pollResultCardWidget.setCardAction(Action.builder().actionType(ActionType.NAVIGATION).url(pageUrl).title(actionTitle).build());
                    pollResultCardWidget.setResultText(highestVotedOption.get() != null ?
                            String.format("Note: Based on the results, the highly voted option is %s", highestVotedOption.get().getOptionValue()) : "");

                    return pollResultCardWidget;
                }
            }
        }
        return null;
    }

    public static BaseWidgetNonVM buildPollWidgetV2(PollSupportClient pollSupportClient, UserServiceClient userServiceClient, UserContext userContext, PollEntry pollEntry) throws BaseException {
        if (pollEntry.getStatus() != null && pollEntry.getStatus().equals(Status.PUBLISHED)) {
            Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
            Date currentTime = calendar.getTime();
            boolean ddsEnabledUser = pollEntry.getPollType() == PollType.DDS && isMentalHealthEnabled(userContext);
            boolean isSurvey = pollEntry.getPollType() == PollType.SURVEY || ddsEnabledUser;
            boolean ongoingPoll = pollEntry.getStartTime()!=null && currentTime.after(pollEntry.getStartTime()) && pollEntry.getExpiryTime()!=null && currentTime.before(pollEntry.getExpiryTime());
            boolean isCompleted = pollEntry.getPollStatus() != null && pollEntry.getPollStatus().equals(PollStatus.COMPLETED);
            boolean viewResults = pollEntry.getExpiryTime()!=null && currentTime.after(pollEntry.getExpiryTime()) && currentTime.before(DateUtils.addDays(pollEntry.getExpiryTime(), 1)); // show result up to 1day after poll has ended

            String pageUrl = "curefit://sfpolldetailspage?pollId=" + pollEntry.getId();
            String actionTitle = pollEntry.getPollType() == PollType.INFO ? "Play Quiz" : isSurvey ? "Take Survey" : "Vote now";

            if(isCompleted) {
                actionTitle = "View results";
            }
            boolean hasMultipleQuestions = pollEntry.getQuestions() != null && pollEntry.getQuestions().size() > 1;

            if (isCompleted && hasMultipleQuestions) {
                pageUrl = "curefit://sfpollresult?actionType=VIEW_QUESTIONS&pollId=" + pollEntry.getId();
            }

            if (((ongoingPoll || isSurvey) && !isCompleted) || viewResults){
                SfPollCardWidgetV2 pollCardWidget = new SfPollCardWidgetV2();
                pollCardWidget.setPollId(pollEntry.getId());
                pollCardWidget.setPollType(pollEntry.getPollType());
                pollCardWidget.setSubtitle(ddsEnabledUser? "Test your": null);
                pollCardWidget.setTitle(pollEntry.getName());
                pollCardWidget.setTime(pollEntry.getExpiryTime());
                pollCardWidget.setVotesCount(pollEntry.getVoteCount());
                pollCardWidget.setCardAction(Action.builder().actionType(ActionType.NAVIGATION).url(pageUrl).title(actionTitle).build());
                return pollCardWidget;
            }
        }
        return null;
    }

    public static SfChallengeCardWidgetV2 buildChallengeCardWidgetV2(UserContext userContext, UserChallengeSummary userChallengeSummary, List<ChallengesUserResponseEntry> userResponseEntries, ServiceInterfaces serviceInterfaces) throws HttpException, OllivanderClientException {
        ChallengesEntry challengesEntry = userChallengeSummary.getChallengesEntry();
        boolean hasChallengeStarted = !SfDateUtils.isFutureDate(userContext, challengesEntry.getStartDate());
        boolean hasUserJoined = challengesEntry.getUserJoinedDate() != null;
        boolean hasChallengeEnded = userChallengeSummary.getIsChallengeClosed();
        boolean isReferralChallenge = challengesEntry.getChallengeType().equals(ChallengeType.REFERRAL_CHALLENGE);
        boolean isIndividual = challengesEntry.getChallengeAudience().equals(ChallengeAudienceType.INDIVIDUAL_CHALLENGE);

        SfChallengeCardWidgetV2.UserProgress userProgress = new SfChallengeCardWidgetV2.UserProgress();
        SfChallengeCardWidgetV2 challengeCardWidget = new SfChallengeCardWidgetV2();
        challengeCardWidget.setChallengeId(challengesEntry.getId());
        challengeCardWidget.setTitle(challengesEntry.getTitle());
        challengeCardWidget.setDuration(challengesEntry.getDuration());
        challengeCardWidget.setStartTime(challengesEntry.getStartDate());
        challengeCardWidget.setEndTime(challengesEntry.getEndDate());
        challengeCardWidget.setUserJoinedDate(challengesEntry.getUserJoinedDate());
        challengeCardWidget.setChallengeAudienceType(challengesEntry.getChallengeAudience());
        challengeCardWidget.setChallengeType(challengesEntry.getChallengeType());
        challengeCardWidget.setTopLeaderBoard(userChallengeSummary.getTopLeaderBoard());
        challengeCardWidget.setShowLeaderBoardResults(hasChallengeEnded);
        List<String> gradientColors = new ArrayList<>();
        gradientColors.add("#FFFFFF33");
        gradientColors.add("#FFFFFF00");
        challengeCardWidget.setCardBgGradientColors(gradientColors);
        challengeCardWidget.setCardBgColor("#624BA3");
        challengeCardWidget.setCardTextHighlightColor("#7A00D6");

        if (challengesEntry.getChallengeTarget() != null) {
            challengeCardWidget.setGoal(challengesEntry.getChallengeTarget().getStepsTarget());
        }

        if (challengesEntry.getChallengeType().equals(ChallengeType.STEPS_CHALLENGE)){
            challengeCardWidget.setTopImageUrl("image/chroniccare/home/<USER>/challenge_card_4.png");
            Action fitnessSyncAction = SfBadgeUtils.getSyncFitnessDeviceAction(userContext, serviceInterfaces);
            if (fitnessSyncAction.getMeta() instanceof FitnessDeviceSyncMeta) {
                challengeCardWidget.setStepsLastSyncedDisplayString(
                        ((FitnessDeviceSyncMeta) fitnessSyncAction.getMeta()).getLastSyncedDisplayTime());
            }
        }else if (challengesEntry.getChallengeType().equals(ChallengeType.WORKOUT_CHALLENGE)){
            challengeCardWidget.setTopImageUrl("image/chroniccare/home/<USER>/challenge_card_3.png");
        }else if (challengesEntry.getChallengeType().equals(ChallengeType.SUGAR_LOG_CHALLENGE)){
            challengeCardWidget.setTopImageUrl("image/chroniccare/home/<USER>/challenge_card_5.png");
        }else if (challengesEntry.getChallengeType().equals(ChallengeType.FOOD_LOG_CHALLENGE)){
            challengeCardWidget.setTopImageUrl("image/chroniccare/home/<USER>/challenge_card_1.png");
        }else if(challengesEntry.getChallengeType().equals(ChallengeType.REFERRAL_CHALLENGE)){
            challengeCardWidget.setTopImageUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/Referral_App%20288%20x%20671_Without%20CTA-2025-05-05-08:31.png");
            challengeCardWidget.setSubtitle("Earn rewards on each referral");
        }

        if (userChallengeSummary.getUserLeaderboard() != null) {
            userProgress.setRank(userChallengeSummary.getUserLeaderboard().getRank());
            userProgress.setPoints(userChallengeSummary.getUserLeaderboard().getPoints());
            challengeCardWidget.setUserProgress(userProgress);
        }

        Action primaryAction = new Action();
        primaryAction.setTitle(hasChallengeEnded || hasUserJoined ? "View Leaderboard" : isReferralChallenge ? "Refer Now" : "Join Challenge");
        String tabKey = "ABOUT";
        if (hasChallengeEnded || hasUserJoined) {
            tabKey = "LEADERBOARD";
        }
        primaryAction.setUrl("curefit://sfchallengedetailspage?challengeId=" + challengesEntry.getId() + "&tabKey=" + tabKey);
        primaryAction.setActionType(ActionType.NAVIGATION);
        challengeCardWidget.setPrimaryAction(primaryAction);

        if (hasChallengeStarted && hasUserJoined) {
            List<SfChallengeUserEntry> challengeUserEntries = new ArrayList<>();
            if (challengesEntry.getChallengeType().equals(ChallengeType.REFERRAL_CHALLENGE)) {
                SfChallengeUserEntry sfChallengeUserEntry = new SfChallengeUserEntry();
                sfChallengeUserEntry.setChallengeType(challengesEntry.getChallengeType());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userResponseEntries)) {
                    SfChallengeUserEntry.UserValue userValue = new SfChallengeUserEntry.UserValue();
                    userValue.setUserReferral(userResponseEntries.getFirst().getUserResponseValue().getUserReferral());
                    sfChallengeUserEntry.setUserValue(userValue);
                }
                challengeUserEntries.add(sfChallengeUserEntry);

            } else {
                Date date = challengesEntry.getStartDate();
                while (!date.after(challengesEntry.getEndDate())) {
                    SfChallengeUserEntry sfChallengeUserEntry = new SfChallengeUserEntry();
                    sfChallengeUserEntry.setActivityTime(date);
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userResponseEntries)) {
                        Date finalDate = date;
                        Optional<ChallengesUserResponseEntry> userResponseEntryOptional = userResponseEntries.stream().filter(e -> SfDateUtils.isSameDay(finalDate, e.getActivityTime())).findFirst();
                        if (userResponseEntryOptional.isPresent()) {
                            ChallengesUserResponseEntry userEntry = userResponseEntryOptional.get();
                            sfChallengeUserEntry.setEntryStatus(SfChallengeUserEntry.EntryStatus.ENTERED);
                            sfChallengeUserEntry.setApprovalStatus(userEntry.getApprovalStatus());
                            sfChallengeUserEntry.setChallengeType(userEntry.getChallengeType());
                            sfChallengeUserEntry.setActivityTime(userEntry.getActivityTime());

                            SfChallengeUserEntry.UserValue userValue = new SfChallengeUserEntry.UserValue();
                            switch (userEntry.getChallengeType()) {
                                case STEPS_CHALLENGE -> {
                                    userValue.setSteps(userEntry.getUserResponseValue().getUserSteps().getStepsCount());
                                }
                                case SUGAR_LOG_CHALLENGE -> {
                                    userValue.setSugarValue(
                                            userEntry.getUserResponseValue().getGlucometerReadingEntry().getValue());
                                }
                                default -> {
                                    userValue.setCaption(userEntry.getUserResponseValue().getCaption());
                                    if (userEntry.getUserResponseValue().getAttachments() != null) {
                                        List<SfAttachment> attachments = new ArrayList<>();
                                        List<com.sugarfit.challenges.pojo.Attachments> attachmentsFromBE = userEntry.getUserResponseValue().getAttachments().values().stream().toList();
                                        attachmentsFromBE.forEach(a -> {
                                            if (a != null) {
                                                SfAttachment sfAttachment = new SfAttachment();
                                                sfAttachment.setFileUrl(a.getAttachmentUrl());
                                                sfAttachment.setMimeType(a.getMimeType());
                                                attachments.add(sfAttachment);
                                            }
                                        });
                                        userValue.setAttachments(attachments);
                                    }
                                }
                            }

                            sfChallengeUserEntry.setUserValue(userValue);
                        } else {
                            if (SfDateUtils.isPastDate(userContext, date)) {
                                sfChallengeUserEntry.setEntryStatus(SfChallengeUserEntry.EntryStatus.MISSED);
                            } else if (isFutureDate(userContext, date)) {
                                sfChallengeUserEntry.setEntryStatus(SfChallengeUserEntry.EntryStatus.LOCKED);
                            } else {
                                sfChallengeUserEntry.setEntryStatus(SfChallengeUserEntry.EntryStatus.OPEN);
                            }
                        }
                    } else {
                        if (SfDateUtils.isPastDate(userContext, date)) {
                            sfChallengeUserEntry.setEntryStatus(SfChallengeUserEntry.EntryStatus.MISSED);
                        } else if (isFutureDate(userContext, date)) {
                            sfChallengeUserEntry.setEntryStatus(SfChallengeUserEntry.EntryStatus.LOCKED);
                        } else {
                            sfChallengeUserEntry.setEntryStatus(SfChallengeUserEntry.EntryStatus.OPEN);
                        }
                    }


                    challengeUserEntries.add(sfChallengeUserEntry);
                    Calendar c = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
                    c.setTime(date);
                    c.add(Calendar.DATE, 1);
                    date = c.getTime();
                }
            }
            challengeCardWidget.setUserEntries(challengeUserEntries);
        }

        if (hasChallengeEnded) {
            SfChallengeCardWidgetV2.WinnerData winnerData = new SfChallengeCardWidgetV2.WinnerData();
            if (userChallengeSummary.getCoachLeaderboard() != null) {
                winnerData.setCoachLeaderboardItem(ChronicCareAppUtil.getChallengeLeaderboardItem(userChallengeSummary.getCoachLeaderboard(), true, serviceInterfaces.getChronicCareServiceHelper()));
            }
            if (userChallengeSummary.getUserLeaderboard() != null) {
                winnerData.setUserLeaderboardItem(ChronicCareAppUtil.getChallengeLeaderboardItem(userChallengeSummary.getUserLeaderboard(), true, serviceInterfaces.getChronicCareServiceHelper()));
            }
            challengeCardWidget.setWinnerData(winnerData);
        }

        return challengeCardWidget;
    }

    public static SfChallengeCardWidget buildChallengeCardWidget(UserContext userContext, ChallengesEntry challengesEntry, List<ChallengesUserResponseEntry> challengesUserResponseEntries,
                                                                 List<LeaderboardEntry> leaderboardEntries, ServiceInterfaces serviceInterfaces, SMSClient smsClient) throws HttpException, OllivanderClientException {
        boolean isIndividual = challengesEntry.getChallengeAudience().equals(ChallengeAudienceType.INDIVIDUAL_CHALLENGE);
        boolean hasChallengeEnded = SfDateUtils.isPastDate(userContext, challengesEntry.getEndDate());
        boolean hasUserJoined = challengesEntry.getUserJoinedDate() != null;
        boolean hasChallengeStarted = !SfDateUtils.isFutureDate(userContext, challengesEntry.getStartDate());
        boolean notEligible = false;
        int minimumParticipationLimit = 0;
        List<String> gradientColors = new ArrayList<String>();
        List<LeaderboardEntry> winners = new ArrayList<LeaderboardEntry>();
        SfChallengeCardWidget.UserProgress userProgress = new SfChallengeCardWidget.UserProgress();
        SfChallengeCardWidget challengeCardWidget = new SfChallengeCardWidget();
        Action primaryAction = new Action();
        primaryAction.setTitle(hasChallengeEnded && hasUserJoined ? "View Leaderboard" : "Join Challenge");
        String tabKey = "ABOUT";
        if (hasUserJoined && hasChallengeStarted) {
            tabKey = "LEADERBOARD";
        }
        primaryAction.setUrl("curefit://sfchallengedetailspage?challengeId=" + challengesEntry.getId() + "&tabKey=" + tabKey);
        primaryAction.setActionType(ActionType.NAVIGATION);
        challengeCardWidget.setTitle(challengesEntry.getTitle());
        challengeCardWidget.setBadgeTitle(isIndividual ? "INDIVIDUAL CHALLENGE" : "BATTLE OF COACHES");
        challengeCardWidget.setDuration(challengesEntry.getDuration());
        challengeCardWidget.setStartTime(challengesEntry.getStartDate());
        challengeCardWidget.setEndTime(challengesEntry.getEndDate());
        challengeCardWidget.setUserJoinedDate(challengesEntry.getUserJoinedDate());
        challengeCardWidget.setChallengeAudienceType(challengesEntry.getChallengeAudience());
        if (challengesUserResponseEntries != null && !challengesUserResponseEntries.isEmpty()) {
            challengeCardWidget.setLastEntryDate(challengesUserResponseEntries.get(challengesUserResponseEntries.size() - 1).getActivityTime());
        }
        if (isIndividual) {
            gradientColors.add("#8791EF");
            gradientColors.add("#4461C8");
        } else {
            gradientColors.add("#816a9f");
            gradientColors.add("#3B225D");
        }

        if (isIndividual && challengesEntry.getEligibilityType().equals(EligibilityType.COMMUNITY)) {
            AssignedAgentResponse assignedAgentResponse = smsClient.getAssignedAgents(Long.valueOf(userContext.getUserProfile().getUserId()));
            if (assignedAgentResponse != null) {
                AgentResponse agent = null;
                try {
                    agent = serviceInterfaces.ollivanderAgentClient.getAgent(assignedAgentResponse.getAssignedCoachId());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (agent != null) {
                    challengeCardWidget.setExclusiveText(String.format("Coach %s's team", agent.getName()));
                }
            }
        }

        SfChallengeCardWidget.UserProfile selfCoachProfile = new SfChallengeCardWidget.UserProfile();
        SfChallengeCardWidget.OtherUsersData otherUsersData = new SfChallengeCardWidget.OtherUsersData();
        if (challengesEntry.getChallengeAudience().equals(ChallengeAudienceType.GROUP_CHALLENGE)) {
            AssignedAgentResponse assignedAgentResponse = smsClient.getAssignedAgents(Long.valueOf(userContext.getUserProfile().getUserId()));
            if (assignedAgentResponse != null) {
                AgentResponse agent = null;
                String imageUrl = null;
                String userName = "";
                try {
                    agent = serviceInterfaces.ollivanderAgentClient.getAgent(assignedAgentResponse.getAssignedCoachId());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (agent != null) {
                    imageUrl = agent.getDisplayImage();
                    userName = agent.getName();
                }
                selfCoachProfile.setName(userName);
                selfCoachProfile.setImageUrl(imageUrl);
            }
            AgentSearchRequestParam agentSearchRequestParam = new AgentSearchRequestParam();
            agentSearchRequestParam.setAgentType(AgentType.LIFESTYLE_COACH);
            agentSearchRequestParam.setAgentAssetsRequired(true);
            Page<AgentResponse> agentResponsePage = serviceInterfaces.ollivanderAgentClient.getAllAgentsPaginated(0, 10, agentSearchRequestParam);
            if (agentResponsePage != null) {
                otherUsersData.setTotalCount(agentResponsePage.getTotalElements());

                Map<Long, String> coachesData = new HashMap<>();
                coachesData.put(103144L, "/image/carefit/doctor/103144_DitiJiveshBakhtiani_dbd196c9-3419-4012-a8a0-3d5b7f067dde");
                coachesData.put(104529L, "/image/carefit/doctor/104529_VenusSingh_c0ad5ae0-b16e-492d-bd18-e32474aa5bbe");
                coachesData.put(103507L, "/image/carefit/doctor/103507_TanyaKhanna_d6b98df2-36ce-4d9c-a55f-4dbf41b047de");
                coachesData.put(104527L, "/image/carefit/doctor/104527_AyushiKamboj_58ed504c-b019-4ba1-991c-96989eb9c1de");
                coachesData.forEach((id, image) -> {
                    SfChallengeCardWidget.UserProfile userProfile = new SfChallengeCardWidget.UserProfile();
                    userProfile.setImageUrl(image);
                    otherUsersData.addUserProfile(userProfile);
                });
//                List<AgentResponse> agentResponses = agentResponsePage.getContent();
//                agentResponses.forEach(coach -> {
//                    if (coach != null && coach.getDisplayImage() != null) {
//                        //Only 3 pics needed for UI
//                        if (otherUsersData.getUserProfiles().size() < 3) {
//                            SfChallengeCardWidget.UserProfile userProfile = new SfChallengeCardWidget.UserProfile();
//                            userProfile.setName(coach.getName());
//                            userProfile.setImageUrl(coach.getDisplayImage());
//                            otherUsersData.addUserProfile(userProfile);
//                        }
//                    }
//                });
            }
        } else {
            String myUserId = userContext.getUserProfile().getUserId();
            try {
                UserEntry myUserEntry = serviceInterfaces.userServiceClient.getUser(myUserId).get();
                if (myUserEntry != null && myUserEntry.getProfilePictureUrl() != null) {
                    selfCoachProfile.setName(myUserEntry.getFirstName() + " " + myUserEntry.getLastName());
                    selfCoachProfile.setImageUrl(myUserEntry.getProfilePictureUrl());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            List<Long> userIdsList;
            if (!CollectionUtils.isEmpty(challengesEntry.getJoinedUserIds()) && challengesEntry.getParticipantCount() != null) {
                otherUsersData.setTotalCount(challengesEntry.getParticipantCount());
                userIdsList = challengesEntry.getJoinedUserIds();
            } else {
                otherUsersData.setTotalCount(10L);
                List<Long> prodUserIds = new ArrayList<Long>(List.of(192L, 3758L, 13014L, 14468L, 20472L, 21607L, 23465L, 26058L, 26919L, 27897L, 30643L, 31351L, 34629L, 35465L, 37118L, 37841L, 38364L, 38986L, 41828L, 44035L));
                List<Long> stageUserIds = new ArrayList<Long>(List.of(4080998L, 4087783L, 4087868L, 4087783L, 4080998L, 4087783L));
                userIdsList = serviceInterfaces.environmentService.isStage() ? stageUserIds : prodUserIds;
                Collections.shuffle(userIdsList);
            }

            userIdsList.forEach(userId -> {
                UserEntry userEntry = null;
                try {
                    userEntry = serviceInterfaces.userServiceClient.getUser(String.valueOf(userId)).get();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (userEntry != null) {
                    String userProfilePic = userEntry.getProfilePictureUrl();
                    if (userProfilePic != null && !userProfilePic.isEmpty()) {
                        //Only 3 pics needed for UI
                        if (otherUsersData.getUserProfiles().size() < 4) {
                            SfChallengeCardWidget.UserProfile userProfile = new SfChallengeCardWidget.UserProfile();
                            String fName = Objects.nonNull(userEntry.getFirstName()) ? userEntry.getFirstName().trim() : "";
                            String lName = Objects.nonNull(userEntry.getLastName()) ? userEntry.getLastName().trim() : "";
                            userProfile.setName(fName + " " + lName);
                            userProfile.setImageUrl(userProfilePic);
                            otherUsersData.addUserProfile(userProfile);
                        }
                    }
                }
            });
        }
        challengeCardWidget.setSelfProfile(selfCoachProfile);
        challengeCardWidget.setOtherUsersData(otherUsersData);

        if (!CollectionUtils.isEmpty(leaderboardEntries)) {
            // Self user will be at 0th position
            LeaderboardEntry leaderboardEntry = leaderboardEntries.get(0);
            if (leaderboardEntry != null) {
                if (leaderboardEntry.getRank() != null) {
                    userProgress.setRank(leaderboardEntry.getRank());
                }
                if (leaderboardEntry.getPoints() != null) {
                    userProgress.setPoints(leaderboardEntry.getPoints());
                }
                if (userProgress != null) {
                    challengeCardWidget.setUserProgress(userProgress);
                }
                if (leaderboardEntry.getRankEligibility() != null) {
                    notEligible = leaderboardEntry.getRankEligibility() == RankEligibilityType.NOT_ELIGIBLE;
                }
                if (leaderboardEntry.getMinimumParticipationLimit() != null) {
                    minimumParticipationLimit = leaderboardEntry.getMinimumParticipationLimit();
                }
            }

            if (hasUserJoined && !isIndividual && notEligible && minimumParticipationLimit >= 0) {
                challengeCardWidget.setErrorMsg((minimumParticipationLimit - leaderboardEntry.getUserLeaderBoardEntries().size()) + " more participants required to be eligible");
            }

            if (hasChallengeEnded && leaderboardEntries.size() >= 3) {
                primaryAction.setTitle("See Leaderboard");
                try {
                    leaderboardEntries.forEach(e -> {
                        if (e.getRank() != null) {
                            winners.add(e);
                        }
                    });
                    if (winners.size() >= 3) {
                        winners.sort(Comparator.comparingInt(LeaderboardEntry::getRank));
                    }
                    List<SfChallengeLeaderboardItem> leaderboardItems = winners.stream().map(winner -> getChallengeLeaderboardItem(winner, true, serviceInterfaces.getChronicCareServiceHelper())).toList();
                    challengeCardWidget.setLeaderboardItems(leaderboardItems);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        challengeCardWidget.setGradientColors(gradientColors);
        challengeCardWidget.setChallengeId(challengesEntry.getId());
        challengeCardWidget.setPrimaryAction(primaryAction);
        if (hasChallengeEnded && CollectionUtils.isEmpty(challengeCardWidget.getLeaderboardItems())) {
            return null;
        }
        return challengeCardWidget;
    }

    @PostConstruct
    public void initialise() {
        ShifuClientConfiguration sugarfitConfig = new ShifuClientConfiguration();
        sugarfitConfig.setSHIFU_API_KEY(this.shifuClientConfiguration.getSHIFU_API_KEY());
        sugarfitConfig.setSHIFU_BASE_URL(this.shifuClientConfiguration.getSHIFU_BASE_URL());
        sugarfitConfig.setSHIFU_TENANT(SHIFU_SUGARFIT_TENANT);

        ShifuClientConfiguration ultrafitConfig = new ShifuClientConfiguration();
        ultrafitConfig.setSHIFU_API_KEY(this.shifuClientConfiguration.getSHIFU_API_KEY());
        ultrafitConfig.setSHIFU_BASE_URL(this.shifuClientConfiguration.getSHIFU_BASE_URL());
        ultrafitConfig.setSHIFU_TENANT(SHIFU_ULTRAFIT_TENANT);

        log.info(String.format("Setting up shifu client with tenant :: %s", SHIFU_SUGARFIT_TENANT));
        sugarfitShifuClient = new ShifuClient(sugarfitConfig, commonHttpHelper);

        log.info(String.format("Setting up shifu client with tenant :: %s", SHIFU_ULTRAFIT_TENANT));
        ultrafitShifuClient = new ShifuClient(ultrafitConfig, commonHttpHelper);
    }

    public ShifuClient getShifuClientFromContext(UserContext userContext) {
        return AppUtil.isUltraFitApp(userContext) ? ultrafitShifuClient : sugarfitShifuClient;
    }

    public String getCoachConsultationProduct(BundleProduct pack) {
        JsonNode productSpecs = pack.getProductSpecs();
        Set<String> productCodesFromProductSpecs = getProductCodesFromProductSpecs(productSpecs, BUNDLE_SPECS_COACH_TYPE_KEY);
        return productCodesFromProductSpecs.stream().findFirst().get();
    }

    public String getDoctorConsultationProduct(BundleProduct pack) {
        JsonNode productSpecs = pack.getProductSpecs();
        Set<String> productCodesFromProductSpecs = getProductCodesFromProductSpecs(productSpecs, BUNDLE_SPECS_DOCTOR_TYPE_KEY);
        return productCodesFromProductSpecs.stream().findFirst().get();
    }

    private Set<String> getProductCodesFromProductSpecs(JsonNode productSpecs, String jsonKey) {
        Set<String> doctorProductCodes = new HashSet<>();
        ArrayNode doctorProductCodesJson = ((ArrayNode) productSpecs.get(jsonKey));
        for (Iterator<JsonNode> it = doctorProductCodesJson.elements(); it.hasNext(); ) {
            doctorProductCodes.add(it.next().textValue());
        }
        return doctorProductCodes;
    }

    public WeeklyProgressResponse getWeeklyProgressReportFromShifu(UserContext userContext, Long userId, long fromEpoch, long toEpoch) throws Exception {
        try {
            return this.getShifuClientFromContext(userContext).getUserWeeklyProgressReport(userId, fromEpoch, toEpoch, true, true);
        } catch (Exception e) {
            String message = String.format("Error in contacting shifu for user :: %s, fromEpoch :: %s, toEpoch :: %s, error :: %s",
                    userId, fromEpoch, toEpoch, e.getMessage());
            log.error(message, e);
            throw new Exception(message, e);
        }
    }

    public CompletableFuture<List<UserActivityEntry>> getUserActivitiesFromShifuFuture(UserContext userContext, String userId, Date date, String timeZone) {
        return supplyAsync(() -> {
            try {
                return getUserActivitiesFromShifu(userContext, userId, date, timeZone);
            } catch (Exception e) {
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public List<UserActivityEntry> getUserActivitiesFromShifu(UserContext userContext, String userId, Date date, String timeZone) throws Exception {
        List<UserActivityEntry> userActivitiesByUserIdAndDate;
        try {
            userActivitiesByUserIdAndDate = getShifuClientFromContext(userContext).getUserActivitiesByUserIdAndDate(Long.parseLong(userId), date.getTime(), timeZone, false, true).get();
            log.debug(String.format("Got Activities for user :: %s, date :: %s, shifu response :: %s", userId, date, userActivitiesByUserIdAndDate));
        } catch (Exception e) {
            String message = String.format("Error in contacting shifu for date :: %s, user :: %s, errror :: %s",
                    date, userId, e.getMessage());
            log.error(message, e);
            throw new Exception(message, e);
        }
        return userActivitiesByUserIdAndDate;
    }

    public UserGoalSummary getShifuUserGoalSummary(UserContext userContext, Long userId, boolean appendDefaultGoals, String timeZone) throws BaseException {
        String shifuTenant = AppUtil.isUltraFitApp(userContext) ? SHIFU_ULTRAFIT_TENANT : SHIFU_SUGARFIT_TENANT;
        return this.getShifuClientFromContext(userContext).getUserGoalSummary(userId, shifuTenant, appendDefaultGoals, timeZone);
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    public List<GoalWithData> getShifuRecentGoals(UserContext userContext, Long userId, Date date, boolean appendDefaultGoals, String timeZone) throws BaseException {
        String shifuTenant = AppUtil.isUltraFitApp(userContext) ? SHIFU_ULTRAFIT_TENANT : SHIFU_SUGARFIT_TENANT;
        return this.getShifuClientFromContext(userContext).getRecentUserGoals(userId, date.getTime(), shifuTenant, null, appendDefaultGoals, timeZone).get();
    }

    public List<UserGoalEntry> getUserActiveGoals(UserContext userContext, Long userId) throws BaseException {
        String shifuTenant = AppUtil.isUltraFitApp(userContext) ? SHIFU_ULTRAFIT_TENANT : SHIFU_SUGARFIT_TENANT;
        return this.getShifuClientFromContext(userContext).getActiveGoals(userId, shifuTenant);
    }

    public static Action getCallAction() throws BaseException {
        Map<String, Object> meta = new HashMap<>();
        meta.put("phoneNumber", "+916361819079");
        return Action.builder().actionType(ActionType.CALL_NUMBER).meta(meta).build();
    }

    public UserActivityEntry updateInterventionActivity(UserContext userContext, InterventionsStatusUpdateRequest req) throws Exception {
        String channel = "APP";
        HashMap<String, Object> meta = null;
        if (req.getAnalytics() != null && StringUtils.hasText(req.getAnalytics().getChannel())) {
            channel = req.getAnalytics().getChannel();
            if (!channel.equalsIgnoreCase("APP")) {
                if (!StringUtils.isEmpty(req.getAnalytics().getChannelId())) {
                    meta = new HashMap<>();
                    meta.put("notification_id", req.getAnalytics().getChannelId());
                }
            }
        }
        UserActivityStatusUpdateRequest userActivityStatusUpdateRequest = UserActivityStatusUpdateRequest.builder()
                .userActivityId(req.getUserActivityId())
                .response(req.getResponse())
                .channel(channel)
                .meta(meta)
                .build();
        return getShifuClientFromContext(userContext).updateUserActivityStatus(userActivityStatusUpdateRequest);
    }

    public List<BundleSellableProduct> getBundleProductsByCodes(List<String> productCodes) {
        try {
            List<BundleSellableProduct> bundleSellableProducts = new ArrayList<>();
            productCodes.forEach(productCode-> {
                Optional<BundleSellableProduct> bundleSellableProduct = getBundleProductByCode(productCode);
                if (bundleSellableProduct.isPresent()) {
                    bundleSellableProducts.add(bundleSellableProduct.get());
                }
            });
            return bundleSellableProducts;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Optional<BundleSellableProduct> getBundleProductByCode(String productCode) {
        List<BundleSellableProduct> bundleProducts = serviceInterfaces.getSfAlbusClient().bundleProductSearch(Collections.singletonList(productCode), false, false, null);
        if (CollectionUtils.isEmpty(bundleProducts)) {
            return Optional.empty();
        } else {
            return Optional.of(bundleProducts.get(0));
        }
    }

    public List<BundleSellableProduct> getAllBundles() {
        return this.serviceInterfaces.getSfAlbusClient().bundleProductSearch(BundleSubCategoryCode.CHRONIC_CARE_SUBSCRIPTION.name(),
                true, false, null);
    }

    public List<BundleSellableProduct> getAllRenewalPacks(String userId, String target, String tenant) {
        return this.serviceInterfaces.getSfAlbusClient().fetchRenewalPacks(Long.parseLong(userId), target, tenant);
    }

    public RenewSubscriptionPageView.SubCardData getRenewalSubCardData(UserContext userContext){
        try {
            RenewSubscriptionPageView.SubCardData subCard = new RenewSubscriptionPageView.SubCardData();
            subCard.setBgGradientColors(List.of("#EFD3FF", "#FFFFFF", "#EFD3FF"));
            subCard.setImage("image/chroniccare/community-users.png");
            subCard.setText("@{boldValue} from your community have renewed this plan");
            subCard.setBoldValues(List.of(
                    String.format("%s+", getVedicNumberFormat((long) ChronicCareAppUtil.generateRandomRenewalNumber()))
            ));
            return subCard;
        } catch (Exception e) {
            log.error("Error in getting renewal sub card", e);
            exceptionReportingService.reportException(String.format("Error in getting renewal sub card :: %s", e.getMessage()), e);
        }
        return null;
    }

    public List<SubscriptionPageItem> getPackMembershipBenefits(ActivePackResponse latestPack) {
        ArrayNode packFeatures = (ArrayNode) latestPack.getBundleProduct().getInfoSection().get("subscriptionBenefits");
        return getSubscriptionItems(packFeatures);
    }

    public List<SubscriptionCardWidget.IncludedItem> getSubscriptionPageFeatures(BundleSellableProduct bundleSellableProduct) {
        ArrayNode packFeatures = (ArrayNode) bundleSellableProduct.getInfoSection().get("packFeaturesSubscriptionPageV2");
        return getIncludedItems(packFeatures);
    }

    public List<SubscriptionCardWidget.IncludedItem> getIncludedItems(ArrayNode packFeatures) {
        List<SubscriptionCardWidget.IncludedItem> planDetails = new ArrayList<>();
        for (Iterator<JsonNode> it = packFeatures.elements(); it.hasNext(); ) {
            try {
                SubscriptionCardWidget.IncludedItem item = objectMapper.treeToValue(it.next(), SubscriptionCardWidget.IncludedItem.class);
                if (StringUtils.isEmpty(item.getValue()) && !CollectionUtils.isEmpty(item.getValues())) {
                    item.setValue(item.getValues().get(0));
                }
                planDetails.add(item);
            } catch (Exception e) {
                log.error("Error parsing included item :: {}", e.getMessage(), e);
                exceptionReportingService.reportException(String.format("Error parsing included item :: %s", e.getMessage()), e);
            }
        }
        return planDetails;
    }

    public List<String> getGradientColor(BundleSellableProduct bundleSellableProduct) {
        if (bundleSellableProduct.getInfoSection().get("renderingInfo").has("gradientColor")) {
            ArrayNode gradientColorList = (ArrayNode) bundleSellableProduct.getInfoSection().get("renderingInfo").get("gradientColor");
            List<String> gradientList = new ArrayList<>();
            for (Iterator<JsonNode> it = gradientColorList.elements(); it.hasNext(); ) {
                String gradient = it.next().asText();
                gradientList.add(gradient);
            }
            return gradientList;
        } else {
            return NOT_HIGHLIGHTED_GRADIENT_CSS;
        }
    }

    public List<SubscriptionPageItem> getSubscriptionItems(ArrayNode packFeatures) {
        List<SubscriptionPageItem> subscriptionPageItems = new ArrayList<>();
        Iterator<JsonNode> it = packFeatures.elements();
        try {
            while (it.hasNext()) {
                SubscriptionPageItem subscriptionPageItem = objectMapper.treeToValue(it.next(), SubscriptionPageItem.class);
                subscriptionPageItems.add(subscriptionPageItem);
            }
        } catch (Exception e) {
            String message = String.format("[SF] error parsing subscription item :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        return subscriptionPageItems;
    }

    public void addSubscriptionDetails(UserContext userContext, ActivePackResponse latestPack, SubscriptionCardWidget widget) throws HttpException {
        AppTenant appTenant = getAppTenantFromUserContext(userContext);
        widget.setPackType(latestPack.getBundleProduct().getName());
        long packDurationInDays = dateDiffInDays(latestPack.getStartDate(), latestPack.getEndDate());
        Date now = new Date();
        long daysRemainingFromEnd = dateDiffInDays(now.getTime(), latestPack.getEndDate());
        long daysPassedFromStart = dateDiffInDays(now.getTime(), latestPack.getStartDate());
        String packLengthText;
        if (packDurationInDays < 30) {
            packLengthText = String.format("%s days pack", packDurationInDays);
        } else if (packDurationInDays % 30 == 0) {
            packLengthText = String.format("%s month pack", packDurationInDays / 30);
        } else if (packDurationInDays == 365) {
            packLengthText = "1 year pack";
        } else {
            packLengthText = String.format("%s month %s day pack", packDurationInDays / 30, packDurationInDays % 30);
        }
        widget.setPackValidityText(packLengthText);

        double packUsageRatio = 100.0;
        if (new Date(latestPack.getStartDate()).after(now)) {
            // case when pack has not yet started
            widget.setPackValidityLeft(String.format("Starting after %s", getDaysRemainingText(daysPassedFromStart)));
            packUsageRatio = 1.0;
        } else if ((new Date(latestPack.getEndDate())).before(now)) {
            // case when pack is already expired
            widget.setPackValidityLeft(String.format("Expired %s days ago.", Math.abs(daysRemainingFromEnd)));
        } else {
            packUsageRatio = 100.0 - Math.min(100, (100.0 * (daysRemainingFromEnd)) / packDurationInDays);
            if (Objects.equals(AppTenant.ULTRAFIT, appTenant)) {
                CgmOnboardingStatusResponse cgmOnboardingStatusResponse = chsClient.fetchOnboardingStatus(
                        Long.valueOf(userContext.getUserProfile().getUserId()),
                        latestPack.getBookingId(), appTenant);
                if (cgmOnboardingStatusResponse.isAtleastOneCGMStarted()) {
                    addPackExpiryText(widget, daysRemainingFromEnd);
                } else {
                    widget.setPackValidityLeft("Pack will start after cgm installation");
                }
            } else {
                addPackExpiryText(widget, daysRemainingFromEnd);
            }
        }
        widget.setPackValidityValue(packUsageRatio);
        List<SubscriptionCardWidget.IncludedItem> memberships = new ArrayList<>();
        Map<String, UserMembershipInfo> productCodeToMembershipMap = latestPack.getUserMembershipInfos().stream().collect(Collectors.toMap(
                UserMembershipInfo::getProductCode,
                userMembershipInfo -> userMembershipInfo,
                (a1, a2) -> a1
        ));

        List<SubscriptionPageItem> packMembershipBenefits = getPackMembershipBenefits(latestPack);
        for (SubscriptionPageItem subscriptionPageItem : packMembershipBenefits) {
            SubscriptionCardWidget.IncludedItem includedItem = new SubscriptionCardWidget.IncludedItem();
            includedItem.setText(subscriptionPageItem.getText());
            includedItem.setIconUrl(subscriptionPageItem.getIconUrl());
            if (Boolean.TRUE.equals(subscriptionPageItem.getIsInfinity())) {
                includedItem.setIsInfinity(true);
                includedItem.setValue(INFINITY_SYMBOL);
            } else if (!StringUtils.isEmpty(subscriptionPageItem.getCount())) {
                includedItem.setIsInfinity(false);
                includedItem.setValue(subscriptionPageItem.getCount());
            } else {
                long ticketsTotal = 0;
                long ticketsConsumed = 0;
                boolean isInfinity = false;
                String count;
                if (subscriptionPageItem.getProductCodes() != null) {
                    for (String productCode : subscriptionPageItem.getProductCodes()) {
                        if (productCodeToMembershipMap.containsKey(productCode)) {
                            UserMembershipInfo userMembershipInfo = productCodeToMembershipMap.get(productCode);
                            ticketsTotal = ticketsTotal + userMembershipInfo.getTickets();
                            ticketsConsumed = ticketsConsumed + userMembershipInfo.getTicketsConsumed();
                        }
                    }
                }
                if (ticketsTotal == 0) {
                    continue;
                }
                if (ticketsTotal > 100) {
                    count = INFINITY_SYMBOL;
                    isInfinity = true;
                } else {
                    if (ticketsConsumed == 0) {
                        count = String.format("%d times", ticketsTotal);
                    } else {
                        count = String.format("%d/%s Done", ticketsConsumed, ticketsTotal);
                    }
                }
                includedItem.setValue(count);
                includedItem.setIsInfinity(isInfinity);
            }
            memberships.add(includedItem);
        }
        if (latestPack.getBundleProduct().getInfoSection().get("rank").asInt() != 1) {
            widget.setGradientColor(NOT_HIGHLIGHTED_GRADIENT_CSS);
        }
        widget.setIncludedItems(memberships);
    }

    private void addPackExpiryText(SubscriptionCardWidget widget, long daysRemainingFromEnd) {
        widget.setPackValidityLeft(String.format("Expiring after %s", getDaysRemainingText(daysRemainingFromEnd)));
    }

    public String getDaysRemainingText(long remainingDays) {
        if (remainingDays == 0) {
            return "today";
        } else if (remainingDays == 1) {
            return "tomorrow";
        } else {
            return String.format("%s days", remainingDays);
        }
    }

    public String getDaysRemainingTextUF(long remainingDays) {
        if (remainingDays == 0) {
            return "today";
        } else if (remainingDays == 1) {
            return "1 day";
        } else {
            return String.format("%s days", remainingDays);
        }
    }

    public boolean isRenewalProduct(BundleProduct bundleProduct) {
        return bundleProduct.getProductSpecs().has(RENEWAL_FLAG_KEY) && bundleProduct.getProductSpecs().get(RENEWAL_FLAG_KEY).asBoolean(false);
    }

    public long getMaxStartDateDelay(BundleProduct bundleProduct) {
        if (bundleProduct.getProductSpecs().has(MAX_START_DATE_DELAY_DAYS_KEY)) {
            return bundleProduct.getProductSpecs().get(MAX_START_DATE_DELAY_DAYS_KEY).asLong(0L);
        }
        return 0L;
    }

    public FitnessDeviceSyncMeta getFitnessDeviceSyncMeta(UserContext userContext) {
        FitnessDeviceSyncMeta meta = new FitnessDeviceSyncMeta();
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        String userTimeZone = userContext.getUserProfile().getTimezone();
        String activeDeviceId = null;
        ActiveDeviceEntry activeDeviceEntry = null;
        try {
            try {
                activeDeviceEntry = chsClient.fetchActiveDevice(userId);
                if (activeDeviceEntry != null && activeDeviceEntry.getDeviceId() != null) {
                    activeDeviceId = activeDeviceEntry.getDeviceId();
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Failed fetching active device id", e);
            }
            meta.setActiveDeviceId(activeDeviceId);

            if (activeDeviceEntry != null && activeDeviceEntry.getLastSyncedMetricDate() != null) {
                meta.setLastSyncedTime(
                        TimeUtil.getStartOfDay(activeDeviceEntry.getLastSyncedMetricDate(), userTimeZone).getTime());
            }
            if (activeDeviceEntry != null && activeDeviceEntry.getLastSyncedOn() != null) {
                DateFormat dateFormat = new SimpleDateFormat("dd MMM");
                dateFormat.setTimeZone(TimeZone.getTimeZone(userTimeZone));
                DateFormat timeFormat = new SimpleDateFormat("hh:mm a");
                timeFormat.setTimeZone(TimeZone.getTimeZone(userTimeZone));

                String lastSyncDisplayTime = isToday(activeDeviceEntry.getLastSyncedOn().getTime(), userTimeZone)
                        ? timeFormat.format(activeDeviceEntry.getLastSyncedOn())
                        : dateFormat.format(activeDeviceEntry.getLastSyncedOn());
                meta.setLastSyncedDisplayTime(lastSyncDisplayTime);
            }
        } catch (Exception e) {
            log.error("Error in fetching fitness device last sync data", e);
        }
        return meta;
    }

    public CompletableFuture<FitnessDeviceSyncMeta> getFitnessDeviceSyncMetaFuture(UserContext userContext) {
        return supplyAsync(() -> getFitnessDeviceSyncMeta(userContext), serviceInterfaces.getTaskExecutor());
    }

    public Action getContactDetailsAction(UserContext userContext) throws BaseException {
        Map<String, Object> meta = new HashMap<>();
        String attributeName = "sf-saved-coach-contact-details";
        Long userId = Long.parseLong(userContext.getUserProfile().getUserId());
//        coach-contact-details attribute having the contact details saved in user's device
        UserAttributesResponse userAttributesResponse = serviceInterfaces.userAttributesClient.getAttributes(userId,
                attributeName, getAppTenantFromUserContext(userContext), null);
//         doorman config for getting latest coach contact details
        SFContactDetails config = this.getCoachContactDetails();
        String newGivenName = config.getGivenName();
        String newFamilyName = config.getFamilyName();
        List<SFContactDetails.PhoneNumber> newPhoneNumbers = config.getPhoneNumbers();
        boolean syncContactDetails = false;
        boolean userAttributeExists = userAttributesResponse != null && userAttributesResponse.getAttributes() != null && userAttributesResponse.getAttributes().containsKey(attributeName);
        SFContactDetails savedContactDetails = null;
        if (config != null && newGivenName != null && newPhoneNumbers != null && !CollectionUtils.isEmpty(newPhoneNumbers)) {
            try {
                savedContactDetails = Serializer.deserialize((String) userAttributesResponse.getAttributes().get(attributeName), SFContactDetails.class);
            } catch (Exception e) {
                log.error("Exception in fetching user attribute value", e);
            }
            if (userAttributeExists && savedContactDetails != null && savedContactDetails.getGivenName() != null && savedContactDetails.getPhoneNumbers() != null) {
                List<SFContactDetails.PhoneNumber> phoneNumbers = savedContactDetails.getPhoneNumbers();
//                    Checking if firstname, lastname or phone number varies
                if (!savedContactDetails.getGivenName().equals(newGivenName) || !savedContactDetails.getFamilyName().equals(newFamilyName) || phoneNumbers.size() != newPhoneNumbers.size()) {
                    syncContactDetails = true;
                }

//                    Checking if phone number change detected
                List<String> savedNumbers = savedContactDetails.getPhoneNumbers().stream().map(SFContactDetails.PhoneNumber::getNumber).toList();
                List<String> newNumbers = savedContactDetails.getPhoneNumbers().stream().map(SFContactDetails.PhoneNumber::getNumber).toList();
                AtomicInteger counter = new AtomicInteger();
                newNumbers.forEach(el -> {
                    if (savedNumbers.contains(el)) {
                        counter.getAndIncrement();
                    }
                });

//                    phone number change detected
                if (counter.get() != newNumbers.size()) {
                    syncContactDetails = true;
                }
            } else {
                syncContactDetails = true;
            }
        }

        if (syncContactDetails) {
            meta.put("givenName", newGivenName);
            meta.put("familyName", newFamilyName);
            meta.put("phoneNumbers", newPhoneNumbers);
            return Action.builder().title("CONTACT DETAILS").actionType(ActionType.SF_SAVE_CONTACT_TO_PHONE).meta(meta).build();
        }

        return null;
    }

    public ChronicCareTeam getAssignedCareTeam(UserContext userContext, Long patientId, BundleProduct bundleProduct) {
        String tenant = "sugarfit";
        if (Objects.nonNull(bundleProduct)) {
            tenant = bundleProduct.getTenant();
        }
        if (!isSpecialSugarControl7DayPlanPack(bundleProduct)) {
            ChronicCareTeam.ChronicCareTeamBuilder builder = ChronicCareTeam.builder();
            String userId = userContext.getUserProfile().getUserId();
            List<PatientPreferredAgentResponse> patientPreferredAgentResponses = this.serviceInterfaces.getSfAlbusClient()
                    .getPreferredAgents(userId, Optional.ofNullable(patientId), tenant);
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(patientPreferredAgentResponses)) {
                for (PatientPreferredAgentResponse patientPreferredAgentResponse : patientPreferredAgentResponses) {
                    if (patientPreferredAgentResponse.getAgentResponse().getType().equals(AgentType.DOCTOR)) {
                        builder.doctor(patientPreferredAgentResponse);
                    } else if (patientPreferredAgentResponse.getAgentResponse().getType().equals(AgentType.LIFESTYLE_COACH)) {
                        builder.coach(patientPreferredAgentResponse);
                    }
                }
            }
            return builder.build();
        }
        return new ChronicCareTeam();
    }

    public CompletableFuture<ChronicCareTeam> getAssignedCareTeamFuture(UserContext userContext, Long patientId, BundleProduct bundleProduct) {
        return supplyAsync(() -> {
            try {
                String tenant = getAppTenantFromUserContext(userContext).toString();
                return getAssignedCareTeam(userContext, patientId, bundleProduct);
            } catch (Exception e) {
                log.error(String.format("Error in fetching assigned care team :: %s", e.getMessage()), e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<RenewalJourneyResponse> getRenewalJourneyFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                if (isSugarFitApp(userContext)) {
                    Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                    return smsClient.getRenewalJourneyStatus(userId);
                }
                return null;
            } catch (Exception e) {
                log.error(String.format("Error in fetching renewal journey :: %s", e.getMessage()), e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<DiagnosticJourneyResponse> getDiagnosticJourneyStatusFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                if (isSugarFitApp(userContext)) {
                    return serviceInterfaces.getSfAlbusClient().getDiagnosticJourneyStatus(userContext.getUserProfile().getUserId());
                }
                return null;
            } catch (Exception e) {
                log.error(String.format("Error in fetching DiagnosticJourneyResponse :: %s", e.getMessage()), e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public String getAgentImageFromResponse(PatientPreferredAgentResponse agentResponse) {
        String imageUrl = agentResponse.getAgentResponse().getDisplayImage();
        if (imageUrl != null && !imageUrl.equals("")) {
            return imageUrl;
        } else {
            if (agentResponse.getAgentResponse().getGender().equals(Gender.Female)) {
                if (agentResponse.getAgentResponse().getType().equals(AgentType.LIFESTYLE_COACH))
                    return "/image/chroniccare/coach_female_v2.png";
                return "/image/chroniccare/doctor_female_v2.png";
            } else if (agentResponse.getAgentResponse().getGender().equals(Gender.Male)) {
                if (agentResponse.getAgentResponse().getType().equals(AgentType.LIFESTYLE_COACH))
                    return "/image/chroniccare/coach_male_v2.png";
                return "/image/chroniccare/doctor_male_v2.png";
            } else {
                return "/image/chroniccare/coach_male_v2.png";
            }
        }
    }

    public SfRecommendedSlotPage getRecommendedSlotPageData(UserContext userContext, String title, Map<String, Object> queryParams) {
        try {
            boolean isRescheduled = false;
            List<Long> phleboTaskIds = new ArrayList<>();
            PhleboTaskCategory taskCategory = PhleboTaskCategory.DELIVERYANDINSTALLATION;

            try {
                isRescheduled = Boolean.valueOf((String) queryParams.getOrDefault("isRescheduled", "false"));
                if (isRescheduled) {
                    phleboTaskIds = objectMapper.readValue(queryParams.get("phleboTaskIds").toString(), new TypeReference<List<Long>>() {
                    });
                    taskCategory = PhleboTaskCategory.valueOf(queryParams.getOrDefault("taskCategory", null).toString());
                }
            } catch (Exception exc) {
            }

            SfRecommendedSlotPage pageData = new SfRecommendedSlotPage();
            String userId = userContext.getUserProfile().getUserId();
            Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());
            UserOnboardingActionWithContext onboardingActions = userOnboardingService
                    .getSugarFitUserOnboardingStatus(userId, activePackResponse.orElseGet(null));
            boolean isCGMInstallationBookingPending = isCGMInstallationBookingPending(onboardingActions);
            pageData.setTitle(!title.equals("") ? title : "Select your CGM\ninstallation slot");
            pageData.setSubtitle("Based on the coach consultation below are the recommended cgm installation slots");

            CgmOnboardingStatusResponse cgmOnboardingStatusResponse = activePackResponse.isPresent()
                    ? chsClient.fetchOnboardingStatus(Long.valueOf(userId), activePackResponse.get().getBookingId(), AppTenant.SUGARFIT)
                    : null;
            //        hasAcceptedDisclaimer used to avoid support of cgm installation on welcome call in NUX
            boolean hasAcceptedDisclaimer = cgmOnboardingStatusResponse != null && cgmOnboardingStatusResponse.getDisclaimerAccepted();
            if (!hasAcceptedDisclaimer) {
//                Usecase - NUX Step Only
                pageData.setHasBooked(!isCGMInstallationBookingPending);
            }
            List<PhleboSlot> slots = new ArrayList<>();
            Long recommendedTimeSlot = null;
            try {
                slots = serviceInterfaces.sfAlbusClient.getRecommendedPhleboSlots(Long.valueOf(userId));
                pageData.setRecommendedSlots(slots);
                recommendedTimeSlot = slots.get(0).getStartTime().getTime();
            } catch (Exception exec) {
                log.error("Exception in getting Recommended Slots: " + exec.getMessage());
                exceptionReportingService.reportException("Exception in getting Recommended Slots: ", exec);
            }

            Action primaryAction = null, seeMoreAction = null;
            if (isRescheduled) {
                HashMap<String, Object> meta = new HashMap<>();
                meta.put("phleboTaskIds", phleboTaskIds);
                meta.put("taskCategory", taskCategory);
                primaryAction = Action.builder().actionType(ActionType.SF_RESCHEDULE_CGM_INSTALLATION).title("Reschedule CGM installation").meta(meta).build();
                String seeMoreActionURL = "curefit://sfchoosebookingslotpage";
                seeMoreActionURL += "?isRescheduled=" + isRescheduled;
                seeMoreActionURL += "&phleboTaskIds=" + phleboTaskIds;
                seeMoreActionURL += "&taskCategory=" + taskCategory;
                if (recommendedTimeSlot != null) {
                    seeMoreActionURL += "&recommendedSlotTime=" + recommendedTimeSlot;
                }
                seeMoreAction = Action.builder().actionType(ActionType.NAVIGATION).title("see other available slots").url(seeMoreActionURL).build();
            } else {
                if (isCGMInstallationBookingPending) {
                    String seeMoreActionURL = "curefit://sfchoosebookingslotpage";
                    if (recommendedTimeSlot != null) {
                        seeMoreActionURL += "?recommendedSlotTime=" + recommendedTimeSlot;
                    }
                    primaryAction = Action.builder().actionType(ActionType.SF_BOOK_CGM_INSTALLATION).title("Book CGM installation").build();
                    seeMoreAction = Action.builder().actionType(ActionType.NAVIGATION).title("see other available slots").url(seeMoreActionURL).build();
                } else {
                    primaryAction = Action.builder().actionType(ActionType.EMPTY_ACTION).title("CGM installation booked").build();
                }
            }

            if (seeMoreAction != null) {
                pageData.setSeeMoreAction(seeMoreAction);
            }
            if (primaryAction != null) {
                pageData.setPrimaryAction(primaryAction);
            }
            return pageData;
        } catch (Exception e) {
            log.error("Recommended Slots Page Data Fetch Failed | Exception:: " + e.getMessage());
        }

        return null;
    }

    public boolean isSfFreemiumUser(UserContext userContext) {
        try {
            if (!isFreemiumSupportedApp(userContext)) {
                return false;
            }
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            NUXStatusResponse response = smsClient.getNUXStatus(Long.valueOf(userContext.getUserProfile().getUserId()), true, timeZone);
            return response != null && response.getFreemium();
        } catch (Exception e) {
            log.info(e.getMessage());
            return false;
        }
    }

    public boolean sugarfitPaidNuxPreferencesPending(NUXStatusResponse response) {
        if (response != null && !response.getFreemium()) {
            return response.getProfiles().getStepToProfileMap().stream()
                    .filter(step -> !step.getStepName().equals(NUXStepName.ADDRESS))
                    .anyMatch(step -> !step.getCompleted());
        } else {
            return false;
        }
    }

    public boolean sugarfitPaidNuxAddressPending(NUXStatusResponse response) {
        if (response != null && !response.getFreemium()) {
            return response.getProfiles().getStepToProfileMap().stream()
                    .anyMatch(step -> step.getStepName().equals(NUXStepName.ADDRESS) && !step.getCompleted());
        } else {
            return false;
        }
    }

    public boolean sugarfitNuxHasAddressPreference(NUXStatusResponse response) {
        if (response != null && !response.getFreemium()) {
            return response.getProfiles().getStepToProfileMap().stream()
                    .anyMatch(step -> step.getStepName().equals(NUXStepName.ADDRESS));
        } else {
            return false;
        }
    }

    public Long getCGMExpertCenterId() {
        Long defaultCenterId = 21932L;
        Long centerId;
        try {
            centerId = appConfigCache.getConfig("CGM_EXPERT_CENTER_ID", new TypeReference<>() {
            }, defaultCenterId);
            return centerId;
        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return defaultCenterId;
    }

    public Action getWatchCGMInstallationVideoAction(String title, Boolean resetNavStack, Boolean hideAskingDifficulty) {
        String actionTitle = !title.equals("") ? title : "WATCH INSTALLATION VIDEO";
        return Action.builder().actionType(ActionType.NAVIGATION).title(actionTitle).url("curefit://sfcgmselfinstallationapplyguidescreen?resetNavStack=" + resetNavStack + "&hideAskingDifficulty=" + hideAskingDifficulty).build();
    }

    public SfDiagnosticTestInstructionView getDiagnosticsTestsInstructions(List<String> productCodes) {
        SfDiagnosticTestInstructionView InstructionView = new SfDiagnosticTestInstructionView();
        try {
            String[] productCodesArray = productCodes.toArray(new String[0]);
            ProductSellerResponse[] products = serviceInterfaces.getFuseService().getProductDetails(productCodesArray);
            boolean fastingRequired = false;
            if (products != null) {
                fastingRequired = Arrays.stream(products)
                        .filter(p -> p.getProduct() != null)
                        .map(p -> p.getProduct().getFasting())
                        .anyMatch(fasting -> fasting != null && fasting.getRequired()); // Check if fasting is required
            }
            List<SfDiagnosticTest.MappedInstruction> mappedInstructions = mapInstructions(fastingRequired);
            InstructionView.setInstructions(mappedInstructions);
            return InstructionView;
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public Action getBookCGMInstallationByPhleboAction(UserContext userContext, String title, String productId) throws ResourceNotFoundException {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        Long patientId = patientDetail.getId();
        String actionTitle = !title.equals("") ? title : "BOOK INSTALLATION SLOT";

        Long centerId = getCGMExpertCenterId();
        return Action.builder().actionType(ActionType.NAVIGATION).title(actionTitle).url("curefit://selectCareDateV1?productId=" + productId + "&centerId=" + centerId + "&isExternal=true&forceCurrentCenter=true&parentBookingId=-1&patientId=" + patientId).isEnabled(true).build();
    }

    public Action getBookCGMInstallationByPhleboAction(Long patientId, String title, String productId) throws ResourceNotFoundException {
        String actionTitle = !title.equals("") ? title : "BOOK INSTALLATION SLOT";
        Long centerId = getCGMExpertCenterId();
        return Action.builder().actionType(ActionType.NAVIGATION).title(actionTitle).url("curefit://selectCareDateV1?productId=" + productId + "&centerId=" + centerId + "&isExternal=true&forceCurrentCenter=true&parentBookingId=-1&patientId=" + patientId).isEnabled(true).build();
    }

    public Action getBookCGMInstallationWebinarAction(String title) {
        String actionTitle = !title.isEmpty() ? title : "BOOK INSTALLATION SLOT";
        return Action.builder().actionType(ActionType.NAVIGATION).title(actionTitle).url("curefit://sfcgmwebinarlistpage").isEnabled(true).build();
    }

    public Action getBookCGMInstallationAction(UserContext userContext, String phleboConsultProductId) throws ResourceNotFoundException {
//        if (isCgmInstallationWebinarSupportedApp(userContext) && !isTierTwoUser(userContext) && !isQuickCommerceOrShopifyPackUser(userContext)) {
//            return getBookCGMInstallationWebinarAction("Book Video Installation Slot");
//        } else {
//            return getBookCGMInstallationByPhleboAction(userContext, "Book Video Installation Slot", phleboConsultProductId);
//        }
        return getBookCGMInstallationWebinarAction("Book Video Installation Slot");
    }

//    public String getDigitalAppAgentDatePickerUrl(String productCode, Long centerId, Long agentId,
//                                                  ActivePackResponse activePackResponse) {
//        StringBuilder actionBuilder = new StringBuilder().append("curefit://selectCareDateV1?productId=").append(productCode).append("&isExternal=true").append("&forceCurrentCenter=true").append("&patientId=").append(activePackResponse.getPatientId());
//        if (Objects.nonNull(centerId)) {
//            actionBuilder.append("&centerId=").append(centerId);
//        }
//
//        Optional<UserMembershipInfo> membershipInfo = activePackResponse.getUserMembershipInfos().stream()
//                .filter(x -> productCode.equals(x.getProductCode())).findFirst();
//
//        if (membershipInfo.isPresent()
//                && membershipInfo.get().getTicketsConsumed() < membershipInfo.get().getTickets()) {
//            actionBuilder.append("&parentBookingId=").append(activePackResponse.getBookingId());
//        }
//
//        return actionBuilder.toString();
//    }

    public String getPsychologistAgentDatePickerUrl(String productCode, ActivePackResponse activePackResponse) {
        StringBuilder actionBuilder = new StringBuilder().append("curefit://selectCareDateV1?productId=").append(productCode).append("&isExternal=true").append("&forceCurrentCenter=true").append("&patientId=").append(activePackResponse.getPatientId());

        Optional<UserMembershipInfo> membershipInfo = activePackResponse.getUserMembershipInfos().stream()
                .filter(x -> productCode.equals(x.getProductCode())).findFirst();

        if (membershipInfo.isPresent()
                && membershipInfo.get().getTicketsConsumed() < membershipInfo.get().getTickets()) {
            actionBuilder.append("&parentBookingId=").append(activePackResponse.getBookingId());
        }

        return actionBuilder.toString();
    }

    public String getDigitalAppDatePickerUrl(String productCode, Long centerId, ActivePackResponse activePackResponse) {
        StringBuilder actionBuilder = new StringBuilder().append("curefit://doctorprofilepage?productId=").append(productCode).append("&isExternal=true").append("&forceCurrentCenter=true").append("&patientId=").append(activePackResponse.getPatientId());
        Optional<UserMembershipInfo> membershipInfo = activePackResponse.getUserMembershipInfos().stream()
                .filter(x -> productCode.equals(x.getProductCode())).findFirst();
        if (centerId != null) {
            actionBuilder.append("&centerId=").append(centerId);
        }
        if (membershipInfo.isPresent()
                && membershipInfo.get().getTicketsConsumed() < membershipInfo.get().getTickets()) {
            actionBuilder.append("&parentBookingId=").append(activePackResponse.getBookingId());
        }
        return actionBuilder.toString();
    }

    public Action getPhleboTaskRecommendedSlotsPageRedirectAction(String title, Boolean isEnabled, Boolean isRescheduled, UpcomingPhleboTaskResponse phleboTask) {
        String actionTitle = !title.equals("") ? title : "Book CGM Installation";
        List<Long> phleboTaskIds = new ArrayList<>();
        PhleboTaskCategory taskCategory = PhleboTaskCategory.DELIVERYANDINSTALLATION;
        if (isRescheduled && phleboTask != null && !CollectionUtils.isEmpty(phleboTask.getPhleboTaskIds())) {
            phleboTaskIds = phleboTask.getPhleboTaskIds();
            taskCategory = phleboTask.getTaskCategory();
        }
        return Action.builder().actionType(ActionType.NAVIGATION).title(actionTitle).url("curefit://sfchooserecommendedbookingslotpage?isRescheduled=" + isRescheduled + "&phleboTaskIds=" + phleboTaskIds + "&taskCategory=" + taskCategory).isEnabled(isEnabled).build();
    }

    public Action getPhleboCancelCGMInstallationAction(UpcomingPhleboTaskResponse phleboTask, String actionTitle) {
        if (!CollectionUtils.isEmpty(phleboTask.getPhleboTaskIds()) && phleboTask.getPhleboTaskIds().get(0) != null) {
            List<Long> phleboTaskIds = phleboTask.getPhleboTaskIds();
            HashMap<String, Object> meta = new HashMap<>();
            meta.put("phleboTaskIds", phleboTaskIds);
            return Action.builder().actionType(ActionType.SF_CANCEL_CGM_INSTALLATION).title(!actionTitle.equals("") ? actionTitle : "Cancel CGM Installation").meta(meta).build();
        }
        return null;
    }

    public Integer getFreemiumMaxVideoTime() {
        Integer maxTimeInSec = 20;
        try {
            HashMap<String, String> config = appConfigCache.getConfig("FREEMIUM_VIDEO_CONFIG", new TypeReference<>() {
            }, new HashMap<>());
            if (config != null && !config.isEmpty()) {
                maxTimeInSec = Integer.parseInt(config.get("maxTimeInSec"));
            }
            return maxTimeInSec;

        } catch (Exception e) {
            String message = String.format("FREEMIUM_VIDEO_CONFIG error :: %s", e.getMessage());
            log.error(message, e);
            return maxTimeInSec;
        }
    }

    public boolean isFreemiumWellnessAllowed(UserContext userContext) {
        try {
            SegmentSet<String> userSegments = (SegmentSet<String>) userContext.getRequestCache()
                    .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
            return userSegments.contains(FREEMIUM_WELLNESS_ENABLED_USERS_SEGMENT);
        } catch (Exception e) {
            String message = String.format("freemium wellness enabled users segment error, userId :: %s, error :: %s", userContext.getUserProfile().getUserId(), e.getMessage());
            log.error(message, e);
            return false;
        }
    }

    public Integer getFreemiumExperinceConfigDay(String userId) {
        try {
            int config = appConfigCache.getConfig("FREEMIUM_EXPERIENCE_CONFIG_DAY", new TypeReference<>() {
            }, 0);
            return config;
        } catch (Exception e) {
            String message = String.format("freemium experience config error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
            return 0;
        }
    }

    private String getSfEventsTalkUserCountKey(String id) {
        return "SFAPP:" + "EVENTSTALK:" + "EVENTID:" + id;
    }

    public String getSfEventsTalkUserCount(String id) {
        int HOURS_IN_SECONDS = 60 * 60 * 1;
        var redisKey = this.getSfEventsTalkUserCountKey(id);
        String longValue = null;
        try {
            longValue = this.cfApiRedisKeyValueStore.get(redisKey);
        } catch (Exception e) {
            log.warn("count  cache miss", e);
        }
        if (longValue != null) {
            return longValue;
        }
        try {
            HashMap<String, String> config = appConfigCache.getConfig("SF_EVENTS_TALK_CONFIG", new TypeReference<>() {
            }, new HashMap<>());
            int min = config.get("min") == null ? 30 : Integer.parseInt(config.get("min"));
            int max = config.get("max") == null ? 50 : Integer.parseInt(config.get("max"));
            longValue = String.valueOf(Math.floor((Math.random() * (max - min)) + min));

        } catch (Exception e) {
            log.error(" app config failed" + e.getMessage());
            return null;
        }
        this.cfApiRedisKeyValueStore.set(redisKey, longValue, HOURS_IN_SECONDS);
        return longValue;
    }

    public SfAppAnnouncementConfig getAnnouncementConfig() {
        SfAppAnnouncementConfig config = new SfAppAnnouncementConfig();
        try {
            config = appConfigCache.getConfig("SF_APP_ANNOUNCEMENT_CONFIG", new TypeReference<>() {
            }, new SfAppAnnouncementConfig());

        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return config;
    }

    public SfAppScratchCardConfig getScratchCardConfig() {
        SfAppScratchCardConfig config = new SfAppScratchCardConfig();
        try {
            config = appConfigCache.getConfig("SCRATCH_CARD_CONFIG", new TypeReference<>() {
            }, new SfAppScratchCardConfig());

        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return config;
    }

    public HashMap<String, String> getECommercePLPConfig() {
        HashMap<String, String> config = new HashMap<>();
        try {
            config = appConfigCache.getConfig("SF_ECOMMERCE_PLP", new TypeReference<>() {
            }, new HashMap<>());

        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return config;
    }

    public HashMap<String, Object> getSfStoreLPConfig() {
        HashMap<String, Object> config = new HashMap<>();
        try {
            config = appConfigCache.getConfig("Storefront_App_Config", new TypeReference<>() {
            }, new HashMap<>());

        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return config;
    }

    public HashMap<String, String> getSmartScalePLPConfig() {
        HashMap<String, String> config = new HashMap<>();
        try {
            config = appConfigCache.getConfig("SF_SMART_SCALE_PLP", new TypeReference<>() {
            }, new HashMap<>());

        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return config;
    }

    public HashMap<String, String> getWellnessAtCenterPrePurchaseConfig() {
        HashMap<String, String> config = new HashMap<>();
        try {
            config = appConfigCache.getConfig("SF_WELLNESS_AT_CENTER", new TypeReference<>() {
            }, new HashMap<>());

        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return config;
    }

    public SFContactDetails getCoachContactDetails() {
        HashMap<String, Object> config = new HashMap<>();
        try {
            config = appConfigCache.getConfig("SF_COACH_CONTACT_CONFIG", new TypeReference<>() {
            }, config);
            SFContactDetails contactDetails = objectMapper.convertValue(config, SFContactDetails.class);
            return contactDetails;

        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return new SFContactDetails();
    }

    public HashMap<String, String> getExperienceCenterCLPConfig() {
        HashMap<String, String> config = new HashMap<>();
        try {
            config = appConfigCache.getConfig("SF_EXPERIENCE_CENTER_CLP", new TypeReference<>() {
            }, new HashMap<>());

        } catch (Exception e) {
            log.error("Exception in fetching doorman config", e);
            exceptionReportingService.reportException("Exception in fetching doorman config", e);
        }
        return config;
    }

    public SfAppUpdateConfig getSfAppUpdateConfig() {
        SfAppUpdateConfig config = new SfAppUpdateConfig();
        try {
            config = serviceInterfaces.appConfigCache.getConfig("SF_APP_UPDATE_CONFIG", new TypeReference<>() {
            }, config);
        } catch (Exception e) {
            String message = String.format("SF_APP_UPDATE_CONFIG error, error :: %s", e.getMessage());
            log.error(message, e);
        }
        return config;
    }

    public boolean hasAnyPollsAvailableForUser(UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            List<PollEntry> participatedPolls = pollSupportClient.fetchUserParticipatedPolls(userId, 0, 1, "createdOn", "DESC").getElements();
            AtomicReference<Boolean> pollAvailable = new AtomicReference<>(false);
            if (!CollectionUtils.isEmpty(participatedPolls)) {
                pollAvailable.set(true);
            } else {
                SegmentSet<String> userSegmentSet = (SegmentSet<String>) userContext.getRequestCache()
                        .getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext).get();
                Set<String> relevantSegments = serviceInterfaces.relevantSegmentCache.getRelevantSegments(RelevantSegmentCache.RelevantSegmentType.SF_POLL_CLIENT);
                List<String> userSegments = new ArrayList<>(userSegmentSet.getRelevantEntries(relevantSegments));
                List<PollEntry> activePolls = pollSupportClient.fetchActivePollsV2(userId, 0, 20, "createdOn", "DESC", userSegments).getElements();
                activePolls = activePolls.stream().filter(el -> el.getPollType() != PollType.DDS).toList();
                if (!CollectionUtils.isEmpty(activePolls)) {
                    pollAvailable.set(activePolls.stream().anyMatch(p -> p.getStatus() != null && p.getStatus().equals(Status.PUBLISHED)));
                }
            }
            return pollAvailable.get();
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public Date getUpcomingConsultationDateForDisablingMultipleBookings(UserContext userContext, String userId, String productToBeBooked) {
        try {
            List<ActiveConsultationResponse> consultationBookings = serviceInterfaces.sfAlbusClient.getActiveConsultations(userId, null);
            Date now = Calendar.getInstance(getUserTimezone(userContext)).getTime();
            if (!CollectionUtils.isEmpty(consultationBookings)) {
                Map<String, Long> productCodeToUpcomingBookingMap = consultationBookings.stream().filter(
                        consultation -> {
                            if (consultation != null && consultation.getConsultationProduct() != null
                                    && consultation.getConsultationProduct().getProductCode() != null
                                    && !COMPLETED_CONSULTATION_STATES.contains(consultation.getStatus())
                                    && (SUGARFIT_COACH_CONSULT_PRODUCTS.contains(consultation.getConsultationProduct().getProductCode())
                                    || SUGARFIT_DOCTOR_CONSULT_PRODUCTS.contains(consultation.getConsultationProduct().getProductCode())
                                    || SUGARFIT_PHLEBO_CONSULT_PRODUCTS.contains(consultation.getConsultationProduct().getProductCode()))) {
                                Date consultEndDate = new Date(consultation.getStartDate() + consultation.getConsultationProduct().getDuration());
                                return consultEndDate.after(now);
                            }
                            return false;
                        }
                ).collect(Collectors.toMap(
                        x -> x.getConsultationProduct().getProductCode(),
                        ActiveConsultationResponse::getStartDate,
                        (x, y) -> x > y ? x : y
                ));
                log.debug("For user :: {}, upcoming consult to date map :: {}", userId, productCodeToUpcomingBookingMap);
                if (!productCodeToUpcomingBookingMap.isEmpty()) {
                    // SUGAR002 to be booked, don't allow if 003 or 002 is booked.
                    if (SUGARFIT_COACH_CONSULT_PRODUCTS.contains(productToBeBooked)) {
                        if (productCodeToUpcomingBookingMap.containsKey(SUGARFIT_SHORT_COACH_CONSULT)) {
                            return new Date(productCodeToUpcomingBookingMap.get(SUGARFIT_SHORT_COACH_CONSULT));
                        }
                        if (productCodeToUpcomingBookingMap.containsKey(SUGARFIT_LONG_COACH_CONSULT)) {
                            return new Date(productCodeToUpcomingBookingMap.get(SUGARFIT_LONG_COACH_CONSULT));
                        }
                        return null;
                    }
                    if (SUGARFIT_DOCTOR_CONSULT_PRODUCTS.contains(productToBeBooked)) {
                        return productCodeToUpcomingBookingMap.containsKey(SUGARFIT_DOCTOR_CONSULT) ?
                                new Date(productCodeToUpcomingBookingMap.get(SUGARFIT_DOCTOR_CONSULT)) :
                                null;
                    }
                    if (SUGARFIT_PHLEBO_CONSULT_PRODUCTS.contains(productToBeBooked)) {
                        return productCodeToUpcomingBookingMap.containsKey(SF_PHLEBO_CGM_CONSULTATION_PRODUCT) ?
                                new Date(productCodeToUpcomingBookingMap.get(SF_PHLEBO_CGM_CONSULTATION_PRODUCT)) :
                                productCodeToUpcomingBookingMap.containsKey(SF_PHLEBO_BLUCON_CONSULTATION_PRODUCT) ?
                                        new Date(productCodeToUpcomingBookingMap.get(SF_PHLEBO_BLUCON_CONSULTATION_PRODUCT)) :
                                        null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error fetching active bookings :: {}", e.getMessage(), e);
        }
        return null;
    }

    public Date getUpcomingConsultationDateForDisablingMultipleBookings(UserContext userContext, CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture, String productToBeBooked) {
        if (SUGARFIT_LONG_COACH_CONSULT.equalsIgnoreCase(productToBeBooked)) {
            // SUGAR003, always allow booking
            return null;
        } else {
            try {
                List<ActiveConsultationResponse> consultationBookings = consultationBookingsFuture.get();
                Date now = Calendar.getInstance(getUserTimezone(userContext)).getTime();
                if (!CollectionUtils.isEmpty(consultationBookings)) {
                    Map<String, Long> productCodeToUpcomingBookingMap = consultationBookings.stream().filter(
                            consultation -> {
                                if (consultation != null && consultation.getConsultationProduct() != null
                                        && consultation.getConsultationProduct().getProductCode() != null
                                        && !COMPLETED_CONSULTATION_STATES.contains(consultation.getStatus())
                                        && (SUGARFIT_COACH_CONSULT_PRODUCTS.contains(consultation.getConsultationProduct().getProductCode())
                                        || SUGARFIT_DOCTOR_CONSULT_PRODUCTS.contains(consultation.getConsultationProduct().getProductCode())
                                        || SUGARFIT_PHLEBO_CONSULT_PRODUCTS.contains(consultation.getConsultationProduct().getProductCode()))) {
                                    Date consultEndDate = new Date(consultation.getStartDate() + consultation.getConsultationProduct().getDuration());
                                    return consultEndDate.after(now);
                                }
                                return false;
                            }
                    ).collect(Collectors.toMap(
                            x -> x.getConsultationProduct().getProductCode(),
                            ActiveConsultationResponse::getStartDate,
                            (x, y) -> x > y ? x : y
                    ));
                    if (!productCodeToUpcomingBookingMap.isEmpty()) {
                        // SUGAR002 to be booked, don't allow if 003 or 002 is booked.
                        if (SUGARFIT_COACH_CONSULT_PRODUCTS.contains(productToBeBooked)) {
                            return productCodeToUpcomingBookingMap.containsKey(SUGARFIT_SHORT_COACH_CONSULT) ?
                                    new Date(productCodeToUpcomingBookingMap.get(SUGARFIT_SHORT_COACH_CONSULT)) :
                                    productCodeToUpcomingBookingMap.containsKey(SUGARFIT_LONG_COACH_CONSULT) ?
                                            new Date(productCodeToUpcomingBookingMap.get(SUGARFIT_LONG_COACH_CONSULT)) :
                                            null;
                        }
                        if (SUGARFIT_DOCTOR_CONSULT_PRODUCTS.contains(productToBeBooked)) {
                            return productCodeToUpcomingBookingMap.containsKey(SUGARFIT_DOCTOR_CONSULT) ?
                                    new Date(productCodeToUpcomingBookingMap.get(SUGARFIT_DOCTOR_CONSULT)) :
                                    null;
                        }
                        if (SUGARFIT_PHLEBO_CONSULT_PRODUCTS.contains(productToBeBooked)) {
                            return productCodeToUpcomingBookingMap.containsKey(SF_PHLEBO_CGM_CONSULTATION_PRODUCT) ?
                                    new Date(productCodeToUpcomingBookingMap.get(SF_PHLEBO_CGM_CONSULTATION_PRODUCT)) :
                                    productCodeToUpcomingBookingMap.containsKey(SF_PHLEBO_BLUCON_CONSULTATION_PRODUCT) ?
                                            new Date(productCodeToUpcomingBookingMap.get(SF_PHLEBO_BLUCON_CONSULTATION_PRODUCT)) :
                                            null;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error fetching active bookings :: {}", e.getMessage(), e);
            }
            return null;
        }
    }

    public CompletableFuture<CgmOnboardingStatusResponse> getCgmOnboardingStatusFuture(Long userId, AppTenant appTenant) {
        return supplyAsync(() -> {
            try {
                return chsClient.fetchOnboardingStatus(userId, null, appTenant);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<FaceBasedVitalScansForDayResponse> getFbvScanForDayDataFuture(Long userId, TimeZone timeZone) {
        return supplyAsync(() -> {
            try {
                return chsClient.getScansForDay(userId, timeZone);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<UserTodoDaySummary> getUserTodoDaySummaryFuture(UserContext userContext, Long queryDateEpoch) {
        return supplyAsync(() -> {
            try {
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                return smsClient.getUserTodoDaySummary(userId, queryDateEpoch);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<CgmUserRequestStatus> getCgmUserRequestStatusFuture(Long userId, AppTenant appTenant) {
        return supplyAsync(() -> {
            try {
                return chsClient.fetchCgmRequestStatus(userId, appTenant);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<CgmStat> getCgmStatFuture(Long userId, String cgmDeviceId,
                                                       Boolean recentStat, AppTenant appTenant, TimeZone timeZone) {
        return supplyAsync(() -> {
            try {
                CgmStatFilter cgmStatFilter = new CgmStatFilter();
                cgmStatFilter.setMostRecentStat(recentStat);
                cgmStatFilter.setActivitySummary(true);
                cgmStatFilter.setActivityDetails(false);
                cgmStatFilter.setNocturnalInsights(false);
                return chsClient.fetchCgmStatV2(userId, cgmDeviceId,cgmStatFilter, appTenant, timeZone);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<Boolean> hasUserPlacedAnyEcommerceOrder(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                UserOrderResponse userOrderResponse = indusClient.hasUserOrdered(Long.valueOf(userContext.getUserProfile().getUserId()));
                if (!Objects.isNull(userOrderResponse)) {
                    return userOrderResponse.getHasUserOrdered();
                }
            } catch (Exception e) {
                log.error("Exception in getAssignedAgentsWidgets", e);
                exceptionReportingService.reportException("Exception in getAssignedAgentsWidgets", e);
            }
            return false;
        }, serviceInterfaces.getTaskExecutor());
    }

//    public CompletableFuture<SfBannerCarouselWidget> getEComBannerWidgetFuture(UserContext userContext, boolean isFreemium) {
//        return supplyAsync(() -> {
//            try {
//                SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
//                List<BannerItem> bannerItemList = new ArrayList<>();
//                SfBannerCarouselWidget eComBannerWidget = this.getEComBannerWidget(userContext, "ecommerceSales", false);
//                if (Objects.nonNull(eComBannerWidget) && org.apache.commons.collections.CollectionUtils.isNotEmpty(eComBannerWidget.getData())) {
//                    bannerItemList.add(0, eComBannerWidget.getData().get(0));
//                }
//
//                SfBannerCarouselWidget eComJuiceBannerWidget = this.getEComBannerWidget(userContext, "ecommerceJuiceSales", false);
//                if (Objects.nonNull(eComJuiceBannerWidget) && org.apache.commons.collections.CollectionUtils.isNotEmpty(eComJuiceBannerWidget.getData())) {
//                    bannerItemList.add(0, eComJuiceBannerWidget.getData().get(0));
//                }
//                if (!CollectionUtils.isEmpty(bannerItemList)) {
//                    sfBannerCarouselWidget.setData(bannerItemList);
//                    Map<String, Object> layoutProps = new HashMap<>();
//                    layoutProps.put("roundedCorners", true);
//                    layoutProps.put("bannerHeight", 180);
//                    layoutProps.put("bannerWidth", 315);
//                    layoutProps.put("bannerOriginalHeight", 180);
//                    layoutProps.put("bannerOriginalWidth", 315);
//                    layoutProps.put("verticalPadding", isFreemium ? 10 : 5);
//                    layoutProps.put("showPagination", true);
//                    layoutProps.put("autoScroll", false);
//                    sfBannerCarouselWidget.setLayoutProps(layoutProps);
//                    return sfBannerCarouselWidget;
//                }
//            } catch (Exception e) {
//                exceptionReportingService.reportException(e);
//            }
//            return null;
//        }, serviceInterfaces.getTaskExecutor());
//    }

    public SfBannerCarouselWidget getReferralChallengeBannerWidget(UserContext userContext, ChallengesEntry challengesEntry, boolean isFreemium) {
        try {
            SfHomepageBannerConfig sfHomepageBannerConfig = null;
            try {
                sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {}, new SfHomepageBannerConfig());
            } catch (Exception e) {
                log.error("Error in fetching sales config", e);
            }
            if (sfHomepageBannerConfig != null && sfHomepageBannerConfig.getSaleBanners() != null && sfHomepageBannerConfig.getSaleBanners().getReferralChallenge() != null) {
                String bannerUrl = sfHomepageBannerConfig.getSaleBanners().getReferralChallenge().getImageUrl();
                String lottieUrl = sfHomepageBannerConfig.getSaleBanners().getReferralChallenge().getLottieUrl();
                if ((bannerUrl !=null && !bannerUrl.isEmpty()) || (lottieUrl != null && !lottieUrl.isEmpty())) {
                    boolean hasUserJoined = challengesEntry.getUserJoinedDate() != null;
                    boolean hasChallengeStarted = !SfDateUtils.isFutureDate(userContext, challengesEntry.getStartDate());

                    SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
                    BannerItem bannerItem = new BannerItem();
                    bannerItem.setImage(bannerUrl);
                    bannerItem.setLottieUrl(lottieUrl);

                    String tabKey = "ABOUT";
                    if (hasUserJoined && hasChallengeStarted) {
                        tabKey = "LEADERBOARD";
                    }
                    bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfchallengedetailspage?challengeId=" + challengesEntry.getId() + "&tabKey=" + tabKey).build());
                    sfBannerCarouselWidget.setData(List.of(bannerItem));

                    Map<String, Object> layoutProps = new HashMap<>();
                    layoutProps.put("roundedCorners", true);
                    layoutProps.put("bannerHeight", 180);
                    layoutProps.put("bannerWidth", 315);
                    layoutProps.put("bannerOriginalHeight", 180);
                    layoutProps.put("bannerOriginalWidth", 315);
                    layoutProps.put("verticalPadding", isFreemium ? 10 : 5);
                    layoutProps.put("showPagination", true);
                    layoutProps.put("autoScroll", false);
                    sfBannerCarouselWidget.setLayoutProps(layoutProps);
                    return sfBannerCarouselWidget;
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

//    public SfBannerCarouselWidget getWellnessAtCenterBannerWidget(UserContext userContext, String bannerKey, boolean isFreemium) {
//        try {
//            SfHomepageBannerConfig sfHomepageBannerConfig = null;
//            try {
//                sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {}, new SfHomepageBannerConfig());
//            } catch (Exception e) {
//                log.error("Error in fetching sales config", e);
//            }
//            if (sfHomepageBannerConfig != null && sfHomepageBannerConfig.getSaleBanners() != null && sfHomepageBannerConfig.getSaleBanners().getWellnessAtCenter() != null) {
//                String bannerUrl = sfHomepageBannerConfig.getSaleBanners().getWellnessAtCenter().getImageUrl();
//                String lottieUrl = sfHomepageBannerConfig.getSaleBanners().getWellnessAtCenter().getLottieUrl();
//                if ((bannerUrl !=null && !bannerUrl.isEmpty()) || (lottieUrl != null && !lottieUrl.isEmpty())) {
//                    SfBannerCarouselWidget bannerCarouselWidget = new SfBannerCarouselWidget();
//                    BannerItem bannerItem = new BannerItem();
//                    bannerItem.setImage(bannerUrl);
//                    bannerItem.setLottieUrl(lottieUrl);
//                    bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfwellnessatcenterprepurchase").build());
//                    bannerCarouselWidget.setData(List.of(bannerItem));
//                    bannerCarouselWidget.setLayoutProps(getEComBannerLayoutProps(isFreemium));
//                    return bannerCarouselWidget;
//                }
//            }
//        } catch (Exception e) {
//            exceptionReportingService.reportException(e);
//        }
//        return null;
//    }

    public SfBannerCarouselWidget getEComBannerWidget(UserContext userContext, String bannerKey, boolean isFreemium) {
        if (isECommerceSupported(userContext)) {
            try {
                SfHomepageBannerConfig sfHomepageBannerConfig = null;
                try {
                    sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {}, new SfHomepageBannerConfig());
                } catch (Exception e) {
                    log.error("Error in fetching sales config", e);
                }
                if (sfHomepageBannerConfig != null && sfHomepageBannerConfig.getSaleBanners() != null && sfHomepageBannerConfig.getSaleBanners().getEcommerce() != null) {
                    String ecommerceBannerUrl = sfHomepageBannerConfig.getSaleBanners().getEcommerce().getImageUrl();
                    String ecommerceLottieUrl = sfHomepageBannerConfig.getSaleBanners().getEcommerce().getLottieUrl();
                    if ((ecommerceBannerUrl !=null && !ecommerceBannerUrl.isEmpty()) || (ecommerceLottieUrl != null && !ecommerceLottieUrl.isEmpty())) {
                        SfBannerCarouselWidget bannerCarouselWidget = new SfBannerCarouselWidget();
                        BannerItem bannerItem = new BannerItem();
                        bannerItem.setImage(ecommerceBannerUrl);
                        bannerItem.setLottieUrl(ecommerceLottieUrl);
                        bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url(isSfStoreTabEnabledUser(userContext) ? "curefit://sfstorelifestylepage" : "curefit://sfecommerceplp").build());
                        bannerCarouselWidget.setData(List.of(bannerItem));
                        bannerCarouselWidget.setLayoutProps(getEComBannerLayoutProps(isFreemium));
                        return bannerCarouselWidget;
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
        }
        return null;
    }

    public BannerItem createBannerItem(String bannerUrl, String lottieUrl, Action action) {
        if ((Objects.nonNull(bannerUrl) && !bannerUrl.isEmpty()) || (Objects.nonNull(lottieUrl) && !lottieUrl.isEmpty())) {
            BannerItem bannerItem = new BannerItem();
            bannerItem.setImage(bannerUrl);
            bannerItem.setLottieUrl(lottieUrl);
            bannerItem.setAction(action);
            return bannerItem;
        }
        return null;
    }

    public SfBannerCarouselWidget getSmartScalePurchaseBannerWidget(UserContext userContext, String bannerKey, boolean isFreemium) {
        if (isECommerceSupported(userContext)) {
            try {
                SfHomepageBannerConfig sfHomepageBannerConfig = null;
                try {
                    sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {}, new SfHomepageBannerConfig());
                } catch (Exception e) {
                    log.error("Error in fetching sales config", e);
                }
                if (sfHomepageBannerConfig != null && sfHomepageBannerConfig.getSaleBanners() != null && sfHomepageBannerConfig.getSaleBanners().getSmartScale() != null) {
                    String bannerUrl = sfHomepageBannerConfig.getSaleBanners().getSmartScale().getImageUrl();
                    String lottieUrl = sfHomepageBannerConfig.getSaleBanners().getSmartScale().getLottieUrl();
                    if ((bannerUrl !=null && !bannerUrl.isEmpty()) || (lottieUrl != null && !lottieUrl.isEmpty())) {
                        SfBannerCarouselWidget bannerCarouselWidget = new SfBannerCarouselWidget();
                        BannerItem bannerItem = new BannerItem();
                        bannerItem.setImage(bannerUrl);
                        bannerItem.setLottieUrl(lottieUrl);
                        if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)
                                && ChronicCareAppUtil.isCgmCombinedCartSupportedAppVersion(userContext)) {
                            bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfecommercepdp?productId=SMART_SCALE").build());
                        } else {
                            bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfsmartscaleplp").build());
                        }
                        bannerCarouselWidget.setData(List.of(bannerItem));
                        bannerCarouselWidget.setLayoutProps(getEComBannerLayoutProps(isFreemium));
                        return bannerCarouselWidget;
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
        }
        return null;
    }

    @GetMapping
    public Map<String, Object> getEComBannerLayoutProps(boolean isFreemium) {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", true);
        layoutProps.put("bannerHeight", 202);
        layoutProps.put("bannerWidth", 325);
        layoutProps.put("bannerOriginalHeight", 202);
        layoutProps.put("bannerOriginalWidth", 304);
        layoutProps.put("verticalPadding", isFreemium ? 10 : 0);
        layoutProps.put("showPagination", false);
        return layoutProps;
    }

    public BaseWidgetNonVM getExperienceCenterBannerWidget(UserContext userContext) {
        boolean isFreemium = true;
        String cityId = getUserCityId(userContext, this.serviceInterfaces);
        if (isExperienceCenterSupported(userContext, isFreemium, cityId)) {
            try {
                SfHomepageBannerConfig sfHomepageBannerConfig = null;
                try {
                    sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {}, new SfHomepageBannerConfig());
                } catch (Exception e) {
                    log.error("Error in fetching sales config", e);
                }
                if (sfHomepageBannerConfig != null && sfHomepageBannerConfig.getSaleBanners() != null && sfHomepageBannerConfig.getSaleBanners().getExperienceCenter() != null) {
                    String bannerUrl = sfHomepageBannerConfig.getSaleBanners().getExperienceCenter().getImageUrl();
                    String lottieUrl = sfHomepageBannerConfig.getSaleBanners().getExperienceCenter().getLottieUrl();
                    if ((bannerUrl !=null && !bannerUrl.isEmpty()) || (lottieUrl != null && !lottieUrl.isEmpty())) {
                        SfBannerCarouselWidget bannerCarouselWidget = new SfBannerCarouselWidget();
                        BannerItem bannerItem = new BannerItem();
                        bannerItem.setImage(bannerUrl);
                        bannerItem.setLottieUrl(lottieUrl);
//                        bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfexperiencecenterclp").build());
                        bannerItem.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfexperiencecenterselectpage").build());
                        bannerCarouselWidget.setData(List.of(bannerItem));
                        bannerCarouselWidget.setLayoutProps(getExperienceCenterBannerLayoutProps(isFreemium));
                        return bannerCarouselWidget;
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
        }

//        if (ChronicCareAppUtil.isExperienceCenterFullBodyTestSupported(userContext, isFreemium, cityId)) {
//            try {
//                HashMap<String, SfSalesBannerUrl> salesBannerConfig = appConfigCache.getConfig("SF_SALES_BANNER_CONFIG",
//                        new TypeReference<>() {
//                        }, new HashMap<>());
//                if (salesBannerConfig != null && salesBannerConfig.get("home") != null) {
//                    SfSalesBannerUrl homeBanners = salesBannerConfig.get("home");
//                    String bannerUrl = homeBanners.getLarge().getOrDefault("experienceCenterFullBodyTest", "");
//                    if (!bannerUrl.isEmpty()) {
//                        SfFreemiumFullBodyTestBannerWidget freemiumFreeTestBannerWidget = new SfFreemiumFullBodyTestBannerWidget();
//                        freemiumFreeTestBannerWidget.setBannerImage(bannerUrl);
//                        freemiumFreeTestBannerWidget.setBannerAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfexperiencecenterselectpage").build());
//                        return freemiumFreeTestBannerWidget;
//                    }
//                }
//            } catch (Exception e) {
//                exceptionReportingService.reportException(e);
//            }
//        }
        return null;
    }

    @GetMapping
    private Map<String, Object> getExperienceCenterBannerLayoutProps(boolean isFreemium) {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", true);
        layoutProps.put("bannerHeight", 180);
        layoutProps.put("bannerWidth", 300);
        layoutProps.put("bannerOriginalHeight", 180);
        layoutProps.put("bannerOriginalWidth", 300);
        layoutProps.put("verticalPadding", isFreemium ? 10 : 0);
        layoutProps.put("showPagination", false);
        return layoutProps;
    }

    public ActiveCard getUpcomingPhleboCard(UserContext userContext, UpcomingPhleboTaskResponse phleboTask, CgmOnboardingStatusResponse cgmOnboardingStatusResponse) {
        ActiveCard phleboTaskCard = new ActiveCard();
        boolean isBlucon = phleboTask.getDeviceType() == DeviceType.BLUCON;
        String kitOrCgm = isBlucon ? "CGM Reading Device" : "CGM";
        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(SCHEDULED);
        cardStatus.setColor("GREEN");
        ActiveCardDetails details = new ActiveCardDetails();
        details.setTitle(getPhleboCardTitle(phleboTask, kitOrCgm));
        details.setIconImageUrl(isBlucon ? "/image/chroniccare/ambrosia_icon.png" : "/image/chroniccare/cgm_icon_1.png");
        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("MMM dd, h:mm");
        dateFormatStartTime.setTimeZone(getUserTimezone(userContext));
        long startDate = phleboTask.getStartTime().getTime();
        String formattedDateStart = dateFormatStartTime.format(startDate);
        SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
        dateFormatEndTime.setTimeZone(getUserTimezone(userContext));
        long endTime = phleboTask.getEndTime().getTime();
        String formattedDateEnd = dateFormatEndTime.format(endTime);
        String subTitle;
        if (startDate == endTime) {
            subTitle = formattedDateStart.replace("am", "AM").replace("pm", "PM");
        } else {
            subTitle = formattedDateStart + " - " + formattedDateEnd.replace("am", "AM").replace("pm", "PM");
        }
        details.setSubTitle(subTitle);

        phleboTaskCard.setStatus(cardStatus);
        phleboTaskCard.setSubType(String.valueOf(phleboTask.getTaskCategory()));
        if (isBlucon) {
            phleboTaskCard.setInstructionColor("#F05C00");
            TextStyle instructionStyle = new TextStyle();
            instructionStyle.setFontSize(13);
            instructionStyle.setFontFamily("Inter-Medium");
            phleboTaskCard.setInstructionStyle(instructionStyle);
            phleboTaskCard.setInstructionImageUrl("/image/chroniccare/active_card_warning.png");
            SfCgmDeviceStatus deviceStatus = getCgmDeviceStatus(userContext, cgmOnboardingStatusResponse, null);
            if (deviceStatus != null && deviceStatus.getIsOnGoingDevice()) {
                phleboTaskCard.setInstruction("Your CGM installation process is completed");
                instructionStyle.setColor("#F05C00");
            } else {
                phleboTaskCard.setInstruction("Don't install the CGM now. CGM expert will guide you about the installation process on video call.");
                phleboTaskCard.setInstructionImageUrl("/image/chroniccare/active_card_warning.png");
                instructionStyle.setColor("#5C5C78");
            }
        } else {
            phleboTaskCard.setInfoTitle("MORE INFO");
            phleboTaskCard.setInstruction(getPhleboCardInstruction(phleboTask, kitOrCgm));
        }

        phleboTaskCard.setBookingDetails(details);
        phleboTaskCard.setStartDate(phleboTask.getStartTime());
        phleboTaskCard.setEndDate(phleboTask.getEndTime());

        Long phleboId = null;
        Long phleboTaskId = null;
        if (phleboTask != null) {
            if (phleboTask.getPhleboProfile() != null && phleboTask.getPhleboProfile().getId() != null) {
                phleboId = phleboTask.getPhleboProfile().getId();
            }
            if (!CollectionUtils.isEmpty(phleboTask.getPhleboTaskIds())) {
                phleboTaskId = phleboTask.getPhleboTaskIds().getFirst();
            }
        }

        String userTimeZone = userContext.getUserProfile().getTimezone();
        if (isToday(phleboTask.getStartTime().getTime(), userTimeZone) && phleboTask.getPhleboProfile() != null && phleboTask.getPhleboProfile().getPhoneNumber() != null) {
            String phleboPhoneNumber = phleboTask.getPhleboProfile().getPhoneNumber();
            try {
                phleboPhoneNumber = Arrays.stream(phleboPhoneNumber.trim().split("-")).toList().get(1);
            } catch (Exception e) {
                log.error("Error in splitting phlebo phone number" + e.getMessage());
            }
            if (Objects.nonNull(phleboPhoneNumber) && !phleboPhoneNumber.isEmpty()) {
                if (isPhleboAppEnabled(userContext)) {
                    Map<String, Object> meta = new HashMap<>();
                    meta.put("phone", phleboPhoneNumber);
                    meta.put("phleboTaskId", phleboTaskId);
                    phleboTaskCard.setPrimaryAction(Action.builder().actionType(ActionType.SF_PHLEBO_PHONE_CALL_BRIDGING).title("Call CGM Expert").meta(meta).isEnabled(true).build());
                } else {
                    phleboTaskCard.setPrimaryAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).url("tel:" + phleboPhoneNumber).title("Call CGM Expert").isEnabled(true).build());
                }
                phleboTaskCard.setDefaultExpanded(true);
            }

            boolean showLiveLocationCTA = !(phleboTask.getTaskState() == null || phleboTask.getTaskState() == PhleboTaskState.SCHEDULED || phleboTask.getTaskState() == PhleboTaskState.COMPLETED || phleboTask.getTaskState() == PhleboTaskState.RESCHEDULED);
            if (isPhleboAppEnabled(userContext) && showLiveLocationCTA) {
                phleboTaskCard.setTertiaryAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfphlebolivelocationpage?phleboId=" + phleboId + "&phleboTaskId=" + phleboTaskId + "&subTitle=" + subTitle).title("Track Phlebo").isEnabled(true).build());
            }
            phleboTaskCard.setShowInfoWithCta(true);
        }

        if (!CollectionUtils.isEmpty(phleboTask.getUserActionList())) {
            List<DropdownAction> actions = new ArrayList<>();
            if (phleboTask.getUserActionList().contains(PhleboTaskUserAction.CANCEL_BOOKING)) {
                DropdownAction cancelDropdownAction = new DropdownAction();
                cancelDropdownAction.setText("Cancel Booking");
                Action cancelAction = getPhleboCancelCGMInstallationAction(phleboTask, "Cancel Booking");
                cancelDropdownAction.setValue(cancelAction);
                actions.add(cancelDropdownAction);
            }
            if (phleboTask.getUserActionList().contains(PhleboTaskUserAction.RESCHEDULE_BOOKING)) {
                DropdownAction rescheduleDropdownAction = new DropdownAction();
                rescheduleDropdownAction.setText("Reschedule");
                Action rescheduleAction = getPhleboTaskRecommendedSlotsPageRedirectAction("Reschedule", true, true, phleboTask);
                rescheduleAction.setIcon(ActionIcon.RESCHEDULE);
                rescheduleDropdownAction.setValue(rescheduleAction);
                actions.add(rescheduleDropdownAction);
            }
            if (isCsTicketSupportedVersion(userContext)) {
                DropdownAction dropdownAction = new DropdownAction();
                dropdownAction.setText("Contact helpdesk");
                Action action = Action.builder().actionType(ActionType.NAVIGATION)
                        .url("curefit://sfsupportpage").title("Contact helpdesk").build();
                dropdownAction.setValue(action);
                actions.add(dropdownAction);
            }
            phleboTaskCard.setSecondaryActions(actions);
        }

        boolean primaryActionEnabled = Objects.nonNull(phleboTaskCard.getPrimaryAction()) && Boolean.TRUE.equals(phleboTaskCard.getPrimaryAction().getIsEnabled());
        boolean primaryActionDisabled = Objects.nonNull(phleboTaskCard.getPrimaryAction()) && Objects.nonNull(phleboTaskCard.getPrimaryAction().getDisabled()) && Boolean.TRUE.equals(phleboTaskCard.getPrimaryAction().getDisabled());
        if (primaryActionEnabled && !primaryActionDisabled) {
            phleboTaskCard.setDefaultExpanded(true);
        }
        phleboTaskCard.setViewMoreTitle("INSTRUCTIONS TO BE FOLLOWED");

        return phleboTaskCard;
    }

    public static boolean isFirstCoachConsultationBooked(ActivePackResponse activePackResponse, ExceptionReportingService exceptionReportingService) {
        try {
            if (Objects.nonNull(activePackResponse)) {
                Optional<Boolean> isFirstConsultationBooked = resolveNullable(() -> activePackResponse
                        .getUserMembershipInfos().stream().anyMatch(userMembershipInfo ->
                                userMembershipInfo.getCategoryCode().equals(CategoryCode.CONSULTATION)
                                        && userMembershipInfo.getProductCode().equals(SUGARFIT_LONG_COACH_CONSULT)
                                        && userMembershipInfo.getTicketsConsumed() == 1));
                return isFirstConsultationBooked.orElse(false);
            }
        } catch (Exception e) {
            log.error("Exception in fetching active devices for congrats page", e);
            exceptionReportingService.reportException("Exception in fetching active devices for congrats page", e);
        }
        return false;
    }

    public Action getWelcomeCallCancellationAction(String actionTitle, Action primaryAction, Action secondaryAction) {
        HashMap<String, Object> meta = new HashMap<>();
        meta.put("title", "Are you sure about canceling?");
        meta.put("subtitle", "Coach consultation is the first most important step of your journey.");
        if (primaryAction != null) {
            meta.put("primaryAction", primaryAction);
        }
        if (secondaryAction != null) {
            meta.put("secondaryAction", secondaryAction);
        }
        meta.put("type", "COACH");
        return Action.builder().actionType(ActionType.SF_WELCOME_CALL_CANCELLATION).title(!actionTitle.equals("") ? actionTitle : "Cancel Booking").meta(meta).build();
    }

    public Action getConsultCancellationAction(ActiveConsultationResponse consultation, String title) {
        CancelActionMeta cancelActionMeta = new CancelActionMeta();
        cancelActionMeta.setTcBookingId(consultation.getBookingId());
        return Action.builder().actionType(ActionType.CANCEL_TC).title(title).meta(cancelActionMeta).build();
    }

    public Action getConsultRescheduleAction(ActiveConsultationResponse consultation, String title) {
        TeleconsultationManageOptionsMeta meta = new TeleconsultationManageOptionsMeta();
        meta.setTcBookingId(consultation.getBookingId());
        meta.setProductId(consultation.getProductCode());
        meta.setParentBookingId(consultation.getBookingId());
        meta.setPatientId(consultation.getPatientId());
        meta.setDoctorId(consultation.getDoctorId());
        return Action.builder().actionType(ActionType.RESCHEDULE_TC).title(title).actionId(ActionId.RESCHEDULE).meta(meta).build();
    }

    public List<ActiveCard> getPhleboActiveCards(UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            List<UpcomingPhleboTaskResponse> scheduledPhleboTaskList = serviceInterfaces.getSfAlbusClient().getAllScheduledPhleboTask(userId);
            CompletableFuture<CgmOnboardingStatusResponse> cgmOnboardingStatusFuture = getCgmOnboardingStatusFuture(userId, AppTenant.SUGARFIT);
            CgmOnboardingStatusResponse cgmOnboardingStatus = cgmOnboardingStatusFuture.get();

            return scheduledPhleboTaskList.parallelStream()
                    .map((phleboTask -> this.getUpcomingPhleboCard(userContext, phleboTask, cgmOnboardingStatus))).toList();
        } catch (Exception e) {
            String msg = String.format("Error on fetching phlebo tasks for user :: %s",
                    userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
            return new ArrayList<>();
        }
    }

    public ActiveCard getCgmDeliveryActiveCard(UserContext userContext) {
        if (!isCgmDeliveryTrackingEnabledAppVersion(userContext)) {
            return null;
        }
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            try {
                DeviceShipmentResponse deviceShipmentResponse = serviceInterfaces.getSfAlbusClient().getDeviceShipmentResponse(userId);
                if (Objects.nonNull(deviceShipmentResponse) && Objects.nonNull(deviceShipmentResponse.getOrderDate())) {
                    boolean isTierOneUser = false;
                    UserPreferencePojo userPreferencePojo = serviceInterfaces.getSfAlbusClient().getUserPreference(userContext.getUserProfile().getUserId(), UserPreferencePojo.PreferenceType.META, "cgmServiceAvailable");
                    if (Objects.nonNull(userPreferencePojo) && !CollectionUtils.isEmpty(userPreferencePojo.getPreferenceTypeValues())) {
                        isTierOneUser = Boolean.parseBoolean(userPreferencePojo.getPreferenceTypeValues().get(0));
                    }
                    if (!"DELIVERED".equalsIgnoreCase(deviceShipmentResponse.getStatus()) && !"CANCELLED".equalsIgnoreCase(deviceShipmentResponse.getStatus())) {
                        Calendar twoDaysFromToday = Calendar.getInstance(getUserTimezone(userContext));
                        twoDaysFromToday.add(Calendar.DAY_OF_MONTH, 2);

                        ActiveCardStatus status = new ActiveCardStatus();
                        status.setTitle("SHIPPED".equalsIgnoreCase(deviceShipmentResponse.getStatus()) ? E_COM_ORDER_SHIPPED : E_COM_ORDER_CONFIRMED);
                        status.setColor("SHIPPED".equalsIgnoreCase(deviceShipmentResponse.getStatus()) ? "YELLOW" : "BLUE");
                        Date edd = Objects.nonNull(deviceShipmentResponse.getDeliveryEta()) ? deviceShipmentResponse.getDeliveryEta() : twoDaysFromToday.getTime();
                        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, h:mm aa");
                        dateFormat.setTimeZone(getUserTimezone(userContext));
                        String formattedEddText = dateFormat.format(edd).replace("am", "AM").replace("pm", "PM");

                        ActiveCard activeCard = new ActiveCard();
                        ActiveCardDetails activeCardDetails = new ActiveCardDetails();
                        activeCardDetails.setTitle("CGM tracking");
                        activeCardDetails.setSubTitle("Delivery by: " + formattedEddText);
                        activeCardDetails.setIconImageUrl("/image/chroniccare/cgm_icon_1.png");


                        HashMap<String, Object> primaryActionMeta = new HashMap<>();
                        SfECommerceProduct cgmProduct = new SfECommerceProduct();
                        cgmProduct.setTitle("Continuous Glucose Monitor");
                        primaryActionMeta.put("product", cgmProduct);

                        SfECommerceDeliveryDetails deliveryDetails = new SfECommerceDeliveryDetails();
                        Calendar deliveryDateTo = Calendar.getInstance();
                        deliveryDateTo.setTime(edd);
                        deliveryDateTo.add(Calendar.DATE, 4);
                        deliveryDetails.setDeliveryEtaFrom(edd);
                        String deliveryTitle = "Delivery by";
                        deliveryDetails.setDeliveryByTitle("DELIVERED".equalsIgnoreCase(deviceShipmentResponse.getStatus()) ? "Delivered on" : deliveryTitle);
                        deliveryDetails.setTrackingDetails(
                                getCgmTrackingDetails(deviceShipmentResponse.getTrackingHistoryList(), deviceShipmentResponse.getOrderDate(), edd));
                        primaryActionMeta.put("deliveryDetails", deliveryDetails);

                        activeCard.setStatus(status);
                        activeCard.setStartDate(edd);
                        activeCard.setBookingDetails(activeCardDetails);

                        SfDeliveryTrackingDetails sfDeliveryTrackingDetails = new SfDeliveryTrackingDetails();
                        sfDeliveryTrackingDetails.setModalTitle("Tracking Details");
                        sfDeliveryTrackingDetails.setTitle("CGM package");
                        sfDeliveryTrackingDetails.setTrackingId(deviceShipmentResponse.getTrackingId());
                        if (!CollectionUtils.isEmpty(deviceShipmentResponse.getTrackingHistoryList())) {
                            List<SfDeliveryTrackingDetails.TrackingState> trackingStates = new ArrayList<>();
                            deviceShipmentResponse.getTrackingHistoryList().forEach(trackingState -> {
                                if (Objects.nonNull(trackingState)) {
                                    Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
                                    SfDeliveryTrackingDetails.TrackingState deliveryTrackingState = new SfDeliveryTrackingDetails.TrackingState();
                                    deliveryTrackingState.setStatusText(trackingState.getStatus());
                                    deliveryTrackingState.setTime(trackingState.getTimestamp());
                                    if (Objects.nonNull(trackingState.getTimestamp())) {
                                        deliveryTrackingState.setIsEstimatedTime(calendar.getTime().getTime() < trackingState.getTimestamp().getTime());
                                    }
                                    deliveryTrackingState.setIsComplete(trackingState.getIsComplete());
                                    deliveryTrackingState.setStatusDescription(trackingState.getLocation());
                                    trackingStates.add(deliveryTrackingState);
                                }
                                sfDeliveryTrackingDetails.setTrackingStates(trackingStates);
                            });

                        }
                        activeCard.setPrimaryAction(
                                Action.builder().isEnabled(true).title("Track Now").actionType(ActionType.SF_SHOW_SHIPMENT_TRACKING_MODAL).meta(sfDeliveryTrackingDetails).build());
                        activeCard.setInstruction("You can check your shipment status here");

                        if (!isTierOneUser) {
                            HashMap<String, Object> secondaryActionMeta = new HashMap<>();
                            secondaryActionMeta.put("orderId", String.valueOf(deviceShipmentResponse.getOrderId()));
                            activeCard.setSecondaryAction(
                                    Action.builder().isEnabled(true).title("Install my CGM").actionType(ActionType.ACTION_SF_SHOW_DELIVERY_CONFIRMATION_MODAL).meta(secondaryActionMeta).build());
                        }

                        activeCard.setDefaultExpanded(false);
                        activeCard.setViewMoreTitle("VIEW DETAILS");

                        return activeCard;
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
            return null;
        } catch (Exception e) {
            String msg = String.format("Error on getCgmDeliveryActiveCards :: %s",
                    userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
            return null;
        }
    }

    private SfECommerceOrderTrackingDetails getCgmTrackingDetails(List<DeviceShipmentResponse.TrackingHistory> trackingHistoryList, Date orderDate, Date deliveryEta) {
        SfECommerceOrderTrackingDetails trackingDetails = new SfECommerceOrderTrackingDetails();
        Optional<SfECommerceOrderTrackingDetails.TrackingState> confirmedDateOpt = trackingDetails.getTrackingStates().stream().filter(s -> s.getStatus().equals(SfECommerceOrderTrackingDetails.TrackingStatus.CONFIRMED)).findFirst();
        confirmedDateOpt.ifPresent(trackingState -> trackingState.setTime(orderDate));
        Optional<SfECommerceOrderTrackingDetails.TrackingState> deliveryDateOpt = trackingDetails.getTrackingStates().stream().filter(s -> s.getStatus().equals(SfECommerceOrderTrackingDetails.TrackingStatus.DELIVERED)).findFirst();

        try {
            if (!CollectionUtils.isEmpty(trackingHistoryList)) {
                trackingHistoryList.forEach(trackingHistory -> {
                    if (!Objects.isNull(trackingHistory) && !Objects.isNull(trackingHistory.getStatus()) && !Objects.isNull(trackingHistory.getTimestamp())) {
                        if (trackingHistory.getStatus().equalsIgnoreCase("SHIPPED")) {
                            Optional<SfECommerceOrderTrackingDetails.TrackingState> shippedDateOpt = trackingDetails.getTrackingStates().stream().filter(s -> s.getStatus().equals(SfECommerceOrderTrackingDetails.TrackingStatus.SHIPPED)).findFirst();
                            shippedDateOpt.ifPresent(trackingState -> trackingState.setTime(trackingHistory.getTimestamp()));
                            shippedDateOpt.ifPresent(trackingState -> trackingState.setComplete(true));
                        }
                        if (trackingHistory.getStatus().equalsIgnoreCase("DELIVERED")) {
                            deliveryDateOpt.ifPresent(trackingState -> trackingState.setTime(trackingHistory.getTimestamp()));
                            deliveryDateOpt.ifPresent(trackingState -> trackingState.setComplete(true));
                        }
                    }
                });
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return trackingDetails;
    }

    public boolean isCGMInstallationBookingAllowed(UserOnboardingActionWithContext context) {
        try {
            return context.getCgmInstallationCardActionWithContext().getAction().isCgmInstallationBookingAllowed();
        } catch (Exception e) {
        }
        return false;
    }

    public boolean isCGMInstallationBookingPending(UserOnboardingActionWithContext context) {
        try {
            return context.getCgmInstallationCardActionWithContext().getAction().isCgmInstallationBookingPending();
        } catch (Exception e) {
        }
        return false;
    }

    public boolean isCGMInstallationCompleted(UserOnboardingActionWithContext context) {
        try {
            return context.getCgmInstallationCardActionWithContext().getAction().isCgmInstallationCompleted();
        } catch (Exception e) {
        }
        return false;
    }

    public CompletableFuture<List<ActiveCard>> getECommerceOrderActiveCardsFuture(UserContext userContext) {
        return supplyAsync(() -> getECommerceOrderActiveCards(userContext), serviceInterfaces.getTaskExecutor());
    }

    public Action getCoachConsultBookingAction(UserContext userContext, String title) throws Exception {
        PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
        String actionTitle = !title.equals("") ? title : "Book Coach Consult";
        Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());
        ChronicCareTeam assignedCareTeam = getAssignedCareTeam(userContext, patientDetail.getId(),
                activePackResponse.map(ActivePackResponse::getBundleProduct).orElse(null));

        if (assignedCareTeam.getCoach() != null && activePackResponse.isPresent()) {
            UserOnboardingActionWithContext onboardingActions = getUserOnboardingActionsFuture(
                    serviceInterfaces,
                    userContext,
                    activePackResponse,
                    userOnboardingService,
                    exceptionReportingService
            ).get();

            String consultationProduct = onboardingActions.getCoachCardActionWithContext().getContext()
                    .getProductCodes().stream().findFirst().get();

            Long centerId = assignedCareTeam.getCoach().getAgentResponse().getAgentCenterMapping().stream().findFirst().get()
                    .getCenterId();

            return Action.builder()
                    .isEnabled(true)
                    .title(actionTitle)
                    .actionType(ActionType.NAVIGATION)
                    .url(SfHomePageUtil.getAgentDatePickerUrl(consultationProduct, centerId, assignedCareTeam.getCoach().getAgentResponse().getId(), activePackResponse.get()))
                    .build();
        }
        return null;
    }

    public Action getPyschologyConsultBookingAction(UserContext userContext) throws Exception {
        String actionTitle = "Book Psychologist Consult";
        Optional<ActivePackResponse> activePackResponse = userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());
        if (activePackResponse.isPresent()) {
            return Action.builder()
                    .isEnabled(true)
                    .title(actionTitle)
                    .actionType(ActionType.NAVIGATION)
                    .url(getPsychologistAgentDatePickerUrl(SUGARFIT_PSYCHOLOGIST_CONSULT, activePackResponse.get()))
                    .build();
        }
        return null;
    }

    public List<ActiveCard> getECommerceOrderActiveCards(UserContext userContext) {
        List<ActiveCard> orderActiveCards = new ArrayList<>();
        if (isECommerceSupported(userContext)) {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            try {
                List<OrderResponse> orderResponses = indusClient.getUserOrders(userId, 0L, 10L);
                if (!CollectionUtils.isEmpty(orderResponses)) {
                    orderResponses.forEach(orderResponse -> {
                        if (!CollectionUtils.isEmpty(orderResponse.getProductResponseList())) {
                            if (orderResponse.getProductResponseList().stream().noneMatch(p -> p.getShipmentStatus().equals(ShipmentStatus.DELIVERED))) {
                                List<OrderProductResponse> pendingToDeliver = new ArrayList<>(orderResponse.getProductResponseList().stream()
                                        .filter(p -> p.getShipmentStatus().equals(ShipmentStatus.SHIPPED) || p.getShipmentStatus().equals(ShipmentStatus.PENDING)).toList());
                                if (!CollectionUtils.isEmpty(pendingToDeliver)) {
                                    Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
                                    calendar.add(Calendar.DAY_OF_MONTH, 2);
                                    pendingToDeliver.sort(Comparator.comparingLong(i -> Objects.nonNull(i.getEdd()) ? i.getEdd().getTime() : calendar.getTime().getTime()));
                                    Collections.reverse(pendingToDeliver);
                                    OrderProductResponse orderToDisplay = pendingToDeliver.get(0);
                                    DateFormat sdf = new SimpleDateFormat("E, dd MMM");
                                    sdf.setTimeZone(getUserTimezone(userContext));
                                    String orderDate = sdf.format(orderResponse.getOrderDate());
//                                String deliveryDate = sdf.format(order.getEdd());

                                    ActiveCardStatus status = new ActiveCardStatus();
                                    status.setTitle(orderToDisplay.getShipmentStatus().equals(ShipmentStatus.SHIPPED) ? E_COM_ORDER_SHIPPED : E_COM_ORDER_CONFIRMED);
                                    status.setColor(orderToDisplay.getShipmentStatus().equals(ShipmentStatus.SHIPPED) ? "YELLOW" : "BLUE");

                                    ActiveCard activeCard = new ActiveCard();
                                    ActiveCardDetails orderDetails = new ActiveCardDetails();
                                    if (pendingToDeliver.size() > 1) {
                                        orderDetails.setTitle(String.format("%s + %s more", orderToDisplay.getTitle(), pendingToDeliver.size() - 1));
                                    } else {
                                        orderDetails.setTitle(orderToDisplay.getOrderedQuantity() + " × " + orderToDisplay.getTitle());
                                    }
                                    String shipmentStatusToDisplay = "Confirmed";
                                    if (!Objects.isNull(orderToDisplay.getShipmentStatus())) {
                                        switch (orderToDisplay.getShipmentStatus()) {
                                            case PENDING -> shipmentStatusToDisplay = "Confirmed";
                                            case SHIPPED -> shipmentStatusToDisplay = "Shipped";
                                            case DELIVERED -> shipmentStatusToDisplay = "Delivered";
                                            case CANCELLED -> shipmentStatusToDisplay = "Cancelled";
                                            default -> {
                                            }
                                        }
                                    }
                                    orderDetails.setSubTitle("Status: " + shipmentStatusToDisplay);
                                    if (!CollectionUtils.isEmpty(orderToDisplay.getProductImages())) {
                                        Map<String, Object> images = orderToDisplay.getProductImages();
                                        if (images.containsKey("small")) {
                                            List<String> urls = (List<String>) images.get("small");
                                            if (!CollectionUtils.isEmpty(urls)) {
                                                orderDetails.setIconImageUrl(urls.get(0));
                                                orderDetails.setIconImageResizeMode("contain");
                                                orderDetails.setIconImageBorderRadius(5);
                                            }
                                        }
                                    }

                                    activeCard.setStatus(status);
                                    activeCard.setStartDate(!Objects.isNull(orderToDisplay.getEdd()) ? orderToDisplay.getEdd() : orderResponse.getOrderDate());
                                    activeCard.setBookingDetails(orderDetails);
//                                activeCard.setPrimaryActionInstruction("Order will be delivered by " + deliveryDate);
                                    activeCard.setPrimaryAction(
                                            Action.builder().isEnabled(true).title("Track Now").actionType(ActionType.NAVIGATION).url("curefit://sfecommerceorderdetails?orderId=" + orderResponse.getOrderId()).build());
                                    activeCard.setInstruction("You can check your order status here");

                                    activeCard.setDefaultExpanded(false);
                                    activeCard.setViewMoreTitle("VIEW MORE");

                                    List<ProductActions> productActions = null;
                                    try {
                                        productActions = indusClient.getProductActions(orderResponse.getOrderId());
                                        if (!CollectionUtils.isEmpty(productActions)) {
                                            if (productActions.contains(ProductActions.CANCEL_ORDER)) {
                                                DropdownAction cancelOrderDrop = new DropdownAction();
                                                cancelOrderDrop.setText("Cancel Order");
                                                Action cancelOrderAction = Action.builder().actionType(ActionType.SF_CANCEL_ECOM_ORDER)
                                                        .subtitle("Order can only be cancelled before the shipment is done")
                                                        .meta(new HashMap<String, Object>() {{
                                                            put("orderId", orderResponse.getOrderId());
                                                        }}).build();
                                                cancelOrderDrop.setValue(cancelOrderAction);
                                                DropdownAction helpDeskDrop = new DropdownAction();
                                                helpDeskDrop.setText("Contact helpdesk");
                                                helpDeskDrop.setValue(Action.builder().actionType(ActionType.NAVIGATION)
                                                        .url("curefit://sfsupportpage").title("Contact helpdesk").build());
                                                activeCard.setSecondaryActions(List.of(cancelOrderDrop, helpDeskDrop));
                                            }
                                        }
                                    } catch (HttpException e) {
                                        exceptionReportingService.reportException(e);
                                    }


                                    orderActiveCards.add(activeCard);
                                }
                            }
                        }
                    });
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
        }
        return orderActiveCards;
    }

    public CompletableFuture<List<ActiveCard>> getDiagnosticActiveCardsFuture(UserContext userContext, PatientDetail patientDetail) {
        return supplyAsync(() -> getDiagnosticActiveCards(userContext, patientDetail), serviceInterfaces.getTaskExecutor());
    }

    public List<ActiveCard> getDiagnosticActiveCards(UserContext userContext, PatientDetail patientDetail) {
        List<ActiveCard> diagnosticActiveCards = new ArrayList<>();
        try {
            String userId = userContext.getUserProfile().getUserId();
            CompletableFuture<List<BookingInfo<DiagnosticStateInfo>>> diagnosticBookings = fetchDiagnosticBookingsFuture(userId, patientDetail);
            CompletableFuture<List<com.sugarfit.housemd.pojo.order.OrderResponse>> ekinCareBookings = fetchEkinCareOrdersFuture(userId);

            for (com.sugarfit.housemd.pojo.order.OrderResponse diagnosticBooking : ekinCareBookings.get()) {
                try {
                    diagnosticActiveCards.add(this.getDiagnosticActiveCardV3(userContext, diagnosticBooking));
                } catch (Exception e) {
                    log.error("Error while fetching Diagnostic Active card", e);
                    exceptionReportingService.reportException("Error while fetching Diagnostic Active card", e);
                }
            }

            for (BookingInfo<DiagnosticStateInfo> diagnosticBooking : diagnosticBookings.get()) {
                DiagnosticsTestOrderResponse diagnosticsTestOrderResponse = diagnosticBooking
                        .getDiagnosticsTestOrderResponse().get(0);
                if ((diagnosticsTestOrderResponse.getThirdPartySellerMetaData() != null)
                        || (diagnosticsTestOrderResponse.getAtHomeDiagnosticOrder() != null)) {
                    try {
                        diagnosticActiveCards.add(this.getDiagnosticActiveCardV2(userContext, diagnosticBooking));
                    } catch (Exception e) {
                        log.error("Error while fetching Diagnostic Active card", e);
                        exceptionReportingService.reportException("Error while fetching Diagnostic Active card", e);
                    }
                }
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return diagnosticActiveCards;
    }

    public CompletableFuture<List<BookingInfo<DiagnosticStateInfo>>> fetchDiagnosticBookingsFuture(String userId, PatientDetail patientDetail) {
        return supplyAsync(() -> {
            try {
                if (Objects.nonNull(patientDetail)) {
                    return serviceInterfaces.sfAlbusClient.getActiveDiagnosticBookings(userId, patientDetail.getId(), null);
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
            return new ArrayList<>();
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<List<com.sugarfit.housemd.pojo.order.OrderResponse>> fetchEkinCareOrdersFuture(String userId) {
        return supplyAsync(() -> fetchEkinCareOrders(userId), serviceInterfaces.getTaskExecutor());
    }

    public List<com.sugarfit.housemd.pojo.order.OrderResponse> fetchEkinCareOrders(String userId) {
        try {
            OrderFilterRequest diagnosticsFilterRequest = new OrderFilterRequest();
            diagnosticsFilterRequest.setActiveOrders(true);
            diagnosticsFilterRequest.setUserIds(List.of(Long.valueOf(userId)));
            OrderFilterResponse orderFilterResponse = housemdClient.filterOrders(diagnosticsFilterRequest, userId);
            if (orderFilterResponse != null && !CollectionUtils.isEmpty(orderFilterResponse.getOrderResponses())) {
                return orderFilterResponse.getOrderResponses();
            }
        } catch (Exception e) {
            exceptionReportingService.reportException("Error while fetching EkinCare diagnostic orders", e);
        }
        return new ArrayList<>();
    }

    private void setTertiaryActionForDiagnostics(ActiveCard card, BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        String productCodesCsv = "";
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(
                diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getProductCodes())) {
            productCodesCsv = diagnosticBooking.getDiagnosticsTestOrderResponse()
                    .get(0)
                    .getProductCodes()
                    .stream()
                    .collect(Collectors.joining(","));
        }

        if (!productCodesCsv.isEmpty()) {
            card.setTertiaryAction(this.getShowDiagnosticInstructionsAction(productCodesCsv));
        } else {
            card.setTertiaryAction(this.getShowInstructionsAction());
        }
    }

    public ActiveCard getDiagnosticActiveCardV2(UserContext userContext,
                                                BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        ActiveCard card = new ActiveCard();
        try {
            Product product = serviceInterfaces.getCatalogueService()
                    .getProduct(diagnosticBooking.getBooking().getProductCode());
            card.setTitle(product.getTitle());
        } catch (BaseException e) {
            String message = String.format("Error fetching product :: %s, error :: %s",
                    diagnosticBooking.getBooking().getProductCode(), e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
            card.setTitle("Diagnostic Tests");
        }
        card.setType("DIAGNOSTICS");
        card.setSubType("DIAGNOSTICS");
        ActiveCardStatus.StatusType status = getDiagnosticStatus(diagnosticBooking);
        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(status);
        cardStatus.setColor(getDiagnosticsStatusColor(status));
        card.setStatus(cardStatus);
        card.setInfoTitle("INSTRUCTIONS");
        card.setInstruction("To prepare for the test, please follow the instructions provided");
        boolean is3POrder = diagnosticBooking.getDiagnosticsTestOrderResponse().get(0)
                .getAtHomeDiagnosticOrder() == null;
        if ((!is3POrder) && status.equals(SCHEDULED)) {
            List<DropdownAction> actions = new ArrayList<>();
            DropdownAction cancelDropdownAction = new DropdownAction();
            cancelDropdownAction.setText("Cancel Booking");
            Action cancelAction = new Action();
            cancelAction.setActionType(ActionType.CANCEL_TC);
            CancelActionMeta cancelActionMeta = new CancelActionMeta();
            cancelActionMeta.setTcBookingId(diagnosticBooking.getBooking().getId());
            cancelAction.setMeta(cancelActionMeta);
            cancelDropdownAction.setValue(cancelAction);
            actions.add(cancelDropdownAction);
            boolean phleboCallingAllowed = diagnosticBooking.getDiagnosticsTestOrderResponse().get(0)
                    .getAtHomeStepInfo().getAllowedActions().contains(AlbusActionType.PHLEBO_CALLING);
            if (phleboCallingAllowed) {
                String phleboNumber = diagnosticBooking.getDiagnosticsTestOrderResponse().get(0)
                        .getAtHomeDiagnosticOrder().getPhleboMobileNumber();
                Action phleboCallAction = new Action();
                phleboCallAction.setActionType(ActionType.PHONE_CALL_NAVIGATION);
                phleboCallAction.setTitle("Call");
                PhleboCallingMeta meta = new PhleboCallingMeta();
                meta.setPhoneNumber(phleboNumber);
                phleboCallAction.setMeta(meta);
                phleboCallAction.setIsEnabled(true);
                card.setPrimaryAction(phleboCallAction);
            } else {
                Action toastAction = getDisabledActionForToastMessage("Available 1 Hour before your slot", "Call");
                toastAction.setIsEnabled(false);
                card.setPrimaryAction(toastAction);
            }
            if (diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getAtHomeStepInfo().getAllowedActions().contains(AlbusActionType.RESCHEDULE)
                    && isRescheduleDiagnosticsSupported(userContext)) {
                actions.add(this.getRescheduleDropdownAction(diagnosticBooking));
            }
            card.setSecondaryActions(actions);

            Optional<String> phleboMobileNumber = resolveNullable(() -> diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getAtHomeDiagnosticOrder().getPhleboMobileNumber());
            Optional<String> phleboName = resolveNullable(() -> diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getAtHomeDiagnosticOrder().getPhleboName());
            if (phleboMobileNumber.isPresent() && isToday(getDiagnosticCardStartTime(diagnosticBooking), userContext.getUserProfile().getTimezone())) {
                card.setInfoTitle(phleboName.orElse("CONTACT AGENT"));
                card.setInstructionImageUrl("/image/chroniccare/call_instruction_icon.png");
                card.setInstruction("Please click here to contact agent in case of delay in sample collection");
                card.setTertiaryAction(this.getPhleboCallingAction(phleboMobileNumber.get()));
            } else {
                card.setInstructionImageUrl("/image/chroniccare/dont_eat.png");
                setTertiaryActionForDiagnostics(card,diagnosticBooking);
            }
        } else if (status.equals(SCHEDULED)) {
            card.setInstructionImageUrl("/image/chroniccare/dont_eat.png");
            setTertiaryActionForDiagnostics(card,diagnosticBooking);
        } else if (status.equals(REPORT_GENERATED)) {
            card.setInfoTitle("EMAIL REPORTS");
            card.setTertiaryAction(getEmailReportAction(diagnosticBooking));
            card.setInstruction("Your report has been sent to you over Whatsapp.");

            try {
                List<UserReportEntry> diagnosticReports = chsClient.getUsersDiagnosticReport(Long.valueOf(userContext.getUserProfile().getUserId()));
                if (!CollectionUtils.isEmpty(diagnosticReports)) {
                    Optional<UserReportEntry> reportOptional = diagnosticReports.stream().filter(userReportEntry -> userReportEntry.getReferenceId().equals(diagnosticBooking.getBooking().getId())).findFirst();
                    if (reportOptional.isPresent()) {
                        UserReportEntry report = reportOptional.get();
                        Action showAssetAction = new Action();
                        Map<String, Object> actionMeta = new HashMap<>();
                        actionMeta.put("fileUrl", report.getReportUrl());
                        actionMeta.put("pageTitle", "Lab Report");
                        showAssetAction.setActionType(ActionType.SF_VIEW_ASSET);
                        showAssetAction.setMeta(actionMeta);
                        showAssetAction.setTitle("View Report");
                        showAssetAction.setIsEnabled(true);
                        card.setPrimaryAction(showAssetAction);
                    }
                }
            } catch (HttpException e) {
                //Ignore
            }

        } else if (status.equals(SAMPLE_COLLECTED)) {
            card.setInstruction(
                    "Samples for your diagnostic tests have been collected. You will receive your reports in 24-48 hours.");
        }
        SfDeliveryTrackingDetails sfDeliveryTrackingDetails = getDiagnosticsTracking(userContext.getUserProfile().getUserId(), diagnosticBooking.getBooking().getId());
        if (Objects.nonNull(sfDeliveryTrackingDetails)) {
            card.setSecondaryAction(
                    Action.builder().isEnabled(true).title("Check Status").actionType(ActionType.SF_SHOW_SHIPMENT_TRACKING_MODAL).meta(sfDeliveryTrackingDetails).build());
        }
        ActiveCardDetails details = new ActiveCardDetails();
        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("MMM dd, h:mm");
        dateFormatStartTime.setTimeZone(getUserTimezone(userContext));
        long startDate = getDiagnosticCardStartTime(diagnosticBooking);
        String formattedDateStart = dateFormatStartTime.format(startDate);
        SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
        dateFormatEndTime.setTimeZone(getUserTimezone(userContext));
        long endTime = getDiagnosticCardEndTime(diagnosticBooking);
        String formattedDateEnd = dateFormatEndTime.format(endTime);
        String subTitle;
        card.setStartDate(new Date(startDate));
        card.setEndDate(new Date(endTime));
        if (startDate == endTime) {
            subTitle = formattedDateStart.replace("am", "AM").replace("pm", "PM");
        } else {
            subTitle = formattedDateStart + " - " + formattedDateEnd.replace("am", "AM").replace("pm", "PM");
        }
        details.setTitle("Diagnostic Tests");
        details.setSubTitle(subTitle);
        card.setStartDate(new Date(startDate));
        details.setIconImageUrl("/image/chroniccare/WhatsNextDiagnostics.png");
        card.setBookingDetails(details);

        boolean primaryActionEnabled = Objects.nonNull(card.getPrimaryAction()) && Boolean.TRUE.equals(card.getPrimaryAction().getIsEnabled());
        boolean primaryActionDisabled = Objects.nonNull(card.getPrimaryAction()) && Objects.nonNull(card.getPrimaryAction().getDisabled()) && Boolean.TRUE.equals(card.getPrimaryAction().getDisabled());
        if (primaryActionEnabled && !primaryActionDisabled) {
            card.setDefaultExpanded(true);
        }
        card.setViewMoreTitle("INSTRUCTIONS TO BE FOLLOWED");

        return card;
    }

    public ActiveCard getDiagnosticActiveCardV3(UserContext userContext, com.sugarfit.housemd.pojo.order.OrderResponse diagnosticBooking) {
        ActiveCard card = new ActiveCard();
        try {
            Product product = serviceInterfaces.getCatalogueService()
                    .getProduct(diagnosticBooking.getOrderEntry().getProductCodes());
            card.setTitle(product.getTitle());
        } catch (BaseException e) {
            String message = String.format("Error fetching product :: %s, error :: %s",
                    diagnosticBooking.getOrderEntry().getProductCodes(), e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
            card.setTitle("Diagnostic Tests");
        }
        card.setType("DIAGNOSTICS");
        card.setSubType("DIAGNOSTICS");
        ActiveCardStatus.StatusType status = getDiagnosticStatusV2(diagnosticBooking);
        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(status);
        cardStatus.setColor(getDiagnosticsStatusColor(status));
        card.setStatus(cardStatus);
        card.setInfoTitle("INSTRUCTIONS");
        card.setInstruction("Do not eat or drink anything except water 10-12 hours before the test");
        if (status.equals(SCHEDULED)) {
            List<DropdownAction> actions = new ArrayList<>();
            DropdownAction cancelDropdownAction = new DropdownAction();
            cancelDropdownAction.setText("Cancel Booking");
            Action cancelAction = new Action();
            cancelAction.setActionType(ActionType.CANCEL_TC);
            CancelActionMeta cancelActionMeta = new CancelActionMeta();
            cancelActionMeta.setTcBookingId(diagnosticBooking.getOrderEntry().getBookingId());
            cancelAction.setMeta(cancelActionMeta);
            cancelDropdownAction.setValue(cancelAction);
            actions.add(cancelDropdownAction);
            boolean phleboCallingAllowed = diagnosticBooking.getAllowedActions().contains(OrderAction.PHLEBO_CALLING);
            if (phleboCallingAllowed) {
                String phleboNumber = diagnosticBooking.getOrderEntry().getPhleboNumber();
                Action phleboCallAction = new Action();
                phleboCallAction.setActionType(ActionType.PHONE_CALL_NAVIGATION);
                phleboCallAction.setTitle("Call");
                PhleboCallingMeta meta = new PhleboCallingMeta();
                meta.setPhoneNumber(phleboNumber);
                phleboCallAction.setMeta(meta);
                phleboCallAction.setIsEnabled(true);
                card.setPrimaryAction(phleboCallAction);
            } else {
                Action toastAction = getDisabledActionForToastMessage("Available 1 Hour before your slot", "Call");
                toastAction.setIsEnabled(false);
                card.setPrimaryAction(toastAction);
            }
            if (diagnosticBooking.getAllowedActions().contains(OrderAction.RESCHEDULE)
                    && isRescheduleDiagnosticsSupported(userContext)) {
                actions.add(getRescheduleDropdownActionV2(diagnosticBooking));
            }
            card.setSecondaryActions(actions);
            card.setInstructionImageUrl("/image/chroniccare/dont_eat.png");
            card.setTertiaryAction(getShowInstructionsAction());
        } else if (status.equals(REPORT_GENERATED)) {
            card.setInfoTitle("EMAIL REPORTS");
            card.setTertiaryAction(getEmailReportActionV2(diagnosticBooking));
            card.setInstruction("Your report has been sent to you over Whatsapp.");

            try {
                List<UserReportEntry> diagnosticReports = chsClient.getUsersDiagnosticReport(Long.valueOf(userContext.getUserProfile().getUserId()));
                if (!CollectionUtils.isEmpty(diagnosticReports)) {
                    Optional<UserReportEntry> reportOptional = diagnosticReports.stream().filter(userReportEntry -> userReportEntry.getReferenceId().equals(diagnosticBooking.getOrderEntry().getBookingId())).findFirst();
                    if (reportOptional.isPresent()) {
                        UserReportEntry report = reportOptional.get();
                        Action showAssetAction = new Action();
                        Map<String, Object> actionMeta = new HashMap<>();
                        actionMeta.put("fileUrl", report.getReportUrl());
                        actionMeta.put("pageTitle", "Lab Report");
                        showAssetAction.setActionType(ActionType.SF_VIEW_ASSET);
                        showAssetAction.setMeta(actionMeta);
                        showAssetAction.setTitle("View Report");
                        showAssetAction.setIsEnabled(true);
                        card.setPrimaryAction(showAssetAction);
                    }
                }
            } catch (HttpException e) {
                //Ignore
            }
        } else if (status.equals(SAMPLE_COLLECTED)) {
            card.setInstruction(
                    "Samples for your diagnostic tests have been collected. You will receive your reports in 24-48 hours.");
        } else if (status.equals(MISSED)) {
            List<DropdownAction> actions = new ArrayList<>();
            if (diagnosticBooking.getAllowedActions().contains(OrderAction.RESCHEDULE)
                    && isRescheduleDiagnosticsSupported(userContext)) {
                actions.add(getRescheduleDropdownActionV2(diagnosticBooking));
                card.setPrimaryAction(getDiagnosticRescheduleAction(diagnosticBooking));
            }
            card.setSecondaryActions(actions);
            card.setInstruction("Please reschedule your appointment");
        }

        SfDeliveryTrackingDetails sfDeliveryTrackingDetails = getDiagnosticsTracking(userContext.getUserProfile().getUserId(), diagnosticBooking.getOrderEntry().getBookingId());
        if (Objects.nonNull(sfDeliveryTrackingDetails)) {
            card.setSecondaryAction(
                    Action.builder().isEnabled(true).title("Check Status").actionType(ActionType.SF_SHOW_SHIPMENT_TRACKING_MODAL).meta(sfDeliveryTrackingDetails).build());
        }

        ActiveCardDetails details = new ActiveCardDetails();
        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("MMM dd, h:mm");
        dateFormatStartTime.setTimeZone(getUserTimezone(userContext));
        long startDate = diagnosticBooking.getOrderEntry().getStartTime().getTime();
        String formattedDateStart = dateFormatStartTime.format(startDate);
        SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
        dateFormatEndTime.setTimeZone(getUserTimezone(userContext));
        long endTime = diagnosticBooking.getOrderEntry().getEndTime().getTime();
        String formattedDateEnd = dateFormatEndTime.format(endTime);
        String subTitle;
        card.setStartDate(new Date(startDate));
        card.setEndDate(new Date(endTime));
        if (startDate == endTime) {
            subTitle = formattedDateStart.replace("am", "AM").replace("pm", "PM");
        } else {
            subTitle = formattedDateStart + " - " + formattedDateEnd.replace("am", "AM").replace("pm", "PM");
        }
        details.setTitle("Diagnostic Tests");
        details.setSubTitle(subTitle);
        card.setStartDate(new Date(startDate));
        details.setIconImageUrl("/image/chroniccare/WhatsNextDiagnostics.png");
        card.setBookingDetails(details);

        boolean primaryActionEnabled = Objects.nonNull(card.getPrimaryAction()) && Boolean.TRUE.equals(card.getPrimaryAction().getIsEnabled());
        boolean primaryActionDisabled = Objects.nonNull(card.getPrimaryAction()) && Objects.nonNull(card.getPrimaryAction().getDisabled()) && Boolean.TRUE.equals(card.getPrimaryAction().getDisabled());
        if (primaryActionEnabled && !primaryActionDisabled) {
            card.setDefaultExpanded(true);
        }
        card.setViewMoreTitle("INSTRUCTIONS TO BE FOLLOWED");

        return card;
    }

    public Action getPhleboCallingAction(String phoneNumber) {
        Action action = new Action();
        action.setActionType(ActionType.PHONE_CALL_NAVIGATION);
        PhleboCallingMeta meta = new PhleboCallingMeta();
        meta.setPhoneNumber(phoneNumber);
        action.setMeta(meta);
        return action;
    }

    public Action getShowInstructionsAction() {
        Action showInstructionAction = new Action();
        showInstructionAction.setActionType(SHOW_DIAGNOSTIC_INSTRUCTIONS_MODAL);
        return showInstructionAction;
    }

    public Action getShowDiagnosticInstructionsAction(String productCodesCsv) {
        Action showInstructionAction = new Action();
        showInstructionAction.setActionType(SHOW_DIAGNOSTIC_INSTRUCTIONS_MODAL);
        if (productCodesCsv != null && !productCodesCsv.isEmpty()) {
            Map<String, Object> metadata = new HashMap<>();
            Map<String, Object> bookingParams = new HashMap<>();
            bookingParams.put("productCodes", productCodesCsv);
            metadata.put("bookingParams", bookingParams);
            showInstructionAction.setMeta(metadata);
        }
        return showInstructionAction;
    }

    public Action getDiagnosticRescheduleAction(com.sugarfit.housemd.pojo.order.OrderResponse diagnosticBooking) {
        String addressIdV1 = diagnosticBooking.getOrderEntry().getAddress().getAddressId();
        String parentBookingId = diagnosticBooking.getOrderEntry().getBookingId().toString();
        String productId = diagnosticBooking.getOrderEntry().getProductCodes();
        String actionUrl = String.format(
                "curefit://selectCareDateV1?productId=%s&parentBookingId=%s&type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&addressIdV1=%s&addressId=%s&diagnosticsProvider=EKINCARE&productCodes=%s",
                productId, parentBookingId, addressIdV1, addressIdV1, productId);
        Action atHomeAction = new Action();
        atHomeAction.setTitle("Reschedule");
        atHomeAction.setSubTitle("Please reschedule your appointment");
        atHomeAction.setActionType(ActionType.NAVIGATION);
        atHomeAction.setUrl(actionUrl);
        atHomeAction.setIsEnabled(true);
        return atHomeAction;
    }

    public DropdownAction getRescheduleDropdownAction(BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        String addressIdV1 = diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getAtHomeDiagnosticOrder()
                .getAddressMetadata().getAddressId();
        String parentBookingId = diagnosticBooking.getBooking().getId().toString();
        String patientId = diagnosticBooking.getBooking().getPatientId().toString();
        String productId = diagnosticBooking.getBooking().getProductCode();
        String actionUrl = String.format(
                "curefit://selectCareDateV1?patientId=%s&productId=%s&parentBookingId=%s&type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&addressIdV1=%s",
                patientId, productId, parentBookingId, addressIdV1);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getProductCodes())){
            AtomicReference<String> productCodesCsv = new AtomicReference<>("");
            diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getProductCodes().forEach(code -> {
                        productCodesCsv.set(productCodesCsv + (!productCodesCsv.get().isEmpty() ? "," : "") + code);
                    }
            );
            actionUrl += "&productCodes=" + productCodesCsv.get();
        }
        Action atHomeAction = new Action();
        atHomeAction.setTitle("Reschedule");
        atHomeAction.setActionType(ActionType.NAVIGATION);
        atHomeAction.setUrl(actionUrl);
        DropdownAction dropdownAction = new DropdownAction();
        dropdownAction.setValue(atHomeAction);
        dropdownAction.setText("Reschedule");
        return dropdownAction;
    }

    public DropdownAction getRescheduleDropdownActionV2(com.sugarfit.housemd.pojo.order.OrderResponse diagnosticBooking) {
        String addressIdV1 = diagnosticBooking.getOrderEntry().getAddress().getAddressId();
        String parentBookingId = diagnosticBooking.getOrderEntry().getBookingId().toString();
        String productId = diagnosticBooking.getOrderEntry().getProductCodes();
        String actionUrl = String.format(
                "curefit://selectCareDateV1?productId=%s&parentBookingId=%s&type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&addressIdV1=%s&addressId=%s&diagnosticsProvider=EKINCARE&productCodes=%s",
                productId, parentBookingId, addressIdV1, addressIdV1, productId);
        Action atHomeAction = new Action();
        atHomeAction.setTitle("Reschedule");
        atHomeAction.setActionType(ActionType.NAVIGATION);
        atHomeAction.setUrl(actionUrl);
        DropdownAction dropdownAction = new DropdownAction();
        dropdownAction.setValue(atHomeAction);
        dropdownAction.setText("Reschedule");
        return dropdownAction;
    }

    public Action getEmailReportAction(BookingInfo<DiagnosticStateInfo> diagnosticBooking) {
        Action emailAction = new Action();
        emailAction.setActionType(ActionType.SF_EMAIL_LABREPORTS);
        Map<String, String> emailActionMeta = new HashMap<>();
        emailActionMeta.put("carefitOrderId",
                String.valueOf(diagnosticBooking.getDiagnosticsTestOrderResponse().get(0).getBookingId()));
        emailAction.setMeta(emailActionMeta);
        return emailAction;
    }

    public Action getEmailReportActionV2(com.sugarfit.housemd.pojo.order.OrderResponse diagnosticBooking) {
        Action emailAction = new Action();
        emailAction.setActionType(ActionType.SF_EMAIL_LABREPORTS);
        Map<String, String> emailActionMeta = new HashMap<>();
        emailActionMeta.put("carefitOrderId", String.valueOf(diagnosticBooking.getOrderEntry().getBookingId()));
        emailAction.setMeta(emailActionMeta);
        return emailAction;
    }

    public CompletableFuture<ActiveGroupClassOrderResponse> getGroupClassBookingsFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                String userId = userContext.getUserProfile().getUserId();
                Calendar c = Calendar.getInstance(getUserTimezone(userContext));
                c.add(Calendar.DAY_OF_YEAR, 30);
                return serviceInterfaces.getSfAlbusClient()
                        .fetchActiveGroupClassOrders(userId, null, 0, 100);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching active consultation bookings", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<List<ActiveConsultationResponse>> getConsultationBookingsFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                String userId = userContext.getUserProfile().getUserId();
                return serviceInterfaces.sfAlbusClient.getActiveConsultations(userId, null);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching active consultation bookings", e);
                return new ArrayList<>();
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<List<ActiveCard>> getConsultationActiveCardsFuture(UserContext userContext, CompletableFuture<ActivePackResponse> activePackResponseFuture,
                                                                                CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture,
                                                                                CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                                                CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture,
                                                                                FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        return supplyAsync(() -> getConsultationActiveCards(userContext, activePackResponseFuture, consultationBookingsFuture, cgmOnboardingStatus, groupClassBookingsFuture, faceBasedVitalScansForDayResponse), serviceInterfaces.getTaskExecutor());
    }

    public List<ActiveCard> getConsultationActiveCards(UserContext userContext, CompletableFuture<ActivePackResponse> activePackResponseFuture,
                                                       CompletableFuture<List<ActiveConsultationResponse>> consultationBookingsFuture,
                                                       CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                       CompletableFuture<ActiveGroupClassOrderResponse> groupClassBookingsFuture,
                                                       FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        List<ActiveCard> consultationActiveCards = new ArrayList<>();
        try {
            String userId = userContext.getUserProfile().getUserId();
            UserEntry user = userServiceClient.getUser(userId).get(500, TimeUnit.MILLISECONDS);
            List<ActiveConsultationResponse> consultationBookings = consultationBookingsFuture.get();
            ActivePackResponse activePackResponse = activePackResponseFuture.get();
            ActiveGroupClassOrderResponse activeGroupClassOrderResponse = null;
            if (groupClassBookingsFuture != null && groupClassBookingsFuture.get() != null) {
                activeGroupClassOrderResponse = groupClassBookingsFuture.get();
            }

            consultationBookings.forEach((consultation) -> {
                try {
                    if (Objects.nonNull(consultation.getProductCode()) && isPhleboConsultation(consultation.getProductCode())) {
                        // Video Consultation - CGM Self Installation
                        boolean isOnBlucon = false;
                        if (Objects.nonNull(cgmOnboardingStatus) && Objects.nonNull(cgmOnboardingStatus.getBluconOnboardingStatusResponse())) {
                            isOnBlucon = cgmOnboardingStatus.getBluconOnboardingStatusResponse().isCurrentlyOnBlucon();
                        }
                        consultationActiveCards.add(getCGMSelfInstallationConsultationActiveCard(userContext, consultation, user, isOnBlucon, cgmOnboardingStatus));
                    } else {
                        // Video Consultation - Doctor/Coach
                        consultationActiveCards.add(getConsultationActiveCardV2(userContext, activePackResponse, consultation, user, cgmOnboardingStatus, faceBasedVitalScansForDayResponse));
                    }
                } catch (Exception e) {
                    exceptionReportingService.reportException("Error while fetching Consultation Active card", e);
                }
            });
            if (activeGroupClassOrderResponse != null && !CollectionUtils.isEmpty(activeGroupClassOrderResponse.getGroupClassOrderEntryList())) {
                Map<String, GroupClassSellableProductEntry> productEntryHashMap = activeGroupClassOrderResponse
                        .getGroupClassSellableProductEntryList().stream().collect(Collectors.toMap(GroupClassSellableProductEntry::getProductCode, item -> item));
                activeGroupClassOrderResponse.getGroupClassOrderEntryList().forEach(booking -> {
                    if (booking != null) {
                        CenterResponseV2 center = null;
                        try {
                            center = this.serviceInterfaces.ollivanderCenterClient.getCenterV3(booking.getCenterId());
                        } catch (Exception e) {
                            log.error(e.getLocalizedMessage());
                        }

                        Calendar today = Calendar.getInstance(getUserTimezone(userContext));
                        Calendar endDateCal = Calendar.getInstance(getUserTimezone(userContext));
                        endDateCal.setTime(booking.getEndTime());
                        if (endDateCal.get(Calendar.DAY_OF_YEAR) >= today.get(Calendar.DAY_OF_YEAR)) {
                            ActiveCard groupClassActiveCard = getGroupClassActiveCard(userContext, booking, productEntryHashMap.get(booking.getOrderProductCodes()), center);
                            if (groupClassActiveCard != null) {
                                consultationActiveCards.add(groupClassActiveCard);
                            }
                        }
                    }
                });
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return consultationActiveCards;
    }

    public static List<SfWellnessTherapyProduct> getAllWellnessAtCenterProducts(ServiceInterfaces serviceInterfaces, UserContext userContext) {
        List<SfWellnessTherapyProduct> therapyProducts = new ArrayList<>();
        try {
            List<GroupClassSellableProductEntry> groupClassSellableProductEntries = serviceInterfaces.getSfAlbusClient()
                    .groupClassProductSearch(null, true, userContext.getUserProfile().getUserId(), List.of(WELLNESS_CENTER_GROUP_THERAPY_PRODUCT));
            List<ConsultationProduct> oneOnOneTherapyProducts = serviceInterfaces.getSfAlbusClient().getAllWellnessProductCodes();
            if (!CollectionUtils.isEmpty(groupClassSellableProductEntries)) {
                for (GroupClassSellableProductEntry groupClass : groupClassSellableProductEntries) {
                    log.info("Group Class :: {}", groupClass);
                    if (groupClass != null) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        SfWellnessTherapyProduct therapyProduct = objectMapper.treeToValue(groupClass.getContentAssets(), SfWellnessTherapyProduct.class);
                        therapyProduct.setProductCode(groupClass.getProductCode());
                        therapyProduct.setImages(List.of(groupClass.getHeroImageUrl()));
                        therapyProduct.setThumbnailImages(List.of(groupClass.getImageUrl()));
                        therapyProduct.setIcon(groupClass.getImageUrl());
                        therapyProduct.setGroupClass(true);
                        therapyProduct.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfwellnessatcenterpdp?productCode=" + groupClass.getProductCode()).build());
                        therapyProducts.add(therapyProduct);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(oneOnOneTherapyProducts)) {
                oneOnOneTherapyProducts.forEach(tp -> {
                    if (tp != null) {
                        SfWellnessTherapyProduct therapyProduct = new SfWellnessTherapyProduct();
                        therapyProduct.setTitle(tp.getName());
                        therapyProduct.setProductCode(tp.getProductCode());
                        therapyProduct.setImages(List.of(tp.getHeroImageUrl()));
                        therapyProduct.setThumbnailImages(List.of(tp.getImageUrl()));
                        therapyProduct.setAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfwellnessatcenterpdp?productCode=" + tp.getProductCode()).build());
                        therapyProducts.add(therapyProduct);
                    }
                });
            }
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
        }
        return therapyProducts;
    }

    public boolean isWellnessCenterTherapyProduct(String productCode) {
        try {
//            List<String> wellnessTherapyProductCodes = serviceInterfaces.getSfAlbusClient().getAllWellnessProductCodes();
            Set<String> wellnessTherapyProductCodes = WELLNESS_CENTER_THERAPY_PRODUCTS;
            if (!CollectionUtils.isEmpty(wellnessTherapyProductCodes)) {
                return wellnessTherapyProductCodes.contains(productCode);
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    };

    public ActiveCard getGroupClassActiveCard(UserContext userContext,
                                              GroupClassOrderEntry booking, GroupClassSellableProductEntry product, CenterResponseV2 center) {
        ActiveCard card = new ActiveCard();
        if (product != null) {
            card.setTitle(product.getProductName());
        }

        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("dd MMM yy, h:mm");
        SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
        Date startDate = booking.getStartTime();
        Date endDate = booking.getEndTime();
        String formattedDateStart = dateFormatStartTime.format(startDate);
        String formattedDateEnd = dateFormatEndTime.format(endDate);
        String slotTimeText = formattedDateStart + " - " + formattedDateEnd.replace("am", "AM")
                .replace("pm", "PM");
        boolean isCompleted = Calendar.getInstance(getUserTimezone(userContext)).getTime().getTime() > endDate.getTime();
        boolean isOngoing = Calendar.getInstance(getUserTimezone(userContext)).getTime().getTime() > startDate.getTime() &&
                Calendar.getInstance(getUserTimezone(userContext)).getTime().getTime() <= endDate.getTime();
        boolean isFutureClass = !isOngoing && !isCompleted;

        card.setType("CONSULTATION");

        ActiveCardStatus activeCardStatus = new ActiveCardStatus();
        card.setStatus(activeCardStatus);
        List<DropdownAction> actions = new ArrayList<>();
        if (isCompleted) {
            activeCardStatus.setColor("#1aa9a4");
            activeCardStatus.setTitle(COMPLETED);
            card.setInfoTitle("MORE INFO");
            card.setInstruction("Thank you for attending the session.");
        } else if (isOngoing) {
            activeCardStatus.setColor("#f7ba41");
            activeCardStatus.setTitle(STARTED);
            card.setInfoTitle("MORE INFO");
            card.setInstruction("You class has started");
            if (center != null && center.getAddress() != null && StringUtils.hasText(center.getAddress().getPlaceUrl())) {
                card.setPrimaryAction(Action.builder().title("View Location").actionType(ActionType.OPEN_MAP).url(center.getAddress().getPlaceUrl()).build());
            }
        } else {
            activeCardStatus.setColor("#f7ba41");
            activeCardStatus.setTitle(UPCOMING);
            card.setInfoTitle("MORE INFO");
            card.setInstruction("Please reach the venue 5 mins earlier to the session time");
            if (center != null && center.getAddress() != null && StringUtils.hasText(center.getAddress().getPlaceUrl())) {
                card.setPrimaryAction(Action.builder().title("View Location").isEnabled(true).actionType(ActionType.OPEN_MAP).url(center.getAddress().getPlaceUrl()).build());
            }
        }

        if (isFutureClass) {
            DropdownAction cancelDropdownAction = new DropdownAction();
            cancelDropdownAction.setText("Cancel Booking");
            Action cancelAction = new Action();
            cancelAction.setActionType(ActionType.CANCEL_TC);
            CancelActionMeta cancelActionMeta = new CancelActionMeta();
            cancelActionMeta.setTcBookingId(booking.getBookingId());
            cancelAction.setMeta(cancelActionMeta);

            cancelDropdownAction.setValue(cancelAction);
            actions.add(cancelDropdownAction);
        }
        if (isCsTicketSupportedVersion(userContext)) {
            DropdownAction dropdownAction = new DropdownAction();
            dropdownAction.setText("Contact helpdesk");
            Action action = Action.builder().actionType(ActionType.NAVIGATION)
                    .url("curefit://sfsupportpage").title("Contact helpdesk").build();
            dropdownAction.setValue(action);
            actions.add(dropdownAction);
        }
        card.setSecondaryActions(actions);
        ActiveCardDetails details = new ActiveCardDetails();
        if (product != null) {
            details.setTitle(product.getProductName());
            details.setIconImageUrl(product.getHeroImageUrl());
        }
        details.setSubTitle(slotTimeText);
        card.setStartDate(startDate);
        card.setEndDate(endDate);
        card.setBookingDetails(details);

        boolean primaryActionEnabled = Objects.nonNull(card.getPrimaryAction()) && Boolean.TRUE.equals(card.getPrimaryAction().getIsEnabled());
        boolean primaryActionDisabled = Objects.nonNull(card.getPrimaryAction()) && Objects.nonNull(card.getPrimaryAction().getDisabled()) && Boolean.TRUE.equals(card.getPrimaryAction().getDisabled());
        if (primaryActionEnabled && !primaryActionDisabled) {
            card.setDefaultExpanded(true);
        }
        card.setViewMoreTitle("INSTRUCTIONS TO BE FOLLOWED");

        return card;
    }


    public ActiveCard getConsultationActiveCardV2(UserContext userContext, ActivePackResponse activePackResponse,
                                                  ActiveConsultationResponse consultation, UserEntry user,
                                                  CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                  FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        DoctorResponse doctor = serviceInterfaces.sfAlbusClient.searchDoctorById(consultation.getDoctorId()).get(0);
        ActiveCard card = new ActiveCard();
        boolean isDoctorConsult = consultation.getConsultationProduct().getHasPrescription();
        boolean isWellnessCenterTherapyProduct = isWellnessCenterTherapyProduct(consultation.getProductCode());
        if (isDoctorConsult) {
            card.setTitle("Doctor");
            card.setSubType("DOCTOR");
        } else if (isWellnessCenterTherapyProduct) {
            card.setTitle("Wellness Therapy");
            card.setSubType("THERAPY");
        } else {
            card.setTitle("Diabetes Expert");
            card.setSubType("COACH");
        }
        card.setType("CONSULTATION");
        ActiveCardStatus.StatusType status = getConsultationStatus(consultation);
        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(status);
        cardStatus.setColor(getConsultationStatusColor(status));
        card.setStatus(cardStatus);
        List<DropdownAction> actions = new ArrayList<>();
        if (status.equals(UPCOMING) || status.equals(STARTED)) {
            if (isWellnessCenterTherapyProduct) {
                card.setInfoTitle("MORE INFO");
                card.setInstruction("Please reach the venue 5 mins prior to the session time");
            } else {
                Action joinAction = this.getJoinCallAction(consultation, doctor, user, false);
                card.setPrimaryAction(joinAction);
                if (!isDoctorConsult && isFaceBasedVitalsEnabledForUser(userContext, cgmOnboardingStatusResponse, activePackResponse, faceBasedVitalScansForDayResponse)) {
                    setFaceBasedVitalScanData(card, userContext, consultation, joinAction);
                } else {
                    if (!joinAction.getIsEnabled() && isFirstDoctorConsultationBooked(activePackResponse, exceptionReportingService)) {
                        card.setInfoTitle("UPLOAD PRESCRIPTION");
                        card.setInstruction("Upload any outside diagnostic report or external prescriptions for doctor's reference");
                        card.setTertiaryAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfreportspage").pageFrom("chroniccarehomepage").build());
                        card.setTertiaryActionBackground("image/chroniccare/generic_banner.png");
                        card.setDarkTertiaryBackground(true);

                        if (card.getPrimaryAction().getIsEnabled()) {
                            card.setShowInfoWithCta(true);
                            card.getPrimaryAction().setSubTitle(null);
                        }
                    } else {
                        card.setInfoTitle("MORE INFO");
                        card.setInstruction("Join the call 5 mins before the scheduled time and ensure your audio, video are working properly");
                        card.setPrimaryActionEnableAt(consultation.getStartDate());
                    }
                }
            }
        } else if (status.equals(MISSED)) {
            card.setPrimaryAction(this.getRescheduleCallAction(consultation,
                    consultation.getConsultationProduct().getHasPrescription(), false));
        } else if (status.equals(COMPLETED)) {
            if (isDoctorConsult) {
                card.setInfoTitle("EMAIL PRESCRIPTION");
                card.setTertiaryAction(getEmailPrescriptionAction(consultation));
            } else {
                card.setInfoTitle("MORE INFO");
            }
            card.setInstruction(isWellnessCenterTherapyProduct ? "Thank you for attending the session." : consultation.getConsultationProduct().getHasPrescription()
                    ? "Your prescription has been sent to your registered email address/Whatsapp."
                    : "Your nutrition, fitness and intervention plan has been updated on the app.");
        }
        if (consultation.getAppointmentActionsWithContext().getCancelActionWithContext().getAction()
                .isActionPermitted()) {
            DropdownAction cancelDropdownAction = new DropdownAction();
            cancelDropdownAction.setText("Cancel Booking");
            Action cancelAction = new Action();

            if (isCGMDplusIEnabled(userContext) && isFirstCoachConsultationBooked(activePackResponse, exceptionReportingService)) {
                // Welcome Call
                Action wcCancelAction = getConsultCancellationAction(consultation, "Cancel Booking");
                Action wcRescheduleAction = getConsultRescheduleAction(consultation, "Reschedule");

                // Dropdown consultation cancel action
                cancelAction = getWelcomeCallCancellationAction("Cancel Booking", wcRescheduleAction, wcCancelAction);
            } else {
                cancelAction.setActionType(ActionType.CANCEL_TC);
                CancelActionMeta cancelActionMeta = new CancelActionMeta();
                cancelActionMeta.setTcBookingId(consultation.getBookingId());
                cancelAction.setMeta(cancelActionMeta);
            }

            cancelDropdownAction.setValue(cancelAction);
            actions.add(cancelDropdownAction);
        }
        if (consultation.getAppointmentActionsWithContext().getRescheduleActionWithContext().getAction()
                .isActionPermitted() && isRescheduleConsultationSupported(userContext)) {
            actions.add(getConsultationRescheduleAction(consultation, false));
        }
        if (isCsTicketSupportedVersion(userContext)) {
            DropdownAction dropdownAction = new DropdownAction();
            dropdownAction.setText("Contact helpdesk");
            Action action = Action.builder().actionType(ActionType.NAVIGATION)
                    .url("curefit://sfsupportpage").title("Contact helpdesk").build();
            dropdownAction.setValue(action);
            actions.add(dropdownAction);
        }
        card.setSecondaryActions(actions);
        ActiveCardDetails details = new ActiveCardDetails();
        if (isWellnessCenterTherapyProduct
                && consultation.getConsultationProduct() != null
                && consultation.getConsultationProduct().getName() != null) {
            details.setTitle(consultation.getConsultationProduct().getName());
            details.setIconImageUrl(consultation.getConsultationProduct().getImageUrl());
        } else {
            details.setTitle("Call with " + doctor.getName());
            details.setIconImageUrl(doctor.getDisplayImage());
        }
        Date startTime = new Date(consultation.getStartDate());
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, h:mm aa");
        dateFormat.setTimeZone(getUserTimezone(userContext));
        String formattedDate = dateFormat.format(startTime).replace("am", "AM").replace("pm", "PM");
        details.setSubTitle(formattedDate);
        card.setStartDate(new Date(consultation.getStartDate()));
        card.setEndDate(new Date(consultation.getStartDate() + consultation.getConsultationProduct().getDuration()));
        card.setBookingDetails(details);
        log.debug(String.format("For user :: %s, consultation :: %s, created card :: %s", user.getId(), consultation.getAppointmentId(), card));

        if (Objects.nonNull(card.getPrimaryAction())) {
            card.setDefaultExpanded(true);
        }
        card.setViewMoreTitle("INSTRUCTIONS TO BE FOLLOWED");

        return card;
    }

    public ActiveCard getCGMSelfInstallationConsultationActiveCard(UserContext userContext, ActiveConsultationResponse consultation, UserEntry user, boolean isOnBlucon, CgmOnboardingStatusResponse cgmOnboardingStatusResponse) {
        DoctorResponse phlebo = serviceInterfaces.sfAlbusClient.searchDoctorById(consultation.getDoctorId()).get(0);
        log.info(String.format("For user :: %s, consultation id :: %s, doctor Id :: %s, got doctor :: %s",
                user.getId(), consultation.getAppointmentId(), consultation.getDoctorId(), phlebo.getName()));
        ActiveCard card = new ActiveCard();
        card.setTitle("CGM Video Installation");
        card.setType("CONSULTATION");
        ActiveCardStatus.StatusType status = getConsultationStatus(consultation);
        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(status);
        cardStatus.setColor(getConsultationStatusColor(status));
        card.setStatus(cardStatus);
        List<DropdownAction> actions = new ArrayList<>();
        if (Objects.nonNull(cgmOnboardingStatusResponse)) {
            List<DeviceModel> availableCgmDevices = cgmOnboardingStatusResponse.getPendingDevices();
            if (availableCgmDevices != null && !availableCgmDevices.isEmpty()) {
                Action watchVideoAction = getCgmWatchVideoAction(availableCgmDevices, true);
                card.setTertiaryAction(watchVideoAction);
            }
        }
        if (isOnBlucon) {
            card.setInstructionColor("#F05C00");
            TextStyle instructionStyle = new TextStyle();
            instructionStyle.setFontSize(13);
            instructionStyle.setFontFamily("Inter-Medium");
            card.setInstructionStyle(instructionStyle);
            SfCgmDeviceStatus deviceStatus = getCgmDeviceStatus(userContext, cgmOnboardingStatusResponse, null);
            if (deviceStatus != null && deviceStatus.getIsOnGoingDevice()) {
                card.setInstruction("Your CGM installation process is completed");
                instructionStyle.setColor("#F05C00");
            } else {
                card.setInstruction("Don't install the CGM now. CGM expert will guide you about the installation process on video call.");
                card.setInstructionImageUrl("/image/chroniccare/active_card_warning.png");
                instructionStyle.setColor("#5C5C78");
            }
        } else {
            TextStyle instructionStyle = new TextStyle();
            instructionStyle.setTextDecorationLine("underline");
            instructionStyle.setColor("#55359C");
            instructionStyle.setFontSize(14);
            instructionStyle.setFontFamily("Inter-Medium");
            card.setInstructionStyle(instructionStyle);
            card.setInstruction("Click here to watch Installation video for reference");
            card.setInstructionImageUrl("/image/chroniccare/active_card_play_btn.png");
        }

        ActiveCardDetails details = new ActiveCardDetails();
        Date startTime = new Date(consultation.getStartDate());
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, h:mm aa");
        dateFormat.setTimeZone(getUserTimezone(userContext));
        String formattedDate = dateFormat.format(startTime).replace("am", "AM").replace("pm", "PM");
        details.setTitle("CGM Video Installation");
        details.setSubTitle(formattedDate);
        details.setIconImageUrl("image/chroniccare/active_card_cgm_logo.png");

        if (status.equals(UPCOMING) || status.equals(STARTED)) {
//            CGM Installation Consultation -> Started or Upcoming
            Action joinAction = this.getJoinCallAction(consultation, phlebo, user, true);
            card.setPrimaryAction(joinAction);
            card.setPrimaryActionEnableAt(consultation.getStartDate());
        } else if (status.equals(MISSED)) {
//            CGM InstallationConsultation -> Missed
            card.setPrimaryAction(getRescheduleCallAction(consultation,
                    consultation.getConsultationProduct().getHasPrescription(), true));
        }

        if (consultation.getAppointmentActionsWithContext().getCancelActionWithContext().getAction()
                .isActionPermitted()) {
            DropdownAction cancelDropdownAction = new DropdownAction();
            cancelDropdownAction.setText("Cancel Booking");
            Action cancelAction = new Action();
            cancelAction.setActionType(ActionType.CANCEL_TC);
            CancelActionMeta cancelActionMeta = new CancelActionMeta();
            cancelActionMeta.setTcBookingId(consultation.getBookingId());
            cancelAction.setMeta(cancelActionMeta);
            cancelDropdownAction.setValue(cancelAction);
            actions.add(cancelDropdownAction);
        }
        if (consultation.getAppointmentActionsWithContext().getRescheduleActionWithContext().getAction()
                .isActionPermitted() && isRescheduleConsultationSupported(userContext)) {
            DropdownAction rescheduleAction = this.getConsultationRescheduleAction(consultation, true);
            actions.add(rescheduleAction);
        }
        if (isCsTicketSupportedVersion(userContext)) {
            DropdownAction dropdownAction = new DropdownAction();
            dropdownAction.setText("Contact helpdesk");
            dropdownAction.setValue(Action.builder().actionType(ActionType.NAVIGATION)
                    .url("curefit://sfsupportpage").title("Contact helpdesk").build());
            actions.add(dropdownAction);
        }
        card.setSecondaryActions(actions);
        card.setStartDate(new Date(consultation.getStartDate()));
        card.setEndDate(new Date(consultation.getStartDate() + consultation.getConsultationProduct().getDuration()));
        card.setBookingDetails(details);
        if (Objects.nonNull(card.getPrimaryAction()) && Objects.nonNull(card.getPrimaryAction().getIsEnabled()) && card.getPrimaryAction().getIsEnabled()) {
            card.setShowInfoWithCta(true);
        }
        log.debug(String.format("For user :: %s, consultation :: %s, created card :: %s", user.getId(), consultation.getAppointmentId(), card));

        if (Objects.nonNull(card.getPrimaryAction())) {
            card.setDefaultExpanded(true);
        }
        card.setViewMoreTitle("INSTRUCTIONS TO BE FOLLOWED");

        return card;
    }

    private Action getEmailPrescriptionAction(ActiveConsultationResponse consultation) {
        Action emailAction = new Action();
        emailAction.setActionType(ActionType.SF_EMAIL_PRESCRIPTION);
        Map<String, String> emailActionMeta = new HashMap<>();
        emailActionMeta.put("bookingId", consultation.getBookingId().toString());
        emailActionMeta.put("consultationId", consultation.getAppointmentId().toString());
        emailAction.setMeta(emailActionMeta);
        return emailAction;
    }

    private void setFaceBasedVitalScanData(ActiveCard card, UserContext userContext,
                                           ActiveConsultationResponse consultation, Action joinAction) {
        try {
            Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
            Date currentTime = calendar.getTime();
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone userTimeZone = TimeZone.getTimeZone(userContext.getUserProfile().getTimezone());
            FaceBasedVitalLogsResponse fbvResponse = chsClient.getLastScanFaceBasedVitalLogs(userId, userTimeZone);

            // show scan if user has never scanned or not scanned in last two hour before consultation
            if (currentTime.after(new Date(consultation.getStartDate() - 60 * 60 * 1000)) && (fbvResponse == null
                    || fbvResponse.getTimeStamp().before(new Date(consultation.getStartDate() - 2 * 60 * 60 * 1000)))) {
                card.setInfoTitle("GET YOUR VITALS");
                card.setPrimaryActionInstruction(
                        !joinAction.getIsEnabled() ? "Link to join will be shared 30 mins before the call." : null);
                card.setInstruction("Scan your face and get health vitals for your consult");
                card.setTertiaryAction(new Action("curefit://sfscanfacepage", "SCAN", ActionType.NAVIGATION));
                card.setTertiaryActionBackground("image/chroniccare/face_scan_home_background.png");
                card.setDarkTertiaryBackground(true);

                if (card.getPrimaryAction().getIsEnabled()) {
                    card.setShowInfoWithCta(true);
                    card.getPrimaryAction().setSubTitle(null);
                }
            } else {
                card.setInfoTitle("MORE INFO");
                card.setInstruction("Join the call 5 mins before the scheduled time and ensure your audio, video are working properly");
                card.setPrimaryActionEnableAt(consultation.getStartDate());
            }
        } catch (Exception e) {
            card.setInfoTitle("MORE INFO");
            card.setInstruction("Join the call 5 mins before the scheduled time and ensure your audio, video are working properly");
            card.setPrimaryActionEnableAt(consultation.getStartDate());
        }
    }

    public boolean shouldShowFBVLockedWidget(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus) {
        try{
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone userTimeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            FaceBasedVitalLogsResponse lastScanResult = this.serviceInterfaces.chsClient.getLastScanFaceBasedVitalLogs(userId, userTimeZone);
            return cgmOnboardingStatus != null && cgmOnboardingStatus.isCgmOperational() && !cgmOnboardingStatus.isAtleastOneCGMCompleted() && lastScanResult == null;
        } catch (Exception e){
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public Action getJoinCallAction(ActiveConsultationResponse consultation, DoctorResponse doctor, UserEntry user, Boolean isPhleboConsultation) {
        AppointmentActionsWithContext appointmentActionsWithContext = consultation.getAppointmentActionsWithContext();
        Boolean isVideoCallEnabled = (appointmentActionsWithContext.getVideoActionWithContext() != null)
                && appointmentActionsWithContext.getVideoActionWithContext().getAction().isActionPermitted();

        if (isVideoCallEnabled) {
            Action action = new Action();
            action.setTitle("Join Call");
            if (!isPhleboConsultation) {
                if (consultation.isHasPrescription()) {
                    action.setSubTitle("Please join the call 2 mins before the start time");
                } else {
                    action.setSubTitle("In-case of NO-SHOW you won't be able to book any session for 3 days");
                }
            }
            String channel = appointmentActionsWithContext.getVideoActionWithContext().getContext()
                    .getTwilioCommunicationMode().getModeName();
            String modeSid = appointmentActionsWithContext.getVideoActionWithContext().getContext().getTwilioCommunicationMode().getModeSid();
            String modeName = appointmentActionsWithContext.getVideoActionWithContext().getContext().getTwilioCommunicationMode().getModeName();
            String chatChannel = null;
            action.setUrl("curefit://videochat?pageFrom=today&appointmentId=" + consultation.getAppointmentId()
                    + "&bookingId=" + consultation.getBookingId() + "&identity=Patient-" + consultation.getPatientId()
                    + "&docName=" + doctor.getName() + "&docImage=" + doctor.getDisplayImage() + "&userImage="
                    + user.getProfilePictureUrl() + "&doctorId=" + consultation.getDoctorId() + "&channel=" + channel + "&modeSid=" + modeSid + "&modeName=" + modeName
                    + "&chatChannel=" + chatChannel + "&enableLog=" + String.valueOf(true) + "&tenant=SUGARFIT");
            action.setActionType(ActionType.TC_JOIN_CALL);
            action.setIsEnabled(true);
            return action;
        } else {
            Action toastAction = getDisabledActionForToastMessage("You can join call 30 minutes before your slot time.", "JOIN CALL");
            toastAction.setIsEnabled(false);
            return toastAction;
        }
    }

    private Action getRescheduleCallAction(ActiveConsultationResponse consultation, Boolean isDoctor, Boolean isPhleboConsultation) {
        TeleconsultationManageOptionsMeta meta = new TeleconsultationManageOptionsMeta();
        meta.setTcBookingId(consultation.getBookingId());
        meta.setProductId(consultation.getProductCode());
        meta.setParentBookingId(consultation.getBookingId());
        meta.setPatientId(consultation.getPatientId());
        meta.setDoctorId(consultation.getDoctorId());
        Action rescheduleAction = new Action();
        rescheduleAction.setIcon(ActionIcon.RESCHEDULE);
        rescheduleAction.setTitle("Reschedule");
        if (isPhleboConsultation) {
            rescheduleAction.setSubTitle("Please reschedule your appointment for CGM Installation.");
        } else {
            rescheduleAction.setSubTitle(isDoctor ? "Please reschedule your appointment with your doctor."
                    : "Please reschedule your appointment with your coach.");
        }
        rescheduleAction.setActionId(ActionId.RESCHEDULE);
        rescheduleAction.setActionType(ActionType.RESCHEDULE_TC);
        rescheduleAction.setMeta(meta);
        rescheduleAction.setIsEnabled(true);
        return rescheduleAction;
    }

    public DropdownAction getConsultationRescheduleAction(ActiveConsultationResponse consultation, Boolean skipAgentId) {
        TeleconsultationManageOptionsMeta meta = new TeleconsultationManageOptionsMeta();
        meta.setTcBookingId(consultation.getBookingId());
        meta.setProductId(consultation.getProductCode());
        meta.setParentBookingId(consultation.getBookingId());
        meta.setPatientId(consultation.getPatientId());
        if (!skipAgentId) {
            meta.setDoctorId(consultation.getDoctorId());
        }
        Action rescheduleAction = new Action();
        rescheduleAction.setIcon(ActionIcon.RESCHEDULE);
        rescheduleAction.setTitle("Reschedule");
        rescheduleAction.setActionId(ActionId.RESCHEDULE);
        rescheduleAction.setActionType(ActionType.RESCHEDULE_TC);
        rescheduleAction.setMeta(meta);
        DropdownAction dropdownAction = new DropdownAction();
        dropdownAction.setText("Reschedule");
        dropdownAction.setValue(rescheduleAction);
        return dropdownAction;
    }

    public CompletableFuture<List<ActiveCard>> getLiveActiveCardsFuture(UserContext userContext) {
        return supplyAsync(() -> getLiveActiveCards(userContext), serviceInterfaces.getTaskExecutor());
    }

    public List<ActiveCard> getLiveActiveCards(UserContext userContext) {
        try {
            String userId = userContext.getUserProfile().getUserId();
            Tenant tenant = Tenant.fromString(AppUtil.getTenantFromUserContext(userContext).toString());
            String countryId = userContext.getUserProfile().getCity().getCountryId();
            DIYFilterRequestV2 diyFilterRequestV2 = DIYFilterRequestV2.builder()
                    .preferredStreamType(PreferredStreamType.VIDEO_CALL).build();
            List<LiveClass> liveClasses = serviceInterfaces.getDiyfsService().getUpcomingClassesByFilters(50, 0, tenant,
                    diyFilterRequestV2, userId, countryId).get();

            List<LiveClass> bookedLiveClasses = liveClasses.parallelStream().filter(
                            liveClass -> {
                                if (liveClass.getSlots().get(0).getSubscriptionStatus() == SubscriptionStatus.SUBSCRIBED) {
                                    boolean isMasterClass = false;
                                    if (!CollectionUtils.isEmpty(liveClass.getTags())) {
                                        if (liveClass.getTags().contains(MASTER_CLASS_TAG_PREFIX)) {
                                            isMasterClass = true;
                                        }
                                    }
                                    return !isMasterClass;
                                }
                                return false;
                            })
                    .toList();

            return bookedLiveClasses.parallelStream().map(liveClass -> {
                if (isCgmInstallationWebinar(liveClass)) {
                    return getUpcomingCgmWebinar(liveClass, userContext);
                }
                return getUpcomingSession(liveClass, userContext);
            }).filter(Objects::nonNull).toList();
        } catch (Exception e) {
            String msg = String.format("Error on fetching live classes for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
            return new ArrayList<>();
        }
    }

    public CompletableFuture<List<ActiveCard>> getLiveActiveCardsFuture(UserContext userContext, CompletableFuture<List<LiveClass>> liveClassFuture) {
        return supplyAsync(() -> getLiveActiveCards(userContext, liveClassFuture), serviceInterfaces.getTaskExecutor());
    }

    public List<ActiveCard> getLiveActiveCards(UserContext userContext, CompletableFuture<List<LiveClass>> liveClassFuture) {
        try {
            if (liveClassFuture == null) {
                return new ArrayList<>();
            }
            List<LiveClass> liveClasses = liveClassFuture.get();

            List<LiveClass> bookedLiveClasses = liveClasses.parallelStream().filter(
                            liveClass -> {
                                if (liveClass.getSlots().get(0).getSubscriptionStatus() == SubscriptionStatus.SUBSCRIBED) {
                                    boolean isMasterClass = false;
                                    if (!CollectionUtils.isEmpty(liveClass.getTags())) {
                                        if (liveClass.getTags().contains(MASTER_CLASS_TAG_PREFIX)) {
                                            isMasterClass = true;
                                        }
                                    }
                                    return !isMasterClass;
                                }
                                return false;
                            })
                    .toList();

            return bookedLiveClasses.parallelStream().map(liveClass -> {
                if (isCgmInstallationWebinar(liveClass)) {
                    return getUpcomingCgmWebinar(liveClass, userContext);
                }
                return getUpcomingSession(liveClass, userContext);
            }).filter(Objects::nonNull).toList();
        } catch (Exception e) {
            String msg = String.format("Error on fetching live classes for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
            return new ArrayList<>();
        }
    }

    public CompletableFuture<List<LiveClass>> getLiveClassesCompletableFuture(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        Tenant tenant = Tenant.fromString(AppUtil.getTenantFromUserContext(userContext).toString());
        String countryId = userContext.getUserProfile().getCity().getCountryId();
        DIYFilterRequestV2 diyFilterRequestV2 = DIYFilterRequestV2.builder()
                .preferredStreamType(PreferredStreamType.VIDEO_CALL).build();
        return serviceInterfaces.getDiyfsService().getUpcomingClassesByFilters(50, 0, tenant,
                diyFilterRequestV2, userId, countryId);
    }

    public List<ActiveCard> getCGMWebinarLiveActiveCards(UserContext userContext, CompletableFuture<List<LiveClass>> liveClassFuture) {
        try {
            List<LiveClass> liveClasses = liveClassFuture.get();

            List<LiveClass> bookedLiveClasses = liveClasses.parallelStream().filter(
                            liveClass -> {
                                if (isCgmInstallationWebinar(liveClass)) {
                                    if (liveClass.getSlots().get(0).getSubscriptionStatus() == SubscriptionStatus.SUBSCRIBED) {
                                        boolean isMasterClass = false;
                                        if (!CollectionUtils.isEmpty(liveClass.getTags())) {
                                            if (liveClass.getTags().contains(MASTER_CLASS_TAG_PREFIX)) {
                                                isMasterClass = true;
                                            }
                                        }
                                        return !isMasterClass;
                                    }
                                }
                                return false;
                            })
                    .toList();

            return bookedLiveClasses.parallelStream().map(liveClass -> getUpcomingCgmWebinar(liveClass, userContext)).filter(Objects::nonNull).toList();
        } catch (Exception e) {
            String msg = String.format("Error on fetching live classes for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
            return new ArrayList<>();
        }
    }

    private ActiveCard getUpcomingSession(LiveClass session, UserContext userContext) {
        Calendar calendar = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext));
        Date tomorrow = SfDateUtils.nextDay(calendar.getTime());
        if (session.getScheduledTimeEpoch() > tomorrow.getTime()) {
            return null;
        }

        ActiveCard liveClassCard = new ActiveCard();

        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(SCHEDULED);
        cardStatus.setColor("GREEN");

        String title = "Join Class";
        Action sessionAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(session.getLiveClassId(),
                session.getSlots().get(0).getClassId(), "now_live_widget", title, userContext,
                session.getContentCategory());
        Action joinAction = LiveUtil.getLiveInteractiveSessionRestApiAction(userContext, session.getLiveClassId());

        if (session.getScheduledTimeEpoch() <= calendar.getTime().getTime()) {
            joinAction.setIsEnabled(true);
            joinAction.setTitle(title);
            joinAction.setSubTitle("Keep track of your workout energy and postures by keeping your video on");
        } else {
            sessionAction.setIsEnabled(true);
        }

        ActiveCardDetails details = new ActiveCardDetails();
        details.setTitle(session.getTitle());

        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("MMM dd, h:mm");
        dateFormatStartTime.setTimeZone(getUserTimezone(userContext));
        long startDate = session.getScheduledTimeEpoch();
        String formattedDateStart = dateFormatStartTime.format(startDate);
        SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
        dateFormatEndTime.setTimeZone(getUserTimezone(userContext));
        long endTime = session.getScheduledTimeEpoch() + session.getDuration();
        String formattedDateEnd = dateFormatEndTime.format(endTime);
        String subTitle;
        if (startDate == endTime) {
            subTitle = formattedDateStart.replace("am", "AM").replace("pm", "PM");
        } else {
            subTitle = formattedDateStart + " - " + formattedDateEnd.replace("am", "AM").replace("pm", "PM");
        }
        details.setSubTitle(subTitle);

        liveClassCard.setPrimaryAction(joinAction);
        liveClassCard.setStatus(cardStatus);
        liveClassCard.setSubType("LIVE_CLASS");
        liveClassCard.setInfoTitle("MORE INFO");
        liveClassCard.setTertiaryAction(sessionAction);
        liveClassCard.setInstruction("Keep track of your workout energy and postures by keeping your video on");
        liveClassCard.setStartDate(new Date(session.getScheduledTimeEpoch()));
        liveClassCard.setSecondaryActions(getLiveClassDropDownActions(session, userContext));
        details.setIconImageUrl(LiveUtil.getImage(session.getBannerImages(), userContext.getSessionInfo(), 1L));
        liveClassCard.setBookingDetails(details);

        boolean primaryActionEnabled = Objects.nonNull(liveClassCard.getPrimaryAction()) && Boolean.TRUE.equals(liveClassCard.getPrimaryAction().getIsEnabled());
        boolean primaryActionDisabled = Objects.nonNull(liveClassCard.getPrimaryAction()) && Objects.nonNull(liveClassCard.getPrimaryAction().getDisabled()) && Boolean.TRUE.equals(liveClassCard.getPrimaryAction().getDisabled());
        if (primaryActionEnabled && !primaryActionDisabled) {
            liveClassCard.setDefaultExpanded(true);
        }
        liveClassCard.setViewMoreTitle("INSTRUCTIONS TO BE FOLLOWED");

        return liveClassCard;
    }

    public ActiveCard getUpcomingCgmWebinar(LiveClass session, UserContext userContext) {
        ActiveCard liveClassCard = new ActiveCard();

        ActiveCardStatus cardStatus = new ActiveCardStatus();
        cardStatus.setTitle(SCHEDULED);
        cardStatus.setColor("GREEN");

        String title = "View Details";
        Action sessionAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(session.getLiveClassId(),
                session.getSlots().get(0).getClassId(), "now_live_widget", title, userContext,
                session.getContentCategory());
        Action joinAction = LiveUtil.getLiveInteractiveSessionRestApiAction(userContext, session.getLiveClassId());
        Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));

        int liveWindowInMinute;
        int joinWindowFromEnd;
        try {
            HashMap<String, Map<String, String>> config = appConfigCache.getConfig("SF_UPCOMING_WEBINAR_CONFIG", new TypeReference<>() {
            }, new HashMap<>());
            liveWindowInMinute = Integer.parseInt(resolveNullable(() -> config.get("cgm_installation").get("live_window_in_minute")).orElse("60"));
            joinWindowFromEnd = Integer.parseInt(resolveNullable(() -> config.get("cgm_installation").get("join_window_from_end")).orElse("30"));
        } catch (Exception e) {
            liveWindowInMinute = 60;
            joinWindowFromEnd = 30;
        }
        long liveWindowInMillis = (long) liveWindowInMinute * 60 * 1000;
        long joinWindowInMillis = (long) joinWindowFromEnd * 60 * 1000;

        // Card visible till few minutes before end time
        if (calendar.getTimeInMillis() > (session.getScheduledTimeEpoch() + session.getDuration() - joinWindowInMillis)) {
            return null;
        }

        if (session.getScheduledTimeEpoch() <= calendar.getTimeInMillis() + liveWindowInMillis) {
            joinAction.setIsEnabled(true);
            joinAction.setTitle("Join Session");
        } else {
            sessionAction.setIsEnabled(true);
        }

        ActiveCardDetails details = new ActiveCardDetails();
        details.setTitle(session.getTitle());

        SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("MMM dd, h:mm");
        dateFormatStartTime.setTimeZone(getUserTimezone(userContext));
        long startDate = session.getScheduledTimeEpoch();
        String formattedDateStart = dateFormatStartTime.format(startDate);
        SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
        dateFormatEndTime.setTimeZone(getUserTimezone(userContext));
        long endTime = session.getScheduledTimeEpoch() + session.getDuration();
        String formattedDateEnd = dateFormatEndTime.format(endTime);
        String subTitle;
        if (startDate == endTime) {
            subTitle = formattedDateStart.replace("am", "AM").replace("pm", "PM");
        } else {
            subTitle = formattedDateStart + " - " + formattedDateEnd.replace("am", "AM").replace("pm", "PM");
        }
        details.setSubTitle(subTitle);

        liveClassCard.setPrimaryAction(joinAction);
        liveClassCard.setStatus(cardStatus);
        liveClassCard.setSubType("LIVE_CLASS");
        liveClassCard.setInfoTitle("MORE INFO");
        liveClassCard.setTertiaryAction(sessionAction);
        liveClassCard.setInstruction("This interactive session will guide you on CGM Installation");
        liveClassCard.setStartDate(new Date(session.getScheduledTimeEpoch()));
        liveClassCard.setSecondaryActions(getLiveClassDropDownActions(session, userContext));
        details.setIconImageUrl(LiveUtil.getImage(session.getBannerImages(), userContext.getSessionInfo(), 1L));
        liveClassCard.setBookingDetails(details);

//        boolean primaryActionEnabled = Objects.nonNull(liveClassCard.getPrimaryAction()) && Boolean.TRUE.equals(liveClassCard.getPrimaryAction().getIsEnabled());
//        boolean primaryActionDisabled = Objects.nonNull(liveClassCard.getPrimaryAction()) && Objects.nonNull(liveClassCard.getPrimaryAction().getDisabled()) && Boolean.TRUE.equals(liveClassCard.getPrimaryAction().getDisabled());
//        if (primaryActionEnabled && !primaryActionDisabled) {
//            liveClassCard.setDefaultExpanded(true);
//        }
        if (Objects.nonNull(liveClassCard.getPrimaryAction())) {
            liveClassCard.setDefaultExpanded(true);
        }
        liveClassCard.setViewMoreTitle("INSTRUCTIONS TO BE FOLLOWED");

        return liveClassCard;
    }

    private List<DropdownAction> getLiveClassDropDownActions(LiveClass session, UserContext userContext) {
        List<DropdownAction> actions = new ArrayList<>();
        DropdownAction cancelDropdownAction = new DropdownAction();
        cancelDropdownAction.setText("Cancel Booking");
        Action cancelAction = new Action();
        cancelAction.setActionType(ActionType.VIDEO_SUBSCRIBE);
        VideoSubscribeUnSubscribeActionMeta cancelActionMeta = new VideoSubscribeUnSubscribeActionMeta();
        cancelActionMeta.setSubscriptionType("VIDEO");
        cancelActionMeta.setStatus("UNSUBSCRIBED");
        cancelActionMeta.setCatalogueEntryId(session.getLiveClassId());
        cancelActionMeta.setSuccessMessage("Your class has been cancelled successfully");

        String startDate = TimeUtil.getTimeInFormatFromMillis(session.getScheduledTimeEpoch(),
                "yyyy-MM-dd'T'HH:mm:ss.SSSZ", userContext.getUserProfile().getTimezone());
        String endDate = TimeUtil.getTimeInFormatFromMillis(session.getScheduledTimeEpoch() + session.getDuration(),
                "yyyy-MM-dd'T'HH:mm:ss.SSSZ", userContext.getUserProfile().getTimezone());
        Action liveCalenderEventCancelAction = new Action();
        liveCalenderEventCancelAction.setPayload(
                new LiveCalenderEvenetCancelPayload(session.getLiveClassId(), session.getTitle(), startDate, endDate));
        liveCalenderEventCancelAction.setActionType(ActionType.DELETE_CALENDAR_EVENT);

        cancelAction.setMeta(cancelActionMeta);
        cancelDropdownAction.setValue(cancelAction);
        actions.add(cancelDropdownAction);

        return actions;
    }

    public CompletableFuture<List<ActiveCard>> getPhleboActiveCardsFuture(UserContext userContext) {
        return supplyAsync(() -> getPhleboActiveCards(userContext), serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<ActiveCard> getCgmDeliveryActiveCardFuture(UserContext userContext) {
        return supplyAsync(() -> getCgmDeliveryActiveCard(userContext), serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<List<ActiveCard>> getAudioCallUpcomingCardsFuture(UserContext userContext) {
        return supplyAsync(() -> getAudioCallUpcomingCards(userContext), serviceInterfaces.getTaskExecutor());
    }

    public List<ActiveCard> getAudioCallUpcomingCards(UserContext userContext) {
        List<ActiveCard> activeCardList = new ArrayList<>();
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            UpcomingAudioTaskSlotResponse upcomingAudioTaskSlotResponse = smsClient.getUpcomingAudioTask(userId);
            if (upcomingAudioTaskSlotResponse.isAudioCallScheduled()) {
                AppointmentAudioTaskMappingEntry audioTask = upcomingAudioTaskSlotResponse.getAppointmentAudioTaskMappingEntry();
                DoctorResponse coach = serviceInterfaces.sfAlbusClient.searchDoctorById(audioTask.getCoachId()).get(0);
                ActiveCard audioCallCard = new ActiveCard();

                ActiveCardStatus cardStatus = new ActiveCardStatus();
                cardStatus.setTitle(PHONE_CALL_SCHEDULED);
                cardStatus.setColor("GREEN");

                ActiveCardDetails details = new ActiveCardDetails();
                details.setTitle("Call with " + coach.getName());
                details.setIconImageUrl(COACH_AUDIO_CALL_ICON_URL);

                SimpleDateFormat dateFormatStartTime = new SimpleDateFormat("h:mm aa");
                dateFormatStartTime.setTimeZone(getUserTimezone(userContext));
                long startDate = audioTask.getStartTime().getTime();
                String formattedDateStart = dateFormatStartTime.format(startDate)
                        .replace("am", "AM").replace("pm", "PM");
                SimpleDateFormat dateFormatEndTime = new SimpleDateFormat("h:mm aa");
                dateFormatEndTime.setTimeZone(getUserTimezone(userContext));
                long endTime = audioTask.getEndTime().getTime();
                String formattedDateEnd = dateFormatEndTime.format(endTime)
                        .replace("am", "AM").replace("pm", "PM");
                String subTitle = "Between " + formattedDateStart + " - " + formattedDateEnd + ", Today";
                details.setSubTitle(subTitle);

                String instruction = "Your coach will call on your registered mobile number anytime between " +
                        formattedDateStart + " - " + formattedDateEnd + " today";

                audioCallCard.setStatus(cardStatus);
                audioCallCard.setSubType("COACH");
                audioCallCard.setInfoTitle("MORE INFO");
                audioCallCard.setInstruction(instruction);
                audioCallCard.setBookingDetails(details);
                audioCallCard.setStartDate(audioTask.getStartTime());
                audioCallCard.setEndDate(audioTask.getEndTime());

                boolean primaryActionEnabled = Objects.nonNull(audioCallCard.getPrimaryAction()) && Boolean.TRUE.equals(audioCallCard.getPrimaryAction().getIsEnabled());
                boolean primaryActionDisabled = Objects.nonNull(audioCallCard.getPrimaryAction()) && Objects.nonNull(audioCallCard.getPrimaryAction().getDisabled()) && Boolean.TRUE.equals(audioCallCard.getPrimaryAction().getDisabled());
                if (primaryActionEnabled && !primaryActionDisabled) {
                    audioCallCard.setDefaultExpanded(true);
                }
                audioCallCard.setViewMoreTitle("INSTRUCTIONS TO BE FOLLOWED");

                activeCardList.add(audioCallCard);
            }
        } catch (Exception e) {
            String msg = String.format("Error on fetching Audio call tasks for user :: %s",
                    userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
            return new ArrayList<>();
        }
        return activeCardList;
    }

    public List<ProductResponse> sortEcomProductsByOffer(List<ProductResponse> products) {
        List<ProductResponse> tempProducts = new ArrayList<>(products);
        tempProducts.sort((a, b) -> {
            double offerPercA = 0L;
            double offerPercB = 0L;
            if (a.getProductEntry().getListingPrice() < a.getProductEntry().getMrp()) {
                offerPercA = 100 - (a.getProductEntry().getListingPrice() / a.getProductEntry().getMrp() * 100);
                offerPercA = offerPercA < 1 ? Math.round(offerPercA * 100.0) / 100.0 : Math.round(offerPercA);
            }
            if (b.getProductEntry().getListingPrice() < b.getProductEntry().getMrp()) {
                offerPercB = 100 - (b.getProductEntry().getListingPrice() / b.getProductEntry().getMrp() * 100);
                offerPercB = offerPercB < 1 ? Math.round(offerPercB * 100.0) / 100.0 : Math.round(offerPercB);
            }
            return Double.compare(offerPercB, offerPercA);
        });
        return tempProducts;
    }

    public ProductResponse getEcomBaseProduct(List<ProductResponse> products) {
        if (CollectionUtils.isEmpty(products)) return null;

        List<ProductResponse> productsClone = new ArrayList<>(products);
        productsClone.sort(Comparator.comparingDouble(a -> a.getProductEntry().getListingPrice())); //Lowest on top
        return productsClone.get(0);
    }

    public List<ProductResponse> sortEcomProductsByOffer(List<ProductResponse> products, UserContext userContext) {
        List<ProductResponse> productsClone = new ArrayList<>(products);
        productsClone.sort((a, b) -> {
            // Check if products are in the recommended list
            boolean isARecommended = Arrays.asList(RECOMMENDED_PRODUCTS).contains(a.getProductEntry().getProductCode());
            boolean isBRecommended = Arrays.asList(RECOMMENDED_PRODUCTS).contains(b.getProductEntry().getProductCode());

            // Prioritize recommended products
            if (isARecommended && !isBRecommended) {
                return -1;
            } else if (!isARecommended && isBRecommended) {
                return 1;
            }

            // Calculate offer percentages
            double offerPercA = 0L;
            double offerPercB = 0L;
            if (a.getProductEntry().getListingPrice() < a.getProductEntry().getMrp()) {
                offerPercA = 100 - (a.getProductEntry().getListingPrice() / a.getProductEntry().getMrp() * 100);
                offerPercA = offerPercA < 1 ? Math.round(offerPercA * 100.0) / 100.0 : Math.round(offerPercA);
            }
            if (b.getProductEntry().getListingPrice() < b.getProductEntry().getMrp()) {
                offerPercB = 100 - (b.getProductEntry().getListingPrice() / b.getProductEntry().getMrp() * 100);
                offerPercB = offerPercB < 1 ? Math.round(offerPercB * 100.0) / 100.0 : Math.round(offerPercB);
            }

            // Sort by offer percentage if neither product is recommended
            return Double.compare(offerPercB, offerPercA);
        });

        return productsClone;
    }

    public String getServiceableUserAddress(String addressId) {
        String addressString = "";

        if (addressId != null) {
            UserDeliveryAddress address = getUserDeliveryAddress(addressId);
            if (address != null) {
                if (address.getAddressLine1() != null) {
                    addressString += address.getAddressLine1();
                    addressString += ", ";
                }
                if (address.getAddressLine2() != null) {
                    addressString += address.getAddressLine2();
                }
            }
        }

        return addressString;
    }

    public UserDeliveryAddress getUserDeliveryAddress(String addressId) {
        if (!StringUtils.isEmpty(addressId)) {
            try {
                return orderService.getAddressById(addressId);
            } catch (Exception ex) {
                log.error("Error while getting address info", ex);
            }
        }
        return null;
    }

    public ProductResponse getAttaWithHighestOffer(List<ProductResponse> products) {
        AtomicReference<ProductResponse> attaProductWithHighestOffer = new AtomicReference<>(null);
        AtomicReference<Double> attaHighestOfferPerc = new AtomicReference<>((double) 0L);
        products.forEach(productResponse -> {
            if (productResponse != null && productResponse.getProductEntry() != null && productResponse.getIsAvailable()) {
                if (E_COM_ATTA_PRODUCTS.contains(productResponse.getProductEntry().getProductCode())) {
                    if (Objects.isNull(attaProductWithHighestOffer.get())) {
                        attaProductWithHighestOffer.set(productResponse);
                    }
                    double offerPerc = 0L;
                    if (productResponse.getProductEntry().getListingPrice() < productResponse.getProductEntry().getMrp()) {
                        offerPerc = 100 - (productResponse.getProductEntry().getListingPrice() / productResponse.getProductEntry().getMrp() * 100);
                        offerPerc = offerPerc < 1 ? Math.round(offerPerc * 100.0) / 100.0 : Math.round(offerPerc);
                    }
                    if (offerPerc > attaHighestOfferPerc.get()) {
                        attaHighestOfferPerc.set(offerPerc);
                        attaProductWithHighestOffer.set(productResponse);
                    }
                }
            }
        });
        return attaProductWithHighestOffer.get();
    }

    public ProductResponse getRiceWithHighestOffer(List<ProductResponse> products) {
        AtomicReference<ProductResponse> riceProductWithHighestOffer = new AtomicReference<>(null);
        AtomicReference<Double> riceHighestOfferPerc = new AtomicReference<>((double) 0L);
        products.forEach(productResponse -> {
            if (productResponse != null && productResponse.getProductEntry() != null && productResponse.getIsAvailable()) {
                if (E_COM_RICE_PRODUCTS.contains(productResponse.getProductEntry().getProductCode())) {
                    if (Objects.isNull(riceProductWithHighestOffer.get())) {
                        riceProductWithHighestOffer.set(productResponse);
                    }
                    double offerPerc = 0L;
                    if (productResponse.getProductEntry().getListingPrice() < productResponse.getProductEntry().getMrp()) {
                        offerPerc = 100 - (productResponse.getProductEntry().getListingPrice() / productResponse.getProductEntry().getMrp() * 100);
                        offerPerc = offerPerc < 1 ? Math.round(offerPerc * 100.0) / 100.0 : Math.round(offerPerc);
                    }
                    if (offerPerc > riceHighestOfferPerc.get()) {
                        riceHighestOfferPerc.set(offerPerc);
                        riceProductWithHighestOffer.set(productResponse);
                    }
                }
            }
        });
        return riceProductWithHighestOffer.get();
    }

    public ProductResponse getAppleCiderVinegarWithHighestOffer(List<ProductResponse> products) {
        AtomicReference<ProductResponse> vinegarProductWithHighestOffer = new AtomicReference<>(null);
        AtomicReference<Double> vinegarHighestOfferPerc = new AtomicReference<>((double) 0L);
        products.forEach(productResponse -> {
            if (productResponse != null && productResponse.getProductEntry() != null && productResponse.getIsAvailable()) {
                if (E_COM_VINEGAR_PRODUCTS.contains(productResponse.getProductEntry().getProductCode())) {
                    if (Objects.isNull(vinegarProductWithHighestOffer.get())) {
                        vinegarProductWithHighestOffer.set(productResponse);
                    }
                    double offerPerc = 0L;
                    if (productResponse.getProductEntry().getListingPrice() < productResponse.getProductEntry().getMrp()) {
                        offerPerc = 100 - (productResponse.getProductEntry().getListingPrice() / productResponse.getProductEntry().getMrp() * 100);
                        offerPerc = offerPerc < 1 ? Math.round(offerPerc * 100.0) / 100.0 : Math.round(offerPerc);
                    }
                    if (offerPerc > vinegarHighestOfferPerc.get()) {
                        vinegarHighestOfferPerc.set(offerPerc);
                        vinegarProductWithHighestOffer.set(productResponse);
                    }
                }
            }
        });
        return vinegarProductWithHighestOffer.get();
    }

    public ProductResponse getSugarControlJuiceWithHighestOffer(List<ProductResponse> products) {
        AtomicReference<ProductResponse> productWithHighestOffer = new AtomicReference<>(null);
        AtomicReference<Double> productHighestOfferPerc = new AtomicReference<>((double) 0L);
        products.forEach(productResponse -> {
            if (productResponse != null && productResponse.getProductEntry() != null && productResponse.getIsAvailable()) {
                if (E_COM_JUICE_PRODUCTS.contains(productResponse.getProductEntry().getProductCode())) {
                    if (Objects.isNull(productWithHighestOffer.get())) {
                        productWithHighestOffer.set(productResponse);
                    }
                    double offerPerc = 0L;
                    if (productResponse.getProductEntry().getListingPrice() < productResponse.getProductEntry().getMrp()) {
                        offerPerc = 100 - (productResponse.getProductEntry().getListingPrice() / productResponse.getProductEntry().getMrp() * 100);
                        offerPerc = offerPerc < 1 ? Math.round(offerPerc * 100.0) / 100.0 : Math.round(offerPerc);
                    }
                    if (offerPerc > productHighestOfferPerc.get()) {
                        productHighestOfferPerc.set(offerPerc);
                        productWithHighestOffer.set(productResponse);
                    }
                }
            }
        });
        return productWithHighestOffer.get();
    }

    public boolean isAppUnderMaintenance() {
        try {
            SfAppMaintenanceConfig appMaintenanceConfig = new SfAppMaintenanceConfig();
            appMaintenanceConfig = serviceInterfaces.appConfigCache.getConfig("SF_APP_MAINTENANCE_BANNER_CONFIG", new TypeReference<>() {
            }, appMaintenanceConfig);
            return !Objects.isNull(appMaintenanceConfig) && appMaintenanceConfig.isEnabled();
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
        }
        return false;
    }

    public SfBannerCarouselWidget getMaintenanceBanner(boolean isFreemium) {
        try {
            SfAppMaintenanceConfig appMaintenanceConfig = new SfAppMaintenanceConfig();
            appMaintenanceConfig = serviceInterfaces.appConfigCache.getConfig("SF_APP_MAINTENANCE_BANNER_CONFIG", new TypeReference<>() {
            }, appMaintenanceConfig);
            if (!Objects.isNull(appMaintenanceConfig) && appMaintenanceConfig.isEnabled()) {
                SfBannerCarouselWidget carouselWidget = new SfBannerCarouselWidget();
                BannerItem bannerItem = new BannerItem();
                bannerItem.setImage(appMaintenanceConfig.getBannerUrl());
                carouselWidget.setData(List.of(bannerItem));
                carouselWidget.setLayoutProps(getMaintenanceBannerLayoutProps(isFreemium));
                return carouselWidget;
            }
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
        }
        return null;
    }

    @GetMapping
    private Map<String, Object> getMaintenanceBannerLayoutProps(boolean isFreemium) {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", true);
        layoutProps.put("bannerHeight", 304);
        layoutProps.put("bannerWidth", 325);
        layoutProps.put("bannerOriginalHeight", 304);
        layoutProps.put("bannerOriginalWidth", 304);
        layoutProps.put("verticalPadding", 20);
        layoutProps.put("showPagination", false);
        return layoutProps;
    }

    public SfCgmDeviceStatus getCgmDeviceStatus(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus, String cgmDeviceId) {
        SfCgmDeviceStatus deviceStatus = new SfCgmDeviceStatus(cgmDeviceId, false, false);
        boolean isSensorJustInitialized = false;
        boolean isTierTwoNonNfcWithSupportedApp = false;
        Boolean isOnGoingDevice = deviceStatus.getIsOnGoingDevice();
        boolean shouldShowScanCardInsideCGMGraph;
        List<CGMDeviceInfo> deviceInfos = cgmOnboardingStatus.getCgmDeviceInfos();
        if (cgmDeviceId == null || cgmDeviceId.isEmpty() || cgmDeviceId.equals(CGM_ACTIVATION_DUMMY_DEVICE_ID)) {
            cgmDeviceId = null;
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(deviceInfos)) {
                deviceInfos = deviceInfos.stream().filter(cgmDeviceInfo -> {
                    if ("AGM_ORDERED".equals(cgmDeviceInfo.getCurrentState()) || "AGM_DELIVERED".equals(cgmDeviceInfo.getCurrentState()) || null == cgmDeviceInfo.getStartedOn()) {
                        return false;
                    }
                    return cgmDeviceInfo.isAtleastOneReadingDone() || "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState());
                }).toList();

                if (deviceInfos.size() > 0) {
                    isSensorJustInitialized = true;
                    cgmDeviceId = deviceInfos.get(deviceInfos.size() - 1).getDeviceId();
                    isOnGoingDevice = deviceInfos.get(deviceInfos.size() - 1).isOngoing();
                }
            }
            if(isSfPhotoReaderSupportedUser(userContext,serviceInterfaces.getSfAlbusClient(), this.serviceInterfaces.chronicCareServiceHelper))
            {
                isTierTwoNonNfcWithSupportedApp = true;
            }
            shouldShowScanCardInsideCGMGraph = (!isTierTwoNonNfcWithSupportedApp && isSensorJustInitialized && cgmOnboardingStatus.isEnableFirstInAppReading()) || cgmActivationAllowed(userContext, cgmOnboardingStatus);
        } else {
            String finalCgmDeviceId = cgmDeviceId;
            CGMDeviceInfo deviceInfo = deviceInfos.stream().filter(cgmDeviceInfo -> "AGM_STARTED".equals(cgmDeviceInfo.getCurrentState()) && cgmDeviceInfo.getDeviceId().equals(finalCgmDeviceId)).findFirst().orElse(null);
            if (deviceInfo != null) {
                isSensorJustInitialized = true;
            }
            CGMDeviceInfo currentDevice = deviceInfos.stream().filter(cgmDeviceInfo -> cgmDeviceInfo.getDeviceId().equals(finalCgmDeviceId)).findFirst().orElse(null);
            if (currentDevice != null) {
                isOnGoingDevice = currentDevice.isOngoing();
            }
            if(isSfPhotoReaderSupportedUser(userContext,serviceInterfaces.getSfAlbusClient(), this.serviceInterfaces.chronicCareServiceHelper))
            {
                isTierTwoNonNfcWithSupportedApp = true;
            }
            shouldShowScanCardInsideCGMGraph = !isTierTwoNonNfcWithSupportedApp && isSensorJustInitialized && cgmOnboardingStatus.isEnableFirstInAppReading();
        }
        deviceStatus.setCgmDeviceId(cgmDeviceId);
        deviceStatus.setIsOnGoingDevice(isOnGoingDevice);
        deviceStatus.setShouldShowScanCardInsideCGMGraph(shouldShowScanCardInsideCGMGraph);
        return deviceStatus;
    }

    public SfHomePageView.CsTicketResolutionNotificationData getCsTicketResolutionNotification(UserContext userContext) {
        try {
            List<NotificationMeta> notificationMetaList = serviceInterfaces.inAppNotificationsService.getActiveUserAndAppId(userContext.getUserProfile().getUserId(),
                    "SUGARFIT");
            String creativeId = "TICKET-UPDATED-";
            Optional<NotificationMeta> csTicketResolutionDataOpt = notificationMetaList.stream().filter(notification ->
                    !Objects.isNull(notification.getTaskId()) && notification.getTaskId().contains(creativeId)).findFirst();
            if (csTicketResolutionDataOpt.isPresent()) {
                String ticketId = csTicketResolutionDataOpt.get().getTaskId().substring(creativeId.length());
                SupportConversationEntry supportConversationEntry = customerSupportClient.fetchSessionDetail(Long.valueOf(userContext.getUserProfile().getUserId()), Long.valueOf(ticketId));
                SfSupportTicket supportTicket = new SfSupportTicket();
                supportTicket.setTicketId(supportConversationEntry.getId());
                supportTicket.setConversationId(supportConversationEntry.getId());
                supportTicket.setFreshdeskTicketId(supportConversationEntry.getTicketId());
                supportTicket.setSubject(supportConversationEntry.getCategory());
                supportTicket.setDescription(supportConversationEntry.getDescriptionText());
                supportTicket.setLastUpdatedTime(supportConversationEntry.getLastModifiedOn());
                supportTicket.setCreatedTime(supportConversationEntry.getCreatedOn());
                supportTicket.setStatus(SfSupportTicket.getTicketStatus(supportConversationEntry.getStatus(), supportConversationEntry.getTicketId()));

                SfHomePageView.CsTicketResolutionNotificationData csTicketResolutionNotificationData = new SfHomePageView.CsTicketResolutionNotificationData();
                if (isCSRatingFeedbackSupportedApp(userContext)) {
                    csTicketResolutionNotificationData.setSubTitle("");
                }
                csTicketResolutionNotificationData.setTicket(supportTicket);
                Action viewedAction = new Action();
                viewedAction.setActionType(ActionType.REST_API);
                viewedAction.setTitle("GOTO TICKET");
                ActionMeta closeActionMeta = new ActionMeta();
                closeActionMeta.setBody(new SfNoShowPenaltyBuilder.CloseActionState());
                closeActionMeta.setMethod("POST");
                closeActionMeta.setUrl("/user/inAppNotification/" + csTicketResolutionDataOpt.get().getNotificationId());
                viewedAction.setMeta(closeActionMeta);
                csTicketResolutionNotificationData.setViewedAction(viewedAction);
                return csTicketResolutionNotificationData;
            }
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return null;
    }

    public MegaSaleData getMegaSaleDataForStripBanner(UserContext userContext,
                                                      CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                      ActivePackResponse activePackResponse) throws IOException, ExecutionException, InterruptedException, TimeoutException {
        CompletableFuture<MegaSaleData> megaSalesBannerDataFuture = this.getMegaSalesDataFuture(userContext, cgmOnboardingStatus, activePackResponse != null ? activePackResponse.getBundleProduct() : null, null, true);
        SfHomepageBannerConfig sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {
        }, new SfHomepageBannerConfig());
        MegaSaleData megaSaleData = megaSalesBannerDataFuture.get(WIDGET_LONG_TIMEOUT, TimeUnit.MILLISECONDS);

        if (Objects.nonNull(megaSaleData) && Objects.nonNull(sfHomepageBannerConfig) && Objects.nonNull(sfHomepageBannerConfig.getSaleConfigurations())) {
            megaSaleData.setSaleVideoUrl(sfHomepageBannerConfig.getSaleConfigurations().getSaleVideoUrlSmallStrip());
            megaSaleData.setSaleImageUrl(sfHomepageBannerConfig.getSaleConfigurations().getSaleImageUrlSmallStrip());
            megaSaleData.setSaleImageUrlV2(sfHomepageBannerConfig.getSaleConfigurations().getSaleImageUrlSmallStrip());
            megaSaleData.setSaleMediaAspectRatio(sfHomepageBannerConfig.getSaleConfigurations().getSaleMediaAspectRatioSmallStrip());
        }
        return megaSaleData;
    }

//    public boolean isMegaSaleRunning(UserContext userContext) {
//        try {
//            MegaSalesConfig megaSalesConfig = appConfigCache.getConfig("SF_MEGA_SALE_CONFIG", new TypeReference<>() {
//            }, new MegaSalesConfig());
//            if (megaSalesConfig != null
//                    && megaSalesConfig.isSaleLive()) {
//                Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
//                if (calendar.getTime().getTime() > megaSalesConfig.getSaleAutoStartEpoch()
//                        && calendar.getTime().getTime() < megaSalesConfig.getSaleAutoEndEpoch()) {
//                    return true;
//                }
//            }
//        } catch (Exception e) {
//            exceptionReportingService.reportException(e);
//        }
//        return false;
//    }

    SfBannerCarouselWidget getOnboardingWalkthroughBannerWidget(UserContext userContext) throws IOException {
            BannerItem bannerItem = new BannerItem();
            bannerItem.setImage("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/onboarding_banner-2025-02-06-17:07.png");
            bannerItem.setAction(Action.builder().url("curefit://onboardingwalkthrough").actionType(ActionType.NAVIGATION).build());
            SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
            sfBannerCarouselWidget.setData(Collections.singletonList(bannerItem));
            Map<String, Object> layoutProps = new HashMap<>();
            layoutProps.put("roundedCorners", true);
            layoutProps.put("bannerHeight", 140);
            layoutProps.put("bannerWidth", 315);
            layoutProps.put("bannerOriginalHeight", 140);
            layoutProps.put("bannerOriginalWidth", 315);
            layoutProps.put("verticalPadding", 0);
            layoutProps.put("showPagination", false);
            layoutProps.put("autoScroll", false);
            layoutProps.put("backgroundColor", "#FFFFFF");
            sfBannerCarouselWidget.setLayoutProps(layoutProps);
            return sfBannerCarouselWidget;
    }

    public CompletableFuture<MegaSaleData> getMegaSalesDataFuture(UserContext userContext,
                                                                  CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                                  BundleProduct bundleProduct, ActivePackResponse wellnessActivePackResponse, Boolean skipCalloutBanners) {
        return supplyAsync(() -> getMegaSalesDataWithSalesBannersV2(userContext, cgmOnboardingStatusResponse, bundleProduct, wellnessActivePackResponse, skipCalloutBanners), serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<SfBannerCarouselWidget> getOnboardingWalkthroughBannerWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getOnboardingWalkthroughBannerWidget(userContext);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public static boolean isOnboardingWalkthroughBannerEnabledUser(ActivePackResponse activePackResponse, UserContext userContext,
                                                                   ChronicCareServiceHelper chronicCareServiceHelper) {
        if(isOnboardingWalkthroughBannerSupportedApp(userContext)
                && !chronicCareServiceHelper.isSpecialSugarControlPlanPack(activePackResponse.getBundleProduct())
                && !chronicCareServiceHelper.isQuickCommerceCGMPack(activePackResponse.getBundleProduct())
                && !chronicCareServiceHelper.isShopifyCGMPack(activePackResponse.getBundleProduct())){
            Date fromLocalDate = new Date(activePackResponse.getStartDate());
            Date today = new Date();
            long daysBetween = SfDateUtils.getDifferenceDays(fromLocalDate, today);
            boolean isRenewal = false;
            if (activePackResponse.getBundleProduct() != null && activePackResponse.getBundleProduct().getProductSpecs() != null && activePackResponse.getBundleProduct().getProductSpecs().get("isRenewal") != null) {
                isRenewal = activePackResponse.getBundleProduct().getProductSpecs().get("isRenewal").asBoolean(false);
            }
            return daysBetween <= 15 && !isRenewal;
        }
        return false;
    }

//    @Deprecated
//    public MegaSaleData getMegaSalesData(UserContext userContext) {
//        if (AppUtil.isSugarFitApp(userContext) && isSalesBannerCarouselEnabled(userContext, segmentationCacheClient) && !isInternationalSugarfitUser(userContext)) {
//            try {
//                MegaSalesConfig megaSalesConfig = appConfigCache.getConfig("SF_MEGA_SALE_CONFIG", new TypeReference<>() {
//                }, new MegaSalesConfig());
//                if (megaSalesConfig != null
//                        && megaSalesConfig.isSaleLive()
//                        && !isUserExistsInSegment(
//                        userContext, segmentationCacheClient,
//                        megaSalesConfig.getSegment_allSugarfitNewUsersNotCompletedWC1AndNotInstalledCGM())) {
//                    if (megaSalesConfig.isEnableOnlyForInternalUsers()
//                            && !isUserExistsInSegment(userContext, segmentationCacheClient, "sugarfit_all_internal_users")) {
//                        return null;
//                    }
//
//                    Calendar calendar = Calendar.getInstance();
//                    if (calendar.getTime().getTime() < megaSalesConfig.getSaleAutoStartEpoch()
//                            || calendar.getTime().getTime() > megaSalesConfig.getSaleAutoEndEpoch()) {
//                        return null;
//                    }
//
//                    MegaSaleData megaSaleData = new MegaSaleData();
//                    long endOfDay = atEndOfDay(calendar.getTime(), getUserTimezone(userContext));
//
//                    megaSaleData.setSaleLive(megaSalesConfig.isSaleLive());
//                    megaSaleData.setSaleEndTimeEpoch(endOfDay);
//                    megaSaleData.setSaleVideoUrl(megaSalesConfig.getSaleVideoUrlSmall());
//                    megaSaleData.setSaleImageUrl(megaSalesConfig.getSaleImageUrlSmall());
//                    megaSaleData.setSaleImageUrlV2(megaSalesConfig.getSaleImageUrlV2());
//                    megaSaleData.setSalesAreaHeight(megaSalesConfig.getSalesAreaHeight());
//                    megaSaleData.setCountDownEnabled(megaSalesConfig.isCountDownEnabled());
//                    megaSaleData.setSaleAnimUrl(megaSalesConfig.getSaleAnimUrl());
//                    megaSaleData.setSaleMediaAspectRatio(megaSalesConfig.getSaleMediaAspectRatioSmall());
//                    megaSaleData.setSalesBannerAction(megaSalesConfig.getSalesBannerAction());
//                    megaSaleData.setSaleBackgroundColor(megaSalesConfig.getSaleBackgroundColorSmall());
//                    return megaSaleData;
//                }
//            } catch (Exception e) {
//                log.error("Error in fetching MegaSaleData", e);
//            }
//        }
//        return null;
//    }

    public MegaSaleData getMegaSalesDataV2(UserContext userContext) {
        if (isSugarFitApp(userContext) && isSalesBannerCarouselEnabled(userContext) && !isInternationalSugarfitUser(userContext)) {
            try {
                SfHomepageBannerConfig sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {
                }, new SfHomepageBannerConfig());

                if (sfHomepageBannerConfig != null
                        && sfHomepageBannerConfig.getSaleConfigurations().getIsSaleBanner()
                        && !isUserExistsInSegment(
                        userContext,
                        sfHomepageBannerConfig.getMeta().get("segment_allSugarfitNewUsersNotCompletedWC1AndNotInstalledCGM"))) {

                    if (sfHomepageBannerConfig.getSaleConfigurations().getEnableOnlyForInternalUsers()
                            && !isUserExistsInSegment(userContext, "sugarfit_all_internal_users")) {
                        return null;
                    }

                    Calendar calendar = Calendar.getInstance();
                    if (calendar.getTime().getTime() < sfHomepageBannerConfig.getSaleConfigurations().getSaleAutoStartEpoch()
                            || calendar.getTime().getTime() > sfHomepageBannerConfig.getSaleConfigurations().getSaleAutoEndEpoch()) {
                        return null;
                    }

                    MegaSaleData megaSaleData = new MegaSaleData();
                    long endOfDay = atEndOfDay(calendar.getTime(), getUserTimezone(userContext));

                    SfHomepageBannerConfig.MegaSaleConfigurations saleConfigs = sfHomepageBannerConfig.getSaleConfigurations();

                    megaSaleData.setSaleLive(saleConfigs.getIsSaleBanner());
                    megaSaleData.setNewYearSale(saleConfigs.getNewYearSale());
                    if (ChronicCareAppUtil.isIOSUser(userContext)) {
                        megaSaleData.setSaleAppIconEnabled(saleConfigs.getSaleAppIconEnabled());
                    }
                    megaSaleData.setSaleEndTimeEpoch(endOfDay);
                    megaSaleData.setSaleVideoUrl(saleConfigs.getSaleVideoUrl());
                    megaSaleData.setSaleImageUrl(saleConfigs.getSaleImageUrlSmall());
                    megaSaleData.setSaleImageUrlV2(saleConfigs.getSaleImageUrlV2());
                    megaSaleData.setSalesAreaHeight(saleConfigs.getSalesAreaHeight());
                    megaSaleData.setCountDownEnabled(saleConfigs.getCountDownEnabled());
                    megaSaleData.setSaleAnimUrl(saleConfigs.getSaleAnimUrl());
                    megaSaleData.setSaleMediaAspectRatio(saleConfigs.getSaleMediaAspectRatioSmall());
                    megaSaleData.setSalesBannerAction(saleConfigs.getSalesBannerAction());
                    megaSaleData.setSaleBackgroundColor(saleConfigs.getSaleBackgroundColorSmall());
                    return megaSaleData;
                }
            } catch (Exception e) {
                log.error("Error in fetching MegaSaleData", e);
            }
        }
        return null;
    }

//    private MegaSaleData getMegaSalesDataWithSalesBanners(UserContext userContext,
//                                                          CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
//                                                          BundleProduct bundleProduct, ActivePackResponse wellnessActivePackResponse,
//                                                          Boolean skipCalloutBanners) {
//
//        if (isSugarFitApp(userContext) && isSalesBannerCarouselEnabled(userContext, segmentationCacheClient)) {
//            MegaSalesConfig megaSalesConfig = null;
//            HashMap<String, SfSalesBannerUrl> salesBannerConfig = null;
//            try {
//                megaSalesConfig = appConfigCache.getConfig("SF_MEGA_SALE_CONFIG", new TypeReference<>() {
//                }, new MegaSalesConfig());
//                salesBannerConfig = appConfigCache.getConfig("SF_SALES_BANNER_CONFIG", new TypeReference<>() {
//                }, new HashMap<>());
//            } catch (Exception e) {
//                log.error("Error in fetching sales config", e);
//            }
//            if (megaSalesConfig != null
//                    && megaSalesConfig.isSaleLive()
//                    && !isUserExistsInSegment(
//                    userContext, segmentationCacheClient,
//                    megaSalesConfig.getSegment_allSugarfitNewUsersNotCompletedWC1AndNotInstalledCGM())) {
//
//                if (megaSalesConfig.isEnableOnlyForInternalUsers()
//                        && !isUserExistsInSegment(userContext, segmentationCacheClient, "sugarfit_all_internal_users")) {
//                    return null;
//                }
//
//                Calendar calendar = Calendar.getInstance();
//                if (calendar.getTime().getTime() < megaSalesConfig.getSaleAutoStartEpoch()
//                        || calendar.getTime().getTime() > megaSalesConfig.getSaleAutoEndEpoch()) {
//                    return null;
//                }
//
//                MegaSaleData megaSaleData = new MegaSaleData();
//                long endOfDay = atEndOfDay(calendar.getTime(), getUserTimezone(userContext));
//
//                megaSaleData.setSaleLive(megaSalesConfig.isSaleLive());
//                megaSaleData.setSaleEndTimeEpoch(endOfDay);
//                megaSaleData.setSaleVideoUrl(megaSalesConfig.getSaleVideoUrl());
//                megaSaleData.setSaleImageUrl(megaSalesConfig.getSaleImageUrl());
//                megaSaleData.setSaleImageUrlV2(megaSalesConfig.getSaleImageUrlV2());
//                megaSaleData.setSaleMediaAspectRatio(megaSalesConfig.getSaleMediaAspectRatio());
//                megaSaleData.setSalesBannerAction(megaSalesConfig.getSalesBannerAction());
//                megaSaleData.setSaleBackgroundColor(megaSalesConfig.getSaleBackgroundColor());
//                megaSaleData.setSalesAreaHeight(megaSalesConfig.getSalesAreaHeight());
//                megaSaleData.setCountDownEnabled(megaSalesConfig.isCountDownEnabled());
//                megaSaleData.setSaleAnimUrl(megaSalesConfig.getSaleAnimUrl());
//
//
//                SfBannerCarouselWidget salesBannerCarouselWidget = new SfBannerCarouselWidget();
//                List<BannerItem> mediumBannersList = new ArrayList<>();
//
//                if (!skipCalloutBanners && !CollectionUtils.isEmpty(megaSalesConfig.getSaleCalloutBanners())) {
//                    megaSalesConfig.getSaleCalloutBanners().forEach(b -> {
//                        BannerItem bannerItem = new BannerItem();
//                        bannerItem.setImage(b.getImageUrl());
//                        bannerItem.setAction(b.getSalesBannerAction());
//                        mediumBannersList.add(bannerItem);
//                    });
//                }
//
//                if (salesBannerConfig != null) {
//                    SfSalesBannerUrl homeBanners = salesBannerConfig.get("home");
//
//                    if (homeBanners != null) {
//                        BannerItem renewalBanner = null;
//                        BannerItem smartScaleBanner = null;
//                        BannerItem ecomBanner = null;
//                        BannerItem cgmBanner = null;
//                        BannerItem offlineCenterBanner = null;
//                        BannerItem sachinMegaSalesBanner = null;
//                        String firstBannerType = homeBanners.getMedium().getOrDefault("firstBannerType", "");
//
//                        // Renewal banner
//                        String renewalBannerUrl = homeBanners.getMedium().get("renewalMegaSales");
//                        String extensionBannerUrl = homeBanners.getMedium().get("extensionMegaSales");
//                        Action renewalPageAction = Action.builder().url("curefit://renewsubscription").actionType(ActionType.NAVIGATION).title("RENEW SUBSCRIPTION").build();
//                        renewalBanner = new BannerItem();
//                        renewalBanner.setImage(renewalBannerUrl);
//                        renewalBanner.setAction(renewalPageAction);
//
//                        // Sachin banner
//                        SfBannerCarouselWidget sachinMegaSales = getEComBannerWidget(userContext, "sachinMegaSales", false);
//                        if (Objects.nonNull(sachinMegaSales) && !CollectionUtils.isEmpty(sachinMegaSales.getData())) {
//                            sachinMegaSalesBanner = sachinMegaSales.getData().get(0);
//                            sachinMegaSalesBanner.setAction(Action.builder().actionType(ActionType.NAVIGATION).url(ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext, segmentationCacheClient) ? "curefit://sfstorelp" : "curefit://sfecommerceplp").build());
//                        }
//
//                        // Smart Scale banner
//                        if (isSmartScaleSellingSupportedAppVersion(userContext) && !isSmartScaleDelivered(userContext)) {
//                            SfBannerCarouselWidget smartScaleMegaSalesWidget = getSmartScalePurchaseBannerWidget(userContext, "smartScaleMegaSales", false);
//                            if (Objects.nonNull(smartScaleMegaSalesWidget) && !CollectionUtils.isEmpty(smartScaleMegaSalesWidget.getData())) {
//                                smartScaleBanner = smartScaleMegaSalesWidget.getData().get(0);
//                            }
//                        }
//
//                        // Ecommerce banner
//                        if (isECommerceSupported(userContext, segmentationCacheClient)) {
//                            SfBannerCarouselWidget eComBannerWidget = getEComBannerWidget(userContext, "ecommerceMegaSales", false);
//                            if (Objects.nonNull(eComBannerWidget) && !CollectionUtils.isEmpty(eComBannerWidget.getData())) {
//                                ecomBanner = eComBannerWidget.getData().get(0);
//                            }
//                        }
//
//                        // CGM banner
//                        if (isSalesCGMBannerEnabled(userContext, segmentationCacheClient, cgmOnboardingStatusResponse, bundleProduct)) {
//                            String cgmMediumBannerUrl = homeBanners.getMedium().get("cgmMegaSales");
//                            Action showSalesBannerAction1 = Action.builder().title("BUY NEW CGM").url(ChronicCareServiceHelper.getCgmStorePageLink(userContext, segmentationCacheClient)).actionType(ActionType.NAVIGATION).build();
//                            cgmBanner = new BannerItem();
//                            cgmBanner.setImage(cgmMediumBannerUrl);
//                            cgmBanner.setAction(showSalesBannerAction1);
//                        }
//
//                        // Offline purchase banner
//                        if (isWellnessAtCenterPrePurchaseEnabledUser(userContext, segmentationCacheClient, userOnboardingService, wellnessActivePackResponse)) {
//                            SfBannerCarouselWidget wellnessCenterBannerWidget = getWellnessAtCenterBannerWidget(userContext, "wellnessAtCenterMegaSales", false);
//                            if (Objects.nonNull(wellnessCenterBannerWidget) && org.apache.commons.collections.CollectionUtils.isNotEmpty(wellnessCenterBannerWidget.getData())) {
//                                offlineCenterBanner = wellnessCenterBannerWidget.getData().get(0);
//                            }
//                        }
//
//                        // Do ordering of banners based on segments
//                        if (isUserExistsInSegment(
//                                userContext, segmentationCacheClient,
//                                megaSalesConfig.getSegment_allSugarfitExpiredPacks())) {
//                            if (Objects.nonNull(renewalBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("RENEWAL")) {
//                                    mediumBannersList.add(0, renewalBanner);
//                                } else {
//                                    mediumBannersList.add(renewalBanner);
//                                }
//                            }
//                            if (Objects.nonNull(ecomBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("ECOMMERCE")) {
//                                    mediumBannersList.add(0, ecomBanner);
//                                } else {
//                                    mediumBannersList.add(ecomBanner);
//                                }
//                            }
//                            if (Objects.nonNull(smartScaleBanner) && !isSmartScaleDelivered(userContext)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SMART_SCALE")) {
//                                    mediumBannersList.add(0, smartScaleBanner);
//                                } else {
//                                    mediumBannersList.add(smartScaleBanner);
//                                }
//                            }
//                            if (Objects.nonNull(sachinMegaSalesBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SACHIN")) {
//                                    mediumBannersList.add(0, sachinMegaSalesBanner);
//                                } else {
//                                    mediumBannersList.add(sachinMegaSalesBanner);
//                                }
//                            }
//                        } else if (ChronicCareAppUtil.isUserExistsInSegment(
//                                userContext, segmentationCacheClient,
//                                megaSalesConfig.getSegment_allSugarfitPacksExpiringInNext3Months())) {
//                            if (Objects.nonNull(renewalBanner)) {
//                                renewalBanner.setImage(extensionBannerUrl);
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("RENEWAL")) {
//                                    mediumBannersList.add(0, renewalBanner);
//                                } else {
//                                    mediumBannersList.add(renewalBanner);
//                                }
//                            }
//                            if (Objects.nonNull(ecomBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("ECOMMERCE")) {
//                                    mediumBannersList.add(0, ecomBanner);
//                                } else {
//                                    mediumBannersList.add(ecomBanner);
//                                }
//                            }
//                            if (Objects.nonNull(cgmBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("CGM")) {
//                                    mediumBannersList.add(0, cgmBanner);
//                                } else {
//                                    mediumBannersList.add(cgmBanner);
//                                }
//                            }
//                            if (Objects.nonNull(smartScaleBanner) && !isSmartScaleDelivered(userContext)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SMART_SCALE")) {
//                                    mediumBannersList.add(0, smartScaleBanner);
//                                } else {
//                                    mediumBannersList.add(smartScaleBanner);
//                                }
//                            }
//                            if (Objects.nonNull(sachinMegaSalesBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SACHIN")) {
//                                    mediumBannersList.add(0, sachinMegaSalesBanner);
//                                } else {
//                                    mediumBannersList.add(sachinMegaSalesBanner);
//                                }
//                            }
//                        } else if (ChronicCareAppUtil.isUserExistsInSegment(
//                                userContext, segmentationCacheClient,
//                                megaSalesConfig.getSegment_allSugarfitBangaloreUsers())) {
//                            if (Objects.nonNull(offlineCenterBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("WELLNESS_AT_CENTER")) {
//                                    mediumBannersList.add(0, offlineCenterBanner);
//                                } else {
//                                    mediumBannersList.add(offlineCenterBanner);
//                                }
//                            }
//                            if (Objects.nonNull(cgmBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("CGM")) {
//                                    mediumBannersList.add(0, cgmBanner);
//                                } else {
//                                    mediumBannersList.add(cgmBanner);
//                                }
//                            }
//                            if (Objects.nonNull(ecomBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("ECOMMERCE")) {
//                                    mediumBannersList.add(0, ecomBanner);
//                                } else {
//                                    mediumBannersList.add(ecomBanner);
//                                }
//                            }
//                            if (Objects.nonNull(smartScaleBanner) && !isSmartScaleDelivered(userContext)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SMART_SCALE")) {
//                                    mediumBannersList.add(0, smartScaleBanner);
//                                } else {
//                                    mediumBannersList.add(smartScaleBanner);
//                                }
//                            }
//                            if (Objects.nonNull(sachinMegaSalesBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SACHIN")) {
//                                    mediumBannersList.add(0, sachinMegaSalesBanner);
//                                } else {
//                                    mediumBannersList.add(sachinMegaSalesBanner);
//                                }
//                            }
//                        } else if (ChronicCareAppUtil.isUserExistsInSegment(
//                                userContext, segmentationCacheClient,
//                                megaSalesConfig.getSegment_allSugarfitUsers())) {
//                            if (Objects.nonNull(cgmBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("CGM")) {
//                                    mediumBannersList.add(0, cgmBanner);
//                                } else {
//                                    mediumBannersList.add(cgmBanner);
//                                }
//                            }
//                            if (Objects.nonNull(ecomBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("ECOMMERCE")) {
//                                    mediumBannersList.add(0, ecomBanner);
//                                } else {
//                                    mediumBannersList.add(ecomBanner);
//                                }
//                            }
//                            if (Objects.nonNull(renewalBanner)) {
//                                renewalBanner.setImage(extensionBannerUrl);
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("RENEWAL")) {
//                                    mediumBannersList.add(0, renewalBanner);
//                                } else {
//                                    mediumBannersList.add(renewalBanner);
//                                }
//                            }
//                            if (Objects.nonNull(smartScaleBanner) && !isSmartScaleDelivered(userContext)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SMART_SCALE")) {
//                                    mediumBannersList.add(0, smartScaleBanner);
//                                } else {
//                                    mediumBannersList.add(smartScaleBanner);
//                                }
//                            }
//                            if (Objects.nonNull(sachinMegaSalesBanner)) {
//                                if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SACHIN")) {
//                                    mediumBannersList.add(0, sachinMegaSalesBanner);
//                                } else {
//                                    mediumBannersList.add(sachinMegaSalesBanner);
//                                }
//                            }
//                        }
//                    }
//                }
//
//                if (!CollectionUtils.isEmpty(mediumBannersList)) {
//                    salesBannerCarouselWidget.setData(mediumBannersList);
//                    Map<String, Object> lp = getSalesBannerLayoutProps(true);
//                    lp.put("verticalPadding", 10);
//                    lp.put("invisiblePageIndicators", true);
//                    salesBannerCarouselWidget.setLayoutProps(lp);
//                    megaSaleData.setSalesBannerCarouselWidget(salesBannerCarouselWidget);
//                }
//
//                if (!CollectionUtils.isEmpty(megaSalesConfig.getSaleCategoryBanners())) {
//                    String firstCategoryType = megaSalesConfig.getFirstCategoryType();
//
//                    SfBannerCarouselWidget salesCategoriesCarouselWidget = new SfBannerCarouselWidget();
//                    List<BannerItem> bannerItemList = new ArrayList<>();
//                    HashMap<String, BannerItem> bannerMap = new HashMap<>();
//                    megaSalesConfig.getSaleCategoryBanners().forEach(b -> {
//                        BannerItem bannerItem = new BannerItem();
//                        bannerItem.setImage(b.getImageUrl());
//                        bannerItem.setAction(b.getSalesBannerAction());
//                        bannerMap.put(b.getType(), bannerItem);
//                    });
//                    if (!CollectionUtils.isEmpty(bannerMap)) {
//                        // Do ordering of banners based on segments
//                        if (isUserExistsInSegment(
//                                userContext, segmentationCacheClient,
//                                megaSalesConfig.getSegment_allSugarfitExpiredPacks())) {
//
//                            if (bannerMap.containsKey("RENEWAL")) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("RENEWAL")) {
//                                    bannerItemList.add(0, bannerMap.get("RENEWAL"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("RENEWAL"));
//                                }
//                            }
//                            if (bannerMap.containsKey("ECOM") && isECommerceSupported(userContext, segmentationCacheClient)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("ECOM")) {
//                                    bannerItemList.add(0, bannerMap.get("ECOM"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("ECOM"));
//                                }
//                            }
//                            if (bannerMap.containsKey("SMART_SCALE") && isSmartScaleSellingSupportedAppVersion(userContext) && !isSmartScaleDelivered(userContext)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("SMART_SCALE")) {
//                                    bannerItemList.add(0, bannerMap.get("SMART_SCALE"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("SMART_SCALE"));
//                                }
//                            }
//                        } else if (isUserExistsInSegment(
//                                userContext, segmentationCacheClient,
//                                megaSalesConfig.getSegment_allSugarfitPacksExpiringInNext3Months())) {
//                            if (bannerMap.containsKey("EXTENSION")) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("RENEWAL")) {
//                                    bannerItemList.add(0, bannerMap.get("EXTENSION"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("EXTENSION"));
//                                }
//                            }
//                            if (bannerMap.containsKey("ECOM") && isECommerceSupported(userContext, segmentationCacheClient)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("ECOM")) {
//                                    bannerItemList.add(0, bannerMap.get("ECOM"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("ECOM"));
//                                }
//                            }
//                            if (bannerMap.containsKey("CGM")
//                                    && isSalesCGMBannerEnabled(userContext, segmentationCacheClient, cgmOnboardingStatusResponse, bundleProduct)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("CGM")) {
//                                    bannerItemList.add(0, bannerMap.get("CGM"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("CGM"));
//                                }
//                            }
//                            if (bannerMap.containsKey("SMART_SCALE") && isSmartScaleSellingSupportedAppVersion(userContext) && !isSmartScaleDelivered(userContext)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("SMART_SCALE")) {
//                                    bannerItemList.add(0, bannerMap.get("SMART_SCALE"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("SMART_SCALE"));
//                                }
//                            }
//                        } else if (isUserExistsInSegment(
//                                userContext, segmentationCacheClient,
//                                megaSalesConfig.getSegment_allSugarfitBangaloreUsers())) {
//                            if (bannerMap.containsKey("OFFLINE_WELLNESS")
//                                    && isWellnessAtCenterPrePurchaseEnabledUser(userContext, segmentationCacheClient, userOnboardingService, wellnessActivePackResponse)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("OFFLINE_WELLNESS")) {
//                                    bannerItemList.add(0, bannerMap.get("OFFLINE_WELLNESS"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("OFFLINE_WELLNESS"));
//                                }
//                            }
//                            if (bannerMap.containsKey("CGM")
//                                    && isSalesCGMBannerEnabled(userContext, segmentationCacheClient, cgmOnboardingStatusResponse, bundleProduct)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("CGM")) {
//                                    bannerItemList.add(0, bannerMap.get("CGM"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("CGM"));
//                                }
//                            }
//                            if (bannerMap.containsKey("ECOM") && isECommerceSupported(userContext, segmentationCacheClient)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("ECOM")) {
//                                    bannerItemList.add(0, bannerMap.get("ECOM"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("ECOM"));
//                                }
//                            }
//                            if (bannerMap.containsKey("SMART_SCALE") && isSmartScaleSellingSupportedAppVersion(userContext) && !isSmartScaleDelivered(userContext)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("SMART_SCALE")) {
//                                    bannerItemList.add(0, bannerMap.get("SMART_SCALE"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("SMART_SCALE"));
//                                }
//                            }
//                        } else if (isUserExistsInSegment(
//                                userContext, segmentationCacheClient,
//                                megaSalesConfig.getSegment_allSugarfitUsers())) {
//                            if (bannerMap.containsKey("CGM")
//                                    && isSalesCGMBannerEnabled(userContext, segmentationCacheClient, cgmOnboardingStatusResponse, bundleProduct)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("CGM")) {
//                                    bannerItemList.add(0, bannerMap.get("CGM"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("CGM"));
//                                }
//                            }
//                            if (bannerMap.containsKey("ECOM") && isECommerceSupported(userContext, segmentationCacheClient)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("ECOM")) {
//                                    bannerItemList.add(0, bannerMap.get("ECOM"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("ECOM"));
//                                }
//                            }
//                            if (bannerMap.containsKey("EXTENSION")) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("RENEWAL")) {
//                                    bannerItemList.add(0, bannerMap.get("EXTENSION"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("EXTENSION"));
//                                }
//                            }
//                            if (bannerMap.containsKey("SMART_SCALE") && isSmartScaleSellingSupportedAppVersion(userContext) && !isSmartScaleDelivered(userContext)) {
//                                if (Objects.nonNull(firstCategoryType) && !firstCategoryType.isBlank() && firstCategoryType.equals("SMART_SCALE")) {
//                                    bannerItemList.add(0, bannerMap.get("SMART_SCALE"));
//                                } else {
//                                    bannerItemList.add(bannerMap.get("SMART_SCALE"));
//                                }
//                            }
//                        }
//
//                        if (bannerItemList.size() == 1) {
//                            bannerItemList.add(bannerMap.get("DUMMY"));
//                        }
//                        salesCategoriesCarouselWidget.setData(bannerItemList);
//                        salesCategoriesCarouselWidget.setLayoutProps(getMegaSalesCategoryBannerLayoutProps());
//                        megaSaleData.setSalesCategoriesCarouselWidget(salesCategoriesCarouselWidget);
//                    }
//                }
//                return megaSaleData;
//            }
//        }
//        return null;
//    }
    private MegaSaleData getMegaSalesDataWithSalesBannersV2(UserContext userContext,
                                                          CgmOnboardingStatusResponse cgmOnboardingStatusResponse,
                                                          BundleProduct bundleProduct, ActivePackResponse wellnessActivePackResponse,
                                                          Boolean skipCalloutBanners) {

        if (isSugarFitApp(userContext) && isSalesBannerCarouselEnabled(userContext)) {
            SfHomepageBannerConfig sfHomepageBannerConfig = null;
            try {
                sfHomepageBannerConfig = appConfigCache.getConfig("SF_APP_HOME_SALES_CONFIG", new TypeReference<>() {}, new SfHomepageBannerConfig());
            } catch (Exception e) {
                log.error("Error in fetching sales config", e);
            }
            if (sfHomepageBannerConfig != null && sfHomepageBannerConfig.getSaleConfigurations() != null
                    && sfHomepageBannerConfig.getSaleConfigurations().getIsSaleBanner()
                    && !isUserExistsInSegment(
                    userContext,
                    sfHomepageBannerConfig.getMeta().get("segment_allSugarfitNewUsersNotCompletedWC1AndNotInstalledCGM"))) {
                SfHomepageBannerConfig.MegaSaleConfigurations saleConfig = sfHomepageBannerConfig.getSaleConfigurations();
                if (saleConfig.getEnableOnlyForInternalUsers()
                        && !isUserExistsInSegment(userContext, "sugarfit_all_internal_users")) {
                    return null;
                }

                Calendar calendar = Calendar.getInstance();
                if (calendar.getTime().getTime() < saleConfig.getSaleAutoStartEpoch()
                        || calendar.getTime().getTime() > saleConfig.getSaleAutoEndEpoch()) {
                    return null;
                }

                MegaSaleData megaSaleData = new MegaSaleData();
                long endOfDay = atEndOfDay(calendar.getTime(), getUserTimezone(userContext));

                megaSaleData.setSaleLive(saleConfig.getIsSaleBanner());
                megaSaleData.setNewYearSale(saleConfig.getNewYearSale());
                if (ChronicCareAppUtil.isIOSUser(userContext)) {
                    megaSaleData.setSaleAppIconEnabled(saleConfig.getSaleAppIconEnabled());
                }
                megaSaleData.setSaleEndTimeEpoch(endOfDay);
                megaSaleData.setSaleVideoUrl(saleConfig.getSaleVideoUrl());
                megaSaleData.setSaleImageUrl(saleConfig.getSaleImageUrl());
                megaSaleData.setSaleImageUrlV2(saleConfig.getSaleImageUrlV2());
                megaSaleData.setSaleMediaAspectRatio(saleConfig.getSaleMediaAspectRatio());
                megaSaleData.setSalesBannerAction(saleConfig.getSalesBannerAction());
                megaSaleData.setSaleBackgroundColor(saleConfig.getSaleBackgroundColor());
                megaSaleData.setSalesAreaHeight(saleConfig.getSalesAreaHeight());
                megaSaleData.setCountDownEnabled(saleConfig.getCountDownEnabled());
                megaSaleData.setSaleAnimUrl(saleConfig.getSaleAnimUrl());


                SfBannerCarouselWidget salesBannerCarouselWidget = new SfBannerCarouselWidget();
                List<BannerItem> mediumBannersList = new ArrayList<>();

                SfHomepageBannerConfig.BannerConfig megaSaleBanners = sfHomepageBannerConfig.getMegaSaleBanners();
                HashMap<String, String> metaConfigs = sfHomepageBannerConfig.getMeta();
                String firstBannerType = sfHomepageBannerConfig.getSaleConfigurations().getFirstBannerType();

                if (megaSaleBanners != null) {
                    BannerItem renewalBanner = null;
                    BannerItem smartScaleBanner = null;
                    BannerItem ecomBanner = null;
                    BannerItem cgmBanner = null;
                    BannerItem offlineCenterBanner = null;
                    BannerItem sachinMegaSalesBanner = null;

                    // Renewal banner
                    String renewalBannerUrl = megaSaleBanners.getRenewalSales().getImageUrl();
                    String extensionBannerUrl = megaSaleBanners.getExtensionSales().getImageUrl();
                    Action renewalPageAction = Action.builder().url("curefit://renewsubscription").actionType(ActionType.NAVIGATION).title("RENEW SUBSCRIPTION").build();
                    renewalBanner = createBannerItem(renewalBannerUrl, megaSaleBanners.getRenewalSales().getLottieUrl(), renewalPageAction);

                    // Smart Scale banner
                    if (isSmartScaleSellingSupportedAppVersion(userContext) && !isSmartScaleDelivered(userContext)) {
                        Action smartScaleAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfsmartscaleplp").build();
                        if (ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)
                                && ChronicCareAppUtil.isCgmCombinedCartSupportedAppVersion(userContext)) {
                            smartScaleAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfecommercepdp?productId=SMART_SCALE").build();
                        }
                        smartScaleBanner = createBannerItem(megaSaleBanners.getSmartScale().getImageUrl(), megaSaleBanners.getSmartScale().getLottieUrl(), smartScaleAction);
                    }

                    // Ecommerce banner
                    if (isECommerceSupported(userContext)) {
                        Action ecomBannerAction = Action.builder().actionType(ActionType.NAVIGATION).url(isSfStoreTabEnabledUser(userContext) ? "curefit://sfstorelifestylepage" : "curefit://sfecommerceplp").build();
                        ecomBanner = createBannerItem(megaSaleBanners.getEcommerce().getImageUrl(), megaSaleBanners.getEcommerce().getLottieUrl(), ecomBannerAction);
                    }

                    // CGM banner
                    if (isSalesCGMBannerEnabled(userContext, cgmOnboardingStatusResponse, bundleProduct)) {
                        Action showSalesBannerAction1 = Action.builder().title("BUY NEW CGM").url(ChronicCareServiceHelper.getCgmStorePageLink(userContext)).actionType(ActionType.NAVIGATION).build();
                        cgmBanner = createBannerItem(megaSaleBanners.getCgm().getImageUrl(), megaSaleBanners.getCgm().getLottieUrl(), showSalesBannerAction1);
                    }

                    // Offline purchase banner
                    if (isWellnessAtCenterPrePurchaseEnabledUser(userContext, userOnboardingService, wellnessActivePackResponse)) {
                        Action wellnessAtCenterAction = Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfwellnessatcenterprepurchase").build();
                        offlineCenterBanner = createBannerItem(megaSaleBanners.getWellnessAtCenter().getImageUrl(), megaSaleBanners.getWellnessAtCenter().getLottieUrl(), wellnessAtCenterAction);
                    }

                    boolean isRenewalFirst = Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("RENEWAL");
                    boolean isEcommerceFirst = Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("ECOMMERCE");
                    boolean isSmartScaleFirst = Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SMART_SCALE");
                    boolean isCgmFirst = Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("CGM");
                    boolean isSachinFirst = Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("SACHIN");

                    // Do ordering of banners based on segments
                    if (isUserExistsInSegment(
                            userContext,
                            metaConfigs.get("segment_allSugarfitExpiredPacks"))) {
                        if (Objects.nonNull(renewalBanner)) {
                            if (isRenewalFirst) {
                                mediumBannersList.add(0, renewalBanner);
                            } else {
                                mediumBannersList.add(renewalBanner);
                            }
                        }
                        if (Objects.nonNull(ecomBanner)) {
                            if (isEcommerceFirst) {
                                mediumBannersList.add(0, ecomBanner);
                            } else {
                                mediumBannersList.add(ecomBanner);
                            }
                        }
                        if (Objects.nonNull(smartScaleBanner) && !isSmartScaleDelivered(userContext)) {
                            if (isSmartScaleFirst) {
                                mediumBannersList.add(0, smartScaleBanner);
                            } else {
                                mediumBannersList.add(smartScaleBanner);
                            }
                        }
                        if (Objects.nonNull(sachinMegaSalesBanner)) {
                            if (isSachinFirst) {
                                mediumBannersList.add(0, sachinMegaSalesBanner);
                            } else {
                                mediumBannersList.add(sachinMegaSalesBanner);
                            }
                        }
                    } else if (isUserExistsInSegment(
                            userContext,
                            metaConfigs.get("segment_allSugarfitPacksExpiringInNext3Months"))) {
                        if (Objects.nonNull(renewalBanner) && Objects.nonNull(extensionBannerUrl) && !extensionBannerUrl.isEmpty()) {
                            renewalBanner.setImage(extensionBannerUrl);
                            if (isRenewalFirst) {
                                mediumBannersList.add(0, renewalBanner);
                            } else {
                                mediumBannersList.add(renewalBanner);
                            }
                        }
                        if (Objects.nonNull(ecomBanner)) {
                            if (isEcommerceFirst) {
                                mediumBannersList.add(0, ecomBanner);
                            } else {
                                mediumBannersList.add(ecomBanner);
                            }
                        }
                        if (Objects.nonNull(cgmBanner)) {
                            if (isCgmFirst) {
                                mediumBannersList.add(0, cgmBanner);
                            } else {
                                mediumBannersList.add(cgmBanner);
                            }
                        }
                        if (Objects.nonNull(smartScaleBanner) && !isSmartScaleDelivered(userContext)) {
                            if (isSmartScaleFirst) {
                                mediumBannersList.add(0, smartScaleBanner);
                            } else {
                                mediumBannersList.add(smartScaleBanner);
                            }
                        }
                        if (Objects.nonNull(sachinMegaSalesBanner)) {
                            if (isSachinFirst) {
                                mediumBannersList.add(0, sachinMegaSalesBanner);
                            } else {
                                mediumBannersList.add(sachinMegaSalesBanner);
                            }
                        }
                    } else if (isUserExistsInSegment(
                            userContext,
                            metaConfigs.get("segment_allSugarfitBangaloreUsers"))) {
                        if (Objects.nonNull(offlineCenterBanner)) {
                            if (Objects.nonNull(firstBannerType) && !firstBannerType.isBlank() && firstBannerType.equals("WELLNESS_AT_CENTER")) {
                                mediumBannersList.add(0, offlineCenterBanner);
                            } else {
                                mediumBannersList.add(offlineCenterBanner);
                            }
                        }
                        if (Objects.nonNull(cgmBanner)) {
                            if (isCgmFirst) {
                                mediumBannersList.add(0, cgmBanner);
                            } else {
                                mediumBannersList.add(cgmBanner);
                            }
                        }
                        if (Objects.nonNull(ecomBanner)) {
                            if (isEcommerceFirst) {
                                mediumBannersList.add(0, ecomBanner);
                            } else {
                                mediumBannersList.add(ecomBanner);
                            }
                        }
                        if (Objects.nonNull(smartScaleBanner) && !isSmartScaleDelivered(userContext)) {
                            if (isSmartScaleFirst) {
                                mediumBannersList.add(0, smartScaleBanner);
                            } else {
                                mediumBannersList.add(smartScaleBanner);
                            }
                        }
                        if (Objects.nonNull(sachinMegaSalesBanner)) {
                            if (isSachinFirst) {
                                mediumBannersList.add(0, sachinMegaSalesBanner);
                            } else {
                                mediumBannersList.add(sachinMegaSalesBanner);
                            }
                        }
                    } else if (isUserExistsInSegment(
                            userContext,
                            metaConfigs.get("segment_allSugarfitUsers"))) {
                        if (Objects.nonNull(cgmBanner)) {
                            if (isCgmFirst) {
                                mediumBannersList.add(0, cgmBanner);
                            } else {
                                mediumBannersList.add(cgmBanner);
                            }
                        }
                        if (Objects.nonNull(ecomBanner)) {
                            if (isEcommerceFirst) {
                                mediumBannersList.add(0, ecomBanner);
                            } else {
                                mediumBannersList.add(ecomBanner);
                            }
                        }
                        if (Objects.nonNull(smartScaleBanner) && !isSmartScaleDelivered(userContext)) {
                            if (isSmartScaleFirst) {
                                mediumBannersList.add(0, smartScaleBanner);
                            } else {
                                mediumBannersList.add(smartScaleBanner);
                            }
                        }
                        if (Objects.nonNull(sachinMegaSalesBanner)) {
                            if (isSachinFirst) {
                                mediumBannersList.add(0, sachinMegaSalesBanner);
                            } else {
                                mediumBannersList.add(sachinMegaSalesBanner);
                            }
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(mediumBannersList)) {
                    salesBannerCarouselWidget.setData(mediumBannersList);
                    Map<String, Object> lp = getMegaSalesBannerLayoutProps(true);
                    lp.put("verticalPadding", 10);
                    lp.put("invisiblePageIndicators", true);
                    salesBannerCarouselWidget.setLayoutProps(lp);
                    megaSaleData.setSalesBannerCarouselWidget(salesBannerCarouselWidget);
                }
                return megaSaleData;
            }
        }
        return null;
    }

    @GetMapping
    public static Map<String, Object> getMegaSalesCategoryBannerLayoutProps() {
        Map<String, Object> layoutProps = new HashMap<>();
        layoutProps.put("roundedCorners", true);
        layoutProps.put("bannerHeight", 120);
        layoutProps.put("bannerWidth", 115);
        layoutProps.put("bannerOriginalHeight", 120);
        layoutProps.put("bannerOriginalWidth", 115);
        layoutProps.put("verticalPadding", 10);
        layoutProps.put("showPagination", false);
        return layoutProps;
    }

    public boolean isSpecialSugarControlPlanPack(BundleProduct bundleProduct) {
        if (bundleProduct != null) {
            return SUGAR_CONTROL_PLANS.contains(bundleProduct.getProductCode());
        }
        return false;
    }

    public boolean isSpecialSugarControlPlanPack(String productCode) {
        if (productCode != null) {
            return SUGAR_CONTROL_PLANS.contains(productCode);
        }
        return false;
    }

    public boolean isSpecialSugarControl7DayPlanPack(BundleProduct bundleProduct) {
        if (bundleProduct != null) {
            return SUGARFIT_SPECIAL_PACK_7D_OFFLINE.equals(bundleProduct.getProductCode());
        }
        return false;
    }

    public boolean isSpecialSugarControl7DayPlanPack(String productCode) {
        if (productCode != null) {
            return SUGARFIT_SPECIAL_PACK_7D_OFFLINE.equals(productCode);
        }
        return false;
    }

    public boolean isQuickCommerceCGMPack(BundleProduct bundleProduct) {
        if (bundleProduct != null) {
            try {
                return "external-cgm".equals(bundleProduct.getProductSpecs().get("target").textValue());
            } catch (Exception e) {
                return false;
            }

        }
        return false;
    }

    public Action getCgmWatchVideoAction(List<DeviceModel> availableDevices) {
        if (hasOnlyAbbottDevices(availableDevices)) {
            return Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .title("WATCH INSTALLATION VIDEO")
                    .url("curefit://sfcgmselfinstallationapplyguidescreen")
                    .build();
        } else if (hasOnlyGlucoRx(availableDevices)) {
            return createGlucoRxCgmInstallationWatchVideoAction();
        } else {
            Map<String, Object> videoMeta = new HashMap<>();
            // GlucoRx action
            Action glucoRxAction = createGlucoRxCgmInstallationWatchVideoAction();
            videoMeta.put("glucoRxVideoAction", glucoRxAction);

            // Abbott action
            Action abbottAction = Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .title("WATCH INSTALLATION VIDEO")
                    .url("curefit://sfcgmselfinstallationapplyguidescreen")
                    .build();
            videoMeta.put("abbottVideoAction", abbottAction);

            return Action.builder()
                    .actionType(ActionType.SHOW_CGM_INSTALLATION_VIDEO_SELECTION_MODAL)
                    .title("WATCH INSTALLATION VIDEO")
                    .meta(videoMeta)
                    .build();
        }
    }

    public Action getCgmWatchVideoAction(List<DeviceModel> availableDevices, boolean hideAskingDifficulty) {
        Action action = getCgmWatchVideoAction(availableDevices);
        if (action!=null && action.getUrl() != null && action.getUrl().equals("curefit://sfcgmselfinstallationapplyguidescreen")) {
            action.setUrl("curefit://sfcgmselfinstallationapplyguidescreen?hideAskingDifficulty=" + hideAskingDifficulty);
        }
        return action;
    }

    public Action createGlucoRxCgmInstallationWatchVideoAction() {
        Map<String, String> currentVideo = new HashMap<>();
        currentVideo.put("videoUrl", GLUCO_RX_CGM_SELF_INSTALLATION_VIDEO_URL);
        currentVideo.put("thumbnailUrl", "");
        currentVideo.put("title", "");

        List<Map<String, String>> allVideos = new ArrayList<>();
        allVideos.add(new HashMap<>(currentVideo));

        Map<String, Object> meta = new HashMap<>();
        meta.put("currentVideo", currentVideo);
        meta.put("allVideos", allVideos);

        return Action.builder()
                .actionType(ActionType.VIEW_SF_VIDEO)
                .title("WATCH INSTALLATION VIDEO")
                .meta(meta)
                .build();
    }

    public boolean isShopifyCGMPack(BundleProduct bundleProduct) {
        if (bundleProduct != null && bundleProduct.getProductCode() != null) {
            return SUGARFIT_Smart_Glucose_Montring_Shopify_1CGM.equals(bundleProduct.getProductCode())
                    || SUGARFIT_Smart_Glucose_Montring_Shopify_2CGM.equals(bundleProduct.getProductCode())
                    || SUGARFIT_Smart_Glucose_Montring_Shopify_4CGM.equals(bundleProduct.getProductCode());
        }
        return false;
    }

    public boolean isQuickCommerceCGMPack(String productCode) {
        if (productCode != null) {
            return SUGARFIT_SPECIAL_PACK_14D_INSTAMART.equals(productCode)
                    || SUGARFIT_SPECIAL_PACK_14D_FLIPKART.equals(productCode)
                    || SUGARFIT_SPECIAL_PACK_14D_AMAZON.equals(productCode);
        }
        return false;
    }


    public List<LiveClass> getAllSugarfitLiveClasses(UserContext userContext, Integer count) {
        try {
            Integer limit = count != null ? count : 20;
            String userId = userContext.getUserProfile().getUserId();
            Tenant tenant = Tenant.fromString(AppUtil.getTenantFromUserContext(userContext).toString());
            String countryId = userContext.getUserProfile().getCity().getCountryId();
            DIYFilterRequestV2 diyFilterRequestV2 = DIYFilterRequestV2.builder()
                    .preferredStreamType(PreferredStreamType.VIDEO_CALL).build();
            List<LiveClass> liveClasses = serviceInterfaces.getDiyfsService().getUpcomingClassesByFilters(limit, 0, tenant,
                    diyFilterRequestV2, userId, countryId).get();
            return liveClasses;
        } catch (Exception e) {
            String msg = String.format("Error on fetching live classes for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
            log.error(msg, e);
            return new ArrayList<>();
        }
    }

    public SfMasterLiveClassWidget getMasterLiveClassWidget(UserContext userContext, String productCode, List<LiveClass> liveClassesList) {
        try {
            MasterClassFilterRequest masterClassFilterRequest = new MasterClassFilterRequest();
            masterClassFilterRequest.setUserId(Long.valueOf(userContext.getUserProfile().getUserId()));
            masterClassFilterRequest.setStatus(UserMasterClassStatus.COMPLETED);
            List<UserMasterClassEntry> completedClasses = masterClassClient.filterMasterClasses(masterClassFilterRequest);
            AtomicBoolean hasAttendedDoctorClass = new AtomicBoolean(false);
            AtomicBoolean hasAttendedNutritionClass = new AtomicBoolean(false);
            AtomicBoolean hasAttendedActiveFoodsClass = new AtomicBoolean(false);
            AtomicBoolean hasAttendedFitnessClass = new AtomicBoolean(false);
            String MASTER_CLASS_DOCTOR = "MASTER-CLASS-DOCTOR";
            String MASTER_CLASS_DOCTOR_TELUGU = "MASTER-CLASS-DOCTOR-TELUGU";
            String MASTER_CLASS_NUTRITION = "MASTER-CLASS-NUTRITION";
            String MASTER_CLASS_NUTRITION_TELUGU = "MASTER-CLASS-NUTRITION-TELUGU";
            String MASTER_CLASS_JUICE = "MASTER-CLASS-JUICE";
            String MASTER_CLASS_GENERIC = "MASTER-CLASS-GENERIC";

            if (!CollectionUtils.isEmpty(completedClasses)) {
                completedClasses.forEach(classEntry -> {
                    if (classEntry.getMasterClassId().equals(MASTER_CLASS_DOCTOR) || classEntry.getMasterClassId().equals(MASTER_CLASS_DOCTOR_TELUGU)) {
                        hasAttendedDoctorClass.set(true);
                    }
                    if (classEntry.getMasterClassId().equals(MASTER_CLASS_NUTRITION) || classEntry.getMasterClassId().equals(MASTER_CLASS_NUTRITION_TELUGU)) {
                        hasAttendedNutritionClass.set(true);
                    }
                    if (classEntry.getMasterClassId().equals(MASTER_CLASS_JUICE)) {
                        hasAttendedActiveFoodsClass.set(true);
                    }
                    if (classEntry.getMasterClassId().equals(MASTER_CLASS_FITNESS)) {
                        hasAttendedFitnessClass.set(true);
                    }
                });
            }

            List<LiveClass> liveClasses = liveClassesList;
            if (CollectionUtils.isEmpty(liveClasses)) {
                liveClasses = getAllSugarfitLiveClasses(userContext, 30);
            }
            List<LiveClass> bookedMasterClasses = new ArrayList<>();
            AtomicReference<LiveClass> doctorClass = new AtomicReference<>();
            AtomicReference<LiveClass> nutritionClass = new AtomicReference<>();
            AtomicReference<LiveClass> activeFoodsClass = new AtomicReference<>();
            AtomicReference<LiveClass> fitnessClass = new AtomicReference<>();
            List<LiveClass> masterClassesGeneric = new ArrayList<>();

            if (!CollectionUtils.isEmpty(liveClasses)) {
                liveClasses.forEach(liveClass -> {
                    if (liveClass != null && !CollectionUtils.isEmpty(liveClass.getTags()) && liveClass.getTags().contains(MASTER_CLASS_TAG_PREFIX)) {
                        if (liveClass.getSlots().get(0).getSubscriptionStatus() == SubscriptionStatus.SUBSCRIBED) {
                            bookedMasterClasses.add(liveClass);
                        } else {
                            if (!CollectionUtils.isEmpty(completedClasses) && !hasAttendedDoctorClass.get()) {
                                if (productCode.equals(SUGARFIT_SPECIAL_PACK_1M_TELUGU)) {
                                    if (liveClass.getTags().contains(MASTER_CLASS_DOCTOR_TELUGU)) {
                                        doctorClass.set(liveClass);
                                    }
                                } else if (liveClass.getTags().contains(MASTER_CLASS_DOCTOR)) {
                                    doctorClass.set(liveClass);
                                }
                            }
                            if (!hasAttendedNutritionClass.get()) {
                                if (productCode.equals(SUGARFIT_SPECIAL_PACK_1M_TELUGU)) {
                                    if (liveClass.getTags().contains(MASTER_CLASS_NUTRITION_TELUGU)) {
                                        nutritionClass.set(liveClass);
                                    }
                                } else if (liveClass.getTags().contains(MASTER_CLASS_NUTRITION)) {
                                    nutritionClass.set(liveClass);
                                }
                            }
                            if (liveClass.getTags().contains(MASTER_CLASS_JUICE) && !hasAttendedActiveFoodsClass.get()) {
                                activeFoodsClass.set(liveClass);
                            }
                            if (liveClass.getTags().contains(MASTER_CLASS_FITNESS) && !hasAttendedFitnessClass.get()) {
                                fitnessClass.set(liveClass);
                            }
                            if (liveClass.getTags().contains(MASTER_CLASS_GENERIC)
                                    && hasAttendedDoctorClass.get() && hasAttendedNutritionClass.get()
                                    && (hasAttendedActiveFoodsClass.get() || hasAttendedFitnessClass.get())) {
                                masterClassesGeneric.add(liveClass);
                            }
                        }

                    }
                });

                if (!CollectionUtils.isEmpty(bookedMasterClasses)) {
                    SfMasterLiveClassWidget masterLiveClassWidget = new SfMasterLiveClassWidget();
                    Integer totalDays = switch (productCode) {
                        case SUGARFIT_SPECIAL_PACK_7D_OFFLINE -> 7;
                        case SUGARFIT_SPECIAL_PACK_3M, SUGARFIT_SUGAR_CONTROL_RENEW_3M -> 90;
                        case SUGARFIT_SUGAR_CONTROL_RENEW_6M, SUGARFIT_SUGAR_CONTROL_RENEW_6M_CGM -> 180;
                        case SUGARFIT_SUGAR_CONTROL_RENEW_12M, SUGARFIT_SUGAR_CONTROL_RENEW_12M_CGM -> 360;
                        default -> 30;
                    };
                    masterLiveClassWidget.setWidgetTitlePart1(String.format("%d Days", totalDays));
                    masterLiveClassWidget.setTotalClasses(totalDays);
                    masterLiveClassWidget.setCompletedClasses(completedClasses.size());

                    bookedMasterClasses.forEach(masterClass -> {
                        if (masterClass != null) {
                            masterLiveClassWidget.addClassCard(userContext, masterClass, true);
                        }
                    });
                    if (doctorClass.get() != null) {
                        masterLiveClassWidget.addClassCard(userContext, doctorClass.get(), false);
                    }
                    if (nutritionClass.get() != null) {
                        masterLiveClassWidget.addClassCard(userContext, nutritionClass.get(), false);
                    }
                    if (activeFoodsClass.get() != null) {
                        masterLiveClassWidget.addClassCard(userContext, activeFoodsClass.get(), false);
                    }
                    if (fitnessClass.get() != null) {
                        masterLiveClassWidget.addClassCard(userContext, fitnessClass.get(), false);
                    }
                    if (!CollectionUtils.isEmpty(masterClassesGeneric)) {
                        masterClassesGeneric.forEach(masterClass -> {
                            if (masterClass != null) {
                                masterLiveClassWidget.addClassCard(userContext, masterClass, true);
                            }
                        });
                    }

                    if (!CollectionUtils.isEmpty(masterLiveClassWidget.getClassCardList())) {
                        return masterLiveClassWidget;
                    }
                }

            }
        } catch (Exception e) {
            String msg = String.format("Error on fetching live classes for user :: %s", userContext.getUserProfile().getUserId());
            exceptionReportingService.reportException(msg, e);
        }
        return null;
    }

    public boolean isRenewalReportDisabledUser(UserContext userContext) {
        try {
//            DeviceDetailEntry device = deviceService.getDeviceByDeviceId(userContext.getSessionInfo().getDeviceId(), AppUtil.getAppTenantFromUserContext(userContext)).get();
//            float osVersion = Float.parseFloat(device.getOsVersion());
            return userContext.getSessionInfo().getAppVersion() != null
                    && userContext.getSessionInfo().getAppVersion() >= 8.87f
                    && userContext.getSessionInfo().getAppVersion() < 8.88f
                    && userContext.getSessionInfo().getOsName().equalsIgnoreCase("ios");
        } catch (Exception e) {
            return false;
        }
    }

    public boolean getUserNfcStatus(UserContext userContext) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            return chsClient.getUserNfcStatus(userId).getUserNfcStatus();
        } catch (Exception e) {
            String errorMessage = String.format("Error in fetching user nfc status :: %s", userId);
            log.error(errorMessage, e);
            return true;
        }
    }

    public boolean userHasTeluguLanguagePreference(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        UserPreferencePojo languagePreferences = serviceInterfaces.getSfAlbusClient().getUserPreference(
                userId, UserPreferencePojo.PreferenceType.META, "language");
        if (languagePreferences != null && !CollectionUtils.isEmpty(languagePreferences.getPreferenceTypeValues())) {
            return languagePreferences.getPreferenceTypeValues().contains("Telugu");
        }

        return false;
    }

    public boolean hasAtleastOneSmartScaleReading(UserContext userContext) {
        AtomicBoolean hasReading = new AtomicBoolean(false);
        try {
            if (isSmartScaleDelivered(userContext)) {
                long userId = Long.parseLong(userContext.getUserProfile().getUserId());
                TimeZone userTimezone = getUserTimezone(userContext);
                SmartScaleLogsResponse smartScaleLogsResponse = chsClient.getLastSmartScaleLogs(userId, userTimezone);
                if (Objects.nonNull(smartScaleLogsResponse)) {
                    hasReading.set(true);
                }
            }
        } catch (Exception e) {
            log.info("hasAtleastOneSmartScaleReading :: exception");
            exceptionReportingService.reportException(e);
        }

        return hasReading.get();
    }

    public boolean isSmartScaleDelivered(UserContext userContext) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            if (isSmartScaleLinkingSupportedAppVersion(userContext)) {
                UserProductOrderSummary userProductOrderSummary = indusClient.getUserOrderSummaryByProductCode(userId, E_COM_SMART_SCALE_PRODUCT);
                if (Objects.nonNull(userProductOrderSummary)) {
                    return userProductOrderSummary.getIsAnyProductDelivered();
                }
            }
        } catch (Exception e) {
            String message = String.format("isSmartScaleDelivered error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
        }
        return false;
    }

    public boolean checkIfProductIsDelivered(UserContext userContext, String productCode) {
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        try {
            if (isSmartScaleLinkingSupportedAppVersion(userContext)) {
                UserProductOrderSummary userProductOrderSummary = indusClient.getUserOrderSummaryByProductCode(userId, productCode);
                if (Objects.nonNull(userProductOrderSummary)) {
                    return userProductOrderSummary.getIsAnyProductDelivered();
                }
            }
        } catch (Exception e) {
            String message = String.format("checkIfProductIsDelivered error, userId :: %s, error :: %s", userId, e.getMessage());
            log.error(message, e);
        }
        return false;
    }

    public CompletableFuture<BaseWidgetNonVM> getSmartScaleIntegrationBannerWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            if (isSmartScaleDelivered(userContext)
                    && !hasAtleastOneSmartScaleReading(userContext)) {
                return getSmartScaleIntegrationBanner();
            } else {
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfBannerCarouselWidget getSmartScaleIntegrationBanner() {
        SfBannerCarouselWidget sfBannerCarouselWidget = new SfBannerCarouselWidget();
        List<BannerItem> bannerItemList = new ArrayList<>();

        try {
            Action action = new Action();
            action.setUrl("curefit://sfweighingscalereaderpage");
            action.setActionType(ActionType.NAVIGATION);
            BannerItem bannerItem = new BannerItem();
            bannerItem.setImage("image/chroniccare/marketing/banners/home/<USER>/smart_scale_integration_v2.png");
            bannerItem.setAction(action);
            bannerItemList.add(bannerItem);
            sfBannerCarouselWidget.setData(bannerItemList);

            Map<String, Object> layoutProps = new HashMap<>();
            layoutProps.put("roundedCorners", true);
            layoutProps.put("bannerHeight", 149);
            layoutProps.put("bannerWidth", 335);
            layoutProps.put("bannerOriginalHeight", 149);
            layoutProps.put("bannerOriginalWidth", 335);
            layoutProps.put("verticalPadding", 30);
            layoutProps.put("showPagination", false);
            sfBannerCarouselWidget.setLayoutProps(layoutProps);
        } catch (Exception e) {
            log.error("Error in smart scale integration banner", e);
            exceptionReportingService.reportException(e);
            return null;
        }

        if (CollectionUtils.isEmpty(bannerItemList)) return null;

        return sfBannerCarouselWidget;
    }


    public static String getCgmStorePageLink(UserContext userContext) {
        if (ChronicCareAppUtil.isCgmCombinedCartSupportedAppVersion(userContext) && ChronicCareAppUtil.isSfStoreTabEnabledUser(userContext)) {
            return "curefit://sfecommercepdp?category=CGM_STORE";
        }
        return "curefit://cgmstorepage";
    }

    public Action buildStoreAddToCartActionFromPartialAction(UserContext userContext, Action partialAction, SfECommercePDPV2Builder sfECommercePDPV2Builder) {
        if (Objects.nonNull(partialAction)) {
            if (partialAction.getActionType().equals(ActionType.SF_ADD_TO_CART)) {
                HashMap<String, Object> givenMeta = (HashMap<String, Object>) partialAction.getMeta();
                if (givenMeta.containsKey("productId")) {
                    String productId = (String) givenMeta.get("productId");
                    HashMap<String, String> queryParams = new HashMap<>() {{
                        put("productId", productId);
                        put("isNfc", "false");
                    }};
                    SfECommercePDPView pdpView = sfECommercePDPV2Builder.buildView(userContext, queryParams);
                    if (Objects.nonNull(pdpView) && Objects.nonNull(pdpView.getProduct())) {
                        HashMap<String, Object> meta = new HashMap<>();
                        meta.put("product", pdpView.getProduct());
                        return Action.builder().title(partialAction.getTitle()).actionType(ActionType.SF_ADD_TO_CART_VIA_MODAL).meta(meta).build();
                    }
                }

            }
            return partialAction;
        }
        return null;
    }

    public List<ProductResponse> filterProductsBasedOnUser(UserContext userContext, List<ProductResponse> givenProductResponses, boolean shouldAvoidCgmProductFiltering) {
        if (!CollectionUtils.isEmpty(givenProductResponses)) {
            List<String> productsTobeFiltered = new ArrayList<>() {{
                add("ALL_AF_COMBO");
                add("AF_COMBO_NO_PROTEIN");
            }};
            if (!isStoreRCTComboEnabledUser(userContext)) {
                productsTobeFiltered.add("RCT_ACV_JUICE_COMBO");
            }
            if (!isCgmCombinedCartSupportedAppVersion(userContext) && !shouldAvoidCgmProductFiltering) {
                givenProductResponses.forEach(p -> {
                    if (ProductCategory.CGM_STORE.equals(p.getProductEntry().getCategory())
                            || ProductSubCategory.CGM_NFC_STORE.name().equals(p.getProductEntry().getSubCategory())
                            || ProductSubCategory.CGM_PHLEBO_STORE.name().equals(p.getProductEntry().getSubCategory())
                    ) {
                        productsTobeFiltered.add(p.getProductEntry().getProductCode());
                    }
                });
            }
            return new ArrayList<>(givenProductResponses.stream().filter(p -> !productsTobeFiltered.contains(p.getProductEntry().getProductCode())).toList());
        }
        return givenProductResponses;
    }

    public SfDeliveryTrackingDetails getDiagnosticsTracking(String userId, Long bookingId) {
        DiagnosticsOrderTrackingResponse diagnosticsOrderTrackingResponse = serviceInterfaces.getSfAlbusClient().fetchDiagnosticOrderTracking(bookingId, userId);
        if (Objects.nonNull(diagnosticsOrderTrackingResponse) && !CollectionUtils.isEmpty(diagnosticsOrderTrackingResponse.getStatusProgresses())) {
            SfDeliveryTrackingDetails sfDeliveryTrackingDetails = new SfDeliveryTrackingDetails();
            sfDeliveryTrackingDetails.setModalTitle("Tracking Details");
            sfDeliveryTrackingDetails.setTitle("Diagnostic Tests");
            sfDeliveryTrackingDetails.setTrackingIdKey("Booking ID:");
            sfDeliveryTrackingDetails.setTrackingId(bookingId.toString());
            List<SfDeliveryTrackingDetails.TrackingState> trackingStates = new ArrayList<>();
            diagnosticsOrderTrackingResponse.getStatusProgresses().forEach(statusProgress -> {
                SfDeliveryTrackingDetails.TrackingState trackingState = new SfDeliveryTrackingDetails.TrackingState();
                trackingState.setStatusText(statusProgress.getStatus());
                trackingState.setTime(Objects.nonNull(statusProgress.getCompletedOn()) ? statusProgress.getCompletedOn(): statusProgress.getExpectedCompletionTime());
                trackingState.setIsComplete(Objects.nonNull(statusProgress.getCompletedOn()));
                trackingState.setIsEstimatedTime(Objects.nonNull(statusProgress.getExpectedCompletionTime()));
                trackingStates.add(trackingState);
            });
            sfDeliveryTrackingDetails.setTrackingStates(trackingStates);
            return sfDeliveryTrackingDetails;
        }
        return null;
    }

    public Boolean isCommunityWelcomeModalShown(UserContext userContext) {
        try {
            UserAttributesResponse userAttributesResponse = serviceInterfaces.userAttributesClient.getAttributes(
                    Long.parseLong(userContext.getUserProfile().getUserId()), "sf-seen-community-welcome-modal",
                    AppUtil.getAppTenantFromUserContext(userContext), null);
            if (null == userAttributesResponse || null == userAttributesResponse.getAttributes()
                    || !userAttributesResponse.getAttributes().containsKey("sf-seen-community-welcome-modal")
                    || userAttributesResponse.getAttributes().get("sf-seen-community-welcome-modal") == null) {
                return false;
            } else {
                return Boolean.valueOf(
                        userAttributesResponse.getAttributes().get("sf-seen-community-welcome-modal").toString());
            }
        } catch (Exception e) {
            log.error(String.format("Exception in fetching user attributes for userId %s",
                    userContext.getUserProfile().getUserId()));
            exceptionReportingService.reportException("Exception in fetching user attributes", e);
        }
        return false;
    }

    public Boolean isUnlockFbvExpModalViewed(UserContext userContext) {
        try {
            UserAttributesResponse userAttributesResponse = serviceInterfaces.userAttributesClient.getAttributes(
                    Long.parseLong(userContext.getUserProfile().getUserId()), UNLOCK_FBV_EXP_USER_ATTRIBUTE_FLAG,
                    AppUtil.getAppTenantFromUserContext(userContext), null);
            if (null == userAttributesResponse || null == userAttributesResponse.getAttributes()
                    || !userAttributesResponse.getAttributes().containsKey(UNLOCK_FBV_EXP_USER_ATTRIBUTE_FLAG)
                    || userAttributesResponse.getAttributes().get(UNLOCK_FBV_EXP_USER_ATTRIBUTE_FLAG) == null) {
                return false;
            } else {
                return Boolean.valueOf(
                        userAttributesResponse.getAttributes().get(UNLOCK_FBV_EXP_USER_ATTRIBUTE_FLAG).toString());
            }
        } catch (Exception e) {
            log.error(String.format("Exception in fetching user attributes for userId %s",
                    userContext.getUserProfile().getUserId()));
            exceptionReportingService.reportException("Exception in fetching user attributes", e);
        }
        return false;
    }

    public Boolean isCommunitySendIntroModalShown(UserContext userContext) {
        try {
            UserAttributesResponse userAttributesResponse = serviceInterfaces.userAttributesClient.getAttributes(
                    Long.parseLong(userContext.getUserProfile().getUserId()), "sf-seen-community-send-intro-modal",
                    AppUtil.getAppTenantFromUserContext(userContext), null);
            if (null == userAttributesResponse || null == userAttributesResponse.getAttributes()
                    || !userAttributesResponse.getAttributes().containsKey("sf-seen-community-send-intro-modal")
                    || userAttributesResponse.getAttributes().get("sf-seen-community-send-intro-modal") == null) {
                return false;
            } else {
                return Boolean.valueOf(
                        userAttributesResponse.getAttributes().get("sf-seen-community-send-intro-modal").toString());
            }
        } catch (Exception e) {
            log.error(String.format("Exception in fetching user attributes for userId %s",
                    userContext.getUserProfile().getUserId()));
            exceptionReportingService.reportException("Exception in fetching user attributes", e);
        }
        return false;
    }

    public static boolean isNonNfcAndNonTierOneUser(UserContext userContext, AlbusClient sfAlbusClient, ChronicCareServiceHelper chronicCareServiceHelper) {
        try {
            boolean isTierOneUser = false;
            UserPreferencePojo userPreferencePojo = sfAlbusClient.getUserPreference(
                    userContext.getUserProfile().getUserId(),
                    UserPreferencePojo.PreferenceType.META,
                    "cgmServiceAvailable"
            );
            if (Objects.nonNull(userPreferencePojo) && org.apache.commons.collections.CollectionUtils.isNotEmpty(userPreferencePojo.getPreferenceTypeValues())) {
                isTierOneUser = Boolean.parseBoolean(userPreferencePojo.getPreferenceTypeValues().get(0));
            }
            boolean isNfcUser = chronicCareServiceHelper.getUserNfcStatus(userContext);
            return !isNfcUser && !isTierOneUser;
        } catch (Exception e) {
            log.error("Error checking user eligibility", e);
        }
        return false;
    }

    public boolean isTierTwoUser(UserContext userContext) {
        try {
            boolean isTierOneUser = false;
            UserPreferencePojo userPreferencePojo = serviceInterfaces.getSfAlbusClient().getUserPreference(
                    userContext.getUserProfile().getUserId(),
                    UserPreferencePojo.PreferenceType.META,
                    "cgmServiceAvailable"
            );
            if (Objects.nonNull(userPreferencePojo) && org.apache.commons.collections.CollectionUtils.isNotEmpty(userPreferencePojo.getPreferenceTypeValues())) {
                isTierOneUser = Boolean.parseBoolean(userPreferencePojo.getPreferenceTypeValues().get(0));
            }
            return !isTierOneUser;
        } catch (Exception e) {
            log.error("Error checking user eligibility", e);
        }
        return false;
    }

    public boolean isQuickCommerceOrShopifyPackUser(UserContext userContext) {
        try {
            Optional<ActivePackResponse> activePackResponse =
                    userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());

            return activePackResponse
                    .map(ActivePackResponse::getBundleProduct)
                    .map(bundleProduct ->
                            isQuickCommerceCGMPack(bundleProduct) || isShopifyCGMPack(bundleProduct))
                    .orElse(false);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public List<String> PCOSStoreCategoriesOrdering() {
        List<String> categoriesOrder = List.of("CGM_STORE", "BODY_RESET_KIT", "DEVICE", "MEDICINES", "BEVERAGES", "FOOD");
        return categoriesOrder;
    }

    public boolean isPCOSPack(String productCode) {
        if (productCode != null) {
            return PCOS_6_MONTHS.equals(productCode);
        }
        return false;
    }

    public boolean isPCOSPackUser(UserContext userContext) {
        try {
            Optional<ActivePackResponse> activePackResponse =
                    userOnboardingService.getSugarFitActivePackForHome(userContext.getUserProfile().getUserId());

            if (activePackResponse.isEmpty() || activePackResponse.get().getBundleProduct() == null) {
                return false;
            }

            return activePackResponse
                    .map(ActivePackResponse::getBundleProduct)
                    .map(bundleProduct -> isPCOSPack(bundleProduct.getProductCode()))
                    .orElse(false);
        } catch (Exception e) {
            exceptionReportingService.reportException(e);
        }
        return false;
    }

    public CompletableFuture<SfHomeFBVWidget> getSfHomeFBVWidgetFuture(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus,
                                                                       ActivePackResponse activePackResponse,
                                                                       FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse) {
        return supplyAsync(() -> getSfHomeFBVWidget(userContext, cgmOnboardingStatus, activePackResponse, faceBasedVitalScansForDayResponse), serviceInterfaces.getTaskExecutor());
    }

    public SfHomeFBVWidget getSfHomeFBVWidget(UserContext userContext, CgmOnboardingStatusResponse cgmOnboardingStatus,
                                              ActivePackResponse activePackResponse, FaceBasedVitalScansForDayResponse faceBasedVitalScansForDayResponse){
        SfHomeFBVWidget sfHomeFBVWidget = null;
        if (shouldShowFBVLockedWidget(userContext, cgmOnboardingStatus)) {
            try {
                List<CGMDeviceInfo> deviceInfoList = cgmOnboardingStatus.getCgmDeviceInfos();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(deviceInfoList)) {
                    CGMDeviceInfo latestCgmDevice = deviceInfoList.get(deviceInfoList.size() - 1);
                    Date cgmStartDate = latestCgmDevice.getStartedOn();
                    Date cgmEndingDate = SfDateUtils.addDays(cgmStartDate, 14L);
                    Date userDate = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext)).getTime();
                    if(SfDateUtils.isFutureDate(userContext, cgmEndingDate) && SfDateUtils.getDifferenceDays(userDate, cgmEndingDate) < 5){
                        sfHomeFBVWidget = new SfHomeFBVWidget(cgmEndingDate.getTime());
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching sfFBVLockedWidget", e);
            }
        }

        if ((isFaceScanTimeHandledAppVersion(userContext)
                && isFaceBasedVitalsEligibleForUser(userContext, cgmOnboardingStatus,
                activePackResponse)) || isFaceBasedVitalsEnabledForUser(userContext, cgmOnboardingStatus,
                activePackResponse, faceBasedVitalScansForDayResponse)) {
            TimeZone userTimeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            Long trendsStartTime = SfDateUtils.atStartOfWeek(userTimeZone);
            Long trendsEndTime = SfDateUtils.atEndOfWeek(userTimeZone);
            sfHomeFBVWidget = new SfHomeFBVWidget(Objects.nonNull(faceBasedVitalScansForDayResponse) && Objects.nonNull(faceBasedVitalScansForDayResponse.getNextScanTime())
                    ? faceBasedVitalScansForDayResponse.getNextScanTime().getTime() : null);
            sfHomeFBVWidget.setTitle("Your");
            sfHomeFBVWidget.setBoldTitle(" Face Scan");
            sfHomeFBVWidget.setHeaderAction(Action.builder().actionType(ActionType.NAVIGATION).title("View Details").url("curefit://fbvhistorypage?tab=logs").isEnabled(true).build());
            sfHomeFBVWidget.setTrendsStartTime(trendsStartTime);
            sfHomeFBVWidget.setTrendsEndTime(trendsEndTime);

            try {
                Date currentTimeStamp = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext)).getTime();
                FaceBasedVitalLogsResponse lastScanResult = this.serviceInterfaces.chsClient.getLastScanFaceBasedVitalLogs(userId, userTimeZone);
                if(lastScanResult != null && SfDateUtils.isSameDay(currentTimeStamp, lastScanResult.getTimeStamp())){
                    sfHomeFBVWidget.setTodayScanResults(lastScanResult);
                } else {
                    SfHomeFBVWidget.TodayScanPending todayScanPending = new SfHomeFBVWidget.TodayScanPending();
                    todayScanPending.setTitle("Scan your face");
                    todayScanPending.setSubTitle("Get your health report in just 60 seconds");
//                todayScanPending.setStreakText("3/7 Days Streaks");
                    todayScanPending.setScanAction(Action.builder().actionType(ActionType.NAVIGATION).title("Scan My Face").url("curefit://sfscanfacepage").isEnabled(true).build());
                    sfHomeFBVWidget.setTodayScanPending(todayScanPending);
                }

                FaceBasedVitalsRequest fbvRequest = new FaceBasedVitalsRequest();
                fbvRequest.setUserId(userId);
                fbvRequest.setStartTimeEpoch(trendsStartTime);
                fbvRequest.setEndTimeEpoch(trendsEndTime);

                List<FaceBasedVitalTrendsResponse> fbvTrends = chsClient.getFaceBasedVitalTrends(fbvRequest, userTimeZone);
                List<FaceBasedVitalMetricConfigResponse> config = chsClient.getFaceBasedVitalConfigurations();

                List<SfHomeFBVWidget.VitalData> trendsData = new ArrayList<>();

                fbvTrends.forEach(metricTrends -> {
                    FaceBasedVitalMetricConfigResponse metricConfigResponse = config.stream().filter(metricConfig ->
                            metricConfig.getMetricName().equals(metricTrends.getMetricName())).toList().get(0);
                    SfHomeFBVWidget.VitalData vitalData = new SfHomeFBVWidget.VitalData();
                    String metricName = metricTrends.getMetricName();
                    vitalData.setMetricName(metricName);
                    vitalData.setMetricLabel(getWidgetTitleFromMetricName(metricName));
                    if (metricTrends.getMetricName().equals("fbv_stress_status")) {
                        vitalData.setStressStatusDataPoints(metricTrends.getStressStatusDataPoints());
                    } else {
                        FaceBasedVitalRange vitalGoodRange = metricConfigResponse.getFaceBasedVitalRanges().stream().filter(metricConfig ->
                                metricConfig.getRangeCategory().equals(FaceBasedVitalRangeCategory.GOOD)).toList().get(0);
                        Long lowerBound = Long.valueOf(vitalGoodRange.getLowerBound());
                        Long upperBound = Long.valueOf(vitalGoodRange.getUpperBound());
                        vitalData.setAverage(metricTrends.getAverage());
                        vitalData.setDataPoints(metricTrends.getDataPoints());
                        vitalData.setGraphMax(getGraphMax(metricTrends, upperBound));
                        vitalData.setGraphMin(getGraphMin(metricTrends, lowerBound));
                        vitalData.setHealthyLowerBound(lowerBound);
                        vitalData.setHealthyUpperBound(upperBound);
                        vitalData.setShowHealthyRange(getShowHealthyRange(metricName));
                        vitalData.setGraphType(getGraphType(metricName));
                    }

                    trendsData.add(vitalData);
                });
                sfHomeFBVWidget.setCurrentWeekTrendsData(trendsData);
            } catch (Exception e) {
                exceptionReportingService.reportException("Error in fetching SfHomeFBVWidget", e);
            }

            if (isUnlimitedFaceScanEnabledUser(userContext)) {
                long passedTs = Calendar.getInstance(ChronicCareAppUtil.getUserTimezone(userContext)).getTime().getTime() - 10000L;
                sfHomeFBVWidget.setUnlockingTimeEpoch(passedTs);
                sfHomeFBVWidget.setTodayScanResults(null);
                SfHomeFBVWidget.TodayScanPending todayScanPending = new SfHomeFBVWidget.TodayScanPending();
                todayScanPending.setTitle("Scan your face");
                todayScanPending.setSubTitle("Get your health report in just 60 seconds");
                todayScanPending.setScanAction(Action.builder().actionType(ActionType.NAVIGATION).title("Scan My Face").url("curefit://sfscanfacepage").isEnabled(true).build());
                sfHomeFBVWidget.setTodayScanPending(todayScanPending);
            }
        }
        return sfHomeFBVWidget;
    }

    public CommunityUserInfo fetchUserInfo(Long userId, UserType userType) {
        CommunityUserInfo data = new CommunityUserInfo();
        data.setUserType(userType.name());
        try {
            if (userType.equals(UserType.USER)) {
                try {
                    UserEntry userEntry = userServiceClient.getUser(String.valueOf(userId)).get();
                    if (userEntry != null) {
                        data.setName((userEntry.getFirstName() != null ? userEntry.getFirstName() : "") + " " + (userEntry.getLastName() != null ? userEntry.getLastName() : ""));
                        data.setImageUrl(userEntry.getProfilePictureUrl());
                        data.setGender(userEntry.getGender());
                    }
                } catch (Exception e) {
                    //Ignore
                }
            } else if (userType.equals(UserType.COACH)) {
                try {
                    AgentResponse agentResponse = serviceInterfaces.getOllivanderAgentClient().getAgent(userId);
                    if (agentResponse != null) {
                        data.setName(agentResponse.getName());
                        data.setImageUrl(agentResponse.getDisplayImage());
                        data.setGender(String.valueOf(agentResponse.getGender()));
                    }
                } catch (Exception e) {
                    //Ignore
                }
            }
        } catch (Exception e) {
            log.info(e.getMessage());
            return data;
        }
        return data;
    }

    public String getUserDietPlanPdfUrl(UserContext userContext){
        try {
            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            UserMealPlanPdfResponse pdfResponse = this.serviceInterfaces.ambrosiaClient.fetchDailyUserMealPlanPdf(patientDetail.getId(), null, AppUtil.getAppTenantFromUserContext(userContext), TimeZone.getTimeZone(userContext.getUserProfile().getTimezone()));
            if(pdfResponse!=null){
                return pdfResponse.getUrl();
            }
        } catch (Exception e){
            log.info(e.getMessage());
        }
        return null;
    }

    public boolean isDietPlanPdfGeneratedForUser(UserContext userContext) {
        try {
            PatientDetail patientDetail = chronicCarePatientService.getChronicCarePatientForUserId(userContext);
            UserMealPlanEntry userMealPlan = this.serviceInterfaces.ambrosiaClient.getUserMealPlan(patientDetail.getId());
            return Objects.nonNull(userMealPlan) && !CollectionUtils.isEmpty(userMealPlan.getMeals());
        } catch (Exception e) {
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return false;
    }

    public boolean isDietPlanPdfGeneratedForForDigitalAppUser(ServiceInterfaces serviceInterfaces, UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            boolean isGenerated = isDietPlanPdfGeneratedForUser(userContext);
            if (isDietPlanRequestedByDigitalAppUser(serviceInterfaces, userContext)) {
                UserAttributesResponse digitalDietPlanShowTime = serviceInterfaces.getUserAttributesClient().getAttributes(userId,
                        DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE, getAppTenantFromUserContext(userContext), null);
                if (digitalDietPlanShowTime.getAttributes().containsKey(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE)
                        && Objects.nonNull(digitalDietPlanShowTime.getAttributes().get(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE))) {
                    long showTime = Long.parseLong((String) digitalDietPlanShowTime.getAttributes().get(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE));
                    Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
                    long currentTime = calendar.getTime().getTime()/1000; // Stored in seconds
                    if (currentTime > showTime) {
                        return isGenerated;
                    }
                }
            } else return isGenerated;
        } catch (Exception e) {
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return false;
    }

    public String getPlanPdfIfDietPlanAvailableForDigitalAppUser(ServiceInterfaces serviceInterfaces, UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            String dietPlanPdfUrl = getUserDietPlanPdfUrl(userContext);
            if (isDietPlanRequestedByDigitalAppUser(serviceInterfaces, userContext)) {
                UserAttributesResponse digitalDietPlanShowTime = serviceInterfaces.getUserAttributesClient().getAttributes(userId,
                        DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE, getAppTenantFromUserContext(userContext), null);
                if (digitalDietPlanShowTime.getAttributes().containsKey(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE)
                        && Objects.nonNull(digitalDietPlanShowTime.getAttributes().get(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE))) {
                    long showTime = Long.parseLong((String) digitalDietPlanShowTime.getAttributes().get(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE));
                    Calendar calendar = Calendar.getInstance(getUserTimezone(userContext));
                    long currentTime = calendar.getTime().getTime()/1000; // Stored in seconds
                    if (dietPlanPdfUrl != null && currentTime > showTime) {
                        return dietPlanPdfUrl;
                    }
                }
            } else if (dietPlanPdfUrl != null) {
                return dietPlanPdfUrl;
            }
        } catch (Exception e) {
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return null;
    }

    public boolean isDietPlanRequestedByDigitalAppUser(ServiceInterfaces serviceInterfaces, UserContext userContext) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            UserAttributesResponse digitalDietPlanShowTime = serviceInterfaces.getUserAttributesClient().getAttributes(userId,
                    DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE, getAppTenantFromUserContext(userContext), null);
            return digitalDietPlanShowTime != null && digitalDietPlanShowTime.getAttributes() != null
                    && digitalDietPlanShowTime.getAttributes().containsKey(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE)
                    && Objects.nonNull(digitalDietPlanShowTime.getAttributes().get(DIGITAL_DIET_PLAN_SHOW_TIME_ATTRIBUTE));
        } catch (Exception e) {
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return false;
    }

    public CompletableFuture<Map<String, UserMetricEntry>> getDigiRiskScoreFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                return serviceInterfaces.getChsClient().getProfileRiskScoreForUserId(userId);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<SessionLogResponse> getDigiLiveSessionsFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
                AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
                Date userDayStart = TimeUtil.getStartOfDay(new Date(), userContext.getUserProfile().getTimezone());
                Date userDayEnd = TimeUtil.getEndOfDay(new Date(), userContext.getUserProfile().getTimezone());
                UserSessionLogFetchRequest userSessionLogFetchRequest = new UserSessionLogFetchRequest();
                userSessionLogFetchRequest.setEventType(UserEventType.JOINED_SESSION);
                userSessionLogFetchRequest.setSessionType(SessionType.LIVE_YT_SESSION);
                userSessionLogFetchRequest.setSortOrder(SortOrder.ASC);
                userSessionLogFetchRequest.setFromDate(userDayStart);
                userSessionLogFetchRequest.setToDate(userDayEnd);
                userSessionLogFetchRequest.setUserId(userId);
                return sfFitnessClient.fetchUserSessions(userSessionLogFetchRequest, appTenant, timeZone);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public CompletableFuture<UserTaskDaySummary> getDigiTasksToDoSummary(UserContext userContext, Long queryDateEpoch) {
        return supplyAsync(() -> {
            try {
                Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
                TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
                return smsClient.getUserTaskDaySummary(userId, queryDateEpoch, timeZone);
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public DigiPrePurchasePageView.PackPurchaseModalData getDigiPackPurchaseModalData(UserContext userContext){
        DigiPrePurchasePageView.PackPurchaseModalData packPurchaseModalData = new DigiPrePurchasePageView.PackPurchaseModalData();
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone tz = ChronicCareAppUtil.getUserTimezone(userContext);
        try{
            List<FreemiumPackResponse> digiPurchasePacks = serviceInterfaces.getSmsClient().getFreemiumSubscriptionPurchasePacks(userId, true, tz);
            if(Objects.nonNull(digiPurchasePacks)){
                CompletableFuture<PatientDetail> patientDetailFuture = chronicCarePatientService.getChronicCarePatientForUserIdFuture(userContext);
                PatientDetail patientDetail = patientDetailFuture.get(5, TimeUnit.SECONDS);
                List<RenewSubscriptionPageView.PackData> packDataList = new ArrayList<>();
                for (FreemiumPackResponse digiPurchasePack : digiPurchasePacks) {
                    BundleSellableProduct bundleSellableProduct = digiPurchasePack.getBundleProduct();
                    boolean isSugarFitInternationalUser = ChronicCareAppUtil.isInternationalSugarfitUser(userContext) || DigitalAppUtil.isDigitalUSAPack(bundleSellableProduct.getProductCode());
                    // we'll use xray config for pack textHighlightColor, gradients, tagText, showAnimatedBorder, packNudgeTitle, packBenefits
                    JsonNode packDetailsFromConfig = bundleSellableProduct.getInfoSection().get("renewalPageV2");
                    if(packDetailsFromConfig != null){
                        RenewSubscriptionPageView.PackData pack = objectMapper.convertValue(packDetailsFromConfig, RenewSubscriptionPageView.PackData.class);
                        pack.setPackCode(bundleSellableProduct.getProductCode());
                        pack.setPackTitle(bundleSellableProduct.getInfoSection().get("packTitle").asText());
                        if (isSugarFitInternationalUser) {
                            pack.setCurrency("USD");
                            if (Objects.isNull(pack.getMrp())) {
                                pack.setMrp(bundleSellableProduct.getMrp());
                            }
                            if (Objects.isNull(pack.getListingPrice())) {
                                pack.setListingPrice(bundleSellableProduct.getListingPrice());
                            }
                        } else {
                            pack.setMrp(bundleSellableProduct.getMrp());
                            pack.setListingPrice(bundleSellableProduct.getListingPrice());
                        }
                        Double listingPriceMonthly = Double.valueOf((pack.getListingPrice()) / (bundleSellableProduct.getInfoSection().get("packDurationMonths").asDouble()));
                        int packDurationMonths = bundleSellableProduct.getInfoSection().get("packDurationMonths").asInt();
                        if (packDurationMonths == 1) {
                            pack.setEmiPriceText("Just at @{boldValue} for 30 days");
                            if (isSugarFitInternationalUser) {
                                pack.setEmiPriceBoldValues(List.of(String.format("$%s",  getVedicNumberFormat(listingPriceMonthly.longValue()))));
                            } else {
                                pack.setEmiPriceBoldValues(List.of(String.format("₹%s",  getVedicNumberFormat(listingPriceMonthly.longValue()))));
                            }
                        } else {
                            pack.setEmiPriceText(String.format("Just at @{boldValue} for %d months", packDurationMonths));
                            if (isSugarFitInternationalUser) {
                                pack.setEmiPriceBoldValues(List.of(String.format("$%s/month",  getVedicNumberFormat(listingPriceMonthly.longValue()))));
                            } else {
                                pack.setEmiPriceBoldValues(List.of(String.format("₹%s/month",  getVedicNumberFormat(listingPriceMonthly.longValue()))));
                            }
                        }
                        int discount = (int) Math.round(((pack.getMrp() - pack.getListingPrice()) * 100) / pack.getMrp());
                        if (discount > 0){
                            pack.setOfferDiscount(discount);
                        }
                        String actionUrl = String.format("curefit://carecartcheckout?patientId=%s&productCode=%s&productId=%s&title=Checkout", patientDetail.getId(), bundleSellableProduct.getProductCode(), bundleSellableProduct.getProductCode());
                        if (isSugarFitInternationalUser) {
                            if (Objects.nonNull(pack.getShopifyPackCode())) {
                                String productQuantity = "1";
                                String shopifyProductCode = pack.getShopifyPackCode();
                                actionUrl = "https://be.sugarfit.com/cart/" + shopifyProductCode +  ":" + productQuantity;
                            } else {
                                actionUrl = "https://be.sugarfit.com/pages/pricing";
                            }
                        }
                        Action action = Action.builder().url(actionUrl).actionType(isSugarFitInternationalUser? ActionType.EXTERNAL_DEEP_LINK : ActionType.NAVIGATION).title("BUY NOW").build();
                        pack.setAction(action);
                        packDataList.add(pack);
                    }
                }
                packPurchaseModalData.setHeaderTitle("What’s Included");
                packPurchaseModalData.setRecommendedPacks(packDataList);
                packPurchaseModalData.setEnableTrialPackActivation(false);
                packPurchaseModalData.setPaymentDisclaimer("Unfortunately, we don't support in-app payments for these packs. Please complete the purchase on our website and come back.");
            }

        }catch (Exception e) {
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return packPurchaseModalData;
    }

    public Double getLatestMetricValue(String userId, Long metricId) {
        try {
            SearchRequestV2 searchRequestV2 = new SearchRequestV2();
            Map<SearchOperator, Map<String, Object>> query = new HashMap<>();
            Map<String, Object> param = new HashMap<>();
            param.put("metricId", metricId);
            param.put("userId", userId);
            query.put(SearchOperator.eq, param);

            searchRequestV2.setQuery(query);
            searchRequestV2.setLimit(1);
            searchRequestV2.setOffset(0);
            searchRequestV2.setSortOrder("DESC");
            searchRequestV2.setSortBy("startTime");
            List<UserMetricEntry> latestMetric = chsClient.searchUserMetricsV2(searchRequestV2);

            if (!CollectionUtils.isEmpty(latestMetric)) {
                return Double.valueOf(latestMetric.getFirst().getValue());
            }
        } catch (Exception e) {
            String errorMessage = String.format("Error is searching metric :: %s", e.getMessage());
            log.error(errorMessage, e);
        }
        return null;
    }

    public SfBannerCarouselWidget getCGMReaderDeliveryDelayBannerWidget(UserContext userContext) {
        if (ChronicCareAppUtil.isCGMReaderDeliveryDelayUser(userContext)) {
            SfBannerCarouselWidget bannerCarouselWidget = new SfBannerCarouselWidget();
            BannerItem bannerItem = new BannerItem();
            bannerItem.setImage("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/reader-delivery%20(1)-2025-03-19-12:18.png");
            bannerCarouselWidget.setData(List.of(bannerItem));
            Map<String, Object> layoutProps = new HashMap<>();
            layoutProps.put("roundedCorners", true);
            layoutProps.put("bannerHeight", 180);
            layoutProps.put("bannerWidth", 315);
            layoutProps.put("bannerOriginalHeight", 180);
            layoutProps.put("bannerOriginalWidth", 315);
            layoutProps.put("verticalPadding", 5);
            layoutProps.put("showPagination", false);
            layoutProps.put("autoScroll", false);
            bannerCarouselWidget.setLayoutProps(layoutProps);
            return bannerCarouselWidget;
        }
        return null;
    }

    public SfBannerCarouselWidget getGlucoRxInstallationDelayBannerWidget(UserContext userContext) {
        if (ChronicCareAppUtil.isGlucoRxInstallationDelaySegementUser(userContext)) {
            SfBannerCarouselWidget bannerCarouselWidget = new SfBannerCarouselWidget();
            BannerItem bannerItem = new BannerItem();
            bannerItem.setImage("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/cgm-app-update%20(1)-2025-05-23-15:11.png");
            bannerItem.setAction(Action.builder().actionType(ActionType.EXTERNAL_DEEP_LINK).url("https://onelink.to/t8ebvc").build());
            bannerCarouselWidget.setData(List.of(bannerItem));
            Map<String, Object> layoutProps = new HashMap<>();
            layoutProps.put("roundedCorners", true);
            layoutProps.put("bannerHeight", 180);
            layoutProps.put("bannerWidth", 315);
            layoutProps.put("bannerOriginalHeight", 180);
            layoutProps.put("bannerOriginalWidth", 315);
            layoutProps.put("verticalPadding", 5);
            layoutProps.put("showPagination", false);
            layoutProps.put("autoScroll", false);
            bannerCarouselWidget.setLayoutProps(layoutProps);
            return bannerCarouselWidget;
        }
        return null;
    }

    public CompletableFuture<SfBannerCarouselWidget> getRagusAiBannerWidget(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                if (ChronicCareAppUtil.isRagusAiSupported(userContext) && ChronicCareAppUtil.isRagusAIChatEnabledForUser(userContext)) {
                    SfBannerCarouselWidget bannerCarouselWidget = new SfBannerCarouselWidget();
                    BannerItem bannerItem = new BannerItem();
                    bannerItem.setImage("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/ragusAI-2025-04-17-14:51.png");
                    bannerItem.setAction(Action.builder().url("curefit://ragusaichatpage").actionType(ActionType.NAVIGATION).build());
                    bannerCarouselWidget.setData(List.of(bannerItem));
                    Map<String, Object> layoutProps = new HashMap<>();
                    layoutProps.put("roundedCorners", true);
                    layoutProps.put("bannerHeight", 100);
                    layoutProps.put("bannerWidth", 375);
                    layoutProps.put("bannerOriginalHeight", 100);
                    layoutProps.put("bannerOriginalWidth", 375);
                    layoutProps.put("verticalPadding", 5);
                    layoutProps.put("showPagination", false);
                    layoutProps.put("autoScroll", false);
                    bannerCarouselWidget.setLayoutProps(layoutProps);
                    return bannerCarouselWidget;
                }
                return null;
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public SfBannerCarouselWidget getCgmPurchaseBannerWidget(BundleProduct bundleProduct) {
        if (isQuickCommerceCGMPack(bundleProduct) || isShopifyCGMPack(bundleProduct)) {
            SfBannerCarouselWidget bannerCarouselWidget = new SfBannerCarouselWidget();
            BannerItem bannerItem = new BannerItem();
            bannerItem.setImage("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/Frame%202147201200-2025-05-27-16:47.png");
            bannerItem.setAction(Action.builder().url("curefit://sfecommercepdp?category=CGM_STORE").actionType(ActionType.NAVIGATION).build());
            bannerCarouselWidget.setData(List.of(bannerItem));
            Map<String, Object> layoutProps = new HashMap<>();
            layoutProps.put("roundedCorners", true);
            layoutProps.put("bannerHeight", 185);
            layoutProps.put("bannerWidth", 334);
            layoutProps.put("bannerOriginalHeight", 185);
            layoutProps.put("bannerOriginalWidth", 334);
            layoutProps.put("verticalPadding", 5);
            layoutProps.put("showPagination", false);
            layoutProps.put("autoScroll", false);
            bannerCarouselWidget.setLayoutProps(layoutProps);
            return bannerCarouselWidget;
        }
        return null;
    }

    public Map<String, Object> updateActionsInUpsellingDataByAddingDynamicParams(Map<String, Object> upsellingDataMap, ActivePackResponse activePackResponse) {

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            UpsellingData upsellingData = objectMapper.convertValue(upsellingDataMap, UpsellingData.class);
            if (Objects.nonNull(upsellingData) && Objects.nonNull(upsellingData.getType()) && Objects.nonNull(upsellingData.getAction())
                    && UpsellingData.UpsellingType.CONSULT.equals(upsellingData.getType())) {
                String modifiedUrl = upsellingData.getAction().getUrl() + "&patientId=" + activePackResponse.getPatientId();

                String productCode = DigitalAppUtil.getQueryParam(upsellingData.getAction().getUrl(), "productId");
                Optional<UserMembershipInfo> membershipInfo = activePackResponse.getUserMembershipInfos().stream()
                        .filter(x -> productCode.equals(x.getProductCode())).findFirst();
                if (membershipInfo.isPresent()
                        && membershipInfo.get().getTicketsConsumed() < membershipInfo.get().getTickets()) {
                    modifiedUrl = modifiedUrl + "&parentBookingId=" + activePackResponse.getBookingId();
                }
                Action givenAction = upsellingData.getAction();
                givenAction.setUrl(modifiedUrl);
                upsellingDataMap.put("action", givenAction);
            }
        } catch (Exception e) {
            log.info("Error is parsing" + e.getLocalizedMessage());
        }
        return upsellingDataMap;
    }

    public boolean isFirstCgmCompletionPending(UserContext userContext, ActivePackResponse activePackResponse) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            CgmOnboardingStatusResponse cgmOnboardingStatus = chsClient.fetchOnboardingStatus(userId, null, getAppTenantFromUserContext(userContext));
            boolean isCgmPack = !ChronicCareAppUtil.isNonCGMProduct(activePackResponse.getBundleProduct(), cgmOnboardingStatus);
            return isCgmPack && !cgmOnboardingStatus.isAtleastOneCGMCompleted();
        } catch (Exception e) {
            log.info("Error in checking isFirstCgmCompletionPending" + e);
            return false;
        }
    }

    public boolean isSecondDoctorConsultBooking(ActivePackResponse activePackResponse) {
        return isFirstDoctorConsultationBooked(activePackResponse, exceptionReportingService);
    }

    public Action getModifiedDoctorConsultActionBasedOnCgmCompletion(UserContext userContext, ActivePackResponse activePackResponse, Action action) {
        if (isDoctorConsultWarningSupportedApp(userContext)
                && isSecondDoctorConsultBooking(activePackResponse) && isFirstCgmCompletionPending(userContext, activePackResponse)) {
            Action consultWarningAction = new Action();
            consultWarningAction.setActionType(ActionType.SF_DOCTOR_CONSULT_ALERT);
            consultWarningAction.setTitle(action.getTitle());

            Map<String, Object> actionMeta = new HashMap<>();
            actionMeta.put("modalTitle", "CGM not completed ⚠️");
            actionMeta.put("alertText", "Your CGM data is not yet available in the system. Your doctor can provide better advice and adjust medication based on this data.\n\nWe recommend booking an appointment after your CGM is complete and the data is available.");
            action.setTitle("Book Anyway");
            actionMeta.put("proceedAction", action);
            consultWarningAction.setMeta(actionMeta);
            consultWarningAction.setIsEnabled(action.getIsEnabled());

            return consultWarningAction;
        }

        return action;
    }

    public CompletableFuture<BaseWidgetNonVM> getSmartScaleStatsWidgetCompletableFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                if (isSmartScaleDelivered(userContext)) {
                    if (hasAtleastOneSmartScaleReading(userContext)) {
                        return getSfSmartScaleStatsWidget(userContext);
                    } else {
                        return getSmartScaleIntegrationBanner();
                    }
                }
            } catch (Exception e) {
                exceptionReportingService.reportException(e);
            }
            return null;
        }, serviceInterfaces.getTaskExecutor());
    }

    private SfSmartScaleStatsWidget getSfSmartScaleStatsWidget(UserContext userContext) {
        try {
            SfSmartScaleStatsWidget sfSmartScaleStatsWidget = new SfSmartScaleStatsWidget();
            long userId = Long.parseLong(userContext.getUserProfile().getUserId());
            TimeZone userTimezone = ChronicCareAppUtil.getUserTimezone(userContext);
            SmartScaleLogsRequest smartScaleLogsRequest = new SmartScaleLogsRequest();
            smartScaleLogsRequest.setUserId(userId);
            smartScaleLogsRequest.setStartTimeEpoch(SmartScaleVitalsListPageBuilder.atStartOfAPastDay(userTimezone, 6));
            smartScaleLogsRequest.setEndTimeEpoch(SmartScaleVitalsListPageBuilder.atEndOfDay(userTimezone));
            SmartScaleLogsResponse smartScaleLogsResponse = chsClient.getLastSmartScaleLogs(userId, userTimezone);
            HashMap<SmartScaleMetricName, MetricEntry> metricDetailsMap =  chsClient.getAllSmartScaleMetrics();
            if (Objects.nonNull(smartScaleLogsResponse)) {
                List<SmartScaleLogsResponse> scaleLogsResponses = new ArrayList<>();
                scaleLogsResponses.add(smartScaleLogsResponse);
                sfSmartScaleStatsWidget.setMetrics(SmartScaleVitalsListPageBuilder.buildMetricList(scaleLogsResponses, metricDetailsMap));
                sfSmartScaleStatsWidget.setTitle("Your ");
                sfSmartScaleStatsWidget.setBoldTitle("Smart Scale Vitals");
                sfSmartScaleStatsWidget.setHeaderAction(Action.builder().actionType(ActionType.NAVIGATION).url("curefit://sfweighingscalereadingtimelinepage").title("View All").build());
                return sfSmartScaleStatsWidget;
            }
        } catch (Exception e) {
            log.error("Error in fetching sfSmartScaleStatsWidget", e);
        }
        return null;
    }


    public CompletableFuture<DigiLessonsCarouselWidget> getDigiLessonsCarouselWidgetFuture(UserContext userContext, boolean isTrialPackExpired) {
        return supplyAsync(() -> {
            try {
                return getDigiLessonsCarouselWidget(userContext, isTrialPackExpired);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getDigiLessonsCarouselWidgetFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private DigiLessonsCarouselWidget getDigiLessonsCarouselWidget(UserContext userContext, boolean isTrialPackExpired){
        Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
        TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
        AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
        try {
            BookComposition book = catalogClient.getActiveBookForUserId(userId, appTenant, timeZone);
            if (book!=null) {
                BookEntry bookEntry = book.getBookEntry();
                Map<String, Object> bookMetaData = bookEntry.getMetaData();
                String bookImageCreative = bookMetaData.get("bookImageCreative").toString();

                DigiLessonsCarouselWidget.LessonCard videoLessonCard = null;
                if (bookMetaData.containsKey("bookVideoCreative") && bookMetaData.get("bookVideoCreative") != null &&
                        bookMetaData.containsKey("bookVideoUrl") && bookMetaData.get("bookVideoUrl") != null) {
                    List<ChapterComposition> videoChapterComposition = book.getChapterCompositionList().stream().filter(cc -> cc.getChapter().getChapterType() == ChapterType.VIDEO).toList();

                    String bookVideoCreative = bookMetaData.get("bookVideoCreative").toString();
                    String bookVideoUrl = bookMetaData.get("bookVideoUrl").toString();

                    videoLessonCard = new DigiLessonsCarouselWidget.LessonCard(DigiLessonsCarouselWidget.CardType.VIDEO_LESSON);
                    videoLessonCard.setCardImgUrl(bookVideoCreative);
                    Action videoLessonAction = new Action();
                    videoLessonAction.setActionType(NAVIGATION);
                    if(videoChapterComposition.isEmpty() || !ChronicCareAppUtil.isInteractiveVideoEnabledAppVersion(userContext)) {
                        videoLessonAction.setUrl(String.format("curefit://singlevideoplayer?bookId=%d&videoUrl=%s&videoOrientation=LANDSCAPE", bookEntry.getBookId(), bookVideoUrl));
                    } else {
                        videoLessonAction.setUrl(String.format("curefit://sfdigiinteractivevideopage?bookId=%d", bookEntry.getBookId()));
                    }
                    videoLessonAction.setTitle("Today's Lesson");
                    videoLessonCard.setLessonAction(videoLessonAction);
                    videoLessonCard.setBookId(bookEntry.getBookId());
                }

                DigiLessonsCarouselWidget.LessonCard bookLessonCard = new DigiLessonsCarouselWidget.LessonCard(DigiLessonsCarouselWidget.CardType.BOOK_LESSON);
                bookLessonCard.setCardImgUrl(bookImageCreative);

                TreeMap<Integer, PageTracking> chapterPageMap = book.getUserChapterTrackingEntry().getChapterTracking();
                int completedChapters = 0;
                int totalChapters = chapterPageMap.size();
                for(PageTracking chapterPageTracking : chapterPageMap.values()){
                    if (chapterPageTracking.getIsCompleted()) {
                        completedChapters++;
                    }
                }
                bookLessonCard.setBookId(bookEntry.getBookId());
                bookLessonCard.setLessonTitle(bookEntry.getTitle());
                bookLessonCard.setBookCoverUrl(org.apache.commons.collections.CollectionUtils.isNotEmpty(bookEntry.getImageUrls()) ? bookEntry.getImageUrls().getFirst() : "");
                Action bookLessonAction = new Action();
                bookLessonAction.setActionType(NAVIGATION);
                bookLessonAction.setUrl(String.format("curefit://digibookpage?bookId=%s", bookEntry.getBookId()));
                bookLessonAction.setTitle(completedChapters == 0 ? "Start Now" : "Continue");
                if (completedChapters==totalChapters) {
                    bookLessonAction.setTitle("View Again");
                    bookLessonCard.setCompleted(true);
                }
                bookLessonCard.setLessonAction(bookLessonAction);
                bookLessonCard.setUpsellingData(bookEntry.getUpsellingData());

                DigiLessonsCarouselWidget digiLessonsCarouselWidget = new DigiLessonsCarouselWidget();
                digiLessonsCarouselWidget.setTitle("Today's");
                digiLessonsCarouselWidget.setBoldTitle(" Lesson");

                if (bookLessonCard.isCompleted()) {
                    if (Objects.nonNull(videoLessonCard)) {
                        digiLessonsCarouselWidget.addLessonCard(videoLessonCard);
                    }
                    digiLessonsCarouselWidget.addLessonCard(bookLessonCard);
                } else {
                    digiLessonsCarouselWidget.addLessonCard(bookLessonCard);
                    if (Objects.nonNull(videoLessonCard)) {
                        digiLessonsCarouselWidget.addLessonCard(videoLessonCard);
                    }
                }

                return org.apache.commons.collections.CollectionUtils.isNotEmpty(digiLessonsCarouselWidget.getLessonCards()) ? digiLessonsCarouselWidget : null;
            }
        }catch (Exception e){
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }

        return null;
    }

    public CompletableFuture<DAProgramJourneyWidget> getDAProgramJourneyWidgetFuture(UserContext userContext) {
        return supplyAsync(() -> {
            try {
                return getDAProgramJourneyWidget(userContext);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getDAProgramJourneyWidgetFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    private DAProgramJourneyWidget getDAProgramJourneyWidget(UserContext userContext){
        DAProgramJourneyWidget programJourneyWidget = new DAProgramJourneyWidget();
        List<DAProgramJourneyWidget.ProgramJourneyItem> journeyItems = new ArrayList<>();
        journeyItems.add(new DAProgramJourneyWidget.ProgramJourneyItem(
                "https://d30nc97kamtr39.cloudfront.net/misc/lessons-v2.png",
                Action.builder().actionType(ActionType.NAVIGATION).url("curefit://digiprogramjourney?selectedTab=library").build()
        ));

        journeyItems.add(new DAProgramJourneyWidget.ProgramJourneyItem(
                "https://d30nc97kamtr39.cloudfront.net/misc/tasks.png",
                Action.builder().actionType(ActionType.NAVIGATION).url("curefit://digiprogramjourney?selectedTab=tasks").build()
        ));

        journeyItems.add(new DAProgramJourneyWidget.ProgramJourneyItem(
                "https://d30nc97kamtr39.cloudfront.net/misc/diet-plan.png",
                Action.builder().actionType(ActionType.NAVIGATION).url("curefit://digiprogramjourney?selectedTab=dietPlan").build()
        ));

        programJourneyWidget.setJourneyItems(journeyItems);

        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            TimeZone timeZone = ChronicCareAppUtil.getUserTimezone(userContext);
            AppTenant appTenant = AppUtil.getAppTenantFromUserContext(userContext);
            List<BookComposition> allUserBooks = catalogClient.getAllBookForUserId(userId, appTenant, timeZone);
            DigiJourneyProgressWidget.DayProgress dayProgress = new DigiJourneyProgressWidget.DayProgress();
            if (Objects.nonNull(allUserBooks)) {
                int booksCompleted = (int) allUserBooks.stream()
                        .filter(userBook -> Objects.nonNull(userBook.getUserChapterTrackingEntry()) && Boolean.TRUE.equals(userBook.getUserChapterTrackingEntry().getIsCompleted()))
                        .count();
                dayProgress.setCurrentDay(booksCompleted);
                dayProgress.setTotalDays(Math.max(allUserBooks.size(), 30));
                programJourneyWidget.setDayProgress(dayProgress);
            }
        } catch (Exception e){
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }

        return programJourneyWidget;
    }

    public ArrayList<String> getStaticCuisineTypes() {
        ArrayList<String> foodTypes = new ArrayList<>(
                Arrays.asList("North Indian", "South Indian", "Tamilian", "Kannadiga", "Bengali", "Gujarati", "Andhra", "Rajasthani", "Malayali", "Marathi", "Continental", "Arab"));
        return foodTypes;
    }

    public List<NUXCuisine> getDynamicCuisineTypes() {
        try {
            return serviceInterfaces.getAmbrosiaClient().fetchCuisines();
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    public static boolean isCoachConsultationSupportedDigiInternationalUser(ActivePackResponse activePackResponse) {
        try {
            if (activePackResponse == null || activePackResponse.getUserMembershipInfos() == null) {
                return false;
            }
            return activePackResponse.getUserMembershipInfos().stream()
                    .filter(info -> SUGARFIT_LONG_COACH_CONSULT.equals(info.getProductCode()))
                    .anyMatch(info ->
                            info.getTicketsConsumed() != null &&
                                    info.getTickets() != null &&
                                    info.getTicketsConsumed() < info.getTickets()
                    );
        } catch (Exception e) {
            log.error("Error in isCoachConsultationSupportedDigiInternationalUser", e);
        }
        return false;
    }

    public DiabeticProfileData getDiabeticProfileData(UserContext userContext, TimeZone timezone, CgmOnboardingStatusResponse cgmOnboardingStatusResponse) {
        try {
            Long userId = Long.valueOf(userContext.getUserProfile().getUserId());
            Optional<UserRecommendationLogEntry> res = this.serviceInterfaces.getSmsClient().getLatestUserRecommendationLog(userId);

            if (res.isPresent() && res.get().getDiabeticProfile() != null) {
                MetricThresholdLimit diabeticProfile = res.get().getDiabeticProfile();

                CompletableFuture<CgmStat> cgmStatFuture = getCgmStatFuture(userId, null, true, getAppTenantFromUserContext(userContext), timezone);
                CgmStat latestCgmStat = cgmStatFuture.get();
                boolean isCgmExpired = !(cgmOnboardingStatusResponse != null && cgmOnboardingStatusResponse.isCgmOperational());

                DiabeticProfileData.CgmMetric hba1c = new DiabeticProfileData.CgmMetric();
                hba1c.setValue(latestCgmStat.getAvgHBA1CValue());
                hba1c.setLabel("Est. HbA1c");
                hba1c.setRange(latestCgmStat.getAvgHBA1CValue() > 6.5 ? "High" : "Normal");

                DiabeticProfileData.CgmMetric avgGlucose = new DiabeticProfileData.CgmMetric();
                avgGlucose.setValue(latestCgmStat.getAvgGlucose());
                avgGlucose.setLabel("Avg. Glucose");
                avgGlucose.setUnit("mg/dL");
                avgGlucose.setRange(latestCgmStat.getAvgGlucose() > 140 ? "High" : latestCgmStat.getAvgGlucose() < 70 ? "Low" : "Normal");

                DiabeticProfileData.CgmMetric timeOverRange = new DiabeticProfileData.CgmMetric();
                timeOverRange.setValue(latestCgmStat.getTimeOverRangeGlucosePercentage());
                timeOverRange.setLabel("Time Over Range");
                timeOverRange.setUnit("%");
                timeOverRange.setRange(latestCgmStat.getTimeOverRangeGlucosePercentage() > 30 ? "High" : "Normal");

                DiabeticProfileData diabeticProfileData = new DiabeticProfileData();
                diabeticProfileData.setDiabeticProfile(diabeticProfile.equals(MetricThresholdLimit.HIGH) ? "HIGH" : "LOW");
                diabeticProfileData.setCgmExpired(isCgmExpired);
                diabeticProfileData.setHba1c(hba1c);
                diabeticProfileData.setAvgGlucose(avgGlucose);
                diabeticProfileData.setTimeOverRange(timeOverRange);

                DiabeticProfileData.SfCgmPreExpiryModalData modalData = new DiabeticProfileData.SfCgmPreExpiryModalData();
                modalData.setHba1c(hba1c);
                modalData.setAvgGlucose(avgGlucose);
                modalData.setTimeOverRange(timeOverRange);

                if (diabeticProfile.equals(MetricThresholdLimit.HIGH)) {
                    diabeticProfileData.setShowModal(true);
                    diabeticProfileData.setTitle("You’re in the Diabetic range!");
                    diabeticProfileData.setHeaderBgUrl("https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/header-bg-diabetic-profile-2025-06-18-18:23.png");
                    Action primaryAction = new Action("curefit://renewsubscription","Manage my diabetes",ActionType.NAVIGATION);
                    primaryAction.setImage(isCgmExpired ? "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/diabetic-renewal-banner-2025-07-04-18:07.png": null);
                    diabeticProfileData.setPrimaryAction(primaryAction);
                    modalData.setHeaderBgImg(isCgmExpired ? null : "https://cdn-ext-sugarfit0.cure.fit/sugarfit-app/storefront/diabeticcgm-hba1c-bg-2025-06-18-12:09.png");
                    modalData.setTitle(isCgmExpired ? "Your CGM is expired" : "You’re in Diabetic Range");
                    modalData.setDescription("As you are in DIABETIC RANGE we highly recommend you with our diabetes reversal programme");
                    modalData.setPrimaryAction(new Action("curefit://renewsubscription", isCgmExpired ? "Manage my diabetes" : "Improve your blood sugar",ActionType.NAVIGATION));
                } else if (diabeticProfile.equals(MetricThresholdLimit.LOW) && isCgmExpired) {
                    diabeticProfileData.setShowModal(true);
                    modalData.setTitle("Your CGM is expired");
                    modalData.setDescription("Running out of sensors? Get a new one to continue tracking your metabolic health and getting real-time insights");
                    modalData.setPrimaryAction(new Action("curefit://sfrenewaljourneypackspage","Buy sensors at lowest price",ActionType.NAVIGATION));
                }
                diabeticProfileData.setModalData(modalData);
                return diabeticProfileData;
            }
            return null;
        } catch (Exception e) {
            serviceInterfaces.getExceptionReportingService().reportException(e);
        }
        return null;
    }

    public CompletableFuture<DiabeticProfileData> getDiabeticProfileDataFuture(UserContext userContext, TimeZone timezone, CgmOnboardingStatusResponse cgmOnboardingStatusResponse) {
        return supplyAsync(() -> {
            try {
                return getDiabeticProfileData(userContext, timezone, cgmOnboardingStatusResponse);
            } catch (Exception e) {
                exceptionReportingService.reportException("Exception in getDiabeticProfileDataFuture", e);
                return null;
            }
        }, serviceInterfaces.getTaskExecutor());
    }

    public int getDaysLeftToCgmExpiry(CgmOnboardingStatusResponse cgmOnboardingStatus, CgmStat cgmStat) {
        try {
            if (Objects.nonNull(cgmOnboardingStatus) && cgmOnboardingStatus.isCgmOperational()) {
                String cgmDeviceId = Objects.nonNull(cgmStat) ? cgmStat.getCgmDeviceId() : null;
                Optional<CGMDeviceInfo> deviceInfoOptional = cgmOnboardingStatus.getCgmDeviceInfos()
                        .stream()
                        .filter(d -> d.getDeviceId().equals(cgmDeviceId))
                        .findFirst();

                CGMDeviceInfo cgmDeviceInfo = deviceInfoOptional.orElse(null);
                if (cgmDeviceInfo != null) {
                    Date startDate = cgmDeviceInfo.getStartedOn();
                    if (startDate != null && cgmDeviceInfo.isOngoing()) {
                        int cgmValidityPeriod = cgmDeviceInfo.getDeviceModel().equals(DeviceModel.GLUCO_RX) ? 15 : 14;

                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(startDate);
                        calendar.add(Calendar.DAY_OF_MONTH, cgmValidityPeriod);
                        Date estimatedEndDate = calendar.getTime();

                        long diffInMillis = estimatedEndDate.getTime() - new Date().getTime();
                        long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);

                        return (int) Math.max(0, diffInDays);
                    }
                }
            }
            return 0;
        } catch (NullPointerException | IllegalArgumentException e) {
            log.error("Error in calculating days left to expire: {}", e.getMessage(), e);
            return 0;
        }
    }


}
