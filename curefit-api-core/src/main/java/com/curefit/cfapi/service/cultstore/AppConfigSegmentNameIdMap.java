package com.curefit.cfapi.service.cultstore;

import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class AppConfigSegmentNameIdMap implements ISegmentNameIdMap{
    private HashMap<String, String> segmentNameIdMap;

    @Autowired
    ServiceInterfaces serviceInterfaces;

    @Autowired
    RollbarService rollbarService;

    @Override
    public List<Long> getActiveSegmentIDs(SegmentSet<String> segmentNames, List<Long> segmentIds) {
        return segmentNameIdMap.keySet().stream()
                .filter(segmentNames::contains)
                .map(segmentNameIdMap::get)
                .map(Long::valueOf)
                .filter(segmentIds::contains)
                .collect(Collectors.toList());
    }

    @PostConstruct
    private void init(){
        refreshSegmentNameIdMap();
    }

    @Scheduled(fixedRate = 600000)
    public void refreshSegmentNameIdMap(){
        try {
            segmentNameIdMap = serviceInterfaces.appConfigCache.getConfig("SEGMENT_NAME_ID_MAP", new TypeReference<>() {}, new HashMap<>());
        } catch (Exception e) {
            log.error("[AppConfigSegmentNameIdMap] Error in refreshing segment name id map from app config", e);
            rollbarService.error(e, "[AppConfigSegmentNameIdMap] Error in refreshing segment name id map from app config");
        }
    }
}

