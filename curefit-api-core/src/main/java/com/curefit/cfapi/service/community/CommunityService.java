package com.curefit.cfapi.service.community;

import com.curefit.base.enums.AppTenant;
import com.curefit.base.enums.Tenant;
import com.curefit.base.service.EnvironmentService;
import com.curefit.cfapi.builder.vm.BuildWidgetResponse;
import com.curefit.cfapi.cache.CultUnboundBannerWodIdsConfigService;
import com.curefit.cfapi.cache.FeatureStateCache;
import com.curefit.cfapi.cache.SegmentCache;
import com.curefit.cfapi.cache.VMCache;
import com.curefit.cfapi.constants.CommunityConstants;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.activityAttributeLogging.entry.ActivityLoggingMeta;
import com.curefit.cfapi.model.internal.activityAttributeLogging.entry.ActivityStoreAttributeEntry;
import com.curefit.cfapi.model.internal.activityAttributeLogging.enums.ExerciseLogTypeEnum;
import com.curefit.cfapi.model.internal.activityAttributeLogging.enums.LoggedExerciseStateEnum;
import com.curefit.cfapi.model.internal.activityAttributeLogging.pojos.response.CustomProgressBarWidgetData;
import com.curefit.cfapi.model.internal.activityAttributeLogging.pojos.response.ExerciseLoggingWidgetData;
import com.curefit.cfapi.model.internal.activityAttributeLogging.pojos.response.WeightLiftRankingWidgetData;
import com.curefit.cfapi.model.internal.exception.BadRequestException;
import com.curefit.cfapi.model.internal.userinfo.PreferredLocation;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.vm.page.PageContext;
import com.curefit.cfapi.model.internal.vm.page.WidgetContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionIcon;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.app.action.RaiseTicketParams;
import com.curefit.cfapi.pojo.base.LatLong;
import com.curefit.cfapi.pojo.chroniccare.nux.ToastMessageMeta;
import com.curefit.cfapi.pojo.community.LatestBookingDetail;
import com.curefit.cfapi.pojo.community.UserContactSyncRequest;
import com.curefit.cfapi.pojo.feedback.Feedback;
import com.curefit.cfapi.pojo.vm.EnergyStreakGame.EnergyStreakGame;
import com.curefit.cfapi.pojo.vm.atom.CFGenericAlertDialog;
import com.curefit.cfapi.pojo.vm.atom.CFTextData;
import com.curefit.cfapi.pojo.vm.atom.UiUtils;
import com.curefit.cfapi.pojo.vm.items.CarouselUpcomingItem;
import com.curefit.cfapi.pojo.vm.items.UpcomingItemCarousal;
import com.curefit.cfapi.pojo.vm.widget.Spacing;
import com.curefit.cfapi.pojo.vm.widget.WidgetType;
import com.curefit.cfapi.ragnar.response.HighlightCardData;
import com.curefit.cfapi.ragnar.response.HighlightCardResponse;
import com.curefit.cfapi.ragnar.response.HighlightPeopleResponse;
import com.curefit.cfapi.service.*;
import com.curefit.cfapi.service.community.SquadGoalStateHelper.SquadGoalStateInfo;
import com.curefit.cfapi.service.kudosService.KudosService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewmodels.community.*;
import com.curefit.cfapi.widgets.activityAttributeLogging.*;
import com.curefit.cfapi.widgets.base.BaseWidget;
import com.curefit.cfapi.widgets.base.BaseWidgetNonVM;
import com.curefit.cfapi.widgets.common.SecondaryButtonWidget;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.common.banner.BaseBannerWidget;
import com.curefit.cfapi.widgets.community.*;
import com.curefit.cfapi.widgets.community.pojo.APIStatus;
import com.curefit.challenges.enums.ChallengeInviteType;
import com.curefit.challenges.enums.ChallengeStatus;
import com.curefit.challenges.pojo.ChallengeCommunityMappingEntry;
import com.curefit.challenges.pojo.ChallengeUserMappingEntry;
import com.curefit.challenges.pojo.ScoringDetails;
import com.curefit.challenges.response.ChallengeResponse;
import com.curefit.cfapi.widgets.community.pojo.*;
import com.curefit.common.data.enums.AppStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.RuntimeBaseException;
import com.curefit.common.data.exception.enums.LogType;
import com.curefit.commons.client.exception.HttpException;
import com.curefit.commons.messaging.publisher.sqs.SqsPublisher;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.contactsync.client.impl.ContactSyncClientImpl;
import com.curefit.contactsync.models.Contact;
import com.curefit.contactsync.models.PhoneNumberDetail;
import com.curefit.contactsync.models.request.SyncContactRequest;
import com.curefit.cult.enums.BookingLabel;
import com.curefit.cult.enums.ProfileVisibility;
import com.curefit.cult.models.BulkBookingResponse;
import com.curefit.cult.models.CultBooking;
import com.curefit.cult.models.TagEntry;
import com.curefit.cult.models.UserProfileEntry;
import com.curefit.cult.models.requests.CultBookingAndWaitlistRequest;
import com.curefit.cult.models.responses.CultClassResponse;
import com.curefit.cult.services.impl.CultServiceImpl;
import com.curefit.curio.FeedbackRating;
import com.curefit.curio.services.FeedbackService;
import com.curefit.hercules.pojo.BodyPart;
import com.curefit.hercules.pojo.Media;
import com.curefit.hercules.pojo.Movement;
import com.curefit.iris.services.spi.CampaignService;
import com.curefit.logging.models.*;
import com.curefit.logging.models.request.ActivityStoreAttributeSearchRequest;
import com.curefit.logging.models.request.SortField;
import com.curefit.maestro.enums.EventType;
import com.curefit.maestro.enums.UserType;
import com.curefit.maestro.pojo.RewardPointsSummary;
import com.curefit.maestro.pojo.UserEventReviewCount;
import com.curefit.maestro.pojo.UserReviewTypeEntry;
import com.curefit.membership.client.MembershipClientImpl;
import com.curefit.membership.client.MembershipFilter;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.types.Status;
import com.curefit.metricservice.exceptions.MetricClientException;
import com.curefit.metricservice.models.UserMetricValue;
import com.curefit.metricservice.pojo.UserMetricValuePojo;
import com.curefit.ollivander.client.agent.OllivanderAgentClient;
import com.curefit.ollivander.common.exception.OllivanderClientException;
import com.curefit.ollivander.common.pojo.response.agent.AgentAttributeResponse;
import com.curefit.ollivander.common.pojo.response.agent.AgentResponse;
import com.curefit.quest.client.spi.QuestClient;
import com.curefit.quest.pojo.Badge;
import com.curefit.quest.pojo.QuestBadge;
import com.curefit.rashi.client.UserAttributesCacheClient;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.reportissues.models.SupportArticle;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.socialservice.client.impl.NotificationCenterServiceClientImpl;
import com.curefit.socialservice.client.impl.SocialServiceClientImpl;
import com.curefit.socialservice.client.impl.ChallengeServiceClientImpl;
import com.curefit.socialservice.enums.*;
import com.curefit.socialservice.pojo.*;
import com.curefit.uas.impl.UASFitnessReportClient;
import com.curefit.uas.impl.UserActivityServiceClient;
import com.curefit.uas.logging.pojos.ExerciseComparisonResponse;
import com.curefit.uas.logging.pojos.ExerciseListResponse;
import com.curefit.uas.pojo.PercentileValue;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.requests.UserActivityStatsRequest;
import com.curefit.uas.responses.FitnessReportResponse;
import com.curefit.uas.responses.StreakResponse;
import com.curefit.uas.responses.UserActivityStatsResponse;
import com.curefit.uas.types.UserActivityType;
import com.curefit.userservice.client.UserServiceClient;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.google.common.collect.Lists;
import io.trino.jdbc.$internal.jackson.databind.ObjectMapper;
import io.trino.jdbc.$internal.jackson.databind.ObjectWriter;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.curefit.cfapi.constants.FitnessPlannerConstants.THUMBNAIL_IMAGE;
import static com.curefit.cfapi.constants.ImageConstants.CULT_MOMENTS_URL_PREFIX;
import static com.curefit.cfapi.util.AppUtil.*;
import static com.curefit.cfapi.util.CDNUtil.CONTENT_CDN_BASE_PATH;
import static com.curefit.cfapi.util.CommunityUtil.*;
import static com.curefit.cfapi.widgets.activityAttributeLogging.EncapsulatorWidget.getCFChartWidgetFromAttributeList;
import static com.curefit.cfapi.widgets.fitness.UserWeeklyActivity.checkNinjaInLastNDays;
import static com.curefit.quest.enums.Vertical.CULT;
import static com.curefit.socialservice.enums.NotificationCenterEntryType.*;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CommunityService {

    RagnarCommunityService ragnarCommunityService;
    UserSegmentClient userSegmentClient;
    EnvironmentService envService;
    static UserServiceClient userServiceClient;
    CultServiceImpl cultServiceImpl;
    SocialServiceClientImpl socialServiceClient;
    NotificationCenterServiceClientImpl notificationCenterServiceClient;
    DeeplinkCacheService deeplinkCacheService;
    OllivanderAgentClient ollivanderAgentClient;
    QuestClient questClient;
    UserAttributesCacheClient userAttributesCacheClient;
    MembershipClientImpl membershipService;
    CampaignService campaignService;
    ExceptionReportingService exceptionReportingService;
    SqsPublisher sqsPublisher;
    VMCache vmCache;
    SegmentCache segmentCache;
    SegmentationCacheClient segmentationCacheClient;
    SegmentEvaluatorService segmentEvaluatorService;
    EnvironmentService environmentService;
    CfApiFeedbackService feedbackService;
    UserService userService;
    FeedbackService iFeedbackService;
    KudosService kudosService;
    FeatureStateCache featureStateCache;
    CultUnboundBannerWodIdsConfigService cultUnboundBannerWodIdsConfigService;
    UserActivityServiceClient userActivityServiceClient;
    CultChampionsPageConfigService cultChampionsPageConfigService;

    ContactSyncClientImpl contactSyncService;
    ChallengeServiceClientImpl challengeServiceClient;

    UASFitnessReportClient uasFitnessReportClient;
    final KeyValueStore cfApiRedisKeyValueStore;

    @Value("${cloud.aws.end-point}${queue.activity}")
    public String communityActivitySQS;

    static ServiceInterfaces serviceInterfaces;

    private static final int PAGE_SIZE = 50;
    private static final int SQUAD_FEED_USER_LIMIT = 5;

    @Autowired
    public CommunityService(RagnarCommunityService ragnarCommunityService, UserSegmentClient userSegmentClient, UserServiceClient userServiceClient, CultServiceImpl cultServiceImpl, SocialServiceClientImpl socialServiceClient, NotificationCenterServiceClientImpl notificationCenterServiceClient, DeeplinkCacheService deeplinkCacheService, OllivanderAgentClient ollivanderAgentClient, QuestClient questClient, UserAttributesCacheClient userAttributesCacheClient, MembershipClientImpl membershipService, CampaignService campaignService, EnvironmentService envService, SqsPublisher sqsPublisher, ExceptionReportingService exceptionReportingService, VMCache vmCache, SegmentCache segmentCache, SegmentationCacheClient segmentationCacheClient, SegmentEvaluatorService segmentEvaluatorService, EnvironmentService environmentService, ServiceInterfaces serviceInterfaces, CfApiFeedbackService feedbackService, UserService userService, FeedbackService iFeedbackService, KudosService kudosService, FeatureStateCache featureStateCache, CultUnboundBannerWodIdsConfigService cultUnboundBannerWodIdsConfigService, CultChampionsPageConfigService cultChampionsPageConfigService, ContactSyncClientImpl contactSyncService, @Qualifier("defaultRedisKeyValueStore") KeyValueStore defaultRedisKeyValueStore, @Qualifier("cfApiRedisKeyValueStore") KeyValueStore cfApiRedisKeyValueStore, UserActivityServiceClient userActivityServiceClient, UASFitnessReportClient uasFitnessReportClient, ChallengeServiceClientImpl challengeServiceClient) {
        this.ragnarCommunityService = ragnarCommunityService;
        this.userSegmentClient = userSegmentClient;
        this.userServiceClient = userServiceClient;
        this.cultServiceImpl = cultServiceImpl;
        this.socialServiceClient = socialServiceClient;
        this.notificationCenterServiceClient = notificationCenterServiceClient;
        this.deeplinkCacheService = deeplinkCacheService;
        this.ollivanderAgentClient = ollivanderAgentClient;
        this.questClient = questClient;
        this.userAttributesCacheClient = userAttributesCacheClient;
        this.membershipService = membershipService;
        this.campaignService = campaignService;
        this.envService = envService;
        this.sqsPublisher = sqsPublisher;
        this.exceptionReportingService = exceptionReportingService;
        this.vmCache = vmCache;
        this.segmentCache = segmentCache;
        this.segmentationCacheClient = segmentationCacheClient;
        this.environmentService = environmentService;
        this.segmentEvaluatorService = segmentEvaluatorService;
        this.serviceInterfaces = serviceInterfaces;
        this.feedbackService = feedbackService;
        this.userService = userService;
        this.iFeedbackService = iFeedbackService;
        this.kudosService = kudosService;
        this.featureStateCache = featureStateCache;
        if (environmentService.isProduction()) {
            this.cfApiRedisKeyValueStore = cfApiRedisKeyValueStore;
        } else {
            this.cfApiRedisKeyValueStore = defaultRedisKeyValueStore;
        }
        this.cultUnboundBannerWodIdsConfigService = cultUnboundBannerWodIdsConfigService;
        this.contactSyncService = contactSyncService;
        this.cultChampionsPageConfigService = cultChampionsPageConfigService;
        this.userActivityServiceClient = userActivityServiceClient;
        this.challengeServiceClient = challengeServiceClient;
        this.uasFitnessReportClient = uasFitnessReportClient;
    }
    private LatLong getUserLatLong(UserContext userContext) {
        LatLong latLong = null;
        try {
            CompletableFuture<PreferredLocation> preferredLocationCompletableFuture = userContext.getUserProfile().getPreferredLocationPromise();
            if (preferredLocationCompletableFuture != null) {
                latLong = preferredLocationCompletableFuture.get().getLatLong();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // TODO
        if (latLong == null) {
            latLong = new LatLong();
            latLong.setLatitude(0f);
            latLong.setLongitude(0f);
        }
        return latLong;
    }

    public ProfilePage getProfilePage(String cultUserId, UserContext userContext, String nextCursor, String cb) throws InterruptedException, ExecutionException, ParseException, BaseException {
        boolean isMyProfile = false;
        boolean isPrivate = true;
        boolean isTrainer;
        CompletableFuture<UserEntry> userEntryCF = null;
        CompletableFuture<UserProfileEntry> profileEntryCF = null;
//        CompletableFuture<CommunityFeedResponse> userPostsCF = null;
//        CompletableFuture<ListGroupResponse> listGroupResponseCF = null;
//        CompletableFuture<UserMultiMediaResponse> userMultiMediaResponseCF = null;
        CompletableFuture<List<QuestBadge>> questBadgeCF = null;
        LocalDate endDate = LocalDate.now(ZoneId.of(userContext.getUserProfile().getTimezone()));
        LocalDate startDate = LocalDate.now(ZoneId.of(userContext.getUserProfile().getTimezone())).minusDays(180);
        ProfilePage page = new ProfilePage();
        List<String> userIds = new ArrayList<>();

        SquadVisibilityType squadVisbility = getSquadVisibility(cultUserId, serviceInterfaces);
        String finalCultUserId = cultUserId == null || Objects.equals(cultUserId, "") ? userContext.getUserProfile().getUserId() : cultUserId;
        if (cultUserId == null || cultUserId.equals(userContext.getUserProfile().getUserId())) {
            String userId = userContext.getUserProfile().getUserId();
            userEntryCF = getUserEntryCF(userId);
            profileEntryCF = CompletableFuture.supplyAsync(() -> cultServiceImpl.getCultUserById(userId), serviceInterfaces.getTaskExecutor());
            userIds.add(userContext.getUserProfile().getUserId());
            isMyProfile = true;
            isPrivate = false;
        } else {
            userEntryCF = getUserEntryCF(cultUserId);
            profileEntryCF = CompletableFuture.supplyAsync(() -> cultServiceImpl.getCultUserById(cultUserId), serviceInterfaces.getTaskExecutor());
            String selfUserId = userContext.getUserProfile().getUserId();
            CompletableFuture<UserProfileEntry> selfUserEntryCF = CompletableFuture.supplyAsync(() -> cultServiceImpl.getCultUserById(selfUserId), serviceInterfaces.getTaskExecutor());
            if (selfUserEntryCF.get().getVisibility() == ProfileVisibility.PRIVATE) {
                UserVisibilityInfo userVisibilityInfo = new UserVisibilityInfo();
                userVisibilityInfo.setPrivate(true);
                userVisibilityInfo.setTitle("Your profile is private");
                userVisibilityInfo.setMessage("To view " + userEntryCF.get().getFirstName() + "'s profile, you should make your own profile public");
                userVisibilityInfo.setCta("Make my profile public");
                page.setUserVisibilityInfo(userVisibilityInfo);
                return page;
            }
            userIds.add(cultUserId);

            if (profileEntryCF.get().getVisibility() == ProfileVisibility.PUBLIC) {
                isPrivate = false;
            } else if (profileEntryCF.get().getVisibility() == ProfileVisibility.MY_CONTACTS) {
                // check if the person opening my profile is in my_contacts or not
                NodeRelationEntry nodeRelationEntry = new NodeRelationEntry();
                nodeRelationEntry.setParentId(Long.valueOf(cultUserId));
                nodeRelationEntry.setChildId(Long.valueOf(userContext.getUserProfile().getUserId()));
                nodeRelationEntry.setAttributeCode(RelationAttributeCode.HAS_CONTACT);
                try {
                    NodeRelationEntry nodeRelationEntryExist = socialServiceClient.getParentChildRelationship(nodeRelationEntry);
                    if (nodeRelationEntryExist != null) {
                        isPrivate = false;
                    } else {
                        isPrivate = true;
                    }
                } catch (Exception e) {
                    log.error("Error in fetching node relation entry: {}", e);
                }
            } else if (profileEntryCF.get().getVisibility() == ProfileVisibility.CULT_MEMBERS) {
                String timezone = userContext.getUserProfile().getCity().getTimezone();
                long currentTime = TimeUtil.now(timezone);
                try {
                    List<Membership> membershipDetails = membershipService.getCachedMembershipsForUser(cultUserId, "curefit", MembershipFilter.builder().benefits(MembershipUtil.MEMBERSHIP_BENEFITS).status(Status.PURCHASED).status(Status.PAUSED).start(currentTime).end(currentTime).build()).get();
                    if (membershipDetails != null && !membershipDetails.isEmpty()) {
                        isPrivate = false;
                    } else {
                        isPrivate = true;
                    }
                } catch (Exception e) {
                    log.error("Error in fetching membership details: {}", e);
                }
            }
        }
        boolean isCommunityEnabled = false;
        if (isMyProfile) {
            isCommunityEnabled = AppUtil.isCommunityTabSupported(userContext, segmentEvaluatorService, environmentService);
        } else {
            Boolean isCommmunityEnabledforMe = AppUtil.isCommunityTabSupported(userContext, segmentEvaluatorService, environmentService);
            if (!isCommmunityEnabledforMe) {
                isCommunityEnabled = false;
            }
            Boolean isCommunityEnabledForOthers = AppUtil.doesUserExistInCommunitySegment(userContext, finalCultUserId, segmentCache, environmentService);
            if (isCommmunityEnabledforMe && isCommunityEnabledForOthers) {
                isCommunityEnabled = true;
            }
        }

        // listGroupResponseCF = CompletableFuture.supplyAsync(() -> ragnarServiceClient.listUserGroupsBySocialId(finalSocialUserId), serviceInterfaces.getTaskExecutor());
        // userMultiMediaResponseCF = CompletableFuture.supplyAsync(() -> ragnarServiceClient.getUserMedia(finalSocialUserId, null, cb), serviceInterfaces.getTaskExecutor());
        questBadgeCF = questClient.getBadgesForUser(finalCultUserId);
        // userPostsCF = CompletableFuture.supplyAsync(() -> ragnarServiceClient.getUserPosts(finalCommunityUserEntry.getCultUserId(), nextCursor, cb), serviceInterfaces.getTaskExecutor());

        isTrainer = false;

        UserEntry userEntry = userEntryCF.get();
        UserProfileEntry profileEntry = profileEntryCF.get();

        if (!isMyProfile && !isMember(userContext, membershipService, envService)) {
            page.addWidget(getNonMemberWidget());
            return page;
        }

        if (nextCursor == null || "".equals(nextCursor)) {
            List<Option> options = new ArrayList<>();
            Action shareAction = new Action();
            ShareProfileMeta shareProfileMeta = new ShareProfileMeta();
            shareProfileMeta.setUrl(this.deeplinkCacheService.getCommunityProfileDeeplink(finalCultUserId, Tenant.CUREFIT_APP));
            shareAction.setMeta(shareProfileMeta);
            shareAction.setActionType(ActionType.SHARE_PROFILE);
            options.add(new Option("SHARE", "Share", shareAction));
            page.setOptions(options);
            ProfileInfoWidget infoWidget = new ProfileInfoWidget();
            SquadInvitationWidget squadWidget = new SquadInvitationWidget();
            infoWidget.setCultUserId(finalCultUserId);
            List<Highlight> hightlights = new ArrayList<>();

            com.curefit.socialservice.pojo.UserProfileEntry userProfileEntry = socialServiceClient.getOrCreateUserProfile(finalCultUserId);
            if (userProfileEntry.getBio() != null &&  !userProfileEntry.getBio().isEmpty()) {
                String bio = userProfileEntry.getBio();
                if(bio.length() > 300) {
                    bio = bio.substring(0, 300) + "...";
                }
                hightlights.add(new Highlight("CULT", bio));
            }

            if(AppUtil.isChampionUserV2(finalCultUserId, segmentationCacheClient)) infoWidget.setUserType("CHAMPION");

            if (isTrainer) {
                try {
                    Long agentId = Long.parseLong(finalCultUserId);
                    AgentResponse agentResponse = ollivanderAgentClient.getAgent(agentId);
                    if (agentResponse != null && agentResponse.getAgentAttributeResponses() != null) {
                        for (AgentAttributeResponse item : agentResponse.getAgentAttributeResponses()) {
                            if ("META".equals(item.getAttributeType())) {
                                switch (item.getAttributeName()) {
                                    case "CENTER":
                                        hightlights.add(new Highlight("LOCATION", item.getAttributeValue()));
                                        break;
                                    case "SPECIALITY":
                                        hightlights.add(new Highlight("SPECIALITY", item.getAttributeValue()));
                                        break;
                                    case "EXPERIENCE":
                                        hightlights.add(new Highlight("EXPERIENCE", item.getAttributeValue()));
                                        break;
                                    case "CULT_CLASSES":
                                        hightlights.add(new Highlight("CLASSES", item.getAttributeValue()));
                                        break;
                                    case "CERTIFICATION":
                                        hightlights.add(new Highlight("CERTIFICATE", item.getAttributeValue()));
                                        break;
                                }
                            }
                        }
                        hightlights = hightlights.stream().sorted(Comparator.comparingInt(a -> getIconPriority(a.getIcon()))).toList();
                        infoWidget.setDescription(agentResponse.getDescription());
                    }
                } catch (OllivanderClientException e) {
                    e.printStackTrace();
                }
            } else if (!isMyProfile) {

                if (!isPrivate && !isTrainer) {
                    try {
                        List<String> attributesToFetch = new ArrayList<>();
                        attributesToFetch.add(CommunityConstants.WORKOUT_FORMAT_AFFINITY_CULT_CENTER_TOP);
                        attributesToFetch.add(CommunityConstants.WORKOUT_TIME_AFFINITY_CULT_CENTER_WEEKDAY);
                        UserAttributesResponse uar = userAttributesCacheClient.getAttributes(Long.valueOf(finalCultUserId), attributesToFetch, AppUtil.getAppTenantFromUserContext(userContext));
                        Object o1 = uar.getAttributes().get(CommunityConstants.WORKOUT_FORMAT_AFFINITY_CULT_CENTER_TOP);
                        Object o2 = uar.getAttributes().get(CommunityConstants.WORKOUT_TIME_AFFINITY_CULT_CENTER_WEEKDAY);
                        if (o2 != null) {
                            String time = o2.toString();
                            if (time != null) {
                                String[] split1 = time.split("-");
                                if (split1.length > 0) {
                                    String[] split2 = split1[0].split(":");
                                    if (split2.length > 0) {
                                        int parseInt = Integer.parseInt(split2[0]);
                                        if (parseInt > 15) {
                                            time = "evening";
                                        } else {
                                            time = "morning";
                                        }
                                        hightlights.add(new Highlight("TIME", "Prefers working out in the " + time)); // TODO
                                    }
                                }
                            }
                        }
                        if (o1 != null) {
                            String format = o1.toString();
                            format = format.substring(2, format.length() - 2);
                            String[] a = format.split("\",\"");
                            StringBuilder sb = new StringBuilder();
                            for (String f : a) {
                                sb.append(f).append(", ");
                            }
                            hightlights.add(new Highlight("WORKOUT", "Favourite format: " + sb.substring(0, sb.length() - 2)));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                } else {
                    hightlights.add(new Highlight("LOCK", "This account is private"));
                }
            }
            if (Objects.equals(squadVisbility, SquadVisibilityType.PHONE_NUMBER)) {
                hightlights.add(new Highlight("LOCK", "Invite by contact only"));
            }

            infoWidget.setHighlights(hightlights);
            List<String> userTags = new ArrayList<>();
            for (TagEntry tagEntry : profileEntry.getTags()) {
                if (tagEntry.isMapped()) {
                    userTags.add(tagEntry.getDisplayName());
                }
            }
            if (AppUtil.isCultOwnerAccount(cultUserId) ||
                    AppUtil.isCultOwnerAccount(userContext.getUserProfile().getUserId())) {
                userTags = List.of("Fitness isn’t a solo journey. It’s always better together in the cult community!");
            }
            infoWidget.setUserTags(userTags);

            if (userEntry.getLastName() == null || "null".equals(userEntry.getLastName()) || "".equals(userEntry.getLastName())) {
                infoWidget.setName(userEntry.getFirstName());
            } else {
                infoWidget.setName(userEntry.getFirstName() + ((userEntry.getLastName() != null && !"".equals(userEntry.getLastName()) && !"null".equals(userEntry.getLastName())) ? " " + userEntry.getLastName() : ""));
            }

            if (isPrivate) {
                if ("female".equals(userEntry.getGender())) {
                    infoWidget.setProfilePictureUrl("https://curefit-content.s3.ap-south-1.amazonaws.com/image/community/default_male_thumbnail.png");
                } else {
                    infoWidget.setProfilePictureUrl("https://curefit-content.s3.ap-south-1.amazonaws.com/image/community/default_male_thumbnail.png");
                }
            } else {
                if (userEntry.getProfilePictureUrl() != null && !"".equals(userEntry.getProfilePictureUrl())) {
                    infoWidget.setProfilePictureUrl(userEntry.getProfilePictureUrl());
                } else {
                    if ("female".equals(userEntry.getGender())) {
                        infoWidget.setProfilePictureUrl("https://curefit-content.s3.ap-south-1.amazonaws.com/image/community/default_male_thumbnail.png");
                    } else {
                        infoWidget.setProfilePictureUrl("https://curefit-content.s3.ap-south-1.amazonaws.com/image/community/default_male_thumbnail.png");
                    }
                }
            }

            if (!isTrainer) {

                Calendar todaysDate = Calendar.getInstance();
                todaysDate.setFirstDayOfWeek(Calendar.MONDAY);
                todaysDate.set(Calendar.HOUR_OF_DAY, 23);
                todaysDate.set(Calendar.MINUTE, 59);
                todaysDate.add(Calendar.DAY_OF_MONTH, -7);

                FitnessReportResponse fitnessReportResponse = this.uasFitnessReportClient.getLatestReportForUserId(Long.valueOf(finalCultUserId)).get();

                RewardPointsSummary rewardPointsSummary = this.kudosService.getTotalPointsCount(finalCultUserId);
                infoWidget.setKarmaPoints(Math.toIntExact(rewardPointsSummary.getTotalPoints()));
                infoWidget.setKarmaText("Karma Points");
                infoWidget.setEnableKarmaPoints(false); //false means true, true means false (will be fixed in next release)
                if (Math.toIntExact(rewardPointsSummary.getTotalPoints()) == 0) {
                    if (finalCultUserId.equals(userContext.getUserProfile().getUserId())) {
                        infoWidget.setEarnKarma("Earn");
                    } else {
                        infoWidget.setEnableKarmaPoints(true);
                    }
                }

                if (Objects.equals(userContext.getUserProfile().getUserId(), finalCultUserId)) {
                    Action action = new Action();
                    action.setActionType(ActionType.NAVIGATION);
                    action.setUrl("curefit://karma_points");
                    Map<String, Object> meta = new HashMap<>();
                    meta.put("viaDeeplink", true);
                    action.setMeta(meta);
                    infoWidget.setKarmaAction(action);
                }
                if (fitnessReportResponse != null && fitnessReportResponse.getFitnessReport() != null) {
                    FitnessReportEntry report = fitnessReportResponse.getFitnessReport();
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Calendar reportEndDate = Calendar.getInstance();
                    reportEndDate.setTime(dateFormat.parse(report.getEndDate()));

                    if (todaysDate.before(reportEndDate)) {
                        infoWidget.setStreakCount(Math.toIntExact(report.getStreak()));
                        infoWidget.setStreakType(infoWidget.getStreakCount() > 1 ? "Weeks Active" : "Week Active");
                    } else {
                        infoWidget.setStreakCount(Math.toIntExact(report.getMaxWeeklyStreak()));
                        infoWidget.setStreakType(Math.toIntExact(report.getMaxWeeklyStreak()) > 1 ? "Best Weeks" : "Best Week");
                    }
                    infoWidget.setText("What is weekly streak?");
                    infoWidget.setSubText("A weekly streak tracks consecutive weeks of consistent activity in a task or goal. It encourages commitment by resetting to zero if a week is missed, motivating consistent progress.");
                } else {
                    log.info("CommunityService::getProfilePage unable to populate Weekly Streak for this week for user " + finalCultUserId + " use  as fitnessReportResponse is null");
                }

                if (fitnessReportResponse != null && fitnessReportResponse.getFitnessReport() != null) {
                    infoWidget.setClassesAttendedText("Classes attended");
                    infoWidget.setClassesAttended((int) fitnessReportResponse.getFitnessReport().getTotalClassesAttended());
                    infoWidget.setText("What is cult class ?");
                    infoWidget.setSubText("You’ll find a wide variety of workout formats, such as - Boxing, Dance Fitness, Yoga, Adidas Strength+, and HRX.");
                } else {
                    log.info("CommunityService::getProfilePage unable to populate Classes this week for user " + finalCultUserId + " use  as fitnessReportResponse" + fitnessReportResponse);
                }
            }
            if (isMyProfile) {
                Action editAction = new Action();
                editAction.setActionType(ActionType.EDIT_PROFILE);
                EditMeta editMeta = new EditMeta();
                editAction.setMeta(editMeta);
                editAction.setTitle("EDIT PROFILE");
                infoWidget.setInfoAction(editAction);

                List<String> attributes = Arrays.asList(Constants.RASHI_ATTRIBUTE_FOR_ENERGY_LEVEL_V2, Constants.RASHI_ATTRIBUTE_FOR_USER_GAME_ID);
                UserAttributesResponse userAttributesResponse = this.userAttributesCacheClient.getAttributes(Long.valueOf(userContext.getUserProfile().getUserId()), attributes, AppTenant.CUREFIT);
                String gameUrl = "curefit://activity_streak_game";
                boolean gameV2Enabled = false;

                EnergyStreakGame userGame = userService.getUsersGame(userContext, userAttributesResponse, serviceInterfaces);

                if (userGame != null) {
                    gameUrl = "curefit://activity_streak_game_v2";
                    gameV2Enabled = true;
                }

                if (gameV2Enabled || AppUtil.doesUserBelongToEnergyStreakSegment(this.serviceInterfaces, userContext)) {
                    Action activityStreakAction = new Action();
                    activityStreakAction.setActionType(ActionType.NAVIGATION);
                    activityStreakAction.setUrl(gameUrl);
                    Map<String, Object> meta = new HashMap<>();
                    meta.put("viaDeeplink", true);
                    activityStreakAction.setMeta(meta);
                    activityStreakAction.setTitle("YOUR FITNESS JOURNEY");

                    infoWidget.setActivityStreakAction(activityStreakAction);
                }
            }
            if (!isMyProfile && AppUtil.isNewSquadUsersSegment(userContext, serviceInterfaces)) {
                CompletableFuture<UserEntry> selfUserEntryCF = getUserEntryCF(userContext.getUserProfile().getUserId());
                squadWidget = getAddToSquadWidget(userContext, cultUserId, selfUserEntryCF.get(), userEntry, serviceInterfaces);
                if (squadWidget != null) {
                    if (squadWidget.getStatus() == SquadState.MEMBER) infoWidget.setUserType("SQUAD MEMBER");
                    if (Objects.equals(squadVisbility, SquadVisibilityType.ANY)) squadWidget.setEnabled(true);
                }
            }
            if (AppUtil.isCultOwnerAccount(cultUserId) ||
                    AppUtil.isCultOwnerAccount(userContext.getUserProfile().getUserId())) {
                infoWidget.setUserType("FOUNDER OF CULT");
            }
            page.addWidget(infoWidget);

            if (squadWidget != null && squadWidget.getStatus() != null) {
                if (Objects.equals(squadWidget.getPeopleComponent(), PeopleComponent.INVITES)
                        || (!isPrivate && Objects.equals(squadVisbility, SquadVisibilityType.ANY)
                        && squadWidget.getStatus() != SquadState.MEMBER)) {
                    page.addWidget(squadWidget);
                }
            }

            if (!isTrainer) {
                BadgesWidget badgesWidget = new BadgesWidget();
                List<Badge> badges = new ArrayList<>();
                Badge activityBadge = null;
                Badge caloriesBadge = null;
                Badge energyBadge = null;
                Badge durationBadge = null;
                Badge streakBadge = null;
                Badge boosterBadge = null;
                Badge challengeBadge = null;
                List<QuestBadge> questBadges = questBadgeCF.get();
                for (QuestBadge questBadge : questBadges) {
                    if (questBadge.getVertical() == CULT) {
                        for (int j = 0; j < questBadge.getBadges().size(); j++) {
                            if (questBadge.getBadges().get(j).isAchieved()) {
                                Badge b = questBadge.getBadges().get(j).getBadge();
                                switch (b.getType()) {
                                    case LIMITED:
                                        badges.add(b);
                                        break;
                                    case CHALLENGE:
                                        challengeBadge = challengeBadge == null ? b :
                                                (challengeBadge.getActivityCount() != null && b.getActivityCount() != null &&
                                                        challengeBadge.getActivityCount() < b.getActivityCount() ? b : challengeBadge);
                                        break;
                                    case ACTIVITY:
                                        activityBadge = activityBadge == null ? b : (activityBadge.getActivityCount() < b.getActivityCount() ? b : activityBadge);
                                        break;
                                    case STREAK:
                                        streakBadge = streakBadge == null ? b :
                                                (Objects.equals(b.getBadgeCategory(), "#MoveMoreChallenge") ?
                                                        (Objects.equals(streakBadge.getBadgeCategory(), "#MoveMoreChallenge") ? (streakBadge.getActivityCount() < b.getActivityCount() ? b : streakBadge) : b)
                                                        : Objects.equals(streakBadge.getBadgeCategory(), "#MoveMoreChallenge") ? streakBadge : streakBadge.getActivityCount() < b.getActivityCount() ? b : streakBadge);
                                        break;
                                    case QUANTUM:
                                        switch (b.getSubType()) {
                                            case CALORIE:
                                                caloriesBadge = caloriesBadge == null ? b : (caloriesBadge.getActivityCount() < b.getActivityCount() ? b : caloriesBadge);
                                                break;
                                            case BOOSTER:
                                                boosterBadge = boosterBadge == null ? b : (boosterBadge.getActivityCount() < b.getActivityCount() ? b : boosterBadge);
                                                break;
                                            case DURATION:
                                                durationBadge = durationBadge == null ? b : (durationBadge.getActivityCount() < b.getActivityCount() ? b : durationBadge);
                                                break;
                                            case ENERGY_SCORE:
                                                energyBadge = energyBadge == null ? b : (energyBadge.getActivityCount() < b.getActivityCount() ? b : energyBadge);
                                                break;
                                        }
                                        break;
                                }
                            }
                        }
                    }
                }
                if (challengeBadge != null) badges.add(challengeBadge);
                if (activityBadge != null) badges.add(activityBadge);
                if (streakBadge != null) badges.add(streakBadge);
                if (durationBadge != null) badges.add(durationBadge);
                if (caloriesBadge != null) badges.add(caloriesBadge);
                if (energyBadge != null) badges.add(energyBadge);
                if (boosterBadge != null) badges.add(boosterBadge);
                badgesWidget.setBadges(badges);
                if (isMyProfile) {
                    badgesWidget.setShowAll(true);
                    if (AppUtil.isBadgeSharingSupported(userContext)) {
                        badgesWidget.setShare(true);
                    }
                    if (AppUtil.isCommunityTabSupported(userContext, segmentEvaluatorService, environmentService))
                        badgesWidget.setCommunityUser(true);
                    badgesWidget.setSelfUser(true);
                    Action seeAction = new Action();
                    seeAction.setActionType(ActionType.NAVIGATION);
                    seeAction.setUrl("curefit://dashboardv2");
                    badgesWidget.setSeeAllAction(seeAction);
                }
                badgesWidget.setUserName(userEntry.getFirstName());
                if (boosterBadge != null || energyBadge != null || caloriesBadge != null || durationBadge != null || streakBadge != null || activityBadge != null) {
                    page.addWidget(badgesWidget);
                }
            }

            /*if ((!isPrivate || isMyProfile) && isCommunityEnabled) {
                ListGroupsWidget listGroupsWidget = new ListGroupsWidget();
                ListGroupResponse listGroupResponse = listGroupResponseCF.get();
                List<GroupMeta> groupList = new ArrayList<>();
                if (listGroupResponse != null && !Collections.isEmpty(listGroupResponse.getData())) {
                    boolean showGroups = false;
                    for (ListGroupResponse.GroupResponse item : listGroupResponse.getData()) {
                        if (isMyProfile || !item.isPrivate()) {
                            showGroups = true;
                            GroupMeta meta = new GroupMeta();
                            meta.setTitle(item.getTitle());
                            meta.setImageUrl(CommunityUtil.getGroupImageUrl(item));
                            meta.setLocation(item.getLocation() != null ? item.getLocation().getLocality() : "");
                            meta.setCount(item.getMembersCount());
                            meta.setGroupId(item.getSocialGroupId());
                            groupList.add(meta);
                        }
                    }
                    if( showGroups) {
                        listGroupsWidget.setGroupList(groupList);
                        Header groupHeader = new Header();
                        groupHeader.setTitle("Groups");
                        listGroupsWidget.setHeader(groupHeader);
                        listGroupsWidget.setShowCount(2);
                        listGroupsWidget.setShowMore(groupList.size() > 2);
                        page.addWidget(listGroupsWidget);
                    }
                }

                MultimediaWidget multimediaWidget = new MultimediaWidget();
                List<UserMultiMediaResponse.MultiMedia> listMedia = new ArrayList<>();
                UserMultiMediaResponse multiMediaResponse = userMultiMediaResponseCF.get();
                multimediaWidget.setSocialUserId(socialUserId);
                if (multiMediaResponse.getMultiMediaList() != null && multiMediaResponse.getMultiMediaList().size() > 0) {
                    for (UserMultiMediaResponse.MultiMedia mediaResponse : multiMediaResponse.getMultiMediaList()) {
                        if (isMyProfile || !mediaResponse.isPrivate()) {
                            listMedia.add(mediaResponse);
                            if (listMedia.size() == 4) {
                                break;
                            }
                        }
                    }
                    multimediaWidget.setListMedia(listMedia);
                    if (!Collections.isEmpty(multiMediaResponse.getMultiMediaList()) && multiMediaResponse.getMultiMediaList().size() > 4) {
                        multimediaWidget.setShowMore(true);
                    }
                    page.addWidget(multimediaWidget);
                }
            }*/
        }
        /*if ((!isPrivate || isMyProfile) && isCommunityEnabled) {
            try {
                CommunityFeedResponse response = userPostsCF.get();
                List<CommunityPostResponse> listPost = response.getCommunityPostResponseList();
                listPost = CommunityUtil.filterCommunityPostResponseByAppVersion(userContext, listPost).stream().sorted((a, b) -> Long.compare(b.getCreatedAt(), a.getCreatedAt())).toList();
                boolean isFirst = true;
                for (CommunityPostResponse post : listPost) {
                    if (post.getPostType() == POST && (isMyProfile || !post.isPrivate())) {
                        PostWidget postWidget = new PostWidget();
                        if (post != null && post.getSocialAuthor() != null && post.getSocialAuthor().getSocialUser() != null && post.getSocialAuthor().getSocialUser().getDisplayName() != null && post.getSocialAuthor().getSocialUser().getId() != null) {
                            SocialUser socialUser = post.getSocialAuthor().getSocialUser();
                            socialUser.setDisplayName(socialUser.getDisplayName().replace("null", "").trim());
                            post.getSocialAuthor().setSocialUser(socialUser);
                        }
                        postWidget.setData(post);
                        if (isFirst) {
                            Header header = new Header();
                            header.setTitle("Posts");
                            postWidget.setHeader(header);
                            isFirst = false;
                        }
                        List<Option> optionsPost = new ArrayList<>();

                        Action shareActionPost = new Action();
                        shareActionPost.setActionType(ActionType.SHARE_POST_DETAILS);
                        shareActionPost.setMeta(new SharePostMeta(post.getSocialPostId(), ""));
                        optionsPost.add(new Option("SHARE", "Share", shareActionPost));


                        if (isMyProfile) {
                            Action deleteAction = new Action();
                            deleteAction.setActionType(ActionType.DELETE_POST);
                            deleteAction.setMeta(new DeletePostMeta(post.getSocialPostId()));
                            optionsPost.add(new Option("DELETE", "Delete", deleteAction));
                        } else {
                            Action reportAction = new Action();
                            reportAction.setActionType(ActionType.REPORT_POST);
                            reportAction.setMeta(new ReportPostMeta(post.getSocialPostId()));
                            optionsPost.add(new Option("REPORT", "Report", reportAction));
                        }

                        postWidget.setOptions(optionsPost);

                        postWidget.setPageType("profilePage");

                        page.addWidget(postWidget);
                    }
                }
                page.setNextCursor(response.getNextCursor());

            } catch (Exception e) {
                log.info("profile page EXCEPTION ", e);
                e.printStackTrace();
            }
        }*/

        return page;
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    private CultBooking getBooking(UserContext userContext, Long bookingID) {
        List<String> userIds = new ArrayList<>();
        String userId = userContext.getUserProfile().getUserId();
        userIds.add(userId);
        CultBookingAndWaitlistRequest request = new CultBookingAndWaitlistRequest();
        request.setUserId(userId);
        List<Long> bookingIds = new ArrayList<>();
        bookingIds.add(bookingID);
        request.setBookingIds(bookingIds);
        BulkBookingResponse bulkBookingResponse = cultServiceImpl.getCultBookingsAndWaitlists(request).get();
        if (bulkBookingResponse != null) {
            List<CultBooking> bookings = bulkBookingResponse.getBookings().stream().filter(booking -> booking.getId().equals(bookingID)).collect(toList());
            if (!bookings.isEmpty()) {
                return bookings.get(0);
            }
        }
        return null;
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    private LatestBookingDetail getLatestBookingForWorkoutId(UserContext userContext, String workoutId) {
        LocalDate endDate = LocalDate.now(ZoneId.of(userContext.getUserProfile().getTimezone()));
        LocalDate startDate = LocalDate.now(ZoneId.of(userContext.getUserProfile().getTimezone())).minusDays(30);
        List<String> userIds = new ArrayList<>();
        String userId = userContext.getUserProfile().getUserId();
        userIds.add(userId);
        Map<String, BulkBookingResponse> map = cultServiceImpl.getAllBooking(userIds, startDate.toString(), endDate.toString()).get();
        if (map.containsKey(userId)) {
            BulkBookingResponse bulkBookingResponse = map.get(userId);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            List<CultBooking> cultBookings = bulkBookingResponse.getBookings().stream().sorted((a, b) -> {
                try {
                    return sdf.parse(a.getCultClass().getDate()).compareTo(sdf.parse(b.getCultClass().getDate()));
                } catch (ParseException e) {
                    log.error("HRXProgression, failed to parse dates a:{}, b:{}", a.getCultClass().getDate(), b.getCultClass().getDate(), e);
                    return 0;
                }
            }).toList();
            for (int i = cultBookings.size() - 1; i > -1; i--) {
                CultBooking booking = cultBookings.get(i);
                log.debug("HRXProgression: CultBooking {}", booking.toString());
                if (booking.getLabel().equals(BookingLabel.COMPLETED) && booking.getCultClass() != null && booking.getCultClass().getWorkoutID() != null && booking.getCultClass().getWorkoutID().toString().equals(workoutId)) {
                    log.debug("HRXProgression: ClassId {}", booking.getId().toString());
                    LatestBookingDetail response = new LatestBookingDetail();
                    response.setBookingId(booking.getId().toString());
                    response.setClassId(booking.getCultClass().getId().toString());
                    response.setWorkoutId(booking.getCultClass().getWorkoutID());
                    return response;
                }
            }
        }
        log.debug("HRXProgression: getLatestBookingForWorkoutId return null");
        return null;
    }

    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    private LatestBookingDetail getLatestBooking(UserContext userContext) {
        LocalDate endDate = LocalDate.now(ZoneId.of(userContext.getUserProfile().getTimezone()));
        LocalDate startDate = LocalDate.now(ZoneId.of(userContext.getUserProfile().getTimezone())).minusDays(30);
        List<String> userIds = new ArrayList<>();
        String userId = userContext.getUserProfile().getUserId();
        userIds.add(userId);
        Map<String, BulkBookingResponse> map = cultServiceImpl.getAllBooking(userIds, startDate.toString(), endDate.toString()).get();
        if (map.containsKey(userId)) {
            BulkBookingResponse bulkBookingResponse = map.get(userId);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            List<CultBooking> cultBookings = bulkBookingResponse.getBookings().stream().sorted((a, b) -> {
                try {
                    return sdf.parse(a.getCultClass().getDate()).compareTo(sdf.parse(b.getCultClass().getDate()));
                } catch (ParseException e) {
                    return 0;
                }
            }).toList();
            for (int i = cultBookings.size() - 1; i > -1; i--) {
                CultBooking booking = cultBookings.get(i);
                if (booking.getLabel().equals(BookingLabel.COMPLETED) && booking.getCultClass() != null) {
                    LatestBookingDetail response = new LatestBookingDetail();
                    response.setBookingId(booking.getId().toString());
                    response.setClassId(booking.getCultClass().getId().toString());
                    response.setWorkoutId(booking.getCultClass().getWorkoutID());
                    return response;
                }
            }
        }
        log.error("No Booking for found for user={} startDate={} endDate={}", userId, startDate.toString(), endDate.toString());
        return null;
    }

    private CompletableFuture<UserEntry> getUserEntryCF(String userId) {
        try {
            return userServiceClient.getUser(userId);
        } catch (RuntimeBaseException e) {
            log.error("error in getUserEntryCF", e);
        }
        return null;
    }
    
    @SneakyThrows({InterruptedException.class, ExecutionException.class})
    public EditProfilePage getEditProfilePage(UserContext userContext, Boolean isCreateProfile) throws HttpException {
        EditProfilePage editProfilePage = new EditProfilePage();
        editProfilePage.setIsCreateProfile(isCreateProfile);

        String userId = userContext.getUserProfile().getUserId();
//        CommunityUserEntry userEntry = ragnarServiceClient.getUserByCultUserId(userId);
//        editProfilePage.setInstagramHandle(userEntry.getInstagramHandle());
        try {
            if (isCreateProfile) {
                Action minorAgeRaiseTicketAction = getMinorAction(serviceInterfaces);
                editProfilePage.setMinorAgeRaiseTicketAction(minorAgeRaiseTicketAction);

            }
            UserEntry user = userServiceClient.getUser(userId).get();
            editProfilePage.setName(user.getFirstName() + ((user.getLastName() != null && !"".equals(user.getLastName()) && !"null".equals(user.getLastName())) ? " " + user.getLastName() : ""));
            if (user.getBirthday() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                String formattedDob = sdf.format(user.getBirthday());
                editProfilePage.setDob(formattedDob);
            }
            if (user.getGender() != null) {
                editProfilePage.setGender(user.getGender());
            }
            editProfilePage.setProfilePictureUrl(user.getProfilePictureUrl());
            editProfilePage.setNameEditable(!MembershipUtil.activeMembershipPresent(membershipService, userContext));

            UserMetricValue heightMetric = null, weightMetric = null;
            try {
                heightMetric = serviceInterfaces.metricClient.getLatestUserMetricValue(userContext.getUserProfile().getUserId(), 2L);
            } catch (Exception e) {
                log.error("SUPPRESS::height metric fetch failed for userId:" + userContext.getUserProfile().getUserId(), e);
            }
            try {
                weightMetric = serviceInterfaces.metricClient.getLatestUserMetricValue(userContext.getUserProfile().getUserId(), 3L);
            } catch (Exception e) {
                log.error("SUPPRESS::weight metric fetch failed for userId:" + userContext.getUserProfile().getUserId(), e);
            }
            if (heightMetric != null && heightMetric.getValue() != null) {
                editProfilePage.setHeight(String.valueOf(heightMetric.getValue()));
            } else {
                editProfilePage.setHeight("");
            }
            if (weightMetric != null && weightMetric.getValue() != null) {
                editProfilePage.setWeight(String.valueOf(weightMetric.getValue()));
            } else {
                editProfilePage.setWeight("");
            }
        } catch (BaseException e) {
            log.error("error", e);
        }
        SquadVisibilityType squadVisibilityType = getSquadVisibility(userId, serviceInterfaces);

        UserProfileEntry profileEntry = cultServiceImpl.getCultUserById(userId);
        editProfilePage.setUserProfile(profileEntry);
        editProfilePage.setSquadData(
                ProfileSquadData.builder()
                        .squadVisibility(squadVisibilityType)
                        .squadVisibiltyTitle("Squad Invitation")
                        .anySquadTitle("Any cult member can invite me")
                        .phoneSquadTitle("Only with phone number")
                        .anySquadSubText("Anyone can request and view my \n" +
                                "bookings. You decide to accept or reject.")
                        .phoneSquadSubText("Only users with my contact details \n" +
                                "can send requests.")
                        .anySquadState("Everyone")
                        .phoneSquadState("Only with contact number")
                        .build());
        editProfilePage.setProfileVisibilityInfo(populateProfileInfo());
        return editProfilePage;
    }

    private ProfileVisibilityFilterSet populateProfileInfo() {
        ProfileVisibilityFilterSet profileVisibilityFilterSet = new ProfileVisibilityFilterSet();
        List<ProfileVisibilityFilters> profileVisibilityFilters = new ArrayList<>();
        ProfileVisibilityFilters profileVisibilityFilter1 = new ProfileVisibilityFilters();

        profileVisibilityFilter1.setText("Everyone");
        profileVisibilityFilter1.setSubText("Visible to everyone, both cult and non-cult members.");
        profileVisibilityFilter1.setProfileVisibility(ProfileVisibility.PUBLIC);
        profileVisibilityFilters.add(profileVisibilityFilter1);

        ProfileVisibilityFilters profileVisibilityFilter3 = new ProfileVisibilityFilters();
        profileVisibilityFilter3.setText("Members of cult");
        profileVisibilityFilter3.setSubText("Visible only to fellow cult members.");
        profileVisibilityFilter3.setProfileVisibility(ProfileVisibility.CULT_MEMBERS);
        profileVisibilityFilters.add(profileVisibilityFilter3);

        ProfileVisibilityFilters profileVisibilityFilter2 = new ProfileVisibilityFilters();
        profileVisibilityFilter2.setText("My contact");
        profileVisibilityFilter2.setSubText("Visible only to your contacts.");
        profileVisibilityFilter2.setProfileVisibility(ProfileVisibility.MY_CONTACTS);
        profileVisibilityFilters.add(profileVisibilityFilter2);

        ProfileVisibilityFilters profileVisibilityFilter4 = new ProfileVisibilityFilters();
        profileVisibilityFilter4.setText("Private profile");
        profileVisibilityFilter4.setSubText("Your profile will remain private and will only be visible to squad members");
        profileVisibilityFilter4.setProfileVisibility(ProfileVisibility.PRIVATE);
        profileVisibilityFilters.add(profileVisibilityFilter4);
        profileVisibilityFilterSet.setProfileVisibilityFiltersList(profileVisibilityFilters);
        return profileVisibilityFilterSet;
    }

    private List<UserMetricValuePojo> generateMetricServicePayload(UserContext userContext, Double height, Double weight) {
        List<UserMetricValuePojo> list = new ArrayList<UserMetricValuePojo>();
        // we only send height and weight to metrics service
        if (height != null) {
            UserMetricValuePojo heightPojo = new UserMetricValuePojo();
            heightPojo.setMetricId(2L); // sensitive
            heightPojo.setUserId(userContext.getUserProfile().getUserId());
            heightPojo.setIsSelf(true);
            heightPojo.setValue(height);
            heightPojo.setSource(userContext.getSessionInfo().getOrderSource());
            heightPojo.setMetricDate(new Date());
            list.add(heightPojo);
        }

        if (weight != null) {
            UserMetricValuePojo weightPojo = new UserMetricValuePojo();
            weightPojo.setMetricId(3L); // sensitive
            weightPojo.setUserId(userContext.getUserProfile().getUserId());
            weightPojo.setIsSelf(true);
            weightPojo.setValue(weight);
            weightPojo.setSource(userContext.getSessionInfo().getOrderSource());
            weightPojo.setMetricDate(new Date());
            list.add(weightPojo);
        }

        return list;
    }
    
    public APIStatus updateProfile(UserContext userContext, UpdateProfile updateProfile) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            // UpdateCommunityUserRequest updateCommunityUserReq = new UpdateCommunityUserRequest();
            // updateCommunityUserReq.setInstagramHandle(updateProfile.getInstagramHandle());
            // updateCommunityUserReq.setUserId(userId);
            // ragnarServiceClient.updateCommunityUser(updateCommunityUserReq);
            // ragnarServiceClient.updateAvatarUrl(userId, updateProfile.getProfilePictureUrl());
            UserEntry userEntry = userServiceClient.getUser(userId).get();
            String[] name = updateProfile.getName().split(" ");
            String firstName = name[0];
            StringBuilder lastName = new StringBuilder();
            for (int i = 1; i < name.length; i++) {
                if (name[i] != null && !"".equals(name[i]) && !"null".equals(name[i]))
                    lastName.append(name[i]);
            }
            userEntry.setFirstName(firstName);
            userEntry.setLastName(lastName.toString());
            userEntry.setProfilePictureUrl(updateProfile.getProfilePictureUrl());
            // DOB
            String dateOfBirth = updateProfile.getDob();
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date date = sdf.parse(dateOfBirth);
            userEntry.setBirthday(date);

            userEntry.setGender(updateProfile.getGender());

            userServiceClient.updateByUserId(userId, userEntry);
            updateProfile.getUserProfile().setProfileAttributes(null);
            updateProfile.getUserProfile().setUserId(userId);
            socialServiceClient.patchUserProfileV2(updateProfile.getUserProfile());
            try {
                Double height = updateProfile.getHeight() != null && !updateProfile.getHeight().isEmpty() ? Double.parseDouble(updateProfile.getHeight()) : null;
                Double weight = updateProfile.getWeight() != null && !updateProfile.getWeight().isEmpty() ? Double.parseDouble(updateProfile.getWeight()) : null;
                serviceInterfaces.metricClient.addUserMetricValues(this.generateMetricServicePayload(userContext, height, weight));
            } catch (MetricClientException e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            log.error("Exception : in updateProfile ", updateProfile, e);
            return new APIStatus(false);
        }
        return new APIStatus(true);
    }
    
    public APIStatus updateVisibility(UserContext userContext, UpdateVisibility updateVisibility) {
        String userId = userContext.getUserProfile().getUserId();
        try {
            // TODO: refactor and make single UserProfileEntry pojo across all services
            UserProfileEntry profileEntry = cultServiceImpl.getCultUserById(userId);
            profileEntry.setVisibility(updateVisibility.getVisibility());
            ObjectMapper mapper = new ObjectMapper();
            ObjectWriter ow = mapper.writer().withDefaultPrettyPrinter();
            String json = ow.writeValueAsString(profileEntry);
            com.curefit.socialservice.pojo.UserProfileEntry userProfile = mapper.readValue(json, com.curefit.socialservice.pojo.UserProfileEntry.class);
            socialServiceClient.patchUserProfile(userProfile);
        } catch (Exception e) {
            e.printStackTrace();
            return new APIStatus(false);
        }
        return new APIStatus(true);
    }

    public Action getMinorAction(ServiceInterfaces interfaces) throws HttpException {
        List<SupportArticle> issueList = interfaces.supportArticleService.getArticlesFromIds(List.of("1942"));
        Action minorAgeRaiseTicketAction = new Action();
        minorAgeRaiseTicketAction.setActionType(ActionType.RAISE_TICKET);
        minorAgeRaiseTicketAction.setTitle("Contact Support");
        minorAgeRaiseTicketAction.setIssueId("1942");
        RaiseTicketParams raiseTicketParams = new RaiseTicketParams();
        raiseTicketParams.setConfirmationMessage("Cult.fit services are available for users aged 18 and above. In case you are under 18, kindly attach a proof of age, and we will help you out with the next steps.");
        raiseTicketParams.setIssueDescription("I am under 18 years of Age");
        minorAgeRaiseTicketAction.setRaiseTicketParams(raiseTicketParams);
        return minorAgeRaiseTicketAction;
    }
    
    private List<RankedLeadershipBoardEntry> setRankInSortedLeaderboard(List<RankedLeadershipBoardEntry> leadershipBoardEntryList) {
        int lastClashedRank = 0;
        for(int i = 0 ; i < leadershipBoardEntryList.size(); i++) {
            RankedLeadershipBoardEntry leadershipBoardEntryWithRank = leadershipBoardEntryList.get(i);
            if (i > 0 && leadershipBoardEntryWithRank.getSessionDone() == leadershipBoardEntryList.get(
                    i - 1).getSessionDone() &&
                    leadershipBoardEntryWithRank.getStepsEntry().getStepsCount() == leadershipBoardEntryList.get(
                            i - 1).getStepsEntry().getStepsCount()
            ) {
                if (lastClashedRank != 0) {
                    leadershipBoardEntryList.get(i).setRank(lastClashedRank);
                } else {
                    leadershipBoardEntryList.get(i).setRank(i);
                    lastClashedRank = i;
                }
            } else {
                lastClashedRank = lastClashedRank + 1;
                leadershipBoardEntryList.get(i).setRank(lastClashedRank);
            }
        }
        return leadershipBoardEntryList;
    }

    public SquadsHighlightData getSquadHighlights(UserContext userContext, List<LeadershipBoardEntry> leadershipBoardEntries, int weekOffset, Boolean setTitle) throws ExecutionException, InterruptedException {
        List<SquadsHighlightData.RankItem> rankItems = new ArrayList<>();
        int index = 0;

        List<String> userIds = leadershipBoardEntries.stream()
                .map(e -> e.getUserId())
                .collect(Collectors.toList());
        Map<String, UserEntry> userEntryMap = getUserEntryMap(userIds);

        List<RankedLeadershipBoardEntry> leadershipBoardEntriesWithRank = leadershipBoardEntries.stream().map(leadershipBoardEntry -> new RankedLeadershipBoardEntry(leadershipBoardEntry, 0)).collect(toList());

        log.info("[SQUAD_TESTING] leadershipBoardEntriesWithRank:: {}", leadershipBoardEntriesWithRank);

        leadershipBoardEntriesWithRank = setRankInSortedLeaderboard(leadershipBoardEntriesWithRank);

        log.info("[SQUAD_TESTING] leadershipBoardEntriesWithRank updated:: {}", leadershipBoardEntriesWithRank);

        for (RankedLeadershipBoardEntry value : leadershipBoardEntriesWithRank) {
            log.info("leadershipBoardEntries value : {}", value.getUserId());
            if ((value.getSessionDone() == null || Objects.equals(value.getSessionDone(), 0L))
                    && !Objects.equals(value.getUserId(), userContext.getUserProfile().getUserId())) {
                continue;
            }
            UserEntry userEntryCF = userEntryMap.get(value.getUserId());
            Action profileClickAction = Action
                    .builder()
                    .actionType(ActionType.NAVIGATION)
                    .url("curefit://social_user_profile?cultUserId=" + value.getUserId())
                    .build();
            if (userEntryCF != null) {
                SquadsHighlightData.RankItem rankItem = new SquadsHighlightData.RankItem(value.getUserId(),
                        Objects.equals(value.getUserId(), userContext.getUserProfile().getUserId()) ? "You"
                                : userEntryCF.getFirstName(),
                        userEntryCF.getProfilePictureUrl(),
                        value.getSessionDone(),
                        Objects.equals(value.getUserId(), userContext.getUserProfile().getUserId()),
                        getRunnerImage(),
                        getTrackImage(),
                        profileClickAction,
                        index == 0 ? "/image/community/squads/crown_large.png" : null,
                        value.getRank()
                );
                log.info("[SQUAD_TESTING] rankItem :: {}", rankItem);

                rankItems.add(rankItem);
            }
            index++;
        }

        SquadsHighlightData squadsHighlightData = new SquadsHighlightData();
        if(setTitle == true) {
            squadsHighlightData.setTitle(weekOffset == 0 ? "Highlights of last week" : "Highlights of the week");
        }
        squadsHighlightData.setIndexTitle("#Rank");
        squadsHighlightData.setRankTitle("Activity");
        squadsHighlightData.setRankItems(rankItems);
        squadsHighlightData.setIndexThreshold(Math.min(4, rankItems.size()));
        squadsHighlightData.setWidgetType(WidgetType.SQUAD_HIGHLIGHTS);
        squadsHighlightData.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("20", "10"));
        return squadsHighlightData;
    }

    public UpcomingItemCarousal getUpcomingActivities(List<UpcomingClassEntry> upcomingClassEntries, UserContext userContext, ServiceInterfaces interfaces) {
        if (upcomingClassEntries == null) return null;

        List<CarouselUpcomingItem> upcomingItemList = new ArrayList<>();

        upcomingClassEntries.forEach(item -> {
            if (item.getClassId() != null) {
                Action bookClassAction = Action.builder()
                        .actionType(ActionType.NAVIGATION)
                        .title("JOIN")
                        .url(item.getBookingNumber() != null ?
                                ("curefit://cultClass?bookingNumber=" + item.getBookingNumber()) :
                                item.getWlBookingNumber() != null ?
                                        ("curefit://cultClass?bookingNumber=" + item.getWlBookingNumber() + "&isWaitlistBooking=true") :
                                        "curefit://prebookClass?classId=" + item.getClassId())
                        .build();
                List<String> userAvatars = new ArrayList<>();
                if (item.getUserIds() == null || item.getUserIds().isEmpty() ||
                        (item.getUserIds().size() == 1
                                && Objects.equals(item.getUserIds().get(0), userContext.getUserProfile().getUserId()))) {
                    return;
                }

                Map<String, UserEntry> userEntryMap = getUserEntryMap(item.getUserIds());

                item.getUserIds().forEach(userId -> {
                    if (!Objects.equals(userId, userContext.getUserProfile().getUserId())) {
                        UserEntry userEntryCF = userEntryMap.get(userId);
                        if (userEntryCF != null) userAvatars.add(userEntryCF.getProfilePictureUrl());
                    } else {
                        bookClassAction.setDisabled(true);
                        bookClassAction.setTitle("JOINED");
                    }
                });
                String workoutDateStr = "";
                if (item.getDate() != null) {
                    LocalDate workoutDate = LocalDate.parse(item.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    workoutDateStr = String.format("%s %s",
                            DateTimeFormatter.ofPattern("d").format(workoutDate),
                            DateTimeFormatter.ofPattern("MMM").format(workoutDate));
                }
                String workoutImage = item.getWorkoutImageUrl() != null ? item.getWorkoutImageUrl() : "https://cdn-images.cure.fit/www-curefit-com/image/upload/c_fill,w_404,ar_1.31,q_auto:eco,dpr_2,f_auto,fl_progressive/image/mem-exp/generic.png";
                CarouselUpcomingItem upcomingItem = new CarouselUpcomingItem(
                        bookClassAction,
                        workoutImage,
                        item.getWorkoutName(),
                        workoutDateStr + " " + item.getStartTime(),
                        null,
                        item.getCenterName(),
                        "joining",
                        userAvatars);
                upcomingItemList.add(upcomingItem);
            }
        });

        UpcomingItemCarousal upcomingItemCarousal = new UpcomingItemCarousal("Upcoming classes of the squad", upcomingItemList, true);
        upcomingItemCarousal.setWidgetType(WidgetType.UPCOMING_ITEM_CAROUSAL);
        return upcomingItemCarousal;
    }


    public static Map<String, UserEntry> getUserEntryMap(List<String> userIds) {
        return CommunityUtil.getBulkUsersEntryMap(userIds, userServiceClient);
    }

    public SquadsLeaderboard getSquadLeaderboard(UserContext userContext, List<LeadershipBoardEntry> leadershipBoardEntries, int selfSessionCount, int weekOffset, Long communityId, Boolean setTitle, ChallengeResponse challengeResponse) throws ExecutionException, InterruptedException {
        if (leadershipBoardEntries == null) return null;
        String title = null;
        if(setTitle) {
            title = weekOffset == 0 ? "This week's Leaderboard" : "Squad Leaderboard";
        }
        String profileImageUrl = "";
        CompletableFuture<UserEntry> userEntryCF = getUserEntryCF(userContext.getUserProfile().getUserId());
        if (userEntryCF != null) profileImageUrl = userEntryCF.get().getProfilePictureUrl();

        List<String> userIds = leadershipBoardEntries.stream()
                .map(LeadershipBoardEntry::getUserId)
                .collect(Collectors.toList());

        Map<String, UserEntry> userEntryMap = getUserEntryMap(userIds);

        List<SquadsLeaderboard.LeaderboardItem> leaderboardItemsList = new ArrayList<>();


        leadershipBoardEntries.forEach(value -> {
            try {
                UserEntry userEntry = userEntryMap.get(value.getUserId());
                SquadsLeaderboard.LeaderboardItem item = getFormattedSquadLeaderboardItem(value, weekOffset, userEntry, serviceInterfaces, userContext, challengeResponse);
                item.setCommunityId(communityId);
                leaderboardItemsList.add(item);
            } catch (Exception e) {
                log.error("Error while formatting squad leaderboard : {}", e);
            }
        });


        int currentRank = 1;
        for (int i = 0; i < leaderboardItemsList.size(); i++) {
            SquadsLeaderboard.LeaderboardItem currentItem = leaderboardItemsList.get(i);

            if (i > 0) {
                long currentSteps = Optional.ofNullable(currentItem.getWeeklyActivityData().getSteps()).orElse(0L);
                long prevSteps = Optional.ofNullable(leaderboardItemsList.get(i - 1).getWeeklyActivityData().getSteps()).orElse(0L);
                Long currentSessions = currentItem.getWeeklyActivityData().getSessionCount();
                Long prevSessions = leaderboardItemsList.get(i - 1).getWeeklyActivityData().getSessionCount();

                if (currentSessions < prevSessions || currentSteps < prevSteps) currentRank += 1;
            }

            switch (currentRank) {
                case 1 -> currentItem.setRankImageUrl("/image/community/squads/rank_1_large.png");
                case 2 -> currentItem.setRankImageUrl("/image/community/squads/rank_2_large.png");
                case 3 -> currentItem.setRankImageUrl("/image/community/squads/rank_3_large.png");
                default -> currentItem.setRank("RANK " + currentRank);
            }
        }


        SquadsLeaderboard squadsLeaderboard = new SquadsLeaderboard(communityId, title, leaderboardItemsList, SQUAD_FEED_USER_LIMIT, "You", selfSessionCount, profileImageUrl, weekOffset, null, null, false);
        squadsLeaderboard.setWidgetType(WidgetType.SQUAD_COMMUNITY_FEED);
        squadsLeaderboard.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("50", "50"));
        if(challengeResponse!=null){
            squadsLeaderboard.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "10"));
        }else{
            squadsLeaderboard.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("50", "50"));
        }
        return squadsLeaderboard;
    }

    public SquadsPostDetail getSquadsPostDetail(UserContext userContext, int weekOffset, String userId) throws Exception {
        LeadershipBoardEntry entry = null;
        try {
            entry = socialServiceClient.getLeadershipBoardEntry(userId, getLastMonday(weekOffset));
        } catch (Exception e) {
            String message = String.format("Error while fetching post details : {}", e.getMessage(), e);
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        if (entry == null) return null;
        SquadsPostDetail squadsPostDetail = new SquadsPostDetail();
        try {
            CompletableFuture<UserEntry> userEntry = getUserEntryCF(userContext.getUserProfile().getUserId());
            if (userEntry != null) {
                SquadsLeaderboard.LeaderboardItem item = getFormattedSquadLeaderboardItem(entry, weekOffset, userEntry.get(), serviceInterfaces, userContext, null);
                squadsPostDetail.setLeaderboardItem(item);
            }
        } catch (Exception e) {
            String message = String.format("Error while formatting squad leaderboard item : {}", e);
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        Action navAction = Action.builder().actionType(ActionType.REACTION_NOTIF_ACTION).url("curefit://squads_feed").build();
        squadsPostDetail.setBackNavAction(navAction);
        return squadsPostDetail;
    }

    public Boolean deleteNotification(UserContext userContext, Long mappingEntryId) {
        if (!AppUtil.version75Check(userContext)) {
            try {
                return notificationCenterServiceClient.deleteNotification(userContext.getUserProfile().getUserId(),
                        NotificationCenterEntryType.SQUAD_INVITATION + "_" + mappingEntryId);
            } catch (Exception e) {
                log.error("notification deletion error : " + e.getMessage());
            }
        }
        return false;
    }

    public SquadInvitesScreen getSquadInvitesScreen(UserContext userContext) {
        SquadInvitesScreen squadInvitesScreen = new SquadInvitesScreen();
        List<BaseWidgetNonVM> widgets = new ArrayList<>();
        CFUserListSection pendingInvitesSection = this.getPendingInvitesSection(userContext);
        if (pendingInvitesSection != null && pendingInvitesSection.getWidgets() != null && !pendingInvitesSection.getWidgets().isEmpty()) {
            widgets.add(pendingInvitesSection);
        }

        squadInvitesScreen.setWidgets(widgets);
        squadInvitesScreen.setTitle("My invitations");
        return squadInvitesScreen;
    }

    private Map<String, String> extractUserContacts(SyncContactRequest contactRequest) {
        // If contactRequest is null, return an empty map
        if (contactRequest == null || contactRequest.getContacts() == null) {
            return new HashMap<>();
        }

        Map<String, String> contactMap = new HashMap<>();

        for (Contact contact : contactRequest.getContacts()) {
            if (contact.getPhoneNumbers() != null) {
                for (PhoneNumberDetail phoneNumberDetail : contact.getPhoneNumbers()) {
                    String sanitizedPhone = CommunityUtil.sanitizePhoneNumber(phoneNumberDetail.getValue());
                    contactMap.put(sanitizedPhone, contact.getDisplayName());
                }
            }
        }
        return contactMap;
    }

    public ContactSyncScreen getContactSyncScreen(UserContext userContext, UserContactSyncRequest userContactSyncRequest) {
        ContactSyncScreen contactSyncScreen = new ContactSyncScreen();
        List<ContactWidgetData> recommendedUsers = new ArrayList<>();

        String communityId = (userContactSyncRequest != null) ? userContactSyncRequest.getCommunityId() : null;
        SyncContactRequest contactRequest = (userContactSyncRequest != null) ? userContactSyncRequest.getContactSyncRequest() : null;

        // If contact list is present, prioritize contacts
        Map<String, String> userContacts = extractUserContacts(contactRequest);

        // No contact list, send system-recommended users
        boolean useSystemRecommended = (contactRequest == null);

        List<String> confirmedUsers = CommunityUtil.fetchConfirmedUsers(this.socialServiceClient, communityId, this.exceptionReportingService);

        List<String> pendingUsers = CommunityUtil.fetchPendingUsers(this.socialServiceClient, communityId, this.exceptionReportingService);

        // Fetch system-recommended users
        List<CommunityRecommendedUserEntry> recommendedUserEntries = new ArrayList<>();
        try {
            recommendedUserEntries = this.socialServiceClient.getRecommendedUsersV2(userContext.getUserProfile().getUserId());
        } catch (Exception e) {
            String message = String.format("Error in fetching recommended users, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }

        try {
            if (recommendedUserEntries != null && !recommendedUserEntries.isEmpty()) {
                List<String> recommendedUserIds = new ArrayList<>();
                for (CommunityRecommendedUserEntry recommendedUser : recommendedUserEntries) {
                    if (recommendedUser.getUserId() == null) continue;
                    if (Objects.equals(recommendedUser.getUserId(), userContext.getUserProfile().getUserId())) continue;
                    recommendedUserIds.add(recommendedUser.getUserId());
                }

                String timezone = userContext.getUserProfile().getCity().getTimezone();
                long currentTime = TimeUtil.now(timezone);
                Map<String, UserEntry> userEntryMap = getUserEntryMap(recommendedUserIds);
                for (CommunityRecommendedUserEntry recommendedUser : recommendedUserEntries) {
                    if (recommendedUser == null || recommendedUser.getUserId() == null) continue;

                    UserEntry userEntry = userEntryMap.get(recommendedUser.getUserId());
                    if (userEntry == null || userEntry.getFirstName() == null) continue;

                    List<Membership> membershipDetails = membershipService.getCachedMembershipsForUser(userEntry.getId().toString(), "curefit", MembershipFilter.builder().benefits(MembershipUtil.MEMBERSHIP_BENEFITS).status(Status.PURCHASED).status(Status.PAUSED).start(currentTime).end(currentTime).build()).get();
                    if (membershipDetails == null || membershipDetails.isEmpty()) {
                        continue;  // Skip non active membership
                    }


                    // Extract & sanitize user phone number
                    String originalPhoneNumber = Optional.ofNullable(userEntry.getPhone()).orElse("");
                    String userMobileNumber = CommunityUtil.sanitizePhoneNumber(originalPhoneNumber);

                    //  Only show users present in phonebook
                    if (!userContacts.containsKey(userMobileNumber)) {
                        continue;  // Skip this user
                    }

                    // Fetch name from phonebook (since we already checked it exists)
                    String displayName = userContacts.get(userMobileNumber);

                    ContactWidgetData recommendedUserWidget = new ContactWidgetData();
                    recommendedUserWidget.setName(displayName);

                    String subTitle = pendingUsers.contains(recommendedUser.getUserId()) ? "Invite already sent"
                            : confirmedUsers.contains(recommendedUser.getUserId()) ? "Already squad member"
                            : null;

                    boolean enabled = subTitle == null;

                    recommendedUserWidget.setSubTitle(subTitle);
                    recommendedUserWidget.setEnabled(enabled);
                    recommendedUserWidget.setAvatarUrl(userEntry.getProfilePictureUrl() != null
                            ? userEntry.getProfilePictureUrl()
                            : CONTENT_CDN_BASE_PATH + "image/community/default_male_thumbnail.png");
                    Action navAction = Action.builder()
                            .actionType(ActionType.NAVIGATION)
                            .url("curefit://social_user_profile?cultUserId=" + recommendedUser.getUserId())
                            .build();

                    recommendedUserWidget.setNavAction(navAction);
                    recommendedUserWidget.setUserId(recommendedUser.getUserId());
                    recommendedUsers.add(recommendedUserWidget);
                }
            }
        } catch (Exception e) {
            String message = String.format("Error in formatting suggestions, error :: %s", e.getMessage());
            log.error(message, e);
            serviceInterfaces.exceptionReportingService.reportException(message, e);
        }

        Action primaryAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .url("curefit://squads_manage")
                .title("MANAGE")
                .build();

        recommendedUsers.sort(Comparator.comparing(ContactWidgetData::getName,
                Comparator.nullsLast(Comparator.naturalOrder())));
        contactSyncScreen.setRecommendedUsers(recommendedUsers);
        if (recommendedUsers.isEmpty()) contactSyncScreen.setErrorMessage("Failed to fetch your contacts");
        if (!recommendedUsers.isEmpty()) contactSyncScreen.setTitle("Active cult members");
        contactSyncScreen.setConfirmationTitle("Successfully invited");
        contactSyncScreen.setConfirmationSubtitle("You will be able to see them once they accept the invite!");
        contactSyncScreen.setPrimaryConfirmationAction(primaryAction);
        contactSyncScreen.setAccessDeniedText("Contacts permission is required to continue. Please enable it in settings.");
        return contactSyncScreen;
    }

    public SquadCreationData getSquadCreationScreen(UserContext userContext, String inviteeUserId) {
        SquadCreationData squadCreationData = new SquadCreationData();
        CommunitiesResponse communitiesResponse = null;
        List<BaseWidgetNonVM> widgets = new ArrayList<>();

        try {
            communitiesResponse = this.socialServiceClient.getUserCommunities(userContext.getUserProfile().getUserId(), CommunityType.LEAGUE, false, 0, 50);
            CFUserListSection communitiesSection = this.getSelfUserCommunitiesSection(userContext, communitiesResponse.getCommunities(), inviteeUserId);
            widgets.add(communitiesSection);
        } catch (Exception e) {
            String message = String.format("Error in fetching communities, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }

        Action navAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .url("curefit://contact_sync?squadSizeLimit=20&inviteeUserId=" + inviteeUserId)
                .title("CREATE SQUAD")
                .build();

        Action confirmationAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .title("MANAGE")
                .url("curefit://squads_manage")
                .build();

        squadCreationData.setNavAction(navAction);
        squadCreationData.setWidgets(widgets);
        if (inviteeUserId != null) squadCreationData.setInviteeUserId(inviteeUserId);
        squadCreationData.setCreationButtonText("Create a new squad");
        squadCreationData.setConfirmationTitle("Successfully invited");
        squadCreationData.setConfirmationSubtitle("You will be able to see them once they accept the invite!");
        squadCreationData.setPrimaryConfirmationAction(confirmationAction);

        return squadCreationData;
    }

    public SquadManageScreen getSquadManageScreen(UserContext userContext) {
        SquadManageScreen squadManageScreen = new SquadManageScreen();
        List<BaseWidgetNonVM> widgets = new ArrayList<>();
        CultNotifObject invitesWidget = CommunityUtil.getSquadInvitationsWidget(userContext, serviceInterfaces);
        CFUserListSection sentInvitesSection = this.getSentInvitesSection(userContext, null);
        widgets.add(invitesWidget);
        CommunitiesResponse communitiesResponse = null;
        List<CommunityRecommendedUserEntry> recommendedUserEntries = new ArrayList<>();
        boolean hasCreatedCommunity = false;

        try {
            communitiesResponse = this.socialServiceClient.getUserCommunities(userContext.getUserProfile().getUserId(), CommunityType.LEAGUE, false, 0, 100);
        } catch (Exception e) {
            String message = String.format("Error in fetching communities, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }

        try {
            recommendedUserEntries = this.socialServiceClient.getRecommendedUsersV2(userContext.getUserProfile().getUserId());
        } catch (Exception e) {
            String message = String.format("Error in fetching recommended users, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }

        if (communitiesResponse != null && communitiesResponse.getCommunities() != null) {
            CFUserListSection userCommunitySection = this.getUserCommunitiesSection(userContext, communitiesResponse.getCommunities());
            if (userCommunitySection != null && !userCommunitySection.getWidgets().isEmpty())
                widgets.add(userCommunitySection);
            try {
                for (var community : communitiesResponse.getCommunities()) {
                    if (Objects.equals(community.getCreatorNode().getEntityId(), userContext.getUserProfile().getUserId())) {
                        hasCreatedCommunity = true;
                        break;
                    }
                }
            } catch (Exception e) {
                String message = String.format("Error in fetching community creatorId, error :: %s", e.getMessage());
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }
        }
        if (sentInvitesSection != null && !sentInvitesSection.getWidgets().isEmpty()) widgets.add(sentInvitesSection);
        if (communitiesResponse != null && communitiesResponse.getCommunityRecommendedUserEntry() != null) {
            CFUserListSection recommendedUsersSection = CommunityUtil.getRecommendedUsersSection(userContext, serviceInterfaces, recommendedUserEntries);
            if (recommendedUsersSection != null && !recommendedUsersSection.getWidgets().isEmpty())
                widgets.add(recommendedUsersSection);
        }

        List<Action> pageActions = new ArrayList<>();
        Action createCommunityAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .url("curefit://create_squad")
                .icon(ActionIcon.ADD)
                .build();
        if (AppUtil.isMultiSquadExpSegment(userContext, serviceInterfaces) || !hasCreatedCommunity) {
            pageActions.add(createCommunityAction);
        }

        squadManageScreen.setWidgets(widgets);
        squadManageScreen.setTitle("");
        squadManageScreen.setPageActions(pageActions);
        return squadManageScreen;
    }

    public SquadGoalIntroWidget getSquadGoalIntroWidget(UserContext userContext, ChallengeResponse challengeResponse) {
        SquadGoalIntroWidget squadGoalIntroWidget = new SquadGoalIntroWidget();
        ChallengeStatus challengeStatus = challengeResponse.getStatus();
        ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
        List<ChallengeUserMappingEntry> usersList = challengeCommunityMappingEntry.getChallengeUserMappings();

        squadGoalIntroWidget.setState(challengeStatus.name());
        squadGoalIntroWidget.setTitle("Squad goals");
        squadGoalIntroWidget.setTitleLottie("https://cdn-media.cure.fit/image/squad_goal/squad-goal.json");
        squadGoalIntroWidget.setHeaderIcon("/image/squad_goal/squad_goal_dart.png");
        squadGoalIntroWidget.setBgImage("/image/squad_goal/squad_goal_banner_bg.png");
        squadGoalIntroWidget.setCtaText("Tap to Find out more");
        squadGoalIntroWidget.setCommunityId(challengeCommunityMappingEntry.getCommunityId());
        squadGoalIntroWidget.setStartDate(challengeCommunityMappingEntry.getStart());
        squadGoalIntroWidget.setEndDate(challengeCommunityMappingEntry.getEnd());
        List<String> userIds = usersList.stream()
                .map(e -> e.getUserId())
                .collect(Collectors.toList());
        Map<String, UserEntry> userEntryMap = getUserEntryMap(userIds);
        squadGoalIntroWidget.setUsersList(userEntryMap);

        if(challengeStatus == ChallengeStatus.INVITED){
            //No one has joined yet
            squadGoalIntroWidget.setWidgetType(WidgetType.SQUAD_GOAL_INTRO_WIDGET);
            squadGoalIntroWidget.setBadgeText("New Squad Goal");
            squadGoalIntroWidget.setBody("Join your squad for 4 weeks of workout, momentum, & rewards.");
            squadGoalIntroWidget.setCtaAction(Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .url("curefit://squad_goal_intro_view")
                    .build());
            squadGoalIntroWidget.setLottie("https://cdn-media.cure.fit/image/squad_goal/new_goal.json");
            return squadGoalIntroWidget;
        }else if(challengeStatus == ChallengeStatus.UPCOMING){
            //Challenge is about to start
            squadGoalIntroWidget.setWidgetType(WidgetType.SQUAD_GOAL_UPCOMING_WIDGET);
            //Calculate days to go
            long daysToGo = TimeUtil.getDaysFromToday(challengeCommunityMappingEntry.getStart(), userContext.getUserProfile().getTimezone()) * -1;
            switch((int) daysToGo){
                case 1 -> squadGoalIntroWidget.setBody("1 day to go. Squad mode: almost on.");
                case 2 -> squadGoalIntroWidget.setBody("2 days left. Ready to move as one?");
                case 3 -> squadGoalIntroWidget.setBody("Ready? 3 days to go!  Workouts hit better with the squad.");
                default -> squadGoalIntroWidget.setBody(STR."\{daysToGo} \{daysToGo==1?"day":"days"} to go! It’s not solo, you’re working with your squad.");
            }
            squadGoalIntroWidget.setBadgeText("Squad Goal Upcoming");
            squadGoalIntroWidget.setCtaAction(Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .url("curefit://squad_goal_intro_view")
                    .build());
            squadGoalIntroWidget.setLottie("https://cdn-media.cure.fit/image/squad_goal/upcoming_goal.json");
            String acceptedBy = challengeCommunityMappingEntry.getMetadata().get("acceptedBy").toString();
            squadGoalIntroWidget.setAcceptedBy(userEntryMap.get(acceptedBy));
            return squadGoalIntroWidget;
        }
        return null;


    }

    public OngoingSquadGoalsBannerWidget getOngoingSquadBannerWidget(UserContext userContext, ChallengeResponse challengeResponse, int weekOffset) {
        ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
        List<ChallengeUserMappingEntry> usersList = challengeCommunityMappingEntry.getChallengeUserMappings();

        OngoingSquadGoalsBannerWidget ongoingSquadGoalsBannerWidget = new OngoingSquadGoalsBannerWidget();
        ongoingSquadGoalsBannerWidget.setWidgetType(WidgetType.ONGOING_SQUADS_BANNER_WIDGET);
        ongoingSquadGoalsBannerWidget.setStartDate(challengeCommunityMappingEntry.getStart());
        ongoingSquadGoalsBannerWidget.setEndDate(challengeCommunityMappingEntry.getEnd());
        ongoingSquadGoalsBannerWidget.setHintText("Your squad’s goal progress is based on the total workouts completed by all members. To hit the goal, your squad needs to collectively meet the weekly workout target.");

        int weeksFromStartDate = CommunityUtil.getWeeksFromStartDate(challengeCommunityMappingEntry.getStart(), challengeCommunityMappingEntry.getEnd(), weekOffset);
        ChallengeCommunityMappingEntry existingWeekProgress = challengeCommunityMappingEntry.getChildMappings().get(weeksFromStartDate);
        if(weeksFromStartDate == 0 && existingWeekProgress.getScoringDetails().getScore()<existingWeekProgress.getScoringDetails().getMilestone() && weekOffset==0){
            ongoingSquadGoalsBannerWidget.setTitleLottie("https://cdn-media.cure.fit/image/squad_goal/goal-mode-active.json");
        }
        ongoingSquadGoalsBannerWidget.setTitle("Squad Goal progress");
        ongoingSquadGoalsBannerWidget.setHeaderIcon("/image/squad_goal/squad_goal_dart.png");
        List<String> userIds = usersList.stream()
                .map(e -> e.getUserId())
                .collect(Collectors.toList());
        Map<String, UserEntry> userEntryMap = getUserEntryMap(userIds);
        ongoingSquadGoalsBannerWidget.setUsersList(userEntryMap);
        String acceptedBy = challengeCommunityMappingEntry.getMetadata().get("acceptedBy").toString();
        ongoingSquadGoalsBannerWidget.setAcceptedBy(userEntryMap.get(acceptedBy));

        switch (weeksFromStartDate){
            case 0 -> ongoingSquadGoalsBannerWidget.setDividerImage("/image/squad_goal/w1_divider.png");
            case 1 -> ongoingSquadGoalsBannerWidget.setDividerImage("/image/squad_goal/w2_divider.png");
            case 2 -> ongoingSquadGoalsBannerWidget.setDividerImage("/image/squad_goal/w3_divider.png");
            case 3 -> ongoingSquadGoalsBannerWidget.setDividerImage("/image/squad_goal/w4_divider.png");
            default -> ongoingSquadGoalsBannerWidget.setDividerImage("/image/squad_goal/end_divider.png");
        }

//        List<ScoringDetails> weeklySquadScoringDetails = new ArrayList<>();
        List<ScoringDetails> weeklyUserScoringDetails = new ArrayList<>();
        List<SquadGoalProgress> goalProgress = new ArrayList<>();

        for (int i = 0; i < challengeCommunityMappingEntry.getChildMappings().size(); i++) {
            //LOOPING THROUGH EACH WEEK'S MAPPING
            ChallengeCommunityMappingEntry childMapping = challengeCommunityMappingEntry.getChildMappings().get(i);
            ScoringDetails squadScoringDetails = childMapping.getScoringDetails();
            SquadGoalProgress squadGoalProgress = new SquadGoalProgress();
            squadGoalProgress.setMilestone(squadScoringDetails.getMilestone());
            squadGoalProgress.setScore(squadScoringDetails.getScore());
            squadGoalProgress.setIsCompleted(squadScoringDetails.isCompleted());
            String reward = Objects.toString(childMapping.getMetadata().get("rewardedAmount"), null);
            if(reward!=null){
                squadGoalProgress.setReward(reward);
            }
            //SET CENTER ICON
            if(squadScoringDetails.getScore()>=squadScoringDetails.getMilestone()){
                //GOAL WEEK COMPLETE
                squadGoalProgress.setIcon("/image/squad_goal/star_icon.png");
            }
            else if(i<(weeksFromStartDate+weekOffset) || i == (weeksFromStartDate+weekOffset)){
                //PAST WEEK
                squadGoalProgress.setIcon("/image/squad_goal/shooting_star_icon.png");
            }
            else{
                //FUTURE WEEK
                squadGoalProgress.setIcon("/image/squad_goal/lock_icon.png");
            }
            squadGoalProgress.setTitle(STR."Week \{i + 1}");

            for (ChallengeUserMappingEntry challengeUserMappingEntry : childMapping.getChallengeUserMappings()) {
                if (Objects.equals(challengeUserMappingEntry.getUserId(), userContext.getUserProfile().getUserId())) {
                    squadGoalProgress.setCurrentUserMilestone(challengeUserMappingEntry.getScoringDetails().getMilestone());
                    ScoringDetails scoringDetails = challengeUserMappingEntry.getScoringDetails();
                    weeklyUserScoringDetails.add(scoringDetails);
                }
            }
            goalProgress.add(squadGoalProgress);
        }

        goalProgress.get(weeksFromStartDate).setIsCurrentWeek(true);
        long workoutCount = goalProgress.get(weeksFromStartDate).getCurrentUserMilestone();
        String workoutText = workoutCount == 1 ? "workout" : "workouts";
        ongoingSquadGoalsBannerWidget.setBannerTitle(STR."All squad members must complete \{workoutCount} \{workoutText} this week. (Gym/ Group Classes/ Play)");
        ongoingSquadGoalsBannerWidget.setSquadScoringDetails(goalProgress);
        ongoingSquadGoalsBannerWidget.setCurrentWeekProgress(goalProgress.get(weeksFromStartDate));
        ongoingSquadGoalsBannerWidget.setBannerId(STR."\{challengeCommunityMappingEntry.getId()}_\{challengeCommunityMappingEntry.getStart()}_\{weeksFromStartDate}");

        //BUILD THE BODY
        SquadGoalProgress currentWeekProgress = goalProgress.get(weeksFromStartDate);

        if(weekOffset==0){
            //ACTIVE GOAL WEEK
            if(currentWeekProgress.getScore() == 0) {
                //GOAL WEEK STARTED
                ongoingSquadGoalsBannerWidget.setSquadGoalState(SquadGoalStateHelper.SquadGoalState.WEEK_STARTED);
                ongoingSquadGoalsBannerWidget.setBodyTitle("0%");
                ongoingSquadGoalsBannerWidget.setBodySubtitle("Goal Progress");
                ongoingSquadGoalsBannerWidget.setBody(STR."Everyone needs to do \{currentWeekProgress.getCurrentUserMilestone()} \{currentWeekProgress.getCurrentUserMilestone() == 1 ? "workout" : "workouts"} this week.");
                String image;
                String lottie;
                switch (weeksFromStartDate){
                    case 0 -> {
                        image = "/image/squad_goal/w1_0_state.png";
                        lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_01_State_0.json";
                    }
                    case 1 -> {
                        image = "/image/squad_goal/w2_0_state.png";
                        lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_02_State_0.json";
                    }
                    case 2 -> {
                        image = "/image/squad_goal/w4_0_state.png";
                        lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_03_State_0.json";
                    }
                    case 3 -> {
                        image = "/image/squad_goal/w3_0_state.png";
                        lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_04_State_0.json";
                    }
                    default -> {
                        image = "/image/squad_goal/w1_0_state.png";
                        lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_01_State_0.json";
                    }
                }
                ongoingSquadGoalsBannerWidget.setBodyImage(image);
                ongoingSquadGoalsBannerWidget.setBodyLottie(lottie);
            }
            else if(currentWeekProgress.getScore()>= currentWeekProgress.getMilestone()){
                //GOAL WEEK COMPLETE
                String reward = currentWeekProgress.getReward();
                if(reward == null){
                    //GOAL IS COMPLETE, BUT NO REWARD
                    ongoingSquadGoalsBannerWidget.setSquadGoalState(SquadGoalStateHelper.SquadGoalState.WEEK_IN_PROGRESS);
                    ongoingSquadGoalsBannerWidget.setBodyImage("/image/goal_completed_no_reward.png");
                    ongoingSquadGoalsBannerWidget.setBodyTitle(String.format("%.0f%%", ((double) currentWeekProgress.getScore() / currentWeekProgress.getMilestone()) * 100));
                    ongoingSquadGoalsBannerWidget.setBodySubtitle("Goal Progress");
                    String body;
                    String image;
                    String lottie;
                    switch (weeksFromStartDate){
                        case 0 -> {
                            body = "You did it! all squad members showed up";
                            image = "/image/squad_goal/w_completed.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_completion.json";
                        }
                        case 1 -> {
                            body = "You did it! all squad members showed up";
                            image = "/image/squad_goal/w_completed.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_completion.json";
                        }
                        case 2 -> {
                            body = "GOAL MET. Every rep. Every member. That’s teamwork!";
                            image = "/image/squad_goal/w_completed.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_completion.json";
                        }
                        case 3 -> {
                            body = "GOAL MET. Every rep. Every member. That’s teamwork!";
                            image = "/image/squad_goal/w_completed.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_completion.json";
                        }
                        default -> {
                            body = "You did it! all squad members showed up";
                            image = "/image/squad_goal/w_completed.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Week_completion.json";
                        }
                    }
                    ongoingSquadGoalsBannerWidget.setBodyImage(image);
                    ongoingSquadGoalsBannerWidget.setBodyLottie(lottie);
                    ongoingSquadGoalsBannerWidget.setBody(body);
                }else{
                    //GOAL WEEK COMPLETE WITH REWARD
                    ongoingSquadGoalsBannerWidget.setSquadGoalState(SquadGoalStateHelper.SquadGoalState.GOAL_COMPLETE);
                    ongoingSquadGoalsBannerWidget.setBodyTitle(reward.equals("1")?"Membership day":"Membership days");
                    ongoingSquadGoalsBannerWidget.setReward(reward);

                    String subtitle;
                    String body;
                    String image;
                    String lottie;
                    switch (weeksFromStartDate){
                        case 0: {
                            subtitle = "Perfect Week Achieved";
                            body = "Everyone showed up and crushed the workout!";
                            image = "/image/squad_goal/w1_reward.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Award_week_01.json";
                            break;
                        }
                        case 1: {
                            subtitle = "Perfect Week Achieved";
                            body = "You showed up. You crushed it. Awards all yours!";
                            image = "/image/squad_goal/w2_reward.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Award_week_02.json";
                            break;
                        }
                        case 2: {
                            subtitle = "Perfect Week Achieved \uD83C\uDF89";
                            body = "You showed up as one. This win’s yours!";
                            image = "/image/squad_goal/w3_reward.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Award_week_03.json";
                            break;
                        }
                        case 3: {
                            subtitle = "Squad effort. Squad win \uD83C\uDF89";
                            body = "4 weeks. No excuses. Just effort, consistency, and showing up";
                            image = "/image/squad_goal/w4_reward.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Award_week_04.json";
                            break;
                        }
                        default: {
                            subtitle = "Perfect Week Achieved";
                            body = "Everyone showed up and crushed the workout!";
                            image = "/image/squad_goal/w1_reward.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Award_week_01.json";
                            break;
                        }
                    }
                    ongoingSquadGoalsBannerWidget.setBodyImage(image);
                    ongoingSquadGoalsBannerWidget.setBodySubtitle(subtitle);
                    ongoingSquadGoalsBannerWidget.setBody(body);
                    ongoingSquadGoalsBannerWidget.setBodyLottie(lottie);
                }
            }
            else{
                //GOAL IS INCOMPLETE
                ongoingSquadGoalsBannerWidget.setSquadGoalState(SquadGoalStateHelper.SquadGoalState.WEEK_IN_PROGRESS);
                ongoingSquadGoalsBannerWidget.setBodyImage("/image/squad_goal/ongoing_squad_goals_badge.png");
                ongoingSquadGoalsBannerWidget.setBodyTitle(String.format("%.0f%%", ((double) currentWeekProgress.getScore() / currentWeekProgress.getMilestone()) * 100));
                ongoingSquadGoalsBannerWidget.setBodySubtitle("Goal Progress");

                String body;
                Long completionPercentage = (long) ((double) currentWeekProgress.getScore() / currentWeekProgress.getMilestone() * 100);
                switch(weeksFromStartDate){
                    case 0: {
                        if(completionPercentage>=0 && completionPercentage<20){
                            body = STR."Everyone needs to do \{currentWeekProgress.getCurrentUserMilestone()} \{currentWeekProgress.getCurrentUserMilestone()==1?"workout":"workouts"} this week.";
                        }
                        else if(completionPercentage>=20 && completionPercentage<40){
                            body = "Squad’s warming up. Keep the streak alive.";
                        }else if (completionPercentage>=40 && completionPercentage<=60) {
                            body = "You're at the edge of a win, push it through.";
                        }else{
                            body = "Just a few workout away from glory.";
                        }
                        break;
                    }
                    case 1:{
                        if(completionPercentage>=0 && completionPercentage<20){
                            body = STR."Everyone needs to do \{currentWeekProgress.getCurrentUserMilestone()} \{currentWeekProgress.getCurrentUserMilestone() == 1?"workout":"workouts"} this week.";
                        }
                        else if(completionPercentage>=20 && completionPercentage<40){
                            body = "Squad’s warming up. Keep the streak alive.";
                        }else if (completionPercentage>=40 && completionPercentage<=60) {
                            body = "Halfway squad, halfway win.";
                        }else{
                            body = "Just a few workout away from glory";
                        }
                        break;
                    }
                    case 2:{
                        if(completionPercentage>=0 && completionPercentage<20){
                            body = STR."Everyone needs to do \{currentWeekProgress.getCurrentUserMilestone()} \{currentWeekProgress.getCurrentUserMilestone() == 1?"workout":"workouts"} this week.";
                        }
                        else if(completionPercentage>=20 && completionPercentage<40){
                            body = "Momentum’s building, time to add your reps to the mix.";
                        }else if (completionPercentage>=40 && completionPercentage<=60) {
                            body = "Solid effort. More reps = more glory. Let’s level up.";
                        }else{
                            body = "The finish line’s in sight. Just a few more workouts to go.";
                        }
                        break;
                    }
                    case 3:{
                        if(completionPercentage>=0 && completionPercentage<20){
                            body = STR."Everyone needs to do \{currentWeekProgress.getCurrentUserMilestone()} workout this week.";
                        }
                        else if(completionPercentage>=20 && completionPercentage<40){
                            body = "Momentum’s building, time to add your reps to the mix.";
                        }else if (completionPercentage>=40 && completionPercentage<=60) {
                            body = "Solid effort. More reps = more glory. Let’s level up.";
                        }else{
                            body = "The finish line’s in sight. Just a few more workouts to go.";
                        }
                        break;
                    }
                    default:{
                        if(completionPercentage>=0 && completionPercentage<20){
                            body = STR."Everyone needs to do \{currentWeekProgress.getCurrentUserMilestone()} \{currentWeekProgress.getCurrentUserMilestone() == 1?"workout":"workouts"} this week.";
                        }
                        else if(completionPercentage>=20 && completionPercentage<40){
                            body = "Momentum’s building, time to add your reps to the mix.";
                        }else if (completionPercentage>=40 && completionPercentage<=60) {
                            body = "Solid effort. More reps = more glory. Let’s level up.";
                        }else{
                            body = "The finish line’s in sight. Just a few more workouts to go.";
                        }
                        break;
                    }
                }
                ongoingSquadGoalsBannerWidget.setBody(body);
            }
        }else{
            //PAST WEEK
            String reward = currentWeekProgress.getReward();
            if(reward!=null){
                //GOAL REWARD GIVEN
                if(currentWeekProgress.getScore()>= currentWeekProgress.getMilestone()) {
                    //GOAL WEEK COMPLETE
                    ongoingSquadGoalsBannerWidget.setSquadGoalState(SquadGoalStateHelper.SquadGoalState.GOAL_COMPLETE);
                    ongoingSquadGoalsBannerWidget.setBodyTitle(reward.equals("1")?"Membership day":"Membership days");
                    ongoingSquadGoalsBannerWidget.setReward(reward);

                    String body;
                    String subtitle;
                    String image;
                    String lottie;

                    switch (weeksFromStartDate) {
                        case 0 -> {
                            subtitle = "Perfect Week Achieved";
                            body = STR."Everyone showed up and crushed the workout!";
                            image = "/image/squad_goal/w1_reward.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Award_week_01.json";
                        }
                        case 1  -> {
                            subtitle = "Perfect Week Achieved";
                            body = "You showed up. You crushed it. Awards all yours!";
                            image = "/image/squad_goal/w2_reward.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Award_week_02.json";
                        }
                        case 2  -> {
                            subtitle = "Perfect Week Achieved \uD83C\uDF89";
                            body = "You showed up as one. This win’s yours!";
                            image = "/image/squad_goal/w3_reward.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Award_week_03.json";
                        }
                        default  -> {
                            subtitle = "Squad effort. Squad win";
                            body = "4 weeks. No excuses. Just effort, consistency, & showing up";
                            image = "/image/squad_goal/w4_reward.png";
                            lottie = "https://cdn-media.cure.fit/image/squad_goal/Award_week_04.json";

                        }
                    }
                    ongoingSquadGoalsBannerWidget.setBodyImage(image);
                    ongoingSquadGoalsBannerWidget.setBodySubtitle(subtitle);
                    ongoingSquadGoalsBannerWidget.setBody(body);
                    ongoingSquadGoalsBannerWidget.setBodyLottie(lottie);
                }else{
                    //PARTIAL REWARD GIVEN
                    ongoingSquadGoalsBannerWidget.setSquadGoalState(SquadGoalStateHelper.SquadGoalState.PARTIAL_GOAL_COMPLETE);
                    ongoingSquadGoalsBannerWidget.setBodyImage("/image/squad_goal/partial_week_image.png");
                    ongoingSquadGoalsBannerWidget.setBodyTitle(reward.equals("1") ?"Membership day":"Membership days");
                    ongoingSquadGoalsBannerWidget.setBodySubtitle("Too close , but missed!");
                    ongoingSquadGoalsBannerWidget.setReward(reward);
                    ongoingSquadGoalsBannerWidget.setBody("You ‘ve won partial effort.");
                }
            }

            else {
                //GOAL WEEK MISSED
                ongoingSquadGoalsBannerWidget.setSquadGoalState(SquadGoalStateHelper.SquadGoalState.GOAL_MISSED);
                ongoingSquadGoalsBannerWidget.setBodyTitle(String.format("%.0f%%", ((double) currentWeekProgress.getScore() / currentWeekProgress.getMilestone()) * 100));

                String subtitle;
                String image;
                String body;
                String lottie = null;
                switch (weeksFromStartDate) {
                    case 0 -> {
                        subtitle = "Missed this week's goal.";
                        body = "Bounce back stronger next time.";
                        image = "/image/squad_goal/w_missed.png";
                        lottie = "https://cdn-media.cure.fit/image/squad_goal/Missed_week.json";
                    }
                    case 1 -> {
                        subtitle = "Missed this week's goal.";
                        body = "That one got away. Let's hit reset and show up stronger.";
                        image = "/image/squad_goal/w_missed.png";
                        lottie = "https://cdn-media.cure.fit/image/squad_goal/Missed_week.json";
                    }
                    case 2 -> {
                        subtitle = "Missed this week's goal.";
                        body = "That one got away. Let's hit reset and show up stronger.";
                        image = "/image/squad_goal/w_missed.png";
                        lottie = "https://cdn-media.cure.fit/image/squad_goal/Missed_week.json";
                    }
                    default -> {
                        subtitle = "Didn’t complete the goal.";
                        body = "But you stayed in the game. Keep the squad spirit going.";
                        image = "/image/squad_goal/w4_missed.png";
                    }
                }

                ongoingSquadGoalsBannerWidget.setBodyImage(image);
                if(lottie!=null){
                    ongoingSquadGoalsBannerWidget.setBodyLottie(lottie);
                }
                ongoingSquadGoalsBannerWidget.setBodySubtitle(subtitle);
                ongoingSquadGoalsBannerWidget.setBody(body);
            }
        }
        ongoingSquadGoalsBannerWidget.setWeekOffset(weekOffset);
        ongoingSquadGoalsBannerWidget.setWeekFromStart(weeksFromStartDate);
        return ongoingSquadGoalsBannerWidget;
    }

    public SquadGoalsWeekTrailWidget getOngoingSquadGoalsLockedTrailWidget(UserContext context, ChallengeResponse challengeResponse, int weekOffset){
        SquadGoalsWeekTrailWidget squadGoalsWeekTrailWidget = new SquadGoalsWeekTrailWidget();
        squadGoalsWeekTrailWidget.setWidgetType(WidgetType.SQUAD_GOAL_WEEK_TRAIL_WIDGET);
        ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
        int weeksFromStartDate = CommunityUtil.getWeeksFromStartDate(challengeCommunityMappingEntry.getStart(), challengeCommunityMappingEntry.getEnd(), weekOffset);
        int totalWeeks = challengeCommunityMappingEntry.getChildMappings().size();
        squadGoalsWeekTrailWidget.setWeeksCompleted(weeksFromStartDate);
        squadGoalsWeekTrailWidget.setWeeksRemaining(totalWeeks-weeksFromStartDate-1);
        return squadGoalsWeekTrailWidget;
    }

    public SquadGoalProgressTrailWidget getSquadGoalProgressTrailWidget(UserContext userContext, ChallengeResponse challengeResponse, int weekOffset) {
        SquadGoalProgressTrailWidget squadGoalProgressTrailWidget = new SquadGoalProgressTrailWidget();
        squadGoalProgressTrailWidget.setWidgetType(WidgetType.SQUAD_GOAL_PROGRESS_TRAIL_WIDGET);
        ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
        int weeksFromStartDate = CommunityUtil.getWeeksFromStartDate(challengeCommunityMappingEntry.getStart(), challengeCommunityMappingEntry.getEnd(), weekOffset);
        List<SquadGoalProgress> squadGoalProgressList = new ArrayList<>();


        for (int i = 0; i <= weeksFromStartDate; i++) {
            boolean isCurrentWeek = (i==weeksFromStartDate);

            SquadGoalProgress progress = new SquadGoalProgress();
            if(isCurrentWeek){
                progress.setIsCurrentWeek(true);
            }
            progress.setTitle("Week " + (i + 1) +" Goal");
            ChallengeCommunityMappingEntry weekMapping = challengeCommunityMappingEntry.getChildMappings().get(i);
            weekMapping.getChallengeUserMappings().forEach(challengeUserMappingEntry -> {
                if(Objects.equals(challengeUserMappingEntry.getUserId(), userContext.getUserProfile().getUserId())){
                    ScoringDetails scoringDetails = challengeUserMappingEntry.getScoringDetails();
                    progress.setMilestone(scoringDetails.getMilestone());
                    progress.setScore(scoringDetails.getScore());
                    progress.setIsCompleted(scoringDetails.isCompleted());
                    SquadGoalStateInfo squadGoalStateInfo;
                    if(isCurrentWeek){
                        if(scoringDetails.getScore()>0 ){
                            squadGoalStateInfo = SquadGoalStateHelper.getStateInfo(SquadGoalStateHelper.SquadGoalState.WEEK_STARTED);
                        }else if(scoringDetails.getScore() < scoringDetails.getMilestone()){
                            squadGoalStateInfo = SquadGoalStateHelper.getStateInfo(SquadGoalStateHelper.SquadGoalState.WEEK_IN_PROGRESS);
                        }else{
                            squadGoalStateInfo = SquadGoalStateHelper.getStateInfo(SquadGoalStateHelper.SquadGoalState.GOAL_COMPLETE);
                        }
                    }else{
                        if(scoringDetails.getScore() < scoringDetails.getMilestone()) {
                            squadGoalStateInfo = SquadGoalStateHelper.getStateInfo(SquadGoalStateHelper.SquadGoalState.GOAL_MISSED);
                        }
                        else {
                            squadGoalStateInfo = SquadGoalStateHelper.getStateInfo(SquadGoalStateHelper.SquadGoalState.GOAL_COMPLETE);
                        }
                    }
                    progress.setBody(squadGoalStateInfo.getTitle());
                    progress.setImage(squadGoalStateInfo.getImage());
                }
            });

            squadGoalProgressList.add(progress);
        }
        squadGoalProgressTrailWidget.setWidgets(squadGoalProgressList);
        return squadGoalProgressTrailWidget;
    }

    public SquadGoalHighlightsWidget getSquadGoalHighlightsWidget(UserContext userContext, ChallengeResponse challengeResponse, int weekOffset) {
        SquadGoalHighlightsWidget squadGoalHighlightsWidget = new SquadGoalHighlightsWidget();
        squadGoalHighlightsWidget.setWidgetType(WidgetType.SQUAD_GOAL_HIGHLIGHTS_WIDGET);
        squadGoalHighlightsWidget.setTitle("Highlights of Squad goal");
        squadGoalHighlightsWidget.setLeftHeaderText("#squad members");
        squadGoalHighlightsWidget.setRightHeaderText("Goal Done");

        ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();

        int weeksFromStartDate = CommunityUtil.getWeeksFromStartDate(challengeCommunityMappingEntry.getStart(), challengeCommunityMappingEntry.getEnd(), weekOffset);
        ScoringDetails fourthWeekScoringDetails = challengeCommunityMappingEntry.getChildMappings().get(3).getScoringDetails();
        if(weeksFromStartDate!=3 ) {
            return null;
        }

        if(weekOffset == 0 && fourthWeekScoringDetails.getScore() < fourthWeekScoringDetails.getMilestone()){
            //CURRENT WEEK IS 4th AND THE GOAL IS NOT COMPLETE
            return null;
        }

        List<ChallengeUserMappingEntry> usersList = challengeCommunityMappingEntry.getChallengeUserMappings();
        List<String> userIds = usersList.stream()
                .map(e -> e.getUserId())
                .collect(Collectors.toList());
        Map<String, UserEntry> userEntryMap = getUserEntryMap(userIds);
        Map<String, UserGoalHighlights> userGoalHighlightsMap = new HashMap<>();


        //CREATE MAP FOR USER GOAL HIGHLIGHTS
        for (String userId : userEntryMap.keySet()) {
            UserEntry userEntry = userEntryMap.get(userId);
            UserGoalHighlights userGoalHighlights = new UserGoalHighlights();
            userGoalHighlights.setUser(userEntry);
            userGoalHighlights.setTrackImage(getGoalTrackImage());
            userGoalHighlights.setCompletedWeeks(0);
            userGoalHighlights.setTotalWeeks(0);
            if(Objects.equals(userContext.getUserProfile().getUserId(), userId)){
                userGoalHighlights.setIsMe(true);
            }
            userGoalHighlightsMap.put(userId, userGoalHighlights);
        }

        for (int i = 0; i < challengeCommunityMappingEntry.getChildMappings().size(); i++) {
            //LOOPING THROUGH EACH WEEK'S MAPPING
            ChallengeCommunityMappingEntry childMapping = challengeCommunityMappingEntry.getChildMappings().get(i);
            //LOOP THROUGH EACH USER
            for (ChallengeUserMappingEntry challengeUserMappingEntry : childMapping.getChallengeUserMappings()) {
                UserGoalHighlights userGoalHighlights = userGoalHighlightsMap.get(challengeUserMappingEntry.getUserId());
                Integer totalWeeks = userGoalHighlights.getTotalWeeks();
                if (totalWeeks == null) {
                    totalWeeks = 0;
                }
                userGoalHighlights.setTotalWeeks(totalWeeks + 1);
                ScoringDetails userScoringDetails = challengeUserMappingEntry.getScoringDetails();
                if(userScoringDetails.getScore()>=userScoringDetails.getMilestone()){
                    //GOAL COMPLETE FOR THAT WEEK
                    Integer completedWeeks = userGoalHighlights.getCompletedWeeks();
                    if (completedWeeks == null) {
                        completedWeeks = 0;
                    }
                    userGoalHighlights.setCompletedWeeks(completedWeeks+1);
                }
                userGoalHighlightsMap.put(challengeUserMappingEntry.getUserId(), userGoalHighlights);
            }
        }

        List<UserGoalHighlights> userGoalHighlightsList = new ArrayList<>(userGoalHighlightsMap.values());
        userGoalHighlightsList.sort(Comparator.comparing(UserGoalHighlights::getCompletedWeeks, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
        squadGoalHighlightsWidget.setUserGoalHighlightsList(userGoalHighlightsList);
        return squadGoalHighlightsWidget;
    }

    public SquadGoalRewardWidget getSquadGoalRewardWidget(UserContext userContext, ChallengeResponse challengeResponse, int weekOffset) {
        ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
        SquadGoalRewardWidget squadGoalRewardWidget = new SquadGoalRewardWidget();
        squadGoalRewardWidget.setWidgetType(WidgetType.SQUAD_GOAL_REWARD_WIDGET);

        int weeksFromStartDate = CommunityUtil.getWeeksFromStartDate(challengeCommunityMappingEntry.getStart(), challengeCommunityMappingEntry.getEnd(), weekOffset);
        ChallengeCommunityMappingEntry currentWeekCommunityMappingEntry = challengeCommunityMappingEntry.getChildMappings().get(weeksFromStartDate);
        String reward = Objects.toString(currentWeekCommunityMappingEntry.getMetadata().get("rewardedAmount"), null);

        if(reward==null || weeksFromStartDate<3){
            return null;
        }

        squadGoalRewardWidget.setTitle("Congratulations\uD83C\uDF89");
        squadGoalRewardWidget.setBody(STR."One team. One goal. One collective win. You’ve earned \{reward} extra days of fitness");
        squadGoalRewardWidget.setImage("/image/squad_goal/reward_banner_image.png");

        return squadGoalRewardWidget;
    }

    public SquadGoalLeaderBoardHeaderWidget getSquadGoalLeaderBoardHeaderWidget(UserContext userContext, ChallengeResponse challengeResponse, int weekOffset) {
        SquadGoalLeaderBoardHeaderWidget squadGoalLeaderBoardHeaderWidget = new SquadGoalLeaderBoardHeaderWidget();
        squadGoalLeaderBoardHeaderWidget.setWidgetType(WidgetType.SQUAD_GOAL_LEADERBOARD_HEADER_WIDGET);
        squadGoalLeaderBoardHeaderWidget.setIcon("/image/squad_goal/squad_goal_dart.png");
        ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
        int weeksFromStartDate = CommunityUtil.getWeeksFromStartDate(challengeCommunityMappingEntry.getStart(), challengeCommunityMappingEntry.getEnd(), weekOffset);
        ChallengeCommunityMappingEntry weekMapping = challengeCommunityMappingEntry.getChildMappings().get(weeksFromStartDate);

        if(weekMapping.getScoringDetails().getScore()>=weekMapping.getScoringDetails().getMilestone()) {
            //GOAL COMPLETED
            squadGoalLeaderBoardHeaderWidget.setTitle(STR."Week \{weeksFromStartDate + 1} Goal Completed");
            squadGoalLeaderBoardHeaderWidget.setLottie("https://cdn-media.cure.fit/image/squad_goal/Goal+completion+success.json");
        } else if (weekOffset != 0) {
            //NOT CURRENT WEEK
            squadGoalLeaderBoardHeaderWidget.setTitle(STR."Week \{weeksFromStartDate + 1} Goal missed");
        }else{
            //CURRENT WEEK AND IN PROGRESS
            squadGoalLeaderBoardHeaderWidget.setTitle(STR."Week \{weeksFromStartDate + 1} Goal in progress");
        }
        return squadGoalLeaderBoardHeaderWidget;
    }

    public GoalSummaryWidget getGoalSummaryWidget(UserContext userContext, ChallengeResponse challengeResponse, int weekOffset) {
        GoalSummaryWidget goalSummaryWidget = new GoalSummaryWidget();
        goalSummaryWidget.setWidgetType(WidgetType.GOAL_SUMMARY_WIDGET);
        int earnedReward = 0;
        int totalReward = 0;

        ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
        int weeksFromStartDate = CommunityUtil.getWeeksFromStartDate(challengeResponse.getChallengeCommunityMappingEntry().getStart(), challengeResponse.getChallengeCommunityMappingEntry().getEnd(), weekOffset);

        List<SquadGoalProgress> goalProgress = new ArrayList<>();
        for (int i = 0; i < challengeCommunityMappingEntry.getChildMappings().size(); i++) {
            //LOOPING THROUGH EACH WEEK'S MAPPING
            ChallengeCommunityMappingEntry childMapping = challengeCommunityMappingEntry.getChildMappings().get(i);
            ScoringDetails squadScoringDetails = childMapping.getScoringDetails();
            SquadGoalProgress squadGoalProgress = new SquadGoalProgress();
            squadGoalProgress.setMilestone(squadScoringDetails.getMilestone());
            squadGoalProgress.setScore(squadScoringDetails.getScore());
            squadGoalProgress.setIsCompleted(squadScoringDetails.isCompleted());
            String rewarded = Objects.toString(childMapping.getMetadata().get("rewardedAmount"), null);
            String total = Objects.toString(childMapping.getMetadata().get("rewardDays"), null);
            if(rewarded!=null){
                earnedReward += Integer.parseInt(rewarded);
                squadGoalProgress.setReward(rewarded);
            }
            if(total!=null){
                totalReward += Integer.parseInt(total);
            }
            //SET CENTER ICON
            if(squadScoringDetails.getScore()>=squadScoringDetails.getMilestone()){
                //GOAL WEEK COMPLETE
                squadGoalProgress.setIcon("/image/squad_goal/star_icon.png");
            }
            else{
                //PAST WEEK
                squadGoalProgress.setIcon("/image/squad_goal/shooting_star_icon.png");
            }
            squadGoalProgress.setTitle(STR."Week \{i + 1}");
            goalProgress.add(squadGoalProgress);
        }
        goalSummaryWidget.setSquadScoringDetails(goalProgress);
        goalSummaryWidget.setEarnedReward(earnedReward);
        goalSummaryWidget.setTotalReward(totalReward);
        goalSummaryWidget.setStartDate(challengeCommunityMappingEntry.getStart());
        goalSummaryWidget.setEndDate(challengeCommunityMappingEntry.getEnd());
        goalSummaryWidget.setWeekFromStart(weeksFromStartDate);
        goalSummaryWidget.setWeekOffset(weekOffset);
        goalSummaryWidget.setDividerImage("/image/squad_goal/end_divider.png");
        goalSummaryWidget.setBadgeTitle("Highlights of goal");
        goalSummaryWidget.setBadgeImage("/image/squad_goal/squad_goal_dart.png");

        Long earnedRewardPercentage = (long) ((double) earnedReward / totalReward * 100);
        if(earnedRewardPercentage == 0){
            goalSummaryWidget.setBodyImage("/image/squad_goal/goal_summary_missed.png");
            goalSummaryWidget.setTitle("Didn’t make it, but don’t stop.");
            goalSummaryWidget.setBodyLeading("Keep moving. Reset, rally, and come back stronger.");
        }else if(earnedRewardPercentage>0 && earnedRewardPercentage<20){
            goalSummaryWidget.setBodyImage("/image/squad_goal/goal_summary_partial_1.png");
            goalSummaryWidget.setTitle("A small start, a small win.");
            goalSummaryWidget.setBodyLeading("You earned ");
            goalSummaryWidget.setBodyTrailing(" days, next time, aim higher!");
        }else if(earnedRewardPercentage>=20 && earnedRewardPercentage<=40){
            goalSummaryWidget.setBodyImage("/image/squad_goal/goal_summary_partial_1.png");
            goalSummaryWidget.setTitle("A good start, a good reward");
            goalSummaryWidget.setBodyLeading("You earned ");
            goalSummaryWidget.setBodyTrailing(" days. Keep the pace and aim higher next time.");
        }else if(earnedRewardPercentage>40 && earnedRewardPercentage<60){
            goalSummaryWidget.setBodyImage("/image/squad_goal/goal_summary_partial_2.png");
            goalSummaryWidget.setTitle("Strong push, strong reward.");
            goalSummaryWidget.setBodyLeading("You earned ");
            goalSummaryWidget.setBodyTrailing(" days — you’re over halfway, next time go for the goal.");
        }else if(earnedRewardPercentage>=60 && earnedRewardPercentage<100){
            goalSummaryWidget.setBodyImage("/image/squad_goal/goal_summary_partial_2.png");
            goalSummaryWidget.setTitle("Almost there! Big win.");
            goalSummaryWidget.setBodyLeading("You earned ");
            goalSummaryWidget.setBodyTrailing(" days. Next time, let’s cross the finish.");
        }else{
            goalSummaryWidget.setBodyImage("/image/squad_goal/goal_summary_success.png");
            goalSummaryWidget.setTitle("You did it together!");
            goalSummaryWidget.setBodyLeading("You earned ");
            goalSummaryWidget.setBodyTrailing(" days for hitting the squad goal. Nice work!");
        }

        return goalSummaryWidget;
    }


    private List<LeadershipBoardEntry> sortLeadershipBoardForSquadGoals(List<LeadershipBoardEntry> leadershipBoardEntriesCurrentWeekGroupWise, ChallengeResponse challengeResponse, int weekOffset) {
        Map<String, Long> userIdToScoreMap = new HashMap<>();
        int weekIndex = CommunityUtil.getWeeksFromStartDate(challengeResponse.getChallengeCommunityMappingEntry().getStart(), challengeResponse.getChallengeCommunityMappingEntry().getEnd(), weekOffset);

        try {
            if (challengeResponse == null || challengeResponse.getChallengeCommunityMappingEntry() == null ||
                    challengeResponse.getChallengeCommunityMappingEntry().getChildMappings() == null ||
                    weekIndex < 0 || weekIndex >= challengeResponse.getChallengeCommunityMappingEntry().getChildMappings().size()) {
                return leadershipBoardEntriesCurrentWeekGroupWise;
            }

            ChallengeCommunityMappingEntry childMapping = challengeResponse.getChallengeCommunityMappingEntry().getChildMappings().get(weekIndex);
            if (childMapping.getChallengeUserMappings() == null) {
                return leadershipBoardEntriesCurrentWeekGroupWise;
            }

            // Build map of userId to score for this week
            for (ChallengeUserMappingEntry userMapping : childMapping.getChallengeUserMappings()) {
                String userId = userMapping.getUserId();
                ScoringDetails scoringDetails = userMapping.getScoringDetails();
                Long score = scoringDetails != null ? scoringDetails.getScore() : null;
                userIdToScoreMap.put(userId, score);
            }

            leadershipBoardEntriesCurrentWeekGroupWise.sort((entry1, entry2) -> {
                Long score1 = userIdToScoreMap.getOrDefault(entry1.getUserId(), -1L);
                Long score2 = userIdToScoreMap.getOrDefault(entry2.getUserId(), -1L);
            
                // Sort in descending order (highest score first)
                return Double.compare(score2, score1);
            });
            return leadershipBoardEntriesCurrentWeekGroupWise;
        } catch (Exception e) {
            log.error("Error creating userId to score map for week {}: {}", weekIndex, e.getMessage());
        }


        return leadershipBoardEntriesCurrentWeekGroupWise;
    }

    public SquadCommunityScreen getGroupedSquadScreen(UserContext userContext, int weekOffset, String scrollToUserId, String expandedTile) throws Exception {
        List<List<com.curefit.cfapi.pojo.vm.widget.BaseWidget>> widgetsV2 = new ArrayList<>();
        int selfSessionCount = 0;
        boolean showStateZero = false;
        List<String> tabNames = new ArrayList<>();
        List<String> tabCommunityIds = new ArrayList<>();
        List<Boolean> enabledTabButton = new ArrayList<>();
        List<String> stateZeroWidgets = new ArrayList<>();
        List<SquadGoalStateHelper.SquadGoalState> squadGoalStates = new ArrayList<>();
        String profileImageUrl = "";
        String headerBgImage = squadLeaderBoardHeaderImage(userContext,false);
        String squadGoalsHeaderBgImage = squadLeaderBoardHeaderImage(userContext,true);
        String userId = userContext.getUserProfile().getUserId();
        CompletableFuture<UserEntry> userEntryCF = getUserEntryCF(userId);
        if (userEntryCF != null) profileImageUrl = userEntryCF.get().getProfilePictureUrl();
        LeadershipBoardResponse leadershipBoardResponse = null;
        double autoScrollOffset = 0;
        List<GroupedConstruct> groupedConstructs = null;
        List<Action> headerActions = new ArrayList<>();


        try {
            leadershipBoardResponse = this.socialServiceClient.getLeadershipBoard(userContext.getUserProfile().getUserId(), CommunityUtil.getLastMonday(weekOffset), 0, 100, false);
            groupedConstructs = leadershipBoardResponse.getGroupedConstruct();
        } catch (Exception e) {
            log.error("Error while fetching leaderboard details : {}", e.getMessage(), e);
            throw new Exception("Error while fetching leaderboard details");
        }

        if (groupedConstructs != null) {
            GroupedConstruct globalConstruct = CommunityUtil.getGlobalLeaderboardGroup(groupedConstructs);
            if(groupedConstructs.size()>1){
                groupedConstructs.add(0, globalConstruct);
            }
            for (GroupedConstruct groupedConstruct : groupedConstructs) {
                tabNames.add(groupedConstruct.getCommunityName());
                tabCommunityIds.add(groupedConstruct.getCommunityId() != null ? groupedConstruct.getCommunityId().toString() : "empty");
                enabledTabButton.add(groupedConstruct.getUserIds().size() <= 1);
                List<com.curefit.cfapi.pojo.vm.widget.BaseWidget> widgetSet = new ArrayList<>();
                List<String> groupUserIds = groupedConstruct.getUserIds();
                List<LeadershipBoardEntry> leadershipBoardEntriesGroupWise = new ArrayList<>();
                ChallengeResponse challengeResponse = groupedConstruct.getChallengeResponse();
                ChallengeResponse challengeSummary = groupedConstruct.getCompletedChallengeResponse();

                //CHECK IF CHALLENGE SUMMARY IS PRESENT
                if(challengeSummary!=null){
                    GoalSummaryWidget goalSummaryWidget = getGoalSummaryWidget(userContext, challengeSummary, weekOffset);
                    widgetSet.add(goalSummaryWidget);
                }
                if(challengeResponse!=null){
                    //BUILD HEADER WIDGET BADGE
                    int weeksFromStartDate = CommunityUtil.getWeeksFromStartDate(challengeResponse.getChallengeCommunityMappingEntry().getStart(), challengeResponse.getChallengeCommunityMappingEntry().getEnd(), weekOffset);
                    if(challengeResponse.getStatus() == ChallengeStatus.INVITED){
                        squadGoalStates.add(SquadGoalStateHelper.SquadGoalState.INVITED);
                    }else if(challengeResponse.getStatus()==ChallengeStatus.UPCOMING){
                        squadGoalStates.add(SquadGoalStateHelper.SquadGoalState.UPCOMING);
                    }else if(challengeResponse.getStatus() == ChallengeStatus.ONGOING && weeksFromStartDate == 0){
                        squadGoalStates.add(SquadGoalStateHelper.SquadGoalState.WEEK_STARTED);
                    }else if(challengeResponse.getStatus() == ChallengeStatus.ONGOING){
                        squadGoalStates.add(SquadGoalStateHelper.SquadGoalState.WEEK_IN_PROGRESS);
                    }else{
                        squadGoalStates.add(SquadGoalStateHelper.SquadGoalState.NO_GOAL);
                    }
                    SquadGoalIntroWidget squadGoalIntroWidget = getSquadGoalIntroWidget(userContext,challengeResponse);
                    if(squadGoalIntroWidget!=null){
                        widgetSet.add(squadGoalIntroWidget);
                    }
                }else{
                    squadGoalStates.add(SquadGoalStateHelper.SquadGoalState.NO_GOAL);
                }
                if(challengeResponse!=null && challengeResponse.getStatus() == ChallengeStatus.ONGOING){
                    widgetSet.add(getOngoingSquadBannerWidget(userContext, challengeResponse, weekOffset));
                    SquadGoalHighlightsWidget squadGoalHighlightsWidget = getSquadGoalHighlightsWidget(userContext, challengeResponse, weekOffset);
                    if(squadGoalHighlightsWidget!=null){
                        widgetSet.add(squadGoalHighlightsWidget);
                    }
                }

                else if (leadershipBoardResponse.getLeadershipBoardEntries() != null) {
                    leadershipBoardEntriesGroupWise = leadershipBoardResponse.getLeadershipBoardEntries().stream().filter(leadershipBoardEntry -> groupUserIds.contains(leadershipBoardEntry.getUserId())).collect(toList());
                    widgetSet.add(getSquadHighlights(userContext, leadershipBoardEntriesGroupWise, weekOffset, false));
                }
                if(challengeResponse != null && challengeResponse.getStatus() == ChallengeStatus.ONGOING){
                    widgetSet.add(getSquadGoalLeaderBoardHeaderWidget(userContext, challengeResponse, weekOffset));
                }
                if (leadershipBoardResponse.getLeadershipBoardEntries() != null) {
                    for (LeadershipBoardEntry entry : leadershipBoardResponse.getLeadershipBoardEntries()) {
                        if (Objects.equals(entry.getUserId(), userId)) {
                            selfSessionCount = (int) Math.min(Math.max(entry.getSessionDone(), 0L), 6L);
                        }
                    }
                    List<LeadershipBoardEntry> leadershipBoardEntriesCurrentWeekGroupWise = leadershipBoardResponse.getLeadershipBoardEntries().stream().filter(leadershipBoardEntry -> groupUserIds.contains(leadershipBoardEntry.getUserId())).collect(toList());

                    // Sort by challenge score for current week if challenge is ongoing
                    if(challengeResponse!=null && challengeResponse.getStatus() == ChallengeStatus.ONGOING){
                        leadershipBoardEntriesCurrentWeekGroupWise = sortLeadershipBoardForSquadGoals(leadershipBoardEntriesCurrentWeekGroupWise, challengeResponse, weekOffset);
                    }

                    SquadsLeaderboard squadsLeaderboard = getSquadLeaderboard(userContext, leadershipBoardEntriesCurrentWeekGroupWise, selfSessionCount, weekOffset, groupedConstruct.getCommunityId(), false, challengeResponse);
                    squadsLeaderboard.setCommunityId(groupedConstruct.getCommunityId());
                    if (squadsLeaderboard != null && squadsLeaderboard.getWidgets() != null && !squadsLeaderboard.getWidgets().isEmpty()) {
                        if (expandedTile != null) {
                            squadsLeaderboard.setExpandedTile(expandedTile);
                        }
                        widgetSet.add(squadsLeaderboard);
                        autoScrollOffset = CommunityUtil.getSquadAutoScroll(scrollToUserId, squadsLeaderboard);
                    }
                    if (challengeResponse != null && challengeResponse.getStatus() == ChallengeStatus.ONGOING) {
                        SquadGoalsWeekTrailWidget squadGoalsWeekTrailWidget = getOngoingSquadGoalsLockedTrailWidget(userContext, challengeResponse, weekOffset);
                        if (squadGoalsWeekTrailWidget != null) {
                            widgetSet.add(squadGoalsWeekTrailWidget);
                        }
                    }
                    if (weekOffset == 0) {
                        List<UpcomingClassEntry> upcomingClassEntries = leadershipBoardResponse.getUpcomingClasses().stream().filter(upcomingClassEntry -> {
                            List<String> upcomingClassEntryUserIds = upcomingClassEntry.getUserIds();
                            int i = 0;
                            if (upcomingClassEntryUserIds.get(i) != null) {
                                while (i < groupUserIds.size()) {
                                    if (i < upcomingClassEntryUserIds.size() && groupUserIds.contains(upcomingClassEntryUserIds.get(i))) {
                                        i++;
                                    } else {
                                        break;
                                    }
                                }
                                if (i == upcomingClassEntryUserIds.size()) {
                                    return true;
                                }
                            }
                            return false;
                        }).collect(toList());
                        UpcomingItemCarousal upcomingItemCarousal = getUpcomingActivities(upcomingClassEntries, userContext, serviceInterfaces);
                        if (upcomingItemCarousal != null && upcomingItemCarousal.getWidgets() != null && !upcomingItemCarousal.getWidgets().isEmpty())
                            widgetSet.add(upcomingItemCarousal);
                    }
                    widgetsV2.add(widgetSet);
                }
                if (challengeResponse != null && challengeResponse.getStatus() == ChallengeStatus.ONGOING) {
                    SquadGoalRewardWidget rewardWidget = getSquadGoalRewardWidget(userContext, challengeResponse, weekOffset);
                    if (rewardWidget != null) {
                        widgetSet.add(rewardWidget);
                    }
                }
            }
        }

        Action manageSquadAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .title("SQUAD UP NOW")
                .icon(ActionIcon.BUDDY)
                .url("curefit://leaguewalltabbedpage?productType=FITNESS&selectedTab=ALL_LEAGUES")
                .build();

        Action createSquadAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .title("SQUAD UP NOW")
                .icon(ActionIcon.ADD)
                .url("curefit://create_squad")
                .build();

        if (AppUtil.isSquadsManageSegment(userContext, serviceInterfaces)) {
            manageSquadAction = Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .title("SQUAD UP NOW")
                    .icon(ActionIcon.BUDDY)
                    .url("curefit://squads_manage")
                    .build();
        }

        if (AppUtil.isMultiSquadExpSegment(userContext, serviceInterfaces)) headerActions.add(createSquadAction);
        headerActions.add(manageSquadAction);

        stateZeroWidgets.add("https://cdn-media.cure.fit/image/community/squads/state0_1_v3.png");
        stateZeroWidgets.add("https://cdn-media.cure.fit/image/community/squads/state0_2_v3.png");
        stateZeroWidgets.add("https://cdn-media.cure.fit/image/community/squads/state0_3_v3.png");
        stateZeroWidgets.add("https://cdn-media.cure.fit/image/community/squads/state0_4_v3.png");

        if (leadershipBoardResponse.getLeadershipBoardEntries() == null || leadershipBoardResponse.getLeadershipBoardEntries().size() < 2) {
            showStateZero = true;
        }

        NotificationTypeRequest notifTypes = new NotificationTypeRequest(List.of(
                WEEKLY_PROGRESS_REACTIONS,
                SQUAD_INVITATION,
                CHALLENGE_INVITATION
        ));

        Action notifTopSheetAction = Action
                .builder()
                .actionType(ActionType.OPEN_NOTIF_TOP_SHEET)
                .meta(notifTypes)
                .build();

        if (widgetsV2.isEmpty() && enabledTabButton.isEmpty()) {
            enabledTabButton.add(false);
        }

        return new SquadCommunityScreen(
                "This week's leaderboard",
                dateRangeFormatter(getLastMonday(weekOffset)),
                profileImageUrl,
                selfSessionCount,
                headerBgImage,
                squadGoalsHeaderBgImage,
                null,
                true,
                weekOffset > 0,
                true,
                "https://cdn-media.cure.fit/image/community/squads/footer_bg.png",
                "Keep Your Fitness Journey Going!",
                null,
                headerActions,
                showStateZero,
                stateZeroWidgets,
                manageSquadAction,
                weekOffset,
                notifTopSheetAction,
                autoScrollOffset,
                tabNames,
                tabCommunityIds,
                enabledTabButton,
                squadGoalStates,
                widgetsV2
                );
    }

    public SquadCommunityScreen getSquadCommunityScreen(UserContext userContext, int weekOffset, String scrollToUserId, String expandedTile) throws Exception {
        if(AppUtil.isMultiSquadExpSegment(userContext, serviceInterfaces)) {
            return getGroupedSquadScreen(userContext, weekOffset, scrollToUserId, expandedTile);
        }
        return getSquadScreen(userContext, weekOffset, scrollToUserId, expandedTile);
    }


    public SquadGoalDetails getSquadGoalDetails(UserContext userContext,String communityId) throws Exception {
        List<com.curefit.cfapi.pojo.vm.widget.BaseWidget> widgets = new ArrayList<>();
        SquadGoalDetails squadGoalDetails = new SquadGoalDetails();
        ChallengeResponse challengeResponse = challengeServiceClient.getChallengeResponse(userContext.getUserProfile().getUserId(), Long.valueOf(communityId));

        if(challengeResponse.getStatus() == ChallengeStatus.INVITED){
            //No one has joined yet
            SquadGoalsBgImageWidget squadGoalDetails1 = new SquadGoalsBgImageWidget();
            squadGoalDetails1.setWidgetType(WidgetType.SQUAD_GOAL_BG_IMAGE_WIDGET);
            squadGoalDetails1.setImage("/image/squad_goal/goal_details_header_v2.png");
            squadGoalDetails1.setGradientImage("/image/squad_goal/goal_detail_header_bg_v1.png");
            Spacing spacing1 = new Spacing();
            spacing1.setBottom("30");
            spacing1.setTop("0");
            squadGoalDetails1.setSpacing(spacing1);
            widgets.add(squadGoalDetails1);

            //Build Date Widget
            SquadGoalsDetailsDateWidget squadGoalsDateWidget = new SquadGoalsDetailsDateWidget();
            squadGoalsDateWidget.setWidgetType(WidgetType.SQUAD_GOAL_DETAILS_DATE_WIDGET);
            squadGoalsDateWidget.setStartDate(challengeResponse.getChallengeCommunityMappingEntry().getStart());
            squadGoalsDateWidget.setEndDate(challengeResponse.getChallengeCommunityMappingEntry().getEnd());

            widgets.add(squadGoalsDateWidget);

            SquadGoalsBgImageWidget squadGoalDetails3 = new SquadGoalsBgImageWidget();
            squadGoalDetails3.setWidgetType(WidgetType.SQUAD_GOAL_BG_IMAGE_WIDGET);
            squadGoalDetails3.setImage("/image/squad_goal/goal_footer_v2.png");
            squadGoalDetails3.setGradientImage("/image/squad_goal/goal_detail_footer_bg_v1.png");
            Spacing spacing3 = new Spacing();
            spacing3.setBottom("150");
            squadGoalDetails3.setSpacing(spacing3);
            widgets.add(squadGoalDetails3);


            //Build squad goals footer widget
            SquadGoalsDetailsFooterWidget squadGoalsDetailsFooterWidget = new SquadGoalsDetailsFooterWidget();
            squadGoalsDetailsFooterWidget.setWidgetType(WidgetType.SQUAD_GOAL_DETAILS_FOOTER_WIDGET);
            squadGoalsDetailsFooterWidget.setTitle("You can accept on behalf of your squad");
            squadGoalsDetailsFooterWidget.setCtaText("Enroll your Squad Now");
            squadGoalsDetailsFooterWidget.setStartDate(challengeResponse.getChallengeCommunityMappingEntry().getStart());
            squadGoalsDetailsFooterWidget.setEndDate(challengeResponse.getChallengeCommunityMappingEntry().getEnd());
            CommunityEntry communityEntry = null;
            String shareText = this.deeplinkCacheService.getSquadWallDeeplink(communityId, Tenant.CUREFIT_APP);
            try {
                communityEntry = socialServiceClient.getCommunityInfo(Long.parseLong(communityId));
                if(communityEntry!=null){
                    shareText = STR."Hey, I just signed up our squad — \{communityEntry.getName()} — for a Squad Goal! Everyone’s got to show up to unlock the reward. If one drops off… we all miss out. Let’s not lose this. Let’s win it. \{this.deeplinkCacheService.getSquadWallDeeplink(communityId, Tenant.CUREFIT_APP)}";
                }
            }catch (Exception e) {
                String message = String.format("Error in fetching community info, error :: %s", e.getMessage());
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }
            squadGoalsDetailsFooterWidget.setShare(shareText);

            ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
            List<ChallengeUserMappingEntry> usersList = challengeCommunityMappingEntry.getChallengeUserMappings();
            List<String> userIds = usersList.stream()
                    .map(e -> e.getUserId())
                    .collect(Collectors.toList());
            Map<String, UserEntry> userEntryMap = getUserEntryMap(userIds);
            squadGoalsDetailsFooterWidget.setUsersList(userEntryMap);
            final String currentUserId = userContext.getUserProfile().getUserId();
            challengeCommunityMappingEntry.getChallengeUserMappings().forEach(mapping -> {
                if (mapping.getUserId().equals(currentUserId)) {
                    squadGoalsDetailsFooterWidget.setChallengeUserMappingId(String.valueOf(mapping.getId()));
                }
            });
            squadGoalsDetailsFooterWidget.setCommunityId(communityId);
            squadGoalDetails.setFooterWidget(squadGoalsDetailsFooterWidget);
        }
        else if(challengeResponse.getStatus() == ChallengeStatus.UPCOMING) {
            //Challenge is about to start
            SquadGoalsBgImageWidget squadGoalDetails1 = new SquadGoalsBgImageWidget();
            squadGoalDetails1.setWidgetType(WidgetType.SQUAD_GOAL_BG_IMAGE_WIDGET);
            squadGoalDetails1.setImage("/image/squad_goal/goal_details_header_v2.png");
            squadGoalDetails1.setGradientImage("/image/squad_goal/goal_detail_header_bg_v1.png");
            Spacing spacing1 = new Spacing();
            spacing1.setBottom("30");
            spacing1.setTop("0");
            squadGoalDetails1.setSpacing(spacing1);
            widgets.add(squadGoalDetails1);

            //Build Date Widget
            SquadGoalsDetailsUpcomingInfoWidget squadGoalsDetailsUpcomingInfoWidget = new SquadGoalsDetailsUpcomingInfoWidget();
            squadGoalsDetailsUpcomingInfoWidget.setWidgetType(WidgetType.SQUAD_GOAL_DETAILS_UPCOMING_INFO_WIDGET);
            squadGoalsDetailsUpcomingInfoWidget.setStartDate(challengeResponse.getChallengeCommunityMappingEntry().getStart());
            squadGoalsDetailsUpcomingInfoWidget.setEndDate(challengeResponse.getChallengeCommunityMappingEntry().getEnd());
            ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
            List<ChallengeUserMappingEntry> usersList = challengeCommunityMappingEntry.getChallengeUserMappings();
            List<String> userIds = usersList.stream()
                    .map(e -> e.getUserId())
                    .collect(Collectors.toList());
            Map<String, UserEntry> userEntryMap = getUserEntryMap(userIds);
            squadGoalsDetailsUpcomingInfoWidget.setUsersList(userEntryMap);
            String acceptedBy = challengeCommunityMappingEntry.getMetadata().get("acceptedBy").toString();
            squadGoalsDetailsUpcomingInfoWidget.setAcceptedBy(userEntryMap.get(acceptedBy));

            widgets.add(squadGoalsDetailsUpcomingInfoWidget);

            SquadGoalsBgImageWidget squadGoalDetails3 = new SquadGoalsBgImageWidget();
            squadGoalDetails3.setWidgetType(WidgetType.SQUAD_GOAL_BG_IMAGE_WIDGET);
            squadGoalDetails3.setImage("/image/squad_goal/goal_footer_v2.png");
            squadGoalDetails3.setGradientImage("/image/squad_goal/goal_detail_footer_bg_v1.png");
            Spacing spacing3 = new Spacing();
            spacing3.setBottom("150");
            squadGoalDetails3.setSpacing(spacing3);
            widgets.add(squadGoalDetails3);

            //Build Footer Widget
            SquadGoalsDetailsUpcomingFooterWidget squadGoalsDetailsUpcomingFooterWidget = new SquadGoalsDetailsUpcomingFooterWidget();
            squadGoalsDetailsUpcomingFooterWidget.setWidgetType(WidgetType.SQUAD_GOAL_DETAILS_UPCOMING_FOOTER_WIDGET);
            squadGoalsDetailsUpcomingFooterWidget.setStartDate(challengeResponse.getChallengeCommunityMappingEntry().getStart());
            squadGoalsDetailsUpcomingFooterWidget.setCommunityId(communityId);
            CommunityEntry communityEntry = null;
            String shareText = this.deeplinkCacheService.getSquadWallDeeplink(communityId, Tenant.CUREFIT_APP);
            try {
                communityEntry = socialServiceClient.getCommunityInfo(Long.parseLong(communityId));
                if(communityEntry!=null){
                    shareText = STR."Hey, I just signed up our squad — \{communityEntry.getName()} — for a Squad Goal! Everyone’s got to show up to unlock the reward. If one drops off… we all miss out. Let’s not lose this. Let’s win it. \{this.deeplinkCacheService.getSquadWallDeeplink(communityId, Tenant.CUREFIT_APP)}";
                }
            } catch (Exception e) {
                String message = String.format("Error in fetching community info, error :: %s", e.getMessage());
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }
            squadGoalsDetailsUpcomingFooterWidget.setShare(shareText);
            squadGoalDetails.setFooterWidget(squadGoalsDetailsUpcomingFooterWidget);
        }


        squadGoalDetails.setWidgetsV2(widgets);
        return squadGoalDetails;
    }

    public APIStatus acceptChallengeInvite(UserContext userContext, String challengeUserMappingId) {
        APIStatus apiStatus = new APIStatus();
        try {
//            ChallengeResponse challengeResponse = challengeServiceClient.getChallengeResponse(userContext.getUserProfile().getUserId(), Long.valueOf(communityId));
//            if (challengeResponse == null || challengeResponse.getChallengeCommunityMappingEntry() == null) {
//                apiStatus.setSuccess(false);
//                apiStatus.setMessage("Challenge not found");
//                return apiStatus;
//            }
//            ChallengeCommunityMappingEntry challengeCommunityMappingEntry = challengeResponse.getChallengeCommunityMappingEntry();
//            if (challengeCommunityMappingEntry.getStatus() != ChallengeStatus.INVITED) {
//                apiStatus.setSuccess(false);
//                apiStatus.setMessage("Challenge already accepted or expired");
//                return apiStatus;
//            }
            challengeServiceClient.acceptChallengeInvite(Long.valueOf(challengeUserMappingId), ChallengeInviteType.GROUP_ACCEPTANCE);
            apiStatus.setStatus(true);
        } catch (Exception e) {
            log.error("Error accepting challenge invite: {}", e.getMessage(), e);
            apiStatus.setStatus(false);
        }
        return apiStatus;
    }

    private SquadCommunityScreen getSquadScreen(UserContext userContext, int weekOffset, String scrollToUserId, String expandedTile) throws Exception {
        List<List<com.curefit.cfapi.pojo.vm.widget.BaseWidget>> widgets = new ArrayList<>();
        List<com.curefit.cfapi.pojo.vm.widget.BaseWidget> widgetSet = new ArrayList<>();

        int selfSessionCount = 0;
        boolean showStateZero = false;
        List<String> stateZeroWidgets = new ArrayList<>();
        String profileImageUrl = "";
        String headerBgImage = squadLeaderBoardHeaderImage(userContext, false);
        String userId = userContext.getUserProfile().getUserId();
        CompletableFuture<UserEntry> userEntryCF = getUserEntryCF(userId);
        if (userEntryCF != null) profileImageUrl = userEntryCF.get().getProfilePictureUrl();
        LeadershipBoardResponse leadershipBoardResponse = null;
        List<LeadershipBoardEntry> lastWeekLeadershipBoardResponse = null;
        double autoScrollOffset = 0;


        try {
            String squad_hl_cache = serviceInterfaces.featureStateCache.get(userId, AppUtil.SQUADS_HIGHLIGHTS_KEY).get();
            String cached_day_of_week = LocalDate.now().getDayOfWeek().toString();
            if (squad_hl_cache == null) {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime endOfWeek = now.with(DayOfWeek.SUNDAY).withHour(23).withMinute(59).withSecond(59);
                Long remainingSeconds = ChronoUnit.SECONDS.between(now, endOfWeek);
                serviceInterfaces.featureStateCache.set(userContext.getUserProfile().getUserId(), AppUtil.SQUADS_HIGHLIGHTS_KEY, LocalDate.now().getDayOfWeek().toString(), remainingSeconds);
            } else {
                cached_day_of_week = squad_hl_cache;
            }

            boolean includeLastWeekCache = (squad_hl_cache == null) || (Objects.equals(LocalDate.now().getDayOfWeek().toString(), cached_day_of_week));
            leadershipBoardResponse = this.socialServiceClient.getLeadershipBoard(userContext.getUserProfile().getUserId(), CommunityUtil.getLastMonday(weekOffset), 0, 100, includeLastWeekCache);
            if (leadershipBoardResponse != null && leadershipBoardResponse.getLastWeekEntries() != null) {
                lastWeekLeadershipBoardResponse = leadershipBoardResponse.getLastWeekEntries();
            }
            if (weekOffset != 0) {
                lastWeekLeadershipBoardResponse = leadershipBoardResponse.getLeadershipBoardEntries();
            }
        } catch (Exception e) {
            log.error("Error while fetching leaderboard details : {}", e.getMessage(), e);
            throw new Exception("Error while fetching leaderboard details");
        }

        if (lastWeekLeadershipBoardResponse != null) {
            widgetSet.add(getSquadHighlights(userContext, lastWeekLeadershipBoardResponse, weekOffset, true));
        }

        if (leadershipBoardResponse != null) {
            if (leadershipBoardResponse.getLeadershipBoardEntries() != null) {
                for (LeadershipBoardEntry entry : leadershipBoardResponse.getLeadershipBoardEntries()) {
                    if (Objects.equals(entry.getUserId(), userId)) {
                        selfSessionCount = (int) Math.min(Math.max(entry.getSessionDone(), 0L), 6L);
                    }
                }
            }
            SquadsLeaderboard squadsLeaderboard = getSquadLeaderboard(userContext, leadershipBoardResponse.getLeadershipBoardEntries(), selfSessionCount, weekOffset, null, true, null);
            if (lastWeekLeadershipBoardResponse == null) {
                squadsLeaderboard.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "30"));
            }
            if (squadsLeaderboard != null && squadsLeaderboard.getWidgets() != null && !squadsLeaderboard.getWidgets().isEmpty()) {
                if (expandedTile != null) {
                    squadsLeaderboard.setExpandedTile(expandedTile);
                }
                widgetSet.add(squadsLeaderboard);
                autoScrollOffset = CommunityUtil.getSquadAutoScroll(scrollToUserId, squadsLeaderboard);
            }
            UpcomingItemCarousal upcomingItemCarousal = getUpcomingActivities(leadershipBoardResponse.getUpcomingClasses(), userContext, serviceInterfaces);
            if (upcomingItemCarousal != null && upcomingItemCarousal.getWidgets() != null && !upcomingItemCarousal.getWidgets().isEmpty())
                widgetSet.add(upcomingItemCarousal);
        }
        widgets.add(widgetSet);

        boolean selfSquadExist = false;
        try {
            CommunitiesResponse communitiesResponse = socialServiceClient.getUserCommunities(userId, CommunityType.LEAGUE, false, 0, 50);
            if (communitiesResponse != null) {
                for (var community : communitiesResponse.getCommunities()) {
                    if (Objects.equals(community.getCreatorNode().getEntityId(), userId)) {
                        selfSquadExist = true;
                        break;
                    }
                }
            }
        } catch (Exception e) {
            String message = String.format("Error in fetching user communities, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }

        Action manageSquadAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .title("SQUAD UP NOW")
                .icon(ActionIcon.SETTINGS)
                .url("curefit://leaguewalltabbedpage?productType=FITNESS&selectedTab=ALL_LEAGUES")
                .build();

        if (AppUtil.isSquadsManageSegment(userContext, serviceInterfaces)) {
            manageSquadAction = Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .title("SQUAD UP NOW")
                    .icon(ActionIcon.SETTINGS)
                    .url(selfSquadExist ? "curefit://squads_manage" : "curefit://create_squad")
                    .build();
        }

        stateZeroWidgets.add("https://cdn-media.cure.fit/image/community/squads/state0_1_v3.png");
        stateZeroWidgets.add("https://cdn-media.cure.fit/image/community/squads/state0_2_v3.png");
        stateZeroWidgets.add("https://cdn-media.cure.fit/image/community/squads/state0_3_v3.png");
        stateZeroWidgets.add("https://cdn-media.cure.fit/image/community/squads/state0_4_v3.png");

        if (leadershipBoardResponse == null
                || leadershipBoardResponse.getLeadershipBoardEntries() == null
                || leadershipBoardResponse.getLeadershipBoardEntries().size() < 2) {
            showStateZero = true;
        }

        NotificationTypeRequest notifTypes = new NotificationTypeRequest(List.of(
                WEEKLY_PROGRESS_REACTIONS,
                SQUAD_INVITATION,
                CHALLENGE_INVITATION
        ));

        Action notifTopSheetAction = Action
                .builder()
                .actionType(ActionType.OPEN_NOTIF_TOP_SHEET)
                .meta(notifTypes)
                .build();


        return new SquadCommunityScreen(
                "Squad wall",
                dateRangeFormatter(getLastMonday(weekOffset)),
                profileImageUrl,
                selfSessionCount,
                headerBgImage,
                null,
                null,
                true,
                weekOffset > 0,
                true,
                "https://cdn-media.cure.fit/image/community/squads/footer_bg.png",
                "Keep Your Fitness Journey Going!",
                widgetSet,
                List.of(manageSquadAction),
                showStateZero,
                stateZeroWidgets,
                manageSquadAction,
                weekOffset,
                notifTopSheetAction,
                autoScrollOffset,
                List.of("Everyone"),
                List.of("empty"),
                List.of(false),
                List.of(SquadGoalStateHelper.SquadGoalState.NO_GOAL),
                widgets
        );
    }

    public FeedInteractionItem postSquadReaction(UserContext userContext, String recieverUserId, ReactionCode reactionCode, boolean isLiked, String startDate) {
        String reactorUserId = userContext.getUserProfile().getUserId();
        FeedInteractionItem feedInteractionItem = null;
        try {
            if (isLiked) {
                Map<ReactionCode, Map<String, List<Date>>> reactions = this.socialServiceClient.addReactionForWeeklyProgressV2(recieverUserId, reactorUserId, reactionCode, startDate);
                feedInteractionItem = getFeedInteractionItem(reactions, recieverUserId, serviceInterfaces, userContext);
                return feedInteractionItem;
            } else {
                Map<ReactionCode, Map<String, List<Date>>> reactions = this.socialServiceClient.deleteReactionsOfWeeklyProgress(recieverUserId, reactionCode, reactorUserId, startDate);
                feedInteractionItem = getFeedInteractionItem(reactions, recieverUserId, serviceInterfaces, userContext);
                return feedInteractionItem;
            }
        } catch (Exception e) {
            log.error("Error while posting reaction : {}", e.getMessage(), e);
        }
        return null;
    }

    public CultNotificationScreen getCultNotificationCenter(UserContext userContext) throws ExecutionException, InterruptedException {

        CultNotificationScreen cultNotificationScreen = new CultNotificationScreen();
        List<CultNotifObject> notifications = new ArrayList<>();

        // get all user invites
        List<CommunityUserMappingEntry> pendingReceivedInvites = new ArrayList<>();
        String userId = userContext.getUserProfile().getUserId();
        try {
            pendingReceivedInvites = socialServiceClient.getPendingInvitesForUser(userId, true, 0, 10);
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
            log.error("Fetch cult notificaations error : ", e);
        }


        // set Notifications
        for (CommunityUserMappingEntry invite : pendingReceivedInvites) {
            if (invite.getCommunity() != null
                    && invite.getCommunity().getCreatorNode() != null
                    && invite.getCommunity().getCreatorNode().getEntityId() != null) {
                String inviterUserId = invite.getCommunity().getCreatorNode().getEntityId();
                CompletableFuture<UserEntry> userEntryCF = getUserEntryCF(inviterUserId);

                CultNotifObject notif = new CultNotifObject();
                notif.setRichTitle("Join " + userEntryCF.get().getFirstName() + "'s squad");

                List<String> avatarUrls = new ArrayList<>();
                avatarUrls.add("/image/community/default_male_thumbnail.png");
                avatarUrls.add(userEntryCF.get().getProfilePictureUrl());
                notif.setIcons(avatarUrls);

                SquadAcceptNotifButtons squadWidget = new SquadAcceptNotifButtons();
                Action rejectInviteAction = Action.builder()
                        .actionType(ActionType.REJECT_SQUAD_INVITE)
                        .meta(getInviteStatusUpdateActionMeta(userId, String.valueOf(invite.getId())))
                        .build();

                Action acceptInviteAction = Action.builder()
                        .actionType(ActionType.ACCEPT_SQUAD_INVITE)
                        .meta(getInviteStatusUpdateActionMeta(userId, String.valueOf(invite.getId())))
                        .build();
                squadWidget.setAcceptAction(acceptInviteAction);
                squadWidget.setRejectAction(rejectInviteAction);

                notif.setActionWidget(squadWidget);

                Instant instant = invite.getLastModifiedOn().toInstant();
                Instant currentTime = Instant.now();
                Duration duration = Duration.between(instant, currentTime);
                String timeAgo = getTimeAgo(duration);

                notif.setLastModifiedOn(timeAgo);
                notif.setNavAction(Action.builder()
                        .actionType(ActionType.NAVIGATION)
                        .url("curefit://leaguewalltabbedpage?selectedTab=ALL_LEAGUES")
                        .build());

                notifications.add(notif);
            }
        }
        cultNotificationScreen.setWidgets(notifications);
        cultNotificationScreen.setTitle("Squad Notifications");
        cultNotificationScreen.setNullNotifImage("/image/mem-exp/fit_squad.png");
        cultNotificationScreen.setNullNotifAction(Action.builder()
                .actionType(ActionType.NAVIGATION)
                .title("MANAGE")
                .url("curefit://leaguewalltabbedpage?selectedTab=ALL_LEAGUES&productType=FITNESS").build());

        SquadVisibilityType squadVisibilityType = getSquadVisibility(userId, serviceInterfaces);
        CultNotificationScreen.SquadVisibilityWidget squadVisibilityWidget = new CultNotificationScreen.SquadVisibilityWidget();
        squadVisibilityWidget.setTitle("Manage squad visibility");
        squadVisibilityWidget.setStatus(Objects.equals(squadVisibilityType, SquadVisibilityType.ANY) ? "Everyone" : "Only with Phone number");
        squadVisibilityWidget.setSubText("Current visibility : ");

        Action squadVisibilityAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .title("CHANGE")
                .url("curefit://edit_profile?squadExpanded=true")
                .build();
        squadVisibilityWidget.setAction(squadVisibilityAction);

        cultNotificationScreen.setSquadVisibilityWidget(squadVisibilityWidget);
        return cultNotificationScreen;
    }


    public CultNotificationScreen getCultNotificationCenterV2(UserContext userContext, List<NotificationCenterEntryType> notifTypes, boolean isKarmaScreen) throws ExecutionException, InterruptedException {
        CultNotificationScreen notificationCenterPage = new CultNotificationScreen();
        String userId = userContext.getUserProfile().getUserId();
        List<CultNotifObject> widgets = new ArrayList<>();
        NotificationCenterResponse notificationCenterResponse = null;
        if (notifTypes == null || notifTypes.isEmpty()) {
            try {
                if (AppUtil.notifDelayCheck(userContext)) {
                    Thread.sleep(300);
                }
                notificationCenterResponse = notificationCenterServiceClient.getNotifications(userId);
            } catch (Exception e) {
                serviceInterfaces.exceptionReportingService.reportException(e);
                log.error("Fetch cult notifications error : ", e);
            }
        } else {
            try {
                notificationCenterResponse = notificationCenterServiceClient.getNotifications(userId, notifTypes);
            } catch (Exception e) {
                serviceInterfaces.exceptionReportingService.reportException(e);
                log.error("Fetch cult notifications error : ", e);
            }
        }
        if (notificationCenterResponse == null) return notificationCenterPage;

        List<NotificationCenterEntry> readNotifsResponse = notificationCenterResponse.getRead();
        List<NotificationCenterEntry> unReadNotifsResponse = notificationCenterResponse.getUnread();

        Comparator<NotificationCenterEntry> sortByDate = Comparator.comparing(NotificationCenterEntry::getCreatedAt).reversed();
        readNotifsResponse.sort(sortByDate);
        unReadNotifsResponse.sort(sortByDate);

        if (unReadNotifsResponse != null) {
            for (NotificationCenterEntry notification : unReadNotifsResponse) {
                if (notification.getNotificationId() == null) continue;
                try {
                    CultNotifObject item = new CultNotifObject();
                    item.setNotificationId(notification.getNotificationId());
                    item.setRichTitle(getNotifBoldTitle(userContext, notification, serviceInterfaces,challengeServiceClient));
                    item.setTitle(getNotifTitleSuffix(userContext, notification, serviceInterfaces, isKarmaScreen, challengeServiceClient));
                    item.setSeen(false);
                    item.setLastModifiedOn(getNotificationsDate(userContext, notification, notification.getCreatedAt(), isKarmaScreen, serviceInterfaces, challengeServiceClient));
                    item.setIcons(getNotifAvatarUrls(notification, serviceInterfaces));
                    item.setNavAction(getNotifAction(notification, serviceInterfaces, isKarmaScreen, userContext));
                    item.setSuffixTitle(getNotifSuffixTitle(notification, serviceInterfaces, isKarmaScreen));
                    item.setSuffixImageUrl(notification.getMeta().getUserReviewIconUrls());
                    item.setActionWidget(getActionWidget(notification, userId, userContext, serviceInterfaces));
                    widgets.add(item);
                } catch (Exception e) {
                    log.error("Error processing notification: {}", e.getMessage(), e);
                    serviceInterfaces.exceptionReportingService.reportException(e);
                }
            }
        }
        if (readNotifsResponse != null) {
            for (NotificationCenterEntry notification : readNotifsResponse) {
                if (notification.getNotificationId() == null) continue;
                try {
                    CultNotifObject item = new CultNotifObject();
                    item.setNotificationId(notification.getNotificationId());
                    item.setRichTitle(getNotifBoldTitle(userContext, notification, serviceInterfaces,challengeServiceClient));
                    item.setTitle(getNotifTitleSuffix(userContext, notification, serviceInterfaces, isKarmaScreen,challengeServiceClient));
                    item.setSeen(true);
                    item.setLastModifiedOn(getNotificationsDate(userContext, notification, notification.getCreatedAt(), isKarmaScreen, serviceInterfaces, challengeServiceClient));
                    item.setIcons(getNotifAvatarUrls(notification, serviceInterfaces));
                    item.setNavAction(getNotifAction(notification, serviceInterfaces, isKarmaScreen, userContext));
                    item.setSuffixTitle(getNotifSuffixTitle(notification, serviceInterfaces, isKarmaScreen));
                    item.setSuffixImageUrl(notification.getMeta().getUserReviewIconUrls());
                    item.setActionWidget(getActionWidget(notification, userId, userContext, serviceInterfaces));
                    widgets.add(item);
                } catch (Exception e) {
                    log.error("Error processing notification with ID {}: {}", notification.getNotificationId(), e.getMessage(), e);
                    serviceInterfaces.exceptionReportingService.reportException(e);
                }
            }
        }

        notificationCenterPage.setWidgets(widgets);
        notificationCenterPage.setTitle("Notifications");
        notificationCenterPage.setNullNotifImage("/image/community/default_notif.png");
        notificationCenterPage.setNullNotifAction(Action.builder()
                .actionType(ActionType.NAVIGATION)
                .title("MANAGE")
                .url("curefit://leaguewalltabbedpage?selectedTab=ALL_LEAGUES&productType=FITNESS").build());

//        SquadVisibilityType squadVisibilityType = CommunityUtil.getSquadVisibility(userId, serviceInterfaces);
//        CultNotificationScreen.SquadVisibilityWidget squadVisibilityWidget = new CultNotificationScreen.SquadVisibilityWidget();
//        squadVisibilityWidget.setTitle("Manage profile visibility");
//        squadVisibilityWidget.setStatus(Objects.equals(squadVisibilityType, SquadVisibilityType.ANY) ? "Everyone" : "Only with Phone number");
//        squadVisibilityWidget.setSubText("Current visibility : ");
//
//        Action squadVisibilityAction = Action.builder()
//                .actionType(ActionType.NAVIGATION)
//                .title("CHANGE")
//                .url("curefit://edit_profile?squadExpanded=true")
//                .build();
//        squadVisibilityWidget.setAction(squadVisibilityAction);
//
//        notificationCenterPage.setSquadVisibilityWidget(squadVisibilityWidget);
        return notificationCenterPage;
    }

    public static CompletableFuture<List<Object>> futureAllOf(CompletableFuture<?>... futures) {
        return CompletableFuture.allOf(futures)
                .thenApply(x -> Arrays.stream(futures)
                        .map(f -> (Object) f.join())
                        .collect(toList())
                );
    }

    public StrengthTrackerPage getStrengthTrackerPage(UserContext userContext) {

        StrengthTrackerPage strengthTrackerPage = new StrengthTrackerPage();
        try {
            String userId = userContext.getUserProfile().getUserId();
            ActivityStoreAttributeSearchRequest searchRequest = new ActivityStoreAttributeSearchRequest();
            searchRequest.setUserId(java.util.Collections.singletonList(userId));
            searchRequest.setActivityType(ActivityTypeDS.WEIGHT_LOGGING);
            ExerciseListResponse exerciseListResponse = serviceInterfaces.activityLoggingService.getLoggedExerciseIds(searchRequest);
            List<String> allExerciseIds = exerciseListResponse.getExerciseIds();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            log.info("CFAPI::strength tracker: I {}", allExerciseIds);

            List<BodyPart> bodyParts = serviceInterfaces.herculesService.bodyPartClient().getAllBodyParts();

            Map<String, BodyPart> muscleDetailsMap = bodyParts != null && !bodyParts.isEmpty() ? bodyParts.stream()
                    .collect(Collectors.toMap(BodyPart::get_id, bodyPart -> bodyPart)) : new HashMap<>();
            Map<String, Movement> movementDetailsMap = serviceInterfaces.herculesService.exerciseClient().getMovementByMovementIds(allExerciseIds);

            List<List<ActivityStoreAttribute>> activityStoreAttributesList = retrieveMultipleLastExerciseLogEntry(serviceInterfaces, userId, allExerciseIds);
            log.info("CFAPI::strength tracker: check exercise logging data - {}", activityStoreAttributesList);
            List<Map<String, ActivityStoreAttribute>> exerciseLogMapList = convertAttributeListToMap(activityStoreAttributesList);
            Map<String, ActivityStoreAttribute> exerciseLogMap = new HashMap<>();
            if (exerciseLogMapList != null && !exerciseLogMapList.isEmpty()) {
                exerciseLogMap = exerciseLogMapList.get(0);
            }
            log.info("CFAPI::strength tracker: check exercise logging data map - {}", exerciseLogMap);

            List<BaseWidget> widgets = new ArrayList<>();

            ExerciseLoggingWidget chestExercises = new ExerciseLoggingWidget();
            ExerciseLoggingWidget backExercises = new ExerciseLoggingWidget();
            ExerciseLoggingWidget armsExercises = new ExerciseLoggingWidget();
            ExerciseLoggingWidget legsExercises = new ExerciseLoggingWidget();
            ExerciseLoggingWidget shouldersExercises = new ExerciseLoggingWidget();

            ExerciseLoggingWidgetData chestExerciseData = new ExerciseLoggingWidgetData();
            chestExerciseData.setHeaderText("Chest");
            chestExerciseData.setBackgroundColor("white10");
            List<ActivityStoreAttributeEntry> chestAttributeEntries = new ArrayList<>();

            ExerciseLoggingWidgetData backExerciseData = new ExerciseLoggingWidgetData();
            backExerciseData.setHeaderText("Back");
            backExerciseData.setBackgroundColor("white10");
            List<ActivityStoreAttributeEntry> backAttributeEntries = new ArrayList<>();

            ExerciseLoggingWidgetData armsExerciseData = new ExerciseLoggingWidgetData();
            armsExerciseData.setHeaderText("Arms");
            armsExerciseData.setBackgroundColor("white10");
            List<ActivityStoreAttributeEntry> armsAttributeEntries = new ArrayList<>();

            ExerciseLoggingWidgetData shouldersExerciseData = new ExerciseLoggingWidgetData();
            shouldersExerciseData.setHeaderText("Shoulders");
            shouldersExerciseData.setBackgroundColor("white10");
            List<ActivityStoreAttributeEntry> shouldersAttributeEntries = new ArrayList<>();

            ExerciseLoggingWidgetData legsExerciseData = new ExerciseLoggingWidgetData();
            legsExerciseData.setHeaderText("Legs");
            legsExerciseData.setBackgroundColor("white10");
            List<ActivityStoreAttributeEntry> legsAttributeEntries = new ArrayList<>();


            for (Map.Entry<String, Movement> movementEntry : movementDetailsMap.entrySet()) {
                Movement movement = movementEntry.getValue();
                String exerciseId = movement.get_id(); // movementEntry.getKey();

                log.info("CFAPI::strength tracker: J {} {}", exerciseId, movement.getTitle());

                BodyPart primaryMusclePart = movement.getPrimaryMuscleId() != null ?
                        muscleDetailsMap.get(movement.getPrimaryMuscleId()) : null;

                if (primaryMusclePart == null) {
                    continue;
                }

                String primaryMuscle = primaryMusclePart.getName();

                ExerciseLogTypeEnum exerciseLogType = ExerciseLogTypeEnum.LOGGED;
                ActivityStoreAttribute logEntity = exerciseLogMap.get(exerciseId);
                LoggedExerciseStateEnum loggedExerciseState = LoggedExerciseStateEnum.STRENGTH_TRACKER;
                boolean canDelete = false;
                String imageUrl = getThumbnailImageUrl(movement.getMedia());
                Action imageClickAction = new Action("curefit://addexercisescreen?pageFrom=GYM_LOGGING", ActionType.NAVIGATION);
                Action logClickAction = getStrengthTrackerClickAction(logEntity.getSessionId(), logEntity.getBookingId(), exerciseId);
                String repMaxEvaluationExpression = "0.85*(weight*(1+(reps)/30))";
                ExerciseExecution exerciseExecution = logEntity.getExercises().getFirst().getExerciseExecution();
                exerciseExecution.setBodyParts(java.util.Collections.singletonList(primaryMusclePart.getBodyPartGroupId() != null ? primaryMusclePart.getBodyPartGroupId() : primaryMusclePart.get_id()));
                logEntity.getExercises().getFirst().setExerciseExecution(exerciseExecution);

                ActivityStoreAttributeEntry logEntry = convertEntityToEntry(logEntity, logEntity.getSessionId(), exerciseId, movement.getTitle(), primaryMuscle,
                        loggedExerciseState, exerciseLogType, canDelete, imageUrl, imageClickAction, logClickAction,
                        repMaxEvaluationExpression);

                log.info("CFAPI::strength tracker: P {} {}", exerciseId, logEntry);

                List<String> musclesInvolved = new ArrayList<>();
                String muscleEnumString = primaryMusclePart.getName().toUpperCase().replace(' ', '_');
                musclesInvolved.add(muscleEnumString);

                logEntry.setMusclesFocused(musclesInvolved);
                String lastLoggedDateString = exerciseLogMap.get(exerciseId).getDate();
                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd MMM");
                LocalDate lastLoggedDate = LocalDate.parse(lastLoggedDateString, formatter);
                String formattedLastLoggedDate = lastLoggedDate.format(outputFormatter);
                logEntry.setLastLoggedOn("Logged on " + formattedLastLoggedDate);

                String muscleGroupId = Constants.MUSCLE_GROUP_ID_MAPPING.get(primaryMusclePart.getBodyPartGroupId() != null ? primaryMusclePart.getBodyPartGroupId() : primaryMusclePart.get_id());

                if (muscleGroupId != null) {
                    switch (Enum.valueOf(MuscleType.class, muscleGroupId)) {
                        case MuscleType.CHEST -> chestAttributeEntries.add(logEntry);
                        case MuscleType.BACK -> backAttributeEntries.add(logEntry);
                        case MuscleType.ARMS -> armsAttributeEntries.add(logEntry);
                        case MuscleType.SHOULDERS -> shouldersAttributeEntries.add(logEntry);
                        case MuscleType.LEGS -> legsAttributeEntries.add(logEntry);
                    }
                } else {
                    serviceInterfaces.exceptionReportingService.reportErrorMessage(STR."Name is null, body part not present in MUSCLE_GROUP_ID_MAPPING, present body part id :\{primaryMusclePart.getBodyPartGroupId() != null ? primaryMusclePart.getBodyPartGroupId() : primaryMusclePart.get_id()}");
                }

            }

            if (!chestAttributeEntries.isEmpty()) {
                log.info("CFAPI::strength tracker: O LOGGED");
                chestExerciseData.setActivityStoreAttributeEntries(chestAttributeEntries);
                chestExercises.setData(chestExerciseData);
                chestExercises.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "20"));
                widgets.add(chestExercises);
            }

            if (!backAttributeEntries.isEmpty()) {
                log.info("CFAPI::strength tracker: O LOGGED");
                backExerciseData.setActivityStoreAttributeEntries(backAttributeEntries);
                backExercises.setData(backExerciseData);
                backExercises.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "20"));
                widgets.add(backExercises);
            }

            if (!legsAttributeEntries.isEmpty()) {
                log.info("CFAPI::strength tracker: O LOGGED");
                legsExerciseData.setActivityStoreAttributeEntries(legsAttributeEntries);
                legsExercises.setData(legsExerciseData);
                legsExercises.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "20"));
                widgets.add(legsExercises);
            }

            if (!armsAttributeEntries.isEmpty()) {
                log.info("CFAPI::strength tracker: O LOGGED");
                armsExerciseData.setActivityStoreAttributeEntries(armsAttributeEntries);
                armsExercises.setData(armsExerciseData);
                armsExercises.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "20"));
                widgets.add(armsExercises);
            }

            if (!shouldersAttributeEntries.isEmpty()) {
                log.info("CFAPI::strength tracker: O LOGGED");
                shouldersExerciseData.setActivityStoreAttributeEntries(shouldersAttributeEntries);
                shouldersExercises.setData(shouldersExerciseData);
                shouldersExercises.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "20"));
                widgets.add(shouldersExercises);
            }

            if (widgets.isEmpty()) {
                strengthTrackerPage.setEmptyState(true);
                Action pageAction = Action.builder()
                        .actionType(ActionType.NAVIGATION)
                        .url("curefit://addexercisescreen?pageFrom=STRENGTH_TRACKER")
                        .title("Add an exercise")
                        .build();
                strengthTrackerPage.setPageAction(pageAction);
            }

            AddExerciseWidget addExerciseWidget = new AddExerciseWidget();
            WidgetContext widgetContext = new WidgetContext();
            HashMap<String, String> queryParams = new HashMap<>();
            queryParams.put("pageFrom", "STRENGTH_TRACKER");
            widgetContext.setQueryParams(queryParams);
            List<BaseWidget> addExerciseWidgetResponse = addExerciseWidget.buildView(serviceInterfaces, userContext, widgetContext);
            if (!addExerciseWidgetResponse.isEmpty() && addExerciseWidgetResponse.get(0) != null) {
                widgets.add(addExerciseWidgetResponse.get(0));
            }

            strengthTrackerPage.setWidgets(widgets);

        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
            log.info("CFAPI::strength tracker: Error in building strenght tracker Widget for gym {}", e);
        }

        return strengthTrackerPage;
    }

    private String getThumbnailImageUrl(List<Media> exerciseMedia) {
        for (Media media : exerciseMedia) {
            if (media.getType().equals(THUMBNAIL_IMAGE)) {
                return media.getUrl();
            }
        }
        return null;
    }

    private List<Map<String, ActivityStoreAttribute>> convertAttributeListToMap(List<List<ActivityStoreAttribute>> activityStoreAttributesList) throws ExecutionException, InterruptedException {
        if (activityStoreAttributesList == null) {
            return null;
        }

        Map<String, ActivityStoreAttribute> exerciseLogMap = new HashMap<>();
        Map<String, ActivityStoreAttribute> secondLatestExerciseLogMap = new HashMap<>();

        for (List<ActivityStoreAttribute> activityStoreAttributes : activityStoreAttributesList) {
            if (activityStoreAttributes == null || activityStoreAttributes.isEmpty()) {
                continue;
            }

            for (ActivityStoreAttribute attribute : activityStoreAttributes) {
                if (attribute.getExercises() == null || attribute.getExercises().isEmpty()) {
                    continue;
                }

                String baseDate = attribute.getDate();
                for (ExerciseAttribute exercise : attribute.getExercises()) {
                    if (exercise.getId() == null) {
                        continue;
                    }

                    ActivityStoreAttribute newAttribute = new ActivityStoreAttribute();
                    List<ExerciseAttribute> newExercise = new ArrayList<>();
                    newExercise.add(exercise);
                    newAttribute.setExercises(newExercise);
                    newAttribute.setDate(baseDate);
                    newAttribute.setUserId(attribute.getUserId());
                    newAttribute.setNamespace(attribute.getNamespace());
                    newAttribute.setSource(attribute.getSource());
                    newAttribute.setActivityType(attribute.getActivityType());
                    newAttribute.setSessionId(attribute.getSessionId());
                    newAttribute.setFrequency(attribute.getFrequency());
                    newAttribute.setIdempotenceKey(attribute.getIdempotenceKey());
                    newAttribute.setBookingId(attribute.getBookingId());
                    newAttribute.setWorkoutId(attribute.getWorkoutId());
                    newAttribute.setUpdatedCount(attribute.getUpdatedCount());
                    newAttribute.setBodyParts(attribute.getBodyParts());
                    newAttribute.setEventTime(attribute.getEventTime());

                    String exerciseId = exercise.getId();
                    if (!exerciseLogMap.containsKey(exerciseId)) {
                        exerciseLogMap.put(exerciseId, newAttribute);
                    } else {
                        ActivityStoreAttribute currentLatest = exerciseLogMap.get(exerciseId);
                        if (attribute.getDate().compareToIgnoreCase(currentLatest.getDate()) > 0) {
                            exerciseLogMap.put(exerciseId, attribute);
                        } else if (attribute.getDate().compareToIgnoreCase(currentLatest.getDate()) == 0) {
                            if (attribute.getEventTime().compareTo(currentLatest.getEventTime()) > 0) {
                                exerciseLogMap.put(exerciseId, attribute);
                            }
                        }
                    }
                }
            }
        }

        log.debug("Check exerciseLogMap {}", exerciseLogMap);
        log.info("Check secondLatestExerciseLogMap {}", secondLatestExerciseLogMap);

        List<Map<String, ActivityStoreAttribute>> result = new ArrayList<>();
        result.add(exerciseLogMap);
        result.add(secondLatestExerciseLogMap);

        return result;
    }

    private List<List<ActivityStoreAttribute>> retrieveMultipleLastExerciseLogEntry(ServiceInterfaces interfaces, String userId, List<String> exerciseIds) throws ExecutionException, InterruptedException {
        List<CompletableFuture<List<ActivityStoreAttribute>>> exerciseLogPromises = new ArrayList<>();

        exerciseIds.stream().forEach(exerciseId -> {
            exerciseLogPromises.add(CompletableFuture.supplyAsync(() -> {
                ActivityStoreAttributeSearchRequest searchLogRequest = new ActivityStoreAttributeSearchRequest();
                searchLogRequest.setUserId(java.util.Collections.singletonList(userId));
                searchLogRequest.setExerciseId(java.util.Collections.singletonList(exerciseId));
                searchLogRequest.setActivityType(ActivityTypeDS.WEIGHT_LOGGING);

                SortField sortField = new SortField();
                sortField.setField("date");
                sortField.setOrder(-1);
                searchLogRequest.setSortFields(java.util.Collections.singletonList(sortField));
                try {
                    List<ActivityStoreAttribute> logResponse = interfaces.loggingService.getActivityStoreAttribute(searchLogRequest);
                    if (logResponse != null && !logResponse.isEmpty()) {
                        for (ActivityStoreAttribute activityStoreAttribute : logResponse) {
                            List<ExerciseAttribute> filtering = new ArrayList<>();
                            for (ExerciseAttribute exerciseAttribute : activityStoreAttribute.getExercises()) {
                                if (exerciseAttribute.getId().equalsIgnoreCase(exerciseId)) {
                                    filtering.add(exerciseAttribute);
                                    break;
                                }
                            }
                            activityStoreAttribute.setExercises(filtering);
                        }
                        return logResponse;
                    }
                } catch (BaseException | IOException e) {
                    return null;
                }
                return null;
            }, interfaces.getTaskExecutor()));
        });

        CompletableFuture<List<List<ActivityStoreAttribute>>> exerciseLogCompletedPromise = FutureUtil.allOf(exerciseLogPromises);
        return exerciseLogCompletedPromise.get();
    }

    public GymHighlightsPage getGymHighlights(UserContext userContext, String exerciseId, String sessionId, List<String> musclesFocused, String bookingId, String pageFrom) throws Exception {

        GymHighlightsPage gymHighlightsPage = new GymHighlightsPage();
        // to fix: requires an app side fix so that should'nt be called with null exercise id. Temporary fix
        if (exerciseId == null) {
            return gymHighlightsPage;
        }
        try {
            WidgetContext widgetContext = new WidgetContext();
            HashMap<String, String> queryParams = new HashMap<>();
            queryParams.put("sessionId", sessionId);
            queryParams.put("exerciseId", exerciseId);
            queryParams.put("bookingId", bookingId);
            widgetContext.setQueryParams(queryParams);
            WeightLiftRankingWidget weightLiftRankingWidget = new WeightLiftRankingWidget();

            gymHighlightsPage.addWidget(weightLiftRankingWidget.buildView(serviceInterfaces, userContext, widgetContext));

            List<ActivityStoreAttributeEntry> activityStoreAttributeEntries = weightLiftRankingWidget.getData().getActivityStoreAttributeEntries();
//                    ActivityLoggingMeta meta = gxActivityLoggingWidget.getMeta();
            ActivityStoreAttributeEntry activityStoreAttributeEntry = new ActivityStoreAttributeEntry();
            if (activityStoreAttributeEntries != null && !activityStoreAttributeEntries.isEmpty()
                    && activityStoreAttributeEntries.get(0).getExercises() != null) {
                activityStoreAttributeEntries.get(0).setLogClickAction(getLogClickAction(false, activityStoreAttributeEntries.get(0).getExercises().get(0).getId(), "GYM_HIGHLIGHTS_PAGE"));
                activityStoreAttributeEntry = activityStoreAttributeEntries.get(0);
                String loggedDate = activityStoreAttributeEntry.getDate();
                if (loggedDate != null) {
                    LocalDate date = LocalDate.parse(loggedDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    // Format the date to "dd MMM"
                    String formattedDate = date.format(DateTimeFormatter.ofPattern("dd MMM"));
                    gymHighlightsPage.setLoggedDate(formattedDate);
                }
            }

            gymHighlightsPage.setActivityStoreAttributeEntry(getNewEntryFromExistingEntry(activityStoreAttributeEntry, userContext.getUserProfile().getUserId()));

            UserMetricValue userMetricValue = serviceInterfaces.metricClient.getLatestUserMetricValue(userContext.getUserProfile().getUserId(), 3L);
            double bodyWeight = (!(userMetricValue == null || userMetricValue.getValue() == null)) ? userMetricValue.getValue() : 0;
            double exerciseScore = (activityStoreAttributeEntry.isAlreadyLogged()) ? evaluateExerciseScore(activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getWeight().get(0),
                    activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getReps(), bodyWeight) : 0;

            UserActivityStatsResponse userActivityStatsResponse = weightLiftRankingWidget.getData().getUserActivityStatsResponse();
            List<PercentileValue> percentileValueList = null;
            if (userActivityStatsResponse != null) {
                percentileValueList = userActivityStatsResponse.getPercentiles();
            }

            if (percentileValueList != null && !percentileValueList.isEmpty()) {
                CustomProgressBarWidget customProgressBarWidget = new CustomProgressBarWidget();
                CustomProgressBarWidgetData customProgressBarWidgetData = getCustomProgressBarWidgetData(exerciseScore, (activityStoreAttributeEntry.isAlreadyLogged()) ? userActivityStatsResponse.getUserPercentile() : 0, percentileValueList, 0, isUserEligibleForPRAndShareFeature(userContext));
                customProgressBarWidgetData.setLocked(!(bodyWeight > 0));
                customProgressBarWidgetData.setLogged(activityStoreAttributeEntry.isAlreadyLogged());
                customProgressBarWidget.setData(customProgressBarWidgetData);
                gymHighlightsPage.addWidget(customProgressBarWidget);
            }
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
            log.error(e.getMessage());
        }

        try {
            EncapsulatorWidget encapsulatorWidget = new EncapsulatorWidget();
            List<ActivityAttributeNamespace> namespaces = new ArrayList<>();
            namespaces.add(ActivityAttributeNamespace.GX);
            namespaces.add(ActivityAttributeNamespace.GYM);
            namespaces.add(ActivityAttributeNamespace.ADHOC);
            List<ActivityStoreAttribute> activityStoreAttributes = encapsulatorWidget.getActivityAttributesForSameExercise(serviceInterfaces, userContext,
                    null, namespaces, null, java.util.Collections.singletonList(exerciseId));
            BaseWidgetNonVM cfChartWidgetResponse = getCFChartWidgetFromAttributeList(userContext, serviceInterfaces, activityStoreAttributes, false, exerciseId, "Your Exercise Trend");
//
//                    }
            if (cfChartWidgetResponse != null) {
                cfChartWidgetResponse.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "30"));
                gymHighlightsPage.addWidget(cfChartWidgetResponse);
            }
        } catch (BaseException e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
            log.info("CFAPI::Activity Logging: Error in building CF Chart Widget for gym {}", e);
        }

        Action stopLoggingAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .url(userContext.getSessionInfo().getAppVersion() >= 10.76 ? "curefit://strength_tracker_screen" : "curefit://hometab")
                .title("Finish")
                .build();

        Action logMoreAction = Action.builder()
                .actionType(ActionType.EMPTY_ACTION)
                .title("LOG MORE")
                .build();

        gymHighlightsPage.setLogMoreAction(logMoreAction);
        gymHighlightsPage.setStopLoggingAction(stopLoggingAction);

        if (pageFrom != null && pageFrom.equalsIgnoreCase("STRENGTH_TRACKER")) {
            gymHighlightsPage.setUpdateLogAction(new Action(ActionType.OPEN_EXERCISE_LOGGING_MODAL, "Add new log"));
        }

        return gymHighlightsPage;
    }

    public ActivityStoreAttributeEntry getNewEntryFromExistingEntry(ActivityStoreAttributeEntry entry, String userId) {
        ActivityStoreAttributeEntry newEntry = new ActivityStoreAttributeEntry();
        newEntry.setLastLoggedText(entry.getLastLoggedText());
        newEntry.setActivityType(entry.getActivityType());
        newEntry.setAlreadyLogged(entry.isAlreadyLogged());
        newEntry.setExerciseLogType(entry.getExerciseLogType());
        newEntry.setExercises(entry.getExercises());
        newEntry.setLastLoggedOn(entry.getLastLoggedOn());
        newEntry.setLoggedExerciseState(entry.getLoggedExerciseState());
        newEntry.setLogClickAction(entry.getLogClickAction());
        newEntry.setMuscleFocusData(entry.getMuscleFocusData());
        newEntry.setMusclesFocused(entry.getMusclesFocused());
        newEntry.setTitle(entry.getTitle());
        newEntry.setSubTitle(entry.getSubTitle());
        newEntry.setDescription(entry.getDescription());
        newEntry.setWorkoutId(entry.getWorkoutId());
        newEntry.setLogUnit(entry.getLogUnit());
        newEntry.setImageUrl(entry.getImageUrl());
        newEntry.setImageClickAction(entry.getImageClickAction());
        newEntry.setLogFrequency(entry.getLogFrequency());
        newEntry.setRepMaxEvaluationExpression(entry.getRepMaxEvaluationExpression());
        newEntry.setSubDescription(entry.getSubDescription());

        ZoneId istZone = ZoneId.of("Asia/Kolkata");
        LocalDateTime now = LocalDateTime.now(Clock.system(istZone));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String currDate = now.format(formatter);

        newEntry.setDate(currDate);
        newEntry.setBookingId(null);
        newEntry.setSessionId(null);
        newEntry.setSource(ActivitySource.CUREFIT_APP);
        newEntry.setIdempotenceKey(ActivityAttributeNamespace.ADHOC + "_" + userId + "_" + System.currentTimeMillis() + "_" + ActivityTypeDS.WEIGHT_LOGGING);
        newEntry.setNamespace(ActivityAttributeNamespace.ADHOC);

        return newEntry;
    }

    public ClassHighlightsPage getHighlights(UserContext userContext, String classId, String bookingId, String workoutIdForDeepLink) throws ExecutionException, InterruptedException, CloneNotSupportedException, BaseException, IOException {
        boolean isSuperUserPresent = false;
        boolean isSelfSuperUser = false;
        boolean showBanner = false;
        boolean showHlBanner = false;
        boolean showCentrumBanner = false;
        List<CommunityUserMappingEntry> pendingReceivedInvites;
        List<Long> communityIds = new ArrayList<>();
        List<String> squadMembers = new ArrayList<>();
        HashMap<String, String> userSquadVisibilityMap = new HashMap<>();
        List<String> hlPeopleUserIds = new ArrayList<>();
        Map<String, PendingCommunityInviteResponse> pendingInvites = new HashMap<>();
        Map<String, CommunityUserMappingEntry> pendingReceivedInviteIds = new HashMap<>();
        String userId = userContext.getUserProfile().getUserId();
        Long classesAttended = 0L;
        ClassHighlightsPage classHighlightsPage = new ClassHighlightsPage();

        classHighlightsPage.setWearableSyncAction(Action.builder().actionType(ActionType.WEARABLE_SYNC).title("WEARABLE_SYNC").build());
        LatestBookingDetail latestBookingDetail = null;
        if (workoutIdForDeepLink != null && !workoutIdForDeepLink.isEmpty()) {
            latestBookingDetail = getLatestBookingForWorkoutId(userContext, workoutIdForDeepLink);
        } else {
            latestBookingDetail = (classId == null || bookingId == null) ? getLatestBooking(userContext) : null;
        }
        if (latestBookingDetail == null) {
            log.error("No latestBookingDetail during getHighlights for userId={} classId={} bookingId={}", userId, classId, bookingId);
        }
        String hlClassId = null;
        String hlBookingId = null;

        if (classId != null && bookingId != null) {
            hlClassId = classId;
            hlBookingId = bookingId;
        } else if (latestBookingDetail != null) {
            hlClassId = latestBookingDetail.getClassId();
            hlBookingId = latestBookingDetail.getBookingId();
        }
        
        Long workoutId = 0L;

        if (bookingId != null) {
            CultBooking booking = getBooking(userContext, Long.parseLong(bookingId));
            if (booking != null) {
                workoutId = booking.getCultClass().getWorkout().getId();
            }
        } else if (latestBookingDetail != null) {
            workoutId = latestBookingDetail.getWorkoutId();
        }

        
        CompletableFuture<HighlightCardResponse> highlightCardResponseCompletableFuture = CompletableFuture.completedFuture(null);
        if (hlClassId != null && hlBookingId != null) {
            highlightCardResponseCompletableFuture = ragnarCommunityService.getHighlightCard(userContext, hlClassId, hlBookingId);
        }
        
        CompletableFuture<HighlightPeopleResponse> highlightPeopleResponseCompletableFuture = CompletableFuture.completedFuture(null);
        if (hlClassId != null) {
            highlightPeopleResponseCompletableFuture = ragnarCommunityService.getHighlightPeople(userContext, hlClassId);
        }

//        log.info("Debugg highlightPeopleResponse: {}", highlightPeopleResponse.toString());
        HighlightCardResponse highlightCardResponse = highlightCardResponseCompletableFuture != null ? highlightCardResponseCompletableFuture.get() : null;
        if (highlightCardResponse != null) {
            classHighlightsPage.setClassName(highlightCardResponse.getTitle());
            classHighlightsPage.setClassTime(highlightCardResponse.getDate() + " • " + highlightCardResponse.getTime());

            try {
                if(highlightCardResponse.getCardData() != null){
                    for(HighlightCardData cardData : highlightCardResponse.getCardData()){
                        if(cardData.getTitle().equalsIgnoreCase("current streak")){
                            cardData.setTitle(Long.parseLong(cardData.getValue()) > 1 ? "Weeks Active" : "Week Active");
                        }
                    }
                }
            } catch (NumberFormatException e) {
                serviceInterfaces.exceptionReportingService.reportException(e);
            }
        }

        Boolean isCommunityUser = AppUtil.isCommunityTabSupported(userContext, segmentEvaluatorService, environmentService);
        Boolean isUserEligibleForGXLogging = AppUtil.isUserEligibleForGXLogging(userContext, serviceInterfaces, workoutId);
        boolean hasUserEverLogged = AppUtil.hasMemberEverDoneLogging(userContext, serviceInterfaces);

        if (isUserEligibleForGXLogging) {
            if (AppUtil.doesUserBelongToCyclopsSegment(userContext, segmentEvaluatorService, AppUtil.LIVE_GYM_PROGRESSION_USERS)) {
                getLoggingWidgetsByNewLogic(userContext, hlClassId, hlBookingId, classHighlightsPage, isSuperUserPresent, classesAttended, showBanner, isSelfSuperUser, hasUserEverLogged, highlightCardResponse);
            } else {
                getLoggingWidgetsByExistingLogic(userContext, hlClassId, hlBookingId, classHighlightsPage, isSuperUserPresent, classesAttended, showBanner, isSelfSuperUser, hasUserEverLogged, highlightCardResponse);
            }
        }

        // Squads Invite option
        try {
            CommunitiesResponse communitiesResponse = this.socialServiceClient.getUserCommunities(userId, CommunityType.LEAGUE, true, 0, 50);
            for (CommunityEntry community : communitiesResponse.getCommunities()) {
                if (Objects.equals(userId, community.getCreatorNode().getEntityId())) {
                    communityIds.add(community.getId());
                }
            }

            pendingReceivedInvites = socialServiceClient.getPendingInvitesForUser(userContext.getUserProfile().getUserId(), true, 0, 10);
            for (CommunityUserMappingEntry invite : pendingReceivedInvites) {
                if (invite.getCommunity() != null
                        && invite.getCommunity().getCreatorNode() != null
                        && invite.getCommunity().getCreatorNode().getEntityId() != null) {
                    pendingReceivedInviteIds.put(invite.getCommunity().getCreatorNode().getEntityId(), invite);
                }
            }
            if (communityIds != null && !communityIds.isEmpty()) {
                communityIds.forEach(communityId -> {
                    List<PendingCommunityInviteResponse> pendingCommunityMembers = this.socialServiceClient.getPendingCommunityMembers(String.valueOf(communityId), 0, 50);
                    for (PendingCommunityInviteResponse pendingInvite : pendingCommunityMembers) {
                        pendingInvites.put(pendingInvite.getUserId(), pendingInvite);
                    }
                });
            }
            squadMembers = this.socialServiceClient.getAllUsersAcrossCommunitiesCached(userId);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        // create people you worked out with data
        HighlightPeopleWidget highlightPeopleWidget = new HighlightPeopleWidget();
        HighlightPeopleWidget.WidgetData data = new HighlightPeopleWidget.WidgetData();
        List<HighlightPeopleWidget.WidgetData.PeopleData> allMemberResponses = new ArrayList<>();
        List<HighlightPeopleWidget.WidgetData.PeopleData> inviteMemberResponses = new ArrayList<>();
        List<HighlightPeopleWidget.WidgetData.PeopleData> squadMemberResponses = new ArrayList<>();
        List<HighlightPeopleWidget.WidgetData.PeopleData> otherMemberResponses = new ArrayList<>();
        List<UserReviewTypeEntry> userReviewTypeEntries = new ArrayList<>();

        List<String> attributes = List.of(Constants.HABIT_BUILDING_COMPLETION_TIME);
        List<CompletableFuture<HighlightPeopleWidget.WidgetData.PeopleData>> getHighlightTasks = new ArrayList<>();
        List<String> fitNinjaLiveCentres = List.of("7", "267", "831", "151");


        List<String> finalSquadMembers = squadMembers;

        // create highlight card widget data

        HighlightCardWidget highlightCardWidget = new HighlightCardWidget();
        if (highlightCardResponse != null) {
            HighlightCardWidget.CardData cardData = new HighlightCardWidget.CardData(hlBookingId, hlClassId, highlightCardResponse.getDate(),
                    highlightCardResponse.getTime(), highlightCardResponse.getTitle(), highlightCardResponse.getHighlightImage() != null ? CULT_MOMENTS_URL_PREFIX + highlightCardResponse.getHighlightImage() : null,
                    highlightCardResponse.getAvatarUrl(), highlightCardResponse.getBadge(), highlightCardResponse.getCardbgUrl(), highlightCardResponse.getDefaultHighlightImage(), highlightCardResponse.getCardData(),
                    1000, "150:200", isCommunityUser, true, "Just had a killer workout session!");

            highlightCardWidget.setData(cardData);
        }


        try {
            WidgetContext widgetContext = new WidgetContext();
            List<BaseWidget> centrumBanner = serviceInterfaces.getWidgetBuilder().buildWidget(Constants.CENTRUM_HIGHLIGHTS, userContext, widgetContext);
            if (centrumBanner != null && !centrumBanner.isEmpty()) {

                HighlightsBannerWidget highlightsBannerWidget = new HighlightsBannerWidget();
                List<BannerItem> bannerData = new ArrayList<>();


                BaseBannerWidget bannerWidget = (BaseBannerWidget) centrumBanner.getFirst();
                if (bannerWidget != null ){
                    if (bannerWidget.getData() != null) {
                        BannerItem bannerItem = bannerWidget.getData().getFirst();
                        String title = bannerItem.getTitle();
                        String[] parts = title.split(",");
                        List<Integer> workoutIds = new ArrayList<>();
                        for (String part : parts) {
                            workoutIds.add(Integer.parseInt(part));
                        }
                        if (workoutIds.contains(Math.toIntExact(workoutId))) {
                            bannerData.add(bannerItem);
                            highlightsBannerWidget.setData(bannerData);
                            if (bannerWidget.getFooter() != null)
                                highlightsBannerWidget.setFooter(bannerWidget.getFooter());
                            if (bannerWidget.getFooterActions() != null)
                                highlightsBannerWidget.setFooterActions(bannerWidget.getFooterActions());
                            if (bannerWidget.getBannerScrollSpeed() != null)
                                highlightsBannerWidget.setBannerScrollSpeed(bannerWidget.getBannerScrollSpeed());
                            if (bannerWidget.getMaxNumBanners() != null)
                                highlightsBannerWidget.setMaxNumBanners(bannerWidget.getMaxNumBanners());
                            if (bannerWidget.getMinNumBanners() != null)
                                highlightsBannerWidget.setMinNumBanners(bannerWidget.getMinNumBanners());
                            if (bannerWidget.getProductType() != null)
                                highlightsBannerWidget.setProductType(bannerWidget.getProductType());
                            if (bannerWidget.getTtlExpiryInMs() != null)
                                highlightsBannerWidget.setTtlExpiryInMs(bannerWidget.getTtlExpiryInMs());
                            if (bannerWidget.getTtlViewCount() != null)
                                highlightsBannerWidget.setTtlViewCount(bannerWidget.getTtlViewCount());
                            if (bannerWidget.getWidgetMetric() != null)
                                highlightsBannerWidget.setWidgetMetric(bannerWidget.getWidgetMetric());
                            if (bannerWidget.getDividerType() != null)
                                highlightsBannerWidget.setDividerType(bannerWidget.getDividerType());
                            if (bannerWidget.getLayoutProps() != null)
                                highlightsBannerWidget.setLayoutProps(bannerWidget.getLayoutProps());
                            if (bannerWidget.getSpacing() != null)
                                highlightsBannerWidget.setSpacing(bannerWidget.getSpacing());
                            if (bannerWidget.getTemplateId() != null)
                                highlightsBannerWidget.setTemplateId(bannerWidget.getTemplateId());
                            showCentrumBanner = true;
                            classHighlightsPage.addWidget(highlightsBannerWidget);
                        }
                    }
                }
            }
        } catch (Exception e) {
            String message = String.format("<Class Highlight> Error in fetching banner, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }

        classHighlightsPage.addWidget(highlightCardWidget);
        highlightCardWidget.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget(showCentrumBanner ? "0" : "30", "30"));

        HighlightPeopleResponse highlightPeopleResponse = highlightPeopleResponseCompletableFuture != null ? highlightPeopleResponseCompletableFuture.get(): null;
        if (highlightPeopleResponse != null && highlightPeopleResponse.getData() != null) {
            for (HighlightPeopleResponse.PeopleResponse people : highlightPeopleResponse.getData()) {
                String cultUserId = people.getCultUserId();
                if (cultUserId != null) {
                    hlPeopleUserIds.add(cultUserId);
                }
            }
        }

        UserProfileBulkRequest userProfileBulkRequest = new UserProfileBulkRequest();
        userProfileBulkRequest.setUserIds(hlPeopleUserIds);
        try {
            List<UserProfileInvite> squadVisibility = this.socialServiceClient.getWhoCanInviteStatus(userProfileBulkRequest);
            squadVisibility.forEach(entry -> {
                userSquadVisibilityMap.put(entry.getUserId(), entry.getWhoCanInvite());
            });
        } catch (Exception e) {
            log.error("squad visibility error : {}", e.getMessage());
        }

        try {
            if (highlightPeopleResponse != null && highlightPeopleResponse.getData() != null) {
                userReviewTypeEntries = this.kudosService.getAllReviewTypes();
                List<UserEventReviewCount> userEventReviewCounts = this.kudosService.getReviewSummaryOfEvent(EventType.CULT_CLASS, hlClassId);
                Set<String> allFitChampionUsers = serviceInterfaces.fitChampionsCache.getAllFitChampions(NEW_CHAMP_SEGMENT_ID);
                CultClassResponse cultClass = serviceInterfaces.cultService.getCultClass(hlClassId, false, false, false, false, false, false).get();

                for (HighlightPeopleResponse.PeopleResponse people : highlightPeopleResponse.getData()) {
                    String cultUserId = people.getCultUserId();
                    String trainerId = people.getTrainerId();
                    log.info("CultUserId: {}", cultUserId);
                    CompletableFuture<HighlightPeopleWidget.WidgetData.PeopleData> task = CompletableFuture.supplyAsync(() -> {
                        boolean isGameCompleted = false;
                        boolean isFitChampion = false;

                        if (cultUserId != null) {
                            UserAttributesResponse userAttributesResponse = null;
                            try {
                                if (fitNinjaLiveCentres.contains(cultClass.getCultClass().getCenterID())) {
                                    userAttributesResponse = serviceInterfaces.userAttributesCacheClient.getAttributes(Long.parseLong(cultUserId), attributes, AppTenant.CUREFIT);
                                }
                                if (allFitChampionUsers != null)
                                    isFitChampion = allFitChampionUsers.contains(people.getCultUserId());
                            } catch (BaseException e) {
                                serviceInterfaces.exceptionReportingService.reportException(e);
                            }
                            if (userAttributesResponse != null && userAttributesResponse.getAttributes().get(Constants.HABIT_BUILDING_COMPLETION_TIME) != null) {
                                Long completionTime = Long.parseLong(userAttributesResponse.getAttributes().get(Constants.HABIT_BUILDING_COMPLETION_TIME).toString());
                                isGameCompleted = checkNinjaInLastNDays(completionTime, 30);
                            }
                        }

                        Optional<UserEventReviewCount> userEventReviewCount = userEventReviewCounts.stream().filter(element -> element.getUserId().equals((cultUserId == null && userContext.getSessionInfo().getAppVersion() >= 10.68f && AppUtil.doesUserBelongToCyclopsSegment(userContext, serviceInterfaces.segmentEvaluatorService, "0ab5a11e-eb24-4062-b9a4-4d058fca688e")) ? trainerId : cultUserId)).findAny();
                        log.info("Karma points check: 2 {}", userEventReviewCount.map(Object::toString).orElse("null"));
                        HighlightPeopleWidget.WidgetData.PeopleData.UserEventReviewCountDecorated userEventReviewCountDecorated = new HighlightPeopleWidget.WidgetData.PeopleData.UserEventReviewCountDecorated();

                        if (people.getCultUserId() == null && people.getTrainerId() != null && userContext.getSessionInfo().getAppVersion() >= 10.68f && AppUtil.doesUserBelongToCyclopsSegment(userContext, serviceInterfaces.segmentEvaluatorService, "0ab5a11e-eb24-4062-b9a4-4d058fca688e")) {
                            userEventReviewCountDecorated.setUserId(trainerId);
                            userEventReviewCountDecorated.setUserType(UserType.TRAINER);
                        } else {
                            userEventReviewCountDecorated.setUserId(cultUserId);
                            userEventReviewCountDecorated.setUserType(UserType.CUSTOMER);
                        }
                        userEventReviewCountDecorated.setShowLottie(false);
                        if (userEventReviewCount.isPresent()) {
                            userEventReviewCountDecorated.setCount(userEventReviewCount.get().getCount());
                            List<String> userIds = userEventReviewCount.get().getReviewerIds();
                            userEventReviewCountDecorated.setGiven(userIds.contains(userId));
                            log.info("Karma points check: 3 {}", userEventReviewCountDecorated);
                        } else {
                            userEventReviewCountDecorated.setCount(0L);
                            userEventReviewCountDecorated.setGiven(false);
                            log.info("Karma points check: 4 {}", userEventReviewCountDecorated);
                        }

                        Action action = null;
                        if((people.getCultUserId() != null && people.getTrainerId() == null)){
                            String url = "curefit://social_user_profile?cultUserId=" + people.getCultUserId();
                            action = new Action(url, ActionType.NAVIGATION);
                        }

                        SquadState itemSquadState = getSquadState(pendingInvites, pendingReceivedInviteIds, userContext, finalSquadMembers, cultUserId);

                        HighlightPeopleWidget.WidgetData.PeopleData peopleData = HighlightPeopleWidget.WidgetData.PeopleData.builder()
                                .displayName(people.getDisplayName())
                                .cultUserId((cultUserId == null && userContext.getSessionInfo().getAppVersion() >= 10.68f && AppUtil.doesUserBelongToCyclopsSegment(userContext, serviceInterfaces.segmentEvaluatorService, "0ab5a11e-eb24-4062-b9a4-4d058fca688e")) ? trainerId : cultUserId)
                                .socialUserId(people.getSocialUserId())
                                .tag(people.getTag())
                                .secondaryData(people.getSecondaryData())
                                .avatarUrl(people.getAvatarUrl())
                                .classesAttended(people.getClassesAttended() != null ? people.getClassesAttended() : 0L)
                                .superUser(people.getSuperUser() != null ? people.getSuperUser() : false)
                                .isCultNinja(isGameCompleted)
                                .isFitChampion(isFitChampion)
                                .squadState(itemSquadState)
                                .squadVisibility(AppUtil.isNewSquadUsersSegment(userContext, serviceInterfaces) && !Objects.equals(cultUserId, userId)
                                        ? userSquadVisibilityMap.getOrDefault(cultUserId, null)
                                        : null)
                                .communityMappingId(null)
                                .userEventReviewCountDecorated(Objects.equals(itemSquadState, SquadState.INVITE_RECEIVED) ? null : userEventReviewCountDecorated)
                                .action(action)
                                .build();
                        return peopleData;
                    });
                    getHighlightTasks.add(task);
                }
            }
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
            log.error("Error in fetching highlights people : {}", e);
        }
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(getHighlightTasks.toArray(new CompletableFuture[0]));
        allTasks.get();

        List<HighlightPeopleWidget.WidgetData.PeopleData> results = getHighlightTasks.stream()
                .map(CompletableFuture::join)
                .toList();
        HighlightPeopleWidget.WidgetData.PeopleData selfUserData = null;

        UserProfileBulkRequest userProfileBulkRequestForPrivateProfile = new UserProfileBulkRequest();
        userProfileBulkRequestForPrivateProfile.setUserIds(hlPeopleUserIds);
        List<com.curefit.socialservice.pojo.UserProfileEntry> userProfileEntries = serviceInterfaces.socialService.getUserProfileBulk(userProfileBulkRequestForPrivateProfile);

        List<String> updatedHlPeopleUserIds = new ArrayList<>();

        for (com.curefit.socialservice.pojo.UserProfileEntry userProfileEntry : userProfileEntries) {
//            log.info("Debug userProfileEntry: {}", userProfileEntry.toString());
            if (userProfileEntry.getUserId().equals(userId)) {
                updatedHlPeopleUserIds.add(userProfileEntry.getUserId());
            }
            if (userProfileEntry.getVisibility() == com.curefit.socialservice.enums.ProfileVisibility.PUBLIC) {
                updatedHlPeopleUserIds.add(userProfileEntry.getUserId());
            } else if (userProfileEntry.getVisibility() == com.curefit.socialservice.enums.ProfileVisibility.MY_CONTACTS) {
                NodeRelationEntry nodeRelationEntry = new NodeRelationEntry();
                nodeRelationEntry.setParentId(Long.valueOf(userProfileEntry.getUserId()));
                nodeRelationEntry.setChildId(Long.valueOf(userId));
                nodeRelationEntry.setAttributeCode(RelationAttributeCode.HAS_CONTACT);
                try {
                    NodeRelationEntry nodeRelationEntryExist = socialServiceClient.getParentChildRelationship(nodeRelationEntry);
                    if (nodeRelationEntryExist != null) {
                        updatedHlPeopleUserIds.add(userProfileEntry.getUserId());
                    }
                } catch (Exception e) {
                    log.error("Error in fetching node relation: {}", e);
                }
            } else if (userProfileEntry.getVisibility() == com.curefit.socialservice.enums.ProfileVisibility.CULT_MEMBERS) {
                String timezone = userContext.getUserProfile().getCity().getTimezone();
                long currentTime = TimeUtil.now(timezone);
                try {
                    List<Membership> membershipDetails = membershipService.getCachedMembershipsForUser(userProfileEntry.getUserId(), "curefit", MembershipFilter.builder().benefits(MembershipUtil.MEMBERSHIP_BENEFITS).status(Status.PURCHASED).status(Status.PAUSED).start(currentTime).end(currentTime).build()).get();
                    if (membershipDetails != null && !membershipDetails.isEmpty()) {
                        updatedHlPeopleUserIds.add(userProfileEntry.getUserId());
                    }
                } catch (Exception e) {
                    log.error("Error in fetching cached membership: {}", e);
                }
            }
        }

        List<HighlightPeopleWidget.WidgetData.PeopleData> updateResults = new ArrayList<>();

        for (HighlightPeopleWidget.WidgetData.PeopleData peopleData : results) {
            if (peopleData.getTag() != null && peopleData.getTag().equals("TRAINER")) {
                updateResults.add(peopleData);
            } else if ((!peopleData.getSquadState().equals(SquadState.NON_MEMBER) && !peopleData.getSquadState().equals(SquadState.REQUESTED)) || (peopleData.getCultUserId() != null)) {
                if (updatedHlPeopleUserIds.contains(peopleData.getCultUserId())) {
                    updateResults.add(peopleData);
                }
            }
        }

        results = updateResults;

        for (HighlightPeopleWidget.WidgetData.PeopleData peopleData : results) {
            String cultUserId = peopleData.getCultUserId();
            if (!Objects.equals(userContext.getUserProfile().getUserId(), peopleData.getCultUserId())) {
                if (pendingReceivedInviteIds.containsKey(cultUserId)) {
                    if (pendingReceivedInviteIds.containsKey(cultUserId))
                        peopleData.setCommunityMappingId(String.valueOf(pendingReceivedInviteIds.get(cultUserId).getId()));
                    HighlightPeopleWidget.WidgetData.PeopleData newPeopleData = (HighlightPeopleWidget.WidgetData.PeopleData) peopleData.clone();
                    newPeopleData.setDisplayName(peopleData.getDisplayName() + "'s squad");
                    newPeopleData.setSecondaryData(null);
                    inviteMemberResponses.add(newPeopleData);
                }
                if (squadMembers.contains(cultUserId)) {
                    squadMemberResponses.add(peopleData);
                } else {
                    if (pendingInvites.containsKey(cultUserId))
                        peopleData.setCommunityMappingId(pendingInvites.get(cultUserId).getId());
                    otherMemberResponses.add(peopleData);
                }
                isSuperUserPresent = isSuperUserPresent || AppUtil.isChampionSegment(cultUserId, serviceInterfaces);
            } else {
                if (peopleData.getClassesAttended() != null) classesAttended = peopleData.getClassesAttended();
                if (peopleData.getSuperUser() != null) isSelfSuperUser = peopleData.getSuperUser();
                peopleData.setTag("YOU");
                selfUserData = peopleData;
            }
        }

        if (selfUserData != null) {
            if (!otherMemberResponses.isEmpty()) otherMemberResponses.add(1, selfUserData);
            else otherMemberResponses.add(selfUserData);
        }

        allMemberResponses.addAll(squadMemberResponses);
        allMemberResponses.addAll(otherMemberResponses);
        if (AppUtil.isNewSquadUsersSegment(userContext, serviceInterfaces)) {
            data.setSquadMembers(squadMemberResponses);
            data.setInviterMembers(inviteMemberResponses);
            data.setData(otherMemberResponses);
        } else
            data.setData(allMemberResponses);

        List<HighlightPeopleWidget.Benefits> squadBenefits = new ArrayList<>();
        squadBenefits.add(new HighlightPeopleWidget.Benefits("FLAME", "Track activity "));
        squadBenefits.add(new HighlightPeopleWidget.Benefits("GROUP", "Workout together"));
        squadBenefits.add(new HighlightPeopleWidget.Benefits("PROGRESS", "View progress"));

        Map<String, String> buttonTitles = new HashMap<>();
        buttonTitles.put("INVITE", "Add To Squad");
        buttonTitles.put("REQUESTED", "Cancel Request");
        buttonTitles.put("INVITE_ACCEPT", "ACCEPT");
        buttonTitles.put("INVITE_REJECT", "REJECT");


        highlightPeopleWidget.setData(data);
        highlightPeopleWidget.setClassId(hlClassId);
        highlightPeopleWidget.setBookingId(hlBookingId);
        highlightPeopleWidget.setSquadTitle("Mutual Squad Members");
        highlightPeopleWidget.setInviterTitle("New squad Invitations");
        highlightPeopleWidget.setOthersTitle(squadMemberResponses.isEmpty() && inviteMemberResponses.isEmpty() ? null : "Others");
        highlightPeopleWidget.setEnableSquads(AppUtil.isNewSquadUsersSegment(userContext, serviceInterfaces));
        highlightPeopleWidget.setInviteState("ADD TO SQUAD");
        highlightPeopleWidget.setRequestedState("REQUESTED");
        highlightPeopleWidget.setInviteActionTitle("SEND INVITE");
        highlightPeopleWidget.setInviteBenefitsText("once accepted you can");
        highlightPeopleWidget.setSquadBenefits(squadBenefits);
        highlightPeopleWidget.setButtonTitles(buttonTitles);
        highlightPeopleWidget.setAvatarUrl((highlightCardWidget.getData() != null && highlightCardWidget.getData().getAvatarUrl() != null)
                ? highlightCardWidget.getData().getAvatarUrl() : null);
        highlightPeopleWidget.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("30", "30"));
        highlightPeopleWidget.setEnableKudos(true);
        highlightPeopleWidget.setNinjaLottieUrl("/image/community/highlights/ninja_lottie.json");
        highlightPeopleWidget.setCoverLottieUrl("/image/community/highlights/champion_lottie.json");

        String kudosOnboardingKey = Constants.KARMA_KUDOS_ONBOARDING_KEY;

        if (!this.featureStateCache.match(userId, kudosOnboardingKey, "viewed").get()) {
            Action kudosOnboardingAction = new Action(ActionType.SHOW_KARMA_POINTS_INTRO, "");
            HashMap<String, Object> actionMeta = new HashMap<>();
            actionMeta.put("title", "");
            actionMeta.put("icon", "assets/karma_with_background.png");
            actionMeta.put("banner", "/image/karma/bannerV4.png");
            actionMeta.put("buttonTitle", "GOT IT");
            kudosOnboardingAction.setMeta(actionMeta);

            classHighlightsPage.setKudosOnboarding(kudosOnboardingAction);
            classHighlightsPage.setShowKudosOnboarding(true);
            this.featureStateCache.set(userId, kudosOnboardingKey, "viewed");
        }

        Action giveKudosAction = new Action(ActionType.SHOW_KARMA_POINTS_BOTTOM_SHEET, "");
        HashMap<String, Object> actionMeta = new HashMap<>();

        actionMeta.put("title", "Thanks For..");
        actionMeta.put("karmaEmoji", "assets/karma_with_background.png");
        actionMeta.put("buttonTitle", "SEND COMPLIMENTS");
        actionMeta.put("userReviewTypeEntry", userReviewTypeEntries);

        giveKudosAction.setMeta(actionMeta);
        highlightPeopleWidget.setKudosBottomSheet(giveKudosAction);


        Action action;
        if (AppUtil.getIfUserBelongsToHabitBuildingGameSegment(userContext, this.serviceInterfaces.environmentService, this.serviceInterfaces.segmentEvaluatorService)) {
            action = Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .url("curefit://activity_streak_game_V2")
                    .title("My Fitness Journey")
                    .build();
        } else {
            action = Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .url("curefit://classbookingv2?productType=FITNESS")
                    .title("Book next class")
                    .build();
        }

        String highlightsBannerWidgetId = "6bac2580-3455-42c8-9f07-d42492dc40dc-final";
        Map<String, String> cultUnboundBannerWodIdsData = cultUnboundBannerWodIdsConfigService.getPageConfigData();
        if (cultUnboundBannerWodIdsData != null && highlightCardResponse != null && cultUnboundBannerWodIdsData.get(highlightCardResponse.getWodId()) != null) {
            highlightsBannerWidgetId = cultUnboundBannerWodIdsData.get(highlightCardResponse.getWodId());
        }

        // Highlight banner
        try {
            HighlightsBannerWidget highlightsBannerWidget = new HighlightsBannerWidget();
            List<String> widgetIds = new ArrayList<>();
            widgetIds.add(highlightsBannerWidgetId);
            PageContext pageContext = new PageContext();
            WidgetContext widgetContext = new WidgetContext(pageContext);
            List<BaseWidget> mergedWidgetList = new ArrayList<>();
            CompletableFuture<BuildWidgetResponse> bodyResponseFuture = this.serviceInterfaces.getWidgetBuilder().buildWidgets(widgetIds, userContext, widgetContext);
            mergedWidgetList.addAll(bodyResponseFuture.get().getWidgets());
            if (!mergedWidgetList.isEmpty()) {
                BaseBannerWidget bannerWidget = (BaseBannerWidget) mergedWidgetList.get(0);
                if (bannerWidget != null) {
                    List<BannerItem> bannerData = new ArrayList<>();
                    if (bannerWidget.getData() != null) {
                        for (BannerItem banner : bannerWidget.getData()) {

                            List<Long> workoutIdList = new ArrayList<>();
                            String bannerTitle = banner.getTitle();
                            if (bannerTitle != null) {
                                String[] eligibleWorkoutIdList = banner.getTitle().split(",");

                                for (String id : eligibleWorkoutIdList) {
                                    try {
                                        workoutIdList.add(Long.parseLong(id.trim()));
                                    } catch (NumberFormatException e) {
                                        log.warn("Skipping invalid workout ID in banner title '{}': '{}'", banner.getTitle(), id.trim());
                                    }
                                }
                            }

                            if (!workoutIdList.isEmpty()) {
                                if (workoutId != null && workoutIdList.contains(workoutId)) {
                                    bannerData.add(banner);
                                    showHlBanner = true;
                                }
                            }

                            else if (Objects.equals(banner.getBannerIdentifier(), "highlights_new_user")) {
                                if (isSuperUserPresent && classesAttended <= 3) {
                                    bannerData.add(banner);
                                    showHlBanner = true;
                                }
                            } else if (Objects.equals(banner.getBannerIdentifier(), "highlights_super_user")) {
                                if (AppUtil.isChampionSegment(userContext.getUserProfile().getUserId(), serviceInterfaces)) {
                                    bannerData.add(banner);
                                    showHlBanner = true;
                                }
                            } else if (Objects.equals(banner.getBannerIdentifier(), "YER banner")) {
                                bannerData.add(banner);
                                showHlBanner = true;
                            } else {
                                bannerData.add(banner);
                                showHlBanner = true;
                            }
                        }
                    }
                    highlightsBannerWidget.setData(bannerData);
                    if (bannerWidget.getFooter() != null)
                        highlightsBannerWidget.setFooter(bannerWidget.getFooter());
                    if (bannerWidget.getFooterActions() != null)
                        highlightsBannerWidget.setFooterActions(bannerWidget.getFooterActions());
                    if (bannerWidget.getBannerScrollSpeed() != null)
                        highlightsBannerWidget.setBannerScrollSpeed(bannerWidget.getBannerScrollSpeed());
                    if (bannerWidget.getMaxNumBanners() != null)
                        highlightsBannerWidget.setMaxNumBanners(bannerWidget.getMaxNumBanners());
                    if (bannerWidget.getMinNumBanners() != null)
                        highlightsBannerWidget.setMinNumBanners(bannerWidget.getMinNumBanners());
                    if (bannerWidget.getProductType() != null)
                        highlightsBannerWidget.setProductType(bannerWidget.getProductType());
                    if (bannerWidget.getTtlExpiryInMs() != null)
                        highlightsBannerWidget.setTtlExpiryInMs(bannerWidget.getTtlExpiryInMs());
                    if (bannerWidget.getTtlViewCount() != null)
                        highlightsBannerWidget.setTtlViewCount(bannerWidget.getTtlViewCount());
                    if (bannerWidget.getWidgetMetric() != null)
                        highlightsBannerWidget.setWidgetMetric(bannerWidget.getWidgetMetric());
                    if (bannerWidget.getDividerType() != null)
                        highlightsBannerWidget.setDividerType(bannerWidget.getDividerType());
                    if (bannerWidget.getLayoutProps() != null)
                        highlightsBannerWidget.setLayoutProps(bannerWidget.getLayoutProps());
                    if (bannerWidget.getSpacing() != null)
                        highlightsBannerWidget.setSpacing(bannerWidget.getSpacing());
                    if (bannerWidget.getTemplateId() != null)
                        highlightsBannerWidget.setTemplateId(bannerWidget.getTemplateId());
                }
            }
            if (showHlBanner) {
                classHighlightsPage.addWidget(highlightsBannerWidget);
            }
        } catch (Exception e) {
            String message = String.format("<Class Highlight> Error in fetching banner, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }


        // set feedbackId
        String feedbackId = null;

        if (classId == null) {
            List<String> allUserIds = new ArrayList<>();
            allUserIds.add(userContext.getUserProfile().getUserId());
            CompletableFuture<Feedback> feedbackPromise = this.feedbackService.getFeedbackToShow(allUserIds, userContext);
            Feedback feedback = feedbackPromise != null ? feedbackPromise.join() : null;
            if (feedback != null) feedbackId = feedback.getFeedbackId();
        } else {
            try {
                com.curefit.curio.Feedback itemFeedback = iFeedbackService.getFeedbackFromItemId(hlClassId, userContext.getUserProfile().getUserId());
                if (itemFeedback != null && (Objects.equals(itemFeedback.getRating(), FeedbackRating.NOT_RATED)
                        || Objects.equals(itemFeedback.getRating(), FeedbackRating.DISMISSED))) {
                    feedbackId = itemFeedback.getFeedbackId();
                }
            } catch (Exception e) {
                String message = String.format("<Class Highlight> Error in fetching feedback, error :: %s", e.getMessage());
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }
        }

        try {
            classHighlightsPage.addWidget(highlightPeopleWidget);
            classHighlightsPage.setBookClassAction(action);
            if (feedbackId != null && AppUtil.doesUserBelongToNewFeedbackSegment(serviceInterfaces, userContext))
                classHighlightsPage.setFeedbackId(feedbackId);
        } catch (Exception e) {
            String message = String.format("<Class Highlight> Error in creating for class highlight, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        log.debug("<Class Highlight> view created :: {} ", classHighlightsPage);
        return classHighlightsPage;
    }

    private void getLoggingWidgetsByNewLogic(UserContext userContext, String hlClassId, String hlBookingId, ClassHighlightsPage classHighlightsPage, boolean isSuperUserPresent, Long classesAttended, boolean showBanner, boolean isSelfSuperUser, boolean hasUserEverLogged, HighlightCardResponse highlightCardResponse) {
        try {
            GXActivityLoggingWidget gxActivityLoggingWidget = new GXActivityLoggingWidget();
            WidgetContext gxWidgetContext = new WidgetContext();
            HashMap<String, String> gxQueryParams = new HashMap<>();
            gxQueryParams.put("sessionId", hlClassId);
            gxQueryParams.put("bookingId", hlBookingId);
            gxWidgetContext.setQueryParams(gxQueryParams);
            BaseWidgetNonVM buildGXLoggingWidget = gxActivityLoggingWidget.buildView(serviceInterfaces, userContext, gxWidgetContext);

            if (isUserEligibleForProgressionV2(userContext, serviceInterfaces)) {
                WeightLiftRankingWidget weightLiftRankingWidget = new WeightLiftRankingWidget();
                BaseWidgetNonVM buildWeightLiftRankingWidget = weightLiftRankingWidget.buildView(serviceInterfaces, userContext, gxWidgetContext);

                if (weightLiftRankingWidget.getData() == null) {
                    return;
                }

                List<ActivityStoreAttributeEntry> activityStoreAttributeEntries = weightLiftRankingWidget.getData().getActivityStoreAttributeEntries();
//                    ActivityLoggingMeta meta = gxActivityLoggingWidget.getMeta();
                ActivityStoreAttributeEntry activityStoreAttributeEntry = new ActivityStoreAttributeEntry();
                if (activityStoreAttributeEntries != null && !activityStoreAttributeEntries.isEmpty()
                        && activityStoreAttributeEntries.get(0).getExercises() != null) {
                    activityStoreAttributeEntries.get(0).setLogClickAction(getLogClickAction(false, (activityStoreAttributeEntries.get(0).getExercises().get(0).getId() != null) ? activityStoreAttributeEntries.get(0).getExercises().get(0).getId() : null, "HIGHLIGHTS_PAGE"));
                    activityStoreAttributeEntry = activityStoreAttributeEntries.get(0);
                }

//                UserActivityStatsResponse userActivityStatsResponse = null;
//                List<PercentileValue> percentileValueList = new ArrayList<>();
//
//                UserMetricValue userMetricValue = serviceInterfaces.metricClient.getLatestUserMetricValue(userContext.getUserProfile().getUserId(), 3L);
//                double bodyWeight = (!(userMetricValue == null || userMetricValue.getValue() == null)) ? userMetricValue.getValue() : 0;
//                double exerciseScore = (activityStoreAttributeEntry.isAlreadyLogged()) ? evaluateExerciseScore(activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getWeight().get(0),
//                        activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getReps(), bodyWeight) : 0;
//
//                if (activityStoreAttributeEntry.isAlreadyLogged()) {
//                    UserActivityStatsRequest userActivityStatsRequest = UserActivityStatsRequest.builder()
//                            .activityId(activityStoreAttributeEntry.getExercises().get(0).getId())
//                            .userId(userContext.getUserProfile().getUserId())
//                            .activityType(UserActivityType.valueOf("EXERCISE_WEIGHT_LOGGING"))
//                            .userActivityValue(Double.valueOf(exerciseScore))
//                            .build();
//                    userActivityStatsResponse = serviceInterfaces.userActivityService.fetchActivityStatsForUser(userActivityStatsRequest);
//                    if (userActivityStatsResponse != null)
//                        percentileValueList = userActivityStatsResponse.getPercentiles();
//                }
//
//                WeightLiftRankingWidget weightLiftRankingWidget = new WeightLiftRankingWidget();
//                WeightLiftRankingWidgetData weightLiftRankingWidgetData = getWeightLiftRankingWidgetData(activityStoreAttributeEntries, activityStoreAttributeEntry, meta, userMetricValue, userActivityStatsResponse);

                // Added redis key for showing new logging banner 2 times
//                if (!activityStoreAttributeEntry.isAlreadyLogged() && isUserEligibleForNewHRXBanner(userContext)) {
//                    String redisKey = "HRX_FIRST_TIME_LOGGING_BANNER" + userContext.getUserProfile().getUserId();
//                    String redisValue = this.cfApiRedisKeyValueStore.get(redisKey);
//                    Boolean firstTimeLoggingBannerApplicable = true;
//                    if (redisValue == null || redisValue.isEmpty()) {
//                        this.cfApiRedisKeyValueStore.set(redisKey, "1");
//                    } else {
//                        try {
//                            int currentValue = Integer.parseInt(redisValue);
//                            int incrementedValue = currentValue + 1;
//                            if(currentValue > 1)
//                                firstTimeLoggingBannerApplicable = false;
//                            else
//                                this.cfApiRedisKeyValueStore.set(redisKey, String.valueOf(incrementedValue));
//                        } catch (Exception e) {
//                            serviceInterfaces.exceptionReportingService.reportException(e);
//                            log.error(e.getMessage());
//                        }
//                    }
//                    if (firstTimeLoggingBannerApplicable) weightLiftRankingWidgetData.setFirstTimeLoggingBannerImg("/image/progression/first_time_banner_v2.png");
//                }

//                weightLiftRankingWidgetData.setLogged(activityStoreAttributeEntry.isAlreadyLogged());
                // appside minor issue in opening half card
                if (!activityStoreAttributeEntry.isAlreadyLogged() &&
                        activityStoreAttributeEntry.getExercises() != null
                        && !activityStoreAttributeEntry.getExercises().isEmpty()) {
                    List<Double> weight = new ArrayList<>();
                    weight.add(0.0);
                    activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).setWeight(weight);
                }
                classHighlightsPage.addWidget(buildWeightLiftRankingWidget);

                if (!activityStoreAttributeEntry.isAlreadyLogged() && !Objects.equals(weightLiftRankingWidget.getData().getTitle(), "")) {
                    HighlightsBannerWidget highlightsBannerWidget = new HighlightsBannerWidget();
                    try {
                        List<String> widgetIds = new ArrayList<>();
                        widgetIds.add("d984957e-c638-4420-97fb-266f954ee515");
                        PageContext pageContext = new PageContext();
                        WidgetContext widgetContext = new WidgetContext(pageContext);
                        List<BaseWidget> mergedWidgetList = new ArrayList<>();
                        CompletableFuture<BuildWidgetResponse> bodyResponseFuture = this.serviceInterfaces.getWidgetBuilder().buildWidgets(widgetIds, userContext, widgetContext);
                        mergedWidgetList.addAll(bodyResponseFuture.get().getWidgets());
                        log.info("Highlights banner: 1 {}", bodyResponseFuture);
                        if (!mergedWidgetList.isEmpty()) {
                            BaseBannerWidget bannerWidget = (BaseBannerWidget) mergedWidgetList.get(0);
                            log.info("Highlights banner: 2 {}", bannerWidget.toString());
                            if (bannerWidget != null) {
                                List<BannerItem> bannerData = new ArrayList<>();
                                if (bannerWidget.getData() != null) {
                                    for (BannerItem banner : bannerWidget.getData()) {
                                        log.info("Highlights banner: 4 {}", banner.toString());
                                        if (Objects.equals(banner.getBannerIdentifier(), "highlights_new_user")) {
                                            if (isSuperUserPresent && classesAttended <= 3) {
                                                bannerData.add(banner);
                                                showBanner = true;
                                            }
                                        } else if (Objects.equals(banner.getBannerIdentifier(), "highlights_super_user")) {
                                            if (isSelfSuperUser) {
                                                bannerData.add(banner);
                                                showBanner = true;
                                            }
                                        } else if (Objects.equals(banner.getBannerIdentifier(), "YER banner")) {
                                            bannerData.add(banner);
                                            showBanner = true;
                                        } else {
                                            bannerData.add(banner);
                                            showBanner = true;
                                        }
                                    }
                                }
                                highlightsBannerWidget.setData(bannerData);
                                if (bannerWidget.getFooter() != null)
                                    highlightsBannerWidget.setFooter(bannerWidget.getFooter());
                                if (bannerWidget.getFooterActions() != null)
                                    highlightsBannerWidget.setFooterActions(bannerWidget.getFooterActions());
                                if (bannerWidget.getBannerScrollSpeed() != null)
                                    highlightsBannerWidget.setBannerScrollSpeed(bannerWidget.getBannerScrollSpeed());
                                if (bannerWidget.getMaxNumBanners() != null)
                                    highlightsBannerWidget.setMaxNumBanners(bannerWidget.getMaxNumBanners());
                                if (bannerWidget.getMinNumBanners() != null)
                                    highlightsBannerWidget.setMinNumBanners(bannerWidget.getMinNumBanners());
                                if (bannerWidget.getProductType() != null)
                                    highlightsBannerWidget.setProductType(bannerWidget.getProductType());
                                if (bannerWidget.getTtlExpiryInMs() != null)
                                    highlightsBannerWidget.setTtlExpiryInMs(bannerWidget.getTtlExpiryInMs());
                                if (bannerWidget.getTtlViewCount() != null)
                                    highlightsBannerWidget.setTtlViewCount(bannerWidget.getTtlViewCount());
                                if (bannerWidget.getWidgetMetric() != null)
                                    highlightsBannerWidget.setWidgetMetric(bannerWidget.getWidgetMetric());
                                if (bannerWidget.getDividerType() != null)
                                    highlightsBannerWidget.setDividerType(bannerWidget.getDividerType());
                                if (bannerWidget.getLayoutProps() != null)
                                    highlightsBannerWidget.setLayoutProps(bannerWidget.getLayoutProps());
                                if (bannerWidget.getSpacing() != null)
                                    highlightsBannerWidget.setSpacing(bannerWidget.getSpacing());
                                if (bannerWidget.getTemplateId() != null)
                                    highlightsBannerWidget.setTemplateId(bannerWidget.getTemplateId());
                            }
                        }
                        if (showBanner) {
                            log.info("Highlights banner: 5 {}", showBanner);
                            classHighlightsPage.addWidget(highlightsBannerWidget);
                        }
                    } catch (Exception e) {
                        log.error("<Class Highlight> banner error : {}", e.getMessage());
                    }
                }

                UserMetricValue userMetricValue = serviceInterfaces.metricClient.getLatestUserMetricValue(userContext.getUserProfile().getUserId(), 3L);
                double bodyWeight = (!(userMetricValue == null || userMetricValue.getValue() == null)) ? userMetricValue.getValue() : 0;
                double exerciseScore = (activityStoreAttributeEntry.isAlreadyLogged()) ? evaluateExerciseScore(activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getWeight().get(0),
                        activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getReps(), bodyWeight) : 0;

                UserActivityStatsResponse userActivityStatsResponse = weightLiftRankingWidget.getData().getUserActivityStatsResponse();
                List<PercentileValue> percentileValueList = null;
                if (userActivityStatsResponse != null)
                    percentileValueList = userActivityStatsResponse.getPercentiles();

                UserActivityStatsResponse previousUserActivityStatsResponse = null;
                if (weightLiftRankingWidget.getData().getPreviousExScore() != 0) {
                    UserActivityStatsRequest userActivityStatsRequest = UserActivityStatsRequest.builder()
                            .activityId(activityStoreAttributeEntry.getExercises().get(0).getId())
                            .userId(userContext.getUserProfile().getUserId())
                            .activityType(UserActivityType.valueOf("EXERCISE_WEIGHT_LOGGING"))
                            .userActivityValue(Double.valueOf(weightLiftRankingWidget.getData().getPreviousExScore()))
                            .build();
                    previousUserActivityStatsResponse = serviceInterfaces.userActivityService.fetchActivityStatsForUser(userActivityStatsRequest);
                }

                try {
                    if (percentileValueList != null && !percentileValueList.isEmpty()) {
                        CustomProgressBarWidget customProgressBarWidget = new CustomProgressBarWidget();
                        CustomProgressBarWidgetData customProgressBarWidgetData = getCustomProgressBarWidgetData(exerciseScore, (activityStoreAttributeEntry.isAlreadyLogged()) ? userActivityStatsResponse.getUserPercentile() : 0, percentileValueList, (previousUserActivityStatsResponse != null && previousUserActivityStatsResponse.getUserPercentile() != null) ? previousUserActivityStatsResponse.getUserPercentile() : 0, isUserEligibleForPRAndShareFeature(userContext));
                        customProgressBarWidgetData.setLocked(!(bodyWeight > 0));
                        customProgressBarWidgetData.setLogged(activityStoreAttributeEntry.isAlreadyLogged());
                        customProgressBarWidget.setData(customProgressBarWidgetData);
                        classHighlightsPage.addWidget(customProgressBarWidget);
                    }
                } catch (Exception e) {
                    serviceInterfaces.exceptionReportingService.reportException(e);
                    log.error(e.getMessage());
                }
            } else {
                if (buildGXLoggingWidget != null) {
                    classHighlightsPage.addWidget(buildGXLoggingWidget);
                }
            }
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
            log.error("CFAPI::Activity Logging: Error in building GX Logging Widget {}", e.getMessage(), e);
        }

        if (hasUserEverLogged && highlightCardResponse != null && highlightCardResponse.getWodId() != null && highlightCardResponse.getDate() != null) {
            try {
                EncapsulatorWidget encapsulatorWidget = new EncapsulatorWidget();
                List<ActivityAttributeNamespace> namespaces = new ArrayList<>();
                namespaces.add(ActivityAttributeNamespace.GX);
                namespaces.add(ActivityAttributeNamespace.GYM);
                namespaces.add(ActivityAttributeNamespace.ADHOC);
                List<ActivityStoreAttribute> activityStoreAttributes = encapsulatorWidget.getActivityAttributesForSameExercise(serviceInterfaces, userContext,
                        highlightCardResponse.getDate(), namespaces, highlightCardResponse.getWodId(), null);
                BaseWidgetNonVM cfChartWidgetResponse = getCFChartWidgetFromAttributeList(userContext, serviceInterfaces, activityStoreAttributes, false, null, null);
//
//                    }
                if (cfChartWidgetResponse != null) {
                    cfChartWidgetResponse.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "30"));
                    classHighlightsPage.addWidget(cfChartWidgetResponse);
                }
            } catch (Exception e) {
                log.error("CFAPI::Activity Logging: Error in building CF Chart Widget {}", e.getMessage(),e);
            }
        }
    }

    private void getLoggingWidgetsByExistingLogic(UserContext userContext, String hlClassId, String hlBookingId, ClassHighlightsPage classHighlightsPage, boolean isSuperUserPresent, Long classesAttended, boolean showBanner, boolean isSelfSuperUser, boolean hasUserEverLogged, HighlightCardResponse highlightCardResponse) {
        try {
            GXActivityLoggingWidget gxActivityLoggingWidget = new GXActivityLoggingWidget();
            WidgetContext gxWidgetContext = new WidgetContext();
            HashMap<String, String> gxQueryParams = new HashMap<>();
            gxQueryParams.put("sessionId", hlClassId);
            gxQueryParams.put("bookingId", hlBookingId);
            gxWidgetContext.setQueryParams(gxQueryParams);
            BaseWidgetNonVM buildGXLoggingWidget = gxActivityLoggingWidget.buildView(serviceInterfaces, userContext, gxWidgetContext);

            if (isUserEligibleForProgressionV2(userContext, serviceInterfaces)) {
                List<ActivityStoreAttributeEntry> activityStoreAttributeEntries = gxActivityLoggingWidget.getActivityStoreAttributeEntries();
                ActivityLoggingMeta meta = gxActivityLoggingWidget.getMeta();
                ActivityStoreAttributeEntry activityStoreAttributeEntry = new ActivityStoreAttributeEntry();
                if (activityStoreAttributeEntries != null && !activityStoreAttributeEntries.isEmpty()
                        && activityStoreAttributeEntries.get(0).getExercises() != null) {
                    activityStoreAttributeEntries.get(0).setLogClickAction(getLogClickAction(false, (activityStoreAttributeEntries.get(0).getExercises().get(0).getId() != null) ? activityStoreAttributeEntries.get(0).getExercises().get(0).getId() : null, "HIGHLIGHTS_PAGE"));
                    activityStoreAttributeEntry = activityStoreAttributeEntries.get(0);
                }

                UserActivityStatsResponse userActivityStatsResponse = null;
                UserEntry userEntry = null;
                ExerciseComparisonResponse exerciseComparisonResponse = null;
                List<PercentileValue> percentileValueList = new ArrayList<>();

                UserMetricValue userMetricValue = serviceInterfaces.metricClient.getLatestUserMetricValue(userContext.getUserProfile().getUserId(), 3L);
                double bodyWeight = (!(userMetricValue == null || userMetricValue.getValue() == null)) ? userMetricValue.getValue() : 0;
                double exerciseScore = (activityStoreAttributeEntry.isAlreadyLogged()) ? evaluateExerciseScore(activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getWeight().get(0),
                        activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getReps(), bodyWeight) : 0;

                if (activityStoreAttributeEntry.isAlreadyLogged()) {
                    UserActivityStatsRequest userActivityStatsRequest = UserActivityStatsRequest.builder()
                            .activityId(activityStoreAttributeEntry.getExercises().get(0).getId())
                            .userId(userContext.getUserProfile().getUserId())
                            .activityType(UserActivityType.valueOf("EXERCISE_WEIGHT_LOGGING"))
                            .userActivityValue(Double.valueOf(exerciseScore))
                            .build();
                    userActivityStatsResponse = serviceInterfaces.userActivityService.fetchActivityStatsForUser(userActivityStatsRequest);
                    if (userActivityStatsResponse != null)
                        percentileValueList = userActivityStatsResponse.getPercentiles();
                    userEntry = serviceInterfaces.userServiceClient.getUser(userContext.getUserProfile().getUserId()).get();
                    exerciseComparisonResponse = serviceInterfaces.activityLoggingService.getPersonalBestExercise(userContext.getUserProfile().getUserId(), activityStoreAttributeEntry.getExercises().get(0).getId());
                }

                WeightLiftRankingWidget weightLiftRankingWidget = new WeightLiftRankingWidget();
                WeightLiftRankingWidgetData weightLiftRankingWidgetData = getWeightLiftRankingWidgetData(activityStoreAttributeEntries, activityStoreAttributeEntry, meta, userMetricValue, userActivityStatsResponse, isUserEligibleForPRAndShareFeature(userContext), exerciseComparisonResponse);

                if (userEntry != null && userEntry.getProfilePictureUrl() != null) {
                    weightLiftRankingWidgetData.setProfileImageUrl(userEntry.getProfilePictureUrl());
                }

                UserActivityStatsResponse previousUserActivityStatsResponse = null;
                if (weightLiftRankingWidgetData.getPreviousExScore() != 0) {
                    UserActivityStatsRequest userActivityStatsRequest = UserActivityStatsRequest.builder()
                            .activityId(activityStoreAttributeEntry.getExercises().get(0).getId())
                            .userId(userContext.getUserProfile().getUserId())
                            .activityType(UserActivityType.valueOf("EXERCISE_WEIGHT_LOGGING"))
                            .userActivityValue(Double.valueOf(weightLiftRankingWidgetData.getPreviousExScore()))
                            .build();
                    previousUserActivityStatsResponse = serviceInterfaces.userActivityService.fetchActivityStatsForUser(userActivityStatsRequest);
                }

                // Added redis key for showing new logging banner 2 times
                if (!activityStoreAttributeEntry.isAlreadyLogged() && isUserEligibleForNewHRXBanner(userContext)) {
                    String redisKey = "HRX_FIRST_TIME_LOGGING_BANNER" + userContext.getUserProfile().getUserId();
                    String redisValue = this.cfApiRedisKeyValueStore.get(redisKey);
                    Boolean firstTimeLoggingBannerApplicable = true;
                    if (redisValue == null || redisValue.isEmpty()) {
                        this.cfApiRedisKeyValueStore.set(redisKey, "1");
                    } else {
                        try {
                            int currentValue = Integer.parseInt(redisValue);
                            int incrementedValue = currentValue + 1;
                            if (currentValue > 1)
                                firstTimeLoggingBannerApplicable = false;
                            else
                                this.cfApiRedisKeyValueStore.set(redisKey, String.valueOf(incrementedValue));
                        } catch (Exception e) {
                            serviceInterfaces.exceptionReportingService.reportException(e);
                            log.error(e.getMessage());
                        }
                    }
                    if (firstTimeLoggingBannerApplicable)
                        weightLiftRankingWidgetData.setFirstTimeLoggingBannerImg("/image/progression/first_time_banner_v2.png");
                }

                weightLiftRankingWidgetData.setLogged(activityStoreAttributeEntry.isAlreadyLogged());
                // appside minor issue in opening half card
                if (!activityStoreAttributeEntry.isAlreadyLogged() &&
                        activityStoreAttributeEntry.getExercises() != null
                        && !activityStoreAttributeEntry.getExercises().isEmpty()) {
                    List<Double> weight = new ArrayList<>();
                    weight.add(0.0);
                    activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).setWeight(weight);
                }
                weightLiftRankingWidget.setData(weightLiftRankingWidgetData);
                classHighlightsPage.addWidget(weightLiftRankingWidget);

                if (!activityStoreAttributeEntry.isAlreadyLogged() && !Objects.equals(weightLiftRankingWidgetData.getTitle(), "")) {
                    HighlightsBannerWidget highlightsBannerWidget = new HighlightsBannerWidget();
                    try {
                        List<String> widgetIds = new ArrayList<>();
                        widgetIds.add("d984957e-c638-4420-97fb-266f954ee515");
                        PageContext pageContext = new PageContext();
                        WidgetContext widgetContext = new WidgetContext(pageContext);
                        List<BaseWidget> mergedWidgetList = new ArrayList<>();
                        CompletableFuture<BuildWidgetResponse> bodyResponseFuture = this.serviceInterfaces.getWidgetBuilder().buildWidgets(widgetIds, userContext, widgetContext);
                        mergedWidgetList.addAll(bodyResponseFuture.get().getWidgets());
                        if (!mergedWidgetList.isEmpty()) {
                            BaseBannerWidget bannerWidget = (BaseBannerWidget) mergedWidgetList.get(0);
                            if (bannerWidget != null) {
                                List<BannerItem> bannerData = new ArrayList<>();
                                if (bannerWidget.getData() != null) {
                                    for (BannerItem banner : bannerWidget.getData()) {
                                        if (Objects.equals(banner.getBannerIdentifier(), "highlights_new_user")) {
                                            if (isSuperUserPresent && classesAttended <= 3) {
                                                bannerData.add(banner);
                                                showBanner = true;
                                            }
                                        } else if (Objects.equals(banner.getBannerIdentifier(), "highlights_super_user")) {
                                            if (isSelfSuperUser) {
                                                bannerData.add(banner);
                                                showBanner = true;
                                            }
                                        } else if (Objects.equals(banner.getBannerIdentifier(), "YER banner")) {
                                            bannerData.add(banner);
                                            showBanner = true;
                                        } else {
                                            bannerData.add(banner);
                                            showBanner = true;
                                        }
                                    }
                                }
                                highlightsBannerWidget.setData(bannerData);
                                if (bannerWidget.getFooter() != null)
                                    highlightsBannerWidget.setFooter(bannerWidget.getFooter());
                                if (bannerWidget.getFooterActions() != null)
                                    highlightsBannerWidget.setFooterActions(bannerWidget.getFooterActions());
                                if (bannerWidget.getBannerScrollSpeed() != null)
                                    highlightsBannerWidget.setBannerScrollSpeed(bannerWidget.getBannerScrollSpeed());
                                if (bannerWidget.getMaxNumBanners() != null)
                                    highlightsBannerWidget.setMaxNumBanners(bannerWidget.getMaxNumBanners());
                                if (bannerWidget.getMinNumBanners() != null)
                                    highlightsBannerWidget.setMinNumBanners(bannerWidget.getMinNumBanners());
                                if (bannerWidget.getProductType() != null)
                                    highlightsBannerWidget.setProductType(bannerWidget.getProductType());
                                if (bannerWidget.getTtlExpiryInMs() != null)
                                    highlightsBannerWidget.setTtlExpiryInMs(bannerWidget.getTtlExpiryInMs());
                                if (bannerWidget.getTtlViewCount() != null)
                                    highlightsBannerWidget.setTtlViewCount(bannerWidget.getTtlViewCount());
                                if (bannerWidget.getWidgetMetric() != null)
                                    highlightsBannerWidget.setWidgetMetric(bannerWidget.getWidgetMetric());
                                if (bannerWidget.getDividerType() != null)
                                    highlightsBannerWidget.setDividerType(bannerWidget.getDividerType());
                                if (bannerWidget.getLayoutProps() != null)
                                    highlightsBannerWidget.setLayoutProps(bannerWidget.getLayoutProps());
                                if (bannerWidget.getSpacing() != null)
                                    highlightsBannerWidget.setSpacing(bannerWidget.getSpacing());
                                if (bannerWidget.getTemplateId() != null)
                                    highlightsBannerWidget.setTemplateId(bannerWidget.getTemplateId());
                            }
                        }
                        if (showBanner) {
                            classHighlightsPage.addWidget(highlightsBannerWidget);
                        }
                    } catch (Exception e) {
                        log.error("<Class Highlight> banner error : {}", e.getMessage());
                    }
                }

                try {
                    if (!percentileValueList.isEmpty()) {
                        CustomProgressBarWidget customProgressBarWidget = new CustomProgressBarWidget();
                        CustomProgressBarWidgetData customProgressBarWidgetData = getCustomProgressBarWidgetData(exerciseScore, (activityStoreAttributeEntry.isAlreadyLogged()) ? userActivityStatsResponse.getUserPercentile() : 0, percentileValueList, (previousUserActivityStatsResponse != null && previousUserActivityStatsResponse.getUserPercentile() != null) ? previousUserActivityStatsResponse.getUserPercentile() : 0, isUserEligibleForPRAndShareFeature(userContext));
                        customProgressBarWidgetData.setLocked(!(bodyWeight > 0));
                        customProgressBarWidgetData.setLogged(activityStoreAttributeEntry.isAlreadyLogged());
                        customProgressBarWidget.setData(customProgressBarWidgetData);
                        classHighlightsPage.addWidget(customProgressBarWidget);
                    }
                } catch (Exception e) {
                    serviceInterfaces.exceptionReportingService.reportException(e);
                    log.error(e.getMessage());
                }
            } else {
                if (buildGXLoggingWidget != null) {
                    classHighlightsPage.addWidget(buildGXLoggingWidget);
                }
            }
        } catch (Exception e) {
            serviceInterfaces.exceptionReportingService.reportException(e);
            log.error("CFAPI::Activity Logging: Error in building GX Logging Widget {}", e.getMessage(),e);
        }

        if (hasUserEverLogged && highlightCardResponse != null && highlightCardResponse.getWodId() != null && highlightCardResponse.getDate() != null) {
            try {
                EncapsulatorWidget encapsulatorWidget = new EncapsulatorWidget();
                List<ActivityAttributeNamespace> namespaces = new ArrayList<>();
                namespaces.add(ActivityAttributeNamespace.GX);
                namespaces.add(ActivityAttributeNamespace.GYM);
                namespaces.add(ActivityAttributeNamespace.ADHOC);
                List<ActivityStoreAttribute> activityStoreAttributes = encapsulatorWidget.getActivityAttributesForSameExercise(serviceInterfaces, userContext,
                        highlightCardResponse.getDate(), namespaces, highlightCardResponse.getWodId(), null);
                BaseWidgetNonVM cfChartWidgetResponse = getCFChartWidgetFromAttributeList(userContext, serviceInterfaces, activityStoreAttributes, false, null, null);
//
//                    }
                if (cfChartWidgetResponse != null) {
                    cfChartWidgetResponse.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "30"));
                    classHighlightsPage.addWidget(cfChartWidgetResponse);
                }
            } catch (Exception e) {
                log.error("CFAPI::Activity Logging: Error in building CF Chart Widget {}", e.getMessage(),e);
            }
        }
    }

    @NotNull
    public static CustomProgressBarWidgetData getCustomProgressBarWidgetData(double exerciseScore, double userPercentile, List<PercentileValue> percentileValueList, double previousUserPercentile, boolean userEligibleForPRAndShareFeature) {
        List<String> colors = Arrays.asList("#FF8A2B", "#FFDC18", "#0FE498", "#078C4E", "#00E5FF");
        List<String> levels = Arrays.asList("Beginner", "Novice", "Intermediate", "Advanced", "Elite");
        CustomProgressBarWidgetData customProgressBarWidgetData = new CustomProgressBarWidgetData();
        customProgressBarWidgetData.setTitle("Where Do I Stand?");
        customProgressBarWidgetData.setSubTitle("Exercise Score");
        customProgressBarWidgetData.setLockedAction(ActionUtil.getBodyweightLoggingAction());
        customProgressBarWidgetData.setLockedImageUrl("/image/progression/score_locked_v2.png");
        List<CustomProgressBarWidgetData.GaugeAnnotationsData> gaugeAnnotationsDataList = new ArrayList<>();
        if (percentileValueList != null && !percentileValueList.isEmpty()) {
            percentileValueList.sort(Comparator.comparingDouble(PercentileValue::getPercentile));
        }
        for (int i = 0; i < 6; i++) {
            CustomProgressBarWidgetData.GaugeAnnotationsData gaugeAnnotationsData = new CustomProgressBarWidgetData.GaugeAnnotationsData();
            gaugeAnnotationsData.setAxisValue(20 * i);
            gaugeAnnotationsData.setPositionFactor(1.1);
            int label = i * 20;
            if (label > 0 && label < 100) {
                gaugeAnnotationsData.setLabel(String.valueOf(percentileValueList != null && !percentileValueList.isEmpty() ? percentileValueList.get(label - 1).getValue().intValue() : label));
            } else {
                gaugeAnnotationsData.setLabel(label == 0 ? "0" : String.valueOf(percentileValueList != null && !percentileValueList.isEmpty() ? percentileValueList.get(label - 2).getValue().intValue() : label));
            }
            gaugeAnnotationsDataList.add(gaugeAnnotationsData);
        }
        customProgressBarWidgetData.setGaugeAnnotations(gaugeAnnotationsDataList);
        List<CustomProgressBarWidgetData.GaugeRangeData> gaugeRangeDataList = new ArrayList<>();
        double rangeDif = 0;
        String pointerColor = "#00E5FF";
        int currentLevelIndex = -1, previousLevelIndex = -1;
        for (int i = 0; i < 5; i++) {
            CustomProgressBarWidgetData.GaugeRangeData gaugeRangeData = new CustomProgressBarWidgetData.GaugeRangeData();
            gaugeRangeData.setColor(colors.get(i));
            gaugeRangeData.setTitle(levels.get(i));
            gaugeRangeData.setStartVal(rangeDif);
            gaugeRangeData.setUnderThis(userPercentile >= rangeDif && userPercentile < (rangeDif + 20));
            if (userPercentile >= rangeDif && userPercentile < (rangeDif + 20 + ((i == 4) ? 1 : 0))) {
                pointerColor = colors.get(i);
                currentLevelIndex = i;
            }
            if (previousUserPercentile != 0 && previousUserPercentile >= rangeDif && previousUserPercentile < (rangeDif + 20 + ((i == 4) ? 1 : 0))) {
                previousLevelIndex = i;
            }
            rangeDif += 20;
            gaugeRangeData.setEndVal(rangeDif);
            gaugeRangeDataList.add(gaugeRangeData);
        }
        if ((currentLevelIndex != -1 && previousLevelIndex != -1) && currentLevelIndex > previousLevelIndex && userEligibleForPRAndShareFeature) {
            customProgressBarWidgetData.setLevelCelebrationEnabled(true);
            customProgressBarWidgetData.setCelebrationTitle(getCelebrationTitleText(currentLevelIndex));
            customProgressBarWidgetData.setCelebrationSubtitle(getCelebrationSubtitleText(currentLevelIndex));
        }
        customProgressBarWidgetData.setPointerColor(pointerColor);
        customProgressBarWidgetData.setExerciseScore(exerciseScore);
        customProgressBarWidgetData.setGaugeRanges(gaugeRangeDataList);
        customProgressBarWidgetData.setPointerValue(userPercentile);
        return customProgressBarWidgetData;
    }

    private static String getCelebrationTitleText(int currentLevelIndex) {
        return switch (currentLevelIndex) {
            case 1 -> " Yay! ";
            case 2 -> " Fantastic! ";
            case 3 -> " Look at you go! ";
            case 4 -> " Elite Level! ";
            default -> "";
        };
    }

    private static String getCelebrationSubtitleText(int currentLevelIndex) {
        return switch (currentLevelIndex) {
            case 1 -> "You’ve reached Novice Level! \n Let’s keep it rolling! 🎉";
            case 2 -> "You’re now at Intermediate Level! \n Keep up the great work! 🌟";
            case 3 -> "Advanced Level unlocked! \n Keep shining! 💪";
            case 4 -> "You’re a star! \n Don't stop shining! ✨";
            default -> "";
        };
    }


    @NotNull
    public static WeightLiftRankingWidgetData getWeightLiftRankingWidgetData(List<ActivityStoreAttributeEntry> activityStoreAttributeEntries, ActivityStoreAttributeEntry activityStoreAttributeEntry, ActivityLoggingMeta meta, UserMetricValue userMetricValue, UserActivityStatsResponse userActivityStatsResponse, boolean userEligibleForPRAndShareFeature, ExerciseComparisonResponse exerciseComparisonResponse) {
        WeightLiftRankingWidgetData weightLiftRankingWidgetData = new WeightLiftRankingWidgetData();
        weightLiftRankingWidgetData.setActivityStoreAttributeEntries(activityStoreAttributeEntries);
        weightLiftRankingWidgetData.setTitle(activityStoreAttributeEntry.getTitle() != null ? activityStoreAttributeEntry.getTitle() : "");
        weightLiftRankingWidgetData.setSubtitle(activityStoreAttributeEntry.isAlreadyLogged() ? "My Best Set" : (activityStoreAttributeEntry.getLastLoggedText() != null) ? activityStoreAttributeEntry.getLastLoggedText() : "");
        String result = "";
        if (activityStoreAttributeEntry.isAlreadyLogged() && activityStoreAttributeEntry.getExercises() != null
                && !activityStoreAttributeEntry.getExercises().isEmpty()) {
            List<WorkoutSet> workoutSets = activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets();
            WorkoutSet bestSet = workoutSets.get(0);
            for (WorkoutSet set : workoutSets) {
                if (set.getWeight().get(0) > bestSet.getWeight().get(0) ||
                        (Objects.equals(set.getWeight().get(0), bestSet.getWeight().get(0)) && set.getReps() > bestSet.getReps())) {
                    bestSet = set; // Update the best set
                }
            }
            // Format weight without trailing zeros if unnecessary
            double bestWeight = bestSet.getWeight().get(0);
            String formattedWeight = (bestWeight == (int) bestWeight) ? String.valueOf((int) bestWeight) : String.valueOf(bestWeight);
            result = formattedWeight + " kg x " + bestSet.getReps() + " reps";
        }
        if (meta.getPreviousValue() == null) meta.setPreviousValue(0.0);
        weightLiftRankingWidgetData.setDescription(result);
        weightLiftRankingWidgetData.setEnableShareButton(userEligibleForPRAndShareFeature);
        weightLiftRankingWidgetData.setShareBackgroundImg("/image/progression/hrx_bg_image.png");
        double bodyWeight = (!(userMetricValue == null || userMetricValue.getValue() == null)) ? userMetricValue.getValue() : 0;
        weightLiftRankingWidgetData.setWeightLift((activityStoreAttributeEntry.isAlreadyLogged() && activityStoreAttributeEntry.getExercises() != null
                && !activityStoreAttributeEntry.getExercises().isEmpty()) ? activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getWeight().get(0) : 0);
        weightLiftRankingWidgetData.setPreviousWeightLift(activityStoreAttributeEntry.isAlreadyLogged() ? meta.getPreviousValue() : 0);
        weightLiftRankingWidgetData.setLockedState(!(bodyWeight > 0));
        List<WeightLiftRankingWidgetData.LiftComparisonCard> liftComparisonCards = new ArrayList<>();
        WeightLiftRankingWidgetData.LiftComparisonCard liftComparisonCardTwo = new WeightLiftRankingWidgetData.LiftComparisonCard();
        double previousExScore = (meta.getPreviousValue() > 0 && bodyWeight > 0 && activityStoreAttributeEntry.isAlreadyLogged() && activityStoreAttributeEntry.getExercises() != null
                && !activityStoreAttributeEntry.getExercises().isEmpty()) ? evaluateExerciseScore(meta.getPreviousValue(), meta.getPreviousLoggedSet(), bodyWeight) : 0;
        weightLiftRankingWidgetData.setPreviousExScore(previousExScore);
        double ex_score = (bodyWeight > 0 && activityStoreAttributeEntry.isAlreadyLogged()) ? evaluateExerciseScore(activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getWeight().get(0), activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getReps(), bodyWeight) : 0;
        liftComparisonCardTwo.setTitle("Exercise Score");
        liftComparisonCardTwo.setSubtitle(bodyWeight > 0 ? String.valueOf(ex_score) : "XX");
        liftComparisonCardTwo.setIcon("💪");
        liftComparisonCardTwo.setDescription("");
        liftComparisonCardTwo.setLocked(!(bodyWeight > 0));
        liftComparisonCardTwo.setPreviousExScoreLogged(previousExScore > 0);
        liftComparisonCardTwo.setAction(new Action("curefit://fl_listpage?pageId=Exercise_score&hideTitle=true&disableAnimation=true", ActionType.NAVIGATION));
        liftComparisonCardTwo.setPreviousExScore(String.valueOf(previousExScore > 0 ? Math.round(Math.abs(previousExScore - ex_score) * 100.0) / 100.0 : 0));
        if (previousExScore > 0) {
            liftComparisonCardTwo.setUpOrDown(ex_score > previousExScore);
        }
        WeightLiftRankingWidgetData.LiftComparisonCard liftComparisonCardThree = new WeightLiftRankingWidgetData.LiftComparisonCard();
        liftComparisonCardThree.setTitle("Better than");
        liftComparisonCardThree.setSubtitle(bodyWeight > 0 && userActivityStatsResponse != null ? userActivityStatsResponse.getUserPercentile() + "%" : "X%");
        liftComparisonCardThree.setIcon("👏");
        liftComparisonCardThree.setLocked(!(bodyWeight > 0));
        liftComparisonCardThree.setDescription((userActivityStatsResponse != null) ? " of cult " + userActivityStatsResponse.getSegmentName() + " members" : "");
        if (activityStoreAttributeEntry.isAlreadyLogged() && userEligibleForPRAndShareFeature) {
            if (exerciseComparisonResponse != null) {
                if (exerciseComparisonResponse.getMaxScoreExercise().getIdempotenceKey().equals(activityStoreAttributeEntry.getIdempotenceKey())) {
                    liftComparisonCardTwo.setEnableMaxExScoreAnimation("/image/progression/max_ex_score.png");
                } else if (exerciseComparisonResponse.getMaxWeightExercise().getIdempotenceKey().equals(activityStoreAttributeEntry.getIdempotenceKey())) {
                    weightLiftRankingWidgetData.setEnableMaxWeightAnimation("/image/progression/max_weight_v2.png");
                } else if (exerciseComparisonResponse.getMaxRepsExercise().getIdempotenceKey().equals(activityStoreAttributeEntry.getIdempotenceKey())) {
                    weightLiftRankingWidgetData.setEnableMaxRepAnimation("/image/progression/max_reps_v2.png");
                }
            }
        }
        liftComparisonCards.add(liftComparisonCardTwo);
        liftComparisonCards.add(liftComparisonCardThree);
        weightLiftRankingWidgetData.setLiftComparisonCard(liftComparisonCards);
        if (bodyWeight == 0) {
            weightLiftRankingWidgetData.setEnableBodyweightLogging(true);
            weightLiftRankingWidgetData.setBodyweightLoggingText("Add your weight and unlock");
            weightLiftRankingWidgetData.setBodyweightLoggingAction(ActionUtil.getBodyweightLoggingAction());
        }
        return weightLiftRankingWidgetData;
    }

    @NotNull
    public static WeightLiftRankingWidgetData getWeightLiftRankingWidgetDataByNewLogic(List<ActivityStoreAttributeEntry> activityStoreAttributeEntries, ActivityStoreAttributeEntry activityStoreAttributeEntry, UserMetricValue userMetricValue, UserActivityStatsResponse userActivityStatsResponse, double previousLoggedValue, int previousLoggedSet, ExerciseComparisonResponse exerciseComparisonResponse, boolean userEligibleForPRAndShareFeature) {
        WeightLiftRankingWidgetData weightLiftRankingWidgetData = new WeightLiftRankingWidgetData();
        weightLiftRankingWidgetData.setActivityStoreAttributeEntries(activityStoreAttributeEntries);
        weightLiftRankingWidgetData.setTitle(activityStoreAttributeEntry.getTitle() != null ? activityStoreAttributeEntry.getTitle() : "");
        weightLiftRankingWidgetData.setSubtitle(activityStoreAttributeEntry.isAlreadyLogged() ? "My Best Set" : (activityStoreAttributeEntry.getLastLoggedText() != null) ? activityStoreAttributeEntry.getLastLoggedText() : "");
        String result = "";
        if (activityStoreAttributeEntry.isAlreadyLogged() && activityStoreAttributeEntry.getExercises() != null
                && !activityStoreAttributeEntry.getExercises().isEmpty()) {
            List<WorkoutSet> workoutSets = activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets();
            WorkoutSet bestSet = workoutSets.get(0);
            for (WorkoutSet set : workoutSets) {
                if (set.getWeight().get(0) > bestSet.getWeight().get(0) ||
                        (Objects.equals(set.getWeight().get(0), bestSet.getWeight().get(0)) && set.getReps() > bestSet.getReps())) {
                    bestSet = set; // Update the best set
                }
            }
            // Format weight without trailing zeros if unnecessary
            double bestWeight = bestSet.getWeight().get(0);
            String formattedWeight = (bestWeight == (int) bestWeight) ? String.valueOf((int) bestWeight) : String.valueOf(bestWeight);
            result = formattedWeight + " kg x " + bestSet.getReps() + " reps";
        }
        weightLiftRankingWidgetData.setDescription(result);
        weightLiftRankingWidgetData.setEnableShareButton(userEligibleForPRAndShareFeature);
        weightLiftRankingWidgetData.setShareBackgroundImg("/image/progression/hrx_bg_image.png");
        double bodyWeight = (!(userMetricValue == null || userMetricValue.getValue() == null)) ? userMetricValue.getValue() : 0;
        weightLiftRankingWidgetData.setWeightLift((activityStoreAttributeEntry.isAlreadyLogged() && activityStoreAttributeEntry.getExercises() != null
                && !activityStoreAttributeEntry.getExercises().isEmpty()) ? activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getWeight().get(0) : 0);
        weightLiftRankingWidgetData.setPreviousWeightLift(activityStoreAttributeEntry.isAlreadyLogged() ? previousLoggedValue : 0);
        weightLiftRankingWidgetData.setLockedState(!(bodyWeight > 0));
        List<WeightLiftRankingWidgetData.LiftComparisonCard> liftComparisonCards = new ArrayList<>();
        WeightLiftRankingWidgetData.LiftComparisonCard liftComparisonCardTwo = new WeightLiftRankingWidgetData.LiftComparisonCard();
        double previousExScore = (previousLoggedValue > 0 && bodyWeight > 0 && activityStoreAttributeEntry.isAlreadyLogged() && activityStoreAttributeEntry.getExercises() != null
                && !activityStoreAttributeEntry.getExercises().isEmpty()) ? evaluateExerciseScore(previousLoggedValue, previousLoggedSet, bodyWeight) : 0;
        double ex_score = (bodyWeight > 0 && activityStoreAttributeEntry.isAlreadyLogged()) ? evaluateExerciseScore(activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getWeight().get(0), activityStoreAttributeEntry.getExercises().get(0).getExerciseExecution().getWorkoutSets().get(0).getReps(), bodyWeight) : 0;
        weightLiftRankingWidgetData.setPreviousExScore(previousExScore);
        liftComparisonCardTwo.setTitle("Exercise Score");
        liftComparisonCardTwo.setSubtitle(bodyWeight > 0 ? String.valueOf(ex_score) : "XX");
        liftComparisonCardTwo.setIcon("💪");
        liftComparisonCardTwo.setDescription("");
        liftComparisonCardTwo.setLocked(!(bodyWeight > 0));
        liftComparisonCardTwo.setPreviousExScoreLogged(previousExScore > 0);
        liftComparisonCardTwo.setAction(new Action("curefit://fl_listpage?pageId=Exercise_score&hideTitle=true&disableAnimation=true", ActionType.NAVIGATION));
        liftComparisonCardTwo.setPreviousExScore(String.valueOf(previousExScore > 0 ? Math.round(Math.abs(previousExScore - ex_score) * 100.0) / 100.0 : 0));
        if (previousExScore > 0) {
            liftComparisonCardTwo.setUpOrDown(ex_score > previousExScore);
        }
        WeightLiftRankingWidgetData.LiftComparisonCard liftComparisonCardThree = new WeightLiftRankingWidgetData.LiftComparisonCard();
        if (activityStoreAttributeEntry.isAlreadyLogged() && userEligibleForPRAndShareFeature) {
            if (exerciseComparisonResponse != null) {
                if (exerciseComparisonResponse.getMaxScoreExercise() != null && exerciseComparisonResponse.getMaxScoreExercise().getIdempotenceKey().equals(activityStoreAttributeEntry.getIdempotenceKey())) {
                    liftComparisonCardTwo.setEnableMaxExScoreAnimation("/image/progression/max_ex_score.png");
                } else if (exerciseComparisonResponse.getMaxWeightExercise() != null && exerciseComparisonResponse.getMaxWeightExercise().getIdempotenceKey().equals(activityStoreAttributeEntry.getIdempotenceKey())) {
                    weightLiftRankingWidgetData.setEnableMaxWeightAnimation("/image/progression/max_weight_v2.png");
                } else if (exerciseComparisonResponse.getMaxRepsExercise() != null && exerciseComparisonResponse.getMaxRepsExercise().getIdempotenceKey().equals(activityStoreAttributeEntry.getIdempotenceKey())) {
                    weightLiftRankingWidgetData.setEnableMaxRepAnimation("/image/progression/max_reps_v2.png");
                }
            }
        }
        liftComparisonCards.add(liftComparisonCardTwo);
        if (bodyWeight > 0 && userActivityStatsResponse != null) {
            liftComparisonCardThree.setTitle("Better than");
            liftComparisonCardThree.setSubtitle(userActivityStatsResponse.getUserPercentile() + "%");
            liftComparisonCardThree.setIcon("👏");
            liftComparisonCardThree.setLocked(false);
            liftComparisonCardThree.setDescription(" of cult " + userActivityStatsResponse.getSegmentName() + " members");
            liftComparisonCards.add(liftComparisonCardThree);
        }
        weightLiftRankingWidgetData.setLiftComparisonCard(liftComparisonCards);
        if (bodyWeight == 0) {
            weightLiftRankingWidgetData.setEnableBodyweightLogging(true);
            weightLiftRankingWidgetData.setBodyweightLoggingText("Add your weight and unlock");
            weightLiftRankingWidgetData.setBodyweightLoggingAction(ActionUtil.getBodyweightLoggingAction());
        }
        if (userActivityStatsResponse != null) {
            weightLiftRankingWidgetData.setUserActivityStatsResponse(userActivityStatsResponse);
        }
        return weightLiftRankingWidgetData;
    }

    public static double evaluateExerciseScore(double weightLifted, int reps, double bodyWeight) {
        if (bodyWeight == 0) return 0;
        // Formula: (Weight lifted * (1 + (reps / 30))) * 100 / Body weight
        log.info("Progression:: weightLifted reps bodyWeight {} {} {}", weightLifted, reps, bodyWeight);
        double result = (weightLifted * (1 + (reps / 30.0))) * 100 / bodyWeight;
        return Math.round(result * 100.0) / 100.0;
    }

    private Action getStrengthTrackerClickAction(String sessionId, String bookingId, String exerciseId) {
        String actionUrl = "curefit://gym_highlights_page?exerciseId=" + exerciseId + "&pageFrom=STRENGTH_TRACKER";
        if (sessionId != null) {
            actionUrl = actionUrl + "&sessionId=" + sessionId;
        }
        if (bookingId != null) {
            actionUrl = actionUrl + "&bookingId=" + bookingId;
        }
        Action action = new Action(actionUrl,
                ActionType.NAVIGATION);
        return action;
    }

//    @SneakyThrows({InterruptedException.class, ExecutionException.class})
//    public RecommendationsPage getRecommendations(UserContext userContext, String classId) {
//        RecommendationsPage recommendationsPage = new RecommendationsPage();
//        LatestBookingDetail latestBookingDetail = getLatestBooking(userContext);
//        String hlClassId = classId != null ? classId : latestBookingDetail.getClassId();
//
//        HighlightPeopleResponse highlightPeopleResponse = ragnarServiceClient.getHighlightPeople(userContext.getUserProfile().getUserId(), hlClassId).get();
//
//        HighlightPeopleWidget highlightPeopleWidget = new HighlightPeopleWidget();
//        HighlightPeopleWidget.WidgetData data = new HighlightPeopleWidget.WidgetData();
//        List<HighlightPeopleWidget.WidgetData.PeopleData> peopleResponses = new ArrayList<>();
//
//        if (highlightPeopleResponse != null && highlightPeopleResponse.getData() != null) {
//            for (HighlightPeopleResponse.PeopleResponse people : highlightPeopleResponse.getData()) {
//                HighlightPeopleWidget.WidgetData.PeopleData peopleData = new HighlightPeopleWidget.WidgetData.PeopleData(people.getDisplayName(), people.getCultUserId(),
//                        people.getSocialUserId(), people.getTag(), people.getSecondaryData(), people.getAvatarUrl(), people.getClassesAttended(), people.getSuperUser(), SquadState.NON_MEMBER, "ANY", null, null, null, null, null);
//                peopleResponses.add(peopleData);
//            }
//        }
//        data.setSquadMembers(peopleResponses);
//        data.setData(peopleResponses);
//        highlightPeopleWidget.setData(data);
//
//        try {
//            recommendationsPage.addWidget(highlightPeopleWidget);
//        } catch (Exception e) {
//            String message = String.format("<Class Highlight> Error in creating for class highlight, error :: %s", e.getMessage());
//            log.error(message, e);
//            exceptionReportingService.reportException(message, e);
//        }
//        log.debug("<Class Highlight> view created :: {} ", recommendationsPage);
//        return recommendationsPage;
//    }

    public SquadRequestInfo getSquadRequestInfo(UserContext userContext, String communityMappingId, String inviteeUserId) {
        SquadRequestInfo squadRequestInfo = new SquadRequestInfo();
        CommunityUserMappingState state = CommunityUserMappingState.PENDING;
        Long communityId = null;
        boolean viewLeaderboard = false;
        inviteeUserId = inviteeUserId.isEmpty() || inviteeUserId.equals("DUMMY") ? userContext.getUserProfile().getUserId() : inviteeUserId;

        List<String> images = new ArrayList<>();
        images.add("https://cdn-media.cure.fit/image/community/squads/squad_info_1.png");
        images.add("https://cdn-media.cure.fit/image/community/squads/squad_info_2.png");
        images.add("https://cdn-media.cure.fit/image/community/squads/squad_info_3.png");

        List<SquadInvitationWidget.Benefits> squadBenefits = new ArrayList<>();
        squadBenefits.add(new SquadInvitationWidget.Benefits("FLAME", "Track activity "));
        squadBenefits.add(new SquadInvitationWidget.Benefits("GROUP", "Workout together"));
        squadBenefits.add(new SquadInvitationWidget.Benefits("PROGRESS", "View progress"));

        Action postClickAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .url("curefit://squads_feed")
                .title("VIEW LEADERBOARD")
                .build();

        Action manageSquadAction = Action.builder()
                .actionType(ActionType.NAVIGATION)
                .title("GOT IT")
                .url("curefit://leaguewalltabbedpage?productType=FITNESS&selectedTab=ALL_LEAGUES")
                .build();

        try {
            CommunityUserMappingEntry communityUserMappingEntry = null;
            if (communityMappingId == null || communityMappingId.equals("DUMMY")) {
                List<CommunityUserMappingEntry> communityUserMappingEntries = serviceInterfaces.socialService.getSystemSquadInvitesForUser(inviteeUserId);
                log.info("getSquadRequestInfo: fetching communityUserMappingEntry for inviteeUserId: {} {}", inviteeUserId, communityUserMappingEntries);
                communityUserMappingEntry = communityUserMappingEntries.stream().filter(communityUserMappingEntry1 ->
                        communityUserMappingEntry1.getCommunity() != null && communityUserMappingEntry1.getCommunity().getCreatorNode().getEntityType() == EntityType.SYSTEM
                                && communityUserMappingEntry1.getState() == CommunityUserMappingState.PENDING
                ).findFirst().orElse(null);
                log.info("getSquadRequestInfo: fetching communityUserMappingEntry: {}", communityUserMappingEntry);
                if (communityUserMappingEntry == null) {
                    communityUserMappingEntry = communityUserMappingEntries.stream().filter(communityUserMappingEntry1 ->
                            communityUserMappingEntry1.getCommunity() != null && communityUserMappingEntry1.getCommunity().getCreatorNode().getEntityType() == EntityType.SYSTEM
                    ).findFirst().orElse(null);
                }
            } else {
                communityUserMappingEntry = serviceInterfaces.socialService.getCommunityUserMappingEntry(communityMappingId);
            }
            if (communityUserMappingEntry != null) {
                communityMappingId = communityUserMappingEntry.getId().toString();
                state = communityUserMappingEntry.getState();
                communityId = communityUserMappingEntry.getCommunityId();
            }
        } catch (Exception e) {
            String message = String.format("Error in fetching communityMappingEntry, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }

        if (communityId != null) {
            try {
                List<CommunityUserMappingEntry> communityMembers = serviceInterfaces.socialService.getCommunityMembers(communityId, 0, 10);
                if (communityMembers != null && !communityMembers.isEmpty() && communityMembers.size() >= 2) {
                    viewLeaderboard = true;
                }
            } catch (Exception e) {
                String message = String.format("Error in fetching communityMembers info, error :: %s", e.getMessage());
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }
        }

        squadRequestInfo.setImages(images);
        squadRequestInfo.setCommunityMappingId(communityMappingId);
        squadRequestInfo.setInviteeUserId(inviteeUserId);
        squadRequestInfo.setBenefitsTitle("Now you can");
        squadRequestInfo.setBenefits(squadBenefits);
        if (viewLeaderboard) {
            squadRequestInfo.setPostClickAction(postClickAction);
        } else {
            squadRequestInfo.setPostClickAction(manageSquadAction);
        }
        squadRequestInfo.setConfirmationTitle("Request accepted");
        squadRequestInfo.setConfirmationSubtitle("Some squad members have private profiles. You'll see them once they accept.");
        if (!Objects.equals(state, CommunityUserMappingState.PENDING)) squadRequestInfo.setConfirmed(true);
        log.info("getSquadRequestInfo: squadRequestInfo: {}", squadRequestInfo);

        return squadRequestInfo;
    }

    public CFUserListSection getUserCommunitiesSection(UserContext userContext, List<CommunityEntry> communitiesResponse) {
        List<CommunityUserWidget> userCommunityList = new ArrayList<>();
        List<String> communityCreatorsList = new ArrayList<>();

        communitiesResponse.sort((item1, item2) -> {
            if (Objects.equals(item1.getCreatorNode().getEntityId(), userContext.getUserProfile().getUserId()))
                return -1;
            else if (Objects.equals(item2.getCreatorNode().getEntityId(), userContext.getUserProfile().getUserId()))
                return 1;
            return 0;
        });

        for (CommunityEntry communityEntry : communitiesResponse) {
            communityCreatorsList.add(communityEntry.getCreatorNode().getEntityId());
        }
        Map<String, UserEntry> userEntryMap = getUserEntryMap(communityCreatorsList);
        for (CommunityEntry communityEntry : communitiesResponse) {
            if (communityEntry == null) continue;

            CommunityUserWidget communityItem = new CommunityUserWidget();
            String creatorId = null;
            String squadMemberId = null;
            UserEntry squadMemberUserEntry;
            String squadMemberProfileUrl = "image/community/default_male_thumbnail.png";
            String creatorProfileUrl = "image/community/default_male_thumbnail.png";

            creatorId = communityEntry.getCreatorNode().getEntityId();

            try {
                List<CommunityUserMappingEntry> communityDetailResponse = this.socialServiceClient.getCommunityMembers(communityEntry.getId(), 0, 50);
                if (communityDetailResponse != null && !communityDetailResponse.isEmpty()) {
                    communityItem.setSubTitle(communityDetailResponse.size() + (communityDetailResponse.size() < 2 ? " member" : " members"));
                    for (CommunityUserMappingEntry communityMember : communityDetailResponse) {
                        if (communityMember != null && communityMember.getUserId() != null && !Objects.equals(creatorId, communityMember.getUserId())) {
                            squadMemberId = communityMember.getUserId();
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                String message = String.format("Error in fetching community data, error :: %s", e);
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }

            Action navAction = Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .url("curefit://edit_squad?communityId=" + communityEntry.getId().toString())
                    .build();
            UserEntry creatorUserEntry = userEntryMap.get(creatorId);
            if (creatorUserEntry != null &&
                    creatorUserEntry.getProfilePictureUrl() != null)
                creatorProfileUrl = creatorUserEntry.getProfilePictureUrl();
            if (squadMemberId != null) {
                try {
                    CompletableFuture<UserEntry> squadMemberUserEntryPromise = getUserEntryCF(squadMemberId);
                    if (squadMemberUserEntryPromise != null) {
                        squadMemberUserEntry = squadMemberUserEntryPromise.get();
                        if (squadMemberUserEntry != null && squadMemberUserEntry.getProfilePictureUrl() != null) {
                            squadMemberProfileUrl = squadMemberUserEntry.getProfilePictureUrl();
                        }
                    }
                } catch (Exception e) {
                    String message = String.format("Error in fetching user entry data, error :: %s", e);
                    log.error(message, e);
                    exceptionReportingService.reportException(message, e);
                }
            }

            communityItem.setUserId(communityEntry.getCreatorNode().getEntityId());
            communityItem.setNavAction(navAction);
            communityItem.setAvatarRadius(22.0);
            communityItem.setTitle(communityEntry.getName());
            communityItem.setAvatarUrl(List.of(squadMemberProfileUrl, creatorProfileUrl));
            communityItem.setRightActionWidget(getCommunityListActionWidget(userContext, serviceInterfaces, communityEntry.getId().toString(), creatorId));
            Map<String, String> meta = Map.of(
                    "communityId", communityEntry.getId().toString()
            );
            communityItem.setMeta(meta);
            userCommunityList.add(communityItem);
        }
        CFUserListSection communitiesSection = new CFUserListSection();
        communitiesSection.setTitle("All Squads");
        communitiesSection.setWidgets(userCommunityList);
        communitiesSection.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("30", "0"));
        return communitiesSection;
    }

    public CFUserListSection getSelfUserCommunitiesSection(UserContext userContext, List<CommunityEntry> communitiesResponse, String inviteeUserId) {
        List<CommunityUserWidget> userCommunityList = new ArrayList<>();
        String userId = userContext.getUserProfile().getUserId();
        CompletableFuture<UserEntry> userEntryCF = getUserEntryCF(userId);
        UserEntry userEntry = null;
        try {
            userEntry = userEntryCF.get();
        } catch (Exception e) {
            String message = String.format("Error in fetching user data, error :: %s", e);
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }


        List<CommunityEntry> filteredCommunities = communitiesResponse.stream()
                .filter(item -> Objects.equals(
                        item.getCreatorNode().getEntityId(),
                        userId
                )).collect(Collectors.toList());

        for (CommunityEntry communityEntry : filteredCommunities) {
            if (communityEntry == null || communityEntry.getId() == null) continue;

            CommunityUserWidget communityItem = new CommunityUserWidget();
            String squadMemberId = null;
            UserEntry squadMemberUserEntry;
            String squadMemberProfileUrl = "image/community/default_male_thumbnail.png";
            String creatorProfileUrl = "image/community/default_male_thumbnail.png";

            try {
                List<CommunityUserMappingEntry> communityDetailResponse = this.socialServiceClient.getCommunityMembers(communityEntry.getId(), 0, 50);
                if (communityDetailResponse != null && !communityDetailResponse.isEmpty()) {
                    communityItem.setSubTitle(communityDetailResponse.size() + (communityDetailResponse.size() < 2 ? " member" : " members"));
                    for (CommunityUserMappingEntry communityMember : communityDetailResponse) {
                        if (communityMember != null && communityMember.getUserId() != null && !Objects.equals(userId, communityMember.getUserId())) {
                            squadMemberId = communityMember.getUserId();
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                String message = String.format("Error in fetching community data, error :: %s", e);
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }

            if (userEntry != null &&
                    userEntry.getProfilePictureUrl() != null)
                creatorProfileUrl = userEntry.getProfilePictureUrl();
            if (squadMemberId != null) {
                try {
                    CompletableFuture<UserEntry> squadMemberUserEntryPromise = getUserEntryCF(squadMemberId);
                    if (squadMemberUserEntryPromise != null) {
                        squadMemberUserEntry = squadMemberUserEntryPromise.get();
                        if (squadMemberUserEntry != null && squadMemberUserEntry.getProfilePictureUrl() != null) {
                            squadMemberProfileUrl = squadMemberUserEntry.getProfilePictureUrl();
                        }
                    }
                } catch (Exception e) {
                    String message = String.format("Error in fetching user entry data, error :: %s", e);
                    log.error(message, e);
                    exceptionReportingService.reportException(message, e);
                }
            }


            Action navAction = Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .url("curefit://contact_sync?communityId=" + communityEntry.getId().toString() + "&communityName=" + communityEntry.getName())
                    .build();

            if (inviteeUserId != null) {
                Map<String, String> meta = new HashMap<>();
                meta.put("inviteeUserId", inviteeUserId);
                meta.put("communityId", communityEntry.getId().toString());
                navAction = Action.builder()
                        .actionType(ActionType.INVITE_USER_TO_SQUAD_ACTION)
                        .meta(meta)
                        .build();
            }

            SecondaryButtonWidget secondaryButtonWidget = new SecondaryButtonWidget();
            secondaryButtonWidget.setOnPressed(navAction);
            secondaryButtonWidget.setTitle("ADD");
            secondaryButtonWidget.setVerticalPadding(5.0);
            secondaryButtonWidget.setHeight(null);

            communityItem.setUserId(userId);
            communityItem.setNavAction(null);
            communityItem.setAvatarRadius(22.0);
            communityItem.setTitle(communityEntry.getName());
            communityItem.setAvatarUrl(List.of(squadMemberProfileUrl, creatorProfileUrl));
            communityItem.setRightActionWidget(secondaryButtonWidget);
            Map<String, String> meta = Map.of(
                    "communityId", communityEntry.getId().toString()
            );
            communityItem.setMeta(meta);
            userCommunityList.add(communityItem);
        }
        CFUserListSection communitiesSection = new CFUserListSection();
        communitiesSection.setTitle(!userCommunityList.isEmpty() ? "Add to my existing squads" : "");
        communitiesSection.setWidgets(userCommunityList);
        communitiesSection.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("50", "0"));
        return communitiesSection;
    }

    public CFUserListSection getPendingInvitesSection(UserContext userContext) {
        String userId = userContext.getUserProfile().getUserId();
        List<CommunityUserWidget> pendingInvites = new ArrayList<>();

        try {
            List<CommunityUserMappingEntry> invitesResponse = socialServiceClient.getPendingInvitesForUser(userId, true, 0, 50);
            if (invitesResponse == null || invitesResponse.isEmpty()) return null;
            List<String> inviterUserIds = new ArrayList<>();
            for (CommunityUserMappingEntry invite : invitesResponse) {
                if (invite.getCommunity() != null
                        && invite.getCommunity().getCreatorNode() != null
                        && invite.getCommunity().getCreatorNode().getEntityId() != null) {
                    inviterUserIds.add(invite.getCommunity().getCreatorNode().getEntityId());
                }
            }
            Map<String, UserEntry> userEntryMap = getUserEntryMap(inviterUserIds);
            for (CommunityUserMappingEntry invite : invitesResponse) {
                if (invite == null) continue;
                String inviterUserId = invite.getCommunity().getCreatorNode().getEntityId();
                CommunityEntry communityEntry = invite.getCommunity();
                if (communityEntry == null) continue;
                CommunityUserWidget sentInviteWidget = new CommunityUserWidget();
                UserEntry userEntry = userEntryMap.get(inviterUserId);
                sentInviteWidget.setUserId(inviterUserId);
                sentInviteWidget.setAvatarRadius(22.0);
                String inviterName = CommunityUtil.getSquadInvitationTitle(userEntry, communityEntry);
                if (AppUtil.v93check(userContext)) {
                    sentInviteWidget.setTitle(inviterName != null ? inviterName : "Join " + communityEntry.getName());
                    sentInviteWidget.setTitleSpan2(inviterName != null ? " has invited you to join " : "");
                    sentInviteWidget.setTitleSpan3(inviterName != null ? communityEntry.getName() : "");
                } else {
                    sentInviteWidget.setTitle("Join " + communityEntry.getName());
                }
                if (userEntry != null) {
                    sentInviteWidget.setAvatarUrl(List.of(userEntry.getProfilePictureUrl() != null ? userEntry.getProfilePictureUrl() : "/image/community/default_male_thumbnail.png"));
                }
                sentInviteWidget.setRightActionWidget(getSquadRequestAction(inviterUserId, invite.getCommunityId(), invite.getId().toString(), userContext, serviceInterfaces));
                pendingInvites.add(sentInviteWidget);
            }
        } catch (Exception e) {
            String message = String.format("Error in fetching pending invites, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        CFUserListSection sentInvitesSection = new CFUserListSection();
        sentInvitesSection.setWidgets(pendingInvites);
        sentInvitesSection.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("0", "0"));
        return sentInvitesSection;
    }

    public CFUserListSection getSentInvitesSection(UserContext userContext, Long communityId) {
        String userId = userContext.getUserProfile().getUserId();
        List<Long> communityIds = new ArrayList<>();
        List<String> inviterUserIds = new ArrayList<>();
        List<PendingCommunityInviteResponse> sentInvitesResponseList = new ArrayList<>();

        if (communityId != null) {
            communityIds.add(communityId);
        } else {
            communityIds = CommunityUtil.getSelfCommunityIdsFromUserId(userId, this.socialServiceClient);
        }
        List<CommunityUserWidget> sentInvites = new ArrayList<>();
        CompletableFuture<UserEntry> selfUserEntryMap = getUserEntryCF(userContext.getUserProfile().getUserId());
        UserEntry selfUserEntry = null;
        try {
            selfUserEntry = selfUserEntryMap.get();
        } catch (Exception e) {
            log.error("Error while fetching user details {}", e);
        }
        for (Long id : communityIds) {
            try {
                List<PendingCommunityInviteResponse> sentInvitesResponse = socialServiceClient.getPendingCommunityMembers(id, userId, 0, 10);
                if (sentInvitesResponse == null || sentInvitesResponse.isEmpty()) continue;

                for (PendingCommunityInviteResponse invite : sentInvitesResponse) {
                    if (invite.getUserId() != null) inviterUserIds.add(invite.getUserId());
                    if (invite.getCommunityId() != null) sentInvitesResponseList.add(invite);
                }
            } catch (Exception e) {
                String message = String.format("Error in fetching sent invites, error :: %s", e.getMessage());
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }
        }

        CommunityUtil.sortSentInvites(sentInvitesResponseList);
        Map<String, UserEntry> userEntryMap = getUserEntryMap(inviterUserIds);
        for (PendingCommunityInviteResponse sentInvite : sentInvitesResponseList) {
            if (sentInvite == null || sentInvite.getCommunityId() == null) continue;

            CommunityUserWidget sentInviteWidget = new CommunityUserWidget();
            CommunityEntry communityEntry = null;
            try {
                communityEntry = socialServiceClient.getCommunityInfo(Long.parseLong(sentInvite.getCommunityId()));
            } catch (Exception e) {
                String message = String.format("Error in fetching community info, error :: %s", e.getMessage());
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }

            if (communityEntry == null) continue;

            if (sentInvite.getUserId() != null) {
                UserEntry userEntry = userEntryMap.get(sentInvite.getUserId());
                sentInviteWidget.setUserId(sentInvite.getUserId());
                sentInviteWidget.setAvatarRadius(22.0);
                if (userEntry != null) {
                    String name = "Cult user";
                    if (userEntry.getFirstName() != null) {
                        name = userEntry.getFirstName() + ((userEntry.getLastName() != null && !"".equals(userEntry.getLastName()) && !"null".equals(userEntry.getLastName())) ? " " + userEntry.getLastName() : "");
                    }
                    sentInviteWidget.setTitle(name);
                    sentInviteWidget.setAvatarUrl(List.of(userEntry.getProfilePictureUrl() != null ? userEntry.getProfilePictureUrl() : "/image/community/default_male_thumbnail.png"));
                }
                sentInviteWidget.setRightActionWidget(getCancelInviteActionButton(sentInvite,
                        sentInvite.getUserId(), selfUserEntry, userEntry));
            } else if (sentInvite.getName() != null && sentInvite.getPhoneNumber() != null) {
                sentInviteWidget.setTitle(sentInvite.getName());
                sentInviteWidget.setAvatarRadius(22.0);
                sentInviteWidget.setAvatarUrl(List.of("/image/community/default_male_thumbnail.png"));
                sentInviteWidget.setRightActionWidget(getCancelInviteActionButton(sentInvite,
                        sentInvite.getUserId(), selfUserEntry, null));
            }
            if (communityEntry.getName() != null) sentInviteWidget.setSubTitle(communityEntry.getName());
            sentInvites.add(sentInviteWidget);
        }
        CFUserListSection sentInvitesSection = new CFUserListSection();
        sentInvitesSection.setTitle("Invites you sent");
        sentInvitesSection.setWidgets(sentInvites);
        sentInvitesSection.setLayoutProps(AppUtil.getDefaultLayoutPropsForWidget("30", "0"));
        return sentInvitesSection;
    }

    public EditSquadWidget getEditSquadScreen(UserContext userContext, Long communityId) throws Exception {
        EditSquadWidget editSquadWidget = new EditSquadWidget();
        List<BaseWidgetNonVM> widgets = new ArrayList<>();
        String communityName = "Squad";
        String creatorId = null;
        boolean selfSquad = false;
        List<CommunityUserMappingEntry> communityDetailResponse = new ArrayList<>();
        try {
            communityDetailResponse = this.socialServiceClient.getCommunityMembers(communityId, 0, 50);
        } catch (Exception e) {
            String message = String.format("Error in fetching squad members, error :: %s", e.getMessage());
            log.error(message, e);
            serviceInterfaces.exceptionReportingService.reportException(message, e);
        }
        try {
            CommunityEntry communtiyEntry = socialServiceClient.getCommunityInfo(communityId);
            if (communtiyEntry != null) {
                if (communtiyEntry.getCreatorNode() != null && communtiyEntry.getCreatorNode().getEntityId() != null) {
                    creatorId = communtiyEntry.getCreatorNode().getEntityId();
                }
                if (communtiyEntry.getName() != null) communityName = communtiyEntry.getName();
                if (creatorId != null && Objects.equals(creatorId, userContext.getUserProfile().getUserId())) {
                    selfSquad = true;
                }
            }
        } catch (Exception e) {
            String message = String.format("Error in fetching squad details, error :: %s", e.getMessage());
            log.error(message, e);
            serviceInterfaces.exceptionReportingService.reportException(message, e);
        }
        CFUserListSection squadMembersSection = CommunityUtil.getCommunityMembersSection(userContext, communityDetailResponse, communityId, creatorId, serviceInterfaces);
        if (squadMembersSection != null) widgets.add(squadMembersSection);
        if (communityDetailResponse != null && !communityDetailResponse.isEmpty() &&
                communityDetailResponse.get(0).getCommunity() != null &&
                communityDetailResponse.get(0).getCommunity().getName() != null) {
            communityName = communityDetailResponse.get(0).getCommunity().getName();
        }
        if (selfSquad) {
            CFUserListSection sentInvitesSection = this.getSentInvitesSection(userContext, communityId);
            if (sentInvitesSection != null
                    && sentInvitesSection.getWidgets() != null
                    && !sentInvitesSection.getWidgets().isEmpty()) widgets.add(sentInvitesSection);
        }
        String title = communityName;
        if (communityDetailResponse != null && !communityDetailResponse.isEmpty()) {
            title = title + " (" + communityDetailResponse.size() + ")";
        }
        editSquadWidget.setWidgets(widgets);
        editSquadWidget.setTitle(title);
        if (selfSquad) {
            Action primaryAction = Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .url("curefit://contact_sync?communityId=" + communityId.toString() + "&communityName=" + communityName)
                    .title("INVITE")
                    .build();
            editSquadWidget.setPageActions(List.of(primaryAction));

            HashMap<String, Object> meta = new HashMap<>();
            List<BaseWidget> headerWidgets = new ArrayList<>();
            WidgetContext widgetContext = new WidgetContext();

            //BUILD INVITE WIDGET
            widgetContext.getQueryParams().put("title", "Invite Squad members");
            widgetContext.getQueryParams().put("icon","buddy");
            CustomBottomSheetButton customBottomSheetButton = new CustomBottomSheetButton();
            customBottomSheetButton.setWidgetType(WidgetType.CUSTOM_BOTTOM_SHEET_BUTTON);
            customBottomSheetButton.setAction(Action.builder()
                    .actionType(ActionType.NAVIGATION)
                    .url("curefit://contact_sync?communityId=" + communityId + "&communityName=" + communityName)
                    .build());
            List<BaseWidget> response = customBottomSheetButton.buildView(serviceInterfaces, userContext, widgetContext);
            if(response != null && response.getFirst() != null){
                headerWidgets.add(response.getFirst());
            }

            //BUILD DELETE SQUAD WIDGET
            HashMap<String, Object> actionMeta = new HashMap<>();
            actionMeta.put("communityId",communityId);
            widgetContext.getQueryParams().put("title", "Delete Squad");
            widgetContext.getQueryParams().put("icon","delete-bin");
            customBottomSheetButton = new CustomBottomSheetButton();
            customBottomSheetButton.setWidgetType(WidgetType.CUSTOM_BOTTOM_SHEET_BUTTON);

            //GENERATE CONFIRMATION POPUP
            CFGenericAlertDialog alertDialog = new CFGenericAlertDialog();
            alertDialog.setTitle(CFTextData.builder().text("Are you sure you want to delete the squad").typeScale(UiUtils.TextTypeScales.P8).maxLine("4").build());
            alertDialog.setShowDismissIcon(false);
            List<Action> alertDialogActions = new ArrayList<>();
            alertDialogActions.add(
                    Action.builder()
                            .actionType(ActionType.DELETE_SQUAD)
                            .meta(actionMeta)
                            .title("YES")
                            .build()
            );
            alertDialogActions.add(
                    Action.builder()
                            .actionType(ActionType.POP_NAVIGATION)
                            .title("NO")
                            .build()
            );
            alertDialog.setActions(alertDialogActions);
            HashMap<String, CFGenericAlertDialog> popupMeta = new HashMap<>();
            popupMeta.put("popupContent", alertDialog);
            customBottomSheetButton.setAction(Action.builder().actionType(ActionType.SHOW_ALERT_POPUP).meta(popupMeta).build());
            response = customBottomSheetButton.buildView(serviceInterfaces, userContext, widgetContext);
            if(response != null && response.getFirst() != null){
                headerWidgets.add(response.getFirst());
            }

            meta.put("widgets",headerWidgets);
            meta.put("showTopNotch", true);
            Action headerActions = Action.builder()
                    .actionType(ActionType.SHOW_CUSTOM_BOTTOM_SHEET)
                    .meta(meta)
                    .build();
            editSquadWidget.setHeaderAction(headerActions);
        }
        return editSquadWidget;
    }

    public GenericOperationResponse deleteSquad(UserContext userContext, Long communityId) throws BadRequestException {
        boolean selfSquad = false;
        String creatorId = null;

        try {
            CommunityEntry communityEntry = socialServiceClient.getCommunityInfo(communityId);
            if (communityEntry != null) {
                if (communityEntry.getCreatorNode() != null && communityEntry.getCreatorNode().getEntityId() != null) {
                    creatorId = communityEntry.getCreatorNode().getEntityId();
                }
                if (creatorId != null && Objects.equals(creatorId, userContext.getUserProfile().getUserId())) {
                    selfSquad = true;
                }
            }
            if(selfSquad){
                return this.socialServiceClient.deleteCommunity(String.format(String.valueOf(communityId)));
            }else{
                throw new BadRequestException("Unable to delete squad");

            }
        } catch (Exception e) {
            String message = String.format("Error in deleting squad, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
            throw new BadRequestException("Something went wrong!");
        }
    }

    public SquadInvite createSquadWithEligibility(UserContext userContext, String squadName) {
        String userId = userContext.getUserProfile().getUserId();
        int maxSquadCount = 10;
        CommunitiesResponse communitiesResponse = this.socialServiceClient.getUserCommunities(userId, CommunityType.LEAGUE, false, 0, 50);
        List<CommunityEntry> userCommunities = new ArrayList<>();
        if (communitiesResponse != null && communitiesResponse.getCommunities() != null) {
            for (CommunityEntry communityEntry : communitiesResponse.getCommunities()) {
                if (userCommunities.size() >= maxSquadCount) {
                    Action toastAction = Action.builder()
                            .title("Toast message")
                            .actionType(ActionType.SHOW_TOAST_MESSAGE)
                            .meta(ToastMessageMeta.builder().message("Reached max number of squads").build())
                            .build();
                    return SquadInvite.builder().success(false).action(toastAction).build();
                }
                if (communityEntry.getCreatorNode() == null
                        || communityEntry.getCreatorNode().getEntityId() == null
                        || !Objects.equals(communityEntry.getCreatorNode().getEntityId(), userId)) {
                    continue;
                }
                if (Objects.equals(communityEntry.getName().toUpperCase(), squadName.toUpperCase())) {
                    Action toastAction = Action.builder()
                            .title("Toast message")
                            .actionType(ActionType.SHOW_TOAST_MESSAGE)
                            .meta(ToastMessageMeta.builder().message("Squad name already exists").build())
                            .build();
                    return SquadInvite.builder().success(false).action(toastAction).build();
                }
                userCommunities.add(communityEntry);
            }
        }

        CommunityEntry communityEntry = createSquad(userContext, squadName);
        String toastMessage = "Something went wrong!";
        boolean success = false;
        String communityId = null;

        if (communityEntry != null) {
            success = true;
            toastMessage = "Squad created";
            communityId = communityEntry.getId().toString();
        }
        Action toastAction = Action.builder()
                .title("Toast message")
                .actionType(ActionType.SHOW_TOAST_MESSAGE)
                .meta(ToastMessageMeta.builder().message(toastMessage).build())
                .build();

        return SquadInvite.builder().success(success).action(toastAction).communityId(communityId).build();
    }

    public CommunityEntry createSquad(UserContext userContext, String squadName) {
        String userId = userContext.getUserProfile().getUserId();
        UserEntry user = null;
        CommunityEntry community = null;
        String communityName = squadName;
        if (squadName == null || squadName.isEmpty()) {
            try {
                user = userServiceClient.getUser(userId).get();
            } catch (Exception e) {
                String message = String.format("Error in fetching user, error :: %s", e.getMessage());
                log.error(message, e);
                exceptionReportingService.reportException(message, e);
            }
            communityName = user != null ? user.getFirstName() + "'s Squad" : null;
        }
        NodeEntry creatorNode = new NodeEntry();
        creatorNode.setEntityId(userId);
        creatorNode.setEntityType(EntityType.USER);
        CommunityEntry communityEntry = new CommunityEntry();
        communityEntry.setName(communityName);
        communityEntry.setDescription(communityName);
        communityEntry.setType(CommunityType.LEAGUE);
        communityEntry.setTenant(com.curefit.socialservice.enums.Tenant.LIVE);
        communityEntry.setCreatorNode(creatorNode);
        try {
            community = socialServiceClient.createCommunity(communityEntry);
        } catch (Exception e) {
            String message = String.format("Error in creating squad, error :: %s", e.getMessage());
            log.error(message, e);
            exceptionReportingService.reportException(message, e);
        }
        return community;
    }

    public SquadInvitesBulk inviteToSquadBulk(UserContext userContext, SquadBulkRequest inviteRequest) {
        String userId = userContext.getUserProfile().getUserId();
        Long communityId = null;
        boolean successMetric = false;
        if (inviteRequest.getCommunityId() != null) communityId = Long.parseLong(inviteRequest.getCommunityId());
        if (inviteRequest.getSquadName() == null)
            communityId = getCommunityIdFromUserId(userId, this.socialServiceClient);

        if (communityId == null) {
            CommunityEntry communityEntry = createSquad(userContext, inviteRequest.getSquadName());
            if (communityEntry != null) {
                communityId = communityEntry.getId();
            }
        }
        if (communityId == null) {
            String toastMessage = "Can not create more than 1 squad";

            Action toastAction = Action.builder()
                    .title("Toast message")
                    .actionType(ActionType.SHOW_TOAST_MESSAGE)
                    .meta(ToastMessageMeta.builder().message(toastMessage).build())
                    .build();

            return SquadInvitesBulk.builder()
                    .success(false)
                    .action(toastAction)
                    .build();
        }

        List<SquadInvite> squadInvites = new ArrayList<>();
        boolean someInvitesFailed = false;
        boolean someInvitesSucceed = false;
        if (inviteRequest.getInviteRequests() != null) {
            for (CommunityUserInviteRequest request : inviteRequest.getInviteRequests()) {
                if (request == null) continue;
                boolean success = false;
                String toastMessageForIndividualInvite = "";
                if (request.getCountryCallingCode() != null && !Objects.equals(request.getCountryCallingCode(), "91")) {
                    toastMessageForIndividualInvite = "Non-Indian members not supported as of now";
                } else {
                    try {
                        CommunityUserInviteRequest communityUserInviteRequest = new CommunityUserInviteRequest(
                                communityId,
                                "+" + request.getCountryCallingCode(),
                                request.getPhoneNumber(),
                                request.getName(),
                                request.getUserId());

                        GenericOperationResponse inviteResponse = this.socialServiceClient.inviteUserToCommunity(communityUserInviteRequest, userId);
                        toastMessageForIndividualInvite = inviteResponse.isSuccess() ? "Invite sent" : "Failed to send invite";
                        if (!inviteResponse.isSuccess()) {
                            someInvitesFailed = true;
                        } else {
                            someInvitesSucceed = true;
                        }
                        success = inviteResponse.isSuccess();
                    } catch (Exception e) {
                        log.error("one invite local 1 : {}", e);
                        log.error("one invite local 2 : {}", e.getMessage());
                        toastMessageForIndividualInvite = "Failed to send invite";
                        String message = String.format("Invite to squad failed for userId:%s, communityId: %s", userId, communityId);
                        log.error(message + " :" + e);
                    }
                }

                Action toastActionForIndividualInvite = Action.builder()
                        .title("Toast message")
                        .actionType(ActionType.SHOW_TOAST_MESSAGE)
                        .meta(ToastMessageMeta.builder().message(toastMessageForIndividualInvite).build())
                        .build();

                squadInvites.add(SquadInvite.builder()
                        .name(request.getName())
                        .userId(request.getUserId())
                        .success(success)
                        .action(toastActionForIndividualInvite)
                        .build());
            }
        }

        Action toastAction;
        String toastMessage = "";
        if (someInvitesSucceed) {
            successMetric = true;
            if (someInvitesFailed) {
                toastMessage = "One or more invites failed";
            } else {
                toastMessage = "Successfully invited";
            }
        }
        if (Objects.equals(squadInvites.size(), 1)) {
            toastAction = squadInvites.get(0).getAction();
        } else {
            toastAction = Action.builder()
                    .title("Toast message")
                    .actionType(ActionType.SHOW_TOAST_MESSAGE)
                    .meta(ToastMessageMeta.builder().message(toastMessage).build())
                    .build();
        }

        return SquadInvitesBulk.builder()
                .squadInvites(squadInvites)
                .action(toastAction)
                .success(successMetric)
                .build();
    }

    public static List<ProgressIndicatorTile> getSquadProgressTilesFromFitnessResponse(
            UserContext userContext,
            ServiceInterfaces interfaces,
            Map<String, UserEntry> userEntryMap,
            Map<String, FitnessReportResponse> fitnessReportResponseMap,
            List<String> userIds
    ) {
        List<ProgressIndicatorTile> progressIndicatorTiles = new ArrayList<>();
        long floorSessionCount = 0L;
        long roofSessionCount = 6L;
        if (fitnessReportResponseMap == null) return progressIndicatorTiles;

        return userIds.stream().map(userId -> {
            long sessionCount = 0;
            UserEntry userEntryCF = userEntryMap.get(userId);
            FitnessReportResponse fitnessReportResponse = fitnessReportResponseMap.get(userId);
            if (fitnessReportResponse != null && fitnessReportResponse.getFitnessReport() != null) {
                sessionCount = Math.min(
                        Math.max(fitnessReportResponse.getFitnessReport().getClassesAttended(), floorSessionCount), roofSessionCount
                );
            }

            ProgressIndicatorTile tile = new ProgressIndicatorTile(
                    userId,
                    userId.equals(userContext.getUserProfile().getUserId()),
                    userEntryCF.getFirstName(),
                    userEntryCF.getProfilePictureUrl(),
                    sessionCount);
            return tile;
        }).collect(toList());
    }

    public Set<String> getAllUserIdsInSegment(String segmentId, AppTenant appTenant) throws BaseException {
        Set<String> allUserIds = new HashSet<>();
        int offset = 0;
        List<String> userIdsInPage;
        do {
            try {
                userIdsInPage = serviceInterfaces.userSegmentClient.getUserIdsInSegment(segmentId, appTenant, Integer.toString(offset), Integer.toString(PAGE_SIZE));
                allUserIds.addAll(userIdsInPage);
                offset = offset + PAGE_SIZE;
            } catch (HttpException e) {
                serviceInterfaces.exceptionReportingService.reportException(e);
                throw new BaseException(
                        "Failed to fetch user data",
                        LogType.ERROR,
                        "cityToSegmentMap",
                        AppStatus.SERVER_ERROR,
                        "SEGMENT_USER_DATA_FETCH_500",
                        "An unexpected error occurred while fetching user data from the server"
                );
            }

        } while (userIdsInPage.size() == PAGE_SIZE);

        return allUserIds;
    }

    public FetchedUsersInfo fetchAllUsers(UserContext userContext, SyncContactRequest syncContactInfo) throws ExecutionException, InterruptedException, BaseException {
        String currentUserCityId = userContext.getUserProfile().getCity().getName();
        List<String> cultChampionUserIds = new ArrayList<>();
        Map<String, String> configData = cultChampionsPageConfigService.getPageConfigData();
        String segmentId = configData.get(currentUserCityId);
        Set<String> userIdsInSegment = new HashSet<>();
        if(segmentId != null) {
            userIdsInSegment = this.getAllUserIdsInSegment(segmentId, AppTenant.CUREFIT);
        }
        if(userIdsInSegment != null) {
            cultChampionUserIds = socialServiceClient.getVisibleUserIds(userContext.getUserProfile().getUserId(), new ArrayList<>(userIdsInSegment));
        }
        FetchedUsersInfo fetchedUsersInfo = new FetchedUsersInfo();
        fetchedUsersInfo.setTitle("See what your friends are up to");
        fetchedUsersInfo.setSubtitle("You can view friends working out at cult.fit centers");
        try {
            List<FriendsInfo> friendsInfoList = new ArrayList<>();
            try {
                contactSyncService.syncContacts(userContext.getUserProfile().getUserId(), syncContactInfo);
            } catch (Exception e) {
                String msg = "Error while syncing contacts in find a friend";
                exceptionReportingService.reportException(msg, e);
                log.error(msg, e);
            }
            ContactsInfo contactsInfo = this.socialServiceClient.getAllContacts(userContext.getUserProfile().getUserId());
            List<String> userIds = contactsInfo.getUserIds();
            if (contactsInfo != null && userIds != null && !userIds.isEmpty()) {
                int privateUsersCount = 0;
                List<String> privateUsers = contactsInfo.getPrivateUsers();
                List<String> sameCityUsers = new ArrayList<>();
                List<String> otherCityUsers = new ArrayList<>();
                List<String> sameCityPrivateUsers = new ArrayList<>();
                List<String> otherCityPrivateUsers = new ArrayList<>();
                userIds.forEach(userId -> {
                    List<String> benefits = List.of("CULT");
                    try {
                        List<Membership> membershipList = membershipService.getCachedMembershipsForUser(userId, "curefit", MembershipFilter.builder().benefits(benefits).status(Status.PURCHASED).status(Status.PAUSED).build()).get();

                        if (membershipList != null && !membershipList.isEmpty()) {
                            Map<String, Object> metadataInfo = membershipList.get(0).getMetadata();
                            if (metadataInfo != null) {
                                Object cityId = metadataInfo.get("cityId");
                                if (cityId != null) {
                                    if (cityId.equals(currentUserCityId)) {
                                        sameCityUsers.add(userId);
                                    } else {
                                        otherCityUsers.add(userId);
                                    }
                                }
                            }
                        }
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    } catch (ExecutionException e) {
                        throw new RuntimeException(e);
                    }
                });
                privateUsers.forEach(userId -> {
                    List<String> benefits = List.of("CULT");
                    try {
                        List<Membership> membershipList = membershipService.getCachedMembershipsForUser(userId, "curefit", MembershipFilter.builder().benefits(benefits).status(Status.PURCHASED).status(Status.PAUSED).build()).get();
                        if (membershipList != null && !membershipList.isEmpty()) {
                            Map<String, Object> metadataInfo = membershipList.get(0).getMetadata();
                            if (metadataInfo != null) {
                                Object cityId = metadataInfo.get("cityId");
                                if (cityId != null) {
                                    if (cityId.equals(currentUserCityId)) {
                                        sameCityPrivateUsers.add(userId);
                                    } else {
                                        otherCityPrivateUsers.add(userId);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error in fetching membership for userId: {}, error: {}", userId, e);
                    }
                });

                if (!sameCityUsers.isEmpty()) {
                    Map<String, Long> totalClassesAttendedMap = contactsInfo.getTotalClassesAttendedMap();
                    List<String> filteredUserInfoList = sameCityUsers.stream().filter(userId -> totalClassesAttendedMap.get(userId) != null).collect(toList());
                    List<UsersInfo> usersInfoList = computeUserInfoList(filteredUserInfoList, contactsInfo, syncContactInfo, true);

                    if(!usersInfoList.isEmpty()) {
                        FriendsInfo friendsInfo = new FriendsInfo();
                        if (currentUserCityId != null) {
                            friendsInfo.setTitle(String.format("Friends in %s", currentUserCityId));
                        } else {
                            friendsInfo.setTitle("Friends in your city");
                        }
                        List<String> finalCultChampionUserIds = cultChampionUserIds;
                        usersInfoList.forEach(usersInfo -> {
                            if(finalCultChampionUserIds.contains(usersInfo.getId().toString())) {
                                usersInfo.setIsCultChampion(true);
                            }
                        });
                        friendsInfo.setUsersInfoList(usersInfoList);
                        friendsInfoList.add(friendsInfo);
                    }
                }
                if (!otherCityUsers.isEmpty()) {
                    Map<String, Long> totalClassesAttendedMap = contactsInfo.getTotalClassesAttendedMap();
                    List<String> filteredUserInfoList = otherCityUsers.stream().filter(userId -> totalClassesAttendedMap.get(userId) != null).collect(toList());
                    List<UsersInfo> usersInfoList = computeUserInfoList(filteredUserInfoList, contactsInfo, syncContactInfo, true);

                    if(!usersInfoList.isEmpty()) {
                        FriendsInfo friendsInfo = new FriendsInfo();
                        friendsInfo.setTitle("Friends in other cities");
                        List<String> finalCultChampionUserIds1 = cultChampionUserIds;
                        usersInfoList.forEach(usersInfo -> {
                            if(finalCultChampionUserIds1.contains(usersInfo.getId().toString())) {
                                usersInfo.setIsCultChampion(true);
                            }
                        });
                        friendsInfo.setUsersInfoList(usersInfoList);
                        friendsInfoList.add(friendsInfo);
                    }
                }
                privateUsersCount = sameCityPrivateUsers.size() + otherCityPrivateUsers.size();
                if (privateUsersCount > 0) {
                    fetchedUsersInfo.setPrivateFriendsText(String.format("Your %s more friends at cult.fit are crushing their workouts, keeping it low-key, silently achieving their fitness goals", privateUsersCount));
                }
                if(!friendsInfoList.isEmpty()) {
                    fetchedUsersInfo.setFriendsInfo(friendsInfoList);
                } else {
                    FriendsInfo cultChampionsInfo = new FriendsInfo();
                    fetchedUsersInfo.setTitle("See what our top athletes are up to");
                    fetchedUsersInfo.setSubtitle("You can view our cult champions -  friendly fitness enthusiats at cult");
                    if(segmentId != null) {
                        Map<String, Long> totalClassesAttendedMap = new ConcurrentHashMap<>();
                        List<List<String>> filteredUserBatches = Lists.partition(cultChampionUserIds, 5);
                        CompletableFuture.allOf(filteredUserBatches.stream().map(
                                filteredUserList -> cultServiceImpl.getTotalClassesAttendedForLatestFitnessReportForUserIds(
                                        filteredUserList.stream().map(Integer::parseInt).collect(toList())).thenAccept(
                                        result -> totalClassesAttendedMap.putAll(
                                                result.getTotalClassesAttendedMap()))).toArray(
                                CompletableFuture[]::new)).get();
                        contactsInfo.setTotalClassesAttendedMap(totalClassesAttendedMap);
                        cultChampionsInfo.setTitle(String.format("Cult Champions in %s", currentUserCityId));
                        List<String> filteredUserInfoList = cultChampionUserIds.stream().filter(userId -> totalClassesAttendedMap.get(userId) != null).collect(toList());
                        List<UsersInfo> usersInfoList = computeUserInfoList(new ArrayList<>(filteredUserInfoList), contactsInfo, syncContactInfo, false);
                        List<String> finalCultChampionUserIds2 = cultChampionUserIds;
                        usersInfoList.forEach(usersInfo -> {
                            if(finalCultChampionUserIds2.contains(usersInfo.getId().toString())) {
                                usersInfo.setIsCultChampion(true);
                            }
                        });
                        cultChampionsInfo.setUsersInfoList(usersInfoList.subList(0, 50));
                    }
                    fetchedUsersInfo.setCultChampionsInfo(cultChampionsInfo);
                }
            }
        } catch (Exception e) {
            log.error("Error in fetching all contacts: {}", e);
        }
        return fetchedUsersInfo;
    }

    private List<UsersInfo> computeUserInfoList(List<String> filteredUserInfoList, ContactsInfo contactsInfo, SyncContactRequest syncContactInfo, Boolean filterFromPhonebook) {
        Map<String, Long> totalClassesAttendedMap = contactsInfo.getTotalClassesAttendedMap();
        Map<String, UserEntry> userEntryMap = getUserEntryMap(filteredUserInfoList);
        Map<String, String> sanitisedContacts = extractUserContacts(syncContactInfo);
        if(filterFromPhonebook) {
            filteredUserInfoList = filteredUserInfoList.stream().filter(userId -> {
                UserEntry userEntryInfo = userEntryMap.get(userId);
                String userPhoneNumber = userEntryInfo.getPhone();
                return sanitisedContacts.get(userPhoneNumber) != null;
            }).collect(toList());
        }
        return filteredUserInfoList.stream().map(userId -> {
            UsersInfo userInfo = new UsersInfo();
            userInfo.setId(Long.parseLong(userId));
            // below logic implement with phone contacts
            Long classesAttended = totalClassesAttendedMap.get(userId);
            userInfo.setClassesAttended(classesAttended);
            try {
                UserEntry userEntryInfo = userEntryMap.get(userId);
                String userPhoneNumber = userEntryInfo.getPhone();
                String userProfilePic = userEntryInfo.getProfilePictureUrl();
                String displayName = sanitisedContacts.get(userPhoneNumber);
                if(displayName == null || displayName == "") {
                    displayName = getDisplayNameFromUserEntry(userEntryInfo);
                } else {
                    String regex = "\\b\\d{10,}\\b";
                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(displayName.replaceAll("\\s+", ""));
                    if (matcher.find()) {
                        displayName = getDisplayNameFromUserEntry   (userEntryInfo);
                    }
                }
                userInfo.setName(displayName);
                userInfo.setAvatarUrl(userProfilePic);
                userInfo.setPhone(userEntryInfo.getPhone());
            } catch (Exception e) {
                log.error("Error in fetching userEntry Map: {}", e);
                throw new RuntimeException(e);
            }
            return userInfo;
        }).sorted((a, b) -> a.getClassesAttended().compareTo(b.classesAttended)).collect(toList()).reversed();
    }

    private String getDisplayNameFromUserEntry(UserEntry userEntry) {
        return userEntry.getFirstName() + ((userEntry.getLastName() != null && !"".equals(userEntry.getLastName()) && !"null".equals(userEntry.getLastName())) ? " " + userEntry.getLastName() : "");
    }
}
