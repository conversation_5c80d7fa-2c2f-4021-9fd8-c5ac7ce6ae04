package com.curefit.cfapi.service.cultsport.payment.snapmint;

import com.curefit.alfred.models.order.ProductSnapshot;
import com.curefit.cfapi.model.internal.fitstore.FitstorePLPProduct;
import com.curefit.cfapi.model.internal.meta.AnalyticsData;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionStyledDisplayText;
import com.curefit.cfapi.pojo.cultsport.CultsportEventName;
import com.curefit.cfapi.util.GearMicroappUtil;
import com.curefit.cfapi.util.OrderUtil;
import com.curefit.cfapi.pojo.vm.widget.Text.TextItem;
import com.curefit.cfapi.pojo.vm.widget.Text.TextItemParent;
import com.curefit.cfapi.pojo.vm.widget.Text.TextStyle;
import com.curefit.cfapi.widgets.cultsport.pdp.SnapmintPDPWidget;
import com.curefit.cfapi.widgets.cultsport.plp.SnapmintPLPStrip;
import com.curefit.gearvault.models.CatalogueProductV2;
import com.curefit.gearvault.models.CatalogueVariantV2;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.curefit.cfapi.pojo.app.action.ActionType.ADD_TO_CART;
import static com.curefit.cfapi.pojo.app.action.ActionType.SHOW_SNAPMINT_EMI_MODAL;

@Slf4j
@Service
@RequiredArgsConstructor
public class CSSnapmintService {
    /*
     * All the prices considered in this class are numbers after offer application.
     */

    private final CSSnapmintConfigStore csSnapmintConfigStore;


    public boolean isArticleTypeEligibleForSnapmintPayment(String articleType) {
        return csSnapmintConfigStore.getSNAPMINT_SUPPORTED_ARTICLE_TYPES().contains(articleType);
    }

    public boolean isArticleTypeEligibleForBuyNowPDPSnapmintStrip(String articleType) {
        return csSnapmintConfigStore.getSNAPMINT_SUPPORTED_ARTICLE_TYPES_PDP_BUY_NOW_OPTION().contains(articleType);
    }

    public boolean isArticleTypeEligibleForEMIInfoPDPSnapmintStrip(String articleType) {
        return csSnapmintConfigStore.getSNAPMINT_SUPPORTED_ARTICLE_TYPES_PDP_INFO_OPTION().contains(articleType);
    }

    public boolean isPriceEligibleForSnapmintPayment(Double amount) {
        return amount >= csSnapmintConfigStore.getSNAPMINT_MIN_CART_AMOUNT();
    }

    public SnapmintPLPStrip getSnapmintPLPPaymentStrip(FitstorePLPProduct product, Double listingPrice) {
        SnapmintInstallmentDetails installmentDetails = computeSnapmintOrderDetailsForValue(listingPrice);
        if (installmentDetails == null) {
            return null;
        }
        HashMap<String, Object> analyticsProps = getPLPAnalyticsProps(product, installmentDetails);
        return SnapmintPLPStrip.builder()
            .offerText(
                List.of(
                    TextItemParent.builder()
                        .items(
                            List.of(
                                TextItem.builder().text(OrderUtil.RUPEE_SYMBOL + installmentDetails.getEmiValue() + "/month").textColor("#393A3B").textStyle(TextStyle.BOLD).build(),
                                TextItem.builder().text("at 0% EMI on UPI").textColor("#393A3B").textStyle(TextStyle.REGULAR).build()
                            )
                        )
                        .build()
                )
            )
            .analyticsData(analyticsProps)
            .build();

    }

    public SnapmintPDPWidget getSnapmintPDPPaymentStrip(CatalogueProductV2 product, Double listingPrice) {
        SnapmintInstallmentDetails installmentDetails = computeSnapmintOrderDetailsForValue(listingPrice);
        if (installmentDetails == null) {
            return null;
        }
        // build the v2 widget only for the hardline cases where there is just one variant, if the variant is oos, make the action disabled.
        SnapmintPDPWidget pdpStrip = new SnapmintPDPWidget();
        List<CatalogueVariantV2> catalogueVariants = product.getVariants();
        List<CatalogueVariantV2> orderableVariants = catalogueVariants.stream().filter(CatalogueVariantV2::isOrderable).toList();
        boolean isProductOOS = orderableVariants.size() == 0;
        if (isProductOOS) {
            Action leftAction = getSnapmintModalAction("", listingPrice, installmentDetails, product);
            leftAction.setAnalyticsData(AnalyticsData.builder().eventKey(CultsportEventName.SNAPMINT_EMI_INFO_CLICK.toString()).build());
            leftAction.setStyledDisplayText(
                ActionStyledDisplayText.builder()
                    .displayText(
                        List.of(
                            TextItemParent.builder()
                                .items(
                                    List.of(
                                        TextItem.builder().text(OrderUtil.RUPEE_SYMBOL + installmentDetails.getEmiValue()).textColor("#00BA78").textStyle(TextStyle.BOLD).build(),
                                        TextItem.builder().text(getEMITenureString(installmentDetails.getEmiTenureInMonths())).textColor("#1A1A1E").textStyle(TextStyle.REGULAR).build()
                                    )
                                )
                                .build()
                        )
                    )
                    .build()
            );

            pdpStrip.setLeftAction(leftAction);
            Action rightAction = getSnapmintModalAction("0% EMI on", listingPrice, installmentDetails, product);
            rightAction.setAnalyticsData(AnalyticsData.builder().eventKey(CultsportEventName.SNAPMINT_EMI_INFO_CLICK.toString()).build());
            pdpStrip.setRightAction(rightAction);
        } else if (isArticleTypeEligibleForBuyNowPDPSnapmintStrip(product.getArticle_type())) {
            Object singleVariantAddToCartMeta = GearMicroappUtil.getGearAddToCartActionMeta(product,
                    catalogueVariants.get(0), true);
            pdpStrip.setVersion("v2");
            pdpStrip.setRightAction(
                Action.builder()
                    .title("Buy on EMI")
                    .actionType(ADD_TO_CART)
                    .disabled(isProductOOS)
                    .meta(singleVariantAddToCartMeta)
                    .analyticsData(AnalyticsData.builder().eventKey(CultsportEventName.SNAPMINT_EMI_BUY_NOW_CLICK.toString()).build())
                    .build()
            );
            Action leftAction = getSnapmintModalAction("", listingPrice, installmentDetails, product);
            leftAction.setStyledDisplayText(
                ActionStyledDisplayText.builder()
                    .displayText(
                        List.of(
                            TextItemParent.builder()
                                .items(
                                    List.of(
                                        TextItem.builder().text(OrderUtil.RUPEE_SYMBOL + installmentDetails.getEmiValue()).textColor("#00BA78").textStyle(TextStyle.BOLD).build(),
                                        TextItem.builder().text(getEMITenureString(installmentDetails.getEmiTenureInMonths())).textColor("#1A1A1E").textStyle(TextStyle.REGULAR).build()
                                    )
                                )
                                .build()
                        )
                    )
                    .build()
            );
            leftAction.setAnalyticsData(AnalyticsData.builder().eventKey(CultsportEventName.SNAPMINT_EMI_INFO_CLICK.toString()).build());
            leftAction.setSubtitle("0% EMI on");
            pdpStrip.setLeftAction(leftAction);
        } else if (isArticleTypeEligibleForEMIInfoPDPSnapmintStrip(product.getArticle_type())) {
            Action leftAction = getSnapmintModalAction("", listingPrice, installmentDetails, product);
            leftAction.setAnalyticsData(AnalyticsData.builder().eventKey(CultsportEventName.SNAPMINT_EMI_INFO_CLICK.toString()).build());
            leftAction.setStyledDisplayText(
                ActionStyledDisplayText.builder()
                    .displayText(
                        List.of(
                            TextItemParent.builder()
                                .items(
                                    List.of(
                                        TextItem.builder().text(OrderUtil.RUPEE_SYMBOL + installmentDetails.getEmiValue()).textColor("#00BA78").textStyle(TextStyle.BOLD).build(),
                                        TextItem.builder().text(getEMITenureString(installmentDetails.getEmiTenureInMonths())).textColor("#1A1A1E").textStyle(TextStyle.REGULAR).build()
                                    )
                                )
                                .build()
                        )
                    )
                    .build()
            );

            pdpStrip.setLeftAction(leftAction);
            Action rightAction = getSnapmintModalAction("0% EMI on", listingPrice, installmentDetails, product);
            rightAction.setAnalyticsData(AnalyticsData.builder().eventKey(CultsportEventName.SNAPMINT_EMI_INFO_CLICK.toString()).build());
            pdpStrip.setRightAction(rightAction);
        } else {
            return null;
        }
        HashMap<String, Object> analyticsProps = getPDPAnalyticsProps(product, installmentDetails);
        pdpStrip.setAnalyticsData(analyticsProps);
        return pdpStrip;
    }

    public ActionStyledDisplayText getSnapmintCartReviewPageStrip(List<ProductSnapshot> productSnapshots, Double totalCartValue) {
        /*
         * This purely works on the cart value, no category checks here.
         */
        SnapmintInstallmentDetails installmentDetails = computeSnapmintOrderDetailsForValue(totalCartValue);
        if (installmentDetails == null) {
            return null;
        }
        HashMap<String, Object> analyticsProps = getCheckoutAnalyticsProps(totalCartValue, productSnapshots, installmentDetails);
        return ActionStyledDisplayText.builder()
            .displayText(
                List.of(
                    TextItemParent.builder()
                        .items(
                            List.of(
                                TextItem.builder().text("Get it for ").textColor("#1A1A1E").build(),
                                TextItem.builder().text(OrderUtil.RUPEE_SYMBOL + installmentDetails.getEmiValue() + "/month ").textColor("#1A1A1E").textStyle(TextStyle.BOLD).build(),
                                TextItem.builder().text("at 0% EMI on UPI").textColor("#1A1A1E").build()
                            )
                        )
                        .build()
                )
            )
            .analyticsData(analyticsProps)
            .build();
    }

    private SnapmintInstallmentDetails computeSnapmintOrderDetailsForValue(Double value) {
        if (!isPriceEligibleForSnapmintPayment(value)) {
            return null;
        }
        SnapmintInstallmentConfig config = csSnapmintConfigStore.getInstallmentPlanForAmount(value);
        if (config != null) {
            return getInstallmentDetailsForPlan(config, value);
        }
        return null;
    }

    private SnapmintInstallmentDetails computeSnapmintOrderDetailsForAmountAndTenure(Double value, int emiTenure) {
        if (!isPriceEligibleForSnapmintPayment(value)) {
            return null;
        }
        SnapmintInstallmentConfig config = csSnapmintConfigStore.getInstallmentPlanForTenure(emiTenure);
        if (config != null) {
            return getInstallmentDetailsForPlan(config, value);
        }
        return null;
    }

    private SnapmintInstallmentDetails getInstallmentDetailsForPlan(@NonNull SnapmintInstallmentConfig config, Double value) {
        /*
         * Base logic behind Snapmint EMI calculations
         */
        int tenureInMonths = config.getEmiTenure();
        float downPaymentPercentage = config.getDownPaymentPercentage();
        float emiPercentage = config.getEmiPercentage();
        int emitValue = Integer.parseInt(OrderUtil.toFixedValue((emiPercentage * value), 0));
        int downPaymentValue = Integer.parseInt(OrderUtil.toFixedValue((downPaymentPercentage * value), 0));

        return SnapmintInstallmentDetails.builder()
                .emiValue(emitValue)
                .emiPercentage(emiPercentage)
                .emiTenureInMonths(tenureInMonths)
                .downPaymentValue(downPaymentValue)
                .downPaymentPercentage(downPaymentPercentage)
                .build();
    }


    private Action getSnapmintModalAction(String title,  Double value, SnapmintInstallmentDetails installmentDetails, CatalogueProductV2 product) {
        AtomicReference<SnapmintEMIModalVersion> version = new AtomicReference<>(null);
        SnapmintInstallmentConfig config = csSnapmintConfigStore.getInstallmentPlanForAmount(value);
        version.set(config.getModalVersion());

        HashMap<String, Object> modalProps = getPDPAnalyticsProps(product, installmentDetails);
        modalProps.put("modalVersion", version.get());
        if (version.get().equals(SnapmintEMIModalVersion.V3)) {
            modalProps.put("emiPlans", buildEMIPlansView(Double.valueOf(product.getPrice())));
        }

        return Action.builder()
            .actionType(SHOW_SNAPMINT_EMI_MODAL)
            .title(title)
            .meta(modalProps)
            .build();
    }

    private HashMap<String, Object> getPDPAnalyticsProps(CatalogueProductV2 product, SnapmintInstallmentDetails installmentDetails) {
        try {
            HashMap<String, Object> props = new HashMap<String, Object>(){{
                put("totalValue", Double.valueOf(product.getPrice()));
                put("emiPrice", installmentDetails.getEmiValue());
                put("emiTenure", installmentDetails.getEmiTenureInMonths());
                put("emiPercentage", installmentDetails.getEmiPercentage());
                put("emiProvider", "Snapmint");
                put("downPaymentPrice", installmentDetails.getDownPaymentValue());
                put("downPaymentPercentage", installmentDetails.getDownPaymentPercentage());
                put("productName", product.getName());
                put("articleType", product.getArticle_type());
                put("productId", product.getId());
                put("category", product.getCategory());
                put("brand", product.getBrand());
                put("listingPrice", Double.valueOf(product.getPrice()));
            }};
            return props;
        } catch (Exception e) {
            log.error("ERR::CSSnapmintService::getPDPAnalyticsProps()" + e.toString());
            return new HashMap<String, Object>();
        }
    }

    private HashMap<String, Object> getPLPAnalyticsProps(FitstorePLPProduct product, SnapmintInstallmentDetails installmentDetails) {
        try {
            HashMap<String, Object> props = new HashMap<String, Object>(){{
                put("productName", product.getTitle());
                put("articleType", product.getAttributes().getArticleType());
                put("listingPrice", product.getAttributes().getPrice().getListingPrice());
                put("emiPrice", installmentDetails.getEmiValue());
                put("emiTenure", installmentDetails.getEmiTenureInMonths());
                put("emiPercentage", installmentDetails.getEmiPercentage());
                put("emiProvider", "Snapmint");
                put("downPaymentPrice", installmentDetails.getDownPaymentValue());
                put("downPaymentPercentage", installmentDetails.getDownPaymentPercentage());
                put("productId", product.getId());
                put("category", product.getAttributes().getCategory());
                put("brand", product.getAttributes().getBrand());
                put("totalValue", product.getAttributes().getPrice().getListingPrice());
            }};
            return props;
        } catch (Exception e) {
            log.error("ERR::CSSnapmintService::getPLPAnalyticsProps()" + e.toString());
            return new HashMap<String, Object>();
        }
    }

    private HashMap<String, Object> getCheckoutAnalyticsProps(Double totalCartValue, List<ProductSnapshot> productSnapshots, SnapmintInstallmentDetails installmentDetails) {
        try {
            HashMap<String, Object> props = new HashMap<>(){{
                put("emiPrice", installmentDetails.getEmiValue());
                put("emiTenure", installmentDetails.getEmiTenureInMonths());
                put("emiPercentage", installmentDetails.getEmiPercentage());
                put("emiProvider", "Snapmint");
                put("downPaymentPrice", installmentDetails.getDownPaymentValue());
                put("downPaymentPercentage", installmentDetails.getDownPaymentPercentage());
                put("productIds", productSnapshots.stream().map(ProductSnapshot::getMasterProductId).collect(Collectors.joining(", ")));
                put("articleTypes", productSnapshots.stream().map(ProductSnapshot::getGearArticleType).collect(Collectors.joining(", ")));
                put("totalPayable", totalCartValue);
            }};
            return props;
        } catch (Exception e) {
            log.error("ERR::CSSnapmintService::getCheckoutAnalyticsProps()" + e.toString());
            return new HashMap<String, Object>();
        }
    }

    private static String getEMITenureString(int tenureInMonths) {
        return String.format("/ month (%s months)", tenureInMonths);
    }

    private List<SnapmintUIModalInfo> buildEMIPlansView(Double price) {
        /*
         * We don't want to show the 2 month UI plan for heavy articles
         */
        List<SnapmintUIModalInfo> uiPlans = new ArrayList<>();
        csSnapmintConfigStore.getOptions().forEach(plan -> {
            if (plan.getEmiTenure() != 2) {
                SnapmintInstallmentDetails detailsForConfig = getInstallmentDetailsForPlan(plan, price);
                uiPlans.add(
                    SnapmintUIModalInfo.builder()
                        .tag("0% EMI")
                        .downPaymentAmount(detailsForConfig.getDownPaymentValue())
                        .emiAmount(detailsForConfig.getEmiValue())
                        .tenure(detailsForConfig.getEmiTenureInMonths())
                        .build()
                );
            }
        });

        csSnapmintConfigStore.getWithInterestOtions().forEach(plan -> {
            if (plan.getEmiTenure() != 2) {
                SnapmintInstallmentDetails detailsForConfig = getInstallmentDetailsForPlan(plan, price);
                uiPlans.add(
                    SnapmintUIModalInfo.builder()
                        .downPaymentAmount(detailsForConfig.getDownPaymentValue())
                        .emiAmount(detailsForConfig.getEmiValue())
                        .tenure(detailsForConfig.getEmiTenureInMonths())
                        .build()
                );
            }
        });
        return uiPlans;
    }

}

