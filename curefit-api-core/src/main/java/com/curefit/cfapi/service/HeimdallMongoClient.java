package com.curefit.cfapi.service;


import com.curefit.cfapi.view.viewmodels.heimdall.GetHeimdallMongoAuditLogsPayload;
import com.curefit.cfapi.view.viewmodels.heimdall.GetHeimdallMongoAuditLogsResponse;
import com.curefit.common.rest.client.CommonHttpHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("heimdallMongoClient")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class HeimdallMongoClient {

    final CommonHttpHelper commonHttpHelper;

    @Value("${heimdallMongoAudit.baseUrl}")
    String baseUrl;
    @Value("${heimdallMongoAudit.apiKey}")
    String apiKey;

    private UriComponentsBuilder getUri() {
        return UriComponentsBuilder.fromHttpUrl(baseUrl);
    }

    private Map<String, String> getHeadersMap() {
        return new HashMap<>() {{
            put("Content-Type", "application/json");
            put("Accept", "application/json");
        }};
    }

    private GetHeimdallMongoAuditLogsResponse getLogs(GetHeimdallMongoAuditLogsPayload payload) {
        String url = getUri().path("v1/auditlog/fetchV2")
                .queryParam("apiKey", apiKey)
                .build().toUriString();
        ResponseEntity<GetHeimdallMongoAuditLogsResponse> httpResponse = commonHttpHelper.request(url, HttpMethod.POST, payload, getHeadersMap(), new TypeReference<>() {
        });
        return httpResponse.getBody();
    }

    private String getCurrentMonthStart() {
        LocalDate firstDayOfMonth = LocalDate.now(ZoneId.of("Asia/Kolkata")).withDayOfMonth(1);
        return firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    public GetHeimdallMongoAuditLogsResponse getCultRevertedNoShowLogs(Long userId) {
        GetHeimdallMongoAuditLogsPayload payload = new GetHeimdallMongoAuditLogsPayload(userId, "CULTFIT_REVERT_NO_SHOW", "noShow", getCurrentMonthStart());
        return getLogs(payload);
    }

    public GetHeimdallMongoAuditLogsResponse getPlayRevertedNoShowLogs(Long userId) {
        GetHeimdallMongoAuditLogsPayload payload = new GetHeimdallMongoAuditLogsPayload(userId, "PLAY_REVERT_NO_SHOW", "noShow", getCurrentMonthStart());
        return getLogs(payload);
    }

}
