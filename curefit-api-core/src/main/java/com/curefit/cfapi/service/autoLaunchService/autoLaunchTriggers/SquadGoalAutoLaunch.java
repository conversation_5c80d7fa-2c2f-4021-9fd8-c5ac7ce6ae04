package com.curefit.cfapi.service.autoLaunchService.autoLaunchTriggers;

import com.curefit.cfapi.cache.FeatureStateCache;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.autoLaunch.AutoLaunchAction;
import com.curefit.cfapi.pojo.feedback.Feedback;
import com.curefit.cfapi.repository.AutoLaunchPageConfigRepository;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.autoLaunchService.AutoLaunchBaseService;
import com.curefit.cfapi.service.autoLaunchService.AutoLaunchEvaluator;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.ExecutionException;

@Slf4j
@Service
public class SquadGoalAutoLaunch extends AutoLaunchBaseService implements AutoLaunchEvaluator {
    
    private static final String FEATURE_STATE_KEY = "SQUAD_GOAL_AUTO_LAUNCH_VIEWED";

    @Autowired
    SquadGoalAutoLaunch(AutoLaunchPageConfigRepository autoLaunchPageConfigRepository, 
                       ServiceInterfaces serviceInterfaces, 
                       FeatureStateCache featureStateCache) throws ExecutionException, InterruptedException {
        super("SquadGoalAutoLaunch", autoLaunchPageConfigRepository, serviceInterfaces, featureStateCache);
    }

    @Override
    public Optional<AutoLaunchAction> evaluate(UserContext userContext, Feedback feedback) throws ExecutionException, InterruptedException {
        super.evaluate(userContext, feedback);
        try {
            if (!super.getAutoLaunchConfig().isEnabled()) {
                return Optional.empty();
            }

            // Check if user belongs to the "Squad Goal Invitation optin" segment
            if (!validateSegmentsWithAnd(userContext)) {
                return Optional.empty();
            }

            // Check if feature state cache is not set
            if (validateFeatureStateCache(FEATURE_STATE_KEY, "viewed", userContext)) {
                return Optional.empty();
            }

            // Auto-launch and set feature state with TTL till next Monday
            return getAndSetAutoLaunchAction(userContext);

        } catch (Exception e) {
            log.error("Unable to trigger SquadGoal auto launch due to: " + e.getMessage());
            this.getServiceInterfaces().exceptionReportingService
                    .reportException(new Exception("Unable to trigger SquadGoal auto launch due to: " + e.getMessage()));
        }
        return Optional.empty();
    }

    private Optional<AutoLaunchAction> getAndSetAutoLaunchAction(UserContext userContext) {        
        // Set feature state cache with TTL till next Monday
        int ttlInSeconds = calculateTtlTillNextMonday();
        this.getServiceInterfaces().featureStateCache.setWithTtl(
            userContext.getUserProfile().getUserId(), 
            FEATURE_STATE_KEY, 
            "viewed", 
            ttlInSeconds
        );
        
        return Optional.of(new AutoLaunchResult(super.getFlutterAction(), super.getPriority(), super.getPageId()));
    }

    private int calculateTtlTillNextMonday() {
        return TimeUtil.getTtlTillNextMonday();
    }

    class AutoLaunchResult extends AutoLaunchAction {

        public AutoLaunchResult(Action action, Integer priority, String pageId) {
            super(action, priority, pageId);
        }

        @Override
        public void onExecute() {
            // Implementation for when auto-launch is executed
        }
    }
} 