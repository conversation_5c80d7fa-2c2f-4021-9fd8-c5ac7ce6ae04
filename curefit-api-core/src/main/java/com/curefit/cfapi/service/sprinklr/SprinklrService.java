package com.curefit.cfapi.service.sprinklr;

import com.curefit.albus.request.AlbusSortOrder;
import com.curefit.albus.request.ConsultationSearchRequestParamModel;
import com.curefit.albus.request.ConsultationSortBy;
import com.curefit.albus.response.CarefitConsultationOrderResponse;
import com.curefit.base.enums.AppTenant;
import com.curefit.center.dtos.CenterEntry;
import com.curefit.center.dtos.CenterMedia;
import com.curefit.center.dtos.CenterSearchFilters;
import com.curefit.center.dtos.CenterSearchRequest;
import com.curefit.center.enums.MediaType;
import com.curefit.cfapi.builder.vm.RequestCache;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.userinfo.UserProfile;
import com.curefit.cfapi.service.HeimdallMongoClient;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.UserService;
import com.curefit.cfapi.util.*;
import com.curefit.cfapi.view.viewbuilders.memberships.MembershipDetailPageViewBuilder;
import com.curefit.cfapi.view.viewmodels.sprinklr.activePacks.ActivePack;
import com.curefit.cfapi.view.viewmodels.sprinklr.activePacks.ActivePackResponse;
import com.curefit.cfapi.view.viewmodels.sprinklr.activePacks.MembershipDetail;
import com.curefit.cfapi.view.viewmodels.sprinklr.center.Center;
import com.curefit.cfapi.view.viewmodels.sprinklr.center.CenterEmployee;
import com.curefit.cfapi.view.viewmodels.sprinklr.center.LatLong;
import com.curefit.cfapi.view.viewmodels.sprinklr.centerManager.CenterManager;
import com.curefit.cfapi.view.viewmodels.sprinklr.doctor.Doctor;
import com.curefit.cfapi.view.viewmodels.sprinklr.patient.Patient;
import com.curefit.cfapi.view.viewmodels.sprinklr.sessions.Session;
import com.curefit.cfapi.view.viewmodels.sprinklr.sessions.SessionsResponse;
import com.curefit.cfapi.view.viewmodels.sprinklr.trainer.Trainer;
import com.curefit.cfapi.view.viewmodels.sprinklr.user.UserDetails;
import com.curefit.cfapi.view.viewmodels.sprinklr.user.UserDetailsRequest;
import com.curefit.cfapi.view.viewmodels.sprinklr.user.UserResponse;
import com.curefit.cfapi.view.viewmodels.sprinklr.workout.TimeSlot;
import com.curefit.cfapi.view.viewmodels.sprinklr.workout.Workout;
import com.curefit.cfapi.widgets.membership.MembershipState;
import com.curefit.cfapi.widgets.membership.MembershipWidgetAurora;
import com.curefit.common.data.exception.BaseException;
import com.curefit.cult.enums.BookingLabel;
import com.curefit.cult.enums.BookingTypes;
import com.curefit.cult.models.BulkBookingResponse;
import com.curefit.cult.models.CultBooking;
import com.curefit.cult.models.CultDocument;
import com.curefit.cult.models.CultEmployeeResponse;
import com.curefit.cult.models.responses.CultClassResponse;
import com.curefit.diyfs.pojo.DigitalCatalogueEntryV1;
import com.curefit.gymfit.dtos.Media;
import com.curefit.gymfit.models.CheckIn;
import com.curefit.gymfit.utils.Enums;
import com.curefit.gymfit.utils.Enums.CheckInType;
import com.curefit.identity.model.IdentityResponse;
import com.curefit.location.models.City;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrCommentWebhookRequest;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrMessageWebhookRequest;
import com.curefit.odin.sprinklr.pojo.webhook.SprinklrTicketWebhookRequest;
import com.curefit.personaltrainer.dto.SessionSearchRequest;
import com.curefit.personaltrainer.pojo.entry.SessionEntry;
import com.curefit.product.enums.ProductType;
import com.curefit.rashi.client.UserAttributesCacheClient;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.sportsapi.pojo.BookingStatus;
import com.curefit.sportsapi.pojo.FTSBookings;
import com.curefit.userservice.pojo.entry.UserEntry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.curefit.center.dtos.CenterSearchFilters.CenterType.CULT_CENTER;
import static com.curefit.center.dtos.CenterSearchFilters.CenterType.PLAY;
import static com.curefit.center.enums.CenterMediaTag.PRODUCT_BANNER;
import static com.curefit.center.enums.CenterStatus.ACTIVE;


@Slf4j
@RequiredArgsConstructor
@Service
public class SprinklrService {

    private final ServiceInterfaces serviceInterfaces;
    private final UserService userService;
    private final UserAttributesCacheClient userAttributesCacheClient;
    private final ObjectMapper objectMapper;
    private final HeimdallMongoClient heimdallMongoClient;

    @Value("${martha.baseUrl}/")
    private String marthaServiceBaseUrl;

    @Value("${fms.baseUrl}/")
    private String fmsServiceBaseUrl;

    private static ConsultationSearchRequestParamModel getConsultationSearchRequestParamModel(String page, String limit, String userId) {
        ConsultationSearchRequestParamModel requestParamModel = new ConsultationSearchRequestParamModel();
        requestParamModel.setIsCancelledAppointmentRequired(false);
        requestParamModel.setIsPatientRequired(true);
        requestParamModel.setPageNo((Integer.parseInt(page) - 1));
        requestParamModel.setPageSize(Integer.parseInt(limit));
        requestParamModel.setSortBy(ConsultationSortBy.STARTTIME);
        requestParamModel.setSortOrder(AlbusSortOrder.DESC);
        requestParamModel.setCustomerId(userId);
        requestParamModel.setIsDoctorRequired(true);
        requestParamModel.setIsRootBookingRequired(true);
        return requestParamModel;
    }

    private List<Session> getGXSessionsCustomProperties(List<CultBooking> combinedList, String userId, Boolean skipTrainerDetails) {
        if (combinedList.isEmpty()) {
            return Collections.emptyList();
        }
        List<CompletableFuture<Session>> futures = combinedList.stream().map(booking -> CompletableFuture.supplyAsync(() -> {
            Session properties = new Session();
            properties.setId(booking.getId().toString());
            if (booking.getWlBookingNumber() != null) {
                ZonedDateTime startDateTimeUTC = LocalDateTime.parse(booking.getCultClass().getDate() + " " + booking.getCultClass().getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                        .atZone(ZoneId.of("Asia/Kolkata"))
                        .withZoneSameInstant(ZoneOffset.UTC);
                if (startDateTimeUTC.isBefore(ZonedDateTime.now(ZoneOffset.UTC))) {
                    return null;
                }
                properties.setWaitlist(true);
                properties.setStatus("WAITLISTED");
                properties.setLabel("WAITLISTED");
            } else {
                properties.setStatus(getStatus(booking.getLabel().toString()));
                properties.setLabel(booking.getLabel().toString());
                properties.setTrial(booking.getBookingType() == BookingTypes.TRIAL);
            }
            String formattedDateTime = LocalDateTime.parse(booking.getCultClass().getDate() + "T" + booking.getCultClass().getStartTime()).format(DateTimeFormatter.ofPattern("EEE, dd MMM yyyy hh:mm:ss a"));
            properties.setStartDateTimeUTC(formattedDateTime);
            properties.setUserId(userId);
            properties.setDate(booking.getCultClass().getDate());
            properties.setStartTime(booking.getCultClass().getStartTime());
            properties.setEndTime(booking.getCultClass().getEndTime());
            com.curefit.cult.models.Workout cultWorkout;
            try {
                cultWorkout = serviceInterfaces.cultServiceImpl.getWorkoutById(userId, booking.getCultClass().getWorkoutID().intValue());
            } catch (Exception e) {
                log.error("Error fetching workout details for workoutId {} cultClassId {} userId {} ", booking.getCultClass().getWorkoutID().intValue(), booking.getCultClass().getId(), userId, e);
                return null;
            }
            properties.setTitle(cultWorkout.getName());
            properties.setSubTitle(booking.getCenter().getName());
            properties.setWorkoutDetails(new Workout(cultWorkout.getId().toString(), cultWorkout.getName()));

            CultDocument document = cultWorkout
                    .getDocuments()
                    .stream()
                    .filter(doc -> doc.getTagID() == 11)
                    .findFirst()
                    .orElse(null);
            String imageUrl = "https://cdn-images.cure.fit/www-curefit-com/image/upload/" + (document != null ? document.getUrl() : "image/<EMAIL>");
            properties.setImage(imageUrl);

            Center center = new Center();
            center.setId(booking.getCenter().getCenterServiceId());
            center.setName(booking.getCenter().getName());
            center.setLocality(booking.getCenter().getLocality());
            center.setCity("");
            properties.setCenterDetails(center);

            if (!skipTrainerDetails) {
                properties.setTrainerDetails(getGxTrainerDetails(booking));
                properties.setCmDetails(getCenterCMsAndTrainers(booking.getCenter().getId()).getCenterManagers());
            }
            return properties;
        })).toList();

        return futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<Session> getGymSessionsCustomProperties(List<CheckIn> checkIns) {
        List<Session> sessions = new ArrayList<>();
        for (CheckIn booking : checkIns) {
            Session properties = new Session();
            properties.setId(booking.getId().toString());
            properties.setStatus(booking.getState().toString());
            properties.setUserId(booking.getUserId());
            if (booking.getStartTime() != null && booking.getEndTime() != null) {
                Map<String, String> dates = getDateAndTime(new Date(booking.getStartTime()), new Date(booking.getEndTime()));
                properties.setDate(dates.get("date"));
                properties.setStartTime(dates.get("startTime"));
                properties.setEndTime(dates.get("endTime"));
                properties.setStartDateTimeUTC(dates.get("startDateTimeIST"));

                properties.setStatus(getGymStatus(booking.getState().toString(), dates));
            }

            if (booking.getCheckInType() == CheckInType.TRIAL) {
                properties.setTrial(true);
            }

            Center center = new Center();
            center.setId(booking.getCenterOffering().getCenter().getCenterServiceId().toString());
            center.setName(booking.getCenterOffering().getCenter().getName());
            center.setLocality(booking.getCenterOffering().getCenter().getLocality());
            center.setCity(booking.getCenterOffering().getCenter().getAddress().getCityId());
            properties.setCenterDetails(center);

            CenterEmployee data = getCenterCMsAndTrainers(booking.getCenterOffering().getCenter().getId());
            properties.setCmDetails(data.getCenterManagers());
            properties.setTrainerDetails(data.getTrainers());
            properties.setTitle("GYM Workout");
            properties.setSubTitle(booking.getCenterOffering().getCenter().getName());

            Media imageMedia = new Media();
            if (booking.getCenterOffering().getCenter() != null && booking.getCenterOffering().getCenter().getCenterMedia() != null && booking.getCenterOffering().getCenter().getCenterMedia().getClpMedia() != null) {
                imageMedia = booking.getCenterOffering()
                        .getCenter()
                        .getCenterMedia()
                        .getClpMedia()
                        .stream()
                        .filter(doc -> Objects.equals(doc.getType().toString(), "IMAGE"))
                        .findFirst()
                        .orElse(null);
            }

            properties.setImage("https://s3.ap-south-1.amazonaws.com/curefit-content" + (imageMedia != null ? imageMedia.getMediaUrl() : "/image/<EMAIL>"));

            sessions.add(properties);
        }
        return sessions;
    }

    private List<Session> getPlaySessionsCustomProperties(List<FTSBookings> userPlayBookings, Boolean skipTrainerDetails) {
        List<Session> sessions = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss z");

        for (FTSBookings booking : userPlayBookings) {
            if (BookingStatus.CANCELLED.equals(booking.getBookingStatus())) {
                continue;
            }
            Session properties = new Session();
            properties.setId(booking.getId().toString());
            properties.setStatus(getStatus(booking.getBookingStatus().toString()));
            properties.setLabel(booking.getBookingStatus().toString());
            properties.setUserId(booking.getUserId().toString());
            properties.setTrial(booking.getIsTrial());
            properties.setDate(booking.getCultClass().getDate());

            String istTime = ZonedDateTime.parse(booking.getCultClass().getStartDateTimeUTC(), formatter)
                    .withZoneSameInstant(ZoneId.of("Asia/Kolkata"))
                    .format(DateTimeFormatter.ofPattern("EEE, dd MMM yyyy hh:mm:ss a"));
            properties.setStartDateTimeUTC(istTime);

            // zero padded time string
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("H:mm:ss");
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
            String paddedStartTime = LocalTime.parse(booking.getCultClass().getStartTime(), inputFormatter).format(outputFormatter);
            String paddedEndTime = LocalTime.parse(booking.getCultClass().getEndTime(), inputFormatter).format(outputFormatter);
            properties.setStartTime(paddedStartTime);
            properties.setEndTime(paddedEndTime);

            Center center = new Center();
            center.setId(booking.getCenter().getId().toString());
            center.setName(booking.getCenter().getName());
            center.setLocality(booking.getCenter().getLocality());
            center.setCity(booking.getCenter().getCity());

            properties.setWorkoutDetails(new Workout(booking.getWorkout().getId().toString(), booking.getWorkout().getName()));
            properties.setCenterDetails(center);

            if (!skipTrainerDetails) {
                properties.setTrainerDetails(new ArrayList<>());
                properties.setCmDetails(getCenterCMsAndTrainers(booking.getCenter().getId()).getCenterManagers());
            }

            properties.setTitle(booking.getWorkout().getName());
            properties.setSubTitle(booking.getCenter().getName());
            properties.setImage("https://s3.ap-south-1.amazonaws.com/curefit-content/" + booking.getWorkout().getIconUrl());

            sessions.add(properties);
        }
        return sessions;
    }

    private List<Session> getTransformConsultationSessionsCustomProperties(List<CarefitConsultationOrderResponse> consultations, String userId) {
        List<Session> sessions = new ArrayList<>();
        if (CollectionUtils.isEmpty(consultations)) {
            return sessions;
        }
        for (CarefitConsultationOrderResponse booking : consultations) {
            Session properties = new Session();
            properties.setId(booking.getBookingId().toString());
            properties.setStatus("SCHEDULED".equals(booking.getStatus()) ? "BOOKED" : booking.getStatus());
            properties.setUserId(userId);
            if (booking.getBooking() != null) {
                properties.setCfOrderId(booking.getBooking().getCfOrderId());
            }

            if (booking.getStartTime() != null && booking.getEndTime() != null) {
                Map<String, String> dates = getDateAndTime(booking.getStartTime(), booking.getEndTime());
                log.info("dates consultation: {}", dates);
                properties.setDate(dates.get("date"));
                properties.setStartTime(dates.get("startTime"));
                properties.setEndTime(dates.get("endTime"));
                properties.setStartDateTimeUTC(dates.get("startDateTimeIST"));
            }

            if (booking.getPatient() != null) {
                Patient patient = new Patient();
                patient.setId(booking.getPatient().getId().toString());
                patient.setName(booking.getPatient().getName());
                patient.setEmailId(booking.getPatient().getEmailId());
                properties.setPatient(patient);
            }

            if (booking.getDoctor() != null) {
                Doctor doctor = new Doctor();
                doctor.setId(booking.getDoctor().getId().toString());
                doctor.setName(booking.getDoctor().getName());
                doctor.setDoctorType(booking.getDoctorType());
                properties.setDoctor(doctor);
                properties.setImage("https://s3.ap-south-1.amazonaws.com/curefit-content/image/<EMAIL>");
                properties.setSubTitle("With" + booking.getDoctor().getName() + "for " + booking.getPatient().getName());
            } else {
                properties.setSubTitle("for " + booking.getPatient().getName());
                properties.setImage("https://s3.ap-south-1.amazonaws.com/curefit-content/image/<EMAIL>");
            }
            if (booking.getConsultationProduct() != null) {
                properties.setTitle(booking.getConsultationProduct().getName());
            } else {
                properties.setTitle(booking.getDoctorTypeCodeResponse().getDisplayValue() + "Consultation");
            }

            sessions.add(properties);
        }
        return sessions;
    }

    private List<Session> getPTSessionsCustomProperties(List<SessionEntry> sessionEntries) {
        List<Session> sessions = new ArrayList<>();
        for (SessionEntry booking : sessionEntries) {
            Session properties = new Session();
            properties.setId(booking.getId().toString());
            properties.setStatus(booking.getStatus().toString());
            properties.setUserId(booking.getUserId());
            if (booking.getStartTime() != null && booking.getEndTime() != null) {
                Map<String, String> dates = getDateAndTime(new Date(booking.getStartTime()), new Date(booking.getEndTime()));
                log.info("dates pt: {}", dates);
                properties.setDate(dates.get("date"));
                properties.setStartTime(dates.get("startTime"));
                properties.setEndTime(dates.get("endTime"));
                properties.setStartDateTimeUTC(dates.get("startDateTimeIST"));

                properties.setStatus(getPTStatus(booking.getStatus().toString(), dates));
            }

            if (Objects.equals(booking.getAccessType().toString(), "TRIAL")) {
                properties.setTrial(true);
            }

            Center center = new Center();
            center.setId(booking.getCenterId());
            try {
                CenterEntry centerDetails = serviceInterfaces.centerService.getCenterDetails(Long.parseLong(booking.getCenterId()), true, Collections.emptyMap(), null).get();
                center.setName(centerDetails.getName());
                center.setLocality(centerDetails.getLocality());
                center.setCity(centerDetails.getCity());
                center.setAddress(centerDetails.getFullAddress1());
            } catch (Exception e) {
                log.error("getPTSessionsCustomProperties: Error PT booking center details {} ", e.getMessage());
            }
            properties.setCenterDetails(center);

            List<Trainer> trainers = new ArrayList<>();
            trainers.add(getDefaultTrainer());
            log.info("getPTSessionsCustomProperties identity Id: {}", booking.getIdentityId());
            if (booking.getIdentityId() != null) {
                IdentityResponse identityData = getIdentityById(Long.parseLong(booking.getIdentityId()));
                log.info("getPTSessionsCustomProperties identity data: {}", identityData);
                Trainer trainer = new Trainer();
                trainer.setId(booking.getIdentityId());
                trainer.setName(identityData.getName());
                trainers.add(trainer);
            }
            properties.setTrainerDetails(trainers);
            properties.setTitle("PT Workout");
            properties.setSubTitle("Add subtitle");
            properties.setImage("https://s3.ap-south-1.amazonaws.com/curefit-content" + "/image/<EMAIL>");
            sessions.add(properties);
        }
        return sessions;
    }

    private List<Session> getHomeSessionsCustomProperties(String userId, String page, String limit) throws Exception {
        List<Session> sessions = new ArrayList<>();

        String body = String.format("""
                {
                    "paginate": {
                        "currentPage": %d,
                        "currentPageSize": %d
                    },
                    "limit": %d,
                    "skip": 0,
                    "userId": "%s"
                }
                """, Integer.parseInt(page), Integer.parseInt(limit), Integer.parseInt(limit), userId);

        HttpClient client = HttpClient.newHttpClient();
        log.info("Request Home booking {} ", body);
        String url = this.marthaServiceBaseUrl + "model/digitalConsolidatedMetrics/filter?";
        HttpRequest request = HttpRequest.newBuilder()
                .uri(new URI(url))
                .header("accept", "application/json")
                .header("content-type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(body))
                .build();
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() == 200) {
            log.info("Response Home booking {} ", response.body());
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(response.body());
            JsonNode results = rootNode.get("results");
            if (results != null && results.isArray() && results.size() > 0) {
                for (int i = 0; i < results.size(); i++) {
                    JsonNode booking = results.get(i);
                    String contentId = booking.get("contentId").asText();
                    if (contentId.contains("DIY_FIT_")) {
                        contentId = contentId.replace("DIY_FIT_", "").trim();
                    }
                    try {

                        DigitalCatalogueEntryV1 digitalCatalogueEntry = serviceInterfaces.diyfsService.getDigitalCatalogueEntry(contentId);
                        log.info("Response Home booking contentId {} ", digitalCatalogueEntry);
                        Session properties = new Session();
                        properties.setId(booking.get("_id").asText());
                        if (booking.get("sessionCompletionStatus").asText() != null && "COMPLETED".equals(booking.get("sessionCompletionStatus").asText())) {
                            properties.setStatus("Completed");
                        } else {
                            properties.setStatus("Non - Completed");
                        }
                        properties.setUserId(userId);
                        if (booking.get("startTime") != null) {
                            Map<String, String> dates = getDateAndTime(new Date(booking.get("startTime").asLong()), new Date(1));
                            log.info("dates pt: {}", dates);
                            properties.setDate(dates.get("date"));
                            properties.setStartTime(dates.get("startTime"));
                            properties.setStartDateTimeUTC(dates.get("startDateTimeIST"));
                        }

                        properties.setTitle(digitalCatalogueEntry.getTitle());
                        properties.setSubTitle(digitalCatalogueEntry.getIntensityLevel().toString());

                        if (digitalCatalogueEntry.getBannerImages() != null && digitalCatalogueEntry.getBannerImages().getMobileImage() != null) {
                            properties.setImage("https://s3.ap-south-1.amazonaws.com/curefit-content/" + digitalCatalogueEntry.getBannerImages().getMobileImage());
                        } else {
                            properties.setImage("https://s3.ap-south-1.amazonaws.com/curefit-content/image/<EMAIL>");
                        }

                        sessions.add(properties);
                    } catch (Exception e) {
                        log.error("Error: HTTP Home booking {} ", e.getMessage());
                    }
                }
            } else {
                log.info("Error: result empty for home {}", response.statusCode());
            }
        } else {
            log.info("Error: HTTP Home booking {} ", response.statusCode());
        }

        return sessions;
    }

    private String getPTStatus(String state, Map<String, String> dates) {
        DateTimeFormatter formatter = DateTimeFormatter.RFC_1123_DATE_TIME;
        ZonedDateTime endZonedDateTime = ZonedDateTime.parse(dates.get("endDateTimeUTC"), formatter);
        ZonedDateTime currentZonedDateTime = ZonedDateTime.parse(dates.get("currentTime"), formatter);
        return switch (state) {
            case "BOOKED", "NO_SHOW", "CREATED", "RESCHEDULED" ->
                    currentZonedDateTime.isAfter(endZonedDateTime) ? "MISSED" : "BOOKED";
            case "ATTENDED" -> currentZonedDateTime.isAfter(endZonedDateTime) ? "COMPLETED" : "ONGOING";
            case null, default -> "";
        };
    }

    private String getGymStatus(String state, Map<String, String> dates) {
        DateTimeFormatter formatter = DateTimeFormatter.RFC_1123_DATE_TIME;
        ZonedDateTime endZonedDateTime = ZonedDateTime.parse(dates.get("endDateTimeUTC"), formatter);
        ZonedDateTime currentZonedDateTime = ZonedDateTime.parse(dates.get("currentTime"), formatter);
        if (Objects.equals(state, Enums.CheckInState.CREATED.name())) {
            return currentZonedDateTime.isAfter(endZonedDateTime) ? "MISSED" : "BOOKED";
        } else if (Objects.equals(state, Enums.CheckInState.VALIDATED.name())) {
            return currentZonedDateTime.isAfter(endZonedDateTime) ? "COMPLETED" : "ONGOING";
        }
        return "CANCELLED";
    }

    private String getStatus(String statusRequested) {
        String status;
        switch (statusRequested) {
            case "UPCOMING" -> status = "BOOKED";
            case "COMPLETED", "ATTENDED" -> status = "COMPLETED";
            case "ONGOING" -> status = "ONGOING";
            case "CANCELLED" -> status = "CANCELLED";
            case "DROPPED_OUT" -> status = "DROPPED OUT";
            case "LATE_ENTRY" -> status = "LATE_ENTRY";
            default -> status = "MISSED";
        }
        return status;
    }

    private void sortCultBooking(List<CultBooking> classBookings) {
        ZoneId timezone = ZoneId.of("Asia/Kolkata");

        classBookings.sort((a, b) -> {
            LocalDate dateA = LocalDate.parse(a.get_Class().getDate());
            LocalDate dateB = LocalDate.parse(b.get_Class().getDate());

            if (dateA.isEqual(dateB)) {
                LocalTime startTimeA = LocalTime.parse(a.get_Class().getStartTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
                LocalTime startTimeB = LocalTime.parse(b.get_Class().getStartTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));

                ZonedDateTime dateTimeA = ZonedDateTime.of(dateA, startTimeA, timezone);
                ZonedDateTime dateTimeB = ZonedDateTime.of(dateB, startTimeB, timezone);

                return dateTimeA.isBefore(dateTimeB) ? 1 : -1;
            }
            return dateA.isBefore(dateB) ? 1 : -1;
        });
    }

    private Map<String, String> getDateAndTime(Date fromTimeUTC, Date toTimeUTC) {

        ZoneId zoneUTC = ZoneId.of("UTC");
        ZoneId zoneIST = ZoneId.of("Asia/Kolkata");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss 'GMT'");
        DateTimeFormatter dateTimeFormatterAmPm = DateTimeFormatter.ofPattern("EEE, dd MMM yyyy HH:mm:ss a");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");

        Instant fromInstant = Instant.ofEpochMilli(fromTimeUTC.getTime());
        Instant toInstant = Instant.ofEpochMilli(toTimeUTC.getTime());

        LocalDate fromDate = fromInstant.atZone(zoneUTC).toLocalDate();
        LocalDateTime fromDateTime = fromInstant.atZone(zoneUTC).toLocalDateTime();
        LocalDateTime fromDateTimeIST = fromInstant.atZone(zoneIST).toLocalDateTime();
        LocalDateTime toDateTime = toInstant.atZone(zoneUTC).toLocalDateTime();

        LocalDateTime adjustedFromDateTime = fromDateTime.plusHours(5).plusMinutes(30);
        LocalDateTime adjustedToDateTime = toDateTime.plusHours(5).plusMinutes(30);

        LocalDateTime currentDateTimeUTC = LocalDateTime.now(zoneUTC);
        String currentTime = dateTimeFormatter.format(currentDateTimeUTC);

        String date = dateFormatter.format(fromDate);
        String startDateTimeUTC = dateTimeFormatter.format(fromDateTime);
        String startDateTimeIST = dateTimeFormatterAmPm.format(fromDateTimeIST);
        String endDateTimeUTC = dateTimeFormatter.format(toDateTime);
        String startTime = timeFormatter.format(adjustedFromDateTime);
        String endTime = timeFormatter.format(adjustedToDateTime);

        Map<String, String> result = new HashMap<>();
        result.put("date", date);
        result.put("startDateTimeUTC", startDateTimeUTC);
        result.put("startDateTimeIST", startDateTimeIST);
        result.put("endDateTimeUTC", endDateTimeUTC);
        result.put("startTime", startTime);
        result.put("endTime", endTime);
        result.put("currentTime", currentTime);
        return result;
    }

    private IdentityResponse getIdentityById(Long id) {
        return serviceInterfaces.identityServiceClient.getIdentityUsingGET(id);
    }

    private Trainer getDefaultTrainer() {
        return new Trainer("1", "Don't know");
    }

    private CenterManager getDefaultCenterManager() {
        return new CenterManager("2", "Don't know");
    }

    private List<Trainer> getGxTrainerDetails(CultBooking booking) {
        List<Trainer> trainers = new ArrayList<>();
        trainers.add(getDefaultTrainer());
        try {
            CultClassResponse bookingResponse = serviceInterfaces.cultServiceImpl.getCultClass(booking.getCultClass().getId().toString(), true, false, false, false, false, false).get();
            if (bookingResponse.getCultClass().getTrainerIDs() != null) {
                List<CompletableFuture<Trainer>> futures = bookingResponse.getCultClass().getTrainerIDs().stream()
                        .map(trainerId -> CompletableFuture.supplyAsync(() -> {
                            CultEmployeeResponse employeeDetails = serviceInterfaces.cultServiceImpl.getCultEmployeesById(Long.parseLong(trainerId));
                            if (employeeDetails.getEmployee() != null) {
                                return new Trainer(employeeDetails.getEmployee().getId(), employeeDetails.getEmployee().getName());
                            }
                            return null;
                        }))
                        .toList();
                trainers.addAll(futures.stream().map(CompletableFuture::join).filter(Objects::nonNull).toList());
            }
        } catch (Exception e) {
            log.error("getGxTrainerDetails::Booking issue: {}", e.getMessage(), e);
        }
        return trainers;
    }

    private List<Session> getGXSessions(String userId, int pageNumber, int pageSize, Boolean skipTrainerDetails) {
        Map<String, BulkBookingResponse> bookingResponseMap;
        try {
            bookingResponseMap = serviceInterfaces.cultService.getAllBooking(List.of(userId), LocalDate.now().minusDays(35).format(DateTimeFormatter.ISO_DATE), LocalDate.now().format(DateTimeFormatter.ISO_DATE), false, null).get(1200, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("getGXSessions::Error fetching cult bookings for userId: {}, error: {}", userId, e.getMessage(), e);
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(bookingResponseMap)) {
            return Collections.emptyList();
        }
        BulkBookingResponse bookingResponse = bookingResponseMap.get(userId);
        List<CultBooking> combinedList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bookingResponse.getBookings())) {
            combinedList.addAll(bookingResponse.getBookings());
        }
        if (!CollectionUtils.isEmpty(bookingResponse.getWaitlists())) {
            combinedList.addAll(bookingResponse.getWaitlists());
        }
        combinedList = combinedList.stream().filter(cultBooking -> !BookingLabel.CANCELLED.equals(cultBooking.getLabel())).collect(Collectors.toList());
        sortCultBooking(combinedList);
        int fromIndex = (pageNumber - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, combinedList.size());
        List<CultBooking> paginatedSessions = (fromIndex >= combinedList.size()) ? Collections.emptyList() : combinedList.subList(fromIndex, toIndex);
        return getGXSessionsCustomProperties(paginatedSessions, userId, skipTrainerDetails);
    }

    private List<Session> getPlaySessions(String userId, int pageNumber, int pageSize, Boolean skipTrainerDetails) {
        try {
            List<FTSBookings> userPlayBookings = serviceInterfaces.sportsApiService.getUserBookings(userId, String.valueOf(pageSize), String.valueOf(pageNumber), null);
            return getPlaySessionsCustomProperties(userPlayBookings, skipTrainerDetails);
        } catch (BaseException e) {
            log.error("getPlaySessions::Error while fetching play bookings for userId: {}, error: {}", userId, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    public SessionsResponse getSessionsInfo(Map<String, String> queryParams) throws Exception {
        String page = queryParams.get("page");
        String pageId = queryParams.get("pageId");
        String limit = queryParams.get("limit");
        String userId = queryParams.get("userId");
        SessionsResponse response = new SessionsResponse();
        List<Session> sessions = new ArrayList<>();
        switch (pageId) {
            case "gx" -> sessions = getGXSessions(userId, Integer.parseInt(page), Integer.parseInt(limit), false);
            case "gym" -> {
                List<Enums.CheckInState> stateList = new ArrayList<>(Arrays.asList(Enums.CheckInState.VALIDATED, Enums.CheckInState.CREATED));
                List<CheckIn> checkIns = serviceInterfaces.gymfitClient.checkInService().getCheckInsForUser(userId, Long.parseLong(limit), Long.parseLong(page), stateList);
                sessions = getGymSessionsCustomProperties(checkIns);
            }
            case "play" -> sessions = getPlaySessions(userId, Integer.parseInt(page), Integer.parseInt(limit), false);
            case "consultation" -> {
                ConsultationSearchRequestParamModel requestParamModel = getConsultationSearchRequestParamModel(page, limit, userId);
                List<CarefitConsultationOrderResponse> consultations = serviceInterfaces.getTransformClient().searchCareFitConsultationOrders(requestParamModel, userId);
                sessions = getTransformConsultationSessionsCustomProperties(consultations, userId);
            }
            case "onepass" -> {
                List<Enums.CheckInState> stateListOnepass = new ArrayList<>(Arrays.asList(Enums.CheckInState.VALIDATED, Enums.CheckInState.CREATED));
                List<CheckIn> checkInsOnepass = serviceInterfaces.gymfitClient.checkInService().getCheckInsForUserByCheckInType(userId, Long.parseLong(limit), Long.parseLong(page), stateListOnepass, "ONEPASS");
                sessions = getGymSessionsCustomProperties(checkInsOnepass);
            }
            case "home" -> sessions = getHomeSessionsCustomProperties(userId, page, limit);
            case "pt" -> {
                SessionSearchRequest searchRequest = SessionSearchRequest.builder()
                        .userIds(Collections.singletonList(userId))
                        .tenantId(com.curefit.cfapi.util.GymPtUtil.GYM_PT_SERVICE_TENANT_ID)
                        .pageNo((Integer.parseInt(page) - 1))
                        .pageSize(Integer.parseInt(limit))
                        .startDateSortOrder(com.curefit.personaltrainer.utils.Enums.SortOrder.DESC)
                        .build();
                Map<String, String> headers = new HashMap<>();
                headers.put("x-user-id", userId);
                List<SessionEntry> sessionEntries = serviceInterfaces.personalTrainerServiceClient.searchSession(searchRequest, headers);
                sessions = getPTSessionsCustomProperties(sessionEntries);
            }
        }
        response.setLimit(Long.parseLong(limit));
        response.setPageNumber(Long.parseLong(page));
        response.setSessions(sessions);
        response.setType(pageId);
        return response;
    }

    public ActivePackResponse getActivePacksInfo(UserContext userContext, Map<String, String> queryParams, Boolean skipSchedulingDetails) throws Exception {
        String page = queryParams.get("page");
        String limit = queryParams.get("limit");
        String userId = queryParams.get("userId");

        ActivePackResponse response = new ActivePackResponse();
        List<ActivePack> membershipsResponse = new ArrayList<>();

        UserProfile userProfile = new UserProfile();
        City city = new City();
        city.setCityId("bangalore");//change
        userProfile.setCity(city);
        userProfile.setUserId(userId);
        userProfile.setTimezone("Asia/Kolkata");
        userContext.setUserProfile(userProfile);
        AtomicInteger membershipCount = new AtomicInteger(0);

        List<Membership> memberships = MembershipWidgetAurora.getMemberships(serviceInterfaces, userContext).get();
        if (!CollectionUtils.isEmpty(memberships)) {
            memberships.forEach(membership -> {
                ActivePack properties = new ActivePack();
                ProductType productType = MembershipUtil.setProductTypeFromProductId(membership);
                if (productType == null) {
                    return;
                }
                MembershipState membershipState = getMembershipState(membership, userContext);
                if (membershipState != MembershipState.ACTIVE && membershipState != MembershipState.PAUSED && membershipState != MembershipState.PENDING_RENEWAL) {
                    return;
                }
                String cultSelectCenterName;
                try {
                    cultSelectCenterName = CultUtil.getSelectCenterNameFromMembership(membership, serviceInterfaces);
                } catch (BaseException e) {
                    throw new RuntimeException(e);
                }
                try {
                    properties.setTitle(MembershipWidgetAurora.getMembershipTitle(productType, membership, serviceInterfaces, !cultSelectCenterName.isEmpty(), userContext));
                } catch (BaseException exception) {
                    log.error("getActivePacksInfo::error {}", membership, exception);
                    properties.setTitle(membership.getName());
                }
                properties.setStatus(membershipState.toString());
                properties.setSubL1(getMembershipSubL1(productType, membership, serviceInterfaces, !cultSelectCenterName.isEmpty(), userContext));

                properties.setSubtitle(cultSelectCenterName);
                properties.setType(productType.toString());

                if (CultUtil.isCultpassXMembership(membership) || CultUtil.isLimitedSessionEliteMembership(membership)) {
                    properties.setSubtitle(CultUtil.getLimitedEliteSessionsLeftText(membership, userContext, false));
                }

                properties.setId(membership.getId().toString());
                DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                properties.setStartDate(Instant.ofEpochMilli(membership.getStart()).atZone(ZoneId.of(userProfile.getTimezone())).toLocalDate().format(dateFormatter));
                properties.setEndDate(Instant.ofEpochMilli(membership.getEnd()).atZone(ZoneId.of(userProfile.getTimezone())).toLocalDate().format(dateFormatter));
                properties.setImage("https://s3.ap-south-1.amazonaws.com/curefit-content/image/<EMAIL>");//later
                membershipCount.incrementAndGet();
                if (membershipsResponse.size() == Integer.parseInt(limit)) {
                    return;
                }
                if (membershipCount.get() <= ((Integer.parseInt(page) - 1) * Integer.parseInt(limit))) {
                    return;
                }
                membershipsResponse.add(properties);
            });
        }
        response.setLimit(Long.parseLong(limit));
        response.setPageNumber(Long.parseLong(page));
        response.setActivePacks(membershipsResponse);
        if (!skipSchedulingDetails) {
            this.setSchedulingDetails(response, userId);
        }
        return response;
    }

    private void setSchedulingDetails(ActivePackResponse activePackResponse, String userId) {
        try {
            UserAttributesResponse userAttributesResponse = this.userAttributesCacheClient.getAttributes(Long.parseLong(userId), List.of("location"), AppTenant.CUREFIT);
            if (userAttributesResponse == null || CollectionUtils.isEmpty(userAttributesResponse.getAttributes()) || Objects.isNull(userAttributesResponse.getAttributes().get("location"))) {
                log.error("LatLong isn't present in rashi for userId: {}", userId);
                return;
            }
            LatLong latLong = objectMapper.readValue(String.valueOf(userAttributesResponse.getAttributes().get("location")), new TypeReference<>() {
            });
            CenterSearchRequest centerSearchRequest = new CenterSearchRequest();
            centerSearchRequest.setLongitude(latLong.getLongitude());
            centerSearchRequest.setLatitude(latLong.getLatitude());
            centerSearchRequest.setOffset(0);
            centerSearchRequest.setLimit(5);
            centerSearchRequest.setStatuses(List.of(ACTIVE));
            centerSearchRequest.setSortBy(CenterSearchFilters.SortBy.DISTANCE);
            centerSearchRequest.setEnrichWithMapDistance(true);
            centerSearchRequest.setRadius(50.0);
            activePackResponse.setGxCenterDetails(getGxCenterDetails(centerSearchRequest));
            activePackResponse.setPlayCenterDetails(getPlayCenterDetails(centerSearchRequest));
        } catch (Exception e) {
            log.error("Error while computing scheduling details for userId {} ", userId, e);
        }
    }

    private List<Center> getGxCenterDetails(CenterSearchRequest centerSearchRequest) throws Exception {
        centerSearchRequest.setType(CULT_CENTER);
        centerSearchRequest.setTypes(List.of(CULT_CENTER));
        List<CenterEntry> centerEntries = serviceInterfaces.centerService.searchCenters(centerSearchRequest, null, null).get();
        centerEntries = centerEntries.stream().filter(center -> !center.getMeta().containsKey("isVirtualCenter") || !(Boolean) center.getMeta().get("isVirtualCenter")).toList();
        return centerEntries.stream().map(c -> makeCenterPayloadV2(c, CULT_CENTER)).toList();
    }

    private List<Center> getPlayCenterDetails(CenterSearchRequest centerSearchRequest) throws Exception {
        centerSearchRequest.setType(PLAY);
        centerSearchRequest.setTypes(List.of(PLAY));
        List<CenterEntry> centerEntries = serviceInterfaces.centerService.searchCenters(centerSearchRequest, null, null).get();
        centerEntries = centerEntries.stream().filter(center -> !center.getMeta().containsKey("isVirtualCenter") || !(Boolean) center.getMeta().get("isVirtualCenter")).toList();
        return centerEntries.stream().map(c -> makeCenterPayloadV2(c, PLAY)).toList();
    }

    public Center makeCenterPayloadV2(CenterEntry centerData, CenterSearchFilters.CenterType type) {
        Center response = new Center();
        response.setId(centerData.getId().toString());
        response.setName(centerData.getName());
        response.setLocality(centerData.getLocality());
        response.setCity(centerData.getCity());
        String imageURL = null;
        if (centerData.getCenterMedias() != null) {
            for (CenterMedia mediaNode : centerData.getCenterMedias()) {
                if (PRODUCT_BANNER.equals(mediaNode.getTag())) {
                    Collection<com.curefit.center.dtos.Media> mediaDetails = mediaNode.getMediaDetails();
                    if (mediaDetails != null && !mediaDetails.isEmpty()) {
                        for (com.curefit.center.dtos.Media detailNode : mediaDetails) {
                            if (detailNode.getType() == MediaType.IMAGE) {
                                imageURL = detailNode.getUrl();
                                break;
                            }
                        }
                    }
                }
            }
        }
        response.setImage(imageURL != null ? imageURL : "https://s3.ap-south-1.amazonaws.com/curefit-content/image/<EMAIL>");
        if (type.equals(CULT_CENTER)) {
            response.setWorkouts(getGXWorkouts());
        } else if (type.equals(PLAY)) {
            response.setWorkouts(getPlayWorkouts());
        }
        return response;
    }

    private List<Workout> getGXWorkouts() {
        return List.of(
                new Workout("1", "Adidas Strength+", "https://cdn-images.cure.fit/www-curefit-com/image/upload/v1/cult-media/v2web/workouts/447_id/PRODUCT_BNR_2025-01-19T11:33:45.564Z.png", getTimeSlots()),
                new Workout("2", "HRX Workout", "https://cdn-images.cure.fit/www-curefit-com/image/upload/v1/cult-media/v2web/workouts/22_id/PRODUCT_BNR_2021-11-03T08:02:26.795Z.png", getTimeSlots()),
                new Workout("3", "Dance Fitness", "https://cdn-images.cure.fit/www-curefit-com/image/upload/v1/cult-media/v2web/workouts/84_id/PRODUCT_BNR_2021-11-02T10:55:56.704Z.png", getTimeSlots()),
                new Workout("4", "Boxing", "https://cdn-images.cure.fit/www-curefit-com/image/upload/v1/cult-media/v2web/workouts/308_id/PRODUCT_BNR_2021-11-03T07:07:08.462Z.png", getTimeSlots()),
                new Workout("5", "Yoga", "https://cdn-images.cure.fit/www-curefit-com/image/upload/v1/cult-media/v2web/workouts/361_id/PRODUCT_BNR_2023-02-22T11:07:56.797Z.png", getTimeSlots())
        );
    }

    private List<Workout> getPlayWorkouts() {
        return List.of(
                new Workout("6", "Swimming", "https://s3.ap-south-1.amazonaws.com/curefit-content/image/icons/fitsoImages/swimming-bs.png", getTimeSlots()),
                new Workout("7", "Badminton", "https://s3.ap-south-1.amazonaws.com/curefit-content/image/<EMAIL>", getTimeSlots())
        );
    }

    private List<TimeSlot> getTimeSlots() {
        return List.of(
                new TimeSlot("1", "6AM", "9AM"),
                new TimeSlot("2", "9AM", "12PM"),
                new TimeSlot("3", "3PM", "6PM"),
                new TimeSlot("4", "6PM", "10PM")
        );
    }

    public UserResponse getUserDetailsResponse(HttpServletRequest request, String userId) throws Exception {
        CompletableFuture<UserEntry> userEntryResponse = this.userService.getUser(userId).exceptionally(error -> {
            log.error("[USER_DETAILS] Error while fetching user entry for userId: {} {}", userId, error.getMessage(), error);
            return null;
        });
        UserEntry userEntry = userEntryResponse.get();
        UserResponse.UserResponseBuilder userResponseBuilder = UserResponse.builder()
                .userId(userId)
                .name(userEntry.getFirstName() + " " + userEntry.getLastName())
                .phone(userEntry.getPhone())
                .email(userEntry.getEmail());
        UserAttributesResponse response = this.userAttributesCacheClient.getAttributes(Long.parseLong(userId), null, AppTenant.CUREFIT);
        Map<String, Object> userAttributesMap = response.getAttributes();
        populatePreferredCenter(userAttributesMap, userResponseBuilder);
        populateLastSessions(userAttributesMap, userResponseBuilder);

        Map<String, String> queryParams = new HashMap<>();
        UserContext userContext = (UserContext) request.getAttribute("userContext");
        queryParams.put("page", "1");
        queryParams.put("limit", "10");
        queryParams.put("userId", userId);

        List<ActivePack> activePacks = this.getActivePacksInfo(userContext, queryParams, true).getActivePacks();
        userResponseBuilder.activePacks(activePacks);
        userResponseBuilder.cultCity(String.valueOf(userAttributesMap.getOrDefault("cultcity", "")));
        userResponseBuilder.corporateCode(String.valueOf(userAttributesMap.getOrDefault("corporatecode", "")));
        return userResponseBuilder.build();
    }

    private void populateLastSessions(Map<String, Object> userAttributesMap, UserResponse.UserResponseBuilder userResponseBuilder) {
        try {
            String lastCultClassDetailsString = String.valueOf(userAttributesMap.get("last_cult_class_details"));
            Map<String, Object> lastCultClassDetailsMap = objectMapper.readValue(lastCultClassDetailsString, Map.class);
            userResponseBuilder.lastGxSession(lastCultClassDetailsMap);
        } catch (Exception e) {
            log.error("[USER_DETAILS] LastCultClassDetailsError: " + e.getMessage());
        }

        try {
            String lastGymSessionDetailsString = String.valueOf(userAttributesMap.get("last_gym_session_details"));
            Map<String, Object> lastGymSessionDetailsMap = objectMapper.readValue(lastGymSessionDetailsString, Map.class);
            userResponseBuilder.lastGymSession(lastGymSessionDetailsMap);
        } catch (Exception e) {
            log.error("[USER_DETAILS] LastGymSessionDetailsError: " + e.getMessage());
        }

        try {
            String lastPlaySessionDetailsString = String.valueOf(userAttributesMap.get("last_play_session_details"));
            Map<String, Object> lastPlaySessionDetailsMap = objectMapper.readValue(lastPlaySessionDetailsString, Map.class);
            userResponseBuilder.lastPlaySession(lastPlaySessionDetailsMap);
        } catch (Exception e) {
            log.error("[USER_DETAILS] LastPlaySessionDetailsError: " + e.getMessage());
        }
    }

    private void populatePreferredCenter(Map<String, Object> userAttributesMap, UserResponse.UserResponseBuilder userResponseBuilder) {
        try {
            long preferredCenterId = Long.parseLong(String.valueOf(userAttributesMap.getOrDefault("preferred_center", null)));
            CenterEntry preferredCenterEntry = this.serviceInterfaces.centerService.getCenterDetails(preferredCenterId, true, Collections.emptyMap(), null).get();
            Center centerDetails = makeCenterPayload(preferredCenterEntry);
            userResponseBuilder.preferredCultCenter(centerDetails);
        } catch (Exception e) {
            log.error("[USER_DETAILS] PreferredCenterError: " + e.getMessage());
        }

        try {
            long playPreferredCenterId = Long.parseLong(String.valueOf(userAttributesMap.getOrDefault("play_preferred_center", null)));
            CenterEntry playPreferredCenterEntry = this.serviceInterfaces.centerService.getCenterDetails(playPreferredCenterId, true, Collections.emptyMap(), null).get();
            Center centerDetailsPlay = makeCenterPayload(playPreferredCenterEntry);
            userResponseBuilder.preferredPlayCenter(centerDetailsPlay);
        } catch (Exception e) {
            log.error("[USER_DETAILS] PlayPreferredCenterError: " + e.getMessage());
        }
    }

    public Center makeCenterPayload(CenterEntry centerData) {
        Center response = new Center();
        response.setId(centerData.getId().toString());
        response.setName(centerData.getName());
        response.setLocality(centerData.getLocality());
        response.setAddress(centerData.getFullAddress1() + centerData.getFullAddress2());
        if (centerData.getLatitude() != null && centerData.getLongitude() != null) {
            response.setLatitude(centerData.getLatitude().toString());
            response.setLongitude(centerData.getLongitude().toString());
        }
        response.setCity(centerData.getCity());
        String imageURL = null;

        if (centerData.getCenterMedias() != null) {
            for (CenterMedia mediaNode : centerData.getCenterMedias()) {
                if ("CLP".equals(mediaNode.getTag().toString())) {
                    Collection<com.curefit.center.dtos.Media> mediaDetails = mediaNode.getMediaDetails();
                    if (mediaDetails != null && !mediaDetails.isEmpty()) {
                        for (com.curefit.center.dtos.Media detailNode : mediaDetails) {
                            if (detailNode.getType() == MediaType.IMAGE) {
                                imageURL = detailNode.getUrl();
                                break;
                            }
                        }
                    }
                }
            }
        }
        response.setImage((imageURL != null ? imageURL : "https://s3.ap-south-1.amazonaws.com/curefit-content/image/<EMAIL>"));
        response.setCenterType(centerData.getCategory());

        CenterEmployee data = getCenterCMsAndTrainers(centerData.getId());
        response.setCmDetails(data.getCenterManagers());
        return response;
    }

    private CenterEmployee getCenterCMsAndTrainers(Number centerId) {
        List<CenterManager> cms = new ArrayList<>();
        List<Trainer> trainers = new ArrayList<>();
        cms.add(getDefaultCenterManager());
        trainers.add(getDefaultTrainer());
        try {
            HttpClient client = HttpClient.newHttpClient();
            String body = String.format("{\"centerServiceId\": %d}", centerId);
            String url = this.fmsServiceBaseUrl + "v1/fitnessResource/find?pageNo=1&pageSize=20";
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(new URI(url))
                    .header("accept", "application/json")
                    .header("content-type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            HttpResponse<String> responseEmployees = client.send(request, HttpResponse.BodyHandlers.ofString());
            if (responseEmployees.statusCode() == 200) {
                extractAndPopulateList(responseEmployees.body(), cms, trainers);
            }
        } catch (Exception e) {
            log.error("makeCenterPayload fetch CM issue: {}", e.getMessage(), e);
        }
        CenterEmployee response = new CenterEmployee();
        response.setCenterManagers(cms);
        response.setTrainers(trainers);
        return response;
    }

    private void extractAndPopulateList(String responseBody, List<CenterManager> cms, List<Trainer> trainers) throws Exception {
        JsonNode rootNode = objectMapper.readTree(responseBody);
        JsonNode contentNode = rootNode.path("content");

        if (contentNode.isArray()) {
            for (JsonNode node : contentNode) {
                String primaryRole = node.path("primaryRole").asText();
                if ("CENTER_MANAGER".equals(primaryRole)) {
                    CenterManager cm = new CenterManager();
                    cm.setId(String.valueOf(node.path("id").asInt()));
                    cm.setName(node.path("name").asText());
                    cms.add(cm);
                } else if ("PERSONAL_TRAINER".equals(primaryRole) || "FLOOR_TRAINER".equals(primaryRole) || "GROUP_TRAINER".equals(primaryRole)) {
                    Trainer trainer = new Trainer();
                    trainer.setId(String.valueOf(node.path("id").asInt()));
                    trainer.setName(node.path("name").asText());
                    trainers.add(trainer);
                }
            }
        }
    }

    private MembershipState getMembershipState(Membership membership, UserContext userContext) {
        LocalDate today = TimeUtil.getDateNow(userContext.getUserProfile().getTimezone()).toLocalDate();
        LocalDate endDate = TimeUtil.getDateFromTime(membership.getEnd(), userContext.getUserProfile().getTimezone()).toLocalDate();
        LocalDate startDate = TimeUtil.getDateFromTime(membership.getStart(), userContext.getUserProfile().getTimezone()).toLocalDate();
        int numDaysToEndFromToday = TimeUtil.getDaysInBetween(today, endDate);

        switch (membership.getStatus()) {
            case PURCHASED: {
                if (startDate.isAfter(today)) {
                    return MembershipState.ACTIVE;
                }
                if (endDate.isBefore(today) && ChronoUnit.DAYS.between(today, endDate) <= 60) {
                    return MembershipState.PENDING_RENEWAL;
                }
                if (endDate.isBefore(today)) {
                    return MembershipState.EXPIRED;
                }
                return MembershipState.ACTIVE;
            }
            case SUSPENDED:
            case CANCELLED: {
                return MembershipState.CANCELLED;
            }
            case PAUSED: {
                return MembershipState.PAUSED;
            }
            default:
                return MembershipState.CANCELLED;
        }
    }

    public String getMembershipSubL1(ProductType productType, Membership membership, ServiceInterfaces interfaces, Boolean isCultSelect, UserContext userContext) {
        boolean isLimitedMembership = CultUtil.isLimitedSessionEliteMembership(membership);
        boolean isCultpassXMembership = CultUtil.isCultpassXMembership(membership);
        if (isEnterpriseMembership(membership)) {
            return "Corporate";
        } else if (productType == ProductType.PLAY) {
            if (PlayUtil.isEnterpriseLimitedSessionPlayMembership(membership)) {
                return "Corporate";
            }
            return "Play";
        } else if (isLimitedMembership) {
            return "Elite";
        } else if (isCultpassXMembership) {
            return "Onepass";
        } else if (isCultSelect) {
            return "Select";
        } else if (productType == ProductType.FITNESS) {
            return "Elite";
        } else if (productType == ProductType.GYMFIT_FITNESS_PRODUCT) {
            return "Pro";
        } else if (productType == ProductType.GYM_PT_PRODUCT) {
            return "PT";
        } else if (productType == ProductType.LUX_FITNESS_PRODUCT) {
            return "";
        } else if (productType == ProductType.ONEPASS_PRODUCT) {
            return "Onepass";
        } else if (productType == ProductType.BUNDLE) {
            String subCategoryCode = TransformUtil.getTransformSubCategoryCode(membership);
            if (membership.getProductId().contains("TRANSFORM_NC")) {
                return "Transform";
            }
            return switch (subCategoryCode) {
                case "BOOTCAMP" -> "Bootcamp";
                case "TRANSFORM_PLUS" -> "TransformPlus";
                default -> "Transform";
            };
        }
        return "Home";
    }

    private boolean isEnterpriseMembership(Membership currentMembership) {
        return currentMembership != null && currentMembership.getMetadata() != null &&
                ((currentMembership.getMetadata().containsKey("limitedSessions") && currentMembership.getMetadata().get("limitedSessions").equals(true)) ||
                        (currentMembership.getMetadata().containsKey("source") && currentMembership.getMetadata().get("source").equals("ENTERPRISE")));
    }

    public void processSprinklrWebhook(SprinklrTicketWebhookRequest sprinklrTicketWebhookRequest) {
        this.serviceInterfaces.sprinklrTicketService.processSprinklrWebhook(sprinklrTicketWebhookRequest);
    }

    public void processSprinklrCommentWebhook(SprinklrCommentWebhookRequest sprinklrCommentWebhookRequest) {
        this.serviceInterfaces.sprinklrTicketService.processSprinklrCommentWebhook(sprinklrCommentWebhookRequest);
    }

    public void processSprinklrMessageWebhook(SprinklrMessageWebhookRequest sprinklrMessageWebhookRequest) {
        this.serviceInterfaces.sprinklrTicketService.processSprinklrMessageWebhook(sprinklrMessageWebhookRequest);
    }

    public UserDetails getUserDetails(UserDetailsRequest userDetailsRequest, UserContext userContext) throws Exception {
        String userId = userDetailsRequest.getUserId();
        userContext = enrichUserContext(userContext, userId);

        UserDetails userDetails = new UserDetails();
        userDetails.setUserId(userId);
        if (!userDetailsRequest.getSkipGXSessions()) {
            userDetails.setGxSessions(getGXSessions(userId, userDetailsRequest.getPage(), userDetailsRequest.getLimit(), true));
            userDetails.setGxNoShowCount(NoShowUtil.getCultNoShowCount(serviceInterfaces.featureStateCache, userContext));
        }
        if (!userDetailsRequest.getSkipPlaySessions()) {
            userDetails.setPlaySessions(getPlaySessions(userId, userDetailsRequest.getPage(), userDetailsRequest.getLimit(), true));
            userDetails.setPlayNoShowCount(PlayNoShowUtil.getCultNoShowCount(serviceInterfaces.featureStateCache, userContext));
        }
        if (!userDetailsRequest.getSkipGXRevertedNoShows()) {
            userDetails.setRevertedGXNoShows(heimdallMongoClient.getCultRevertedNoShowLogs(Long.valueOf(userId)));
        }
        if (!userDetailsRequest.getSkipPlayRevertedNoShows()) {
            userDetails.setRevertedPlayNoShows(heimdallMongoClient.getPlayRevertedNoShowLogs(Long.valueOf(userId)));
        }
        if (!userDetailsRequest.getSkipMembershipDetails()) {
            List<MembershipDetail> membershipDetails = new ArrayList<>();
            List<Membership> memberships = MembershipWidgetAurora.getMemberships(serviceInterfaces, userContext).get();
            for (Membership membership : memberships) {
                MembershipDetail membershipDetail = getMembershipDetailFromCoreMembership(membership, userContext);
                if (membershipDetail != null) {
                    membershipDetails.add(membershipDetail);
                }
            }
            userDetails.setMemberships(membershipDetails);
        }

        return userDetails;
    }

    private MembershipDetail getMembershipDetailFromCoreMembership(Membership membership, UserContext userContext) {
        try {
            String timezone = userContext.getUserProfile().getTimezone();
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            MembershipDetail membershipDetail = new MembershipDetail();
            membershipDetail.setMembershipServiceRefId(membership.getId());
            membershipDetail.setName(membership.getName());
            membershipDetail.setStartDate(Instant.ofEpochMilli(membership.getStart()).atZone(ZoneId.of(timezone)).toLocalDate().format(dateFormatter));
            membershipDetail.setEndDate(Instant.ofEpochMilli(membership.getEnd()).atZone(ZoneId.of(timezone)).toLocalDate().format(dateFormatter));
            membershipDetail.setOrderId(membership.getOrderId());
            membershipDetail.setStatus(MembershipUtil.getMembershipState(membership, userContext).toString());
            membershipDetail.setProductType(MembershipUtil.setProductTypeFromProductId(membership));

            if (membershipDetail.getProductType() == null || ProductType.BUNDLE.equals(membershipDetail.getProductType())) {
                return membershipDetail;
            }

            try {
                MembershipDetailPageViewBuilder membershipDetailPageViewBuilder = new MembershipDetailPageViewBuilder();
                membershipDetailPageViewBuilder.buildView(userContext, getMembershipViewQueryParams(membership), serviceInterfaces, true);
                membershipDetail.setMembershipType(membershipDetailPageViewBuilder.getUserMembershipType().toString());
                membershipDetail.setBenefits(membershipDetailPageViewBuilder.getMembershipBenefitsItem());
                membershipDetail.setCityId(membershipDetailPageViewBuilder.getMembershipCity().getCityId());
                membershipDetail.setMembershipId(membershipDetailPageViewBuilder.getVerticalSpecificMembershipId());
                if (membershipDetailPageViewBuilder.getAccessCenter() != null) {
                    Center center = new Center(membershipDetailPageViewBuilder.getAccessCenter().getId(), membershipDetailPageViewBuilder.getAccessCenter().getName());
                    membershipDetail.setAccessCenter(center);
                }
            } catch (Exception e) {
                log.error("Error while populating details from MembershipDetailPageViewBuilder for userId: {}, membershipId: {}, error: {}", userContext.getUserProfile().getUserId(), membership.getId(), e.getMessage(), e);
            }
            return membershipDetail;
        } catch (Exception e) {
            log.error("Error while converting membership for userId: {}, membershipId: {}, error: {}", userContext.getUserProfile().getUserId(), membership.getId(), e.getMessage(), e);
            return null;
        }
    }

    private Map<String, String> getMembershipViewQueryParams(Membership membership) {
        return Map.of("membershipServiceId", membership.getId().toString(), "addDebugLogs", "sure");
    }

    private UserContext enrichUserContext(UserContext userContext, String userId) {
        if (Objects.isNull(userContext)) {
            userContext = new UserContext();
        }
        UserProfile userProfile = new UserProfile();
        userProfile.setUserId(userId);
        userProfile.setTimezone("Asia/Kolkata");
        City city = new City();
        city.setCityId("Bangalore");  // Fetch from rashi
        userProfile.setCity(city);
        userContext.setUserProfile(userProfile);
        if (Objects.isNull(userContext.getRequestCache())) {
            RequestCache requestCache = new RequestCache(serviceInterfaces);
            userContext.setRequestCache(requestCache);
        }
        return userContext;
    }
}