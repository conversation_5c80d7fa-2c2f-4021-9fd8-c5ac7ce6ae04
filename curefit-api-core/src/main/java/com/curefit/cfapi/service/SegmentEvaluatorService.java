package com.curefit.cfapi.service;

import com.curefit.cfapi.cache.SegmentCache;
import com.curefit.cfapi.model.internal.segment.ConditionEvaluatorRequest;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.segmentevaluator.BaseEvaluator;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.FutureUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SegmentEvaluatorService {

    final BaseEvaluatorFactory baseEvaluatorFactory;
    final SegmentCache segmentCache;
    final AsyncListenableTaskExecutor taskExecutor;
    final ExceptionReportingService exceptionReportingService;

    @Autowired
    @Lazy
    SegmentEvaluatorService self;

    @Autowired
    public SegmentEvaluatorService(
            BaseEvaluatorFactory baseEvaluatorFactory,
            SegmentCache segmentCache,
            ExceptionReportingService exceptionReportingService,
            @Qualifier("cfApiTaskExecutor") AsyncListenableTaskExecutor taskExecutor
    ) {
        this.baseEvaluatorFactory = baseEvaluatorFactory;
        this.segmentCache = segmentCache;
        this.taskExecutor = taskExecutor;
        this.exceptionReportingService = exceptionReportingService;
    }

    @Async
    public CompletableFuture<Segment> checkCondition(List<String> segmentIds, UserContext userContext) {
        return checkCondition(segmentIds, userContext, false);
    }

    public Segment checkConditionSync(List<String> segmentIds, UserContext userContext) throws ExecutionException, InterruptedException {
        return checkCondition(segmentIds, userContext, false).get();
    }

    @Async
    public CompletableFuture<Segment> checkCondition(List<String> segmentIds, UserContext userContext, boolean isDebugEnabled) {
        if (CollectionUtils.isEmpty(segmentIds)) {
            return CompletableFuture.completedFuture(null);
        }
        try {
            for (String segmentId : segmentIds) {
                Segment segment = this.checkSegmentCondition(segmentId, userContext, false, isDebugEnabled).get();
                if (isDebugEnabled) {
                    log.info("SegmentEvaluatorService:checkCondition:: segmentId:{} evaluated as valid:{} for userId:{}",
                            segmentId, segment != null, userContext.getUserProfile().getUserId());
                }
                if(segment != null) {
                    return CompletableFuture.completedFuture(segment);
                }
            }
        } catch (Exception e) {
            this.exceptionReportingService.reportException("Error in segment evaluation: " , e);
        }
        return CompletableFuture.completedFuture(null);
    }

    public Map<String,Segment> getSegmentConditionMap(List<String> segmentIds, UserContext userContext) throws ExecutionException, InterruptedException {
        return this.getSegmentConditionMap(segmentIds, userContext, false);
    }

    public Map<String,Segment> getSegmentConditionMap(List<String> segmentIds, UserContext userContext, boolean skipHamletEvaluation) throws ExecutionException, InterruptedException {
        if (CollectionUtils.isEmpty(segmentIds)) {
            return Collections.emptyMap();
        }
        Map<String, Segment> res = new HashMap<>();
        for (String segmentId : segmentIds) {
            res.put(segmentId, this.checkSegmentCondition(segmentId, userContext, skipHamletEvaluation).get());
        }
        return res;
    }

    public CompletableFuture<Segment> checkSegmentCondition(String segmentId, UserContext userContext, boolean skipHamletEvaluation) {
        return checkSegmentCondition(segmentId, userContext, skipHamletEvaluation, false);
    }

    public CompletableFuture<Segment> checkSegmentCondition(String segmentId, UserContext userContext, boolean skipHamletEvaluation,
                                                            boolean isDebugEnabled) {
        if (StringUtils.isEmpty(segmentId)) {
            return CompletableFuture.completedFuture(null);
        }
        try {
            Segment segment = segmentCache.getSegment(segmentId, AppUtil.bypassCache(userContext)).get();
            if (segment == null) {
                return CompletableFuture.completedFuture(null);
            }
            if (skipHamletEvaluation) {
                segment.setHamlet(null);
            }
            Set<BaseEvaluator> evaluators = baseEvaluatorFactory.getEvaluatorsEligibleForSegment(segment);
            List<CompletableFuture<Boolean>> conditionResultPromises = new ArrayList<>();

            List<Pair<BaseEvaluator, ConditionEvaluatorRequest>> allEvaluatorRequestsForUser = new ArrayList<>();
            for (BaseEvaluator evaluator : evaluators) {
                List<ConditionEvaluatorRequest> evaluatorRequestsForUser = evaluator.getEvaluatorRequestsForUser(segment, userContext);
                for (ConditionEvaluatorRequest evaluatorRequest : evaluatorRequestsForUser) {
                    conditionResultPromises.add(evaluator.checkCondition(evaluatorRequest, segment));
                    allEvaluatorRequestsForUser.add(Pair.of(evaluator, evaluatorRequest));
                }
            }
            CompletableFuture<List<Boolean>> resultPromise = FutureUtil.allOf(conditionResultPromises);
            if (isDebugEnabled) {
                log.info("SegmentEvaluatorService:checkSegmentCondition:: evaluating for segmentId:{} for userId:{}, evaluators:{}, results:{}",
                        segmentId, userContext.getUserProfile().getUserId(), allEvaluatorRequestsForUser.stream()
                                .map(it -> it.getLeft().getType() + "=>" + it.getRight().getType().toString() + "|" + StringUtils.join(it.getRight().getValues()))
                                .collect(Collectors.joining("; ")), resultPromise.get());
            }
            for (Boolean checkCondition : resultPromise.get()) {
                if (BooleanUtils.isNotTrue(checkCondition)) {
                    return CompletableFuture.completedFuture(null);
                }
            }
            return CompletableFuture.completedFuture(segment);
        } catch (Exception e) {
            this.exceptionReportingService.reportException("Error while evaluating segments", e);
            return CompletableFuture.completedFuture(null);
        }
    }
}
