package com.curefit.cfapi.service.onyx;

import com.curefit.cfapi.pojo.onyx.OnyxCoachUpsellFooter;
import com.curefit.cfapi.pojo.onyx.OnyxCoachUpsellResponse;
import com.curefit.cfapi.pojo.onyx.OnyxCoachUpsellSection;
import com.curefit.cfapi.pojo.onyx.OnyxWorkoutCatalog;
import com.curefit.cfs.pojo.UserFormEntry;
import com.curefit.common.data.exception.BaseException;
import com.curefit.hercules.pojo.GymWorkoutSplit;
import com.curefit.onyx.client.OnyxApiClient;
import com.curefit.onyx.pojo.constants.Constants;
import com.curefit.onyx.pojo.dto.WorkoutExtended;
import com.curefit.onyx.pojo.entry.metrics.Session;
import com.curefit.onyx.pojo.entry.UserStreakDetails;
import com.curefit.onyx.pojo.entry.*;
import com.curefit.onyx.pojo.entry.activity.Activity;
import com.curefit.onyx.pojo.entry.conversation.TwilioConversationToUserMap;
import com.curefit.onyx.pojo.enums.DifficultyLevel;
import com.curefit.onyx.pojo.enums.WorkoutCategory;
import com.curefit.onyx.pojo.entry.conversation.TwilioAccessToken;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import response.AllWorkoutResponse;

import java.util.*;

@Service
@Slf4j
public class OnyxService {
    final OnyxApiClient onyxApiClient;
    final List<OnyxCoachUpsellSection> coachUpsellSections = List.of(
        new OnyxCoachUpsellSection(
            "Achieve your goals with Onyx Coach",
            "Partner with a world-class personal trainer to guide, motivate, and personalize your path to amazing results.",
            "image/onyx/coach-upsell/onyx1.png"
            ),
        new OnyxCoachUpsellSection(
            "Real Experts.\nReal Connection.",
            "Whether it’s a live video call to review your progress or a quick text to get you motivated, together with an Onyx coach you will build a partnership towards a healthy lifestyle.",
            "image/onyx/coach-upsell/onyx2.png",
            true
        ),
        new OnyxCoachUpsellSection(
            "Personalized For You",
            "Your needs are unique. Thats why your coach uses data from every workout to analyze your performance, find areas for improvement, and design your workout plan every week.",
            "image/onyx/coach-upsell/onyx3.png"
        ),
        new OnyxCoachUpsellSection(
            "Lasting, Positive Change",
            "We know sticking with it is hard. Your coach will help you reach your goals and maintain them for good, so you can create the lifestyle you've always wanted.",
            "image/onyx/coach-upsell/onyx4.png"
        )
    );
    final List<OnyxCoachUpsellSection> coachUpsellfooterItems = List.of(
        new OnyxCoachUpsellSection(
            "Select a coach",
            "We’ll recommend the best coaches for you based on their training style and expertise, and your unique goals. If it’s not a good fit, we’ll help you find someone even better."
        ),
        new OnyxCoachUpsellSection(
            "Build a plan",
            "Your coach will work with you to develop the perfect fitness plan every week based on your needs and abilities."
        ),
        new OnyxCoachUpsellSection(
            "Sweat it out",
            "Whether you like strength or cardio, dumbbells or bodyweight, your coach will create custom workouts that adapt to your performance in real time."
        ),
        new OnyxCoachUpsellSection(
            "Track your progress",
            "Our intelligent 3D tracking provides unparalleled visibility into your perfornance, helping you and your coach understand your progress and reach your goals even faster."
        )
    );

    @Autowired
    public OnyxService(final OnyxApiClient onyxApiClient) {
        this.onyxApiClient = onyxApiClient;
    }

    public UserStreakDetails getWeeklyStreakDetails(String userId, String timezone) throws BaseException {
        return onyxApiClient.getWeeklyStreakDetails(userId, timezone);
    }

    public OnyxWorkoutCatalog getAllOnyxWorkout(List<String> goals, DifficultyLevel intensity, int count, String userId) {
        AllWorkoutResponse response;
        List<WorkoutExtended> catalogWorkoutList = new ArrayList<>();
        Map<WorkoutCategory, ArrayList<String> > genre =  new HashMap<>();
        Map<String, WorkoutExtended> workoutMap = new HashMap<>();
        List<String> display = new ArrayList<>();
        List<String> recommendations = new ArrayList<>();
        OnyxWorkoutCatalog onyxWorkoutCatalog = new OnyxWorkoutCatalog();
        try {
            response = onyxApiClient.getAllExtendedWorkouts(goals, intensity, count, userId);
            catalogWorkoutList = response.getWorkouts();
            recommendations = response.getWorkoutRecommendationIds();
        } catch (BaseException e) {
            log.error("Extended Workout Call Failed" + e.getMessage());
        }
        for (WorkoutExtended workout : catalogWorkoutList) {
            try {
                String workoutId = workout.getWorkoutId();
                List<WorkoutCategory> categories = workout.getCategories();
                workout.setCategory(categories.get(0));
                WorkoutCategory category = workout.getCategory();
                if (genre.containsKey(category)) {
                    genre.get(category).add(workoutId);
                } else {
                    genre.put(category, new ArrayList<>(List.of(workoutId)));
                }
                workoutMap.put(workoutId,workout);
                display.add(workoutId);
            }
            catch (Exception e) {
                log.error("WorkoutId : " + workout.getWorkoutId() + " " + e.getMessage());
            }
        }
        onyxWorkoutCatalog.setDisplayList(display);
        Map<WorkoutCategory, ArrayList<String> > sortedGenre =  new LinkedHashMap<>();
        //TODO deprecate genre order as linked hash map doesn't preserve order after serialization.
        Constants.WORKOUT_CATEGORIES_ORDER.forEach(workoutCategory -> {
            if (genre.containsKey(workoutCategory)) {
                sortedGenre.put(workoutCategory, genre.get(workoutCategory));
            }
        });
        onyxWorkoutCatalog.setGenre(sortedGenre);
        onyxWorkoutCatalog.setWorkoutMap(workoutMap);
        onyxWorkoutCatalog.setRecommendations(recommendations);
        onyxWorkoutCatalog.setGenreOrder(Constants.WORKOUT_CATEGORIES_ORDER);
        return onyxWorkoutCatalog;
    }

    public Session saveWorkoutSession(Session session) throws BaseException {
        return this.onyxApiClient.createSession(session);
    }

    public OnboardingUserBasicInfo saveOnboardingUserBasicInfo(final OnboardingUserBasicInfo onboardingUserBasicInfo)
        throws Exception {
        return this.onyxApiClient.createOnboardingUserBasicInfo(onboardingUserBasicInfo);
    }

    public OnboardingUserBasicInfo getOnboardingUserBasicInfo(final String userId)
            throws Exception {
        return this.onyxApiClient.getOnboardingUserBasicInfoByUserId(userId);
    }

    public OnyxCoachUpsellResponse getOnyxCoachUpsellResponse() {
        var footer = new OnyxCoachUpsellFooter("How it works", this.coachUpsellfooterItems);
        var res = new OnyxCoachUpsellResponse(this.coachUpsellSections, footer);

        return res;
    }

    public UserFormEntry createNewUserForm(final CreateNewUserForm createNewUserForm)
            throws BaseException {
        return this.onyxApiClient.createNewUserForm(createNewUserForm);
    }

    public CFSUserFormEntry submitCFSUserForm(final CFSUserFormEntry userFormEntryCFS)
            throws BaseException {
        return this.onyxApiClient.submitCFSUserForm(userFormEntryCFS);
    }

    public CFSOnyxFormEntry getFormById(final String formId) throws BaseException {
        return this.onyxApiClient.getFormById(formId);
    }

    public List<Coach> getAllCoaches() throws BaseException {
        return this.onyxApiClient.getAllCoaches();
    }

    public Coach getCoach(final String coachId) throws BaseException {
        return this.onyxApiClient.getCoach(coachId);
    }

    public OnyxCoachMembership getActiveOnyxCoachMembership(final String userId) throws Exception {
        return this.onyxApiClient.getActiveOnyxCoachMembership(userId);
    }

    public TwilioAccessToken saveAccessToken(final TwilioAccessToken twilioAccessToken) throws BaseException {
        return this.onyxApiClient.saveAccessToken(twilioAccessToken);
    }

    public String getAccessToken(final String userId) throws BaseException {
        return this.onyxApiClient.getAccessToken(userId);
    }

    public TwilioConversationToUserMap saveConversationWithParticipants(final TwilioConversationToUserMap
                                                                                conversationToUserMap)
            throws BaseException {
        return this.onyxApiClient.saveConversationToUserMap(conversationToUserMap);
    }

    public TwilioConversationToUserMap getConversationToUserMap(final String userId) throws BaseException {
        return this.onyxApiClient.getConversationToUserMap(userId);
    }

    public List<Coach> getRecommendedCoaches(final String userId)
            throws BaseException {
        return this.onyxApiClient.getRecommendedCoaches(userId);
    }

    public List<UserWorkoutPlanDetailsClient> getUserPlan(String userId, String startDate, String endDate, int offset, int limit, String sortBy, String sortOrder)
            throws Exception {
        Map<String, GymWorkoutSplit> gymWorkoutSplit = this.onyxApiClient.getGymWorkoutSplit();
        List<Coach> coachList = this.onyxApiClient.getAllCoaches();

        List<UserWorkoutPlanDetails> userPlanDetails = this.onyxApiClient.getMicrocyclesWithDate(userId, startDate, endDate, offset, limit, sortOrder, sortBy);
        List<UserWorkoutPlanDetailsClient> userWorkoutPlanDetailsList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(userPlanDetails)) {
            userPlanDetails.forEach(userWorkoutPlanDetails -> {
                UserWorkoutPlanDetailsClient userWorkoutPlanDetailsClient = new UserWorkoutPlanDetailsClient();
                MicroCycleEntryClient microCycleEntryClient = new MicroCycleEntryClient();
                List<UserWodEntryClient> userWodEntryClientList = new ArrayList<>();

                microCycleEntryClient.setStartDate(userWorkoutPlanDetails.getMicroCycleEntry().getStartDate());
                microCycleEntryClient.setId(userWorkoutPlanDetails.getMicroCycleEntry().getId());

                Coach currentCoach = coachList.stream().filter(coach -> coach.getTrainerId().equalsIgnoreCase(userWorkoutPlanDetails.getMicroCycleEntry().getCreatedBy()))
                        .findAny()
                        .orElse(null);

                userWorkoutPlanDetails.getMicroCycleEntry().getUserWods().forEach(userWodEntry -> {
                    UserWodEntryClient userWodEntryClient = new UserWodEntryClient();
                    TrainerInfo trainerInfo = new TrainerInfo();

                    if (currentCoach != null) {
                        trainerInfo.setName(currentCoach.getName());
                        List<ImageAsset> coachImage = currentCoach.getImages();
                        if (coachImage.get(1) != null){
                            trainerInfo.setImage(coachImage.get(1));
                        }
                    }

                    GymWorkoutSplit gymWorkout = gymWorkoutSplit.get(userWodEntry.getGymWorkoutSplitId());
                    if (gymWorkout!=null) {
                        userWodEntryClient.setGenre(gymWorkout.getName());
                    }

                    if (userWodEntry.getMeta() != null) {
                        UserWodMetaClient userWodMetaClient = new UserWodMetaClient();
                        userWodMetaClient.setCoverImage(new ImageAsset(userWodEntry.getMeta().getCoverImage()));
                        userWodMetaClient.setTallImage(new ImageAsset(userWodEntry.getMeta().getTallImage()));
                        userWodEntryClient.setMeta(userWodMetaClient);
                    }

                    userWodEntryClient.setDayOfCycle(userWodEntry.getDayOfCycle());
                    userWodEntryClient.setEndTime(userWodEntry.getEndTime());
                    userWodEntryClient.setStartTime(userWodEntry.getStartTime());
                    userWodEntryClient.setLevel(userWodEntry.getLevel());
                    userWodEntryClient.setSets(userWodEntry.getSets());
                    userWodEntryClient.setExerciseVolumeLevel(userWodEntry.getExerciseVolumeLevel());
                    userWodEntryClient.setMicroCycleId(userWorkoutPlanDetails.getMicroCycleEntry().getId());
                    userWodEntryClient.setWorkoutPhaseType(String.valueOf(userWodEntry.getSourceWorkoutType()));
                    userWodEntryClient.setStatus(userWodEntry.getStatus());
                    userWodEntryClient.setWorkoutName(userWodEntry.getWorkoutName());
                    userWodEntryClient.setSourceWorkoutId(userWodEntry.getSourceWorkoutId());
                    userWodEntryClient.setSourceWorkoutType(String.valueOf(userWodEntry.getSourceWorkoutType()));
                    userWodEntryClient.setCreatedBy(userWodEntry.getCreatedBy());
                    userWodEntryClient.setWodId(userWodEntry.getId());
                    userWodEntryClient.setId(userWodEntry.getId());
                    userWodEntryClient.setTrainerInfo(trainerInfo);

                    userWodEntryClientList.add(userWodEntryClient);
                });

                microCycleEntryClient.setUserWods(userWodEntryClientList);

                userWorkoutPlanDetailsClient.setWorkoutActivities(userWorkoutPlanDetails.getWorkoutActivities());
                userWorkoutPlanDetailsClient.setMicroCycleEntry(microCycleEntryClient);

                userWorkoutPlanDetailsList.add(userWorkoutPlanDetailsClient);
            });
        }

        return userWorkoutPlanDetailsList;
    }

    public Activity completeWorkoutEvent(Activity activity)
            throws Exception {
        return this.onyxApiClient.completeWorkoutEvent(activity);
    }

    public CoachSubscriptionDetails getCoachSubscriptionDetails()
            throws Exception {
        CoachSubscriptionDetails coachSubscriptionDetails = new CoachSubscriptionDetails();
        boolean enableTrial = false;
        coachSubscriptionDetails.setTitle("Reach your goals.\nTogether.");
        coachSubscriptionDetails.setPrice("$59.99/month");
        coachSubscriptionDetails.setPriceInfo(enableTrial ? "Your very own coach for just $2/day. 2 week free trial included." : "Your very own coach for just $2/day.");
        coachSubscriptionDetails.setFooterText("Your Onyx subscription is $60/month, and renews automatically each" +
                " month unless turned off at least 24 hours before current period" +
                " ends. Payment is charged to your iTunes account. Manage and cancel" +
                " subscriptions in Account Settings after purchase.");
        List<String> benefits= new ArrayList<>();
        benefits.add("Unlimited access to your coach");
        benefits.add("Custom workouts and weekly plans");
        benefits.add("Advanced tracking and metrics");
        benefits.add("Swap your coach anytime");
        coachSubscriptionDetails.setBenefits(benefits);
        coachSubscriptionDetails.setProductId(enableTrial ? "fit.onyx.onyx.monthly_subscription_with_trial.59.99" : "fit.onyx.onyx.monthly_subscription.59.99");
        coachSubscriptionDetails.setSubTitle(enableTrial ? "14 day free trial. Cancel anytime." : "Billed monthly. Cancel anytime.");
        return coachSubscriptionDetails;
    }

    public UserToSelectedCoachMapping createUserToSelectedCoachMapping(final
                                                                       UserToSelectedCoachMapping
                                                                               userToSelectedCoachMapping)
            throws BaseException {
        return this.onyxApiClient.createUserToSelectedCoachMapping(userToSelectedCoachMapping);
    }
}
