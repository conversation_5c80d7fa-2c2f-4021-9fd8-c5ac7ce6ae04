package com.curefit.cfapi.service.appConfig;

import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.service.EnterpriseUtils;
import com.curefit.cfapi.util.PlayUtil;
import com.curefit.cfapi.widgets.common.banner.BannerItem;
import com.curefit.cfapi.widgets.enterprise.BenefitInfo;
import com.curefit.cfapi.widgets.enterprise.TimeOfDayType;
import fit.cult.enterprise.dto.program.ProgramType;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class EnterpriseConfig {

    public static final String EMAIL_DEEP_LINK_CLICKED_EVENT_NAME = "cultpass_CORP_email_deep_link_clicked";
    public static final String DEEP_LINK_CORP_CODE_PARAMETER_KEY = "cult_pass_corp_code_tobe_activated";
    public static final String DEEP_LINK_ACTIVATE_CORP_PARAMETER_KEY = "activate_cult_pass_corp";
    public static final String BUTTON_TITLE_BUY_NOW = "BUY NOW";
    public static final String BUTTON_TITLE_BOOK_CLASS = "BOOK YOUR CLASS";
    public static final String BUTTON_TITLE_ACTIVATE_NOW = "ACTIVATE NOW";
    public static final String BUTTON_TITLE_BOOK_LIVE_CLASS = "BOOK AT-HOME SESSION";
    public static final String BUTTON_TITLE_BOOK_CULT_CLASS = "BOOK CULT CLASS";
    public static final String BUTTON_TITLE_PLAY_SPORTS = "PLAY SPORTS";
    public static final String BUTTON_TITLE_BOOK_ONEPASS_CLASS = "BOOK ONEPASS SESSION";
    public static final String STARTING_AT_CAPTION = "Starting at";
    public static final String ACTION_VARIANT_PRIMARY = "primary";
    public static final String TRIAL_TYPE_DURATION = "duration";
    public static final String TRIAL_TYPE_SESSION = "session";
    public static final String ENTERPRISE_CORP_VERTICAL = "ENTERPRISE_CORP";
    public static final String ADD_LOCATION_PREFERENCE = "Add your location preferences to access your benefit";
    public static final int WORKOUT_SESSION_MIN_STAGE_THRESHOLD = 0;
    public static final int WORKOUT_TIME_MIN_STAGE_THRESHOLD = 0;
    public static final int WORKOUT_KCAL_MIN_STAGE_THRESHOLD = 0;
    public static final int DOCTOR_SESSIONS_MIN_STAGE_THRESHOLD = 0;
    public static final int WORKOUT_SESSION_MIN_THRESHOLD = 50;
    public static final int WORKOUT_TIME_MIN_THRESHOLD = 500;
    public static final int WORKOUT_KCAL_MIN_THRESHOLD = 1000;
    public static final int METRIC_AVG_WORKOUTS_MIN_THRESHOLD = 1;
    public static final int METRIC_AVG_MINUTE_TIME_MIN_THRESHOLD = 30;
    public static final int METRIC_AVG_KCAL_MIN_THRESHOLD = 200;
    public static final int DOCTOR_SESSIONS_MIN_THRESHOLD = 10;
    public static final int TRIAL_CARD_LIMITED_SESSION_THRESHOLD = 20;
    public static final int ZOOM_CALL_JOIN_BUTTON_SHOW_MINUTES_THRESHOLD = 5;
    public static final int ZOOM_CALL_WIDGET_SHOW_HOUR_THRESHOLD = 24;
    public static final int DISAPPEAR_GOAL_BASED_CHALLENGE_AFTER_DAYS = 10;

    public static final String ZOOM_CLASS_WIDGET_IMAGE = "image/vm/b32b8ca4-c046-4b15-8d32-1231a29baf4d.png";
    public static final Map<ProgramType, BenefitInfo> PROGRAM_BENEFITS = new HashMap<>();
    public static final Map<ProgramType, BenefitDetails> PROGRAM_BENEFIT_DETAILS = new HashMap<>();
    public static final Map<TimeOfDayType, String> TIME_OF_DAY_TYPE_STORY_LOTTIE = new HashMap<>();
    public static final Set<String> THERAPY_DOCTOR_TYPE_CODES = new HashSet<>();
    public static final Set<String> NUTRITIONIST_DOCTOR_TYPE_CODES = new HashSet<>();
    public static final Map<ProgramType, Action> TRIAL_CARD_ACTIONS = new HashMap<>();
    public static final List<ProgramType> offeredCultLiveProgramTypes = new ArrayList<>();
    public static final List<String> supportedMetricStoryType = new ArrayList<>();

    public static final String DOCTOR_FLUTTER_URL = "curefit://fl_doctorbooking?isDoctorSearch=true&appsource=flutter";
    public static final String DOCTOR_REACT_URL = "curefit://doctorbooking?isDoctorSearch=true";
    public static final String THERAPY_BOOKING_URL = "curefit://fl_doctorlisting?isDoctorSearch=true&appsource=flutter&productId=CONS_MIND_THP_ONLINE&doctorType=MIND_THERAPIST";
    public static final String CULT_LIVE_CLP_MEMBER_URL = "curefit://tabpage?pageFrom=enterpriseclp&productType=FITNESS&isLiveBookingPage=true&pageId=fitnesshub&selectedTab=cultpassLIVE-Members_NEW";
    public static final String FITNESS_TAB_URL = "curefit://tabpage?pageFrom=enterpriseclp&productType=FITNESS&isLiveBookingPage=true&pageId=fitnesshub";
    public static final String CULT_LIVE_SKU_PURCHASE_URL = "curefit://livefitnessbrowsepage";
    public static final String CULT_PRO_SKU_PURCHASE_URL = "curefit://listpage?pageId=SKUPurchasePage&selectedTab=gold&scrollToWidgetId=0c77be10-b296-4a7d-85af-79f7144282ed&widgetId=0c77be10-b296-4a7d-85af-79f7144282ed";
    public static final String CULT_PRO_SKU_PURCHASE_URL_WEB = "curefit://fitness/cultpass-pro?widgetId=9fb14464-d8af-48cb-a265-c0189c3a3be9";
    public static final String CULT_PRO_CLP_MEMBER_URL = "curefit://fl_listpage?pageId=cultpassPRO-Member";
    public static final String CULT_ELITE_SKU_PURCHASE_URL = "curefit://listpage?pageId=SKUPurchasePage&selectedTab=black&scrollToWidgetId=0c77be10-b296-4a7d-85af-79f7144282ed&widgetId=0c77be10-b296-4a7d-85af-79f7144282ed";
    public static final String CULT_ELITE_SKU_PURCHASE_URL_WEB = "curefit://fitness/cultpass-elite?widgetId=cdaf7867-b716-4d8c-84de-a994e6da4fda";
    public static final String CULT_ELITE_SKU_PLUS_PURCHASE_URL_WEB = "curefit://fitness/cultpass-elite?widgetId=cdaf7867-b716-4d8c-84de-a994e6da4fda";
    public static final String CULT_ELITE_CLP_MEMBER_URL = "curefit://fl_listpage?pageId=cultpassELITE-Member";
    public static final String CULT_ELITE_CLP_MEMBER_URL_FLUTTER = "curefit://fl_listpage?pageId=cultpassELITE-Member";
    public static final String CULT_LIVE_FITNESS_TAB_URL = "curefit://tabpage?pageId=fitnesshub&selectedTab=cultpassLIVE-Members_NEW";
    public static final String AT_CENTER_FITNESS_TAB_URL = "curefit://tabpage?pageId=fitnesshub&selectedTab=cultpassELITE-Member";
    public static final String CULT_PLAY_CLP_MEMBER_URL = "curefit://listpage?pageId=cultpassPLAY-Member";
    public static final String CULT_PLAY_CLP_HOME_PAGE_URL = "curefit://tabpage?pageId=playclp";
    public static final String CULT_PLAY_SKU_PURCHASE_URL = "curefit://listpage?pageId=SKUPurchasePage&selectedTab=play&scrollToWidgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play&widgetId=0c77be10-b296-4a7d-85af-79f7144282ed_play";

    public static final String ELITE_SKU_BUY_PAGE_URL = "curefit://fl_listpage?pageId=CultPassBlackSKUBuy";
    public static final String PRO_SKU_BUY_PAGE_URL = "curefit://fl_listpage?pageId=CultPassGoldSKUBuy";
    private static final String THERAPY_CLP_URL = "curefit://therapyclp";
    private static final String NUTRITIONIST_CLP_URL = "curefit://listPage?pageId=nutritionistclp";
    private static final String CONSULTATION_CLP_URL = "curefit://listPage?pageId=clpconsultation";
    private static final String ONLINE_PT_CLP_URL = "curefit://listPage?pageId=LivePT";
    public static final String ENTERPRISE_CLP_URL = "curefit://enterpriseclp";
    public static final String INFO_ICON_PATH = "/image/vm/7e8fb9db-8172-49dd-9ddd-e75a78bb9395.png";
    public static final String ENTERPRISE_CLP_ZOOM_SESSION_WIDGET_URL_STAGE = "curefit://enterpriseclp?widgetId=6e72cc57-68e3-4bca-9cc4-3719ae7231dd";
    public static final String ENTERPRISE_CLP_ZOOM_SESSION_WIDGET_URL_PROD = "curefit://enterpriseclp?widgetId=2bacfb24-6f80-4f48-827a-eee19581687c";
    public static final String CORP_UNLOCK_STORY_IMAGE = "image/vm/0faebce3-8cd2-4903-9991-d50367b06e0d.png";
    public static final String CORP_ACTIVATE_STORY_IMAGE = "image/vm/d43a483e-c61c-47fa-9297-3ed13a7489e6.png";
    public static final String CORP_CLP_INTRO_VIDEO = "https://cdn-media.cure.fit/video/vm/71e0e34e-b97f-49f5-9162-764fbc83de8f.mp4";
    public static final String MEDITATION_CLP_URL = "curefit://listpage?pageId=MindLive";
    public static final String DESK_EXERCISES_PROGRAM_URL = "curefit://cultdiypack?packId=FITIN5_3&query=desk&productId=FITIN5_3";
    public static final String CHALLENGE_DETAIL_URL = "curefit://challengedetails?id=%s";
    public static final String TRANSFORM_NEW_CLP_URL = "curefit://tf_weight_loss_tab";
    public static final String ONEPASS_MEMBER_URL = "curefit://allgyms?centerType=ONEPASS";
    public static final String TNC_RASHI_ATTRIBUTE = "ent_tnc_accepted";

    public static final Map<String, String> ENTERPRISE_WEBHOOK_STAGE_API_KEYS = new HashMap<>();

    public static final Map<String, String> ENTERPRISE_WEBHOOK_PROD_API_KEYS = new HashMap<>();

    public static final List<String> tncEnabledDomains = new ArrayList<>();

    public static final List<ProgramType> skuWidgetNewUiSupported = new ArrayList<>();
    static {
        BenefitInfo pt = new BenefitInfo().setTitle("Online Personal Training")
                                          .setDesc("1:1 sessions and workouts tailored to your fitness level and goal")
                                          .setIconUrl("/image/vm/7a86ab7d-eb6c-4848-b7e6-066782ae1b69.png")
                .setPosition(5);

        BenefitInfo tc = new BenefitInfo().setTitle("Doctor Teleconsultation")
                                          .setDesc("Speak to doctors across 20+ specialities")
                                          .setIconUrl("/image/vm/882572dc-53ea-49f7-8e9f-72ebf1e5fc78.png")
                                            .setPosition(6);

        BenefitInfo thp = new BenefitInfo().setTitle("Therapy")
                                           .setDesc("1:1 online sessions with best therapists and coaches")
                                           .setIconUrl("/image/vm/013603bf-078d-46fa-a5ba-547a362f2551.png")
                                            .setPosition(8);

        BenefitInfo dt = new BenefitInfo().setTitle("Online Dietician")
                                          .setDesc("Personalised plans for weight management and more")
                                          .setIconUrl("/image/vm/e682ae01-171c-4c6f-a904-1041b6e79b2f.png")
                                            .setPosition(7);

        BenefitInfo cl = new BenefitInfo().setTitle("cultpass HOME")
                                          .setDesc("Online workout sessions across 10+ fitness forms")
                                          .setIconUrl("/image/vm/d15df472-2767-452d-b9d2-be0021543dd8.png")
                                            .setPosition(4);

        BenefitInfo dcl = new BenefitInfo().setTitle("cultpass HOME")
                                           .setDesc("Online workout sessions across 10+ fitness forms")
                                           .setIconUrl("/image/vm/d15df472-2767-452d-b9d2-be0021543dd8.png")
                                            .setPosition(4);

        BenefitInfo dclElite = new BenefitInfo().setTitle("cultpass ELITE")
                                .setDesc("Unlimited access to all cult centres, ELITE and PRO gyms, and live workouts")
                                .setIconUrl("/image/vm/a470bbc5-d5d6-49b0-b94d-af8ebad7c141.png")
                                .setPosition(1);

        BenefitInfo dclPro = new BenefitInfo().setTitle("cultpass PRO")
                .setDesc("Unlimited access to all PRO gyms, and live workouts")
                .setIconUrl("/image/vm/5d10db55-c151-49d2-b1ec-501788201c65.png")
                .setPosition(2);

        BenefitInfo dclPlay = new BenefitInfo().setTitle("cultpass PLAY")
                .setDesc("Unlimited access to sports centers")
                .setIconUrl("/image/cultpassPlay_trial_cardImg.png")
                .setPosition(3);

        BenefitInfo dclLimitedPlay = new BenefitInfo().setTitle("cultpass PLAY")
                .setDesc("Limited access to sports centers")
                .setIconUrl("/image/cultpassPlay_trial_cardImg.png")
                .setPosition(3);

        BenefitInfo dclLimitedPlaySwim = new BenefitInfo().setTitle("cultpass Swim")
                .setDesc("Limited access to sports centers")
                .setIconUrl("/image/cultpassPlay_trial_cardImg.png")
                .setPosition(3);

        BenefitInfo dclLimitedPlayBadminton = new BenefitInfo().setTitle("cultpass Badminton")
                .setDesc("Limited access to sports centers")
                .setIconUrl("/image/cultpassPlay_trial_cardImg.png")
                .setPosition(3);

        BenefitInfo dclLimitedPlayTT = new BenefitInfo().setTitle("cultpass Table Tennis")
                .setDesc("Limited access to sports centers")
                .setIconUrl("/image/cultpassPlay_trial_cardImg.png")
                .setPosition(3);

        BenefitInfo transform = new BenefitInfo().setTitle("cult Transform")
                .setDesc("Personal weight loss coaching with customised fitness plan and nutritional guidance")
                .setIconUrl("/image/vm/transform_trial_card.png")
                .setPosition(9);

        BenefitInfo onepass = new BenefitInfo().setTitle("OnePass")
                .setDesc("Unlimited access to Fitternity gyms, group classes & sports centers")
                .setIconUrl("/image/vm/5d10db55-c151-49d2-b1ec-501788201c65.png")
                .setPosition(10);
        BenefitInfo limitedElite = new BenefitInfo().setTitle("cultpass ELITE (Limited Pack)")
                .setDesc("Limited access to cult centers, ELITE and PRO gyms")
                .setIconUrl("/image/vm/a470bbc5-d5d6-49b0-b94d-af8ebad7c141.png")
                .setPosition(1);
        BenefitInfo cultpassX = new BenefitInfo().setTitle("ELITE + ONEPASS")
                .setDesc("Limited access to Cult and Fitternity centers, ELITE and PRO gyms")
                .setIconUrl("/image/vm/a470bbc5-d5d6-49b0-b94d-af8ebad7c141.png")
                .setPosition(1);

        PROGRAM_BENEFITS.put(ProgramType.THERAPY, thp);
        PROGRAM_BENEFITS.put(ProgramType.CULT_LIVE, cl);
        PROGRAM_BENEFITS.put(ProgramType.COUPON_BASED_CULT_LIVE, cl);
        PROGRAM_BENEFITS.put(ProgramType.DOCTOR_CONSULTATION, tc);
        PROGRAM_BENEFITS.put(ProgramType.ONLINE_PT, pt);
        PROGRAM_BENEFITS.put(ProgramType.ONLINE_DIETICIAN, dt);
        PROGRAM_BENEFITS.put(ProgramType.DISCOUNTED_CULT_LIVE, dcl);
        PROGRAM_BENEFITS.put(ProgramType.DISCOUNTED_CULT_PRO, dclPro);
        PROGRAM_BENEFITS.put(ProgramType.DISCOUNTED_CULT_ELITE, dclElite);
        PROGRAM_BENEFITS.put(ProgramType.ELITE_FIX_DATES, dclElite);
        PROGRAM_BENEFITS.put(ProgramType.ELITE_FIX_DURATION, dclElite);
        PROGRAM_BENEFITS.put(ProgramType.PRO_FIX_DATES, dclPro);
        PROGRAM_BENEFITS.put(ProgramType.PRO_FIX_DURATION, dclPro);
        PROGRAM_BENEFITS.put(ProgramType.DISCOUNTED_CULT_PLAY, dclPlay);
        PROGRAM_BENEFITS.put(ProgramType.COACH, transform);
        PROGRAM_BENEFITS.put(ProgramType.CULT_LIVE_FIXED_DURATION, cl);
        PROGRAM_BENEFITS.put(ProgramType.OP_PREMIUM_FIX_DATE, onepass);
        PROGRAM_BENEFITS.put(ProgramType.OP_PREMIUM_FIX_DURATION, onepass);
        PROGRAM_BENEFITS.put(ProgramType.OP_STANDARD_FIX_DURATION, onepass);
        PROGRAM_BENEFITS.put(ProgramType.OP_STANDARD_FIX_DATE, onepass);
        PROGRAM_BENEFITS.put(ProgramType.ELITE_FIX_DATES_WITHOUT_LIVE, dclElite);
        PROGRAM_BENEFITS.put(ProgramType.ELITE_FIX_DURATION_WITHOUT_LIVE, dclElite);
        PROGRAM_BENEFITS.put(ProgramType.ELITE_LIMITED_FIX_DURATION, limitedElite);
        PROGRAM_BENEFITS.put(ProgramType.ELITE_LIMITED_FIX_DATES, limitedElite);
        PROGRAM_BENEFITS.put(ProgramType.CULTPASSX_FIX_DATES, cultpassX);
        PROGRAM_BENEFITS.put(ProgramType.CULTPASSX_FIX_DURATION, cultpassX);
        PROGRAM_BENEFITS.put(ProgramType.PLAY_LIMITED_FIX_DATES, dclLimitedPlay);
        PROGRAM_BENEFITS.put(ProgramType.PLAY_LIMITED_FIX_DURATION, dclLimitedPlay);
        PROGRAM_BENEFITS.put(ProgramType.PLAY_SWIM_LIMITED_FIX_DATES, dclLimitedPlaySwim);
        PROGRAM_BENEFITS.put(ProgramType.PLAY_SWIM_LIMITED_FIX_DURATION, dclLimitedPlaySwim);
        PROGRAM_BENEFITS.put(ProgramType.PLAY_BADMINTON_LIMITED_FIX_DATES, dclLimitedPlayBadminton);
        PROGRAM_BENEFITS.put(ProgramType.PLAY_BADMINTON_LIMITED_FIX_DURATION, dclLimitedPlayBadminton);
        PROGRAM_BENEFITS.put(ProgramType.PLAY_TT_LIMITED_FIX_DATES, dclLimitedPlayTT);
        PROGRAM_BENEFITS.put(ProgramType.PLAY_TT_LIMITED_FIX_DURATION, dclLimitedPlayTT);

        PROGRAM_BENEFIT_DETAILS.put(ProgramType.THERAPY, BenefitDetails.THERAPY);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.ONLINE_PT, BenefitDetails.PT);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.DOCTOR_CONSULTATION, BenefitDetails.TC);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.ONLINE_DIETICIAN, BenefitDetails.DIETICIAN);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.DISCOUNTED_CULT_ELITE, BenefitDetails.CULT_ELITE);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.DISCOUNTED_CULT_PRO, BenefitDetails.CULT_PRO);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.CULT_LIVE, BenefitDetails.CULT_LIVE);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.COUPON_BASED_CULT_LIVE, BenefitDetails.CULT_LIVE);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.DISCOUNTED_CULT_LIVE, BenefitDetails.CULT_LIVE);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.ELITE_FIX_DATES, BenefitDetails.CULT_ELITE);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.ELITE_FIX_DURATION, BenefitDetails.CULT_ELITE);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.PRO_FIX_DURATION, BenefitDetails.CULT_PRO);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.PRO_FIX_DATES, BenefitDetails.CULT_PRO);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.DISCOUNTED_CULT_PLAY, BenefitDetails.CULT_PLAY);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.OP_PREMIUM_FIX_DURATION, BenefitDetails.ONEPASS);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.OP_PREMIUM_FIX_DATE, BenefitDetails.ONEPASS);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.OP_STANDARD_FIX_DURATION, BenefitDetails.ONEPASS);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.OP_STANDARD_FIX_DATE, BenefitDetails.ONEPASS);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.ELITE_FIX_DURATION_WITHOUT_LIVE, BenefitDetails.CULT_ELITE_WITHOUT_LIVE);
        PROGRAM_BENEFIT_DETAILS.put(ProgramType.ELITE_FIX_DATES_WITHOUT_LIVE, BenefitDetails.CULT_ELITE_WITHOUT_LIVE);
//        PROGRAM_BENEFIT_DETAILS.put(ProgramType.PLAY_LIMITED_FIX_DATES, BenefitDetails.CULT_PLAY);
//        PROGRAM_BENEFIT_DETAILS.put(ProgramType.PLAY_LIMITED_FIX_DURATION, BenefitDetails.CULT_PLAY);

        NUTRITIONIST_DOCTOR_TYPE_CODES.addAll(Arrays.asList("LC"));
        THERAPY_DOCTOR_TYPE_CODES.addAll(Arrays.asList("MIND_THERAPIST", "COUPLE_THERAPIST"));

        TRIAL_CARD_ACTIONS.put(ProgramType.THERAPY, new Action(THERAPY_CLP_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.ONLINE_DIETICIAN, new Action(NUTRITIONIST_CLP_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.DOCTOR_CONSULTATION, new Action(CONSULTATION_CLP_URL,
                ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.CULT_LIVE, new Action(CULT_LIVE_CLP_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.COUPON_BASED_CULT_LIVE, new Action(CULT_LIVE_CLP_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.DISCOUNTED_CULT_LIVE, new Action(CULT_LIVE_CLP_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.DISCOUNTED_CULT_ELITE, new Action(CULT_ELITE_CLP_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.DISCOUNTED_CULT_PRO, new Action(CULT_PRO_CLP_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.DISCOUNTED_CULT_PLAY, new Action(CULT_PLAY_CLP_HOME_PAGE_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.PLAY_LIMITED_FIX_DURATION, new Action(CULT_PLAY_CLP_HOME_PAGE_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.PLAY_LIMITED_FIX_DATES, new Action(CULT_PLAY_CLP_HOME_PAGE_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.ONLINE_PT, new Action(ONLINE_PT_CLP_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.COACH, new Action(TRANSFORM_NEW_CLP_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.CULT_LIVE_FIXED_DURATION, new Action(CULT_LIVE_CLP_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.OP_PREMIUM_FIX_DATE, new Action(ONEPASS_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.OP_PREMIUM_FIX_DURATION, new Action(ONEPASS_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.OP_STANDARD_FIX_DATE, new Action(ONEPASS_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.OP_STANDARD_FIX_DURATION, new Action(ONEPASS_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.ELITE_LIMITED_FIX_DATES, new Action(CULT_ELITE_CLP_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.ELITE_LIMITED_FIX_DURATION, new Action(CULT_ELITE_CLP_MEMBER_URL, ActionType.NAVIGATION));
        TRIAL_CARD_ACTIONS.put(ProgramType.CULTPASSX_FIX_DURATION, EnterpriseUtils.getCultpassXTrialCardAction());
        TRIAL_CARD_ACTIONS.put(ProgramType.CULTPASSX_FIX_DATES, EnterpriseUtils.getCultpassXTrialCardAction());

//        TIME_OF_DAY_TYPE_STORY_LOTTIE.put(TimeOfDayType.MORNING, "/image/vm/6dc29638-5ba6-41bf-8866-da3363a23ba8.jpg");
//        TIME_OF_DAY_TYPE_STORY_LOTTIE.put(TimeOfDayType.AFTERNOON, "/image/vm/839cfab7-92fd-4ff8-a21c-a26f4b437a63.jpg");
//        TIME_OF_DAY_TYPE_STORY_LOTTIE.put(TimeOfDayType.EVENING, "/image/vm/4c9ab9a4-7265-4e2a-b3f7-28e663749ecf.jpg");
//        TIME_OF_DAY_TYPE_STORY_LOTTIE.put(TimeOfDayType.NIGHT, "/image/vm/26f9492a-daec-4d9d-8db3-c30607e82905.jpg");

        TIME_OF_DAY_TYPE_STORY_LOTTIE.put(TimeOfDayType.MORNING, "/image/enterprise/lottie/background/v3/morning.json");
        TIME_OF_DAY_TYPE_STORY_LOTTIE.put(TimeOfDayType.AFTERNOON, "/image/enterprise/lottie/background/v3/afternoon.json");
        TIME_OF_DAY_TYPE_STORY_LOTTIE.put(TimeOfDayType.EVENING, "/image/enterprise/lottie/background/v3/evening.json");
        TIME_OF_DAY_TYPE_STORY_LOTTIE.put(TimeOfDayType.NIGHT, "/image/enterprise/lottie/background/v3/night.json");

        offeredCultLiveProgramTypes.add(ProgramType.CULT_LIVE);
        offeredCultLiveProgramTypes.add(ProgramType.COUPON_BASED_CULT_LIVE);
        offeredCultLiveProgramTypes.add(ProgramType.DISCOUNTED_CULT_LIVE);
        offeredCultLiveProgramTypes.add(ProgramType.DISCOUNTED_CULT_PRO);
        offeredCultLiveProgramTypes.add(ProgramType.DISCOUNTED_CULT_ELITE);
        offeredCultLiveProgramTypes.add(ProgramType.DISCOUNTED_CULT_PLAY);

        supportedMetricStoryType.add(BannerItem.BannerStoryType.CULT_LIVE.name());
        supportedMetricStoryType.add(BannerItem.BannerStoryType.WORKOUT_METRIC.name());
        supportedMetricStoryType.add(BannerItem.BannerStoryType.WORKOUT_SESSION.name());
        supportedMetricStoryType.add(BannerItem.BannerStoryType.WELLNESS_BENEFIT.name());

        skuWidgetNewUiSupported.add(ProgramType.CULT_LIVE);
        skuWidgetNewUiSupported.add(ProgramType.DISCOUNTED_CULT_LIVE);
        skuWidgetNewUiSupported.add(ProgramType.DISCOUNTED_CULT_PRO);
        skuWidgetNewUiSupported.add(ProgramType.DISCOUNTED_CULT_ELITE);
        skuWidgetNewUiSupported.add(ProgramType.ELITE_FIX_DATES);
        skuWidgetNewUiSupported.add(ProgramType.ELITE_FIX_DURATION);
        skuWidgetNewUiSupported.add(ProgramType.PRO_FIX_DATES);
        skuWidgetNewUiSupported.add(ProgramType.PRO_FIX_DURATION);
        skuWidgetNewUiSupported.add(ProgramType.DISCOUNTED_CULT_PLAY);
        skuWidgetNewUiSupported.add(ProgramType.CULT_LIVE_FIXED_DURATION);
        skuWidgetNewUiSupported.add(ProgramType.OP_PREMIUM_FIX_DATE);
        skuWidgetNewUiSupported.add(ProgramType.OP_PREMIUM_FIX_DURATION);
        skuWidgetNewUiSupported.add(ProgramType.OP_STANDARD_FIX_DURATION);
        skuWidgetNewUiSupported.add(ProgramType.OP_STANDARD_FIX_DATE);
        skuWidgetNewUiSupported.add(ProgramType.ELITE_FIX_DURATION_WITHOUT_LIVE);
        skuWidgetNewUiSupported.add(ProgramType.ELITE_FIX_DATES_WITHOUT_LIVE);
        skuWidgetNewUiSupported.add(ProgramType.PLAY_LIMITED_FIX_DATES);
        skuWidgetNewUiSupported.add(ProgramType.PLAY_LIMITED_FIX_DURATION);

        tncEnabledDomains.add("in.bosch.com");
        tncEnabledDomains.add("tpv-tech.com");

        ENTERPRISE_WEBHOOK_STAGE_API_KEYS.put("a38b3422-2213-11ee-be56-0242ac120002", "Corp123456");
        ENTERPRISE_WEBHOOK_STAGE_API_KEYS.put("f3ce09ab-b30b-4b3b-8202-c698ab94bac0", "GETVISIT0723");
        // For testing
        ENTERPRISE_WEBHOOK_PROD_API_KEYS.put("3415e75d-89f8-4dfd-bbc6-b10a26cb0047", "TestCultpassCORP");
        ENTERPRISE_WEBHOOK_PROD_API_KEYS.put("178683b0-d477-4544-be54-d1a4f68ea1c8", "GETVISIT0723");
    }

    @Data
    @Accessors(chain = true)
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class BenefitDetails {
        String title;
        String subTitle;
        String bgImage;
        Action action;
        Action bottomAction;
        String subTitleColor;
        List<String> details;

        public static final BenefitDetails PT;
        public static final BenefitDetails TC;
        public static final BenefitDetails THERAPY;
        public static final BenefitDetails DIETICIAN;
        public static final BenefitDetails CULT_ELITE;
        public static final BenefitDetails CULT_ELITE_WITHOUT_LIVE;
        public static final BenefitDetails CULT_PRO;
        public static final BenefitDetails CULT_LIVE;
        public static final BenefitDetails CULT_PLAY;
        public static final BenefitDetails ONEPASS;

        static {
            PT = new BenefitDetails().setTitle("online personal")
                                     .setSubTitle("Training")
                                     .setBgImage("image/vm/f02afd33-1063-4bb5-b168-effdd5faa0d4.jpg")
                                     .addDetail("1:1 sessions and workouts tailored to your fitness level and goal")
                                     .setAction(new Action(ONLINE_PT_CLP_URL, ActionType.NAVIGATION));

            TC = new BenefitDetails().setTitle("doctor")
                                     .setSubTitle("Teleconsultation")
                                     .setBgImage("/image/vm/fd2c7628-b18e-428b-a143-5d4acf26ccba.jpg")
                                     .addDetail("20+ qualified specialities")
                                     .setAction(new Action(CONSULTATION_CLP_URL, ActionType.NAVIGATION))
                                     .addDetail("Connect directly with the doctor, no chatbots!");

            THERAPY = new BenefitDetails().setSubTitle("Therapy")
                                          .setBgImage("/image/vm/0496769e-6868-4cdd-bf97-dfe59f43bac3.jpg")
                                          .addDetail("1:1 session with experts")
                                          .setAction(new Action(THERAPY_CLP_URL, ActionType.NAVIGATION))
                                          .addDetail("Consult for sleep, stress, relationship issues and more");

            DIETICIAN = new BenefitDetails().setTitle("online")
                                            .setSubTitle("Dietician")
                                            .setBgImage("/image/vm/f60c154e-a539-4547-8c56-d00437c91922.jpg")
                                            .addDetail("Personalized diet plans for weight loss")
                                            .setAction(new Action(NUTRITIONIST_CLP_URL, ActionType.NAVIGATION))
                                            .addDetail("Choose from amongst 200+ clinical nutritionists");

            CULT_ELITE = new BenefitDetails().setTitle("cultpass")
                                            .setSubTitle("ELITE")
                    .setBgImage("/image/vm/8125c21e-299a-49a5-94b0-f19eda7b4d7f.jpg")
                    .setSubTitleColor("GOLD")
                    .addDetail("Unlimited access to all cult centres, ELITE & PRO gyms")
                    .addDetail("At-center group classes")
                    .addDetail("Smart workout plans for your fitness goals")
                    .addDetail("At-home workouts")
                    .setAction(new Action(CULT_ELITE_SKU_PURCHASE_URL, ActionType.NAVIGATION))
                    .setBottomAction(new Action(CULT_ELITE_SKU_PURCHASE_URL, "BUY NOW" ,ActionType.NAVIGATION));

            CULT_ELITE_WITHOUT_LIVE = new BenefitDetails().setTitle("cultpass")
                    .setSubTitle("ELITE")
                    .setBgImage("/image/vm/8125c21e-299a-49a5-94b0-f19eda7b4d7f.jpg")
                    .setSubTitleColor("GOLD")
                    .addDetail("Unlimited access to all cult centres, ELITE & PRO gyms")
                    .addDetail("At-center group classes")
                    .addDetail("Smart workout plans for your fitness goals")
                    .setAction(new Action(CULT_ELITE_SKU_PURCHASE_URL, ActionType.NAVIGATION))
                    .setBottomAction(new Action(CULT_ELITE_SKU_PURCHASE_URL, "BUY NOW" ,ActionType.NAVIGATION));

            CULT_PRO = new BenefitDetails().setTitle("cultpass")
                    .setSubTitle("PRO")
                    .setBgImage("/image/vm/cfc7d9f9-0cf1-4ecc-92d6-e5c8eb29d4e0.jpg")
                    .setSubTitleColor("SILVER")
                    .addDetail("Unlimited access to all PRO gyms")
                    .addDetail("2 sessions/month at ELITE gyms and group classes")
                    .addDetail("Smart workout plans for your fitness goals")
                    .addDetail("At-home workouts")
                    .setAction(new Action(CULT_PRO_SKU_PURCHASE_URL, ActionType.NAVIGATION))
                    .setBottomAction(new Action(CULT_PRO_SKU_PURCHASE_URL, "BUY NOW" ,ActionType.NAVIGATION));

            CULT_LIVE = new BenefitDetails().setTitle("cultpass")
                    .setSubTitle("HOME")
                    .setBgImage("/image/vm/17c35854-85f0-4235-9b10-036614569179.jpg")
                    .setSubTitleColor("PINK")
                    .addDetail("Unlimited access to at-home workouts")
                    .addDetail("Track Performance With Energy Meter")
                    .setAction(new Action(CULT_LIVE_SKU_PURCHASE_URL, ActionType.NAVIGATION));

            CULT_PLAY = new BenefitDetails().setTitle("cultpass")
                    .setSubTitle("PLAY")
                    .setSubTitleColor("GREEN")
                    .setBgImage("/image/cultpassPlay_skucard_img.png")
                    .addDetail("Unlimited access to sports centers")
                    .addDetail("2 sessions/month at ELITE/PRO gyms and group classes")
                    .addDetail("Smart workout plans for your fitness goals")
                    .addDetail("At-home live workouts")
                    .setAction(new Action(PlayUtil.PLAY_TAB_ON_HOME_PAGE, ActionType.NAVIGATION));

            ONEPASS = new BenefitDetails().setTitle("fitternity")
                    .setSubTitle("ONEPASS")
                    .setSubTitleColor("GOLD")
                    .setBgImage("/image/vm/onepass_sku.png")
                    .addDetail("Unlimited access to Fitternity group classes, gym and sports centers")
                    .setAction(new Action(ONEPASS_MEMBER_URL, ActionType.NAVIGATION));
        }

        public BenefitDetails addDetail(String message) {
            if (details == null) {
                details = new ArrayList<>();
            }
            details.add(message);
            return this;
        }
    }
}
