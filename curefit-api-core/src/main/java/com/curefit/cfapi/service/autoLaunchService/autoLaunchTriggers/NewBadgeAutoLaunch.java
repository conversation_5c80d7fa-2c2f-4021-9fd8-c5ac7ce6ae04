package com.curefit.cfapi.service.autoLaunchService.autoLaunchTriggers;

import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.cache.FeatureStateCache;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.pojo.app.action.Action;
import com.curefit.cfapi.pojo.app.action.ActionType;
import com.curefit.cfapi.pojo.autoLaunch.AutoLaunchAction;
import com.curefit.cfapi.pojo.feedback.Feedback;
import com.curefit.cfapi.repository.AutoLaunchPageConfigRepository;
import com.curefit.cfapi.service.ServiceInterfaces;
import com.curefit.cfapi.service.autoLaunchService.AutoLaunchBaseService;
import com.curefit.cfapi.service.autoLaunchService.AutoLaunchEvaluator;
import com.curefit.cfapi.util.AppUtil;
import com.curefit.cfapi.util.Debugger;
import com.curefit.cfapi.widgets.fitness.UserWeeklyActivity;
import com.curefit.common.data.exception.BaseException;
import com.curefit.quest.pojo.Badge;
import com.curefit.rashi.enums.UserEventType;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.json.simple.JSONObject;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

import static com.curefit.cfapi.constants.Constants.NEW_BADGE_PAGE;

@Service
public class NewBadgeAutoLaunch extends AutoLaunchBaseService implements AutoLaunchEvaluator {

    ServiceInterfaces interfaces;

    public NewBadgeAutoLaunch(AutoLaunchPageConfigRepository autoLaunchPageConfigRepository, ServiceInterfaces serviceInterfaces, FeatureStateCache featureStateCache) throws ExecutionException, InterruptedException {
        super("NewBadgeAutoLaunch", autoLaunchPageConfigRepository, serviceInterfaces, featureStateCache);
        this.interfaces = serviceInterfaces;
    }

    @Override
    public Optional<AutoLaunchAction> evaluate(UserContext userContext, Feedback feedback) throws ExecutionException, InterruptedException {

        super.evaluate(userContext, feedback);
        Debugger debugger = Debugger.getDebuggerFromUserContext(userContext);
        try {
            if (AppUtil.isNewBadgeAwardAutoLaunchSupported(userContext, interfaces)) {
                UserWeeklyActivity userWeeklyActivity = new UserWeeklyActivity();
                Map<String, Object> badgeDataMap = userWeeklyActivity.getLatestBadge(userContext, interfaces, 1);
                debugger.msg(badgeDataMap);
                if (badgeDataMap != null && badgeDataMap.containsKey("badge")) {
                    Badge badge = (Badge) badgeDataMap.get("badge");
                    if (badge != null) {
                        String key = badge.getBadgeId() + "_autolaunch";
                        debugger.msg("key", key);
                        boolean isBadgeVisible = validateFeatureStateCache(key, "viewed", userContext);
                        debugger.msg("isBadgeVisible", isBadgeVisible);
                        if (!isBadgeVisible) {
                            Action action = Action.builder().actionType(ActionType.NAVIGATION).url(NEW_BADGE_PAGE).build();
                            AutoLaunchResult result = new AutoLaunchResult(action, super.getPriority(), userContext, super.getPageId(), badge);
                            return Optional.of(result);
                        }
                    }
                }
            }
        } catch (Exception e) {
            debugger.err(e);
            interfaces.exceptionReportingService.reportException(e);
        }

        return Optional.empty();
    }

    class AutoLaunchResult extends AutoLaunchAction {
        Badge badge;
        UserContext userContext;

        public AutoLaunchResult(Action action, Integer priority, UserContext userContext, String pageId, Badge badge) {
            super(action, priority, pageId);
            this.userContext = userContext;
            this.badge = badge;
        }

        @Override
        public void onExecute() throws BaseException, JsonProcessingException {
            JSONObject body = new JSONObject();
            body.put("badgeId", badge.getBadgeId());
            interfaces.rashiClient.publishUserEvent(UserEventType.USER_ACTIVITY_EVENT, "NEW_BADGE_RECEIVED_VIEWED", new Date(), userContext.getUserProfile().getUserId(), body, AppTenant.CUREFIT, UUID.randomUUID().toString());
            String key = badge.getBadgeId() + "_autolaunch";
            setFeatureStateCache(key, "viewed", userContext);
        }
    }
}
