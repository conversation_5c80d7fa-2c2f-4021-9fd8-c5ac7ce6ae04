package com.curefit.cfapi.service;

import com.curefit.base.enums.AppTenant;
import com.curefit.cfapi.builder.vm.RequestCache;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.SegmentTagCache;
import com.curefit.cfapi.constants.Constants;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.userinfo.UserProfile;
import com.curefit.hamlet.models.pojo.UserAssignment;
import com.curefit.hamlet.models.response.UserAllocation;
import com.curefit.hamlet.types.Bucket;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.helpers.SegmentUsageIncrementHelper;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.google.common.collect.Sets;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.assertj.core.util.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@RunWith(MockitoJUnitRunner.class)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserServiceTest {
    @InjectMocks
    UserService userService;

    @Mock
    SegmentationCacheClient segmentationCacheClient;

    @Mock
    SegmentTagCache segmentTagCache;

    @Mock
    RequestCache requestCache;

    @Test
    public void addAnalyticsData() throws ExecutionException, InterruptedException {
        Mockito.when(segmentTagCache.getSessionTrackedSegments(AppTenant.CUREFIT)).thenReturn(Sets.newHashSet("aaa", "bbb", "ccc"));
        Bucket bucket = new Bucket();
        bucket.setBucketId("Treatment");
        UserAssignment userAssignment = new UserAssignment(1, "111", bucket);
        UserAllocation userAllocation = new UserAllocation(Maps.newHashMap("111", userAssignment));

        UserProfile userProfile = new UserProfile();
        userProfile.setUserId("123");
        UserContext userContext = new UserContext();
        userContext.setUserProfile(userProfile);
        userContext.setRequestCache(requestCache);
        Set<String> segmentSet = Sets.newHashSet("aaa", "bbb", "eeee");
        SegmentUsageIncrementHelper segmentUsageIncrementHelper = Mockito.mock(SegmentUsageIncrementHelper.class);
        Mockito.when(requestCache.getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext))
                .thenReturn(CompletableFuture.completedFuture(new SegmentSet<String>(segmentSet, segmentUsageIncrementHelper)));

        Map<String, String> analyticsData = userService.addAnalyticsData(userContext, AppTenant.CUREFIT, CompletableFuture.completedFuture(userAllocation));
        Assert.assertEquals("aaa,bbb", analyticsData.get(Constants.DATALAKE_ANALYTICS_KEY_SEGMENTS));
        Assert.assertEquals("E:111_Treatment_1", analyticsData.get(Constants.DATALAKE_ANALYTICS_KEY_EXPERIMENT));
    }
}