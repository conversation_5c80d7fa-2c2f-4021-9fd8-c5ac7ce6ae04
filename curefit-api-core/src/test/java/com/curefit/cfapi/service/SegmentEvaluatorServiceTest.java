package com.curefit.cfapi.service;

import com.curefit.cfapi.builder.vm.RequestCache;
import com.curefit.cfapi.builder.vm.request.RequestType;
import com.curefit.cfapi.cache.SegmentCache;
import com.curefit.cfapi.model.internal.segment.ConditionType;
import com.curefit.cfapi.model.internal.segment.ConditionalField;
import com.curefit.cfapi.model.internal.segment.Hamlet;
import com.curefit.cfapi.model.internal.userinfo.UserContext;
import com.curefit.cfapi.model.internal.userinfo.UserProfile;
import com.curefit.cfapi.model.mongo.Segment;
import com.curefit.cfapi.segmentevaluator.CitiesEvaluator;
import com.curefit.cfapi.segmentevaluator.ConditionsFieldEvaluator;
import com.curefit.cfapi.segmentevaluator.HamletEvaluator;
import com.curefit.cfapi.segmentevaluator.PlatformSegmentEvaluator;
import com.curefit.hamlet.models.pojo.UserAssignment;
import com.curefit.hamlet.models.response.UserAllocation;
import com.curefit.hamlet.types.Bucket;
import com.curefit.location.models.City;
import com.curefit.segmentation.client.helpers.SegmentUsageIncrementHelper;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Maps;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.task.SyncTaskExecutor;
import org.springframework.core.task.TaskExecutor;

import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@RunWith(MockitoJUnitRunner.class)
public class SegmentEvaluatorServiceTest {
    @InjectMocks
    SegmentEvaluatorService segmentEvaluatorService;

    @Mock
    SegmentCache segmentCache;

    @Mock
    ExceptionReportingService exceptionReportingService;

    @Spy
    TaskExecutor taskExecutor = new SyncTaskExecutor();

    PlatformSegmentEvaluator platformSegmentEvaluator = new PlatformSegmentEvaluator();
    ConditionsFieldEvaluator conditionsFieldEvaluator = new ConditionsFieldEvaluator();

    HamletEvaluator hamletEvaluator = new HamletEvaluator();
    CitiesEvaluator citiesEvaluator = new CitiesEvaluator();

    @Spy
    BaseEvaluatorFactory baseEvaluatorFactory = new BaseEvaluatorFactory(Lists.newArrayList(platformSegmentEvaluator,
            conditionsFieldEvaluator, citiesEvaluator, hamletEvaluator));

    @Mock
    RequestCache requestCache = Mockito.mock(RequestCache.class);

    @Before
    public void fixSelf() throws IllegalAccessException {
        FieldUtils.writeField(segmentEvaluatorService, "self", segmentEvaluatorService, true);
    }

    @Test
    public void checkCondition_segmentEvaluator() throws ExecutionException, InterruptedException {
        Segment segment = new Segment();
        segment.setSegmentId("cyclopsSegment");
        ConditionalField conditionalField = new ConditionalField();
        conditionalField.setCondition(ConditionType.NOT_BELONGS_TO_ALL);
        conditionalField.setField("PLATFORM_SEGMENTS");
        conditionalField.setValue(Lists.newArrayList("segment123"));
        segment.setConditionalFields(Lists.newArrayList(conditionalField));
        UserContext userContext = getUserContext();
        Mockito.when(segmentCache.getSegment("cyclopsSegment", false)).thenReturn(CompletableFuture.completedFuture(segment));
        Segment result = segmentEvaluatorService.checkCondition(Lists.newArrayList("cyclopsSegment"), userContext).get();
        Assert.assertNull(result);

        conditionalField.setCondition(ConditionType.BELONGS_TO_ANY);
        result = segmentEvaluatorService.checkCondition(Lists.newArrayList("cyclopsSegment"), userContext).get();
        Assert.assertEquals("cyclopsSegment", result.getSegmentId());
    }

    @Test
    public void checkCondition_cityEvaluator() throws ExecutionException, InterruptedException {
        Segment segment = new Segment();
        segment.setSegmentId("cyclopsSegment");
        segment.setCityIds(Lists.newArrayList("Gurgaon"));
        UserContext userContext = getUserContext();
        Mockito.when(segmentCache.getSegment("cyclopsSegment", false)).thenReturn(CompletableFuture.completedFuture(segment));
        Segment result = segmentEvaluatorService.checkCondition(Lists.newArrayList("cyclopsSegment"), userContext).get();
        Assert.assertNull(result);

        segment.setCityIds(Lists.newArrayList("Bangalore"));
        result = segmentEvaluatorService.checkCondition(Lists.newArrayList("cyclopsSegment"), userContext).get();
        Assert.assertEquals(segment, result);
    }

    @Test
    public void checkCondition_hamletEvaluator() throws ExecutionException, InterruptedException {
        Segment segment = new Segment();
        segment.setSegmentId("cyclopsSegment");
        segment.setHamlet(new Hamlet("experiment1", "1"));
        UserContext userContext = getUserContext();
        Mockito.when(segmentCache.getSegment("cyclopsSegment", false)).thenReturn(CompletableFuture.completedFuture(segment));
        Segment result = segmentEvaluatorService.checkCondition(Lists.newArrayList("cyclopsSegment"), userContext).get();
        Assert.assertNull(result);

        segment.setHamlet(new Hamlet("experiment1", "2"));
        result = segmentEvaluatorService.checkCondition(Lists.newArrayList("cyclopsSegment"), userContext).get();
        Assert.assertEquals(segment, result);
    }

    @Test
    public void checkCondition_segmentAndHamletAndCityCheck() throws ExecutionException, InterruptedException {
        Segment segment = new Segment();
        segment.setSegmentId("cyclopsSegment");
        segment.setCityIds(Lists.newArrayList("Bangalore"));
        segment.setHamlet(new Hamlet("experiment1", "2"));
        ConditionalField conditionalField = new ConditionalField();
        conditionalField.setCondition(ConditionType.NOT_BELONGS_TO_ALL);
        conditionalField.setField("PLATFORM_SEGMENTS");
        conditionalField.setValue(Lists.newArrayList("segment123"));
        segment.setConditionalFields(Lists.newArrayList(conditionalField));
        UserContext userContext = getUserContext();
        Mockito.when(segmentCache.getSegment("cyclopsSegment", false)).thenReturn(CompletableFuture.completedFuture(segment));

        conditionalField.setCondition(ConditionType.BELONGS_TO_ANY);
        Segment result = segmentEvaluatorService.checkCondition(Lists.newArrayList("cyclopsSegment"), userContext).get();
        Assert.assertEquals("cyclopsSegment", result.getSegmentId());
    }

    @NotNull
    private UserContext getUserContext() {
        UserContext userContext = new UserContext();
        userContext.setRequestCache(requestCache);
        UserProfile userProfile = new UserProfile();
        City city = new City();
        city.setCityId("Bangalore");
        userProfile.setCity(city);
        UserAllocation userAllocation = new UserAllocation(Maps.newHashMap("experiment1",
                UserAssignment.builder().experimentId("experiment1").bucket(Bucket.builder().bucketId("2").build()).build()));
        userProfile.setHamletUserExperimentPromise(CompletableFuture.completedFuture(userAllocation));
        userContext.setUserProfile(userProfile);
        Set<String> segmentSet = Sets.newHashSet("segment123", "segment124");
        SegmentUsageIncrementHelper segmentUsageIncrementHelper = Mockito.mock(SegmentUsageIncrementHelper.class);
        Mockito.when(requestCache.getRequestFuture(RequestType.PLATFORM_SEGMENTS, userContext))
                .thenReturn(CompletableFuture.completedFuture(
                        new SegmentSet<>(segmentSet, segmentUsageIncrementHelper)));
        return userContext;
    }
}