<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.curefit</groupId>
		<artifactId>curefit-api</artifactId>
		<version>0.0.2</version>
	</parent>
	<artifactId>curefit-api-core</artifactId>

	<dependencies>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cf-commons</artifactId>
			<version>${curefit.common-sf.version}</version>
		</dependency>


		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cf-commons-messaging</artifactId>
			<version>${curefit.common-sf.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>media-gateway-client</artifactId>
			<version>3.0.0</version>
		</dependency>

		<dependency>
			<artifactId>reward-client</artifactId>
			<groupId>com.curefit</groupId>
			<version>1.2.14</version>
		</dependency>

		<dependency>
			<artifactId>lms-client</artifactId>
			<groupId>com.sugarfit</groupId>
			<version>${lms.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.commons</groupId>
			<artifactId>curefit-commons-util</artifactId>
			<version>${curefit-commons.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.commons</groupId>
			<artifactId>common-data</artifactId>
			<version>${carefit-common.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.commons</groupId>
			<artifactId>common-integrations</artifactId>
			<version>${carefit-common.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.commons</groupId>
			<artifactId>common-server</artifactId>
			<version>${carefit-common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.mapstruct</groupId>
					<artifactId>mapstruct</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.undertow</groupId>
					<artifactId>undertow-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.springfox</groupId>
					<artifactId>springfox-swagger-ui</artifactId>
				</exclusion>
				<!-- Exclude vulnerable mysql-connector-java to fix CVE-2023-22102 -->
				<exclusion>
					<groupId>mysql</groupId>
					<artifactId>mysql-connector-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>1.5.3.Final</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.commons</groupId>
			<artifactId>common-rest-client</artifactId>
			<version>1.2.28</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.commons</groupId>
			<artifactId>curefit-commons-redis</artifactId>
			<version>${curefit-commons.version}</version>
		</dependency>

		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>3.10.0</version>
		</dependency>

		<dependency>
			<groupId>io.lettuce</groupId>
			<artifactId>lettuce-core</artifactId>
			<version>${redis.lettuce.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.12.0</version>
			<type>jar</type>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-resolver-dns-native-macos</artifactId>
			<version>4.1.107.Final</version>
			<classifier>osx-aarch_64</classifier>
		</dependency>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-mongodb</artifactId>
		</dependency>


		<dependency>
			<groupId>org.passay</groupId>
			<artifactId>passay</artifactId>
			<version>1.6.4</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>hamlet-sdk</artifactId>
			<version>${curefit.hamlet.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>hamlet-common</artifactId>
			<version>${curefit.hamlet.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>hamlet-core</artifactId>
			<version>${curefit.hamlet.version}</version>
		</dependency>

		<dependency>
			<artifactId>metrics-client</artifactId>
			<groupId>com.curefit.analytics</groupId>
			<version>0.5.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>identity-api-client</artifactId>
			<version>0.0.3</version>
			<exclusions>
				<exclusion>
					<groupId>io.swagger</groupId>
					<artifactId>swagger-annotations</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>nl.basjes.parse.useragent</groupId>
			<artifactId>yauaa</artifactId>
			<version>5.23</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>aakash-vani-client</artifactId>
			<version>1.0.5</version>
		</dependency>

		<dependency>
			<groupId>eu.bitwalker</groupId>
			<artifactId>UserAgentUtils</artifactId>
			<version>1.21</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.13.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>curefit-api-common</artifactId>
			<version>${project.parent.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>personal-trainer-v2-client</artifactId>
			<version>1.7.3</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>gymfit-common</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>catalog-client-v1</artifactId>
			<version>0.4.10</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit.cult</groupId>
					<artifactId>cult-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>product-common</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>diyfs-client</artifactId>
			<version>${diyfs.version}</version>
		</dependency>

		<dependency>
			<groupId>dev.shortloop.agent</groupId>
			<artifactId>agent-java</artifactId>
			<version>0.0.12</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>diyfs-common</artifactId>
			<version>${diyfs.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>falcon-client</artifactId>
			<version>1.3.7</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>falcon-common</artifactId>
			<version>1.3.7</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>shifu-client</artifactId>
			<version>${shifu.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>subscription-manager-domain</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>albus-client</artifactId>
			<version>${albus.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>metric-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>curefit-commons-util</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>subscription-manager-domain</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>albus-common</artifactId>
            <version>${albus.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.curefit</groupId>
                    <artifactId>metric-client</artifactId>
                </exclusion>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>subscription-manager-domain</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>curefit-commons</artifactId>
				</exclusion>
            </exclusions>
        </dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>ehr-client</artifactId>
			<version>2.0.11</version>
			<exclusions>
				<exclusion>
					<groupId>io.undertow</groupId>
					<artifactId>undertow-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.springfox</groupId>
					<artifactId>springfox-swagger-ui</artifactId>
				</exclusion>
				<!-- Exclude vulnerable mysql-connector-java to fix CVE-2023-22102 -->
				<exclusion>
					<groupId>mysql</groupId>
					<artifactId>mysql-connector-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>maverick-client</artifactId>
			<version>1.1</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>polaris-client</artifactId>
			<version>1.1.7</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>metric-client</artifactId>
			<version>1.10.6</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>metric-model</artifactId>
			<version>1.10.8</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>logging-client</artifactId>
			<version>0.0.56</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>activity-logging-client</artifactId>
			<version>2.4.12</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>segmentation-client</artifactId>
			<version>4.7.10</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>gearvault-client</artifactId>
			<version>${gearvault.client.version}</version>
		</dependency>

		<dependency>
			<groupId>fit.cult.enterprise</groupId>
			<artifactId>enterprise-client</artifactId>
			<version>0.4.24</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>gearvault-common</artifactId>
			<version>${gearvault.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.offers</groupId>
					<artifactId>offer-service-commons</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>ufs-client</artifactId>
			<version>2.2.17</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>hercules-common</artifactId>
			<version>2.0.17</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>hercules-client</artifactId>
			<version>2.0.17</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>ollivander.client</artifactId>
			<version>${ollivander-client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>ollivander.common</artifactId>
			<version>${ollivander-client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cfs-java-client</artifactId>
			<version>0.0.7</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>base-common</artifactId>
			<version>${base-common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-messaging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>apps-common</artifactId>
			<version>0.0.22</version>
		</dependency>

		<dependency>
			<groupId>com.slack.api</groupId>
			<artifactId>slack-api-client</artifactId>
			<version>1.35.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>util-common</artifactId>
			<version>0.0.2</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>product-common</artifactId>
			<version>${curefit.product-common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.cult</groupId>
					<artifactId>cult-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>albus-common</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>gymfit-common</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>auth-common</artifactId>
			<version>0.0.36</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>location-common</artifactId>
			<version>0.0.50</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>user-service-client</artifactId>
			<version>${user-service.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>mystique-client</artifactId>
			<version>2.1.2</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>user-service-common</artifactId>
			<version>${user-service.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>common-data</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jetty</artifactId>
		</dependency>

		<dependency>
			<groupId>org.eclipse.jetty.http2</groupId>
			<artifactId>http2-server</artifactId>
			<version>9.4.54.v20240208</version>
		</dependency>

		<dependency>
			<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-ui</artifactId>
			<version>1.7.0</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-zuul</artifactId>
			<version>2.2.10.RELEASE</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.16.0</version>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.15.0</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>2.15.3</version>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback.contrib</groupId>
			<artifactId>logback-contrib-parent</artifactId>
			<version>0.1.5</version>
			<type>pom</type>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback.contrib</groupId>
			<artifactId>logback-json-classic</artifactId>
			<version>0.1.5</version>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback.contrib</groupId>
			<artifactId>logback-jackson</artifactId>
			<version>0.1.5</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.riddler</groupId>
			<artifactId>riddler-client</artifactId>
			<version>1.0.1</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.iris</groupId>
			<artifactId>iris-client</artifactId>
			<version>2.0.18</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>base-common</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-messaging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit.fitcash</groupId>
			<artifactId>fitcash-client</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.datadoghq</groupId>
			<artifactId>dd-trace-api</artifactId>
			<version>0.92.0</version>
		</dependency>

		<dependency>
			<groupId>io.sentry</groupId>
			<artifactId>sentry</artifactId>
		</dependency>

		<dependency>
			<groupId>io.sentry</groupId>
			<artifactId>sentry-spring</artifactId>
		</dependency>

		<dependency>
			<groupId>io.sentry</groupId>
			<artifactId>sentry-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>io.sentry</groupId>
			<artifactId>sentry-logback</artifactId>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>alfred-common</artifactId>
			<version>${alfred.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>alfred-client</artifactId>
			<version>${alfred.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>oms-client</artifactId>
			<version>${oms.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>ambrosia-client</artifactId>
			<version>${ambrosia.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>customer-support-client</artifactId>
			<version>${customersupport.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>curefit-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>curefit-commons-redis</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>challenges-client</artifactId>
			<version>${challenges-client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>indus-client</artifactId>
			<version>${indus.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>lexicon-client</artifactId>
			<version>${lexicon.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>talktube-client</artifactId>
			<version>${talktube.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>poll-support-client</artifactId>
			<version>${pollsupport.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>housemd-client</artifactId>
			<version>${housemd-client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>fitness-client</artifactId>
			<version>${fitness.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-client</artifactId>
				</exclusion>
				<!-- Exclude vulnerable mysql-connector-java to fix CVE-2023-22102 -->
				<exclusion>
					<groupId>mysql</groupId>
					<artifactId>mysql-connector-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>logging-client</artifactId>
			<version>${logging.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>central-health-store-client</artifactId>
			<version>${chs.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>catalog-service-client</artifactId>
			<version>${catalog.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>experiment-client</artifactId>
			<version>${experiment.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>nest-client</artifactId>
			<version>${nest.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>experiment-domain</artifactId>
			<version>${experiment.domain.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>subscription-manager-client</artifactId>
			<version>${sms.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>subscription-manager-domain</artifactId>
			<version>${sms.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>medicine-client</artifactId>
			<version>${medicine.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>sleep-client</artifactId>
			<version>${sleep.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>eat-api-common</artifactId>
			<version>0.0.11</version>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-validator</artifactId>
			<version>5.4.3.Final</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>eat-api-client</artifactId>
			<version>0.0.11</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.offers</groupId>
			<artifactId>offer-service-client</artifactId>
			<version>${offer.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit.offers</groupId>
			<artifactId>offer-service-commons</artifactId>
			<version>${offer.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.samskivert</groupId>
			<artifactId>jmustache</artifactId>
			<version>1.15</version>
		</dependency>
		<dependency>
			<groupId>com.curefit.search</groupId>
			<artifactId>search-commons</artifactId>
			<version>0.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.search</groupId>
			<artifactId>search-client</artifactId>
			<version>0.1.0</version>
		</dependency>

		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-spring-boot2</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-circuitbreaker</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-bulkhead</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-ratelimiter</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-retry</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-annotations</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>gymfit-client</artifactId>
			<version>${gymfit.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.rollbar</groupId>
					<artifactId>rollbar-logback</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>converse-client</artifactId>
			<version>1.4.4</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>onyx-api-client</artifactId>
			<version>1.0.10-5</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>contact-sync-common</artifactId>
			<version>0.0.3</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>rashi-client</artifactId>
			<version>3.9.4</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>rashi-common</artifactId>
			<version>3.9.4</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>app-config-client</artifactId>
			<version>1.0.7</version>
		</dependency>

		<dependency>
			<groupId>com.google.firebase</groupId>
			<artifactId>firebase-admin</artifactId>
			<version>9.4.3</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>microlearning-client</artifactId>
			<version>${microlearning.client.version}</version>
		</dependency>

		<dependency>
			<artifactId>foodway-common</artifactId>
			<groupId>com.curefit</groupId>
			<version>1.15.2</version>
		</dependency>

		<dependency>
			<artifactId>foodway-client</artifactId>
			<groupId>com.curefit</groupId>
			<version>1.15.2</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>magneto-client</artifactId>
			<version>${magneto.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cultsport-user-client</artifactId>
			<version>${magneto.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cultsport-warranty-client</artifactId>
			<version>${magneto.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>xandar-client</artifactId>
			<version>1.8.1</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>magneto-common</artifactId>
			<version>${magneto.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cultsport-user-common</artifactId>
			<version>${magneto.client.version}</version>
		</dependency>

		<dependency>
			<groupId>io.opentracing</groupId>
			<artifactId>opentracing-util</artifactId>
			<version>0.33.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.21.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.21.1</version>
		</dependency>

		<dependency>
			<artifactId>center-service-client</artifactId>
			<groupId>com.curefit</groupId>
			<version>${center.client.version}</version>
		</dependency>

		<dependency>
			<artifactId>center-service-domain</artifactId>
			<groupId>com.curefit</groupId>
			<version>${center.client.version}</version>
		</dependency>

		<dependency>
			<artifactId>membership-client</artifactId>
			<groupId>com.curefit</groupId>
			<version>${membership.version}</version>
		</dependency>

		<dependency>
			<artifactId>sports-api-client</artifactId>
			<groupId>com.curefit</groupId>
			<version>0.0.66-stage-1</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.report-issues</groupId>
			<artifactId>report-issues-client</artifactId>
			<version>2.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.report-issues</groupId>
			<artifactId>report-issues-commons</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>fuse-client</artifactId>
			<version>1.1.57</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>social-service-client</artifactId>
			<version>${social-service.version}</version>
		</dependency>

        <dependency>
			<groupId>com.curefit</groupId>
			<artifactId>social-service-common</artifactId>
			<version>${social-service.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>quest-client</artifactId>
			<version>3.0.5</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cultsport-feedback-client</artifactId>
			<version>1.4.5</version>
		</dependency>

		<dependency>
			<groupId>org.jetbrains</groupId>
			<artifactId>annotations</artifactId>
			<version>24.1.0</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>tread-api-client</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>tread-api-common</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>third-party-integrations-client</artifactId>
			<version>0.0.2-RELEASE</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.cult</groupId>
			<artifactId>cult-commons</artifactId>
			<version>${cult.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>meal-planner-client</artifactId>
			<version>${meal.planner.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.cult</groupId>
			<artifactId>cult-client</artifactId>
			<version>${cult.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>user-activity-service-client</artifactId>
			<version>${user-activity-service.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>user-activity-service-commons</artifactId>
			<version>${user-activity-service.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.curio</groupId>
			<artifactId>curio-client</artifactId>
			<version>1.1.2</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>smartdevice-client</artifactId>
			<version>${smartdevice.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>smartdevice-commons</artifactId>
			<version>${smartdevice.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.mixpanel</groupId>
			<artifactId>mixpanel-java</artifactId>
			<version>1.5.2</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>tms-client</artifactId>
			<version>0.0.5</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>ticketing-system-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.postgresql</groupId>
					<artifactId>postgresql</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>membership-commons</artifactId>
            <version>${membership.version}</version>
            <scope>compile</scope>
        </dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>shifu-commons</artifactId>
			<version>${shifu.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>assessment-client</artifactId>
			<version>2.2.7</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>ticket-management-client</artifactId>
			<version>${ticketing-system.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>pack-management-service-client</artifactId>
			<version>${pack-management.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>pack-management-service-common</artifactId>
			<version>${pack-management.version}</version>
		</dependency>

		<dependency>
			<groupId>com.facebook.business.sdk</groupId>
			<artifactId>facebook-java-business-sdk</artifactId>
			<version>19.0.2</version>
		</dependency>
    
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-sts</artifactId>
			<version>${aws-java-sdk.version}</version>
		</dependency>

		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>sts</artifactId>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>maestro-client</artifactId>
			<version>${maestro.version}</version>
		</dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
<!--					<source>${java.version}</source>-->
<!--					<target>${java.version}</target>-->
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>1.5.3.Final</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok-mapstruct-binding</artifactId>
							<version>0.2.0</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.1.0</version>
				<configuration>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>mmdb</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
        </plugins>
	</build>

</project>
