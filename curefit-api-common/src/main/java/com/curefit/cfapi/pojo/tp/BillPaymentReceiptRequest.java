package com.curefit.cfapi.pojo.tp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@ToString
public class BillPaymentReceiptRequest {
    String refId;
    String txnId;
    CustomerParams customerParams;
    Object customerDetails;
    String planId;
    String billNumber;
    Double amount;
    String paymentName;
    List<Object> amountDetails;
    List<Object> blrAdditionalInfo;
}
