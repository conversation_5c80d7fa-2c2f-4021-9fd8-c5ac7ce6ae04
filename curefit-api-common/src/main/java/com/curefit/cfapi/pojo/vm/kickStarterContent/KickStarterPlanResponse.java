package com.curefit.cfapi.pojo.vm.kickStarterContent;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@ToString
@FieldDefaults(level = AccessLevel.PRIVATE)
public class KickStarterPlanResponse {
    boolean isUserInKickStarterPlan;
    String getWasActiveInLastClass;
    String getNextWorkoutCategory;
}
