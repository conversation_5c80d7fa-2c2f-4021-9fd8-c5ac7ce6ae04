package com.curefit.cfapi.pojo.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@ToString
public class CenterMapping {
    @JsonProperty("CENTER_SERVICE_CENTER_ID")
    private Long CENTER_SERVICE_CENTER_ID;

    @JsonProperty("CULT_CENTER_ID")
    private Long CULT_CENTER_ID;

    @JsonProperty("GYMFIT_CENTER_ID")
    private Long GYMFIT_CENTER_ID;
}
