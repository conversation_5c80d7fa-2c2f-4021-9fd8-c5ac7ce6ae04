package com.curefit.cfapi.pojo.status;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class UserStatusRequestBody {
    RequestBodyDeviceInfo requestBodyDeviceInfo;
    String cityId;
    String advertiserId;
    BranchParams branchParams;

    @JsonProperty("client_id")
    String clientId;
    String state;
    String code;
    @JsonProperty("redirect_uri")
    String redirectUri;

    String formId;

    // Populated from request headers
    String webClientId;
    String firebaseInstanceId;
}
