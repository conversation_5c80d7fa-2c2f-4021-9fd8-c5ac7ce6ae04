package com.curefit.cfapi.pojo.chroniccare.ecommerce;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Slf4j
@NoArgsConstructor
@Component
public class SfECommerceOrderTrackingDetails {
    List<TrackingState> trackingStates = List.of(
            TrackingState.builder().status(TrackingStatus.CONFIRMED).statusText("Order Confirmed").isComplete(true).build(),
            TrackingState.builder().status(TrackingStatus.SHIPPED).statusText("Shipped").build(),
            TrackingState.builder().status(TrackingStatus.DELIVERED).statusText("Delivered").build()
            );

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Builder
    public static class TrackingState {
        TrackingStatus status;
        String statusText;
        Date time;
        @JsonProperty("isComplete")
        boolean isComplete;
    }

    public enum TrackingStatus {
        CONFIRMED, SHIPPED, DELIVERED
    }
}
