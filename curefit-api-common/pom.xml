<project
		xmlns="http://maven.apache.org/POM/4.0.0"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

    <parent>
		<groupId>com.curefit</groupId>
		<artifactId>curefit-api</artifactId>
		<version>0.0.2</version>
	</parent>
	<artifactId>curefit-api-common</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.curefit.cult</groupId>
			<artifactId>cult-commons</artifactId>
			<version>${cult.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cf-commons-domain</artifactId>
			<version>${curefit.common-sf.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>common-data</artifactId>
				</exclusion>

				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-messaging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>base-common</artifactId>
			<version>${base-common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-messaging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>ehr-client</artifactId>
			<version>2.0.11</version>
			<exclusions>
				<exclusion>
					<groupId>redis.clients</groupId>
					<artifactId>jedis</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.undertow</groupId>
					<artifactId>undertow-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.springfox</groupId>
					<artifactId>springfox-swagger-ui</artifactId>
				</exclusion>
				<!-- Exclude vulnerable mysql-connector-java to fix CVE-2023-22102 -->
				<exclusion>
					<groupId>mysql</groupId>
					<artifactId>mysql-connector-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>smartdevice-commons</artifactId>
			<version>${smartdevice.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>location-common</artifactId>
			<version>0.0.50</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
			</exclusions>
		</dependency>


		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>apps-common</artifactId>
			<version>0.0.22</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>gearvault-common</artifactId>
			<version>${gearvault.client.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>util-common</artifactId>
			<version>0.0.2</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>product-common</artifactId>
			<version>${curefit.product-common.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.cult</groupId>
					<artifactId>cult-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>gymfit-common</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>gymfit-common</artifactId>
			<version>${gymfit.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.rollbar</groupId>
					<artifactId>rollbar-logback</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>alfred-client</artifactId>
			<version>${alfred.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>oms-client</artifactId>
			<version>${oms.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>oms-common</artifactId>
			<version>${oms.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>medicine-client</artifactId>
			<version>${medicine.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>ambrosia-domain</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>challenges-client</artifactId>
			<version>${challenges-client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>indus-client</artifactId>
			<version>${indus.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>sleep-client</artifactId>
			<version>${sleep.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>customer-support-client</artifactId>
			<version>${customersupport.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>curefit-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>curefit-commons-redis</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>ambrosia-domain</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>subscription-manager-domain</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>eat-api-common</artifactId>
			<version>0.0.11</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>user-service-common</artifactId>
			<version>${user-service.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>common-data</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>contact-sync-common</artifactId>
			<version>0.0.3</version>
		</dependency>

		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>diyfs-common</artifactId>
			<version>${diyfs.version}</version>
		</dependency>

		<dependency>
			<groupId>com.curefit.subuser</groupId>
			<artifactId>sub-user-common</artifactId>
			<version>1.0.7</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>common-data</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.10.0</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>shifu-client</artifactId>
			<version>${shifu.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>subscription-manager-domain</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>meal-planner-client</artifactId>
			<version>${meal.planner.version}</version>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>fitness-client</artifactId>
			<version>${fitness.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>ambrosia-domain</artifactId>
				</exclusion>
				<!-- Exclude vulnerable mysql-connector-java to fix CVE-2023-22102 -->
				<exclusion>
					<groupId>mysql</groupId>
					<artifactId>mysql-connector-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>microlearning-client</artifactId>
			<version>${microlearning.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>albus-client</artifactId>
			<version>${albus.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit.commons</groupId>
					<artifactId>curefit-commons-util</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>metric-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>subscription-manager-domain</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>metric-model</artifactId>
			<version>1.10.8</version>
		</dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>magneto-common</artifactId>
            <version>${magneto.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
			</exclusions>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>onyx-api-common</artifactId>
            <version>1.0.10-5</version>
            <scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>cf-commons</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.curefit</groupId>
					<artifactId>ufs-common</artifactId>
				</exclusion>
			</exclusions>
        </dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>converse-commons</artifactId>
			<version>1.4.4</version>
		</dependency>

		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>central-health-store-domain</artifactId>
			<version>${chs.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>ambrosia-domain</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>logging-domain</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>subscription-manager-domain</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>logging-domain</artifactId>
			<version>${logging.client.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.sugarfit</groupId>
					<artifactId>ambrosia-domain</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.curefit.offers</groupId>
			<artifactId>offer-service-commons</artifactId>
			<version>${offer.client.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>ambrosia-domain</artifactId>
			<version>${ambrosia.client.version}</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>user-activity-service-commons</artifactId>
			<version>${user-activity-service.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>user-activity-service-client</artifactId>
			<version>${user-activity-service.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>activity-logging-client</artifactId>
			<version>2.4.12</version>
		</dependency>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>activity-logging-commons</artifactId>
			<version>2.4.12</version>
		</dependency>
        <dependency>
            <groupId>com.facebook.business.sdk</groupId>
            <artifactId>facebook-java-business-sdk</artifactId>
            <version>19.0.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>maestro-common</artifactId>
            <version>${maestro.version}</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>com.sugarfit</groupId>
			<artifactId>subscription-manager-domain</artifactId>
			<version>${sms.client.version}</version>
			<scope>compile</scope>
		</dependency>
    </dependencies>
</project>

